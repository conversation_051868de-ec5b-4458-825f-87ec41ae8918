/*
 * @Descripttion: 用户角色管理单例
 * @Author: 小圆
 * @Date: 2023-11-23 11:50:17
 * @LastEditors: 小圆
 */
import {
  classList,
  getSubjectListByRole,
  getSubjectsAPI,
  getTeacherClassList,
  getUserInfoToPersonalityTest,
  getUserRoleAPI,
  getUserRoles,
  getUserInfoAPI,
  getGradeListByRole,
  getClassListByRole,
  getCampusCodeList,
  getTeaCampusCodeListAPI,
} from '@/service/api';

import store from '@/store/index';
import { localSave, sessionSave } from '@/utils';

// import Vue from "vue";
// import Bus from "@/utils/bus"; // 事件中心
// Vue.use(Bus);
import { selectAllType } from '@/service/pbook';
import { uniqueFunc } from './common';

import UserRoleUtils from './UserRoleUtils';
import moment from 'moment';

export interface LoginInfo {
  account_type: number;
  id: string;
  user_name: string;
  mobile: string;
  email: string;
  avatar: string;
  realname: string;
  sex: string;
  /** 用户类型 1：教师 2：学生 3：家长 0：注册用户 4：代理商 5:运营 6:区域用户 */
  user_type: number;
  /** 管理员类型 0为默认什么都不是 1为总管理员 2为校级管理员 3为班级管理员 4区域管理员 */
  admin_type: number;
  card_id: string;
  schoolid: string;
  school_name: string;
  subjectid: string;
  sub_list: string;
  sub_name_list: string;
  center_id: string;
  huohua_id: string;
  state: number;
  isdelete: number;
  gmt_create: Date;
  pinyin_first: string;
  expire_datetime: Date;
  expire_type: string;
  salt: string;
  origin: number;
  isregister: number;
  examine_state: number;
  tencent_id: string;
  gmt_modified: Date;
  stu_status_no: string;
  exam_no: string;
  stu_no: string;
  tag_id: string;
  tag_title: string;
  _MASK_FROM_V2: string;
  school_type: number;
  multiple_phase: string;
  phase: number;
  schooldelete: number;
  loginSchoolRelation: LoginSchoolRelation[];
  classAdmin: number;
  token: string;
}

export interface SchoolInfo {
  id: string;
  school_name: string;
  schoolName: string;
  schoolId: string;
}

export interface ClassList {
  classId: string;
  year: number;
  system_code: string;
  className: string;
  class_order: number;
  class_name: string;
  classType: number;
  campusCode: string;
}

export interface CampusList {
  code: string;
  name: string;
  subjectList: IRoleSubject[];
  schoolLeaderSubjectList: IRoleSubject[];
  classList: ClassList[];
  substituteClassList: SubstituteClassList[];
  leaderClassList: LeaderClassList[];
}

export interface LoginSchoolRelation {
  userId: string;
  userType: number;
  realName: string;
  pinyinFirst: string;
  adminType: string;
  schoolId: string;
  schoolName: string;
  createTime: Date;
}

export interface IRoleClass {
  id: string;
  year: number;
  system_code: string;
  schoolid: string;
  phase: string;
  class_name: string;
  class_order: number;
  leader_ids: string;
  leader_names: string;
  leader_class: string;
  class_type: number;
  creatid: string;
  center_id: string;
  isdelete: number;
  gmt_create: string;
  gmt_modified: string;
  ip: string;
  graduate: number;
  student_count: number;
  origin: number;
  allow_register: boolean;
  _MASK_FROM_V2: string;
  gradeId: string;
  gradename: string;
}

export interface IRoleList {
  types: string;
  status: string;
}

export interface IRoleSubject {
  subjectId: string;
  subjectName: string;
  year: number | string;
  systemCode: string;
  phase: string | number;
  userType: number[];
  userTypes: string;
  gradeName: string;
}

export interface IUserGrdList {
  id: number | string;
  name: string;
  phaseId?: number;
  year?: number;
  systemCode?: string;
}

export interface IUserInfo {
  schGrdList: IUserGrdList[];
  schSubList: IUserSubList[];
  userGrdList: IUserGrdList[];
  userSubList: IUserSubList[];
  userPSubMap: {
    [x: string]: IUserSubList[];
  };
  subjectId: string;
  substituteClassList: SubstituteClassList[];
  leaderClassList: LeaderClassList[];
}

export interface SubstituteClassList {
  subjectId: string;
  subjectName: string;
  classId: string;
  gradeName: string;
  className: string;
  phase: string;
  year: number;
  system_code: string;
  campusCode: string;
}

export interface LeaderClassList {
  id: string;
  year: number;
  systemCode: string;
  className: string;
  classType: number;
  phase: string;
  gradeName: string;
  campusCode: string;
}

export interface IUserSubList {
  id: string;
  subject_name?: string;
  phase?: number;
  center_code?: string;
  isCustom?: number;
  phaseId?: number;
  name: string;
}

export interface IRoleGrade {
  phase: number;
  gradeName: string;
  gradeId: string;
  year: number;
  systemCode: string;
}

export interface IRoleClassAPI {
  classId: string;
  year: number;
  classOrder: number;
  system_code: string;
  className: string;
  class_order: number;
  class_name: string;
  classType: number;
}

export enum RoleEnum {
  /** 学校管理员 */
  SCHOOL_ADMIN = 1,
  /** 年级主任 */
  GRADE_LEADER = 2,
  /** 学科主任 */
  SUBJECT_LEADER = 3,
  /** 备课组长 */
  PREPARE_LEADER = 4,
  /** 班主任 */
  CLASS_LEADER = 5,
  /** 普通教师 */
  TEACHER = 6,
  /** 德育主任 */
  MORAL_LEADER = 7,
  /** 校长 */
  PRESIDENT = 8,
  /** 副校长 */
  VICE_PRESIDENT = 9,
  /** 教务主任 */
  DEAN_LEADER = 10,
  /** 信息主任 */
  INFORMATION_LEADER = 11,
}

function getExcludeRoles(roles: number[], excludeRoles: number[]) {
  let roleSet = new Set(roles.map(r => Number(r)));
  if (excludeRoles.length) {
    excludeRoles.forEach(r => {
      roleSet.delete(Number(r));
    });
  }
  return [...roleSet];
}

class UserRole {
  $sessionSave = sessionSave;

  // 实例
  static instance = null;
  // 是否加载
  isInit = null;
  // 用户类型
  roleTypes = null;
  // 考试用户权限
  examRolesTypes = '';
  // 用户角色列表
  userRolesList = null;
  // 权限列表
  roleList = null;
  // 角色班级列表
  roleClassList = null;
  // 年级信息
  gradeInfo = null;
  // 学校校区列表
  campusList: CampusList[] = [];
  // 用户角色学科列表
  roleSubjectList = null as IRoleSubject[];
  // 校管学科列表
  schoolLeaderSubjectList = [] as IRoleSubject[];
  // 班级列表
  classList: ClassList[] = [];
  // 用户代课班级
  substituteClassList = [] as SubstituteClassList[];
  // 班主任班级
  leaderClassList = [] as LeaderClassList[];
  // 用户学科列表
  userSubList = null;
  // 用户年级列表
  userGrdList = null;
  // 用户信息
  userInfo: IUserInfo = null;
  // 类型列表
  typeList = null;
  userId = null;
  _schoolInfo = null;
  utils = UserRoleUtils;

  /** 是否运营 */
  get isOperation() {
    let loginInfo = this.loginInfo;
    return (loginInfo?.user_type == 5 && loginInfo?.admin_type != 2) || loginInfo?.account_type == 5;
  }
  /** 是否区域用户 */
  get isRegion() {
    return this.loginInfo?.user_type == 6;
  }
  /** 是否学校管理员 1 */
  isSchoolAdmin = false;
  /** 是否年级主任 2 */
  isGradeLeader = false;
  /** 是否学科主任 3 */
  isSubjectLeader = false;
  /** 是否备课组长 4 */
  isPrepareLeader = false;
  /** 是否班主任 5 */
  isClassLeader = false;
  /** 是否普通老师 6 */
  isTeacher = false;
  /** 是否德育主任 7 */
  isMoralLeader = false;
  /** 是否校长 8 */
  isPresident = false;
  /** 是否副校长 9 */
  isVicePresident = false;
  /** 是否教务主任 10 */
  isDeanLeader = false;
  /** 是否信息主任 11 */
  isInformationLeader = false;
  /** 是否学校领导（学校管理员、校长、副校长、教务主任）*/
  get isSchoolLeader() {
    return this.isSchoolAdmin || this.isPresident || this.isVicePresident || this.isDeanLeader;
  }

  // 学校信息
  get schoolInfo(): SchoolInfo {
    return this.$sessionSave.get('schoolInfo') as any;
  }

  set schoolInfo(val) {
    this._schoolInfo = val;
  }

  async setSchoolInfo(val) {
    try {
      if (this.userId == this.loginInfo?.id && JSON.stringify(this._schoolInfo) == JSON.stringify(val) && this.isInit) {
        return;
      }
      this._schoolInfo = val;
      if (!this.loginInfo && !this.loginInfo?.id) return;
      this.userId = this.loginInfo.id;
      this.dispose();
      await this.init();
    } catch (error) {
      console.error(error);
      this._schoolInfo = val;
      this.dispose();
    }
  }

  get loginInfo(): LoginInfo {
    return this.$sessionSave.get('loginInfo') as any;
  }

  constructor() {
    UserRole.instance = this;
    (window as any).UserRole = this;
  }

  /**
   * @description: 获取实例
   */
  static getInstance() {
    if (!UserRole.instance) {
      UserRole.instance = new UserRole();
    }
    return UserRole.instance;
  }

  // 销毁实例
  public dispose() {
    this.roleTypes = null;
    this.examRolesTypes = '';
    this.userRolesList = null;
    this.roleList = null;
    this.roleClassList = null;
    this.schoolLeaderSubjectList = [];
    this.substituteClassList = [];
    this.leaderClassList = [];
    this.gradeInfo = null;
    this.roleSubjectList = null;
    this.userSubList = null;
    this.userGrdList = null;
    this.userInfo = null;
    this.typeList = null;
    this.campusList = [];

    this.isSchoolAdmin = false;
    this.isGradeLeader = false;
    this.isSubjectLeader = false;
    this.isPrepareLeader = false;
    this.isClassLeader = false;
    this.isTeacher = false;
    this.isMoralLeader = false;
    this.isPresident = false;
    this.isVicePresident = false;
    this.isDeanLeader = false;
    this.isInformationLeader = false;

    this.isInit = false;
  }

  // 初始化获取
  public async init() {
    if (!this.schoolInfo.id && this.isOperation) {
      this.isInit = true;
      return;
    }
    await this.getRoleList();
    this.setCurrentRole();
    await Promise.all([this.getUserInfoPersonalityTest(), this.getRoleClass(), this.getRoleSubjectList()]);
    await this.getCampusList();
    this.isInit = true;
  }

  // 获取用户校区列表
  async getCampusList() {
    this.classList = await this.getClassList({ classType: -1 });
    const res = await getTeaCampusCodeListAPI({
      teaId: this.loginInfo.id,
      // schoolId: this.schoolInfo.id,
      // page: 1,
      // limit: 1000,
    });
    this.campusList = res.data.map(item => {
      return {
        name: item.name,
        code: item.code,
        schoolLeaderSubjectList: [],
        subjectList: [],
        classList: [],
        substituteClassList: [],
        leaderClassList: [],
      };
    });
    for (const item of this.campusList) {
      item.subjectList = await this.getRoleSubjectList(item.code);
      item.schoolLeaderSubjectList = await this.getRoleSubjectList(item.code, '1');
      item.classList = this.classList.filter(t => t.campusCode == item.code);
      item.substituteClassList = this.substituteClassList.filter(t => t.campusCode == item.code);
      item.leaderClassList = this.leaderClassList.filter(t => t.campusCode == item.code);
    }
    this.schoolLeaderSubjectList = await this.getRoleSubjectList('', '1');
  }

  // 设置当前角色
  private setCurrentRole() {
    let examRoles = [];
    for (const item of this.roleList) {
      if (item.types == '1') {
        this.isSchoolAdmin = true;
        examRoles.push(1);
      }
      if (item.types == '2') {
        this.isGradeLeader = true;
        examRoles.push(2);
      }
      if (item.types == '3') {
        this.isSubjectLeader = true;
        examRoles.push(3);
      }
      if (item.types == '4') {
        this.isPrepareLeader = true;
        examRoles.push(4);
      }
      if (item.types == '5') {
        this.isClassLeader = true;
        examRoles.push(5);
      }
      if (item.types == '6') {
        this.isTeacher = true;
        examRoles.push(6);
      }
      if (item.types == '7') {
        this.isMoralLeader = true;
        examRoles.push(6);
      }
      if (item.types == '8') {
        this.isPresident = true;
        examRoles.push(1);
      }
      if (item.types == '9') {
        this.isVicePresident = true;
        examRoles.push(1);
      }
      if (item.types == '10') {
        this.isDeanLeader = true;
        examRoles.push(1);
      }
      if (item.types == '11') {
        this.isInformationLeader = true;
        examRoles.push(6);
      }
    }

    this.examRolesTypes = Array.from(new Set(examRoles)).join(',');
  }

  /** 获取年级列表 */
  public async getGradeList(option?: { schoolYear?; year?; excludeRoles?; subjectId? }): Promise<IUserGrdList[]> {
    let { schoolYear = '', year = '', subjectId = '', excludeRoles = [] } = option || {};

    // let ret = await this.getUserInfoPersonalityTest();
    // if (this.isOperation) {
    //   return ret.schGrdList;
    // }

    let roles = this.examRolesTypes.split(',').map(r => Number(r));
    if (subjectId) {
      roles = this.utils.getSubjectRoles(subjectId, year);
    }
    roles = getExcludeRoles(roles, excludeRoles);
    // 战哥说运营也要根据学年，动态获取年级。
    if (this.isOperation) {
      roles = [1];
    }

    let params: any = {
      schoolId: this.schoolInfo.id,
      userId: this.loginInfo.id,
      userType: roles.join(','),
    };

    if (schoolYear) {
      if (String(schoolYear).length == 4) {
        params.schoolYear = `${schoolYear}-08-10`;
      } else {
        params.schoolYear = moment(schoolYear).format('YYYY-MM-DD');
      }
    }

    let res = await getGradeListByRole(params);
    let gradeList = Array.isArray(res.data) ? res.data : [];
    gradeList = gradeList.map(item => {
      return {
        id: Number(item.gradeId) || item.gradeId,
        name: item.gradeName,
        year: item.year,
        phaseId: item.phase + 2,
        phase: item.phase,
        systemCode: item.systemCode,
      };
    });
    return gradeList;
  }

  /** 获取学科列表 */
  public async getSubjectList(option?: { systemCode?; year?; roles?; excludeRoles? }): Promise<IUserSubList[]> {
    let {
      systemCode = '',
      year = '',
      roles = this.examRolesTypes.split(',').map(r => Number(r)),
      excludeRoles = [],
    } = option || {};
    let ret = await this.getUserInfoPersonalityTest();
    if (this.isOperation || this.isSchoolLeader) {
      return ret.schSubList;
    }
    let userRoles = getExcludeRoles(roles, excludeRoles);
    let res = await getSubjectListByRole({
      schoolId: this.schoolInfo.id,
      userId: this.loginInfo.id,
      userType: userRoles.join(','),
      year: year,
      systemCode: systemCode,
    });
    let subjectList = res.data || [];
    let subjectArr = [];
    subjectList.forEach(item => {
      let subject = ret.schSubList.find(t => t.id == item.subjectId);
      if (subject && !subjectArr.find(t => t.id == subject?.id))
        subjectArr.push({ ...subject, systemCode: item.systemCode });
    });
    return subjectArr;
  }

  /** 获取班级列表 */
  public async getClassList({
    subjectId = '',
    year = '',
    systemCode = '',
    classType = 1,
    roles = [],
    excludeRoles = [],
  }): Promise<ClassList[]> {
    if (!roles.length) roles = this.examRolesTypes.split(',').map(r => Number(r));
    if (subjectId) {
      roles = this.utils.getSubjectRoles(subjectId, year);
    }
    roles = getExcludeRoles(roles, excludeRoles);
    if (this.isOperation) {
      roles = [1];
    }
    const res = await getClassListByRole({
      schoolId: this.schoolInfo.id,
      userId: this.loginInfo.id,
      userType: roles.join(','), // 角色列表
      subjectId: subjectId, // 学科ID，任课教师用
      year: year, // 入学年份
      systemCode: systemCode, // 学制
      classType: classType, // 班级类型行政班 1行政班 3 分层班 -1:全部班级
    });
    let classList = Array.isArray(res.data) ? res.data : [];
    classList.forEach(item => {
      let classRoles = [];
      roles.forEach(role => {
        if (role == 1 || role == 2 || role == 3 || role == 4) {
          classRoles.push(role);
        }
        if (role == 5) {
          if (this.leaderClassList.some(leaClass => leaClass.id == item.classId)) {
            classRoles.push(role);
          }
        }
        if (role == 6) {
          if (
            this.substituteClassList.some(
              subClass => subClass.classId == item.classId && (subClass.subjectId == subjectId || !subjectId)
            )
          ) {
            classRoles.push(role);
          }
        }
      });
      item.roles = classRoles;
    });
    return classList;
  }

  // 获取角色列表
  public async getRoleList(): Promise<IRoleList[]> {
    if (!this.roleList) {
      let roleList = [];
      let data = await this.getUserRoles();
      let rolesStatus = data.status.split(',');
      let roles = data.types.split(',');
      rolesStatus.forEach((item, index) => {
        if (item != '德育主任') {
          roleList.push({
            types: roles[index],
            status: item,
          });
        }
      });
      this.roleList = roleList;
    }
    return this.utils.deepClone(this.roleList);
  }

  /**
   * @description: 获取角色下班级
   */
  public async getRoleClass(): Promise<IRoleClass[]> {
    if (!this.roleClassList) {
      const { data } = await getTeacherClassList({
        userId: this.loginInfo.id,
        containLeaderClass: 1,
      });
      let roleClassList = [];
      let classList = [];
      data.fcList.length &&
        data.fcList.forEach(item => {
          roleClassList.push(item);
          classList.push(item);
        });
      data.xzList.length &&
        data.xzList.forEach(item => {
          roleClassList.push(item);
          classList.push(item);
        });
      let leaderClass = classList.filter(ite => {
        return ite.leader_class;
      });
      this.$sessionSave.set('leaderClass', leaderClass);

      if (!this.roleTypes) await this.getRoleList();
      if (this.roleTypes.indexOf('4') != -1) {
        roleClassList = await this.getPreparationInfo(roleClassList);
      }
      let roleClassListUnique = uniqueFunc(roleClassList, 'id');
      this.$sessionSave.set('roleClassList', roleClassListUnique);
      this.roleClassList = roleClassList;
    }
    return this.utils.deepClone(uniqueFunc(this.roleClassList, 'id'));
  }

  /**
   *
   * @name:根据用户角色获取学科
   */
  public async getRoleSubjectList(campusCode = '', examRolesTypes = ''): Promise<IRoleSubject[]> {
    let list = [];
    let userType = this.examRolesTypes;
    if (examRolesTypes) {
      userType = examRolesTypes;
    }
    const res = await getSubjectListByRole({
      schoolId: this.schoolInfo.id,
      userId: this.loginInfo.id,
      userType: userType,
      isDistinctSubject: 0, // 是否根据学科ID去重 0:否 1:是 默认1
      campusCode: campusCode,
    });
    list = res.data || [];
    let mainSubjectList = [];
    let normalSubjectList = [];
    let subjectLeaderList = [];
    list.forEach(item => {
      item.userType = item.userTypes.split(',').map(t => Number(t));
      if (item.userType.includes(RoleEnum.PREPARE_LEADER)) {
        mainSubjectList.push(item);
      }
      if (item.userType.includes(RoleEnum.TEACHER)) {
        normalSubjectList.push(item);
      }
      if (item.userType.includes(RoleEnum.SUBJECT_LEADER)) {
        // 如果学科主任有入学年份，则添加未知年份的学科主任学科
        if (item.year) {
          const subjectItem = this.utils.deepClone(item);
          subjectItem.gradeName = '未知';
          subjectItem.year = 0;
          subjectItem.systemCode = '';
          subjectItem.userTypes = '3';
          subjectItem.userType = [RoleEnum.SUBJECT_LEADER];
          subjectItem.phase = item.phase;
          subjectLeaderList.push(subjectItem);
        }
      }
    });
    list.push(...subjectLeaderList);
    let roleSubjectList = this.utils.deepClone(list);
    if (!campusCode && !examRolesTypes) {
      this.$sessionSave.set('mainSubjectList', mainSubjectList);
      this.$sessionSave.set('normalSubjectList', normalSubjectList);
      let roleSubjectListUnique = uniqueFunc(list, 'subjectId');
      this.$sessionSave.set('roleSubjectList', roleSubjectListUnique);
      this.roleSubjectList = roleSubjectList;
    }
    return roleSubjectList;
  }

  /**
   * @description: 获取用户信息
   */
  public async getUserInfoPersonalityTest(): Promise<IUserInfo> {
    if (!this.userInfo) {
      const res = await getSubjectsAPI({ schoolId: this.schoolInfo.id });
      let subjectList = [];
      if (res.code == 1) {
        subjectList = res.data.map(item => {
          item.phase = item.phase + 2;
          item.phaseId = item.phase;
          item.name = item.subject_name;
          return item;
        });
      }
      localSave.set('SUBJECT_LIST', subjectList);
      store.commit('SET_SUBJECT', subjectList);

      const ret = await getUserInfoToPersonalityTest();
      this.substituteClassList = ret.substituteClassList;
      this.leaderClassList = ret.leaderClassList;
      this.userSubList = ret.userSubList;
      this.userGrdList = ret.userGrdList;
      this.userInfo = ret;
    }
    return this.utils.deepClone(this.userInfo);
  }

  // 通过接口获取用户
  public async getUserRoles(): Promise<{ status: string; types: string }> {
    const { data } = await getUserRoles({
      schoolId: this.schoolInfo.id,
      userId: this.loginInfo.id,
    });
    this.roleTypes = data.types;
    this.$sessionSave.set('roleTypes', this.roleTypes); // 0总管理员 1校管 2年级组长 3学科组长 4备课组长 5班主任 6普通老师

    return {
      status: data.status,
      types: data.types,
    };
  }

  // 获取全部类型
  public async getAllType(): Promise<{ id: number; name: string }[]> {
    if (!this.typeList) {
      const { data } = await selectAllType();
      const typeList = data;
      this.typeList = typeList;
    }
    return this.utils.deepClone(this.typeList);
  }

  /**
   * @name:获取备课组长信息
   */
  private async getPreparationInfo(roleClassList) {
    const res = await getUserRoleAPI({
      userId: this.loginInfo.id,
      roleType: 2,
    });
    this.gradeInfo = res.data[0];
    const { data } = await classList(this.gradeInfo.year, this.gradeInfo.phase);
    data.rows.forEach(item => {
      roleClassList.push(item);
    });
    return roleClassList;
  }
}

export default new UserRole();

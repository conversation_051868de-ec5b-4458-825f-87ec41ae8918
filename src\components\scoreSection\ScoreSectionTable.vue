<template>
  <div class="score-section-table-box">
    <el-table
        v-if="!hideTable"
        class="el-table--group"
        :data="totalData"
        stripe
        ref="scoreSectionTable"
        :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
        style="width: 100%"
        v-sticky-table="0"
        >
      <el-table-column
          align="center"
          prop="className"
          fixed
          label="班级"
          width="160"
          >
        <template slot-scope="scope">
          <span>{{scope.row.className}}</span>
        </template>
      </el-table-column>
      <template v-for="(scoreItem, index) in totalData[0].scoreSections">
        <el-table-column
            align="center"
            :min-width="totalData[0].scoreSections.length>7?100:''"
            :label="index==0?scoreItem.close+'':'('+scoreItem.close+'-'+scoreItem.open+']'">
          <template slot-scope="scope">
              <span v-if="scope.row.scoreSections[index]">
                  {{scope.row.scoreSections[index].total}}
              </span>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <div class="default" v-else>
      <p style="text-align: center;font-size: 16px;margin: 80px 0;">暂无数据!</p>
    </div>
  </div>
</template>

<script>
export default {
  name: "ScoreSectionTable",
  props: ['tableData'],
  data() {
    return {
      hideTable: false,
      totalData: [
        {
          scoreSections: [{close: '', total: ''}]
        }
      ],
    }
  },
  watch: {
    tableData: {
      immediate: true,
      deep: true,
      handler(newVal, oldValue) {
        // console.log(newVal)
        if(!newVal || !newVal.length) {
          this.hideTable = true;
        } else if(!newVal[0].scoreSections) {
          this.hideTable = true;
        } else {
          this.hideTable = false;
          let res = this.$deepClone(newVal)
          let data = res.map(item => {
            let obj = item;
            if(item.classId==''&&item.scoreSections[0].close == item.scoreSections[0].open) {
              // obj.scoreSections.unshift({
              //   close: item.scoreSections[0].close,
              //   total: item.total || 0
              // })
            } else {
              obj.scoreSections.unshift({
                close: item.scoreSections[0].close,
                total: item.fullTotal || 0
              })
            }

            return obj
          })
          this.totalData = data;
          this.layout();
        }
      }
    }
  },
  methods: {
    layout() {
      if(!this.$refs.scoreSectionTable) {
        return
      }
      this.dom = this.$refs.scoreSectionTable.bodyWrapper;
      this.dom.addEventListener('scroll', () => {
        this.$nextTick(() => {
          //滚动高度
          let scrollTop = this.dom.scrollTop
          if (scrollTop > 5) {
            // (document.getElementsByClassName('el-table__fixed')[0]).style.cssText += 'top: -5px;';
          } else {
            // (document.getElementsByClassName('el-table__fixed')[0]).style.cssText += 'top: 0px;';
          }
        })
      });
    }
  }
}
</script>

<style scoped>

</style>
<style lang="scss">
.score-section-table-box {
  .el-table--scrollable-x .el-table__body-wrapper {
    //overflow-x: hidden;
    //cursor: pointer;
  }
  .el-table__fixed {
    height: 100%!important;
  }
}
</style>

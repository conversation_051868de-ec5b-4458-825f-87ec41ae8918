<!--
 * @Descripttion: 
 * @Author: 小圆
 * @Date: 2023-11-14 08:53:14
 * @LastEditors: Please set LastEditors
-->
<template>
  <div class="edit-block clearfix">
    <span v-if="isShowScoreRate">
      <span v-if="!hasScaned || noOneChooseQues('clsTotal')">班级得分率：——</span>
      <span v-else-if="item.classScoringRate !== undefined">班级得分率：
        <span class="score-rate-display" :class="`level-${item.level}`">
          {{
            item.classScoringRate == 0
            ? item.classScoringRate
            : Number(item.classScoringRate * 100).toFixed(2)
          }}%
        </span>
      </span>
      <span v-else>班级得分率：
        <span class="score-rate-display" :class="`level-${item.level}`">
          {{ getBigQuesScoreRate(item, "classScoringRate") }}%
        </span>
      </span>
    </span>
    <span v-if="isShowScoreRate">
      <span style="margin-left: 22px" v-if="noOneChooseQues('grdTotal')">年级得分率：——</span>
      <span style="margin-left: 22px" v-else-if="item.gradeScoringRate !== undefined">年级得分率：{{
        item.gradeScoringRate == 0
        ? item.gradeScoringRate
        : Number(item.gradeScoringRate * 100).toFixed(2)
      }}%</span>
      <span v-else style="margin-left: 22px">年级得分率：{{ getBigQuesScoreRate(item, "gradeScoringRate") }}%</span>
    </span>

    <span v-if="isShowComment && !isIframe">
      <el-button class="edit-btn comment pull-right" @click.stop="startComment(item, content)">讲评</el-button>
    </span>

    <template v-if="showWKBtn && !isSplitItem">
      <span class="pull-right ques-btn" style="margin-right: 0;"
        @click.stop="openResourceDialog({ item: content && content.courseList ? content : item, type: 'look' })"
        v-if="allResourceLength > 0">
        {{ onlyWeiKe ? "查看微课" : "查看资源" }}（{{ allResourceLength }}）</span>

      <span class="pull-right ques-btn" @click.stop="openResourceDialog({ item, type: 'add' })"
        v-else-if="!isDesktop">{{ onlyWeiKe ? "添加微课" : "添加资源" }}<i class="el-icon-circle-plus"></i></span>

      <el-popover popper-class="popover-wkbtns" placement="bottom" trigger="hover" v-else>
        <div class="ques-btn text-center click-element"
          @click.stop="recordWK(content && content.courseList ? content : item)">录制微课</div>
        <div class="ques-btn text-center click-element"
          @click.stop="openResourceDialog({ item: content && content.courseList ? content : item, type: 'add' })">
          网盘微课
        </div>

        <span class="pull-right ques-btn" slot="reference">添加微课<i class="el-icon-circle-plus"></i></span>
      </el-popover>
    </template>

    <span v-if="isShowVariant" class="pull-right ques-btn" :class="item.variant ? 'active' : ''"
      @click.stop="showVariant(item, content)">
      变式练习 <i :class="item.variant ? 'el-icon-caret-top active' : 'el-icon-caret-bottom'"
        v-if="!canOpenVarEditDialog"></i>
      <i class="el-icon-circle-plus" v-else></i>
    </span>

    <span v-if="hasScaned && isShowAnsDetail" class="pull-right ques-btn" :class="item.ansDetail ? 'active' : ''"
      @click.stop="showAnsDetail(item, content)">答题统计<i
        :class="item.ansDetail ? 'el-icon-caret-top active' : 'el-icon-caret-bottom'"></i></span>

    <span v-if="isShowQuesDetails" class="pull-right ques-btn" :class="item.showDetail ? 'active' : ''"
      @click.stop="showQuesDetails(item, content)">题目详情<i
        :class="item.showDetail ? 'el-icon-caret-top active' : 'el-icon-caret-bottom'"></i></span>

    <el-button v-if="ggbResource" class="pull-right ques-btn ggb-btn"
      @click.stop="lookGGB(item, content)">GGB动画</el-button>


    <!-- 重选变式题弹窗 -->
    <el-dialog v-dragDialog custom-class="comment-similar-dialog" :title="`变式练习`" :show-close="false"
      :visible.sync="isShowSimilarDialog" :modal-append-to-body="false" :append-to-body="true" :destroy-on-close="true"
      width="880px" v-if="isShowVariant">
      <el-tabs v-model="similarActiveTabType" @tab-click="handleSATabClick" v-if="isShowSimilarDialog">

        <el-tab-pane label="系统推荐" name="systemPush">
          <SimilarQuesDialog v-on="$listeners" :originQues="originQues" :selectIds="selectIds" @selectQues="selectQues"
            :maxVariantLength="maxVariantLength" />
        </el-tab-pane>

        <el-tab-pane label="我的题库" name="myQuesBank">
          <MyQuestionBank v-on="$listeners" :selectIds="selectIds" @selectQues="selectQues"
            :maxVariantLength="maxVariantLength" v-if="hasLoadedMyPaperBank" />
        </el-tab-pane>

        <el-tab-pane :label="`已添加（${selectIds.length}/${maxVariantLength}）`" name="myAdded">
          <MyPaperDetail v-on="$listeners" :selectIds="selectIds" @selectQues="selectQues"
            :maxVariantLength="maxVariantLength" :onShowAdded="true" :addedList="linkAddedList" />
        </el-tab-pane>
      </el-tabs>

      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowSimilarDialog = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getQueryString } from '@/utils';
import SimilarQuesDialog from '@/components/paper/similarQuesDialog.vue';
import MyQuestionBank from '@/components/paper/myQuestionBank/index.vue';
import MyPaperDetail from '@/components/paper/myQuestionBank/myPaperDetail.vue';
import { addQsLink, remQsLink } from '@/service/pexam'

export default {
  components: { SimilarQuesDialog, MyQuestionBank, MyPaperDetail },
  props: {
    // 题目对象
    item: {
      type: Object,
      default: () => { },
    },

    // 子题目对象
    content: {
      type: Object,
      default: null,
    },

    // 试卷结构类型 1：二级题型 2：三级题型 3：合并题型 4：拆分题型
    paperStructType: {
      type: Number,
      default: 1
    },

    // 讲评模式 'normal'普通，'empty'空试卷
    paperType: {
      type: String,
      default: "normal",
    },

    courseCount: {
      type: Number,
      default: 0,
    },

    // 是否显示得分率
    isShowScoreRate: {
      type: Boolean,
      default: true,
    },

    // 是否代课班级
    isSubstituteClass: {
      type: Boolean,
      default: true,
    },

    // 是否显示题目详情
    isShowQuesDetails: {
      type: Boolean,
      default: true,
    },

    // 是否显示答题统计
    isShowAnsDetail: {
      type: Boolean,
      default: true,
    },

    // 是否显示选入、讲评
    isShowComment: {
      type: Boolean,
      default: true,
    },

    // 是否显示变式练习
    isShowVariant: {
      type: Boolean,
      default: true,
    },

    hasScaned: {
      type: Boolean,
      default: true,
    },

    // 是否拆分元素
    isSplitItem: {
      type: Boolean,
      default: false,
    },

    // 是否AB卷
    abPaper: {
      type: String,
      default: false,
    },
  },

  computed: {
    // 是否显示GGB资源
    ggbResource() {
      if (!this.content) return null;
      if (!this.content.data) return null;
      if (!this.content.data.url) return null;
      if (!this.content.data.url.length) return null;
      let urls = this.content.data.url;
      let ggbRes;
      for (const item of urls) {
        if (item.resourceType === '6') {
          ggbRes = item;
          break;
        }
      }
      return ggbRes;
    },

    // 已添加的变式题列表
    linkAddedList() {
      if (!this.item || !this.item.linkQues) return [];

      return this.item.linkQues.map(it => it.content)
    },

    // 显示微课
    showWKBtn() {
      return this.hasScaned && this.paperStructType != 3 && this.isSubstituteClass
    },

    // 支持打开编辑变式题弹窗
    canOpenVarEditDialog() {
      return !this.item.links.length && !(this.item.links.length || this.item.isPBook || !this.isSubstituteClass)
    },

    // 当前题目已经选择的变式题
    selectIds() {
      return this.item.links;
    },

    // 全部资源的数量（图片、链接、视频）
    allResourceLength() {
      if (!this.content) return this.courseCount;

      let resLength = this.content.data.url.filter(it => it.resourceType == '4' || it.resourceType == '7').length
      return resLength + this.courseCount;
    },

    // 如果纯卡，则仅显示微课
    onlyWeiKe() {
      return !this.content
    }
  },

  data() {
    return {
      // 是否pc端
      isDesktop: (!!window.cef && this.$route.name != 'paperComment') || getQueryString('isDesktop') == 1,
      // 是否被Iframe内嵌
      isIframe: window !== window.parent,
      // 变式题选卡类型
      similarActiveTabType: "systemPush",
      // 是否显示变式题
      isShowSimilarDialog: false,
      // 是否已加载我的题库
      hasLoadedMyPaperBank: false,
      // 原题
      originQues: null,
      // 父级参数
      parentParams: null,
      // 变式题最大数量
      maxVariantLength: 3,
    };
  },
  created() {
    this.handleEditLinks = this.handleEditLinks.bind(this);
    this.selectQues = this.selectQues.bind(this);

    let $listeners = this.$listeners
    this.parentParams = $listeners.getParams();
    this.$bus.on("editLinks", this.handleEditLinks)
  },
  beforeDestroy() {
    this.$bus.off("editLinks", this.handleEditLinks)
  },

  methods: {
    handleEditLinks(item) {
      this.similarActiveTabType = 'myAdded';
      this.showVariantDialog(item);
    },
    // 处理变式题tab点击
    handleSATabClick(tab) {
      if (tab.name === 'myQuesBank') this.hasLoadedMyPaperBank = true;
    },

    // 无人选做题
    noOneChooseQues(rateType) {
      return this.item[rateType] == 0;
    },
    // 获取班级得分率
    getBigQuesScoreRate(item, key) {
      if (!item.content[0].data.qs) return Number(item[key] * 100).toFixed(2);
      const smallQuesList = item.content[0].data.qs;
      let sum = 0;
      let count = 0;
      smallQuesList.forEach(smallQuesItem => {
        if (Object.hasOwnProperty.call(smallQuesItem, key)) {
          sum = sum + Number(smallQuesItem[key]);
          count++;
        }
      });
      let avg = sum / count;
      return (avg * 100).toFixed(2);
    },

    startComment(item, content) {
      this.$emit('startComment', item, content);
    },

    async selectQues(item, content) {
      try {
        // 1|2:巩固题|拔高题
        let qLinkType = 1;
        let qLinkName = "";
        let examId = this.$sessionSave.get('reportDetail').examId;

        let qsId = this.item.bigId.includes(',') ? (this.item.id || this.item.quesId) : this.item.bigId;
        // 如果是合并题，只取第一题id
        if (qsId.includes(',')) qsId = qsId.split(',')[0];
        if (this.item.links.includes(item.id)) {
          // 移除变式题
          let res = await remQsLink({
            examId,
            subjectId: this.parentParams.subjectId,
            qsId,
            linkQuesId: item.id,
            abPaper: this.abPaper
          })
          if (res.code != 1) throw res;

          let findIndex = this.item.links.findIndex(itId => itId === item.id);
          this.item.links.splice(findIndex, 1);
          this.item.linkQues.splice(findIndex, 1);

          if (!this.item.links.length) this.$emit('hideVariant', this.item);
          this.$notify({
            type: 'success',
            title: '操作成功',
            offset: 100,
            message: "移除变式题成功！"
          });
        } else {

          // 添加变式题
          let res = await addQsLink({
            examId,
            subjectId: this.parentParams.subjectId,
            qsId,
            linkQuesId: item.id,
            type: qLinkType,
            abPaper: this.abPaper
          })
          if (res.code != 1) throw res;

          this.item.links.push(item.id);
          if (!this.item.linkQues) this.item.linkQues = [];
          this.item.linkQues.push({
            type: item.data.type,
            name: qLinkName,
            content: item,
            showAnswer: false,
            showAnalysis: false,
          })
          this.$notify({
            type: 'success',
            title: '操作成功',
            offset: 100,
            message: "添加变式题成功！"
          });
        }

      } catch (error) {
        console.error(error)

        this.$notify.error({
          type: 'error',
          title: '操作变式题失败',
          // position: 'top-left',
          offset: 100,
          message: error.data || error.msg
        });
      } finally {
        // 重新命名变式题名称
        this.item.linkNames = this.createLinkNames(this.item.linkQues, this.item.linkType);
        this.item.active = 'link_0'
      }
    },

    /**
     * @description: 根据变式题数量生成变式题标题
     * linkType:0|1:拔高、巩固|变式题123
     * @param {*} linkQues
     * @return {*}
     */
    createLinkNames(linkQues, linkType) {
      if (!linkQues.length) return [];

      let linkNames = [];
      for (let i = 0; i < linkQues.length; i++) {
        let linkQ = linkQues[i];
        let name = "";
        if (linkType == 0) {
          const names = ["巩固题", "拔高题", "变式题"]
          name = names[i];
        } else {
          name = `变式${i + 1}`;
        }
        linkQ.name = name;
        linkNames.push(name)
      }

      return linkNames;
    },

    // 显示变式题
    showVariant(item, content) {
      if (this.canOpenVarEditDialog) {
        // 添加变式题
        this.similarActiveTabType = 'systemPush';
        this.showVariantDialog(item)
      } else {
        this.$emit('showVariant', item);
      }
    },

    // 显示变式题弹窗
    showVariantDialog(item) {
      // 合并题仅编辑第一个id,显示合并题的弹窗
      if (item.quesId.includes(',') && this.item.qId) return;
      if (this.item.quesId !== item.quesId) return;

      item.linkIds = item.links
      if (item.points) {
        item.pointIds = item.points.map(p => p.id).join(',')
      }
      this.originQues = item;
      this.isShowSimilarDialog = true;
    },

    showAnsDetail(item) {
      this.$emit('showAnsDetail', item);
    },

    showQuesDetails(item, content) {
      this.$emit('showQuesDetails', item);
    },

    lookGGB(item, content) {
      if (!this.ggbResource) return;
      this.$emit('lookGGB', this.ggbResource);
    },

    openResourceDialog(data) {
      this.$emit('openResourceDialog', data)
    },

    recordWK(data) {
      this.$emit('recordWK', data)
    }
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/config";

.edit-block {
  width: 100%;
  // height: 48px;
  line-height: 48px;
  padding: 0 21px 0 11px;
  background: #f5f8fa;
  border-radius: 3px;
  font-size: 14px;
  color: #4e5668ff;

  .score-rate-display {
    font-weight: bold;

    &.level-0 {
      border-color: $level-color-0;
      color: $level-color-0;
    }

    &.level-1 {
      border-color: $level-color-1;
      color: $level-color-1;
    }

    &.level-2 {
      border-color: $level-color-2;
      color: $level-color-2;
    }

    &.level-3 {
      border-color: $level-color-3;
      color: $level-color-3;
    }
  }

  .edit-btn {
    width: 80px;
    height: 32px;
    border-radius: 4px;
    padding: 0;
    line-height: 32px;
    color: #fff;
    margin-top: 8px !important;
    // margin-left: 20px;

    &.add {
      background: #409effff;
    }

    &.hasAdd {
      background-color: #fff;
      border: 1px solid #468fffff;
      color: #468fffff;
    }

    &.comment {
      background: #f5a033;
    }
  }

  .ques-btn {
    position: relative;
    padding-right: 18px;
    margin-right: 24px;
    cursor: pointer;

    i {
      font-size: 22px;
      position: absolute;
      top: 12px;
      right: -4px;

      &.el-icon-circle-plus {
        right: -3px;
        margin-top: 5px;
        font-size: 15px;
        color: #b6b8bfff;
      }
    }

    &.active {
      color: #409effff;

      i {
        top: 13px;
      }
    }

    .el-icon-caret-top {
      color: #409effff;
    }
  }

  .ggb-btn {
    width: 90px;
    height: 32px;

    padding: 0;
    margin-top: 8px !important;
    margin-right: 14px;
    border-radius: 4px;
    line-height: 32px;
    color: #fff;
    background: #409effff;
  }

  .el-icon-caret-bottom {
    color: #b6b8bfff;
  }
}
</style>

<style lang="scss">
.comment-similar-dialog {
  color: #f00;

  .el-dialog__body {
    padding-top: 0;
    padding-bottom: 0;
    max-height: 50vh;
    min-height: 30vh;
    overflow-y: auto;

    .el-tabs__header {
      position: sticky;
      top: 0;
      z-index: 10;
      background-color: #fff;
      user-select: none;
    }
  }
}

.popover-wkbtns {
  margin-top: 0 !important;
}
</style>
export const subject = {
  all: function () {
    return [{ id: "", phase: 0, name: "全部学科", code: "" }].concat(
      this.list()
    );
  },
  list: function (phase) {
    if (!phase) {
      phase = 1;
    }
    return [
      { id: 24, subjectId: 1, phase: phase, phaseId: phase, name: "小学语文" },
      { id: 23, subjectId: 2, phase: phase, phaseId: phase, name: "小学数学" },
      { id: 25, subjectId: 3, phase: phase, phaseId: phase, name: "小学英语" },
      {
        id: 233,
        subjectId: 239,
        phase: phase,
        phaseId: phase,
        name: "小学道德与法治",
      },
      { id: 19, subjectId: 19, phase: phase, phaseId: phase, name: "小学科学" },
      { id: 68, subjectId: 68, phase: phase, phaseId: phase, name: "小学日语" },
      {
        id: 976,
        subjectId: 976,
        phase: phase,
        phaseId: phase,
        name: "小学俄语",
      },
      {
        id: 980,
        subjectId: 980,
        phase: phase,
        phaseId: phase,
        name: "小学德语",
      },
      {
        id: 983,
        subjectId: 983,
        phase: phase,
        phaseId: phase,
        name: "小学法语",
      },
      {
        id: 986,
        subjectId: 986,
        phase: phase,
        phaseId: phase,
        name: "小学西班牙语",
      },

      {
        id: 1,
        subjectId: 1,
        phase: phase + 1,
        phaseId: phase + 1,
        name: "初中语文",
      },
      {
        id: 2,
        subjectId: 2,
        phase: phase + 1,
        phaseId: phase + 1,
        name: "初中数学",
      },
      {
        id: 3,
        subjectId: 3,
        phase: phase + 1,
        phaseId: phase + 1,
        name: "初中英语",
      },
      {
        id: 52,
        subjectId: 239,
        phase: phase + 1,
        phaseId: phase + 1,
        name: "初中道德与法治",
      },
      {
        id: 8,
        subjectId: 12,
        phase: phase + 1,
        phaseId: phase + 1,
        name: "初中历史",
      },
      {
        id: 9,
        subjectId: 14,
        phase: phase + 1,
        phaseId: phase + 1,
        name: "初中地理",
      },
      {
        id: 4,
        subjectId: 5,
        phase: phase + 1,
        phaseId: phase + 1,
        name: "初中物理",
      },
      {
        id: 5,
        subjectId: 6,
        phase: phase + 1,
        phaseId: phase + 1,
        name: "初中化学",
      },
      {
        id: 6,
        subjectId: 13,
        phase: phase + 1,
        phaseId: phase + 1,
        name: "初中生物",
      },
      {
        id: 51,
        subjectId: 19,
        phase: phase + 1,
        phaseId: phase + 1,
        name: "初中科学",
      },
      {
        id: 36,
        subjectId: 26,
        phase: phase + 1,
        phaseId: phase + 1,
        name: "初中信息技术",
      },
      {
        id: 69,
        subjectId: 69,
        phase: phase + 1,
        phaseId: phase + 1,
        name: "初中日语",
      },
      {
        id: 977,
        subjectId: 977,
        phase: phase + 1,
        phaseId: phase + 1,
        name: "初中俄语",
      },
      {
        id: 981,
        subjectId: 981,
        phase: phase + 1,
        phaseId: phase + 1,
        name: "初中德语",
      },
      {
        id: 984,
        subjectId: 984,
        phase: phase + 1,
        phaseId: phase + 1,
        name: "初中法语",
      },
      {
        id: 987,
        subjectId: 987,
        phase: phase + 1,
        phaseId: phase + 1,
        name: "初中西班牙语",
      },

      {
        id: 10,
        subjectId: 1,
        phase: phase + 2,
        phaseId: phase + 2,
        name: "高中语文",
      },
      {
        id: 11,
        subjectId: 2,
        phase: phase + 2,
        phaseId: phase + 2,
        name: "高中数学",
      },
      {
        id: 12,
        subjectId: 3,
        phase: phase + 2,
        phaseId: phase + 2,
        name: "高中英语",
      },
      {
        id: 16,
        subjectId: 103,
        phase: phase + 2,
        phaseId: phase + 2,
        name: "高中政治",
      },
      {
        id: 17,
        subjectId: 12,
        phase: phase + 2,
        phaseId: phase + 2,
        name: "高中历史",
      },
      {
        id: 18,
        subjectId: 14,
        phase: phase + 2,
        phaseId: phase + 2,
        name: "高中地理",
      },
      {
        id: 13,
        subjectId: 5,
        phase: phase + 2,
        phaseId: phase + 2,
        name: "高中物理",
      },
      {
        id: 14,
        subjectId: 6,
        phase: phase + 2,
        phaseId: phase + 2,
        name: "高中化学",
      },
      {
        id: 15,
        subjectId: 13,
        phase: phase + 2,
        phaseId: phase + 2,
        name: "高中生物",
      },
      {
        id: 28,
        subjectId: 26,
        phase: phase + 2,
        phaseId: phase + 2,
        name: "高中信息技术",
      },
      {
        id: 691,
        subjectId: 102,
        phase: phase + 2,
        phaseId: phase + 2,
        name: "高中通用技术",
      },
      {
        id: 70,
        subjectId: 70,
        phase: phase + 2,
        phaseId: phase + 2,
        name: "高中日语",
      },
      {
        id: 978,
        subjectId: 978,
        phase: phase + 2,
        phaseId: phase + 2,
        name: "高中俄语",
      },
      {
        id: 982,
        subjectId: 982,
        phase: phase + 2,
        phaseId: phase + 2,
        name: "高中德语",
      },
      {
        id: 985,
        subjectId: 985,
        phase: phase + 2,
        phaseId: phase + 2,
        name: "高中法语",
      },
      {
        id: 988,
        subjectId: 988,
        phase: phase + 2,
        phaseId: phase + 2,
        name: "高中西班牙语",
      },
    ];
  },
  get: function (id) {
    return this.list().find((x) => x.id == id);
  },
  get3: function (id) {
    return this.list(3).find((x) => x.id == id);
  },
};

export const phase = {
  all: function () {
    return [{ id: 0, name: "全部" }].concat(this.list());
  },

  list: function (phase) {
    if (!phase) {
      phase = 1;
    }
    return [
      { id: phase, name: "小学" },
      { id: phase + 1, name: "初中" },
      { id: phase + 2, name: "高中" },
    ];
  },
};

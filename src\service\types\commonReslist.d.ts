/*
 * @Descripttion: 
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-09-01 20:01:32
 * @LastEditors: Doudo<PERSON>
 */

import { ResExtraStates } from ".";

/**
 * 课件资源
 **/
export interface CoursewareResource extends ResExtraStates {
    [propName: string]: any;
    // 第三方资源类型
    "thirdPartyType": string
    // 用户id
    "userId": string
    // 学校名称
    "schoolName": string
    // 转换后图片数量
    "imgCount": number
    // 资源源文件地址
    "fileUrl": string
    // 是否是文件夹 0:不是 1:是
    "isFolder": 0 | 1
    // 备课课堂活动类型详情见字典表数据
    "bkType": string
    // 预览次数
    "previews": number
    // 第三方资源链接用于存储第三方资源的预览链接
    "thirdPartyLink": string
    // 文件创建时间
    "createTime": string
    // 学校id
    "schoolId": string
    // 下载次数
    "downloads": number
    // 资源id
    "id": string
    "rowNum": number
    // 来源类型(0:个人网盘 1:收藏 2:火花资源 3:公共资源 4:校本资源 5:白板 6:一起资源 7:智启资源
    "origin": 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7
    // 父级id，用于文件夹层级
    "parentId": string
    // 应用类型 0:普通资源 1:课堂活动 2:备课
    "appType": 0 | 1 | 2
    // 资源后缀
    "fileExt": string
    // 缩略图地址
    "imgUrl": string
    // 资源时长单位毫秒
    "timeLength": number
    // 用户姓名
    "userRealname": string
    // 文件类型 0:其它 1:文档 2:微课 3:音频 4:图片
    "fileType": 0 | 1 | 2 | 3 | 4
    // 来源源文件id
    "originId": string
    // 文件md5值
    "fileMd5": string
    // 资源大小单位字节
    "fileSize": number
    // 学科id
    "subjectId": number
    // 转换状态 -2:转换失败 -1:转换中 0:待转换 1:转换成功
    "convertState": number
    // 资源名称不带后缀
    "title": string
    // 年级id
    "gradeId": string
    // 转换次数
    "convertCount": number
    // 节点路径
    "nodePath": string
    // 转换后文件夹地址
    "resultUrl": string
}
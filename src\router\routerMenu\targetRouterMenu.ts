/*
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-12-06 14:50:42
 * @LastEditors: 小圆
 */
export default {
  path: 'target',
  name: 'target',
  component: resolve => require(['../../pages/target/index.vue'], resolve),
  children: [
    {
      path: 'targetResult',
      name: 'targetResult',
      component: resolve => require(['../../pages/target/result/new.vue'], resolve),
    },
    {
      path: 'targetOnline',
      name: 'targetOnline',
      component: resolve => require(['../../pages/target/online/index.vue'], resolve),
    },
    {
      path: 'targetScore',
      name: 'targetScore',
      component: resolve => require(['../../pages/target/score/new.vue'], resolve),
    },
    {
      path: 'targetFraction',
      name: 'targetFraction',
      component: resolve => require(['../../pages/target/fraction/index.vue'], resolve),
    },
    {
      path: 'targetNewExam',
      name: 'targetNewExam',
      component: resolve => require(['../../pages/target/newExam/index.vue'], resolve),
    },
  ],
};

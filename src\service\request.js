import axios from 'axios'
import { Message } from '@iclass/element-ui'
import baseConf from './config.17.js'
import { getToken } from './auth'
// create an axios instance
const service = axios.create(baseConf)

// 请求拦截
service.interceptors.request.use((config) => {
  const token = getToken()
  if (token) { // 判断是否存在token，如果存在的话，则每个http header都加上token
    config.headers['token'] = config.headers['token'] || token
  }
  return config

}, (error) => {
  return Promise.reject(error)
})

// response interceptor
service.interceptors.response.use(
  response => {
    const res = response.data

    // if the custom code is not 1, it is judged as an error.
    if (res.code !== 1) {
      // Message({
      //   message: res.msg || 'Error',
      //   type: 'error',
      //   duration: 5 * 1000
      // })

      // 未登录
      if (res.code === -100 || res.code === -101) {
        // Vue.$router
        // window.location.href = '/admin/login'
        // router.push('/login')
        return Promise.reject(new Error(res.msg || 'Error'))
      } else {

      }
      return Promise.reject(new Error(res.msg || 'Error'))
    } else {
      return res.data
    }
  },
  error => {
    console.log('err' + error) // for debug
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service

/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2020-03-16 13:54:18
 * @LastEditors: 小圆
 * @LastEditTime: 2025-01-17 14:58:50
 */
import { ElNotification } from '@iclass/element-ui/types/notification';
import { ElMessage } from '@iclass/element-ui/types/message';
import { ElLoadingComponent, LoadingServiceOptions } from '@iclass/element-ui/types/loading';
import { ElMessageBox, ElMessageBoxShortcutMethod } from '@iclass/element-ui/types/message-box';
import * as Echarts from 'echarts';
import {
    ArrayUnique,
    ArrSort,
    BodyOverFlow,
    CefMsg,
    ClearHttpRequestingList,
    CookieSave,
    CreatCompare,
    DeepClone,
    FormatDate,
    FormatStrToDate,
    GetBrowser,
    GetQueryString,
    GetQuesType,
    GetQuesTypeId,
    GetSortType,
    GetViewportSize,
    HttpAxios,
    IsJson,
    IsObjective,
    KatexUpdate,
    LocalSave,
    Lodash,
    MathJaxUpdate,
    OrderByTime,
    ParentIndexOf,
    RemoveDuplicatedItem,
    SectionToChinese,
    SessionSave,
    SortBy,
    UUid,
} from '@/utils';
type AlginH = 'left' | 'center' | 'right';
type AlginV = 'top' | 'middle' | 'bottom';

interface AlginPos {
    x: number;
    y: number;
}

declare module '@iclass/element-ui/types/notification' {
    interface ElNotification {
        closeAll: Function;
    }
}

declare module '@iclass/element-ui/types/message-box' {
    interface ElMessageBoxShortcutMethod {
        (message: string, title: string, options?: ElMessageBoxOptions): Promise<any>;
        (message: string, options?: ElMessageBoxOptions): Promise<MessageBoxData>;
    }
}

declare module 'vue/types/vue' {
    interface Vue {
        $http: HttpAxios;
        $clearHttpRequestingList: ClearHttpRequestingList;
        $cefMsg: CefMsg;
        $uuid: UUid;
        $_: Lodash;
        $getQueryString: GetQueryString;
        $sectionToChinese: SectionToChinese;
        $getSortType: GetSortType;
        $creatCompare: CreatCompare;
        $cookieSave: CookieSave;
        $localSave: LocalSave;
        $sessionSave: SessionSave;
        $getQuesType: GetQuesType;
        $getQuesTypeId: GetQuesTypeId;
        $isObjective: IsObjective;
        $katexUpdate: KatexUpdate;
        $mathJaxUpdate: MathJaxUpdate;
        $getBrowser: GetBrowser;
        $parentIndexOf: ParentIndexOf;
        $deepClone: DeepClone;
        $getViewportSize: GetViewportSize;
        $sortBy: SortBy;
        $arrSort: ArrSort;
        $arrayUnique: ArrayUnique;
        $removeDuplicatedItem: RemoveDuplicatedItem;
        $formatDate: FormatDate;
        $formatStrToDate: FormatStrToDate;
        $bodyOverFlow: BodyOverFlow;
        $orderByTime: OrderByTime;
        $isJson: IsJson;
        $message: ElMessage;
        $msgbox: ElMessageBox;
        $notify: ElNotification;
        $alert: ElMessageBoxShortcutMethod;
        $confirm: ElMessageBoxShortcutMethod;
        $prompt: ElMessageBoxShortcutMethod;
        $loading: (options: LoadingServiceOptions) => ElLoadingComponent;
        $bus: Vue;
        $echarts: typeof Echarts;
    }
}

declare module 'vue-router/types/router' {
    interface VueRouter {
        hasPathName(pathName: string): boolean;
        pathNames: string[];
    }
}

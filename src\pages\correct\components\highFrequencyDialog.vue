<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-08-19 10:37:32
 * @LastEditors: 小圆
-->
<template>
  <el-dialog title="高频作答" :visible.sync="dialogVisible" width="1200px" @closed="handleClose">
    <div class="answer-container">
      正确答案：<span class="answer"><span v-html="quesAnswer"></span> </span>
    </div>

    <div class="child-list-group">
      <el-button
        v-for="(item, index) in quesLineList"
        :key="index"
        :type="index === currentQuesLineIndex ? 'primary' : ''"
        @click="changeCurrentQuesLineIndex(index)"
      >
        {{ '空' + item.index }}
      </el-button>
    </div>

    <div class="custom-table">
      <!-- 表头 -->
      <div class="table-header">
        <div class="table-cell header-cell">学生作答</div>
        <div class="table-cell header-cell">数量</div>
        <div class="table-cell header-cell">操作</div>
      </div>

      <!-- 表格内容 -->
      <div class="table-body" v-if="tableData.length">
        <div v-for="(item, index) in tableData" :key="index" class="table-row">
          <div
            class="table-cell"
            :class="{ 'is-right': getQuesLine(item.quesId).answer.some(t => t == item.userAnswer) }"
          >
            <span v-html="getUserAnswer(item.userAnswer)"></span>
          </div>
          <div class="table-cell">{{ item.frequency }}</div>
          <div class="table-cell">
            <el-button
              class="correct-btn check"
              :class="{ active: getListItem(item)?.score == getFullScore(item.quesId) }"
              @click="setScore(item, getFullScore(item.quesId))"
            >
              <i class="el-icon-check"></i>
            </el-button>
            <el-button
              v-if="halfCheckEnabled"
              class="correct-btn warning"
              :class="{ active: getListItem(item)?.score == getFullScore(item.quesId) / 2 }"
              @click="setScore(item, getFullScore(item.quesId) / 2)"
            >
              <i class="el-icon-check"></i>
            </el-button>
            <el-button
              class="correct-btn error"
              :class="{ active: getListItem(item)?.score == 0 }"
              @click="setScore(item, 0)"
            >
              <i class="el-icon-close"></i>
            </el-button>
          </div>
        </div>
        <!-- 空数据提示 -->
        <div v-if="tableData.length === 0" class="empty-data">
          <div class="empty-text">暂无数据</div>
        </div>
      </div>

      <!-- 空数据提示 -->
      <div v-if="tableData.length === 0" class="empty-data">
        <div class="empty-text">暂无数据</div>
      </div>
    </div>

    <div class="btn-group">
      <el-button @click="dialogVisible = false">关闭</el-button>
      <el-button
        v-if="currentQuesLineIndex < quesLineList.length - 1"
        @click="changeCurrentQuesLineIndex(currentQuesLineIndex + 1)"
        type="primary"
        >下一空</el-button
      >
      <el-button v-else type="primary" :disabled="!tableData.length" :loading="saveLoading" @click="save"
        >提交</el-button
      >
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { getHighFrequencyAPI, saveFrequencyCorrectAPI } from '@/service/api';
import { Component, Prop, Vue } from 'vue-property-decorator';
import { PREVIEW_TYPE, SMART_TYPE, TeaCorrectQues } from '../types';
import { isSupportMathSubject } from '@/utils';

export interface HighFrequency {
  quesId: string;
  frequency: Frequency[];
}

export interface Frequency {
  quesId: string;
  userAnswer: string;
  frequency: number;
  isRight?: number;
}

@Component
export default class HighFrequencyDialog extends Vue {
  /** 当前题目 */
  @Prop({ default: () => null }) curQues: TeaCorrectQues | null;
  /** 作答模式 1:作答错误 2:作答正确 */
  @Prop({ default: PREVIEW_TYPE.ONLY_ERROR }) type;
  /** 批改状态 0:待复核 1:已复核 */
  @Prop({ default: SMART_TYPE.WAIT_REVIEW }) smartType;
  /** 是否启用半对 */
  @Prop({ default: false }) halfCheckEnabled: boolean;
  dialogVisible = true;
  iconCheckImg = require('@/assets/icon_check.png');
  iconErrorImg = require('@/assets/icon_error.png');
  iconHalfcheckImg = require('@/assets/icon_halfcheck.png');

  // 表格数据
  tableData: Frequency[] = [];
  // 当前题目空Id
  currentQuesLineId = '';
  // 当前题目索引
  currentQuesLineIndex = 0;

  // 存储修改的列表
  list: {
    childQueId: string;
    frequency: string;
    isRight: number;
    score: number;
  }[] = [];

  // 保存loading
  saveLoading = false;

  get quesLineList() {
    if (this.curQues.lineList) {
      return this.curQues.lineList;
    } else {
      return [];
    }
  }

  // 获取题目答案
  get quesAnswer() {
    if (this.curQues.lineList && this.curQues.lineList.length) {
      let lineList = this.curQues.lineList;
      if (this.currentQuesLineId) {
        lineList = [this.getQuesLine(this.currentQuesLineId)];
      }
      let str = '';
      lineList.forEach((lineItem, lineIndex) => {
        const answers = lineItem.answer;
        str += '<span>';
        str += `<span style="color: #333"> 空${lineItem.index}: </span>`;
        const answer = answers.map((item, index) => `<span> ${this.getAnswer(item)}</span>`).join('/');
        str += `${answer}`;
        if (lineIndex < lineList.length - 1) {
          str += `<span style="color: #333;"> ； </span>`;
        }
        str += '</span>';
      });
      return str;
    }
    const answers = this.curQues.answer;
    if (!answers) return '';
    if (Array.isArray(answers)) {
      return answers.map(t => this.getAnswer(t)).join('；');
    } else {
      return this.getAnswer(answers);
    }
  }

  getAnswer(answer) {
    if (answer == '$$' || answer == '\\$\\$') {
      return '<span style="color: red">（该题由老师批改）</span>';
    }
    if (!answer && answer !== 0) return '';
    if (this.isMathSubject()) {
      answer = answer.replace(/\\\(|\\\)/g, ''); // 去掉括号
      try {
        const res = MathJax.tex2svg(answer, { display: false });
        return `<span data-latex="${answer}" class="math-tex-svg">${res.outerHTML}</span>`;
      } catch (error) {
        return `<span style="color: red">${answer}</span>`;
      }
    }
    return answer;
  }

  // 获取作答
  getUserAnswer(userAnswer: string) {
    const answer = userAnswer;
    if (answer === '') return `<span style="color: red;font-weight: 700">未作答</span>`;
    if (answer == '$$' || answer == '\\$\\$') return answer;
    return this.getAnswer(userAnswer);
  }

  mounted() {
    this.currentQuesLineId = this.quesLineList[0].quesId;
    this.currentQuesLineIndex = 0;
    this.getHighFrequency();
  }

  isMathSubject() {
    return isSupportMathSubject(Number(this.$route.query.subjectId));
  }

  // 获取题目行
  getQuesLine(quesId) {
    return this.curQues.lineList.find(item => item.quesId == quesId);
  }

  // 获取高频作答
  async getHighFrequency() {
    const params: any = {
      schoolId: this.$sessionSave.get('schoolInfo').id,
      workId: this.$route.query.workId,
      userId: this.$sessionSave.get('loginInfo').id,
      quesId: this.curQues.quesId,
      type: this.type,
      smartType: this.smartType,
      childQueId: this.currentQuesLineId,
    };
    if (this.$route.query.classId) {
      params.classId = this.$route.query.classId;
      params.source = 1;
    }
    const res = await getHighFrequencyAPI(params);
    const tableData = res.data[0]?.frequency || [];
    this.tableData = tableData;
  }

  // 获取题目总分
  getFullScore(quesId) {
    return this.getQuesLine(quesId).score;
  }

  getListItem(item: Frequency) {
    return this.list.find(t => t.childQueId == item.quesId && t.frequency == item.userAnswer);
  }

  // 设置正确状态
  setScore(item: Frequency, score: number) {
    const listItem = this.getListItem(item);
    const fullScore = this.getFullScore(item.quesId);
    if (listItem) {
      if (score == listItem.score) {
        this.list = this.list.filter(t => {
          return !(item.quesId == t.childQueId && item.userAnswer == t.frequency);
        });
      } else {
        listItem.score = score;
        listItem.isRight = score == fullScore ? 1 : 0;
      }
    } else {
      this.list.push({
        childQueId: item.quesId,
        frequency: item.userAnswer,
        isRight: score == fullScore ? 1 : 0,
        score: score,
      });
    }
  }

  async save() {
    const params: any = {
      schoolId: this.$sessionSave.get('schoolInfo').id as string,
      workId: this.$route.query.workId as string,
      quesId: this.curQues.quesId,
      userId: this.$sessionSave.get('loginInfo').id as string,
      type: this.type,
      smartType: this.smartType,
      list: this.list,
    };
    if (this.list.length === 0) {
      this.$message.warning('请修改学生作答');
      return;
    }
    if (this.$route.query.classId) {
      params.classId = this.$route.query.classId;
      params.source = 1;
    }
    this.saveLoading = true;
    try {
      const res = await saveFrequencyCorrectAPI(params);
      this.$message.success('提交成功');
      this.$emit('submit-success');
    } catch (error) {
    } finally {
      this.saveLoading = false;
    }
  }

  handleClose() {
    this.dialogVisible = false;
    this.$emit('closed');
  }

  // 改变当前题目空
  changeCurrentQuesLineIndex(index) {
    this.currentQuesLineIndex = index;
    this.currentQuesLineId = this.quesLineList[index].quesId;
    this.getHighFrequency();
  }
}
</script>

<style scoped lang="scss">
.answer-container {
  overflow-x: auto;
  margin-bottom: 10px;
  .answer {
    color: #27991f;
    border: 0;
  }
}

.child-list-group {
  margin-bottom: 10px;
}

.custom-table {
  width: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;

  .table-header {
    display: flex;
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;

    .header-cell {
      font-weight: 600;
      color: #909399;
      background-color: #f5f7fa;
    }
  }

  .table-body {
    .table-row {
      display: flex;
      border-bottom: 1px solid #ebeef5;
      transition: background-color 0.3s;

      &:hover {
        background-color: #f5f7fa;
      }

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .table-cell {
    flex: 1;
    padding: 12px 15px;
    border-right: 1px solid #ebeef5;
    min-height: 48px;
    overflow: auto;

    &.is-right {
      color: #27991f;
    }

    &:last-child {
      border-right: none;
    }

    // 第一列（学生作答）占更多空间
    &:first-child {
      flex: 5;
    }

    // 第二列（数量）居中显示
    &:nth-child(2) {
    }

    // 第三列（操作）按钮样式
    &:last-child {
      gap: 8px;
    }
  }

  .empty-data {
    padding: 40px 0;
    text-align: center;

    .empty-text {
      color: #909399;
      font-size: 14px;
    }
  }
}

.correct-btn {
  display: inline-block;
  padding: 0;
  margin-left: 8px;
  width: 32px;
  height: 32px;
  line-height: 32px;
  font-size: 18px;
  background: #f8f9ff;
  border: 1px solid #e5e5e5;
  border-radius: 16px;

  color: #b3b3b3;
  cursor: pointer;

  &.active {
    color: #fff;
    border: 0;
  }

  &.check {
    &.active {
      background: #67c23a;
    }
  }

  &.error {
    &.active {
      background: #f94e4e;
    }
  }

  &.warning {
    .el-icon-check {
      position: relative;
      &::after {
        content: '|';
        position: absolute;
        left: 9px;
        top: 2px;
        transform: rotate(-45deg);
        font-size: 12px;
      }
    }

    &.active {
      background: #e6a23c;
    }
  }

  &:first-child {
    margin-left: 0;
  }
}

.btn-group {
  margin-top: 10px;
  text-align: center;
}
</style>

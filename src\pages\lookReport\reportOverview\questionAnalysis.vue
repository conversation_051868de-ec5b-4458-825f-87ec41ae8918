<template>
  <div class="questionAnalysis" v-loading="isLoading">
    <div class="header">
      <div class="header--left">
        <div class="titleLine">大小题分析</div>
        <el-popover placement="bottom" width="400" trigger="click" popper-class="popover" v-model="isPopoverVisible">
          <el-checkbox-group class="checkbox-type-group" v-model="tempCheckTypeList" @change="handleCheckboxChange">
            <el-checkbox class="checkbox" v-for="item in typeList" :key="item.value" :label="item.label" size="small">{{
              item.label
            }}</el-checkbox>
          </el-checkbox-group>
          <div class="validation-tip" v-if="showValidationTip">
            <i class="el-icon-warning"></i>
            <span>大题分析和小题分析必须至少选择一个</span>
          </div>
          <div class="popover-footer">
            <el-button size="small" @click="isPopoverVisible = false">取消</el-button>
            <el-button type="primary" size="small" @click="handleCheckType">确定</el-button>
          </div>
          <el-button class="filtrate-btn" slot="reference" type="text"
            >指标筛选 <i class="el-icon-arrow-down"></i
          ></el-button>
        </el-popover>
        <span class="tip red">注：平均分低于年级标红显示</span>
      </div>
      <div class="header--right">
        <el-button class="button export-button" type="primary" @click="isDialogVisible = true">导出</el-button>
      </div>
    </div>

    <el-tooltip placement="bottom-start" v-if="doQuesList.length">
      <div slot="content">
        <div v-html="getDoQuesChoiceTipHtml()"></div>
      </div>
      <span class="mixin-dochoice-tip">{{ getDoQuesChoiceTipText() }}</span>
    </el-tooltip>
    <div class="main" v-if="tableData.length">
      <el-table
        ref="elTable"
        class="el-table--group"
        style="width: 100%"
        stripe
        :data="getTableData"
        :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
        :tree-props="{ children: 'childs', hasChildren: 'hasChildren' }"
        v-sticky-table="0"
        v-drag-table
        row-key="tQuesNo"
        :expand-row-keys="expandRowKeys"
        @expand-change="handleExpandChange"
      >
        <el-table-column
          header-align="center"
          align="left"
          label="题号"
          prop="quesNoDesc"
          fixed
          :min-width="140"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span class="click-element" @click="toggleRowExpansion(scope.row)">
              {{ scope.row.quesNoDesc }}
              <span v-if="getDoChoiceQuesCountTextByQuesNo(scope.row.tQuesNo)">
                (<span class="mixin-dochoice-text">{{ getDoChoiceQuesCountTextByQuesNo(scope.row.tQuesNo) }}</span
                >)
              </span>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkTypeList.includes('题型')"
          align="center"
          label="题型"
          prop="quesType"
          fixed
          :min-width="100"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span>
              {{ scope.row.quesType || '--' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkTypeList.includes('分值')"
          align="center"
          label="分值"
          prop="fullScore"
          fixed
          :min-width="100"
          :show-overflow-tooltip="true"
        >
        </el-table-column>

        <template v-for="item in tableHeader">
          <el-table-column :key="item.id" :label="item.name" align="center">
            <el-table-column
              v-if="checkTypeList.includes('满分率')"
              align="center"
              label="满分率"
              :prop="`data[${item.id}].fullRate`"
              :min-width="85"
              :show-overflow-tooltip="true"
            >
              <template #default="scope">
                <span>{{
                  scope.row.data[item.id].totalNum == 0 ? '--' : formateRate(scope.row.data[item.id].fullRate)
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="checkTypeList.includes('零分率')"
              align="center"
              label="零分率"
              :prop="`data[${item.id}].zeroRate`"
              :min-width="85"
              :show-overflow-tooltip="true"
            >
              <template #default="scope">
                <span>{{
                  scope.row.fullScore === 0 || scope.row.data[item.id].totalNum == 0
                    ? '--'
                    : formateRate(scope.row.data[item.id].zeroRate)
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="checkTypeList.includes('难度')"
              align="center"
              label="难度"
              :prop="`data[${item.id}].difficulty`"
              :min-width="85"
              :show-overflow-tooltip="true"
            >
              <template #default="scope">
                <span> {{ scope.row.data[item.id].totalNum == 0 ? '--' : scope.row.data[item.id].difficulty }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="checkTypeList.includes('区分度')"
              align="center"
              label="区分度"
              :prop="`data[${item.id}].distinguish`"
              :min-width="85"
              :show-overflow-tooltip="true"
            >
              <template #default="scope">
                <span> {{ scope.row.data[item.id].totalNum == 0 ? '--' : scope.row.data[item.id].distinguish }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="checkTypeList.includes('标准差')"
              align="center"
              label="标准差"
              :prop="`data[${item.id}].standard`"
              :min-width="85"
              :show-overflow-tooltip="true"
            >
              <template #default="scope">
                <span>
                  {{
                    scope.row.data[item.id].totalNum == 0 || scope.row.data[item.id].standard == -1
                      ? '--'
                      : scope.row.data[item.id].standard
                  }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              v-if="checkTypeList.includes('均分')"
              align="center"
              label="均分"
              :prop="`data[${item.id}].avgScore`"
              :min-width="85"
              :show-overflow-tooltip="true"
            >
              <template #default="scope">
                <span
                  :class="{
                    red: scope.row.data['all']?.avgScore > scope.row.data[item.id].avgScore,
                  }"
                  >{{ scope.row.data[item.id].totalNum == 0 ? '--' : scope.row.data[item.id].avgScore }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              v-if="checkTypeList.includes('得分率')"
              align="center"
              label="得分率"
              :prop="`data[${item.id}].scoreRate`"
              :min-width="85"
              :show-overflow-tooltip="true"
            >
              <template #default="scope">
                <span>{{
                  scope.row.data[item.id].totalNum == 0 ? '--' : formateRate(scope.row.data[item.id].scoreRate)
                }}</span>
              </template>
            </el-table-column>
          </el-table-column>
        </template>
      </el-table>
      <div ref="qsChartRef" class="qs-chart" style="width: 100%; height: 500px"></div>
    </div>
    <no-data v-else />

    <CheckSubClassDialog
      v-if="isDialogVisible"
      :canIndicator="true"
      :indicatorList="typeList"
      :defaultIndicators="checkTypeList"
      @closed="isDialogVisible = false"
      @confirm="exportQs"
    />
  </div>
</template>

<script lang="ts">
import { listQsAnalysis } from '@/service/pstat';
import { Component, Mixins, Prop, Vue, Watch } from 'vue-property-decorator';

import NoData from '@/components/noData.vue';
import CheckSubClassDialog from './components/CheckSubClassDialog.vue';
import UserRole from '@/utils/UserRole';
import { ElTable } from '@iclass/element-ui/types/table';
import DochoiceMixin from '../mixin/DochoiceMixin.vue';
import { indicatorManager } from '@/utils/examReportUtils';

export interface IFilterData {
  classId: string;
  classIds: any[];
  subjectId: string;
  phaseId: number;
  xfId: number;
  subjectName: string;
  abPaper: string;
}

@Component({
  components: {
    NoData,
    CheckSubClassDialog,
  },
})
export default class QuestionAnalysis extends Mixins(DochoiceMixin) {
  $refs: {
    elTable: ElTable;
    qsChartRef: HTMLDivElement;
  };
  @Prop({
    type: Object,
    default: () => {},
  })
  filterData: IFilterData;

  // 表格数据
  tableData = [];
  // 表格表头
  tableHeader = [];
  // 是否加载
  isLoading: boolean = false;
  // 默认展开行
  expandRowKeys: Array<any> = [];
  // 指标列表
  typeList = indicatorManager.questionAnalysisIndicatorList;
  // 筛选类型
  checkTypeList = ['大题分析', '小题分析', '题型', '分值', '标准差', '均分', '得分率'];
  // 暂存筛选类型
  tempCheckTypeList = ['大题分析', '小题分析', '题型', '分值', '标准差', '均分', '得分率'];
  // 是否显示弹出框
  isPopoverVisible: boolean = false;
  // 是否显示导出弹窗
  isDialogVisible: boolean = false;
  // 是否显示验证提示
  showValidationTip: boolean = false;

  // 当前报告详情
  get reportDetail(): IExamReportInfo {
    return this.$sessionSave.get('reportDetail');
  }

  get getTableData() {
    let data = JSON.parse(JSON.stringify(this.tableData));
    // 如果没有包含大题分析，则去除大题，但需要显示大题中的小题
    if (!this.checkTypeList.includes('大题分析')) {
      data = this.flattenData(data);
    }

    // 如果没有包含小题分析，则去除小题
    if (!this.checkTypeList.includes('小题分析')) {
      data = this.removeChilds(data);
    }

    return data;
  }

  // 扁平化数据，将大题的小题提升为顶级节点
  flattenData(data) {
    const result = [];
    data.forEach(item => {
      if (item.childs && item.childs.length > 0) {
        // 如果有子题，则将子题添加到结果中，不添加父题
        result.push(...item.childs);
      } else {
        // 如果没有子题，说明这个就是小题，直接添加
        result.push(item);
      }
    });
    return result;
  }

  // 移除所有子题，只保留大题
  removeChilds(data) {
    return data
      .map(item => {
        const newItem = { ...item };
        // 如果有子题，则移除子题
        if (newItem.childs && newItem.childs.length > 0) {
          delete newItem.childs;
        }
        return newItem;
      })
      .filter(item => {
        // 过滤掉原本是小题的节点（即在原始数据中没有childs或childs为空的节点）
        const originalItem = this.tableData.find(orig => orig.tQuesNo === item.tQuesNo);
        return originalItem && originalItem.childs && originalItem.childs.length > 0;
      });
  }

  @Watch('filterData', {
    deep: true,
  })
  updateFilter(val: IFilterData) {
    this.tableData = [];
    this.tableHeader = [];
    this.getListQsAnalysis();
    this.getPaperChoice(this.reportDetail.examId, this.filterData.subjectId);
  }

  @Watch('isPopoverVisible')
  async updateIsPopoverVisible(val: boolean) {
    this.tempCheckTypeList = JSON.parse(JSON.stringify(this.checkTypeList));
    // 重置验证提示
    this.showValidationTip = false;
  }

  formateRate(val) {
    return val + '%';
  }

  mounted() {
    this.checkTypeList = indicatorManager.getIndicator('questionAnalysis');
    this.getListQsAnalysis();
    this.getPaperChoice(this.reportDetail.examId, this.filterData.subjectId);
  }

  async getListQsAnalysis() {
    try {
      this.isLoading = true;

      if (this.$sessionSave.get('innerClassList').length == 0) {
        this.tableData = [];
        this.tableHeader = [];
        this.isLoading = false;
        return;
      }

      const res = await listQsAnalysis({
        examId: this.reportDetail.examId,
        subjectId: this.filterData.subjectId,
        classId: this.filterData.classIds.join(','),
        abPaper: this.filterData.abPaper,
        v: this.reportDetail.v,
      });
      this.tableHeader = res.data.qsHead;
      this.tableData = res.data.rows;
      let classList = this.$sessionSave.get('innerClassList');
      let classIds = classList ? classList.map(item => item.id) : [];
      this.tableHeader = this.tableHeader.filter(item => item.id == 'all' || classIds.includes(item.id));
      this.isLoading = false;

      this.$nextTick(() => {
        this.initQsChart();
      });
    } catch (error) {
      console.error(error);
      this.tableData = [];
      this.tableHeader = [];
      this.isLoading = false;
    }
  }

  // 初始化小题图表
  initQsChart() {
    const qsChartRef = this.$refs.qsChartRef;
    if (!qsChartRef) return;
    const chart = this.$echarts.init(qsChartRef);

    const qsList = this.findAllQs();

    chart.setOption({
      tooltip: {
        trigger: 'item',
        valueFormatter: value => value + '%',
        height: '50px',
        padding: [15, 15],
        enterable: true, //滚动条
        extraCssText: 'max-width:60%;max-height:83%; overflow: auto; ', //滚动条
        //改变提示框的位置 不超出屏幕显示
        position: function (point, params, dom, rect, size) {
          //其中point为当前鼠标的位置，
          //size中有两个属性：viewSize和contentSize，分别为外层div和tooltip提示框的大小
          // 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
          // 提示框位置
          var x = 0; // x坐标位置
          var y = 0; // y坐标位置
          // 当前鼠标位置
          var pointX = point[0];
          var pointY = point[1];
          // 提示框大小
          var boxWidth = size.contentSize[0];
          var boxHeight = size.contentSize[1];
          // boxWidth > pointX 说明鼠标左边放不下提示框
          if (boxWidth > pointX) {
            x = 5;
          } else {
            // 左边放的下
            x = pointX - boxWidth;
          }
          // boxHeight > pointY 说明鼠标上边放不下提示框
          if (boxHeight > pointY) {
            y = 5;
          } else {
            // 上边放得下
            y = pointY - boxHeight;
          }
          return [x, y];
        },
      },
      legend: {
        data: this.tableHeader.map(item => item.name),
      },
      radar: {
        shape: 'circle',
        indicator: qsList.map(item => ({
          name: item.quesNoDesc,
        })),
      },
      series: [
        {
          name: 'Budget vs spending',
          type: 'radar',
          data: this.tableHeader.map(item => ({
            value: qsList.map(qs => qs.data[item.id].scoreRate),
            name: item.name,
          })),
        },
      ],
    });
  }

  // 获取所有小题
  findAllQs() {
    const qsList = [];
    const getLastLevelQs = data => {
      data.forEach(item => {
        if (item.childs && item.childs.length) {
          getLastLevelQs(item.childs);
        } else {
          qsList.push(item);
        }
      });
    };
    getLastLevelQs(this.tableData);
    return qsList;
  }

  // 导出大小题分析
  async exportQs({ subjectCheckList, indicatorCheckList }) {
    let role = '';
    if (!UserRole.isOperation) {
      const { year, campusCode } = this.$sessionSave.get('reportDetail');
      const map = await UserRole.utils.getRoleSubjectClassMap(
        year,
        campusCode,
        this.$sessionSave.get('reportType') == 'school' ? true : false
      );
      role = JSON.stringify(map);
    }

    const params: any = {
      examId: this.reportDetail.examId,
      subjectId: subjectCheckList.join(','),
      filter: indicatorCheckList.join(','),
      role: role,
      abPaper: this.filterData.abPaper,
      v: this.reportDetail.v,
    };
    const urlSearch = new URLSearchParams(params);
    let url = process.env.VUE_APP_KKLURL + `/pstat/_/export-qs-analysis?${urlSearch.toString()}`;
    window.open(url);
    this.isDialogVisible = false;
  }

  toggleRowExpansion(row) {
    this.$refs.elTable.toggleRowExpansion(row);
  }

  // 处理checkbox变化
  handleCheckboxChange() {
    const hasBigQuesAnalysis = this.tempCheckTypeList.includes('大题分析');
    const hasSmallQuesAnalysis = this.tempCheckTypeList.includes('小题分析');
    
    // 如果两个都没选择，显示验证提示
    this.showValidationTip = !hasBigQuesAnalysis && !hasSmallQuesAnalysis;
  }

  handleCheckType() {
    // 验证大题分析和小题分析必须至少选择一个
    const hasBigQuesAnalysis = this.tempCheckTypeList.includes('大题分析');
    const hasSmallQuesAnalysis = this.tempCheckTypeList.includes('小题分析');
    
    if (!hasBigQuesAnalysis && !hasSmallQuesAnalysis) {
      this.$message.warning('大题分析和小题分析必须至少选择一个');
      return;
    }
    
    this.checkTypeList = JSON.parse(JSON.stringify(this.tempCheckTypeList));
    this.isPopoverVisible = false;
    indicatorManager.setIndicator('questionAnalysis', this.checkTypeList);
  }

  // 缓存打开的题目
  handleExpandChange(row, isExpand) {
    if (isExpand) {
      this.expandRowKeys.push(row.tQuesNo);
    } else {
      this.expandRowKeys = this.expandRowKeys.filter(item => item != row.tQuesNo);
    }
  }
}
</script>

<style scoped lang="scss">
.red {
  color: red;
}

.tip {
  color: red;
  font-size: 14px;
  margin-left: 10px;
}

.questionAnalysis {
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .titleLine {
    display: inline-block;
    position: relative;
    height: 54px;
    line-height: 54px;
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;
    padding-left: 16px;

    &:before {
      content: '';
      width: 6px;
      height: 24px;
      background: #409eff;
      border-radius: 3px;
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      margin: auto;
    }
  }
}

.popover-footer {
  margin-top: 10px;
  text-align: right;
}

.filtrate-btn {
  margin-left: 10px;
}

.qs-chart {
  margin-top: 20px;
  height: 500px;
  width: 100%;
}

.checkbox-type-group {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  column-gap: 10px;
  row-gap: 10px;
}

.validation-tip {
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  color: #f56c6c;
  font-size: 12px;
  display: flex;
  align-items: center;
  
  i {
    margin-right: 5px;
    font-size: 14px;
  }
}
</style>

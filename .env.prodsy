###
 # @Description:
 # @Author: l<PERSON><PERSON><PERSON> <EMAIL>
 # @Date: 2024-07-02 08:30:38
 # @LastEditors: 小圆
 # @LastEditTime: 2024-11-25 17:29:25
###
# just a flag

NODE_ENV = 'production'
ENV = 'production'

# base api
VUE_APP_BASE_API = https://service.iclass30.com
VUE_APP_BASE_URL = /bigdata/
VUE_APP_CUT_URL = https://service.iclass30.com
VUE_APP_FS_URL = https://fs.iclass30.com
VUE_APP_KKLURL = https://kklservice.iclass30.com
VUE_APP_CARDURL = https://gc.iclass30.com/mc/
VUE_APP_ABCARDURL = /mc/abCard
VUE_APP_CUTPAPERURL = https://cuttestpaper.iclass30.com/
VUE_APP_AUDITURL = https://gc.iclass30.com/book-audit/home
VUE_APP_MATH_CDN = https://latexcdn.iclass30.com
VUE_WX = https://homeworkservice.iclass30.com
VUE_APP_LLKTURL = https://gc.iclass30.com
VUE_APP_17 = https://homeworkservice.iclass30.com
VUE_APP_TESTBANK = https://homeworkservice.iclass30.com
VUE_APP_DOMAIN = gc.iclass30.com
VUE_APP_SSO_PATH = https://fs.iclass30.com/aliba/plug/sso/sso-client-sdk.js
VUE_APP_APPID = wjhz2xm6hdooelrwo9nm
VUE_APP_XUEBAN_API = https://scanimage.iclass30.com
VUE_APP_CORRECT_URL = https://zy.iclass30.com/#/webCorrect
VUE_APP_BACKSTAGE = https://console.iclass30.com/base/schoolMain
VUE_APP_ZP = https://c30zp.iclass30.com/
VUE_APP_POINTS = https://scanimage.iclass30.com/scan_image/view_paper_mark
VUE_APP_SCAN_MOBILE = https://gc.iclass30.com/scan-task-mobile
VUE_APP_SCAN_SOCKET = wss://kklservice.iclass30.com/psocket/socket?token=PsQsE7LoceMEV4D1203F2HpcaLH1XR6/Wz5acRVl+vm7XNs9bM7poJ7zSj1DwoSd
VUE_APP_HOMEWORK = https://homeworkreport.iclass30.com
VUE_APP_ZUOWEN = https://zuowen.iclass30.com
VUE_APP_CHANNEL = sy

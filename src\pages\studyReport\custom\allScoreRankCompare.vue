<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-04-18 09:25:16
 * @LastEditors: 小圆
-->

<template>
  <div>
    <div v-loading="tableLoading">
      <base-table
        v-if="tableData.length"
        :data="tableData"
        :column="tableColumns"
        :span-method="handleSpanMethod"
        v-bind="getTableAttr()"
        v-drag-table
      ></base-table>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>

    <!-- 总分分布分段（人数） -->
    <div style="margin-top: 20px; width: 100%; height: 450px" id="TotalNumberChart"></div>
    <!-- 总分分布分段（比例） -->
    <div style="margin-top: 20px; width: 100%; height: 450px" id="TotalRateChart"></div>
  </div>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';
import {
  getDefaultGrid,
  getDefaultLegend,
  getDefaultPercentAxis,
  getDefaultTitle,
  getDefaultToolBox,
} from '../plugins/DefaultEchartsOption';

@Component({
  components: {},
})
export default class Index extends Mixins(TableCommon) {
  // 总分分布分段（人数）图表
  totalNumberChart: EChartsType = null;
  // 总分分布分段（比例）图表
  totalRateChart: EChartsType = null;

  /**
   * 合并单元格句柄方法
   */
  handleSpanMethod({
    row, // 行
    column, // 列
    rowIndex, // 行索引
    columnIndex, // 列索引
  }) {
    const rowspanArr = this.formatRowspanAndColspan(this.tableData, 'scoreRange');
    if (columnIndex === 0) {
      return {
        rowspan: rowspanArr[rowIndex],
        colspan: 1,
      };
    }
  }

  callbackGetTableData() {
    this.renderTotalNumberChart();
    this.renderTotalRateChart();
  }

  renderTotalNumberChart() {
    if (this.totalNumberChart) {
      this.totalNumberChart.dispose();
      this.totalNumberChart = null;
    }

    const dom = document.getElementById('TotalNumberChart');
    this.totalNumberChart = this.$echarts.init(dom);

    const series = [];
    this.tableColumns.forEach(col => {
      if (col.prop !== 'scoreRange' && col.children?.length) {
        let prop = col.children[0].prop;
        let data = [];
        this.tableData.forEach(item => {
          data.push(item[prop]);
        });
        series.push({
          name: col.title,
          data: data,
          type: 'line',
        });
      }
    });

    let option: EChartsOption = {
      grid: getDefaultGrid(),
      title: { ...getDefaultTitle(), text: '总分分布分段（人数）' },
      legend: getDefaultLegend(),
      tooltip: {
        trigger: 'axis',
        valueFormatter: value => {
          return value + '人';
        },
      },
      toolbox: getDefaultToolBox(),
      xAxis: {
        type: 'category',
        data: this.tableData.map(item => item.scoreRange),
        boundaryGap: false,
        axisLabel: {},
      },
      yAxis: { type: 'value' },
      series: series,
    };

    this.totalNumberChart.setOption(option);
  }

  renderTotalRateChart() {
    if (this.totalRateChart) {
      this.totalRateChart.dispose();
      this.totalRateChart = null;
    }

    const dom = document.getElementById('TotalRateChart');
    this.totalRateChart = this.$echarts.init(dom);

    const series = [];
    this.tableColumns.forEach(col => {
      if (col.prop !== 'scoreRange' && col.children?.length) {
        let prop = col.children[1].prop;
        let data = [];
        this.tableData.forEach(item => {
          data.push(parseFloat(item[prop]) || 0);
        });
        series.push({
          name: col.title,
          data: data,
          type: 'line',
        });
      }
    });

    let option: EChartsOption = {
      grid: getDefaultGrid(),
      title: { ...getDefaultTitle(), text: '总分分布分段（比例）' },
      legend: getDefaultLegend(),
      tooltip: {
        trigger: 'axis',
        valueFormatter: value => {
          return value + '%';
        },
      },
      toolbox: getDefaultToolBox(),
      xAxis: {
        type: 'category',
        data: this.tableData.map(item => item.scoreRange),
        boundaryGap: false,
        axisLabel: {},
      },
      yAxis: getDefaultPercentAxis(),
      series: series,
    };

    this.totalRateChart.setOption(option);
  }
}
</script>

<style scoped lang="scss"></style>

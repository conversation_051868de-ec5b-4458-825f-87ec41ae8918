<template>
  <div
      class="paper-container"
      v-loading="loading"
      :element-loading-text="loadingTest"
      element-loading-spinner="el-icon-loading"
  >
    <div class="error-back el-icon-back">
      <span class="back-word" @click="backReport">
        返回 <span style="margin-left: 10px; color: #dcdfe6">| </span></span
      >
      <span class="back-examname" :title="headContent">{{ headContent }}</span>
    </div>
    <div class="error-container" v-if="taskList.length">
      <div class="error-left">
        <el-tabs
            v-model="activeName"
            @tab-click="tabClick"
            class="scan-student-error-tabs"
            type="card"
        >
          <el-tab-pane v-for="(item, index) in errorTypeList" :key="index" :name="item.type">
            <span slot="label">
              {{ item.text }}
              <el-badge
                  v-if="allErrorNums[item.type].num != 0"
                  :value="allErrorNums[item.type].num"
              ></el-badge>
            </span>
            <!-- 错误提示 -->
            <error-tips :currentError="activeName" :errorCount="(allErrorNums[item.type].num + allErrorNums[item.type].waitedNum)" :examErrorData="examErrorData" @update-points="updatePoints"></error-tips>
          </el-tab-pane>
        </el-tabs>
        <!-- 题目异常图片 -->
        <ques-error
            v-if="activeName == 'objective' || activeName == 'subject' || activeName == 'choice'"
            :errorData="quesErrorData"
            :pointsData="pointsData"
            :examInfo="examInfo"
            :currentError="activeName"
            :choiceScoreMap="choiceScoreMap"
            :taskIds="taskIds"
            :chooseDoCount="requestInfo?.doCount || 1"
            :cardType="cardType"
            @update-error="getErrorNums"
            ref="quesError"
        ></ques-error>
        <!-- 缺考异常 -->
         <batch-absent v-else-if="activeName == 'absent'"
            :examId="examInfo.examId"
            :handleInfo="allErrorNums[activeName]"
            :examNoInfo="examNoInfo"
            :requestInfo="requestInfo"
            @set-absent="setAbsent"
         >
         </batch-absent>
        <!-- 考号异常图片 -->
        <exam-error
            v-else
            :examErrorData="examErrorData"
            :firstImage="firstImage"
            :pointsData="pointsData"
            :currentError="activeName"
            @mark-image-error="makeImageError"
            @reverse-page="reversePage"
        >
        </exam-error>
        <template v-if="firstTaskId != ''">
          <el-button
              class="check-all-button operate-button"
              type="primary"
              size="medium"
              @click="checkAllPaper"
          >查看全部答卷
          </el-button
          >
          <el-button class="check-task-button operate-button" size="medium" @click="checkTask"
          >查看扫描批次
          </el-button
          >
          <el-button
              v-show="source == 3"
              class="publish-button operate-button"
              :type="isPublishScore ? 'success' : 'warning'"
              size="medium"
              @click="openPublishDialog"
          >{{ isPublishScore ? '成绩已发布' : '发布成绩' }}
          </el-button
          >
          <el-button
              class="check-insider-button operate-button"
              type="text"
              size="medium"
              @click="insider"
          ></el-button>
        </template>
      </div>
      <!-- 右侧操作区 -->
      <div class="error-right">
        <silder-set
            @change-type="changeHandleType"
            @change-page="changePage"
            @submit-exam-no="submitExamNo"
            @delete-paper="deletePaper"
            @start-loading="loading = true"
            @close-loading="closeLoading"
            @refesh-data="getErrorNums"
            @get-next-exam-no="onGetNextExamNo"
            @set-absent="setAbsent"
            @set-abpaper="setABPaper"
            :handleInfo="allErrorNums[activeName]"
            :pagination="pagination"
            :errorType="activeName"
            :isABCard="isABCard"
            :examInfo="examInfo"
            :pageNum="pointsData.page_num"
            ref="silderSet"
        ></silder-set>
      </div>
    </div>
    <div class="noScan" v-else>
      <img :src="noScanImg" alt=""/>
      <p class="text-center">请将学生答卷放入扫描仪进行扫描</p>
    </div>
    <!-- 发布成绩弹窗 -->
    <publish-score-dialog
        v-if="isPublishScoreDialog"
        :examInfo="publishScoreInfo"
        @close-publish="closePublish"
        @publish-score="publishScore"
    ></publish-score-dialog>
  </div>
</template>

<script>
import {mapGetters} from 'vuex';
import {ERRORCARD_TYPE, ERROR_TYPE} from '@/typings/scan';
import SilderSet from '@/components/scan/SilderSet.vue';
import ErrorTips from '@/components/scan/ErrorTips.vue';
import QuesError from '@/components/scan/QuesError.vue';
import batchAbsent from '@/components/scan/batchAbsent.vue';
import {
  getQuesScanImg,
  getErrorNums,
  getExamNoAPI,
  saveExamNoImgAPI,
  saveExamNoAPI,
  sendMergeMsgAPI,
  getBatchListAPI,
  deleteScanPageAPI,
  savePageAPI,
  markNormalAPI,
  getPersonalBookInfo,
  getPublishScoreStateAPI,
  getScanImageAPI,
  setAbsentAPI,
  setABPaperAPI,
  setImagePointError,
  getMergeStatus,
  examDelayMerge,
  imageCodeDetect
} from '@/service/pexam';
import {netPublishScore, publishScoreAPI} from '@/service/api';
import {getScanPaperPoints, getTbDetails} from '@/service/testbank';
import ExamError from '@/components/scan/ExamError.vue';
import PublishScoreDialog from '@/components/PublishScoreDialog.vue';
import {Loading} from '@iclass/element-ui';
import {GetScoreMap} from '@/service/xueban';
import {getQueryString} from '@/utils';
import {getImgUrl} from '@/utils/index';
import {replaceALiUrl} from '@/utils/common';
import {ICARD_STATE} from '@/typings/card';
import Batch from './batch.vue';

export default {
  name: 'scan-success',
  data() {
    return {
      paperId: this.$route.query.paperId,
      //考试信息
      examInfo: {
        examId: this.$route.query.personBookId,
        examName: this.$route.query.examName,
        paperNo: this.$route.query.paperNo,
        subjectName: this.$route.query.subjectName || '',
        schoolId: this.$route.query.schoolId,
        taskId: this.$route.query.taskId || '',
      },
      teacherId: getQueryString('teacherId') || '',
      //当前tab
      activeName: 'paper',
      //异常类型
      errorType: ERRORCARD_TYPE,
      //题目异常数据
      quesErrorData: [],
      //异常数量
      allErrorNums: {
        paper: {},
        page: {},
        point: {},
        exam: {},
        absent: {},
        objective: {},
        choice: {},
        subject: {},
        abCard: {}
      },
      errType: ERROR_TYPE,
      requestInfo: {
        status: 0, //处理状态 0 待处理，1 已处理
        quesNo: 0, // 题号
        examNoId: '', //考号Id
        idx: '', //页数
        stuNo: '', //学号
        taskId: '', //批次id
      },
      //考号信息
      examNoInfo: {},
      //分页
      pagination: {
        page: 1,
        limit: 40,
        total_rows: 0,
        page_count: 0,
      },
      fsUrl: process.env.VUE_APP_FS_URL,
      //图片分辨率
      imgDpi: 4.173,
      //考号异常数据
      examErrorData: [],
      //批次列表
      taskList: [],
      //当前选中的下一个考号
      nextExamNo: '',
      loading: false,
      loadingTest: '正在识别，请稍候…若长时间未完成，请刷新页面',
      //第一个考号异常的图片
      firstImage: '',
      //第一个批次id
      firstTaskId: '',
      // 坐标点数据
      pointsData: {},
      noScanImg: require('@/assets/scan/no-scan.png'),
      clickCount: 0,
      timeoutId: null,
      examReportId: '',
      //发布成绩确认弹窗
      isPublishScoreDialog: false,
      publishScoreInfo: {},
      //加载中状态
      scoreLoading: null,
      //未处理异常数
      errNums: 0,
      //是否发布了成绩
      isPublishScore: false,
      //3:手阅 4:网阅
      source: '',
      //pbook对应的学科名
      bookSubName: '',
      //多选题得分规则
      choiceScoreMap: {},
      taskIds: '',
      //合并数据状态
      mergeLoading: null,
      mergeTimes: 0,
      cardType: 0,
      abPaperDatas: [],
      paperNum: 1,
    };
  },
  computed: {
    //是否为题目异常
    isQuesError() {
      return this.activeName == 'subject' || this.activeName == 'objective' || this.activeName == 'choice';
    },
    headContent() {
      let subjectName = this.examInfo.subjectName || this.bookSubName;
      if (subjectName != '') {
        return `${this.examInfo.examName} （${subjectName}）`;
      } else {
        return this.examInfo.examName;
      }
    },
    errorTypeList() {
      const list = this.errorType.filter(item => {
        //选做题、ab卷无异常数则不显示
        if (this.allErrorNums[item.type].num == 0 && this.allErrorNums[item.type].waitedNum == 0 && ['abCard', 'choice'].includes(item.type)) {
          return false;
        }
        return item;
      })
      return list;
    }
  },
  components: {
    QuesError,
    batchAbsent,
    SilderSet,
    ErrorTips,
    ExamError,
    PublishScoreDialog,
    Batch,
  },
  watch: {
    //监听异常处理数据
    allErrorNums: {
      handler(newVal, oldVal) {
        let nums = 0;
        for (let key in newVal) {
          nums += newVal[key].num;
        }
        this.errNums = nums;
        let examNo = newVal.exam.num;
        //无异常发消息
        if (nums == 0 || examNo == 0) {
          this.handleComplete();
        }
      },
      deep: true,
      immediate: true,
    },
    activeName: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.nextExamNo = '';
          if (newVal == 'subject' || newVal == 'objective' || newVal == 'choice') {
            this.handelQuesError();
          } else {
            this.handelExamNo('');
          }
        }
      },
      // deep: true,
      // immediate: true,
    },
  },
  async created() {
    //获取坐标点
    await this.getTbDetails();
    //获取批次信息
    await this.getBatchList();
    //获取全部异常
    await this.initErrorNums();
    //获取考试信息
    await this.getPersonalBookInfo();
    this.activeName =
        Object.keys(this.allErrorNums).find(key => this.allErrorNums[key].num != 0) || 'paper';
    console.log('activeName', this.activeName);
    if (this.activeName != 'subject' && this.activeName != 'objective' && this.activeName != 'choice') {
      //考号、答卷、页码异常
      this.handelExamNo();
    } else {
      //题目
      this.handelQuesError();
    }
    await this.getScoreMap();
    this.examDelayMerge();
  },
  methods: {
    async getTbDetails() {
      const res = await getTbDetails({
        paperNo: this.examInfo.paperNo,
      });
      for (const item of res.data) {
        this.paperNum = Math.max(this.paperNum, item.paperNum);
        let paper = await getScanPaperPoints({
          paperNo: item.paperNo,
        });
        if (paper.code == 1) {
          const paperData = JSON.parse(paper.data);
          let pageQuestionList = []
          paperData.pages.forEach(page => {
            let pageQuestion = []
            page.forEach(ques => {
              if (ques.item_type === 0 || ques.item_type === 10) {
                return
              }

              ques.type = ques.item_type

              if (ques.item_type === 3) {
                ques.is_obj = true
                ques.answer_int = 0


                ques.list = ques.option_list.map(op => {
                  return {
                    'pos': op.map(v => parseFloat(v)),
                    'fill': false
                  }
                })
              } else {
                if (!ques.score_list) {
                  ques.score_list = []
                }
              }
              pageQuestion.push(ques)
            })
            pageQuestionList.push(pageQuestion)
          })
          paperData.pageQuestionList = pageQuestionList
          this.abPaperDatas[item.paperNo] = paperData;
          // this.abPaperDatas[item.paperNo] = JSON.parse(paper.data);
        }
      }

    },
    /**
     * @name:获取多选题得分规则
     */
    async getScoreMap() {
      GetScoreMap({paper_no: this.examInfo.paperNo})
          .then(res => {
            this.choiceScoreMap = res.data;
          })
          .catch(err => {
            console.log(err);
          });
    },
    async initErrorNums() {
      this.requestInfo.quesNo = '';
      this.requestInfo.examNoId = '';
      this.examNoInfo = {};
      this.examErrorData = [];
      this.$bus.$emit('get-image', '');
      let res = await getErrorNums({
        examId: this.examInfo.examId,
        taskId: this.examInfo.taskId || this.taskIds,
        schoolId: this.$route.query.schoolId || '',
        paperNum: this.paperNum
      });
      if (res.code == 1) {
        const keys = ['paper', 'page', 'point', 'exam', 'absent', 'objective', 'choice', 'subject', 'abCard'];
        keys.forEach(item => {
          this.allErrorNums[item] = res.data[item];
        });
      }
    },
    /**
     * @name:获取考试id
     */
    async getPersonalBookInfo() {
      let res = await getPersonalBookInfo({
        id: this.examInfo.examId,
      });
      if (res.code == 1) {
        this.examReportId = res.data.examId;
        this.source = res.data.source;
        this.bookSubName = res.data.subjectName || '';
        this.cardType = res.data.cardType || 0;
        this.isABCard = res.data.relateCardType == ICARD_STATE.abCard || res.data.relateCardType == ICARD_STATE.abPaper || res.data.relateCardType == ICARD_STATE.abPaperTwo;
      }
    },
    /**
     * @name:打开发布成绩弹窗
     */
    openPublishDialog() {
      //4月8号尹格  去除限制
      // if (this.errNums != 0) {
      //   this.$message({
      //     message: '请先处理完异常再发布',
      //     type: 'warning',
      //     duration: 1000,
      //   });
      //   return;
      // }
      this.getMergeStatus();
      let paperInfo = {
        personBookId: this.examInfo.examId,
        subectName: this.examInfo.subjectName,
      };
      this.publishScoreInfo.examId = this.examInfo.examId;
      this.publishScoreInfo.examName = this.examInfo.examName;
      this.publishScoreInfo.paperInfo = paperInfo;
      this.publishScoreInfo.schoolId = this.examInfo.schoolId;
      // this.isPublishScoreDialog = true;
    },
    examDelayMerge(){
      const params = {
        workId:this.examInfo.examId
      }
      examDelayMerge(params)
    },
    /**
     * @name:获取合并状态
     */
    getMergeStatus() {
      this.mergeLoading = Loading.service({
        lock: true,
        text: '正在合并数据，请稍候',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      this.mergeTimes = 0;
      this.checkingMergeStatus();
    },
    checkingMergeStatus() {
      let params = {
        examId: this.examInfo.examId,
        times: this.mergeTimes,
      };
      getMergeStatus(params)
          .then(res => {
            if (res.data.status === 0) {
              // this.mergeTimes = 0;
              this.mergeLoading.close();
              this.isPublishScoreDialog = true;
            } else {
              setTimeout(() => {
                this.mergeTimes++;
                this.checkingMergeStatus();
              }, 1000);
            }
          })
          .catch(err => {
            this.mergeLoading.close();
          });
    },
    /**
     * @name:发布成绩
     */
    publishScore() {
      this.isPublishScoreDialog = false;
      publishScoreAPI({
        schoolId: this.$route.query.schoolId,
        workId: this.examInfo.examId,
      })
          .then(res => {
            this.scoreLoading = Loading.service({
              lock: true,
              text: '正在生成分析报告，请稍候，预计5-20s，若长时间未生成，请刷新页面重试',
              background: 'rgba(0, 0, 0, 0.7)',
            });
            this.checkingScoreStatus();
          })
          .catch(err => {
          });
    },
    /**
     * @name:检查发布成绩状态
     */
    checkingScoreStatus() {
      setTimeout(async () => {
        let params = {
          id: this.examInfo.examId,
        };
        getPublishScoreStateAPI(params)
            .then(res => {
              if(res.code != 1){
                this.$message({
                  message: res.msg,
                  type: 'error',
                  duration: 1000,
                });
                this.isPublishScore = true;
                return;
              }
              if (res.data.dataState >= 1) {
                this.scoreLoading.close();
                this.$message({
                  message: '发布成功！',
                  type: 'success',
                  duration: 1000,
                });
                this.isPublishScore = true;
              } else {
                this.checkingScoreStatus();
              }
            })
            .catch(err => {
              this.scoreLoading.close();
            });
      }, 5000);
    },
    /**
     * @name:关闭发布成绩弹窗
     */
    closePublish() {
      this.isPublishScoreDialog = false;
    },
    onGetNextExamNo(id) {
      this.nextExamNo = id;
    },
    /**
     * @name:获取题目坐标
     */
    getScanPaperPoints() {
      getScanPaperPoints({
        paperNo: this.examInfo.paperNo,
      })
          .then(res => {
            this.pointsData = JSON.parse(res.data);
          })
          .catch(err => {
          });
    },
    /**
     * @name:获取批次列表
     */
    async getBatchList() {
      let params = {
        examId: this.examInfo.examId,
        teacherId: this.teacherId,
      };
      const res = await getBatchListAPI(params);
      if (res.code == 1) {
        this.taskList = res.data;
        if (this.teacherId != '') {
          this.taskIds = this.taskList.map(item => item.id).join(',');
        }
        this.firstTaskId = (this.taskList.length && this.taskList[0].id) || '';
        this.judgePublishSatus();
      }
    },
    /**
     * @name:判断发布成绩状态
     */
    judgePublishSatus() {
      this.isPublishScore = false;
      if (this.examInfo.taskId != '') {
        this.isPublishScore =
            this.taskList.filter(item => {
              return item.id == this.examInfo.taskId && item.status == 4;
            }).length > 0;
      } else {
        this.isPublishScore =
            this.taskList.filter(item => {
              return item.status == 4;
            }).length > 0;
      }
    },
    /**
     * @name:获取异常统计数量
     */
    async getErrorNums(type) {
      this.requestInfo.quesNo = '';
      this.requestInfo.examNoId = '';
      this.examNoInfo = {};
      this.examErrorData = [];
      this.$bus.$emit('get-image', '');
      await getErrorNums({
        examId: this.examInfo.examId,
        taskId: this.examInfo.taskId || this.taskIds,
        schoolId: this.$route.query.schoolId || this.examInfo.schoolId || '',
      })
          .then(res => {
            this.allErrorNums = res.data;
            if (this.activeName != 'subject' && this.activeName != 'objective' && this.activeName != 'choice') {
              //考号、答卷、页码异常
              this.handelExamNo(type);
            } else {
              //题目
              this.handelQuesError(type);
            }
          })
          .catch(err => {
          });
      //
    },
    updatePoints(){
      let params = {
        examId: this.examInfo.examId,
        code: 67
      }
      imageCodeDetect(params)
    },
    /**
     * @name:处理考号、答卷、页码异常数据
     */
    handelExamNo(type) {
      let list =
          this.requestInfo.status == 0
              ? this.allErrorNums[this.activeName].wait
              : this.allErrorNums[this.activeName].waited;
      this.taskList?.forEach(item => {
        this.allErrorNums[this.activeName].wait?.forEach(ite => {
          if (item.id == ite.taskId) {
            this.$set(ite, 'batchId', item.batchId);
          }
        });

        this.allErrorNums[this.activeName].waited?.forEach(ite => {
          if (item.id == ite.taskId) {
            this.$set(ite, 'batchId', item.batchId);
          }
        });
      });
      if (list.length != 0) {
        if (this.nextExamNo != '') {
          //判断是否还在异常列表里
          let isInList = list.find(item => {
            return item.id == this.nextExamNo;
          });
          if (!isInList) {
            this.nextExamNo = list[0].id;
            this.$refs.silderSet.getCurrent(this.nextExamNo);
          }
        }
        let tempId = this.nextExamNo;
        if (type == 'changeTab') {
          tempId = '';
        }
        //考号异常获取第一个批次Id
        this.examNoInfo =
            tempId == ''
                ? list[0]
                : list.filter(item => {
                  return item.id == tempId;
                })[0];
        this.getExamNo();
      } else {
        this.examErrorData = [];
      }
      setTimeout(() => {
        this.$refs.silderSet.getNext();
      }, 500);
    },
    /**
     * @name:处理题目异常数据
     */
    handelQuesError(type) {
      let list =
          this.requestInfo.status == 0
              ? this.allErrorNums[this.activeName].wait
              : this.allErrorNums[this.activeName].waited;
      //题目异常获取第一题考号
      this.requestInfo.quesNo = list[0]?.quesNo;
      this.requestInfo.doCount = list[0]?.doCount;
      this.requestInfo.abPaper = list[0]?.abPaper;
      this.getQuesError();
    },
    /**
     * @name:获取处理状态的列表
     */
    changeHandleType(info) {
      this.pagination.page = 1;
      this.requestInfo = info;
      this.requestInfo.examNoId = info.examNoId;
      if (!this.isQuesError) {
        let list =
            info.status == 0
                ? this.allErrorNums[this.activeName].wait
                : this.allErrorNums[this.activeName].waited;
        this.nextExamNo = info.nextExamNo;
        this.examNoInfo = (info.list||list).filter(item => {
          return item.id == info.examNoId;
        })[0];
        if (this.examNoInfo) this.getExamNo();
      } else {
        this.getQuesError();
        this.$refs.quesError.scrollTotop();
      }
    },
    /**
     * @name:分页
     */
    changePage(page) {
      this.pagination.page = page;
      this.getQuesError();
    },
    /**
     * @name:提交匹配考号数据
     */
    submitExamNo(data) {
      let list = [
        {
          id: data.id,
          stuId: data.stuId,
          exam_id: this.examInfo.examId,
          page: data.page,
          task_id: data.taskId,
          old_stu_no: data.oldStuNo,
          stu_no: data.stuNo,
          idx: data.idx,
          is_deleted: false,
        },
      ];
      if (this.activeName == 'exam') {
        this.saveExamNoData(list);
      } else {
        this.savePage(list);
      }
    },

    /**
     * @name:删除考号、答卷、页码异常
     */
    deletePaper(data) {
      let list = [];
      if (this.activeName == 'exam') {
        list = data.map(item => {
          return {
            id: item.id,
            exam_id: this.examInfo.examId,
            page: item.page,
            task_id: item.taskId,
            old_stu_no: item.stuNo,
            stu_no: item.stuNo,
            is_deleted: true,
            idx: item.idx,
            code: item.code,
          };
        });
        this.saveExamData(list);
      } else {
        list = data.map(item => {
          return {
            id: item.id,
            exam_id: this.examInfo.examId,
            page: item.page,
            task_id: item.taskId,
            is_deleted: true,
            idx: item.idx,
          };
        });
        this.deletePage(list);
      }
    },
    saveExamNoData(list) {
      saveExamNoAPI(list[0])
          .then(res => {
            this.$message({
              message: '提交成功！',
              type: 'success',
              duration: 1000,
            });
            this.getErrorNums();
          })
          .catch(err => {
          });
    },
    /**
     * @name:保存题号异常数据
     */
    saveExamData(list) {
      saveExamNoImgAPI(list)
          .then(res => {
            this.$message({
              message: '提交成功！',
              type: 'success',
              duration: 1000,
            });
            this.getErrorNums();
          })
          .catch(err => {
          });
    },
    /**
     * @name:保存页码异常数据
     */
    savePage(list) {
      savePageAPI(list)
          .then(res => {
            this.$message({
              message: '提交成功！',
              type: 'success',
              duration: 1000,
            });
            this.getErrorNums();
          })
          .catch(err => {
          });
    },
    /**
     * @name:删除扫描页数（答卷异常，页码异常）
     */
    async deletePage(list) {
      const res = await deleteScanPageAPI(list);
      if (res.code == 1) {
        this.$message({
          message: '提交成功！',
          type: 'success',
          duration: 1000,
        });
        this.getErrorNums();
      } else {
        this.$message({
          message: '提交失败！',
          type: 'error',
          duration: 1000,
        });
      }
    },
    /**
     * @name:获取题目异常数据
     */
    getQuesError() {
      let func = null;
      let params = {
        schoolId: this.$route.query.schoolId,
        examId: this.examInfo.examId,
        status: this.requestInfo.status,
        taskId: this.examInfo.taskId || this.taskIds,
        quesNo: this.requestInfo.quesNo,
        page: this.pagination.page,
        paperNum: this.paperNum,
        abPaper: this.requestInfo.abPaper
      };
      //选做题
      if (this.activeName == 'choice') {
        params.chooseType = 1;
        let count = this.requestInfo?.doCount || 1;
        params.size = 40 - (40 % count);
      } else {
        params.chooseType = 0;
        params.size = this.pagination.limit;
      }
      getQuesScanImg(params)
          .then(res => {
            this.quesErrorData = res.data.rows;
            this.pagination.page_count = res.data.page_count;
            this.getErrorImg();
          })
          .catch(err => {
            this.quesErrorData = [];
          });
    },
    /**
     * @name:获取考号异常数据
     */
    getExamNo() {
      this.pointsData = this.abPaperDatas[this.examNoInfo.cardId];
      if (this.activeName === 'point' || this.activeName === 'paper') {
        this.getImageInfo();
        return;
      }
      getExamNoAPI({
        examId: this.examInfo.examId,
        taskId: this.examNoInfo.taskId,
        idx: this.examNoInfo.idx,
        // stuNo: this.examNoInfo.stuNo,
      })
          .then(res => {
            this.handleImageList(res.data);
          })
          .catch(err => {
            this.examErrorData = [];
          });
    },
    getImageInfo() {
      getScanImageAPI({id: this.examNoInfo.id, examId: this.examInfo.examId})
          .then(res => {
            this.handleImageList([res.data]);
          })
          .catch(err => {
            this.examErrorData = [];
          });
    },
    handleImageList(images) {
      this.examErrorData = images;
      this.examErrorData.forEach(items => {
        items.questions.forEach(item => {
          if (!item.is_obj) {
            item.score_list &&
            item.score_list.forEach(ite => {
              let scores = [];
              ite.scores?.forEach(it => {
                let isChoice =
                    item.code == 3 && item.status == 0
                        ? false
                        : ite.score == it && item.miss_score != item.score
                            ? true
                            : false;
                scores.push({value: it, isChoice: isChoice});
              });
              //每一行的高度
              let rowHeight = ite.pos[3] / ite.rows;
              const newArray = [];
              for (let i = 0; i < scores.length; i += ite.cols) {
                const subArray = scores.slice(i, i + ite.cols);
                newArray.push(subArray);
              }
              let array1 = [];
              newArray.forEach((everyRow, index) => {
                let tempObj = {};
                tempObj.pos = [ite.pos[0], ite.pos[1] + index * rowHeight, ite.pos[2], rowHeight];
                tempObj.new_score_list = [...everyRow];
                tempObj.cols = ite.cols;
                array1.push(tempObj);
              });
              this.$set(ite, 'new_score_list', scores);
              this.$set(ite, 'newArray', array1);
            });
          }
        });
      });
      console.log('this.pointsData.pages', this.pointsData);
      let pageBox = this.pointsData.pages[0].filter(item => {
        return item.item_type == 1 || item.item_type == 2 || item.item_type == 5;
      });
      this.examErrorData.forEach(item => {
        if (item.page == 1) {
          this.$set(item, 'pageBox', pageBox);
        }
      });
      // console.log("this.$set(itempageBox");
      this.firstImage = this.examErrorData[0].image || this.examErrorData[0].origin;
      // console.log('调$bus.$emit("get-image"');
      this.$bus.$emit('get-image', this.examErrorData[0]);
    },
    /**
     * @name:获取异常图片
     */
    async getErrorImg() {
      this.quesErrorData.forEach(async item => {
        //需要截取的x,y,w,h
        let cutStartX = '',
            cutStartY = '',
            cutHeight = '',
            topY = '';
        if (!item.is_obj) {
          //是否手写打分
          let isWrite = item.type == 13;
          let isOnline = item.type == 15;
          if (isWrite) {
            //构造打分栏数据
            const num = 16;
            let scoresList = Array.from({length: item.total_score}, (_, index) => index);
            scoresList.push(item.total_score);
            if (item.has_point) {
              scoresList.unshift(-1)
            }
            let rows = Math.ceil(scoresList.length / num);
            let scores = new Array(num * rows).fill(-1);
            scoresList.forEach((s, i) => {
              scores[i] = s;
            })

            let _score = parseInt(item.score);
            let score_list = [{
              cols: num,
              detect_score: _score,
              pos: [item.pos[0], item.pos[1] - rows * 6, item.pos[2], rows * 6],
              rows: rows,
              score: _score,
              scores: scores.reverse()
            }]
            if (item.has_point) {
              let _score = item.score != parseInt(item.score) ? 0.5 : 0;
              score_list.push({
                cols: 1,
                detect_score: _score,
                pos: [item.pos[0] + item.pos[2] - item.pos[2] / num, item.pos[1] - 6, item.pos[2] / num, 6],
                rows: 1,
                score: _score,
                scores: [0.5]
              })
            }
            item.score_list = score_list;
          }
          if (isOnline) {
            item.score_list.forEach(ite => {
              let totalScore = ite.total_score || item.total_score;
              let scores = [];
              scores.push(totalScore);
              scores.push(totalScore / 2);
              scores.push(0);
              ite.scores = scores;
              ite.cols = 1;
              ite.rows = 1;
            })
          }
          item.score_list.forEach(ite => {
            let missScore = item.miss_score;
            if(isOnline){
              //针对线上批改半对分值为0导致匹配框选错误的问题 强制半对分值为总分一半
              missScore = ite.score / 2;
            }
            let scores = [];
            ite.scores?.forEach(it => {
              let isChoice = false;
              isChoice = item.code == 3 && item.status == 0 && !isOnline
                  ? false
                  : ite.score == it && missScore != item.score
                    ? true
                    : false;
              // let isChoice = ite.score == it && item.status == 1 ? true : false;
              scores.push({value: it, isChoice: isChoice, totalScore: ite.total_score});
            });
            //每一行的高度
            let rowHeight = ite.pos[3] / ite.rows;
            const newArray = [];
            for (let i = 0; i < scores.length; i += ite.cols) {
              const subArray = scores.slice(i, i + ite.cols);
              newArray.push(subArray);
            }
            let array1 = [];
            newArray.forEach((everyRow, index) => {
              let tempObj = {};
              tempObj.pos = [ite.pos[0], ite.pos[1] + index * rowHeight, ite.pos[2], rowHeight];
              tempObj.new_score_list = [...everyRow];
              tempObj.cols = ite.cols;
              array1.push(tempObj);
            });
            this.$set(ite, 'newArray', array1);
            this.$set(ite, 'new_score_list', scores);
            //主观题截取作答图片 延展
            cutStartX = (item.score_list[0].pos[0] * this.imgDpi).toFixed();
            cutStartY = ((item.score_list[0].pos[1] - 5) * this.imgDpi).toFixed();
            if (isWrite) {
              //手写框构造的打分栏高度根据行数扩大
              cutHeight = ((item.score_list[0].pos[3] + 10 + item.score_list[0].rows * 6) * this.imgDpi).toFixed();
            } else if (isOnline) {
              cutStartY = ((item.pos[1] - 3) * this.imgDpi).toFixed();
              cutHeight = ((item.pos[3] + 4) * this.imgDpi).toFixed();
            } else {
              cutHeight = ((item.score_list[0].pos[3] + 10) * this.imgDpi).toFixed();
            }
            topY = (item.score_list[0].pos[1] - 5) * this.imgDpi;
          });
        } else {
          let offsetY = 0;
          if (item.type == 16) {
            //客观题手写增加高度偏移  扩大显示
            offsetY = 2;
            //客观题手写作答
            let x = item.list[0].pos[0] + item.list[0].pos[2];
            let y = item.list[0].pos[1] + offsetY;
            let paddingX = 2;
            let w = 8;
            let h = 5;
            item.list.forEach((ite, index) => {
              ite.pos = [x + (index * w) + paddingX * index, y, w, h];
            })
          }
          cutStartX = (item.list[0].pos[0] * this.imgDpi).toFixed();
          cutStartY = ((item.list[0].pos[1] - offsetY) * this.imgDpi).toFixed();
          cutHeight = (
              (item.list[item.list.length - 1].pos[1] +
                  item.list[item.list.length - 1].pos[3] -
                  item.list[0].pos[1] + offsetY * 2) *
              this.imgDpi + 10
          ).toFixed();
          topY = item.list[0].pos[1] * this.imgDpi;
        }
        this.$set(item, 'topY', topY);
        // 题目坐标
        item.pos = item.pos.map(ite => {
          return (ite * this.imgDpi).toFixed();
        });
        let tempPos = [item.pos[0], Number(cutStartY), item.pos[2], Number(cutHeight)];
        //截取选项或打分框图片
        let img = await this.getSplitImg(item.image, tempPos);
        //整题图片
        let originImgs = [];
        let primitiveImg = await this.getSplitImg(item.image, item.pos);
        //题目跨页或跨面
        if (item.data.length !== 0) {
          const updatedItems = item.data.map(async ite => {
            ite.pos = ite.pos.map((it, index) => {
              // 宽、高增加6mm
              if (index === 2) {
                return ((Number(it) + 10) * this.imgDpi).toFixed();
              } else if (index === 3) {
                //打印坐标高度默认增加2mm,此处-2mm
                if (item.type == 18) {
                  return ((Number(it) + 3) * this.imgDpi).toFixed();
                } else {
                  return ((Number(it) + 8) * this.imgDpi).toFixed();
                }
              } else {
                //x,y减4mm
                return ((Number(it) - 5) * this.imgDpi).toFixed();
              }
            });
            return this.getSplitImg(ite.image, ite.pos);
          });
          await Promise.all(updatedItems)
              .then(images => {
                images.forEach(img => originImgs.push(replaceALiUrl(img)));
              })
              .catch(error => {
                console.error('Error during processing:', error);
              });
        }
        //题目存在考号异常，data里面的图片为空，取题目上的坐标截取
        if (originImgs.length == 0) {
          originImgs.push(replaceALiUrl(primitiveImg));
        }
        this.$set(item, 'newImg', replaceALiUrl(img));
        this.$set(item, 'originImgs', originImgs);
        this.$set(item, 'isShowOrigin', false);
        this.$set(item, 'list', item.is_obj ? item.list : item.score_list);
        let socre = Math.min(item.score, item.total_score);
        if (item.scoringMode == 1) {
          //减分制
          socre = Math.max(item.total_score - item.score, 0);
        }
        this.$set(item, 'tempScore', socre);
      });
      // console.log("this.quesErrorData", this.quesErrorData);
    },
    /**
     * @name:获取题目作答图片
     */
    async getSplitImg(img, pos) {
      let src =
          `${img}` +
          '?x-oss-process=image/resize,h_1238,' +
          `image/crop,x_${pos[0]},y_${pos[1]},w_${pos[2]},h_${pos[3]},image/format,jpeg`;
      return await getImgUrl(src);
    },
    /***
     * @name:返回上一页
     */
    backReport() {
      this.$router.back();
    },
    /**
     * @name:跳转到全部答卷
     */
    checkAllPaper() {
      this.$router.push({
        path: '/scan/images',
        query: {
          examId: this.examInfo.examId,
          paperNo: this.examInfo.paperNo,
          teacherId: this.teacherId,
        },
      });
    },
    checkTask() {
      if (this.$route.query.fromPage == 'batch') {
        this.$router.push({
          path: '/scan_batch',
          query: {
            id: this.$route.query.fromId,
            isEdit: 2,
            teacherId: this.teacherId,
          },
        });
      } else {
        this.$router.push({
          path: '/scan_task',
          query: {
            // taskId: this.firstTaskId,
            examId: this.examInfo.examId,
            isEdit: 2,
            teacherId: this.teacherId,
            schoolId: this.$route.query.schoolId,
          },
        });
      }
    },
    /**
     * @name:处理完成
     */
    async handleComplete() {
      let params = {
        examId: this.examInfo.examId,
      };
      const res = await sendMergeMsgAPI(params);
    },
    /**
     * @name:切换异常
     */
    tabClick() {
      this.requestInfo.status = 0;
      this.$refs.silderSet.initData();
      if (this.isQuesError) {
        this.requestInfo.quesNo =
            (this.allErrorNums[this.activeName].wait.length &&
                this.allErrorNums[this.activeName].wait[0].quesNo) ||
            0;
        this.requestInfo.doCount =
            (this.allErrorNums[this.activeName].wait.length &&
                this.allErrorNums[this.activeName].wait[0].doCount) ||
            1;
        this.requestInfo.quesIds =
            (this.allErrorNums[this.activeName].wait.length &&
                this.allErrorNums[this.activeName].wait[0].quesIds) ||
            [];
      } else {
        this.quesErrorData = [];
        this.getErrorNums('changeTab');
      }
    },
    // 计算方式，center.x/image.width,center.y/image.height
    // args = {

    //   image_id':"f52d4914520111eea9cf0242ac11000b'page' : '1'
    //   paper_no':100497,
    //   qrcode_center':0.193,0.273
    // }
    closeLoading(data) {
      this.loading = false;
      if (data.length == 0) {
        this.$message({
          message: '重新识别失败！',
          type: 'error',
          duration: 1000,
        });
      } else {
        this.examErrorData = data;
        this.$bus.$emit('get-image', this.examErrorData[0]);
        this.getErrorNums();
      }
    },
    /**
     * @name:标记答卷正常
     */
    async makeImageError(data, weight) {
      let params = {
        id: data.id,
        weight: weight,
        examId: this.examInfo.examId,
      }
      setImagePointError(params)
          .then(res => {
            this.$message({
              message: '设置成功！',
              type: 'success',
              duration: 1000,
            });
            this.getErrorNums();
          })
          .catch(err => {
          });
    },
    reversePage(info) {
      this.$refs.silderSet.reversePaperDetect(info);
    },
    /**
     * @name:点击进入查看全部题目
     */
    insider() {
      clearTimeout(this.timeoutId);
      this.clickCount++;
      if (this.clickCount >= 15) {
        this.clickCount = 0; // 重置点击计数器
        this.$router.push({
          path: '/scan/insider',
          query: {
            schoolId: this.$route.query.schoolId,
            examId: this.examInfo.examId,
            paperNo: this.examInfo.paperNo,
            examName: this.examInfo.examName,
            subjectName: this.examInfo.subjectName,
          },
        });
      } else {
        this.timeoutId = setTimeout(() => {
          this.clickCount = 0; // 清空点击计数器
        }, 700);
      }
      console.log(this.clickCount);
    },
    /**
     * @name:提交缺考异常
     * @param {*} data
     */
    async setAbsent(data) {
      let params = [];
      params = data.map(item => {
        return {
          id: item.id,
          isAbsent: item.isAbsent,
          examId: this.examInfo.examId,
          taskId: item.taskId,
          idx: item.idx,
        };
      });
      if (params.length == 0) {
        this.$message({
          message: '请先选择需要操作的数据！',
          type: 'warning',
          duration: 1000,
        });
        return;
      }
      setAbsentAPI(params)
          .then(res => {
            this.$message({
              message: '提交成功！',
              type: 'success',
              duration: 1000,
            });
            this.getErrorNums();
          })
          .catch(err => {
          });
    },
    /**
     * @name:提交缺考异常
     * @param {*} data
     */
    async setABPaper(data, abPaper) {
      let params = [];
      params = data.map(item => {
        return {
          id: item.id,
          AbPaper: abPaper,
          examId: this.examInfo.examId,
          taskId: item.taskId,
          idx: item.idx,
        };
      });
      if (params.length == 0) {
        this.$message({
          message: '请先选择需要操作的数据！',
          type: 'warning',
          duration: 1000,
        });
        return;
      }
      setABPaperAPI(params)
          .then(res => {
            this.$message({
              message: '提交成功！',
              type: 'success',
              duration: 1000,
            });
            this.getErrorNums();
          })
          .catch(err => {
          });
    },
  },
};
</script>

<style lang="scss" scoped>
.paper-container {
  position: relative;
  height: calc(100% - 35px);
  padding: 20px 0 40px 20px !important;
  // min-width: 1300px;
  // max-width: 1500px;
  // margin: 0 auto;
  // background-color: #f7fafc;
  .header-back {
    // left: 30px;
    top: 20px;
    position: absolute;
    padding: 12px 20px;
    font-size: 16px;
    color: #3a5eff;
    cursor: pointer;
  }
}

.paper-header {
  display: flex;
  justify-content: space-between;
}

.error-container {
  display: flex;
  height: 100%;
}

.error-left {
  position: relative;
  width: 70%;
}

.error-right {
  width: 30%;
  margin-top: 50px;
}

.operate-button {
  position: absolute;
  top: 12px;
  display: flex;
  justify-content: center;
  width: 115px;
}

.check-all-button {
  right: -120px;
}

.check-task-button {
  right: -245px;
}

.check-insider-button {
  right: 10px;
  width: 45px;
  cursor: auto;
  height: 35px !important;
}

.publish-button {
  right: -370px;
}

.complete-button {
  position: absolute;
  top: 12px;
  left: 400px;
}

.noScan {
  width: 100%;
  height: auto;
  margin-top: 10%;

  img {
    display: block;
    margin: 0 auto;
  }
}

.error-back {
  display: flex;
  align-items: center;
  font-size: 18px;
  margin-bottom: 5px;

  .back-word {
    margin-right: 10px;
    cursor: pointer;
  }
}

.back-examname {
  display: inline-block;
  width: 80%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
<style lang="scss">
.scan-student-error-tabs {
  position: relative;
  height: calc(100% - 50px);

  .el-tabs__content {
    height: 98%;

    .el-tab-pane {
      height: 100%;
    }
  }

  .el-tabs__header {
    margin-top: 10px;
    // margin-left: 80px;
  }
}

.error-header {
  align-items: center;

  .el-page-header__left {
    width: 80px;
  }
}

// .error-container::-webkit-scrollbar {
//   height: 10px;
//   overflow: auto;
//   width: 5px;
// }
// .error-container::-webkit-scrollbar-track {
//   // background: rgb(239, 239, 239);
//   border-radius: 2px;
// }
</style>

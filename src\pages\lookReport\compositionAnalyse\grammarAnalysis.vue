<template>
  <div class="grammar-analysis report-wrapper">
    <headerFilter @updateFilter="updateFilter" ref="headerFilter" class="header-filter"></headerFilter>
    <div class="section">
      <div class="titleLine">语法分析</div>
      <div
        ref="grammarAnalysisContent"
        style="width: 100%; height: 450px"
        v-if="grammarAnalysisDetail && grammarAnalysisDetail.grammarList && grammarAnalysisDetail.grammarList.length"
      ></div>
      <no-data v-else></no-data>
    </div>

    <div v-if="grammarAnalysisDetail" class="grammar-list">
      <div
        v-for="(item, index) in grammarAnalysisDetail.grammarList"
        :key="index"
        class="grammar-item"
        :class="{ active: currentGrammarIndex === index }"
        @click="selectGrammar(item, index)"
      >
        <div class="grammar-content">
          <span class="grammar-name">{{ item.grammar }}</span>
          <span class="grammar-count">
            <span class="count">{{ item.errNum }}</span>
            <span class="unit">人</span>
          </span>
        </div>
      </div>
    </div>

    <div v-if="currentGrammar">
      <div class="titleLine" style="width: 100%">
        <span class="title">
          {{ currentGrammar.grammar }}
        </span>
        <span class="expand-all" @click="handleExpandAll">
          {{ isAllExpand ? '收起' : '展开' }}全部 <i :class="isAllExpand ? 'el-icon-top' : 'el-icon-bottom'"></i>
        </span>
      </div>
      <div
        class="error-list"
        :class="{ expand: item.expand }"
        v-for="(item, index) in currentGrammar.errSt"
        :key="index"
      >
        <div class="error-header" @click="handleExpand(item)">
          <div class="error-rank" :class="{ error: index < 3 }">
            {{ index + 1 }}
          </div>
          <div class="error-name">{{ item.stuName }} ({{ item.count }}次)</div>
          <div class="error-expand">
            <i :class="item.expand ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </div>
        </div>
        <div class="error-content" v-show="item.expand">
          <div v-for="sentence in item.sentences" class="error-sentences">
            <p class="error-sentence" v-html="renderSentenceHtml(sentence)"></p>
            <p class="error-grammar">{{ sentence.grammar }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import HeaderFilter from '@/components/headerFilter.vue';
import NoData from '@/components/noData.vue';
import { getExamQueClassDetailsAPI } from '@/service/api';
import { ECharts } from 'echarts';
import { Component, Mixins, Ref, Vue } from 'vue-property-decorator';
import SentenceMixin, { SentenceMixinSpace } from './SentenceMixin';

interface GrammarAnalysisDetail {
  grammarList: GrammarList[];
  goods: Good[];
}

interface Good {
  sentence: string;
  className: string;
  stuName: string;
}

interface GrammarList {
  grammar: string;
  errCount: number;
  errNum: number;
  errSt: ErrSt[];
}

export interface ErrSt {
  stuId: string;
  stuName: string;
  sentences: SentenceMixinSpace.Sentence[];
  count: number;
  expand?: boolean;
}

@Component({
  components: {
    HeaderFilter,
    NoData,
  },
})
export default class GrammarAnalysis extends Mixins(SentenceMixin) {
  @Ref() private grammarAnalysisContent!: HTMLDivElement;

  // 当前选中的筛选项数据
  filterData = {
    classId: '',
    classIds: [],
    subjectId: '',
    phaseId: '',
    xfId: '',
    classList: [],
    aliasName: '', // 组合学科
    qType: 0, // 0:得分 1：赋分
    source: 0, // 0:成绩榜 1：组合成绩榜
    abPaper: '', // -1:普通 0:A卷 1:B卷
    quesInfo: null, // 作文题
  };

  // 语法分析详情
  grammarAnalysisDetail: GrammarAnalysisDetail | null = null;
  // 当前语法
  currentGrammar: GrammarList | null = null;
  // 当前语法索引
  currentGrammarIndex: number = 0;

  get isAllExpand() {
    if (this.currentGrammar) {
      return this.currentGrammar.errSt.every(item => item.expand);
    }
    return false;
  }

  // 当前workId
  get workId() {
    let subjectId = this.filterData.subjectId;
    let subjectList = this.$sessionSave.get('innerNoRoleSubjectList');
    let subject = subjectList.find(item => item.id == subjectId);
    if (subject) {
      return subject.workIds[Number(this.filterData.abPaper)];
    }
    return '';
  }
  // 语法分析图表
  private grammarAnalysisChart: ECharts | null = null;

  // 更新筛选条件
  updateFilter(filterData) {
    this.filterData = this.$deepClone(filterData);
    this.getExamQueClassDetails();
  }

  mounted() {
    this.initGrammarAnalysisChart();
  }

  // 获取语法分析
  async getExamQueClassDetails() {
    const res = await getExamQueClassDetailsAPI({
      schoolId: this.$sessionSave.get('schoolInfo').id,
      workId: this.workId,
      quesNo: this.filterData.quesInfo?.quesNo,
      tQuesNo: this.filterData.quesInfo?.tQuesNo,
      classId: this.filterData.classId,
      type: 2, // 1:词汇分析 2:语法分析
    });
    this.grammarAnalysisDetail = res.data;

    this.currentGrammarIndex = 0;
    this.currentGrammar = res.data.grammarList[this.currentGrammarIndex];
    this.currentGrammar.errSt.forEach(item => {
      this.$set(item, 'expand', true);
    });

    this.$nextTick(() => {
      this.initGrammarAnalysisChart();
    });
  }

  // 初始化语法分析图表
  initGrammarAnalysisChart() {
    if (!this.grammarAnalysisChart) {
      this.grammarAnalysisChart = this.$echarts.init(this.grammarAnalysisContent);
    }
    this.grammarAnalysisChart.setOption({
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        show: false,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: this.grammarAnalysisDetail?.grammarList.map(item => item.grammar),
        axisLabel: {
          show: true,
          interval: 0,
          formatter: function (val) {
            var _l = val.length;
            if (_l > 7) {
              return val.substring(0, 7) + '\n' + val.substring(7);
            }
            return val;
          },
        },
      },
      yAxis: {
        type: 'value',
        name: '人数',
      },
      series: [
        {
          name: `错误人数`,
          type: 'bar',
          data: this.grammarAnalysisDetail?.grammarList.map(item => item.errNum),
          barWidth: 30,
          label: {
            show: true,
            position: 'top',
            color: 'inherit',
          },
        },
      ],
    });

    this.grammarAnalysisChart.off('click');
    this.grammarAnalysisChart.on('click', params => {
      this.selectGrammar(this.grammarAnalysisDetail?.grammarList[params.dataIndex], params.dataIndex);
    });
  }

  // 展开/收起全部
  handleExpandAll() {
    let expand = !this.isAllExpand;
    this.currentGrammar.errSt.forEach(item => {
      this.$set(item, 'expand', expand);
    });
  }

  // 展开/收起单个
  handleExpand(item) {
    this.$set(item, 'expand', !item.expand);
  }

  // 选择语法类型
  selectGrammar(grammar: GrammarList, index: number) {
    this.currentGrammar = grammar;
    this.currentGrammarIndex = index;
    this.currentGrammar.errSt.forEach(item => {
      this.$set(item, 'expand', true);
    });
  }
}
</script>

<style scoped lang="scss">
@import './index.scss';
.grammar-analysis {
}

.section {
  margin-bottom: 24px;
}

.expand-all {
  float: right;
  font-size: 14px;
  font-weight: 400;
  color: #409eff;
  cursor: pointer;
}

.error-list {
  margin-bottom: 8px;

  .error-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border-bottom: 1px solid #e8e8e8;
    cursor: pointer;

    &:hover {
      background-color: #f7f7f7;
    }

    .error-rank {
      width: 26px;
      height: 26px;
      line-height: 26px;
      border-radius: 50%;
      background-color: #d1d1d1;
      color: #fff;
      text-align: center;

      &.error {
        background-color: #ff9f40;
      }
    }

    .error-name {
      flex: 1;
      margin-left: 10px;
    }
  }

  .error-content {
    padding: 12px;
    background-color: #f7f7f7;

    .error-sentences {
      margin-bottom: 8px;

      .error-sentence {
        &::before {
          content: '';
          display: inline-block;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          background-color: #409eff;
          margin-right: 10px;
        }
      }

      .error-grammar {
        margin-top: 4px;
        margin-left: 20px;
      }
    }
  }

  &.expand {
    border: 1px solid #e8e8e8;

    .error-header {
      background-color: #e9effb;
    }
  }

  .error-expand {
    cursor: pointer;

    i {
      font-size: 16px;
      color: #409eff;
      font-weight: 700;
    }
  }
}

.error-table {
  table {
    width: 100%;
    border-collapse: collapse;

    td {
      border: 1px solid #e8e8e8;
      padding: 12px;
      vertical-align: middle;
    }

    .error-type {
      width: 120px;
      background-color: #f8f8f8;
      font-weight: 500;
    }

    .error-content {
      div {
        margin-bottom: 8px;
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.grammar-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;

  .grammar-item {
    width: calc(20% - 16px);
    padding: 16px;
    background: #f7f7f7;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid transparent;

    &:hover {
      background: #e7ecf5;
    }

    &.active {
      background: #e7ecf5;
      border-color: #4688ff;
    }

    .grammar-content {
      display: flex;
      align-items: center;
      gap: 8px;

      .grammar-name {
        flex: 1;
        font-size: 14px;
        color: #333;
        line-height: 1.4;
        // 超出一行显示省略号
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .grammar-count {
        flex-shrink: 0;
        display: flex;
        align-items: baseline;

        .count {
          font-size: 14px;
          font-weight: bold;
          color: #4688ff;
          line-height: 1;
        }

        .unit {
          margin-left: 2px;
          font-size: 14px;
          color: #666;
        }
      }
    }
  }
}
</style>

<!--
 * @Descripttion: 学生题目图片预览
 * @Author: 小圆
 * @Date: 2024-01-13 09:26:00
 * @LastEditors: Please set LastEditors
-->
<template>
  <previewSlider ref="previewSlider" :class="{ desktop: isDesktopStyle }" :data="usedImgList" @slideChange="slideChange"
    @tabPrev="tabPrev" @tabNext="tabNext" @close="handleCloseDialog">
    <!-- 主体 -->
    <template slot-scope="{ item, index }">
      <img :src="item" :data-src="item" :data-index="index" @load="loadImage(index)" class="swiper-lazy" />
    </template>

    <!-- 按钮组 -->
    <div slot="toolLeft">
      <div class="swiper-imageTool swiper-imageTool-left clearfix" v-if="stuList.length">
        <div class="swiper-imageTool-txt pull-left">
          <span>
            {{ showAnonymousName ? selectStu.anonymous : selectStu.name }}
            {{ showStudentScore ? `（${selectStu.score}分）` : '' }}
          </span>
        </div>

        <!-- 优秀|错误作答 -->
        <div class="swiper-comment-buttons pull-left clearfix" v-if="selectStu.state != -1">
          <div class="pull-left btn-comment btn-comment--correct click-element" :class="{ active: showStuCorrect }"
            @click="toggleCorrectImg" v-if="isSpecialWriting">
            <img src="../../assets/nor-correct.png" alt="" />
            批改痕迹
          </div>
          <div class="pull-left btn-comment btn-comment--good click-element" :class="{ active: selectStu.state == 1 }"
            @click="doComment(1)">
            <img src="../../assets/nor-excellent.png" alt="" />
            优秀作答
          </div>
          <div class="pull-left btn-comment btn-comment--bad click-element" :class="{ active: selectStu.state == 2 }"
            @click="doComment(2)">
            <img src="../../assets/nor-false.png" alt="" />
            典型错误
          </div>
        </div>
      </div>
    </div>

    <div class="clearfix" slot="toolRight" v-if="isDesktop">
      <div class="swiper-imageTool swiper-imageTool-right swiper-imageTool-comment click-element pull-left"
        :class="stuJoined ? 'tool-block--remove' : 'tool-block--add'" @click="addToComment(stuJoined)">
        {{ stuJoined ? '取消加入' : '加入讲解' }}
      </div>

      <div
        class="swiper-imageTool swiper-imageTool-right swiper-imageTool-comment click-element tool-block--add pull-left"
        @click="insertWhiteBoard">
        插入白板
      </div>
    </div>

    <!-- 遮罩层 -->
    <div class="preview-swiper-cover" :style="!stuList.length ? { bottom: 0 } : ''"></div>

    <!-- 学生列表 -->
    <div slot="footer">
      <div class="swiper-bottom-stu-list-cover" v-if="stuList.length"></div>
      <div class="swiper-bottom-stu-list" v-if="stuList.length">
        <ul class="student-list text-left dis-inline list-none">
          <li :id="'student_' + item.id" class="btn-student click-element"
            :class="{ active: selectStu.id === item.id, joined: joinedCommentIds.includes(item.uName) }"
            v-for="item in stuList" :key="item.id" @click="switchStudent(item.id)">
            {{ showAnonymousName ? item.anonymous : item.name }}
          </li>
        </ul>
      </div>
    </div>
  </previewSlider>
</template>

<script>
import previewSlider from '@/components/SwiperViewer/previewSlider.vue';
import { getNewQuesPointSourceList } from '@/service/api';
import { setExamStuEvaluate } from '@/service/pexam';
import { replaceALiUrl } from '@/utils/common';
import { getQueryString } from '@/utils';
import { getDtVersionCode, ver2int } from '@/utils/index';
import { mapState, mapGetters } from "vuex";

// 3.7版本
let lastVersion = ver2int('1.3.1173.99');
let dtVersion = getDtVersionCode();
const showNewVersion = dtVersion > lastVersion;

async function coverProcedureToImage(images, procedure) {
  images = images.map(item => {
    return replaceALiUrl(item);
  });
  if (!procedure) return images;
  try {
    procedure = JSON.parse(procedure);
    const loadIMGPromises = images.map(item => {
      return new Promise((resolve, reject) => {
        let img = new Image();
        img.src = item;
        img.crossOrigin = 'anonymous';
        img.onload = e => {
          let canvas = document.createElement('canvas');
          canvas.width = img.width;
          canvas.height = img.height;
          let ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0);
          ctx.font = '32px Times New Roman';
          ctx.fillStyle = '#FF0000';

          procedure.forEach(item => {
            const score = Math.abs(item.score);
            const isScoreAdd = item.isScoreAdd;
            const scoreText = isScoreAdd ? '+' + score : '-' + score;

            const textWidth = ctx.measureText(scoreText).width;
            const textHeight =
              ctx.measureText(scoreText).fontBoundingBoxAscent +
              ctx.measureText(scoreText).fontBoundingBoxDescent || 36;

            const x = item.x * img.width - textWidth / 2;
            const y = item.y * img.height - textHeight / 2 + textHeight - 10;

            ctx.fillText(scoreText, x, y);
          });

          let base64 = canvas.toDataURL('image/jpeg');
          resolve(base64);
        };
        img.onerror = e => reject(e);
      });
    });
    const imgList = await Promise.all(loadIMGPromises);
    return imgList;
  } catch (error) {
    console.error(error);
    return images;
  }
}

export default {
  components: {
    previewSlider,
  },
  props: {
    // 学生列表 [{id, name, uName（学号）, score}]
    stuList: {
      type: Array,
      default: () => [],
    },
    // 图片列表[""]
    imgs: {
      type: Array,
      default: () => [],
    },
    // 学生索引
    initialStuIndex: {
      type: Number,
      default: 0,
    },
    // 查询参数 {schoolId, workId,quesNo}
    queryParams: {
      type: Object,
      default: () => null,
    },
  },
  computed: {
    ...mapState(["showAnonymousName", "showStudentScore", "joinCommentParam"]),
    ...mapGetters(["joinedCommentIds"]),
    // 学生是否已加入
    stuJoined() {
      return this.joinedCommentIds.includes(this.selectStu.uName);
    },
    // 当前学生
    selectStu() {
      return this.stuList[this.stuIndex];
    },
  },
  data() {
    return {
      // 是否桌面样式
      isDesktopStyle: (!!window.cef) || getQueryString('isDesktop') == 1,
      // 是否桌面环境
      isDesktop: showNewVersion && ((!!window.cef && this.$route.name != 'paperComment')) || getQueryString('isDesktop') == 1,
      // 当前图片列表
      imgList: [],
      // 有批改痕迹的图片
      vestigeImgList: [],
      // 当前是否的图片
      usedImgList: [],
      // 当前学生索引
      stuIndex: this.initialStuIndex,
      activeIndex: 0,
      // 显示学生的批改痕迹
      showStuCorrect: false,
      // 是否特殊作文题
      isSpecialWriting: false
    };
  },

  beforeDestroy() {
    this.$bus.off('closePreview', this.close);
  },

  async mounted() {
    if (this.imgs.length) {
      this.imgList = this.imgs;
    }
    this.checkSpicalWriting();
    await this.getStuQuesSourceList();
    this.close = this.close.bind(this);
    this.$bus.on('closePreview', this.close);
    this.scrollToStudent('instant');
  },

  methods: {
    // 检查当前题目是否为特殊作文题
    checkSpicalWriting() {
      let examTeamQues = this.$sessionSave.get(`examTeamQues_${this.$sessionSave.get('loginInfo').id}`);
      if (examTeamQues) {
        this.isSpecialWriting = !!examTeamQues.find(it => it.data.some(sit => sit.tQuesNo === this.queryParams.tQuesNo && sit.specialTypeId == 4))
      }
      if (this.isSpecialWriting) {
        this.showStuCorrect = this.$sessionSave.get("showStuCorrect_" + this.queryParams.optUserId);
      }
    },

    // 图片加载
    loadImage(index) {
      this.$refs.previewSlider.previewTo('fitWindow', index);
    },

    // 获取题目图片
    async getStuQuesSourceList() {
      if (!this.stuList || !this.stuList.length) {
        this.usedImgList = this.$deepClone(this.imgList)
        return;
      }
      if (!this.queryParams) {
        this.usedImgList = this.$deepClone(this.imgList)
        return;
      }

      let stu = this.stuList[this.stuIndex];
      if (stu.url) {
        this.imgList = [replaceALiUrl(stu.url)];
        this.usedImgList = this.$deepClone(this.imgList)
        return;
      }

      const stuNo = stu.uName;
      try {
        const { code, data, msg } = await getNewQuesPointSourceList({
          ...this.queryParams,
          stuId: stu.id,
          stuNo: stuNo,
        });

        if (!data.images || !data.images.length) {
          this.imgList = [];
          this.vestigeImgList = [];
          this.usedImgList = [];
          return;
        }

        this.vestigeImgList = await coverProcedureToImage(data.vestige, data.procedure);
        this.imgList = await coverProcedureToImage(data.images, data.procedure);
        this.usedImgList = this.$deepClone(this.showStuCorrect ? this.vestigeImgList : this.imgList)
      } catch (err) {
        console.error(err);
        this.imgList = [];
        this.vestigeImgList = [];
        this.usedImgList = [];
      }
      await this.$nextTick();
    },

    // 索引更新
    slideChange(index) {
      this.activeIndex = index;
      console.log('当前索引', index);
    },

    // 按钮切换上一个
    async tabPrev(activeIndex) {
      if (activeIndex == 0 && this.stuIndex > 0) {
        this.stuIndex += -1;
        await this.getStuQuesSourceList();
        this.scrollToStudent('instant');
      }
    },

    // 按钮切换下一个
    async tabNext(activeIndex) {
      if (
        (activeIndex == this.usedImgList.length - 1 || this.usedImgList.length === 0) &&
        this.stuIndex < this.stuList.length - 1
      ) {
        this.stuIndex += 1;
        await this.getStuQuesSourceList();
        this.scrollToStudent('instant');
      }
    },

    // 根据索引切换学生
    async tabStuByIndex(index) {
      this.stuIndex = Number(index);
      this.getStuQuesSourceList();
    },

    // 根据id切换学生
    async switchStudent(id) {
      let stuIndex = this.stuList.findIndex(item => item.id == id);
      this.stuIndex = stuIndex;
      await this.getStuQuesSourceList();
      await this.$nextTick();

      this.scrollToStudent();
    },

    scrollToStudent(behavior = "smooth") {
      document
        .getElementById('student_' + this.selectStu.id)
        ?.scrollIntoView({ behavior, block: 'center', inline: 'center' });
    },

    /**
     * @description: 加入讲解
     * @return {*}
     */
    addToComment(stuJoined) {
      if (stuJoined) {
        this.$store.commit('QUIT_COMPARE_COMMENT', this.selectStu.uName);
      } else {
        const stu = this.selectStu;
        let joinStudent = {
          student: {
            id: stu.id,
            name: stu.name,
            usedName: this.showAnonymousName ? stu.anonymous : stu.name,
            uName: stu.uName,
            score: stu.score,
            showScore: this.showStudentScore,
            url: this.usedImgList[this.activeIndex]
          },
          quesNo: this.queryParams.quesNo,
          tQuesNo: this.queryParams.tQuesNo
        };
        this.$store.commit('JOIN_COMPARE_COMMENT', joinStudent)
      }
    },

    // 插入白板
    insertWhiteBoard() {
      this.$cefMsg(
        'mirco.call_cplus',
        'insertImages2wb',
        JSON.stringify({ list: [this.usedImgList[this.activeIndex]] })
      );
    },

    close() {
      this.$emit('close');
    },

    handleCloseDialog(){
      this.$store.commit('CLEAR_COMPARE_COMMENT')
      this.close();
    },

    // 切换批改痕迹
    toggleCorrectImg() {
      this.showStuCorrect = !this.showStuCorrect;
      this.$sessionSave.set("showStuCorrect_" + this.queryParams.optUserId, this.showStuCorrect)
      // 切换显示图片
      this.usedImgList = this.$deepClone(this.showStuCorrect ? this.vestigeImgList : this.imgList);
    },

    // 执行评价 state:-1|0|1|2
    async doComment(state) {
      let reportDetail = this.$sessionSave.get('reportDetail');
      let subjectId = this.$sessionSave.get('subjectId');
      let isCanceling = this.selectStu.state == state;
      let params = {
        examId: reportDetail.examId,
        subjectId: subjectId || reportDetail.subjectId,
        stuId: this.selectStu.id,
        quesNo: this.queryParams.quesNo,
        tQuesNo: this.queryParams.tQuesNo,
        // 如果刚好相等则是要取消评价
        evaluateState: isCanceling ? 0 : state,
      };
      let message = '',
        messageType = 'success',
        title = '设置评价';

      try {
        // 大题套小题设置成功，但是没有同步
        await setExamStuEvaluate(params);
        let usedStuName = this.showAnonymousName ? this.selectStu.anonymous : this.selectStu.name;
        if (isCanceling) {
          this.selectStu.state = 0;
          message = `取消 ${usedStuName} 的${state == 1 ? '优秀作答' : '典型错误'}成功！`;
          title = '取消评价';
        } else {
          title = state == 1 ? '优秀作答' : '典型错误';
          this.selectStu.state = state;
          message = `设置 ${usedStuName} 评价成功！`;
        }

        // 通知父组件响应列表
        this.$emit('evaluate', {
          state: isCanceling ? 0 : state,
          data: isCanceling
            ? {
              stuId: this.selectStu.id,
              anonymous: this.selectStu.anonymous,
              stuName: this.selectStu.name,
            }
            : {
              resId: '',
              stuId: this.selectStu.id,
              anonymous: this.selectStu.anonymous,
              stuName: this.selectStu.name,
              url: this.usedImgList[this.activeIndex],
            },
        });
      } catch (error) {
        messageType = 'error';
        title = '评价失败';
        message = `请稍后重试！`;
      } finally {
        this.$notify({
          position: 'top-right',
          offset: 150,
          title,
          message,
          type: messageType,
          duration: 3000,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.swiper-comment-buttons {
  border-left: 1px solid #ffffff7e;
  padding: 4px 0 0 10px;
  overflow: hidden;
  height: 100%;

  .btn-comment {
    margin-right: 5px;
    padding: 3px 15px;
    font-size: 12px;
    text-align: center;
    line-height: normal;
    border-radius: 5px;

    >img {
      display: block;
      margin: 0 auto 3px;
    }

    &.active {
      &.active {
        &.btn-comment--bad {
          background-color: #f56c6c;
        }

        &.btn-comment--good {
          background-color: #07c29d;
        }

        &.btn-comment--correct {
          background-color: #3da6ff;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.swiper-bottom-stu-list {
  .btn-student {
    display: inline-block;
    min-width: 140px;
    margin: 0 10px;
    padding: 4px 24px;
    color: #fff;
    border-radius: 30px;
    line-height: normal;
    border: 3px solid transparent;
    box-sizing: border-box;
    text-align: center;

    &.active {
      background: #3e73f6;
      border-color: #3e73f6;
    }

    &.joined {
      border-color: #3e73f6;
    }
  }

  .ivu-tabs-bar {
    border-bottom: unset !important;
    height: 92px !important;
    color: #fff !important;
  }

  .ivu-tabs-nav-container {
    line-height: 60px !important;
    font-size: 30px !important;
  }

  .ivu-tabs-nav-next {
    line-height: 92px !important;
  }

  .ivu-tabs-nav-prev {
    line-height: 92px !important;
  }

  .ivu-icon {
    font-size: 28px;
  }

  .ivu-tabs-content {
    display: none;
  }

  .ivu-tabs-nav .ivu-tabs-tab-active {
    color: #2d8cf0;
    background: #fff;
    color: #fff;
    height: 60px;
    background: #3e73f6;
    margin-top: 16px;
    border-radius: 30px;
  }

  .ivu-tabs-nav .ivu-tabs-tab {
    padding: 0 16px !important;
    margin-right: 34px !important;
  }

  .ivu-tabs-nav-scroll {
    margin: 0 30px;
  }

  .ivu-tabs-ink-bar {
    display: none !important;
    height: 0 !important;
  }

  .ivu-tabs-nav-container:focus .ivu-tabs-tab-focused {
    border-top: unset !important;
  }

  .ivu-tabs .ivu-tabs-tabpane {
    display: none;
  }
}
</style>

<template>
  <div class="analyzePage" v-loading="isLoading">
    <el-table
      v-if="tableData.length"
      ref="tableRef"
      :data="tableData"
      style="width: 100%"
      stripe
      @mousedown.native="mouseDownHandler"
      @mouseup.native="mouseUpHandler"
      @mousemove.native="mouseMoveHandler"
      v-sticky-table="0"
      class="analyze-table"
      @sort-change="sortChange"
    >
      <!--知识点-->
      <el-table-column prop="name" fixed label="知识点" width="200"> </el-table-column>
      <!--考频-->
      <el-table-column fixed sortable prop="frequency" label="考频" width="80"> </el-table-column>
      <!--涉及题号-->
      <el-table-column fixed prop="quesTitleInfo" label="涉及题号" width="90">
        <template slot-scope="scope">
          <div
            :class="{
              activeQuesInfo: classList.length === 1 && filterData.classIds.length > 0,
            }"
            @click="lookDetailQues(scope.row)"
          >
            {{ scope.row.quesTitleInfo }}
          </div>
        </template>
      </el-table-column>
      <!--分值-->
      <el-table-column prop="fullScore" fixed label="分值" width="80"> </el-table-column>
      <!--年级(平均分，得分率)-->
      <el-table-column width="140" label="年级">
        <el-table-column prop="grdAvgScore" label="平均分"> </el-table-column>
        <el-table-column sortable prop="grdScoreRate" label="得分率">
          <template slot-scope="scope">
            <span>{{ Number(Number(scope.row.grdScoreRate) * 100).toFixed(2) }}%</span>
          </template>
        </el-table-column>
      </el-table-column>
      <!--各班级平均分和得分-->
      <el-table-column
        v-for="(item, index) in classList"
        v-if="classList.length"
        :label="item.class_name"
        :key="index"
        align="center"
      >
        <el-table-column label="平均分">
          <template slot-scope="scope">
            <span>{{ scope.row.classObj[item.id] ? scope.row.classObj[item.id].clsAvgScore : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="得分率">
          <template slot-scope="scope">
            <span>{{ scope.row.classObj[item.id] ? Number(scope.row.classObj[item.id].clsScoreRate * 100).toFixed(2)+ '%' : '--' }}</span>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
    <div v-if="!tableData.length && !tableLoading" class="nodata">
      <img :src="noResImg" alt="" />
      <p class="text-center">暂无数据</p>
    </div>

    <!--分页器-->
    <!-- <el-pagination
      background
      style="margin: 30px 0"
      :hide-on-single-page="!tableData.length"
      class="text-center"
      layout="total, prev, pager, next"
      @current-change="handleCurrentChange"
      :current-page="pagination.page"
      :page-size="pagination.pageSize"
      :total="pagination.total"
    >
    </el-pagination> -->

    <el-drawer custom-class="quesDrawer" :visible.sync="showQues" :with-header="false">
      <div v-loading="listLoading" element-loading-text="加载中...">
        <!--class="ques__expand clearfix display_flex flex-direction_column"-->
        <div v-if="expandQuesList.length" class="stystem__inner">
          <div
            class="expand__queList display_flex flex-direction_column"
            v-for="(item, index) in expandQuesList"
            :key="index"
            v-if="expandQuesList.length"
          >
            <div class="question_content">
              <div style="margin-bottom: 15px">
                <strong style="font-size: 18px; padding-left: 20px">
                  第{{ item.sortOrder }}题：
                </strong>
              </div>
              <!--题面-->
              <div class="question_body" v-html="item.topic"></div>
              <!--选项
                            <div class="optionsBox display_flex align-items_center flex_wrap_wrap">
                                <div :class="{choiceQues: item.quesType == 1 || item.quesType == 8,
                                        arrange_1: item.optionArrange == 1,
                                        arrange_2: item.optionArrange == 2,
                                        arrange_3: item.optionArrange == 3}">
                                            <span v-for="(optionItem, idx) in item.optionText"
                                                  class="optionsClass mobile"
                                                  style="display: inline-block">
                                            {{String.fromCharCode(65 + Number(idx))}}. <span
                                                    v-html="optionItem"></span>
                                            </span>
                                </div>
                            </div>
                            -->
            </div>
            <div class="question__statistic">
              <span
                >班级得分率：{{
                  Number(selectRow.classData[0].clsScoreRate * 100).toFixed(2)
                }}%</span
              >
              <span style="margin-left: 20px"
                >年级得分率：{{ Number(Number(selectRow.grdScoreRate) * 100).toFixed(2) }}%</span
              >
            </div>
            <div class="question__detail">
              <!--答案-->
              <div class="answer_box display_flex align-items_flex-start">
                <strong class="flex_shrink_0">【答案】</strong>
                <div class="flex_1">
                  <div class="answer_content" v-if="item.quesType === 2">
                    {{ item.answer.split(',')[0] === 'A' ? '正确' : '错误' }}
                  </div>
                  <div class="answer_content" v-else v-html="item.answer"></div>
                </div>
              </div>
              <!--考点-->
              <div v-if="item.knowledgeName">
                <strong>【考点】</strong>{{ item.knowledgeName.split(',').join('，') }}
              </div>
              <!--解析-->
              <div v-if="item.analysis" class="answer_box display_flex align-items_flex-start">
                <span class="flex_shrink_0"><strong>【解析】</strong></span>
                <div class="answer_content flex_1" v-html="'' + item.analysis"></div>
              </div>
              <!--<div class="display_flex align-items_flex-start" style="margin-top:5px;">-->
              <!--<strong>【统计】</strong>-->
              <!--<div>-->
              <!--共有{{item.clsFullNum}}人答对，{{item.clsErrorNum}}人答错-->
              <!--</div>-->
              <!--</div>-->
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { getKnowledgePoint } from '@/service/pexam';
import { chooseQuesSearch } from '@/service/pbook';

export default {
  name: 'knowledgeAnalyze',
  props: ['filterData'],
  data() {
    return {
      tableLoading: false,
      noResImg: require('@/assets/no-res.png'),
      showQues: false,
      tableData: [],
      // 平均分列表
      averageScoreList: [],
      // 得分率列表
      scoringList: [],
      classList: [],
      mouseFlag: false,
      mouseOffset: 0,
      posObj: {
        width: 0,
        height: 0,
      },
      listLoading: false,
      expandQuesList: [],
      selectRow: {},
      // 排序字段
      sortType: 1,
      // asc:升序， desc:降序
      orderType: 'asc',
      pagination: {
        page: 1,
        pageSize: 100,
        total: 0,
      },
      isLoading: false,
    };
  },

  watch: {
    filterData: {
      immediate: true,
      deep: true,
      handler(newValue, oldValue) {
        this.geKnowledgePoint();
      },
    },
  },
  mounted() {},
  methods: {
    updateData({ isLoading }) {
    },
    // 分页查询
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.geKnowledgePoint();
      console.log('分页查询');
    },
    // 获取到班级和学科后更新
    updateFilter(data) {
    },
    // 点击题号查看题目详情
    lookDetailQues(row) {
      this.quesList = [];
      if (this.classList.length > 1 || this.filterData.classIds.join(',') == '') return;
      this.showQues = true;
      this.selectRow = row;
      this.$nextTick(() => {
        // 控制题目详情弹窗位置
        let quesDrawer = document.getElementsByClassName('quesDrawer')[0],
          posObjData = this.$refs.tableRef.$el.getBoundingClientRect();
        let height = document.body.clientHeight * 0.8,
          top = (document.body.clientHeight - height) / 2;
        quesDrawer.style.cssText += `width: ${posObjData.width}px;height: ${height}px;
                     margin-left: ${posObjData.left}px;margin-top: ${top}px`;

        // 获取题面
        this.chooseQuesSearch();
      });
    },
    // 获取知识点列表
    geKnowledgePoint() {
      this.isLoading = true;
      this.tableData = [];
      getKnowledgePoint({
        examId: this.$sessionSave.get('reportDetail').examId,
        classIds: this.filterData.classIds.join(','),
        subjectId: this.filterData.subjectId,
        type: this.sortType,
        orderType: this.orderType,
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
        abPaper: this.filterData.abPaper
      })
        .then(data => {
          // this.pagination.total = data.data.total;
          this.tableData = data.data.list || [];

          if (this.tableData.length) {
            let classList = [];
            let clsList = this.$sessionSave.get('innerClassList');
            for (let i = 0; i < this.tableData.length; i++) {
              let arr = [];
              this.tableData[i].classObj = {};
              clsList.forEach(item => {
                this.tableData[i].classData.forEach(ite => {
                  if (item.id == ite.classId) {
                    arr.push(ite);
                    this.tableData[i].classObj[ite.classId] = ite;
                    classList.push(item);
                  }
                });
              });
              this.tableData[i].classData = arr;
            }
            this.classList = classList.filter((item, index) => {
              return classList.findIndex(t => t.id == item.id) === index;
            });
          }

          this.isLoading = false;
        })
        .catch(err => {
          this.tableData = [];
          this.isLoading = false;
        });
    },
    // 获取涉及题目的题面
    chooseQuesSearch(ids) {
      chooseQuesSearch({
        qIds: this.selectRow.quesList,
        subjectId: this.filterData.xfId,
        phaseId: this.filterData.phaseId,
      }).then(data => {
        this.expandQuesList = data.data.sort((x, y) => x.sortOrder - y.sortOrder);
        this.listLoading = false;

        this.$nextTick(() => {
          this.$katexUpdate();
        });
      });
    },
    // 按下鼠标记录鼠标位置
    mouseDownHandler(e) {
      this.mouseOffset = e.clientX;
      this.mouseFlag = true;
    },
    mouseUpHandler(e) {
      this.mouseFlag = false;
    },
    mouseMoveHandler(e) {
      // 这里面需要注意，通过ref需要那个那个包含table元素的父元素
      let divData = this.$refs.tableRef.bodyWrapper;
      if (this.mouseFlag) {
        // 设置水平方向的元素的位置
        divData.scrollLeft -= -this.mouseOffset + (this.mouseOffset = e.clientX);
        // divData.scrollTop -= (- this.mouseOffset + (this.mouseOffset = e.clientY));
      }
    },
    sortChange({ prop, order }) {
      this.sortType = prop === 'frequency' ? 1 : 2;
      this.orderType = order === 'ascending' ? 'asc' : 'desc';
      this.geKnowledgePoint();
    },
  },
};
</script>

<style lang="scss" scoped>
.katex-display {
  font-size: 16px;
  display: inline-block;
  margin: 10px 0;
}

.optionsClass {
  font-size: 16px;
}

.queList__content {
  width: 100%;
  overflow-x: auto;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
}

.expand__queList {
  margin-bottom: 40px;
  /*border-bottom : 1px solid #e4e8eb;*/
  .question__statistic {
    width: 100%;
    height: 48px;
    line-height: 48px;
    background: #f5f8fa;
    border-radius: 3px;
    font-size: 16px;
    color: #4e5668;
    padding-left: 20px;
    margin-top: 10px;
  }
  .question__detail {
    padding: 0px 20px;
    > div {
      margin: 10px 0;
      font-size: 16px;
    }
  }
}

.activeQuesInfo {
  color: #008dea;
  cursor: pointer;
}
</style>
<style lang="scss">
.katex-display {
  margin: 0;
}

.analyze-table {
  margin-top: 15px;
  &.el-table th,
  &.el-table td {
    text-align: center;
  }
  .el-table__header th {
    padding: 4px 0;
  }
  &.el-table thead {
    font-size: 16px;
    color: rgb(63, 74, 84);
  }
}

.quesDrawer {
  position: initial !important;
  border: 1px solid #e4e8eb;
  border-radius: 3px;
  right: initial !important;
  bottom: initial !important;
  margin-bottom: 30px !important;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 0;
}

.analyzePage {
  .el-drawer__container {
    overflow-y: auto;
    overflow-x: hidden;
  }
  /*.el-table--scrollable-x .el-table__body-wrapper {*/
  /*overflow-x : hidden;*/
  /*cursor     : pointer;*/
  /*}*/
}
</style>

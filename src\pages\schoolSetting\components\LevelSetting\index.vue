<template>
  <div>
    <div class="setting-header">
      <div class="title">等级设置</div>
      <div class="header-right" v-if="!examId">
        <el-button type="primary" size="small" @click="reset" :loading="resetLoading">恢复默认</el-button>
        <el-button type="primary" size="small" @click="save" :loading="saveLoading">保存</el-button>
      </div>
    </div>

    <SettingSubjectHeader
      v-if="!examId"
      ref="settingSubjectHeader"
      :currentPhase="currentPhase"
      :currentGradeId="currentGradeId"
      :currentGradeItem="currentGradeItem"
      :currentGradeList="currentGradeList"
      :examId="examId"
      @init="onSubjectInit"
      @change="onSubjectChange"
    >
    </SettingSubjectHeader>

    <el-form
      class="setting-form"
      :model="{ jCfg, sizeColumns }"
      label-suffix="："
      label-position="left"
      hide-required-asterisk
    >
      <div>
        <el-form-item label="等级">
          <el-select size="small" v-model="jCfg.size" @change="onChangeSize">
            <el-option v-for="item in maxSize" :key="item" :value="item">{{ item }}</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="规则">
          <el-radio-group v-model="jCfg.type" size="small" @change="onChangeType">
            <el-radio :label="1">按得分率</el-radio>
            <el-radio :label="2">按排名</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>

      <div>
        <el-table
          class="setting-table"
          :data="currentData"
          style="width: 100%"
          max-height="600"
          border
          v-drag-table
          row-key="id"
        >
          <el-table-column
            prop="name"
            label="等级"
            width="120"
            align="center"
            fixed="left"
            :resizable="false"
          ></el-table-column>
          <el-table-column
            v-for="(item, index) in sizeColumns"
            :key="index"
            align="center"
            min-width="200"
            :resizable="false"
          >
            <template slot="header" slot-scope="scope">
              <el-form-item
                label=""
                :prop="`sizeColumns[${index}]`"
                :rules="[{ required: true, message: '必填项', trigger: 'blur' }]"
              >
                <el-input v-model.trim="sizeColumns[index]" size="mini" style="width: 120px"> </el-input>
              </el-form-item>
            </template>
            <template slot-scope="scope">
              <div class="score-range" v-if="jCfg.type == 1">
                <span v-if="index === 0">[</span>
                <span v-else>(</span>
                <el-form-item
                  label=""
                  :prop="`jCfg.rateData[${scope.$index}].data[${index}].end`"
                  :rules="[{ required: true, message: '必填项', trigger: 'blur' }]"
                >
                  <el-input-number
                    v-model="scope.row.data[index].end"
                    :min="0"
                    :max="100"
                    :disabled="index !== 0"
                    size="mini"
                    :controls="false"
                    @change="(val, oldValue) => changeScoreEnd(scope.row, index, 'end', val, oldValue)"
                  >
                  </el-input-number>
                </el-form-item>
                <span>-</span>
                <el-form-item
                  label=""
                  :prop="`jCfg.rateData[${scope.$index}].data[${index}].start`"
                  :rules="[{ required: true, message: '必填项', trigger: 'blur' }]"
                >
                  <el-input-number
                    v-model="scope.row.data[index].start"
                    :min="0"
                    :max="scope.row.data[index].end"
                    size="mini"
                    :controls="false"
                    @change="(val, oldValue) => changeScoreStart(scope.row, index, 'start', val, oldValue)"
                  >
                  </el-input-number>
                </el-form-item>
                <span>]</span>
              </div>

              <div class="score-range" v-if="jCfg.type == 2">
                <span v-if="index === 0">前</span>
                <span v-else>(</span>
                <el-form-item
                  v-if="index != 0"
                  label=""
                  :prop="`jCfg.rankData[${scope.$index}].data[${index}].start`"
                  :rules="[{ required: true, message: '必填项', trigger: 'blur' }]"
                >
                  <el-input-number
                    v-model="scope.row.data[index].start"
                    :min="0"
                    :max="scope.row.data[index].end"
                    :disabled="index !== 0"
                    :controls="false"
                    size="mini"
                    @change="(val, oldValue) => changeScoreStart(scope.row, index, 'start', val, oldValue)"
                  >
                  </el-input-number>
                </el-form-item>
                <span v-if="index != 0">-</span>
                <el-form-item
                  label=""
                  :prop="`jCfg.rankData[${scope.$index}].data[${index}].end`"
                  :rules="[{ required: true, message: '必填项', trigger: 'blur' }]"
                >
                  <el-input-number
                    v-model="scope.row.data[index].end"
                    :min="scope.row.data[index].start"
                    :max="100"
                    size="mini"
                    :controls="false"
                    @change="(val, oldValue) => changeScoreEnd(scope.row, index, 'end', val, oldValue)"
                  >
                  </el-input-number>
                </el-form-item>
                <span v-if="index != 0">]</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>

    <SettingSaveDialog
      v-if="isShowSaveDialog"
      :currentPhase="currentPhase"
      :currentGradeId="currentGradeId"
      :currentGradeItem="currentGradeItem"
      :currentGradeList="currentGradeList"
      :examId="examId"
      @save="handleSave"
      @closed="isShowSaveDialog = false"
    ></SettingSaveDialog>
  </div>
</template>

<script lang="ts">
import { Component, Mixins, Prop, Ref, Vue } from 'vue-property-decorator';
import SchoolSettingMixin from '../SchoolSetting.mixin';
import SettingSubjectHeader from '../SettingSubjectHeader.vue';
import { getSchCfgAPI, restoreSchCfgAPI, setSchCfgAPI } from '@/service/pexam';
import { SchoolSettingType, SettingChangeParams } from '../../types';
import SettingSaveDialog from '../SettingSaveDialog.vue';

const MAX_SIZE = 50;

interface JCfg {
  size: number;
  type: number;
  rateData: JCfgDatum[];
  rankData: JCfgDatum[];
}

interface JCfgDatum {
  id: string;
  name: string;
  data: {
    name: string;
    start: number;
    end: number;
  }[];
}

@Component({
  components: {
    SettingSubjectHeader,
    SettingSaveDialog,
  },
})
export default class LevelSetting extends Mixins(SchoolSettingMixin) {
  @Ref() settingSubjectHeader!: SettingSubjectHeader;
  // 科目列表
  private subjectList = [];
  // 等级列
  private sizeColumns: string[] = [];
  // 配置项
  private jCfg: JCfg = {
    size: 5,
    type: 1,
    rateData: [],
    rankData: [],
  };
  // 最大等级长度
  private maxSize = MAX_SIZE;
  // 恢复默认loading
  private resetLoading: boolean = false;
  // 保存loading
  private saveLoading: boolean = false;
  // 保存对话框
  private isShowSaveDialog = false;

  get currentData() {
    return this.jCfg.type == 1 ? this.jCfg.rateData : this.jCfg.rankData;
  }

  mounted() {
    if (this.examId) {
      this.getConfig();
    }
  }

  // 学科列表初始化
  onSubjectInit(subjectList) {
    this.subjectList = subjectList;
    this.getConfig();
  }

  // 获取配置项
  async getConfig() {
    const res = await getSchCfgAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      type: SchoolSettingType.LevelSetting,
      phase: this.currentPhase,
      gradeId: this.currentGradeId,
      examId: this.examId,
    });
    if (res.data && res.data.jCfg) {
      this.jCfg = res.data.jCfg;
    } else {
      this.jCfg.size = 5;
      this.jCfg.type = 1;
      this.jCfg.rateData = [];
      this.jCfg.rankData = [];
    }

    let data = this.jCfg.type == 1 ? this.jCfg.rateData : this.jCfg.rankData;

    if (this.jCfg && data.length) {
      this.sizeColumns = data[0].data.map(item => item.name);
      this.subjectList = data.map(item => ({ id: item.id, name: item.name }));
      if (!this.examId) {
        this.settingSubjectHeader.setCurrentSubjectIds(data.map(item => item.id));
      }
    } else {
      this.subjectList = [];
      this.settingSubjectHeader?.resetSubject();
      this.onChangeSize(this.jCfg.size);
    }
  }

  // 学科列表变化
  onSubjectChange(subjectList) {
    let rateData = [];
    let rankData = [];
    this.subjectList = subjectList;

    subjectList.forEach(item => {
      let rateDataItem = this.jCfg.rateData.find(tableItem => tableItem.id == item.id);
      let rankDataItem = this.jCfg.rankData.find(tableItem => tableItem.id == item.id);
      if (rateDataItem || rankDataItem) {
        if (!rateDataItem.name) {
          rateDataItem.name = item.name;
        }
        if (!rankDataItem.name) {
          rankDataItem.name = item.name;
        }
        rateData.push(rateDataItem);
        rankData.push(rankDataItem);
      } else {
        rateData.push({
          id: item.id,
          name: item.name,
          data: this.getDefaultData('rate'),
        });
        rankData.push({
          id: item.id,
          name: item.name,
          data: this.getDefaultData('rank'),
        });
      }
    });
    this.jCfg.rateData = rateData;
    this.jCfg.rankData = rankData;
  }

  // 设置更改
  onSettingChange(data: SettingChangeParams) {}

  // 等级变化
  onChangeSize(size: number) {
    // 生成等级列
    this.sizeColumns = Array.from({ length: this.jCfg.size }, (_, i) => String.fromCharCode(65 + i));
    this.jCfg.rateData = this.subjectList.map(subject => ({
      id: subject.id,
      name: subject.name,
      data: this.getDefaultData('rate'),
    }));
    this.jCfg.rankData = this.subjectList.map(subject => ({
      id: subject.id,
      name: subject.name,
      data: this.getDefaultData('rank'),
    }));
  }

  // 规则变化
  onChangeType(type: number) {}

  // 获取默认数据
  getDefaultData(type: 'rate' | 'rank' = 'rate') {
    let range = [];
    if (this.jCfg.size == 5) {
      range = [100, 85, 70, 60, 40, 0];
    }
    let datas = Array.from({ length: this.jCfg.size }, (_, i) => ({
      name: this.sizeColumns[i],
      end: range.length ? range[i] : Math.floor(100 - (i * 100) / this.jCfg.size),
      start: range.length ? range[i + 1] || 0 : Math.floor(100 - ((i + 1) * 100) / this.jCfg.size) || 0,
    }));
    if (type == 'rate') {
      return datas;
    }

    if (type == 'rank') {
      return datas.map(item => {
        let start = item.start;
        let end = item.end;
        return {
          ...item,
          start: 100 - end,
          end: 100 - start,
        };
      });
    }
  }

  // 恢复默认
  async reset() {
    this.resetLoading = true;
    try {
      await this.restoreSchCfg();
      this.$notify.success({
        title: '成功',
        message: '恢复成功',
      });
      this.settingSubjectHeader?.reset();
    } catch (error) {
      this.$notify.error({
        title: '失败',
        message: '恢复失败',
      });
    } finally {
      this.resetLoading = false;
    }
  }

  // 保存
  async save() {
    await this.checkSizeColumn();
    await this.checkConfig();

    if (this.currentGradeId == '0') {
      this.isShowSaveDialog = true;
    } else {
      this.handleSave([this.currentGradeId]);
    }
  }

  // 保存
  async handleSave(gradeIds: string[]) {
    this.saveLoading = true;
    this.isShowSaveDialog = false;

    try {
      await this.setSchCfg(gradeIds);
      this.$notify.success({
        title: '成功',
        message: '保存成功',
      });
    } catch (error) {
      this.$notify.error({
        title: '失败',
        message: '保存失败',
      });
    } finally {
      this.saveLoading = false;
    }
  }

  // 恢复学校配置
  public restoreSchCfg() {
    return restoreSchCfgAPI({
      phase: this.currentPhase,
      schId: this.$sessionSave.get('schoolInfo').id,
      type: SchoolSettingType.LevelSetting,
      gradeId: this.currentGradeId,
      examId: this.examId,
    });
  }

  // 设置学校配置
  public setSchCfg(gradeIds: string[]) {
    const jCfg = this.getCfg().jCfg;

    const promises = [];
    for (const gradeId of gradeIds) {
      promises.push(
        setSchCfgAPI({
          schId: this.$sessionSave.get('schoolInfo').id,
          schName: this.$sessionSave.get('schoolInfo').schoolName,
          type: SchoolSettingType.LevelSetting,
          phase: this.currentPhase,
          gradeId,
          jCfg,
          examId: this.examId,
        })
      );
    }
    return Promise.all(promises);
  }

  // 获取配置信息
  public getCfg() {
    this.jCfg.rateData.forEach(item => {
      item.data.forEach((item, i) => {
        item.name = this.sizeColumns[i];
      });
    });
    this.jCfg.rankData.forEach(item => {
      item.data.forEach((item, i) => {
        item.name = this.sizeColumns[i];
      });
    });
    return {
      type: SchoolSettingType.LevelSetting,
      jCfg: this.jCfg,
    };
  }

  // 检查等级列
  checkSizeColumn() {
    const uniqueSizeColumns = [...new Set(this.sizeColumns)];
    if (uniqueSizeColumns.length !== this.sizeColumns.length) {
      this.$notify.error({
        title: '【等级设置】',
        message: '等级名称不能相同',
      });
      return Promise.reject(false);
    }
    // 等级名称不能为空
    if (this.sizeColumns.some(item => item === '' || item === null || item === undefined)) {
      this.$notify.error({
        title: '【等级设置】',
        message: '等级名称不能为空',
      });
      return Promise.reject(false);
    }
    return Promise.resolve(true);
  }

  // 检查配置
  checkConfig() {
    if (this.currentData.length == 0) {
      this.$notify.error({
        title: '【等级设置】',
        message: '请选择学科',
      });
      return Promise.reject(false);
    }
    for (const subItem of this.currentData) {
      for (const [index, item] of subItem.data.entries()) {
        // 判断空
        if ((!item.start && item.start !== 0) || (!item.end && item.end !== 0)) {
          this.$notify.error({
            title: '【等级设置】',
            message: '分数区间不能为空',
          });
          return Promise.reject(false);
        }
      }
    }
    return Promise.resolve(true);
  }

  handleLabelInput(index: number, value: string) {
    this.$set(this.sizeColumns, index, value);
  }

  changeScoreEnd(row: JCfgDatum, index: number, prop: 'start' | 'end', val: number, oldVal: number) {
    let json = row.data;
    let keyEnd = 'end';
    let keyStart = 'start';

    // 保留两位小数
    if (json[index][keyEnd] && json[index][keyEnd].toString().split('.')[1]?.length > 2) {
      let val = json[index][keyEnd];
      const factor = Math.pow(10, 2);
      val = Math.round(val * factor) / factor;

      this.$nextTick(() => {
        json[index][keyEnd] = val;
      });
    }

    this.$nextTick(() => {
      if (this.jCfg.type == 1) {
        if (json[index][keyStart] > val) {
          json[index][keyEnd] = oldVal;
          this.$notify.error({
            title: '【等级设置】',
            message: '分数填写左边值应大于等于右边值',
          });
        }
        for (const [i, item] of json.entries()) {
          if (i < index && item[keyStart] < val) {
            json[index][keyEnd] = oldVal;
          }
        }
      }

      if (this.jCfg.type == 2) {
        if (json[index + 1]) {
          json[index + 1][keyStart] = json[index][keyEnd];
          if (json[index + 1][keyStart] > json[index + 1][keyEnd]) {
            json[index + 1][keyEnd] = undefined;
          }
        }
      }
    });
  }

  changeScoreStart(row: JCfgDatum, index: number, prop: 'start' | 'end', val: number, oldVal: number) {
    let json = row.data;
    let keyEnd = 'end';
    let keyStart = 'start';

    // 保留两位小数
    if (json[index][keyStart] && json[index][keyStart].toString().split('.')[1]?.length > 2) {
      let val = json[index][keyStart];
      const factor = Math.pow(10, 2);
      val = Math.round(val * factor) / factor;

      this.$nextTick(() => {
        json[index][keyStart] = val;
      });
    }

    this.$nextTick(() => {
      if (this.jCfg.type == 1) {
        if (json[index][keyEnd] < val) {
          json[index][keyStart] = json[index][keyEnd];
        }
        if (json[index + 1]) {
          json[index + 1][keyEnd] = json[index][keyStart];
          if (json[index + 1][keyEnd] < json[index + 1][keyStart]) {
            json[index + 1][keyStart] = undefined;
          }
        }
      }

      if (this.jCfg.type == 2) {
        if (json[index][keyEnd] < val) {
          json[index][keyStart] = oldVal;
          this.$notify.error({
            title: '【等级设置】',
            message: '排名填写右边值应大于等于左边值',
          });
        }
        for (const [i, item] of json.entries()) {
          if (i < index && item[keyStart] < val) {
            json[index][keyStart] = oldVal;
          }
        }
      }
    });
  }
}
</script>

<style lang="scss" scoped>
@import '../page-style.scss';

.score-range {
  display: flex;
  align-items: center;
  justify-content: center;

  .el-input-number {
    width: 80px;
    margin: 0 4px;

    &::after {
      display: inline-block;
      content: '%';
      position: absolute;
      top: 2px;
      bottom: 0;
      right: 5px;
      color: #c0c4cc;
    }
  }
}

.el-table {
  :v-deep .el-table__header {
    .el-input {
      .el-input__inner {
        padding: 0 5px;
        text-align: center;
      }
    }
  }

  ::v-deep .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
  }
}
</style>

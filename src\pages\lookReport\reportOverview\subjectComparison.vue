<template>
  <div class="subject-wrapper" v-loading="isLoading">
    <div class="subject-comparison-header">
      <div class="titleLine">
        学科对比

        <el-popover
          placement="top-start"
          width="310"
          trigger="hover"
          content="Z分数=（班级平均分-年级均分）/ 年级标准差"
        >
          <i class="el-icon-question" slot="reference"></i>
        </el-popover>
      </div>

      <div class="subject-comparison-header-tip">
        注：Z分数为负数标红显示，代表学科内低于年级平均水平，班级内低分学科为班级相对弱势学科，需要多加关注。
      </div>

      <div>
        <el-button class="pull-right" type="primary" @click="exportSubjectComparison"> 导出 </el-button>
      </div>
    </div>
    <div class="table-box">
      <subject-rate-table v-if="isInit" :tableData="tableData" :filterData="filterData"></subject-rate-table>
    </div>

    <div class="chart-box">
      <div class="titleLine">均分</div>
      <subject-avg-chart
        v-if="isInit"
        :tableData="tableData"
        :schoolTableData="schoolTableData"
        :filterData="filterData"
      ></subject-avg-chart>
    </div>

    <div class="chart-box">
      <div class="titleLine">五率</div>

      <subject-rate-chart v-if="isInit" :tableData="tableData" :filterData="filterData"></subject-rate-chart>
    </div>

    <div class="chart-box" v-if="filterData.classId && tableData.length > 1">
      <div class="titleLine">Z分数</div>
      <subject-z-score-radar-chart
        v-if="isInit"
        :tableData="tableData"
        :schoolTableData="schoolTableData"
        :filterData="filterData"
      ></subject-z-score-radar-chart>

      <div class="relative-subject" v-if="relativeAdvantageSubject && relativeWeakSubject && tableData.length > 1">
        <div class="relative-subject-item advantage">
          <img class="relative-subject-icon" src="@/assets/icon_smile.png" alt="" /> 相对优势学科：{{
            relativeAdvantageSubject
          }}
        </div>
        <div class="relative-subject-item weak">
          <img class="relative-subject-icon" src="@/assets/icon_frown.png" alt="" /> 相对弱势学科：{{
            relativeWeakSubject
          }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { getSubjectRate } from '@/service/pexam';

import SubjectRateTable from '@/components/subjectComparison/SubjectRateTable.vue';
import SubjectRateChart from '@/components/subjectComparison/SubjectRateChart.vue';
import SubjectAvgChart from '@/components/subjectComparison/SubjectAvgChart.vue';
import SubjectZScoreRadarChart from '@/components/subjectComparison/SubjectZScoreRadarChart.vue';

import { getToRoles, findIntersection } from '@/utils/index';
import UserRole from '@/utils/UserRole';

export interface IFilterData {
  classId: string;
  classIds: any[];
  subjectId: string;
  phaseId: number;
  xfId: number;
  classList: ClassList[];
  subjectName: string;
  qType: number;
}

export interface ClassList {
  id: string;
  class_name: string;
  grdId?: string;
  grdName?: string;
  sort?: number;
  isShow?: boolean;
}

export interface ISubjectRateData {
  subjectId: string;
  subjectName: string;
  sourceId: number;
  maxScore: number;
  avgScore: number;
  minScore: number;
  totalNum: number;
  teaName: null;
  fineNum: number;
  fineRate: number;
  goodNum: number;
  goodRate: number;
  passNum: number;
  passRate: number;
  failNum: number;
  failRate: number;
  lowNum: number;
  lowRate: number;
  passAvgNum: number;
  passAvgRate: number;
  tScore: number;
  zScore: number;
}

@Component({
  components: {
    SubjectRateTable,
    SubjectRateChart,
    SubjectAvgChart,
    SubjectZScoreRadarChart,
  },
})
export default class SubjectComparison extends Vue {
  @Prop({
    type: Object,
    default: () => {},
  })
  filterData: IFilterData;
  // 学科对比数据
  tableData: ISubjectRateData[] = [];
  // 校级学科数据
  schoolTableData: ISubjectRateData[] = [];
  // 是否正在加载
  isLoading: boolean = false;
  // 是否初始化数据
  isInit: boolean = false;

  // 相对优势学科
  relativeAdvantageSubject: string = '';
  // 相对弱势学科
  relativeWeakSubject: string = '';

  @Watch('filterData', {
    deep: true,
  })
  async updateFilter(val: IFilterData) {
    try {
      this.tableData = await this.getSubjectRate(this.filterData.classId);
      const validData = this.tableData.filter(item => item.subjectId !== '');
      this.relativeAdvantageSubject =
        validData.find(item => item.zScore == Math.max(...validData.map(item => item.zScore)))?.subjectName || '';
      this.relativeWeakSubject =
        validData.find(item => item.zScore == Math.min(...validData.map(item => item.zScore)))?.subjectName || '';
    } catch (error) {
      this.tableData = [];
    }
  }

  async mounted() {
    try {
      this.schoolTableData = await this.getSubjectRate(''); // 获取校级数据（全部班级数据）
    } catch (error) {
      this.schoolTableData = [];
    }
    await this.updateFilter(this.filterData);
    this.isInit = true;
  }

  async getSubjectRate(classId): Promise<ISubjectRateData[]> {
    let roles = getToRoles(
      this.$sessionSave.get('reportDetail').year,
      this.$sessionSave.get('reportDetail').campusCode
    );
    if (roles.length) {
      roles = roles.map(t => Number(t));
      roles = findIntersection(roles, [1, 2, 5]);
    }
    const res = await getSubjectRate({
      classId: classId,
      examId: this.$sessionSave.get('reportDetail').examId,
      role: roles.join(','),
      v: this.$sessionSave.get('reportDetail').v,
      qType: this.filterData.qType,
    });
    const noTotalData = res.data.filter(item => item.subjectName !== '总分') || [];
    if (noTotalData.length <= 1) {
      res.data = noTotalData;
    }
    return Promise.resolve(res.data);
  }

  // 导出学科对比
  async exportSubjectComparison() {
    const { examId, campusCode, v, year } = this.$sessionSave.get('reportDetail');
    const classList = this.$sessionSave.get('innerClassList');
    const classIds = classList.map(item => {
      if (!item.id) return 'all';
      return item.id;
    });
    let roles = getToRoles(year, campusCode);
    if (roles.length) {
      roles = roles.map(t => Number(t));
      roles = findIntersection(roles, [1, 2, 5]);
    }
    const params: any = {
      examId,
      roleClassId: classIds,
      role: roles.join(','),
      v,
      qType: this.filterData.qType,
    };
    const urlSearch = new URLSearchParams(params);
    let url = process.env.VUE_APP_KKLURL + `/pexam/_/exp-subject-rate?${urlSearch.toString()}`;
    window.open(url);
  }
}
</script>

<style scoped lang="scss">
.subject-wrapper {
  background: #ffffff;
  border-radius: 6px;
  padding-bottom: 30px;
}

.subject-comparison-header {
  display: flex;
  align-items: center;
}

.subject-comparison-header-tip {
  flex: 1;
  color: red;
  font-size: 14px;
  margin-left: 16px;
}

.chart-box {
  position: relative;
  background: #ffffff;
  margin: 20px 0;
}
.avg-chart {
  border: 1px solid #e4e8eb;
  border-radius: 3px;
}

.table-box {
  // border: 1px solid #e4e8eb;
  // border-radius: 3px;
}

.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;
  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 15px;
  }
}

.relative-subject {
  position: absolute;
  top: 50%;
  right: 20px;

  .relative-subject-item {
    margin-bottom: 20px;

    &.advantage {
      color: #67c23a;
    }

    &.weak {
      color: #f56c6c;
    }
  }

  .relative-subject-icon {
    width: 40px;
    height: 40px;
    vertical-align: middle;
    margin-right: 10px;
  }
}
</style>

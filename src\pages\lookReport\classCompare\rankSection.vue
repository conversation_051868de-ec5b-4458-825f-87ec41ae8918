<template>
  <div class="rank-section-box">
    <div class="titleLine">各名次段分析</div>
    <div class="table-box" v-loading="isLoading">
      <RankSectionTable :tableData="tableData" :targetData="targetData"></RankSectionTable>
    </div>
    <div class="chart-box" v-loading="isLoading" id="pane-chart">
      <RankSectionChart
        ref="rankSectionChart"
        :tableData="tableData"
        :targetData="targetData"
      ></RankSectionChart>
    </div>
  </div>
</template>

<script>
import RankSectionChart from '@/components/rankSection/RankSectionChart.vue';
import RankSectionTable from '@/components/rankSection/RankSectionTable.vue';
import { getRankSection } from '@/service/pexam';

export default {
  name: 'rankSection',
  props: ['targetData', 'currentSubId'],
  components: {
    RankSectionChart,
    RankSectionTable,
  },
  data() {
    return {
      activeName: 'chart',
      isLoading: false,
      tableData: [],
      clsList: [],
    };
  },
  watch: {
    async currentSubId(newValue, oldValue) {
      this.clsList = this.$sessionSave.get('innerClassList');
      this.getRankSection();
    },
  },

  async mounted() {
    this.clsList = this.$sessionSave.get('innerClassList');
    this.getRankSection();
  },
  methods: {
    // 获取各名次分布
    async getRankSection() {
      try {
        this.tableData = [];
        const data = await getRankSection({
          subjectId: this.currentSubId,
          examId: this.$sessionSave.get('reportDetail').examId,
        });
        if (!data.data.length) {
          this.tableData = [];
          return;
        }
        var list = [];
        this.clsList.forEach(item => {
          var clz = data.data.find(x => x.classId == item.id);
          if (clz != null) {
            clz.classId = item.id;
            clz.className = item.class_name;
            list.push(clz);
          }
        });
        let tableData = list;
        tableData.forEach(item => {
          let arr = [];
          for (let i in item.score) {
            arr.push(i);
          }
          this.$set(item, 'arr', arr);
        });
        this.tableData = tableData;
      } catch (error) {
        console.log(error);
        this.tableData = [];
      }
    },

    updateData({ isLoading }) {
      // console.log('updatting', isLoading)
      this.isLoading = isLoading;
    },
    handleClick(tab, event) {
      if (tab.name == 'chart') {
        this.$nextTick(() => {
          this.$refs.rankSectionChart.rankSectionChart.resize();
          // this.$refs.fiveRateChart.fiveRateChart.resize();
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.rank-section-box {
  border-radius: 6px;
  padding-bottom: 30px;
  background-color: #fff;

  .chart-box {
    height: 475px;
    background: #ffffff;
    border: 1px solid #e4e8eb;
    border-radius: 3px;
    margin: 20px 0;
  }

  .table-box {
    border: 1px solid #e4e8eb;
    border-radius: 3px;
  }
}
.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;
  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 15px;
  }
}
</style>
<style lang="scss">
.rank-section-box {
  .el-tabs {
    .el-tabs__header {
      margin: 0 0 1px 4px;
    }
    .el-tabs__nav-wrap::after {
      width: 160px;
    }
    .el-tabs__item {
      width: 80px;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      &.is-top:last-child {
        padding-left: 0;
      }
      &.is-top:nth-child(2) {
        padding-right: 0;
      }
    }
  }
}
</style>

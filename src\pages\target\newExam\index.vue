<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-06-11 17:14:49
 * @LastEditors: 小圆
-->
<template>
  <div class="new-exam-setting">
    <div class="select-wrapper">
      <span> 请选择赋分方式: </span>
      <el-select class="setting-select" v-model="activeName">
        <el-option label="新高考赋分" value="new"></el-option>
        <el-option label="自定义赋分" value="custom"></el-option>
      </el-select>
    </div>

    <exam-new v-if="activeName == 'new'"></exam-new>
    <exam-custom v-if="activeName == 'custom'"></exam-custom>
  </div>
</template>

<script lang="ts">
import { setScoreRuleId } from '@/service/pexam';
import { Component, Vue } from 'vue-property-decorator';

import ExamCustom from './exam-custom.vue';
import ExamNew from './exam-new.vue';

type ActiveName = 'new' | 'custom';

@Component({
  components: {
    ExamNew,
    ExamCustom,
  },
})
export default class Index extends Vue {
  // 赋分方式
  activeName: ActiveName = 'new';

  mounted() {
    if (this.$sessionSave.get('reportDetail').customScoreRuleId) {
      this.activeName = 'custom';
    }
    if (!this.$sessionSave.get('reportDetail').scoreRuleId) {
      this.activeName = 'custom';
    }
  }
}
</script>

<style scoped lang="scss">
.new-exam-setting {
  font-size: 14px;
  line-height: 1.5;
  padding-top: 20px;
  padding-left: 10px;
  padding-right: 20px;
}

.select-wrapper {
  margin-bottom: 10px;

  .setting-select {
    margin-left: 10px;
  }
}
</style>

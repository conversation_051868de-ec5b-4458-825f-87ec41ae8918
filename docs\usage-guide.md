# 图片预览功能使用指南

## 功能概述

优化后的图片上传预览功能提供了完整的图片管理体验，包括上传、预览、旋转、缩放、拖拽等功能，并确保预览中的操作能够同步到图片列表。

## 基本操作

### 1. 图片上传
- **拖拽上传**: 将图片文件拖拽到上传区域
- **点击上传**: 点击上传区域选择文件
- **扫描上传**: 点击扫描按钮使用扫描仪

### 2. 图片列表操作
- **预览**: 点击图片缩略图打开预览
- **旋转**: 点击旋转图标（🔄）旋转图片90度
- **删除**: 点击删除图标（🗑️）删除图片
- **交换**: 点击交换图标（↕️）交换两张图片顺序

### 3. 预览模式操作

#### 3.1 基本控制
- **关闭预览**: 点击右上角关闭按钮或按ESC键
- **切换图片**: 点击左右箭头或使用键盘左右方向键
- **图片计数**: 顶部显示当前图片位置（如：1/3）

#### 3.2 缩放功能
- **鼠标滚轮**: 向上滚动放大，向下滚动缩小
- **工具栏按钮**: 点击放大镜图标进行缩放
- **键盘快捷键**: 
  - `+` 或 `=` 键放大
  - `-` 键缩小
  - `0` 键重置视图

#### 3.3 旋转功能
- **工具栏按钮**: 点击旋转图标（🔄）
- **键盘快捷键**: 按空格键旋转
- **同步状态**: 预览中的旋转会自动同步到图片列表

#### 3.4 拖拽功能
- **鼠标拖拽**: 按住鼠标左键拖拽图片
- **视觉反馈**: 拖拽时鼠标指针变为抓手状态

## 键盘快捷键

| 快捷键 | 功能 |
|--------|------|
| `ESC` | 关闭预览 |
| `←` | 上一张图片 |
| `→` | 下一张图片 |
| `空格` | 旋转图片 |
| `+` / `=` | 放大 |
| `-` | 缩小 |
| `0` | 重置视图 |

## 技术特性

### 1. 旋转状态同步
- 预览中的旋转操作会实时同步到图片列表
- 列表中的旋转操作会在预览中保持
- 支持0°、90°、180°、270°四个角度

### 2. 智能适配
- 根据图片旋转角度自动调整显示尺寸
- 确保旋转后的图片完整显示在可视区域内
- 保持图片原始宽高比

### 3. 性能优化
- 使用CSS transform进行硬件加速
- 防抖处理滚轮事件
- 内存泄漏防护

### 4. 用户体验
- 流畅的动画过渡效果
- 直观的视觉反馈
- 响应式设计，支持不同屏幕尺寸

## 注意事项

### 1. 文件格式
- 支持格式：JPG、PNG
- 文件大小限制：20MB以内

### 2. 浏览器兼容性
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### 3. 操作建议
- 上传前确保图片方向正确
- 使用预览功能检查图片质量
- 旋转操作会保持图片原始质量
- 建议按顺序上传多页答题卡

## 故障排除

### 1. 图片无法显示
- 检查网络连接
- 确认图片格式是否支持
- 刷新页面重试

### 2. 旋转功能异常
- 确保浏览器支持CSS transform
- 清除浏览器缓存
- 检查控制台是否有错误信息

### 3. 预览窗口无法关闭
- 按ESC键强制关闭
- 刷新页面
- 检查是否有JavaScript错误

## 更新日志

### v2.0.0 (当前版本)
- ✅ 新增自定义预览组件
- ✅ 实现旋转状态同步
- ✅ 支持鼠标滚轮缩放
- ✅ 添加键盘快捷键支持
- ✅ 优化拖拽体验
- ✅ 改进响应式设计

### v1.0.0 (原版本)
- 基础图片上传功能
- Element UI预览组件
- 简单旋转功能（不同步）

## 技术支持

如遇到问题或需要技术支持，请联系开发团队或查看项目文档。

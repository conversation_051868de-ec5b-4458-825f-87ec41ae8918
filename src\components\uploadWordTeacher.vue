<template>
  <div>
    <dt-modal
      :modalVisible="formVisible"
      :title="editParamsData.id ? '修改' : '上传'"
      :modalWidth="700"
      :sureBtnLoading="sureBtnLoading"
      @click-sure="addSubmit"
      @click-cancel="cancel()"
    >
      <template slot="customSlot">
        <div class="quesCard-upload-div upload-dialog">
          <div class="upload-main">
            <div class="upload-main-top" v-show="!editParamsData.id">
              <a
                class="download-paper"
                :href="`${fsUrl}/aliba/resources/testbank/template/Word试卷模板.docx?response-content-type=application%2Foctet-stream`"
                >下载word模板</a
              >
              <span>按模板上传，制作更智能、更快捷~</span>
            </div>
            <el-upload
              ref="uploadFile"
              :action="uploadUrl"
              :accept="coverAccept"
              :show-file-list="false"
              :on-success="uploadSuccess"
              :before-upload="beforeUpload"
              :data="uploadData"
              :http-request="ossUpload"
              v-show="!editParamsData.id"
            >
              <div class="upload-main-search" style="position: relative">
                <span type="button" id="upload-file" style="z-index: 1"
                  >选择文件
                  <label class="file-name">
                    <p class="limit-info" v-if="formItem.filePath == ''">
                      请选择100M以内的文档上传（doc、docx）
                    </p>
                    <p class="limit-info" v-else>{{ formItem.tbName }}</p>
                  </label>
                </span>
              </div>
            </el-upload>

            <ul>
              <li class="select" v-if="editParamsData.id">
                <div style="width: 100%; text-align: left">
                  <span style="margin-right: 10px">试卷名称:</span>
                  <el-input v-model="formItem.tbName" style="width: 80%" :disabled="isRelatedWork"></el-input>
                </div>
              </li>
              <li class="select">
                <div style="width: 50%; text-align: left">
                  <span style="margin-right: 10px">适用年级:</span>
                  <el-select
                    v-model="formItem.gCode"
                    value-key="greadeCode"
                    style="width: 70%"
                    @change="changeGrade"
                    :disabled="isSupplement || isRelatedWork"
                  >
                    <el-option
                      v-for="item in uploadGradeList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </div>
                <div style="width: 50%; text-align: left">
                  <span style="margin-right: 10px">所属学科:</span>
                  <el-select
                    :disabled="isSupplement || isRelatedWork"
                    v-model="formItem.sCode"
                    value-key="value"
                    style="width: 70%"
                  >
                    <el-option
                      v-for="(item, index) in subjectList"
                      :label="item.name"
                      :value="item.id"
                      :key="item.id"
                    ></el-option>
                  </el-select>
                </div>
              </li>
              <li class="select">
                <div style="width: 50%; text-align: left">
                  <span style="margin-right: 10px">试卷类别:</span>
                  <el-select
                    v-model="formItem.typeCode"
                    :disabled="isSupplement || isRelatedWork"
                    value-key="id"
                    style="width: 70%"
                  >
                    <el-option
                      v-for="item in typeList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </div>
                <div style="width: 50%; text-align: left">
                  <span style="margin-right: 10px">所属年份:</span>
                  <el-select v-model="formItem.year" :disabled="isSupplement || isRelatedWork" style="width: 70%">
                    <el-option
                      v-for="item in yearList"
                      :key="item"
                      :label="item"
                      :value="item"
                    ></el-option>
                  </el-select>
                </div>
              </li>
              <template v-if="!isSupplement && (editParamsData.cardType == 1 || editParamsData.cardType == 2 || !editParamsData.id)">
                <li class="select">
                <div style="text-align: left;line-height: 40px;">
                  <span style="margin-right: 10px">文档加密
                    <el-popover placement="top-right" width="300" trigger="hover">
                      <p>设置加密后，输入密码方可查看、编辑、分享和下载</p>
                      <i class="el-icon-question" slot="reference"></i>
                    </el-popover>:</span>
                  <el-switch
                    v-model="formItem.needCheckPassword"
                    @change="formItem.shareType=0">
                  </el-switch>
                  <template v-if="formItem.needCheckPassword">
                    <el-input style="width: 140px;margin:0 5px;" maxlength="20" v-model="formItem.tbPassword" show-password placeholder="请设置密码"></el-input>
                    <span>发布成绩后自动解密</span>
                  </template>
                </div>
              </li>
              <li class="select">
                <div style="text-align: left;line-height: 40px;">
                  <span style="margin-right: 10px">分享至校本:</span>
                  <el-radio-group v-model="formItem.shareType">
                    <el-radio :label="0">不分享</el-radio>
                    <el-radio v-if="!formItem.needCheckPassword" :label="1">立即分享</el-radio>
                    <el-radio :label="5">发布成绩后分享</el-radio>
                  </el-radio-group>
                </div>
              </li>
              </template>
            </ul>
          </div>
        </div>
      </template>
    </dt-modal>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import DtModal from '../ui/dt-modal';
import { getTBSearchInfo, saveTestBanks } from '@/service/testbank';
import ossUploadFile from '@/utils/ossUploadFile';
import { guid,get_suffix } from '@/utils/index';
import UserRole from '@/utils/UserRole';

export default {
  name: 'upload-word-teacher',
  components: {
    DtModal,
  },
  props: ['formVisible', 'editParamsData', 'uploadGradeList', 'isSupplement'],
  data() {
    return {
      sureBtnLoading: false,
      fsUrl: process.env.VUE_APP_FS_URL,
      formItem: {
        tbId: '', //wordId
        tbName: '', //word名称
        gCode: '', //年级编码
        gName: '', //年级名称
        sCode: '', //学科编码
        sName: '', //学科名称
        year: '', //年份
        phase: '', //学段
        typeCode: '', //word类型编码
        typeName: '', //word类型名称
        shareType: 0, //分享类别(0:不分享 1:分享到校本)
        bookCode: '', //word所挂书本code
        catalogCode: '', //word所挂章节目录code
        catalogName: '', //word所挂章节目录名称
        fileName: '', //文件名称
        filePath: '', //文件路径
        fileSize: 0, //文件大小
        tbPassword: '',
        needCheckPassword:false,
      },
      shareChecked: false,
      coverAccept: '.doc,.docx',
      // coverAccept: "application/msword",
      uploadUrl: '', //word上传地址
      uploadData: {}, //word上传所附带的参数
      filePath:'aliba/resources/testbank',
      typeList: [
        {
          id: '101',
          name: '练习',
        },
      ],
      yearList: [],
      showDlg: false,
      testBankInfo: null,
    };
  },
  watch: {
    // 监测改变
    formVisible: 'checkModalVisible',
  },
  computed: {
    ...mapState(['loginInfo', 'schoolInfo', 'gradeList', 'teacherSubjects']),
    curSubject() {
      return (
        this.$localSave.get('SUBJECT_LIST').find(it => it.id == this.loginInfo.subjectid) || {}
      );
    },
    subjectLists() {
      let allSubjectList = this.$localSave.get('SUBJECT_LIST');
      let tempList = [];
      if (this.loginInfo.multiple_phase && this.loginInfo.multiple_phase != '') {
        tempList = allSubjectList.filter(item => {
          return this.loginInfo.multiple_phase.indexOf(item.phase - 2) >= 0;
        });
      } else if (this.loginInfo.phase && this.loginInfo.phase != '') {
        tempList = allSubjectList.filter(item => {
          return this.loginInfo.phase.indexOf(item.phase - 2) >= 0;
        });
      } else {
        tempList = allSubjectList;
      }
      return tempList;
    },
    subjectList() {
      let subList = [];
      subList = this.subjectLists.filter(item => {
        return item.phase - 2 == this.formItem.phase;
      });
      //综合学科添加至学科列表
      if(this.editParamsData.subjectId?.indexOf(',')>-1){
        subList.push({
          id: this.editParamsData.subjectId,
          isCustom: 0,
          name: this.editParamsData.subjectName,
          phase: "",
          phaseId: "",
          subject_code: "",
          subject_name: this.editParamsData.subjectName,
        })
      }
      // this.formItem.sCode = subList[0].id;
      return subList;
    },
    selectType() {
      return this.typeList.find(it => it.id == this.formItem.typeCode);
    },
    selectGrade() {
      return this.uploadGradeList.find(it => it.id == this.formItem.gCode);
    },
    isRelatedWork(){
      //是否关联考试
      return this.editParamsData.isRelatedWork == 1;
    }
  },
  created(){
    let isPdfParse = this.$sessionSave.get('tbPdfParse')  == "1";
    if(isPdfParse){
      this.coverAccept ='.doc,.docx,.pdf';
    }
  },
  methods: {
    async initSchool() {
      const ret = await UserRole.getUserInfoPersonalityTest();
      ret.userSubList.unshift({
        id: '',
        name: '全部',
        subjectId: '',
        phaseId: '',
      });
      this.uploadGradeList = ret.schGrdList.filter(ite => {
        return ite.id != '';
      });
    },
    /**
     * 监听窗口是否打开
     */
    async checkModalVisible() {
      if(!this.uploadGradeList?.length){
        await this.initSchool();
      }
      this.formItem.tbId = this.editParamsData.id;
      this.formItem.tbName = this.editParamsData.tbName;
      this.formItem.shareType = this.editParamsData.shapeType;
      this.formItem.needCheckPassword = this.editParamsData.needCheckPassword == '1';
      this.shareChecked = this.formItem.shareType == 1;
      let sub = this.$localSave.get('SUBJECT_LIST').find(it => it.id == this.loginInfo.subjectid);
      if (sub && !this.isSupplement) {
        this.formItem.sCode = String(sub.id);
      }
      console.log(this.subjectList);
      console.log(this.formItem.sCode, typeof this.formItem.sCode);
      this.formItem.phase = this.loginInfo.userPhase || this.loginInfo.phase;
      let curGrade = this.uploadGradeList.filter(item => {
        return (
          item.phaseId - 2 == this.loginInfo.userPhase || item.phaseId - 2 == this.loginInfo.phase
        );
      })[0];
      if (curGrade) {
        this.formItem.gCode = curGrade.id;
      }
      await getTBSearchInfo({ phase: this.loginInfo.userPhase || this.loginInfo.phase }, data => {
        this.typeList = data.sources;
        this.yearList = data.years;
        // let sid = data.sources.find(item => item.id == '1').id;
        this.formItem.typeCode = 1;
        this.formItem.year = data.years[0];
        if (JSON.stringify(this.editParamsData) != '{}') {
          this.formItem.typeCode = Number(this.editParamsData.typeCode) || '';
          this.formItem.year = this.editParamsData.year || '';
          this.formItem.gCode = Number(this.editParamsData.gradeCode) || '';
        }
      });
      if (this.isSupplement || JSON.stringify(this.editParamsData) != '{}') {
        this.formItem.sCode = this.editParamsData.subjectId
          ? String(this.editParamsData.subjectId)
          : this.editParamsData.subjectCode || this.subjectList[0].id || '';
        this.formItem.gCode = Number(this.editParamsData.gradeCode);
        this.formItem.typeCode = Number(this.editParamsData.typeCode);
        this.formItem.year = this.editParamsData.year;
      }
      this.showDlg = this.formVisible;
    },
    changeGrade() {
      this.formItem.phase =
        this.uploadGradeList.filter(item => {
          return item.id == this.formItem.gCode;
        })[0].phaseId - 2;
      let tempSubject = this.subjectList.find(it => it.id == this.loginInfo.subjectid) || {};
      if (Object.keys(tempSubject).length > 0) {
        if (this.formItem.phase == tempSubject.phase - 2) {
          this.formItem.sCode = tempSubject.id;
        }
      } else {
        this.formItem.sCode = this.subjectList[0].id;
      }
    },
    /**
     * word上传前
     */
    async beforeUpload(file) {
      let promise = new Promise(async (resolve, reject) => {
        if(file.size > 100*1024*1024 ){
          this.$message.error('文件大小不能超过100MB');
          reject()
          return;
        }
        if(!this.coverAccept.split(',').includes(get_suffix(file.name).toLocaleLowerCase())){
          this.$message.error('文件格式不支持，请选择'+this.coverAccept+'格式的文档');
          reject()
          return;
        }
        let path = this.getDateForPath();
        await ossUploadFile.getSTSToken(path);
        resolve(true);
      });
      return promise; // 通过返回一个promis对象解决
    },
    ossUpload(data){
      return new Promise((resolve, reject) => {
        let path = this.getDateForPath() + guid() + '/f' + get_suffix(data.file.name);
        ossUploadFile.uploadFile(data.file, path, (res) => {
          if (res.code == 1) {
            resolve(res.res)
          } else {
            this.$message.error('上传失败')
            reject(res)
          }
        })
      });
    },
    /**
     * 上传成功回调
     */
    uploadSuccess(response, file, fileList) {
      this.formItem.filePath = '/' + response.name;
      this.formItem.tbName = file.name.substring(0, file.name.lastIndexOf('.'));
      this.formItem.fileName = file.name;
      this.formItem.fileSize = file.size;
    },
    /**
     * 根据当前时间当前用户的学校id和用户id拼接文件夹路径
     * @returns {string}
     */
    getDateForPath() {
      let date = new Date()
      let y = date.getFullYear()
      let m = date.getMonth() + 1
      m = m < 10 ? ('0' + m) : m
      let d = date.getDate()
      d = d < 10 ? ('0' + d) : d
      if (this.loginInfo.schoolid) {
        // 当用户学校id不为空时
        return this.filePath + '/' + y + '/' + m + '/' + d + '/' + this.loginInfo.schoolid + '/' + this.loginInfo.id + '/';
      } else {
        // 当用户学校id为空时
        return this.filePath + '/' + y + '/' + m + '/' + d + '/' + this.loginInfo.id + '/';
      }
    },
    /**
     * word文件太大的提示
     */
    handleMaxSize(file) {
      this.$Message.error('文件' + file.name + '太大，不允许超过100M');
    },
    /**
     * 上传的文件格式验证失败的提示
     */
    handleFormat(file) {
      //4月12 尹格去除可上传pdf提示
      this.$Message.error('文件' + file.name + '格式不支持，请选择doc、docx格式的文档');
    },
    /**
     * 确定保存
     */
    addSubmit() {
      let _this = this;
      if (!this.formItem.tbId) {
        if (!this.formItem.filePath) {
          this.$Message.error('请上传word文档');
          return;
        }
      }
      if(!this.formItem.sCode){
        this.$Message.error('请选择学科');
        return;
      }
      if(!this.formItem.gCode){
        this.$Message.error('请选择年级');
        return;
      }
      if(!this.formItem.typeCode){
        this.$Message.error('请选择类别');
        return;
      }
      if(!this.formItem.year){
        this.$Message.error('请选择年份');
        return;
      }

      this.sureBtnLoading = true;
      this.formItem.typeName = this.selectType.name;
      this.formItem.sName = this.subjectList.filter(item => {
        return item.id == this.formItem.sCode;
      })[0].name;
      this.formItem.gName = this.selectGrade.name;
      // this.formItem.shareType = this.shareChecked ? 1 : 0;
      this.formItem.userId = this.loginInfo.id;
      this.formItem.userName = this.loginInfo.realname;
      this.formItem.schoolId = this.loginInfo.schoolid;
      // this.formItem.phase = this.loginInfo.userPhase || this.loginInfo.phase;

      let para = Object.assign({}, _this.formItem);

      _this.toSaveInfo(para);
    },
    async toSaveInfo(para) {
      let _this = this;
      //试卷转成公式
      para.isConvertFormula = 1;
      para.source = 1;
      //0纯答题卡 1题卡合一 2题卡分离 修改
      para.cardType = this.editParamsData.id ? this.editParamsData.cardType : 1;
      //是否为补录试卷
      para.isRelatedAnswer = this.isSupplement ? 1 : 0;
      para.tbId = this.editParamsData.id || this.editParamsData.tbId;
      if (this.isSupplement) {
        para.tbName = this.editParamsData.tbName;
        para.cardType = 2;
      }
      if(!para.tbPassword){
        delete para.tbPassword;
      }
      if(!para.needCheckPassword){
        para.tbPassword = '';
      }
      saveTestBanks(para)
        .then(function (result) {
          if (result && parseInt(result.code) === 1) {
            _this.$message({
              type: 'success',
              message: '保存成功！',
              duration: '2000',
            });
            _this.cancel(true);
          } else {
            _this.$message.error(result.msg);
            _this.showLoad();
          }
        })
        .catch(function (error) {
          _this.showLoad();
        });
    },
    // 表单提交时报错后不显示提交按钮load
    showLoad() {
      let _this = this;
      setTimeout(function () {
        _this.sureBtnLoading = true;
        _this.$nextTick(() => {
          _this.sureBtnLoading = false;
        });
      }, 100);
    },
    /**
     * 取消
     */
    cancel(success) {
      this.formItem.tbId = '';
      this.formItem.tbName = '';
      this.formItem.gCode = '';
      this.formItem.gName = '';
      this.formItem.sCode = '';
      this.formItem.sName = '';
      this.formItem.year = '';
      this.formItem.phase = '';
      this.formItem.bookCode = '';
      this.formItem.catalogCode = '';
      this.formItem.catalogName = '';
      this.formItem.typeCode = '';
      this.formItem.typeName = '';
      this.formItem.fileName = '';
      this.formItem.filePath = '';
      this.formItem.fileSize = '';
      this.formItem.tbPassword = '';
      this.testBankInfo = null;

      this.sureBtnLoading = false;

      this.$refs.uploadFile.clearFiles();
      //关闭弹窗
      this.$emit('closeDialog', success);
    },
  },
};
</script>

<style lang="scss" scoped></style>
<style lang="css" scoped>
/*@import "../../../library/css/base.css";*/
@import '../styles/questionCard/upload.scss';
</style>
<style lang="scss" scoped>
$mainColor: #008dea;

.upload-dialog {
  ul {
    margin: 0 auto;

    li {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 20px;
      list-style: none;

      &.exam-input-li {
        border: 1px solid $mainColor;
        height: 45px;
        border-radius: 4px;
        position: relative;

        .exam-name {
          position: absolute;
          left: 0;
          top: 0;
          width: calc(100% - 100px);
          height: 100%;
          text-align: left;
          text-indent: 20px;
          line-height: 45px;
          cursor: pointer;
        }

        .exam-input {
          width: 100%;
          height: 100%;
          opacity: 0;
        }

        .exam-label {
          float: right;
          height: 100%;
          width: 100px;
          line-height: 45px;
          background-color: $mainColor;
          border-left: 1px solid $mainColor;
          color: #fff;
          border-radius: 0 4px 4px 0;
          cursor: pointer;
        }
      }

      &.select {
        justify-content: space-between;
      }
    }
  }
}
</style>
<style lang="scss">
.upload-dialog .el-dialog__body {
  padding: 0 20px 50px;
}

.quesCard-upload-div .upload-main-search {
  // display: unset !important;
}

.quesCard-upload-div .el-upload {
  margin-bottom: 10px !important;
  display: block;
}

.quesCard-upload-div .ivu-form-item {
  margin-bottom: 10px !important;
}

.quesCard-upload-div .ivu-form-item-content {
  line-height: 34px !important;
  font-size: 16px !important;
}

.quesCard-upload-div .ivu-select-selected-value {
  font-size: 16px !important;
}

.quesCard-upload-div .ivu-select-item {
  font-size: 16px !important;
}
</style>

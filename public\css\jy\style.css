/*试题中用到的黑色实线表格*/
table.edittable, table.edittable2, table.edittable3 { border-collapse: collapse; margin: 2px; }
table.edittable th, table.edittable td, table.edittable2 th, table.edittable2 td, table.edittable3 th, table.edittable3 td { line-height: 30px; padding: 5px; white-space: normal; word-break: break-all; border: 1px solid #000; vertical-align: middle; }
table.edittable { text-align: center; }
table.edittable2 { text-align: left; }
table.edittable3 { text-align: left; }
table.edittable3 tr:first-child td { text-align: center; }
/*针对一题多个算式排版用*/
table.composition { border-collapse: collapse; text-align: left; margin: 2px; width: 98%; }
table.composition th, table.composition td { line-height: 30px; white-space: normal; word-break: break-all; border-width: 0px; vertical-align: middle; }
/*排版对齐*/
table.composition2 { border-collapse: collapse;width:auto }
table.composition2 th, table.composition2 td {text-align:left;line-height:30px; white-space:normal;word-break:break-all;border:none;border-width: 0px;vertical-align: middle; }
table[name="optionsTable"]{
  margin-left: 20px;
}
table tr td .str{ font-family:  微软雅黑, \5b8b\4f53 !important; }
.MathJye { border: 0 none; direction: ltr; line-height: normal; display: inline-block; float: none; font-family: 'Times New Roman','宋体'; font-size: 15px; font-style: normal; font-weight: normal; letter-spacing: 1px; line-height: normal; margin: 0; padding: 0; text-align: left; text-indent: 0; text-transform: none; white-space: nowrap; word-spacing: normal; word-wrap: normal; -webkit-text-size-adjust: none; }
.MathJye div, .MathJye span { border: 0 none; margin: 0; padding: 0; line-height: normal; text-align: left; height: auto; _height: auto; white-space: normal; }
.MathJye table { border-collapse: collapse; margin: 0; padding: 0; text-align: center; vertical-align: middle; line-height: normal; font-size: inherit; *font-size: 100%; _font-size: 100%; font-style: normal; font-weight: normal; border: 0; float: none; display: inline-block; *display: inline; zoom: 0; }
.MathJye table td { padding: 0; font-size: inherit; line-height: normal; white-space: nowrap; border: 0 none; width: auto; _height: auto; }
/* 编辑题目样式 */
#tinymce table[cellpadding] { border-collapse: collapse; margin: 0; padding: 0; text-align: center; vertical-align: middle; line-height: normal; font-size: inherit; *font-size: 100%; _font-size: 100%; font-style: normal; font-weight: normal; border: 0; float: none; display: inline-block; *display: inline; zoom: 0; }
#tinymce table[cellpadding] td { padding: 0; font-size: inherit; line-height: normal; white-space: nowrap; border: 0 none; width: auto; _height: auto; }
/* .edit-ques-box table[cellpadding] { font-family: 'JyeMath','JyeMathLetters','Times New Roman','宋体'; font-size: 16px!important; border-collapse: collapse; margin: 0; padding: 0; text-align: center; vertical-align: middle; line-height: normal; font-size: inherit; *font-size: 100%; _font-size: 100%; font-style: normal; font-weight: normal; border: 0; float: none; display: inline-block; *display: inline; zoom: 0; }
.edit-ques-box table[cellpadding] td { font-family: 'JyeMath','JyeMathLetters','Times New Roman','宋体'; font-size: 16px!important; padding: 0; font-size: inherit; line-height: normal; white-space: nowrap; border: 0 none; width: auto; _height: auto; }
.quesList-item table[cellpadding] { font-family: 'JyeMath','JyeMathLetters','Times New Roman','宋体'; font-size: 16px!important; border-collapse: collapse; margin: 0; padding: 0; text-align: center; vertical-align: middle; line-height: normal; font-size: inherit; *font-size: 100%; _font-size: 100%; font-style: normal; font-weight: normal; border: 0; float: none; display: inline-block; *display: inline; zoom: 0; }
.quesList-item table[cellpadding] td { font-family: 'JyeMath','JyeMathLetters','Times New Roman','宋体'; font-size: 16px!important; padding: 0; font-size: inherit; line-height: normal; white-space: nowrap; border: 0 none; width: auto; _height: auto; } */
.ques-right table[cellpadding] { font-family: 'JyeMath','JyeMathLetters','Times New Roman','宋体'; font-size: 16px!important; border-collapse: collapse; margin: 0; padding: 0; text-align: center; vertical-align: middle; line-height: normal; font-size: inherit; *font-size: 100%; _font-size: 100%; font-style: normal; font-weight: normal; border: 0; float: none; display: inline-block; *display: inline; zoom: 0; }
.ques-right table[cellpadding] td { font-family: 'JyeMath','JyeMathLetters','Times New Roman','宋体'; font-size: 16px!important; padding: 0; font-size: inherit; line-height: normal; white-space: nowrap; border: 0 none; width: auto; _height: auto; }

.flipv{-ms-transform: scaleX(-1);-moz-transform: scaleX(-1);-webkit-transform: scaleX(-1);-o-transform: scaleX(-1);transform: scaleX(-1);filter: FlipH;}
.fliph{-ms-transform: scaleY(-1);-moz-transform: scaleY(-1);-webkit-transform: scaleY(-1);-o-transform: scaleY(-1);transform: scaleY(-1);filter: FlipV;}

/*加粗*/
.mathjye-bold {font-weight:800}
/*删除线*/
.mathjye-del {text-decoration:line-through}
/*下划线*/
.mathjye-underline {border-bottom:1px solid #000;padding-bottom:1px;}
@-moz-document url-prefix() {.mathjye-underline{padding-bottom:0px;}}
/*点线*/
.mathjye-underpline {border-bottom:2px dotted #000; padding-bottom:3px;}
@-moz-document url-prefix() {.mathjye-underpline {padding-bottom:1px;}}
/*加点*/
.mathjye-underpoint {background: url(https://img.jyeoo.net/images/formula/point.png) no-repeat center bottom; padding-bottom:4px;}
.mathjye-underpoint2 {border-bottom:2px dotted #000; padding-bottom:3px;}
@-moz-document url-prefix() {.mathjye-underpoint{padding-bottom:1px;}}
/*波浪线*/
.mathjye-underwave {background: url(https://img.jyeoo.net/images/formula/wave.png) bottom repeat-x; padding-bottom:4px;}
@-moz-document url-prefix() {.mathjye-underwave {padding-bottom:1px;}}
/*左对齐*/
.mathjye-alignleft {display:block;text-align:left;}
/*居中对齐*/
.mathjye-aligncenter {display:block;text-align:center;}
/*右对齐*/
.mathjye-alignright {display:block;text-align:right;}
/*着重号*/
.tb-dot{
  text-emphasis:circle;text-emphasis-position:under;-webkit-text-emphasis:circle;-webkit-text-emphasis-position:under;xml_id: 80;
}
.tb-wave {
  background: radial-gradient(circle at 10px -7px, transparent 8px, currentColor 8px, currentColor 9px, transparent 9px) repeat-x, radial-gradient(circle at 10px 27px, transparent 8px, currentColor 8px, currentColor 9px, transparent 9px) repeat-x;
  background-size: 20px 20px;
  background-position: -5px calc(100% + 18px), 5px calc(100% - 1px);
  text-decoration: none;
}
/*学科网着重号*/
fieldset.quesborder td, fieldset.queserror td {
  line-height: 25px;
}
font{
  position: relative;
}
font:before{
  content: '';
  width: 100%;
  height: 4px;
  bottom: -4px;
  left: 0;
  background: url(https://zujuan.xkw.com/resource/image/dot.png) repeat-x 0 center;
  position: absolute;
}

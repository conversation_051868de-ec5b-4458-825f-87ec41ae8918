<template>
  <div class="student-paper-list">
    <div class="paper-scan-info" v-if="studentList.length">
      <div class="student-list">
        <p class="left_title">学生名单</p>
        <ul class="student-list-ul">
          <li class="student-item" v-for="student in studentList"
              :key="student.id" :class="{active:student.id===currentStudent.id}"
              @click="currentStudent=student">
            <span class="student-name">{{ student.studentName }}</span>
            <span class="student-score">{{ student.score }}分</span>
          </li>
        </ul>
      </div>
      <div class="student-paper-detail">
        <scan-student v-if="currentStudent && paperInfo" :student-paper-id="currentStudent.id"
                      :student-id="currentStudent.studentId"
                      @student-update="getPaperStudent"
                      @change-student-no="changeStudentNo"
                      :paper-info="paperInfo"></scan-student>
      </div>
    </div>
    <no-data v-else></no-data>

    <el-dialog
        class="edit-number-dialog"
        title="修改考号"
        width="30%"
        :visible.sync="editNumber.show">
      <el-form ref="form" :model="editNumber.info" label-width="120px">
        <el-form-item label="学生姓名：">
          <el-input v-model="editNumber.info.studentName" disabled></el-input>
        </el-form-item>
        <el-form-item label="学生考号：">
          <el-input v-model="editNumber.info.studentNo" disabled></el-input>
        </el-form-item>

        <el-form-item label="新的考号：">
          <el-input v-model="editNumber.studentNo"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editNumber.show = false">取 消</el-button>
        <el-button type="primary" @click="saveStudentNo(false)">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import {mapGetters} from 'vuex';
import {getPaperStudent, updateStudentNo} from "@/service/pexam";
import ScanStudent from "@/components/scan/ScanStudent";
import NoData from "@/components/noData";

export default {
  name: 'scan-student-list',
  props: ['paperId', 'status', 'paperInfo'],

  data() {
    return {
      studentList: [],
      currentStudent: null,
      editNumber: {
        show: false,
        studentNo: '',
        info: {}
      }
    };
  },
  computed: {
    ...mapGetters([
      'filterSubjectList'
    ])
  },
  components: {NoData, ScanStudent},
  mounted() {
    this.init()
  },
  watch: {
    'currentStudent': function (newValue) {

    }
  },
  created() {
    this.initEvent()
  },
  beforeDestroy() {
    this.$bus.$off('updateScanScoreError')
  },
  methods: {
    /**
     * 去编辑学生考号
     * @param studentId
     */
    changeStudentNo(studentId) {
      let student = this.studentList.find(it => it.id === studentId)
      this.editNumber.info = student
      this.editNumber.studentNo = student.studentNo
      this.editNumber.show = true
    },
    /**
     * 保存学生考号
     */
    saveStudentNo(force) {
      updateStudentNo({
        id: this.editNumber.info.id,
        stuNo: this.editNumber.studentNo,
        force: force
      }).then(res => {
        if (res.code === 2) {
          this.forceUpdateNo()
        } else {
          this.editNumber.show = false;
          this.getPaperStudent()
          this.$message.success('更新成功')
          if (res.data.status === 1) {
            this.$bus.$emit('updateScanScoreError')
          }
        }
      }).catch(error => {
      })
    },
    forceUpdateNo() {
      this.$confirm('此考号已绑定学生，是否强制绑定?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.saveStudentNo(true)
      }).catch(() => {
      });
    },
    init() {
      this.getPaperStudent()
    },
    initEvent() {
      this.$bus.$on('updateScanScoreError', () => {
        this.getPaperStudent()
      })
    },
    async getPaperStudent() {
      this.currentStudent = {}
      getPaperStudent({
        paperId: this.paperId,
        status: this.status
      }).then(res => {
        this.studentList = res.data
        this.currentStudent = this.studentList[0]
        this.$emit('student-count', {name: 'score', total: res.data.length})
      })
    }

  }
};
</script>

<style lang="scss" scoped>
.student-paper-list {
  height: calc(100% - 20px);
}

.paper-scan-info {
  width: 100%;
  height: 100%;
  margin-top: 10px;
  display: flex;

  .student-paper-detail {
    width: 100%;
    margin-left: 10px;
  }

  .left_title {
    text-align: center;
    padding: 10px;
    font-family: "Microsoft YaHei", serif;
    font-size: 18px;
  }

  .student-list {
    height: 100%;
    background: white;
    border: solid 1px #EAEAEA;

    .student-list-ul {
      list-style: none;
      width: 250px;
      border-top: solid 1px #EAEAEA;
      height: calc(100% - 48px);
      padding: 10px 0;
      overflow-y: auto;

      .student-item {
        padding: 5px 10px;

        &.active {
          background: #E1EEFF;
        }

        .student-score {
          float: right;


        }
      }
    }
  }
}
</style>

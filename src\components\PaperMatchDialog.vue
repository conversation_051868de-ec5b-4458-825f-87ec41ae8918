<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-10-25 08:43:45
 * @LastEditors: liuyue <EMAIL>
-->
<template>
  <el-dialog :visible.sync="dialogVisible" title="试卷讲评匹配" width="700px" v-on="$listeners">
    <div class="exam-id">当前考试ID：{{ examId }}</div>

    <div class="exam-setting">
      <el-select v-model="subject" value-key="personalBookId" placeholder="请选择学科">
        <el-option v-for="item in subjectList" :key="item.personalBookId" :label="item.name" :value="item">
        </el-option>
      </el-select>

      <el-button-group class="button-group" v-if="subject">
        <el-button type="primary" :loading="loading" @click="setMatch(0)">题号匹配</el-button>
        <el-button type="primary" :loading="loading" @click="setMatch(1)">撤销</el-button>
      </el-button-group>
      <div v-if="subject">点击题号匹配后仍未显示，请点击手动匹配。<el-button type="text" @click="manualMatch">手动匹配>></el-button></div>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { matchReviewAPI } from '@/service/phelp';
import { getExamSubjectMulti } from '@/service/pexam';
import { IAB_CARD_SHEEFT_TYPE, ICARD_STATE } from '@/typings/card';

@Component
export default class PaperMatchDialog extends Vue {
  dialogVisible: boolean = true;
  loading: boolean = false;
  subjectList = [];
  subject = null;

  get examId() {
    return this.$sessionSave.get('reportDetail').examId;
  }
  get examName() {
    return this.$sessionSave.get('reportDetail').examName;
  }

  async getSubjectList() {
    let res = await getExamSubjectMulti({
      examId: this.examId,
      statType: 1,
      v: this.$sessionSave.get('reportDetail').v,
      isPublish: 1,
    });
    res.data.forEach(item => {
      if (item.relateCardType == ICARD_STATE.abPaper || item.relateCardType == ICARD_STATE.abPaperTwo) {
        item.name = item.name + (item.abCardSheetType == IAB_CARD_SHEEFT_TYPE.aCard ? '（A）' : '（B）');
      }
    });

    this.subjectList = res.data;

  }

  // 设置讲评匹配 0系统通过题号匹配，1系统通过回流匹配
  async setMatch(type: 0 | 1) {
    this.loading = true;

    try {
      let res = await matchReviewAPI({
        examId: this.examId,
        subjectId: this.subject.id,
        cardType: type,
      });
      this.$bus.$emit('desktop-change-filter', { type: "subject", refresh: true })
      this.$message.success(res.msg || '匹配成功');
    } catch (err) {
    }

    this.loading = false;
  }

  manualMatch() {
    let query = {
      personBookId: this.subject.personalBookId,
      examName: this.examName,
      examId: this.examId,
      subjectId: this.subject.id,
      bindQueState: this.subject.bindQueState,
      abCardSheetType: this.subject.abCardSheetType
    };
    if (this.subject.relateCardType == ICARD_STATE.default || this.subject.relateCardType == ICARD_STATE.abCard) {
      delete query.abCardSheetType;
    }
    this.$router.push({
      path: '/home/<USER>',
      query
    });
  }

  mounted() {
    this.getSubjectList();
  }
}
</script>

<style lang="scss" scoped>
.exam-id {}

.exam-setting {
  margin-top: 10px;
}

.button-group {
  margin-left: 20px;
}
</style>

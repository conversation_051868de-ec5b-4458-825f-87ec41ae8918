<template>
  <div
    class="reportDetail display_flex flex-direction_column"
    ref="reportDetail"
    :style="{ marginLeft: posLeft + 'px' }"
  >
    <div class="reportDetail_top">
      <div
        class="reportDetail__back el-icon-arrow-left"
        @click="
          $router.push({
            path: '/home/<USER>',
          })
        "
      >
        {{ $sessionSave.get("reportDetail").examName }}
      </div>
      <div>
        <el-button
          class="downloadeBtn"
          type="primary"
          @click="downloadReport"
          >下载报告</el-button>
      </div>
    </div>
    <div class="reportDetail__main flex_1 display_flex align-items_flex-start">
   
      <div class="confirm-table-container">
        <div class="confirm-tips">
          <div class="confirm-tips-header">
            共<span class="titleInfo">{{examStat.classCount}}</span
            >个班，<span class="titleInfo">{{ examStat.stuCount }}</span
            >人参与考试，
            <span class="titleInfo">{{examStat.stuCount - examStat.confirmCount}}</span
            >人待确认
          </div>
          <div>
            <el-radio-group v-model="scoreConfiromInfo.state" @input="changeState">
              <el-radio :label="-1">全部</el-radio>
              <el-radio :label="0">未确认</el-radio>
            </el-radio-group>
          </div>
        </div>
        <el-table
          class="confirm-table"
          :data="studentList"
          :header-cell-style="headerStyle"
          style="width: 100%">
          <el-table-column
            prop="clsName"
            label="班级"
            >
          </el-table-column>
          <el-table-column
            prop="stuName"
            label="姓名"
            >
          </el-table-column>
          <el-table-column
            prop="time"
            label="成绩确认">
            <template slot-scope="scope">
              <span :style="{color:scope.row.confirmState == 0 ? 'red' : ''}">
                {{scope.row.confirmState == 0 ? '未确认' : '已确认'}}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="confirmTime"
            label="确认时间">
            <template slot-scope="scope">
              <span>
                {{scope.row.confirmTime.substring(0,16) || '--'}}
              </span>
            </template>
          </el-table-column>
        </el-table>
        <!--分页器-->
        <el-pagination
          background
          style="margin-bottom: 30px;margin-top:20px"
          :hide-on-single-page="!studentList.length"
          class="text-center"
          layout="total, prev, pager, next"
          @current-change="handleCurrentChange"
          :current-page.sync="scoreConfiromInfo.page"
          :page-size="scoreConfiromInfo.limit"
          :total="totalRows"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { getScoreConfirmStuListAPI, getScoreConfirmStatAPI } from "../service/pexam";
import {getToken} from "@/service/auth"
export default {
  name:'score-confirom-detail',
  components: {
  },
  data(){
    return{
      // 表格头部样式
      headerStyle: {
        background: "rgba(242, 242, 242, 1)",
        color: "#3F4A54",
        fontWeight: "bold",
        fontSize: "15px",
        width: "100%",
        height: "51px",
      },
      posLeft: 0,
      downLoadState: "", //报告下载状态
      isShowDownReport:false,
      //考试概览
      examStat:{},
      studentList:[],//学生列表
      scoreConfiromInfo:{
        state:-1,//确认成绩状态 -1:全部 0:未确认 1:已确认
        page:1,
        limit:50,
        keyWord:''
      },
      totalRows:0,
      kklUrl:process.env.VUE_APP_KKLURL
    }
  },
  mounted() {
    this.$nextTick(() => {
      // 定位内容区的位置
      let windowW = document.body.clientWidth;
      this.posLeft =
        (windowW - this.$refs.reportDetail.getBoundingClientRect().width) / 2;
      this.downLoadState = this.$sessionSave.get("downLoadState");
    });
    this.getScoreStat()
    this.getStuList()
  },
  methods:{
    /**
     * @name:下载报告
     */
    downloadReport() {
      window.open(this.kklUrl + `/pexam/exam/exportScoreConfirmStuList?examId=${this.$sessionSave.get("reportDetail").examId}&token=${getToken()}`,'_self')
    },
    /**
     * @name:改变确认成绩状态
     */
    changeState(){
      this.scoreConfiromInfo.page = 1
      this.getStuList()
    },
    /**
     * @name: 分页查询
     */
    handleCurrentChange(val){
      this.scoreConfiromInfo.page = val
      this.getStuList()
    },
    /**
     * @name:获取参考数据
     */
    getScoreStat(){
      getScoreConfirmStatAPI({
        examId : this.$sessionSave.get("reportDetail").examId
      }).then(res => {
        this.examStat = res.data
      }).catch(err => {

      })

    },
    /**
     * @name:获取学生列表
     */
    getStuList(){
      let params = { examId: this.$sessionSave.get("reportDetail").examId,...this.scoreConfiromInfo}
      getScoreConfirmStuListAPI(params).then(res => {
        this.studentList = res.data.rows
        this.totalRows = res.data.total_rows
      }).catch(err => {
        this.studentList = []
      })
    }
  }

}
</script>

<style lang="scss" scoped>
.reportDetail_top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.downloadeBtn {
  margin-right: 15px;
}
.reportDetail {
  font-family: Microsoft YaHei;
  .reportDetail__back {
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;
    cursor: pointer;
  }
  .reportDetail__main {
    background: #fff;
    width: 100%;
  }
}
.confirm-table-container{
  width: 100%;
}
.confirm-tips{
  padding: 20px 20px;
  display: flex;
  justify-content: space-between;
  .confirm-tips-header{
    font-size:16px;
  }
}
.titleInfo{
  font-size: 20px;
  font-weight: bold;
  color: #3f4a54;
}
.confirm-table{
  padding: 0 10px;
}
</style>
<!--
 * @Description: 
 * @Author: liuyue y<PERSON><PERSON>@class30.com
 * @Date: 2024-05-11 14:04:00
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2024-07-04 15:53:58
-->
<template>
    <div>
        <Editor ref="rContent" v-model="myValue" :id="tinymceId" :init="init" :disabled="disabled"></Editor>
    </div>
</template>

<script>

import tinymce from 'tinymce/tinymce'
import Editor from '@tinymce/tinymce-vue'
import 'tinymce/themes/mobile/theme'
import 'tinymce/themes/silver/theme'
import 'tinymce/icons/default/icons' ////解决了icons.js 报错Unexpected token '<'
import 'tinymce/plugins/advlist'
import 'tinymce/plugins/autolink'
import 'tinymce/plugins/lists'
import 'tinymce/plugins/link'
import 'tinymce/plugins/image'
import 'tinymce/plugins/charmap'
import 'tinymce/plugins/print'
import 'tinymce/plugins/preview'
import 'tinymce/plugins/anchor'
import 'tinymce/plugins/searchreplace'
import 'tinymce/plugins/visualblocks'
import 'tinymce/plugins/fullscreen'
import 'tinymce/plugins/code'
import 'tinymce/plugins/insertdatetime'
import 'tinymce/plugins/media'
import 'tinymce/plugins/table'
import 'tinymce/plugins/paste'
import 'tinymce/plugins/help'
import 'tinymce/plugins/wordcount'
import 'tinymce/plugins/imagetools'
import 'tinymce/plugins/directionality'
import 'tinymce/plugins/visualchars'
import 'tinymce/plugins/template'
import 'tinymce/plugins/codesample'
import 'tinymce/plugins/hr'
import 'tinymce/plugins/pagebreak'
import 'tinymce/plugins/nonbreaking'
import 'tinymce/plugins/advlist'
import 'tinymce/plugins/textpattern'
import 'tinymce/plugins/emoticons'
import 'tinymce/plugins/autosave'
import 'tinymce/plugins/autoresize'
import ossUploadFile from '@/utils/ossUploadFile'
import { generateUUID } from '@/utils/index'
import { replaceALiUrl } from '@/utils/common'

export default {
    name: 'TinymceEditor',
    components: { Editor },
    props: {
        id: {
            type: String,
            default: function () {
                return `vue-tinymce-${new Date().getTime()}${(Math.random() * 1000).toFixed(0)}`
            }
        },
        value: {
            type: String,
            default: ''
        },
        height: {
            type: [Number, String],
            required: false,
            default: 700
        },
        width: {
            type: [Number, String],
            required: false,
            default: 'auto'
        },
        disabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            tinymceId: this.id,
            myValue: this.value,
            oldValue: '',
            init: {
                selector: `#${this.id}`,
                content_style: "p {margin: 0; border:0; padding: 0;}",
                language_url: '/bigdata/tinymce/langs/zh_CN.js',
                language: 'zh_CN',
                skin_url: '/bigdata/tinymce/skins/ui/oxide',
                height: this.height,
                body_class: 'panel-body ',
                object_resizing: true,//是否允许调整图像大小.
                branding: false, //隐藏右下角由TINY驱动
                contextmenu_never_use_native: true, //防止浏览器上下文菜单出现在编辑器中
                elementpath: false, //隐藏底栏的元素路径（隐藏右下角元素显示）
                toolbar: [
                    'formatselect fontselect fontsizeselect insert styleselec | undo redo removeformat | bold italic underline strikethrough superscript subscript backcolor forecolor | numlist bullist | table | searchreplace fullscreen',
                    'alignleft aligncenter alignright alignjustify lineheight outdent indent | link unlink image media | preview code',
                ],
                menubar: false,
                plugins: 'lists link anchor code wordcount image table visualchars visualblocks searchreplace preview pagebreak nonbreaking media insertdatetime fullscreen directionality autosave autolink advlist',
                font_formats: "微软雅黑='微软雅黑';宋体='宋体';黑体='黑体';仿宋='仿宋';楷体='楷体';隶书='隶书';幼圆='幼圆';Andale Mono=andale mono,times;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier;Georgia=georgia,palatino;Helvetica=helvetica;Impact=impact,chicago;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco;Times New Roman=times new roman,times;Trebuchet MS=trebuchet ms,geneva;Verdana=verdana,geneva;Webdings=webdings;Wingdings=wingdings", //字体
                font_size_formats: '9px 10px 11px 12px 14px 16px 18px 20px 22px 24px 26px 28px 36px 42px 48px 72px',
                // 配置工具栏时添加的 fontsizeinput 工具可以展示一个可输入字号的组件，默认单位是pt，现改为px
                font_size_input_default_unit: 'px',
                paste_retain_style_properties: 'all', //从Word中复制，保留所有样式
                //paste_word_valid_elements: '*[*]', // 该设置在自word粘贴时，允许指定元素和属性保存在过滤结果中，要使用此功能，paste_enable_default_filters 要设置为true
                paste_data_images: true, // 粘贴的同时能把内容里的图片自动上传，非常强力的功能
                paste_convert_word_fake_lists: false, // 设为false可禁用复制word中的列表内容时，转换为html的UL或OL格式。
                paste_webkit_styles: 'all',
                paste_merge_formats: true, //启用PowerPaste插件的合并格式功能，例如：<b>abc <b>bold</b> 123</b>成为<b>abc bold 123</b>
                end_container_on_empty_block: true, //如果在空的内部块元素中按下Enter键，则可以使用此选项拆分当前容器块元素。
                // powerpaste_word_import: 'clean', //保留标题，表格和列表等内容的结构，但删除内联样式和类。这样就产生了使用站点CSS样式表的简单内容，同时保留了原始文档的语义结构。
                advlist_bullet_styles: 'default,circle,disc,square',
                advlist_number_styles: 'default,lower-alpha,lower-greek,lower-roman,upper-alpha,upper-roman',
                default_link_target: '_blank', //默认链接是当前窗口打开，你也可以通过此参数将其变为_blank新窗口打开。
                link_title: false,
                nonbreaking_force_tab: true, // 按tab键插入三个&nbsp;。一直按一直插。
                // images_upload_handler: this.images_upload_handler, //该images_upload_handler选项允许你指定被用来替代TinyMCE的默认的JavaScript和定制逻辑上传处理函数的函数。
                convert_urls: false,
                file_picker_types: 'file media',
                file_picker_callback: (cb, value, meta) => {
                    let input = document.createElement('input');
                    input.setAttribute('type', 'file');
                    let _this = this;
                    input.onchange = function () {
                        let file = this.files[0]; //只选取第一个文件。如果要选取全部，后面注意做修改
                        let name = file.name
                        let path = _this.handleUploadPath(name)
                        ossUploadFile.uploadFile(file, path, function (res) {
                            cb(replaceALiUrl(res.res.name), {
                                title: name
                            })
                        })
                    }
                    input.click();
                },
                urlconverter_callback: function (url, node, on_save, name) {
                    if (node === 'img' && url.startsWith('blob:') || url.startsWith('data:')) {
                        // console.log('urlConverter:', url, 'node',node, on_save, name)
                        tinymce.activeEditor && tinymce.activeEditor.uploadImages()
                    }
                    return url;
                },
                // 此处为图片上传处理函数 该方法为工具栏上传图片 需要走后台上传接口 与上面的方法区分开
                images_upload_handler: (blobInfo, success, failure) => {
                    let base64 = 'data:image/jpeg;base64,' + blobInfo.base64();
                    let name = blobInfo.filename()
                    let path = this.handleUploadPath(name)
                    ossUploadFile.uploadBase64File(base64, path, name, function (res) {
                        success(replaceALiUrl(res.res.name))
                    })
                },
                setup: (editor) => {
                    let self = this;
                    editor.on('init', function (e) {
                        this.setContent(self.value)
                        this.getBody().style.fontSize = '14pt';
                        this.getBody().style.fontFamily = '微软雅黑';
                    });
                    editor.on('change',function(e){
                        self.$emit('input',e.target.getContent())
                    })
                },
                content: this.value
            }
        }
    },
    watch: {
        value(newValue) {
            this.myValue = newValue
        },
        myValue(newValue) {
            // this.$emit('input', newValue)
        }
    },
    mounted() {
        tinymce.init({});
        ossUploadFile.getSTSToken()
    },
    methods: {
        /**
     * 处理上传资源地址
     */
        handleUploadPath(fileName) {
            let filePath = 'gece_image' + this.getDateForPath();
            let uuid = generateUUID();
            filePath = filePath + uuid + '/' + fileName;
            return filePath;
        },
        /**
         * 根据当前时间拼接文件夹路径
         * @returns {string}
         */
        getDateForPath() {
            let date = new Date();
            let y = date.getFullYear();
            let m = date.getMonth() + 1;
            let month = m < 10 ? '0' + m : m;
            let d = date.getDate();
            let day = d < 10 ? '0' + d : d;
            //当用户学校id为空时
            return '/help/' + y + '/' + month + '/' + day + '/';
        }
    }
}
</script>

<style lang="scss" scoped>
.tinymce-container {
    position: relative;
    line-height: normal;
}

.tinymce-container {
    ::v-deep {
        .mce-fullscreen {
            z-index: 10000;
        }
    }
}

.tinymce-textarea {
    visibility: hidden;
    z-index: -1;
}

.editor-custom-btn-container {
    position: absolute;
    right: 4px;
    top: 4px;
    //z-index: 2005;
}

.fullscreen .editor-custom-btn-container {
    z-index: 10000;
    position: fixed;
}

.editor-upload-btn {
    display: inline-block;
}
</style>
<style>
.oe-editor-wrap {
    position: relative;
}

.oe-editor-del-btn {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 3010;
    padding: 0 2px 1px;
    border-left: 1px solid #DCDFE6;
}

.oe-editor-del-btn img {
    height: 36px;
}

.tox .tox-dialog-wrap__backdrop {
    z-index: 3000;
    background-color: rgba(0, 0, 0, .6);
}

.tox .tox-dialog {
    z-index: 3001;
}

/*调整堆叠顺序，让下拉列表等显示*/
.tox-tinymce-aux {
    z-index: 3002 !important;
}

.tox-tinymce {
    border: 1px solid #DCDFE6 !important;
}

.tox .tox-statusbar {
    border-top: 1px solid #DCDFE6 !important;
}

.tox:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {
    border-right: 1px solid #DCDFE6 !important;
}

.tox .tox-toolbar,
.tox .tox-toolbar__overflow,
.tox .tox-toolbar__primary {
    background: none !important;
    border-bottom: 1px solid #DCDFE6;
}

.tox .tox-tbtn svg {
    fill: #7b8397 !important;
}
</style>
/*
 * @Description:
 * @Author: 小圆
 * @Date: 2025-03-24 09:44:20
 * @LastEditors: 小圆
 */
export interface Catalog {
  id: string;
  title: string;
  pid: string;
  code: string;
  quesCount: string;
  startPageCode: string;
  endPageCode: string;
  expand: boolean;
  sort: number;
  pcode: string;
  isOpen: number;
  hasSchoolRes: string;
  children: Catalog[];
}

export interface ScanData {
  classCount: number;
  imgCount: number;
  catalog_code: string;
  stuCount: number;
  /** workId */
  workId: string;
}

export interface DirRate {
  examId: number;
  workId: null;
  isPublish: boolean;
  submitNum: number;
  submitRate: number;
  noSubmitNum: number;
  scoreRate: number;
  avgScore: number;
  fullScore: number;
  passRate: number;
  clzList: ClzList[];
  qsList: QsList[];
}

export interface ClzList {
  id: string;
  name: string;
  examId: number;
  workId: string;
  isPublish: boolean;
  submitNum: number;
  submitRate: number;
  noSubmitNum: number;
  scoreRate: number;
  avgScore: number;
  fullScore: number;
  passRate: number;
}

export interface QsList {
  id: string;
  qsNo: number;
  tQsNo: string;
  qsNoDesc: string;
  clsRate: number;
  grdRate: number;
}

import UserRole from '@/utils/UserRole';
import { Login, getUserSchoolRole, getLoginUserInfoAPI } from '@/service/api';
import { setToken, setUserId } from '@/service/auth';
import { getVipInfo } from '@/service/pexam';
import { TrackHelper } from '@/plugins/track-helper';
import { checkWeakPassword } from '@/utils/index';
const publicTrack = TrackHelper.Instance.PublicTrack.bind(TrackHelper.Instance);
export default {
  data() {
    return {
      isLoading: false,
      dialogVisible: false,
      accountValue: '',
      passwordValue: '',
      currentSelType: null,
      canSubmit: true,
      userTypes: [],
      userList: [],
      loadXKWLogout: false,
      showForgetPwd: false,
    };
  },
  
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.userTypes = [];
      }
    },
  },

  mounted() {
    sessionStorage.clear();
    UserRole.dispose();
    if (this.$route.query.type == 'sso') {
      ssoClient.check(process.env.VUE_APP_APPID, false);
      const userInfo = ssoClient.getUserInfo();
      const userId = this.$route.query.userid || this.$route.query.userId || userInfo.id;
      if (userId) {
        this.ssoLogin(userId);
        return;
      }
    }

    document.onkeydown = e => {
      let key = window.event.keyCode;
      if (key === 13) {
        if (this.dialogVisible) {
          this.confirmSubmit();
          return;
        }
        this.loginWithNoType();
      }
    };
    
    this.accountValue = this.accountValue || this.$localSave.get('account_number') || '';
  },

  methods: {
    async ssoLogin(userId) {
      await this.checkXkwLogin(userId);
      const data = await getLoginUserInfoAPI({ userId: userId });
      this.$localSave.set('mk_xkw_account', userId);
      let item = data.data;
      let schoolInfo = this.setSchool();
      if (!schoolInfo) {
        schoolInfo = {
          id: data.data.schoolid,
          school_name: data.data.schoolName || data.data.school_name,
        };
      }
      this.getVipInfo(schoolInfo);
      this.$store.commit('saveSchoolInfo', schoolInfo);
      ssoClient.setCookie(data.data.token);
      setToken(data.data.token);
      setUserId(data.data.id);
      document.onkeydown = null;
      
      this.handleUserRole(item, data.data);
    },

    setSchool() {
      return '';
    },

    selectType(item) {
      this.currentSelType = item.type;
    },

    confirmSubmit() {
      if (!this.currentSelType) {
        this.$message.error('请选择用户身份！');
        return;
      }
      this.canSubmit = false;
      if (this.currentSelType == 1) {
        this.loginWithType(1);
      }
      if (this.currentSelType == 4) {
        let loginData = this.userList.find(item => item.admin_type == 2);
        this.loginWithType(loginData.user_type);
      }
      if (this.currentSelType == 5) {
        this.loginWithType(5);
      }
      this.canSubmit = true;
    },

    validateUserInfo() {
      if (!this.accountValue) {
        this.$message({
          message: '用户名不能为空！',
          type: 'error',
          duration: 1500,
        });
        return false;
      }
      if (!this.passwordValue) {
        this.$message({
          message: '密码不能为空！',
          type: 'error',
          duration: 1500,
        });
        return false;
      }
      return true;
    },

    async checkXkwLogin(key) {
      const lastAccount = this.$localSave.get('mk_xkw_account');
      if (lastAccount?.trim() != key.trim()) {
        this.loadXKWLogout = true;
        await this.$nextTick();
      }
    },

    handleUserRole(item, data) {
      if (item.admin_type == 2) {
        // 校长
        this.$sessionSave.set('loginInfo', { account_type: 4, ...data });
      } else if (item.user_type == 5 && item.admin_type != 2) {
        // 运营
        this.$sessionSave.set('loginInfo', { account_type: 5, ...data });
      }
      if (item.admin_type == 2 || (item.user_type == 5 && item.admin_type != 2)) {
        this.goToUrl();
      }
      if (item.user_type == 1 && item.admin_type != 2) {
        this.$sessionSave.set('loginInfo', { ...data });
        this.getUserSchoolRole(data);
      }
      if (item.user_type != 1 && item.user_type != 5 && item.admin_type != 2) {
        this.$message.error('该账号角色不匹配！');
      }
    },

    async loginWithNoType() {
      await this.checkXkwLogin(this.accountValue);
      this.userTypes = [];
      if (!this.validateUserInfo()) return;
      this.isLoading = true;
      
      try {
        const data = await Login({
          account: this.accountValue,
          passWord: this.passwordValue,
          logintype: 22,
        });
        
        this.$localSave.set('mk_xkw_account', this.accountValue);
        this.isLoading = false;
        this.$localSave.set('account_number', this.accountValue);

        if (data.code == 2 && Array.isArray(data.data)) {
          this.handleMultipleRoles(data.data);
        } else if (data.code == 1) {
          this.handleSingleRole(data);
        } else {
          this.$message.error(data.msg);
        }
      } catch (err) {
        if (err.code !== -1) {
          this.$message.error(err.msg);
        }
        this.isLoading = false;
      }
    },

    handleMultipleRoles(roles) {
      this.userList = roles.filter(item => {
        if (item.user_type == 1 && item.admin_type != 2) {
          this.userTypes.push({ name: '教师', type: 1 });
        } else if (item.user_type == 5 && item.admin_type != 2) {
          this.userTypes.push({ name: '运营', type: 5 });
        }
        if (item.admin_type == 2) {
          this.userTypes.push({ name: '校管', type: 4 });
        }
        return item.user_type == 1 || item.user_type == 5 || item.admin_type == 2;
      });

      if (this.userList.length == 0) {
        this.$message.error('无权访问该网站！');
        return;
      }

      if (this.userTypes.length == 1) {
        this.selectType(this.userTypes[0]);
        this.$nextTick(() => {
          this.confirmSubmit();
        });
      } else {
        this.dialogVisible = true;
      }
    },

    handleSingleRole(data) {
      let item = data.data;
      let schoolInfo = this.setSchool();
      if (!schoolInfo) {
        schoolInfo = {
          id: data.data.schoolid,
          school_name: data.data.school_name,
        };
      }
      this.getVipInfo(schoolInfo);
      this.$store.commit('saveSchoolInfo', schoolInfo);
      setToken(data.data.token);
      setUserId(data.data.id);
      ssoClient.setCookie(data.data.token);
      document.onkeydown = null;

      if (checkWeakPassword(this.passwordValue)) {
        this.$sessionSave.set('weakPassword', true);
        this.$sessionSave.set('changePwdVisible', true);
      }

      this.handleUserRole(item, data.data);
    },

    getUserSchoolRole(loginData) {
      getUserSchoolRole({
        schoolId: loginData.schoolid,
        userId: loginData.id,
      })
        .then(data => {
          let loginInfo = this.$sessionSave.get('loginInfo');
          if (data.code == 1) {
            if (data.data.roleType == 0 || data.data.roleType == 4) {
              // 普通教师
              this.$sessionSave.set('loginInfo', {
                account_type: 1,
                ...loginInfo,
              });
            }
            if (data.data.roleType == 1) {
              // 年级组长
              this.$sessionSave.set('loginInfo', {
                account_type: 3,
                ...loginInfo,
              });
            }
            if (data.data.roleType == 2 || data.data.roleType == 3) {
              // 备课组长
              this.$sessionSave.set('loginInfo', {
                account_type: 2,
                ...loginInfo,
              });
            }
            this.goToUrl();
          } else {
            this.$message.error(data.msg);
          }
        })
        .catch(err => {
          this.$message.error(err.msg);
        });
    },

    loginWithType(userType) {
      Login({
        account: this.accountValue,
        passWord: this.passwordValue,
        logintype: 22,
        userType: userType,
      })
        .then(data => {
          this.canSubmit = true;
          let loginData = data.data;
          TrackHelper.Instance.UpdateLoginInfo(loginData);
          let schoolInfo = this.setSchool();
          if (!schoolInfo) {
            schoolInfo = {
              id: data.data.schoolid,
              school_name: data.data.school_name,
            };
          }
          this.$store.commit('saveSchoolInfo', schoolInfo);
          this.getVipInfo(schoolInfo);
          if (data.code !== 1) {
            this.$message.error(data.msg);
            return;
          }
          setToken(data.data.token);
          ssoClient.setCookie(data.data.token);
          setUserId(data.data.id);
          document.onkeydown = null;

          if (checkWeakPassword(this.passwordValue)) {
            this.$sessionSave.set('weakPassword', true);
            this.$sessionSave.set('changePwdVisible', true);
          }

          this.handleUserRole(loginData, data.data);
        })
        .catch(err => {
          this.$message.error(err.msg);
        });
    },

    goToUrl() {
      publicTrack("0118", { "login_id": this.$sessionSave.get('loginInfo').id,"client_time": new Date().getTime() });
      if (this.$route.query.redirect) {
        this.$router.push({ path: this.$route.query.redirect });
      } else {
        this.$router.push({ path: '/' });
      }
      this.$store.dispatch('initData');
    },

    getVipInfo(schoolInfo) {
      getVipInfo({ schoolId: schoolInfo.id })
        .then(res => {
          const vipInfo = res.data;
          vipInfo.isVip = false;
          if (JSON.stringify(vipInfo) != '{}') {
            vipInfo.dateExpired = vipInfo.dateExpired.substring(0, 10);
            vipInfo.dateStart = vipInfo.dateStart.substring(0, 10);
            //当前时间
            let curTime = res.responsetime;
            //服务到期时间
            let dateExpired = new Date(vipInfo.dateExpired);
            if (curTime < dateExpired.getTime()) {
              vipInfo.isVip = true;
              vipInfo.showTip = (dateExpired.getTime() - curTime) / (1000 * 60 * 60 * 24) <= 15;
            }
          } else {
            vipInfo.dateExpired = '1999-01-01';
            //未设置过期时间
            vipInfo.isNotUsed = true;
          }
          store.commit('savevipInfo', vipInfo);
        })
        .catch(err => {});
    },

    handleForgetPassword() {
      this.showForgetPwd = true;
    },

    // 返回C30登录
    backLogin() {
      this.showForgetPwd = false;
    },

    // 更新数据库密码
    updatePassword(pwd, phone) {
      this.passwordValue = pwd;
    },
  }
}; 
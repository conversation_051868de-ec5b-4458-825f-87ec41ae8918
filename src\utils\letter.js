/**
 * @description: 字母工具
 */
// 是否全英文
export function isPureEnglish(str) {
    return /^[A-Z]+$/.test(str);
}

// 获取元素的所有组合
export function getAllCombinations(options) {
    const result = [];
    const totalCombinations = 1 << options.length; // 2^n

    for (let i = 0; i < totalCombinations; i++) {
        const combination = [];
        for (let j = 0; j < options.length; j++) {
            if (i & (1 << j)) {
                combination.push(options[j]);
            }
        }
        result.push(combination);
    }
    return result;
}

// 根据数字获取字母
export function generateLetters(count) {
    const letters = [];
    const startCharCode = 'A'.charCodeAt(0);
    for (let i = 0; i < count; i++) {
        letters.push(String.fromCharCode(startCharCode + i));
    }
    return letters;
}
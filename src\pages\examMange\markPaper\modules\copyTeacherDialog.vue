<template>
  <div>
    <el-dialog
      custom-class="seled-tea-container"
      :visible="modalVisible"
      width="890px"
      :before-close="closeModal"
      :close-on-click-modal="false"
    >
      <div slot="title" class="dialog-title">
        <span style="margin-right: 10px">复制阅卷任务</span>
      </div>
      <div class="sele-tea-area">
        <el-form :model="copyInfo" :rules="rules" ref="copyInforef">
          <el-form-item label="请选择题块" prop="checkedQues">
            <el-checkbox
              :indeterminate="quesIndeterminate"
              v-model="isCheckdAllQues"
              @change="handleCheckAllQues"
              >全选</el-checkbox
            >
            <el-checkbox-group v-model="copyInfo.checkedQues" @change="handleCheckedQues">
              <el-checkbox
                v-for="(ques, quesIndex) in quesList"
                :label="ques.quesNos"
                :key="ques.quesNos"
                >{{ ques.titleNos }}</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="请选择模块" prop="checkedModes">
            <el-checkbox
              :indeterminate="modesIndeterminate"
              v-model="isChaeckedAllModes"
              @change="handleCheckAllModes"
              :disabled="progress >= 4"
              >全选</el-checkbox
            >
            <el-checkbox-group v-model="copyInfo.checkedModes" @change="handleCheckedModes">
              <el-checkbox
                v-for="(item, index) in modes"
                :label="item.key"
                :key="index"
                :disabled="progress >= 4 && item.key == 'correctMode'"
                >{{ item.name }}</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>
      <!-- </div> -->
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeModal">取 消</el-button>
        <el-button type="primary" :loading="isComfirming" @click="sureClick">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    modalVisible: {
      type: Boolean,
      required: true,
    },
    quesList: {
      type: Array,
      required: true,
    },
    progress: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      isComfirming: false,
      copyInfo: {
        checkedQues: [],
        checkedModes: [],
      },
      rules: {
        checkedQues: [{ required: 'array', message: '请至少选择一个题块', trigger: 'change' }],
        checkedModes: [{ required: 'array', message: '请至少选择一个模块', trigger: 'change' }],
      },
      modes: [
        //评阅方式
        {
          key: 'correctMode',
          name: '评阅方式',
        },
        {
          key: 'assignType',
          name: '阅卷分配',
        },
        {
          key: 'leaderList',
          name: '题组长',
        },
        {
          key: 'teacherList',
          name: '阅卷教师',
        },
        {
          key: 'arbitration',
          name: '仲裁设置及仲裁教师',
        },
      ],
      //是否全选题目
      isCheckdAllQues: false,
      //全选状态
      quesIndeterminate: false,
      //是否全选模块
      isChaeckedAllModes: false,
      //模块全选状态
      modesIndeterminate: false,
    };
  },
  mounted() {},
  methods: {
    closeModal() {
      this.$emit('close-copy-teacher');
    },
    sureClick() {
      this.$refs['copyInforef'].validate(valid => {
        if (valid) {
          this.$emit('confirm-copy-teacher', this.copyInfo);
          this.$emit('close-copy-teacher');
        } else {
          return false;
        }
      });
      return;
    },
    /**
     * @name:全选题目
     */
    handleCheckAllQues(val) {
      let quesList = this.quesList.map(item => {
        return item.quesNos;
      });
      this.copyInfo.checkedQues = val ? quesList : [];
      this.quesIndeterminate = false;
    },
    /**
     * @name:选择题目
     */
    handleCheckedQues(value) {
      let checkedCount = value.length;
      this.isCheckdAllQues = checkedCount == this.quesList.length;
      this.quesIndeterminate = checkedCount > 0 && checkedCount < this.quesList.length;
    },
    /**
     * @name：全选模式
     */
    handleCheckAllModes(val) {
      let modes = this.modes.map(item => item.key);
      this.copyInfo.checkedModes = val ? modes : [];
      this.modesIndeterminate = false;
    },
    /**
     * @name：选择模块
     */
    handleCheckedModes(value) {
      let checkedCount = value.length;
      this.isChaeckedAllModes = checkedCount == this.modes.length;
      this.modesIndeterminate = checkedCount > 0 && checkedCount < this.modes.length;
    },
  },
};
</script>

<style>
</style>
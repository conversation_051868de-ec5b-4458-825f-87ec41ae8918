<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-04-01 08:49:52
 * @LastEditors: 小圆
-->
<template>
  <div class="study-report__header clearfix">
    <report-exam-info-select v-if="filtersOption.includes('examId')"></report-exam-info-select>
    <report-grade-select
      v-if="filtersOption.includes('examId') && filtersOption.includes('gradeId')"
    ></report-grade-select>
    <report-subject-select
      v-if="filtersOption.includes('examId') && filtersOption.includes('subjectId')"
    ></report-subject-select>
    <report-exam-infos-select v-if="filtersOption.includes('examList')"></report-exam-infos-select>
    <report-exams-grade-select
      v-if="filtersOption.includes('examList') && filtersOption.includes('gradeId')"
    ></report-exams-grade-select>
    <report-exams-subject-select
      v-if="filtersOption.includes('examList') && filtersOption.includes('subjectId')"
    ></report-exams-subject-select>
    <report-contrast-exam-select
      v-if="filtersOption.includes('contrastExamId')"
    ></report-contrast-exam-select>
    <report-class-info-select v-if="filtersOption.includes('classInfo')"></report-class-info-select>
    <report-class-infos-select
      v-if="filtersOption.includes('classList')"
    ></report-class-infos-select>
    <report-student-select v-if="filtersOption.includes('stuId')"> </report-student-select>
    <report-rank-select v-if="filtersOption.includes('rank')"></report-rank-select>
    <report-rank-range-input
      v-if="filtersOption.includes('startRank') && filtersOption.includes('endRank')"
    ></report-rank-range-input>
    <report-limit-input v-if="filtersOption.includes('limit')"></report-limit-input>
    <report-step-input v-if="filtersOption.includes('step')"></report-step-input>
    <slot></slot>
  </div>
</template>

<script lang="ts">
import { FilterDataKey } from '@/pages/studyReport/plugins/types';
import { Component, Prop, Vue } from 'vue-property-decorator';

import ReportClassInfoSelect from './ReportClassInfoSelect.vue';
import ReportClassInfosSelect from './ReportClassInfosSelect.vue';
import ReportContrastExamSelect from './ReportContrastExamSelect.vue';
import ReportExamInfoSelect from './ReportExamInfoSelect.vue';
import ReportExamInfosSelect from './ReportExamInfosSelect.vue';
import ReportExamsGradeSelect from './ReportExamsGradeSelect.vue';
import ReportExamsSubjectSelect from './ReportExamsSubjectSelect.vue';
import ReportGradeSelect from './ReportGradeSelect.vue';
import ReportLimitInput from './ReportLimitInput.vue';
import ReportRankRangeInput from './ReportRankRangeInput.vue';
import ReportRankSelect from './ReportRankSelect.vue';
import ReportStepInput from './ReportStepInput.vue';
import ReportStudentSelect from './ReportStudentSelect.vue';
import ReportSubjectSelect from './ReportSubjectSelect.vue';

@Component({
  components: {
    ReportExamInfoSelect,
    ReportExamInfosSelect,
    ReportClassInfoSelect,
    ReportClassInfosSelect,
    ReportGradeSelect,
    ReportRankSelect,
    ReportSubjectSelect,
    ReportLimitInput,
    ReportContrastExamSelect,
    ReportStudentSelect,
    ReportStepInput,
    ReportRankRangeInput,
    ReportExamsSubjectSelect,
    ReportExamsGradeSelect,
  },
})
export default class ReportHeader extends Vue {
  @Prop({ default: [] }) filtersOption!: FilterDataKey[];
}
</script>

<style scoped lang="scss"></style>

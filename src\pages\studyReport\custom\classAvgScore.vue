<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-04-18 09:25:16
 * @LastEditors: 小圆
-->

<template>
  <div>
    <div v-loading="tableLoading">
      <base-table
        v-if="tableData.length"
        :data="tableData"
        :column="tableColumns"
        v-bind="getTableAttr()"
        v-drag-table
      ></base-table>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';

@Component({
  components: {},
})
export default class Index extends Mixins(TableCommon) {
}
</script>

<style scoped lang="scss"></style>

<template>
  <div>
    <div v-show="!showDefault" id="scoreSectionChart"
         :style="{width: '100%', height: '460px'}"></div>

    <div v-show="showDefault">
      <div style="text-align: center"><img style="width: 350px;" :src="noResImg" alt=""></div>
      <p style="text-align: center;font-size: 16px;margin-top: 10px;">暂无数据!</p>
    </div>
  </div>
</template>

<script>
export default {
  name: "ScoreSectionChart",
  props: ['tableData'],

  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      scoreSectionChart: null,
      showDefault: false,
      colors: [
        '#409EFF', '#07C29D', '#FF6A68', '#3E73F6', '#FFB400', '#BD3DDD',
        '#88FF9C', '#FFAF68', '#3E73F6', '#F368FF', '#68FFFA',
        '#3EF0CD', '#418AFD', '#B4F0FA', '#07C29D',
        '#FF8ACF', '#68BAFF'
      ],
      seriesData: [],
      titleList: [],
    }
  },
  watch: {
    tableData: {
      handler(val) {
        if(val.length) {
          this.showDefault = false;
        }
        // this.resetDomSize('scoreSectionChart', 460);
        this.$nextTick(()=>{
          this.drawImg();
        })
      },
      deep: true,
      immediate: true,
    },
  },
  beforeDestroy() {
    if(this.scoreSectionChart != null && this.scoreSectionChart != "" && this.scoreSectionChart != undefined) {
      this.scoreSectionChart.dispose();
      this.scoreSectionChart = null;
    }
  },
  mounted(){
    let _this = this;
    window.onresize = ()  =>{
      return (() => {
        if(_this.scoreSectionChart) {
          // this.resetDomSize('scoreSectionChart', 460);
          _this.scoreSectionChart.resize();
        }
      })()
    };
    if(!this.scoreSectionChart) {
      this.drawImg();
    }
    // this.drawImg()
  },
  methods: {
    // resetDomSize(el, height) {
      // let width = document.getElementById('pane-chart').clientWidth
      // Object.defineProperty(document.getElementById(el),'clientWidth',
          // {get:function(){return width;}, configurable: true})
      // Object.defineProperty(document.getElementById(el),'clientHeight',
          // {get:function(){return height;}, configurable: true})
    // },
    handleChartData(seriesItemSetting) {
      let data = this.tableData && this.tableData.length ? this.tableData : [] ;

      if(data.length) {
        this.seriesData = []
        if(data[0].scoreSections) {
          this.titleList = data[0].scoreSections.map(it => {
            if(it.close != it.open) {
              return `${it.close}-${it.open}`
            } else {
              return it.close+''
            }

          })
        }

        // this.titleList.unshift(data[0].scoreSections[0].close + '')
        data.map((item, index) => {
          let {className, scoreSections, fullTotal} = item;
          let res = [];
          if(scoreSections) {
            if(index==0) {
              res = scoreSections.map(it => it.total);
            } else {
              res = scoreSections.map(it => it.total);
              res.unshift(fullTotal);
            }
          }


          let obj = {
            name: className,
            data: res,
            ...seriesItemSetting
          };
          this.seriesData.push(obj)
        })
      } else {
        this.showDefault = true;
      }
    },
    drawImg() {
      if(this.scoreSectionChart != null && this.scoreSectionChart != "" && this.scoreSectionChart != undefined) {
        this.scoreSectionChart.dispose();
        this.scoreSectionChart = null;
      }

      let seriesItemSetting = {
        type: 'line',
        symbol: 'circle',
        symbolSize: 10,
      }
      this.handleChartData(seriesItemSetting);
      let _this = this;


      this.scoreSectionChart = this.$echarts.init(
          document.getElementById('scoreSectionChart')
      );
      this.scoreSectionChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {            // 坐标轴指示器，坐标轴触发有效
            type: 'line'        // 默认为直线，可选为：'line' | 'shadow'
          },
          position: function (point, params, dom, rect, size) {
            // 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
            // 提示框位置
            let x = 0; // x坐标位置
            let y = 0; // y坐标位置

            // 当前鼠标位置
            let pointX = point[0];
            let pointY = point[1];

            // 外层div大小
            // var viewWidth = size.viewSize[0];
            // var viewHeight = size.viewSize[1];

            // 提示框大小
            let boxWidth = size.contentSize[0];
            let boxHeight = size.contentSize[1];

            // boxWidth > pointX 说明鼠标左边放不下提示框
            if (boxWidth > pointX) {
              x = 5;  // 自己定个x坐标值，以防出屏
              y -= 15; // 防止点被覆盖住，可根据情况自行调节
            } else { // 左边放的下
              x = pointX - boxWidth - 15;
            }

            // boxHeight > pointY 说明鼠标上边放不下提示框
            if (boxHeight + 20 > pointY) {
              y = pointY - 125;
            } else if (boxHeight > pointY) {
              y = 5;
            } else { // 上边放得下
              y += pointY - boxHeight;
            }
            return [x, y];
          }
        },
        color: this.colors,
        legend: {
          icon: "circle",
          type: 'scroll',
          orient: 'horizontal',
          top: 10,
          right: 50,
          textStyle: {
            color: '#757C8C',
            fontSize: '14'
          },
          padding: [10, 0, 0, 30],
        },
        grid: {
          left: '3%',
          right: '6%',
          top: '18%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          name: '分段',
          data: _this.titleList,
          axisLabel: {
            // interval: 0
          },
          // data: [
          //   '100-90', '90-80', '80-70', '70-60', '60-50', '50-40', '40-30',
          //     '30-20', '20-10', '10-0'
          // ]
        },
        yAxis: {
          type: 'value',
          name: '人数'
        },
        series: _this.seriesData
      })
    }
  }
}
</script>

<style scoped>

</style>

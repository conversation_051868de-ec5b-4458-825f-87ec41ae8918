<template>
  <el-table
      :data="tableData"
      stripe
      :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
      style="width: 100%"
      v-sticky-table="0"
      >
    <el-table-column
        align="center"
        prop="className"
        label="班级"
        width="140">
      <template slot-scope="scope">
        <span>{{scope.row.className }}</span>
      </template>
    </el-table-column>
    <el-table-column
        align="center"
        prop="fullScore"
        label="满分">
    </el-table-column>
    <el-table-column
        align="center"
        prop="avgScore"
        label="平均分">
    </el-table-column>
    <el-table-column
        align="center"
        prop="standard"
        label="标准差">
    </el-table-column>
    <el-table-column
        align="center"
        prop="q005"
        label="第5%">
      <template slot-scope="scope">
        <span>{{Number(scope.row.q005) == -1 ? '-' : scope.row.q005}}</span>
      </template>
    </el-table-column>
    <el-table-column
        align="center"
        prop="q095"
        label="第95%">
      <template slot-scope="scope">
        <span>{{Number(scope.row.q095) == -1 ? '-' : scope.row.q095}}</span>
      </template>
    </el-table-column>
    <el-table-column
        align="center"
        prop="poor"
        label="极差">
    </el-table-column>
    <el-table-column
        align="center"
        prop="median"
        label="中位数">
      <template slot-scope="scope">
        <span>{{Number(scope.row.median) == -1 ? '-' : scope.row.median}}</span>
      </template>
    </el-table-column>
    <el-table-column
        align="center"
        prop="q3"
        width="130">
      <template slot="header" slot-scope="scope">
        <span >第三四分位</span>
        <el-tooltip class="th-icon" effect="dark" content="该样本中所有数值由小到大排列后第75%的数字" placement="top">
          <i class="el-icon-info" style="vertical-align: middle;"></i>
        </el-tooltip>
      </template>
      <template slot-scope="scope">
        <span>{{Number(scope.row.q3) == -1 ? '-' : scope.row.q3}}</span>
      </template>
    </el-table-column>
    <el-table-column
        align="center"
        prop="q1"
        width="130">
      <template slot="header" slot-scope="scope">
        <span>第一四分位</span>
        <el-tooltip class="th-icon" effect="dark" content="该样本中所有数值由小到大排列后第25%的数字" placement="top">
          <i class="el-icon-info"></i>
        </el-tooltip>
      </template>
      <template slot-scope="scope">
        <span>{{Number(scope.row.q1) == -1 ? '-' : scope.row.q1}}</span>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: "BoxplotTable",
  props: ['tableData'],
  data() {
    return {
      tableData1: [
        //年级数据
        {
          classId: '',
          fullScore: '100', //int型，满分
          average: '61.7', //平均分
          standardDeviation: '11.9', //标准差
          quartileHighest: '81', //第5%
          quartileLowest: '43',//第95%
          poor: '91', //极差
          median: '62', //中位数
          upperQuartile: '70', //上四分位
          lowerQuartile: '54', //下四分位
        },
        //班级数据
        {
          className: '1901班',
          classId: '_ghjkl1',
          fullScore: '100', //int型，满分
          average: '73.6', //平均分
          standardDeviation: '13.7', //标准差
          quartileHighest: '87', //第5%
          quartileLowest: '66',//第95%
          poor: '91', //极差
          median: '74', //中位数
          upperQuartile: '81.3', //上四分位
          lowerQuartile: '69', //下四分位
        },
        {
          className: '1902班',
          classId: '_ghjkl2',
          fullScore: '100', //int型，满分
          average: '62.8', //平均分
          standardDeviation: '7.7', //标准差
          quartileHighest: '75', //第5%
          quartileLowest: '51',//第95%
          poor: '29', //极差
          median: '62', //中位数
          upperQuartile: '70', //上四分位
          lowerQuartile: '57', //下四分位
        },
      ]
    }
  }
}
</script>

<style scoped lang="scss">
.th-icon {
  margin-left: 5px;
}
</style>

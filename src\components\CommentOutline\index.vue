<!--
 * @Description: 答题概览
 * @Author: qmzhang
 * @Date: 2024-02-18 11:50:28
 * @LastEditTime: 2025-08-19 16:40:31
 * @FilePath: \personal-bigdata\src\components\CommentOutline\index.vue
-->
<template>
    <div class="comment-outline"
        :style="{ '--block-size': blockSize, '--block-fontsize': blockFontSize, '--block-margin': blockMargin }">
        <div class="com-header clearfix">
            <div class="title pull-left">答题概览</div>
            <el-button class="pull-right" type="text" @click="openLevelSetting">
                <span class="ver-bot">设置</span> <i class="el-icon-setting ver-mid"></i>
            </el-button>
        </div>

        <div class="com-content">
            <!-- 得分等级 -->
            <ul class="com-score-list list-none clearfix">
                <li class="score-item pull-left" :title="item.name" v-for="(item, index) in scoreLevel"
                    :key="item.name">
                    <div class="color-block ver-mid dis-inline" :class="`level-${index}`"></div>
                    [ <div class="score-string ver-mid dis-inline">{{ item.level[0] }}~{{ item.level[1] }}</div>
                    {{ index === 3 ? ']' : ')' }}
                </li>
            </ul>

            <!-- AB卷筛选 -->
            <div class="com-abpaper-list" v-if="abPaperList.length">
                <div class="abpaper-item" v-for="item in abPaperList" :key="item.value"
                    :class="item.value === abPaper ? 'active' : ''" @click="onChangeAbPaper(item.value)">
                    {{ item.text }}
                </div>
            </div>

            <!-- 题目序号列表 -->
            <div class="com-ques-list" v-for="item in quesNumberList" :key="item.index">
                <div class="ques-title text-ellipsis-two" :title="item.fullTitle">{{ item.fullTitle }}</div>
                <ul class="com-number-list list-none clearfix">
                    <li class="color-block ver-mid dis-inline click-element" :class="`level-${subItem.level}`"
                        v-for="subItem in item.list" :key="subItem.quesId" @click="anchorToTarget(subItem)">
                        {{
                            subItem.quesName || subItem.sortOrder || subItem.quesNo
                        }}
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import LevelSettingDialog from './levelSettingDialog.vue'
import createDialog from '@/components/createDialog';

interface QuesCard {
    index: number;
    fullTitle: string;
    list: any[]
}
type QUES_SORT = 'quesNo' | 'scoreRate' | 'scoreRateDiffer';

export type QuesCardList = QuesCard[]
@Component
export default class CommentOutline extends Vue {
    @Prop({ default: () => [] }) list: QuesCardList;
    @Prop({ default: '' }) abPaper: string;
    @Prop({ default: () => [] }) abPaperList: any[];
    @Prop({ default: '36px' }) blockSize: string
    @Prop({ default: '16px' }) blockFontSize: string
    @Prop({ default: '20px' }) blockMargin: string

    // 得分率设置
    levelList: number[] = [0, 60, 70, 90, 100];
    levelStrList: string[] = ['不及格', '及格', '中等', '优秀'];

    // 根据list初始化最终渲染的题号列表
    get quesNumberList(): QuesCardList {
        let list = this.list.slice();
        list.forEach(item => {
            item.list.forEach(subItem => {
                subItem.level = 3;
                if (subItem.clsTotal == 0) return;

                const classScoringRate = Number(subItem.classScoringRate);
                if (classScoringRate >= 1) return;

                for (let i = 0; i < this.levelList.length - 1; i++) {
                    const range = [this.levelList[i] / 100, this.levelList[i + 1] / 100];
                    if (classScoringRate >= range[0] && classScoringRate < range[1]) {
                        subItem.level = i;
                        break;
                    }
                }
            })
        })

        return list;
    }

    // 分数等级列表
    get scoreLevel(): { name: string; level: number[] }[] {
        let list = [];
        for (let i = 0; i < 4; i++) {
            list.push({
                name: this.levelStrList[i],
                level: [this.levelList[i] / 100, this.levelList[i + 1] / 100]
            })
        }

        return list;
    }

    created() {
        this.initLevelList();
    }

    /**
     * @description: 初始化得分率等级
     * @return {*}
     */
    initLevelList() {
        let loginInfo = this.$sessionSave.get('loginInfo');
        let keypath = `levelList_${loginInfo.id}`;
        try {
            let data = this.$localSave.get(keypath);
            if (data) {
                this.levelList = data;
            }
        } catch (error) {
            localStorage.removeItem(keypath);
        }
    }

    // 是否拆分题组
    getIsSplitQues(item) {
        if (item.split) {
            return item.split.length > 1;
        } else {
            return false;
        }
    }

    /* 锚向对应目标 */
    anchorToTarget(item: any) {
        this.$emit('anchor', item)
    }

    /* 打开等级设置 */
    openLevelSetting() {
        createDialog({
            title: '设置',
            size: 'middle',
            appendToBody: true,
            closeOnClickModal: false,
            customClass: 'outline-setting-dialog',
            component: LevelSettingDialog,
            store: this.$store,
            router: this.$router,
            width: '560px',
            data: {
                levelList: this.levelList,
            },
            close: () => {
                //关闭后触发
                console.log('dialog is closed');
            },
            change: () => { },
            confirm: (data: { levelList: number[]; }) => {
                this.levelList = data.levelList;
                let loginInfo = this.$sessionSave.get('loginInfo');
                let keypath = `levelList_${loginInfo.id}`;
                this.$localSave.set(keypath, data.levelList);
                this.$emit("change", this.levelList)
            },
        });
    }

    /** 更改AB卷类型 */
    onChangeAbPaper(value) {
        this.$emit('changeAbPaper', value);
    }
}
</script>

<style lang="scss" scoped>
@import './styles/index.scss';
</style>

<style lang="scss">
.outline-setting-dialog .el-dialog__body {
    padding: 0 20px;
}
</style>
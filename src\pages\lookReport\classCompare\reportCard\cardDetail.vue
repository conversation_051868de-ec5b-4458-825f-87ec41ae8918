<template>
  <div>
    <div class="reportCard-title clearfix">
      <div class="pull-left el-icon-arrow-left back-arrow" @click="backPage">查看详情</div>
      <!--搜索-->
      <div class="header__select pull-right">
        <div class="header__serarch clearfix display_flex">
          <el-input
            class="search__text"
            placeholder="输入学号或姓名搜索"
            v-model="searchValue"
            @keyup.enter.native="searchReport"
            @clear="searchReport"
            clearable
          >
          </el-input>
          <div
            class="search__icon el-icon-search display_flex align-items_center justify-content_flex-center"
            @click="searchReport"
          ></div>
        </div>
      </div>
    </div>

    <div v-if="stuList.length" v-loading="isLoading">
      <div
        class="reportCard-stuList display_flex align-items_center"
        ref="cardRef"
        style="line-height: 22px"
      >
        <span class="title">学生：</span>
        <el-row class="list-none stu-ul flex_1" :gutter="20" v-if="stuList.length">
          <el-col
            :span="2"
            class="stu-li text-ellipsis"
            :class="{ active: item.stuId === activeStuId }"
            @click.native="changeStu(item)"
            v-for="(item, index) in stuList.slice(0, 12)"
            :key="index"
            :title="item.stuName"
          >
            {{ item.stuName }}
          </el-col>
        </el-row>
        <!-- <el-row class="list-none stu-ul flex_1" :gutter="20" v-else>
          <el-col :span="2" class="stu-li text-ellipsis" :class="{ active: true }">
            {{ searchValue }}
          </el-col>
        </el-row> -->
        <el-popover
          id="morePopover"
          ref="morePopover"
          popper-class="morePopover"
          placement="bottom-end"
          title=""
          @show="showPopover"
          @hide="isFold = true"
          width="100%"
          v-if="stuList.length > 12"
          trigger="click"
        >
          <div>
            <el-row class="list-none stu-ul" :gutter="20">
              <el-col
                :span="2"
                class="stu-li text-ellipsis"
                :class="{ active: item.stuId === activeStuId }"
                @click.native="changeStu(item, true)"
                v-for="(item, index) in stuList.slice(12)"
                :key="index"
                :title="item.stuName"
              >
                {{ item.stuName }}
              </el-col>
            </el-row>
          </div>

          <span slot="reference" class="showMore" @click="isFold = !isFold">
            {{ isFold ? '展开' : '收起' }}
            <i :class="isFold ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </span>
        </el-popover>
      </div>
      <div class="statistic display_flex align-items_center justify-content_space-evenly">
        <div v-for="(item, key, index) of statisticList" :key="index">
          <div class="title">{{ key }}</div>
          <div class="score">{{ item }}</div>
        </div>
      </div>
      <!--趋势图-->
      <div>
        <div class="titleLine">趋势图</div>
        <div
          id="cardDetailChart"
          :style="{ width: '100%', height: '400px', border: '1px solid #E4E8EB' }"
        ></div>
      </div>
      <!--得分分析-->
      <div>
        <div class="titleLine">
          <span> 得分分析 </span>
        </div>
        <el-tooltip placement="bottom-start" v-if="doQuesList.length">
          <div slot="content">
            <div v-html="getDoQuesChoiceTipHtml()"></div>
          </div>
          <span class="mixin-dochoice-tip">{{ getDoQuesChoiceTipText() }}</span>
        </el-tooltip>
        <!-- <div v-if="doQuesList.length" class="mixin-dochoice-tip" v-html="getDoQuesChoiceTipHtml()"></div> -->
        <el-table
          :data="tableData"
          style="width: 100%; border: 1px solid #e4e8eb"
          :header-cell-style="{ fontSize: '16px', color: '#3F4A54' }"
          v-if="filterData.subjectId"
          height="371"
          stripe
          class="scoring-table"
          :row-class-name="tableRowClassName"
        >
          <!--知识点-->
          <el-table-column prop="quesNo" label="题号（小题满分）">
            <template slot-scope="scope">
              {{ scope.row.quesNo }}
              <span v-if="getDoChoiceQuesCountTextByQuesNo(scope.row.quesNo)">
                (<span class="mixin-dochoice-text">{{
                  getDoChoiceQuesCountTextByQuesNo(scope.row.quesNo)
                }}</span
                >)
              </span>
              （{{ scope.row.fullScore }}）
            </template>
          </el-table-column>
          <el-table-column prop="quesType" label="题型"></el-table-column>
          <el-table-column prop="score" label="得分">
            <template slot-scope="scope">
              {{ scope.row.chooseType === '0' ? '--' : scope.row.score ? scope.row.score : 0 }}
            </template>
          </el-table-column>
          <!--<el-table-column prop="fullScore" label="小题满分"></el-table-column>-->
          <el-table-column prop="scoreRate" label="得分率">
            <template slot-scope="scope">
              {{
                scope.row.chooseType === '0'
                  ? '--'
                  : `${Number(scope.row.scoreRate * 100).toFixed(2)}%`
              }}
            </template>
          </el-table-column>
          <el-table-column prop="difficulty" label="难度">
            <template slot-scope="scope">
              {{ !scope.row.difficulty ? '简单' : scope.row.difficulty == 1 ? '中等' : '困难' }}
            </template>
          </el-table-column>
          <el-table-column prop="clsScoreRate" label="班级得分率">
            <template slot-scope="scope">
              {{ Number(scope.row.clsScoreRate * 100).toFixed(2) }}%
            </template>
          </el-table-column>
          <el-table-column prop="grdScoreRate" label="年级得分率">
            <template slot-scope="scope">
              {{ Number(scope.row.grdScoreRate * 100).toFixed(2) }}%
            </template>
          </el-table-column>
        </el-table>
        <div class="hideBlock" style="height: 371px" v-else>选择具体学科后显示数据</div>
      </div>
      <!--知识点掌握情况-->
      <div>
        <div class="titleLine">知识点掌握情况</div>
        <el-table
          :data="pointTable"
          :header-cell-style="{ fontSize: '16px', color: '#3F4A54' }"
          style="width: 100%; border: 1px solid #e4e8eb"
          v-if="filterData.subjectId"
          height="371"
          stripe
          class="scoring-table"
        >
          <!--知识点-->
          <el-table-column prop="name" label="知识点"></el-table-column>
          <el-table-column prop="frequency" label="考频"></el-table-column>
          <el-table-column prop="scoreRate" label="我的得分率"></el-table-column>
          <el-table-column prop="clsScoreRate" label="班级得分率"></el-table-column>
          <el-table-column prop="grdScoreRate" label="年级得分率"></el-table-column>
        </el-table>
        <div class="hideBlock" style="height: 371px" v-else>选择具体学科后显示数据</div>
      </div>
    </div>
    <no-data v-else v-loading="isLoading">
      <p class="text-center">
        未查询到本班<b class="">{{ stuName }}</b
        >学生数据
      </p>
    </no-data>
  </div>
</template>

<script>
import {
  getReportCard,
  getThreeTestData,
  getStuQuesAnalyze,
  getStuknowlPointAnalyze,
  getNewReportCard,
} from '@/service/pexam';
import DochoiceMixin from '../../mixin/DochoiceMixin.vue';
import NoData from '@/components/noData.vue';

export default {
  name: 'card-detail',
  mixins: [DochoiceMixin],
  components: {
    NoData,
  },
  data() {
    return {
      searchValue: '',
      sortType: '',
      isFold: true,
      stuList: [],
      leftPos: 0,
      activeStu: {},
      activeStuId: '',
      queryData: {},
      tableData: [],
      pointTable: [],
      cardDetailChart: null,
      filterData: {},
      chartData: [],
      isFirst: true,
      statisticList: {},
      stuName: '',
      isLoading: false,
    };
  },
  mounted() {
    this.queryData = this.$route.query;
    this.activeStuId = this.queryData.stuId;
    this.activeStu.stuId = this.activeStuId;
    this.activeStu.abPaper = this.queryData.abPaper;
    this.leftPos = document.body.clientWidth - 100 - 186;
    this.$emit('hideTotalClass', this.queryData.clsId);
  },
  methods: {
    // 班级学科初始化后更新数据
    updateFilter(data, type) {
      this.filterData = data;
      // 切换班级
      this.getReportCard(type);
      this.getPaperChoice(this.$sessionSave.get('reportDetail').examId, this.filterData.subjectId);
    },
    // 返回成绩单主页
    backPage() {
      this.$sessionSave.set('backContrastObj', true);
      let base = '/home';
      if (this.$route.path.includes('dReport')) {
        base = '/dReport';
      }
      this.$router.push({
        path: base + '/reportDetail/classCompare/cardHome',
        query: {
          from: 'cardDetail',
          examId: this.queryData.withExamId,
          examName: this.queryData.examName,
        },
      });
    },
    // 搜索学生
    searchReport() {
      this.getReportCard();
    },
    // 搜索数据
    getReportCard(type) {
      this.isLoading = true;
      getNewReportCard({
        examId: this.$sessionSave.get('reportDetail').examId,
        classId: this.filterData.classId,
        subjectId: this.filterData.subjectId,
        withExamId: this.queryData.withExamId,
        text: this.searchValue,
        sort: this.queryData.sortType,
        page: 1,
        pageSize: 1000,
        abPaper: this.filterData.abPaper,
        v: this.$sessionSave.get('reportDetail').v,
      })
        .then(data => {
          this.isLoading = false;
          this.stuList = data.data.rows.stuList;
          this.stuName = this.searchValue;
          if (this.isFirst) {
            this.getThreeTestData();
            this.isFirst = false;
            this.stuList.forEach((item, index) => {
              if (item.stuId === this.activeStuId) {
                if (index >= 12) {
                  this.searchValue = item.stuName;
                }
              }
            });
          } else {
            if(!this.stuList.find(item => item.stuId == this.activeStuId)) {
              this.activeStu = this.stuList[0];
              this.activeStuId = this.stuList[0].stuId;
            }
            this.getThreeTestData();
          }
          if (this.filterData.subjectId) {
            this.getStuknowlPointAnalyze();
            this.getStuQuesAnalyze();
          }
        })
        .catch(err => {
          this.isLoading = false;
          this.stuName = this.searchValue;
        });
    },
    // 获取相邻三次考试数据统计
    getThreeTestData(stuId) {
      this.isLoading = true;
      getThreeTestData({
        examId: this.$sessionSave.get('reportDetail').examId,
        classId: this.filterData.classId,
        subjectId: this.filterData.subjectId,
        studentId: this.activeStuId,
        page: 1,
        pageSize: 10,
        v: this.$sessionSave.get('reportDetail').v,
      }).then(data => {
        this.isLoading = false;
        this.chartData = data.data;
        if (!this.chartData.length) return;

        let chartData =
          this.chartData.find(
            t =>
              t.examId == this.$sessionSave.get('reportDetail').examId ||
              t.examId == this.$sessionSave.get('reportParent').examId
          ) || this.chartData[0];
        this.statisticList = {
          我的分数: chartData.score,
          我的得分率: chartData.scoreRate + '%',
          班级平均分: chartData.clsAvgScore,
          班级最高分: chartData.clsMaxScore,
        };
        this.drawImg();
      }).catch(err => {
        this.isLoading = false;
      })
    },
    // 获取知识点掌握情况数据
    getStuknowlPointAnalyze() {
      this.isLoading = true;
      getStuknowlPointAnalyze({
        examId: this.$sessionSave.get('reportDetail').examId,
        classId: this.filterData.classId,
        subjectId: this.filterData.subjectId,
        studentId: this.activeStuId,
        abPaper: this.activeStu.abPaper
      }).then(data => {
        this.isLoading = false;
        if (data.code === 1) {
          this.pointTable = data.data.list;
          this.pointTable.length &&
            this.pointTable.forEach(item => {
              this.$set(item, 'scoreRate', item.scoreRate + '%');
              this.$set(item, 'clsScoreRate', item.clsScoreRate + '%');
              this.$set(item, 'grdScoreRate', item.grdScoreRate + '%');
            });
        }
      }).catch(err => {
        this.isLoading = false;
      })
    },
    // 获取学生题目得分分析
    getStuQuesAnalyze() {
      this.isLoading = true;
      getStuQuesAnalyze({
        examId: this.$sessionSave.get('reportDetail').examId,
        classId: this.filterData.classId,
        subjectId: this.filterData.subjectId,
        studentId: this.activeStuId,
        abPaper: this.activeStu.abPaper,
      }).then(data => {
        this.tableData = data.data;
      }).catch(err => {
        this.isLoading = false;
      })
    },
    // 得分小于满分则标红
    tableRowClassName({ row, rowIndex }) {
      if (row.chooseType === '0') return '';
      if (Number(row.score) < Number(row.fullScore)) {
        return 'warning-row';
      }
      return '';
    },
    // 显示学生列表
    showPopover() {
      let container = this.$refs.cardRef;
      document.getElementsByClassName(
        'morePopover'
      )[0].style.cssText += `left: ${container.offsetLeft}px; width: ${container.offsetWidth}px`;
    },
    // 切换学生
    changeStu(item, inner) {
      if (inner) {
        document.body.click();
        this.isFold = true;
      }
      this.activeStu = item;
      this.activeStuId = item.stuId;
      this.searchValue = inner ? item.stuName : '';
      this.stuName = this.searchValue;
      this.getThreeTestData();
      this.getStuknowlPointAnalyze();
      this.getStuQuesAnalyze();
    },
    // 渲染图表
    drawImg() {
      let reportNameList = [],
        personData = [],
        classData = [];
      this.chartData.reverse().forEach(item => {
        reportNameList.push(item.examName);
        personData.push(item.scoreRate);
        classData.push(item.clsScoreRate);
      });
      let self = this;
      this.cardDetailChart = this.$echarts.init(document.getElementById('cardDetailChart'));
      this.cardDetailChart.setOption({
        tooltip: {
          // 鼠标经过折线，显示当前x,y坐标
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985',
            },
          },
        },
        legend: {
          top: 10,
          right: 20,
          textStyle: {
            color: '#757C8C',
            fontSize: '14',
          },
          data: ['个人得分率', '班级得分率'],
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '50', // x轴文字和dataZoom保持间距，防止重叠
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            // boundaryGap: false,
            axisLabel: {
              interval: 0,
              rotate: 0,
              // 文字过长换行显示
              // formatter: function (value) {
              // let ret = "";//拼接加\n返回的类目项
              // let maxLength = 10;//每项显示文字个数
              // let valLength = value.length;//X轴类目项的文字个数
              // let rowN = Math.ceil(valLength / maxLength); //类目项需要换行的行数
              // if (rowN > 1) {
              //     for (let i = 0; i < rowN; i++) {
              //         let temp = "";//每次截取的字符串
              //         let start = i * maxLength;//开始截取的位置
              //         let end = start + maxLength;//结束截取的位置
              //         //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
              //         temp = value.substring(start, end) + "\n";
              //         ret += temp; //凭借最终的字符串
              //     }
              //     return ret;
              // }
              // else {
              //     return value;
              // }
              // }
            },
            axisLine: {
              lineStyle: {
                color: '#757C8C',
                fontSize: 14,
              },
            },
            data: reportNameList,
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: '得分率', // 给X轴加单位
            nameTextStyle: {
              padding: [0, 0, 10, -40], // 四个数字分别为上右下左与原位置距离
            },
          },
        ],
        series: [
          {
            name: '个人得分率',
            type: 'line',
            symbol: 'circle', // 节点为实心圆
            // 线条样式
            lineStyle: {
              color: '#5470C6',
              width: 1.5,
              type: 'dashed',
            },
            // 节点样式
            itemStyle: {
              borderWidth: 1,
              color: '#5470C6',
            },
            symbolSize: 10, //设定节点的大小
            data: personData,
          },
          {
            name: '班级得分率',
            type: 'line',
            symbol: 'triangle',
            symbolSize: 12,
            lineStyle: {
              color: '#F3981E',
              width: 1.5,
              type: 'dashed',
            },
            itemStyle: {
              borderWidth: 3,
              color: '#F3981E',
            },
            data: classData,
          },
        ],
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.reportCard-title {
  margin: 15px 0 18px;
  .back-arrow {
    line-height: 40px;
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;
    cursor: pointer;
  }
  .header__select {
    display: inline-block;
    margin-right: 29px;
  }
  .header__serarch {
    display: flex;
    width: 240px;
    .search__icon {
      width: 38px;
      font-size: 18px;
      color: #fff;
      background: #409eff;
      /*background: #0dc2b3 url(../../static/img/pub-icon_reportlist.cfdb858.png) 9px 7px no-repeat;*/
      border-radius: 0 3px 3px 0;
      outline: none;
      cursor: pointer;
    }
  }
}

.titleLine {
  display: flex;
  align-items: center;
  position: relative;
  height: 66px;
  line-height: 66px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;
  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 21px;
  }
}

.mixin-dochoice-tip {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: break-all;
}

.statistic {
  width: 100%;
  height: 80px;
  background: #f7fafc;
  border-radius: 8px;
  margin-top: 20px;
  margin-bottom: 20px;
  line-height: 33px;
  text-align: center;
  .title {
    font-size: 16px;
    color: #8f9ca8;
  }
  .score {
    font-weight: bold;
    font-size: 20px;
    color: #3f4a54ff;
  }
}

.reportCard-stuList {
  .stu-ul {
    .stu-li {
      font-size: 16px;
      color: #3f4a54;
      cursor: pointer;
      max-width: 82px;
      &.active {
        color: #409eff;
      }
    }
  }
  .showMore {
    width: 80px;
    margin-left: 20px;
    cursor: pointer;
    .el-icon-arrow-up {
      color: #409eff;
    }
  }
  .title {
    width: 60px;
  }
}

.hideBlock {
  line-height: 371px;
  text-align: center;
  font-size: 20px;
  border: 1px solid #e4e8eb;
}

.multiple-tip {
  margin-left: 10px;

  font-size: 12px;
  font-weight: 400;
  color: #f59a23;
}

.stu-li {
  font-size: 16px;
  color: #3f4a54;
  cursor: pointer;
  max-width: 120px;
  &.active {
    color: #409eff;
  }
}
</style>
<style lang="scss">
.search__text {
  .el-input__inner {
    border-radius: 4px 0 0 4px;
  }
}

.morePopover {
  height: 170px;
  overflow-y: auto;
  right: initial !important;
}

.scoring-table {
  &.el-table th,
  &.el-table td {
    text-align: center;
  }
  &.el-table .warning-row {
    color: #f56353ff;
  }
}
</style>

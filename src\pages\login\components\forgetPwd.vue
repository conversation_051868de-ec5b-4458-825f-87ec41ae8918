<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2022-03-18 15:57:19
 * @LastEditTime: 2025-05-30 11:26:10
 * @LastEditors: 小圆
-->

<template>
  <el-container class="login">
    <el-main class="login--main">
      <transition enter-active-class="animated fadeInRight">
        <!-- 手机号码验证 -->
        <div class="forget-pwd-container text-center" v-if="!IsSetPassword">
          <div class="forget-main-container">
            <div class="forget__title text-center"><span>忘记密码</span></div>
            <Phone @nextPass="nextPass" :guid="guid"></Phone>
          </div>

          <div class="forget-pwd__back">
            <span class="back-text pointer-auto click-element" @click="back" type="text">
              <i class="iconfont icon-fanye" style="vertical-align: middle"></i> 返回登录
            </span>
          </div>
        </div>
      </transition>

      <transition enter-active-class="animated fadeInRight">
        <!-- 修改密码 -->
        <div class="forget-pwd-container text-center" v-if="IsSetPassword">
          <div class="forget-main-container">
            <div class="forget__title text-center"><span>忘记密码</span></div>
            <ChangePwd
              :guid="guid"
              :phoneNumber="phoneNumber"
              @back="backLogin"
              @backUpdatePassword="backUpdatePassword"
            ></ChangePwd>
          </div>
          <div class="forget-pwd__back">
            <span class="back-text pointer-auto click-element" @click="back" type="text">
              <i class="iconfont icon-fanye" style="vertical-align: middle"></i> 返回上一步
            </span>
          </div>
        </div>
      </transition>
    </el-main>
  </el-container>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import ChangePwd from './changePwd.vue';
import Phone from './phone.vue';
import { getSafetyIdAPI } from '@/service/api';

@Component({
  components: {
    Phone,
    ChangePwd,
  },
})
export default class ForgetPwd extends Vue {
  @Prop({ type: Boolean }) visible: any;
  // 是否是设置密码界面
  IsSetPassword: boolean = false;
  // 从接口中获取guid
  guid: any = '';
  // 手机号码
  phoneNumber: any = '';

  // 获取guid
  async mounted() {
    this.guid = (await getSafetyIdAPI({})).data;
    this.loadCaptchaScript();
  }

  /**
   * @description: 动态加载腾讯验证码
   */
  loadCaptchaScript() {
    if (document.querySelector('#tcaptcha')) {
      return;
    }
    let script = document.createElement('script'); //创建一个script标签
    script.id = 'tcaptcha';
    script.src = 'https://fs.iclass30.com/aliba/plug/tencent/TCaptcha.js';
    let $body = document.getElementsByTagName('body')[0];
    $body.appendChild(script);
  }

  /**
   * @description: 进入下一步（重置密码界面）
   */
  nextPass(val: any, number: any) {
    this.IsSetPassword = val;
    this.phoneNumber = number;
  }

  // 返回上一级
  back() {
    if (!this.IsSetPassword) {
      this.$emit('back');
    } else {
      this.IsSetPassword = false;
    }
  }

  // 返回登录
  backLogin() {
    this.$emit('back');
  }

  // 修改密码成功，更新密码
  backUpdatePassword(pwd, phone) {
    this.$emit('UpdatePassword', pwd, phone);
  }
}
</script>
<style lang="scss" scoped>
.login {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .login--main {
    padding: 0;
    overflow: hidden;

    .login-avatar-box {
      // margin: 0 auto;
      width: 300px;
      margin: 0 auto;
      overflow: hidden;
      text-align: center;
      padding-top: 50px;

      .user-img {
        width: 90px;
        height: 90px;
        margin: 0 auto;
        border-radius: 45px;
        overflow: hidden;
      }

      .user-name {
        margin: 25px 0;
        font-size: 18px;
      }
    }
  }
}

.forget-pwd-container {
  width: 100%;
  height: 100%;
  vertical-align: middle;

  .forget-pwd__back {
    z-index: 10;
    color: #2574ff;
    text-align: center;
    color: #7c8394;

    .back-text {
      font-size: 16px; // font-size: 16px; //vw(16, $win_width);
      cursor: pointer;
    }
  }

  .forget__title {
    margin-top: 50px; // margin-top: vh(50, $win_height);
    margin-bottom: 45px; // margin-bottom: vh(45, $win_height);
    color: #2a3034;
    font-size: 26px;
    font-weight: 700;
  }
}

.password-form {
  width: 360px; // width: vw(360, $win_width);
  margin: 0 auto;
}
</style>

function createNumberConverter() {
    // 预编译正则表达式
    const suffixRegex = /^(\d+)(st|nd|rd|th)$/i;
    const romanRegex = /^[ivxlcdmIVXLCDM]+$/;
    
    // 数字映射表
    const chineseDigits = {
        '零': 0, '一': 1, '二': 2, '三': 3, '四': 4,
        '五': 5, '六': 6, '七': 7, '八': 8, '九': 9
    };
    
    const chineseUnits = {
        '十': 10, '百': 100, '千': 1000, '万': 10000,
        '亿': 100000000
    };
    
    const romanNumerals = {
        'I': 1, 'V': 5, 'X': 10, 'L': 50,
        'C': 100, 'D': 500, 'M': 1000,
        'i': 1, 'v': 5, 'x': 10, 'l': 50,
        'c': 100, 'd': 500, 'm': 1000
    };

    // 解析中文数字
    function parseChineseNumber(s) {
        if (chineseDigits.hasOwnProperty(s)) {
            return chineseDigits[s];
        }
        
        let result = 0;
        let current = 0;
        
        for (let i = 0; i < s.length; i++) {
            const char = s[i];
            
            if (chineseDigits.hasOwnProperty(char)) {
                current += chineseDigits[char];
            } 
            else if (chineseUnits.hasOwnProperty(char)) {
                const unit = chineseUnits[char];
                
                if (current === 0 && unit === 10 && i === 0) {
                    current = 1;
                }
                
                if (unit >= 10) {
                    current *= unit;
                    result += current;
                    current = 0;
                }
            } 
            else {
                return null;
            }
        }
        
        result += current;
        return result > 0 ? result : null;
    }

    // 解析罗马数字
    function parseRomanNumber(s) {
        if (!romanRegex.test(s)) {
            return null;
        }
        
        let result = 0;
        let prevValue = 0;
        
        for (let i = s.length - 1; i >= 0; i--) {
            const currentValue = romanNumerals[s[i]];
            
            if (currentValue < prevValue) {
                result -= currentValue;
            } else {
                result += currentValue;
            }
            
            prevValue = currentValue;
        }
        
        return result;
    }

    // 主转换函数
    return function convertToNumber(str) {
        const s = str.toString().trim(); // 确保是字符串并去空格
        if (!s) return Infinity;
        
        // 1. 阿拉伯数字（包括带千分位）
        if (/^[\d,]+$/.test(s)) {
            const cleanNumber = s.replace(/,/g, '');
            const num = parseFloat(cleanNumber);
            if (!isNaN(num)) return num;
        }
        
        // 2. 带括号的数字
        if (s.length > 2 && (s[0] === '(' || s[0] === '（') && (s[s.length - 1] === ')' || s[s.length - 1] === '）')) {
            return convertToNumber(s.slice(1, -1));
        }
        
        // 3. 带后缀的数字
        const suffixMatch = s.match(suffixRegex);
        if (suffixMatch) {
            return parseInt(suffixMatch[1], 10);
        }
        
        // 4. 中文数字
        if (/^[\u4e00-\u9fa5]/.test(s)) {
            const chineseNum = parseChineseNumber(s);
            if (chineseNum !== null) return chineseNum;
        }
        
        // 5. 罗马数字
        if (romanRegex.test(s)) {
            return parseRomanNumber(s);
        }
        
        // 无法识别的格式
        return Infinity;
    };
}

// 创建转换器实例
const convertToNumber = createNumberConverter();

/**
 * 对对象数组根据指定字段进行排序
 * @param {Array} array 要排序的对象数组
 * @param {string} field 用于排序的字段名
 * @param {boolean} ascending 是否升序排列（默认true）
 * @returns {Array} 排序后的新数组
 */
export function sortObjectArrayByField(array, field, ascending = true) {
    // 检查输入有效性
    if (!Array.isArray(array) || typeof field !== 'string') {
        return [...array]; // 返回原数组副本
    }
    
    // 预计算所有对象的排序值，避免重复计算
    const withSortValues = array.map(item => ({
        original: item,
        // 提取字段值并转换为可排序的数字
        sortValue: convertToNumber(item[field] !== undefined ? item[field] : '')
    }));
    
    // 排序并提取原始对象
    const sorted = withSortValues.sort((a, b) => {
        return ascending 
            ? a.sortValue - b.sortValue 
            : b.sortValue - a.sortValue;
    });
    
    return sorted.map(item => item.original);
}

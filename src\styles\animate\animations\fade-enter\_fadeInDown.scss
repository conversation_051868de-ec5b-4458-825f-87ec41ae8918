@if $use-fadeInDown == true {

	@-webkit-keyframes fadeInDown {
		0% {
			opacity: 0;
			-webkit-transform: translateY(-$base-distance * 2);
		}

		100% {
			opacity: 1;
			-webkit-transform: translateY(0);
		}
	}

	@keyframes fadeInDown {
		0% {
			opacity: 0;
			transform: translateY(-$base-distance * 2);
		}

		100% {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.fadeInDown {
		@include animate-prefixer(animation-name, fadeInDown);
	}

}

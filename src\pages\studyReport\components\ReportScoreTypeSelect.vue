<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-03-25 10:38:56
 * @LastEditors: 小圆
-->
<template>
  <div class="header-item">
    <el-radio-group v-model="filterData.scoreType" @input="onChange">
      <el-radio-button :label="0">得分</el-radio-button>
      <el-radio-button :label="1">标准分</el-radio-button>
    </el-radio-group>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import ReportComponent from './ReportComponent';

@Component
export default class ReportScoreTypeSelect extends ReportComponent {
  onChange(value: number) {
    this.filterData.scoreType = value;
    this.FilterModule.trigger('changeFilter');
  }
}
</script>

<style scoped lang="scss">
@import './reportSelect.scss';
</style>

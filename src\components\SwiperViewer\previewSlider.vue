<!--
 * @Descripttion: swiper缩放组件
 * @Author: 小圆
 * @Date: 2024-01-16 14:30:07
 * @LastEditors: 小圆
 
  使用
    <previewSlider :data @slideChange @tabPrev @tabNext>
      <template slot-scope="{item, index}">
      </template>
    </previewSlider>
    item,index为data数据项与索引
-->
<template>
  <div class="previewSlider" v-loading="!pageInited || loading" element-loading-background="rgba(0, 0, 0, 0)">
    <swiper :options="topSwiperOption" ref="galleryTop" class="gallery-top" v-show="data.length > 0"
      v-loading="pageInited && dataLoading" element-loading-background="rgba(0, 0, 0, 0)">
      <swiper-slide v-for="(item, index) in data" :key="index">
        <div class="swiper-tanslate-box">
          <div class="swiper-rorate-box">
            <div class="swiper-zoom-container">
              <div class="swiper-zoom-box">
                <image-pinch @swiperight="slidePrev" @swipeleft="slideNext" @scale="onImageScaled" ref="imagePinch"
                  :minSize="0.1">
                  <slot :item="item" :index="index"></slot>
                </image-pinch>
              </div>
            </div>
          </div>
        </div>
      </swiper-slide>
      <div class="swiper-pagination" slot="pagination"></div>
    </swiper>
    <no-data v-show="data.length == 0 && pageInited && !loading" :text="noText" class="no-data"></no-data>

    <!-- 切换按钮 -->
    <div class="swiper-imageTool-preAndNext">
      <div class="icon-pre" @click="tabPrev"></div>
      <div class="icon-next" @click="tabNext"></div>
    </div>

    <!-- 按钮组 -->
    <div class="swiper-imageTool-box">
      <slot name="toolLeft"></slot>
      <div class="swiper-imageTool">
        <div class="icon-imageTools icon-zoomin" @click="previewTo('zoomin')"></div>
        <div class="icon-imageTools icon-zoomout" @click="previewTo('zoomout')"></div>
        <div class="icon-imageTools icon-recover" @click="previewTo('recover')"></div>
        <div class="icon-imageTools icon-rotateR" @click="previewTo('rotateR')"></div>
        <div class="icon-imageTools icon-close" @click="closePreview('previewMode')"></div>
      </div>
      <slot name="toolRight"></slot>
    </div>

    <!-- 遮罩层 -->
    <div class="preview-swiper-cover"></div>

    <slot name="footer"></slot>
  </div>
</template>
  
<script>
import ImagePinch from '@/components/ImagePinch.vue';
import NoData from '@/components/noData.vue';
import { sleep } from '@/utils/index';

let prevOverflow = '';
let vm = null;

export default {
  components: {
    ImagePinch,
    NoData,
  },
  props: {
    // 数据列表
    data: {
      type: Array,
      default: () => [],
    },

    noText: {
      type: String,
      default: '暂无数据'
    },

    loading: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    data: {
      async handler(newValue, oldValue) {
        if (!newValue) return;
        this.initState();        
      },
    },
  },

  data() {
    return {
      pageInited: false,
      dataLoading: false,
      topSwiperOption: {
        preloadImages: true,
        updateOnImagesReady: true,
        allowTouchMove: false,
        zoom: false,
        longSwipesMs: 1000,
        longSwipes: false,
        spaceBetween: 10,
        initialSlide: 0,
        noSwiping: true,
        noSwipingClass: 'stop-swiping',
        pagination: {
          el: '.swiper-pagination',
          type: 'bullets',
        },
        navigation: {
          nextEl: '.swiper-imageTool-preAndNext .icon-next',
          prevEl: '.swiper-imageTool-preAndNext .icon-pre',
        },
        on: {
          //滑块释放时如果触发slider向前(右、下)切换则执行
          slideNextTransitionStart: function () {
            vm.handleSlideNext();
          },
          //滑块释放时如果触发slider向后(左、上)切换则执行
          slidePrevTransitionStart: function () {
            vm.handleSlidePrev();
          },
        },
      },
    };
  },

  computed: {
    topSwiper() {
      return this.$refs.galleryTop.swiper;
    },
  },

  created() {
    vm = this;
  },

  async mounted() {
    if(this.data.length){
      this.initState()
    }
    // document.body.appendChild(this.$el);
    document.body.style.overflow = 'hidden';
    this.eventListenerInstall();
  },

  beforeDestroy() {
    this.eventListenerUnInstall();
    document.body.style.overflow = prevOverflow;
  },

  destroyed() {
    // if (this.$el && this.$el.parentNode) {
    //   this.$el.parentNode.removeChild(this.$el);
    // }
  },

  methods: {
    async initState() {
        this.dataLoading = true;
        await sleep(300);
        this.topSwiper.slideTo(0);
        await this.$nextTick();
        this.pageInited = true;
        this.dataLoading = false;
    },
    // 注册事件
    eventListenerInstall() {
      this._keyDownHandler = e => {
        e.stopPropagation();
        const keyCode = e.keyCode;
        switch (keyCode) {
          // ESC
          case 27:
            this.closePreview();
            break;
        }
      };
      document.addEventListener('keydown', this._keyDownHandler);
    },

    // 销毁事件
    eventListenerUnInstall() {
      document.removeEventListener('keydown', this._keyDownHandler);
      this._keyDownHandler = null;
    },

    // 当图片处于锁定状态，响应左滑动事件
    slideNext: function () {
      this.topSwiper.slideNext();
    },
    // 当图片处于锁定状态，响应右滑动事件
    slidePrev: function () {
      this.topSwiper.slidePrev();
    },

    handleSlideNext() {
      if (!this.data.length || this.data.length === 1) return;
      this.previewTo('fitWindow', this.topSwiper.activeIndex - 1);
      this.$emit('slideChange', this.topSwiper.activeIndex);
    },
    handleSlidePrev() {
      if (!this.data.length || this.data.length === 1) return;
      this.previewTo('fitWindow', this.topSwiper.activeIndex + 1);
      this.$emit('slideChange', this.topSwiper.activeIndex);
    },

    onImageScaled({ scale, forbidPan }) {
      console.log('onImageScaled forbidPan', forbidPan);
      this.topSwiper.allowTouchMove = false;
    },

    tabPrev() {
      this.$emit('tabPrev', this.topSwiper.activeIndex);
    },

    tabNext() {
      this.$emit('tabNext', this.topSwiper.activeIndex);
    },

    // 预览按钮操作
    previewTo(type, index) {
      let _index = index == null || index == 'undefine' ? this.topSwiper.activeIndex : index;
      if (!this.$refs.imagePinch) return;
      let $imageRef = this.$refs.imagePinch[_index];
      switch (type) {
        case 'zoomin':
        case 'zoomout':
          $imageRef.buttonScale(type);
          break;
        case 'rotateL':
        case 'rotateR':
          $imageRef.rotate();
          break;
        case 'recover':
          $imageRef.reset();
          break;
        case 'fitWindow':
          $imageRef.fitWindow($imageRef.$el.querySelector('img'));
          break;
      }
    },

    // 关闭预览
    closePreview(type) {
      this.$emit('close', type);
    },
  },
};
</script>
  
<style lang="scss" scoped>
@import './previewSlider.scss';

.split-line {
  float: left;
  width: 1px;
  height: 58px;
  background: #b5b5b5;
  //margin: 6px 20px;
  margin: 6px 10px;
}

.no-data {
  top: 30%;
  height: 100%;
  width: 100%;
  position: fixed;
  z-index: 101;
}
</style>
  
<style lang="scss">
.swiper-bottom-stu-list {
  .ivu-tabs-bar {
    border-bottom: unset !important;
    height: 92px !important;
    color: #fff !important;
  }

  .ivu-tabs-nav-container {
    line-height: 60px !important;
    font-size: 30px !important;
  }

  .ivu-tabs-nav-next {
    line-height: 92px !important;
  }

  .ivu-tabs-nav-prev {
    line-height: 92px !important;
  }

  .ivu-icon {
    font-size: 28px;
  }

  .ivu-tabs-content {
    display: none;
  }

  .ivu-tabs-nav .ivu-tabs-tab-active {
    color: #2d8cf0;
    background: #fff;
    color: #fff;
    height: 60px;
    background: #3e73f6;
    margin-top: 16px;
    border-radius: 30px;
  }

  .ivu-tabs-nav .ivu-tabs-tab {
    padding: 0 16px !important;
    margin-right: 34px !important;
  }

  .ivu-tabs-nav-scroll {
    margin: 0 30px;
  }

  .ivu-tabs-ink-bar {
    display: none !important;
    height: 0 !important;
  }

  .ivu-tabs-nav-container:focus .ivu-tabs-tab-focused {
    border-top: unset !important;
  }

  .ivu-tabs .ivu-tabs-tabpane {
    display: none;
  }
}
</style>
  
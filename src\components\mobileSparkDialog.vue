<template>
  <div>
    <el-dialog
      custom-class="spark-dialog"
      :title="list[switchIndex].resourceName"
      center
      :visible="dialogFormVisible"
      width="100%"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <!-- <i
        class="left-arrow el-icon-arrow-left"
        v-if="switchIndex > 0"
        @click="switchSpark('prev')"
      ></i>
      <i
        class="right-arrow el-icon-arrow-right"
        v-if="switchIndex < list.length - 1"
        @click="switchSpark('next')"
      ></i> -->
      <iframe
        :src="list[switchIndex].resourceUrl"
        scrolling="false"
        id="analyzeFrame"
        name="analyzeFrame"
        width="100%"
        height="100%"
        class="bigPicImg"
        frameborder="0"
        v-if="
          list[switchIndex].resourceUrl.indexOf('huohuaschool') >= 0 ||
          list[switchIndex].resourceUrl.indexOf('www.geogebra.org') >= 0 ||
          list[switchIndex].resourceUrl.indexOf('ggb') >= 0
        "
      >
      </iframe>
      <iframe
        :src="list[switchIndex].resourceUrl"
        scrolling="false"
        id="analyzeFrame-2"
        name="analyzeFrame"
        width="100%"
        height="100%"
        class="bigPicImg"
        frameborder="0"
        v-else-if="
          list[switchIndex].resourceUrl.indexOf('leleketang') >= 0 &&
          list[switchIndex].resourceUrl.indexOf('mp4') == -1
        "
      >
      </iframe>
      <video
        id="videoPlay"
        style="z-index: 12"
        webkit-playsinline="true"
        x-webkit-airplay="true"
        playsinline="true"
        x5-video-player-type="h5"
        x5-video-player-fullscreen="true"
        controls="controls"
        width="100%"
        height="100%"
        class="bigPicImg"
        autoplay
        preload="auto"
        :src="list[switchIndex].resourceUrl"
        v-else
      >
        您的浏览器不支持 video 标签
      </video>

      <el-button class="close-dialog-btn" @click="handleClose">关闭</el-button>
    </el-dialog>
  </div>
</template>

<script>
import { getContrastTestList, setContrastTest } from "@/service/pexam";

export default {
  name: "sparkDialog",
  props: ["list", "curIndex"],
  data() {
    return {
      dialogFormVisible: true,
      switchIndex: 0,
    };
  },
  mounted() {
    this.switchIndex = this.curIndex;
    this.$nextTick(() => {
      let voice = document.getElementById("videoPlay");
      if (
        typeof WeixinJSBrdgeReady == "object" &&
        typeof WeixinJSBridge.invoke == "function"
      ) {
        voice.play();
      } else {
        if (document.addEventListener) {
          document.addEventListener(
            "WeixinJSBridgeReady",
            function () {
              voice.play();
            },
            false
          );
        } else if (document.attachEvent) {
          document.attachEvent("WeixinJSBridgeReady", function () {
            voice.play();
          });
          document.attachEvent("onWeixinJSBridgeReady", function () {
            voice.play();
          });
        }
      }
    });
  },
  methods: {
    // 关闭弹窗
    handleClose(done) {
      this.$emit("closeDialog");
      this.dialogFormVisible = false;
      try {
        done();
      } catch (e) {}
    },
    switchSpark(type) {
      if (type === "prev") {
        this.switchIndex = this.switchIndex === 0 ? 0 : (this.switchIndex -= 1);
      } else {
        this.switchIndex =
          this.switchIndex === this.list.length - 1
            ? this.list.length - 1
            : (this.switchIndex += 1);
      }
    },
  },
};
</script>

<style lang="scss">
.spark-dialog {
  &.el-dialog {
    width: 83% !important;
    height: 100%;
    margin: 0 !important;
    border-radius: 0;
    .el-dialog__header {
      background-color: #0198fe;
      color: #fff;
      .el-dialog__title {
        color: #fff;
        font-weight: 400;
      }
      .el-dialog__close {
        color: #fff;
      }
    }
    .el-dialog__body {
      position: fixed;
      left: 0;
      width: 83% !important;
      top: 54px;
      bottom: 0;
      padding: 0;
      background-color: #424156;
      .left-arrow,
      .right-arrow {
        font-size: 60px;
        color: #fff;
        position: fixed;
        top: 50%;
        transform: translateY(-50%);
        z-index: 11;
        width: 60px;
        height: 60px;
        text-align: center;
        line-height: 60px;
        cursor: pointer;
        &.disabled {
          opacity: 0.5;
          pointer-events: none;
        }
      }
      .left-arrow {
        left: 0;
      }
      .right-arrow {
        right: 0;
      }

      .close-dialog-btn {
        z-index: 10;
        position: absolute;
        bottom: 78px;
        right: 5px;
        width: 150px;
        height: 52px;
        font-size: 18px;
        color: #495060;
        background-color: #fff;
        border-color: #dddee1;
      }
    }
  }
}

#analyzeFrame {
  background-color: #424156;
}
#analyzeFrame-2 {
  background-color: #424156;
}
</style>


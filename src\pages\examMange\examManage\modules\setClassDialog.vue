<template>
  <el-dialog
    custom-class="seled-ques-grooup"
    :visible="modalVisible"
    width="1020px"
    :before-close="handleClose"
    :modal-append-to-body="false"
    :close-on-click-modal="false"
  >
    <div slot="title" class="dialog-title">
      <span style="margin-right: 10px">{{ title }}</span>
    </div>
    <div class="set-group-container">
      <div class="set-radio">
        <el-radio-group v-model="classType" @input="handleRadioChange">
          <el-radio :label="1">行政班</el-radio>
          <el-radio :label="3">分层班</el-radio>
        </el-radio-group>
        <el-input
          v-if="classType == 3"
          v-model="keyWord"
          placeholder="请输入班级名称搜索"
          @change="getClassList"
        >
          <i slot="suffix" class="el-input__icon el-icon-search"></i>
        </el-input>
        <el-checkbox v-model="checkCompretAll" @change="handleChecAll">全选</el-checkbox>
      </div>

      <div class="class-container">
        <div
          v-for="(item, index) in classData"
          :key="index"
          class="class-item"
          @click="choiceClass(item)"
          :class="item.isChoice ? 'active' : ''"
        >
          <div class="class-name" :title="item.grade_name + item.class_name">
            {{ item.grade_name }} {{ item.class_name }}
          </div>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeModal">取 消</el-button>
      <el-button type="primary" :loading="isComfirming" @click="sureClick">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { classList, updateExamHomeWorkInfo } from '@/service/api';
import { getQueryString } from '@/utils';
import { saveExamSubjectInfo } from '@/service/pexam';

export default {
  props: {
    modalVisible: {
      type: Boolean,
      required: false,
    },
    subjectInfo: {
      type: Object,
      required: false,
    },
  },
  watch: {
    subjectInfo: {
      handler(newVal, oldVal) {
        this.title = '设置参考学生' + `（${newVal.subjectName}）`;
        this.classType = newVal.classType;
      },
      deep: true,
      immediate: true,
    },
    modalVisible: {
      handler(newVal, oldVal) {
        this.keyWord = '';
        this.dict = this.$sessionSave.get('examDict');
        this.getClassList();
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      isComfirming: false,
      classType: 1,
      //班级数据
      classData: [],
      gradeId: getQueryString('gradeId') || '',
      checkCompretAll: false,
      isIndetCompre: false,
      title: '',
      keyWord: '',
    };
  },
  mounted() {
    // console.log("dict", this.dict);
  },
  methods: {
    handleClose() {
      this.$emit('close-set-class');
    },
    closeModal() {
      this.$emit('close-set-class');
    },
    /**
     * @name:根据年级和班级类型获取班级列表
     */
    async getClassList() {
      let grade = this.dict.grdList.find(q => q.id == this.gradeId);
      let phase = grade.phaseId ? grade.phaseId - 2 : grade.phase;
      let result = await classList(grade.year, phase, this.classType, { keyWord: this.keyWord });
      if (result && result.code == 1) {
        this.classData = result.data.rows;
        if (JSON.stringify(this.subjectInfo) != '{}') {
          this.handleChoice();
        }
      }
    },
    handleChoice() {
      this.classData.forEach(item => {
        if (this.subjectInfo.classIds.indexOf(item.id) != -1) {
          this.$set(item, 'isChoice', true);
        } else {
          this.$set(item, 'isChoice', false);
        }
      });
      const checkedCount = this.classData.filter(item => {
        return item.isChoice;
      }).length;
      this.checkCompretAll = checkedCount === this.classData.length;
    },
    handleChecAll() {
      const checkedCount = this.classData.filter(item => {
        return item.isChoice;
      }).length;
      if (checkedCount == this.classData.length) {
        this.classData.forEach(item => {
          item.isChoice = false;
        });
      } else {
        this.classData.forEach(item => {
          item.isChoice = true;
        });
      }
      // this.isIndetCompre = false;
    },
    /**
     * @name:选择班级
     */
    choiceClass(item) {
      item.isChoice = !item.isChoice;
      const checkedCount = this.classData.filter(item => {
        return item.isChoice;
      }).length;
      this.checkCompretAll = checkedCount == this.classData.length;
      // this.isIndetCompre = checkedCount > 0 && checkedCount < this.classData.length;
    },
    /**
     * @name:确定修改参考班级
     */
    sureClick() {
      const finalData = this.classData.filter(item => {
        return item.isChoice;
      });
      const classIds = finalData.map(item => {
        return item.id;
      });
      const classNames = finalData.map(item => {
        return item.grade_name + item.class_name;
      });
      if(classIds.length == 0){
        this.$message({
          message: '请选择至少一个班级！',
          type: 'warning',
          duration: 1000,
        });
        return;
      }
      saveExamSubjectInfo({
        personalBookId: this.subjectInfo.personalBookId,
        classIds: classIds.join(','),
        classNames: classNames.join(','),
      }).then(res => {
        this.$message({
          message: '设置成功！',
          type: 'success',
          duration: 1000,
        });
        this.$emit('confirm-set-class');
      });
    },

    handleRadioChange() {
      this.keyWord = '';
      this.getClassList();
    },
  },
};
</script>

<style lang="scss" scoped>
.class-container {
  display: flex;
  flex-wrap: wrap;
  overflow-y: auto;
  max-height: 350px;
  .class-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 180px;
    height: 40px;
    background: #f4f4f4;
    border-radius: 6px;
    margin: 20px 14px 4px 0;
    cursor: pointer;
    &.active {
      background: rgba(0, 141, 234, 0.08);
      border: 1px solid #008dea;
      font-weight: 400;
      color: #008dea;
    }
  }
  .class-name {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    padding: 0 10px;
  }
}
.set-radio {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 30px;

  .el-radio-group {
    flex: 1;
  }

  .el-input {
    width: 200px;
    margin-right: 20px;
  }
}
</style>
<style lang="scss">
.seled-ques-grooup {
  .el-dialog__header {
    height: 45px;
    .dialog-title {
      line-height: 45px;
      font-size: 18px;
    }
  }
}
</style>

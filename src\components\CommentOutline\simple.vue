<!--
 * @Description: 答题概览
 * @Author: qmzhang
 * @Date: 2024-02-18 11:50:28
 * @LastEditTime: 2024-02-28 16:15:00
 * @FilePath: \personal-bigdata\src\components\CommentOutline\simple.vue
-->
<template>
  <div class="comment-outline noselect"
    :style="{ '--block-size': blockSize, '--block-fontsize': blockFontSize, '--block-margin': blockMargin }">
    <div class="com-header clearfix">
      <div class="title pull-left">答题概览</div>
      <el-button class="pull-right" type="text" @click="openLevelSetting">
        <i class="el-icon-setting ver-mid"></i>
      </el-button>
    </div>

    <div class="com-content">
      <!-- 题目序号列表 -->
      <div class="com-ques-list" v-for="item in quesNumberList" :key="item.index">
        <div class="ques-title">{{ item.fullTitle }}</div>
        <ul class="com-number-list list-none clearfix">
          <li class="color-block ver-mid dis-inline click-element" :class="`level-${subItem.level}`"
            v-for="subItem in item.list" :key="subItem.quesId" @click="anchorToTarget(subItem)">{{
                            subItem.quesName
                        }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import LevelSettingDialog from "./levelSettingDialog.vue";
import createDialog from "@/components/createDialog";

interface QuesCard {
  index: number;
  fullTitle: string;
  list: any[];
}

export type QuesCardList = QuesCard[];
@Component
export default class CommentOutline extends Vue {
  @Prop({ default: () => [] }) list: QuesCardList;
  @Prop({ default: "36px" }) blockSize: string;
  @Prop({ default: "16px" }) blockFontSize: string;
  @Prop({ default: "20px" }) blockMargin: string;

  levelList: number[] = [0, 0.6, 0.7, 0.9, 1];
  levelStrList: string[] = ["不及格", "及格", "中等", "优秀"];
  // 分数等级列表
  get scoreLevel(): { name: string; level: number[] }[] {
    let list = [];
    for (let i = 0; i < 4; i++) {
      list.push({
        name: this.levelStrList[i],
        level: [this.levelList[i], this.levelList[i + 1]],
      });
    }

    return list;
  }

  // 根据list初始化最终渲染的题号列表
  get quesNumberList(): QuesCardList {
    let list = this.list.slice();

    list.forEach((item) => {
      item.list.forEach((subItem) => {
        for (let i = 0; i < this.levelList.length; i++) {
          const lItem = this.levelList[i];
          if (Number(subItem.classScoringRate) > lItem) continue;

          subItem.level = i === 0 ? 0 : i - 1;
          break;
        }
      });
    });
    return list;
  }

  /* 锚向对应目标 */
  anchorToTarget(item: any) {
    this.$emit("anchor", item);
  }

  /* 打开等级设置 */
  openLevelSetting() {
    createDialog({
      title: "设置",
      size: "middle",
      appendToBody: true,
      closeOnClickModal: true,
      customClass: "el-dialog--globalVW",
      component: LevelSettingDialog,
      store: this.$store,
      router: this.$router,
      width: "560px",
      data: {
        levelList: this.levelList,
        levelStrList: this.levelStrList,
      },
      close: () => {
        //关闭后触发
        console.log("dialog is closed");
      },
      change: () => {},
      confirm: (data: number[]) => {
        this.levelList = data;
      },
    });
  }
}
</script>

<style lang="scss" scoped>
@import url("./styles/index.scss");

.com-number-list {
  > li {
    float: none;
    width: 100%;
  }
}

.com-content {
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
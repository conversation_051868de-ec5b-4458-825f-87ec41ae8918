<template>
  <div class="xkw-container" style="width: 100%; height: 100%;padding: 0;">
    <iframe
      id="xueke-iframe"
      :src="url"
      allowfullscreen="true"
      frameborder="0"
      scrolling="no"
      :style="style"
    ></iframe>
  </div>
</template>

<script>
import { initPaperQuestionInfo } from "@/service/api";
import { getToken } from "@/service/auth";
export default {
  name: "xueke",
  data() {
    return {
      url:
        "https://xkwzujuan.iclass30.com/xkwejt.html?userId=" +
        this.$sessionSave.get("loginInfo").id,
      style: "width: 100%; height: 100%",
      isFinish:false
    };
  },
  mounted() {
    window.addEventListener("message",this.listenMsg,false);
  },
  destroyed(){
    window.removeEventListener("message",this.listenMsg,false);
  },
  methods: {
    listenMsg(e){
      let data = typeof e.data == "object" ? e.data : JSON.parse(e.data);
        if (data.type == "info-zjw") {
          switch (data.data.event) {
            case "page-height":
              this.setIframeStyle(data.data.content);
              break;
            case "paper-callback":
              if(!this.isFinish){
                this.isFinish = true;
                this.getXKQuesInfos(data.data.content);
              }
              break;
            default:
              break;
          }
        }
    },
    setIframeStyle(content) {
      // this.style = `width: 100%; height: ${content.height}px`;
    },
    /**
     * @name: 获取学科网题目
     * @param info 参数信息
     * @description: 方法说明
     */
    getXKQuesInfos(info) {
      initPaperQuestionInfo({
        userId: this.$sessionSave.get("loginInfo").id,
        xkwOpenId: info.openid,
        xkwPaperId: info.paperid,
        subjectId: info.bankid,
        schoolId: this.$sessionSave.get("loginInfo").schoolid,
        userName:this.$sessionSave.get("loginInfo").realname
      }).then((res) => {
        if(res.code == 1){
          this.$router.push({
            path: "/previewPaper", 
            query: {
              tbId: res.data.paperId || "",
              subId: res.data.subjectCode || "",
              userId: this.$sessionSave.get("loginInfo").id,
              schoolId: this.$sessionSave.get("schoolInfo").id,
              fromPaper: "paperBag",
              shareType: "",
              isXueKe:1
            }
          });
        }else{
          this.$message.error(res.msg);
        }
        
      });
    }
  },
};
</script>

<style></style>

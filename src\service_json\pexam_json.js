import * as httpApi from './index';

const kklUrl = process.env.VUE_APP_KKLURL;

/**
 * api转换
 * @type {{POST: API.POST, GET: API.GET}}
 */
const API = {
    POST: function (url, params) {
        return httpApi.POST(url, params, kklUrl);
    },
};

/**
 * 获取学生扫描的试卷信息
 * @param params
 * @returns {*}
 */
export const updateStudentPaper = (params) => {
    return API.POST('/pexam/scan/updateStudentPaper', params);
};


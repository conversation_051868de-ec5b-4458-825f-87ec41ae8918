/**
 * Created by ssh on 2018/12/19
 * description: axios请求全局配置
 **/
import qs from 'qs';
import axios from 'axios';
import baseConf from './config';

import { getToken, getUserId } from './auth.js';
import { sessionSave } from '@/utils';
import { Message } from '@iclass/element-ui';

// // 基地址
// config.baseURL = BASE_URL;

const service = axios.create(baseConf);

// 请求拦截
service.interceptors.request.use(
  config => {
    const token = getToken() || sessionSave.get('loginInfo')?.token;
    if (token) {
      // 判断是否存在token，如果存在的话，则每个http header都加上token
      config.headers.token = token;
    }
    if (config.method === 'post') {
      if (config.data) {
        if (config.headers['Content-Type'] != 'application/json; charset=utf-8') {
          config.data.serviceVersion = '5.0';
          config.data.token = token;
          config.data = qs.stringify(config.data);
        }
        // if (config.url.indexOf('/testbank/testBank/updateBigViewPaper') > -1) {
        //   config.headers['Content-Type'] = 'application/json';
        // }
      }
    } else if (config.method === 'get') {
      if (config.params) {
        config.params.ServiceVersion = '13.0';
      }
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 返回状态判断
service.interceptors.response.use(
  res => {
    // 判断接口返回类型是否是字符串
    if (typeof res.data == 'string') {
      return Promise.resolve(res.data);
    }
    let _data = '';
    switch (res.data.code) {
      case 1:
        _data = res.data;
        break;
      case -1:
        console.error(`请求失败: ${res.config.url} ${JSON.stringify(res.config.params)}`);
        console.error(`失败原因: ${res.data.msg}`);
        Message.error({
          message: res.data.msg,
          duration: 2000,
        });
        _data = Promise.reject(res.data);
        break;
      case -200:
        _data = Promise.reject(res.data);
        break;
      case 1001:
        _data = res.data.code;
        break;
      case -2:
        _data = res.data.code;
        break;
      default:
        _data = res.data;
        break;
    }

    return Promise.resolve(_data);
  },
  error => {
    // 404等问题可以在这里处理
    switch (error.response.status) {
      case 400:
        error.message = '请求错误';
        break;

      case 401:
        error.message = '未授权，请登录';
        break;

      case 402:
        error.message = '登录过期，请重新登录';
        break;

      case 403:
        error.message = '拒绝访问';
        break;

      case 404:
        error.message = `请求地址出错: ${error.response.config.url}`;
        break;

      case 408:
        error.message = '请求超时';
        break;

      case 500:
        error.message = '服务器内部错误';
        break;

      case 501:
        error.message = '服务未实现';
        break;

      case 502:
        error.message = '网关错误';
        break;

      case 503:
        error.message = '服务不可用';
        break;

      case 504:
        error.message = '网关超时';
        break;

      case 505:
        error.message = 'HTTP版本不受支持';
        break;
      default:
        error.message = '服务不可用';
        break;
    }
    console.error(error.message);
    Message.error({
      message: error.message,
      duration: 2000,
    });
    return Promise.reject(error);
  }
);

export default service;

// 通用方法
export const POST = (url, params, baseUrl) => {
  if (params !== undefined) {
    params.optUserId = getUserId();
  }
  return service({
    method: 'post',
    url,
    data: params,
    baseURL: baseUrl,
  });
};

// 通用方法
export const POSTJson = (url, params, baseUrl) => {
  if (params !== undefined) {
    params.optUserId = getUserId();
  }
  return service({
    method: 'post',
    url,
    headers: {
      post: { 'Content-Type': 'application/json; charset=utf-8' },
    },
    data: JSON.stringify(params),
    baseURL: baseUrl,
  });
};
export const POSTForm = (url, params, baseUrl) => {
  return new Promise((resolve, reject) => {
    if (params !== undefined) {
      params.append("optUserId",getUserId());
    }
    axios.post(baseUrl + url, params, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'token': getToken() || sessionSave.get('loginInfo')?.token,
      },
    }).then(res => {
      if(res.data.code == 1){
        resolve(res.data);
      }else{
        if(res.data.data.url){
          resolve({code:2,data:res.data.data});
        }else{
          reject(res.data);
        }
      }
    }).catch(err => {
      reject(err);
    })
  })
};

// 新post请求
export const POSTSTRING = (url, params, baseUrl) => {
  return service({
    method: 'post',
    url,
    data: params,
    baseURL: baseUrl,
  });
};

export const GET = (url, params, baseUrl) => {
  if (params !== undefined) {
    params.optUserId = getUserId();
  }
  return service({
    method: 'get',
    url,
    params,
    baseURL: baseUrl,
  });
};

export const DELETE = (url, params) => {
  if (params !== undefined) {
    params.optUserId = getUserId();
  }
  return service({
    method: 'delete',
    url,
    params,
  });
};

// 用于mock拦截请求
export function FETCH(url, params) {
  // return new Promise((resolve, reject) => {
  //     axios.post(url, params)
  //         .then(response => {
  //             resolve(response.data);
  //         })
  //         .catch((error) => {
  //             reject(error);
  //         })
  // })
  return axios.post(url, qs.stringify(params)).then(res => res.data);
}

<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-04-29 16:44:45
 * @LastEditors: 小圆
-->

<template>
  <div>
    <div v-loading="tableLoading">
      <base-table
        v-if="tableData.length"
        :data="tableData"
        :column="tableColumns"
        v-bind="getTableAttr()"
        v-drag-table
      ></base-table>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>

    <!-- 学生成绩变化趋势（原始分 -->
    <div style="margin-top: 20px; width: 100%; height: 500px" id="ScoreChart"></div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins, Prop } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';
import {
  getDefaultGrid,
  getDefaultLegend,
  getDefaultTitle,
  getDefaultToolBox,
  getDefaultTooltipFormatter,
} from '../plugins/DefaultEchartsOption';

@Component({
  components: {},
})
export default class Index extends Mixins(TableCommon) {
  tableLeftFixed: any[] = ['time', 'examName'];

  // 学生成绩变化趋势（原始分）图表
  scoreChart: EChartsType = null;

  get subjectColumns() {
    return this.tableColumns.filter(item => item.prop !== 'time' && item.prop !== 'examName');
  }

  callbackGetTableData() {
    if (this.scoreChart) {
      this.scoreChart.dispose();
    }
    const dom = document.getElementById('ScoreChart');
    this.scoreChart = this.$echarts.init(dom);

    const series = [];
    this.subjectColumns.forEach(col => {
      const data = [];
      this.tableData.forEach(item => {
        data.push(item[col.prop] || undefined);
      });
      series.push({
        name: col.title,
        type: 'line',
        data: data,
      });
    });

    let option: EChartsOption = {
      grid: getDefaultGrid({ top: 100 }),
      title: getDefaultTitle({
        text: '学生成绩变化趋势',
      }),
      legend: getDefaultLegend(),
      tooltip: {
        trigger: 'item',
        formatter: params => {
          return getDefaultTooltipFormatter(
            params.name,
            params.seriesName,
            params.value,
            params.color
          );
        },
      },
      toolbox: getDefaultToolBox(),
      xAxis: {
        type: 'category',
        data: this.tableData.map(item => item.examName),
        axisLabel: {
          rotate: 45,
        },
        boundaryGap: false,
      },
      yAxis: {
        type: 'value',
      },
      series: series,
    };
    this.scoreChart.setOption(option);
  }
}
</script>

<style scoped lang="scss"></style>

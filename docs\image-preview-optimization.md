# 图片上传预览功能优化方案

## 问题描述

原有的Vue.js组件使用Element UI的`el-image`组件进行图片预览，存在以下问题：
- 在Element UI预览弹窗中旋转图片后，旋转状态无法同步回图片列表
- 用户在预览模式下的旋转操作丢失，需要重新在列表中手动旋转

## 解决方案

### 1. 核心改进

#### 1.1 旋转状态管理
- 新增 `imageRotations` 对象来统一管理每张图片的旋转角度
- 使用图片的唯一ID作为key，旋转角度（0°、90°、180°、270°）作为value
- 所有旋转操作都通过这个统一状态进行管理

#### 1.2 自定义预览组件
替换Element UI的预览功能，实现完全自定义的预览组件：
- 支持图片旋转、缩放、拖拽
- 支持鼠标滚轮缩放
- 支持键盘导航（左右箭头切换图片）
- 旋转状态与列表同步

#### 1.3 响应式图片显示
- 根据旋转角度动态调整图片最大尺寸
- 确保旋转后的图片不会超出显示区域
- 保持图片宽高比，完整显示图片内容

### 2. 技术实现

#### 2.1 数据结构
```javascript
data() {
  return {
    // 图片列表
    filePathList: [],
    // 旋转状态管理
    imageRotations: {}, // { imageId: rotationAngle }
    // 预览相关
    previewVisible: false,
    currentPreviewIndex: 0,
    previewScale: 1,
    // 拖拽相关
    isDragging: false,
    dragStart: { x: 0, y: 0 },
    imagePosition: { x: 0, y: 0 }
  }
}
```

#### 2.2 核心方法
- `getImageRotation(imageId)`: 获取图片旋转角度
- `getOriginalImageUrl(url)`: 获取原始图片URL（去除旋转参数）
- `openPreview(index)`: 打开预览
- `rotatePreviewImage()`: 预览中旋转图片
- `handleRotate(item)`: 列表中旋转图片
- `handleWheel(event)`: 鼠标滚轮缩放

#### 2.3 UI组件结构
```vue
<template>
  <!-- 图片列表 -->
  <div class="img-list-item" 
       :style="{ transform: `rotate(${getImageRotation(item.id)}deg)` }"
       @click="openPreview(index)">
    <img :src="getOriginalImageUrl(item.url)" />
  </div>
  
  <!-- 自定义预览组件 -->
  <div class="custom-image-preview">
    <div class="preview-container">
      <div class="preview-header">...</div>
      <div class="preview-body">
        <div class="preview-controls">
          <img :style="{ 
            transform: `rotate(${rotation}deg) scale(${scale})`,
            maxWidth: getImageMaxSize().width,
            maxHeight: getImageMaxSize().height
          }" />
        </div>
        <div class="preview-toolbar">...</div>
      </div>
    </div>
  </div>
</template>
```

### 3. 功能特性

#### 3.1 旋转功能
- ✅ 支持0°、90°、180°、270°四个角度旋转
- ✅ 预览中旋转与列表旋转状态同步
- ✅ 旋转后图片自动适应显示区域

#### 3.2 缩放功能
- ✅ 鼠标滚轮缩放（0.1x - 5x）
- ✅ 工具栏按钮缩放
- ✅ 一键重置视图

#### 3.3 拖拽功能
- ✅ 鼠标拖拽移动图片
- ✅ 拖拽状态视觉反馈

#### 3.4 导航功能
- ✅ 左右箭头切换图片
- ✅ 图片计数显示
- ✅ 键盘ESC关闭预览

#### 3.5 响应式设计
- ✅ 根据旋转角度动态调整最大尺寸
- ✅ 不同宽高比图片完美适配
- ✅ 移动端友好的触摸操作

### 4. 使用方法

#### 4.1 基本使用
```vue
<template>
  <create-third-card-dialog
    :dialogVisible="dialogVisible"
    @close-dialog="closeDialog"
    @confirm-create="handleConfirm"
  />
</template>
```

#### 4.2 测试组件
使用 `src/components/ImagePreviewTest.vue` 进行功能测试

### 5. 兼容性

- ✅ 保持原有上传、删除、交换功能不变
- ✅ 保持原有扫描功能不变
- ✅ 保持原有数据结构和API接口不变
- ✅ 向后兼容，无破坏性更改

### 6. 性能优化

- 使用CSS transform进行旋转，硬件加速
- 图片懒加载和缓存
- 防抖处理滚轮事件
- 内存泄漏防护（组件销毁时清理事件监听）

### 7. 浏览器支持

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 总结

这个优化方案完全解决了原有的旋转状态同步问题，同时提供了更丰富的图片预览功能。通过统一的状态管理和自定义预览组件，实现了预览与列表的完美同步，大大提升了用户体验。

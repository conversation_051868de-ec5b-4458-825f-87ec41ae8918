<template>
  <el-dialog
    custom-class="seled-ques-grooup"
    :visible="modalVisible"
    width="790px"
    :before-close="handleClose"
    :modal-append-to-body="false"
    :close-on-click-modal="false"
  >
    <div slot="title" class="dialog-title">
      <span style="margin-right: 10px">添加综合学科</span>
    </div>
    <div class="add-subject-container">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="综合学科" prop="generalSubName">
          <el-input
            style="width: 300px"
            placeholder="请输入综合学科名称，8字以内，如文综"
            v-model="ruleForm.generalSubName"
            maxlength="8"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="包含单科" prop="currentSubjects">
          <el-checkbox-group v-model="ruleForm.currentSubjects">
            <el-checkbox
              v-for="item in checkSubjectList"
              :key="item.id"
              :label="item.id"
              :disabled="selectedSubjectIds.includes(item.id) || disabledSubjectList.includes(item.id)"
              >{{ item.name }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="答题卡" prop="useCardType">
          <el-radio-group v-model="ruleForm.useCardType">
            <el-radio :disabled="source=='1'" label="0">共用1张答题卡</el-radio>
            <el-radio label="1">各学科独立答题卡</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <!-- <el-checkbox-group v-model="currentSubjects">
        <el-checkbox
          v-for="item in checkSubjectList"
          :key="item.id"
          :label="item.id"
          :disabled="selectedSubjectIds.includes(item.id)"
          >{{ item.name }}</el-checkbox
        >
      </el-checkbox-group> -->
      <!-- <div class="add-button">
        <el-button
          type="text"
          :disabled="ruleForm.currentSubjects.length < 2"
          @click="addComperSubject"
          ><i class="el-icon-plus"></i>新增综合学科</el-button
        >
        <div class="sel-subjects">
          <div class="sel-subjects-container">
            <span>已添加：</span>
            <div
              class="sel-subjects-item"
              v-for="(item, index) in selectedSubjectList"
              :key="index"
            >
              {{ item.showNames }}<i class="el-icon-close" @click.stop="cancelSubject(item)"></i>
            </div>
          </div>
        </div>
      </div> -->
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeModal">取 消</el-button>
      <el-button type="primary" :loading="isComfirming" @click="sureClick">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    modalVisible: {
      type: Boolean,
      required: false,
    },
    checkSubjectIds: {
      type: Array,
      required: false,
    },
    subjectList: {
      type: Array,
      required: false,
    },
    selComperSubject: {
      type: Array,
      required: false,
    },
    disabledSubjectList:{
      type: Array,
      required: false,
    },
    source:{
      type: String,
      required: false,
      default: '4' //4：网阅，3：手阅，1：补录
    }
  },
  data() {
    return {
      isComfirming: false,
      //已选的学科列表
      checkSubjectList: [],
      //已经选为综合学科的学科id
      selectedSubjectIds: [],
      //已选的综合学科
      selectedSubjectList: [],
      ruleForm: {
        //综合学科名称
        generalSubName: '',
        //当前选择的学科
        currentSubjects: '',
        useCardType: '1',
      },
      rules: {
        generalSubName: [
          { required: true, message: '请输入综合学科名称', trigger: 'blur' },
          { min: 1, max: 8, message: '长度在 1 到 8 个字符', trigger: 'blur' },
        ],
        currentSubjects: [{ required: true, message: '请选择学科', trigger: 'change' }],
        useCardType: [{ required: true, message: '请选择答题卡', trigger: 'change' }],
      },
    };
  },
  watch: {
    modalVisible(newVal, oldVal) {
      this.ruleForm.generalSubName = '';
      this.ruleForm.currentSubjects = [];
      this.checkSubjectList = this.subjectList.filter(item => {
        return this.checkSubjectIds.includes(item.id);
      });
      this.selectedSubjectList = JSON.parse(JSON.stringify(this.selComperSubject));
      this.selectedSubjectIds = this.selectedSubjectList
        .map(ite => ite.ids)
        .join(',')
        .split(',');
    },
  },
  mounted() {},
  methods: {
    // 关闭弹窗
    handleClose(done) {
      this.$emit('close-add-dialog');
    },
    /**
     * 取消
     */
    closeModal() {
      this.$emit('close-add-dialog');
    },
    /**
     * @name:确定添加综合学科
     */
    sureClick() {
      this.$refs.ruleForm.validate(async valid => {
        if (valid) {
          this.addComperSubject();
        } else {
        }
      });
    },
    /**
     * @name:新增综合学科
     */
    addComperSubject() {
      let list = this.checkSubjectList.filter(item => {
        return this.ruleForm.currentSubjects.includes(item.id);
      });
      let names = list.map(item => item.name).join('-');
      let ids = list.map(item => item.id).join('-');
      let obj = {
        ids: this.ruleForm.currentSubjects.join(','),
        showNames: this.ruleForm.generalSubName,
        showIds: ids,
        subNames: names,
        useCardType: this.ruleForm.useCardType,
      };
      this.selectedSubjectList.push(obj);
      this.selectedSubjectIds = [...this.ruleForm.currentSubjects, ...this.selectedSubjectIds];
      this.ruleForm.currentSubjects = [];
      this.ruleForm.generalSubName = '';
      this.$emit('confirm-add-dialog', this.selectedSubjectList);
    },
    /**
     * @name:取消综合学科
     */
    cancelSubject(item) {
      this.selectedSubjectIds = this.selectedSubjectIds.filter(ite => {
        return !item.ids.split(',').includes(ite);
      });
      this.selectedSubjectList = this.selectedSubjectList.filter(ite => {
        return item.ids != ite.ids;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.add-button {
  margin-top: 15px;
}
.sel-subjects-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .sel-subjects-item {
    height: 30px;
    line-height: 30px;
    border-radius: 15px;
    padding: 0 4px 0 8px;
    margin: 10px;
    background: #f5faff;
    cursor: pointer;
    &:hover {
      color: #409eff;
    }
  }
}
.add-subject-input {
  display: flex;
  align-items: center;
}
</style>
<style lang="scss">
.seled-ques-grooup {
  .el-dialog__header {
    height: 45px;
    .dialog-title {
      line-height: 45px;
    }
  }
}
</style>
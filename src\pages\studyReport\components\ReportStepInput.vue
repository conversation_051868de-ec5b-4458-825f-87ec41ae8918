<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-03-25 10:32:59
 * @LastEditors: 小圆
-->
<template>
  <div class="header-item">
    <span class="header-item__label"
      >步长：
      <span
        ><el-input-number
          class="header-item__inputnum"
          v-model="filterData.step"
          controls-position="right"
          :min="1"
          :step="1"
          step-strictly
          v-bind="$attrs"
          v-on="$listeners"
        ></el-input-number
      ></span>
      步</span
    >
  </div>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import ReportComponent from './ReportComponent';

@Component
export default class ReportLimitInput extends ReportComponent {}
</script>

<style scoped lang="scss">
@import './reportSelect.scss';
</style>

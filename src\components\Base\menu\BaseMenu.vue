<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-03-18 15:06:35
 * @LastEditors: 小圆
-->
<template>
  <el-menu
    ref="elMenu"
    v-bind="this.$attrs"
    v-on="this.$listeners"
    :default-active="getDefaultActive()"
    :class="{ 'base-menu--accordion': accordion }"
  >
    <base-menu-item
      v-for="item in menuList"
      :key="item.path"
      :item="item"
      :baseRoute="baseRoute"
    ></base-menu-item>
  </el-menu>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import BaseMenuItem from './BaseMenuItem.vue';
import { IMenuConfig } from './type';
import { ElMenu } from '@iclass/element-ui/types/menu';

function toCamelCase(str) {
  return str.replace(/-(\w)/g, (_, c) => (c ? c.toUpperCase() : ''));
}

@Component({
  components: {
    BaseMenuItem,
  },
})
export default class BaseMenu extends Vue {
  // 菜单列表
  @Prop({ type: Array, default: () => [] }) menuList!: IMenuConfig[];
  // 路由前缀，如：/home/<USER>
  @Prop({ type: String, default: '' }) baseRoute!: string;
  // 是否开启手风琴模式
  @Prop({ type: Boolean, default: false }) accordion!: boolean;

  // :unique-opened="isUniqueOpened"
  // isUniqueOpened: boolean = !!this.attrs['uniqueOpened'] || this.accordion || false;

  get attrs() {
    const attrs = {};
    for (let key in this.$attrs) {
      // 将属性名转换为驼峰命名
      const camelKey = toCamelCase(key);
      attrs[camelKey] = this.$attrs[key];
    }
    return attrs;
  }

  get elMenu(): ElMenu {
    return this.$refs.elMenu as ElMenu;
  }

  mounted() {}

  get activeMenu() {
    return this.$route.path;
  }

  open(index, indexPath) {
    this.$emit('open', index, indexPath);
  }
  close(index, indexPath) {
    this.$emit('close', index, indexPath);
  }
  select(index, indexPath) {
    this.$emit('select', index, indexPath);
  }

  getDefaultActive() {
    if (Reflect.has(this.$attrs, 'defaultActive')) {
      return this.$attrs.defaultActive;
    } else {
      return this.activeMenu;
    }
  }
}
</script>

<style scoped lang="scss">
@import './index.scss';
</style>

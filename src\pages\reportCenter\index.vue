<template>
  <div class="report-center">
    <div class="header">
      <!-- <el-tabs type="card" v-model="activeTab" @tab-click="onClickTab">
        <el-tab-pane :label="item.name" :name="item.id" v-for="item in tabList"></el-tab-pane>
      </el-tabs> -->
      <div class="header__tabs" v-if="tabList">
        <div
          class="header__tab"
          v-for="item in tabList"
          :key="item.id"
          :class="{ active: item.id == activeTab }"
          @click="onClickTab(item)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>

    <div class="tip" v-if="!isCef">
      依据教育部“双减”政策要求，严禁拍照/截图/导出等方式泄露考试成绩、排名等相关信息，违者上报至教育主管部门严肃追责！
    </div>
    <router-view v-if="activeTab" :reportType="activeTab"></router-view>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { isCef } from '@/utils/index';
import { getSchCfgAPI } from '@/service/pexam';
import { SchoolSettingType } from '../schoolSetting/types';

@Component
export default class ReportCenter extends Vue {
  // 是否cef环境
  isCef = isCef();

  pathMapping = {
    class: '/home/<USER>/list',
    school: '/home/<USER>/schoolList',
    track: '/home/<USER>/teaching',
  };

  // 列表
  tabList = this.$sessionSave.get('reportCenterTabList') || [
    {
      id: 'class',
      path: '/home/<USER>/list',
      name: '单次报告',
    },
    {
      id: 'track',
      path: '/home/<USER>/teaching',
      name: '历次追踪',
    },
  ];
  // 当前TabId
  activeTab = '';
  // keepAlive 缓存数据
  keepCache = {
    schoolId: this.$sessionSave.get('schoolInfo').id,
  };

  created() {
    this.initTabList();
  }

  activated() {
    if (this.keepCache.schoolId !== this.$sessionSave.get('schoolInfo').id) {
      this.keepCache.schoolId = this.$sessionSave.get('schoolInfo').id;
      this.initTabList();
    } else {
      let tabList = this.$sessionSave.get('reportCenterTabList');
      if (tabList) {
        this.tabList = tabList;
      } else {
        this.initTabList();
      }
    }
    this.checkTab();
  }

  async initTabList() {
    const res = await getSchCfgAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      type: SchoolSettingType.ReportSetting,
    }).catch(err => {
      return null;
    });
    if (res && res.data && res.data.jCfg) {
      const data = res.data;
      const jCfg = data.jCfg;
      const list = jCfg.find(item => item.checked == 1).data;
      this.tabList = list.map(item => {
        let path = this.pathMapping[item.id];
        return {
          id: item.id,
          path,
          name: item.name,
        };
      });
      this.$sessionSave.set('reportCenterTabList', this.tabList);
    }
    this.checkTab();
  }

  // 检测当前Tab
  checkTab() {
    for (const item of this.tabList) {
      let route = this.$route.matched.find(t => t.path == item.path);
      if (route) {
        this.activeTab = item.id;
        return;
      }
    }
  }

  // 切换Tab
  onClickTab(item) {
    this.activeTab = item.id;
    this.$router.push(item.path);
  }
}
</script>

<style lang="scss" scoped>
.report-center {
  display: flex;
  flex-direction: column;

  .header {
    margin-bottom: 10px;
    ::v-deep {
      .el-tabs__header {
        margin: unset !important;
      }

      .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
        background: #fff;
        font-weight: bold;
      }

      .el-tabs__item {
        font-size: 16px !important;
      }
    }

    .header__tabs {
      display: flex;
      align-items: center;
      height: 50px;
      width: fit-content;
      padding: 0 10px;

      background: #ffffff;
      border-radius: 6px;
      box-shadow: 0px 1px 10px 0px rgba(228, 232, 235, 0.6);
    }

    .header__tab {
      padding: 0 20px;
      min-width: 132px;
      height: 40px;
      line-height: 40px;
      font-size: 18px;
      color: #333333;
      text-align: center;
      border-radius: 6px;
      cursor: pointer;

      &.active {
        font-weight: bold;
        font-size: 18px;
        color: #ffffff;
        background-color: #409eff;
      }
    }
  }

  .tip {
    color: #fd032f;
    font-size: 14px;
    margin-bottom: 10px;
  }
}
</style>

<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-03-17 16:42:54
 * @LastEditors: liuyue <EMAIL>
-->
<template>
  <div class="reference-sample report-wrapper" v-loading="loading">
    <headerFilter @updateFilter="updateFilter" ref="headerFilter" class="header-filter"></headerFilter>

    <div>
      <div class="titleLine">写作思路</div>
      <div class="writing-content">
        <p>
          {{ exampleComposition?.describe }}
        </p>
      </div>
    </div>
    <div>
      <div class="titleLine">参考范文</div>
      <div class="writing-content">
        <p>
          <vue-markdown :source="exampleComposition?.example"></vue-markdown>
        </p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { getExampleCompositionAPI } from '@/service/api';
import { Component, Vue } from 'vue-property-decorator';
import NoData from '@/components/noData.vue';
import HeaderFilter from '@/components/headerFilter.vue';
import VueMarkdown from 'vue-markdown'

export interface ExampleComposition {
  thought: string;
  describe: string;
  example: string;
}

@Component({
  components: {
    NoData,
    HeaderFilter,
    VueMarkdown
  },
})
export default class ReferenceSample extends Vue {
  // 当前选中的筛选项数据
  filterData = {
    classId: '',
    classIds: [],
    subjectId: '',
    phaseId: '',
    xfId: '',
    classList: [],
    aliasName: '', // 组合学科
    qType: 0, // 0:得分 1：赋分
    source: 0, // 0:成绩榜 1：组合成绩榜
    abPaper: '', // -1:普通 0:A卷 1:B卷
    quesInfo: null, // 作文题
  };

  // 当前workId
  get workId() {
    let subjectId = this.filterData.subjectId;
    let subjectList = this.$sessionSave.get('innerNoRoleSubjectList');
    let subject = subjectList.find(item => item.id == subjectId);
    if (subject) {
      return subject.workIds[Number(this.filterData.abPaper)];
    }
    return '';
  }

  // 作文范文
  exampleComposition: ExampleComposition | null = null;
  // 是否加载中
  loading = false;

  mounted() { }

  // 更新筛选条件
  updateFilter(filterData) {
    this.filterData = this.$deepClone(filterData);
    this.getExampleComposition();
  }

  // 获取作文范文
  async getExampleComposition() {
    this.loading = true;
    const res = await getExampleCompositionAPI({
      schoolId: this.$sessionSave.get('schoolInfo').id,
      workId: this.workId,
      quesNo: this.filterData.quesInfo?.quesNo,
      tQuesNo: this.filterData.quesInfo?.tQuesNo,
    });

    if (res.code == 1) {
      this.exampleComposition = res.data;
    }
    this.loading = false;
  }
}
</script>

<style scoped lang="scss">
@import './index.scss';

.reference-sample {}

.writing-content {
  padding: 10px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  line-height: 1.8;
  transition: all 0.3s ease;

  p {
    text-indent: 2em;
  }

  &:hover {
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  }
}
</style>

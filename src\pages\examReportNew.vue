<template>
  <div>
    <div class="examReport display_flex flex-direction_column">
      <!-- 头部切换 -->
      <el-tabs class="examReport__tabs" v-model="pageType" type="card" @tab-click="changePageType">
        <el-tab-pane label="测评管理" name="testMge"> </el-tab-pane>
        <el-tab-pane label="阅卷任务" name="checkTask"></el-tab-pane>
        <el-tab-pane label="答题卡工具" name="cardTool"></el-tab-pane>
      </el-tabs>
      <!-- 校管 运营查看待处理异常考试 -->
      <el-badge :value="errExamNums" class="error-count">
        <el-button round size="small" @click="goHandleError">待处理</el-button>
      </el-badge>
      <div class="examReport__header clearfix" v-if="pageType == 'testMge'">
        <div class="header__select">
          <!-- <span class="select__label">时间：</span> -->
          <el-select v-model="timeValue" style="width: 80px; margin-right: 15px" @change="changeTimeValue()">
            <el-option v-for="item in timeType" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <!-- 学年 -->
          <el-select v-if="timeValue == 3" v-model="yearValue" class="year-select" @change="changeYear"
            placeholder="请选择" style="width: 150px">
            <el-option v-for="item in years" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
          <!-- 按月 -->
          <el-date-picker v-if="timeValue == 2" popper-class="datePicker__time" style="width: 240px" v-model="timeSlot"
            :clearable="false" type="monthrange" align="right" range-separator="—" start-placeholder="开始月份"
            end-placeholder="结束月份" @change="changeMonth">
          </el-date-picker>
          <!-- 按日 -->
          <el-date-picker v-if="timeValue == 1" popper-class="datePicker__time" style="width: 240px" v-model="timeSlot"
            :clearable="false" type="daterange" align="right" unlink-panels format="yyyy-MM-dd" range-separator="—"
            start-placeholder="开始日期" end-placeholder="结束日期" @change="changeDay">
          </el-date-picker>
        </div>
        <!--年级 (仅校长和运营账号显示年级切换)-->
        <div class="header__select">
          <span class="select__label">年级：</span>
          <el-select v-model="gradeValue" class="grade-select" @change="changeGrade" placeholder="请选择">
            <el-option v-for="item in grdList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </div>
        <!-- 学科 -->
        <div class="header__select">
          <span class="select__label">学科：</span>
          <el-select v-model="subjectValue" class="grade-select" @change="changeSubject" placeholder="请选择">
            <el-option v-for="item in subjectList" v-if="item.show || item.show == undefined" :key="item.id"
              :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </div>
        <!-- 类别 -->
        <div class="header__select">
          <span class="select__label">类别：</span>
          <el-select v-model="typeValue" class="grade-select" @change="changeType" placeholder="请选择">
            <el-option v-for="item in typeList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </div>
        <!--搜索-->
        <div class="header__select">
          <div class="header__serarch clearfix display_flex">
            <el-input class="search__text" placeholder="输入考试名称搜索" v-model="searchValue"
              @keyup.enter.native="searchReport" clearable>
            </el-input>
            <div class="search__icon el-icon-search display_flex align-items_center justify-content_flex-center"
              @click="searchReport"></div>
          </div>
        </div>
        <div class="header-action" v-if="pageType == 'testMge'">
          <!--创建考试-->
          <!-- 运营,校管,年级组长、备课组长有创建考试的权限 -->
          <el-button type="primary" @click="createReport">新建
          </el-button>
          <el-button type="warning" @click="showCopySetDialog">复用
          </el-button>
          <el-button :loading="refreshLoading" @click="searchReport">刷新</el-button>
        </div>
      </div>
      <template v-if="pageType == 'testMge'">
        <div class="examReport__main flex_1 display_flex flex-direction_column" ref="examReport__main">
          <div class="examReport__content flex_1"
            :class="examReportList.length ? '' : 'display_flex align-items_center'" v-loading="listLoading">
            <!--个册列表数据-->
            <ul id="popoverUl" class="examReport__list list-none" v-if="examReportList.length"
              element-loading-text="加载中...">
              <li v-for="(item, index) in examReportList" :key="index">
                <test-item :item="item" :index="index" @open-publish="openPublishDialog" @look-report="lookReport"
                  @download-report="downLoadReport" @handler-command="handlerCommand" @to-upload-word="toUploadWord"
                  @create-paper="createPaper" @process-handle="processHandle" @to-export="toExport"
                  @to-match-ques-num="toMatchQuesNum" @export-grade="exportGrade"
                  @scan-exam="recognitionHandle" @reload-data="getExamReportList"
                  @update-exam-info="refreshStuInfo"></test-item>
              </li>
            </ul>
            <!--没有个册数据缺省图-->
            <div class="nodata flex_1" v-if="!listLoading && !examReportList.length">
              <img :src="noResImg" alt="" />
              <p class="text-center">暂无数据</p>
            </div>
            <!--分页器-->
            <el-pagination background style="margin-bottom: 30px" :hide-on-single-page="!examReportList.length"
              class="text-center" layout="total, prev, pager, next" @current-change="handleCurrentChange"
              :current-page.sync="pagination.page" :page-size="pagination.limit" :total="pagination.total_rows">
            </el-pagination>
          </div>
        </div>
      </template>
      <template v-if="pageType == 'checkTask'">
        <task-list></task-list>
      </template>
      <template v-if="pageType == 'cardTool'">
        <card-main></card-main>
      </template>
    </div>
    <!--添加个册弹框-->
    <crete-report-dialog :editObj="editObj" :dict="dict" @closeDialog="closeDialog" @updateData="getExamReportList"
      v-if="createReportDialogVisible"></crete-report-dialog>
    <!--添加个册弹框-->
    <look-report :reportInfo="reportInfo" @closeDialog="closeDialog" @lookReport1="lookReport1"
      v-if="lookReportDialogVisible"></look-report>
    <!--上传word-->
    <upload-word v-if="uploadWordShow" ref="uploadWord" :formVisible="uploadWordShow" :formDataParam="uploadWordInfo"
      :subjectInfo="subjectInfo" :editParamsData="editWordInfo" @closeDialog="closeUpload"></upload-word>
    <!--上传word-->

    <!--导入成绩-->
    <upload-excel ref="uploadWord" v-if="uploadExcelShow" :formVisible="uploadExcelShow" :excellData="excellData"
      :processState="curProcessState" @closeDialog="closeExcelUpload"></upload-excel>
    <!--导入成绩-->
    <!--下载报告-->
    <download-report ref="downloadreport" v-if="isShowDownReport" @cloase-dialog="closeDownload"></download-report>
    <!--下载报告-->
    <!-- 导入成绩弹框 -->
    <exportXlsx ref="exportXlsx" :examId="examInfo.examId" :year="importGrades.year" :phase="importGrades.phaseId - 2"
      :subjectIds="examInfo.subjectIds" type="single" @closeDialog="dlgExportXlsxVisible = false"
      v-if="dlgExportXlsxVisible"></exportXlsx>
    <!-- 发布成绩弹窗 -->
    <publish-score-dialog v-if="isPublishScoreDialog" :examInfo="examInfo" @close-publish="closePublish"
      @publish-score="publishScore"></publish-score-dialog>
    <!-- 复制考试信息弹窗 -->
    <copy-exam-dialog v-if="isShowCopyDialog" :dialogVisible="isShowCopyDialog" @closeDialog="isShowCopyDialog = false"
      @copyExam="copyExam">
    </copy-exam-dialog>
    <!-- 服务到期提示 -->
    <date-expired-dialog :dialogVisible="isShowExpiredDialog" :tips="tips"></date-expired-dialog>
  </div>
</template>

<script>
import { selectAllType, saveBaseRelation } from '../service/pbook';
import {
  getExamReportList,
  deleteExamInfo,
  getDefaultData,
  verifyWordFile,
  getImportInfo,
  getPublishScoreStateAPI,
  getExamScoreRuleList,
  getErrorExamNums,
  getMergeStatus
} from '../service/pexam';
import creteReportDialog from '@/components/createReport.vue';
import lookReport from '@/components/lookReport.vue';
import UploadExcel from '@/components/uploadExcel.vue';
import TestItem from '@/components/TestItem/index.vue';
import taskList from './examMange/markPaper/taskList.vue';
import cardMain from './examMange/card/index.vue';
import CopyExamDialog from '@/components/copyExamDialog.vue';
import {
  getUserInfoAPI,
  publishScoreAPI,
  updateExamHomeWorkInfo,
} from '../service/api';
import UploadWord from '@/components/uploadWord.vue';
import DownloadReport from '@/components/downloadReport.vue';
import ExportXlsx from '@/components/exportXlsx';
import { uniqueFunc, reviseListMap } from '@/utils/common';
import PublishScoreDialog from '@/components/PublishScoreDialog.vue';
import { getToken } from '@/service/auth';
import { Loading } from '@iclass/element-ui';
import UserRole from '@/utils/UserRole';
import DateExpiredDialog from '@/components/TestItem/dateExpiredDialog.vue';
import moment from "moment";

var now = '';

// 防抖
function _debounce(func, delay) {
  let timer;
  return function () {
    const context = this;
    const args = arguments;
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(context, args);
    }, delay);
  };
}

export default {
  name: 'exam-report',
  data() {
    return {
      reportInfo: '',
      lookReportDialogVisible: false,
      isFirst: false,
      noResImg: require('../assets/no-res.png'),
      //页面类型 testMge:测评管理 checkTask：阅卷任务 cardTool：答题卡工具
      pageType: 'testMge',
      // 学年列表
      years: [],
      yearValue: '',
      //时间筛选类型
      timeType: [
        { value: 1, label: '按日' },
        { value: 2, label: '按月' },
        { value: 3, label: '学年' },
      ],
      timeValue: 2,
      // 学期列表
      terms: [
        { id: '', name: '全部学期' },
        { id: 110, name: '第一学期' },
        { id: 111, name: '第二学期' },
      ],
      timeSlot: [],
      defMonthSlot: [],
      termValue: '',
      // 年级列表
      grdList: [],
      gradeValue: '',

      subjectList: [],
      // 选中的学科索引
      subjectValue: '',
      // 类别列表
      typeList: [],
      // 选中的类别索引
      typeValue: '',
      // 搜索框值
      searchValue: '',
      // 考试报告列表
      examReportList: [],
      // 分页
      pagination: {
        page: 1,
        limit: 10,
        total_rows: 0,
      },
      // 加载中
      listLoading: true,
      // 是否显示创建报告弹窗
      createReportDialogVisible: false,
      // 筛选项
      dict: {
        grdList: [],
        subjectList: [],
        typeList: [],
        years: [],
        terms: [],
      },
      // 编辑的考试报告数据
      editObj: {},
      // 是否显示导入成绩弹窗
      uploadExcelShow: false,
      curProcessState: 0,
      excellData: {},
      isShowDetail: false,
      paperList: [],
      uploadWordInfo: {},
      subjectInfo: {},
      uploadWordShow: false,
      editWordInfo: {},
      isUploadAgain: false,
      fsUrl: process.env.VUE_APP_FS_URL,
      // 是否显示下载报告弹窗
      isShowDownReport: false,
      //导入成绩弹框
      dlgExportXlsxVisible: false,
      importGrades: {},
      examInfo: {},
      //角色列表
      roleList: [],
      roles: '', // 6普通教师 5班主任 4备课组长 1校管,2年级组长 4备课组长
      roleTypes: '',
      //年级信息
      gradeInfo: {},
      //角色下的班级
      roleClassList: [],
      //角色下的学科
      roleSubjectList: [],
      accountType: '',
      //发布成绩确认弹窗
      isPublishScoreDialog: false,
      //刷新按钮加载
      refreshLoading: false,
      cardUrl: process.env.VUE_APP_CARDURL,
      //加载中状态
      scoreLoading: null,
      listTimer: null,
      timerGap: 15000,
      roleMap: null,
      isShowExpiredDialog: false,
      tips: '',
      //未处理异常考试数
      errExamNums: 0,
      errExamNumsTimer: null,
      isShowCopyDialog: false,
    };
  },
  components: {
    creteReportDialog,
    UploadExcel,
    UploadWord,
    DownloadReport,
    ExportXlsx,
    lookReport,
    PublishScoreDialog,
    TestItem,
    taskList,
    cardMain,
    DateExpiredDialog,
    CopyExamDialog,
  },
  watch: {
    $route(to, from) {
      clearTimeout(this.listTimer);
      clearTimeout(this.errExamNumsTimer);
      if (
        from.name == 'examInfo' ||
        from.path.indexOf('home/question') != -1 ||
        from.name == 'teaching'
      ) {
        this.searchReport();
      }
      if (
        from.name == 'answerSetting' ||
        from.name == 'markPaperSetting' ||
        from.path.indexOf('errornew') != -1
      ) {
        this.getExamReportList();
      }
    },
  },
  beforeDestroy() {
    clearTimeout(this.listTimer);
    clearTimeout(this.errExamNumsTimer);
  },
  computed: {
    // 对选择后的时间格式化
    timeSlotFormat() {
      return [
        this.timeSlot[0] ? this.$formatDate(new Date(this.timeSlot[0])) : '',
        this.timeSlot[0] ? this.$formatDate(new Date(this.timeSlot[1])) : '',
      ];
    },
  },
  async mounted() {
    //删除未保存的报告
    this.deleteUnsavedReport();
    if (this.years.length > 0) return;
    await this.initMounted();
    this.changeTimeValue();
  },
  async activated() {
    if (!this.isFirst) {
      return;
    }
    await this.initActivated();
  },
  deactivated() {
    clearTimeout(this.listTimer);
    clearTimeout(this.errExamNumsTimer);
  },
  methods: {
    async initMounted() {
      this.years = this.$sessionSave.get('YEAR_LIST') || [];
      this.years.unshift({ id: '', name: '全部学年' });
      this.yearValue = (this.years[1] || this.years[0]).id;
      const start = moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD');
      const end = moment().add(1, 'months').endOf('month').format('YYYY-MM-DD');
      this.defMonthSlot = [start, end];
      // let md = Number(`${M}${d < 10 ? `0${d}` : d}`);
      // if (md >= 710) {
      //   this.termValue = 110;
      // } else {
      //   if (md <= 115) {
      //     this.termValue = 110;
      //   } else {
      //     this.termValue = 111;
      //   }
      // }
      let loginInfo = this.$sessionSave.get('loginInfo');
      this.accountType = loginInfo.account_type;
      await this.selectAllType();
      this.getUserInfo();
      await this.initSchool();
      clearTimeout(this.listTimer);
      clearTimeout(this.errExamNumsTimer);
      if (this.pageType == 'testMge') {
        await this.getDefaultData();
      }
      this.getErrorExamNums();
      this.handleRouteQueryOperation();
      this.isFirst = true;
    },
    async initActivated() {
      console.log('activated');
      this.initMounted();
      this.getExamReportList();
    },

    // 处理路由参数操作
    async handleRouteQueryOperation() {
      const operation = this.$route.query.operation;
      if (operation) {
        await this.$router.replace({ query: {} });
        if (operation == 'create') {
          this.createReport();
        } else if (operation == 'reuse') {
          this.showCopySetDialog();
        }
      }
    },

    /**
     * 获取未处理异常考试数量
     */
    async getErrorExamNums() {
      let params = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        type: 1, //1-异常考试,0-无异常
        page: 1,
        pageSize: 1,
      };
      //非校领导和运营人员只能查看自己任教班级的异常数据
      if (!UserRole.isSchoolLeader && !UserRole.isOperation) {
        let classList = await UserRole.getClassList({ classType: -1 });
        params.classIdList = classList.map(it => it.classId).join(',');
        let subjectIds = [
          ...new Set(
            UserRole.substituteClassList
              .map(item => {
                return String(item.subjectId);
              })
          ),
        ];
        params.subjectIdList = subjectIds.join(',');
      }
      getErrorExamNums(params)
        .then(res => {
          this.errExamNums = res.data.total;
          if (this.errExamNums != 0) {
            clearTimeout(this.errExamNumsTimer);
            this.errExamNumsTimer = setTimeout(() => {
              this.getErrorExamState(params);
            }, 30000);
          } else {
            this.clearErrTimer();
          }
        })
        .catch(err => {
          this.errExamNums = 0;
        });
    },
    async getErrorExamState(params) {
      const res = await getErrorExamNums(params);
      if (res.code == 1 && res.data.list.length) {
        this.errExamNums = res.data.total;
        if (this.errExamNums != 0) {
          clearTimeout(this.errExamNumsTimer);
          this.errExamNumsTimer = setTimeout(() => {
            this.getErrorExamState(params);
          }, 30000);
        } else {
          this.clearErrTimer();
        }
      }
    },
    clearErrTimer() {
      window.clearTimeout(this.errExamNumsTimer);
      clearTimeout(this.errExamNumsTimer);
      this.errExamNumsTimer = null;
    },
    //切换时间筛选类型，按日默认最近一周，按月默认当前月
    changeTimeValue() {
      let date = new Date(),
        y = date.getFullYear(),
        M = date.getMonth(),
        d = date.getDate();
      if (this.timeValue === 1) {
        this.timeSlot = [new Date(y, M, d - 7), new Date(y, M, d)];
      } else if (this.timeValue === 2) {
        this.timeSlot = this.defMonthSlot;
      }
      this.pagination.page = 1;
      this.getExamReportList();
    },
    // 选择月份筛选后
    changeMonth(val) {
      const start = moment(val[0]).startOf('month').format('YYYY-MM-DD');
      const end = moment(val[1]).endOf('month').format('YYYY-MM-DD');
      this.timeSlot = [start, end];

      this.pagination.page = 1;
      this.getExamReportList();
    },
    //删除未保存的报告
    deleteUnsavedReport() {
      if (this.$sessionSave.get('customReportInfo')) {
        let reportInfo = this.$sessionSave.get('customReportInfo');
        deleteExamInfo({
          examId: reportInfo.examId,
        })
          .then(data => {
            this.$message({
              message: '未保存的报告已删除！',
              type: 'success',
              duration: 2000,
            });
            this.$sessionSave.remove('customReportInfo');
          })
          .catch(err => {
          });
      }
    },

    // 初始化学校后
    async initSchool(cb) {
      if (now) {
        if (new Date().getTime() - now.getTime() < 1500) {
          return;
        }
      } else {
        now = new Date();
      }

      let $this = this;
      if (this.subjectList.length > 0) {
        if (cb) {
          cb.call($this);
        }
        return;
      }

      const ret = await UserRole.getUserInfoPersonalityTest();

      this.subjectList = [{ id: '', name: '全部' }, ...ret.schSubList];
      this.grdList = [{ id: '', name: '全部' }, ...ret.schGrdList];
      this.subjectValue = '';
      this.gradeValue = "";

      if (cb) {
        cb.call($this);
      }
    },

    //1校管 : 全校所有老师创建的考试
    //2年级组长 : 管理年级相关的所有考试
    //4备课组长 : 所任年级本学科相关的所有考试
    //5班主任 : 所任班级相关的所有考试
    //6学科老师 : 所任班级、学科相关的考试
    getRoleSubjectListByRole(role) { },

    getUserInfo() {
      return getUserInfoAPI({
        userId: this.$sessionSave.get('loginInfo').id,
      })
        .then(res => {
          let subClass = res.data.substituteClassList;
          let subjectClz = reviseListMap(subClass, 'subjectName', 'className');
          this.$sessionSave.set('subjectClz', subjectClz);
          let mapSub = reviseListMap(subClass, 'subjectId', 'className');
          this.$sessionSave.set('subjectIdClz', mapSub);
        })
        .catch(err => { });
    },

    // 切换学校
    async changeSchool() {
      this.subjectList = [];
      this.searchValue = '';
      this.gradeValue = '';
      this.subjectValue = '';
      this.typeValue = '';
      await this.initSchool();
      clearTimeout(this.listTimer);
      this.getExamReportList();
    },

    buildExamDict(item) {
      let grdList = [];
      if (item && item.gradeCode) {
        //编辑模式，根据年级id获取对应学段的年级
        let examPhase = this.grdList.find(q => q.id == item.gradeCode).phaseId;
        grdList = this.grdList.filter(q => q.id != '' && q.phaseId == examPhase)
      } else {
        //非编辑模式，获取所有年级
        grdList = this.grdList.filter(q => q.id != '')
      }
      this.dict = {
        grdList: grdList,
        subjectList: this.subjectList.filter(q => q.id != ''),
        typeList: this.typeList.filter(q => q.id != ''),
        years: this.years.filter(q => q.id != ''),
        terms: this.terms.filter(q => q.id != ''),
      };
      this.$sessionSave.set('examDict', this.dict);
    },
    // 创建、编辑报告
    async createReport(item) {
      if (!this.checkExamEnabled()) {
        return;
      }

      this.buildExamDict(item);

      if (item && item.examId) {
        this.cancelPopover();
        // 编辑
        this.$sessionSave.set('examInfo', item);
        // this.createReportDialogVisible = true;
        // this.editObj = item;
      } else {
        // 创建
        this.$sessionSave.set('examInfo', '');
        // this.createReportDialogVisible = true;
        // this.editObj = "";
      }
      this.$router.push({ name: 'examInfo' });
    },

    async copyExam(item) {
      this.isShowCopyDialog = false;
      this.buildExamDict(item);
      item.isCopy = true;
      this.$sessionSave.set('examInfo', item);
      this.$router.push({ name: 'examInfo' });
    },

    async showCopySetDialog() {
      if (!this.checkExamEnabled()) {
        return;
      }
      this.isShowCopyDialog = true;
    },
    // 删除报告
    deleteItem(item) {
      this.$confirm('将删除本次考试的全部相关数据，删除后无法恢复，确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.cancelPopover();
          deleteExamInfo({
            examId: item.examId,
          })
            .then(data => {
              this.$message({
                message: '删除成功！',
                type: 'success',
                duration: 2000,
              });
              clearTimeout(this.listTimer);
              this.getExamReportList();
            })
            .catch(err => {
            });
        })
        .catch(() => {
          this.cancelPopover();
        });
    },
    // 切换年级，联动显示学科
    changeGrade(data) {
      if (data == '') {
        this.subjectList.forEach(v => {
          v.show = true;
        });
      } else {
        let grd = this.grdList.find(q => q.id == data);
        this.subjectList.forEach(v => {
          if (!v.id) return;
          v.show = v.phaseId == grd.phaseId || v.phaseId == grd.phase + 2;
        });
      }
      this.subjectValue = "";
      clearTimeout(this.listTimer);
      this.pagination.page = 1;
      this.getExamReportList();
    },
    /**
     * @name:切换学科
     */
    changeSubject() {
      clearTimeout(this.listTimer);
      this.pagination.page = 1;
      this.getExamReportList();
    },
    /**
     * @name：切换类别
     */
    changeType() {
      clearTimeout(this.listTimer);
      this.pagination.page = 1;
      this.getExamReportList();
    },
    // 获取类别列表
    async selectAllType() {
      const res = await selectAllType();
      this.typeList = res.data;
      this.typeList.unshift({
        id: '',
        name: '全部',
      });
    },
    changeDay() {
      this.pagination.page = 1;
      this.getExamReportList();
    },
    // 切换学年
    changeYear(val) {
      this.$sessionSave.set('lastYear', val);
      clearTimeout(this.listTimer);
      this.pagination.page = 1;
      this.getExamReportList();
    },
    // 切换学期
    changeTerm(val) {
      this.$sessionSave.set('lastTerm', val);
      clearTimeout(this.listTimer);
      this.getExamReportList();
    },
    // 搜索
    searchReport() {
      clearTimeout(this.listTimer);
      this.refreshLoading = true;
      this.pagination.page = 1;
      this.getExamReportList();
    },
    // 获取考试报告列表
    getExamReportList() {
      clearTimeout(this.listTimer);
      this.listLoading = true;
      let params = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        acadYearsId: this.timeValue == 3 ? this.yearValue : '',
        acadTermId: '',
        gradeId: this.gradeValue || '',
        subjectId: this.subjectValue || '',
        categoryId: this.typeValue || '',
        keyWord: this.searchValue.trim(),
        page: this.pagination.page, // 页码
        pageSize: this.pagination.limit,
        userId: (UserRole.isSchoolLeader || UserRole.isOperation) ? '' : this.$sessionSave.get('loginInfo').id,
        orderType: 1, //排序规则 0:按照考试时间 1:按照创建时间
        desc: 1, //desc 排序是否按照降序 0:否 1:是
        startTime: this.timeValue != 3 ? this.timeSlotFormat[0] : '',
        endTime: this.timeValue != 3 ? this.timeSlotFormat[1] : '',
      };
      getExamReportList(params)
        .then(res => {
          let testBankIds = [];
          this.examReportList = res.data.list;
          this.examReportList.forEach(item => {
            let createTime = new Date(item.dateCreated);
            createTime.setMonth(createTime.getMonth() + 2);
            let currentTime = res.responsetime;
            if (currentTime < createTime.getTime()) {
              item.isHistory = false;
            } else {
              item.isHistory = true;
            }
          });
          this.pagination.total_rows = res.data.total;
          this.listLoading = false;
          this.refreshLoading = false;
          clearTimeout(this.listTimer);
          this.listTimer = setTimeout(() => {
            this.getPaperState(params);
          }, this.timerGap);

        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    /**
     * @name:获取试卷转换状态
     */
    async getPaperState(params) {
      const res = await getExamReportList(params);
      if (res.code == 1 && res.data.list.length) {
        let testBankIds = [];
        res.data.list.forEach(item => {
          this.examReportList.forEach(ite => {
            if (item.examId == ite.examId) {
              item.paperList.forEach((paper, index) => {
                paper.isUse = ite.paperList[index].isUse;
                paper.state = ite.paperList[index].state;
                paper.phase = ite.paperList[index].phase;
                paper.qc = ite.paperList[index].qc;
              });
              ite.paperList = item.paperList;
              this.$set(ite, 'dataState', item.dataState);
            }
          });
        });
        clearTimeout(this.listTimer);
        this.listTimer = setTimeout(() => {
          this.getPaperState(params);
        }, this.timerGap);
      }
    },
    /**
     * @name：清除定时器
     */
    clearTimer() {
      window.clearTimeout(this.listTimer);
      clearTimeout(this.listTimer);
      this.listTimer = null;
    },
    // 切换筛选项
    changeFilter(type, v) {
      switch (type) {
        case 'subject':
          this.subjectValue = v;
          break;
        default:
          this.typeValue = v;
          break;
      }
      clearTimeout(this.listTimer);
      this.getExamReportList();
    },
    // 分页查询
    handleCurrentChange(val) {
      this.$refs.examReport__main.scrollTop = 0;
      this.pagination.page = val;
      clearTimeout(this.listTimer);
      this.getExamReportList('changePage');
    },
    closeDialog() {
      this.createReportDialogVisible = false;
      this.lookReportDialogVisible = false;
    },
    // 导入成绩弹框
    exportGrade(item) {
      this.examInfo.examId = item.examId;
      this.examInfo.subjectIds = item.subjectId.split(',');
      getImportInfo({ examId: item.examId })
        .then(data => {
          let importGrades = this.grdList.find(q => q.id == data.data.gradeId);
          this.importGrades = {
            year: data.data.year,
            phaseId: importGrades.phaseId
          }
          this.dlgExportXlsxVisible = true;
        })
        .catch(err => {
        });
    },
    /**
     * @name：更多操作
     */
    handlerCommand(command) {
      let commands = command.split('_');
      this[commands[0]](this.examReportList[commands[1]]);
    },
    //同步数据
    syncData(item) {
      window.open(`/bigdata/importExcel?examId=${item.examId}`, '_blank');
    },
    // 关闭导入成绩弹窗
    closeExcelUpload(success) {
      this.uploadExcelShow = false;
      if (success) {
        clearTimeout(this.listTimer);
        this.getExamReportList(true);
      }
    },
    // 查看报告
    async lookReport(item) {
      let params = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        parentId: item.examId,
        acadYearsId: '',
        acadTermId: '',
        gradeId: '',
        subjectId: '',
        categoryId: '',
        keyWord: '',
        page: 1, // 页码
        pageSize: 10,
      };
      const res = await getExamReportList(params);

      if (res.code == 1) {
        if (res.data.list.length == 0) {
          //无新增的报告
          this.$sessionSave.set('reportParent', item);
          this.lookReport1(item);
        } else {
          this.lookReportDialogVisible = true;
          this.reportInfo = item;
        }
      } else {
        this.$message({
          message: '查看报告失败！',
          type: 'error',
          duration: 1000,
        });
      }
    },
    lookReport1(item) {
      this.lookReportDialogVisible = false;
      this.$sessionSave.set('innerClassList', null);
      this.$sessionSave.set('innerSubjectList', null);
      this.$sessionSave.set('loadComment', true);
      this.$sessionSave.set('reportDetail', item);
      this.$sessionSave.remove('subjectId');
      let dt = new Date(item.dateModified).getTime();
      this.$sessionSave.set('downLoadState', item.statState);
      this.$sessionSave.remove('contrastObj');
      this.$sessionSave.set('lookReportFrom', null);
      this.$router.push({ path: '/home/<USER>' });
    },
    /**
     * @name:打开发布成绩弹窗
     */
    openPublishDialog(item, index = -1, subjectItem) {
      // let workId = this.getHwIdByIndex(item, index);
      // if (index == -1) {
      //   examId = item.examId;
      // } else {
      //   examId = this.getHwIdByIndex(item, index);
      // }
      this.examInfo.examId = item.examId;
      this.examInfo.workId = subjectItem.workId;
      this.examInfo.examName = item.examName;
      this.examInfo.paperInfo = subjectItem;
      this.examInfo.schoolId = this.$sessionSave.get('schoolInfo').id;
      this.isPublishScoreDialog = true;
    },
    /**
     * @name:发布成绩
     */
    publishScore() {
      this.isPublishScoreDialog = false;
      publishScoreAPI({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        workId: this.examInfo.paperInfo.personBookId,
      })
        .then(res => {
          this.scoreLoading = Loading.service({
            lock: true,
            text: '正在生成分析报告，请稍候，预计5-20s，若长时间未生成，请刷新页面重试',
            background: 'rgba(0, 0, 0, 0.7)',
          });
          this.checkingScoreStatus();
        })
        .catch(err => {
        });
    },
    checkingScoreStatus() {
      setTimeout(async () => {
        let params = {
          id: this.examInfo.paperInfo.personBookId,
        };
        const res = await getPublishScoreStateAPI(params).catch(err => {
          this.scoreLoading.close();
        });
        if (res.code != 1) {
          this.$message({
            message: res.msg,
            type: 'error',
            duration: 1000,
          });
          this.scoreLoading.close();
          return;
        }
        if (res.data.dataState >= 1) {
          this.scoreLoading.close();
          this.$message({
            message: '发布成功！',
            type: 'success',
            duration: 1000,
          });
          this.getExamReportList();
        } else {
          this.checkingScoreStatus();
        }
      }, 5000);
    },
    /**
     * @name:关闭发布成绩弹窗
     */
    closePublish() {
      this.isPublishScoreDialog = false;
    },
    //新增报告
    addReport(item) {
      this.$sessionSave.remove('customReportInfo');
      this.$sessionSave.set('innerClassList', null);
      this.$sessionSave.set('innerSubjectList', null);
      this.$sessionSave.set('reportDetail', item);
      this.$sessionSave.remove('subjectId');
      this.$router.push({ path: '/home/<USER>' }).catch(err => {
        console.error(err);
      });
    },
    /**
     * @name:进入设置考生答案区域
     */
    setStuAnsArea(item) {
      this.$router.push({
        path: 'settingStuAnsArea',
        query: {
          examId: item.examId,
          title: item.examName,
        },
      });
    },
    /**
     * @name:考生管理
     */
    manageStudent(item) {
      this.dict = {
        grdList: this.grdList.filter(q => q.id != ''),
        subjectList: this.subjectList.filter(q => q.id != ''),
        typeList: this.typeList.filter(q => q.id != ''),
        years: this.years.filter(q => q.id != ''),
        terms: this.terms.filter(q => q.id != ''),
      };
      this.$sessionSave.set('examDict', this.dict);
      this.$router.push({
        path: '/home/<USER>',
        query: {
          examId: item.examId,
          examName: item.examName,
          gradeId: item.gradeCode,
          source: item.source,
          year: item.year
        },
      });
    },

    // 进入成绩修改
    goScoreSetting(item) {
      this.$sessionSave.set('reportDetail', item);
      this.$router.push({
        path: '/home/<USER>',
        query: {
          examId: item.examId,
          examName: item.examName,
        },
      });
    },

    // 进入导出原卷
    goPaperExport(item) {
      this.$sessionSave.set('reportDetail', item);
      this.$router.push('/home/<USER>');
    },

    // 进入成绩发布
    goPublishSetting(item) {
      this.$sessionSave.set('reportDetail', item);
      this.$router.push({
        path: '/home/<USER>',
      });
    },

    // 进入成绩补录
    goScoreEntry(item) {
      let dict = {
        grdList: this.grdList.filter(q => q.id != ''),
        subjectList: this.subjectList.filter(q => q.id != ''),
        typeList: this.typeList.filter(q => q.id != ''),
        years: this.years.filter(q => q.id != ''),
        terms: this.terms.filter(q => q.id != ''),
      };
      this.$sessionSave.set('examDict', dict);
      this.$sessionSave.set('reportDetail', item);
      this.$router.push({
        path: '/home/<USER>',
      });
    },

    //识别处理
    recognitionHandle(item, index = -1, subjectItem) {
      //ab卷取主卡数据
      this.$router.push({
        path: '/scan/errornew',
        query: {
          examId: subjectItem.mainWorkId || subjectItem.workId,
          examName: item.examName,
          subjectName: subjectItem.subectName,
          paperNo: subjectItem.mainPaperNo || subjectItem.paperNo,
          schoolId: this.$sessionSave.get('schoolInfo').id,
          personBookId: subjectItem.mainWorkId || subjectItem.workId,
        },
      });
    },
    editPaper(item) {
      let token = getToken();
      let routeData =
        this.cardUrl + `?id=${item.yqExamId}&examName=${item.examName}&token=${token}`;
      window.open(routeData, '_blank');
    },
    // 下载报告
    downLoadReport(item) {
      this.$sessionSave.set('reportDetail', item);
      this.$sessionSave.remove('subjectId');
      this.isShowDownReport = true;
    },
    /**
     * @name:查看成绩确认详情
     */
    scoreConfirmDetail(item) {
      this.$sessionSave.set('reportDetail', item);
      this.$sessionSave.remove('subjectId');
      this.$sessionSave.set('downLoadState', item.statState);
      this.$router.push({ path: '/home/<USER>' });
    },
    /**
     * @name:关闭下载报告弹窗
     */
    closeDownload() {
      this.isShowDownReport = false;
    },
    goMissExamList(item) {
      this.$router.push({
        path: '/home/<USER>',
        query: {
          examId: item.examId,
          subjectIds: item.subjectId,
          subjectNames: item.subjectName,
        },
      });
    },
    // 更新列表map信息
    updateListmap() {
      this.gradeMap = new Map(this.gradeMap);
    },
    cancelPopover() {
      document.getElementById('popoverUl').click();
    },
    // 获取默认值
    getDefaultData() {
      return getDefaultData().then(data => {
        this.$sessionSave.set('defaultList', data.data);
      });
    },
    // 去制卷
    createPaper(ite, item) {
      let loginInfo = this.$sessionSave.get('loginInfo');
      if (ite.state !== 2) {
        this.$message({
          message: '请刷新页面！',
          type: 'warning',
        });
        return;
      }
      if (ite.state == 2 && ite.isUse) {
        this.$emit('updateTab', -1);
        this.$router.push({
          path: '/home/<USER>',
          query: {
            testBankId: ite.testBankId,
            testBankName: item.examName,
            subjectId: ite.subectId,
          },
        });
        return;
      }

      let routeData = `${process.env.VUE_APP_CUTPAPERURL}cutTestPaper/matchQues.html?id=${ite.testBankId}&userid=${loginInfo.id}&url=${process.env.VUE_APP_CUT_URL}&subject=${ite.subectId}&phase=${ite.phase}&token=${loginInfo.token}`;
      window.open(routeData, '_blank');
      this.$confirm('是否制卷完成，刷新数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          clearTimeout(this.listTimer);
          this.getExamReportList();
        })
        .catch(err => {
          console.log(err);
        });
    },
    //上传试卷
    toUploadWord(ite, item, isUploadAgain) {
      item.phase = this.setPhase(item.gradeName);
      this.isUploadAgain = isUploadAgain;
      this.uploadWordInfo = item;
      this.subjectInfo = ite;
      this.uploadWordShow = true;
    },
    //单科导入
    toExport(ite, item) {
      window.open(
        `/bigdata/importExcel?examId=${item.examId}&subjectId=${ite.subectId}&qc=${ite.qc ?? ''}`,
        '_blank'
      );
    },
    //跳转到题号匹配
    toMatchQuesNum(ite, item) {
      this.$router.push({
        path: '/home/<USER>',
        query: {
          personBookId: ite.personBookId,
          examName: item.examName,
          examId: item.examId,
          subjectId: ite.subectId,
          bindQueState: ite.bindQueState,
        },
      });
    },
    //计算学段
    setPhase(data) {
      let number = '';
      if (data.includes('高')) {
        number = '3';
      } else if (data.includes('七') || data.includes('八') || data.includes('九')) {
        number = '2';
      } else {
        number = '1';
      }
      return number;
    },
    // 点击发送加工或者显示加工进度
    processHandle(ite, item) {
      if (ite.isExamUse == 1) {
        this.$confirm('未关联个册，发送加工后将不能关联个册，是否发送加工?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.sendProcess(ite);
          })
          .catch(() => { });
      } else {
        this.$confirm('已关联个册，是否直接发送加工?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.sendProcess(ite);
          })
          .catch(() => { });
      }
    },
    // 发送加工
    sendProcess(ite) {
      clearTimeout(this.listTimer);
      saveBaseRelation({
        personBookId: ite.personBookId,
        testBankId: ite.testBankId,
        schoolId: this.$sessionSave.get('schoolInfo').id,
      })
        .then(data => {
          this.$message({
            message: '发送成功！',
            type: 'success',
            duration: 2000,
          });
          this.getExamReportList();
        })
        .catch(err => {
        });
    },
    // 关闭上传word弹窗
    closeUpload() {
      this.uploadWordShow = false;
      clearTimeout(this.listTimer);
      this.getExamReportList();
    },
    verifyWordFile(ite) {
      verifyWordFile({
        subjectId: ite.subjectId,
        examId: ite.examId,
      })
        .then(data => {
          console.log('0000', data.data);
        })
        .catch(err => {
          console.log(err);
        });
    },
    /**
     * @name: 改变页面类型
     */
    changePageType() {
      if (this.pageType == 'testMge' && this.listLoading) {
        clearTimeout(this.listTimer);
        this.getExamReportList();
      }
    },
    /**
     * @name：根据学科序号获取作业id
     */
    getHwIdByIndex(item, index) {
      let ids = item.yqExamId;
      let idArr = [];
      let workId = '';
      if (ids.indexOf(',') > -1) {
        idArr = ids.split(',');
        if (idArr.length > index) {
          workId = idArr[index];
        }
      } else {
        workId = item.yqExamId;
      }
      return workId;
    },
    /**
     * @name：根据学科序号获取作业id
     */
    getPaperNoByIndex(item, index) {
      // let ids = item.paperNo;
      // let idArr = [];
      // let examId = "";
      // if (ids.indexOf(",") > -1) {
      //   idArr = ids.split(",");
      //   if (idArr.length > index) {
      //     examId = idArr[index];
      //   }
      // } else {
      //   examId = item.paperNo;
      // }
      return item.paperList[index].paperNo;
    },
    /**
     * @name: 根据下标查找学科信息
     */
    getSubjectInfoByIndex(item, index) {
      let paperInfo = {};
      if (item && item.paperList.length > index) {
        paperInfo = item.paperList[index];
      }
      return paperInfo;
    },
    /**
     * @name:更新考试作业师生关系信息
     */
    async refreshStuInfo(item) {
      this.$confirm(
        `更新后，当前考试参考班级的考生信息会被后台最新的考生信息所覆盖，是否继续？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        let res = await updateExamHomeWorkInfo({ examId: item.examId });
        if (res.code == 1) {
          this.$message({
            message: '更新成功！',
            type: 'success',
            duration: 1000,
          });
        } else {
          this.$message({
            message: '更新失败',
            type: 'error',
            duration: 1000,
          });
        }
      });
    },
    /**
     * @name:跳转到异常处理
     */
    goHandleError() {
      this.$router.push({
        name: 'errExamList',
      });
    },

    // 检查考试是否可用
    checkExamEnabled() {
      let vipInfo = this.$sessionSave.get('vipInfo');
      if (!vipInfo.isVip) {
        this.tips = '非常抱歉，贵校服务已到期，请与商务联系进行延期。';
        this.isShowExpiredDialog = true;
        setTimeout(() => {
          this.isShowExpiredDialog = false;
        }, 1500);
        return false;
      }
      if (!this.$store.getters.examEnabled) {
        this.tips = '非常抱歉，贵校考试次数已达上限。';
        this.isShowExpiredDialog = true;
        setTimeout(() => {
          this.isShowExpiredDialog = false;
        }, 1500);
        return false;
      }
      return true;
    }
  },
};
</script>

<style lang="scss" scoped>
.examReport {
  // padding-right: 50px;
  overflow-y: auto;
  height: 100%;
  background: #fff;
  border-radius: 6px;

  .error-count {
    position: absolute;
    right: 30px;
    top: 12px;
  }

  .examReport__header {
    position: relative;
    padding: 15px 20px;
    // background: #fff;
    font-size: 14px;
    // border: 1px solid #e4e7ed;
    border-top: unset;

    .header__select {
      display: inline-block;
      margin-right: 10px;
      float: left;

      .role-select,
      .year-select,
      .term-select,
      .grade-select {
        width: 110px;
      }
    }

    .header-action {
      display: inline-block;
      position: absolute;
      right: 20px;
    }

    .header_role {
      position: absolute;
      top: -41px;
      left: 24%;
    }

    .header__serarch {
      display: flex;
      width: 240px;

      .search__icon {
        width: 38px;
        font-size: 18px;
        color: #fff;
        background: #409eff;
        border-radius: 0 3px 3px 0;
        outline: none;
        cursor: pointer;
      }

      .search__text {}
    }
  }

  .examReport__main {
    width: 100%;
    padding: 0 15px;
    margin-top: 14px;
    font-size: 16px;
    overflow: auto;

    .header__class {
      margin: 10px 0;

      .class__li {
        display: inline-block;
        margin-right: 15px;
        padding: 0 8px;
        border-radius: 3px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;

        &.active,
        &:hover {
          color: #409eff;
        }
      }

      .leftText {
        width: auto;
      }

      .rightText {
        width: auto;
      }

      &.category {
        border-bottom: 1px solid #e4e8eb;
        margin-bottom: 20px;
        padding-bottom: 8px;
      }
    }

    .examReport__content {
      width: 100%;

      .examReport__list {
        width: 100%;
      }

      .uploadContent {
        width: 97%;
        box-sizing: border-box;
        background: #f7fafc;
        padding: 16px 43px 16px 26px;
        border: 1px solid #e4e7eb;
        border-top: unset;
        border-radius: 6px;
        color: #222;
        font-size: 16px;
        margin: -30px 10px 20px 20px;
      }

      .uploadSubject {
        margin: 10px 0px 10px 0px;
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 30px;
        font-weight: bold;
        color: #3f4a54;
      }

      .uploadingState {
        display: inline-block;
        width: 70px;
        cursor: pointer;
        font-weight: 400;
      }

      .sendProcess {
        margin-left: 30px;
        color: #b8b8b8;
        pointer-events: none;
        cursor: pointer;
        font-weight: 400;
      }

      .sendProcess.allowClick {
        margin-left: 30px;
        pointer-events: auto;
        cursor: pointer;
      }

      .uploadAgain {
        color: #409eff;
        pointer-events: auto;
        cursor: pointer;
        font-weight: 400;
      }

      .uploadAgain.banClick {
        color: #b8b8b8;
        pointer-events: none;
        cursor: pointer;
      }

      .nodata {
        width: 100%;
        height: auto;

        img {
          display: block;
          margin: 0 auto;
        }
      }
    }
  }
}

::-webkit-scrollbar {
  display: none;
}
</style>
<style lang="scss">
.el-divider--vertical {
  display: inline-block;
  width: 1px;
  height: 100%; //更改竖向分割线长度
  margin: 0 8px;
  vertical-align: middle;
  position: absolute;
  top: 0;
}

.moreEdit {
  width: 63px;
  display: inline-block;
  height: 30px;
  margin-top: 13px;
  cursor: pointer;
  color: #008dea;
  font-size: 18px;
  background: url('../assets/edit.png') center center no-repeat;
}

.more,
.editPopper {
  width: 100px !important;
  min-width: 100px !important;
  // height: 100px;
  padding: 10px 0;

  ul {
    li {
      width: 100px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      cursor: pointer;

      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}

.examReport__item {
  .el-icon-more {
    width: 40px;
    text-align: center;
    transform: rotate(90deg);
  }
}

.examReport {
  .examReport__tabs {
    .el-tabs__header {
      background: #E9F4FF;
      border: unset;
    }

    >.el-tabs__header .el-tabs__nav,
    .el-tabs__item {
      border: unset;
      border-left: unset !important;
      height: 50px;
      line-height: 50px;
    }
  }

  .el-tabs__header {
    margin: unset !important;
  }

  .el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
    background: #fff;
    font-weight: bold;
  }

  .el-tabs__item {
    font-size: 18px !important;
  }

  .search__text {
    .el-input__inner {
      border-right: none;
      border-radius: 3px 0 0 3px;

      &:focus {
        border-color: #409EFF !important;
      }
    }
  }
}
</style>

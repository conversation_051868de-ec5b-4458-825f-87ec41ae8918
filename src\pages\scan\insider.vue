<template>
  <div class="paper-container">
    <el-page-header @back="backReport" :content="headContent"> </el-page-header>
    <div class="error-container">
      <div class="error-left">
        <span class="setting-scan" @click="dialogVisible=true"><i class="el-icon-setting"></i>识别概率设置</span>
        <el-tabs v-model="activeName" @tab-click="tabClick" class="scan-student-error-tabs" type="card">
          <el-tab-pane v-for="(item, index) in errorType" :key="index" :label="item.text" :name="item.type">
          </el-tab-pane>
        </el-tabs>
        <ques-error v-loading="loading" :isInner="true" :errorData="quesErrorData" :pointsData="pointsData" :examInfo="examInfo"
          :choiceScoreMap="choiceScoreMap" @update-error="getAllQuestions" ref="quesError"></ques-error>
      </div>
      <!-- 右侧操作区 -->
      <div class="error-right">
        <silder-set ref="silderSet" @change-type="changeHandleType" @change-page="changePage"
          :handleInfo="allErrorNums[activeName]" :pagination="pagination" :errorType="activeName"
          :examInfo="examInfo" :pageNum="pointsData.page_num"></silder-set>
      </div>
    </div>
    <el-dialog title="识别概率设置" :visible.sync="dialogVisible" :close-on-click-modal="false" width="30%">
      <span>注：识别概率越低，结果越容易出错</span>
      <el-slider v-model="scanRate" :marks="marks" range :max="1" :min="0" :step="0.1">
      </el-slider>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="setScanRate">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import SilderSet from '@/components/scan/SilderSet.vue';
import QuesError from '@/components/scan/QuesError.vue';
import { getAllQuestions, getAllQuestionsImg } from '@/service/pexam';
import { getScanPaperPoints } from '@/service/testbank';
import { GetScoreMap } from '@/service/xueban';
import { replaceALiUrl } from '@/utils/common';

export default {
  components: {
    SilderSet,
    QuesError,
  },
  data() {
    return {
      errorType: [
        {
          text: '客观题异常',
          type: 'objective',
        },
        {
          text: '主观题异常',
          type: 'subject',
        },
      ],
      quesErrorData: [],
      activeName: 'objective',
      allErrorNums: {},
      //分页
      pagination: {
        page: 1,
        limit: 40,
        total_rows: 0,
        page_count: 0,
      },
      // 坐标点数据
      pointsData: {},
      requestInfo: {
        status: 0, //处理状态 0 待处理，1 已处理
        quesNo: 0, // 题号
        examNoId: '', //考号Id
        idx: '', //页数
        stuNo: '', //学号
        taskId: '', //批次id
        scanRate: [0,1]
      },
      //考试信息
      examInfo: {
        examId: this.$route.query.examId,
        examName: this.$route.query.examName,
        paperNo: this.$route.query.paperNo,
        subjectName: this.$route.query.subjectName || '',
      },
      fsUrl: process.env.VUE_APP_FS_URL,
      //图片分辨率
      imgDpi: 4.173,
      loading: false,
      //多选题得分规则
      choiceScoreMap: {},
      dialogVisible:false,
      scanRate:[0,1],
      marks:{
          0: '0',
          0.1: '0.1',
          0.2: '0.2',
          0.3: '0.3',
          0.4: '0.4',
          0.5: '0.5',
          0.6: '0.6',
          0.7: '0.7',
          0.8: '0.8',
          0.9: '0.9',
          1: '1',
        }
    };
  },
  computed: {
    headContent() {
      if (this.examInfo.subjectName != '') {
        return `${this.examInfo.examName} （${this.examInfo.subjectName}）`;
      } else {
        return this.examInfo.examName;
      }
    },
  },
  mounted() {
    this.getScanPaperPoints();
    this.getAllQuestions();
    this.getScoreMap();
  },
  methods: {
    setScanRate(){
      this.requestInfo.scanRate = this.scanRate;
      this.dialogVisible = false;
      this.getQuesError();
    },
    /**
     * @name:获取多选题得分规则
     */
    async getScoreMap() {
      GetScoreMap({ paper_no: this.examInfo.paperNo })
        .then(res => {
          this.choiceScoreMap = res.data;
        })
        .catch(err => {
          console.log(err);
        });
    },
    /**
     * @name:获取题目坐标
     */
    getScanPaperPoints() {
      getScanPaperPoints({
        paperNo: this.examInfo.paperNo,
      })
        .then(res => {
          this.pointsData = JSON.parse(res.data);
        })
        .catch(err => {});
    },
    tabClick() {
      this.requestInfo.status = 0;
      this.$refs.silderSet.initData();
      this.requestInfo.quesNo =
        (this.allErrorNums[this.activeName].wait.length &&
          this.allErrorNums[this.activeName].wait[0].quesNo) ||
        0;
      this.getQuesError();
    },
    async getAllQuestions() {
      let params = {
        examId: this.$route.query.examId,
      };
      const res = await getAllQuestions(params);
      if (res.code == 1) {
        this.allErrorNums = res.data;
        this.loading = true;
        this.handelQuesError();
      } else {
        this.allErrorNums = {};
      }
    },
    /**
     * @name:处理题目异常数据
     */
    handelQuesError() {
      //题目异常获取第一题考号
      this.requestInfo.quesNo =
        this.allErrorNums[this.activeName].wait.length &&
        this.allErrorNums[this.activeName].wait[0].quesNo;
      this.getQuesError();
    },
    /**
     * @name:获取处理状态的列表
     */
    changeHandleType(info) {
      this.pagination.page = 1;
      this.requestInfo = {...this.requestInfo,...info};
      this.requestInfo.examNoId = info.examNoId;
      this.loading = true;
      this.getQuesError();
      this.$refs.quesError.scrollTotop();
    },
    /**
     * @name:分页
     */
    changePage(page) {
      this.pagination.page = page;
      this.getQuesError();
      this.$refs.quesError.scrollTotop();
    },
    /**
     * @name:获取题目异常数据
     */
    getQuesError() {
      let params = {
        schoolId: this.$route.query.schoolId,
        examId: this.examInfo.examId,
        quesNo: this.requestInfo.quesNo,
        status: this.requestInfo.status,
        page: this.pagination.page,
        size: this.pagination.limit,
      }
      if(this.activeName == 'subject'){
        params.min = this.requestInfo.scanRate[0];
        params.max = this.requestInfo.scanRate[1]
      }
      getAllQuestionsImg(params)
        .then(res => {
          this.quesErrorData = res.data.rows;
          this.pagination.page_count = res.data.page_count;
          this.getErrorImg();
          setTimeout(() => {
            this.loading = false;
          }, 500);
        })
        .catch(err => {
          this.loading = false;

          this.quesErrorData = [];
        });
    },
    /**
     * @name:获取异常图片
     */
    getErrorImg() {
      this.quesErrorData.forEach(item => {
        //需要截取的x,y,w,h
        let cutStartX = '',
          cutStartY = '',
          cutHeight = '',
          topY = '';
        if (!item.is_obj) {
          //是否手写打分
          let isWrite = item.type == 13;
          let isOnline = item.type == 15;
          if (isWrite) {
            //构造打分栏数据
            const num = 16;
            let scoresList = Array.from({ length: item.total_score }, (_, index) => index);
            scoresList.push(item.total_score);
            if (item.has_point) {
              scoresList.unshift(-1)
            }
            let rows = Math.ceil(scoresList.length / num);
            let scores = new Array(num * rows).fill(-1);
            scoresList.forEach((s, i) => {
              scores[i] = s;
            })

            let _score = parseInt(item.score);
            let score_list = [{
              cols: num,
              detect_score: _score,
              pos: [item.pos[0], item.pos[1] - rows * 6, item.pos[2], rows * 6],
              rows: rows,
              score: _score,
              scores: scores.reverse()
            }]
            if (item.has_point) {
              let _score = item.score != parseInt(item.score) ? 0.5 : 0;
              score_list.push({
                cols: 1,
                detect_score: _score,
                pos: [item.pos[0] + item.pos[2] - item.pos[2] / num, item.pos[1] - 6, item.pos[2] / num, 6],
                rows: 1,
                score: _score,
                scores: [0.5]
              })
            }
            item.score_list = score_list;
          }
          if(isOnline){
            item.score_list.forEach(ite => {
              let totalScore = ite.total_score || item.total_score;
              let scores = [];
              scores.push(totalScore);
              scores.push(totalScore / 2);
              scores.push(0);
              ite.scores = scores;
              ite.cols = 1;
              ite.rows = 1;
            })
          }
          item.score_list.forEach(ite => {
            let missScore = item.miss_score;
            if(isOnline){
              //针对线上批改半对分值为0导致匹配框选错误的问题 强制半对分值为总分一半
              missScore = ite.total_score / 2;
            }
            let scores = [];
            ite.scores.forEach(it => {
              let isChoice =
                item.code == 3 && item.status == 0 && !isOnline
                  ? false
                  : ite.score == it && missScore != item.score
                  ? true
                  : false;
              // let isChoice = ite.score == it && item.status == 1 ? true : false;
              scores.push({ value: it, isChoice: isChoice,totalScore : ite.total_score  });
            });
            //每一行的高度
            let rowHeight = ite.pos[3] / ite.rows;
            const newArray = [];
            for (let i = 0; i < scores.length; i += ite.cols) {
              const subArray = scores.slice(i, i + ite.cols);
              newArray.push(subArray);
            }
            let array1 = [];
            newArray.forEach((everyRow, index) => {
              let tempObj = {};
              tempObj.pos = [ite.pos[0], ite.pos[1] + index * rowHeight, ite.pos[2], rowHeight];
              tempObj.new_score_list = [...everyRow];
              tempObj.cols = ite.cols;
              array1.push(tempObj);
            });
            this.$set(ite, 'newArray', array1);
            this.$set(ite, 'new_score_list', scores);
            //主观题截取作答图片 延展
            cutStartX = (item.score_list[0].pos[0] * this.imgDpi).toFixed();
            cutStartY = ((item.score_list[0].pos[1] - 5) * this.imgDpi).toFixed();
            if(isWrite){
              //手写框构造的打分栏高度根据行数扩大
              cutHeight = ((item.score_list[0].pos[3] + 10 + item.score_list[0].rows * 6) * this.imgDpi).toFixed();
            }else if(isOnline){
              cutStartY = ((item.pos[1] - 3) * this.imgDpi).toFixed();
              cutHeight = ((item.pos[3] + 4) * this.imgDpi).toFixed();
            }else{
              cutHeight = ((item.score_list[0].pos[3] + 10) * this.imgDpi).toFixed();
            }
            topY = (item.score_list[0].pos[1] - 5) * this.imgDpi;
          });
        } else {
          let offsetY = 0;
          if(item.type == 16){
            //客观题手写增加高度偏移  扩大显示
            offsetY = 2;
            //客观题手写作答
            let x = item.list[0].pos[0] + item.list[0].pos[2];
            let y = item.list[0].pos[1] + offsetY;
            let paddingX = 2;
            let w = 8;
            let h = 5;
            item.list.forEach((ite,index) => {
              ite.pos = [x+(index*w)+paddingX*index,y,w,h];
           })
          }
          cutStartX = (item.list[0].pos[0] * this.imgDpi).toFixed();
          cutStartY = ((item.list[0].pos[1] - offsetY) * this.imgDpi).toFixed();
          cutHeight = (
            (item.list[item.list.length - 1].pos[1] +
              item.list[item.list.length - 1].pos[3] -
              item.list[0].pos[1] + offsetY * 2) *
            this.imgDpi
          ).toFixed();
          topY = item.list[0].pos[1] * this.imgDpi;
        }
        // 题目坐标
        item.pos = item.pos.map(ite => {
          return (ite * this.imgDpi).toFixed();
        });
        let tempPos = [item.pos[0], cutStartY, item.pos[2], cutHeight];
        //截取选项或打分框图片
        let img = this.getSplitImg(item.image, tempPos);
        //整题图片
        let originImgs = [];
        let primitiveImg = this.getSplitImg(item.image, item.pos);
        // originImgs.push(this.getSplitImg(item.image, item.pos));
        //题目跨页或跨面
        item.data.length != 0 &&
          item.data.forEach(ite => {
            ite.pos = ite.pos.map(it => {
              return (it * this.imgDpi).toFixed();
            });
            originImgs.push(this.getSplitImg(ite.image, ite.pos));
          });
        //题目存在考号异常，data里面的图片为空，取题目上的坐标截取
        if (originImgs.length == 0) {
          originImgs.push(replaceALiUrl(primitiveImg));
        }
        this.$set(item, 'newImg', replaceALiUrl(img));
        this.$set(item, 'originImgs', originImgs);
        this.$set(item, 'isShowOrigin', false);
        this.$set(item, 'topY', topY);
        this.$set(item, 'list', item.is_obj ? item.list : item.score_list);
        this.$set(item, 'tempScore', item.score);
      });
      // console.log("this.quesErrorData", this.quesErrorData);
    },
    /**
     * @name:获取题目作答图片
     */
    getSplitImg(img, pos) {
      return (
        `${img}` +
        '?x-oss-process=image/resize,h_1238,' +
        `image/crop,x_${pos[0]},y_${pos[1]},w_${pos[2]},h_${pos[3]},image/format,jpeg`
      );
    },
    /***
     * @name:返回上一页
     */
    backReport() {
      this.$router.back();
    },
  },
};
</script>

<style lang="scss" scoped>
.paper-container {
  position: relative;
  height: calc(100% - 35px);
  padding: 20px 0 40px 20px !important;
  .header-back {
    // left: 30px;
    top: 20px;
    position: absolute;
    padding: 12px 20px;
    font-size: 16px;
    color: #3a5eff;
    cursor: pointer;
  }
}
.error-container {
  display: flex;
  height: 100%;
}
.error-left {
  position: relative;
  width: 70%;
  .setting-scan{
    position: absolute;
    top: 20px;
    right: 0;
    cursor: pointer;
    z-index: 9;
    &:hover{
      color: #409EFF;
    }
  }
}
.error-right {
  width: 30%;
  margin-top: 50px;
}
</style>
<style lang="scss">
.scan-student-error-tabs {
  position: relative;
  height: calc(100% - 50px);

  .el-tabs__content {
    height: 98%;

    .el-tab-pane {
      height: 100%;
    }
  }

  .el-tabs__header {
    margin-top: 10px;
    // margin-left: 80px;
  }
}
</style>
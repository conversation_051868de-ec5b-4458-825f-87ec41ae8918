@if $use-slideInRight == true {

	@-webkit-keyframes slideInRight {
		0% {
			-webkit-transform: translate3d(100%, 0, 0);
			visibility: visible;
		}

		100% {
			-webkit-transform: translate3d(0, 0, 0);
		}
	}

	@keyframes slideInRight {
		0% {
			transform: translate3d(100%, 0, 0);
			visibility: visible;
		}

		100% {
			transform: translate3d(0, 0, 0);
		}
	}

	.slideInRight {
		@include animate-prefixer(animation-name, slideInRight);
	}

}

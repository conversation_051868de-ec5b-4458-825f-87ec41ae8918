<!--
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2024-07-25 11:27:24
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-02-14 10:18:10
-->
<template>
    <div class="object-detail-container">
        <!--客观题信息栏-->
        <div class="object-detail-info">
            <div class="info-left">
                <span class="info-item">统计人数：{{ objData.totalNum }}人</span>
                <span class="info-item">满分：{{ objData.fullScore }}分</span>
                <span class="info-item">平均分：{{ objData.avgScore }}分</span>
                <span class="info-item">满分人数：{{ objData.fullNum }}人</span>
                <span class="info-item">最高分：{{ objData.maxScore }}分</span>
                <span class="info-item">最低分：{{ objData.minScore }}分</span>
            </div>
            <div class="info-right">
                <el-dropdown @command="handlerCommand">
                    <el-button type="primary">
                        导出<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="getSubjectScoreExcel">导出客观题得分明细表</el-dropdown-item>
                        <el-dropdown-item command="getSubjectAnswerExcel">导出客观题答案明细表</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
        </div>
        <!--客观题分析折线图-->
        <div style="width: 100%;height: 400px;" id="object-line-chart"></div>
        <!--客观题分析明细表-->
        <div class="object-detail-table">
            <div style="font-size: 18px; margin: 10px 0; font-weight: bold;">客观题作答详情</div>
            <el-row class="table-header">
                <el-col :span="8" class="col-li">题号</el-col>
                <el-col :span="2" class="col-li"> 题型 </el-col>
                <el-col :span="2" class="col-li"> 分值 </el-col>
                <el-col :span="2" class="col-li"> 平均分</el-col>
                <el-col :span="2" class="col-li"> 得分率 </el-col>
                <el-col :span="4" class="col-li"> 答对人数 </el-col>
                <el-col :span="4" class="col-li"> 作答统计 </el-col>
            </el-row>
            <!--内容-->
            <div class="content-height" v-if="objData.objs?.length > 0">
                <template v-for="(item, qindex) in objData.objs">
                    <el-row class="table-content" :style="{
                        height: item.answers.length * 22 + 'px'
                    }">
                        <el-col :span="8" class="col-li summary-col">
                            {{ item.qsNo }}
                        </el-col>
                        <el-col :span="2" class="col-li">
                            {{ item.typeName }}
                        </el-col>
                        <el-col :span="2" class="col-li">
                            {{ item.fullScore }}
                        </el-col>
                        <el-col :span="2" class="col-li">
                            {{ item.avgScore }}
                        </el-col>
                        <el-col :span="2" class="col-li">
                            {{ item.scoreRate }}%
                        </el-col>
                        <el-col :span="4" class="col-li">
                            {{ item.rightAnswer }}：{{ item.rightNum }}人
                        </el-col>
                        <el-col :span="4" class="col-li answer-col">
                            <template v-for="ans in item.answers">
                                <div style="height: 22px;line-height: 22px;">{{ ans.text }}:{{ ans.num }}人</div>
                            </template>
                        </el-col>
                    </el-row>
                </template>
            </div>
            <div class="content-height" v-else>
                <div class="no-data">暂无数据</div>
            </div>
        </div>
    </div>
</template>

<script>
import { listScanObjDesc } from '@/service/pstat';
export default {
    name: 'objectDetail',
    props: {
        workId: '',
        title: '',
        examId:'',
        subjectId:''
    },
    components: {
    },
    data() {
        return {
            objData: []
        }
    },
    async created() {
        await this.getObjScanData();
        this.initChart();
    },
    methods: {

        initChart() {
            var chartDom = document.getElementById('object-line-chart');
            var myChart = this.$echarts.init(chartDom);
            var option;

            option = {
                title: {
                    text: '客观题得分分布图',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis'
                },
                grid: {
                    left: '5%',
                    right: '5%',
                    //  bottom: '5%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: true,
                    data: this.objData.plans.map(item => { return item.section }),
                    name: "分数区间"
                },
                yAxis: {
                    type: 'value',
                    name: "人数"
                },
                series: [
                    {
                        name: '人数',
                        type: 'line',
                        data: this.objData.plans.map(item => { return item.num })
                    }
                ]
            };

            option && myChart.setOption(option);
        },
        async getObjScanData() {
            let params = {
                bookId: this.workId,
                examId: this.examId,
                subjectId: this.subjectId
            };
            let res = await listScanObjDesc(params);
            if (res.code == 1) {
                this.objData = res.data;
            }
        },
        /**
         * @name： 下载得分、答案详情
         */
        handlerCommand(comand) {
            let title = this.title;
            if(comand == "getSubjectScoreExcel"){
                title += '得分明细表'
            }else{
                title += '答案明细表'
            }
            let url = process.env.VUE_APP_KKLURL + `/pexam/export/${comand}?examId=${this.workId}&fileName=${title}`;
            window.open(url, 'blank');
        },
    },
}
</script>

<style lang="scss" scoped>
.object-detail-container {
    .object-detail-info {
        .info-left {
            display: inline-block;
            line-height: 40px;
        }

        .info-right {
            display: inline-block;
            vertical-align: top;
            float: right;
        }

        .info-item {
            margin-right: 20px;
        }
    }

    .object-detail-table {
        .col-li {
            text-align: center;
            border-right: 1px solid #f3f3f3;
            border-bottom: 1px solid #f3f3f3;
            vertical-align: middle;
            display: flex;
            height: 100%;
            align-items: center;
            justify-content: center;

            &.answer-col {
                display: unset;
            }
        }

        .ques-col {
            flex-direction: column;
            justify-content: unset;

            >span {
                line-height: 25px;
            }
        }

        .summary-col:first-child {
            border-left: 1px solid #f3f3f3;
        }

        .table-header {
            height: 50px;
            line-height: 50px;
            background: #f8f8f9;
            border-radius: 5px 5px 0 0;
            display: flex;
        }

        .content-height {
            // height: 612px;
            margin-top: 1px;
            overflow-y: auto;
        }
    }
}
</style>
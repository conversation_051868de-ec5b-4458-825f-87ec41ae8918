<template>
  <div class="paper-analyze-box">
    <headerFilter
      @updateFilter="updateFilter"
      @init="initFilter"
      ref="headerFilter"
      class="header-filter"
    ></headerFilter>
    <transition name="slide-fade" mode="out-in">
      <router-view
        v-if="isInit"
        ref="routerViewBox"
        class="router-view"
        :class="{ desktop: isDesktop }"
        :filterData="filterData"
      ></router-view>
      <el-skeleton style="padding: 20px" :rows="7" animated v-else />
    </transition>
  </div>
</template>

<script>
import headerFilter from '@/components/headerFilter.vue';
import NoData from '@/components/noData.vue';
import { clzListAPI } from '@/service/pexam';

let timeId = null;
export default {
  name: 'PaperAnalyze',
  components: {
    headerFilter,
    NoData,
  },
  data() {
    return {
      // 当前选中的筛选项数据
      filterData: {
        classId: '',
        subjectId: '',
        phaseId: '',
        xfId: '',
      },
      isInit: false,
      examSubjectList: [],
    };
  },

  computed: {
    isDesktop() {
      return this.$route.path.includes('/dReport');
    },
  },
  async mounted() {},
  methods: {
    initFilter() {
      this.isInit = true;
    },

    async updateFilter(data) {
      this.filterData = this.$deepClone(data);
    },
  },
};
</script>

<style lang="scss" scoped>
@import '../../styles/base.css';

.header-filter {
  padding-top: 20px;
  padding-left: 24px;
  padding-right: 24px;
}

.router-view {
  padding: 24px;
  padding-top: 0;
  height: 100%;

  &.desktop {
    overflow: hidden;
    overflow-y: auto;
  }
  // overflow: hidden;
  // overflow-y: auto;
}

.paper-analyze-box {
  // padding: 20px;
  background: #ffffff;
  box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
  border-radius: 6px;

  .line {
    border-top: 1px solid #e4e8eb;
  }

  .radioGroup {
    margin: 0 auto 10px;
  }

  .subjects-box {
    margin: 10px;
    margin-left: 0;

    //padding-bottom: 10px;
    .subject-label {
      color: #3f4a54;
      margin-right: 10px;
    }

    .subject-list {
      display: inline-flex;

      .subject-item {
        color: #3f4a54;
        font-size: 16px;
        margin: 0 5px;
        cursor: pointer;

        &.active {
          color: #409eff;
        }
      }
    }
  }

  .sel-class-box {
    display: flex;
    font-size: 16px;
    margin-bottom: 10px;

    .class-label {
      margin-right: 15px;
      white-space: nowrap;
    }

    .class-tags {
      .class-item {
        margin-right: 5px;
        cursor: pointer;

        &.active {
          color: #409eff;
        }
      }

      .tag-item {
        //width: 92px;
        height: 36px;
        //line-height: 36px;
        text-align: center;
        display: inline-block;
        //margin: 8px;
        margin-left: 0;
        margin-right: 8px;
        margin-top: -6px;
        cursor: pointer;
      }
    }
  }

  .opt-box {
    font-size: 16px;
    color: #3f4a54;
    text-align: right;
    margin-right: 10px;

    .chip {
      margin: 0 5px;
    }

    .confirm-btn {
      font-size: 16px;
      margin-top: 10px;
      margin-left: 20px;
    }
  }

  .quality-base-info-box {
    font-size: 16px;
    margin: 10px 10px 10px;
    padding: 20px 10px;
    background: #f7fafc;
    border-radius: 8px;

    .base-info-p {
      color: #8f9ca8;

      strong {
        color: #3f4a54;
      }
    }

    .base-info-box {
      font-size: 16px;
      margin-top: 20px;
      display: flex;
      justify-content: space-around;
      align-items: center;

      .col {
        display: flex;
        flex-direction: column;
        text-align: center;

        .title {
          color: #8f9ca8;
        }

        strong {
          margin-top: 6px;
          font-size: 20px;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.paper-analyze-box {
  .radioGroup {
    .el-radio-button__inner {
      font-size: 16px;
    }
  }

  .sel-class-box {
    .cls-tag {
      //max-width: 102px;
      white-space: nowrap;
      color: #409eff;
      background-color: #f7fbff;
      border-radius: 15px;
      font-size: 16px;
      //text-overflow: ellipsis;
      //overflow: hidden;
    }
  }
}

.el-tooltip__popper {
  max-width: 400px;
  //line-height: 180%;
}
</style>

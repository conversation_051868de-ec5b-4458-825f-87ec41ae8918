<template>
  <div class="student-paper-list">
    <div class="paper-scan-info" v-if="studentList.length>0">
      <div class="student-list">
        <p class="left_title">学生名单</p>
        <ul class="student-list-ul">
          <li class="student-item item-header">
            <span class="student-no">考号</span>
            <span class="student-score">操作</span>
          </li>
          <li class="student-item" v-for="student in studentList"
              :key="student.id" :class="{active:student.id===currentStudent.id}"
              @click="currentStudent=student">
            <span class="student-no">{{ student.studentNo }}</span>
            <span class="student-edit">
            <el-button class="stu-edit-button" @click="toEditNumber(student)" type="text">修改</el-button>
            <el-button class="stu-edit-button" @click="deleteStudent(student)" type="text">删除</el-button>
          </span>
          </li>
        </ul>
      </div>
      <div class="student-paper-detail">
        <scan-student v-if="currentStudent&&paperInfo" :student-paper-id="currentStudent.id"
                      :student-id="currentStudent.studentId"
                      @student-update="getPaperStudent"
                      :readonly="true"
                      :paper-info="paperInfo"></scan-student>
      </div>
      <el-dialog
          class="edit-number-dialog"
          title="修改考号"
          width="30%"
          :visible.sync="editNumber.show">
        <el-form ref="form" :model="editNumber.info" label-width="120px">
          <el-form-item label="学生姓名：">
            <el-input v-model="editNumber.info.studentName" disabled></el-input>
          </el-form-item>
          <el-form-item label="学生考号：">
            <el-input v-model="editNumber.info.studentNo" disabled></el-input>
          </el-form-item>

          <el-form-item label="新的考号：">
            <el-input v-model="editNumber.studentNo"></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
        <el-button @click="editNumber.show = false">取 消</el-button>
        <el-button type="primary" @click="saveStudentNo(false)">确 定</el-button>
      </span>
      </el-dialog>
    </div>
    <no-data v-else></no-data>
  </div>
</template>

<script>
import {mapGetters} from 'vuex';
import {deleteStudent, getPaperStudent, updateStudentNo} from "@/service/pexam";
import ScanStudent from "@/components/scan/ScanStudent";
import NoData from "@/components/noData";

export default {
  name: 'scan-student-not-found',
  props: ['paperId', 'paperInfo'],

  data() {
    return {
      studentList: [],
      currentStudent: null,
      status: 128,
      editNumber: {
        show: false,
        studentNo: '',
        info: {}
      }
    };
  },
  computed: {
    ...mapGetters([
      'filterSubjectList'
    ])
  },
  components: {NoData, ScanStudent},
  mounted() {
    this.init()
  },
  watch: {
    'currentStudent': function (newValue) {

    }
  },
  methods: {
    init() {
      this.getPaperStudent()
    },
    async getPaperStudent() {
      getPaperStudent({
        paperId: this.paperId,
        status: this.status
      }).then(res => {
        this.studentList = res.data
        this.currentStudent = this.studentList[0]
        this.$emit('student-count', {name: 'find', total: res.data.length})
      })
    },
    /**
     * 去编辑学生考号
     * @param student
     */
    toEditNumber(student) {
      this.editNumber.info = student
      this.editNumber.studentNo = student.studentNo
      this.editNumber.show = true
    },
    /**
     * 保存学生考号
     */
    saveStudentNo(force) {
      updateStudentNo({
        id: this.editNumber.info.id,
        stuNo: this.editNumber.studentNo,
        force: force
      }).then(res => {
        if (res.code === 2) {
          this.forceUpdateNo()
        } else {
          this.editNumber.show = false;
          this.getPaperStudent()
          this.$message.success('更新成功')
          if (res.data.status === 1) {
            this.$bus.$emit('updateScanScoreError')
          }
          if (force) {
            this.$bus.$emit('updateScanRepeat')
          }
        }
      }).catch(error => {
      })
    },
    forceUpdateNo() {
      this.$confirm('此考号已绑定学生，是否强制绑定?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.saveStudentNo(true)
      }).catch(() => {
      });
    },
    /**
     * 删除学生
     * @param info
     */
    deleteStudent(info) {
      this.$confirm(`试卷删除后数据不可恢复，确定删除？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText : '取消',
        type             : 'warning'
      }).then(() => {
        deleteStudent({
          id: info.id,
        }).then(res => {
          this.getPaperStudent()
          this.$message.success('删除成功')
        }).catch(error => {
        })
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.student-paper-list {
  height: calc(100% - 20px);
}
.paper-scan-info {
  width: 100%;
  height: 100%;
  margin-top: 10px;
  display: flex;

  .student-paper-detail {
    width: 100%;
    margin-left: 10px;
  }

  .left_title {
    text-align: center;
    padding: 10px;
    font-family: "Microsoft YaHei", serif;
    font-size: 18px;
  }

  .student-list {
    height: 100%;
    background: white;
    border: solid 1px #EAEAEA;

    .student-list-ul {
      list-style: none;
      width: 250px;
      border-top: solid 1px #EAEAEA;
      height: calc(100% - 48px);
      padding: 10px 0;
      overflow-y: auto;

      .student-item {
        display: flex;
        padding: 5px 10px;
        line-height: 30px;
        text-align: center;

        &.item-header {
          //text-align: center;
        }

        &.active {
          background: #E1EEFF;
        }

        > span {
          flex: 1;
        }

        .student-edit {
          .stu-edit-button {
            padding: 5px 0;
          }
        }
      }
    }
  }
}

.edit-number-dialog {

}
</style>

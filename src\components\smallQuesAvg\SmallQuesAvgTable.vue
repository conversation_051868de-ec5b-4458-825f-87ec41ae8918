<template>
  <div class="small-ques-box">
    <el-table
      v-if="tableData.length"
      :data="tableData"
      stripe
      ref="smallQuesTable"
      :header-cell-style="{
        fontSize: '16px',
        color: '#3F4A54',
        backgroundColor: '#f5f7fa',
      }"
      v-drag-table
      v-sticky-table="0"
    >
      <el-table-column align="center" prop="quesNoDesc" label="题号">
        <template slot-scope="scope">
          <div class="small-no-cell">
            {{ scope.row.quesNoDesc }}
            <div class="avg-square" v-if="scope.row.specialTypeId != 0">
              <span class="avg-square-word">
                {{
                  scope.row.specialTypeId == 1 ? '送' : scope.row.specialTypeId == 2 ? '零' : '附'
                }}</span
              >
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="fullScore" label="分值"> </el-table-column>
      <template v-for="(item, index) in clzList">
        <el-table-column align="center" :label="item.name" :key="index">
          <template slot-scope="scope">
            <span
              :style="{
                color: scope.row.data[''].avg > scope.row.data[item.id].avg ? 'red' : 'inherit',
              }"
            >
              {{ scope.row.data[item.id].avg }}</span
            >
          </template>
        </el-table-column>
      </template>
    </el-table>
    <div class="default" v-else>
      <p style="text-align: center; font-size: 16px; margin: 80px 0">暂无数据!</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SmallQuesAvgTable',
  props: ['tableData', 'clzList'],
  watch: {
    async tableData(newVal) {
      this.layout();
    },
  },
  mounted() {},
  data() {
    return {
      hideTable: false,
      totalData: [
        {
          questionList: [],
        },
      ],
      mouseFlag: false,
      mouseOffset: 0,
      newTotalData: [
        {
          quesNo: '',
          data: [],
        },
      ],
    };
  },
  methods: {
    layout() {
      if (!this.$refs.smallQuesTable) {
        return;
      }
      this.dom = this.$refs.smallQuesTable.bodyWrapper;
      this.dom.addEventListener('scroll', () => {
        this.$nextTick(() => {
          //滚动高度
          let scrollTop = this.dom.scrollTop;
          if (scrollTop > 5) {
            // (document.getElementsByClassName('el-table__fixed')[0]).style.cssText += 'top: -5px;';
          } else {
            // (document.getElementsByClassName('el-table__fixed')[0]).style.cssText += 'top: 0px;';
          }
        });
      });
    },
  },
};
</script>

<style lang="scss">
.small-ques-box {
  .el-table--scrollable-x .el-table__body-wrapper {
    //overflow-x: hidden;
    //cursor: pointer;
  }
  .el-table__fixed {
    height: 100% !important;
  }
}
.small-no-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}
.small-ques-no {
  position: relative;
  display: flex;
  justify-content: flex-start;
}
.small-ques-mark {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  // position: absolute;
  // top: 2px;
  // right: 2px;
  border: 1px solid;
  width: 20px;
  height: 20px;
  color: #e6a23c;
  background: #fdf6ec;
  border-color: #f5dab1;
  border-radius: 5px;
}
.avg-square {
  width: 0;
  height: 0;
  border: 16px solid transparent;
  border-top: 16px solid #fdf6ec;
  border-left: 16px solid #fdf6ec;
  z-index: 100;
  border-radius: 5px 0 0 0;
  position: absolute;
  left: 0;
  top: 0;
  .avg-square-word {
    position: absolute;
    left: -12px;
    top: -16px;
    color: #e6a23c;

    font-size: 13px;
  }
}
</style>
<style lang="scss" scoped>
.square {
  width: 0;
  height: 0;
  border: 16px solid transparent;
  border-top: 16px solid #fdf6ec;
  border-left: 16px solid #fdf6ec;
  z-index: 100;
  border-radius: 5px 0 0 0;
  position: absolute;
  left: 0;
  top: 0;
  .square-word {
    position: absolute;
    left: -12px;
    top: -16px;
    color: #e6a23c;

    font-size: 13px;
  }
}
</style>

/*
 * @Description:
 * @Author: 小圆
 * @Date: 2025-06-19 09:36:17
 * @LastEditors: 小圆
 */
import UserRole, { IRoleClass, IRoleSubject, RoleEnum } from './UserRole';

export type SubjectCenterCode = 'ENGLISH' | 'CHINESE';

// 深拷贝函数
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  if (Array.isArray(obj)) {
    const newArr = [];
    for (const item of obj) {
      newArr.push(deepClone(item));
    }
    return newArr;
  }

  let newObj = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      newObj[key] = deepClone(obj[key]);
    }
  }
  return newObj;
}

// 分组方法
function groupBy(objectArray, property) {
  return objectArray.reduce((acc, obj) => {
    const key = obj[property];

    if (Array.isArray(key)) {
      // 如果属性值是数组,则对数组中每个值都创建一个分组
      key.forEach(k => {
        const curGroup = acc[k] ?? [];
        acc[k] = [...curGroup, obj];
      });
      return acc;
    } else {
      // 如果属性值不是数组,则直接以该值作为分组key
      const curGroup = acc[key] ?? [];
      return { ...acc, [key]: [...curGroup, obj] };
    }
  }, {});
}

/**
 * 获取学科中心编码
 * @param subjectId 学科id
 * @returns 学科中心编码
 */
export function getSubjectCenterCode(subjectId: string | number): SubjectCenterCode {
  return UserRole.userInfo.schSubList.find(t => t.id == subjectId)?.center_code as SubjectCenterCode;
}

/**
 * 获取角色名称
 * @param userType 用户类型
 * @returns 角色名称
 */
export function getExamRoleName(userType: number) {
  if (UserRole.isOperation) {
    return '运营';
  }
  if (userType == RoleEnum.SCHOOL_ADMIN) {
    return '校领导';
  }
  if (userType == RoleEnum.GRADE_LEADER) {
    return '年级主任';
  }
  if (userType == RoleEnum.SUBJECT_LEADER) {
    return '学科主任';
  }
  if (userType == RoleEnum.PREPARE_LEADER) {
    return '备课组长';
  }
  if (userType == RoleEnum.CLASS_LEADER) {
    return '班主任';
  }
  if (userType == RoleEnum.TEACHER) {
    return '任课教师';
  }
  return '';
}

/**
 * 获取角色对应的学科对应的班级
 */
export async function getRoleSubjectClassMap(year?: string | number, campusCode?: string, addSchoolLeader?: boolean) {
  let role = {};
  let roleSubjectMap = getRoleSubjectListMapByYear(year, campusCode);
  if (addSchoolLeader) {
    roleSubjectMap[1] = getSchoolLeaderSubjectList(year, campusCode);
  }
  const leaderClassList = getLeaderClassList(campusCode);
  const substituteClassList = getSubstituteClassList(campusCode);

  for (const key in roleSubjectMap) {
    if (Object.prototype.hasOwnProperty.call(roleSubjectMap, key)) {
      const item = roleSubjectMap[key];
      let subjectIds = item.map(item => item.subjectId);
      if (key == '1' || key == '2' || key == '3' || key == '4') {
        role[key] = {};
        subjectIds.forEach(t => {
          role[key][t] = 'all';
        });
      } else if (key == '5') {
        role[key] = {};
        subjectIds.forEach(t => {
          role[key][t] = leaderClassList.map(item => item.id).join(',');
        });
      } else if (key == '6') {
        role[key] = {};
        let map = substituteClassList.reduce((prev, cur) => {
          if (prev[cur.subjectId]) {
            prev[cur.subjectId].push(cur.classId);
          } else {
            prev[cur.subjectId] = [cur.classId];
          }
          return prev;
        }, {});
        for (const subjectId in map) {
          let clzIds = map[subjectId];
          role[key][subjectId] = clzIds.join(',');
        }
      }
    }
  }
  return role;
}

/**
 * @description: 根据用户角色获取班级
 */
export async function getRoleClassByRoleType(roleType: number | string): Promise<IRoleClass[]> {
  const roleClassList = await UserRole.getRoleClass();
  // 0总管理员 1校管 2年级组长 3学科组长 4备课组长 5班主任 6普通老师
  if (roleType == 0 || roleType == 1 || roleType == 2 || roleType == 4) return roleClassList;
  if (roleType == 5) return deepClone(UserRole.leaderClassList);
  if (roleType == 3 || roleType == 6) {
    let userSubClassList = UserRole.substituteClassList;
    return roleClassList.filter(item => {
      return userSubClassList.find(u => u.classId == item.id);
    });
  }
  return [];
}

// 根据入学年份获取角色对应学科表
export function getRoleSubjectListMapByYear(year?, campusCode?): { [x: string]: IRoleSubject[] } {
  let subjectList: IRoleSubject[] = UserRole.roleSubjectList;
  if (campusCode) {
    subjectList = UserRole.campusList.find(item => item.code == campusCode).subjectList;
  }
  if (!year) {
    return groupBy(deepClone(subjectList), 'userType');
  }
  const roleSubjectMap = groupBy(
    deepClone(
      subjectList.filter(item => {
        return item.year === year || item.year == '';
      })
    ),
    'userType'
  );
  if (roleSubjectMap[RoleEnum.SUBJECT_LEADER]) {
    roleSubjectMap[RoleEnum.SUBJECT_LEADER] = roleSubjectMap[RoleEnum.SUBJECT_LEADER].filter(item => {
      return item.userTypes == RoleEnum.SUBJECT_LEADER;
    });
  }
  return roleSubjectMap;
}

// 根据入学年份学科对应角色表
export function getSubjectRoleMapByYear(year?, campusCode?): { [x: string]: number[] } {
  function reverRoleMap(roleSubjectMap) {
    let map = {};
    for (const key in roleSubjectMap) {
      let subjectArr = roleSubjectMap[key];
      subjectArr.forEach(item => {
        if (map[item.subjectId]) {
          map[item.subjectId].push(Number(key));
        } else {
          map[item.subjectId] = [Number(key)];
        }
      });
    }
    return map;
  }

  const roleSubjectMap = getRoleSubjectListMapByYear(year, campusCode); // 角色-学科
  return reverRoleMap(roleSubjectMap);
}

// 根据入学年份角色列表
function getRolesByYear(year?, campusCode?) {
  let roleSubject = getRoleSubjectListMapByYear(year, campusCode);
  return Object.keys(roleSubject).map(item => Number(item));
}

// 获取学科角色列表
function getSubjectRoles(subjectId, year?, campusCode?) {
  const map = getSubjectRoleMapByYear(year, campusCode); // 角色-学科
  let arr = [];
  if (map[subjectId]) {
    arr = Array.from(new Set(map[subjectId]));
  }
  return arr;
}

/**
 * 获取任教班级
 * @param campusCode 校区编码
 * @returns
 */
function getSubstituteClassList(campusCode?: string) {
  if (campusCode) {
    return UserRole.substituteClassList.filter(item => item.campusCode == campusCode);
  }
  return UserRole.substituteClassList;
}

/**
 * 获取班主任班级
 * @param campusCode 校区编码
 * @returns
 */
function getLeaderClassList(campusCode?: string) {
  if (campusCode) {
    return UserRole.leaderClassList.filter(item => item.campusCode == campusCode);
  }
  return UserRole.leaderClassList;
}

// 获取校管权限下的学科列表
function getSchoolLeaderSubjectList(year?: any, campusCode?: string) {
  let subjectList: IRoleSubject[] = UserRole.schoolLeaderSubjectList;
  if (campusCode) {
    subjectList = UserRole.campusList.find(item => item.code == campusCode).schoolLeaderSubjectList;
  }
  if (year) {
    subjectList = subjectList.filter(item => item.year === year || item.year == '');
  }
  return subjectList;
}

export default {
  deepClone,
  groupBy,
  getSubjectCenterCode,
  getExamRoleName,
  getRoleSubjectClassMap,
  getRoleClassByRoleType,
  getRoleSubjectListMapByYear,
  getSubjectRoleMapByYear,
  getRolesByYear,
  getSubjectRoles,
  getSubstituteClassList,
  getLeaderClassList,
  getSchoolLeaderSubjectList,
};

NODE_ENV = production

# just a flag
ENV = 'staging'

# base api
VUE_APP_BASE_API = https://test.iclass30.com
VUE_APP_BASE_URL = /bigdata/
# VUE_APP_BASE_API = https://kklservicetest.iclass30.com
VUE_APP_CUT_URL = https://test.iclass30.com
VUE_APP_FS_URL = https://fs.iclass30.com
VUE_APP_KKLURL = https://kklservicetest.iclass30.com
VUE_APP_CARDURL = http://webtest.iclass30.com:6160
VUE_APP_ABCARDURL = http://webtest.iclass30.com:6160/abCard
VUE_APP_CUTPAPERURL = http://webtest.iclass30.com:6161/
VUE_APP_AUDITURL = https://gecetest.iclass30.com/book-audit/
VUE_APP_MATH_CDN = https://latexcdn.iclass30.com
VUE_APP_LLKTURL = https://gc.iclass30.com
VUE_APP_17 = https://test.iclass30.com
VUE_APP_TESTBANK = https://test.iclass30.com
VUE_APP_DOMAIN = gecetest.iclass30.com
VUE_APP_SSO_PATH = https://fs.iclass30.com/aliba/plug/sso/sso-client-sdk-test.js
VUE_APP_APPID = s1wttaloqm2339e1tus2
VUE_APP_XUEBAN_API = https://scanimagetest.iclass30.com
VUE_APP_CORRECT_URL = http://webtest.iclass30.com:6262/#/webCorrect
VUE_APP_BACKSTAGE = http://webtest.iclass30.com:5500/base/schoolMain
VUE_APP_ZP = https://c30zp.iclass30.com/
VUE_APP_POINTS = https://scanimagetest.iclass30.com/scan_image/view_paper_mark
VUE_APP_SCAN_MOBILE = https://gecetest.iclass30.com/scan-task-mobile
VUE_APP_SCAN_SOCKET = wss://kklservicetest.iclass30.com/psocket/socket?token=PsQsE7LoceMEV4D1203F2HpcaLH1XR6/Wz5acRVl+vm7XNs9bM7poJ7zSj1DwoSd
VUE_APP_HOMEWORK = http://webtest.iclass30.com:84
VUE_APP_ZUOWEN = https://zuowentest.iclass30.com
VUE_APP_CHANNEL = c30

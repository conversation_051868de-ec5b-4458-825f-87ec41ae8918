<template>
  <div>
    <div class="luckysheet_info_detail" style="position:absolute;width:100%;display:flex;align-items: center;">
      <div class="form-box" style="margin-left:20px;width:calc(100% - 328px);display:flex;">
        <div class="form-item">
          <el-select v-model="chkClsIds" style="width:190px" multiple collapse-tags @change="changeCls"
            placeholder="请选择要导入班级">
            <el-option-group v-for="cls in clsList" :key="cls.name">
              <el-checkbox :label="cls.id" :indeterminate="cls.indet" v-model="cls.checked" @change="chkAll(cls)"
                :key="cls.name" style="padding-left:16px;">{{ cls.name }}
              </el-checkbox>
              <el-option v-for="item in cls.data" :key="item.id" :label="item.name" :value="item.id"
                style="padding-left:40px;">
              </el-option>
            </el-option-group>
          </el-select>
        </div>
        <div class="form-item split-line"></div>
        <div class="form-item" style="margin-left:20px;">
          <b style="color:red;">{{ grd.name }}{{ sjt.name }}学科</b>题量：
          <el-input-number :disabled="sjtAble" v-model="num" style="width:120px" @change="cgQuesNum" :min="1" :max="200"
            label="请输入题目数量"></el-input-number>
        </div>
        <div class="form-item split-line" v-if="sjtAble"></div>
        <div class="form-item" v-if="sjtAble">
          <el-input v-model="sjt.qc" style="width:95px;margin-left:20px;" placeholder="拆分题列"></el-input>
          <el-input v-model="sjt.qn" style="width:95px;margin-left:20px;" placeholder="拆分题数"></el-input>
          <el-input v-model="sjt.sc1" style="width:95px;margin-left:20px;" placeholder="特殊题列1"></el-input>
          <el-input v-model="sjt.sc2" style="width:95px;margin-left:20px;" placeholder="特殊题列2"></el-input>
          <el-button @click="splitColumns" style="margin-left:20px;" type="primary">拆分</el-button>
        </div>
        <div class="form-item split-line"></div>
      </div>

      <div class="buttons" style="float:right">
        <el-button type="primary" @click="exportXlsx">导入excel</el-button>
        <el-button @click="clearClsData" type="primary">清除缺考</el-button>
        <el-button @click="saveClsData" type="primary">保存</el-button>
      </div>

      <exportXlsx ref="exportXlsx" :examId="exam.examId" :year="year" :phase="grd.phaseId - 2"
        :subjectIds="exam.subjectIds" @closeDialog="closeDialog" v-if="dlgExportXlsxVisible"></exportXlsx>
    </div>

    <div id="luckysheet"
      style="margin-top:55px;padding:0px;position:absolute;width:100%;left: 0px;top: 0px;bottom:0px;z-index:0;"></div>
  </div>
</template>

<script>
import DtModal from '../ui/dt-modal'
import exportXlsx from "@/components/exportXlsx";
import { getImportInfo, examImportStu, getExamClsStu } from "../service/pexam"
import { classList, getStuListByClassId } from '../service/api'
import UserRole from '@/utils/UserRole';
import { loadScript, loadStyle } from '@/utils/index';

export default {
  name: "upload-excel",
  components: {
    DtModal,
    exportXlsx
  },
  props: [],
  data() {
    return {
      //题量
      num: 1,
      userGrdList: [],
      userSubList: [],
      defBg: "#EDEDED",
      redBg: "#FC1944",
      sjt: {
        qc: '',
        qn: '',
        sc1: '',
        sc2: ''
      },
      sjtAble: false,
      exam: {},
      grd: {},
      year: '',
      chkClsIds: [],
      clsList: [],
      clsMap: {},
      clsStuMap: {},
      xlsMap: {},
      bookClsStuMap: {},
      ccli: 0,
      stuClsMap: {},
      stuMap: {},
      cfMap: {},
      dlgExportXlsxVisible: false
    }
  },
  computed: {},
  async mounted() {
    await Promise.all([
      loadScript('https://fs.iclass30.com/webcdn/luckysheet-2.1.13/package/dist/plugins/js/plugin.js'),
      loadScript('https://fs.iclass30.com/webcdn/luckysheet-2.1.13/package/dist/luckysheet.umd.js'),
      loadStyle('https://fs.iclass30.com/webcdn/luckysheet-2.1.13/package/dist/plugins/css/pluginsCss.css'),
      loadStyle('https://fs.iclass30.com/webcdn/luckysheet-2.1.13/package/dist/plugins/plugins.css'),
      loadStyle('https://fs.iclass30.com/webcdn/luckysheet-2.1.13/package/dist/css/luckysheet.css'),
      loadStyle('https://fs.iclass30.com/webcdn/luckysheet-2.1.13/package/dist/assets/iconfont/iconfont.css'),
    ])
    this.initGrdCls()
  },
  watch: {},
  methods: {
    exportXlsx() {
      this.dlgExportXlsxVisible = true;
    },
    closeDialog() {
      this.dlgExportXlsxVisible = false;
    },
    initQuesNum(sjtId) {
      let nums = this.exam[sjtId]
      let quesNums = this.exam.quesNums[sjtId]

      let sid1 = `1_${sjtId}`
      let sid2 = `2_${sjtId}`
      let config1 = this.cfMap[sid1]
      let config2 = this.cfMap[sid2]

      let sheet1 = luckysheet.getLuckysheetfile()[config1.idx]
      let sheet2 = luckysheet.getLuckysheetfile()[config2.idx]

      let cells1 = sheet1.data
      let cells2 = sheet2.data
      for (let i = 0; i < nums.length; i++) {
        if (cells1[0][4 + i] && cells1[0][4 + i].v) {
          continue
        }

        let v = (i + 1)
        if (quesNums[i]) {
          v = quesNums[i];
        }
        cells1[0][4 + i] = { v: v, bl: 1, "bg": this.defBg, vt: 0, ht: 0 }
        cells2[0][2 + i] = { v: v, bl: 1, "bg": this.defBg, vt: 0, ht: 0 }

        v = '请输入题目总分'
        if (nums[i]) {
          v = nums[i]
        }

        cells1[1][4 + i] = { v: v, bl: 1, "bg": this.defBg, vt: 0, ht: 0 }
        cells2[1][2 + i] = { v: v, bl: 1, "bg": this.defBg, vt: 0, ht: 0 }

        config1.columnlen[`${i + 4}`] = 100

        config2.columnlen[`${i + 2}`] = 100
      }

      for (let i = 0; i < cells1.length; i++) {
        let r1 = new Array(cells1[i].length)
        let r2 = new Array(cells2[i].length)
        for (let j = 0; j < nums.length + 4; j++) {
          r1[j] = cells1[i][j]
          if (j < nums.length + 2) {
            r2[j] = cells2[i][j]
          }
        }

        cells1[i] = r1
        cells2[i] = r2
      }
      if (nums.length > 0) {
        config1.borderInfo[3] = {
          "rangeType": "range",
          "borderType": "border-all",
          "color": "#000",
          "style": "1",
          range: [{ "row": [0, 1], "column": [3, nums.length + 3] }]
        }
        config2.borderInfo[2] = {
          "rangeType": "range",
          "borderType": "border-all",
          "color": "#000",
          "style": "1",
          range: [{ "row": [0, 1], "column": [2, nums.length + 1] }]
        }
      }

      luckysheet.setConfig(config1, { order: config1.idx })
      luckysheet.setConfig(config2, { order: config2.idx })
      luckysheet.refresh()
    },
    initNum() {
      for (let m = 0; m < this.exam.sjts.length; m++) {
        let sjt = this.exam.sjts[m]
        let nums = this.exam[sjt.id]
        this.initQuesNum(sjt.id, nums)
      }
      this.exam.isInit = true
    },
    cgQuesNum(num) {
      let sjtId = this.sjt.id
      let nums = this.exam[sjtId]
      let nums2 = new Array(num)

      if (nums.length > 0) {
        if (nums.length >= nums2.length) {
          for (let i = 0; i < nums2.length; i++) {
            nums2[i] = nums[i]
          }
        } else {
          for (let i = 0; i < nums.length; i++) {
            nums2[i] = nums[i]
          }
        }
      }

      nums = nums2
      this.exam[sjtId] = nums2
      this.initQuesNum(sjtId, nums)
    },
    initBook(cb) {
      if (this.exam.examId) {
        cb.call(this)
      } else {
        let examId = this.$route.query.examId
        let sjtId = this.$route.query.subjectId
        let qc = Number(this.$route.query.qc);
        getImportInfo({ examId: examId }).then(data => {
          this.exam = {
            examId: examId,
            quesNums: {}
          }
          let d = data.data

          this.grd = this.userGrdList.find(q => q.id == d.gradeId)
          this.year = d.year;
          let sjtIds = [];
          if (sjtId) {
            sjtIds = sjtId.split(',');
          } else {
            sjtIds = d.subjectIds.split(',');
          }
          this.exam.subjectIds = sjtIds;
          let sjts = []
          for (let m = 0; m < sjtIds.length; m++) {
            let sjt = this.userSubList.find(q => q.id == sjtIds[m])
            if (sjt) {
              sjt.name = sjt.name.replace(/小学|初中|高中/g, "")
              sjts.push(sjt)
              if (sjtId && qc > 0) {
                this.exam[sjt.id] = new Array(qc)
                this.exam.quesNums[sjt.id] = new Array(qc)
              } else {
                this.exam[sjt.id] = new Array(1)
                this.exam.quesNums[sjt.id] = new Array(qc)
              }
            }
          }

          this.exam.sjts = sjts
          if (d.useCls) {
            this.exam.useCls = JSON.parse(d.useCls);
          }

          if (d.scores) {
            for (let n in d.scores) {
              this.exam[n] = d.scores[n]
            }
          }

          if (d.quesNums) {
            for (let n in d.quesNums) {
              this.exam["quesNums"][n] = d.quesNums[n]
            }
          }

          if (sjts.length > 0) {
            this.sjt = sjts[0]
            this.num = this.exam[this.sjt.id].length
          }

          cb.call(this)
        })
      }
    },
    async initGrd(cb) {
      let $this = this
      if (!$this.userGrdList.length) {
        const ret = await UserRole.getUserInfoPersonalityTest();
        $this.userGrdList = ret.userGrdList
        $this.userSubList = ret.userSubList
        if (cb) {
          cb.call($this)
        }
      } else {
        if (cb) {
          cb.call($this)
        }
      }
    },
    convert26(num) {
      var str = ""
      while (num > 0) {
        var m = num % 26
        if (m == 0) {
          m = 26
        }
        str = String.fromCharCode(m + 64) + str
        num = (num - m) / 26
      }
      return str
    },
    chkAll(cls) {
      if (cls.checked) {
        cls.data.forEach(q => {
          this.chkClsIds.push(q.id)
        })
      } else {
        let clsIds = []
        for (let i = 0; i < this.chkClsIds.length; i++) {
          let clsId = this.chkClsIds[i]

          let hasId = cls.data.find(q => q.id == clsId)
          if (!hasId) {
            clsIds.push(clsId)
          }
        }

        this.chkClsIds = clsIds
      }

      this.chkClsIds = Array.from(new Set(this.chkClsIds))
      this.changeCls()
    },
    chkQues(sn, c, r, v, ques, type) {
      if (type) {
        if (!v || v.v == "" || v.v == undefined || v.v == "缺考") {
          return true
        }
      }

      v.bg = this.redBg

      if (v.v == "请输入题目总分") {
        this.$message.error(`${sn}表格，请在标红区域输入正确的内容：${this.convert26(c)}${r}`)
        return false
      }

      let score = parseFloat(v.v)
      if (isNaN(score)) {
        this.$message.error(`${sn}表格，请输入正确的分数格式：${this.convert26(c)}${r}`)
        return false
      }
      if (type) {
        if (score < 0) {
          this.$message.error(`${sn}表格，题目得分必须大于等于0：${this.convert26(c)}${r}`)
          return false
        }
        if (score > parseFloat(ques.v)) {
          this.$message.error(`${sn}表格，题目得分必须小于等于题目总分：${this.convert26(c)}${r}`)
          return false
        }
      } else {
        if (score <= 0) {
          this.$message.error(`${sn}表格，题目总分必须大于0：${this.convert26(c)}${r}`)
          return false
        }
      }

      v.bg = this.defBg
      return true
    },
    splitColumns() {
      let config = this.cfMap[`2_${this.sjt.id}`]
      let sjt = this.sjt
      let nums = this.exam[sjt.id]

      if (!nums || nums.length == 0) {
        this.$message.error(`${sn}表格，至少导入一题的作答信息`)
        return
      }
      let len = nums.length
      let qc = Number(sjt.qc)
      let qn = Number(sjt.qn)
      let sc1 = Number(sjt.sc1) || 0
      let sc2 = Number(sjt.sc2) || 0
      let special1 = sc1 + 1
      let special2 = sc2 + 1

      if (!qc) {
        this.$message.error(`拆分题列输入不正确，请输入数字`)
        return
      }

      if (!qn) {
        this.$message.error(`拆分题数输入不正确，请输入数字1~${len}`)
        return
      }

      if (sc2 && !sc1) {
        this.$message.error(`若只有一个特殊题列请在特殊题列1填写！`)
        return
      }

      if (sc2 || sc1) {
        if (sc1 === sc2) {
          this.$message.error(`请设置不同的特殊题列`)
          return
        }

        if (sc1 && (sc1 < qc || sc1 >= qc + qn)) {
          this.$message.error(`请设置正确的特殊题列1`)
          return
        }

        if (sc2 && (sc2 < qc || sc2 >= qc + qn)) {
          this.$message.error(`请设置正确的特殊题列2`)
          return
        }
      }

      let cells = luckysheet.getLuckysheetfile()[config.idx].data

      let row1 = cells[1]

      for (let c1 = 2; c1 < nums.length; c1++) {
        let v = row1[c1]
        if (!v || !v.v || v.v === '请输入题目总分') {
          this.$message.error(`请输入小题分数`)
          break
        }

        let vv = parseFloat(v.v)

        if (!vv || vv <= 0) {
          this.$message.error(`请输入正确的小题分数`)
          break
        }
      }

      let copyHeaders = []
      for (let i = 0; i < 2; i++) {
        let cell = cells[i]

        copyHeaders.push(cell)
      }

      cells = luckysheet.getLuckysheetfile()[config.idx].data

      let start = qc + 1
      let end = start + qn

      for (let r = 2; r < cells.length; r++) {
        let row = cells[r]
        if (row[0] == null || !row[0].v) {
          break
        }

        let v = row[start]
        if (!v || !v.v) {
          v = 0
        } else {
          v = parseFloat(v.v)
        }

        let quesScore = Number(row1[start].v)
        let fullScore = quesScore * qn
        let fullScore1 = quesScore * (qn - 1)
        let subScore = fullScore - v
        let scores = {}

        for (let col = start; col < end; col++) {
          scores[col] = quesScore
        }

        //有减分
        if (subScore) {
          let modScore = subScore % quesScore

          //能不能整除
          if (modScore) {
            //5分
            if (quesScore === 5) {
              //减4,两个特殊列分别2分
              if (modScore === 4) {
                if (sc2) {
                  scores[special2] = quesScore - 2
                  modScore = 2
                  subScore -= 2
                }
              }
              //减6,两个特殊列分别3分 subScore < fullScore1 说明有两题可以减3分
              else if (modScore === 1 && subScore > quesScore && subScore < fullScore1) {
                if (sc2) {
                  scores[special2] = quesScore - 3
                  modScore = 3
                  subScore -= 3
                }
              }
            }
            //3分
            else if (quesScore === 3) {
              //减2分,两个特殊列分别1分
              if (modScore === 2 && subScore < fullScore1) {
                if (sc2) {
                  scores[special2] = quesScore - 1
                  modScore = 1
                  subScore -= 1
                }
              }
            }

            //减剩下的
            if (sc1) {
              scores[special1] = quesScore - modScore
              subScore -= modScore
              modScore = 0
            }
          }

          //去除已赋分，剩下的列减分
          for (let col = end - 1; subScore > 0 && col >= start; col--) {
            if (scores[col] < quesScore) {
              continue
            }
            if (subScore > quesScore) {
              scores[col] = 0
              subScore -= quesScore
            } else {
              scores[col] = quesScore - subScore
              subScore = 0
            }
          }
        }

        for (let c2 = start; c2 < end; c2++) {
          row[c2] = { v: scores[c2], vt: 0, ht: 0 }
        }

      }

      luckysheet.refresh()
    },
    clearClsData() {
      for (let m = 0; m < this.exam.sjts.length; m++) {
        let sjt = this.exam.sjts[m]

        let config = this.cfMap[`1_${sjt.id}`]
        let sn = this.grd.name + sjt.name
        let nums = this.exam[sjt.id]
        if (!nums || nums.length == 0) {
          this.$message.error(`${sn}表格，至少导入一题的作答信息`)
          break
        }

        let cells = luckysheet.getLuckysheetfile()[config.idx].data

        let useCells = []
        let useCells2 = []
        for (let r = 0; r < cells.length; r++) {
          let row = cells[r]
          if (r < 2) {
            useCells.push(row)
            continue
          }
          if (!row || !row[0] || !row[0].v) {
            break
          }
          let type = 0
          let scoreStr = ""
          for (let c2 = 4; c2 < nums.length + 4; c2++) {
            let score = ''
            if (row[c2]) {
              score = row[c2].v
            }

            if (score == "缺考") {
              type = 2
            } else if (score == "未扫描") {
              type = 1
            }
            if (score >= 0) {
              scoreStr += score
            }
          }
          if (!scoreStr) {
            type = 2
          }

          if (type != 0) {
            useCells2.push(new Array(cells[r].length))
          } else {
            useCells.push(cells[r])
          }
        }

        useCells.push(...useCells2)

        for (let r = 0; r < useCells.length; r++) {
          let row = useCells[r]
          cells[r] = row
        }

        luckysheet.refresh()
      }
    },
    saveClsData() {
      if (this.chkClsIds.length == 0) {
        this.$message.error("请选择导入的班级")
        return
      }
      luckysheet.exitEditMode()

      if (luckysheet.getSheet().index[0] == 2) {
        this.$message.error(`请检查同步的sheet是否正确，检查后保存！`)
        return
      }

      let xls = {
        examId: this.exam.examId,
        clzs: [],
        stuMap: {},
        data: {}
      }

      let flag = false
      for (let m = 0; m < this.exam.sjts.length; m++) {
        let sjt = this.exam.sjts[m]

        let config = this.cfMap[`1_${sjt.id}`]
        let sn = this.grd.name + sjt.name
        let nums = this.exam[sjt.id]
        if (!nums || nums.length == 0) {
          this.$message.error(`${sn}表格，至少导入一题的作答信息`)
          flag = true
          break
        }

        let cells = luckysheet.getLuckysheetfile()[config.idx].data

        let r0 = 0
        let ques0 = cells[r0]
        var titles = [];
        for (let c0 = 4; c0 < nums.length + 4; c0++) {
          titles[c0 - 4] = `${ques0[c0].v}`
        }

        let r = 1
        let ques = cells[r]
        for (let c = 4; c < nums.length + 4; c++) {
          if (!this.chkQues(sn, c + 1, r + 1, ques[c])) {
            flag = true
            break
          }

          nums[c - 4] = ques[c].v
        }

        if (flag) {
          luckysheet.refresh()
          return
        }

        let clsData = {
          titles: titles,
          scores: nums,
          stuAnsMap: {}
        }

        r = r + 1
        for (; r < cells.length; r++) {
          let row = cells[r]
          if (!row || !row[0] || !row[0].v) {
            break
          }
          let sQues = []
          let type = 0
          let scoreStr = ""
          for (let c2 = 4; c2 < nums.length + 4; c2++) {
            if (!this.chkQues(sn, c2 + 1, r + 1, row[c2], ques[c2], 1)) {
              flag = true
              break
            }
            let score = ''
            if (row[c2]) {
              score = row[c2].v
            }

            if (score == "缺考") {
              type = 1
            } else if (score == "未扫描") {
              type = 2
            }
            if (score >= 0) {
              scoreStr += score
            }

            sQues.push(score || 0)
          }
          if (!scoreStr) {
            type = 2
          }

          if (type != 0) {
            for (let sq = 0; sq < sQues.length; sq++) {
              sQues[sq] = 0
            }
          }

          if (flag) {
            break
          }

          let r0 = row[0].v.split('_')
          let uid = r0[1], clsId = r0[2], uName = row[1].v, name = row[2].v, clsName = row[3].v

          let clz = xls.clzs.find(z => z.id == clsId)
          if (!clz) {
            clz = {
              id: clsId,
              name: clsName,
              idx: xls.clzs.length,
            }
            xls.clzs.push(clz)
          }

          clsData.stuAnsMap[uName] = { type: type, data: sQues }

          if (m == 0) {
            if (!xls.stuMap[`${clz.idx}@${uName}`]) {
              xls.stuMap[`${clz.idx}@${uName}`] = {
                id: uid,
                name: name
              }
              xls.clzs[clz.idx] = clz
            }
          }
        }

        if (flag) {
          luckysheet.refresh()
          return
        }

        xls.data[sjt.id] = clsData
      }

      if (flag) {
        return
      }

      examImportStu(xls).then(data => {
        if (data.code == 1) {
          this.$message({
            message: '导入成功！',
            type: 'success',
            duration: 2000
          })
          setTimeout(() => {
            window.close()
          }, 1000)
        } else {
          this.$message({
            message: data.msg,
            type: 'error',
            duration: 2000
          })
        }
      })
    },
    changeCls(val) {
      this.clsList.forEach(v => {
        let len = v.data.length
        let len2 = 0
        v.data.forEach(v2 => {
          let clsId = this.chkClsIds.find(v3 => v3 == v2.id)
          if (clsId) {
            len2++
          }
        })
        if (len > 0 && len == len2) {
          v.checked = true
          v.indet = false
        } else if (len > 0 && len > len2) {
          if (len2) {
            v.indet = true
          } else {
            v.indet = false
          }
          v.checked = false
        }
      })
      let clsLen = 0
      for (let m = 0; m < this.exam.sjts.length; m++) {
        let sjtId = this.exam.sjts[m].id
        this.exam[`cci${sjtId}`] = 2
        let config = this.cfMap[`1_${sjtId}`]
        let cells = luckysheet.getLuckysheetfile()[config.idx].data
        for (let j = 2; j < cells.length; j++) {
          let cr = cells[j]
          if (!cr[0] || !cr[0].v) {
            break
          }

          let c0d = []
          for (let c0 = 0; c0 < cr.length; c0++) {
            if (cr[c0] && (cr[c0].v || cr[c0].v == 0)) {
              c0d.push(cr[c0].v)
            } else {
              break
            }
          }

          this.xlsMap[cr[0].v] = c0d

          cells[j] = []
        }

        clsLen += this.chkClsIds.length
        this.chkClsIds.forEach((clsId, ci) => {
          let cr = this.clsMap[clsId]
          if (cr.use) {
            if (this.bookClsStuMap[clsId]) {
              this.getStuListByClassId(sjtId, cr, cells)
            } else {
              getExamClsStu({ examId: this.exam.examId, clsId: clsId }).then(d2 => {
                this.bookClsStuMap[clsId] = 1
                d2.data.forEach(t2 => {
                  this.clsStuMap[`${t2.subjectId}_${t2.studentId}_${clsId}`] = t2
                })

                this.getStuListByClassId(sjtId, cr, cells)
              })
            }
          } else {
            this.getStuListByClassId(sjtId, cr, cells)
          }
        })

        if (clsLen == 0) {
          luckysheet.refresh()
        } else {
          let $this = this
          let max = 0
          let readStus = setInterval(() => {
            if ($this.ccli == clsLen || max >= 1500) {
              for (let n = 0; n < $this.exam.sjts.length; n++) {
                let sjt = $this.exam.sjts[n]

                let cfi = this.cfMap[`1_${sjt.id}`]
                let len = luckysheet.getLuckysheetfile()[cfi.idx].data.length
                let cci = $this.exam[`cci${sjt.id}`]
                if (cci >= len) {
                  luckysheet.insertRow(cci, {
                    order: cfi.idx, number: 1, success: function () {
                    }
                  })
                }

                $this.exam[`cci${sjt.id}`] = 0
              }

              luckysheet.refresh()

              clearInterval(readStus)
            }
            max += 500
          }, 500)
        }
      }
    },
    readStuData(sjtId, cr, cells) {
      let data = this.stuClsMap[cr.id]
      data.forEach(item => {
        let id = `${sjtId}_${item.id}_${cr.id}`
        let cr0 = this.xlsMap[id]
        let arr = [id, item.examNo, item.realname, cr.name]
        if (cr.use) {
          let clsStu = this.clsStuMap[id]
          if (clsStu) {
            if (clsStu.type != 0) {
              for (var i = 0; i < this.num; i++) {
                arr.push(clsStu.type == 2 ? "缺考" : "未扫描")
              }
            } else {
              clsStu.questions.forEach(r3 => {
                arr.push(r3.score)
              })
            }
          } else {
            for (var i = 0; i < this.num; i++) {
              arr.push("缺考")
            }
          }
        }
        if (cr0) {
          for (let c0 = 4; c0 < cr0.length; c0++) {
            arr[c0] = cr0[c0]
          }
        }

        let cell = []
        arr.forEach(v => {
          let c = { v: v, vt: 0, ht: 0 }
          if (v == "缺考") {
            c.bl = 1
            c.fc = '#EA7D24'
          }
          cell.push(c)
        })
        cells[this.exam[`cci${sjtId}`]] = cell
        this.exam[`cci${sjtId}`]++
      })
    },
    getStuListByClassId(sjtId, cr, cells) {
      if (this.stuClsMap[cr.id] && this.stuClsMap[cr.id].length > 0) {
        this.readStuData(sjtId, cr, cells)
        this.ccli++
      } else {
        getStuListByClassId({ classId: cr.id }).then(data => {
          this.stuClsMap[cr.id] = data.data
          this.readStuData(sjtId, cr, cells)
          this.ccli++
        })
      }
    },
    initGrdCls() {
      this.initGrd(function () {
        this.initBook(function () {
          this.chkClsIds = []
          let useCls = this.exam.useCls

          let clsIds = ""
          if (useCls) {
            var subjectUseCls = useCls[this.sjt.id];
            if (subjectUseCls)
              for (let clsId in subjectUseCls) {
                clsIds += `${clsId},`
              }
          }
          this.exam.useClsIds = clsIds

          let noClsList = []
          let useClsList = []
          this.clsMap = {}
          classList(this.year, this.grd.phaseId - 2).then(data => {
            data.data.rows.forEach(r => {
              if (r.student_count > 0) {
                let cls = { id: r.id, name: r.class_name }
                if (this.exam.useClsIds && this.exam.useClsIds.indexOf(r.id) >= 0) {
                  useClsList.push(cls)
                  cls.use = true
                } else {
                  noClsList.push(cls)
                  cls.use = false
                }
                this.clsMap[cls.id] = cls
              }
            })
          })

          this.clsList = [
            { name: "未导入班级", data: noClsList },
            { name: "已导入班级", data: useClsList },
          ]

          let arr = [['用户id', '考号', '姓名', '行政班级'], ['', '', '', '']]
          let config = {
            colhidden: { 0: 0 },
            columnlen: { 1: 85, 2: 85, 3: 85 },
            merge: {
              "0_0": { "r": 0, "c": 0, "rs": 2, "cs": 1 },
              "0_1": { "r": 0, "c": 1, "rs": 2, "cs": 1 },
              "0_2": { "r": 0, "c": 2, "rs": 2, "cs": 1 },
              "0_3": { "r": 0, "c": 3, "rs": 2, "cs": 1 }
            },
            borderInfo: []
          }

          let arr2 = [['姓名', '行政班级'], ['', '']]
          let config2 = {
            columnlen: { 0: 85, 1: 85 },
            merge: { "0_0": { "r": 0, "c": 0, "rs": 2, "cs": 1 }, "0_1": { "r": 0, "c": 1, "rs": 2, "cs": 1 } },
            borderInfo: []
          }

          for (let i = 0; i < 3; i++) {
            config.borderInfo.push({
              "rangeType": "range",
              "borderType": "border-outside",
              "color": "#000",
              "style": "1",
              range: [{ "row": [0, 1], "column": [i, i + 1] }]
            })
          }

          for (let i = 0; i < 2; i++) {
            config2.borderInfo.push({
              "rangeType": "range",
              "borderType": "border-outside",
              "color": "#000",
              "style": "1",
              range: [{ "row": [0, 1], "column": [i, i + 1] }]
            })
          }

          let cells = []
          arr.forEach((v1, i1) => {
            v1.forEach((v2, i2) => {
              cells.push({
                r: i1, c: i2, v: {
                  v: v2, bl: 1, "bg": this.defBg, vt: 0, ht: 0
                }
              })
            })
          })
          let cells2 = []
          arr2.forEach((v1, i1) => {
            v1.forEach((v2, i2) => {
              cells2.push({
                r: i1, c: i2, v: {
                  v: v2, bl: 1, "bg": this.defBg, vt: 0, ht: 0
                }
              })
            })
          })

          let $this = this

          let celldata = []
          let exam = this.exam
          let sjts = exam.sjts

          let idx = 0
          for (let i = 0; i < sjts.length; i++) {
            config.idx = idx
            let sjt = sjts[i]
            let cfi1 = this.cfMap[`1_${sjt.id}`] = JSON.parse(JSON.stringify(config))
            celldata.push({
              name: `${this.grd.name}${sjt.name}`,
              index: `1_${sjt.id}`,
              config: cfi1,
              celldata: cells
            })

            idx++
          }
          for (let i = 0; i < sjts.length; i++) {
            config2.idx = idx
            let sjt = sjts[i]

            let cfi2 = this.cfMap[`2_${sjt.id}`] = JSON.parse(JSON.stringify(config2))
            celldata.push({
              name: `${this.grd.name}${sjt.name}副本`,
              index: `2_${sjt.id}`,
              config: cfi2,
              celldata: cells2
            })
            idx++
          }
          luckysheet.create({
            container: 'luckysheet', // 设定DOM容器的id
            showinfobar: false,
            lang: 'zh', // 设定表格语言
            showsheetbarConfig: {
              add: false
            },
            hook: {
              // cellMousedownBefore: function (cell, postion, sheet, ctx) {
              //   if (sheet.index[0] == 1) {
              //     if (postion.c == 1 || postion.c == 2 || postion.c == 3 || postion.r == 0) {
              //       return false
              //     }
              //   }
              // },
              cellEditBefore: function (range, x) {
                let cr = range[0]
                if (cr.row_focus == 1) {
                  let sjt = $this.sjt
                  let sjtAble = $this.sjtAble
                  let sid = `1_${sjt.id}`
                  if (sjtAble) {
                    sid = `2_${sjt.id}`
                  }

                  let cfi = $this.cfMap[sid]

                  let v = luckysheet.getLuckysheetfile()[cfi.idx].data[cr.row_focus][cr.column_focus]
                  if (v && v.v == "请输入题目总分") {
                    v.v = ""
                  }
                }
              },
              cellAllRenderBefore: function (data, sheet, ctx) {
                if (!$this.exam.isInit) {
                  $this.initNum()
                  $this.exam[`act_${$this.sjt.id}`] = true
                }
              },
              sheetActivate: function (index, isPivotInitial, isNewSheet) {
                let sjtId = index.substring(2)

                $this.sjt = $this.exam.sjts.find(q => q.id == sjtId)
                if (index[0] == 1) {
                  $this.sjtAble = false
                } else {
                  $this.sjtAble = true
                }
                $this.num = 0
                let nums = $this.exam[sjtId]
                if (nums) {
                  $this.num = nums.length
                }

                let act = $this.exam[`act_${sjtId}`]
                if (!act) {
                  $this.cgQuesNum($this.num)
                  $this.exam[`act_${sjtId}`] = true
                }
                if (index[0] == 1) {
                  let lsf = luckysheet.getLuckysheetfile()
                  for (let m = 0; m < $this.exam.sjts.length; m++) {
                    let sjt = $this.exam.sjts[m]
                    let cf1 = $this.cfMap[`1_${sjt.id}`]
                    let cf2 = $this.cfMap[`2_${sjt.id}`]

                    let cell0 = lsf[cf1.idx].data
                    let cell1 = lsf[cf2.idx].data

                    let stuMap = {}

                    let c1 = cell0[1]
                    let c2 = cell1[1]
                    if (c2) {
                      for (let c = 2; c < $this.num + 2; c++) {
                        if (c2[c] && c2[c].v && c2[c].v != "请输入题目总分" && parseFloat(c2[c].v) > 0) {
                          c1[c + 2].v = c2[c].v
                        }
                      }
                    }

                    for (let i = 2; i < cell1.length; i++) {
                      let d = cell1[i]
                      if (d[0] && d[0].v && d[1] && d[1].v) {
                        let sqs = []
                        let type = 0
                        let asentStr = ""
                        for (let c = 2; c < $this.num + 2; c++) {
                          let cv = ''
                          if (d[c]) {
                            if (d[c].v >= 0) {
                              cv = d[c].v
                            } else if (d[c].v == "缺考") {
                              cv = ''
                              type = 2
                            } else if (d[c].v == "未扫描") {
                              cv = ''
                              type = 1
                            } else {
                              cv = ''
                            }
                          } else {
                            cv = ''
                          }
                          if (cv >= 0) {
                            asentStr += cv
                          }
                          sqs.push(cv)
                        }
                        let k = `${d[0].v}_${d[1].v}`
                        if (stuMap[k]) {
                          stuMap[k] = 1
                        } else {
                          stuMap[k] = sqs
                        }
                        if (!asentStr) {
                          type = 2
                        }
                        if (type != 0) {
                          stuMap[`_${k}`] = type
                        }
                      }
                    }
                    if (Object.keys(stuMap) == 0) {
                      continue
                    }

                    for (let j = 2; j < cell0.length; j++) {
                      let cell = cell0[j]
                      if (cell[0] && cell[0].v) {
                        let k = `${cell[2].v}_${cell[3].v}`
                        let sqs2 = stuMap[k]
                        let type = stuMap[`_${k}`]
                        if (sqs2) {
                          if (sqs2 === 1) {
                            for (let c = 0; c < $this.num + 4; c++) {
                              if (!cell[c]) {
                                continue
                              }
                              cell[c].bg = $this.redBg
                              if (c >= 4) {
                                cell[c].v = ''
                              }
                            }
                          } else {
                            for (let c = 0; c < $this.num + 4; c++) {
                              if (c >= 4) {
                                if (type == 2) {
                                  cell[c] = { v: '缺考', vt: 0, ht: 0 }
                                } else if (type == 1) {
                                  cell[c] = { v: '未扫描', vt: 0, ht: 0 }
                                } else {
                                  cell[c] = { v: sqs2[c - 4], vt: 0, ht: 0 }
                                }
                              }
                              if (cell[c]) {
                                delete cell[c].bg
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            },
            data: celldata
          })
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.split-line {
  margin-left: 20px;
  border-left: 1px solid #ccc;
  width: 1px;
}
</style>

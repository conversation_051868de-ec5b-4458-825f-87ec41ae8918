<template>
  <div class="paperComment">
    <div style="display: flex; justify-content: center; margin-top: 20px"
      v-loading="listLoading && !quesList.length">
      <span v-if="quesList.length" class="paperTitle">
        <p>清华附中湾区学校八年级</p>
        <p>数学学科在线练习问卷</p>
      </span>
    </div>
    <!-- 得分率展示 -->
    <div style="padding-left: 30px;padding-top: 10px;" v-if="quesList.length">
      <div class="card-title clearfix">
        <span class="card--left pull-left">得分率</span>
      </div>
      <div class="card-content">
        <ul class="list-none content-template clearfix">
          <li v-for="index in 4" :key="index">
            [{{ showSetList[index - 1] }}~{{ showSetList[index]
            }}{{ index === 4 ? "]" : "）" }}
          </li>
        </ul>
        <!-- 题目概览 -->
        <el-collapse>
          <el-collapse-item name="1">
            <div class="content-ques" v-for="(item, index) in cardList" :key="index">
              <div class="big-title">
                {{ $sectionToChinese(item.index) }}、{{ item.title }}
              </div>
              <ul class="list-none option-ul clearfix">
                <li v-for="(subItem, subIndex) in item.list" :key="subIndex"
                  @click="getActiveClass(subItem)" :class="
                        subItem.colorGrade == 0
                          ? 'type0'
                          : subItem.colorGrade == 1
                          ? 'type1'
                          : subItem.colorGrade === 2
                          ? 'type2'
                          : 'type3'
                      ">
                  {{ subItem.totalIndex + 1 }}
                </li>
              </ul>
            </div>

          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <div v-loading="listLoading && !quesList.length">
      <div class="comment-container display_flex align-items_flex-start" style="padding: 30px"
        v-if="quesList.length">
        <!--题目列表-->
        <div class="comment-ques flex_1">
          <div class="ques-list" v-for="(item, index) in quesList" :key="index"
            :data-index="item.totalIndex + 1" @click="selectQuesItem(item)"
            :class="{ active: item.quesId === detailId }">
            <div class="ques-content" v-if="item.content">
              <div class="question_content">
                <div class="question_body" v-html="item.content.topic"></div>
              </div>
            </div>
            <div class="edit-block clearfix">
              <span style="margin-left: 22px">难度系数：{{
                  Number(item.gradeScoringRate * 100).toFixed(2)
                }}%</span>
              <span class="pull-right ques-btn" :class="!item.showDetail ? 'active' : ''"
                @click.stop="showQuesDetails(item)">题目详情<i :class="
                    !item.showDetail
                      ? 'el-icon-caret-top active'
                      : 'el-icon-caret-bottom'
                  "></i></span>
            </div>
            <!-- 题目详情 -->
            <div class="ques-detail" v-if="!item.showDetail && item.content">
              <!--答案-->
              <div class="answer_box display_flex align-items_flex-start">
                <strong class="flex_shrink_0">【答案】</strong>
                <div class="flex_1">
                  <div class="answer_content" v-if="item.content.quesType === 2">
                    {{
                      item.content.answer.split(",")[0] === "A"
                        ? "正确"
                        : "错误"
                    }}
                  </div>
                  <div class="answer_content" v-else v-html="item.content.answer"></div>
                </div>
              </div>
              <!--相关资源-->
              <div class="answer_box resourceList display_flex" v-if="item.resList.length">
                <strong class="flex_shrink_0">【知识点资源】</strong>
                <div class="
                    flex_1
                    display_flex
                    align-items_flex-start
                    flex-wrap_wrap
                  ">
                  <img class="answer-imgList" v-for="(imgItem, idx) in item.resList" :key="idx"
                    @click="showImg(item.resList, idx)" :src="
                      imgItem.thumbnailUrl
                        ? `https://fs.iclass30.com/${imgItem.thumbnailUrl}`
                        : require('@/assets/thumbnai.png')
                    " alt="" />
                </div>
              </div>
              <!--题目资源-->
              <div class="answer_box resourceList display_flex" v-if="item.quesResList.length">
                <strong class="flex_shrink_0" @click="showQuesRes(item.quesResList, 0)"
                  style="cursor: pointer;text-decoration: underline;text-decoration-color: blue;">
                  【<span style="color: blue;">几何画板互动作答</span>】
                </strong>
                <div class="
                    flex_1
                    display_flex
                    align-items_flex-start
                    flex-wrap_wrap
                  ">
                  <img class="answer-imgList" style="width:50px"
                    v-for="(imgItem, idx) in item.quesResList" :key="idx"
                    @click="showQuesRes(item.quesResList, idx)" :src="
                      imgItem.thumbnailUrl
                        ? `https://fs.iclass30.com/${imgItem.thumbnailUrl}`
                        : require('@/assets/thumb_ggb.png')
                    " alt="" />
                </div>
              </div>
              <div v-if="item.points && item.points.length">
                <strong>【考点】</strong>
                <span v-for="(it, idx) in item.points" :key="it.name + Math.random()">{{ it.name
                  }}{{ idx != item.points.length - 1 ? "," : "" }}</span>
              </div>
              <!--解析-->
              <div v-if="item.content.analysis"
                class="answer_box display_flex align-items_flex-start">
                <span class="flex_shrink_0"><strong>【解析】</strong></span>
                <div class="answer_content flex_1" v-html="'' + item.content.analysis"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- <div v-if="!quesList.length && !listLoading" class="nodata">
      <img :src="noResImg" alt="" />
      <p class="text-center">{{ errorMsg }}</p>
    </div> -->
    <mobile-spark-dialog :curIndex="curIndex" :list="sparkList" @closeDialog="closeDialog"
      v-if="sparkVisible"></mobile-spark-dialog>

    <!--得分率设置弹窗-->
    <el-dialog title="得分率设置" custom-class="setScoringDialog" :visible.sync="showSetDialog"
      width="720">
      <ul class="set-ul">
        <li v-for="(item, index) in textList" :key="index"
          class="display_flex justify-content_flex-center align-items_center">
          <div class="li-box display_flex align-items_center">
            <span class="color-block" :class="`color-block_${index}`"></span>
            <span class="text">{{ item }}：</span>
            <el-input onkeyup="value=value.replace(/[^0-9.]/g,'')"
              v-model="setList[index]"></el-input>
            <span class="line">-</span>
            <el-input onkeyup="value=value.replace(/[^0-9.]/g,'')"
              v-model="setList[index + 1]"></el-input>
          </div>
        </li>
      </ul>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showSetDialog = false">取 消</el-button>
        <el-button type="primary" @click="setScoring">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 查看资源弹窗 -->
    <video-mobile-player v-if="defaultPlayerResource.show" :src="defaultPlayerResource.url"
      :title="defaultPlayerResource.title"
      @close-dialog="defaultPlayerResource.show = false"></video-mobile-player>
  </div>
</template>

<script>
import { getTestPaperSub, getTestPaperComment, listExamStu } from '@/service/pexam';
import { chooseQuesSearch } from '@/service/pbook';
import mobileSparkDialog from '@/components/mobileSparkDialog.vue';
import { fixedNoZero } from '@/utils/common';
import VideoPlayer from '@/components/VideoPlayer/VideoPlayer.vue';
import VideoMobilePlayer from '@/components/VideoPlayer/VideoMobilePlayer.vue';

export default {
  name: 'paper-comment',
  components: {
    mobileSparkDialog,
    VideoPlayer,
    VideoMobilePlayer,
  },
  data() {
    return {
      sparkVisible: false,
      sparkList: [],
      cardHeight: 0,
      noResImg: require('@/assets/no-res.png'),
      // 选入的题目列表
      selectQuesList: [],
      showCommentPage: false,
      // 班级学科数据
      filterData: {},
      // 当前选中的题目id
      detailId: '',
      // 拖动中的标签
      dragPos: {
        top: 0,
        left: 0,
      },
      // 元素平移缓存值
      deltaCache: {
        x: 0,
        y: 0,
      },
      // 判断是否拖出边界时候需要取值的屏幕宽高和拖动块的宽高数据对象
      computeObj: {},
      setList: [0, 0.6, 0.7, 0.9, 1],
      showSetList: [0, 0.6, 0.7, 0.9, 1],
      textList: ['不及格', '及格', '中等', '优秀'],
      // 显示得分率设置弹窗
      showSetDialog: false,
      allowedSubjects: [],
      listLoading: false,
      showVtouch: false,
      // 答题卡数据
      cardList: [],
      // 题目列表
      quesList: [],
      showHeader: false,
      errorMsg: '未查询到该班级该次考试数据',
      stuList: [],
      quesMap: {},
      sourceMap: {},
      token:
        'l64SP0aMIv0B9E3wCdZpLPXIBj9yfnTf8qKfNMf33wi8Db0a9fHx3R/zRrAsaqu1xsAq81xZZTeI7xD3NCCT8A==',
      // 当前播放资源
      defaultPlayerResource: {
        url: '',
        title: '',
        show: false,
      },
      resList: [
        {
          id: 30,
          resourceUrl:
            'https://fs.iclass30.com/znbk/resources/static_resource/geogebra_project_play/embed.html?t=1672994369102&filename=https://fs.iclass30.com/aliba/resources/exam/ggb/894/30.ggb',
          dateCreated: '',
          dateModified: '',
          resourceName: '第30题',
        },
        {
          id: 25,
          resourceUrl:
            'https://fs.iclass30.com/znbk/resources/static_resource/geogebra_project_play/embed.html?t=1672994369102&filename=https://fs.iclass30.com/aliba/resources/exam/ggb/894/25.ggb',
          dateCreated: '',
          dateModified: '',
          resourceName: '第25题',
        },
        {
          id: 10,
          resourceUrl:
            'https://fs.iclass30.com/znbk/resources/static_resource/geogebra_project_play/embed.html?t=1672994369102&filename=https://fs.iclass30.com/aliba/resources/exam/ggb/894/10.ggb',
          dateCreated: '',
          dateModified: '',
          resourceName: '第10题',
        },
        {
          id: 13,
          resourceUrl:
            'https://fs.iclass30.com/znbk/resources/static_resource/geogebra_project_play/embed.html?t=1672994369102&filename=https://fs.iclass30.com/aliba/resources/exam/ggb/894/13.ggb',
          dateCreated: '',
          dateModified: '',
          resourceName: '第13题',
        },
        {
          id: 15,
          resourceUrl:
            'https://fs.iclass30.com/znbk/resources/static_resource/geogebra_project_play/embed.html?t=1672994369102&filename=https://fs.iclass30.com/aliba/resources/exam/ggb/894/15.ggb',
          dateCreated: '',
          dateModified: '',
          resourceName: '第15题',
        },
        {
          id: 19,
          resourceUrl:
            'https://fs.iclass30.com/znbk/resources/static_resource/geogebra_project_play/embed.html?t=1672994369102&filename=https://fs.iclass30.com/aliba/resources/exam/ggb/894/19.ggb',
          dateCreated: '',
          dateModified: '',
          resourceName: '第19题',
        },
        {
          id: 21,
          resourceUrl:
            'https://fs.iclass30.com/znbk/resources/static_resource/geogebra_project_play/embed.html?t=1672994369102&filename=https://fs.iclass30.com/aliba/resources/exam/ggb/894/21.ggb',
          dateCreated: '',
          dateModified: '',
          resourceName: '第21题',
        },
        {
          id: 23,
          resourceUrl:
            'https://fs.iclass30.com/znbk/resources/static_resource/geogebra_project_play/embed.html?t=1672994369102&filename=https://fs.iclass30.com/aliba/resources/exam/ggb/894/23.ggb',
          dateCreated: '',
          dateModified: '',
          resourceName: '第23题',
        },
        {
          id: 26,
          resourceUrl:
            'https://fs.iclass30.com/znbk/resources/static_resource/geogebra_project_play/embed.html?t=1672994369102&filename=https://fs.iclass30.com/aliba/resources/exam/ggb/894/26.ggb',
          dateCreated: '',
          dateModified: '',
          resourceName: '第26题',
        },
        {
          id: 29,
          resourceUrl:
            'https://fs.iclass30.com/znbk/resources/static_resource/geogebra_project_play/embed.html?t=1672994369102&filename=https://fs.iclass30.com/aliba/resources/exam/ggb/894/29.ggb',
          dateCreated: '',
          dateModified: '',
          resourceName: '第29题',
        },
        {
          id: 8,
          resourceUrl:
            'https://fs.iclass30.com/znbk/resources/static_resource/geogebra_project_play/embed.html?t=1672994369102&filename=https://fs.iclass30.com/aliba/resources/exam/ggb/894/8.ggb',
          dateCreated: '',
          dateModified: '',
          resourceName: '第8题',
        },
        {
          id: 9,
          resourceUrl:
            'https://fs.iclass30.com/znbk/resources/static_resource/geogebra_project_play/embed.html?t=1672994369102&filename=https://fs.iclass30.com/aliba/resources/exam/ggb/894/9.ggb',
          dateCreated: '',
          dateModified: '',
          resourceName: '第9题',
        },
      ],
    };
  },
  mounted() {
    console.log('newpapercomment mounted');
    this.getTestPaperComment();
  },
  activated() {
    console.log('papercomment activated');
    let timer = setTimeout(() => {
      this.showVtouch = true;
      clearTimeout(timer);
    }, 200);

    // 第一次进来需要加载
    if (this.$sessionSave.get('loadComment') || !this.quesList.length) {
      this.$sessionSave.set('loadComment', false);
      this.showHeader = false;
      this.$nextTick(() => {
        this.showHeader = true;
      });
    }
  },
  methods: {
    closeDialog() {
      this.sparkVisible = false;
    },
    showImg(list, index) {
      let copyList = this.$deepClone(list);
      copyList.forEach(item => {
        if (item.resourceUrl.startsWith('https://www.huohuaschool.com/')) {
          item.resourceUrl = item.resourceUrl.replace(
            /component\/(\d+)$/,
            `component/play/$1?token=${this.token}&appkey=Daite`
          );
        } else {
          let match = item.resourceUrl.match(
            /\/cooperation\/tencent\/let\/download_video.php\?id=([^&]*)/
          );
          if (match) {
            let id = match[1];
            let LLKT_URL = process.env.VUE_APP_LLKTURL;
            if (id) {
              item.resourceUrl = `${LLKT_URL}/llkt/${id}.mp4`;
            }
          }
        }
      });
      this.curIndex = index;
      this.sparkList = copyList;
      this.sparkVisible = true;
    },
    /**
     * @name: 查看题目资源
     */
    showQuesRes(list, index) {
      // this.defaultPlayerResource = {
      //   url: list[index].resourceUrl,
      //   title: list[index].resourceName,
      //   show: true,
      // };
      this.$router.push({
        path: '/mobilplayer',
        query: {
          url: list[index].resourceUrl,
        },
      });
    },
    // 锚点
    getActiveClass(item) {
      this.detailId = item.quesId;
      let jump = document.querySelectorAll('.ques-list');
      jump[item.totalIndex].scrollIntoView({
        block: 'start',
        behavior: 'smooth',
      });
      this.$nextTick(() => {
        this.$katexUpdate();
      });
    },
    // 初始化拖动块位置
    initPos() {
      this.showVtouch = true;
      this.$nextTick(() => {
        this.computeObj = {
          winWidth: document.body.clientWidth,
          winHeight: document.body.clientHeight,
          dragWidth: this.$refs.dragBlock.offsetWidth,
          dragHeight: this.$refs.dragBlock.offsetHeight,
        };
        this.deltaCache.x = this.dragPos.left;
        this.deltaCache.y = this.dragPos.top;
      });
    },
    // 点击题目亮显
    selectQuesItem(item) {
      this.detailId = item.quesId === this.detailId ? '' : item.quesId;
      this.$nextTick(() => {
        if (this.detailId) {
          this.$katexUpdate();
        }
      });
    },
    // 获取有数据的学科
    getTestPaperSub() {
      getTestPaperSub({
        examId: this.$sessionSave.get('reportDetail').examId,
      })
        .then(data => {
          if (data.data.length) {
            this.allowedSubjects = data.data.map(item => {
              return item.subjectId;
            });
            if (this.allowedSubjects.indexOf(this.filterData.subjectId) !== -1) {
              this.getTestPaperComment();
            }
          } else {
            this.listLoading = false;
          }
        })
        .catch(err => {
          this.listLoading = false;
        });
    },
    // 获取试卷
    getTestPaperComment(changeClass) {
      listExamStu({
        examId: this.$route.query.examId,
        classId: this.$route.query.classId,
        subjectId: this.$route.query.subjectId,
      }).then(data => {
        this.stuList = data.data;
      });

      getTestPaperComment({
        examId: this.$route.query.examId,
        classId: this.$route.query.classId,
        subjectId: this.$route.query.subjectId,
      })
        .then(data => {
          this.errorMsg = '未查询到该班级该次考试数据';
          // 切换了班级
          let ids = data.data.map(item => {
            this.sourceMap[item.quesId] = item.links;
            let quesId = item.quesId;
            if (item.links[0]) {
              quesId += `,${item.links[0]}`;
            }
            if (item.links[1]) {
              quesId += `,${item.links[1]}`;
            }
            return quesId;
          });
          if (!ids.length) return;
          // 获取题面
          this.chooseQuesSearch(data.data, ids);
        })
        .catch(err => {
          this.listLoading = false;
          this.quesList = [];
          this.cardList = [];
          this.errorMsg = '未查询到该班级该次考试数据';
        });
    },
    // 获取题目的题面
    chooseQuesSearch(totalList, ids) {
      this.listLoading = true;
      chooseQuesSearch({
        qIds: ids.join(','),
        subjectId: this.filterData.xfId,
        phaseId: this.filterData.phaseId,
      })
        .then(data => {
          data.data.forEach(v => {
            this.quesMap[v.id] = {
              content: v,
              points: (v.data && v.data.tag_ids) || [],
              type: v.data.type,
            };
          });

          for (let i = 0; i < totalList.length; i++) {
            let item = totalList[i];
            let ques = this.quesMap[item.quesId];
            item.content = ques.content;
            item.points = ques.points;
            item.type = ques.type;
            item.links = this.sourceMap[item.quesId];

            if (item.links[0]) {
              item.normal = this.quesMap[item.links[0]];
            }

            if (item.links[1]) {
              item.hard = this.quesMap[item.links[1]];
            }
          }

          this.handleData(totalList);

          this.$nextTick(() => {
            this.$katexUpdate();
            this.listLoading = false;
          });
        })
        .catch(err => {
          this.listLoading = false;
        });
    },
    // 处理数据
    handleData(allList) {
      let totalList = this.$deepClone(allList);
      let quesTypeList = [];
      this.quesList = [];
      this.cardList = [];
      totalList.length &&
        totalList.forEach((item, index) => {
          this.$set(item, 'colorGrade', this.getColorGrade(item.gradeScoringRate));
          this.$set(item, 'totalIndex', index);
          this.$set(item, 'quesResList', []);

          let quesType = this.$getQuesType(item.type);

          if (quesTypeList.indexOf(quesType) === -1) {
            quesTypeList.push(quesType);
            this.cardList.push({
              title: quesType,
              index: quesTypeList.length,
              list: [item],
            });
          } else {
            this.cardList.forEach(card => {
              if (card.title === quesType) {
                card.list.push(item);
              }
            });
          }
          this.quesList.push(item);
        });
      this.quesList.forEach(item => {
        this.resList.forEach(ite => {
          if (item.quesNumber + 1 == ite.id) {
            this.$set(item, 'quesResList', [ite]);
          }
        });
      });
    },
    // 获取题目得分率所在范围
    getColorGrade(num) {
      num = Number(num);
      let returnType = 0;
      if (num <= this.setList[4] && num >= this.setList[3]) {
        returnType = 3;
      } else if (num < this.setList[3] && num >= this.setList[2]) {
        returnType = 2;
      } else if (num < this.setList[2] && num >= this.setList[1]) {
        returnType = 1;
      } else if (num < this.setList[1] && num >= this.setList[0]) {
        returnType = 0;
      }
      return returnType;
    },
    // 选中所有题目
    selectAllComment() {
      this.selectQuesList = [];
      this.quesList.forEach(item => {
        this.$set(item, 'hasAdd', true);
      });
      this.quesList.forEach(item => {
        let nId = '',
          hId = '';
        if (item.normal && item.normal.content) {
          nId = item.normal.content.id;
        }

        if (item.hard && item.hard.content) {
          hId = item.hard.content.id;
        }
        this.selectQuesList.push({
          quesId: item.quesId,
          totalIndex: item.totalIndex,
          clsFullNum: item.clsFullNum,
          clsErrorNum: item.clsErrorNum,
          resList: item.resList,
          normalId: nId,
          hardId: hId,
        });
      });
    },
    // 进入讲评
    startComment(item) {
      let selectQuesList = [];
      // 讲解单题
      if (item.quesId) {
        let nId = '',
          hId = '';
        if (item.normal && item.normal.content) {
          nId = item.normal.content.id;
        }

        if (item.hard && item.hard.content) {
          hId = item.hard.content.id;
        }
        selectQuesList = [
          {
            quesId: item.quesId,
            totalIndex: item.totalIndex,
            clsFullNum: item.clsFullNum,
            clsErrorNum: item.clsErrorNum,
            resList: item.resList,
            normalId: nId,
            hardId: hId,
          },
        ];
      } else {
        selectQuesList = this.selectQuesList;
      }
      if (!selectQuesList.length) {
        this.$message.error('您还没有选入题目，请至少选择一题进入讲评');
        return;
      }
      selectQuesList.sort(this.$sortBy('totalIndex')).reverse();
      this.showCommentPage = true;
      this.showVtouch = false;
      this.$sessionSave.set('selectQuesList', selectQuesList);
      this.$router.push({
        path: '/home/<USER>/paperCommentDetail',
        query: {
          subjectId: this.filterData.xfId,
          phaseId: this.filterData.phaseId,
          examId: this.$route.query.examId,
          subjectRealId: this.$route.query.subjectRealId,
        },
      });
    },
    // 清空讲评题目
    emptyQues() {
      this.quesList.forEach(item => {
        this.$set(item, 'hasAdd', false);
      });
      this.selectQuesList = [];
    },
    // 题目加入、移除讲评
    selectQues(item) {
      this.$set(item, 'hasAdd', !item.hasAdd);
      let quesList = this.selectQuesList.filter(sub => {
        return item.quesId === sub.quesId;
      });
      if (!item.hasAdd && quesList.length) {
        let dataLength = this.selectQuesList.length;
        for (let i = 0; i < dataLength; i++) {
          let curItem = this.selectQuesList[i];
          if (curItem.quesId === item.quesId) {
            this.selectQuesList.splice(i, 1);
            return;
          }
        }
      } else {
        let nId = '',
          hId = '';
        if (item.normal && item.normal.content) {
          nId = item.normal.content.id;
        }

        if (item.hard && item.hard.content) {
          hId = item.hard.content.id;
        }
        this.selectQuesList.push({
          quesId: item.quesId,
          totalIndex: item.totalIndex,
          clsFullNum: item.clsFullNum,
          clsErrorNum: item.clsErrorNum,
          resList: item.resList,
          normalId: nId,
          hardId: hId,
        });
      }
    },
    // 展开收起题目详情
    showQuesDetails(item) {
      this.$set(item, 'variant', false);
      this.$set(item, 'ansDetail', false);

      this.$set(item, 'showDetail', !item.showDetail);
      this.$nextTick(() => {
        this.$katexUpdate();
      });
    },
    // 展开收起答题详情
    showAnsDetail(item) {
      this.$set(item, 'showDetail', false);
      this.$set(item, 'variant', false);
      this.$set(item, 'ansDetail', !item.ansDetail);

      if (item.ansMap) {
        return;
      }

      let fullScore = parseFloat(item.fullScore);

      item.ansMap = {};
      let k = '';
      let stus = JSON.parse(item.stus);
      for (let i = 0; i < stus.length; i++) {
        let stu = stus[i];
        let id = stu[0],
          score = parseFloat(stu[1]);

        let stu2 = this.stuList.find(q => q.id == id);

        if ('12578'.indexOf(item.content.data.type) >= 0) {
          if (!item.ansMap['正确']) {
            item.ansMap['正确'] = [];
            item.ansMap['错误'] = [];
          }

          k = '';
          if (option ==='A') {
            k = '正确';
          } else {
            k = '错误';
          }
        }

        if ('346'.indexOf(item.content.data.type) >= 0) {
          k = '';
          let fullScore9 = fixedNoZero(fullScore * 0.9, 1);
          let fullScore7 = fixedNoZero(fullScore * 0.7, 1);
          let fullScore6 = fixedNoZero(fullScore * 0.6, 1);
          if (!item.ansMap[`${fullScore}分`]) {
            item.ansMap[`${fullScore}分`] = [];
            item.ansMap[`[${fullScore9}分-${fullScore}分)`] = [];
            item.ansMap[`[${fullScore7}分-${fullScore9}分)`] = [];
            item.ansMap[`[${fullScore6}分-${fullScore7}分)`] = [];
            item.ansMap[`[0分-${fullScore6}分)`] = [];
          }
          if (score == fullScore) {
            k = `${fullScore}分`;
          } else if (score >= fullScore9) {
            k = `[${fullScore9}分-${fullScore}分)`;
          } else if (score >= fullScore7) {
            k = `[${fullScore7}分-${fullScore9}分)`;
          } else if (score >= fullScore6) {
            k = `[${fullScore6}分-${fullScore7}分)`;
          } else {
            k = `[0分-${fullScore6}分)`;
          }
        }
        if (!k) {
          continue;
        }
        if (!item.ansMap[k]) {
          item.ansMap[k] = [];
        }
        item.ansMap[k].push(stu2.name);
      }

      console.log(item.objs);
    },
    // 展开收起变式练习
    showVariant(item) {
      this.$set(item, 'showDetail', false);
      this.$set(item, 'variant', !item.variant);
      this.$set(item, 'ansDetail', false);
      this.$set(item, 'active', 'first');
      this.$nextTick(() => {
        this.$katexUpdate();
      });
    },
    // 设置得分率
    setScoring() {
      if (
        !String(this.setList[0]) ||
        !String(this.setList[1]) ||
        !String(this.setList[2]) ||
        !String(this.setList[3]) ||
        !String(this.setList[4])
      ) {
        this.$message.error('设置的值不能为空！');
        return;
      }
      if (Number(this.setList[4]) > 1) {
        this.$message.error('得分率不能大于1');
        return;
      }
      let dataLength = this.setList.length;
      for (let i = 0; i < dataLength; i++) {
        let item = Number(this.setList[i]);
        if (!(item >= 0 && item <= 1)) {
          this.$message({
            message: '设置的得分率只能取0到1之间的小数',
            type: 'error',
            duration: 1000,
          });
          return;
        }
        if (item > this.setList[i + 1]) {
          this.$message({
            message: '设置的区间左边的取值不能大于右边取值',
            type: 'error',
            duration: 2000,
          });
          return;
        }
      }
      this.showSetList = this.$deepClone(this.setList);
      this.showSetDialog = false;
      this.updateData();
    },
    updateData() {
      this.cardList.forEach(card => {
        card.list.forEach(list => {
          this.$set(list, 'colorGrade', this.getColorGrade(list.gradeScoringRate));
        });
      });
    },
    // 拖动开始
    elementPanStart(ev) {
      this.panResponse(ev.deltaX, ev.deltaY);
    },
    // 拖动中
    elementPanMove(ev) {
      this.panResponse(ev.deltaX, ev.deltaY);
    },
    // 拖动结束
    elementPanEnd(ev) {
      this.deltaCache.x = ev.deltaX + this.deltaCache.x;
      this.deltaCache.y = ev.deltaY + this.deltaCache.y;
    },
    // 响应元素平移
    panResponse(deltaX, deltaY) {
      let posX = deltaX + this.deltaCache.x,
        posY = deltaY + this.deltaCache.y;
      this.limitPos(posX, posY);
    },
    // 判断拖动块是否超出边界
    limitPos(posX, posY) {
      let winWidth = this.computeObj.winWidth,
        winHeight = this.computeObj.winHeight,
        dragWidth = this.computeObj.dragWidth,
        dragHeight = this.computeObj.dragHeight;
      posX = posX < 0 ? 0 : posX;
      posX = posX > winWidth - dragWidth ? winWidth - dragWidth : posX;
      posY = posY < 0 ? 0 : posY;
      posY = posY > winHeight - dragHeight ? winHeight - dragHeight : posY;
      this.dragPos.left = posX;
      this.dragPos.top = posY;
    },
  },
};
</script>

<style lang="scss" scoped>
.katex-display {
  font-size: 16px;
  display: inline-block;
  margin: 0;
}

.optionsClass {
  font-size: 16px;
}
.paperTitle {
  font-size: 26px;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}
.card-content {
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0 24px 20px 26px;
  .content-template {
    > li {
      position: relative;
      width: 50%;
      float: left;
      display: inline-block;
      padding-left: 34px;
      font-size: 16px;
      color: #3f4a54ff;
      margin-bottom: 20px;
      &:before {
        position: absolute;
        left: 0;
        top: 0;
        content: '';
        width: 24px;
        height: 24px;
        background: #f78989ff;
        border: 1px solid #f56c6cff;
        border-radius: 4px;
      }
      &:nth-child(2):before {
        background: #ffbb19ff;
        border: 1px solid #e8a400ff;
      }
      &:nth-child(3):before {
        background: #5f9effff;
        border: 1px solid #468fffff;
      }
      &:nth-child(4):before {
        background: #39ceb1ff;
        border: 1px solid #07c29dff;
      }
    }
  }
  .content-ques {
    // border-top: 1px dashed #e4e7ed;
    margin-bottom: 0px;
    .big-title {
      font-size: 16px;
      color: #4e5668ff;
      margin: 10px 0;
    }
    .option-ul {
      > li {
        color: #fff;
        text-align: center;
        line-height: 24px;
        width: 24px;
        height: 24px;
        background: #39ceb1;
        border: 1px solid #07c29d;
        border-radius: 4px;
        display: inline-block;
        margin-right: 20px;
        margin-bottom: 10px;
        cursor: pointer;
        &.type0 {
          background: #f78989ff;
          border: 1px solid #f56c6cff;
        }
        &.type1 {
          background: #ffbb19ff;
          border: 1px solid #e8a400ff;
        }
        &.type2 {
          background: #5f9effff;
          border: 1px solid #468fffff;
        }
        &.type3 {
          background: #39ceb1ff;
          border: 1px solid #07c29dff;
        }
      }
    }
  }
}
.card-title {
  line-height: 40px;
  padding: 0 20px 0 10px;
  .card--left {
    position: relative;
    color: #3f4a54ff;
    font-size: 16px;
    padding-left: 14px;
    &:before {
      content: '';
      position: absolute;
      display: inline-block;
      left: 0;
      width: 4px;
      height: 20px;
      background: #409eff;
      border-radius: 2px;
      top: 10px;
    }
  }
}
// .content-ques {
//   margin-bottom: 0px;
//   .big-title {
//     font-size: 16px;
//     color: #4e5668ff;
//     margin: 10px 0;
//   }
//   .option-ul {
//     > li {
//       color: #fff;
//       text-align: center;
//       line-height: 24px;
//       width: 24px;
//       height: 24px;
//       background: #39ceb1;
//       border: 1px solid #07c29d;
//       border-radius: 4px;
//       display: inline-block;
//       margin-right: 20px;
//       margin-bottom: 10px;
//       cursor: pointer;
//       &.type0 {
//         background: #f78989ff;
//         border: 1px solid #f56c6cff;
//       }
//       &.type1 {
//         background: #ffbb19ff;
//         border: 1px solid #e8a400ff;
//       }
//       &.type2 {
//         background: #5f9effff;
//         border: 1px solid #468fffff;
//       }
//       &.type3 {
//         background: #39ceb1ff;
//         border: 1px solid #07c29dff;
//       }
//     }
//   }
// }
.comment-container {
  .comment-ques {
    .ques-list {
      position: relative;
      border-bottom: 1px solid #e4e8eb;
      border-radius: 3px;
      margin-bottom: 20px;
      &:before {
        content: attr(data-index);
        position: absolute;
        left: -1px;
        top: -1px;
        width: 32px;
        height: 38px;
        background: url('../assets/flag_gray.png') center center no-repeat;
        font-size: 16px;
        color: #3f4a54ff;
        text-align: center;
        line-height: 30px;
      }
      .ques-content {
        min-height: 100px;
        padding: 40px 0 10px;
        line-height: 2em;
      }
      .edit-block {
        width: 100%;
        height: 48px;
        line-height: 48px;
        padding: 0 21px 0 11px;
        background: #f5f8fa;
        border-radius: 3px;
        font-size: 14px;
        color: #4e5668ff;
        .edit-btn {
          width: 80px;
          height: 32px;
          border-radius: 4px;
          padding: 0;
          line-height: 32px;
          color: #fff;
          margin-top: 8px !important;
          margin-left: 20px;
          &.add {
            background: #409effff;
          }
          &.hasAdd {
            background-color: #fff;
            border: 1px solid #468fffff;
            color: #468fffff;
          }
          &.comment {
            background: #f5a033;
          }
        }
        .ques-btn {
          position: relative;
          padding-right: 18px;
          margin-right: 28px;
          cursor: pointer;
          i {
            font-size: 22px;
            margin-left: 2px;
            position: absolute;
            top: 9px;
          }
          &.active {
            // color: #409effff;
            i {
              top: 13px;
            }
          }
          .el-icon-caret-top {
            color: #409effff;
          }
        }
        .el-icon-caret-bottom {
          color: #b6b8bfff;
          margin-top: 4px;
        }
      }
      .ques-detail {
        padding: 20px;
        > div {
          margin-bottom: 10px;
        }
        .resourceList {
          margin-top: 10px;
          margin-bottom: 0;
        }
        .answer-imgList {
          display: inline-block;
          width: 85px;
          height: 50px;
          margin-right: 10px;
          cursor: pointer;
          margin-bottom: 10px;
          border: 1px solid #cecece;
        }
        .sub {
          span {
            line-height: 28px;
          }
          .ans-btn {
            width: 155px;
            height: 28px;
          }
          .last {
            width: calc(100% - 110px);
          }
        }
        .obj {
          span {
            line-height: 28px;
          }
          .ans-btn {
            width: 100px;
            height: 28px;
          }
          .last {
            width: calc(100% - 110px);
          }
        }
        .ans {
          display: flex;
          padding: 4px 0;
          .ans-btn {
            display: inline-block;
            border: 1px solid #ccc;
            line-height: 26px;
            text-align: center;
          }

          .blue {
            color: #409effff;
            padding-left: 10px;
          }
        }
      }
      &.active,
      &:hover {
        // border: 1px solid #409eff;
        &:before {
          background: url('../assets/flag_active.png') center center no-repeat;
          color: #fff;
        }
      }
    }
  }
  .comment-card {
    width: 290px;
    margin-bottom: 20px;
    margin-left: 20px;
    border: 1px solid #e4e8eb;
    border-radius: 3px;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: #fff;

    position: -webkit-sticky;
    position: sticky;
    top: 0px;
    .card-title {
      line-height: 40px;
      padding: 0 20px 0 10px;
      .card--left {
        position: relative;
        color: #3f4a54ff;
        font-size: 16px;
        padding-left: 14px;
        &:before {
          content: '';
          position: absolute;
          display: inline-block;
          left: 0;
          width: 4px;
          height: 20px;
          background: #409eff;
          border-radius: 2px;
          top: 10px;
        }
      }
      .card--right {
        position: relative;
        color: #5f9effff;
        font-size: 14px;
        cursor: pointer;
        padding-right: 20px;
        &:after {
          position: absolute;
          right: 0;
          top: 14px;
          content: '';
          width: 14px;
          height: 14px;
          background: url('../assets/setIcon.png') center center no-repeat;
        }
      }
    }
    .card-content {
      height: 500px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 0 24px 20px 26px;
      .content-template {
        > li {
          position: relative;
          width: 50%;
          float: left;
          display: inline-block;
          padding-left: 34px;
          font-size: 16px;
          color: #3f4a54ff;
          margin-bottom: 20px;
          &:before {
            position: absolute;
            left: 0;
            top: 0;
            content: '';
            width: 24px;
            height: 24px;
            background: #f78989ff;
            border: 1px solid #f56c6cff;
            border-radius: 4px;
          }
          &:nth-child(2):before {
            background: #ffbb19ff;
            border: 1px solid #e8a400ff;
          }
          &:nth-child(3):before {
            background: #5f9effff;
            border: 1px solid #468fffff;
          }
          &:nth-child(4):before {
            background: #39ceb1ff;
            border: 1px solid #07c29dff;
          }
        }
      }
      .content-ques {
        border-top: 1px dashed #e4e7ed;
        margin-bottom: 0px;
        .big-title {
          font-size: 16px;
          color: #4e5668ff;
          margin: 10px 0;
        }
        .option-ul {
          > li {
            color: #fff;
            text-align: center;
            line-height: 24px;
            width: 24px;
            height: 24px;
            background: #39ceb1;
            border: 1px solid #07c29d;
            border-radius: 4px;
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
            cursor: pointer;
            &.type0 {
              background: #f78989ff;
              border: 1px solid #f56c6cff;
            }
            &.type1 {
              background: #ffbb19ff;
              border: 1px solid #e8a400ff;
            }
            &.type2 {
              background: #5f9effff;
              border: 1px solid #468fffff;
            }
            &.type3 {
              background: #39ceb1ff;
              border: 1px solid #07c29dff;
            }
          }
        }
      }
    }
  }
}

.addComment {
  position: fixed;
  right: 255px;
  bottom: 50px;
  background: url('../assets/add_comment.png') center center no-repeat;
  width: 100px;
  height: 270px;
  z-index: 1;
  padding: 50px 0 0;
  .comment-number {
    width: 100px;
    height: 100px;
    font-size: 48px;
    color: #994e2cff;
    text-align: center;
    line-height: 90px;
  }
  .comment-edit {
    > li {
      display: block;
      position: relative;
      height: 38px;
      line-height: 38px;
      color: #fff;
      font-size: 18px;
      text-align: center;
      font-weight: bold;
      cursor: pointer;
      &:after {
        content: '';
        position: absolute;
        width: 50px;
        height: 1px;
        background: #ffdbabff;
        left: 25px;
        bottom: 0;
      }
      &:last-child:after {
        content: none;
      }
    }
  }
}

.comment-page {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 111;
  background: #f1f5f8ff;
}

.question__detail {
  padding: 0px 20px;
  > div {
    margin: 10px 0;
    font-size: 14px;
  }
}
</style>
<style lang="scss">
.katex-display {
  margin: 0;
}

.setScoringDialog {
  .el-dialog__body {
    padding: 20px 0;
  }
  .set-ul {
    border-bottom: 1px solid #e6eaf0ff;
    padding-bottom: 20px;
    li {
      width: 100%;
      .li-box {
        margin: 15px auto;
      }
      .color-block {
        display: inline-block;
        width: 30px;
        height: 30px;
        border-radius: 4px;
        margin-right: 9px;
        background: #f78989;
        border: 1px solid #f56c6c;
      }
      .color-block_1 {
        background: #ffbb19ff;
        border: 1px solid #e8a400ff;
      }
      .color-block_2 {
        background: #5f9effff;
        border: 1px solid #468fffff;
      }
      .color-block_3 {
        background: #39ceb1ff;
        border: 1px solid #07c29dff;
      }
      .text {
        display: inline-block;
        width: 60px;
        height: 30px;
        line-height: 30px;
        text-align: left;
      }
      .el-input {
        width: 60px;
        height: 30px;
        text-align: center;
      }
      .line {
        display: inline-block;
        margin: 0 10px;
        color: #3f4a54ff;
      }
      .el-input__inner {
        width: 60px;
        height: 30px;
        line-height: 30px;
      }
    }
  }
}
</style>

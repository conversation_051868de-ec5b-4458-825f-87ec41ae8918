/**
 * 一起题库接口
 */
import ds from 'dom-serialize'
import request from '@/service/request'
import { getC30Token } from '@/service/ques'
import { localSave } from '@/utils'

const yiqiUrl = process.env.VUE_APP_17
const MATH_CDN = process.env.VUE_APP_MATH_CDN

const mathContentReg1 = /<span[^>]*>\\\(([\s\S]*?)\\\)<\/span>/g
const mathContentReg2 = /\\\(([\s\S]*?)\\\)/g
const mathContentReg3 = /\$([\s\S]*?)\$/g

function api(config) {
  config.baseURL = yiqiUrl
  return request(config)
}

// 转换所有题目里的公式
async function covertYiqQuesList(rows) {
  for (let i = 0; i < rows.length; i++) {
    const item = rows[i]
    await convertYiqQues(item.data)
  }
}
function formatFormula(str) {
  if(!str) {
    return ''
  }
  str = str.replace(/\\left\(\s*\\\)\s*\\\(\\right\)\s*/g, '\\left(\\qquad\\right)'); // 优化圆括号 \\left( \\)　\\(\\right)
  str = str.replace( /\\\(\s*\\\((.*?)\\\)\s*\\\)/g, '\\($1\\)'); // 匹配 \( \(   \) )\
  str = str.replace( /\\\((.*?)\\\)/g, '<span class="math-tex">\\($1\\)</span>'); // 匹配 \(( ))\
  str = str.replace( /\\\[{([\s\S]*?)}\\\]/g, '<span class="math-tex">\\($1\\)</span>'); // 匹配 \[{ }]\
  str = str.replace( /\\\[([\s\S]*?)\\\]/g, '<span class="math-tex">\\($1\\)</span>'); // 匹配 \[( )]\
  return str
}
// 转换题目里的公式
async function convertYiqQues(it) {
  it.html = convertYiqHtml(formatFormula(it.html))
}

function convertLatex2image(all, input) {
  input = input.replace(/&amp;/g, '&')
  input = input.replace(/&nbsp;/g, ' ')
  input = input.replace(/\\\\<br.?>/g, '\\\\')
  input = input.replace(/\\<br.?>/g, '')
  input = input.replace(/<br.?>/g, '')
  input = input.replace(/\\div/g, '÷')
  input = input.replace(/&lt;/g, '<')
  input = input.replace(/&gt;/g, '>')
  const res = MathJax.tex2svg(input);
  const svg = res.children[0];
  const str = ds(svg);
  return `<span alt="${input}" class="math-tex-svg">${str}</span>`
  // return `<img alt="${input}" class="math-tex-img" src="${MATH_CDN}/mathsvg/${encodeURIComponent(encodeURIComponent(input))}.svg"/>`
}

// 获取html里的公共转换成图片显示
function convertYiqHtml(html) {
  if (!html) {
    return ''
  }
  html = html.replace(/<a[^>]*>(.*?)<\/a>/g, '$1')
  html = html.replace(/<span[^>]*class="MathJax_Preview"[^>]*><\/span>/g, '')

  html = html.replace(mathContentReg1, convertLatex2image)
  html = html.replace(mathContentReg2, convertLatex2image)
  html = html.replace(mathContentReg3, convertLatex2image)

  return html
}

// 根据用户ID获取年份，省份，题型，等等属性
export function getSearchInfo(params) {
  return api({
    url: '/questionbank/questionWork/getSearchInfo',
    method: 'post',
    ...params
  })
}

export async function getSearchInfo2(data) {
  let userInfo = await getC30Token({subjectId: 0})
  return apiAddToken('/questionbank/questionWork/getSearchInfo', data, userInfo)
}
function apiAddToken(url, data, userInfo) {
  return request({
    baseURL: yiqiUrl,
    url,
    method: 'post',
    // 自定义的请求头
    headers: {
      post: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      token: userInfo.token || ''
    },
    data: {
      ...data,
      userId: userInfo.id
    }
  })
}

// 根据用户id获取知识点
export function getKnowLedgeInfos(params) {
  return api({
    url: '/questionbank/17question/getKnowLedgeInfos',
    method: 'post',
    ...params
  })
}

// 根据知识点获取题目
// export function getQuesByKnowLedge(params) {
//   return api({
//     url: '/questionbank/17question/getQuesByKnowLedge',
//     method: 'post',
//     ...params
//   })
// }
// 查询题库
export function getQuesByKnowLedge(data) {
  return new Promise(resolve => {
    api({
      url: '/questionbank/17question/getQuesByKnowLedge',
      method: 'post',
      ...data
    }).then(async result => {
      let res = JSON.parse(result);
      let data = res.data;
      if(res.data && res.data.items && res.data.items.length) {
        await covertYiqQuesList(res.data.items)
        resolve(res)
      } else {
        resolve({size: 0, items: []})
      }
    })
  })
}

// 根据用户和题目id获取单个题目详情(教师使用)
// export function getQuesDetails(params) {
//   return api({
//     url: '/questionbank/17question/getQuesDetails',
//     method: 'post',
//     ...params
//   })
// }
export function getQuesDetails(data) {
  return new Promise(resolve => {
    api({
      url: '/questionbank/17question/getQuesDetails',
      method: 'post',
      ...data
    }).then(async result => {
      let res = JSON.parse(result);
      if(res.data && res.data.data) {
        await convertYiqQues(res.data.data)
        resolve(res)
      } else {
        resolve({size: 0, items: []})
      }
    })
  })
}

// 根据用户id获取知识点
export function getQueTypeListBySubId(params) {
  return api({
    url: '/questionbank/17question/getQueTypeListBySubId',
    method: 'post',
    ...params
  })
}
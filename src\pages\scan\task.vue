<template>
  <div class="scan-task-container" v-loading="scanLoading" element-loading-text="扫描中..."
    element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.8)">
    <div class="exam-info">
      <el-page-header @back="backReport" content="" v-if="isEdit == 2"> </el-page-header>
      <div v-if="examInfo.workId">
        <p v-if="isEdit == 1">请确认本次考试信息</p>
        <p class="exam-name">{{ examInfo.title }}</p>
        <p><label>学校：</label>{{ examInfo.schoolName }}</p>
        <p><label>年级：</label>{{ examInfo.gradeName }}</p>
        <p><label>学科：</label>{{ examInfo.subjectName }}</p>
        <p><label>班级：</label>{{ examInfo.classNames }}</p>
        <p><label>计划人数：</label>{{ examInfo.totalStuNum }}</p>
        <p v-if="examInfo1"><label>已扫人数：</label>{{ (examInfo.realScanNum + examInfo1.realScanNum) }} (A卡{{
          examInfo.realScanNum }}人、B卡{{ examInfo1.realScanNum }}人)</p>
        <p v-else><label>已扫人数：</label>{{ examInfo.realScanNum }}</p>
        <p>
          <label>缺考人数：</label>{{ examInfo.noScanNum }}
          <el-button type="text" style="margin-left: 20px" @click="toNoScanNumPage">缺考名单
          </el-button>
        </p>
        <p v-if="examInfo.relateCardType == ICARD_STATE.default"><label>应扫张数：</label>{{ examInfo.totalPaperNum }}</p>
      </div>
      <div v-else>
        <p>暂无考试信息</p>
      </div>
      <!-- <template v-if="isEdit == 1">
        <div v-show="taskInfo" class="exam-change">
          若考试信息有误，请点击<a href="javascript:void(0)" @click="onEditExamDlg">修改考试信息</a>
        </div>
      </template> -->
    </div>
    <div class="task-info">
      <div>
        <span>扫描批次</span>
        <template v-if="examInfo?.originCardType == ICARD_TYPE.THIRD || examInfo1?.originCardType == ICARD_TYPE.THIRD">
          <template v-if="scanClientOnlineState">
            <template
              v-if="examInfo.relateCardType == ICARD_STATE.abCard || examInfo.relateCardType == ICARD_STATE.abPaperTwo">
              <Button v-if="examInfo?.originCardType == ICARD_TYPE.THIRD" class="scan-btn" type="success"
                @click="startScan(examInfo)">开始扫描A</Button>
              <Button v-if="examInfo1?.originCardType == ICARD_TYPE.THIRD" class="scan-btn" type="success"
                @click="startScan(examInfo1)">开始扫描B</Button>
            </template>
            <template v-else>
              <Button class="scan-btn" type="success" @click="startScan(examInfo)">开始扫描</Button>
            </template>
          </template>
          <Button v-else class="scan-btn" type="primary" @click="startApp">启动扫描端</Button>
        </template>

        <!-- <span>已连接设备：{{ onLineData.decviceCode }}</span> -->
        <div class="error-handle-btn">
          <template v-if="isShowBatchHanle">
            <Button type="success" :disabled="!multipleSelection.length"
              @click="isShowBatchShiftDialog=true">批量转移</Button>
            <Button style="margin-left: 15px;" type="default" @click="isShowBatchHanle=false">取消</Button>
          </template>
          <template v-else>
            <Button type="default" @click="getTaskList">刷新</Button>
            <Button style="margin-left: 15px;" type="primary" @click="onGogoHandleError">处理异常 </Button>
          </template>
          <el-dropdown @command="handlerCommand" class="task-dropdown-menu">
            <span>
              <i slot="reference" class="el-icon-more"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <template
                v-if="examInfo.relateCardType == ICARD_STATE.abPaper || examInfo.relateCardType == ICARD_STATE.abPaperTwo">
                <template v-if="examInfo.relateCardType">
                  <el-dropdown-item
                    :command="{func:'getSubjectScoreExcel',type:0,title:'A卷得分详情表'}">下载A卷得分详情</el-dropdown-item>
                  <el-dropdown-item
                    :command="{func:'getSubjectAnswerExcel',type:0,title:'A卷答案详情表'}">下载A卷答案详情</el-dropdown-item>
                </template>
                <template v-if="examInfo1.relateCardType">
                  <el-dropdown-item
                    :command="{func:'getSubjectScoreExcel',type:1,title:'B卷得分详情表'}">下载B卷得分详情</el-dropdown-item>
                  <el-dropdown-item
                    :command="{func:'getSubjectAnswerExcel',type:1,title:'B卷答案详情表'}">下载B卷答案详情</el-dropdown-item>
                </template>
              </template>
              <template v-else>
                <el-dropdown-item
                  :command="{func:'getSubjectScoreExcel',type:-1,title:'得分详情表'}">下载得分详情</el-dropdown-item>
                <el-dropdown-item
                  :command="{func:'getSubjectAnswerExcel',type:-1,title:'答案详情表'}">下载答案详情</el-dropdown-item>
              </template>
              <el-dropdown-item :command="{func:'batchHandle'}">批量操作</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>

      <div class="scan-task-list-box">
        <!--扫描批次数据-->
        <el-table ref="multipleTable" :data="taskList" v-loading="listLoading" :row-class-name="tableRowClassName"
          style="width: 100%" :height="elTableHeight" @selection-change="handleSelectionChange">
          <el-table-column v-if="isShowBatchHanle" type="selection" :selectable="checkSelectable" width="55">
          </el-table-column>
          <el-table-column prop="batchId" label="扫描批次" width="200"> </el-table-column>
          <el-table-column prop="scan_num" label="已扫描（页）" width="120"> </el-table-column>
          <el-table-column label="已识别（页）" prop="detect_num" width="120"> </el-table-column>

          <el-table-column prop="error_num" label="异常" width="120">
            <template slot-scope="scope">
              <span class="task_error_num" :class="{ error_no_zero: scope.row.error_num > 0 }">{{
                scope.row.error_num
                }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="status_str" label="状态">
            <template slot-scope="scope">
              <span>{{ scope.row.status_str }}</span>
              <span v-if="scope.row.repeat_num > 0" style="color: #ff0000;">（存在重复扫描，已被直接覆盖）</span>
            </template>
          </el-table-column>

          <el-table-column label="操作">
            <template slot-scope="scope">
              <div v-if="scope.row.id">
                <div v-show="scope.row.status === 1">
                  <span class="task_progress_label">识别进度：<i class="el-icon-loading"></i></span>
                  <el-progress :percentage="scope.row.percentage" :format="process_format"
                    class="task_progress"></el-progress>
                  <el-button size="mini" @click="cancelDetect(scope.$index, scope.row)">取消识别
                  </el-button>
                </div>
                <div v-show="scope.row.status !== 1">
                  <el-button size="mini" style="margin-right: 10px" @click="handleViewImage(scope.$index, scope.row)">查看详情
                  </el-button>
                  <el-dropdown>
                    <el-button size="mini">
                      更多<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item @click.native="onReDetectTask(scope.$index, scope.row)">重新识别</el-dropdown-item>
                      <el-dropdown-item @click.native="handleDeleteTask(scope.$index, scope.row)">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <el-dialog title="修改考试信息" :visible.sync="showEditDlg" width="600px">
      <p class="alter-tips">温馨提示：请确认考试信息，选择错误将无法生成考试分析报告</p>
      <el-form ref="form" label-width="120px">
        <el-form-item label="年级：">
          <el-select v-model="dlgExamInfo.gradeId" @change="onSelectGradeChange" clearable class="grade-select"
            placeholder="请选择">
            <el-option v-for="(item, index) in gradeList" :key="item.id" :label="item.grade_name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="学科：">
          <el-select v-model="dlgExamInfo.subjectId" @change="getSchoolExamList" clearable class="grade-select"
            placeholder="请选择">
            <el-option v-for="(item, index) in gradeSubjectList" :key="item.id" :label="item.subject_name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="考试名称：">
          <el-select v-model="dlgExamInfo.examId" filterable remote @change="onSelectExamChange" style="width: 300px"
            reserve-keyword :remote-method="queryExamList" placeholder="请选择">
            <el-option v-for="(item, index) in examList" :key="'subject_' + index" :label="item.title"
              :value="item.workId">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showEditDlg = false">取 消</el-button>
        <el-button type="primary" @click="onSaveExamInfo">确 定</el-button>
      </span>
    </el-dialog>

    <DetectSetting :visible.sync="reDetectDlg.show" :taskId="reDetectDlg.taskId" :preId="reDetectDlg.preId"
      @onTaskReDetect="onTaskReDetect">
    </DetectSetting>
    <date-expired-dialog :dialogVisible="isShowDialog" :tips="tips"></date-expired-dialog>
    <batch-shift-dialog v-if="isShowBatchShiftDialog" :schoolId="schoolId" :taskList="multipleSelection"
      :visible.sync="isShowBatchShiftDialog" @cancel="closeBatchShiftDialog" />
  </div>
</template>

<script>
import {
  getMultipleScanPaperWorkStat,
  getSchoolGrade,
  getSubjectsAPI,
  searchScanPaperWorkList,
} from '@/service/api';
import {
  deleteScanTask,
  getBatchListAPI,
  getScanTaskList,
  getTaskProcess,
  taskReDetect,
  updateTaskExam,
  cancelDetectAPI
} from '@/service/pexam';
import DetectSetting from '@/components/scan/DetectSetting';
import DateExpiredDialog from '@/components/TestItem/dateExpiredDialog.vue';
import batchShiftDialog from './modules/batchShiftDialog.vue';
import { getQueryString } from '@/utils';
import { generateUUID } from '@/utils/index.js';
import socket from '@/utils/socket';
import { ICARD_STATE,ICARD_TYPE } from '@/typings/card';

export default {
  name: 'scan-task',
  components: { DetectSetting, DateExpiredDialog,batchShiftDialog },
  data() {
    return {
      ICARD_STATE,
      ICARD_TYPE,
      noResImg: require('@/assets/no-res.png'),
      taskId: getQueryString('taskId'),
      examId: getQueryString('examId'),
      schoolId: getQueryString('schoolId'),
      cardType: getQueryString('cardType') || 0,
      originCardType: getQueryString('originCardType') || 0,
      roomId: generateUUID().replace(/-/g, ''),
      examInfo: {
        workId: '',
        title: '',
        schoolName: '',
        gradeName: '',
        subjectName: '',
        classNames: '',
        totalStuNum: 0,
        realScanNum: 0,
        totalPaperNum: 0,
      },
      examInfo1: {
        workId: '',
        title: '',
        schoolName: '',
        gradeName: '',
        subjectName: '',
        classNames: '',
        totalStuNum: 0,
        realScanNum: 0,
        totalPaperNum: 0,
      },
      taskInfo: null,
      processTaskList: [],
      getProcessId: 0,
      taskList: [],
      listLoading: true,
      showEditDlg: false,
      subjectList: [],
      examList: [],
      gradeList: [],
      dlgExamInfo: {
        gradeId: '',
        selectPhase: 0,
        subjectId: '',
        examId: '',
        loading: false,
      },
      statusTypes: ['扫描中', '识别中', '识别完成', '处理完成', '处理完成'],
      isEdit: 1,
      downLoadUrl: process.env.VUE_APP_KKLURL,
      elTableHeight: 'calc(100%)',
      reDetectDlg: {
        show: false,
        taskId: '',
        preId: '',
      },
      //上线
      onLineData: null,
      //客户端下线
      offLineData: null,
      //扫描仪链接状态
      scanClientOnlineState: false,
      isShowDialog: false,
      tips: '',
      startLoading: false,
      //客户端状态
      readyStatus: null,
      closeStatus: null,
      workingStatus: null,
      //设备号
      decviceCode: '',
      teacherId: getQueryString('teacherId') || '',
      taskIds: [],
      scanLoading: false,
      timer: null,
      isShowBatchHanle:false,
      multipleSelection:[],
      isShowBatchShiftDialog:false,//是否显示批量转移弹窗
    };
  },
  computed: {
    gradeSubjectList() {
      if (!this.dlgExamInfo.selectPhase) {
        return this.subjectList;
      }

      return this.subjectList.filter(it => it.phase === this.dlgExamInfo.selectPhase);
    },
  },
  watch: {
    decviceCode: {
      handler(newVal, oldVal) { },
      deep: true,
      immediate: true,
    },
    isShowBatchHanle:{
      handler(newVal, oldVal) {
        this.handleSelectionChange([])
      },
    }
  },
  async created() {
    await this.getInitInfo();
    //三方卡
    if (this.examInfo.originCardType == ICARD_TYPE.THIRD || this.examInfo1.originCardType == ICARD_TYPE.THIRD) {
      // 加入房间
      socket.joinRoom(this.roomId);
      // 监听客户端上线
      socket.addListener('scanClientOnline', this.scanClientOnline);
      // 监听客户端下线
      socket.addListener('scanClientOffline', this.scanClientOffline);
      // 监听客户端握手
      socket.addListener('clientReady', this.clientReady);
      socket.addListener('clientClose', this.clientClose);
      socket.addListener('clientWorking', this.clientWorking);
      socket.addListener('updateExamName', this.updateExamName);
      socket.addListener('deviceConnectioned', this.deviceConnectioned);
      socket.addListener('feederLoaded', this.feederLoaded);
      socket.addListener('scanStop', this.scanStop);
      this.decviceCode = this.$localSave.get('decviceCode');
      if (this.decviceCode) {
        socket.joinRoom(this.decviceCode);
        // this.sendShakeHands(this.decviceCode);
      }
    }
  },
  destroyed() {
    clearTimeout(this.timer);
    if (this.onLineData) {
      let data = {
        type: 'closeExamHandle',
        data: '{}',
      };
      let msg = {
        type: 'send2room',
        toUser: this.onLineData.decviceCode,
        msg: JSON.stringify(data),
      };
      socket.sendMessage(msg);
    }
    // socket.closeSocket();
  },
  mounted() {
    this.isEdit = this.$route.query.isEdit || 1;
    this.getTaskList();
  },
  methods: {
    checkSelectable(row, index) {
      // 返回 true 允许选中，false 禁止选中
      return row.status >= 2;
    },
    // 发送握手
    sendShakeHands(decviceCode) {
      let roomId = {
        roomId: this.roomId,
      };
      let data = {
        type: 'shakeHands',
        data: JSON.stringify(roomId),
      };
      let msg = {
        type: 'send2room',
        toUser: decviceCode,
        msg: JSON.stringify(data),
      };
      socket.sendMessage(msg);
    },
    // 监听客户端准备就绪
    clientReady(msg) {
      this.readyStatus = msg;
      console.log('客户端准备就绪', this.readyStatus);
      if (this.readyStatus != null) {
        this.reConnectionHandle(this.decviceCode);
      }
    },
    //客户端关闭
    clientClose(msg) {
      this.closeStatus = msg;
      console.log('客户端关闭', this.closeStatus);
      if (this.closeStatus != null) {
        this.startClient();
      }
    },
    //启动客户端
    startClient() {
      let data = {
        type: 'startClient',
        data: JSON.stringify({ type: 2 }),
      };
      let sendMsg = {
        type: 'send2room',
        toUser: this.decviceCode,
        msg: JSON.stringify(data),
      };
      socket.sendMessage(sendMsg);
    },
    // 监听客户端工作
    clientWorking(msg) {
      this.workingStatus = msg;
      console.log('客户端工作中', this.workingStatus);
    },

    // 监听客户端上线
    scanClientOnline(msg) {
      this.onLineData = JSON.parse(msg.data);
      this.$localSave.set('decviceCode', this.onLineData.decviceCode);
      socket.joinRoom(this.onLineData.decviceCode);
      // console.log('onLineData', this.onLineData);
    },
    //监听客户端下线
    scanClientOffline(msg) {
      this.offLineData = JSON.parse(msg.data);
      this.onLineData = null;
      this.scanClientOnlineState = false;
    },
    startApp() {
      this.startLoading = true;
      if (this.onLineData == null) {
        const url = `c30scan://?roomId=${this.roomId}&type=2`;
        window.location.href = url;
        this.startLoading = false;
        setTimeout(() => {
          if (this.onLineData == null) {
            this.isShowDialog = true;
            this.tips = '检测到尚未安装C30扫描，请安装后再进行扫描';
            setTimeout(() => {
              this.isShowDialog = false;
            }, 1500);
            return;
          }
        }, 12000);
      }
    },
    // 开始扫描
    startScan(examInfo) {
      this.startLoading = true;
      if (this.workingStatus != null) {
        this.isShowDialog = true;
        this.tips = '正在扫描，请稍后再试';
        setTimeout(() => {
          this.isShowDialog = false;
        }, 1500);
      } else {
        if (this.onLineData != null) {
          let examId = {
            examId: this.examId,
            title: examInfo.title,
            paperNo: examInfo.paperNo
          };
          //发送更新考试id消息
          let data = {
            type: 'updateExamId',
            data: JSON.stringify(examId),
          };
          let sendMsg = {
            type: 'send2room',
            toUser: this.onLineData.decviceCode,
            msg: JSON.stringify(data),
          };
          socket.sendMessage(sendMsg);
        }
        console.log('开始扫描', this.onLineData);
        let data = {
          type: 'startScan',
          data: '',
        };
        let msg = {
          type: 'send2room',
          toUser: this.onLineData.decviceCode,
          msg: JSON.stringify(data),
        };
        socket.sendMessage(msg);
        this.startLoading = false;
      }
    },
    /**
     * 监听更新考试信息，获取任务列表
     */
    updateExamName(msg) {
      setTimeout(() => {
        this.getTaskList();
      }, 1000);
    },
    //重新唤起客户端
    reConnectionHandle(decviceCode) {
      let roomId = {
        roomId: this.roomId,
        type: 2
      };
      let data = {
        type: 'reConnectionHandle',
        data: JSON.stringify(roomId),
      };
      let msg = {
        type: 'send2room',
        toUser: decviceCode,
        msg: JSON.stringify(data),
      };
      socket.sendMessage(msg);
    },
    deviceConnectioned(msg) {
      let data = JSON.parse(msg.data);
      if (data.status == "true") {
        this.scanClientOnlineState = true;
      } else {
        this.$message.error('扫描仪连接异常');
      }
    },
    feederLoaded(msg) {
      let data = JSON.parse(msg.data);
      if (!data.state) {
        this.$message.error('扫描仪未检测到纸张');
      } else {
        this.scanLoading = true;
      }
    },
    scanStop() {
      this.scanLoading = false;
      this.getTaskList();
    },
    async getInitInfo() {
      this.getGradeSubjectInfo(this.schoolId);
      if (!this.schoolId) {
        return;
      }
      let res = await getMultipleScanPaperWorkStat({
        workId: this.examId,
        schoolId: this.schoolId,
      })
      if (res.code === 1) {
        this.examInfo = this.$deepClone(res.data[0]);
        this.examInfo1 = this.$deepClone(res.data[1]);
        if(this.examInfo.relateCardType == ICARD_STATE.abCard){
          this.examInfo1.paperNo = this.examInfo1.paperNo1;
        }
      }
    },
    handleSelectionChange(val){
      this.multipleSelection = val;
      if(!val.length){
        this.$refs.multipleTable.clearSelection();
      }
    },
    closeBatchShiftDialog(isCancel){
      this.isShowBatchHanle = isCancel;
      this.isShowBatchShiftDialog = false;
      if(!isCancel){
        this.getTaskList();
      }
    },
    /***
     * @name:返回上一页
     */
    backReport() {
      this.$router.back();
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.id) {
        return '';
      }
      return 'sum_table_info';
    },
    process_format(percentage) {
      return `${percentage.toFixed(1)}%`;
    },
    /**
     * 获取列表list
     */
    getTaskList(no_process) {
      this.listLoading = true;
      // delete params.schoolId
      let queryFunction = getScanTaskList;
      this.processTaskList = [];
      if (this.examId) {
        queryFunction = getBatchListAPI;
      }
      queryFunction({ taskId: this.taskId, examId: this.examId, teacherId: this.teacherId })
        .then(res => {
          this.listLoading = false;
          let error_num = 0;
          let detect_num = 0;
          let scan_num = 0;
          res.data.forEach(it => {
            it.error_num = it.error_num || 0;
            it.detect_num = it.detect_num || 0;
            it.percentage = (it.detect_num / it.scan_num) * 100;
            if (it.status >= 0) {
              it.status_str = this.statusTypes[it.status];
            } else {
              it.status_str = '识别失败';
            }

            if (it.status === 0 || it.status === 1) {
              this.processTaskList.push(it);
            }
            error_num += it.error_num;
            detect_num += it.detect_num;
            scan_num += it.scan_num;
          });
          this.taskIds = res.data.map(item => item.id);
          this.taskList = res.data;
          if (this.taskList.length === 0) {
            return;
          }
          let taskInfo = this.taskList.find(it => it.id === this.taskId);
          if (!taskInfo) {
            taskInfo = this.taskList[0];
          }
          this.taskInfo = taskInfo;
          this.taskList.push({
            batchId: '合计',
            scan_num: scan_num,
            detect_num: detect_num,
            error_num: error_num,
          });

          this.getExamInfo();
          if (this.processTaskList.length > 0) {
            this.updateTaskProcess(this.getProcessId + 1);
          }
        })
        .catch(data => {
          this.listLoading = false;
          this.taskList = [];
          this.taskInfo = null;
        });
    },
    updateTaskProcess(id) {
      if (!this.processTaskList.length) return;
      if (id <= this.getProcessId) {
        return;
      }
      this.getProcessId = id;
      let processId = id;
      let id_list = this.processTaskList.map(it => it.id);

      getTaskProcess({
        taskIds: id_list.join(';'),
      }).then(res => {
        let result = res.data;
        let hasChange = false;

        let error_num = 0;
        let detect_num = 0;
        let scan_num = 0;

        this.taskList.forEach(it => {
          if (!it.id) return;
          let data = result[it.id];
          if (data) {
            let finish = parseInt(data.finish);
            let status = parseInt(data.status || '1');
            let error = parseInt(data.error);
            it.detect_num = finish;
            it.error_num = error;
            it.scan_num = parseInt(data.image_num) || it.detect_num;

            it.status = status;
          }

          if (it.scan_num > 0) {
            it.percentage = (it.detect_num / it.scan_num) * 100;
          } else {
            it.percentage = 0;
          }

          if (it.status >= 0) {
            it.status_str = this.statusTypes[it.status];
          } else {
            it.status_str = '识别失败';
          }
          error_num += it.error_num;
          detect_num += it.detect_num;
          scan_num += it.scan_num;

          if (data) {
            hasChange = hasChange || it.status !== 1;
          }
        });

        this.taskList.splice(this.taskList.length - 1, 1, {
          batchId: '合计',
          scan_num: scan_num,
          detect_num: detect_num,
          error_num: error_num,
        });

        if (hasChange) {
          this.getTaskList();
        } else {
          this.timer = setTimeout(() => {
            this.updateTaskProcess(processId + 1);
          }, 1000);
        }
      });
    },
    getExamInfo() {
      if (this.taskList.length === 0) return;
      let task = this.taskList[0];
      if (!this.schoolId) {
        this.schoolId = task.schoolId;
      }

      this.getGradeSubjectInfo(this.schoolId);

      if (!task.examId || !this.schoolId) {
        return;
      }
      getMultipleScanPaperWorkStat({
        workId: task.examId,
        schoolId: this.schoolId,
      }).then(res => {
        if (res.code === 1) {
          this.examInfo = res.data[0];
          this.examInfo1 = res.data[1];
          if(this.examInfo.relateCardType == ICARD_STATE.abCard){
          this.examInfo1.paperNo = this.examInfo1.paperNo1;
        }
        }
      });
    },

    /**
     * 获取年级学科列表
     * @param schoolId
     */
    getGradeSubjectInfo(schoolId) {
      if (!schoolId) return;
      // 获取年级列表
      if (this.gradeList.length === 0) {
        getSchoolGrade({
          schoolId: schoolId,
        }).then(res => {
          this.gradeList = res.data;
        });
      }

      if (this.subjectList.length === 0) {
        getSubjectsAPI({
          schoolId: schoolId,
        }).then(res => {
          this.subjectList = res.data;
          // console.log(res)
        });
      }
    },
    handleViewImage(index, item) {
      this.$router.push({
        path: '/scan/images',
        query: {
          examId: item.examId,
          taskId: item.id,
          paperNo: this.taskInfo.cardId,
          teacherId: this.teacherId,
        },
      });
    },
    async cancelDetect(index,item){
      await cancelDetectAPI({taskId: item.id})
      this.$message.success('取消成功');
      this.getTaskList();
    },
    onReDetectTask(index, item) {
      this.reDetectDlg.taskId = item.id;
      this.reDetectDlg.preId = item.preId;
      this.reDetectDlg.show = true;
    },
    onTaskReDetect(taskId) {
      let item = this.taskList.find(it => it.id === taskId);
      if (!item) {
        this.$message.error('批次不存在');
        return;
      }
      item.status = 1;
      this.$message.success('正在重新识别');
      this.processTaskList.push(item);
      this.updateTaskProcess(this.getProcessId + 1);
    },
    toReDetectTask(item) {
      item.percentage = 0;
      taskReDetect({
        taskId: item.id,
      })
        .then(res => {
          item.status = 1;
          this.$message.success('正在重新识别');
          this.processTaskList.push(item);
          this.updateTaskProcess(this.getProcessId + 1);
        })
        .catch(data => {
        });
    },
    /**
     * 删除任务
     * @param index
     * @param item
     */
    handleDeleteTask(index, item) {
      this.$confirm('删除后数据不可恢复，是否删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.toDeleteTask(item);
        })
        .catch(() => {
          // 用户点击了取消，不做任何事情
        });
    },
    toDeleteTask(item) {
      deleteScanTask({ taskId: item.id }).then(res => {
        if (res.code === 1) {
          this.$message.success('删除成功');
          this.getTaskList();
        } else {
          this.$message.error(res.msg || '删除失败');
        }
      });
    },
    onSelectGradeChange(gradeId) {
      let phase = 0;
      if (gradeId >= 1 && gradeId <= 6) {
        phase = 1;
      } else if (gradeId >= 7 && gradeId <= 9) {
        phase = 2;
      } else if (gradeId >= 10 && gradeId <= 12) {
        phase = 3;
      }
      this.dlgExamInfo.selectPhase = phase;
      this.getSchoolExamList();
    },
    /**
     * 获取考试列表
     */
    getSchoolExamList() {
      this.queryExamList(this.dlgExamInfo.query);
    },
    queryExamList(query) {
      this.dlgExamInfo.query = query;
      if (this.dlgExamInfo.loading) return;
      if (!this.dlgExamInfo.gradeId || !this.dlgExamInfo.subjectId) {
        // return
      }
      this.dlgExamInfo.loading = true;
      searchScanPaperWorkList({
        schoolId: this.schoolId,
        keyWord: query,
        gradeId: this.dlgExamInfo.gradeId,
        subjectId: this.dlgExamInfo.subjectId,
      })
        .then(res => {
          this.dlgExamInfo.loading = false;
          this.examList = res.data.rows;

          if (this.dlgExamInfo.examId) {
            let examInfo = this.examList.find(it => it.workId === this.dlgExamInfo.examId);
            if (!examInfo) {
              this.dlgExamInfo.examId = '';
            }
          }

          // 查询条件变化，再次查询
          if (query !== this.dlgExamInfo.query) {
            this.queryExamList(this.dlgExamInfo.query);
          }
        })
        .catch(data => {
          this.dlgExamInfo.loading = false;
        });
    },
    onEditExamDlg() {
      this.showEditDlg = true;
    },
    onSaveExamInfo() {
      if (!this.dlgExamInfo.examId) {
        this.$message.error('没有选择考试');
        return;
      }
      if (this.dlgExamInfo.examId === this.examInfo.workId) {
        this.$message.error('当前考试信息一致，不需要修改');
        return;
      }

      updateTaskExam({
        taskId: this.taskId,
        examId: this.dlgExamInfo.examId,
      }).then(res => {
        this.$message.success('更新成功');
        this.getTaskList();
      });
    },
    onSelectExamChange(item) {
      console.log(this.dlgExamInfo.examId);
    },
    onGogoHandleError() {
      if (!this.examInfo.workId) {
        this.$message.error('没有获取到考试信息');
        return;
      }
      if (!this.taskInfo) {
        this.$message.error('没有获取到扫描信息');
        return;
      }

      this.$router.push({
        path: '/scan/errornew',
        query: {
          examId: this.examInfo.workId,
          paperNo: this.taskInfo.cardId,
          examName: this.examInfo.title,
          schoolId: this.taskInfo.schoolId,
          personBookId: this.examInfo.workId,
          subjectName: this.examInfo.subjectName,
          taskId: this.teacherId != '' ? this.taskIds.join(',') : '',
          teacherId: this.teacherId,
        },
      });
    },
    /**
     * @name:跳转到未扫名单列表
     */
    toNoScanNumPage() {
      this.$router.push({
        path: '/scan_not',
        query: {
          workId: this.examInfo.workId,
          schoolId: this.taskInfo?.schoolId || this.schoolId,
          userId: this.teacherId,
        },
      });
    },
    /**
     * @name： 下载得分、答案详情
     */
    handlerCommand(comand) {
      if(comand.func == "batchHandle"){
        this.isShowBatchHanle = true;
      }else{
        let examId = this.examId || this.dlgExamInfo.examId || this.examInfo.workId;
        let title = `${this.examInfo.title}（${this.examInfo.subjectName}）${comand.title}`;
        let url = this.downLoadUrl + `/pexam/export/${comand.func}?examId=${examId}&fileName=${title}&abTestPaperType=${comand.type}`;
        window.open(url, 'blank');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.scan-task-container {
  display: flex;
  height: 100%;

  .exam-info {
    background: #f2f2f2;
    // width: 340px;
    width: 20%;
    height: 100vh;
    overflow: scroll;
    color: #000;
    padding: 30px 0 0 30px;

    p {
      margin: 10px;
    }

    .exam-name {
      font-weight: bold;
    }

    .exam-change {
      // position: absolute;
      // bottom: 100px;
      margin: 20px 10px 10px;
    }
  }

  .scan-btn {
    margin-left: 15px;
  }

  .task-info {
    padding: 30px 20px;
    flex: 1;
    width: 80%;
    height: 100%;

    .error-handle-btn {
      float: right;

      .btn {
        margin-left: 10px;
      }
    }

    .scan-task-list-box {
      margin-top: 20px;
      border: solid #f2f2f2;
      height: calc(100% - 15px);

      .task_progress_label {
        color: #3a5eff;
      }

      .task_progress {
        width: 200px;
        display: inline-block;

        ::v-deep .el-progress-bar {
          padding-right: 65px;
          margin-right: -70px;
        }
      }
    }
  }

  .nodata {
    width: 100%;
    height: auto;

    /*background : url("../assets/no-res.png") center center no-repeat;*/
    img {
      display: block;
      margin: 0 auto;
    }
  }

  .alter-tips {
    color: #ff4d51;
    margin-left: 40px;
    padding: 0 20px 20px;
  }

  .error_no_zero {
    color: red;
  }

  ::v-deep .el-table__row.sum_table_info {
    background: #f5f7fa;
  }
}

.task-dropdown-menu {
  margin-left: 10px;
  transform: rotate(90deg);
}
</style>
<style lang="scss">
.scan-task-list-box {
  .el-table {
    .el-table__body {

      // 吸底
      .sum_table_info.el-table__row {
        display: table-row;
        position: sticky;
        bottom: 0;
      }
    }
  }
}
</style>

.upload-ml {
    position         : fixed;
    width            : 100%;
    height           : 100%;
    z-index          : 99;
    background-color : #000;
    opacity          : .2;
    left             : 0;
    top              : 74px;
    right            : 0;
    bottom           : 0
}

.quesCard-upload-div {
    display : block
}

.quesCard-upload-div .upload-main {
    padding : 25px 85px 20px
}

.quesCard-upload-div .upload-main-search {
    position    : relative;
    height      : 40px;
    line-height : 40px
}

.quesCard-upload-div .download-paper {
    /*margin : 0*/
    padding-left : 20px;
    margin-right : 10px;
    color        : #008DEA !important;
    background   : url(../../assets/questionCard/icon_download_org.png) left no-repeat;
    cursor       : pointer
}

#upload-file {
    position   : absolute !important;
    right      : 0;
    width      : 120px;
    text-align : center;
    color      : #fff;
    background : #008DEA;
    cursor     : pointer
}

.quesCard-upload-div .upload-main-search label {
    position      : absolute;
    left          : -378px;
    width         : 380px;
    height        : 40px;
    border        : 1px solid #008DEA;
    border-radius : 3px;
    border-right  : none;
    color         : rgba(78, 86, 104, 1);
    font          : 17px/38px tahoma, 微软雅黑, \5b8b\4f53;
    text-indent   : 20px;
    text-align    : left;
    white-space   : nowrap;
    overflow      : hidden;
    text-overflow : ellipsis;
    cursor        : pointer
}

.quesCard-upload-div .limit-info {
    font-size : 14px;
    color     : rgba(165, 172, 189, 1)
}

.quesCard-upload-div .upload-no {
    right : 200px !important
}

.quesCard-upload-div .upload-no:hover {
    background-color : #ebecef
}

.quesCard-upload-div .upload-ok {
    background : #008DEA;
    color      : #fff !important
}

.quesCard-upload-div .upload-ok:hover {
    background : #008DEA !important
}

.quesCard-upload-div .upload-main-top {
    padding-bottom : 10px
}

.quesCard-upload-div .upload-main-top span {
    color     : rgba(165, 172, 189, 1);
    font-size : 14px
}

.quesCard-upload-div .upload-main-bottom {
    position : relative;
    padding  : 25px 0
}

.quesCard-upload-div .upload-main-bottom > li {
    float         : left;
    width         : 50%;
    margin-bottom : 25px
}

.quesCard-upload-div .upload-main-bottom > li > span {
    margin-right : 10px
}

.quesCard-upload-div .upload-main-bottom > li:nth-child(even) {
    text-align : right
}

.quesCard-upload-div .upload-main-bottom > li:nth-child(2) {
    pointer-events : none !important
}

.quesCard-upload-div .upload-main-bottom > li:nth-child(2) .select2-selection__arrow {
    display : none !important
}

.quesCard-upload-div .upload-main-bottom > li .sclect-wrap {
    width      : 160px;
    display    : inline-block;
    text-align : left
}

.quesCard-upload-div .progbar {
    width            : 0%;
    height           : 2px;
    background-color : #72d628
}

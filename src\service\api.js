import { version } from '@iclass/element-ui';
import * as httpApi from './index';
import { sessionSave } from '../utils';

const BASE_API = process.env.VUE_APP_BASE_API;
const KKL_URL = process.env.VUE_APP_KKLURL;

const API = {
  GET: function (url, params, baseUrl) {
    return httpApi.GET(url, params, baseUrl || BASE_API);
  },
  POST: function (url, params, baseUrl) {
    return httpApi.POST(url, params, baseUrl || BASE_API);
  },
  POSTJson: function (url, params, baseUrl) {
    return httpApi.POSTJson(url, params, baseUrl || BASE_API);
  },
};

/**
 * @name: 批量保存作业微课数据
 * @param {params} 接口参数
 *    @teaId 教师id
 *    @teaName 教师姓名
 *    @workId 作业id
 *    @stuId 学生id(公共微课可以不传)
 *    @stuName 学生姓名(公共微课可以不传)
 *    @cardQuestionLevel 答题卡题目级别1:大题 2:小题
 *    @cardQuestionId 答题卡题目id(作业的大小题id)
 *    @questionId 题库题目id(如果是答题卡作业,该参数和cardQuestionId保持一致)
 *    @questionName 题目名称
 *    @microCourseListJson 微课集合json(示例:[{"title(微课名称)":"","microCourseUrl(微课地址)":"","duration(微课时长(单位秒))":0,"fileSize(微课大小)":0}])
 *    @targetType 目标类型1:公共微课 2:学生微课
 */
export const BatchSaveHomeWorkMicroCourse = params => {
  return API.POST('/homework/teahomework/batchSaveHomeWorkMicroCourse', params);
};
export const SaveHomeWorkMicroCourse = params => {
  return API.POST('/homework/teahomework/saveHomeWorkMicroCourse', params);
};

/**
 * @description: 添加资源到网盘
 * @param {*} params
 * @return {*}
 */
export const BatchInsertResource = params => {
  return API.POST('/resource/center/batchInsertResource', params);
};

/**
 * @description: 获取微课详情
 * @param {*} params
 * @return {*}
 */
export const GetWeikeDetails = params => {
  return API.GET('/online/courseWares/details', params);
};

/**
 * @name: 编辑微课
 * @param {params} 接口参数
 *    @id 微课id
 *    @stuId 学生id(公共微课可以不传)
 *    @stuName 学生姓名(公共微课可以不传)
 *    @title 微课名称
 *    @beforeTargetType 编辑前的目标类型(1:公共微课 2:学生微课)
 *    @afterTargetType 编辑后的目标类型(1:公共微课 2:学生微课)
 */
export const EditQuesWk = params => {
  return API.POST('/homework/teahomework/editHomeWorkMicroCourse', params);
};

/**
 * @name: 删除微课
 * @param {params} 接口参数
 *    @id 微课id
 *    @targetType 目标类型(1:公共微课 2:学生微课)
 */
export const DeleteQuesWk = params => {
  return API.POST('/homework/teaHomework/deleteQuesMicroCourse', params);
};

// 获取资源列表
export const GetNewResourceList = params => {
  params.subCatalogRes = 1;
  return API.GET('/resource/center/getResourceList', params);
};

// 获取精准教学中学段
export const getSchoolById = params => {
  return httpApi.GET('/userMgr/baseschool/getSchoolById', params);
};

// 获取教师所带年级和班级
export const getTeacherClassList = params => {
  return httpApi.GET('/userMgr/baseclass/getTeacherClassList', params);
};

// 获取年级主任所在年级
export const getGradeListByRole = params => {
  return API.GET('/website/teaching/getGradeListByRole', params);
};

// 根据用户和年级获取班级 获取最高权限（获取年级下的班级)
export const getClassListByRole = params => {
  return API.GET('/website/teaching/getClassListByRole', params);
};

// 根据用户id获取用户token
export const getUserInfoToToken = params => {
  return API.GET('/userMgr/baselogin/getUserInfoToPersonalityTest', params);
};

// 根据入学年份获取班级
export const classList = (year, phase, classType = 1, options = { keyWord: '' }) => {
  let login = JSON.parse(sessionStorage.loginInfo);
  let sch = JSON.parse(sessionStorage.schoolInfo);
  let schId = login.schoolid;
  if (login.user_type == 5) {
    schId = sch.schoolId || sch.id;
  }

  let params = {
    year: year,
    schoolId: schId,
    classType: classType,
    phase: phase,
    keyWord: options.keyWord,
    limit: -1,
  };

  return API.GET('/userMgr/baseclass/classList', params);
};
//根据多个班级ID获取班级信息
export const getClassInfoByClassIds = ids => {
  let login = JSON.parse(sessionStorage.loginInfo);
  let sch = JSON.parse(sessionStorage.schoolInfo);
  let schId = login.schoolid;
  if (login.user_type == 5) {
    schId = sch.schoolId || sch.id;
  }

  let params = {
    schoolId: schId,
    ids: ids,
  };

  return API.GET('/userMgr/baseclass/getClassInfoByClassIds', params);
};
//根据班级id获取班级学生
export const getStuListByClassId = params => {
  return API.GET('/userMgr/baseclass/getStuListByClassId', params);
};

//根据学校id获取标签
export const getSchoolTagList = params => {
  return API.GET('/userMgr/baseSchoolTag/getSchoolTagList', params);
};

//获取用户详情
export const getUserInfoToPersonalityTest = cb => {
  return new Promise((resolve, reject) => {
    function getPms() {
      let login = JSON.parse(sessionStorage.loginInfo);
      let sch = JSON.parse(sessionStorage.schoolInfo);
      let schId = login.schoolid;
      // 5: 运营 6: 区域
      if (login.user_type == 5 || login.user_type == 6) {
        schId = sch.schoolId;
      }
      return {
        userId: login.id,
        schoolId: schId,
      };
    }

    function init(params, cb) {
      let subjectList = JSON.parse(localStorage.SUBJECT_LIST || '[]');
      let subjectMap = {};
      let phaseMap = {};

      let grdMap = {
        一年级: 1,
        二年级: 2,
        三年级: 3,
        四年级: 4,
        五年级: 5,
        六年级: 6,
        七年级: 7,
        八年级: 8,
        九年级: 9,
        高一: 10,
        高二: 11,
        高三: 12,
      };

      subjectList.forEach(function (v) {
        let phase = v.phaseId - 2;
        if (phaseMap[phase]) {
          phaseMap[phase].push(v);
        } else {
          phaseMap[phase] = [v];
        }

        subjectMap[v.id] = v;
      });

      return new Promise((resolve, reject) => {
        params.showGraduateClass = 1;
        httpApi.GET('/userMgr/baselogin/getUserInfoToPersonalityTest', params).then(data => {
          //schoolInfo里的phase gradeList，schoolRoleList，leaderClassList，substituteClassList
          let res = data.data;

          let schInfo = res.schoolInfo;

          let ret = {
            schGrdList: [],
            schSubList: [],
            userGrdList: [],
            userSubList: [],
            userPSubMap: {},
          };

          function getGrd(name, phase, year) {
            return {
              id: grdMap[name],
              name: name,
              phaseId: Number(phase) + 2,
              year: year,
            };
          }

          if (schInfo) {
            if (schInfo.gradeList) {
              schInfo.gradeList.forEach(v =>
                ret.schGrdList.push({ ...getGrd(v.gradeName, v.phase, v.year), systemCode: v.systemCode })
              );
            }
            if (schInfo.phase) {
              for (let phase in phaseMap) {
                if (schInfo.phase.indexOf(phase) > -1) {
                  ret.schSubList.push(...phaseMap[phase]);
                }
              }
            }
          }

          let userPGrdMap = {};

          //运营校管
          if (res.adminType == 2 || res.userType == 5) {
            ret.userGrdList = ret.schGrdList;
            ret.userSubList.push(...ret.schSubList);
          } else {
            //班主任
            let leaderCls = res.leaderClassList;
            if (leaderCls) {
              leaderCls.forEach(function (v) {
                ret.userGrdList.push(getGrd(v.gradeName, v.phase, v.year));
                ret.userSubList.push(...phaseMap[v.phase]);
              });
            }
            let schRoles = res.schoolRoleList;

            if (schRoles) {
              schRoles.forEach(function (v) {
                let grd = getGrd(v.gradeName, v.phase, v.year);
                //年级主任
                if (v.roleType == 1) {
                  ret.userGrdList.push(grd);
                  ret.userSubList.push(...phaseMap[v.phase]);
                }

                //备课组长
                if (v.roleType == 2) {
                  let s = subjectMap[v.subjectId];
                  if (s) {
                    userPGrdMap[grd.id] = grd;
                    if (ret.userPSubMap[grd.id]) {
                      if (ret.userPSubMap[grd.id].indexOf(s) == -1) {
                        ret.userPSubMap[grd.id].push(s);
                      }
                    } else {
                      ret.userPSubMap[grd.id] = [s];
                    }
                    ret.userSubList.push(s);
                  }
                }
              });
            }

            //授课教师
            let substituteCls = res.substituteClassList;
            if (substituteCls) {
              substituteCls.forEach(function (v) {
                let grd = getGrd(v.gradeName, v.phase, v.year);
                let s = subjectMap[v.subjectId];
                if (s) {
                  userPGrdMap[grd.id] = grd;
                  if (ret.userPSubMap[grd.id]) {
                    if (ret.userPSubMap[grd.id].indexOf(s) == -1) {
                      ret.userPSubMap[grd.id].push(s);
                    }
                  } else {
                    ret.userPSubMap[grd.id] = [s];
                  }
                  ret.userSubList.push(s);
                }
              });
            }
          }

          ret.substituteClassList = res.substituteClassList || [];
          ret.leaderClassList = res.leaderClassList || [];
          sessionSave.set('substituteClassList', res.substituteClassList);
          sessionSave.set('leaderClassList', res.leaderClassList);

          // if (cb) {
          function unique(arr, k) {
            const res = new Map();
            return arr.filter(a => !res.has(a[k]) && res.set(a[k], 1));
          }

          ret.userGrdList.forEach(function (v) {
            delete ret.userPSubMap[v.id];
            delete userPGrdMap[v.id];
          });

          for (let k in userPGrdMap) {
            ret.userGrdList.push(userPGrdMap[k]);
          }
          ret.userGrdList = ret.userGrdList.filter(item => {
            return item.id;
          });
          ret.userGrdList = unique(ret.userGrdList, 'name');
          ret.userSubList = unique(ret.userSubList, 'id');
          ret.schSubList = ret.schSubList.sort((a, b) =>
            a.phaseId == b.phaseId ? a.id - b.id : a.phaseId - b.phaseId
          );
          ret.userSubList = ret.userSubList.sort((a, b) =>
            a.phaseId == b.phaseId ? a.id - b.id : a.phaseId - b.phaseId
          );
          ret.schGrdList = ret.schGrdList.sort((a, b) => a.id - b.id);
          ret.userGrdList = ret.userGrdList.sort((a, b) => a.id - b.id);
          ret.subjectId = res.subjectid;
          if (cb) {
            cb(ret);
            return;
          }
          resolve(ret);
          return ret;
        });
      });
    }
    let time = 0;
    let schItv = setInterval(async () => {
      let pms = getPms();
      if (time >= 2000) {
        clearInterval(schItv);
      }
      if (pms.schoolId) {
        clearInterval(schItv);
        resolve(init(pms, cb));
      }
      time += 200;
    }, 200);
  });
};

// 获取用户类型
export const getUserStatus = params => {
  return API.GET('/base/baserole/getUserStatus', params);
};

// 获取运营学校
export const getOperSchoolList = params => {
  return API.GET('/operag/operschool/getOperSchoolList', params);
};

// 登录
export const Login = params => {
  return API.POST('/base/baselogin/login', params);
};
// 根据用户ID获取用户信息（和登录返回信息一致）
export const getLoginUserInfoAPI = params => {
  return API.GET('/userMgr/baselogin/getLoginUserInfo', params);
};
// 根据token获取用户信息
export const getLoginInfoByToken = token => {
  const params = {
    token: token,
  };
  return httpApi.POST('/userMgr/baselogin/getUserInfoByToken', params);
};
// 根据userId获取用户信息
export const getLoginInfoByUserId = userId => {
  const params = {
    userId: userId,
  };
  return httpApi.POST('/userMgr/baselogin/getLoginUserInfo', params);
};

// 获取STS授权数据信息
export const getSTSTokenApi = params => {
  return API.POST('/public/oss/getStsToken', params);
};

// 根据用户Id获取角色信息
export const getUserSchoolRole = params => {
  return API.POST('/base/baseSchoolRole/getUserSchoolRole', params);
};

// 学校角色-获取学校角色列表
export const getSchoolRoleList = params => {
  return API.GET('/userMgr/baseSchoolRole/getSchoolRoleList', params);
};

// 获取地区
export const getRegion = params => {
  return API.GET('/base/baseregion/getRegion', params);
};

/**
 * @name: 获取教师学科列表
 * @param userId 用户id
 */
export const getTeacherSubjectListAPI = params => {
  return API.POST('userMgr/baselogin/getTeacherSubjectList', params);
};

//根据作业ID获取布置班级列表
export const findClassListByWorkId = params => {
  return API.GET('/homework/teahomework/getClassListByWorkIdNew', params);
};

//获取作业阅卷任务题目列表
export const getExamMarkingQues = params => {
  return API.GET('/homework/cloudexam/getExamMarkingQues', params);
};

//获取阅卷抽查学生列表
export const getExamCheckStuList = params => {
  return API.GET('/homework/cloudexam/getExamCheckStuList', params);
};

//重阅某道题的阅卷任务
export const rejectExamCorrect = params => {
  return API.GET('/homework/cloudexam/rejectExamCorrect', params);
};

//保存抽查保存数据
export const saveExamCheckData = params => {
  return API.POSTJson('/homework/cloudexam/saveExamCheckData', params);
};

//获取抽查结果
export const getExamCheckResult = params => {
  return API.GET('/homework/cloudexam/getExamCheckResult', params);
};

/**
 * @name: 根据学科ID获取教师的带课班级或备课班级
 * @param userId 用户ID
 * @param subjectId 学科ID
 * @param year 年份注:查询备课班级时必传
 * @param systemCode 学制注:查询备课班级时必传
 * @param type 1获取带课班级 2获取备课班级
 * @param page 页码
 * @param limit 请求条目
 */
export const getClassListAPI = params => {
  return API.GET('userMgr/baseclass/getClassListBySubjectId', params);
};
/**
 * @name 获取学科code
 * @param userId 用户ID
 */
export const getSubjectsAPI = params => {
  return API.GET('userMgr/basesubject/getSubject', params);
};
/**
 * @name: 保存题库作业
 * @param {params} 接口参数
 *    @workJson 作业JSON
 *    @userId 用户id
 *    @state 来源(0:定时 1:已发送 2:草稿箱[默认])
 *    @creatType 发布方式(1:教师发布 2:备课组长发布)
 *    @sendSource 发布来源(0:其他 1:安卓移动授课助手 2:PC 3:大眼 4:ios移动授课助手 5:大屏 6:针对性练习)
 *    @darftId 草稿箱ID或者作业ID 不传为新布置作业
 *    @correctType 批改类型(0:老师自批改，1:全班互批，2:学生自批)
 *    @permissionType 学生作业互看状态（0:允许互看优秀作答 1:允许互看 2:不允许互看）
 *    @autoSubmit 是否允许补交作业（0:是[默认] 1:否）
 *    @subjectId 学科id（中学传空，小学题库传选择的学科）
 *    @firstType 作业一级类型（0:作业 1:考试 2:互动）
 */
export const saveBankJSONAPI = params => {
  return API.POST('homework/quesTeaHomeWork/createQuesHomework', params);
};
/**
 * @name: 根据作业ID获取作业信息
 * @param id 作业id
 */
export const getQueWorkInfoAPI = params => {
  return API.POST('homework/quesTeaHomeWork/getQueWorkInfoByWorkId', params);
};
// 获取用户角色id集合
export const getUserRoleIdListAPI = params => {
  return API.POST('/operag/operator/getUserRoleIdList', params);
};

/**
 * 根据学校获取年级列表
 * @param params
 * @returns {AxiosPromise}
 */
export const getSchoolGrade = params => {
  return API.POST('/userMgr/basegrade/getSchoolGrade', params);
};

/**
 * 上传word
 */
export const saveTestBanks = params => {
  return API.POST('/testbank/testBank/saveTestBanks', params);
};
// 删除上传的word
export const delXBWordResource = params => {
  return API.POST('/testbank/testBank/delXBWordResource', params);
};
// 删除之前上传的word
export const delXBResource = params => {
  return API.POST('/testbank/testBank/delXBResource', params);
};
// 获取制卷状态和解析状态
export const findTestBanksData = params => {
  return API.POST('/testbank/TBQuestion/findTestBanksData', params);
};
/**
 * 获取试卷下题型和题目关系
 */
export const loadTopicInfo = params => {
  return API.GET('/testbank/testBank/loadTopicInfo', params);
};
/**
 * 获取整个试卷题目信息
 */
export const findTestBankQuestionInfo = params => {
  return API.GET('/testbank/TBQuestion/getTestBankQuestionInfo', params);
};
/**
 * 根据学科id 获取对应题型列表
 */
export const findQueTypeList = params => {
  return API.GET('/questionbank/jyeooques/getQueTypeList', params);
};
/**
 * 获取作业题目信息
 */
export const getQueListWithWork = params => {
  return API.POST('/homework/quesTeaHomeWork/getQueListWithWork', params);
};
/**
 * 获取配置中心学校所配置的状态(外部题库获取开通一起题库权限)
 */
export const getPublicConfigBySchoolInfo = params => {
  return API.POST('/public/config/getPublicConfigBySchoolInfo', params);
};

// 提交中控配置
export const addOrUpdatePublicConfigSchool = params => {
  return API.POST('/public/config/addOrUpdatePublicConfigSchool', params);
};
/**
 * 获取用户多个身份
 */
export const getUserRoles = params => {
  return API.GET('/userMgr/baserole/getUserMultipleStatus', params);
};
/**
 * 获取学科列表 0总管理员/1校管>2年级组长>3学科组长>4备课组长 获取最高权限
 */
export const getSubjectListByRole = params => {
  return API.GET('/website/teaching/getSubjectListByRole', params);
};
/**
 * 学校角色-根据用户ID获取用户角色（1:年级组长 2:备课组长 3: 学科组长）
 */
export const getUserRoleAPI = params => {
  return API.GET('/userMgr/baseSchoolRole/getUserRole', params);
};
/**
 * 根据用户id获取用户信息-v2
 */
export const getUserInfoAPI = params => {
  return API.GET('/userMgr/v2/baselogin/getUserInfo', params);
};
/**
 * 获取考试信息
 * @param params
 * @returns {AxiosPromise}
 */
export const getScanPaperWorkStat = params => {
  return API.GET('/pexam/scanExam/getScanPaperWorkStat', params, KKL_URL);
};
/**
 * 获取考试信息-ab卡 ab卷适用
 * @param params
 * @returns {AxiosPromise}
 */
export const getMultipleScanPaperWorkStat = params => {
  return API.GET('/pexam/scanExam/getMultipleScanPaperWorkStat', params, KKL_URL);
};
/**
 * 发布成绩
 * @param params
 * @returns {AxiosPromise}
 */
export const publishScoreAPI = params => {
  return API.POST('/pexam/scanExam/publishScore', params, KKL_URL);
};
/**
 * 根据考试id获取扫描作业概况
 * @param params
 * @returns {AxiosPromise}
 */
export const getScanPaperWorkStatAPI = params => {
  return API.GET('/pexam/scanExam/getScanPaperWorkStat', params, KKL_URL);
};

/**
 * 获取考试列表
 * @param params
 * @returns {AxiosPromise}
 */
export const searchScanPaperWorkList = params => {
  return API.GET('/pexam/scanExam/searchScanPaperWorkList', params, KKL_URL);
};
/**
 * 根据作业ID获取布置班级列表()
 * @param params
 * @returns {AxiosPromise}
 */
export const getClassListByWorkIdAPI = params => {
  return API.GET('/pexam/scanExam/getClassListByWorkIdPBook', params, KKL_URL);
};
/**
 * 获取未扫描学生名单
 * @param params
 * @returns {AxiosPromise}
 */
export const getScanStuInfosAPI = params => {
  return API.GET('/pexam/scanExam/getScanStuInfos', params, KKL_URL);
};
/**
 * 获取代课老师列表
 * @param params
 * @returns {AxiosPromise}
 */
export const findTeacherAssignmentList = params => {
  return API.GET('/homework/cloudexam/getTeacherAssignmentList', params);
};
/**
 * 获取阅卷列表
 * @param params
 * @returns {AxiosPromise}
 */
export const getExamTeacherList = params => {
  return API.GET('/homework/cloudexam/getExamTeacherList', params);
};
/**
 * 获取网阅任务列表
 * @param params
 * @returns {AxiosPromise}
 */
export const getMarkingTaskList = params => {
  return API.GET('/homework/cloudexam/getMarkingTaskList', params);
};
// [阅卷分配] 获取试题阅卷分配详情
export const findQuestionAssignmentList = params => {
  // return API.GET("/homework/cloudexam/getQuestionAssignmentList", params);
  return API.GET('/homework/cloudexam/getPaperQuesAssignmentList', params);
};
// [阅卷分配] 根据学校和年份获取代课教师
export const getTeacherSubstituteBySchool = params => {
  return API.GET('/userMgr/baseclass/getTeacherSubstituteBySchool', params);
};
// [阅卷分配] 阅卷分配保存或结束
export const generateAssignment = params => {
  // return API.POST("/homework/cloudexam/generateAssignment", params);
  return API.POST('/homework/cloudexam/initPaperAssignment', params);
};
// [阅卷分配] 根据学校和年份,学科获取教师
export const getSchoolTeacher = params => {
  return API.GET('/userMgr/baselogin/getTeacherListSupportSubjectAndGrade', params);
};
//获取用户的角色
export const getUserRole = params => {
  return API.GET('/userMgr/baseSchoolRole/getUserSchoolRole', params);
};
//获取学校校区列表
export const getCampusCodeList = params => {
  return API.GET('/userMgr/baseCampusCode/getCampusCodeList', params);
};
// 获取教师校区列表
export const getTeaCampusCodeListAPI = params => {
  return API.GET('/userMgr/baseCampusCode/getTeaCampusCodeList', params);
}
//开始阅卷
export const startCorrect = params => {
  return API.POST('/homework/teaPersonalWork/startCorrect', params);
};
//网阅-异常处理完发消息
export const netPublishScore = params => {
  return API.POST('/pexam/scanExam/publishScore', params, KKL_URL);
};
//网阅-阅卷进度列表
export const findExamProgress = params => {
  return API.GET('/homework/cloudexam/getExamProgress', params);
};
//网阅-点击结束阅卷
export const glanceOverMark = params => {
  return API.POST('/homework/cloudexam/glanceOverMark', params);
};
//网阅-是否可以结束考试
export const canGlanceOver = params => {
  return API.POST('/homework/cloudexam/canGlanceOver', params);
};
// 阅卷管理-获取作业学生题号截图列表
export const getQuesPointSourceList = params => {
  return API.GET('/homework/cloudexam/getQuesPointSourceList', params);
};

export const getNewQuesPointSourceList = params => {
  params.version = 2.0;
  return API.GET('/homework/cloudexam/getQuesPointSourceList', params);
};

export const getBatchQuesPointSourceList = params => {
  return API.GET('/homework/cloudexam/getBatchQuesPointSourceList', params);
};

export const searchStuInfoByWork = params => {
  return API.GET('/pexam/scanExam/searchStuInfo', params, KKL_URL);
};
// 教师批改题目--智批改 smartCorrect 0：主观题阅卷 1：智批改阅卷
export const getTeaCorrectQuesList = params => {
  return API.GET('/homework/cloudexam/getTeaCorrectQuesList', params);
};
// 题目批改学生列表
export const getCorrectStuList = params => {
  return API.GET('/homework/cloudexam/getCorrectStuList', params);
};
// 题目批改学生列表
export const getSmartCorrectStuList = params => {
  return API.GET('/homework/cloudexam/getSmartCorrectStuList', params);
};
// 教师批量智批改作业数据
export const saveBatchCorrectResult = params => {
  return API.POST('/homework/cloudexam/saveBatchCorrectResult', params);
};
/**
 * 智批改批量处理
 * @param {Object} params - 接口参数
 * @param {string} params.schoolId - 学校id
 * @param {string} params.workId - 作业id
 * @param {string} params.userId - 用户id
 * @param {string} params.quesId - 题目id
 * @param {number} params.type - 类型 1：作答错误 2：作答正确
 * @param {number} params.smartType - 智批改类型 0：待复核 1：已复核
 * @param {number} params.similar - 相似度（默认0）
 * @param {number} params.maxSimilar - 最大相似度（默认100）
 * @param {number} params.isRight - 0:错误（默认）1：正确
 * @param {number|string} params.score - 分数
 */
export const batchCorrectSmartQues = params => {
  return API.GET('/homework/cloudexam/batchCorrectSmartQues', params);
};
// 更新考试作业师生关系信息
export const updateExamHomeWorkInfo = params => {
  return API.POST('/pexam/scanExam/updateExamHomeWorkInfo', params, KKL_URL);
};
/**
 * @name: 学科网选题创建试卷信息
 * @param {params} 接口参数
 *    @schoolId 学校id
 *    @userId 用户id
 *    @openId 开放id(第一次查询需要传)
 *    @paperId 试卷id(第一次查询需要传)
 *    @subjectId 学科id(第一次查询需要传)
 *    @workId 作业id(第一次查询需要传)
 */
export const initPaperQuestionInfo = params => {
  return API.POST('/testbank/testBank/initPaperQuestionInfo', params);
};
/**
 * @name:校验考试班级是否设置授课教师
 * @param {*} params
 * @param paperNo 试卷号
 * @param subjectId 学科id
 * @param examId 考试id
 * @returns
 */
export const changeWorkClass = params => {
  return API.POST('/homework/cloudexam/changeWorkClass', params);
};
/**
 * @name:校验是否更改了阅卷分配
 * @param {*} params
 * @param schoolId 学校id
 * @param paperNo  试卷号
 * @param json     阅卷分配详细json
 * @returns
 */
export const changeMarkingJson = params => {
  return API.POST('/homework/cloudexam/changeMarkingJson', params);
};
/**
 * @name:获取阅卷质量监控
 * @param {*} params
 * @param schoolId 学校id
 * @param workId 作业id
 * @param quesId 题目id
 * @returns
 */
export const getExamMarkingQualityData = params => {
  return API.POST('/homework/cloudexam/getExamMarkingQualityData', params);
};

/**
 * @name:获取考试学生
 * @param {*} params
 * @param workId 考试id
 * @param schoolId 学校id
 * @param text 查询关键字
 * @param scanState 默认所有学生 1:已扫描学生 2：缺考学生
 */
export const getWorkStuList = params => {
  return API.POST('/homework/cloudexam/getWorkStuList', params);
};

/**
 * @name:获取学生作答区域数据
 * @param {*} params
 * @param workId 考试id
 * @param stuId 学生id
 * @param quesNo 题号
 */
export const getStuAnswerAreaReplaceData = params => {
  return API.POST('/homework/cloudexam/getStuAnswerAreaReplaceData', params);
};

/**
 * @name:保存学生作答区域数据
 * @param {*} params
 * @param workId 考试id
 * @param stuId 学生id
 * @param json 题目数据
 */
export const saveStuAnswerAreaReplace = params => {
  return API.POSTJson('/homework/cloudexam/saveStuAnswerAreaReplace', params);
};

/**
 * @name:获取学生作答图片
 * @param {*} params
 * @param workId 考试id
 * @param stuId 学生id
 */
export const getStuAnswerImages = params => {
  return API.POST('/homework/cloudexam/getStuAnswerImages', params);
};

/**
 * @name:获取错题互换记录
 * @param {*} params
 * @param schoolId 学校id
 * @param examId 考试id
 * @param page
 * @param pageSize
 */
export const getExamAnswerReplaceRecords = params => {
  return API.POST('/homework/cloudexam/getExamAnswerReplaceRecords', params);
};

/**
 * @description: 获取考试学生明细列表
 * @param {*} params
 * @param examId 考试id
 * @param subjectId 学科id
 * @return {*}
 */
export const getExamStuList = params => {
  return API.POST('/homework/cloudexam/getExamStuList', params);
};

/**
 * @description: 获取学生试卷作答明细
 * @param {*} params
 * @param examId 考试id
 * @param subjectId 学科id
 * @param studentId 学生id
 * @return {*}
 */
export const getExamStuScanData = params => {
  return API.POST('/homework/cloudexam/getExamStuScanData', params);
};

/**
 * @description: 保存学生考试成绩
 * @param {*} params
 * @return {*}
 */
export const saveExamStuScanDetails = params => {
  return API.POSTJson('/homework/cloudexam/saveExamStuScanDetails', params);
};

/**
 * @description: 修改学生成绩
 * @param {*} params
 * @param examId 考试id
 * @param subjectId 学科id
 * @param studentId 学生id
 * @param state 扫描状态 0:未扫描 1：已扫描/批改完整 2：缺考，3：批改不完整，4：0分学生，5：不参与统计
 * @return {*}
 */
export const setExamStuState = params => {
  return API.POST('/homework/cloudexam/setExamStuState', params);
};

// 获取学生选做题目
export const getStuElectiveQues = params => {
  return API.GET('/homework/cloudexam/getStuElectiveQues', params);
};
// 获取试卷分值信息
export const getAbVolumeDetail = params => {
  return API.GET('/homework/cloudexam/getAbVolumeDetail', params);
};

// 发布教师考试
export const publishTeaExamAPI = params => {
  return API.GET('homework/cloudexam/publishTeaExam', params);
};
// 获取考试题目得分列表
export const getExamQueScoreListAPI = params => {
  return API.POST('/homework/cloudexam/getExamQueScoreList', params);
};

/**
 * @description: 获取考试学生批改详情
 * @param {*} params
 * @param params.schoolId 学校id
 * @param params.workId 考试id
 * @param params.stuId 学生id
 * @param params.quesNo 题号
 * @param params.tQuesNo 题号
 */
export const getExamStuCorrectDetailAPI = params => {
  return API.GET('/homework/cloudexam/getExamStuCorrectDetail', params);
};

/**
 * @description: 删除学生句子
 * @param {*} params
 * @param params.id 
 * @param params.sentence 
 */
export const removeStuSentenceAPI = params => {
  return API.POST('/homework/cloudexam/removeStuSentence', params);
};


/**
 * @description: 保存学生句子错误
 * https://doc.apipost.net/docs/detail/2f4b82728464000?target_id=2ff7ccb8faf040
 */
export const saveStuSentenceErrorAPI = params => {
  return API.POSTJson('/homework/cloudexam/saveStuSentenceError', params);
};

/**
 * @description: 保存考试学生批改数据
 * @param {*} params
 * @param params.schoolId 学校id
 * @param params.workId 考试id
 * @param params.shwId 学生id
 * @param params.userId 题号
 * @param params.realname 题号
 * @param params.correctJson 批改数据
 * @return {*}
 */
export const saveExamCorrectDataAPI = params => {
  return API.POST('/homework/cloudexam/saveCorrectData', params);
};

/**
 * @description: 获取作文范文
 * https://doc.apipost.net/docs/detail/2f4b82728464000?target_id=2ff5aa393af010
 */
export const getExampleCompositionAPI = params => {
  return API.GET('/homework/cloudexam/getExampleComposition', params);
};

/**
 * @description: 获取班级词汇、语法分析
 * https://doc.apipost.net/docs/detail/2f4b82728464000?target_id=32e1abbb3af00d
 */
export const getExamQueClassDetailsAPI = params => {
  return API.GET('/homework/cloudexam/getExamQueClassDetails', params);
};

/**
 * @name:  根据区管id, 获取平台信息
 * @param userId 区管id
 **/
export const getPlatFormInfoByUserIdAPI = params => {
  return API.POST('/platformManage/getPlatFormInfoByUserId', params);
};


// 获取AI批改题目
export const getExamAiCorrectQueAPI = params => {
  return API.GET("homework/cloudexam/getExamAiCorrectQue", params);
}
// 保存AI批改题目
export const saveExamAiCorrectQueAPI = params => {
  return API.POST("homework/cloudexam/saveExamAiCorrectQue", params);
}

// 获取腾讯验证码应用APP ID
export const getVerificationIdAPI=(params) => {
  return API.GET('/userMgr/verificationCode/getAppId',params);
}

// 获取安全码
export const getSafetyIdAPI =(params) => {
  return API.GET('/userMgr/register/getsafetyid',params);
}

// 获取短信验证码
export const getSMSCodeAPI = params => {
  return API.GET('/userMgr/register/getNewSMSCodeByMobileNumber', params);
}

// 验证短信验证码
export const checkSMSCodeAPI = params => {
  return API.GET('/userMgr/register/checkMobileCodeRetunUserInfo', params);
}

// 重置密码
export const resetPassWordByMobileAPI = params => {
  return API.GET('/userMgr/register/resetPassWordByMobile', params);
}

// 修改密码
export const modifyPassWordAPI = (params) => {
  return API.POST('/base/baselogin/modifyPassWord', params);
};

// 获取学生语文报告PDF
export const getExamStuPdfUrlAPI = (params) => {
  return API.GET('/homework/cloudexam/getExamStuPdfUrl', params);
}

/**
 * 获取考试班级学生列表
 * https://docs.apipost.net/docs/detail/2f4b82728464000?target_id=205bcbeb70401b
 */
export const getExamClassStuListAPI = (params) => {
  return API.GET('/homework/cloudexam/getExamClassStuList', params);
}


/**
 * 保存学生手阅题目修改信息
 * https://docs.apipost.net/docs/detail/2f4b82728464000?target_id=4a54d8aef8be000
 */
export const saveHandReadStuExamAPI = (params) => {
  return API.POSTJson('/homework/cloudexam/saveHandReadStuExam', params);
}

// 获取学校学年列表
export const getSchoolYearListAPI = params => {
  return API.GET('/userMgr/baseschool/getSchoolYearList', params);
};

// 设置阅卷状态
export const setMarkingStatusAPI = (params) => {
  return API.GET('/homework/cloudexam/setMarkingStatus', params);
}

// 获取阅卷状态
export const getMarkingStatusAPI = (params) => {
  return API.GET('/homework/cloudexam/getMarkingStatus', params);
}

/**
 * 获取学生高频作答
 * https://docs.apipost.net/docs/detail/2f4b82728464000?target_id=24a94e5a39203c
 */
export const getHighFrequencyAPI = (params) => {
  return API.GET('/homework/cloudexam/getHighFrequency', params);
}

/**
 * 保存填空题高频作答复核
 * https://docs.apipost.net/docs/detail/2f4b82728464000?target_id=4e57a24560a4000
 */
export const saveFrequencyCorrectAPI = (params) => {
  return API.POSTJson('/homework/cloudexam/saveFrequencyCorrect', params);
}
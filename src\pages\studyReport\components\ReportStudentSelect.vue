<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-04-01 10:03:14
 * @LastEditors: 小圆
-->
<template>
  <div class="header-item">
    <span class="header-item__label">学生：</span>
    <el-select
      class="header-item__select short-select"
      value-key="id"
      :value="filterInfo.stuInfo"
      @change="onChange"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <el-option
        v-for="item in selectOption.stuList"
        :key="item.id"
        :title="item.name"
        :label="item.name"
        :value="item"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import ReportComponent from './ReportComponent';

@Component
export default class ReportStudentSelect extends ReportComponent {
  onChange(value) {
    this.FilterModule.setStuInfo(value);
    this.FilterModule.trigger('changeFilter');
  }
}
</script>

<style scoped lang="scss">
@import './reportSelect.scss';
</style>

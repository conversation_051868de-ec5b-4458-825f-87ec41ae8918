import Vue from 'vue';

// import '@/style/base.css';
import 'view-design/dist/styles/iview.css';
import {
  // Affix,
  // Alert,
  // AutoComplete,
  Avatar,
  // BackTop,
  Badge,
  Breadcrumb,
  BreadcrumbItem,
  Button,
  ButtonGroup,
  Card,
  Carousel,
  CarouselItem,
  Cascader,
  Checkbox,
  CheckboxGroup,
  Col,
  Circle,
  // Collapse,
  // ColorPicker,
  Content,
  DatePicker,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  Footer,
  Form,
  FormItem,
  // Header,
  Icon,
  Input,
  InputNumber,
  Scroll,
  Sider,
  Submenu,
  Layout,
  LoadingBar,
  Menu,
  MenuGroup,
  MenuItem,
  Message,
  Modal,
  Notice,
  Option,
  OptionGroup,
  Page,
  Panel,
  Poptip,
  Progress,
  Radio,
  RadioGroup,
  Rate,
  Row,
  Select,
  // Slider,
  Spin,
  // Step,
  Steps,
  Switch,
  Table,
  Tabs,
  TabPane,
  Tag,
  Timeline,
  TimelineItem,
  TimePicker,
  Tooltip,
  Transfer,
  Tree,
  Upload
} from 'view-design';

// Vue.component('Affix', Affix);
// Vue.component('Alert', Alert);
// Vue.component('AutoComplete', AutoComplete);
Vue.component('Avatar', Avatar);
// Vue.component('BackTop', BackTop);
Vue.component('Badge', Badge);
Vue.component('Breadcrumb', Breadcrumb);
Vue.component('BreadcrumbItem', BreadcrumbItem);
Vue.component('Button', Button);
Vue.component('ButtonGroup', ButtonGroup);
Vue.component('Card', Card);
Vue.component('Carousel', Carousel);
Vue.component('CarouselItem', CarouselItem);
Vue.component('Cascader', Cascader);
Vue.component('Checkbox', Checkbox);
Vue.component('CheckboxGroup', CheckboxGroup);
Vue.component('Col', Col);
Vue.component('iCircle', Circle);
// Vue.component('Collapse', Collapse);
// Vue.component('ColorPicker', ColorPicker);
Vue.component('Content', Content);
Vue.component('DatePicker', DatePicker);
Vue.component('Dropdown', Dropdown);
Vue.component('DropdownItem', DropdownItem);
Vue.component('DropdownMenu', DropdownMenu);
Vue.component('Footer', Footer);
Vue.component('Form', Form);
Vue.component('FormItem', FormItem);
// Vue.component('Header', Header);
Vue.component('Icon', Icon);
Vue.component('Input', Input);
Vue.component('InputNumber', InputNumber);
Vue.component('Scroll', Scroll);
Vue.component('Sider', Sider);
Vue.component('Submenu', Submenu);
Vue.component('Layout', Layout);
Vue.component('LoadingBar', LoadingBar);
Vue.component('Menu', Menu);
Vue.component('MenuGroup', MenuGroup);
Vue.component('MenuItem', MenuItem);
Vue.prototype.$Message = Message;
window.Message = Message;
Vue.component('Modal', Modal);
Vue.prototype.$Modal = Modal;
Vue.component('Notice', Notice);
Vue.prototype.$Notice = Notice;
Vue.component('Option', Option);
Vue.component('OptionGroup', OptionGroup);
Vue.component('Page', Page);
Vue.component('Panel', Panel);
Vue.component('Poptip', Poptip);
Vue.component('Progress', Progress);
Vue.component('Radio', Radio);
Vue.component('RadioGroup', RadioGroup);
Vue.component('Rate', Rate);
Vue.component('Row', Row);
Vue.component('Select', Select);
// Vue.component('Slider', Slider);
Vue.component('Spin', Spin);
// Vue.component('Step', Step);
Vue.component('Steps', Steps);
Vue.component('iSwitch', Switch);
Vue.component('Table', Table);
Vue.component('Tabs', Tabs);
Vue.component('TabPane', TabPane);
Vue.component('Tag', Tag);
Vue.component('Timeline', Timeline);
Vue.component('TimelineItem', TimelineItem);
Vue.component('TimePicker', TimePicker);
Vue.component('Tooltip', Tooltip);
Vue.component('Transfer', Transfer);
Vue.component('Tree', Tree);
Vue.component('Upload', Upload);
Vue.prototype.$Loading = LoadingBar;
window.$Loading = LoadingBar;

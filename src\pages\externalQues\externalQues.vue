<template>
  <div class="add-ques-wrapper">
    <div class="add-ques-left">
      <div class="content-catalog">
        <div class="catalog-header display_flex align-items_center justify-content_flex-justify">
          <span class="catalog-title">知识点目录</span>
          <el-select
            size="mini"
            class="select-subject"
            v-model="currentSubject"
            @change="subjectChange"
          >
            <el-option
              v-for="item in subjectList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <div class="point-tree-box" v-loading="isLoading">
          <el-tree
            class="catalog-tree"
            :data="treeData"
            ref="pointerTreeYiQList"
            :props="defaultProps"
            empty-text="暂无数据"
            :check-strictly="false"
            :expand-on-click-node="false"
            :check-on-click-node="true"
            :highlight-current="true"
            :default-expanded-keys="expandedKeys"
            node-key="code"
            @current-change="checkYiQChange"
          >
            <span class="el-tree-node__label" slot-scope="{ node }">
              <span class="text-ellipsis" :title="node.label">{{ node.label }}</span>
            </span>
          </el-tree>
        </div>
      </div>
    </div>
    <div class="add-ques-right" v-show="currentStep != 2">
      <div id="sticky_container" class="right-wrapper">
        <div v-show="currentStep === 1" class="add-ques-content add-ques-content-2 sticky_net">
          <ques-net-filter
            data-stuck="stuck_net"
            :stuck="stuckInfo.stuck_net"
            class="sticky"
            ref="netFilter"
          >
          </ques-net-filter>
          <select-yiqi-ques-card
            ref="yiQuesCardNet"
            @getParams="getParams"
            :subjectId="currentSubject"
            :currentStep="1"
            :tokenInfo="tokenInfo"
            :needQuesNum="10"
            :isShowEditBUtton="true"
          ></select-yiqi-ques-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { getC30Token } from '@/service/ques';
import { getKnowLedgeInfos } from '@/service/yiqi';

import SelectYiqiQuesCard from '@/components/SelectQuesCard/SelectYiqiQuesCard.vue';
import QuesNetFilter from '@/components/QuesNetFilter/QuesNetFilter.vue';
import { getUserInfoToPersonalityTest } from '../../service/api';
import { localSave, sessionSave } from '@/utils';
import UserRole from '@/utils/UserRole';

export default {
  name: 'ExternalQues',
  components: {
    QuesNetFilter,
    SelectYiqiQuesCard,
  },
  computed: {
    ...mapGetters(['subjectMap']),
  },
  data() {
    return {
      currentStep: 1,
      tokenInfo: null,
      currentSubject: '',
      lastSubject: '',
      subjectList: [],
      treeData: [],
      // tree默认值类型
      defaultProps: {
        children: 'children',
        label: 'name',
      },
      expandedKeys: [],
      isLoading: false,
      stuckInfo: {
        stuck_c30: false,
        stuck_net: false,
      },
    };
  },
  mounted() {
    this.initTimeout();
    this.currentSubject = '';
  },
  watch: {
    currentSubject(newVal, oldVal) {
      this.lastSubject = oldVal;
    },
  },
  methods: {
    initTimeout() {
      this.init();
    },
    async init() {
      this.isLoading = true;
      this.subjectList = this.$localSave.get('SUBJECT_LIST');
      this.subjectList = this.subjectList.filter(item => item.phaseId != 3);
      var $this = this;
      const ret = await UserRole.getUserInfoPersonalityTest();
      $this.subjectList = ret.userSubList.filter(item => item.phaseId != 3);
      if ($this.subjectList.length == 0) {
        return;
      }
      if (!$this.currentSubject) {
        if ($this.$route.query.fromName === 'previewPaper') {
          $this.currentSubject = $this.subjectList.filter(item => {
            return item.name == $this.$route.query.subjectName;
          })[0].id;
        } else {
          $this.currentSubject = $this.subjectList[0].id;
        }
        sessionSave.set('currentSubject', $this.subjectMap[$this.currentSubject]);
      }

      $this.$nextTick(() => {
        observeStickyHeaderChanges(document.querySelector('#sticky_container'));
        document.addEventListener('sticky-change', e => {
          let node = e.detail.target;

          // this.$set(this.stuckInfo, "stuck_net", true);

          $this.$set($this.stuckInfo, node.dataset['stuck'], e.detail.stuck);
        });

        $this.changeFilter();
        $this.$refs.yiQuesCardNet.reload();
      });
    },
    async changeFilter() {
      let subject = this.subjectMap[this.currentSubject];
      this.$localSave.set('currentSubject', subject);
      const subInfo = this.$localSave.get('currentSubject');
      this.tokenInfo = await getC30Token({
        subjectId: subInfo.id || '',
      });
      this.$refs.netFilter.getFilterCondition(this.tokenInfo, subInfo);
      this.getYiQPointTree(this.tokenInfo);
      // this.$refs.yiQuesCardNet.getQuesByCondition(this.tokenInfo)
      this.$bus.$emit('changeSubjectFilter', this.tokenInfo);
      this.$bus.$emit('content', '');
      this.$refs.yiQuesCardNet.reload();
    },
    subjectChange() {
      let subject = this.subjectMap[this.currentSubject];
      sessionSave.set('currentSubject', subject);
      this.changeFilter();
      this.$refs.yiQuesCardNet.reload();
    },
    handleMoreClick(node, checkedStatus) {
      let knowledgeCode = '';
      let needKeys = [];
      checkedStatus.checkedNodes.forEach(item => {
        if (checkedStatus.checkedKeys.indexOf(item.parentCode) < 0) {
          needKeys.push(item.code);
        }
      });
      knowledgeCode = needKeys.join(',');
      this.$bus.$emit('changeYiqPoint', knowledgeCode);
      // this.checkYiQChange(knowledgeCode)
    },
    checkYiQChange(...args) {
      this.$bus.$emit('changeYiqPoint', args[0]);
    },
    // 切换学校后更新数据
    changeSchool() {
      this.init();
      this.currentSubject = '';
    },
    // 获取一起题库知识点
    getYiQPointTree(tokenInfo) {
      getKnowLedgeInfos({
        headers: {
          token: tokenInfo.token || '',
        },
        data: {
          userId: tokenInfo.id,
        },
      })
        .then(data => {
          const res = JSON.parse(data);
          if (res.data && res.data.tags.length) {
            let list = this.handleYiQPointTree(res.data.tags);
            if (list[0].parent == null && list[0].children.length) {
              // this.yiQTreeList = list[0].children;
              this.treeData = list[0].children;
            }
          }
          this.$refs.yiQuesCardNet.reload();
        })
        .catch(err => {
          console.log(err);
          this.currentSubject = this.lastSubject;
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    getParams() {
      return {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        subjectId: this.currentSubject,
      };
    },
    // 处理一起知识点数据
    handleYiQPointTree(treeList, node) {
      if (node) {
        return treeList
          .filter(item => item.parent === node._id)
          .map(item => {
            item.children = this.handleYiQPointTree(treeList, item);
            return {
              ...item,
              code: item._id,
            };
          });
      } else {
        return treeList
          .filter(item => item.parent == null)
          .map(item => {
            item.children = this.handleYiQPointTree(treeList, item);
            return {
              ...item,
              code: item._id,
            };
          });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.point-tree-box .el-tree-node__content:hover,
.point-tree-box
  .el-tree
  div[role='treeitem'].is-current
  > .el-tree-node__content
  .el-tree-node__label {
  color: #008dea;
}
.add-ques-wrapper {
  display: flex;

  &.block {
    display: block;
  }

  .edit-remark-box {
    overflow-y: auto;
    height: calc(100vh - 196px);

    .save-ques-btn {
      text-align: center;
      margin-bottom: 35px;

      & > button {
        width: 136px;
        font-size: 18px;
      }
    }
  }

  .add-ques-left {
    // overflow-y: auto;
    // height: calc(100vh - 134px);
    .add-ques-kinds-box {
      // text-align: center;
      margin: 12px 0;
      margin-left: 22px;

      .el-button {
        padding: 12px 10px;
      }
    }
  }

  .add-ques-right {
    flex: 1;

    .right-wrapper {
      height: calc(100vh - 115px);
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .add-ques-content {
        flex: 1;
        overflow-y: auto;
        // padding-right: 150px;
        position: relative;
      }
    }
  }
}

.content-catalog {
  width: 300px;
  background: #fff;
  border: 1px solid #e8ebed;
  border-radius: 3px;

  .catalog-header {
    width: 298px;
    height: 48px;
    line-height: 48px;
    background: #f5f6fa;
    border-radius: 4px 4px 0 0;
    font-size: 16px;
    font-weight: 400;
    color: #666;
    padding: 0 15px 0 13px;
    .select-subject {
      // position: absolute;
      // top: 10px;
      width: 96px;
      right: -70px;
    }
    .catalog-title {
      position: relative;
      padding-left: 10px;

      &:before {
        content: '';
        position: absolute;
        width: 4px;
        height: 16px;
        background: #008dea;
        border-radius: 2px;
        left: 0;
        top: 17px;
      }
    }
  }
  .point-tree-box {
    height: 550px;
    overflow: scroll;
  }

  .catalog-treeBox {
    height: 100%;

    .pointer-type {
      margin: 10px auto;

      .pointer-type-group {
        display: flex;
        align-items: center;
        justify-content: space-around;
        border-bottom: 2px solid #e4e8ed;

        .pointer-type-pane {
          width: 50%;
          line-height: 48px;
          text-align: center;
          font-size: 16px;
          color: #4c5866;
          position: relative;

          &:hover {
            color: #409eff;
            cursor: pointer;
          }

          &.active {
            color: #409eff;

            &::before {
              content: '';
              width: 100%;
              height: 0;
              border-bottom: 2px solid #409eff;
              position: absolute;
              bottom: -2px;
              left: 0;
            }
          }
        }
      }
    }

    .catalog-tree {
      width: 100%;
      height: calc(100% - 118px);
      overflow-x: hidden;
      overflow-y: auto;
    }
  }
}

::-webkit-scrollbar {
  height: 10px;
  overflow: auto;
  width: 5px;
}
::-webkit-scrollbar-track {
  background: rgb(239, 239, 239);
  border-radius: 2px;
}
</style>

<style lang="scss">
.content-catalog {
  .catalog-header {
    .ques-bank-select {
      .el-input__inner {
        width: 130px;
      }
    }
  }
}
.catalog-tree {
  margin-top: 6px;

  .el-tree-node__expand-icon.expanded {
    transform: none;
  }

  .el-icon-caret-right:before {
    content: '\e7a0' !important;
  }

  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {
    content: '\e7a2' !important;
  }

  .el-tree-node__expand-icon {
    font-size: 17px;
  }

  .el-tree-node__label {
    font-size: 16px;
  }

  .el-tree-node__content {
    height: 26px;
  }

  .el-tree-node__content {
    display: flex;
  }

  .el-tree-node__content > span:last-child {
    flex: 1;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .el-checkbox.is-checked + .el-tree-node__label {
    color: #409eff;
  }

  &::-webkit-scrollbar,
  .selected-catalog-box::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }

  &::-webkit-scrollbar-thumb,
  .selected-catalog-box::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    background-color: rgba(144, 147, 153, 0.3);
  }

  &::-webkit-scrollbar-track,
  .selected-catalog-box::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    // box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #ffffff;
    border-radius: 10px;
  }
}

// 树节点选中高亮
.catalog-treeBox
  .catalog-tree.chapter__tree
  .el-tree-node.is-current.is-focusable
  > .el-tree-node__content {
  background-color: #ffffff;

  & > .el-tree-node__label {
    color: #008dea;
  }
}

.catalog-treeBox .el-tree-node__content:hover,
.catalog-treeBox
  .el-tree
  div[role='treeitem'].is-current
  > .el-tree-node__content
  .el-tree-node__label {
  color: #008dea;
}
</style>

<template>
  <div>
    <div v-show="!showDefault" id="quesTypeAvgChart" style="width: 100%; height: 400px"></div>
    <div v-show="showDefault">
      <div style="text-align: center">
        <img style="width: 350px" :src="noResImg" alt="" />
      </div>
      <p style="text-align: center; font-size: 16px; margin-top: 10px">暂无数据!</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'QuesTypeAvgChart',
  props: ['tableData', 'currentQuesType', 'chartType'],
  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      showDefault: false,
      quesTypeAvgChart: null,
      classNames: [],
      classAvgList: [],
      classAvgList: [],
      upperAvgList: [],
      lowerAvgList: [],
      gradeAvgList: [],
    };
  },
  mounted() {
    let _this = this;
    window.onresize = () => {
      return (() => {
        if (_this.quesTypeAvgChart) {
          this.resetDomSize('quesTypeAvgChart', 400);
          this.$nextTick(() => {
            _this.quesTypeAvgChart.resize();
          });
        }
      })();
    };
    if (!this.quesTypeAvgChart) {
      this.drawImg();
    }
  },
  beforeDestroy() {
    if (this.quesTypeAvgChart != null && this.quesTypeAvgChart != '' && this.quesTypeAvgChart != undefined) {
      this.quesTypeAvgChart.dispose();
      this.quesTypeAvgChart = null;
    }
  },
  watch: {
    tableData: {
      handler(newVal) {
        if (newVal.length && newVal[0].questionTypes) {
          this.showDefault = false;
          this.resetDomSize('quesTypeAvgChart', 400);
          this.$nextTick(() => {
            this.drawImg();
          });
        } else {
          this.showDefault = true;
        }
      },
      deep: true,
    },
    currentQuesType() {
      this.drawImg();
    },
    chartType() {
      this.drawImg();
    },
  },
  methods: {
    resetDomSize(el, height) {
      let width = document.getElementById('pane-chart').clientWidth;
      Object.defineProperty(document.getElementById(el), 'clientWidth', {
        get: function () {
          return width;
        },
        configurable: true,
      });
      Object.defineProperty(document.getElementById(el), 'clientHeight', {
        get: function () {
          return height;
        },
        configurable: true,
      });
    },
    handleChartData() {
      let data = this.tableData && this.tableData.length ? this.tableData : [];
      // console.log(this.tableData)
      let grdData = {};
      if (data.length) {
        if (this.currentQuesType === 'total') {
          // 处理总分数据
          grdData = {
            avgScore: this.tableData[0].totalAverageScore,
            scoreRate: this.tableData[0].totalScoreRate,
          };
          data = this.tableData.slice(1);
        } else {
          // 处理题型数据
          if (!this.tableData[0].questionTypes) {
            return;
          }
          grdData = this.tableData[0].questionTypes.filter(item => item.quesType == this.currentQuesType)[0];
          data = this.tableData.slice(1);
        }
      } else {
        this.showDefault = true;
      }

      this.classNames = data.map(item => item.classId);

      let subData;
      if (this.currentQuesType === 'total') {
        // 总分数据直接从班级数据中获取
        subData = data.map(item => {
          return {
            avgScore: item.totalAverageScore,
            scoreRate: item.totalScoreRate,
          };
        });
      } else {
        // 题型数据从questionTypes中获取
        subData = data.map(item => {
          return item.questionTypes.find(it => it.quesType == this.currentQuesType);
        });
      }

      // 根据chartType选择不同的数据处理逻辑
      if (this.chartType === 'avgScore') {
        // 均分数据处理 - 使用原始数据
        this.classAvgList = subData.map(item => {
          return (item.avgScore * 1).toFixed(2);
        });
        this.gradeAvgList = data.map(item => {
          return (grdData.avgScore * 1).toFixed(2);
        });
      } else {
        // 得分率数据处理 - 使用原始数据
        this.classAvgList = subData.map(item => {
          return (item.scoreRate * 1).toFixed(2);
        });
        this.gradeAvgList = data.map(item => {
          return (grdData.scoreRate * 1).toFixed(2);
        });
      }
    },
    drawImg() {
      if (this.quesTypeAvgChart != null && this.quesTypeAvgChart != '' && this.quesTypeAvgChart != undefined) {
        this.quesTypeAvgChart.dispose();
        this.quesTypeAvgChart = null;
      }
      this.handleChartData();
      let _this = this;
      // 基于准备好的dom，初始化echarts实例
      this.quesTypeAvgChart = this.$echarts.init(document.getElementById('quesTypeAvgChart'));
      // 绘制图表
      this.quesTypeAvgChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'none', // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: params => {
            let arr = [];
            arr.push(`<div>${params[0].name}</div>`);
            for (const item of params) {
              arr.push(
                `<div>${item.marker}  ${item.seriesName}: <b style="float: right; margin-left: 20px;">${item.value}</b> </div>`
              );
            }
            const successMarker = `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:#67C23A;"></span>`;
            const failMarker = `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:#F56C6C;"></span>`;
            let value = Number(params[0].value.replace('%', ''));
            let gradeValue = Number(params[1].value.replace('%', ''));
            let gradeLabel = this.chartType === 'avgScore' ? '年级均分' : '年级得分率';
            let gradeUnit = this.chartType === 'avgScore' ? '分' : '%';
            if (value - gradeValue > 0) {
              arr.push(
                `<div>${successMarker} 高于${gradeLabel}: <b style="float: right; margin-left: 20px;">${Math.abs(
                  value - gradeValue
                ).toFixed(2)}${gradeUnit}</b></div>`
              );
            } else if (value - gradeValue < 0) {
              arr.push(
                `<div>${failMarker} 低于${gradeLabel} : <b style="float: right; margin-left: 20px;">${Math.abs(
                  value - gradeValue
                ).toFixed(2)}${gradeUnit}</b></div>`
              );
            }
            return arr.join('\n');
          },
        },
        color: ['#B3DBFA', '#409EFF', '#83AEDA', '#FBA110'],
        legend: {
          icon: 'circle',
          top: 10,
          right: 70,
          textStyle: {
            color: '#757C8C',
            fontSize: '14',
          },
          data: this.chartType === 'avgScore' ? ['班级均分', '年级均分'] : ['班级得分率', '年级得分率'],
          selected:
            this.chartType === 'avgScore'
              ? {
                  班级均分: true,
                  年级均分: true,
                }
              : {
                  班级得分率: true,
                  年级得分率: true,
                },
        },
        grid: {
          left: '3%',
          right: '10%',
          bottom: '12%',
          containLabel: true,
        },
        yAxis: {
          type: 'value',
          name: this.chartType === 'avgScore' ? '均分' : '得分率',
        },
        xAxis: {
          type: 'category',
          name: '班级',
          axisLine: {
            lineStyle: {
              color: '#757C8C',
            },
          },
          data: this.classNames,
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: (6 / _this.classNames.length) * 100,
          },
        ],
        series: [
          {
            name: this.chartType === 'avgScore' ? '班级均分' : '班级得分率',
            type: 'bar',
            label: {
              show: true,
              position: 'top',
              textStyle: {
                color: '#757C8C',
                fontSize: 14,
              },
              formatter: params => {
                if (this.chartType !== 'avgScore') return params.value + '%';
                return params.value;
              },
            },
            z: 2,
            data: this.classAvgList,
          },
          {
            name: this.chartType === 'avgScore' ? '年级均分' : '年级得分率',
            type: 'line',
            data: this.gradeAvgList,
            color: 'red',
            symbol: 'none',
            lineStyle: {
              opacity: 0,
            },
            markLine: {
              symbol: 'none',
              data: [
                {
                  type: 'average',
                  name: this.chartType === 'avgScore' ? '年级均分' : '年级得分率',
                },
              ],
              label: {
                formatter: this.chartType === 'avgScore' ? '年级均分:{c}分' : '年级得分率:{c}%',
                color: 'inherit',
              },
              itemStyle: {
                opacity: 0,
              },
              lineStyle: {
                width: 2,
                type: 'solid',
              },
            },
          },
        ],
      });
    },
  },
};
</script>

<style scoped></style>

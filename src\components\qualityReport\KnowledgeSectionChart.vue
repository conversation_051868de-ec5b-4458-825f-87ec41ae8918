<template>
  <div id="pointChartBox">
    <div v-show="!showDefault" id="knowledgeSectionChart"
         style="width: 100%; height: 480px"></div>
    <div v-show="showDefault">
      <div style="text-align: center"><img style="width: 350px;" :src="noResImg" alt=""></div>
      <p style="text-align: center;font-size: 16px;margin-bottom: 10px;">暂无数据!</p>
    </div>
  </div>
</template>

<script>
export default {
  name: "KnowledgeSectionChart",
  props: ['qualityInfo'],
  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      showDefault: false,
      knowledgeSectionChart: null,
    }
  },
  mounted(){
    let _this = this;
    window.onresize = ()  =>{
      return (() => {
        if(_this.knowledgeSectionChart) {
          this.resetDomSize('knowledgeSectionChart', 480);
          _this.knowledgeSectionChart.resize();
        }
      })()
    };
    // this.drawImg()
  },
  beforeDestroy() {
    if(this.knowledgeSectionChart != null && this.knowledgeSectionChart != "" && this.knowledgeSectionChart != undefined) {
      this.knowledgeSectionChart.dispose();
      this.knowledgeSectionChart = null;
    }
  },
  watch: {
    qualityInfo: {
      handler(val) {
        if(val.knowledge && val.knowledge.knowledges.length) {
          this.showDefault = false;
        }
        this.resetDomSize('knowledgeSectionChart', 480);
        this.$nextTick(()=>{
          this.drawImg();
        })
      },
      deep: true
    }
  },
  methods: {
    resetDomSize(el, height) {
      let width = document.getElementById('pointChartBox').clientWidth
      Object.defineProperty(document.getElementById(el),'clientWidth',
          {get:function(){return width;}, configurable: true})
      Object.defineProperty(document.getElementById(el),'clientHeight',
          {get:function(){return height;}, configurable: true})
    },
    handleChartData() {
      let data = {
        legendData: [],
        selected: {},
        seriesData: []
      }
      if(this.qualityInfo.knowledge && this.qualityInfo.knowledge.knowledges.length) {
        this.showDefault = false;
        this.qualityInfo.knowledge.knowledges.map((it, index) => {
          data.legendData.push(it.knowledgeName);
          data.selected[it.knowledgeName] = true;
          data.seriesData.push({
            name: it.knowledgeName,
            value: it.knowledgeRate
          })
        })
      } else {
        this.showDefault = true;
      }
      return data;
    },
    drawImg() {
      // this.genData(50)
      if(this.knowledgeSectionChart != null && this.knowledgeSectionChart != "" && this.knowledgeSectionChart != undefined) {
        this.knowledgeSectionChart.dispose();
        this.knowledgeSectionChart = null;
      }
      let data = this.handleChartData();
      let _this = this;
      // 基于准备好的dom，初始化echarts实例
      this.knowledgeSectionChart = this.$echarts.init(
          document.getElementById("knowledgeSectionChart")
      );
      // 绘制图表
      this.knowledgeSectionChart.setOption({
        title: {
          text: '知识点-题量占比',
          left: '35%',
          bottom: 40,
          textAlign: 'center',
          textStyle: {
            fontSize: 16,
            color: '#3F4A54'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: 题量占比{c} (饼图占比{d}%)'
        },
        legend: {
          icon: 'circle',
          type: 'scroll',
          orient: 'vertical',
          left: '75%',
          top: 20,
          bottom: 10,
          data: data.legendData,
          selected: data.selected,
          pageIcons: {
            vertical: [
              'path://M349.866667 640l166.4-166.4L682.666667 640a41.258667 41.258667 0 0 0 59.733333 0 41.258667 41.258667 0 0 0 0-59.733333l-196.266667-196.266667a41.258667 41.258667 0 0 0-59.733333 0l-196.266667 196.266667A42.24 42.24 0 1 0 349.866667 640z',
              'path://M674.133333 384l-166.4 166.4L341.333333 384a41.258667 41.258667 0 0 0-59.733333 0 41.258667 41.258667 0 0 0 0 59.733333l196.266667 196.266667a41.258667 41.258667 0 0 0 59.733333 0l196.266667-196.266667A42.24 42.24 0 1 0 674.133333 384z'
            ]
          },
          pageIconColor: '#757C8C',
          pageIconInactiveColor: '#E4E8EB'
        },
        series: [
          {
            name: '占比',
            type: 'pie',
            radius: '44%',
            center: ['35%', '45%'],
            data: data.seriesData,
            label: {
              show: false
            },
          }
        ]
      });
    },
    genData(count) {
      var nameList = [
        '赵', '钱', '孙', '李', '周', '吴', '郑', '王', '冯', '陈', '褚', '卫', '蒋', '沈', '韩', '杨', '朱', '秦', '尤', '许', '何', '吕', '施', '张', '孔', '曹', '严', '华', '金', '魏', '陶', '姜', '戚', '谢', '邹', '喻', '柏', '水', '窦', '章', '云', '苏', '潘', '葛', '奚', '范', '彭', '郎', '鲁', '韦', '昌', '马', '苗', '凤', '花', '方', '俞', '任', '袁', '柳', '酆', '鲍', '史', '唐', '费', '廉', '岑', '薛', '雷', '贺', '倪', '汤', '滕', '殷', '罗', '毕', '郝', '邬', '安', '常', '乐', '于', '时', '傅', '皮', '卞', '齐', '康', '伍', '余', '元', '卜', '顾', '孟', '平', '黄', '和', '穆', '萧', '尹', '姚', '邵', '湛', '汪', '祁', '毛', '禹', '狄', '米', '贝', '明', '臧', '计', '伏', '成', '戴', '谈', '宋', '茅', '庞', '熊', '纪', '舒', '屈', '项', '祝', '董', '梁', '杜', '阮', '蓝', '闵', '席', '季', '麻', '强', '贾', '路', '娄', '危'
      ];
      var legendData = [];
      var seriesData = [];
      var selected = {}
      for (var i = 0; i < count; i++) {
        var name = Math.random() > 0.65
            ? makeWord(4, 1) + '·' + makeWord(3, 0)
            : makeWord(2, 1);
        legendData.push(name);
        seriesData.push({
          name: name,
          value: Math.round(Math.random() * 100000)
        });
        selected[name] = i < 16;
      }

      return {
        legendData: legendData,
        seriesData: seriesData,
        selected: selected
      };

      function makeWord(max, min) {
        var nameLen = Math.ceil(Math.random() * max + min);
        var name = [];
        for (var i = 0; i < nameLen; i++) {
          name.push(nameList[Math.round(Math.random() * nameList.length - 1)]);
        }
        return name.join('');
      }
    }
  }
}
</script>

<style scoped>

</style>

<template>
  <div>
    <el-dialog
      custom-class="seled-tea-container"
      :visible="modalVisible"
      width="450px"
      :before-close="closeModal"
      :close-on-click-modal="false"
    >
      <div slot="title" class="dialog-title">
        <span style="margin-right: 10px">{{title}}</span>
      </div>
      <div class="sele-tea-area">
        <el-checkbox-group v-model="checkList">
              <el-checkbox
                v-for="(info, index) in copyList"
                :label="info.key"
                :key="info.key"
                >{{ info.name }}</el-checkbox
              >
            </el-checkbox-group>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeModal">取 消</el-button>
        <el-button type="primary" :loading="isComfirming" @click="sureClick">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getViewPaper,
} from '@/service/testbank';
export default {
  props: {
    modalVisible: {
      type: Boolean,
      required: true,
    },
    quesInfo: {
      type: Array,
      required: true,
    },
    copyPaperNo: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      default: 'objectSetting',
      required: true,
    },
  },
  data() {
    return {
      isComfirming: false,
      title: '', 
      checkList: [],
      copyList: [],
      objectCheckList: [
        {
          key: 'answer',
          name: '复用答案',
        },
        {
          key: 'score',
          name: '复用分值',
        },
      ],
      subjectCheckList: [
      {
          key: 'points',
          name: '复用题块',
        },
        {
          key: 'score',
          name: '复用分值',
        },
      ],
      aiCheckList: [
      {
          key: 'answer',
          name: '复用答案',
        },
        {
          key: 'score',
          name: '复用分值',
        },
      ],
      teacherCheckList: [
      {
          key: 'correctMode',
          name: '复用评阅方式',
        },
        {
          key: 'assignType',
          name: '复用阅卷分配',
        },
        {
          key: 'leaderList',
          name: '复用题组长',
        },
        {
          key: 'teacherList',
          name: '复用阅卷教师',
        }, 
        {
          key: 'arbitration',
          name: '复用仲裁设置及仲裁教师',
        },
      ],
      copyInfo: {}
    };
  },
  created() {
    const typeConfig = {
      objectSetting: {
      title: '复制答案和分值',
      copyList: this.objectCheckList
    },
    subjectSetting: {
      title: '复制题块和分值',
      copyList: this.subjectCheckList
    },
    aiSetting: {
      title: '复制答案和分值',
      copyList: this.aiCheckList
    },
    compositeSetting: {
      title: '复制阅卷任务',
      copyList: this.teacherCheckList
    }
  };

  const config = typeConfig[this.type] || { title: '', copyList: [] };
  this.title = config.title;
  this.copyList = config.copyList;
  if(this.type == 'objectSetting' || this.type == 'subjectSetting' || this.type == 'aiSetting') {
    this.getViewPaper();
  }
},
  mounted() {},
  methods: {
    getViewPaper() {
      getViewPaper({
        paperNo: this.copyPaperNo,
      }).then(res => {
        this.copyInfo = JSON.parse(res.data.teamInfo);
      });
    },
    isMatchProperty(item) {
      if(this.type == 'objectSetting'){
        return [1,2,8].includes(Number(item.typeId));
      }else if(this.type == 'subjectSetting'){
        return [3,6,9].includes(Number(item.typeId));
      }else if(this.type == 'aiSetting'){
        return item.typeId == 7;
      }
      return false;
    },
    buildCopyInfo() {
      let matchDatas = [];
      this.copyInfo.forEach((item, index) => {
        item.data.forEach((sitem, sindex) => {
          if (sitem.data && sitem.data.length > 0) {
            sitem.data.forEach((titem, tindex) => {
              if (this.isMatchProperty(titem)) {
                matchDatas.push(titem);
              }
            })
          } else {
            if (this.isMatchProperty(sitem)) {
              matchDatas.push(sitem);
            }
          }
        })
      })

      this.quesInfo.forEach((item, index) => {
        item.data.forEach((sitem, sindex) => {
          if (sitem.data && sitem.data.length > 0) {
            sitem.data.forEach((titem, tindex) => {
              if (this.isMatchProperty(titem)) {
                let cData = matchDatas.shift()
                if (!cData) return;
                if (titem.typeId != cData.typeId) return;
                this.checkList.forEach(key => {
                  titem[key] = cData[key];
                  if (key == 'score' && titem.typeId == 1) {
                    //多选题复制分值 同步复制规则
                    titem.rules = cData.rules;
                    titem.ruleType = cData.ruleType;
                    titem.halfScore = cData.halfScore;
                  }
                })
              }
            })
          } else {
            if (this.isMatchProperty(sitem)) {
              let cData = matchDatas.shift();
              if (!cData) return;
              if (sitem.typeId != cData.typeId) return;
              this.checkList.forEach(key => {
                sitem[key] = cData[key];
                if (key == 'score' && sitem.typeId == 1) {
                  //多选题复制分值 同步复制规则
                  sitem.rules = cData.rules;
                  sitem.ruleType = cData.ruleType;
                  sitem.halfScore = cData.halfScore;
                }
              })
            }
          }
        })
      });
    },
    closeModal() {
      this.$emit('close-copy-set-data');
    },
    sureClick() {
      if(this.checkList.length == 0) {
        this.$message.warning('请选择要复制的选项');
        return;
      }
      this.buildCopyInfo();
      this.$emit('confirm-copy-set-data', this.quesInfo);
          this.$emit('close-copy-set-data');
    },
  },
};
</script>

<style>
</style>
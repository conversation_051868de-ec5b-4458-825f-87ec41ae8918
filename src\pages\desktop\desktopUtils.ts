/*
 * @Description:
 * @Author: 小圆
 * @Date: 2025-08-30 14:48:56
 * @LastEditors: 小圆
 */
import router from '@/router/baseRouter';
import { sessionSave } from '@/utils';

// 打开路由，在新窗口打开
export function openRoute(routePath, query = {}) {
  let domain = 'https://gecetest.iclass30.com';
  if (process.env.VUE_APP_BASE_API == 'https://test.iclass30.com') {
    domain = 'https://gecetest.iclass30.com';
  } else {
    domain = 'https://gc.iclass30.com';
  }

  const userid = sessionSave.get('loginInfo').id;
  const routerResolve = router.resolve({
    path: routePath,
    query: {
      ...query,
      userid: userid,
    },
  });

  const url = domain + routerResolve.href;
  openBrowser(url);
}

// 打开浏览器
export function openBrowser(url: string): void {
  if (window !== window.parent) {
    let topWindow: any = window.top;
    if (topWindow.wincore) {
      topWindow.wincore.System.shell_execute(url, '', 'open', true);
      return;
    }
  }
  // 未找到wincore或非嵌套环境,使用默认浏览器打开
  window.open(url, '_blank');
}

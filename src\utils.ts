/***
 * 工具类api
 **/
import Vue from 'vue';
import axios from 'axios';
import store from './store/index';
import SparkMD5 from 'spark-md5';
import { uuid } from 'uuid/v1';
import lodash from 'lodash';
import { cefMsg } from './callCplus/index';

// 取消http请求
export const clearHttpRequestingList = (uuidKey, isCancel) => {
    if (!Vue.$httpRequestList) Vue.$httpRequestList = new Map();
    if (Vue.$httpRequestList.size) {
        if (!uuidKey) {
            Vue.$httpRequestList.forEach(item => {
                item();
            });
            Vue.$httpRequestList.clear();
            return;
        }
        if (isCancel) {
            Vue.$httpRequestList.forEach((item, uuid) => {
                if ((uuidKey && uuid.indexOf(uuidKey) > -1) || !uuid) {
                    item();
                    Vue.$httpRequestList.delete(uuid);
                }
            });
        } else {
            Vue.$httpRequestList.delete(`${uuidKey.cancelTokenTag}_${uuidKey.cancelTokenUuid}`);
        }
    }
};

// 全局获取url参数
export const getQueryString = name => {
    try {
        // 获取url中"?"符后的字串
        let url = decodeURIComponent(location.search) || '?' + decodeURIComponent(location.hash.split('?')[1]);
        let theRequest = {};
        if (url.indexOf('?') !== -1) {
            let str = url.substr(1);
            let strs = str.split('&');
            for (let i = 0; i < strs.length; i++) {
                theRequest[strs[i].split('=')[0]] = strs[i].split('=')[1];
            }
        }
        return name ? theRequest[name] : theRequest;
    } catch (e) {
        console.log(' getQueryString function error=' + e.message);
    }
};

// cookie存取操作
export const cookieSave = {
    // 设置cookies
    set: function (name, value) {
        let Days = 30;
        let exp = new Date();
        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = name + '=' + escape(JSON.stringify(value)) + ';expires=' + exp.toGMTString();
    },
    // 读取cookies
    get: function (name) {
        let arr;
        let reg = new RegExp('(^| )' + name + '=([^]*)(|$)');
        if (document.cookie.match(reg)) {
            arr = document.cookie.match(reg);
            let _result = unescape(arr[2]).split(';')[0];
            try {
                _result = JSON.parse(_result);
            } catch (err) {}
            return _result;
        } else {
            return null;
        }
    },
    // 删除cookies
    del: function (name) {
        let exp = new Date();
        exp.setTime(exp.getTime() - 1);
        let cval = this.$cookieSave.get(name);
        if (cval != null) document.cookie = name + '=' + cval + 'expires=' + exp.toGMTString();
    },
};

export const isMobileMode = () => {
    return navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
    );
};

export const getQuesType = type => {
    let quesType = '';
    switch (type) {
        case 1:
            quesType = '多选题';
            break;
        case 2:
            quesType = '判断题';
            break;
        case 3:
            quesType = '填空题';
            break;
        case 4:
            quesType = '语音题';
            break;
        case 5:
            quesType = '英语自动评测题';
            break;
        case 6:
            quesType = '简答题';
            break;
        case 7:
            quesType = '填空题智批题';
            break;
        case 8:
            quesType = '单选题';
            break;
        default:
            quesType = '';
            break;
    }
    return quesType;
};

export const getQuesTypeId = typeName => {
    let quesTypeId: string | number = '';
    switch (typeName) {
        case '多选题':
            quesTypeId = 1;
            break;
        case '判断题':
            quesTypeId = 2;
            break;
        case '填空题':
            quesTypeId = 3;
            break;
        case '语音题':
            quesTypeId = 4;
            break;
        case '英语自动评测题':
            quesTypeId = 5;
            break;
        case '简答题':
            quesTypeId = 6;
            break;
        case '填空题智批题':
            quesTypeId = 7;
            break;
        case '单选题':
            quesTypeId = 8;
            break;
        default:
            quesTypeId = '';
            break;
    }
    return quesTypeId;
};

/**
 * @name：根据题型id判断是否是客观题
 * @param {*} type
 * @returns
 */
export const isObjective = type => {
    let isObj = false;
    if (type == 1 || type == 2 || type == 8) {
        isObj = true;
    }
    return isObj;
};

/**
 * @name: 判断是否为空,null或undefined
 * @return: boolean
 */
export const isNullOrUndefined = ang => {
    return ang == null || typeof ang == 'undefined' || ang === '';
};

export const creatCompare = propertyName => {
    //根据哪个属性值排序
    return function (obj1, obj2) {
        //对象两两比较
        let value1 = obj1[propertyName];
        let value2 = obj2[propertyName];
        if (value1 < value2) {
            return -1;
        } else if (value1 > value2) {
            return 1;
        } else {
            return 0;
        }
    };
};

/**
 * @name: 判断是否支持公式学科
*/
export const isSupportMathSubject = (subjectId: number) => {
    //数 理 化 生 科学
    return [23, 2, 4, 5, 51, 6, 11, 13, 14, 15].includes(subjectId);
};

export const getSortType = type => {
    let sortType = '';
    switch (type) {
        case '单选题':
            sortType = 1;
            break;
        case '多选题':
            sortType = 2;
            break;
        case '判断题':
            sortType = 3;
            break;
        case '填空题':
            sortType = 4;
            break;
        case '填空题智批题':
            sortType = 5;
            break;
        case '简答题':
            sortType = 6;
            break;
        default:
            sortType = 9;
    }
    return sortType;
};

export const katexUpdate = () => {
    let texList = document.getElementsByClassName('math-tex');

    let reg = /\\\((.*)\\\)/;
    for (let i = texList.length - 1; i >= 0; i--) {
        let it = texList[i];
        let match = it.innerText.match(reg);

        if (match) {
            try {
                it.outerHTML = katex.renderToString(match[1], {
                    throwOnError: true,
                    displayMode: true,
                    leqno: false,
                    fleqn: false,
                    errorColor: '#cc0000',
                    strict: 'warn',
                    output: 'htmlAndMathml',
                    trust: false,
                    macros: {
                        '\\f': 'f(#1)',
                    },
                });
            } catch (e) {
                console.log(match[1]);
            }
        } else {
            console.debug('no match:' + it.innerText);
        }
    }
    if (document.getElementsByClassName('math-tex').length) {
        mathJaxUpdate();
    }
};

export const mathJaxUpdate = () => {
    let success = false;

    if (MathJax && MathJax.startup) {
        try {
            MathJax.startup.getComponents();
            MathJax.typeset();
            success = true;
        } catch (e) {}
    }

    if (!success) {
        setTimeout(() => {
            mathJaxUpdate();
        }, 100);
    }
};

// 阿拉伯数字转中文数字
export const sectionToChinese = number => {
    let units = '个十百千万@#%亿^&~';
    let chars = '零一二三四五六七八九';
    let a = (number + '').split(''),
        s = [];
    if (a.length > 12) {
        throw new Error('too big');
    } else {
        for (let i = 0, j = a.length - 1; i <= j; i++) {
            if (j == 1 || j == 5 || j == 9) {
                //两位数 处理特殊的 1*
                if (i == 0) {
                    if (a[i] != '1') s.push(chars.charAt(a[i]));
                } else {
                    s.push(chars.charAt(a[i]));
                }
            } else {
                s.push(chars.charAt(a[i]));
            }
            if (i != j) {
                s.push(units.charAt(j - i));
            }
        }
    }
    //return s;
    return s
        .join('')
        .replace(/零([十百千万亿@#%^&~])/g, function (m, d, b) {
            //优先处理 零百 零千 等
            b = units.indexOf(d);
            if (b !== -1) {
                if (d === '亿') return d;
                if (d === '万') return d;
                if (a[j - b] == '0') return '零';
            }
            return '';
        })
        .replace(/零+/g, '零')
        .replace(/零([万亿])/g, function (m, b) {
            // 零百 零千处理后 可能出现 零零相连的 再处理结尾为零的
            return b;
        })
        .replace(/亿[万千百]/g, '亿')
        .replace(/[零]$/, '')
        .replace(/[@#%^&~]/g, function (m) {
            return {
                '@': '十',
                '#': '百',
                '%': '千',
                '^': '十',
                '&': '百',
                '~': '千',
            }[m];
        })
        .replace(/([亿万])([一-九])/g, function (m, d, b, c) {
            c = units.indexOf(d);
            if (c !== -1) {
                if (a[j - c] == '0') return d + '零' + b;
            }
            return m;
        });
};
// 缓存读取操作
export const localSave = {
    get: function (key) {
        let data = localStorage.getItem(key);
        try {
            data = JSON.parse(data);
        } catch (err) {}
        return data;
    },
    set: function (key, val) {
        // 处理浏览器缓存超出限制的问题，自动清除两个班级的容量
        (function () {
            let size = 0;
            for (let item in window.localStorage) {
                if (window.localStorage.hasOwnProperty(item)) {
                    size += window.localStorage.getItem(item).length;
                }
            }
            // 计算缓存剩余容量
            let _residue = 5120 - (size / 1024).toFixed(2);
            // console.log(`剩余容量：${_residue}KB`);
            // 当剩余容量少于100kb时,删除两个班级的缓存
            if (_residue < 100) {
                console.log(`缓存容量不足,清除所有缓存`);
                window.localStorage.clear();
            }
        })();
        try {
            val = JSON.stringify(val);
            localStorage.setItem(key, val);
        } catch (err) {}
    },
    remove: function (key) {
        if (!key) {
            localStorage.clear();
        } else {
            localStorage.removeItem(key);
        }
    },
};

interface ISessionSave {
    get(key: 'loginInfo'): LoginInfo;
    get(key: 'schoolInfo'): SchoolInfo;
    get(key: 'reportDetail'): IExamReportInfo;
    get(key: 'reportParent'): IExamReportInfo;
    get(key: 'schoolReportPermission'): ISchoolReportPermission;
    get(key: 'innerClassList'): IInnerClassList[];
    get(key: 'innerSubjectList'): IInnerSubjectList[];
    get(key: string): any;
    set(key: string, val: any): void;
    remove(key: string): void;
}

// 会话缓存读取操作
export const sessionSave: ISessionSave = {
    get: function (key: string) {
        let data = sessionStorage.getItem(key);
        try {
            data = JSON.parse(sessionStorage.getItem(key));
        } catch (err) {}
        return data;
    },
    set: function (key, val) {
        val = JSON.stringify(val);
        sessionStorage.setItem(key, val);
    },

    remove: function (key) {
        if (!key) {
            sessionStorage.clear();
        } else {
            sessionStorage.removeItem(key);
        }
    },
};

export function deepClone(data) {
    if (!data) return data;

    return JSON.parse(JSON.stringify(data));
}

// axios请求
export const httpAxios = (baseURL, url, method, params) => {
    return axios.request({
        baseURL: baseURL,
        url: url,
        method: method,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
        },
        params: params,
        data: params,
        timeout: 60000,
        responseType: 'json',
    });
};

/**
 * @description: 通过链接获取页面信息
 * @param {*} url
 * @return {*}
 */
export async function getPageInfoFromLink(url) {
    try {
        // 使用 Fetch API 获取页面内容
        const response = await fetch(url);
        const html = await response.text();

        // 使用 DOMParser 解析 HTML
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // 获取标题
        const title = doc.querySelector('title')?.innerText || '无标题';

        // 获取 favicon
        let favicon =
            doc.querySelector('link[rel="icon"]')?.href || doc.querySelector('link[rel="shortcut icon"]')?.href;

        // 处理相对路径
        if (favicon && !favicon.startsWith('http')) {
            const urlObj = new URL(url);
            favicon = new URL(favicon, urlObj.origin).href;
        }

        return { title, favicon };
    } catch (error) {
        console.error('获取页面信息失败:', error);
        return null;
    }
}

// 全局发布订阅模式对象
export const subsEvents = (() => {
    let list = {},
        listen,
        trigger,
        remove;
    listen = function (key, fn) {
        if (!list[key]) {
            list[key] = [];
        }
        list[key].push(fn);
    };
    trigger = function () {
        let key = Array.prototype.shift.call(arguments),
            fns = list[key];
        if (!fns || fns.length === 0) {
            return false;
        }
        for (let i = 0, fn; (fn = fns[i++]); ) {
            fn.apply(this, arguments);
        }
    };
    remove = function (key, fn) {
        let fns = list[key];
        if (!fns) {
            return false;
        }
        if (!fn) {
            fns && (fns.length = 0);
        } else {
            for (let i = fns.length - 1; i >= 0; i--) {
                let _fn = fns[i];
                if (_fn === fn) {
                    fns.splice(i, 1);
                }
            }
        }
        delete list[key];
    };
    return {
        listen: listen,
        trigger: trigger,
        remove: remove,
        list,
    };
})();

/**
 * @name: 是否JSON
 * @param param 待检测片段
 */
export const isJSON = param => {
    if (isNullOrUndefined(param) || typeof param != 'string') {
        return false;
    }
    let resObj;
    try {
        resObj = JSON.parse(param);
    } catch (err) {
        return false;
    }
    if (typeof resObj == 'object') {
        return true;
    }
    return false;
};
/**
 * @description: 获取火花预览链接
 * @return {*}
 */
export async function getHuohuaPreviewLink(resUrl, huohua_id) {
    const BASE_API = process.env.VUE_APP_BASE_API;
    let baseURL = BASE_API.includes('test') ? 'https://test.huohuaschool.com' : 'https://prod.huohuaschool.com';
    let htokenRes = await httpAxios(baseURL, 'api-hh/oauth/request/token', 'GET', {
        userId: huohua_id,
        clientId: 'daite-znbk',
    });

    if (htokenRes.status !== 200) {
        logger.error('火花预览失败，', JSON.stringify(htokenRes));
        return resUrl;
    }

    let curUrl = '';
    let htoken = htokenRes.data.token;

    if (resUrl.includes('/play/') && resUrl.includes('?')) {
        curUrl = resUrl.split('?')[0] + `?token=${htoken}&appkey=Daite`;
    } else {
        curUrl = `${resUrl.slice(0, 39)}play/${resUrl.slice(39)}${
            resUrl.includes('?') ? '&' : '?'
        }token=${htoken}&appkey=Daite`;
    }

    return curUrl;
}

/**
 * @name: 获取浏览器内核参数
 * @param n
 * @returns
 */
function getBrowser(n) {
    let ua = navigator.userAgent.toLowerCase(),
        s,
        name = '',
        ver = 0;
    // 探测浏览器
    (s = ua.match(/msie ([\d.]+)/))
        ? _set('ie', _toFixedVersion(s[1]))
        : (s = ua.match(/firefox\/([\d.]+)/))
        ? _set('firefox', _toFixedVersion(s[1]))
        : (s = ua.match(/chrome\/([\d.]+)/))
        ? _set('chrome', _toFixedVersion(s[1]))
        : (s = ua.match(/opera.([\d.]+)/))
        ? _set('opera', _toFixedVersion(s[1]))
        : (s = ua.match(/version\/([\d.]+).*safari/))
        ? _set('safari', _toFixedVersion(s[1]))
        : 0;

    function _toFixedVersion(ver, floatLength?) {
        ver = ('' + ver).replace(/_/g, '.');
        floatLength = floatLength || 1;
        ver = String(ver).split('.');
        ver = ver[0] + '.' + (ver[1] || '0');
        ver = Number(ver).toFixed(floatLength);
        return ver;
    }

    function _set(bname, bver) {
        name = bname;
        ver = bver;
    }

    return n === 'n' ? name : n === 'v' ? ver : name + ver;
}

/**
 * @name: 判断元素父节点是否包含class
 * @param node
 * @param parentClassName
 * @returns
 */
function parentIndexOf(node, parentClassName) {
    if (node.className.indexOf(parentClassName) !== -1) {
        return node;
    }
    for (let i = 0, n = node; (n = n.parentNode); i++) {
        if (n.className.indexOf(parentClassName) !== -1) {
            // console.log(n.className)
            return n;
        }
        // 找不到目标父节点，防止死循环
        if (n === document.documentElement) {
            return false;
        }
    }
}

/**
 * @name: 获取屏幕宽高
 * @returns
 */
function getViewportSize() {
    return {
        width: window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth,
        height: window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight,
    };
}

/**
 * @name: 数组排序
 * @param name
 * @param minor
 * @returns
 */
function sortBy(name, minor) {
    return function (o, p, bool) {
        // 当bool为true实现正向排序
        let a, b;
        if (o && p && typeof o === 'object' && typeof p === 'object') {
            a = o[name];
            b = p[name];
            if (a === b) {
                return typeof minor === 'function' ? minor(o, p, true) : 0;
            }
            if (typeof a === typeof b) {
                if (bool) {
                    return a < b ? -1 : 1;
                }
                return a < b ? 1 : -1;
            }
            if (bool) {
                return typeof a < typeof b ? -1 : 1;
            }
            return typeof a < typeof b ? 1 : -1;
        } else {
            throw 'error';
        }
    };
}

/**
 * @name: 数组排序
 * @param arr1
 * @returns
 */
function arrSort(arr1) {
    var arr = []; //定义一个临时数组
    for (var i = 0; i < arr1.length; i++) {
        if (arr.indexOf(arr1[i]) == -1) {
            arr.push(arr1[i]);
        }
    }
    var sequence = function (a, b) {
        return a - b;
    };
    return arr.sort(sequence);
}

/**
 * @name: 数组对象去重
 * @param arr
 * @param name
 * @returns
 */
function arrayUnique(arr, name) {
    let hash = {};
    return arr.reduce(function (item, next) {
        hash[next[name]] ? '' : (hash[next[name]] = true && item.push(next));
        return item;
    }, []);
}

/**
 * @name: 非对象数组去重
 * @param arr
 * @returns
 */
function removeDuplicatedItem(arr) {
    for (let i = 0; i < arr.length - 1; i++) {
        for (let j = i + 1; j < arr.length; j++) {
            if (arr[i] === arr[j]) {
                arr.splice(j, 1);
                j--;
            }
        }
    }
    return arr;
}

/**
 * @name: 时间格式化
 * @param date
 * @returns
 */
function formatDate(date) {
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let weekday = date.getDate();

    if (month < 10) {
        month = '0' + month;
    }
    if (weekday < 10) {
        weekday = '0' + weekday;
    }
    return year + '-' + month + '-' + weekday;
}

/**
 * @name: 时间格式化
 * @param inputTime
 * @returns
 */
function formatStrToDate(inputTime) {
    let date = new Date(inputTime);
    let y = date.getFullYear();
    let m = date.getMonth() + 1;
    m = m < 10 ? '0' + m : m;
    let d = date.getDate();
    d = d < 10 ? '0' + d : d;
    return y + '-' + m + '-' + d;
}

/**
 * @name: body超出隐藏/显示
 * @param data
 */
function bodyOverFlow(data) {
    if (data === 'hide') {
        document.getElementsByTagName('body')[0].className = 'over-hide';
    } else {
        document.body.removeAttribute('class', 'over-hide');
    }
}

/**
 * @name: 将数据按属性分组
 * @param list 需要分组的列表
 * @param timename 时间属性
 * @param isShowDate 是否显示日期
 * @returns
 */
function orderByTime(list, timename, isShowDate) {
    timename = timename || 'update_time_stamp';
    let map = {};
    let arrTemp = [];
    for (let i = 0; i < list.length; i++) {
        let ai = list[i];
        let _time = new Date(Number(ai[timename]));
        let _createTime = `${_time.getFullYear()}年${_time.getMonth() + 1}月`;
        let _hours = _time.getHours(),
            _minute = _time.getMinutes();

        if (isShowDate === true) {
            ai.date_time = `${_time.getFullYear()}年${_time.getMonth() + 1}月${_time.getDate()}日 ${
                _hours < 10 ? '0' + _hours : _hours
            }:${_minute < 10 ? '0' + _minute : _minute}`;
        } else {
            ai.date_time = `${_createTime} ${_hours < 10 ? '0' + _hours : _hours}:${
                _minute < 10 ? '0' + _minute : _minute
            }`;
        }
        if (!map[_createTime]) {
            arrTemp.push({
                update_time_stamp: _createTime,
                timeStamp: ai[timename],
                hide: !!ai.hide,
                data: [ai],
            });
            map[_createTime] = ai;
        } else {
            for (let j = 0; j < arrTemp.length; j++) {
                let dj = arrTemp[j];
                if (dj.update_time_stamp === _createTime) {
                    dj.data.push(ai);
                    break;
                }
            }
        }
    }
    return arrTemp;
}

/**
 * @name: 是否JSON
 * @param param 待检测片段
 */
function isJson(param) {
    if (isNullOrUndefined(param) || typeof param != 'string') {
        return false;
    }
    let resObj;
    try {
        resObj = JSON.parse(param);
    } catch (err) {
        return false;
    }
    if (typeof resObj == 'object') {
        return true;
    }
    return false;
}

// C++调用js的方法
window.executePdu = (cmd, data) => {
    console.log(data, '获得返回数据');
    try {
        data = data.replace(/&apos;/g, "'").replace(/&quot;/g, '\\"');
    } catch (err) {
        console.log(err);
    }
    if (typeof data === 'string') {
        data = JSON.parse(data);
    }

    if (subsEvents.list[`executePdu_${data.sortid}`]) {
        subsEvents.trigger(`executePdu_${data.sortid}`, data);
    }
};

export type HttpAxios = typeof httpAxios;
export type ClearHttpRequestingList = typeof clearHttpRequestingList;
export type CefMsg = typeof cefMsg;
export type UUid = typeof uuid;
export type Lodash = typeof lodash;
export type GetQueryString = typeof getQueryString;
export type SectionToChinese = typeof sectionToChinese;
export type GetSortType = typeof getSortType;
export type CreatCompare = typeof creatCompare;
export type ArrayUnique = typeof arrayUnique;
export type CookieSave = typeof cookieSave;
export type LocalSave = typeof localSave;
export type SessionSave = typeof sessionSave;
export type GetQuesType = typeof getQuesType;
export type GetQuesTypeId = typeof getQuesTypeId;
export type IsObjective = typeof isObjective;
export type KatexUpdate = typeof katexUpdate;
export type MathJaxUpdate = typeof mathJaxUpdate;
export type GetBrowser = typeof getBrowser;
export type ParentIndexOf = typeof parentIndexOf;
export type DeepClone = typeof deepClone;
export type GetViewportSize = typeof getViewportSize;
export type SortBy = typeof sortBy;
export type ArrSort = typeof arrSort;
export type RemoveDuplicatedItem = typeof removeDuplicatedItem;
export type FormatDate = typeof formatDate;
export type FormatStrToDate = typeof formatStrToDate;
export type BodyOverFlow = typeof bodyOverFlow;
export type OrderByTime = typeof orderByTime;
export type IsJson = typeof isJson;

export default {
    install: function (Vue, options) {
        Vue.prototype.$http = httpAxios;
        Vue.prototype.$clearHttpRequestingList = clearHttpRequestingList;
        Vue.prototype.$cefMsg = cefMsg;
        Vue.prototype.$uuid = uuid;
        Vue.prototype.$_ = lodash;
        Vue.prototype.$getQueryString = getQueryString;
        Vue.prototype.$sectionToChinese = sectionToChinese;
        Vue.prototype.$getSortType = getSortType;
        Vue.prototype.$creatCompare = creatCompare;
        Vue.prototype.$cookieSave = cookieSave;
        Vue.prototype.$localSave = localSave;
        Vue.prototype.$sessionSave = sessionSave;
        Vue.prototype.$getQuesType = getQuesType;
        Vue.prototype.$getQuesTypeId = getQuesTypeId;
        Vue.prototype.$isObjective = isObjective;
        Vue.prototype.$katexUpdate = katexUpdate;
        Vue.prototype.$mathJaxUpdate = mathJaxUpdate;
        Vue.prototype.$getBrowser = getBrowser;
        Vue.prototype.$parentIndexOf = parentIndexOf;
        Vue.prototype.$deepClone = deepClone;
        Vue.prototype.$getViewportSize = getViewportSize;
        Vue.prototype.$sortBy = sortBy;
        Vue.prototype.$arrSort = arrSort;
        Vue.prototype.$arrayUnique = arrayUnique;
        Vue.prototype.$removeDuplicatedItem = removeDuplicatedItem;
        Vue.prototype.$formatDate = formatDate;
        Vue.prototype.$formatStrToDate = formatStrToDate;
        Vue.prototype.$bodyOverFlow = bodyOverFlow;
        Vue.prototype.$orderByTime = orderByTime;
        Vue.prototype.$isJson = isJson;
    },
};

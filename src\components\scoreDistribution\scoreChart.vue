<template>
  <div>
    <el-radio-group style="padding: 20px" v-model="chartType" @input="drawImg">
      <el-radio-button
        v-for="item in chartTypeListMap[checktype]"
        :key="item.label"
        :label="item.label"
        >{{ item.name }}</el-radio-button
      >
    </el-radio-group>
    <div
      v-show="!showDefault"
      id="totalRankChart"
      class="echart"
      ref="echart"
      style="width: 100%; height: 450px"
    ></div>
    <!-- <div v-show="showDefault">
      <div style="text-align: center">
        <img style="width: 350px" :src="noResImg" alt="" />
      </div>
      <p style="text-align: center; font-size: 16px; margin-top: 10px">暂无数据!</p>
    </div> -->
  </div>
</template>

<script>
const chartTypeListMap = {
  avg: [
    { label: 0, name: '按考试维度' },
    {
      label: 1,
      name: '按班级维度',
    },
  ],
  avgDif: [
    {
      label: 0,
      name: '按考试维度',
    },
    {
      label: 1,
      name: '按班级维度',
    },
  ],
  subject: [
    {
      label: 0,
      name: '按考试维度',
    },
    {
      label: 1,
      name: '按学科维度',
    },
  ],
};

export default {
  name: 'scoreChart',
  props: {
    tableData: {
      type: Array,
      default() {
        return [];
      },
    },
    targetData: {
      type: Array,
      default() {
        return [];
      },
    },
    // avg：分数分布；avgDif：均差分布；subject：学科分布；
    checktype: {
      type: String,
      default() {
        return '';
      },
    },
  },
  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      totalRankChart: null,
      showDefault: false,
      source: [],
      seriesData: [],
      xAxisTitle: [],
      loadingChart: false,
      // 图表类型
      chartType: 0,
      // 图表类型对象列表
      chartTypeListMap: chartTypeListMap,
    };
  },
  watch: {
    tableData: {
      handler(val) {
        if (val.length) {
          this.showDefault = false;
        }
        this.resetDomSize('totalRankChart', 400);
        this.$nextTick(() => {
          this.drawImg();
        });
      },
      deep: true,
    },
  },
  beforeDestroy() {
    if (
      this.totalRankChart != null &&
      this.totalRankChart != '' &&
      this.totalRankChart != undefined
    ) {
      this.totalRankChart.dispose();
      this.totalRankChart = null;
    }
  },
  mounted() {
    let _this = this;
    window.onresize = () => {
      return (() => {
        if (_this.totalRankChart) {
          this.resetDomSize('totalRankChart', 400);
          _this.totalRankChart.resize();
        }
      })();
    };
    if (!this.totalRankChart) {
      this.drawImg();
    }
  },
  methods: {
    resetDomSize(el, height) {
      let width = document.getElementById('totalRankChart').clientWidth;
      Object.defineProperty(document.getElementById(el), 'clientWidth', {
        get: function () {
          return width;
        },
        configurable: true,
      });
      Object.defineProperty(document.getElementById(el), 'clientHeight', {
        get: function () {
          return height;
        },
        configurable: true,
      });
    },
    /**
     * @name:组装数据
     */
    handleChartData() {
      let data = this.tableData && this.tableData.length ? this.tableData : [];
      if (data.length) {
        this.seriesData = [];
        if (this.checktype == 'avg') {
          this.seriesData = this.getAvgSeriesData(data);
        } else if (this.checktype == 'avgDif') {
          this.seriesData = this.getAvgDifSeriesData(data);
        } else if (this.checktype == 'subject') {
          this.seriesData = this.getSubjectSeriesData(data);
        }
      } else {
        this.showDefault = true;
      }
    },
    drawImg() {
      if (
        this.totalRankChart != null &&
        this.totalRankChart != '' &&
        this.totalRankChart != undefined
      ) {
        this.totalRankChart.dispose();
        this.totalRankChart = null;
      }
      this.handleChartData();
      let _this = this;
      this.totalRankChart = this.$echarts.init(this.$refs.echart);
      this.totalRankChart.setOption({
        // color: [
        //   "#409EFF",
        //   "#07C29D",
        //   "#FF6A68",
        //   "#3E73F6",
        //   "#FFB400",
        //   "#BD3DDD",
        //   "#9EEBAA",
        // ],
        legend: {
          // icon: "circle",
          top: 10,
          right: 50,
          formatter: function (name) {
            return name.length > 25 ? name.substr(0, 25) + '...' : name;
          }, //开启tooltip
          tooltip: {
            show: true,
          },
        },
        tooltip: {},
        grid: {
          left: '3%',
          right: '6%',
          top: '21%',
          // bottom: '10%',
          containLabel: true,
        },
        // dataset: {
        //   source: _this.source
        // },
        splitLine: { show: false },
        splitArea: { show: false },
        xAxis: {
          type: 'category',
          // name: '班级',
          data: _this.xAxisTitle,
          triggerEvent: true,
          axisLabel: {
            interval: 0,
            rotate: _this.checktype == 'avg' ? 30 : 0,
            formatter: function (value) {
              if (value.length > 15) {
                value = value.substring(0, 15) + '..';
              }
              return value;
            },
          },
        },
        yAxis: {
          type: 'value',
          // name: '人数'
        },
        dataZoom: [
          {
            type: 'slider',
            startValue: 0,
            endValue: 10,
          },
        ],
        series: _this.seriesData,
      });
      if (this.checktype == 'avg') {
        _this.extensionOne(_this.totalRankChart);
      }
    },
    /**
     * @name:x轴悬浮展示
     */
    extensionOne(myChart) {
      var id = document.getElementById('extensionOne');
      //判断是否创建过div框,如果没有创建过，则创建。（创建时，默认隐藏）
      if (!id) {
        var div = "<div id = 'extensionOne' style='display:none'></div>";
        $('html').append(div);
      }
      var arrow_left = '20px';
      //鼠标悬浮事件
      myChart.on('mouseover', function (params) {
        if (params.componentType != 'xAxis') {
          return;
        }

        //设置div框样式，并为其填充值。
        $('#extensionOne')
          .css({
            position: 'absolute',
            color: '#90979c',
            // "border": "solid 0px white",
            'font-family': 'Arial',
            'font-size': '14px',
            padding: '5px',
            display: 'inline',
          })
          .text(params.value);
        var xx_text = params.event.offsetX - 35;
        arrow_left = xx_text;

        $('#totalRankChart').mousemove(function (event) {
          // console.log("X轴坐标：" + event.pageX + " Y轴坐标：" + event.pageY);
          var xx = event.pageX - 30;
          var yy = event.pageY + 10;
          $('#extensionOne').css('top', yy).css('left', xx);
        });
      });

      myChart.on('mouseout', function (params) {
        $('#extensionOne').css('display', 'none');
      });
    },

    // 获取分数分布数据
    getAvgSeriesData() {
      let seriesData = [];
      if (this.chartType == 0) {
        this.xAxisTitle = this.targetData.map(res => res.name);
        this.tableData.forEach((ite, inde) => {
          let arr = [];
          for (let i in this.targetData) {
            arr.push(ite.data[i].avg);
          }
          seriesData.push({
            name: ite.clzName,
            data: arr,
            type: ite.clzName.indexOf("年级平均") != -1 ? "line" : "bar",
            label: {
              show: true,
              position: "top",
              formatter: "{c}",
            },
          });
        });
      } else {
        this.xAxisTitle = this.tableData.map(res => res.clzName);

        this.targetData.forEach((exam, i) => {
          let arr = [];
          this.tableData.forEach(item => {
            arr.push(item.data[i].avg == '--' ? 0 : item.data[i].avg);
          });
          seriesData.push({
            name: exam.name,
            type: 'bar',
            barMaxWidth: 50,
            data: arr,
            label: {
              show: true,
              position: "top",
              formatter: "{c}",
            },
          });
        });
      }

      return seriesData;
    },

    // 获取均差分布数据
    getAvgDifSeriesData() {
      let seriesData = [];
      if (this.chartType == 0) {
        this.xAxisTitle = this.targetData.map(res => res.name);
        this.tableData.forEach(item => {
          let arr = [];
          this.targetData.forEach((exam, i) => {
            arr.push(item.data[i].avgDif);
          });
          seriesData.push({
            name: item.clzName,
            type: item.clzName.indexOf('年级平均') != -1 ? 'line' : 'bar',
            barMaxWidth: 50,
            data: arr,
            label: {
              show: true,
              position: "top",
              formatter: "{c}",
            },
          });
        });
      } else {
        this.xAxisTitle = this.tableData.map(res => res.clzName);
        this.targetData.forEach((item, index) => {
          let arr = [];
          this.tableData.forEach((ite, inde) => {
            arr.push(ite.data[index].avgDif);
          });
          seriesData.push({
            name: item.name,
            data: arr,
            type: 'bar',
            label: {
              show: true,
              position: "top",
              formatter: "{c}",
            },
          });
        });
      }
      return seriesData;
    },

    // 获取学科分布数据
    getSubjectSeriesData() {
      let seriesData = [];
      if (this.chartType == 0) {
        this.xAxisTitle = this.tableData.map(item => item.examName);
        this.targetData.forEach((item, i) => {
          let arr = [];
          this.tableData.forEach(exam => {
            arr.push(exam.data[i].avg);
          });
          seriesData.push({
            name: item.name,
            type: 'bar',
            barMaxWidth: 50,
            data: arr,
            label: {
              show: true,
              position: "top",
              formatter: "{c}",
            },
          });
        });
      } else {
        this.xAxisTitle = this.targetData.map(res => res.name);
        this.tableData.forEach((ite, inde) => {
          let arr = [];
          for (let i in this.targetData) {
            arr.push(ite.data[i].avg);
          }
          seriesData.push({
            name: ite.examName,
            data: arr,
            type: 'bar',
            label: {
              show: true,
              position: "top",
              formatter: "{c}",
            },
          });
        });
      }

      return seriesData;
    },
  },
};
</script>

<style scoped></style>

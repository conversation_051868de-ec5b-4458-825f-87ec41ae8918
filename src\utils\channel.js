/*
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2024-08-26 16:55:26
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2024-12-07 10:39:52
 */
const channel = process.env.VUE_APP_CHANNEL

const CHANNEL = {
    C30:'c30',//c30
    YC: 'yc',//易成
    SY: 'sy',//上虞
    SJ: 'sj'//绥江
}

export function isC30() {
    return channel === CHANNEL.C30;
}

export function isYc() {
    return channel === CHANNEL.YC;
} 

/** 是否上虞环境 */
export function isSy() {
    return channel === CHANNEL.SY;
}
/** 是否绥江环境 */
export function isSj() {
    return channel === CHANNEL.SJ;
}

export function getLogo() {
    let url;
    switch (channel) {
        case CHANNEL.C30:
            url = require('@/assets/logo.png');
            break;
        case CHANNEL.YC:
            url = require('@/assets/yclogo.png');
            break;
        case CHANNEL.SJ:
                url = '';
                break;
        default:
            url = require('@/assets/logo.png');
            break;
    }
    return url;
}
export function getLoginBg() {
    let url;
    switch (channel) {
        case CHANNEL.C30:
            url = require('../assets/loginBg2.png');
            break;
        case CHANNEL.YC:
            url = require('../assets/yc_loginBg.png');
            break;
        case CHANNEL.SJ:
                url = require('../assets/sj_loginBg.png');
                break;
        default:
            url = require('../assets/loginBg2.png');
            break;
    }
    return url;
}
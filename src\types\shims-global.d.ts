/*
 * @Descripttion: 全局类型定义
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2020-11-20 20:32:21
 * @LastEditors: 小圆
 */
declare module 'animate.css';
declare module 'http';
declare module 'https';
declare module 'vue-touch';
declare module 'uuid';
declare module 'clipboard';
// 全局http基地址
declare const BASE_URL: string;
declare const wincore: typeof import('@iclass/wincore-ts').default;
declare const cef: CefQuery;
declare const define: Function;
declare function sleep(tim?: number): Promise<number>;
declare const logger: Window['logger'];
declare let $: any;

declare type EChartsType = import('echarts').ECharts;
declare type EChartsOption = import('echarts').EChartsOption;

declare const MathJax: any;

interface WincoreClass {
  Disk: WincoreDiskClass;
}

interface WincoreDiskClass {
  get_file_size(filepath: string, cb: any): any;
}

interface CefQuery {
  fs: {
    dirExists: Function;
    fileExists: Function;
    readAll: Function;
    writeAll: Function;
  };
  message: {
    sendMessage: Function;
    setMessageCallback: Function;
  };
}

interface LoginSchoolRelation {
  userId: string;
  userType: number;
  realName: string;
  pinyinFirst: string;
  adminType: string;
  schoolId: string;
  schoolName: string;
  createTime: Date;
}

// 用户登录信息
interface LoginInfo {
  account_type: number;
  id: string;
  user_name: string;
  mobile: string;
  email: string;
  avatar: string;
  realname: string;
  sex: string;
  user_type: number;
  admin_type: number;
  card_id: string;
  schoolid: string;
  school_name: string;
  subjectid: string;
  sub_list: string;
  sub_name_list: string;
  center_id: string;
  huohua_id: string;
  state: number;
  isdelete: number;
  gmt_create: Date;
  pinyin_first: string;
  expire_datetime: Date;
  expire_type: string;
  salt: string;
  origin: number;
  isregister: number;
  examine_state: number;
  tencent_id: string;
  gmt_modified: Date;
  stu_status_no: string;
  exam_no: string;
  stu_no: string;
  tag_id: string;
  tag_title: string;
  _MASK_FROM_V2: string;
  school_type: number;
  multiple_phase: string;
  phase: number;
  schooldelete: number;
  classAdmin: number;
  token: string;
  loginSchoolRelation: LoginSchoolRelation[];
}

interface SchoolInfo {
  id: string;
  schoolId: string;
  schoolName: string;
  school_name: string;
}

/**
 * 登录类型
 **/
type LoginType = 'auto' | 'account' | 'mobile' | 'wechat' | '';

/**
 * 客户端环境类型
 * @param prod - 现网
 * @param xwtest - 现测网
 * @param test - 测网
 **/
type ClientEnv = 'prod' | 'xwtest' | 'test';

// 用戶选择目录信息
interface SelectedBook {
  bookId: string;
  bookCode: string;
  bookName: string;
  cataId: string;
  cataCode: string;
  cataName: string;
  gradeInfo: any;
  subjectInfo: any;
  editionInfo: any;
}

// 渠道
type DtChannel =
  // 沐坤教育
  | 'iclass'
  // 天立泰（省平台登录）
  | 'telit'
  // 康佳定制版
  | 'konka'
  // 戴特省平台版本（登录为省平台登录）
  | 'dt_province'
  // 天立泰通行版本（戴特登录）
  | 'telit_general'
  // 腾讯通行版本（戴特登录）
  | 'tencent_general'
  // 金陵微校通行版本
  | 'jlwx_general'
  // 长虹通行版本
  | 'changhong_general'
  // 智慧课堂
  | 'cmcc_general'
  // 智启学堂
  | 'zhiqiclass_general'
  // 石伽禾智慧课堂
  | 'wentong_general'
  // 海康威视(厂商版)
  | 'hikvision_general'
  // 一起智慧课堂
  | '17_general'
  // 职教云
  | 'icve_general'
  // 高教云
  | 'talent_general';

/**
 * iframe组件loaded回调事件
 **/
interface IframeLoadedEvent {
  type: 'iframeLoaded';
  target: HTMLIFrameElement;
}

interface ThemeItem {
  [prop: string]: any;
  id: string;
  themeId: string;
  thumbnail: string;
  bg: string;
  title: string;
  // 卡片区域尺寸
  cardW: number;
  cardH: number;
  // 卡片遮罩尺寸
  maskW: number;
  maskH: number;
  // 卡片相对盒子的偏移
  offset: [number, number];
}

/**
 * @description: 主题JSON配置
 * @return {*}
 */
interface ThemeJson {
  path: string;
  cover: string;
  thumbnail: string;
  bkimage: string;
  list: {
    id: number;
    fname: string;
    title: string;
    // 卡片区域尺寸
    cardW: number;
    cardH: number;
    // 卡片遮罩尺寸
    maskW: number;
    maskH: number;
    // 卡片相对盒子的偏移
    offset: [number, number];
  }[];
}

// BaseTable列接口
interface IColumn {
  prop?: string;
  label?: string;
  title?: string;
  type?: string;
  index?: number;
  render?: (h, scope) => any;
  columnKey?: string;
  width?: string | number;
  minWidth?: string | number;
  fixed?: string | boolean;
  renderHeader?: (h, { column, $index }) => any;
  sortable?: string;
  sortMethod?: Function;
  sortBy?: string | string[];
  sortOrders?: string[];
  resizable?: boolean;
  formatter?: Function;
  showOverflowTooltip?: boolean;
  align?: string;
  headerAlign?: string;
  className?: string;
  labelClassName?: string;
  selectable?: boolean;
  reserveSelection?: boolean;
  filters?: any[];
  filterPlacement?: string;
  filterMultiple?: boolean;
  filterMethod?: Function;
  filteredValue?: any[];
  children?: IColumn[];
}

// 考试报告对象类型
interface IExamReportInfo {
  createUserId: string;
  progressState: number;
  campusCode: string;
  campusName: string;
  year: number;
  yqExamId: string;
  classNames: string;
  paperList: Paper[];
  source: number;
  categoryName: string;
  subjectId: string;
  createRealName: string;
  scoreRuleId: string;
  analysisMode: number;
  comprehensiveSubjectIds: string;
  fileUrl: string;
  scoreConfirm: number;
  subjectName: string;
  gradeName: string;
  statState: string;
  schoolTermId: string;
  classIds: string;
  examName: string;
  dataState: number;
  persRecoveryType: number;
  dateModified: string;
  categoryCode: string;
  examDateTime: string;
  schoolYearId: string;
  isPublish: number;
  comprehensiveSubjectNames: string;
  paperNo: string;
  examId: number;
  clzType: string;
  progress: number;
  gradeCode: string;
  classType: string;
  leaderIds: string;
  v: string | number;
}

interface Paper {
  importScoreState: number;
  testBankId: string;
  progressState: number;
  subectName: string;
  processState: number;
  subectId: string;
  submitNum: number;
  isExamUse: number;
  isMarkingPapers: number;
  realScanNum: number;
  problemPaperCount: number;
  isTeachingSchedule: number;
  isUploadWord: number;
  bindQueState: number;
  needAudit: boolean;
  errorNum: number;
  progressList: Progress[];
  checkTeacherId: string;
  workId: string;
  handleCompleteCount: number;
  completeNum: number;
  paperNo: string;
  progress: number;
  personBookId: string;
  workingState: number;
  leaderIds: string;
}

interface Progress {
  id: string;
  examId: number;
  relationId: string;
  schoolId: string;
  progress: number;
  progressName: string;
  progressState: number;
}

interface ISchoolReportPermission {
  menuIds: string[];
  funcIds: string[];
  menuRoles: {
    menuId: string;
    roleIds: string[];
  }[];
  funcRoles: {
    funcId: string;
    roleIds: string[];
  }[];
}

interface IInnerClassList {
  grdId: string;
  grdName: string;
  id: string;
  class_name: string;
  totalNum: number;
  abPaper: string;
  intersectionRoles: number[];
}

interface IInnerSubjectList {
  id: string;
  name: string;
  phase: number;
  phaseId: number;
  xfId: number;
  xkId: number;
  progress: number;
  progressState: number;
  processState: number;
  importScoreState: number;
  bindQueState: number;
  gradeId: number;
  year: number;
  categoryId: number;
  statType: number;
  relateCardType: number;
  abCardSheetType: number;
  originCardType: number;
  source: number;
  code: number;
  status: number;
  personalBookId: string;
  workId: string;
  workIds: string[];
  testBankId: string;
  paperNo: string;
  fullScore: number;
  markingPapersProgress: number;
  roles: number[];
  isRule: boolean;
  v: number;
  isSmart: number;
  intersectionRoles: number[];
}

declare module 'worker-loader!*' {
  class WebpackWorker extends Worker {
    constructor();
  }

  export default WebpackWorker;
}
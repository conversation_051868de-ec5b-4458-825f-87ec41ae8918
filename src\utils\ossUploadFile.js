import OSS from 'ali-oss'
import md5 from 'js-md5'
import {getSTSTokenApi} from '@/service/api'
import encryp from './encryp';

let path_dir = "gece_image"
const ossUploadFile = {
    client: null,//oss对象
    accessKeyId:'',//授权id
    accessKeySecret:'',//授权秘钥
    stsToken:'',//临时token
    region:'',//区域
    bucket:'',//
    expiration:'',
    stsKey: "m0b1ycd2ef3g4hi5jk6lm7op8d9fg1",//加密key
    aseKey: 'e3140fd34ead440755a8fe34ac11f3fd',
    initConfig() {
        var self = this;
        //使用STS临时授权数据初始化OSS对象
        this.client = new OSS({
            accessKeyId: self.accessKeyId,
            accessKeySecret: self.accessKeySecret,
            stsToken: self.stsToken,
            region: self.region,
            bucket: self.bucket
        });
    },
    /**
     * @name: 获取STS授权数据信息
     * @param {path} 授权地址
     */
    async getSTSToken(path) {
        path_dir = path?.replace(/\/$/,'') || 'gece_image';
        console.log(path_dir)
        var timestamp = new Date().getTime();
        var key = md5(this.stsKey + path_dir + timestamp);
        let params = {
            path: path_dir,
            timestamp: timestamp,
            secure: key.toUpperCase(),
            type:'basefile'
        }
        let res = await getSTSTokenApi(params);
        let data = res.data
        data = JSON.parse(encryp.decrypt(data,this.aseKey));
        this.accessKeyId = data.accessKeyId;
        this.accessKeySecret = data.accessKeySecret;
        this.stsToken = data.securityToken;
        this.region = data.region;
        this.bucket = data.bucket;
        this.expiration = data.expiration;
        this.initConfig();        
    },
    /**
     * 判断临时凭证是否到期。
     **/
    isCredentialsExpired() {
        if (!this.credentials) {
        return true;
        }
        const expireDate = new Date(this.credentials);
        const now = new Date();
        // 如果有效期不足一分钟，视为过期。
        return expireDate.getTime() - now.getTime() <= 60000;
    },
    /**
     * @name: 上传文件
     * @param {file} 文件对象
     * @param {filePath} 文件地址
     * @param {cb} 回调函数
     */
    async uploadFile(file, filePath, cb) {
		// console.log('this.client-->', this.client)
        // window.LOG.INFO("[oss] : 开始上传,文件地址:" + filePath);
        if (!this.client) {
            this.initConfig().call(this);
        }
        //判断上传地址与授权地址是否匹配
        if(!filePath.includes(path_dir)){
            let path = filePath;
            //截取上传地址首位/
            path = path.replace(/^\//,'')
            //获取相对地址根地址作为授权地址
            path = path.split('/')[0];
            await this.getSTSToken(path)
        }
        // if(this.isCredentialsExpired()){
        //     await this.getSTSToken(this.path_dir);
        // }
		// console.log('进uploadFile-->', file)
        //调用OSS的上传方法，将file上传到res.filepath
        this.client.multipartUpload(filePath, file, {
            progress: async function(a, b){
                //上传进度回调;
                // console.log('上传进度--->', a, b);
            }
        }).then(function (res) {
            //上传完成;
            if (cb) {
                // window.LOG.INFO("[oss] :上传成功,res:" + res);
                cb({ code: 1, res: res });
            }
        }).catch(function (err) {
            //上传失败
            if (cb) {
                // window.LOG.ERROR("[oss] :上传失败,res:" + err);
                cb({ code: -1, res: err });
            }
        });
    },
    /**
     * @name: 上传文件
     * @param {base64} 文件base64
     * @param {filePath} 文件地址
     * @param {fileName} 文件名称
     * @param {cb} 回调函数
     */
    uploadBase64File(base64, filePath, fileName, cb) {
		// console.log('--进uploadBase64File--')
        if (filePath.indexOf('?') >= 0) {
            filePath = filePath.substring(0, filePath.indexOf('?'));
        }
        var file = this.blobToFile(this.dataURLtoBlob(base64), fileName);
        this.uploadFile(file, filePath, cb);
    },
    /**
     * @name: base64转blob
     * @param {dataurl} base64数据
     */
    dataURLtoBlob(dataurl) {
        let arr = dataurl.split(','),
            mime = arr[0].match(/:(.*?);/)[1],
            bstr = atob(arr[1]),
            n = bstr.length,
            u8arr = new Uint8Array(n);
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }
        return new Blob([u8arr], { type: mime });
    },
    /**
     * @name: blob转file
     * @param {theBlob}  blob数据
     * @param {fileName} 文件名称
     */
    blobToFile(theBlob, fileName) {
        // theBlob.lastModifiedDate = new Date();
        // theBlob.name = fileName;
        // return theBlob;
		console.log('--blobToFile--')
        return new File([theBlob], fileName);
    },
}

export default ossUploadFile;

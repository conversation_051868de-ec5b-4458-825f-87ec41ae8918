<!--
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>@class30.com
 * @Date: 2024-05-11 11:53:29
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2024-06-17 14:52:41
-->
<template>
    <div class="editor-container">
        <div class="edit-item title">
            <el-input v-model="title" placeholder="请输入标题" style="width: 300px;" class="edit-title" />
            <span style="margin-left: 10px;">序号：<el-input v-model="sort" placeholder="请输入排序号" style="width: 50px;" class="edit-title" /></span>
        </div>
        <div v-if="isShowEdit" class="edit-item content">
            <tinymce-editor-vue v-model="content" @input="contentChange"></tinymce-editor-vue>
        </div>
        <div class="edit-item handle">
            <el-button @click="cancel">取消</el-button>
            <el-button type="primary" @click="submit">提交</el-button>
        </div>
    </div>
</template>

<script>
import TinymceEditorVue from './components/TinymceEditor.vue'
import { saveHelpArticleAPI, editHelpArticleAPI, getHelpArticleAPI } from '@/service/phelp';

export default {
    name: 'Editor',
    components: {
        TinymceEditorVue
    },
    data() {
        return {
            title: "",
            content: "",
            sort:0,
            hasAdminPower:false,
            isShowEdit:false
        }
    },
    created() {
        this.hasAdminPower = this.$sessionSave.get('hasAdminPower') || false;
        if(!this.hasAdminPower){
            this.$router.replace({
                path: '/help/list'
            })
            return;
        }
        this.getHelpArticle()
    },
    methods: {
        async getHelpArticle() {
            if(!this.$route.query.id){
                this.isShowEdit = true;
                return;
            } 
            let res = await getHelpArticleAPI({
                id: this.$route.query.id
            })
            if(res.code == 1){
                this.title = res.data.title;
                this.sort = res.data.sort;
                this.content = res.data.content;
            }
            this.isShowEdit = true;
        },
        async saveHelpArticle() {
            if(this.title == '' || this.content == ''){
                this.$message.error("请输入标题或内容");
                return;
            }
            let params = {
                "sort": this.sort,
                "parentId": this.$route.query.categoryId||"",
                "title": this.title,
                "content": this.content
            }
            let res = await saveHelpArticleAPI(params);
            if(res.code == 1){
                this.$message.success("保存成功");
                this.$router.back()
            }
        },
        async editHelpArticle() {
            let params = {
                "id":this.$route.query.id,
                "sort": this.sort,
                "parentId": this.$route.query.categoryId||"",
                "title": this.title,
                "content": this.content
            }
            let res = await editHelpArticleAPI(params)
            if(res.code == 1){
                this.$message.success("修改成功");
                this.$router.back()
            }
        },
        cancel() {
            this.$router.back()
        },
        submit() {
            if(this.$route.query.id){
                this.editHelpArticle()
            }else{
                this.saveHelpArticle();
            }
        },
        contentChange(val) {
            this.content = val
        }
    }
}
</script>

<style lang="scss" scoped>
.editor-container {
    .edit-item {
        margin: 10px;
        text-align: center;
    }
}
</style>
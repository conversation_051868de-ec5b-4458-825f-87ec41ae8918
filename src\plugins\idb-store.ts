/**
 * @description: 初始化indexDB数据库
 * @return {*}
 */
import { IDBPDatabase, openDB } from 'idb';

interface StoreInfo {
    storeName: string
    keyPath: string;
    indexes?: Array<string | { name: string, keyPath: string }>
}

type StoreList = Array<StoreInfo>;

const DB_NAME = 'bigdata';
const KEY_PATH = 'userid';
const DB_VERSION = 3;

export async function initIDBStore() {
    let storeList: StoreList = [
        {
            storeName: 'comment',
            keyPath: KEY_PATH
        },
        {
            storeName: 'compare',
            keyPath: KEY_PATH
        },
    ];

    window.idbStore = await openDB(DB_NAME, DB_VERSION, {
        upgrade: (db, oldVersion, newVersion, transaction) => {
            if (db.objectStoreNames.length === storeList.length) return;

            storeList.forEach((storeInfo: StoreInfo) => {
                if(db.objectStoreNames.contains(storeInfo.storeName)) return;

                let storeName = storeInfo.storeName;
                let store = db.createObjectStore(storeName, {
                    keyPath: storeInfo.keyPath,
                    autoIncrement: true,
                });
                if (storeInfo.indexes) {
                    storeInfo.indexes.forEach(storeIndex => {
                        if (typeof storeIndex === 'string') {
                            store.createIndex(storeIndex, storeIndex);
                        } else {
                            store.createIndex(storeIndex.name, storeIndex.keyPath);
                        }
                    });
                }
            });
        },
    });
}
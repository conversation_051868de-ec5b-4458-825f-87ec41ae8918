<!--
 * @Descripttion: 学生查看原卷预览
 * @Author: 小圆
 * @Date: 2024-01-13 09:26:00
 * @LastEditors: Please set LastEditors
-->
<template>
  <previewSlider ref="previewSlider" :class="{ desktop: isDesktop }" :data="smartMarkData" :loading="loading"
    :no-text="'该场考试未上传试卷'" @slideChange="slideChange" @tabPrev="tabPrev" @tabNext="tabNext" @close="$emit('close')">
    <template slot-scope="{ item, index }">
      <div class="img-container">
        <!-- 图片 -->
        <el-image class="img" :src="item" @load="e => loadImage(e.currentTarget, index)" />

        <!-- 图片标记 -->
        <div class="mark-container" v-if="marks.length && aiCorrects.length">
          <template v-for="item in marks[index]">
            <el-popover popper-class="aitip-poppover" placement="bottom" title="AI智批" width="450" trigger="hover">
              <p class="ai-tip">{{ aiQuestions.get(item.question_id).score_list[0].evaluation_new }}</p>

              <div class="mark" :key="item.question_id" :style="{
                top: item.pos[1] * scale + 'px',
                left: (item.pos[0] + item.pos[2] - subWidth) * scale + 'px',
              }" slot="reference">
                {{ item.score }}
                <sup>{{ item.score < item.total_score ? '×' : '√' }}</sup>
              </div>
            </el-popover>
          </template>
        </div>
      </div>
    </template>
  </previewSlider>
</template>

<script>
import previewSlider from '@/components/SwiperViewer/previewSlider.vue';
import ImgList from '@/components/scan/ImgList.vue';
import StuPaperImg from './StuPaperImg.vue';
import { getStudentPaperImage } from '@/service/xueban';
import { getPublicConfigBySchoolInfo } from '@/service/api';
import { supportsAvif } from '@/utils/index';
import { getStuQuesAnalyze, getStuScanDataAPI } from '@/service/pexam';
import { getScanPaperPoints } from '@/service/testbank';
import { mapGetters, mapState } from 'vuex';
import { httpAxios } from '@/utils';

export default {
  components: {
    previewSlider,
    ImgList,
    StuPaperImg,
  },
  props: {
    personalBookId: {
      type: String,
      default: '',
    },

    studentNo: {
      type: String,
      default: '',
    },

    // 默认总分
    defaultScore: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false, // 是否正在加载
      isDesktop: !!window.cef,
      smartMarkData: [], // 智能批注图片

      // 图片标记
      marks: [],
      // AI批改数据
      aiCorrects: [],
      // 答案坐标需要减去的宽度 mm
      subWidth: 20,
      scale: 1
    };
  },
  computed: {
    ...mapState(['schoolInfo']),
    // ai批改题目映射
    aiQuestions() {
      return new Map(this.aiCorrects.map(item => [item.question_id, item]))
    },
  },
  mounted() {
    this.getSmartMarkDataIMG();

    const { schoolId } = this.schoolInfo;
    if (schoolId === '62ba4e0ca09639af5e15ce72') {
      // "上海市南洋模范中学"
      this.getImageSmallMark();
    }
  },

  methods: {
    // 获取图片的智能标记
    async getImageSmallMark() {
      try {
        const res = await getStuScanDataAPI({
          examId: this.personalBookId,
          stuNo: this.studentNo,
        });
        const aiReqs = [];
        for (let index = 0; index < res.data.length; index++) {
          this.marks.push(res.data[index].questions);

          const req = httpAxios('https://scanpaper.iclass30.com', `gece_image/exam_ai_page/${this.personalBookId}/${this.studentNo}_${index + 1}.json`);
          aiReqs.push(req);
        }
        // 获取AI批改信息
        const aiRespones = await Promise.all(aiReqs);
        for (let index = 0; index < aiRespones.length; index++) {
          const aiRes = aiRespones[index];
          if (aiRes.status != 200) throw aiRes;

          this.aiCorrects = this.aiCorrects.concat(aiRes.data.ai_questions);
        }
      } catch (error) {
        console.error("getImageSmallMark", error)
      }
    },

    // 获取智能批注图片
    getSmartMarkDataIMG() {
      const COUNT = 2;
      this.smartMarkData = [];
      for (let i = 0; i < COUNT; i++) {
        this.smartMarkData.push(
          `https://scanpaper.iclass30.com/gece_image/exam_ai_page/${this.personalBookId}/${this.studentNo}_${i + 1}.png`
        );
      }
    },

    loadImage(img, index) {
      this.scale = img.height / 297;
      this.$refs.previewSlider.previewTo('fitWindow', index);
    },

    // 索引更新
    slideChange(index) {
      console.log('当前索引', index);
    },

    // 按钮切换上一个
    tabPrev(activeIndex) { },

    // 按钮切换下一个
    tabNext(activeIndex) { },

    // 关闭预览
    closePreview(type) {
      this.$emit('close', type);
    },
  },
};
</script>

<style lang="scss" scoped>
.previewSlider.desktop {
  bottom: 20px !important;
}

::v-deep .gallery-top {
  height: 100% !important;
}

::v-deep .swiper-imageTool-box {
  bottom: 5% !important;
}

.img-container {
  position: relative;
  z-index: 1;

  .el-image {
    display: block;
  }
}

.mark-container {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  height: 100%;

  .mark {
    position: absolute;
    z-index: 100;
    padding: 0 5px;
    line-height: 1;
    font-size: 38px;
    color: red;
    border-bottom: 4px solid transparent;
    background-color: rgba(255, 255, 255, 0.7);

    &:hover {
      border-bottom-color: red;
    }
  }

  sup {
    font-family: 微软雅黑, Microsoft YaHei, Verdana, Arial, Helvetica, sans-serif;
  }
}
</style>

<style lang="scss">
.aitip-poppover {
  box-shadow: 0 2px 15px 0 rgba(0, 0, 0, .2);
  border: 2px solid red;

  .el-popover__title {
    font-size: 18px;
  }

  .ai-tip {
    white-space: pre-line;
    font-size: 16px;
    line-height: 1.5;
  }
}
</style>
/*
 * @Description:
 * @Author: 小圆
 * @Date: 2024-07-03 09:05:54
 * @LastEditors: 小圆
 */
import * as httpApi from './index';

const kklUrl = process.env.VUE_APP_KKLURL;
const baseUrl = process.env.VUE_APP_BASE_API;
const url17 = process.env.VUE_WX;

/**
 * api转换
 * @type {{POST: API.POST, GET: API.GET}}
 */
const API = {
  GET: function (url, params) {
    return httpApi.GET(url, params, kklUrl);
  },
  POST: function (url, params, BASEURL = kklUrl) {
    return httpApi.POST(url, params, BASEURL);
  },
  POSTJson: function (url, params) {
    return httpApi.POSTJson(url, params, kklUrl);
  },
};

// 获取学生小分表
export const listStuDesc = params => {
  return API.GET('/pstat/_/list-stu-desc', params);
};

// 导出学生小分表
export const exportStuDesc = params => {
  return API.GET('/pstat/_/export-stu-desc', params);
};

// 获取大小题分析
export const listQsAnalysis = params => {
  return API.GET('/pstat/_/list-qs-analysis', params);
};

// 导出大小题分析
export const exportQsAnalysis = params => {
  return API.GET('/pstat/_/export-qs-analysis', params);
};

// 获取扫描客观题明细
export const listScanObjDesc = params => {
  return API.GET('pstat/_/list-scan-obj-desc', params);
};

// 获取菜单
export const listMenuAPI = params => {
  return API.GET('pstat/_/list-menu', params);
};

// 获取角色菜单
export const listRoleMenuAPI = params => {
  return API.GET('/pstat/_/list-role-menu', params);
};

// 保存角色菜单
export const saveRoleMenuAPI = params => {
  return API.POSTJson('/pstat/_/save-role-menu', params);
};

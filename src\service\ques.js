import request from '@/service/request'

const kklUrl = process.env.VUE_APP_KKLURL

function api(config) {
  config.baseURL = kklUrl
  return request(config)
}

/**
 * 一审完成提交审核
 * @param params
 * @returns {AxiosPromise}
 */
export function wordReviewSubmitCheck(params) {
  return api({
    url: '/ptask/wordPaper/wordReviewSubmitCheck',
    method: 'post',
    params
  })
}

/**
 * 加工老师提交一审审核
 * @param params
 * @returns {AxiosPromise}
 */
export function wordSubmitChecking(params) {
  return api({
    url: '/ptask/wordPaper/wordSubmitChecking',
    method: 'post',
    params
  })
}

/**
 * 一审老师标记错因
 * @param params
 * @returns {AxiosPromise}
 */
export function wordReviewMarking(params) {
  return api({
    url: '/ptask/wordPaper/wordReviewMarking',
    method: 'post',
    params
  })
}

/**
 * 保存wrod试卷
 * @param params
 * @returns {AxiosPromise}
 */
export function saveWordPaper(params) {
  return api({
    url: '/ptask/wordPaper/saveWordPaper',
    method: 'post',
    params
  })
}

/**
 * 制卷完成后关联题目关系
 * @param params
 * @returns {AxiosPromise}
 */
export function saveWordRelation(params) {
  return api({
    url: '/ptask/wordPaper/saveWordRelation',
    method: 'post',
    params
  })
}

/**
 * 获取word加工列表
 * @param params
 * @returns {AxiosPromise}
 */
export function findWordProcessList(params) {
  return api({
    url: '/ptask/wordPaper/findWordProcessList',
    method: 'post',
    params
  })
}

/**
 * 获取word审核列表
 * @param params
 * @returns {AxiosPromise}
 */
export function findWordReviewList(params) {
  return api({
    url: '/ptask/wordPaper/findWordReviewList',
    method: 'post',
    params
  })
}

/**
 * 获取word试卷里的题目
 * @param params
 * @returns {AxiosPromise}
 */
export function getWordQuesByWordId(params) {
  return api({
    url: '/ptask/wordPaper/getWordQuesByWordId',
    method: 'post',
    params
  })
}

/**
 * 设置word为打回状态
 * @param params
 * @returns {AxiosPromise}
 */
export function setWordBackState(params) {
  return api({
    url: '/ptask/wordPaper/wordBackState',
    method: 'post',
    params
  })
}

/**
 * 设置word为打回状态
 * @param params
 * @returns {AxiosPromise}
 */
export function getC30Token(params) {
  return api({
    url: '/ptask/wordPaper/getToken',
    method: 'get',
    params
  })
}

/**
 * 删除试卷
 * @param data
 * @returns {AxiosPromise}
 */
export function deleteWordPaper(data) {
  return api({
    url: '/ptask/wordPaper/deleteWordPaper',
    method: 'post',
    data
  })
}

// 插入缓存接口
export const InsertCommonCatch = (data) => {
  return api({
    url: '/public/catch/insertCommonCatch',
    method: 'post',
    data
  })
};


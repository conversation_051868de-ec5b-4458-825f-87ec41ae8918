import * as httpApi from './index';
import { localSave } from '@/utils';

const testBank = process.env.VUE_APP_TESTBANK;

/**
 * api转换
 * @type {{POST: API.POST, GET: API.GET}}
 */
const API = {
  GET: function (url, params,baseURL = testBank) {
    return httpApi.GET(url, params, baseURL);
  },
  POST: function (url, params,baseURL = testBank) {
    return httpApi.POST(url, params, baseURL);
  },
  POSTJson: function (url, params,baseURL = testBank) {
    return httpApi.POSTJson(url, params, baseURL);
  },
};

//保存个册试卷
export const savePersonalTestBank = params => {
  return API.POST('/testbank/testBank/savePersonalTestBank', params);
};
// 获取最近一次打开的试卷
export const getPersonalTestBank = params => {
  return API.GET('/testbank/testBank/getPersonalTestBank', params);
};

// 批量修改保存题目信息
export const batchSaveQuestionData = params => {
  return API.POST('/testbank/TBQuestion/batchSaveQuestionData', params);
};

// 删除试卷(主线版本校本题库使用)
export const delXBResource = params => {
  return API.POST('/testbank/testBank/delXBResource', params);
};

// 打开试卷（更新试卷状态为打开状态）
export const openPersonalTestBank = params => {
  return API.GET('/testbank/testBank/openPersonalTestBank', params);
};

// 获取试卷列表
export const getTestPaperList = params => {
  return API.POST('/testbank/testBank/getTestPaperList', params);
};

// 预览试卷
export const getViewPaper = params => {
  return API.POST('/testbank/testBank/getViewPaper', params);
};

// 下载试卷
export const exportTestBank = params => {
  return API.POST('/testbank/testBank/exportTestBank', params);
};

//直接从node服务导出试卷pdf
export const exportTestBankPDFByNodeAPI = params => {
  return window.fetch('https://exportnode.iclass30.com/export/exportBigExam', {
      method: 'POST', // 指定请求方法
      headers: {
          'Content-Type': 'application/json'
      },
      body: JSON.stringify(params)
  })
};
// 获取年份列表
export const getTBSearchInfo = (params, cb) => {
  if (params.phase) {
    API.POST('/testbank/testBank/getTestBankSearchInfo', params)
      .then(data => {
        localSave.set('c30_tb_' + params.phase, data.data);
        if (cb) {
          cb(data.data);
        }
      })
      .catch(err => {
        console.log(err);
      });
  }
};

/**
 * 上传word保存试卷
 */
export const saveTestBanks = params => {
  return API.POST('/testbank/testBank/saveTestBanks', params);
};
/**
 * 外部题库-卷库题目结构信息保存
 */
export const saveOneQuestion = params => {
  return API.POST('/testbank/TBQuestion/saveOneQuestion', params);
};
/**
 * 外部题库-根据学科ID获取一起题库题型列表
 */
export const getQueTypeListBySubId = params => {
  return API.POST('/questionbank/17question/getQueTypeListBySubId', params);
};
/**
 * 获取题目类型
 * @param params
 * @returns {AxiosPromise}
 */
export const getQueTypeListAPI = params => {
  return API.GET('/testbank/TBQuestion/getQueTypeList', params);
};
/**
 * 加载试卷结构数据
 * @param params
 * @returns {AxiosPromise}
 */
export const loadTestStructureAPI = params => {
  return API.GET('/testbank/testBank/loadTestStructure', params);
};
/**
 * 获取坐标点
 * @param params
 * @returns {AxiosPromise}
 */
export const getScanPaperPoints = params => {
  return API.GET('/testbank/testBank/getScanPaperPoints', params);
};
/**
 * 获取答题卡试卷列表
 * @param params
 * @returns {AxiosPromise}
 */
export const getCardPaperList = params => {
  return API.GET('/testbank/testBank/getCardPaperList', params);
};
// 更新试卷
export const updateViewPaper = params => {
  return API.POSTJson('/testbank/testBank/updateBigViewPaper', params);
};
export const updateBigViewPaper = params => {
  return API.POSTJson('/testbank/testBank/updateBigViewPaper', params);
};
// 复制题卡
export const copyCardPaper = params => {
  return API.POST('/testbank/testBank/copyCardPaper', params);
};
// 删除题卡
export const delCardPaper = params => {
  return API.POST('/testbank/testBank/delCardPaper', params);
};
// 获取题卡信息
export const getBankInfoByPaperNo = params => {
  return API.GET('/testbank/testBank/getBankInfoByPaperNo', params);
};
// 获取教辅
export const getSchoolBook = params => {
  return API.GET('/testbank/QuestionBookSchool/findAllQuestionSchoolBook', params);
};
// 获取学科列表
export const getAllSubjectList = params => {
  return API.GET('/resource/subject/getAllSubjectList', params);
};
// 获取年级列表
export const getAllGradeList = params => {
  return API.GET('/resource/grade/getAllGradeList', params);
};
/**
 * @name: 获取册别列表
 * @param {params} 接口参数
 */
export const volumeList = params => {
  return API.POST('/resource/volume/volumeList', params);
};
/**
 * @name: 获取版本列表
 * @param {params} 接口参数
 */
export const getAllEditionList = params => {
  return API.POST('/resource/edition/getAllEditionList', params);
};

/**
 * @name: 获取出版社列表
 * @param {params} 接口参数
 */
export const getAllPublisherList = params => {
  return API.POST('/resource/edition/getAllPublisherList', params);
};
/**
 * @name: 获取目录列表
 * @param {params} 接口参数
 *    @bookCode 教材编码
 */
export const getStructureCatalogList = params => {
  return API.POST('/resource/catalog/getStructureCatalogList', params);
};
/**
 * @name: 根据章节获取题目列表
 * @param {params} 接口参数
 *    @bookCode 书本编码
 *    @code 目录编码
 *    @page 页码
 *    @limit 当页总数
 */
export const getQuesByCatalog = params => {
  return API.POST('/testbank/TBQuestion/getQuesByCatalog', params);
};
/**
 * @name: 获取校本预览题目
 * @param {params} 接口参数
 *     @qIds 题目id集合
 */
export const getXBPreviewQues = params => {
  return API.GET('/testbank/TBQuestion/getQuestionInfoBatch', params);
};
/**
 * @name: 获取题目使用次数
 * @param {params} 接口参数
 *    @teaId 教师id
 *    @subId 学科id
 *    @queIds 题目id列表List<String>
 */
export const getQuesUseCount = params => {
  return API.POST('/questionbank/questionWork/getTeaQuestionUseData', params);
};
/**
 * @name: 获取区本区域列表
 * @param {params} 接口参数
 *     @testBankId 试卷id
 */
export const getPlatFromInfoBySchoolIdAPI = params => {
  return API.POST('/platformManage/getPlatFromInfoBySchoolId', params);
};
/**
 * @name: 记录题目使用以及下载 （组卷下载和预览题目详情记录）
 * @param params
 * @schName 学校名称
 * @userId 用户id
 * @userName 用户姓名
 * @areaId 区域id
 * @schId 学校id
 * @quesId 题目id
 * @tbId 试卷id
 * @subId 学科id
 * @behavior 1：预览2：下载或者使用
 */
export const recordUseQue = params => {
  return API.POST('/testbank/testBank/recordUseQue', params);
};
/**
 * @name: 获取学科网试卷下载word地址
 * @param params
 * @schoolId 学校id
 * @id 主键id
 * @type 类型 1: 在线作业(id对应workId) 2: 大精组卷(id对应tbId/testBankId)
 */
export const getXkwPaperDownloadUrl = params => {
  return API.GET('/questionbank/xkw/getXkwPaperDownloadUrl', params);
};

/**
 * @name: 获取学科网知识点树
 * @param params
 */
export const getXkwPointTreeAPI = params => {
  return API.GET('/questionbank/xkw/getXkwPointTree', params);
};

/**
 * @name: 获取学科网匹配学科列表
 */
export const getXkwSubjectListAPI = params => {
  return API.GET('/questionbank/xkw/getMatchSubList', params);
};

/**
 * @name: 获取题目结构树
 * @param params
 * @schoolId 学校id
 * @examId 考试id
 * @subjectId 学科id
 */
export const getExamTeamQues = params => {
  return API.GET('/testbank/testBank/getExamTeamQues', params);
};
/**
 * @name: 获取题卡信息
 * @param params
 * @id 试卷id
 */
export const getTestBankInfo = params => {
  return API.GET('/testbank/testBank/getTestBankInfo', params);
};
/**
 * @name: 完成制卡
 * @param schoolId 学校id
 * @param tbId 试卷id
 */
export const overCard = params => {
  return API.POST('/testbank/testBank/overCard', params);
};
/**
 * @name: 取消完成制卡状态
 * @param tbId 试卷id
 */
export const updateNotOverCard = params => {
  return API.POST('/testbank/testBank/updateNotOverCard', params);
};
/**
 * @name: 获取是否修改题组
 */
export const changeViewPaper = params => {
  return API.POST('/testbank/testBank/changeViewPaper', params);
};
/**
 * @name: 设为模板卡
 */
export const setTemplateCard = params => {
  return API.POST('/testbank/testBank/setTemplateCard', params);
};

/**
 * @description: 建立下载word文档任务
 * @param {*} params
 * @return {*}
 */
export const addTbWordBuildTask = params => {
  return API.POST('/testbank/testBank/addTbWordBuildTask', params);
};

/**
 * @description: 获取生成下载word文档任务详情
 * @param {*} params
 * @return {*}
 */
export const getTbWordBuildTaskDetails = params => {
  return API.GET('/testbank/testBank/getTbWordBuildTaskDetails', params);
};

/**
 * @description: 获取已完成制题/制卡列表数据
 * @param {*} params
 * @return {*}
 */
export const getTestBankOrCardList = params => {
  return API.GET('/testbank/testBank/getTestBankList', params);
};
/**
 * @description: 恢复答题卡
 * @param {*} params
 * @return {*}
 */
export const modifyNotDelete = params => {
  return API.GET('/testbank/testBank/modifyNotDelete', params);
};
/**
 * @description: 校验题目密码
 * @param {*} params
 * @return {*}
 */
export const checkTbPassword = params => {
  return API.POST('/testbank/testBank/checkTbPassword', params);
};
/**
 * @description: 修改题目密码
 * @param {*} params
 * @return {*}
 */
export const modifyTbPassword = params => {
  return API.POST('/testbank/testBank/modifyTbPassword', params);
};

/**
 * @description: 获取试卷详情
 * @param {*} params
 * @return {*}
 */
export const getTbDetails = params => {
  return API.POST('/testbank/testBank/getTbDetails', params);
};

/**
 * @description: 修改试卷张数接口
 * @param {*} params
 * @return {*}
 */
export const updatePaperNum = params => {
  return API.POST('/testbank/testBank/updatePaperNum', params);
};

/**
 * @description: 获取教辅试卷id（兼容图片制作教辅生成制卡问题）
 * @param {*} params
 * @return {*}
 */
export const getSchoolBookTbIdAPI = params => {
  return API.POST('/testbank/testBank/getSchoolBookTbId', params);
};


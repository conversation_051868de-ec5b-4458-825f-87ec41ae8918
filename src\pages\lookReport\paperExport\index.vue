<template>
    <div class="paper-export">
        <div>
            <span class="back-btn el-icon-arrow-left" @click="goBack">返回</span>
        </div>
        <el-card class="paper-export-card" shadow="hover">
            <div class="titleLine">导出原卷</div>

            <div class="form-line">
                <div class="form-item">
                    <div class="form-item__label">考试：</div>
                    <div class="form-item__content exam-name">
                        <span :title="examInfo.examName">{{ examInfo.examName }}</span>
                    </div>
                </div>
                <div class="form-item">
                    <div class="form-item__label">学科：</div>
                    <div class="form-item__content">
                        <el-select
                            v-model="currentSubject"
                            placeholder="请选择考试学科"
                            value-key="id"
                            @change="setCurrentSubject"
                        >
                            <el-option
                                v-for="item in examSubjectList"
                                :key="item.id"
                                :label="item.name"
                                :value="item"
                            ></el-option
                        ></el-select>
                    </div>
                </div>

                <div class="form-item" v-if="abPaperList.length">
                    <div class="form-item__label">类型：</div>
                    <div class="form-item__content">
                        <el-select
                            v-model="currentAbPaper"
                            placeholder="请选择试卷类型"
                            @change="val => setCurrentAbPaper(val)"
                        >
                            <el-option v-for="item in abPaperList" :key="item.value" :label="item.text" :value="item.value"></el-option>
                        </el-select>
                    </div>
                </div>

                <div class="form-item">
                    <div class="form-item__label">班级：</div>
                    <div class="form-item__content">
                        <el-select
                            v-model="currentClz"
                            placeholder="请选择考试班级"
                            value-key="id"
                            @change="val => setCurrentClz(val, true)"
                        >
                            <el-option
                                v-for="item in classList"
                                :key="item.id"
                                :label="item.class_name"
                                :value="item"
                            ></el-option>
                        </el-select>
                    </div>
                </div>
                <div class="form-item">
                    <div class="form-item__label">题号：</div>
                    <div class="form-item__content">
                        <el-select
                            v-if="questionGroups.length"
                            v-model="currentTQuesNo"
                            placeholder="请选择题目"
                            value-key="quesNo"
                            @change="val => setCurrentTQuesNo(val, true)"
                        >
                            <el-option-group v-for="(group, gi) in questionGroups" :key="gi" :label="group.name">
                                <el-option
                                    v-for="(item, i) in group.data"
                                    :key="i"
                                    :label="`${item.quesNoDesc}`"
                                    :value="item.tQuesNo"
                                >
                                </el-option>
                            </el-option-group>
                        </el-select>

                        <el-select
                            v-else
                            v-model="currentQues"
                            placeholder="请选择题目题号"
                            value-key="quesNo"
                            @change="val => setCurrentQues(val, true)"
                        >
                            <el-option
                                v-for="item in quesList"
                                :key="item.quesNo"
                                :label="item.quesNoDesc"
                                :value="item"
                            >
                            </el-option>
                        </el-select>

                        <div style="text-align: left" v-if="currentQues.quesNo">
                            <el-checkbox
                                v-model="viewPaperType"
                                v-if="currentSubject?.source == 4"
                                @change="getStuScoreList"
                                >保留批改痕迹</el-checkbox
                            >
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-item">
                <div class="form-item__label">导出类型：</div>
                <div class="form-item__content">
                    <el-radio
                        v-for="item in exportTypeList"
                        v-model="currentExportType"
                        border
                        :key="item.value"
                        :label="item.value"
                        @input="val => setCurrentExportType(val, true)"
                        >{{ item.label }}</el-radio
                    >
                </div>
            </div>

            <div class="search-container">
                <div class="form-item" v-if="currentExportType == 0">
                    <div class="form-item__label">请输入需要导出的分数区间：</div>
                    <div class="form-item__content">
                        <div>
                            <span>
                                <el-input-number
                                    class="score-input"
                                    v-model="scoreRange[0]"
                                    :controls="false"
                                    :min="0"
                                    :max="scoreRange[1]"
                                ></el-input-number>
                                <span> 分 </span>
                            </span>
                            <span> —— </span>
                            <span>
                                <el-input-number
                                    class="score-input"
                                    v-model="scoreRange[1]"
                                    :controls="false"
                                    :min="scoreRange[0]"
                                    :max="currentQues.fullScore"
                                ></el-input-number>
                                <span> 分 </span>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="form-item" v-if="currentExportType == 1">
                    <div class="form-item__label">请输入需要导出的排名范围：</div>
                    <div class="form-item__content">
                        <div>
                            <span>
                                <el-input-number
                                    class="score-input"
                                    v-model="rankRange[0]"
                                    :controls="false"
                                    :step="1"
                                    step-strictly
                                    :min="1"
                                    :max="rankRange[1]"
                                ></el-input-number>
                                <span> 名 </span>
                            </span>
                            <span> —— </span>
                            <span>
                                <el-input-number
                                    class="score-input"
                                    v-model="rankRange[1]"
                                    :controls="false"
                                    :step="1"
                                    step-strictly
                                    :min="rankRange[0]"
                                    :max="currentClz.totalNum"
                                ></el-input-number>
                                <span> 名 </span>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="form-item" v-if="currentExportType == 2">
                    <div class="form-item__label">请输入需要导出的学生姓名或考号：</div>
                    <div class="form-item__content">
                        <div>
                            <span>
                                <el-input
                                    v-model="keyWord"
                                    @keyup.enter.native="onQueryStu"
                                    placeholder="请输入学生姓名或考号"
                                    style="width: 200px"
                                ></el-input>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="search-btns">
                    <el-button type="primary" @click="onQueryStu" icon="el-icon-search" class="paper-button"
                        >查询</el-button
                    >
                    <el-button
                        v-if="currentExportType !== 2"
                        type="primary"
                        :disabled="!stuScoreList.length"
                        @click="onExportPaper"
                        icon="el-icon-download"
                        class="paper-button"
                        >全部导出</el-button
                    >

                    <el-button
                        v-if="currentExportType !== 2"
                        type="primary"
                        @click="openDownloadDialog"
                        icon="el-icon-s-order"
                        class="paper-button"
                        >进度</el-button
                    >
                </div>
            </div>

            <div class="table-wrapper" v-if="stuScoreList.length">
                <div style="margin-bottom: 20px" v-if="pagination.total">
                    共查询到 <span style="color: #409eff"> {{ pagination.total }} </span> 条数据
                </div>
                <el-table
                    :data="stuScoreList"
                    :header-cell-style="{
                        fontSize: '16px',
                        color: '#3F4A54',
                        backgroundColor: '#f5f7fa',
                    }"
                    row-key="stuId"
                    stripe
                    border
                >
                    <el-table-column label="序号" prop="rowNum" align="center" :resizable="false"></el-table-column>
                    <el-table-column label="班级" prop="className" align="center" :resizable="false"></el-table-column>
                    <el-table-column label="学生" prop="stuName" align="center" :resizable="false"></el-table-column>
                    <el-table-column label="考号" prop="stuNo" align="center" :resizable="false"></el-table-column>
                    <el-table-column label="得分" prop="score" align="center" :resizable="false"></el-table-column>
                    <el-table-column label="操作" align="center" :resizable="false">
                        <template #default="scope">
                            <el-button type="text" size="small" @click="onViewPaper(scope.row)">查看</el-button>
                            <el-button
                                :class="{ 'is-click': !!scope.row.isClick }"
                                type="text"
                                size="small"
                                @click="onDownloadPaper(scope.row)"
                                >下载</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
                <!--分页器-->
                <el-pagination
                    background
                    v-show="stuScoreList.length"
                    style="margin: 15px auto"
                    class="text-center"
                    layout="total, prev, pager, next"
                    @current-change="onPageChange"
                    :current-page.sync="pagination.page"
                    :page-size="pagination.limit"
                    :total="pagination.total"
                >
                </el-pagination>
            </div>
            <NoData v-else></NoData>
        </el-card>

        <download-dialog
            v-if="isShowDownloadDialog"
            :width="'1200px'"
            @closed="isShowDownloadDialog = false"
        ></download-dialog>

        <img-preview v-if="isShowPreviewImg" :imgList="previewImgList" @close="isShowPreviewImg = false"></img-preview>
    </div>
</template>

<script lang="ts">
import NoData from '@/components/noData.vue';
import { exportPaper, getStuScoreList } from '@/service/eReport';
import { clzListAPI, getReportSmallAnalysis, listExamSubject } from '@/service/pexam';
import { getExamTeamQues } from '@/service/testbank';
import { replaceALiUrl } from '@/utils/common';
import { getImgUrl } from '@/utils/index';
import { Component, Vue } from 'vue-property-decorator';
import DownloadDialog from './DownloadDialog.vue';
import ExamInfoSelect from './ExamInfoSelect.vue';
import ImgPreview from '@/components/SwiperViewer/ImgPreview.vue';
import UserRole from '@/utils/UserRole';

interface ISubject {
    id: number;
    name: string;
    phaseId: number;
    xfId: number;
    xkId: number;
    progress: number;
    progressState: number;
    code: null;
    status: number;
    personalBookId: null;
    testBankId: null;
    paperNo: null;
    fullScore: number;
    source: number;
}

interface IClz {
    id: string;
    class_name: string;
    grdId?: string;
    grdName?: string;
    sort?: number;
    totalNum?: number;
    abPaper?: string;
}

interface IQues {
    quesNo: number | string;
    quesNoDesc: string;
    tQuesNo: string;
    fullScore: number;
    quesType?: string | number;
}

interface IStuScore {
    rowNum: number;
    classId: string;
    className: string;
    stuId: string;
    stuName: string;
    stuNo: string;
    score: number;
    paperUrl?: string[];
}

interface ITeamQues {
    quesNo: string;
    data: {
        quesNo: string;
        id: string;
        quesNos: string;
        tQuesNo: string;
    }[];
    name: string;
    id: string;
    quesNos: string;
}

type ITypeValue = 0 | 1 | 2 | 3 | 4;

/**
 * @name:获取学科
 */
function getPhaseName(phaseId) {
    let name;
    if (phaseId == 5) {
      name = `高中`;
    } else if (phaseId == 4) {
      name = `初中`;
    } else {
      name = `小学`;
    }
    return name;
}

@Component({
    components: {
        DownloadDialog,
        ExamInfoSelect,
        NoData,
        ImgPreview,
    },
})
export default class Index extends Vue {
    // 全部导出类型列表
    typeList: { label: string; value: ITypeValue }[] = [
        {
            label: '分数范围',
            value: 0,
        },
        {
            label: '排名范围',
            value: 1,
        },
        {
            label: '单个学生',
            value: 2,
        },
        {
            label: '优秀作答',
            value: 3,
        },
        {
            label: '典型错误',
            value: 4,
        },
    ];

    get exportTypeList() {
        let values = this.typeList.map(item => item.value);
        if (!this.currentQues.quesNo) {
            values = [0, 1, 2];
        } else {
            values = [0, 2, 3, 4];
        }

        return this.typeList.filter(item => {
            return values.find(t => t === item.value) || values.find(t => t === item.value) === 0 ? true : false;
        });
    }
    // 当前导出类型
    currentExportType: ITypeValue = 0;
    // 考试学科列表
    examSubjectList: ISubject[] = [];
    // 当前考试学科
    currentSubject: ISubject = null;
    // 考试班级列表
    examClassList: IClz[] = [];
    // 班级列表
    get classList() {
        let arr = JSON.parse(JSON.stringify(this.examClassList));
        if (this.currentAbPaper !== '') {
            arr = this.examClassList.filter(
                item => item.abPaper !== null && item.abPaper !== undefined && item.abPaper.split(',').includes(this.currentAbPaper)
            );
        }
        const totalNum = arr.reduce((prev, current) => {
            return prev + current.totalNum;
        }, 0);

        arr.unshift({
            id: '',
            class_name: '全部班级',
            totalNum: totalNum,
        });
        return arr;
    }
    // 题号列表
    quesList: IQues[] = [];
    // 题目组列表
    questionGroups: { name: string; data: IQues[] }[] = [];
    // 当前考试班级
    currentClz: IClz = null;
    // 当前试卷类型
    currentAbPaper: string = ''; // -1:普通 0:A卷 1:B卷
    // 当前题目
    currentQues: IQues = {
        quesNo: null,
        tQuesNo: null,
        fullScore: 0,
        quesNoDesc: '',
    };
    // 当前题目TQuesNo
    currentTQuesNo: any = null;
    // 分数范围
    scoreRange: number[] = [0, 50];
    // 排名范围
    rankRange: number[] = [1, 50];
    // 学生考号
    keyWord: string = '';
    // 分页器
    pagination = {
        page: 1,
        limit: 10,
        total: 0,
    };
    // 学生分数列表
    stuScoreList: IStuScore[] = [];
    // 是否显示下载进度弹窗
    isShowDownloadDialog: boolean = false;
    // 考试信息
    examInfo: IExamReportInfo = null;
    // 预览图片列表
    previewImgList: string[] = [];
    // 是否预览图片
    isShowPreviewImg: boolean = false;
    // 是否保留批改痕迹
    viewPaperType: boolean = true;

    // AB卷列表
    abPaperList: any = [];

    // 是否考试管理员
    get isExamLeader() {
        let isOperation = UserRole.isOperation; // 运营
        let isSchoolAdmin = UserRole.isSchoolLeader; // 校管
        let reportDetail = this.$sessionSave.get('reportDetail');
        let isExamLeader = reportDetail.leaderIds?.includes(this.$sessionSave.get('loginInfo').id); // 考试管理员
        let isSameCreateUser = reportDetail.createUserId == this.$sessionSave.get('loginInfo').id; // 创建者
        return isOperation || isSchoolAdmin || isExamLeader || isSameCreateUser;
    }

    created() {
        this.examInfo = this.$sessionSave.get('reportDetail');
    }

    mounted() {
        this.init();
    }

    init() {
        this.initExam();
    }

    changeExam(examInfo) {
        this.examInfo = examInfo;
        this.$sessionSave.set('reportDetail', examInfo);
        this.init();
    }

    async initExam() {
        await this.getExamSubjectList();
        this.setCurrentSubject(this.examSubjectList[0]);
    }

    // 获取考试学科列表
    async getExamSubjectList() {
        const examId = this.$sessionSave.get('reportDetail').examId;
        let gradeCode = this.$sessionSave.get('reportDetail').gradeCode;

        let res = await listExamSubject({ examId, statType: 1, v: this.examInfo.v });
        let examSubjectList = res.data as ISubject[];

        if (!this.isExamLeader) {
            let reportDetail = this.$sessionSave.get('reportDetail');
            let paperList = reportDetail.paperList;
            examSubjectList = examSubjectList.filter(item => {
                return paperList.find(paper => {
                    return (
                        paper.subectId.includes(item.id) &&
                        paper.leaderIds.includes(this.$sessionSave.get('loginInfo').id)
                    );
                });
            });
        }

        examSubjectList.forEach(item => {
            item.name = getPhaseName(item.phaseId) + item.name;
        });
        this.examSubjectList = examSubjectList;
    }

    // 获取考试班级列表
    async getExamClassList(subjectId) {
        const examId = this.$sessionSave.get('reportDetail').examId;
        let clsList = (
            await clzListAPI({
                examId: examId,
                subjectId: subjectId,
            })
        ).data as IClz[];
        this.examClassList = clsList;
        this.getAbPaperList();
    }

    // 获取AB卷列表
    getAbPaperList() {
      let list = [];
      let isShowAPaper = this.examClassList.some(
        item => item.abPaper !== null && item.abPaper !== undefined && item.abPaper.split(',').includes('0')
      );
      let isShowBpaper = this.examClassList.some(
        item => item.abPaper !== null && item.abPaper !== undefined && item.abPaper.split(',').includes('1')
      );
    if (isShowAPaper) {
        list.push({
        text: 'A卷',
        value: '0',
        });
    }
    if (isShowBpaper) {
        list.push({
        text: 'B卷',
        value: '1',
        });
    }

      this.abPaperList = list;
      this.currentAbPaper = list[0]?.value || '';
    }

    // 获取试卷小题分析
    async getQuesList(subjectId = '') {
        this.quesList = [];
        this.questionGroups = [];
        try {
            const data = await getReportSmallAnalysis({
                subjectId: subjectId || this.currentSubject.id,
                examId: this.$sessionSave.get('reportDetail').examId,
                abPaper: this.currentAbPaper,
            });
            if (!data.data.length) {
                return;
            }
            const quesList: IQues[] = data.data.map(item => {
                return {
                    quesNo: item.quesNo,
                    quesNoDesc: item.quesNoDesc,
                    tQuesNo: item.tQuesNo,
                    fullScore: item.fullScore,
                    quesType: item.quesType,
                };
            });
            let subject = this.examSubjectList.find(t => t.id == this.currentSubject.id);

            const paperOption = {
                quesNo: '',
                quesNoDesc: '整张试卷',
                tQuesNo: '',
                fullScore: subject?.fullScore || 0,
            };

            const subQuesList = quesList.filter(
                item => item.quesType == 3 || item.quesType == 4 || item.quesType == 6 || item.quesType == 7
            );

            subQuesList.unshift(paperOption);
            this.quesList = subQuesList;

            const teamQuesRes = await getExamTeamQues({
                schoolId: this.$sessionSave.get('schoolInfo').id,
                examId: this.$sessionSave.get('reportDetail').examId,
                subjectId: this.currentSubject.id,
                abCardSheetType: this.currentAbPaper,
            });
            const teamQuesData = teamQuesRes.data as ITeamQues[];
            if (teamQuesData.length) {
                const questionGroups = [];
                for (const item of teamQuesData) {
                    let name = item.name;
                    let arr: IQues[] = [];
                    for (const data of item.data) {
                        const ques = this.quesList.find(e => e.tQuesNo == data.tQuesNo);
                        if (ques) arr.push(ques);
                    }
                    questionGroups.push({
                        name: name,
                        data: arr,
                    });
                }
                questionGroups.unshift({
                    name: '',
                    data: [paperOption],
                });
                this.questionGroups = questionGroups.filter(item => item.data.length);
            }
        } catch (error) {
            console.error(error);
        }
    }

    // 获取学生列表
    async getStuScoreList() {
        let start;
        let end;
        let keyWord;
        if (this.currentExportType == 0) {
            start = this.scoreRange[0];
            end = this.scoreRange[1];
        }

        if (this.currentExportType == 1) {
            start = this.rankRange[0];
            end = this.rankRange[1];
        }

        if (this.currentExportType == 2) {
            keyWord = this.keyWord || '';
        }

        const params = {
            schoolId: this.$sessionSave.get('schoolInfo').id,
            userId: this.$sessionSave.get('loginInfo').id,
            examId: this.$sessionSave.get('reportDetail').examId,
            subjectId: this.currentSubject.id,
            classId: this.currentClz.id,
            quesNo: this.currentQues.quesNo,
            quesNos: this.currentQues.quesNo ? this.currentQues.quesNoDesc : '',
            tQuesNo: this.currentQues.tQuesNo,
            exportType: this.currentExportType,
            start: start,
            end: end,
            keyWord: keyWord,
            page: this.pagination.page,
            limit: this.pagination.limit,
            viewPaperType: undefined,
            abTestPaperType: this.currentAbPaper,
        };
        if (this.currentQues.quesNo) params.viewPaperType = this.viewPaperType ? 1 : 0;

        try {
            const res = await getStuScoreList(params);
            this.stuScoreList = res.data.rows || [];
            this.pagination.total = res.data.total_rows;
        } catch (error) {
            console.error(error);
            this.stuScoreList = [];
            this.pagination.total = 0;
        }
    }

    // 更换学科
    async setCurrentSubject(item: ISubject) {
        this.currentSubject = item;
        await this.getExamClassList(item.id);
        await this.getQuesList();
        this.setCurrentClz(this.classList[0]);
        this.setCurrentQues(this.quesList[0]);
        this.setCurrentExportType(this.exportTypeList[0].value);
        this.onQueryStu();
    }

    // 设置当前试卷类型
    async setCurrentAbPaper(val: string) {
        this.currentAbPaper = val;
        this.setCurrentClz(this.classList[0], true);
        await this.getQuesList();
        this.setCurrentQues(this.quesList[0]);
    }

    // 设置当前班级
    setCurrentClz(clz: IClz, isQuery = false) {
        this.currentClz = clz;
        this.rankRange = [1, clz.totalNum];
        if (isQuery) this.onQueryStu();
    }

    // 设置当前题目
    setCurrentQues(item: IQues, isQuery = false) {
        this.currentQues = item;
        this.currentTQuesNo = item.tQuesNo;
        this.scoreRange = [0, item.fullScore];

        if (!this.exportTypeList.find(item => this.currentExportType == item.value))
            this.setCurrentExportType(this.exportTypeList[0].value);
        if (isQuery) this.onQueryStu();
    }

    // 设置当前题目tQuesNo
    setCurrentTQuesNo(val: any, isQuery = false) {
        const currentQues = this.quesList.find(item => item.tQuesNo == val);
        this.setCurrentQues(currentQues, isQuery);
    }

    // 设置导出类型
    setCurrentExportType(val: ITypeValue, isQuery = false) {
        this.currentExportType = val;
        if (isQuery) this.onQueryStu();
    }

    // 查询数据
    onQueryStu() {
        this.pagination.page = 1;
        this.getStuScoreList();
    }

    // 表格中切换分页
    onPageChange(val) {
        this.pagination.page = val;
        this.getStuScoreList();
    }

    // 一键导出
    async onExportPaper() {
        let start;
        let end;
        let keyWord;
        if (this.currentExportType == 0) {
            start = this.scoreRange[0];
            end = this.scoreRange[1];
        }

        if (this.currentExportType == 1) {
            start = this.rankRange[0];
            end = this.rankRange[1];
        }
        const params = {
            schoolId: this.$sessionSave.get('schoolInfo').id,
            userId: this.$sessionSave.get('loginInfo').id,
            examId: this.$sessionSave.get('reportDetail').examId,
            subjectId: this.currentSubject.id,
            classId: this.currentClz.id,
            quesNo: this.currentQues.quesNo,
            quesNos: this.currentQues.quesNo ? this.currentQues.quesNoDesc : '',
            tQuesNo: this.currentQues.tQuesNo,
            exportType: this.currentExportType,
            start: start,
            end: end,
            viewPaperType: undefined,
            abTestPaperType: this.currentAbPaper,
        };
        if (this.currentQues.quesNo) params.viewPaperType = this.viewPaperType ? 1 : 0;
        const res = await exportPaper(params);
        if (res.code == 1) {
            this.$message.success('已提交至后台进行处理，请前往【进度】进行下载！');
        }
    }

    // 打开下载进度对话框
    openDownloadDialog() {
        this.isShowDownloadDialog = true;
    }

    // 查看
    async onViewPaper(item: IStuScore) {
        this.previewImgList = [];
        if (!item.paperUrl || item.paperUrl.length == 0) return this.$message.warning('当前学生没有作答图片');
        this.isShowPreviewImg = true;
        let arr = [];

        for (const element of item.paperUrl) {
            let url = element;
            if (!this.currentQues.quesNo) url = element + '?x-oss-process=image/format,jpeg'; // 原卷添加avif格式
            url = await getImgUrl(replaceALiUrl(url));
            arr.push(url);
        }
        this.previewImgList = arr;
    }

    // 下载
    onDownloadPaper(item: IStuScore) {
        if (!item.paperUrl || item.paperUrl.length == 0) return this.$message.warning('当前学生没有作答图片');
        const paperUrl = item.paperUrl;
        const fileName =
            item.className +
            item.stuName +
            this.$sessionSave.get('reportDetail').examName +
            (!this.currentQues.quesNo ? '全卷' : `第${this.currentQues.quesNoDesc}题`) +
            (item.score + '分') +
            `(${this.currentSubject.name})`;

        if (paperUrl.length == 1) {
            this.downloadFile(paperUrl[0], fileName);
        } else {
            paperUrl.forEach((item, i) => {
                this.downloadFile(item, fileName + `(${i + 1})`);
            });
        }
        this.$set(item, 'isClick', true);
    }

    // 下载文件
    async downloadFile(url, fileName?) {
        const res = await fetch(url);
        const blob = await res.blob();
        let downUrl = window.URL.createObjectURL(blob);
        let a = document.createElement('a');
        a.href = downUrl;
        a.download = fileName + '.png';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(downUrl);
    }

    // 返回
    goBack() {
        this.$router.go(-1);
    }
}
</script>

<style scoped lang="scss">
.is-click {
    opacity: 0.8;
}

.paper-export-card {
    position: relative;
    margin-top: 20px;
    min-height: 500px;
}

.form-line {
    display: flex;
}

.form-item {
    margin-top: 40px;
    display: flex;
    gap: 10px;
    font-size: 16px;
    color: #606266;
    line-height: 40px;
    text-align: right;
    padding: 0 12px 0 0;
    box-sizing: border-box;

    &__label {
        // min-width: 220px;
        padding-left: 60px;
        white-space: nowrap;
    }

    &__content {
    }
}

.search-container {
    position: relative;
    display: flex;
    align-items: baseline;

    .search-btns {
        padding-left: 60px;
        margin-top: 20px;

        .paper-button {
            margin-left: 20px;

            &:first-child {
                margin-left: 0;
            }
        }
    }
}

.back-btn {
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;
    cursor: pointer;
}

.score-input {
    width: 135px;
}

.table-wrapper {
    margin-top: 20px;
}

.titleLine {
    display: inline-block;
    position: relative;
    height: 54px;
    line-height: 54px;
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;
    padding-left: 16px;

    &::before {
        content: '';
        width: 6px;
        height: 24px;
        background: #409eff;
        border-radius: 3px;
        position: absolute;
        left: 0;
        top: 15px;
    }
}

.exam-name {
    width: 250px;
    text-align: left;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}
</style>

<template>
  <div class="track-main-right">
    <div style="background-color: #fff; padding-top: 10px">
      <span>考试：</span
      ><span style="color: #409eff">{{ $sessionSave.get("reportDetail").examName }}</span>
      <span style="margin-left: 30px">年级：</span
      ><span style="color: #409eff">{{ $sessionSave.get("reportDetail").gradeName }}</span>
    </div>
    <div class="track-container">
      <div class="title">{{ stepTitles[0] }}:</div>
      <div>
        <el-input
          v-model="reportName"
          autocomplete="off"
          placeholder="请输入报告名称"
          maxlength="20"
          style="width: 470px; height: 36px"
        ></el-input>
      </div>
    </div>
    <div class="splicLine"></div>
    <div class="track-container">
      <div class="title">{{ stepTitles[2] }}</div>
      <div style="color: #97a0a8; line-height: 80px">
        已选择<span style="color: #409eff; font-weight: 600"> {{ checkedSubjectList.length }}</span
        >个学科
      </div>
    </div>
    <div class="box">
      <el-checkbox
        :indeterminate="isIndeterminatesub"
        v-model="checkAllSub"
        @change="(val) => handleCheckAllChange(1, val)"
        style="margin-left: 40px"
        >全部学科</el-checkbox
      >
      <div class="subject-list">
        <el-checkbox-group
          v-model="checkedSubjectList"
          @change="(val) => handleCheckedChange(1, val)"
        >
          <el-checkbox
            v-for="item in examSubjectList"
            :label="item.id"
            :key="item.id"
            border
            style="margin: 0 20px 20px 0"
            >{{ getSubjectName(item) }}</el-checkbox
          >
        </el-checkbox-group>
      </div>
    </div>

    <div class="splicLine"></div>
    <div class="track-container">
      <div class="title">{{ stepTitles[1] }}</div>
      <div style="color: #97a0a8; line-height: 80px">
        已选择<span style="color: #409eff; font-weight: 600">
          {{ checkedExamClassList.length }}</span
        >个班级
      </div>
    </div>
    <div class="box">
      <el-checkbox
        :indeterminate="isIndeterminatecla"
        v-model="checkAllCla"
        @change="(val) => handleCheckAllChange(0, val)"
        style="margin-left: 40px"
        >全部班级</el-checkbox
      >
      <div class="subject-list">
        <el-checkbox-group
          v-model="checkedExamClassList"
          @change="(val) => handleCheckedChange(0, val)"
        >
          <el-checkbox
            v-for="item in examClassList"
            :label="item.id"
            :key="item.id"
            border
            style="margin: 0 20px 20px 0"
            >{{ item.class_name }}</el-checkbox
          >
        </el-checkbox-group>
      </div>
    </div>
    <div class="splicLine"></div>
    <div class="button">
      <el-button type="primary" @click="addReport" :loading="isComfirming">下一步</el-button>
    </div>
  </div>
</template>
<script>
import { getExamSubject, clzListAPI, createdExam } from "@/service/pexam";
export default {
  data() {
    return {
      activeStep: 3,
      checkAllSub: false,
      checkAllCla: false,
      isIndeterminatesub: false,
      isIndeterminatecla: false,
      examId: this.$route.query.customExamId,
      isComfirming: false,
      checkedSubjectList: [],
      checkedExamClassList: [],
      checkedExamClassNames: [], // 选择的班级名称
      examClassList: [],
      examSubjectList: [],
      roleClassList: [],
      reportName: "",
      roleSubjectList: [],
      filterData: {
        classId: "",
        subjectId: "",
        phaseId: "",
        xfId: "",
      },
      stepTitles: [
        "报告名称设置",
        "学生范围设置",
        "学科范围设置",
        "考试指标设置",
        "成绩指标设置",
        "分数换算设置",
      ],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    /**
     * @name:初始化
     */
    init() {
      this.getExamSubjectList();
      setTimeout(() => {
        this.getExamClassList();
      }, 300);
      if (this.$sessionSave.get("customReportInfo")) {
        (this.checkedSubjectList = this.$sessionSave.get("customReportInfo").subjectIds),
          (this.checkedExamClassList = this.$sessionSave.get("customReportInfo").classIds),
          (this.reportName = this.$sessionSave.get("customReportInfo").name);
        (this.isIndeterminatesub = this.checkedSubjectList.length ? true : false),
          (this.isIndeterminatecla = this.checkedExamClassList.length ? true : false);
      }
    },
    /**
     * @name:切换全选
     */
    handleCheckAllChange(isSubject, val) {
      if (isSubject) {
        this.checkedSubjectList = val ? this.examSubjectList.map((item) => item.id) : [];
        this.isIndeterminatesub = false;
      } else {
        this.checkedExamClassList = val ? this.examClassList.map((item) => item.id) : [];
        this.isIndeterminatecla = false;
      }
    },
    /**
     * @name:切换复选框
     */
    handleCheckedChange(isSubject, value) {
      let checkedCount = value.length;
      if (isSubject) {
        this.checkAllSub = checkedCount === this.examSubjectList.length;
        this.isIndeterminatesub = checkedCount > 0 && checkedCount < this.examSubjectList.length;
      } else {
        this.checkAllCla = checkedCount === this.examClassList.length;
        this.isIndeterminatecla = checkedCount > 0 && checkedCount < this.examClassList.length;
      }
    },
    /**
     * @name:添加报告前的数据校验与提示
     */
    addReport() {
      if (!this.reportName) {
        this.$message.error("名称不能为空！");
        return;
      }
      if (!this.checkedSubjectList.length) {
        this.$message.error("学科不能为空！");
        return;
      }
      if (!this.checkedExamClassList.length) {
        this.$message.error("班级不能为空！");
        return;
      }
      this.checkedExamClassNames = this.checkedExamClassList.map(id => {
        let classInfo = this.examClassList.find(t => t.id == id);
        return classInfo.class_name;
      });
      this.createReport();
    },
    /**
     * @name:添加报告
     */
    createReport() {
      this.isComfirming = true;
      createdExam({
        name: this.reportName,
        schoolId: this.$sessionSave.get("schoolInfo").id,
        examId: this.examId ? this.examId : "",
        parentId: this.$sessionSave.get("reportDetail").examId,
        classIds: this.checkedExamClassList.join(","),
        classNames: this.checkedExamClassNames.join(","),
        subjectIds: this.checkedSubjectList.join(","),
        gradeId: this.$sessionSave.get("reportDetail").gradeCode,
        category: this.$sessionSave.get("reportDetail").categoryCode,
        type: this.examSubjectList.length > 1 ? "2" : "1",
        examTime: this.$sessionSave.get("reportDetail").examDateTime,
        schoolYearId: this.$sessionSave.get("reportDetail").schoolYearId,
        schoolTermId: this.$sessionSave.get("reportDetail").schoolTermId,
        scoreConfirm: this.$sessionSave.get("reportDetail").scoreConfirm,
        isPublish: this.$sessionSave.get("reportDetail").isPublish,
        year: this.$sessionSave.get("reportDetail").year
      })
        .then((data) => {
          this.$message({
            message: `保存成功！`,
            type: "success",
            duration: 1000,
          });
          this.isComfirming = false;
          let reportInfo = {
            name: this.reportName,
            classIds: this.checkedExamClassList,
            subjectIds: this.checkedSubjectList,
            examId: data.data,
          };
          this.$sessionSave.set("customReportInfo", reportInfo);
          this.examId = data.data;
          this.$emit("updateActiveStep", this.activeStep, this.examId);
        })
        .catch((err) => {
          this.isComfirming = false;
        });
    },
    /**
     * @name:获取学科
     */
    async getExamSubjectList() {
      let routerPath = this.$route.path,
        loginInfo = this.$sessionSave.get("loginInfo"),
        accountType = loginInfo.account_type,
        rolesIds = this.$sessionSave.get("roleTypes"),
        examId = this.$sessionSave.get("reportDetail").examId;
      // 获取学科
      let examSubjectList = (await getExamSubject({ examId: examId })).data;
      examSubjectList.forEach((item) => {
        item.isShow = true;
      });
      this.examSubjectList = examSubjectList.filter((item) => item.isShow);
      this.$sessionSave.set("innerSubjectList", this.examSubjectList);
    },
    /**
     * @name:获取班级
     */
    async getExamClassList(type = "") {
      let routerPath = this.$route.path,
        loginInfo = this.$sessionSave.get("loginInfo"),
        accountType = loginInfo.account_type,
        examId = this.$sessionSave.get("reportDetail").examId;
      this.examClassList = [];
      // 获取班级
      let clsList = (
        await clzListAPI({
          examId: examId,
          subjectId: this.filterData.subjectId,
        })
      ).data;
      clsList.forEach((item) => {
        item.isShow = true;
      });
      this.examClassList = clsList.filter((item) => item.isShow);
      this.$sessionSave.set("innerClassList", clsList);
    },
    /**
     * @name:获取学科
     */
    getSubjectName(item) {
      let phaseId = item.phaseId;
      if (phaseId == 5) {
        return `高中${item.name}`;
      } else if (phaseId == 4) {
        return `初中${item.name}`;
      } else {
        return `小学${item.name}`;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.track-main-right {
  width: 100%;

  .track-container {
    background: #fff;
    display: flex;
    align-items: center;

    .title {
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #3f4a54;
      line-height: 48px;
      padding: 16px 28px 16px 40px;
    }

    .export-button {
      position: absolute;
      right: 20px;
    }
  }

  .box {
    background: #fff;

    .subject-list {
      margin: 20px 40px 0px;
      height: auto;
      display: flex;
      flex-wrap: wrap;
    }
  }

  .splicLine {
    height: 1px;
    background: #e4e8eb;
    margin: -1px 25px 0 25px;
  }

  .button {
    background-color: #fff;
    padding: 20px;
    position: absolute;
    right: 20px;
  }
}
</style>
<style lang="scss">
.box {
  .subject-list {
    .el-checkbox__inner {
      display: none;
    }
  }
}
</style>
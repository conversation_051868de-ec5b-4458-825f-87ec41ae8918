<!--
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>@class30.com
 * @Date: 2024-05-14 20:00:24
 * @LastEditors: l<PERSON><PERSON><PERSON> y<PERSON><PERSON>@class30.com
 * @LastEditTime: 2024-05-23 20:16:24
-->
<template>
    <el-dialog :title="isEdit ? '编辑' : '添加'" width="400px" :close-on-click-modal="false" :visible.sync="dialogFormVisible" @close="cancel">
        <el-form :model="form" @submit.native.prevent>
            <el-form-item label="名称">
                <el-input v-model="form.title" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="排序">
                <el-input v-model="form.sort" autocomplete="off"></el-input>
            </el-form-item>
            <el-form-item label="说明">
                <el-input type="textarea" v-model="form.desc" autocomplete="off"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="submit">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    props: {
        data: {
            type: Object,
            default: null
        },
        isEdit: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            dialogFormVisible: true,
            form: {
                id: "",
                title: "",
                sort:0,
                desc: ""
            }
        }
    },
    created() {
        if (this.isEdit) {
            this.form.id = this.$props.data.id;
            this.form.title = this.$props.data.title;
            this.form.sort = this.$props.data.sort;
            this.form.desc = this.$props.data.desc;
        }
    },
    methods: {
        cancel() {
            this.dialogFormVisible = false
            this.$emit('close')
        },
        submit() {
            if(!this.form.title){
                this.$message.error('请输入名称')
                return;
            }
            this.dialogFormVisible = false
            this.$emit('submit', this.form)
        }
    }
}
</script>

<style></style>
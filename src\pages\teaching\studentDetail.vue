<template v-on="$listeners">
  <div class="stuPage display_flex align-items_flex-start">
    <div
      class="stuPage-left display_flex flex-direction_column"
      :style="{ height: listHeight + 'px' }"
    >
      <div>
        <el-checkbox v-model="attentionChecked" class="attention-checkbox" style="margin-right: 10px">关注标记</el-checkbox>
        <el-popover placement="top-start" title="" width="500" trigger="hover">
          <div style="line-height: 25px; font-size: 13px">
            知识点列表：按照班级知识点得分从低到高排序 <br />
            学生列表：按照关注学生、下降、波动、平稳、上升排序
          </div>
          <i class="el-icon-question" slot="reference"></i>
        </el-popover>
      </div>

      <div class="header__serarch clearfix display_flex">
        <el-input
          class="search__text"
          placeholder="请输入学生姓名或学号"
          v-model="searchValue"
          @keyup.enter.native="searchReport"
          clearable
        >
        </el-input>
        <div
          class="search__icon el-icon-search display_flex align-items_center justify-content_flex-center"
          @click="searchReport"
        ></div>
      </div>
      <ul class="stuList flex_1 list-none" v-loading="stuListLoading">
        <li
          v-for="(item, index) in stuList"
          :key="index"
          :class="item.stuId === selectStuIndex ? 'active' : ''"
          :title="`${item.stuName}${item.stuNo}`"
          @click="changeStu(item)"
        >
          <div class="li-inner display_flex justify-content_space-between">
            <div class="left flex_1 display_flex align-items_center">
              <i
                class="star el-icon-star-on"
                :style="{ visibility: attentionChecked ? '' : 'hidden' }"
                :class="item.isFollow ? 'active' : ''"
                @click.stop="starStudent(item)"
              ></i>
              <span style="height: 70px" class="flex_1 text-ellipsis">{{ item.stuName }}</span>
            </div>
            <div class="right display_flex align-items_center">
              <i
                class="type-icon"
                :class="
                  !item.status
                    ? ''
                    : item.status === 1
                    ? 'goDown'
                    : item.status === 2
                    ? 'wave'
                    : item.status === 3
                    ? 'smooth'
                    : 'goUp'
                "
              >
              </i>
              {{ !item.status ? "--" : "" }}
            </div>
          </div>
        </li>
      </ul>
    </div>
    <div class="stuPage-right flex_1 clearfix" :style="{ minHeight: listHeight + 'px' }">
      <!-- <div class="title">分数分布</div>
      <el-table
        :data="scoreData"
        class="score-table"
        ref="tableRef"
        v-if="scoreData.length"
        @mousedown.native="mouseDownHandler"
        @mouseup.native="mouseUpHandler"
        @mousemove.native="mouseMoveHandler"
      >
        <el-table-column prop="name" label="" width="150" fixed>
        </el-table-column>
        <el-table-column
          :label="item"
          v-for="(item, index) in scoreData[0].subjectList"
          :key="item"
          align="center"
        >
          <el-table-column label="分数" width="120" align="center">
            <template slot-scope="scope">
              {{ scope.row.scoreList[index] }}
            </template>
          </el-table-column>
          <el-table-column label="班级排名" width="120" align="center">
            <template slot-scope="scope">
              {{ scope.row.clzRankList[index] }}
            </template>
          </el-table-column>
          <el-table-column label="年级排名" width="120" align="center">
            <template slot-scope="scope">
              {{ scope.row.grdRankList[index] }}
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
      <div class="nodata flex_1" v-else>
        <img :src="noResImg" alt="" />
        <p class="text-center">暂无数据</p>
      </div> -->
      <!--成绩趋势图-->
      <div class="title">成绩趋势图</div>
      <div class="detail-box">
        <div class="indicator display_flex align-items_flex-start">
          指 标：
          <ul class="indicator-ul list-none">
            <li
              v-for="(item, index) in indicatorList"
              :key="index"
              @click="activeIndicatorIndex = index"
              :class="index === activeIndicatorIndex ? 'active' : ''"
            >
              {{ item }}
            </li>
          </ul>
        </div>
        <div class="category display_flex align-items_flex-start">
          类 别：
          <ul class="indicator-ul list-none">
            <li
              v-for="(item, index) in categoryList"
              :key="index"
              @click="activeCategoryId = item.id"
              :class="item.id === activeCategoryId ? 'active' : ''"
            >
              {{ item.name }}
            </li>
          </ul>
        </div>
      </div>
      <div v-if="examList.length && chartExamList.length">
        <div class="chart-info-box">
          <p class="exam-name">{{ currentExamInfo.name || "-" }}</p>
          <p>
            <span class="label">T分数：</span>
            <span class="content">{{
              (currentExamInfo.data && currentExamInfo.data[2]) || "-"
            }}</span>
            <span class="label">得分：</span>
            <span class="content">{{ currentExamInfo.score || "-" }}</span>
            <span class="label">班级均分：</span>
            <span class="content">{{ currentExamInfo.clsAvgScore || "-" }}</span>
            <span class="label">{{ activeIndicatorIndex === 1 ? "校级" : "班级" }}名次：</span>
            <span class="content">{{
              (currentExamInfo.data && currentExamInfo.data[activeIndicatorIndex === 1 ? 1 : 0]) ||
              "-"
            }}</span>
          </p>
        </div>
        <div
          id="gradeChart"
          @mouseleave="setBrush"
          :style="{ width: '100%', height: '400px' }"
        ></div>
      </div>
      <div class="nodata flex_1" v-if="!examList.length || !chartExamList.length">
        <img :src="noResImg" alt="" />
        <p class="text-center">暂无数据</p>
      </div>
      <!--            
              <el-tabs v-model="activeType" @tab-click="handleClick">
                <el-tab-pane label="趋势图" name="chart">
                  <div v-if="examList.length&&chartExamList.length">
                    <div class="chart-info-box">
                      <p class="exam-name">{{ currentExamInfo.name || '-' }}</p>
                      <p>
                        <span class="label">T分数：</span> <span class="content">{{ currentExamInfo.data && currentExamInfo.data[2] || '-' }}</span>
                        <span class="label">得分：</span> <span class="content">{{ currentExamInfo.score || '-' }}</span>
                        <span class="label">班级均分：</span> <span
                            class="content">{{ currentExamInfo.clsAvgScore || '-' }}</span>
                        <span class="label">{{activeIndicatorIndex===1?'校级':'班级'}}名次：</span> <span class="content">{{ currentExamInfo.data && currentExamInfo.data[activeIndicatorIndex === 1? 1: 0] || '-' }}</span>
                      </p>
                    </div>
                    <div id="gradeChart" @mouseleave="setBrush" :style="{width: '100%', height: '400px'}"></div>
                  </div>
                  <div class="nodata flex_1" v-if="!examList.length || !chartExamList.length">
                    <img :src="noResImg" alt="">
                    <p class="text-center">暂无数据</p>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="趋势表" name="table"></el-tab-pane>
              </el-tabs> -->

      <!--知识点掌握情况-->
      <div class="title secondTitle">
        知识点掌握情况
        <el-checkbox
          size="medium"
          v-model="lookWeakPoint"
          class="point-checkbox"
          @change="showWeakPoint"
          style="margin-left: 40px; font-size: 16px"
        >
          仅看薄弱知识点
        </el-checkbox>
      </div>
      <div class="clearfix">
        <div class="scoreDescribe pull-right">
          <span>个人得分率</span>
          <span class="grd">年级得分率</span>
          <el-select
            v-model="scoreVal"
            style="width: 160px"
            class="score-select"
            @change="changeSort"
            placeholder="请选择"
          >
            <el-option v-for="item in scoreList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </div>
      </div>
      <el-row type="flex" :gutter="46" class="pointList flex-wrap_wrap" v-if="pointList.length">
        <el-col
          :span="12"
          v-for="(item, index) in pointList"
          :key="index"
          class="point-col display_flex align-items_center"
        >
          <span class="name text-ellipsis text-left" :title="item.pointName">{{
            item.pointName
          }}</span>
          <div class="flex_1" style="width: 100%">
            <el-progress
              color="#409EFF"
              :stroke-width="8"
              class="progress flex_1 display_flex align-items_center"
              :percentage="Number(Number(item.grdRate * 100).toFixed(2))"
            ></el-progress>
            <el-progress
              color="#07C29D"
              :stroke-width="8"
              class="progress flex_1 display_flex align-items_center"
              :percentage="Number(Number(item.rate * 100).toFixed(2))"
            ></el-progress>
          </div>
        </el-col>
      </el-row>
      <el-pagination
        background
        style="margin: 20px auto 0px"
        :hide-on-single-page="!pointList.length"
        class="text-center"
        layout="total, prev, pager, next"
        @current-change="handleCurrentChange"
        :current-page.sync="pagination.page"
        :page-size="pagination.pageSize"
        :total="pagination.total_rows"
      >
      </el-pagination>
      <div class="nodata flex_1" v-if="!listLoading && !pointList.length">
        <img :src="noResImg" alt="" />
        <p class="text-center">暂无数据</p>
      </div>

      <div>
        <div class="title">错题本</div>
        <clsWrong
          v-on="$listeners"
          ref="clsWrong"
          :stuId="selectStuIndex"
          checktype="stu"
        ></clsWrong>
      </div>
    </div>
  </div>
</template>

<script>
import { listClsStu, getStuScoreTrend, followStu, getStuPoint, getStuStat } from "@/service/pexam";
import { selectAllType } from "@/service/pbook";
import clsWrong from "@/components/paper/clsWrong.vue";

export default {
  components: {
    clsWrong,
  },
  name: "studentDetail",
  data() {
    return {
      chartExamList: [],
      listLoading: false,
      noResImg: require("../../assets/no-res.png"),
      listHeight: 200,
      selectStuIndex: 0,
      selectStuName: "",
      attentionChecked: true,
      lookWeakPoint: true,
      stuListLoading: false,
      searchValue: "",
      stuList: [],
      originStuList: [],
      indicatorList: ["班级名次", "校级名次", "T分数"],
      activeIndicatorIndex: 0,
      categoryList: [],
      activeCategoryName: "全部",
      activeCategoryId: "",
      scoreVal: 0,
      scoreList: [
        { id: 0, name: "得分率从低到高" },
        { id: 1, name: "得分率从高到低" },
      ],
      pointList: [],
      // 分页
      pagination: {
        page: 1,
        pageSize: 10,
        total_rows: 0,
      },
      // charts实例
      gradeChart: null,
      // 当前选中学生考试列表
      examList: [
        // {name: '20210423数学练习', dateCreated: '2021-04-23', data: [12, 126, 88, 86], classScore: 77},
      ],
      // 当前选择的考试信息
      currentExamInfo: {},
      //当前页面tab
      activeType: "chart",
      //分数分布数据
      scoreData: [],
      noResImg: require("@/assets/no-res.png"),
    };
  },
  mounted() {
    this.$emit("scrollToTop");
    this.selectAllType();
  },
  watch: {
    activeIndicatorIndex(newVal) {
      this.drawImg();
    },
    activeCategoryName() {
      this.drawImg();
    },
    activeCategoryId() {
      this.drawImg();
    }
  },
  computed: {},
  methods: {
    handleClick() {},
    emptyData() {
      this.examList = [];
      this.pointList = [];
      this.stuList = [];
      this.pagination.total_rows = 0;
    },
    // 获取类别列表
    selectAllType() {
      selectAllType().then((res) => {
        this.$store.commit("saveCategoryList", res.data);
        this.categoryList = this.$deepClone(res.data);
        this.categoryList.unshift({
          id: "",
          name: "全部",
        });
        this.listClsStu();
        this.preDrawChart();
        this.$nextTick(() => {
          this.listHeight =
            document.getElementsByClassName("scrollContainer")[0].clientHeight - 160;
        });
      });
    },
    // 按下鼠标记录鼠标位置
    mouseDownHandler(e) {
      this.mouseOffset = e.clientX;
      this.mouseFlag = true;
    },
    mouseUpHandler(e) {
      this.mouseFlag = false;
    },
    mouseMoveHandler(e) {
      // 这里面需要注意，通过ref需要那个那个包含table元素的父元素
      let divData = this.$refs.tableRef.bodyWrapper;
      if (this.mouseFlag) {
        // 设置水平方向的元素的位置
        divData.scrollLeft -= -this.mouseOffset + (this.mouseOffset = e.clientX);
      }
    },
    // 关注学生
    starStudent(item) {
      this.selectStuIndex = item.stuId;
      this.selectStuName = item.stuName;
      let pms = this.$listeners.getParams();
      followStu({
        teaId: this.$sessionSave.get("loginInfo").id,
        classId: pms.classId,
        className: pms.className,
        subjectId: pms.subjectId,
        studentId: this.selectStuIndex,
        studentName: this.selectStuName,
        isFollow: !item.isFollow,
      }).then((data) => {
        this.listClsStu(item.isFollow ? "cancelStar" : "star");
      });
    },
    // 获取班级学生
    listClsStu(changeStar) {
      this.stuListLoading = true;
      listClsStu({
        teaId: this.$sessionSave.get("loginInfo").id,
        ...this.$listeners.getParams(),
      })
        .then((res) => {
          this.stuListLoading = false;
          this.originStuList = res.data;
          let stuId = this.$sessionSave.get("studentDetail").stuId;
          this.stuList = res.data;
          let index = this.stuList.findIndex((item) => item.stuId === stuId);
          if (index !== -1) {
            let stu = this.stuList.splice(index, 1);
            this.stuList.unshift(stu[0]);
          }
          if (!res.data.length) {
            this.examList = [];
            this.pointList = [];
            this.pagination.total_rows = 0;
            this.scoreData = [];
            this.drawImg();
          } else {
            this.preDrawChart();
            if (!changeStar) {
              this.selectStuIndex =
                this.selectStuIndex == 0
                  ? this.$sessionSave.get("studentDetail").stuId
                  : res.data[0].stuId;
              this.selectStuName =
                this.selectStuName == 0
                  ? this.$sessionSave.get("studentDetail").stuName
                  : res.data[0].stuName;
            }
            this.getStuScoreTrend();
            this.getStuPoint();
            this.getStuErrQues();
            // this.getStuStatData();
          }
        })
        .catch((err) => {
          this.stuListLoading = false;
        });
    },
    // 获取学生成绩趋势
    getStuScoreTrend() {
      this.examList = [];
      getStuScoreTrend({
        studentId: this.selectStuIndex,
        studentName: this.selectStuName,
        ...this.$listeners.getParams(),
      }).then((res) => {
        this.examList = res.data;
        // this.examList = res.data.length > 8 ? res.data.slice(8) : res.data;
        if (!this.examList.length) return;
        this.preDrawChart();
        let dataList = [],
          isRepeat = false;
        this.examList.forEach((item, idx) => {
          this.$set(item, "wholeDateCreated", item.dateCreated);
          this.$set(item, "dateCreated", item.dateCreated.split(" ")[0]);
          if (dataList.indexOf(item.dateCreated) >= 0) {
            isRepeat = true;
          }
          dataList.push(item.dateCreated);
          this.$set(item, "data", [item.clsRank, item.grdRank, item.tscore]);
          this.$set(item, "index", idx);
        });
        // 如果日期有重复的则把日期具体时间拼接上去，否则只显示年月日
        if (isRepeat) {
          this.examList.forEach((item) => {
            this.$set(item, "dateCreated", item.wholeDateCreated);
          });
        }

        this.currentExamInfo = this.examList[this.examList.length - 1];
        this.drawImg();
      });
    },
    // 仅显示薄弱知识点
    showWeakPoint(val) {
      this.pagination.page = 1;
      this.getStuPoint();
    },
    changeSort(val) {
      this.pagination.page = 1;
      this.getStuPoint();
    },
    // 学生知识点
    getStuPoint() {
      this.listLoading = true;
      getStuPoint({
        sort: !this.scoreVal ? "scoreRate" : "-scoreRate",
        isWeak: this.lookWeakPoint ? 1 : 0,
        studentId: this.selectStuIndex,
        studentName: this.selectStuName,
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
        ...this.$listeners.getParams(),
      })
        .then((res) => {
          this.listLoading = false;
          this.pointList = res.data.list || [];
          this.pagination.total_rows = res.data.total || 0;
        })
        .catch((err) => {
          this.listLoading = false;
        });
    },
    // 图表预处理
    preDrawChart() {
      let _this = this;
      window.onresize = () => {
        return (() => {
          if (_this.gradeChart) {
            _this.gradeChart.resize();
          }
        })();
      };
    },
    // 搜索
    searchReport() {
      let stuList = [];
      this.originStuList.forEach((item) => {
        if (
          item.stuName.indexOf(this.searchValue) >= 0 ||
          item.stuNo.indexOf(this.searchValue) >= 0
        ) {
          stuList.push(item);
        }
      });
      this.stuList = stuList;
    },
    // 切换学生
    changeStu(item) {
      this.selectStuIndex = item.stuId;
      this.selectStuName = item.stuName;
      this.getStuScoreTrend();
      this.pagination.page = 1;
      this.getStuPoint();
      this.getStuErrQues();
      // this.getStuStatData();
    },

    // 获取错题本
    async getStuErrQues() {
      await this.$nextTick();
      this.$refs.clsWrong && this.$refs.clsWrong.listErrQues();
    },
    // 切换分页
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getStuPoint();
    },
    // 处理examList数据
    handleStuExamList() {
      let chartExamList = this.$deepClone(this.examList);
      if (this.activeCategoryId != "") {
        chartExamList = chartExamList.filter((item) => {
          return item.category == this.activeCategoryId;
        });
      }

      chartExamList = chartExamList.map((item) => {
        return [item.dateCreated, item.data[this.activeIndicatorIndex]];
      });
      return chartExamList;
    },
    getStuStatData() {
      let pms = this.$listeners.getParams();
      getStuStat({
        studentId: this.selectStuIndex,
        studentName: this.selectStuName,
        schoolId: pms.schoolId,
        gradeId: pms.gradeId,
        startTime: pms.startTime,
        endTime: pms.endTime,
      })
        .then((res) => {
          this.scoreData = res.data;
        })
        .catch((err) => {
          this.scoreData = [];
        });
    },
    // 绘制图表
    drawImg() {
      this.chartExamList = this.handleStuExamList();
      this.$nextTick(() => {
        let _this = this;
        if (this.gradeChart != null && this.gradeChart != "" && this.gradeChart != undefined) {
          this.gradeChart.dispose();
          this.gradeChart = null;
        }

        let seriesItemSetting = {
          type: "line",
          smooth: true,
          symbol: "circle",
          symbolSize: 10,
          color: "#419EFF",
          lineStyle: {
            width: 3,
          },
          areaStyle: {
            color: _this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "#B3D9FF",
              },
              {
                offset: 1,
                color: "#fff",
              },
            ]),
          },
        };
        if (!document.getElementById("gradeChart")) return;
        this.gradeChart = this.$echarts.init(document.getElementById("gradeChart"));
        let chartLen = this.chartExamList.length,
          start = 0;
        if (chartLen > 8) {
          start = (100 / chartLen) * (chartLen - 8);
        }
        this.gradeChart.setOption({
          tooltip: {
            trigger: "axis",
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "line", // 默认为直线，可选为：'line' | 'shadow'
              lineStyle: {
                color: "#409EFF",
              },
            },
            formatter(params) {
              let index = null;
              let infos = _this.examList.filter((it, idx) => {
                index = idx;
                return it.dateCreated == params[0].name;
              });
              _this.currentExamInfo = infos[0];
              return "";
            },
          },
          grid: {
            left: "3%",
            right: "2%",
            top: "2%",
            bottom: "12%",
            containLabel: true,
          },
          xAxis: {
            type: "category",
            boundaryGap: true,
            axisLabel: { rotate: 0 },
            splitLine: { show: true },
          },
          yAxis: {
            type: "value",
            name: "",
            min: this.activeIndicatorIndex < 2 ? 1 : 0,
            minInterval: 1,
            inverse: this.activeIndicatorIndex < 2,
          },
          dataZoom: [
            {
              type: "slider",
              show: true,
              xAxisIndex: [0],
              start: start,
              end: 100,
              // minValueSpan: 1,
              // maxValueSpan: 12,
            },
          ],
          series: [
            {
              ...seriesItemSetting,
              data: [...this.chartExamList],
            },
          ],
        });
        //默认显示最后一个提示框
        this.gradeChart.dispatchAction({
          type: "showTip",
          seriesIndex: 0, // 显示第几个series
          dataIndex: this.chartExamList.length - 1, // 显示第几个数据
        });

        document.getElementById("gradeChart").onmouseover = function () {
          _this.setBrush();
        };
      });
    },
    // 设置竖线
    setBrush(focus) {
      // console.log("1111111=========>", this.currentExamInfo.index);
      this.gradeChart.dispatchAction({
        type: "showTip",
        seriesIndex: 0, // 显示第几个series
        dataIndex: this.currentExamInfo.index, // 显示第几个数据
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.stuPage {
  > div {
    background: #fff;
    box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
    border-radius: 6px;
  }
  .stuPage-left {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    width: 17%;
    margin-right: 16px;
    padding: 24px 0;
    overflow-y: auto;
    overflow-x: hidden;
    .attention-checkbox {
      margin-left: 27px;
      font-size: 16px;
    }
    .header__serarch {
      display: flex;
      width: 220px;
      margin: 22px auto 0;
      .search__icon {
        width: 48px;
        height: 40px;
        font-size: 18px;
        color: #fff;
        background: #409eff;
        border-radius: 0 3px 3px 0;
        outline: none;
        cursor: pointer;
      }
    }
    .stuList {
      width: 100%;
      margin-top: 10px;
      overflow-y: auto;
      overflow-x: hidden;
      > li {
        padding: 0 28px;
        cursor: pointer;
        height: 70px;
        line-height: 70px;
        font-size: 16px;
        color: #3f4a54;
        &:last-child {
          border: none;
        }
        &.active {
          background: #e0efff;
        }
        .li-inner {
          width: 100%;
          height: 100%;
          border-bottom: 1px solid #e7eaed;
          .left {
            font-size: 16px;
            color: #3f4a54;
            .star {
              margin-right: 3px;
              font-size: 24px;
              color: #d5dbdf;
              flex-shrink: 0;
              &.active {
                color: #ffb400;
              }
            }
          }
          .right {
            font-size: 14px;
            color: #717980;
            .type-icon {
              margin-right: 5px;
              display: inline-block;
              width: 22px;
              height: 20px;
            }
            .goUp {
              background: url("../../assets/goUp.png") center center no-repeat;
            }
            .goDown {
              background: url("../../assets/goDown.png") center center no-repeat;
            }
            .smooth {
              background: url("../../assets/smooth.png") center center no-repeat;
            }
            .wave {
              background: url("../../assets/wave.png") center center no-repeat;
            }
          }
        }
      }
    }
  }
  .stuPage-right {
    // width: 100%;
    padding: 34px 40px 34px 35px;
    margin-bottom: 20px;
    .title {
      position: relative;
      padding-left: 38px;
      font-size: 18px;
      font-weight: bold;
      color: #3f4a54;
      margin-bottom: 18px;
      margin-top: 20px;
      &:before {
        content: "";
        display: inline-block;
        width: 30px;
        height: 19px;
        background: url("../../assets/titleIcon.png") center center no-repeat;
        position: absolute;
        left: 0;
        top: 4px;
      }
    }
    .secondTitle {
      margin-top: 50px;
    }
    .detail-box {
      width: 100%;
      background: #f1f5f8;
      margin-bottom: 30px;
      padding: 8px 30px;
      font-size: 16px;
      color: #3f4a54;
      > div {
        line-height: 30px;
      }
      > div > ul {
        margin-left: 10px;
        > li {
          display: inline-block;
          margin-right: 25px;
          cursor: pointer;
          &.active {
            color: #409eff;
          }
        }
      }
      .indicator > ul > li {
        margin-right: 35px;
      }
    }
    .chart-info-box {
      margin-bottom: 20px;
      p {
        text-align: center;
        vertical-align: middle;
      }
      .exam-name {
        font-size: 16px;
        font-weight: bold;
        color: #22272c;
        margin-bottom: 10px;
      }
      .label {
        font-size: 14px;
        color: #717980;
      }
      .content {
        font-size: 16px;
        color: #22272c;
        margin-right: 15px;
        font-weight: bold;
      }
    }
    .scoreDescribe {
      .score-select {
        margin-left: 16px;
      }
      > span {
        position: relative;
        margin-left: 20px;
        &:before {
          content: "";
          position: absolute;
          width: 10px;
          height: 10px;
          left: -16px;
          top: 3px;
          background: #07c29d;
          border-radius: 50%;
        }
        &.grd {
          margin-left: 36px;
        }
        &.grd:before {
          background: #409eff;
        }
      }
    }
    .pointList {
      overflow-y: auto;
      overflow-x: hidden;
      width: 100%;
      margin: 0 !important;
      align-content: flex-start;
      .point-col {
        position: relative;
        line-height: 110px;
        height: 110px;
        &:after {
          content: "";
          position: absolute;
          bottom: 0;
          height: 1px;
          background-color: #e7eaed;
        }
        &:nth-child(odd) {
          padding-left: 0 !important;
          &:after {
            left: 0px;
            right: 23px;
          }
        }
        &:nth-child(even) {
          padding-left: 23px !important;
          padding-right: 0 !important;
          &:after {
            left: 23px;
            right: 0px;
          }
        }
      }
      .name {
        width: 180px;
        margin-right: 10px;
      }
      .progress {
        width: 100%;
        &:first-child {
          margin-bottom: 18px;
          margin-right: 10px;
        }
      }
    }
  }
}

.analyze-table {
  margin-top: 15px;
  &.el-table th,
  &.el-table td {
    text-align: center;
  }
  .el-table__header th {
    padding: 4px 0;
  }
  &.el-table thead {
    font-size: 16px;
    color: rgb(63, 74, 84);
  }
}
.score-table {
  width: 100%;
  cursor: move;
}

#gradeChart > div {
  width: 100% !important;
}
</style>
<style lang="scss">
.search__text {
  &.el-input--suffix .el-input__inner {
    padding-right: 25px;
  }
  .el-input__inner {
    border-right: none;
    border-radius: 3px 0 0 3px;
  }
}

.point-checkbox {
  .el-checkbox__inner {
    width: 20px;
    height: 20px;
    &:after {
      height: 10px;
      left: 6px;
      position: absolute;
      top: 1px;
      width: 5px;
    }
  }
  .el-checkbox__label {
    font-size: 16px;
  }
}
</style>

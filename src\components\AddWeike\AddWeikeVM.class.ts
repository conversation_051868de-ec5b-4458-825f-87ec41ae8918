/*
 * @Description: 添加微课VM
 * @Author: qmzhang
 * @Date: 2024-07-29 10:07:41
 * @LastEditTime: 2024-08-01 10:23:03
 * @FilePath: \personal-bigdata\src\components\AddWeike\AddWeikeVM.class.ts
 */
import { GetNewResourceList } from "@/service/api.js"
import { ReqStructure } from "@/service/types";
import { CoursewareResource } from "@/service/types/commonReslist";
import Notification from '@iclass/element-ui/packages/notification';

export interface AddWeikeListJson {
    id?: string
    resourceId: string
    title: string
    microCourseUrl: string
    duration: number
    fileSize: number
    imgUrl: string
    targetType: number
    questionId?: string
    questionName?: string
    time?: string
    createTime?: string
    targetTypeLabel?: string
    isNew?: boolean
}

export class AddWeikeVM {
    // 实例成员
    private static instance: AddWeikeVM;
    public static GetInstance() {
        return AddWeikeVM.instance;
    }

    private loginInfo: LoginInfo = null;
    // 4:任教班级-错误学生 5:任教班级-所有学生 6:全部班级-错误学生 7:全部班级-所有学生
    static readonly ReciverList = [
        {
            label: "任教班级-错误学生",
            value: 4
        },
        {
            label: "任教班级-所有学生",
            value: 5
        },
        {
            label: "全部班级-错误学生",
            value: 6
        },
        {
            label: "全部班级-所有学生",
            value: 7
        }
    ]

    constructor(vModel: any) {
        this.loginInfo = vModel.loginInfo;

        AddWeikeVM.instance = this;
    }

    /**
     * @description: 获取微课列表
     * @param {Number} page
     * @param {string} keyWord
     * @return {*}
     */
    async GetWeikeList(page: Number, keyWord: string, addedWeikeList: AddWeikeListJson[]): Promise<ReqStructure<CoursewareResource>> {
        let res: any = await GetNewResourceList({
            schoolId: this.loginInfo.schoolid,
            userId: this.loginInfo.id,
            appType: 0,
            suffix: "all",
            page: page,
            limit: 8,
            fileType: 2,
            keyWord,
            sortType: 5,
            order: "desc"
        })

        let listResult: ReqStructure<CoursewareResource>;
        if (res.code) {
            if (res.code == 1) {
                listResult = res.data;
            } else {
                Notification({
                    title: "获取失败",
                    message: "获取列表失败，请稍后重试！",
                    type: 'error',
                    offset: 100,
                });
            }
        } else {
            listResult = res;
        }

        listResult.rows.forEach((item: CoursewareResource) => {
            item.time = AddWeikeVM.FormatSeconds(item.timeLength);
            let findItem = addedWeikeList.find(addedItem => addedItem.resourceId === item.id);
            if (!findItem) return;

            item.addedId = findItem.id;
            item.checked = true;
            item.locked = !findItem.isNew;
            item.targetType = findItem.targetType;
            item.targetTypeLabel = AddWeikeVM.ReciverList.find(rItem => item.targetType == rItem.value).label;
        })

        return listResult;
    }

    /**
     * @description: 毫秒转格式
     * @param {number} ms
     * @return {*}
     */
    static FormatSeconds(ms: number): string {
        const seconds = ms / 1000;
        // 计算分钟和秒数
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;

        // 使用 padStart 确保秒数为两位数
        const formattedSeconds = secs.toString().padStart(2, '0');

        // 返回格式化后的字符串
        return `${minutes}:${formattedSeconds}`;
    }
}
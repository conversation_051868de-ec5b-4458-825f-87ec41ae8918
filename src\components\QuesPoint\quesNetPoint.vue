<template>
  <div class="ques-point-wrapper">
    <div class="content-catalog">
      <div class="catalog-header display_flex align-items_center justify-content_flex-justify">
        <span class="catalog-title">知识点目录</span>
        <el-select v-show="false" class="ques-bank-select" v-model="quesBankValue" placeholder="请选择">
          <el-option
            v-for="item in quesBankOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </div>

      <div class="catalog-treeBox" v-loading="isLoadingPoint">
        <!-- 知识点 -->
        <div class="pointer-type" v-show="quesBankValue == 'xf'">
          <div class="pointer-type-group">
            <div class="pointer-type-pane" :class="{'active': currentPonitType==item.type}" v-for="item in pointerType"
                 :key="item.label" @click="changePointType(item.type)"><span>{{item.label}}</span></div>
          </div>
        </div>
        <div class="point-tree-box">
          <el-tree v-show="quesBankValue=='yiqi'" class="catalog-tree"
                   :data="treeData"
                   ref="pointerTreeYiQList"
                   :props="defaultProps"
                   empty-text="暂无数据"
                   :check-strictly="false"
                   :expand-on-click-node="false"
                   :check-on-click-node="true"
                   :highlight-current="true"
                   :default-expanded-keys="expandedKeys"
                   node-key="code"
                   @current-change="checkYiQChange">
              <span class="el-tree-node__label" slot-scope="{ node, data }">
                <span class="text-ellipsis" :title="node.label">{{ node.label }}</span>
              </span>
          </el-tree>
          <el-tree v-show="quesBankValue=='jyeoo'" class="catalog-tree"
                   :data="treeData"
                   ref="pointerTreeJyeooList"
                   :props="defaultProps"
                   empty-text="暂无数据"
                   :check-strictly="false"
                   :expand-on-click-node="false"
                   :check-on-click-node="true"
                   :highlight-current="true"
                   :default-expanded-keys="expandedKeys"
                   node-key="code"
                   @current-change="checkJyeooChange">
              <span class="el-tree-node__label" slot-scope="{ node, data }">
                <span class="text-ellipsis" :title="node.label">{{ node.label }}</span>
              </span>
          </el-tree>
          <el-tree v-show="quesBankValue=='xf'" class="catalog-tree"
                   :data="treeData"
                   ref="pointerTreeList"
                   show-checkbox
                   :props="defaultProps"
                   empty-text="暂无数据"
                   :check-strictly="false"
                   :expand-on-click-node="false"
                   :default-checked-keys="defaultChecked"
                   node-key="code"
                   @check="handleMoreClick">
              <span class="el-tree-node__label" slot-scope="{ node, data }">
                <span class="text-ellipsis" :title="node.label">{{ node.label }}</span>
              </span>
          </el-tree>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {cookieSave, sessionSave, localSave} from "@/utils/index.js"
// import {getXfPointTree, findPoint} from '@/api/ptask.js'
// import {findPointSubject} from "@/api/ptask"
import { getKnowLedgeInfos } from '@/service/yiqi'
// import { getPointsBySubId } from '@/api/jyeoo'

export default {
  name: 'QuesNetPoint',
  props: {
    pointInfo: null,
    //1：个册知识点，2：网络知识点
    type:1,
  },
  data() {
    return {
      // 当前选中的一起知识点key
      currentYiQKey: null,
      // 题库筛选项
      quesBankOptions: [{
        value: 'jyeoo',
        label: '优选题库'
      },{
        value: 'yiqi',
        label: '一起作业'
      },  {
        value: 'xf',
        label: '其他题库'
      }],
      // 当前选择的题库
      quesBankValue: 'yiqi',
      // 知识点数据
      treeData: [],
      // tree默认值类型
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      // 当前知识点选择的是哪种集合(交集和并集)
      currentPonitType: 0,
      // 知识点交集和并集
      pointerType: [{type: 0, label: '知识点并集'}, {type: 1, label: '知识点交集'}],
      //
      knowledgeCode: '',
      // 是否在加载知识点
      isLoadingPoint: false,
      // 知识点来源
      pointOrigin: '',
      defaultChecked: [],
      // 一起知识点原始数据
      yiQTreeList: [],
      // xf知识点原始数据
      XfTreeList: [],
      // 菁优知识点原始数据
      jyeooTreeList: [],
      p1: '', // 菁优一级知识点
      p2: '', // 菁优二级知识点
      p3: '', // 菁优三级知识点
      yiqiPoint: '',
      expandedKeys: [],
    }
  },
  mounted() {
    this.workbookInfo = this.$route.query
  },
  watch: {
    quesBankValue(newVal) {
      if(newVal == 'yiqi') {
        this.treeData = this.yiQTreeList;
        this.expandedKeys = [];
        this.$nextTick(()=>{
          this.$refs.pointerTreeYiQList.setCurrentKey(this.yiqiPoint);
          if(this.yiqiPoint) {
            this.expandedKeys.push(this.yiqiPoint);
          }
        })
        // 改变 QuesNetFilter 组件的筛选条件
        this.$bus.$emit('changeQuesBank', 'yiqi');
      } else if(newVal == 'xf') {
        this.treeData = this.XfTreeList;
        // 改变 QuesNetFilter 组件的筛选条件
        this.$bus.$emit('changeQuesBank', 'xf');
      } else if(newVal == 'jyeoo') {
        this.treeData = this.jyeooTreeList;
        // this.p1 = this.treeData[0].code;
        // this.p2 = this.treeData[0].code;

        this.$nextTick(()=>{
          // this.$refs.pointerTreeJyeooList.setCurrentKey(this.treeData[0].code);
          this.$refs.pointerTreeJyeooList.setCurrentKey(this.p3?this.p3:this.p2);
          if(this.p1 || this.p2 || this.p3) {
            this.expandedKeys.push(this.p3?this.p3:this.p2);
          }
        })
        // 改变 QuesNetFilter 组件的筛选条件
        this.$bus.$emit('changeQuesBank', 'jyeoo')
      }
    },
    yiQTreeList: {
      handler(newVal) {
        if(newVal.length) {
          // this.treeData = newVal;
        }
      },
      deep: true,
    },
    jyeooTreeList: {
      handler(newVal) {
        if(newVal.length) {
          this.treeData = newVal;
        }
      },
      deep: true,
    }
  },
  methods: {
    // 清除已选的节点
    clearCheckedKeys() {
      this.$refs.pointerTreeList.setCheckedKeys([]);
      this.$refs.pointerTreeYiQList.setCurrentKey(null);
      // this.$refs.pointerTreeJyeooList.setCurrentKey(null);
    },
    // 处理多选节点——xf知识点
    handleMoreClick(node, checkedStatus) {
      console.log(node, checkedStatus)
      let needKeys = []
      // TODO: 获取需要的节点。子节点全选就选择根节点
      checkedStatus.checkedNodes.forEach(item => {
        if (checkedStatus.checkedKeys.indexOf(item.parentCode) < 0) {
          needKeys.push(item.code)
        }
      })
      // console.log('needKeys-->', needKeys);
      this.knowledgeCode = needKeys.join(',')
      this.changePoint()
    },
    // 切换知识点类型（交集和并集）
    changePointType(type) {
      if (this.currentPonitType == type) {
        return
      }
      this.currentPonitType = type
      this.changePoint()
    },
    // 切换知识点类型或触发节点时 获取新的题目列表
    changePoint() {
      this.$bus.$emit('changePoint', this.pointOrigin, {
        ponitType: this.currentPonitType,
        knowledgeCode: this.knowledgeCode
      })
    },
    // 查询知识点三级结构数据
    getC30Point() {
      let subject = localSave.get('currentSubject')
      let pointData = localSave.get('C30Point_' + subject.id)
      if (pointData && pointData.time
        && pointData.time - new Date().getTime() > 0) {

        this.currentPonitType = 0
        // this.knowledgeCode = '';
        if (this.pointInfo) {
          this.knowledgeCode = this.pointInfo.knowledgeCode
          if (this.knowledgeCode) {
            this.defaultChecked = this.knowledgeCode.split(',')
          }
        }
        this.treeData = pointData.data
        this.pointOrigin = 'c30Point'
        this.changePoint()
        return
      }
      this.isLoadingPoint = true
      findPointSubject({
        subjectId: subject.id
      }).then(data => {
        localSave.set('C30Point_' + subject.id, {
          time: new Date().getTime() + 8640000,
          data
        })
        this.isLoadingPoint = false

        this.currentPonitType = 0
        this.knowledgeCode = ''
        if (this.pointInfo) {
          this.knowledgeCode = this.pointInfo.knowledgeCode
          if (this.knowledgeCode) {
            this.defaultChecked = this.knowledgeCode.split(',')
          }
        }
        this.treeData = data

        this.pointOrigin = 'c30Point'
        this.changePoint()
      }).catch(err => {
        console.log(err)
      })

    },
    // 获取讯飞题库知识点
    getXfPointTree() {
      if (this.treeData.length) {
        this.currentPonitType = 0
        this.pointOrigin = 'xfPoint'
        this.changePoint()
        return
      }
      this.isLoadingPoint = true
      getXfPointTree({
        subjectId: this.workbookInfo.subject_id,
        phaseId: this.workbookInfo.phase_id
      }).then(data => {
        this.isLoadingPoint = false
        this.XfTreeList = data
        // this.treeData = this.XfTreeList

        this.currentPonitType = 0
        this.knowledgeCode = ''
        this.pointOrigin = 'xfPoint'
        this.changePoint()
      }).catch(err => {
        console.log(err)
      })
    },
    // 点击一起知识点
    checkYiQChange(...args) {
      // console.log('yiqi point-->', args[0])
      this.yiqiPoint = args[0];
      this.$bus.$emit('changeYiqPoint', args[0])
    },
    // 点击菁优知识点
    checkJyeooChange(...args) {
      // console.log(args)
      let node = args[1];
      let p1 = node.data.code || '', p2 = node.data.code || '', p3 = '';
      if(node.level === 1) {
        // do nothing
      } else if(node.level === 2) {
        p2 = node.data.code || '';
        p1 = node.parent.data.code || '';
      } else if(node.level === 3) {
        p3 = node.data.code || '';
        p2 = node.parent.data.code || '';
        p1 = node.parent.parent.data.code || '';
      }
      this.p1 = p1; this.p2 = p2; this.p3 = p3;
      this.$bus.$emit('changeJyeooPoint', {p1, p2, p3})
      // console.log(p1, p2, p3)
    },
    // 获取所有知识点
    getAllPointTree(tokenInfo) {
      this.getYiQPointTree(tokenInfo);
      this.getJyeooPointTree(tokenInfo);
    },
    // 获取一起题库知识点
    getYiQPointTree(tokenInfo) {
      getKnowLedgeInfos({
        headers: {
          token: tokenInfo.token || ''
        },
        data: {
          userId: tokenInfo.id,
        }
      }).then(data => {
        const res = JSON.parse(data);
        if(res.data && res.data.tags.length) {
          let list = this.handleYiQPointTree(res.data.tags);
          if(list[0].parent == null && list[0].children.length) {
            this.yiQTreeList = list[0].children;
            // this.treeData = this.yiQTreeList;
          }
        }
      }).catch(err => {
        console.log(err)
      })
    },
    // 处理一起知识点数据
    handleYiQPointTree(treeList, node) {
      if (node) {
        return treeList.filter(item => item.parent === node._id).map(item => {
          item.children = this.handleYiQPointTree(treeList, item)
          return {
            ...item,
            code: item._id,
          }
        })
      } else {
        return treeList.filter(item => item.parent == null).map(item => {
          item.children = this.handleYiQPointTree(treeList, item)
          return {
            ...item,
            code: item._id,
          }
        })
      }
    },
    // 获取菁优题库知识点
    getJyeooPointTree(tokenInfo) {
      let subject = localSave.get('currentSubject')
      getPointsBySubId({
        headers: {
          token: tokenInfo.token || ''
        },
        data: {
          subId: subject.id,
        }
      }).then(data => {
        if(data && data.length) {
          this.jyeooTreeList = this.handleJyeooPointTree(data);
          this.p1 = this.jyeooTreeList[0].code;
          this.p2 = this.jyeooTreeList[0].code;
          this.$nextTick(()=>{
            this.$refs.pointerTreeJyeooList.setCurrentKey(this.jyeooTreeList[0].code);
            this.$parent.$refs.jyeooQuesCardNet.getQuesByCondition(this.tokenInfo);
          })
        } else {
          this.jyeooTreeList = []
        }
      }).catch(err => {
        console.log(err)
        this.jyeooTreeList = []
      })
    },
    // 处理菁优题库知识点
    handleJyeooPointTree(data) {
      if(!Array.isArray(data) || !data.length) {
        return []
      }
      return data.map(item => {
        item.children = this.handleJyeooPointTree(item.childs)
        return {
          name: item.pointName,
          code: item.num,
          children: item.children
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.content-catalog {
  height: 100%;

  width: 300px;
  background: #fff;
  border: 1px solid #e8ebed;
  border-radius: 3px;

  .catalog-header {
    width: 298px;
    height: 48px;
    line-height: 48px;
    background: #f5f6fa;
    border-radius: 4px 4px 0 0;
    font-size: 16px;
    font-weight: 400;
    color: #666;
    padding: 0 15px 0 13px;

    .catalog-title {
      position: relative;
      padding-left: 10px;

      &:before {
        content: '';
        position: absolute;
        width: 4px;
        height: 16px;
        background: #008dea;
        border-radius: 2px;
        left: 0;
        top: 17px;
      }
    }

  }

  .catalog-treeBox {
    height: 100%;

    .pointer-type {
      margin: 10px auto;

      .pointer-type-group {
        display: flex;
        align-items: center;
        justify-content: space-around;
        border-bottom: 2px solid #E4E8ED;

        .pointer-type-pane {
          width: 50%;
          line-height: 48px;
          text-align: center;
          font-size: 16px;
          color: #4C5866;
          position: relative;

          &:hover {
            color: #409EFF;
            cursor: pointer;
          }

          &.active {
            color: #409EFF;

            &::before {
              content: '';
              width: 100%;
              height: 0;
              border-bottom: 2px solid #409EFF;
              position: absolute;
              bottom: -2px;
              left: 0;
            }
          }
        }
      }
    }

    .point-tree-box {
      height: 100%;
    }

    .catalog-tree {
      width: 100%;
      // min-height : 500px;
      // height: 530px;
      /*height: calc(100vh - 340px);*/
      height: calc(100% - 118px);
      overflow-x: hidden;
      overflow-y: auto;
    }
  }
}
</style>

<style lang="scss">
.content-catalog {
  .catalog-header {
    .ques-bank-select {
      .el-input__inner {
        width: 130px;
      }
    }
  }
}
.catalog-tree {
  margin-top: 6px;

  .el-tree-node__expand-icon.expanded {
    transform: none;
  }

  .el-icon-caret-right:before {
    content: "\e7a0" !important;
  }

  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {
    content: "\e7a2" !important;
  }

  .el-tree-node__expand-icon {
    font-size: 17px;
  }

  .el-tree-node__label {
    font-size: 16px;
  }

  .el-tree-node__content {
    height: 26px;
  }

  .el-tree-node__content {
    display: flex;
  }

  .el-tree-node__content > span:last-child {
    flex: 1;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .el-checkbox.is-checked + .el-tree-node__label {
    color: #409EFF;
  }

  &::-webkit-scrollbar, .selected-catalog-box::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }

  &::-webkit-scrollbar-thumb, .selected-catalog-box::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    background-color: rgba(144, 147, 153, .3);
  }

  &::-webkit-scrollbar-track, .selected-catalog-box::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    // box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #ffffff;
    border-radius: 10px;
  }
}

// 树节点选中高亮
.catalog-treeBox .catalog-tree.chapter__tree .el-tree-node.is-current.is-focusable > .el-tree-node__content {
  background-color: #ffffff;

  & > .el-tree-node__label {
    color: #008DEA;
  }
}

.catalog-treeBox .el-tree-node__content:hover, .catalog-treeBox .el-tree div[role=treeitem].is-current>.el-tree-node__content .el-tree-node__label {
  color: #008DEA;
}
</style>

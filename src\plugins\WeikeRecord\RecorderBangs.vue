<!--
 * @Description: 微课录制刘海
 * @Author: qmzhang
 * @Date: 2024-08-09 17:35:34
 * @LastEditTime: 2024-08-10 18:02:12
 * @FilePath: \question-comment-apart\plugins\WeikeRecord\RecorderBangs.vue
-->
<template>
    <div class="recorder-bangs animated slideInDown">
        <div class="title text-ellipsis">{{ title }}</div>
        微课录制中
    </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component
export default class RecorderBangs extends Vue {
    @Prop() title: string;
}
</script>

<style lang="scss" scoped>
.recorder-bangs {
    position: fixed;
    top: 0;
    left: 50%;
    z-index: 100;
    width: 300px;
    text-align: center;
    padding: 10px 20px;
    margin-left: -150px;
    border-radius: 0 0 10px 10px;
    background-color: #0199ff;
    color: #fff;
    font-size: 18px;

    .title {
        margin-bottom: 5px;
        font-size: 16px;
    }
}
</style>
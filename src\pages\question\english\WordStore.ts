import { saveTestBanks } from '@/service/api';
import { getToken } from '@/service/auth';
import { sessionSave } from '@/utils';
import { guid } from '@/utils/index';
import { Message } from '@iclass/element-ui';
import { IQUES_TYPE, IQUES_SCAN_MODE } from '@/typings/card';

export interface WordInfo {
  id: string;
  word: string;
  translation: string;
}

class WordStore {
  // 中译英
  choseEnList: WordInfo[] = [];
  // 英译中
  choseZhList: WordInfo[] = [];
  // 错题
  choseErrList: WordInfo[] = [];

  get wordList() {
    return [...this.choseEnList, ...this.choseZhList, ...this.choseErrList];
  }

  constructor() {}

  // 清空
  public clear() {
    this.clearEnList();
    this.clearZhList();
    this.clearErrList();
  }

  // 清空中译英
  public clearEnList() {
    this.choseEnList = [];
  }

  // 清空英译中
  public clearZhList() {
    this.choseZhList = [];
  }

  // 清空错题
  public clearErrList() {
    this.choseErrList = [];
  }

  // 构建题目数据
  public buildQuesInfo() {
    let bigQuesList = [];
    let quesNo = 1;
    if (this.choseEnList.length || this.choseErrList.length) {
      let bigQues = this.getEmptyQuesObj('中译英');
      this.choseEnList.forEach(item => {
        bigQues.data.push(this.getEmptyChildQuesObj(item, quesNo++, true));
      });

      this.choseErrList.forEach(item => {
        bigQues.data.push(this.getEmptyChildQuesObj(item, quesNo++, true));
      });

      bigQues.score = bigQues.data.length;
      bigQues.count = bigQues.data.length;
      bigQuesList.push(bigQues);
    }
    if (this.choseZhList.length) {
      let bigQues = this.getEmptyQuesObj('英译中');
      this.choseZhList.forEach(item => {
        bigQues.data.push(this.getEmptyChildQuesObj(item, quesNo++, false));
      });
      bigQues.score = bigQues.data.length;
      bigQues.count = bigQues.data.length;
      bigQuesList.push(bigQues);
    }
    return bigQuesList;
  }

  // 去制卡
  public async gotoCard(option: { subjectId }) {
    option.subjectId = option.subjectId || 3;

    let quesInfo = this.buildQuesInfo();
    let loginInfo = sessionSave.get('loginInfo');
    let params = {
      tbName: '英语专项练习卡',
      schoolId: sessionSave.get('schoolInfo').id,
      userId: loginInfo.id,
      userName: loginInfo.realname,
      cardType: 4,
      quesInfo: JSON.stringify(quesInfo),
    };
    let result = (await saveTestBanks(params).catch(function (error) {})) as any;
    if (result && parseInt(result.code) == 1) {
      if (result.data && result.data.tbInfo && result.data.tbInfo.tbId) {
        let testBankId = result.data.tbInfo.tbId;
        let subjectId = option.subjectId;
        let token = getToken();
        let url =
          process.env.VUE_APP_CARDURL +
          `?id=${testBankId}&examName=英语专项练习卡&cardType=4&correctType=1&pageLayout=1&subjectId=${subjectId}&token=${token}`;
        window.open(url, '_blank');
      }
    } else {
      Message.error(result.msg);
    }
  }

  /**
   * @name: 获取一个空的题目对象
   * @return: 空的大题对象
   */
  private getEmptyQuesObj(name) {
    return {
      id: guid(),
      name: name,
      count: 0,
      score: 0,
      typeId: IQUES_TYPE.fill,
      scanMode: IQUES_SCAN_MODE.AI_FILL,
      type: '填空智批题',
      data: new Array(),
    };
  }
  /**
   * @name: 获取一个空的小题对象
   * @return: 空的小题对象
   */
  private getEmptyChildQuesObj(item: WordInfo, index: number, isEn: boolean) {
    return {
      id: guid(),
      name: '填空智批题',
      wordId: item.id,
      word: isEn ? item.translation : item.word,
      optionCount: 0,
      quesNos: index,
      quesNo: index,
      answer: isEn ? item.word : item.translation,
      score: 1,
      typeId: IQUES_TYPE.fill,
      scanMode: IQUES_SCAN_MODE.AI_FILL,
      type: '填空智批题',
    };
  }
}

export default new WordStore();

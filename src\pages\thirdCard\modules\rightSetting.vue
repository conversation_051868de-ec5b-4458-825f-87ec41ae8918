<template>
  <div>
    <div class="right-ques-list">
      <el-collapse v-model="activeNames" @change="handleChange">
        <el-collapse-item title="基本信息" name="1" v-click-five-times="handleFiveClicks">
          <el-form ref="baseInfoForm" label-width="80px">
            <el-form-item label="定位信息" v-if="pointList.length > 0">
              <div class="column-list">
                <span v-for="(item, index) in pointList" :key="index">
                  第{{ index + 1 }}面
                  <span v-if="!item?.[0]?.pos_list?.length" class="point-num">
                    未框选
                  </span>
                  <template v-else>
                    <span v-if="item?.[0]?.pos_list?.length == 4">{{ (item?.[0]?.pos_list?.length || 0) + '个' }}</span>
                    <span v-else class="point-num">{{ (item?.[0]?.pos_list?.length || 0) + '个' }}</span>
                  </template>
                </span>
              </div>
            </el-form-item>
            <el-form-item label="标题" v-if="titlePos.length > 0">
              <div class="column-list">
                <span v-for="(item, index) in titlePos" :key="index">
                  第{{ index + 1 }}面
                  <span v-if="item && item.length"> 已框选 </span>
                  <span v-else class="point-num">未框选</span>
                </span>
              </div>
            </el-form-item>
            <el-form-item label="页码标记" v-if="pageCount > 2">
              <span>
                第一张
                <span v-if="(pagePos[0] && pagePos[0]?.length) || (pagePos[1] && pagePos[1]?.length)"> 已框选 </span>
                <span v-else class="point-num">未框选</span>
              </span>
              ,
              <span>
                第二张
                <span v-if="(pagePos[2] && pagePos[2]?.length) || (pagePos[3] && pagePos[3]?.length)"> 已框选 </span>
                <span v-else class="point-num">未框选</span>
              </span>
            </el-form-item>

            <el-form-item label="考号" v-if="examNoList.length > 0">
              <template v-if="pageCount > 2">
                <p>第一张</p>
                <span>填涂
                  <span v-if="examNoList[0] && examNoList[0].length">已框选</span>
                  <span class="point-num" v-else>未框选</span>
                </span>，
                <span>条形码
                  <span v-if="barcodeExamNoList[0] && barcodeExamNoList[0].length">已框选</span>
                  <span class="point-num" v-else>未框选</span>
                </span> 
                <p>第二张</p>
                <span>填涂
                  <span v-if="examNoList[2] && examNoList[2].length">已框选</span>
                  <span class="point-num" v-else>未框选</span>
                </span>，
                <span>条形码
                  <span v-if="barcodeExamNoList[2] && barcodeExamNoList[2].length">已框选</span>
                  <span class="point-num" v-else>未框选</span>
                </span>
              </template>
              <template v-else>
                <span>填涂
                <span v-if="examNoList[0] && examNoList[0].length">已框选</span>
                <span class="point-num" v-else>未框选</span>
              </span>，
              <span>条形码
                <span v-if="barcodeExamNoList[0] && barcodeExamNoList[0].length">已框选</span>
                <span class="point-num" v-else>未框选</span>
              </span>
              </template>
            </el-form-item>

            <el-form-item label="缺考标记" v-if="missMarkPos.length > 0">
              <span v-if="missMarkPos[0] && missMarkPos[0].length">已框选</span>
              <span class="point-num" v-else>未框选</span>
            </el-form-item>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="题目信息" name="2">
          <div class="big-ques">
            <div class="ques-title-cover click-element none-select"></div>
            <el-collapse v-model="activeQuesType">
              <!-- 客观题 -->
              <el-collapse-item name="3" v-if="objectQues.length > 0">
                <template slot="title">客观题{{ objectQuesNums }}题</template>
                <el-row>
                  <el-col :span="9">题号</el-col>
                  <el-col :span="8">题型</el-col>
                  <el-col :span="4">选项</el-col>

                </el-row>
                <template v-for="(page, pageIndex) in objectQues">
                  <draggable :key="pageIndex" v-model="objectQues[pageIndex]" chosenClass="chosen"
                    ghost-class="ghostClass" :group="{ name: 'object', pull: false }" @end="dragEnd($event)"
                    :no-transition-on-drag="true" fallbackTolerance="20" :options="{ forceFallback: true }">
                    <el-row v-for="(ques, index) in objectQues[pageIndex]" :key="index"
                      class="small-ques-row none-select">
                      <el-col :span="9" class="none-select">
                        <el-input class="none-select" v-model="ques.question_nos" placeholder="请输入题号"></el-input>
                      </el-col>
                      <el-col :span="8" class="none-select">
                        <el-select class="none-select" v-model="ques.question_type" placeholder="请选择">
                          <el-option v-for="item in objectQueesType" :key="item.id" :label="item.name" :value="item.id">
                          </el-option>
                        </el-select>
                      </el-col>
                      <el-col :span="4" class="none-select">
                        <span :style="{ color: optColors[ques.option_list.length] }">{{ ques.option_list.length }}</span>
                      </el-col>
                      <el-col :span="3">
                        <i class="el-icon-rank"></i>
                      </el-col>
                    </el-row>
                  </draggable>
                </template>
              </el-collapse-item>
              <!-- 主观题 -->
              <el-collapse-item name="4" v-if="subjectQues.length > 0">
                <template slot="title">主观题{{ subjectQuesNums }}题 </template>
                <el-row>
                  <el-col :span="9">题号</el-col>
                  <el-col :span="8">题型</el-col>
                </el-row>
                <template v-for="(page, pageIndex) in subjectQues">
                  <draggable :key="pageIndex" v-model="subjectQues[pageIndex]" :group="{ name: 'subject', pull: false }"
                    chosenClass="chosen" ghost-class="ghostClass" @end="dragSubjectEnd($event)"
                    :no-transition-on-drag="true" fallbackTolerance="20" :options="{ forceFallback: true }">
                    <template v-for="(ques, index) in subjectQues[pageIndex]">
                      <el-row v-if="!ques.isSplitQues" :key="index" class="small-ques-row none-select">
                        <el-col :span="9" class="none-select">
                          <el-input class="none-select" v-model="ques.question_nos"
                            @change="((val) => { changeQuesNos(val, ques) })" placeholder="请输入题号"></el-input>
                        </el-col>
                        <el-col :span="8" class="none-select">
                          <el-select class="none-select" v-model="ques.question_type"
                            @change="((val) => { changeQuesType(val, ques) })" placeholder="请选择">
                            <el-option v-for="item in subjectQuesType" :key="item.id" :label="item.name"
                              :value="item.id">
                            </el-option>
                          </el-select>
                        </el-col>
                        <el-col :span="2">
                          <i class="el-icon-rank"></i>
                        </el-col>
                      </el-row>
                    </template>
                  </draggable>
                </template>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <div class="right-ques-hand">
      <el-button @click="saveData(false)" size="medium"
        v-if="paperInfo.isRelatedWork != 1 && paperInfo.isOverCard != 1">
        {{ currentStep == 'others' ? '完成' : '保存' }}
      </el-button>
      <el-button @click="saveData(true)" size="medium" :style="{ display: (isShowCancel ? '' : 'none') }"
        v-if="paperInfo.isRelatedWork == 1 && paperInfo.isOverCard == 1 && currentStep == 'others'">
        修改
      </el-button>
      <el-button type="danger" size="medium"
        v-if="paperInfo.isOverCard == 1 && paperInfo.isRelatedWork != 1 && isShowCancel"
        @click="cancelOver">取消完成</el-button>
      <el-button v-if="!(currentStep == 'others' && currPage + 1 == pageCount)" type="primary" size="medium"
        @click="nextPage">{{ currPage + 1 == pageCount ? '下一步' : '下一页' }}</el-button>
    </div>
  </div>
</template>

<script>
import { updateNotOverCard } from '@/service/testbank.js';
import { getQueryString } from '@/utils';
import draggable from 'vuedraggable';

export default {
  props: {
    //主观题
    subjectQues: {
      type: Array,
      default() {
        return [];
      },
    },
    //当前页
    currPage: {
      type: Number,
      default() {
        return 0;
      },
    },
    //客观题
    objectQues: {
      type: Array,
      default() {
        return [];
      },
    },
    //总页数
    pageCount: {
      type: Number,
      default() {
        return 0;
      },
    },
    //定位点
    pointList: {
      type: Array,
      default() {
        return [];
      },
    },
    //考号
    examNoList: {
      type: Array,
      default() {
        return [];
      },
    },
    //条形码
    barcodeExamNoList: {
      type: Array,
      default() {
        return [];
      },
    },
    //缺考
    missMarkPos: {
      type: Array,
      default() {
        return [];
      },
    },
    //标题
    titlePos: {
      type: Array,
      default() {
        return [];
      },
    },
    //页码标记
    pagePos: {
      type: Array,
      default() {
        return [];
      },
    },
    //当前步骤
    currentStep: {
      type: String,
      default() {
        return '';
      },
    },
    //试卷信息
    paperInfo: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  components: {
    draggable,
  },
  watch: {
    objectQues: {
      handler(newval, oldval) {
        this.objectQuesNums = newval.reduce((total, item) => total + item.length, 0);
      },
      deep: true,
      immediate: true,
    },
    subjectQues: {
      handler(newval, oldval) {
        this.subjectQuesNums = newval.reduce((total, item) => {
          //多区域题目仅统计一次
          return total + item.filter((q) => { return !q.isSplitQues }).length
        }, 0);
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      activeNames: ['1', '2'],
      activeQuesType: ['3', '4'],
      tbId: getQueryString('tbId') || '',
      //客观题题型
      objectQueesType: [
        {
          id: 8,
          name: '单选题',
        },
        {
          id: 1,
          name: '多选题',
        },
        {
          id: 2,
          name: '判断题',
        },
      ],
      //主观题题型
      subjectQuesType: [
        {
          id: 3,
          name: '填空题',
        },
        {
          id: 6,
          name: '简答题',
        },
      ],
      isShowCancel: false,
      // 客观题题数
      objectQuesNums: 0,
      // 主观题题数
      subjectQuesNums: 0,
      optColors: [
        '#000000',
        '#FE5D50',
        '#E60BE0',
        '#FBBC29',
        '#409eff',
        '#F71212',
        '#0426FA',
        '#00CAAF',
        '#996F55',
        '#FA7104',
        '#81FF07',
        '#606266',
        '#4C0CF4',
        '#000000',
        '#FE5D50'
      ]
    };
  },
  mounted() { },
  methods: {
    handleFiveClicks() {
      this.isShowCancel = true;
    },
    changeQuesNos(nos, item) {
      this.subjectQues.forEach((page) => {
        page.forEach((ques) => {
          if (ques.question_id == item.question_id) {
            ques.question_nos = nos;
          }
        })
      })
    },
    changeQuesType(type, item) {
      this.subjectQues.forEach((page) => {
        page.forEach((ques) => {
          if (ques.question_id == item.question_id) {
            ques.question_type = type;
          }
        })
      })
    },
    async saveData(isUpdate) {
      if (isUpdate) {
        await this.$confirm('修改试卷信息会同步重置所有阅卷设置，此操作无法恢复，是否继续修改？', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
      }
      this.$emit('save-data', isUpdate);
    },
    nextPage() {
      this.$emit('next-page');
    },
    // 取消完成制卡
    async cancelOver() {
      const updateParams = {
        tbId: this.tbId,
        optUserId: this.$sessionSave.get('loginInfo')?.id || '',
      };
      const updateRes = await updateNotOverCard(updateParams);
      if (updateRes.code == 1) {
        location.reload();
      }
    },
    handleChange() { },
    // 拖动结束
    dragEnd(evt) {
      // this.$emit('drag-object-end', this.objectQues);
    },
    dragSubjectEnd(evt) {
      // this.$emit('drag-subject-end', this.subjectQues);
    },
  },
};
</script>

<style lang="scss" scoped>
.point-num {
  color: #f5222d;
  font-weight: bold;
}

.right-ques-list {
  height: calc(100% - 36px);
  font-size: 18px;
  overflow: auto;

  .column-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }
}

.right-ques-hand {
  height: 40px !important;
  text-align: center;
  line-height: 40px !important;
}

.big-ques {
  .small-ques-row {
    margin-bottom: 5px;
  }
}

.none-select {
  user-select: none;
  /* iOS Safari */
  -webkit-user-select: none;
  /* Chrome/Safari/Opera */
  -khtml-user-select: none;
  /* Konqueror */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
}

.ghostClass {
  border-radius: 2px;
  border: 1px solid #409eff;
}
</style>
<style lang="scss">
.right-ques-list {
  .el-collapse-item__header {
    font-size: 18px;
    font-weight: bolder;
  }

  .el-collapse-item__content {
    color: #606266;
    .el-form-item{
      margin-bottom:0;
    }
  }
}

.big-ques {
  .el-collapse-item__header {
    font-size: 16px;
    font-weight: 500;
    // background-color: #f4f4f4;
    padding: 0 10px;
  }

  .el-collapse-item__content {
    color: #606266;
    padding: 0 10px;
  }

  .el-row {
    height: 30px;
    line-height: 30px;
  }

  .el-col {
    text-align: center;
  }

  .el-input {
    width: 80px !important;
  }

  .el-input__inner {
    height: 30px !important;
    line-height: 30px !important;
    padding: 1.2mm 1mm !important;
  }

  .el-input__icon {
    line-height: 30px !important;
  }
}
</style>
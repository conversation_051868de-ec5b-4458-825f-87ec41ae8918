<template>
  <el-dialog custom-class="seled-assign-container" :visible="modalVisible" width="490px" :before-close="handleClose"
    :modal-append-to-body="false" :close-on-click-modal="false">
    <div slot="title" class="dialog-title">
      <span style="margin-right: 10px">分配方式</span>
    </div>
    <el-radio-group v-model="assignType">
      <el-radio :label="1">平均分配</el-radio>
      <el-radio :label="2">动态平均</el-radio>
      <el-radio :label="5">效率优先</el-radio>
      <el-radio :label="3" v-if="setType != 'batch'">定量分配</el-radio>
      <el-radio :label="4" v-if="setType == 'batch' && !isDoubleMode">按任教班级</el-radio>
    </el-radio-group>
    <div v-if="assignType != 3" class="set-assign-container">
      <span v-if="assignType == 1">
        每位老师阅卷任务为总上传试卷数取平均，若阅卷人已阅试卷量超过平均数，则无需再阅。
      </span>
      <span v-else-if="assignType == 5">
        每位阅卷老师不设上限，阅的越快的人阅的越多，尽快完成整个题组。
      </span>
      <span v-else-if="assignType == 4">
        每位阅卷老师批改自己所任教的班级。
      </span>
      <span v-else-if="assignType == 2">
        按实际参考人数平均分配任务至每位阅卷老师，老师阅完自己的任务后可继续帮助其他老师阅卷
      </span>
    </div>
    <template v-else>
      <div style="margin:10px 0px;">
        <el-radio-group v-model="assignRule" size="medium">
          <el-radio-button :label="2">按实考人数</el-radio-button>
          <el-radio-button :label="1">按计划人数</el-radio-button>
        </el-radio-group>
      </div>
      <div v-if="assignRule == 2" style="color:red;">*若后续有新的试卷扫描，将默认按定量比例分配至每位阅卷老师</div>
      <div class="set-assign-container">
      <span v-if="assignRule == 2">
        按实际参考人数自定义分配每位老师的阅卷任务。
      </span>
      <span v-else-if="assignRule == 1">
        按计划参考人数自定义分配每位老师的阅卷任务。
      </span>
    </div>
      <div class="assign-rule-container">
        <div class="assign-rule-count">
          <span>待分配任务量：<span class="red-tip">{{ taskCout }}</span></span>
          <span class="blue-tip" @click="equalDivid">平均分配</span>
        </div>
        <div>
          <el-table :data="teacherList" :key="isUpdateKey" style="width: 100%">
            <el-table-column prop="teaName" label="阅卷人">
            </el-table-column>
            <el-table-column prop="taskCount" label="任务量" min-width="150px;">
              <template slot-scope="scope">
                <el-input-number :min="scope.row.readTaskCount" :step-strictly="true" :step="1" size="mini"
                  :max="maxCount" v-model="scope.row.taskCount"></el-input-number>
              </template>
            </el-table-column>
            <el-table-column prop="readTaskCount" label="已阅">
            </el-table-column>
          </el-table>
        </div>
      </div>
    </template>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeModal">取 消</el-button>
      <el-button type="primary" @click="sureClick">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { IQUES_SCAN_MODE } from '@/typings/card';
export default {
  props: {
    modalVisible: {
      type: Boolean,
      required: false,
    },
    currentQues: {
      type: Object,
      required: false,
    },
    setType: {
      type: String,
      required: false,
    },
    quesList: {
      type: Array,
      required: false,
    },
  },
  data() {
    return {
      assignType: 1,
      assignRule: 2,
      teacherList: [],
      isUpdateKey: '',
    };
  },
  watch: {
    modalVisible(newVal, oldVal) {
      if (this.setType != 'batch') {
        this.assignType = (this.currentQues && this.currentQues.assignType) || 1;
        this.assignRule = (this.currentQues && this.currentQues.assignRule) || 2;
        this.teacherList = JSON.parse(JSON.stringify(this.currentQues.teacherList));
        this.teacherList.map((value) => {
          if (!value.taskCount) {
            this.$set(value, 'taskCount', 0)
          }
          if (!value.readTaskCount) {
            this.$set(value, 'readTaskCount', 0)
          }
          return value
        })
      } else {
        this.assignType = 1;
        this.assignRule = 1;
      }
    },
  },
  computed: {
    //是否有双评
    isDoubleMode() {
      return this.quesList.some(item => {
        return item.correctMode == 2 && item.scanMode == IQUES_SCAN_MODE.NORMAL;
      });
    },
    totalCount() {
      return this.assignRule == 1 ? this.currentQues.taskTotalCount : this.currentQues.actualTotalCount;
    },
    taskCout() {
      let use = this.teacherList.reduce((acc, cur) => {
        return acc + Number(cur.taskCount || 0)
      }, 0)
      return this.totalCount - use;
    },
    maxCount(){
      if(this.assignRule == 2){
        if(this.currentQues.correctMode == 2){
          return this.currentQues.actualTotalCount / 2
        }else{
          return this.currentQues.actualTotalCount
        }
      }else{
        return this.currentQues.stuTotalCount
      }
    }
  },
  methods: {
    //平均分配
    equalDivid() {
      let count = this.totalCount;
      let avg = Math.floor(count / this.teacherList.length);
      let remanet = count % this.teacherList.length;
      this.teacherList.forEach((tea, index) => {
        if (remanet >= (index + 1)) {
          this.$set(tea, 'taskCount', avg + 1)
        } else {
          this.$set(tea, 'taskCount', avg)
        }
      })
    },
    // 关闭弹窗
    handleClose(done) {
      this.$emit('close-set-assign');
      done();
    },
    closeModal() {
      this.$emit('close-set-assign');
    },
    sureClick() {
      if (this.assignType == 3 && this.teacherList.length == 0) {
        this.$message.error('请设置阅卷老师');
        return;
      }
      if (this.taskCout < 0) {
        this.$message.error('已分配任务量超过剩余任务量，请重新设置');
        return;
      }
      if (this.assignType == 3 && this.taskCout > 0) {
        this.$confirm('任务量未分配完成，是否继续保存？', '提示', {
          confirmButtonText: '保存',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.confirmAssignType();
          })
          .catch(() => {
            // 用户点击了取消，不做任何事情
          });
      } else {
        this.confirmAssignType();
      }
    },
    confirmAssignType() {
      this.$emit('confirm-set-assign', {assignType:this.assignType,assignRule:this.assignRule}, this.teacherList);
    },
  },
};
</script>
<style lang="scss" scoped>
.set-assign-container {
  margin: 10px 0px;
  line-height: 24px;
  background: #f0f2f5;
  border-radius: 10px;
  color: #606266;
  // text-align: center;
  font-weight: 400;
  font-size: 14px;
  padding: 10px 11px;
}

.assign-rule-container {
  margin-top: 10px;

  .assign-rule-count {
    display: flex;
    justify-content: space-between;

    .red-tip {
      color: red;
    }

    .blue-tip {
      color: #409EFF;
      cursor: pointer;
    }
  }
}
</style>
<style lang="scss">
.seled-assign-container {
  .el-dialog__header {
    height: 45px;

    .dialog-title {
      line-height: 45px;
    }
  }
}
</style>
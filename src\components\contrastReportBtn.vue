<!--
 * @Description: 添加对比考试按钮
 * @Author: 小圆
 * @Date: 2024-06-20 17:37:04
 * @LastEditors: 小圆
-->
<template>
  <div>
    <span
      >{{ label }}：<span v-if="examName" class="selectClass">{{ examName }}</span></span
    >
    <el-button v-if="!examId" type="text" class="select-btn" @click="openDialog">选择对比</el-button>
    <el-button v-if="examId" type="text" class="select-btn" @click="updateData({ examId: '', examName: '' })"
      >取消</el-button
    >

    <!--添加对比考试弹窗-->
    <contrast-report-dialog
      ref="contrastReport"
      v-if="isShowDialog"
      :contrastId="examId"
      @closeDialog="closeDialog"
      @updateData="updateData"
    ></contrast-report-dialog>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import ContrastReportDialog from '@/components/contrastReport.vue';

@Component({
  components: {
    ContrastReportDialog,
  },
})
export default class ContrastReportBtn extends Vue {
  // 标签
  @Prop({ default: '对比测评' }) label: string;
  // 对比考试名称
  @Prop({ default: '' }) examName: string;
  // 对比考试ID
  @Prop({ default: '' }) examId: string | number;

  // 是否显示对比考试对话框
  isShowDialog: boolean = false;

  // 打开对话框
  openDialog() {
    this.isShowDialog = true;
  }

  // 关闭对话框
  closeDialog() {
    this.isShowDialog = false;
  }

  // 更新对比考试数据
  updateData(item) {
    this.$emit('updateData', item);
  }
}
</script>

<style scoped lang="scss">
.selectClass {
  // color: #409eff;
  font-size: 16px;
  .el-icon-close {
    margin-left: 5px;
    cursor: pointer;
  }
}

.select-btn {
  margin-left: 20px;
  font-size: 16px;
}
</style>

<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-03-25 10:27:44
 * @LastEditors: 小圆
-->
<template>
  <div class="header-item">
    <span class="header-item__label">班级：</span>
    <el-select
      class="header-item__select"
      multiple
      collapse-tags
      value-key="id"
      :value="filterInfo.classInfos"
      @change="onChange"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <el-option
        v-for="item in selectOption.classList"
        :key="item.id"
        :title="item.class_name"
        :label="item.class_name"
        :value="item"
      >
      </el-option>
      <div style="padding: 5px">
        <el-button size="medium" @click="onSelectAll(true)">全选</el-button>
        <el-button size="medium" @click="onSelectAll(false)">全不选</el-button>
      </div>
    </el-select>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import ReportComponent from './ReportComponent';
import { IClassInfo } from '@/pages/studyReport/plugins/types';

@Component
export default class ReportClassInfosSelect extends ReportComponent {
  onChange(value: IClassInfo[]) {
    let arr = this.FilterModule.selectOption.classList.filter(item => value.includes(item)); // 顺序排序
    this.FilterModule.setClassInfos(arr);
  }

  onSelectAll(isAll: boolean) {
    if (isAll) {
      this.FilterModule.setClassInfos(this.selectOption.classList);
    } else {
      this.FilterModule.setClassInfos([]);
    }
  }
}
</script>

<style scoped lang="scss">
@import './reportSelect.scss';
</style>

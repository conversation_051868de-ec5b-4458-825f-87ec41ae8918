<template>
  <!--基本信息弹窗-->
  <el-dialog
    title="基本信息"
    custom-class="baseMessage"
    :visible.sync="showBaseDialog"
    :before-close="closeDialog"
    width="720"
  >
    <el-form ref="form" :model="form" label-width="120px">
      <el-form-item label="试卷名称：">
        <el-input v-model="form.name" maxlength="20" :disabled="forbidOperate"></el-input>
      </el-form-item>
      <el-form-item label="学 科：">
        <span class="blockSpan subject-block">{{ form.subjectName }}</span>
      </el-form-item>
      <el-form-item label="年 级：">
        <span
          class="blockSpan grade-block"
          v-for="item in gradeList"
          :key="item.value"
          @click="changeGrade(item)"
          :class="{
            active: form.gradeId == item.value,
            forbidOperate: form.gradeId == item.value && forbidOperate,
          }"
          >{{ item.label }}</span
        >
      </el-form-item>
      <el-form-item label="试卷类型：">
        <span
          class="blockSpan category-block"
          v-for="item in categoryList"
          :key="item.id"
          @click="changeCategory(item)"
          :class="{
            active: form.categoryId == item.id,
            forbidOperate: form.categoryId == item.id && forbidOperate,
          }"
          >{{ item.name }}</span
        >
      </el-form-item>
      <el-form-item label="年份：">
        <el-select
          v-model="form.year"
          class="year-select"
          @change="changeYear"
          placeholder="请选择"
          :disabled="forbidOperate"
        >
          <el-option v-for="item in yearList" :key="item" :label="item" :value="item"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="">
        <el-checkbox :disabled="forbidOperate" v-model="isShareChecked"
          >试卷同步至校本卷库</el-checkbox
        >
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer" v-if="!forbidOperate">
      <el-button @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="savePaper">确 认</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
import { getTBSearchInfo } from '@/service/testbank';
import { listGrdOrCls } from '@/service/pexam';
import UserRole from '@/utils/UserRole';

export default {
  name: 'form-dialog',
  // paramsData需要包括subjectId, gradeId, categoryId
  props: ['paramsData', 'forbidOperate', 'defaultName'],
  data() {
    return {
      showBaseDialog: true,
      form: {
        name: '',
        subjectId: '',
        subjectName: '',
        gradeId: '',
        gradeName: '',
        categoryId: '',
        categoryName: '',
        remark: '',
        year: '',
        shareType: '',
      },
      yearList: [],
      gradeList: [],
      selectSubject: {},
      categoryList: [],
      shareList: [
        {
          id: 0,
          name: '私有',
        },
        {
          id: 1,
          name: '公开',
        },
      ],
      tooltipContent: '',
      //是否分享到校本卷库
      isShareChecked: false,
    };
  },
  computed: {
    ...mapGetters(['subjectMap']),
  },
  mounted() {
    this.getBaseInfo();
  },
  methods: {
    closeDialog() {
      this.showBaseDialog = false;
      this.$emit('close');
    },
    // 切换年级value,label
    changeGrade(item) {
      if (this.forbidOperate) return;
      this.form.gradeId = item.value;
      this.form.gradeName = item.label;
    },
    // 切换学科 id, name
    changeCategory(item) {
      if (this.forbidOperate) return;
      this.form.categoryId = item.id;
      this.form.categoryName = item.name;
    },
    //切换是否公开
    changeSharetype(item) {
      if (this.forbidOperate) return;
      this.form.shareType = item.id;
    },
    // 切换年份
    changeYear() {
      if (this.forbidOperate) return;
      //  this.form.year = item
    },
    // 打开弹窗显示基本信息
    async getBaseInfo() {
      if (this.paramsData.tbId) {
        this.form.name = this.paramsData.name;
        this.form.remark = this.paramsData.remark;
      }
      this.isShareChecked = Number(this.paramsData.shapeType) == 1;
      // 获取学科名字
      if (this.paramsData.subjectId) {
        this.selectSubject = this.subjectMap[this.paramsData.subjectId];
        this.form.subjectId = this.selectSubject.id;
        this.form.subjectName = this.selectSubject.name;
      }
      const ret = await UserRole.getUserInfoPersonalityTest();
      // 获取年级
      let grad = [];
      let gradList = [];
      let phase = this.subjectMap[this.form.subjectId].phaseId
        ? Number(this.subjectMap[this.form.subjectId].phaseId) - 2 || ''
        : '';
      grad = ret.schGrdList.filter(item => item.phaseId - 2 == phase);
      // grad = this.$sessionSave.get("cascGrds").filter((item) => item.phaseId - 2 == phase);
      grad.forEach(ite => {
        gradList.push({
          value: ite.id,
          label: ite.name,
        });
      });
      this.gradeList = gradList;
      if (!this.gradeList || !this.gradeList.length) {
        await listGrdOrCls({
          schoolId: this.$sessionSave.get('schoolInfo').id,
          subjectId: this.paramsData.subjectId || '',
        }).then(res => {
          let data = res.data;
          if (!data || JSON.stringify(data) === '{}') {
            return;
          }
          let cascaderList = [];
          for (let i in data) {
            cascaderList.push({
              value: i,
              label: data[i].grade,
            });
          }
          this.gradeList = cascaderList;
        });
      }
      let grade = this.gradeList.filter(item => {
        return item.value == this.paramsData.gradeId;
      })[0];
      let curGradeList = grade ? grade : this.gradeList[0];
      this.form.gradeId = curGradeList.value;
      this.form.gradeName = curGradeList.label;

      // 获取题类
      this.readC30FilterCondition();

      let {
        year,
        gradeId = '',
        categoryId = '',
        fromName = '',
        shapeType = '',
      } = this.$route.query;
      if (fromName == 'previewDetail') {
        this.form.gradeId = gradeId;
        this.form.categoryId = Number(categoryId) || '301';
        this.form.year = year;
        this.isShareChecked = false;
        // this.form.shareType = Number(shapeType);
      }
    },

    // 从缓存读取C30筛选条件
    readC30FilterCondition() {
      let phase = this.subjectMap[this.form.subjectId].phaseId
        ? Number(this.subjectMap[this.form.subjectId].phaseId) - 2 || ''
        : '';
      let $this = this;
      getTBSearchInfo({ phase: phase }, function (data) {
        $this.yearList = (data && data.years) || [];
        if ($this.paramsData.year) {
          $this.form.year = $this.paramsData.year;
        } else if ($this.$route.query.year) {
          $this.form.year = $this.$route.query.year || '';
        } else if ($this.yearList.length) {
          $this.form.year = $this.yearList[0];
        }

        let c30_tb = data.sources;

        // 获取题类
        let otherIndex = -1,
          otherObj = {};
        c30_tb.forEach((item, index) => {
          if (item.name === '其他') {
            otherIndex = index;
            otherObj = item;
          }
        });
        if (otherIndex !== -1) {
          c30_tb.splice(otherIndex, 1);
          c30_tb.push(otherObj);
        }
        $this.categoryList = $this.$deepClone(c30_tb);
        let curList = $this.paramsData.categoryId
          ? $this.categoryList.find(item => item.id == $this.paramsData.categoryId)
          : $this.categoryList.find(item => item.id == '1');
        if (curList) {
          $this.form.categoryId = curList.id;
          $this.form.categoryName = curList.name;
        }
      });
    },
    // 基本信息弹窗点击确认
    savePaper() {
      this.form.name = this.form.name.trim();
      this.form.shareType = this.isShareChecked ? 1 : 0;
      // 保存基本信息
      if (!this.form.name) {
        this.$message.error('试卷名称不能为空！');
        return;
      }
      if (this.defaultName == this.form.name) {
        this.$confirm('试卷名称未修改是否继续保存?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          this.saveNext();
        });
      } else {
        this.saveNext();
      }
    },
    saveNext() {
      // this.form.year = new Date().getFullYear();
      this.showBaseDialog = false;
      this.$emit('saveMsg', this.form);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
<style lang="scss">
.baseMessage {
  font-family: Microsoft YaHei;
  font-size: 14px;
  .blockSpan {
    display: inline-block;
    min-width: 70px;
    height: 38px;
    padding: 0px 10px;
    border-radius: 4px;
    text-align: center;
    line-height: 38px;
    margin-right: 10px;
    cursor: pointer;
  }
  .subject-block {
    height: 38px;
    background: #fff;
    border: 1px solid #e4e7eb;
    color: #3f4a54;
    cursor: default;
  }
  .grade-block,
  .category-block,
  .save-block {
    color: #3f4a54;
    &.active {
      background: #409eff;
      color: #fff;
    }
    &.forbidOperate {
      cursor: default;
      background: #78b3ef;
    }
  }
}
</style>

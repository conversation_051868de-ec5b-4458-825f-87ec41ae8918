<template>
  <div class="right-yiqiQuesList-inner" v-loading="isLoadingQues">
    <div
      class="quesList-item display_flex flex-direction_column"
      v-for="item in expandQuesList"
      :key="item._id"
      v-loading="item.loadingDetails"
      ref="stemHtml"
    >
      <div
        class="queList-content over-hidden"
        @click="getQuesDetails(tokenInfo, item)"
      >
        <div
          id="yiqi-ques-content"
          v-html="item.data.html"
          v-show="!item.showDetails"
        ></div>
        <div
          :id="`yiqi-ques-details-${item._id}`"
          v-html="item.details"
          v-show="item.showDetails"
        ></div>
      </div>
      <div class="queList-footer">
        <div class="difficult-star pull-left display_flex align-items_center">
          难度:
          <span
            class="star"
            v-for="i in item.data.difficulty"
            :key="i + item._id"
          ></span>
        </div>
        <div class="difficult-star pull-left display_flex align-items_center">
          题型:
          <span style="margin-left: 8px">{{ item.typeName }}</span>
        </div>
        <template>
          <span
            class="edit-text detail"
            :class="{ active: item.showDetails }"
            @click="getQuesDetails(tokenInfo, item)"
            >详情</span
          >
          <span
            class="add edit-btn text-center"
            v-if="isShowEditBUtton"
            @click.stop="selectQues(item)"
            :class="[
              selectIds.length && selectIds.indexOf(item._id) >= 0
                ? 'hasAdd el-icon-minus'
                : 'el-icon-plus',
            ]"
          >
            {{
              selectIds.length && selectIds.indexOf(item._id) >= 0
                ? "移除"
                : " 选入"
            }}
          </span>
        </template>
        <!-- <span class="edit-text error-correction" @click="setDebunk(item)">纠错</span> -->
      </div>
    </div>
    <!-- 分页 -->
    <div v-if="expandQuesList.length != 0" class="ques-pagination">
      <el-button size="small" @click="pervious" style="margin-right: 30px"
        >上一页</el-button
      >
      <el-pagination
        style="display: none"
        class="ques-pagination"
        background
        layout="total,prev, pager, next, jumper"
        :current-page="currentPage"
        @current-change="paginationChange"
        :hide-on-single-page="true"
        :page-size="10"
        :total="paginationTotal"
        ref="pageGroup"
      >
      </el-pagination>
      <el-button size="small" @click="next">下一页</el-button>
    </div>
    <div
      class="no-ques"
      v-if="expandQuesList.length == 0 && isLoadingQues == false"
    >
      <div class="bg"></div>
      <p>暂无相关题目，换其他条件试试吧~</p>
    </div>
    <div class="no-ques" v-if="isLoadingQues">
      <p>正在获取题目...</p>
    </div>
    <!--试卷袋-->
    <fixedPaper
      v-on="$listeners"
      ref="fixedPaper"
      class="fixed-box"
      v-if="$route.path.indexOf('externalQues') >= 0"
      @update="updateData"
    ></fixedPaper>
  </div>
</template>

<script>
import { setDebunk, getAllCauseError } from "@/service/ptask.js";
import { mapGetters } from "vuex";
import {
  getQuesByKnowLedge,
  getQuesDetails,
  getQueTypeListBySubId,
} from "@/service/yiqi";
import { saveOneQuestion } from "@/service/testbank";
import { getC30Token } from "@/service/ques";
import ds from "dom-serialize";
import fixedPaper from "@/components/fixedPaper";

export default {
  name: "SelectYiqiQuesCard",
  components: {
    fixedPaper,
  },
  props: [
    "currentStep",
    "tokenInfo",
    "needQuesNum",
    "subjectId",
    "isShowEditBUtton",
  ],
  data() {
    return {
      quesInfo: [],
      // 查看详情
      visibleDetailId: "",
      // 错因列表
      allCauseError: [],
      // 选错列表
      checkList: [],
      textarea: "",
      // 当前需要纠错的题目
      currentErrorQuesInfo: "",
      // 当前待选入的题目信息
      currentQuesInfo: "",
      // 纠错弹窗可见性
      debunkDialogVisible: false,
      //题目列表
      expandQuesList: [],
      // 是否正在加载题目
      isLoadingQues: false,
      // 分页总条数
      paginationTotal: 1,
      // 当前分页位置
      currentPage: 1,
      // 是否正在请求错因
      isSubmitError: false,
      // 知识点信息
      pointInfo: {},
      // 网络题筛选条件
      filterNetInfo: {},
    };
  },
  computed: {
    ...mapGetters(["selectedQuesIds", "selectedQuesList"]),

    selectIds() {
      let totalList = [],
        ids = [];
      this.quesInfo.length &&
        this.quesInfo.forEach((item) => {
          totalList = totalList.concat(item.data);
        });
      totalList.length &&
        totalList.forEach((item) => {
          if (item.data.length) {
            ids = ids.concat(
              item.data.map((sub) => {
                return sub.id;
              })
            );
          }
        });
      return ids;
    },
  },
  beforeDestroy() {
    this.$bus.$off("changeNetYiqFilter");
    this.$bus.$off("changeYiqPoint");
  },
  mounted() {
    this.onConditionChangeNet();
    this.$bus.$on("changeSubjectFilter", (value) => {
      this.filterNetInfo = {};
      this.pointInfo.knowledgeCode = "";
      this.getQuesByCondition(value);
    });
  },
  methods: {
    reload() {
      this.$nextTick(() => {
        this.$refs.fixedPaper.getPersonalTestBank();
      });
    },
    //选入题目前处理题目
    selectQues(item) {
      if (item.typeName == "单选题") {
        let topic = "";
        if (item.data.q_html) {
          item.data.q_html =
            item.data.q_html.replace(
              /<q id=\".*\".*>.*<div.*class=\"source\".*>(.*)<\/div>.*<\/q>/g,
              "$1"
            ) || "";
        } else if (item.data.desc_html) {
          item.data.desc_html =
            item.data.desc_html.replace(
              /<q id=\".*\".*>.*<div.*class=\"source\".*>(.*)<\/div>.*<\/q>/g,
              "$1"
            ) || "";
        }
        if (item.data.qs[0].desc_html) {
          topic =
            item.data.qs[0].desc_html.replace(
              /^.*<q.*?>(.*)<\/q.*?>\s*$/g,
              "$1"
            ) || "";
        } else if (item.qs[0].desc) {
          topic =
            item.data.qs[0].desc.replace(/^.*<q.*?>(.*)<\/q.*?>\s*$/g, "$1") ||
            "";
        }
        item.data.qs[0].desc_html = this.formatImg(
          this.formatFormula(topic) || ""
        );
        item.data.qs[0].ans = Array.isArray(item.data.qs[0].ans)
          ? item.data.qs[0].ans[0]
          : item.data.qs[0].ans;
        let option = item.data.qs[0].opts;
        item.data.qs[0].opts_htmls = option.map((it) => {
          return this.formatImg(this.formatFormula(it));
        });
      } else if (item.typeName == "双选题" || item.typeName == "多选题") {
        //多选题
        let topic = "";
        if (item.data.q_html) {
          item.data.q_html =
            item.data.q_html.replace(
              /<q id=\".*\".*>.*<div.*class=\"source\".*>(.*)<\/div>.*<\/q>/g,
              "$1"
            ) || "";
        } else if (item.data.desc_html) {
          item.data.desc_html =
            item.data.desc_html.replace(
              /<q id=\".*\".*>.*<div.*class=\"source\".*>(.*)<\/div>.*<\/q>/g,
              "$1"
            ) || "";
        }
        if (item.data.qs[0].desc_html) {
          topic =
            item.data.qs[0].desc_html.replace(
              /^.*<q.*?>(.*)<\/q.*?>\s*$/g,
              "$1"
            ) || "";
        } else if (item.data.qs[0].desc) {
          topic =
            item.data.qs[0].desc.replace(/^.*<q.*?>(.*)<\/q.*?>\s*$/g, "$1") ||
            "";
        }
        item.data.qs[0].desc_html = this.formatImg(
          this.formatFormula(topic) || ""
        );
        item.data.qs[0].ans = item.data.qs[0].ans.join(",");
        let option = item.data.qs[0].opts;
        item.data.qs[0].opts_htmls = option.map((it) => {
          return this.formatImg(this.formatFormula(it));
        });
      } else if (item.typeName == "判断题") {
        //判断题
        if (item.data.html) {
          item.data.q_html =
            item.data.html.replace(
              /<q id=\".*\".*>.*<div.*class=\"source\".*>(.*)<\/div>.*<\/q>/g,
              "$1"
            ) || "";
          item.data.qs[0].q_html = this.formatImg(
            this.formatFormula(item.data.q_html) || ""
          );
          item.data.desc_html = this.formatImg(
            this.formatFormula(item.data.q_html) || ""
          );
          item.data.q_html = this.formatImg(
            this.formatFormula(item.data.q_html) || ""
          );
        } else if (item.data.html) {
          item.data.desc_html =
            item.data.html.replace(
              /<q id=\".*\".*>.*<div.*class=\"source\".*>(.*)<\/div>.*<\/q>/g,
              "$1"
            ) || "";
          item.data.qs[0].q_html = this.formatImg(
            this.formatFormula(item.data.desc_html) || ""
          );
          item.data.desc_html = this.formatImg(
            this.formatFormula(item.data.desc_html) || ""
          );
          item.data.q_html = this.formatImg(
            this.formatFormula(item.data.desc_html) || ""
          );
        }
        item.data.qs[0].ans = Array.isArray(item.data.qs[0].ans)
          ? item.data.qs[0].ans[0]
          : item.data.qs[0].ans;
        let option = item.data.qs[0].opts;
        item.data.qs[0].opts_htmls = option.map((it) => {
          return this.formatImg(this.formatFormula(it));
        });
      } else if (item.typeName == "填空题") {
        //填空题
        let topic = "";
        if (item.data.q_html) {
          item.data.q_html =
            item.data.q_html.replace(
              /<q id=\".*\".*>.*<div.*class=\"source\".*>(.*)<\/div>.*<\/q>/g,
              "$1"
            ) || "";
        } else if (item.data.desc_html) {
          item.data.desc_html =
            item.data.desc_html.replace(
              /<q id=\".*\".*>.*<div.*class=\"source\".*>(.*)<\/div>.*<\/q>/g,
              "$1"
            ) || "";
        }
        if (item.data.qs[0].desc_html) {
          topic =
            item.data.qs[0].desc_html.replace(
              /^.*<q.*?>(.*)<\/q.*?>\s*$/g,
              "$1"
            ) || "";
        } else if (item.data.qs[0].desc) {
          topic =
            item.data.qs[0].desc.replace(/^.*<q.*?>(.*)<\/q.*?>\s*$/g, "$1") ||
            "";
        }
        item.data.qs[0].desc_html = this.formatImg(
          this.formatFormula(topic) || ""
        );
        let answer = [];
        if (Array.isArray(item.data.qs[0].ans)) {
          answer = item.data.qs[0].ans.map((answer) => {
            return this.formatImg(answer) || "";
          });
        } else {
          let str = this.formatImg(item.data.qs[0].ans) || "";
          answer.push(str);
        }
        let ans = answer.map((it) => {
          return this.formatFormula(it);
        });
        item.data.qs[0].ans = ans.join(";");
      } else {
        //解答题
        let topic = "";
        let sources = [];
        sources = item.data.html.match(/<div.+?class=\"source\">(.+?)<\/div>/);
        if (sources == null) {
          topic = item.data.html;
        } else {
          topic = item.data.html.replace(
            /<div.+?class=\"source\">(.+?)<\/div>/g,
            ""
          );
        }
        item.data.q_html = this.formatImg(this.formatFormula(topic) || "");
        if (item.data.stem == "") {
          item.data.stem = item.data.q_html;
        }
        item.data.desc_html = this.formatImg(
          this.formatFormula(item.data.stem) || ""
        );
        item.data.qs.forEach((ite) => {
          ite.opts_htmls = ite.opts;
          if (Array.isArray(ite.ans)) {
            ite.ans = ite.ans.map((answer) => {
              return (
                this.formatImg(
                  this.formatFormula(
                    answer.replace(/^.*<q.*?>(.*)<\/q.*?>\s*$/g, "$1") || ""
                  ) || ""
                ) || ""
              );
            });
          } else {
            ite.ans = this.formatImg(
              this.formatFormula(
                ite.ans.replace(/^.*<q.*?>(.*)<\/q.*?>\s*$/g, "$1") || ""
              ) || ""
            );
          }
        });
      }
      this.saveOneQuestion(item);
    },
    saveOneQuestion(item) {
      //是大题挂小题且为主观题levelcode为302,客观题levelcode301，不是大题挂小题levelcode为空
      item.data.bigOrSmall == 1
        ? item.data.subOrObj == 1
          ? (item.data.levelcode = "302")
          : (item.data.levelcode = "301")
        : (item.data.levelcode = "");
      let fullScore = 0;
      item.data.qs.forEach((ite) => {
        ite.type = item.data.type;
        fullScore += ite.score;
      });
      item.data.exams = [];
      item.score = fullScore;
      item.quesId = item._id;
      item.quesType = item.data.type;
      this.$refs.fixedPaper.selectQues(item);
      let qJson = [
        {
          data: item.data,
          qId: item.quesId,
          bigTitle: item.typeName,
          subject: this.subjectId,
          type: item.quesType,
        },
      ];
      //若题目为未选入状态，点击选入题目入库
      if (this.selectIds.length && this.selectIds.indexOf(item._id) >= 0) {
        saveOneQuestion({ qJson: JSON.stringify(qJson) });
      }
    },
    // 更新加入试卷的数据
    updateData(quesInfo) {
      this.quesInfo = quesInfo || this.quesInfo;
    },
    // 监听条件变化
    onConditionChangeNet() {
      this.$bus.$on("changeNetYiqFilter", (value) => {
        this.filterNetInfo = value;
        this.currentPage = 1;
        this.getQuesByCondition();
      });
      this.$bus.$on("changeYiqPoint", (value) => {
        this.currentPage = 1;
        this.pointInfo.knowledgeCode = value.code;
        this.getQuesByCondition();
      });
    },
    // 获取一起题库数据
    async getQuesByCondition(tokenInfo) {
      if (!tokenInfo) {
        tokenInfo = this.tokenInfo;
      }
      if (!tokenInfo) {
        const subInfo = this.$localSave.get("currentSubject");
        tokenInfo = await getC30Token({
          subjectId: subInfo.id || "",
        });
      }
      this.isLoadingQues = true;

      let {
        content = "",
        difficultyCode = "",
        provinceCode = "",
        xfSectionCode = "",
        examCode = "",
        sourceCode = "",
        yearCode = "",
      } = this.filterNetInfo;
      let knowledgeCode = this.pointInfo.knowledgeCode || "",
        currentPage = this.currentPage || 1;
      getQuesByKnowLedge({
        headers: {
          token: tokenInfo.token,
        },
        data: {
          userId: tokenInfo.id,
          tageId: knowledgeCode, // 知识点
          type: xfSectionCode, // 题型
          keyWord: content, // 题干内容
          difficulty: difficultyCode, // 难度
          provinces: provinceCode, // 省份
          sources: sourceCode, // 来源
          itemTag: examCode, // 题类
          years: yearCode, // 年份
          page: currentPage,
          limit: 10,
          open: 1,
        },
      })
        .then((result) => {
          // let res = JSON.parse(result);
          let res = result;
          let data = res.data;
          if (data && data.items && data.items.length) {
            data.items.forEach((it) => {
              it.showDetails = false;
              it.details = null;
              it.qs = [];
              it.typeName = "";
              it.loading = false;
              it.loadingDetails = false;
            });

            this.paginationTotal = data.size;
            this.expandQuesList = data.items;
            this.expandQuesList.forEach((item) => {
              if (item.data.difficulty == 0) {
                //若难度为0，设置难度默认值为1
                item.data.difficulty = 1;
              }
            });
            this.setQuesType();
            this.$nextTick(() => {
              this.katexUpdate();
              this.renderHtml(false);
            });
          } else {
            this.expandQuesList = [];
            this.paginationTotal = 0;
          }
          this.$bus.$emit("getQuesNums", this.paginationTotal);
        })
        .catch((err) => {
          console.log(err);
          this.expandQuesList = [];
          this.paginationTotal = 0;
          this.$bus.$emit("getQuesNums", this.paginationTotal);
        })
        .finally(() => {
          this.isLoadingQues = false;
        });
    },
    pervious() {
      this.$refs.pageGroup.prev();
      this.$emit("getParams");
    },
    next() {
      this.$refs.pageGroup.next();
      this.$emit("getParams");
    },
    /**
     * @name: 公式,语音渲染
     * @param isDetail 是否展示详情
     */
    renderHtml(isDetail) {
      this.$nextTick(() => {
        let dom = this.$refs.stemHtml;
        $(".auxiliary", dom).hide();
      });
    },
    // 设置题目的题型
    setQuesType() {
      let item_types =
        this.$parent.$refs.netFilter.item_types_with_subtype || [];
      if (item_types.length) {
        this.expandQuesList.forEach((it) => {
          let ques_types = item_types.filter(
            (item) => item.type == it.data.type
          );
          if (ques_types.length) {
            let sub_types = ques_types.filter(
              (item) => item.subtype == it.data.subtype
            );
            if (sub_types.length) {
              it.typeName = sub_types[0].name;
            }
          }
        });
      }
      this.getQueTypeListBySubId();
    },
    // 根据学科ID获取一起题库题型列表
    getQueTypeListBySubId() {
      getQueTypeListBySubId({
        headers: {
          token: this.tokenInfo.token || "",
        },
        data: {
          subId: this.subjectId,
          hwTypeCode: 101,
        },
      })
        .then((data) => {
          data.forEach((item) => {
            this.expandQuesList.forEach((ite) => {
              if (item.queTypeId == ite.data.type) {
                ite.typeName = item.dtTypeName;
                ite.data.type = item.dtTypeId;
                ite.data.bigOrSmall = item.bigOrSmall; //是否为大题挂小题
                ite.data.subOrObj = item.subOrObj; //主观或客观题
              }
            });
          });
        })
        .catch((err) => {});
    },
    // 查看详情
    getQuesDetails(tokenInfo, item) {
      if (item.showDetails) {
        item.showDetails = !item.showDetails;
        return;
      }
      if (item.details) {
        item.showDetails = true;
        return;
      }
      item.loadingDetails = true;
      getQuesDetails({
        headers: {
          token: tokenInfo.token,
        },
        data: {
          userId: tokenInfo.id,
          questionId: item._id,
        },
      })
        .then((result) => {
          // let res = JSON.parse(result);
          let res = result;
          let data = res.data && res.data.data;
          item.details = data.html;
          item.qs = data.qs;

          this.$nextTick(() => {
            this.insertTagDom(res);
            this.katexUpdate();
            item.showDetails = true;
            item.loadingDetails = false;
          });
        })
        .catch((err) => {
          console.log(err);
          item.loadingDetails = false;
        });
    },
    // 插入知识点
    insertTagDom(result) {
      let data = result.data;
      let container = document.querySelector(`#yiqi-ques-details-${data._id}`);
      let pointDomList = container.querySelectorAll(".auxiliary");
      let tags = [];
      tags = this.traversalTags(data.data.qs, tags);
      pointDomList.forEach((item, index) => {
        let fragment = document.createDocumentFragment();
        let dom = this.renderTag(tags[index]);
        if (dom) {
          fragment.appendChild(dom);
          item.insertBefore(fragment, item.childNodes[0]);
        }
      });
    },
    // 遍历tag知识点
    traversalTags(qs, tags) {
      qs.forEach((item) => {
        tags.push(item.tag_ids);
        if (item.qs.length) {
          tags = this.traversalTags(item.qs, tags);
        }
      });
      return tags;
    },
    /**
     * @name: 知识点拼接
     * @param tags 知识点列表
     * @return: 知识点 Node Fragment
     */
    renderTag(tags) {
      if (!Array.isArray(tags) || !tags.length) {
        return "";
      }
      let str = `<div class='q_tags'>`;
      str += `<div class='dt long' >知识点：</div>`;
      str += `<div  class='dd'>`;
      for (let i = 0; i < tags.length; i++) {
        if (!tags[i]) {
          continue;
        }
        str += `<span>${tags[i].name}；</span>`;
      }
      str += `</div></div>`;

      let frag = document.createRange().createContextualFragment(str);
      return frag;
    },
    // 分页变化
    paginationChange(e) {
      this.currentPage = e;
      this.getQuesByCondition();
    },

    convertLatex2image(all, input) {
      const MATH_CDN = process.env.VUE_APP_MATH_CDN;
      input = input.replace(/&amp;/g, "&");
      input = input.replace(/&nbsp;/g, " ");
      input = input.replace(/\\\\<br.?>/g, "\\\\");
      input = input.replace(/\\<br.?>/g, "");
      input = input.replace(/<br.?>/g, "");
      input = input.replace(/\\div/g, "÷");
      input = input.replace(/&lt;/g, "<");
      input = input.replace(/&gt;/g, ">");
      const res = MathJax.tex2svg(input);
      const svg = res.children[0];
      const str = ds(svg);
      return `<span alt="${input}" class="math-tex-svg">${str}</span>`;
    },

    // 获取html里的公共转换成图片显示
    convertYiqHtml(html) {
      if (!html) {
        return "";
      }
      const mathContentReg1 = /<span[^>]*>\\\(([\s\S]*?)\\\)<\/span>/g;
      const mathContentReg2 = /\\\(([\s\S]*?)\\\)/g;
      const mathContentReg3 = /\$([\s\S]*?)\$/g;

      html = html.replace(/<a[^>]*>(.*?)<\/a>/g, "$1");
      html = html.replace(
        /<span[^>]*class="MathJax_Preview"[^>]*><\/span>/g,
        ""
      );

      html = html.replace(mathContentReg1, this.convertLatex2image);
      html = html.replace(mathContentReg2, this.convertLatex2image);
      html = html.replace(mathContentReg3, this.convertLatex2image);

      return html;
    },
    // 删除一个反斜杠
    formatFormula(str) {
      if (!str) {
        return "";
      }
      str = str.replace(
        /\\left\(\s*\\\)\s*\\\(\\right\)\s*/g,
        "\\left(\\qquad\\right)"
      ); // 优化圆括号
      str = str.replace(/\\\(\s*\\\((.*?)\\\)\s*\\\)/g, "\\($1\\)"); // 匹配 \( \(   \) )\
      str = str.replace(
        /\\\(([\s\S]*?)\\\)/g,
        '<span class="math-tex">\\($1\\)</span>'
      ); // 匹配 \(( ))\
      str = str.replace(
        /\\\[{([\s\S]*?)}\\\]/g,
        '<span class="math-tex">\\($1\\)</span>'
      ); // 匹配 \[{ }]\
      str = str.replace(
        /\\\[([\s\S]*?)\\\]/g,
        '<span class="math-tex">\\($1\\)</span>'
      ); // 匹配 \[( )]\
      return this.convertYiqHtml(str);
    },
    // [[img]] -> <img src>
    formatImg(str) {
      return (
        str.replaceAll(
          /\[\[img\]\]([^\[]*)\[\[\/img\]\]/g,
          function (match, $1) {
            return `<img src="${JSON.parse($1).src}" width="${
              JSON.parse($1).width || ""
            }"/>`;
          }
        ) || ""
      );
    },
    // 关闭纠错弹窗
    handleCloseDebunkDialog() {
      this.checkList = [];
      this.textarea = "";
      this.currentErrorQuesInfo = null;
      this.debunkDialogVisible = false;
    },
    // 获取错因
    getAllCauseError() {
      if (this.allCauseError.length) {
        return;
      }
      getAllCauseError()
        .then((data) => {
          this.allCauseError = data;
        })
        .catch();
    },
    // 纠错选择
    handleCheckedErrorChange(val) {
      this.checkList = val;
    },
    // 提交纠错
    confirmError() {
      if (!this.textarea && this.checkList.length == 0) {
        this.$message({
          message: "请填写纠错信息！",
          type: "warning",
          offset: "90",
        });
      } else {
        this.isSubmitError = true;
        let info = this.$parent.queryInfo;
        setDebunk({
          personBookId: info.personBookId,
          questionId: this.currentErrorQuesInfo._id,
          type: 1,
          state: 0,
          causeErrorId: this.checkList.join(","),
          errorDescription: this.textarea,
        })
          .then((data) => {
            this.handleCloseDebunkDialog();
            this.$message({
              message: "纠错信息发送成功",
              type: "success",
              offset: "90",
            });
            this.isSubmitError = false;
          })
          .catch((err) => {
            this.isSubmitError = false;
            console.log(err);
          });
      }
    },
    // 设置错因
    setDebunk(item) {
      this.getAllCauseError();
      this.debunkDialogVisible = true;
      this.currentErrorQuesInfo = item;
    },
    mathJaxUpdate() {
      let success = false;
      if (MathJax && MathJax.startup) {
        try {
          MathJax.startup.getComponents();
          MathJax.typeset();
          success = true;
        } catch (e) {}
      }

      if (!success) {
        setTimeout(() => {
          this.mathJaxUpdate();
        }, 100);
      }
    },
    katexUpdate() {
      MathJax.typeset();
    },
  },
};
</script>

<style lang="scss" scoped>
.edit-text.detail {
  padding-left: 15px;
  position: relative;
  right: 100px;
}
.edit-btn {
  width: 80px;
  height: 32px;
  border-radius: 4px;
  padding: 0;
  line-height: 32px;
  color: #fff;
  cursor: pointer;
  &.add {
    background: #409effff;
    position: absolute;
    right: 12px;
    margin-top: 4px;
  }
  &.hasAdd {
    background-color: #fff;
    border: 1px solid #468fffff;
    color: #468fffff;
  }
}
.right-yiqiQuesList-inner {
  margin: 10px;
  position: relative;

  .ques-pagination {
    text-align: center;
    margin: 0 auto;
    margin: 30px 0;
  }

  .el-backtop {
    width: 25px;
    height: 20px;
    -webkit-box-shadow: none;
    box-shadow: none;
    background-color: transparent;
  }
}

.quesList-item {
  width: 100%;
  min-height: 122px;
  background: #fff;
  border: 1px solid #e8ebed;
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;

  &:hover {
    border: 1px solid #008dea;
  }

  .queList-content {
    padding: 10px;
    min-height: 80px;

    &:hover {
      cursor: pointer;
    }

    q ol {
      list-style: none;
    }

    .option-box {
      .row-four {
        display: flex;
        flex-wrap: wrap;

        .flex-four {
          width: 20%;
          margin: 10px;

          img {
            width: 100%;
          }
        }
      }

      .row-two {
        display: flex;
        flex-wrap: wrap;

        .flex-two {
          width: 42%;
          margin: 10px;
        }
      }

      .option-item {
        display: flex;

        .option-item-letter {
          margin-right: 4px;
        }
      }
    }
  }

  .ques-detail-box {
    background: #f8f9fc;
    padding: 10px;
    border-top: 1px solid #eeeeee;

    .point-box,
    .answer-box,
    .analysis-box {
      display: flex;
    }
  }

  .queList-footer {
    width: 100%;
    height: 40px;
    background: #f8f9fc;
    border-radius: 0 0 6px 6px;
    padding: 0 20px 0 0;
    line-height: 40px;
    font-size: 16px;
    font-weight: 400;
    color: #525766;

    .difficult-star {
      font-size: 14px;
      margin-left: 30px;

      .star {
        width: 16px;
        height: 16px;
        background: url("../../assets/star.png") center center/100% 100%
          no-repeat;
        margin-left: 5px;

        &:first-child {
          margin-left: 8px;
        }
      }
    }

    .el-button {
      min-width: 80px;
      height: 30px;
      background: #3a84f9;
      border-radius: 4px;
      margin-left: 14px;
      float: right;
      color: #fff;
      cursor: pointer;
      margin-top: 5px;
      padding: 0;
      font-size: 16px;
      font-weight: 400;

      &.remove {
        border: 1px solid #3a84f9;
        background: #fff;
        color: #3a84f9;
      }
    }

    .edit-text {
      margin-left: 18px;
      float: right;
      cursor: pointer;

      &.disclose,
      &.error-correction,
      &.detail {
        position: relative;

        &:before {
          content: "";
          position: absolute;
          top: 11px;
          left: 0;
          background-image: url("../../assets/analyzeQuesIcons.png");
        }
      }
      &.detail.active::before {
        transform: rotateZ(180deg);
        top: 14px;
      }

      &.disclose {
        padding-left: 15px;

        &:before {
          background-position: -34px -15px;
          width: 10px;
          height: 10px;
        }
      }

      &.error-correction {
        padding-left: 15px;

        &:before {
          background: url("../../assets/eraser.png") center center/100% 100%
            no-repeat;
          width: 16px;
          height: 16px;
          top: 12px;
          left: -2px;
        }
      }

      &.detail {
        padding-left: 15px;

        &:before {
          background-image: url("../../assets/analyzeQuesIcons.png");
          background-position: -31px 4px;
          width: 14px;
          height: 14px;
          left: -2px;
        }
      }

      &.similar {
        padding-left: 15px;
        position: relative;

        &:before {
          content: "";
          width: 16px;
          height: 16px;
          background: url("../../assets/icon/search.png") no-repeat;
          background-size: 100% 100%;
          position: absolute;
          left: -3px;
          top: 12px;
        }
      }
    }
  }
}

.no-ques {
  padding-top: 30px;
  font-size: 16px;
  text-align: center;

  .bg {
    width: 352px;
    height: 250px;
    margin: 0 auto;
    background: url("../../assets/no-res.png") no-repeat;
    background-size: 100% 100%;
  }
}

.edit-ques-dlg {
  position: fixed;
  top: 0;
  left: 0;
  background: rgb(0, 0, 0, 0.6);
  overflow-y: scroll;
  height: 100%;
  width: 100%;
  /* text-align: center; */
  z-index: 20;
  padding: 5vh 0;

  .edit-ques-mark {
    width: 100%;
    height: 100%;
  }

  .backtop_btn {
    width: 24px;
    height: 24px;

    .icon-toup {
      width: 24px;
      height: 24px;
      background: url(../../assets/icon/toup.png) no-repeat;
    }
  }

  .edit-ques-body {
    width: 80%;
    margin: auto;
    /* height: 80%; */
    background: white;
    border-radius: 2px;
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 20px;
  }

  .edit-ques-btns {
    text-align: center;

    .el-button {
      width: 100px;
    }
  }
}

.icon-toup {
  width: 32px;
  height: 32px;
  background: url(../../assets/icon/toup.png) no-repeat;
}
</style>

<style lang="scss">
.right-yiqiQuesList-inner {
  .el-loading-spinner {
    top: 80px;
  }

  .queList-content {
    overflow-x: auto;
    width: 1100px;
    q ol {
      list-style: none;
      margin: 20px 0 20px 0px;
    }
    .intermediate-q {
      margin-bottom: 0;
      margin-left: 25px;
    }
  }

  .el-dialog {
    overflow: hidden;
  }

  .el-dialog__body {
    overflow-y: auto;
    max-height: 550px;
  }

  .body {
    .quesList-item {
      width: 100%;
      min-height: 122px;
      background: #fff;
      border: 1px solid #e8ebed;
      border-radius: 8px;
      margin-bottom: 15px;
      overflow: hidden;

      &:hover {
        border: 1px solid #008dea;
      }
      .ques-detail-box {
        overflow-x: auto;
      }
      .queList-content {
        padding: 10px;
        min-height: 80px;
        overflow-x: auto;

        &:hover {
          cursor: pointer;
        }

        .option-box {
          .row-four {
            display: flex;
            flex-wrap: wrap;

            .flex-four {
              width: 20%;
              margin: 10px;

              img {
                width: 100%;
              }
            }
          }

          .row-two {
            display: flex;
            flex-wrap: wrap;

            .flex-two {
              width: 42%;
              margin: 10px;
            }
          }

          .option-item {
            display: flex;

            .option-item-letter {
              margin-right: 4px;
            }
          }
        }
      }

      .ques-detail-box {
        background: #f8f9fc;
        padding: 10px;
        border-top: 1px solid #eeeeee;

        .point-box,
        .answer-box,
        .analysis-box {
          display: flex;
        }
      }

      .queList-footer {
        width: 100%;
        height: 40px;
        background: #f8f9fc;
        border-radius: 0 0 6px 6px;
        padding: 0 20px 0 0;
        line-height: 40px;
        font-size: 16px;
        font-weight: 400;
        color: #525766;

        .difficult-star {
          font-size: 14px;
          margin-left: 30px;

          .star {
            width: 16px;
            height: 16px;
            background: url("../../assets/star.png") center center/100% 100%
              no-repeat;
            margin-left: 5px;

            &:first-child {
              margin-left: 8px;
            }
          }
        }

        .el-button {
          width: 80px;
          height: 30px;
          background: #3a84f9;
          border-radius: 4px;
          margin-left: 14px;
          float: right;
          color: #fff;
          cursor: pointer;
          margin-top: 5px;
          padding: 0;
          font-size: 16px;
          font-weight: 400;

          &.remove {
            border: 1px solid #3a84f9;
            background: #fff;
            color: #3a84f9;
          }
        }

        .edit-text {
          margin-left: 18px;
          float: right;
          cursor: pointer;

          &.disclose,
          &.error-correction,
          &.detail {
            position: relative;

            &:before {
              content: "";
              position: absolute;
              top: 11px;
              left: 0;
              background-image: url("../../assets/analyzeQuesIcons.png");
            }
          }

          &.disclose {
            padding-left: 15px;

            &:before {
              background-position: -34px -15px;
              width: 10px;
              height: 10px;
            }
          }

          &.error-correction {
            padding-left: 15px;

            &:before {
              background: url("../../assets/eraser.png") center center/100% 100%
                no-repeat;
              width: 16px;
              height: 16px;
              top: 12px;
              left: -2px;
            }
          }

          &.detail {
            padding-left: 15px;

            &:before {
              background-image: url("../../assets/analyzeQuesIcons.png");
              background-position: -31px 4px;
              width: 14px;
              height: 14px;
              left: -2px;
            }
          }

          &.similar {
            padding-left: 15px;
            position: relative;

            &:before {
              content: "";
              width: 16px;
              height: 16px;
              background: url("../../assets/icon/search.png") no-repeat;
              background-size: 100% 100%;
              position: absolute;
              left: -3px;
              top: 12px;
            }
          }
        }
      }
    }
  }

  .error-item {
    margin-bottom: 10px;
    display: flex;
    align-items: center;

    > span {
      width: 65px;
      margin-right: 10px;
    }

    label.el-checkbox {
      margin-right: 10px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }
}
</style>

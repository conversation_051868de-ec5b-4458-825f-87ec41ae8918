<template>
    <div class="reportDetail">
        <div class="reportDetail__main display_flex align-items_flex-start">
            <ul class="list-none left-menu" ref="menuRef">
                <li v-for="item in menuList"
                    :key="item.id"
                    @click="changeMenu(item)"
                    :class="{active: item.id===activeMenuIndex}">{{item.name}}
                </li>
            </ul>
            <router-view class="right-content flex_1"
                         :style="{minHeight: minHeight + 'px'}"
                         @updateTab="matchingRoutes"
                         ref="routerView">
            </router-view>
        </div>
    </div>
</template>

<script>
    export default {
        name   : 'exam-report-detail',
        data () {
            return {
                // 选中的菜单索引
                activeMenuIndex: 0,
                // 菜单列表项
                menuList       : [
                    {
                        name: '题库',
                        path: 'examOverview',
                        id  : 0
                    },
                    {
                        name: '试卷',
                        path: 'reportCard',
                        id  : 1
                    }

                ],
                // 右侧内容区定位参数
                contentPos     : {
                    left : 0,
                    width: 0
                },
                minHeight: 0
            };
        },
        watch  : {
            $route (to, from) {
                this.matchingRoutes();
            }
        },
        created () {
            this.matchingRoutes();
        },
        mounted () {
            this.$nextTick(() => {
                // 定位右边内容区的位置
                let menuRect = this.$refs.menuRef.getBoundingClientRect();
                this.contentPos = {
                    left : menuRect.left + menuRect.width + 20,
                    width: document.body.clientWidth - menuRect.left * 2 - menuRect.width - 20
                };

                // this.minHeight = document.body.clientHeight - 160;
            });
        },
        activated () {
        },
        methods: {
            matchingRoutes (data) {
                let localPath = data || this.$route.path;
                for (let item of this.menuList) {
                    if (localPath.indexOf(item.path) !== -1) {
                        this.activeMenuIndex = item.id;
                        break;
                    }
                }
            },
            changeMenu (item) {
                if (this.activeMenuIndex == item.id) {
                    return;
                }
                this.activeMenuIndex = item.id;
                this.$router.push({path: `/home/<USER>/${item.path}`});
            },
        }
    };
</script>

<style lang="scss" scoped>
    .reportDetail {

        font-family : Microsoft YaHei;
        .reportDetail__back {
            font-size     : 16px;
            font-weight   : bold;
            color         : #3f4a54;
            cursor        : pointer;
            margin-bottom : 20px;
        }
        .reportDetail__main {
            .left-menu {
                position      : -webkit-sticky;
                position      : sticky;
                top           : 0px;
                width         : 166px;
                height        : 290px;
                background    : #fff;
                box-shadow    : 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
                border-radius : 6px;
                margin-right  : 20px;
                li {
                    cursor      : pointer;
                    width       : 100%;
                    height      : 48px;
                    line-height : 48px;
                    font-size   : 16px;
                    color       : #3f4a54;
                    text-align  : center;
                    &.active {
                        background-color : #f7fbff;
                        color            : #409eff;
                        position         : relative;
                        &:before {
                            content       : '';
                            position      : absolute;
                            width         : 4px;
                            height        : 48px;
                            background    : #409eff;
                            border-radius : 2px;
                            left          : 0;
                            top           : 0;
                        }
                    }
                }
            }
            .right-content {
                height        : auto;
                background    : #fff;
                box-shadow    : 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
                border-radius : 6px;
                padding       : 10px 0 0 24px;
                min-height    : 290px;
            }

        }
    }
</style>

<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-03-18 15:39:50
 * @LastEditors: 小圆
-->
<template>
  <div class="study-report">
    <div class="study-report__title title-tag" v-if="menuTitle">
      <span>
        {{ menuTitle }}
      </span>
      <span v-if="currentTip">
        <el-popover placement="top-start" title="" width="500" trigger="hover">
          <div v-for="(item, index) in currentTip" style="line-height: 25px; font-size: 13px">
            <span>
              {{ item }}
            </span>
          </div>
          <i class="el-icon-question" slot="reference"></i>
        </el-popover>
      </span>
    </div>
    <router-view v-if="FilterModule.isInit" class="study-report__main"></router-view>
  </div>
</template>

<script lang="ts">
import FilterModule from '@/pages/studyReport/plugins/FilterModule';
import { Component, Vue, Watch } from 'vue-property-decorator';
import ReportHeader from '../components/ReportHeader.vue';

@Component({
  components: {
    ReportHeader,
  },
})
export default class TeacherReport extends Vue {
  // 监听筛选单例
  FilterModule: typeof FilterModule = FilterModule;

  get menuTitle() {
    return this.$route.meta.title;
  }

  tip: any = {
    allSubject: ['1、标准分区：T=10Z+50（以 50 为平均数），Z =（学生分数-全体平均分）/全体标准差'],
    rankDistribution: [
      '1、“累计有效数”是指总分（累计）和学科（累计）都在指定范围内的累计数。',
      '2、配合度=累计有效数/总分累计数',
    ],
    classDisparity: ['1、相对平均：学生个人得分-班级均分', '2、相对最高：学生个人得分-班级最高分'],
    schoolDisparity: ['1、相对平均：学生个人得分-校级均分', '2、相对最高：学生个人得分-校级最高分'],
    singleSubjectComprehensiveScore: [
      '1、前X名贡献人数：年级前X名各班人数',
      '2、贡献率：各班贡献人数/年级前X名人数*100%',
    ],
    gradeSingleSubjectComprehensiveScore: [
      '1、高分人数：学优生',
      '2、高分率：学优生人数/实考人数*100%',
    ],
    subjectContributionIndex: [
      '贡献指数：',
      '班级学科贡献指数(平均分)=班级学科标准分(平均分)/班级总分标准分(平均分)*100;',
      '班级学科贡献指数(平均分)用来衡量班级学科对班级总分的贡献程度,指数越大,贡献越大,临界值为100;',
    ],
  };

  currentTip = null;

  @Watch('$route.name')
  onRouteChange(val: string) {
    this.currentTip = this.tip[val];
  }

  mounted() {
    this.currentTip = this.tip[this.$route.name];
  }
}
</script>

<style scoped lang="scss">
@import '../index.scss';
</style>

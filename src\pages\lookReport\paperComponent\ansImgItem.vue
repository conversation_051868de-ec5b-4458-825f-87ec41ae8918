<!--
 * @Descripttion: 
 * @Author: 小圆
 * @Date: 2023-12-15 17:31:00
 * @LastEditors: Please set LastEditors
-->
<template>
  <div class="item-answer">
    <div class="item-title-wrap">
      <span class="item-title"
        :title="showAnonymousName ? ansItem.anonymous : ansItem.stuName">{{ showAnonymousName ? ansItem.anonymous : ansItem.stuName }}</span>
    </div>
    <img class="item-img" :src="getImgSrc(imgList[0])" alt="" @click="handleImgClick" />
  </div>
</template>

<script>
import { replaceALiUrl } from '@/utils/common';
import { mapState } from "vuex";

// 图片前缀
const fsUrl = 'https://fs.iclass30.com/';
export default {
  props: ['ansItem'],
  data() {
    return {
      imgList: [],
    };
  },
  mounted() {
    this.initUrl();
  },
  computed: {
    ...mapState(["showAnonymousName"]),
  },
  methods: {
    // 初始化图片
    initUrl() {
      let url = this.ansItem.url;
      if (typeof url == 'string') {
        this.imgList.push(url);
        return;
      }
      this.imgList = url;
    },

    // 获取图片
    getImgSrc(url) {
      return replaceALiUrl(url);
    },

    // 处理图片点击
    handleImgClick() {
      this.$emit('open-preview', [this.ansItem.stuId, this.imgList]);
    },
  },
};
</script>

<style lang="scss" scoped>
.item-answer {
  position: relative;
  float: left;
  margin-right: 10px;
  margin-bottom: 20px;
  width: 360px;
  height: 210px;
  background-color: #eef2fe;
  vertical-align: middle;
  text-align: center;
  cursor: pointer;
}

.item-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.item-title-wrap {
  top: 0;
  position: absolute;
  width: 120px;
  height: 44px;
  background-color: #000;
  border-top-right-radius: 22px;
  border-bottom-right-radius: 22px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: center;
  justify-content: center;
  opacity: 0.5;

  .item-title {
    text-align: center;
    color: #fff;
    line-height: 44px;
    font-size: 16px;
  }
}
</style>

<!--
 * @Descripttion: 
 * @Author: 小圆
 * @Date: 2023-12-22 13:57:01
 * @LastEditors: 小圆
-->
<template>
  <div class="setting">
    <bread-crumbs :title="'修改成绩'">
      <template slot="titleSlot"> </template>
    </bread-crumbs>
    <div class="setting-container" v-loading="isStuLoading">
      <div class="setting-header">
        <div class="setting-header__title">{{ examName }}</div>
        <div>
          <el-select
            v-model="currentSubjectId"
            class="query_item"
            placeholder="请选择学科"
            @change="handleChangeSubject"
          >
            <el-option v-for="item in getExamSubjectList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
          <el-input v-model="filterText" placeholder="请输入学生姓名或考号" class="query_item"></el-input>
          <el-button type="primary" @click="rePublishScore" :loading="isRePublishLoading">重新发布</el-button>
          <span class="tip">*本页面内容改动后，请点击重新发布</span>
        </div>
      </div>

      <div class="setting-main">
        <div class="setting-main--left">
          <el-tree
            class="stu-tree"
            ref="tree"
            :data="stuTree"
            :props="defaultProps"
            node-key="stuId"
            highlight-current
            :filter-node-method="filterNode"
            :default-expanded-keys="defaultExpandedKeys"
            @node-click="handleChangeStu"
          >
            <span slot-scope="{ node }" :title="node.label">
              {{ node.label }}
            </span>
          </el-tree>
        </div>
        <div class="setting-main--center answer-container">
          <div class="answer-img-container" v-if="stuTableData && stuTableData.length">
            <exam-img-list
              v-for="(stuimg, index) in stuTableData"
              :index="index"
              :key="index"
              :cardType="cardInfo?.cardType"
              :currentRow="stuimg"
              :currentSort="hoverQues.currentSort"
              :currentSortTitle="hoverQues.currentSortTitle"
              :currentQuesNo="hoverQues.currentQuesNo"
              :currentQuesId="hoverQues.currentQuesId"
              :currentQues="hoverQues.currentQues"
            ></exam-img-list>
          </div>

          <div v-else-if="!isStuLoading && paperImgList.length" class="answer-img-container">
            <el-image
              v-for="url in paperImgList"
              :key="url"
              :src="url"
              :preview-src-list="paperImgList"
              fit="fill"
            ></el-image>
          </div>

          <no-data v-else></no-data>
        </div>
        <div class="setting-main--right correct-record" v-show="questionList.length">
          <div class="correct-record__main">
            <div class="correct-record__score clearfix">
              <span class="fl">批改记录</span
              ><span class="fr"
                >总分：<span class="number">{{ totalScore }}</span>
              </span>
            </div>
            <el-radio-group
              v-if="currentSubjectIsAbPaper"
              class="correct-record__ab-card"
              v-model="abCardSheetType"
              :disabled="disabledAbCardSheetType"
              @change="handleAbCardSheetTypeChange"
            >
              <el-radio :label="0">A卷</el-radio>
              <el-radio :label="1">B卷</el-radio>
            </el-radio-group>
            <div class="correct-record__list">
              <div v-for="(item, i) in sortQuestionList" class="question" :key="i">
                <ObjQues
                  v-if="item.isObj"
                  class="ques-hover"
                  :item="item"
                  :index="i"
                  @mouseenter.native="handleMouseEnter($event, item)"
                  @mouseleave.native="handleMouseLeave($event, item)"
                ></ObjQues>
                <SubjectiveQues
                  v-else
                  class="ques-hover"
                  :item="item"
                  :index="i"
                  :doChoicetext="getDoChoiceQuesCountTextByQuesNo(item.quesNo)"
                  @setChooseType="setChooseType"
                  @mouseenter.native="handleMouseEnter($event, item)"
                  @mouseleave.native="handleMouseLeave($event, item)"
                >
                  <el-button
                    v-if="source == 3 || source == 4"
                    type="text"
                    class="fr el-icon-edit-outline"
                    @click="openRecord(item)"
                    >阅卷记录</el-button
                  >
                </SubjectiveQues>
              </div>
            </div>

            <div class="correct-record__footer">
              <el-button type="primary" class="save-btn" @click="saveCorrect" :loading="isSaveLoading">保存</el-button>
              <el-dropdown
                v-if="source == 3 || source == 4"
                style="margin-left: 20px"
                size="medium"
                @command="key => setCurrentStuType(key)"
              >
                <el-button> 设置类型<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="1">批改完整</el-dropdown-item>
                  <el-dropdown-item command="3">批改不完整</el-dropdown-item>
                  <el-dropdown-item command="2">缺考</el-dropdown-item>
                  <el-dropdown-item command="0">未扫描</el-dropdown-item>
                  <el-dropdown-item command="4">0分学生</el-dropdown-item>
                  <el-dropdown-item command="5">不参与统计</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <!-- 兼容补录 -->
              <span v-else>
                <el-button
                  style="margin-left: 20px"
                  type="warning"
                  v-if="currentStu?.type == 4"
                  @click="setCurrentStuType(2)"
                  >设置缺考</el-button
                >
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <score-records-dialog
      v-if="isShowRecordDialog"
      :ques="currentQues"
      @closed="isShowRecordDialog = false"
    ></score-records-dialog>
  </div>
</template>

<script>
import BreadCrumbs from '@/components/Breadcrumbs/index.vue';
import { getExamSubjectMulti, getStuScanDataAPI } from '@/service/pexam';

import ObjQues from './objQues.vue';
import SubjectiveQues from './subjectiveQues.vue';

import NoData from '@/components/noData.vue';
import { getViewPaper } from '@/service/testbank';
import { GetScoreMap, getPaperImgMark } from '@/service/xueban';
import { getImgUrl } from '@/utils/common';
import ExamImgList from './examImgList.vue';

import { DefaultScoreSetting } from './plugin/DefaultScoreSetting';
import { SupplScoreSetting } from './plugin/SupplScoreSetting';
import ScoreRecordsDialog from './score-records-dialog.vue';
import DochoiceMixin from '@/pages/lookReport/mixin/DochoiceMixin.vue';
import UserRole from '@/utils/UserRole';

const defaultProps = {
  children: 'children',
  label: 'label',
};

// 判断Set是否相同
function setsAreEqual(set1, set2) {
  if (set1.size !== set2.size) {
    return false;
  }

  for (const item of set1) {
    if (!set2.has(item)) {
      return false;
    }
  }

  return true;
}

// 根据题目选项获取题目索引
function getOptionIndex(option) {
  const index = option.charCodeAt() - 65;
  return Math.pow(2, index);
}

export default {
  mixins: [DochoiceMixin],
  components: {
    BreadCrumbs,
    SubjectiveQues,
    ObjQues,
    ExamImgList,
    NoData,
    ScoreRecordsDialog,
  },
  data() {
    return {
      // 考试Id
      examId: this.$route.query.examId,
      // 考试名称
      examName: this.$route.query.examName,
      // 一起考试Id
      yqExamId: this.$route.query.yqExamId,
      // 查询参数
      filterText: '',
      // 考试学科列表
      examSubjectList: [],
      // 当前考试学科
      currentSubject: null,
      // 当前考试学科Id
      currentSubjectId: '',
      // 学生列表
      stuList: [],
      // 学生树型结构
      stuTree: [],
      // 当前选择学生ID
      currentStuId: '',
      // 当前选择学生对象
      currentStu: null,

      // 题目列表
      questionList: [],
      // 扫描图片数据
      stuTableData: [],
      // 答题卡图片列表
      paperImgList: [],

      // 是否正在加载学生
      isStuLoading: false,
      // 是否正在保存
      isSaveLoading: false,
      // 是否正在重新发布
      isRePublishLoading: false,

      // el-Tree默认配置选项
      defaultProps: defaultProps,
      // el-Tree默认展开
      defaultExpandedKeys: [],

      // 当前鼠标悬浮题目
      hoverQues: {
        // 当前题目序号
        currentSort: '',
        // 当前题目序号标题
        currentSortTitle: '',
        // 当前题目quesNo
        currentQuesNo: '',
        // 当前题目ID
        currentQuesId: '',
        // 当前题目
        currentQues: null,
      },
      // 题卡信息
      cardInfo: null,
      // 分数映射
      scoreMap: {},

      // 当前题目
      currentQues: null,
      // 是否显示记录弹窗
      isShowRecordDialog: false,
      // 修改成绩管理器
      scoreSettingManager: null,

      // AB卷AB卡类型 0、普通考试 1、AB卡(1卷2卡) 2、AB卷1卡  3、AB卷2卡
      relateCardType: 0,
      // AB卷类型 0: A卷; 1: B卷
      abCardSheetType: '',
      // 是否禁用AB卷切换
      disabledAbCardSheetType: true,

      // 答题卡信息缓存 testBankId -> 答题卡信息
      cardInfoMap: {},
      // 分数信息缓存 paperNo -> 分数信息
      paperNoScoreMap: {},
    };
  },

  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },

  computed: {
    // 考试学科列表
    getExamSubjectList() {
      return this.examSubjectList.filter((item, index, self) => index === self.findIndex(t => t.id === item.id));
    },

    // 当前学科是否AB卷
    currentSubjectIsAbPaper() {
      return this.examSubjectList.filter(item => item.id === this.currentSubjectId).length > 1;
    },

    // 总分
    totalScore() {
      if (!this.questionList || !this.questionList.length) return 0;
      let sumScore = 0;
      sumScore = this.questionList.reduce((prev, item) => {
        let score = 0;
        if (item.isObj) {
          if (!item.answer && item.type != 7) {
            score = 0;
          } else {
            score = this.scoreSettingManager.getObjScore(item);
          }
        } else {
          score = item.tempScore || item.tempScore === 0 ? item.tempScore : item.score;
        }
        if (item.tempChooseType === 0) score = 0;
        return prev + score;
      }, sumScore);
      return sumScore;
    },

    sortQuestionList() {
      if (!this.questionList || !this.questionList.length) return [];
      const newArray = [...this.questionList];
      return newArray.sort((a, b) => b.isObj - a.isObj);
    },

    // 来源 1: 补录; 3: 手阅; 4: 网阅，
    source() {
      if (this.currentSubject) {
        return this.currentSubject.source;
      } else {
        return this.$sessionSave.get('reportDetail').source;
      }
    },

    // 是否考试管理员
    isExamLeader() {
      let isOperation = UserRole.isOperation; // 运营
      let isSchoolAdmin = UserRole.isSchoolLeader; // 校管
      let reportDetail = this.$sessionSave.get('reportDetail');
      let isExamLeader = reportDetail.leaderIds?.includes(this.$sessionSave.get('loginInfo').id); // 考试管理员
      let isSameCreateUser = reportDetail.createUserId == this.$sessionSave.get('loginInfo').id; // 创建者
      return isOperation || isSchoolAdmin || isExamLeader || isSameCreateUser;
    },
  },

  async mounted() {
    this.getExamSubject();
  },

  methods: {
    // 获取当前学科参数 personBookId， workId， testBankId
    getSubjectParam(param, noAbPaper = false) {
      let reportDetail = this.$sessionSave.get('reportDetail');
      let paperList = reportDetail.paperList;

      const paper = paperList.find(item => {
        if (this.currentSubjectIsAbPaper && !noAbPaper) {
          return String(this.currentSubjectId) == item.subectId && item.abCardSheetType == this.abCardSheetType;
        } else {
          return String(this.currentSubjectId) == item.subectId;
        }
      });
      if (!paper) return '';
      return paper[param];
    },

    initSetting() {
      if (this.source == 3 || this.source == 4) {
        this.scoreSettingManager = new DefaultScoreSetting(); // 默认网手阅修改成绩管理
      } else {
        this.scoreSettingManager = new SupplScoreSetting(); // 兼容补录修改成绩管理
      }
    },

    // 处理鼠标移入题目
    handleMouseEnter(e, item) {
      this.hoverQues.currentSort = item.sort;
      this.hoverQues.currentSortTitle = item.sortTitle;
      this.hoverQues.currentQuesNo = item.quesNo;
      this.hoverQues.currentQuesId = item.questionId;
      this.hoverQues.currentQues = item;
    },

    // 处理鼠标移出题目
    handleMouseLeave(e, item) {
      this.hoverQues.currentSort = '';
      this.hoverQues.currentSortTitle = '';
      this.hoverQues.currentQuesNo = '';
      this.hoverQues.currentQuesId = '';
      this.hoverQues.currentQues = null;
    },

    // 查询tree树
    filterNode(value, data) {
      if (!value) return true;
      const labelMatch = data.label && data.label.indexOf(value) !== -1;
      const stuNoMatch = data.stuNo && data.stuNo.indexOf(value) !== -1;
      return labelMatch || stuNoMatch;
    },

    // 获取考试学科
    async getExamSubject() {
      try {
        const params = {
          examId: this.examId,
          isPublish: 1,
        };
        const res = await getExamSubjectMulti(params);
        let examSubjectList = res.data;
        if (!this.isExamLeader) {
          let reportDetail = this.$sessionSave.get('reportDetail');
          let paperList = reportDetail.paperList;
          examSubjectList = examSubjectList.filter(item => {
            return paperList.find(paper => {
              return paper.subectId == item.id && paper.leaderIds.includes(this.$sessionSave.get('loginInfo').id);
            });
          });
        }

        this.examSubjectList = examSubjectList;
        this.handleChangeSubject(this.getExamSubjectList[0].id);
      } catch (err) {
        console.error(err);
      }
    },

    // 获取分数映射
    async getScoreMap() {
      try {
        const paperNo = this.cardInfo.paperNo;
        if (this.paperNoScoreMap[paperNo]) {
          this.scoreMap = this.paperNoScoreMap[paperNo];
        } else {
          const res = await GetScoreMap({ paper_no: paperNo });
          if (res.code == 1) {
            this.scoreMap = res.data || {};
            this.paperNoScoreMap[paperNo] = this.scoreMap;
          }
        }
      } catch (error) {
        this.scoreMap = {};
      }
      this.scoreSettingManager.setScoreMap(this.scoreMap);
    },

    // 获取学生成绩分数列表
    async getStuList(isDefaultCheck) {
      this.stuList = [];
      this.stuTree = [];
      this.defaultExpandedKeys = [];
      this.isStuLoading = true;
      try {
        const { stuList, stuTree } = await this.scoreSettingManager.initStu({
          examId: this.examId,
          subjectId: this.currentSubjectId,
        });
        this.stuList = stuList;
        this.stuTree = stuTree;

        // 设置默认第一个学生选中
        if (isDefaultCheck && this.stuList.length) {
          for (let i = 0; i < this.stuTree.length; i++) {
            if (this.stuTree[i].children.length) {
              this.currentStuId = this.stuTree[i].children[0].children[0].stuId;
              break;
            }
          }
        }
        if (this.currentStuId) {
          let stuItem = this.stuList.find(item => item.stuId == this.currentStuId);
          await this.handleChangeStu(stuItem);
          await this.$nextTick();
          this.defaultExpandedKeys = [this.currentStuId];
          this.$refs.tree.setCurrentKey(this.currentStuId);
        }
      } catch (err) {
        console.error(err);
      } finally {
        this.isStuLoading = false;
      }
    },

    // 获取当前学生成绩信息
    async getStuScore() {
      this.isStuLoading = true;
      this.questionList = [];
      this.stuTableData = [];
      const params = {
        examId: this.examId,
        subjectId: this.currentSubjectId,
        studentId: this.currentStuId,
        abCardSheetType: this.abCardSheetType,
      };
      try {
        const questionList = await this.scoreSettingManager.initStuQuestion(params);
        questionList.forEach(item => {
          item.tempChooseType = item.chooseType;
          item.tempTargetId = item.targetId;
        });

        if (this.abCardSheetType === '' || this.abCardSheetType === undefined || this.abCardSheetType === null) {
          this.abCardSheetType = questionList[0]?.abCardSheetType;
        }
        await this.getViewPaper();
        await this.getScoreMap();
        this.questionList = questionList;
        await this.getStuScanData();
        this.getDisabledAbCardSheetType();
      } catch (err) {
        console.error(err);
      } finally {
        this.isStuLoading = false;
      }
    },

    // 获取学生扫描数据
    async getStuScanData() {
      const stu = this.stuList.find(item => this.currentStuId == item.stuId);
      this.stuTableData = [];
      if (!stu.stuNo) return;
      let params = {
        examId: this.getSubjectParam('workId', true),
        stuNo: stu.stuNo,
      };
      try {
        const res = await getStuScanDataAPI(params);
        let stuTableData = res.data;
        this.stuTableData = stuTableData;
      } catch (err) {
        console.error(err);
        return this.getViewPaper();
      }
    },

    // 没有扫描数据获取答题卡
    async getViewPaper() {
      let imgList = [];
      try {
        const testBankId = this.getSubjectParam('testBankId');
        if (this.cardInfoMap[testBankId]) {
          this.cardInfo = this.cardInfoMap[testBankId];
        } else {
          const res = await getViewPaper({ id: testBankId });
          this.cardInfo = res.data;
          if (testBankId && this.cardInfo) this.cardInfoMap[testBankId] = this.cardInfo;
        }
        imgList = await this.getViewPaperImgs(this.cardInfo.paperNo);
      } catch (err) {
        imgList = [];
        console.error(err);
      }
      this.paperImgList = imgList;
    },

    // 获取答题卡图片
    async getViewPaperImgs(paperNo) {
      const params = {
        paper_no: paperNo,
        show_answer: true,
      };
      let res = await getPaperImgMark(params);
      if (res.code == 1) {
        return res.pdf_images;
      }
      return [];
    },

    // 保存方法
    async saveCorrect() {
      try {
        this.isSaveLoading = true;
        const option = {
          questionList: this.questionList,
          cardInfo: this.cardInfo,
          examId: this.examId,
          subjectId: this.currentSubjectId,
          studentId: this.currentStuId,
          images: this.paperImgList,
          abCardSheetType: this.abCardSheetType,
        };

        await this.scoreSettingManager.saveCorrect(option);
        this.$message.success('保存成功');
        this.getStuList();
      } catch (err) {
        console.error(err);
      } finally {
        this.isSaveLoading = false;
      }
    },

    // 重新方法
    async rePublishScore() {
      this.isRePublishLoading = true;
      try {
        let subjectList = this.examSubjectList.filter(item => item.id == this.currentSubjectId);

        for (const item of subjectList) {
          await this.scoreSettingManager.rePublishScore({
            examId: this.examId,
            schoolId: this.$sessionSave.get('schoolInfo').id,
            personalBookId: item.personalBookId,
            source: item.source,
          });
        }

        this.$message.success('重新发布成绩成功');
        this.getStuList();
      } catch (err) {
        console.log(err);
      } finally {
        this.isRePublishLoading = false;
      }
    },

    // 设置当前学生类型
    async setCurrentStuType(state) {
      if (!this.currentStu) return;
      const stuId = this.currentStu.stuId;
      let params = {
        examId: this.examId,
        subjectId: this.currentSubjectId,
        studentId: stuId,
        state: state,
      };

      try {
        await this.scoreSettingManager.setStuType(params);
        this.getStuList();
      } catch (err) {
        console.error(err);
      }
    },

    // 更换学科
    async handleChangeSubject(val) {
      this.currentSubjectId = val;
      this.currentSubject = this.getExamSubjectList.find(item => item.id == val);
      this.currentStuId = '';
      this.currentStu = null;
      this.relateCardType = this.currentSubject.relateCardType;
      this.abCardSheetType = '';
      this.getPaperChoice(this.examId, this.currentSubjectId);
      this.initSetting();
      this.getStuList(true);
    },

    // 更换AB卷
    async handleAbCardSheetTypeChange(val) {
      this.abCardSheetType = val;
      await this.getViewPaper();
      this.getStuScore();
    },

    // 更换学生
    async handleChangeStu(data) {
      if (!data.stuId) return;
      this.abCardSheetType = '';
      this.currentStuId = data.stuId;
      this.currentStu = data;
      await this.getStuScore();
    },

    // 是否禁用AB卷切换
    async getDisabledAbCardSheetType() {
      let disabled = true;

      // 0、普通考试
      if (this.relateCardType == 0) {
        disabled = true;
      }

      // AB卡：
      // 已扫描不支持切换AB卡
      // 未扫描默认A卡版式，不支持切换
      if (this.relateCardType == 1) {
        disabled = true;
      }

      // AB卷(1张卡)：
      // 已扫描获取填涂结果，支持切换AB卷，切换后分值、客观题选项均不变
      // 未扫描默认A卷，支持切换
      if (this.relateCardType == 2) {
        disabled = false;
      }

      // AB卷(2张卡)：
      // 已扫描不支持切换AB卷
      // 未扫描默认A卷，支持切换
      if (this.relateCardType == 3) {
        disabled = this.stuTableData.length;
      }

      this.disabledAbCardSheetType = disabled;
    },

    // 打开阅卷记录
    openRecord(item) {
      this.currentQues = item;
      this.isShowRecordDialog = true;
    },

    // 设置选做题
    setChooseType(item, index) {
      let ques = this.questionList[index];
      if (ques.chooseType === '' || ques.chooseType === undefined || ques.chooseType === null) return;
      let quesId = item.questionId;
      let doQues = this.doQuesList.find(item => item.ids.includes(quesId));

      let quesArr = [];
      for (const ques of this.questionList) {
        if (doQues.ids.includes(ques.questionId)) {
          quesArr.push(ques);
        }
      }
      if (ques.tempChooseType === 0) {
        let count = 0;
        for (const ques of quesArr) {
          if (ques.tempChooseType == 1) {
            count++;
          }
          if (count >= doQues.doCount) {
            return this.$message.warning('超出选做范围');
          }
        }
      }
      this.$set(ques, 'tempChooseType', ques.tempChooseType === 1 ? 0 : 1);

      if (this.cardInfo?.cardType == 1) return;
      // 非题卡合一，手动设置targetId
      // 取消选做
      if (ques.tempChooseType == 0) {
        ques.tempTargetId = '';
      } else {
        //选中选做，根据剩余的targetIds分配
        let targetIds = doQues.targetIds;
        targetIds = targetIds.filter(id => {
          return !quesArr.some(item => item.tempTargetId == id);
        });
        ques.tempTargetId = targetIds[0];
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.fl {
  float: left;
}
.fr {
  float: right;
}
.setting {
  font-size: 14px;
}

.tip {
  color: red;
  margin-left: 10px;
}

.setting-container {
  padding: 15px;
  // border: 1px solid #ccc;

  .setting-header {
    background: #fff;
    padding: 15px;
    box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
    border-radius: 6px;
  }

  .setting-header__title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 10px;
  }

  .query_item {
    width: 210px;
    margin-right: 20px;
  }
}

.setting-main {
  display: flex;
  width: 100%;
  height: 600px;
  margin-top: 20px;

  $leftWidth: 220px; // 左盒子宽度
  $rightWidth: 340px; // 右盒子宽度
  $gap: 20px; // 间隙
  $radius: 6px; // 盒子圆角
  $boxShadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04); // 盒子阴影

  .setting-main--left {
    min-width: $leftWidth;
    width: $leftWidth;
    margin-right: $gap;
    height: 100%;
    overflow: hidden;
    overflow-y: auto;
    background: #fff;

    box-shadow: $boxShadow;
    border-radius: $radius;
  }

  .setting-main--right {
    min-width: $rightWidth;
    width: $rightWidth;
    height: 100%;
    margin-left: $gap;
    overflow: hidden;
    background: #fff;

    box-shadow: $boxShadow;
    border-radius: $radius;
  }

  .setting-main--center {
    flex: 1;
    min-width: 500px;
    height: 100%;
    overflow: hidden;

    box-shadow: $boxShadow;
    border-radius: $radius;
  }
}

.stu-tree {
  padding: 10px;
}

.correct-record {
  position: relative;
  // padding: 10px;

  .correct-record__main {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .correct-record__score {
    // position: absolute;
    // top: 0;
    width: 100%;
    height: 50px;
    line-height: 50px;
    padding-left: 15px;
    padding-right: 20px;
    color: #3b4a68;
    font-size: 16px;
    font-weight: 700;
    background-color: #fff;
    box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);

    .number {
      color: #101019;
    }
  }

  .correct-record__ab-card {
    display: flex;
    justify-content: center;

    padding-top: 15px;
    padding-bottom: 15px;
  }

  .correct-record__list {
    flex: 1;
    overflow: hidden;
    overflow-y: auto;
    padding: 10px;
  }

  .correct-record__footer {
    // position: absolute;
    // bottom: 0;
    width: 100%;
    height: 60px;
    line-height: 60px;
    text-align: center;
    background: #fff;
  }
}

.question {
  position: relative;
  width: 100%;
  min-height: 60px;
  margin-top: 10px;
  font-size: 14px;

  &:first-child {
    margin-top: 0;
  }

  .question__no-obj {
  }

  .question-input {
    width: 120px;
    height: 40px;
  }
}

.answer-container {
  .answer-img-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
    overflow-y: auto;
  }
}

.ques-hover {
  &:hover {
    background-color: #f5f5f5;
  }
}
</style>

<template v-on="$listeners">
  <div class="track-main flex_1 display_flex align-items_flex-start">
    <ul class="track-main-nav left-menu flex-shrink_0" ref="menuRef">
      <li
        v-for="item in menuList"
        :key="item.id"
        @click="changeMenu(item)"
        :class="{ active: item.id === activeMenuIndex }"
      >
        {{ item.name }}
      </li>
    </ul>
    <div class="track-main-right">
      <div class="track-container" ref="score">
        <div class="title">分数分布</div>
        <!-- 柱状图 -->
        <score-chart
          ref="scoreRankChart"
          checktype="avg"
          :tableData="scoreScatter.clzList"
          :targetData="scoreScatter.exams"
        ></score-chart>
        <!-- 表格 -->
        <score-table
          checktype="avg"
          :tableData="scoreScatter.clzList"
          :targetData="scoreScatter.exams"
        >
        </score-table>
      </div>
      <div class="track-container" ref="deviation">
        <div class="title">均差分布</div>
        <!-- 柱状图 -->
        <score-chart
          ref="scoreRankChart"
          checktype="avgDif"
          :tableData="avgDifData"
          :targetData="scoreScatter.exams"
        ></score-chart>
        <!-- 表格 -->
        <score-table checktype="avgDif" :tableData="avgDifData" :targetData="scoreScatter.exams">
        </score-table>
      </div>

      <five-rate class="track-container" ref="fiverate"></five-rate>
      <div class="track-container" ref="subject" v-if="filterInfo.subjectIdTrack == ''">
        <div class="title">学科分布</div>
        <!-- 柱状图 -->
        <score-chart
          ref="scoreRankChart"
          checktype="subject"
          :tableData="scoreScatter.subjectList"
          :targetData="scoreScatter.subjects"
        ></score-chart>
        <!-- 表格 -->
        <score-table
          checktype="subject"
          :tableData="scoreScatter.subjectList"
          :targetData="scoreScatter.subjects"
        >
        </score-table>
      </div>
      <div class="track-container" ref="diff" v-if="filterInfo.subjectIdTrack == ''">
        <div class="title">试卷难度</div>
        <!-- 雷达图 -->
        <score-piechart
          ref="scoreRankChart"
          checktype="diff"
          :tableData="scoreScatter.subjectList"
          :targetData="scoreScatter.subjects"
        >
        </score-piechart>
        <!-- 表格 -->
        <score-table
          checktype="diff"
          :tableData="scoreScatter.subjectList"
          :targetData="scoreScatter.subjects"
        >
        </score-table>
      </div>
      <div class="track-container" ref="online" v-if="filterInfo.subjectIdTrack == ''">
        <div class="title" style="display: flex">
          上线分布
          <div>
            <el-radio-group v-model="selectedLine" size="large" style="margin-left: 25px">
              <el-radio-button v-for="line in 4" :label="'基线' + line" :key="line" />
            </el-radio-group>
          </div>
        </div>
        <!-- 表格 -->
        <score-table
          checktype="online"
          :tableData="onLineData"
          :targetData="[]"
          style="min-height: 500px"
        >
        </score-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getToken } from '@/service/auth';
import {
  getExamReportList,
  getHisFiveRateAPI,
  getOnlineHisList,
  getTrackHistory,
} from '@/service/pexam';
import UserRole from '@/utils/UserRole';
import { localSaveUnique } from '@/utils/index';
import ScoreChart from '../../components/scoreDistribution/scoreChart.vue';
import ScorePiechart from '../../components/scoreDistribution/scorePiechart.vue';
import ScoreTable from '../../components/scoreDistribution/scoreTable.vue';
import FiveRate from './trackComponents/FiveRate.vue';

export default {
  name: 'track',
  components: {
    ScoreChart,
    ScoreTable,
    ScorePiechart,
    FiveRate,
  },
  data() {
    return {
      onLineDataList: [],
      //选择上线分布设置线
      selectedLine: '基线1',
      //分数分布、
      scoreScatter: {},
      //均差分布数据
      avgDifData: [],
      //筛选数据
      filterInfo: {},
      //年级列表
      gradeList: [],
      //年级
      gradeValue: '',
      fiveRateHeader: [],
      fiveRateData: [],
      fsUrl: process.env.VUE_APP_KKLURL,
      activeMenuIndex: 0,
      tabList: [
        {
          name: '分数分布',
          refName: 'score',
          id: 0,
        },
        {
          name: '均差分布',
          refName: 'deviation',
          id: 1,
        },
        {
          name: '五率分布',
          refName: 'fiverate',
          id: 2,
        },
        {
          name: '学科分布',
          refName: 'subject',
          id: 3,
        },
        {
          name: '试卷难度',
          refName: 'diff',
          id: 4,
        },
        {
          name: '上线分布',
          refName: 'online',
          id: 5,
        },
      ],

      menuList: [
        {
          name: '分数分布',
          refName: 'score',
          id: 0,
        },
        {
          name: '均差分布',
          refName: 'deviation',
          id: 1,
        },
        {
          name: '五率分布',
          refName: 'fiverate',
          id: 2,
        },
        {
          name: '学科分布',
          refName: 'subject',
          id: 3,
        },
        {
          name: '试卷难度',
          refName: 'diff',
          id: 4,
        },
        {
          name: '上线分布',
          refName: 'online',
          id: 5,
        },
      ],
      //上线分析数据
      onLineData: [],
      userId: '',
    };
  },

  computed: {},
  watch: {
    selectedLine: function (newLine) {
      this.onLineData = this.onLineDataList.map(item => {
        return {
          clz_name: item.clz_name,
          hisLine: item.hisLine.filter(item => item.line == newLine[newLine.length - 1] - 1),
        };
      });
    },
  },
  created() {
    this.$bus.$on('updateTrackData', () => {
      this.initData();
    });
  },
  beforeDestroy() {
    this.$bus.$off('updateTrackData');
    document.querySelector('.scrollContainer').removeEventListener('scroll', this.scrollHandler);
  },
  mounted() {
    this.initData();
    this.$nextTick(() => {
      this.intersectionObserverEntry();
    });
  },
  methods: {
    /**
     * @name:切换导航栏
     */
    changeMenu(tab) {
      let dom = this.$refs[tab.refName].$el ?? this.$refs[tab.refName];
      dom.scrollIntoView({
        behavior: window == window.parent ? 'smooth' : 'instant',
        block: 'start',
      });
    },

    // 设置监听模块进入可视区域
    intersectionObserverEntry() {
      document.querySelector('.scrollContainer').addEventListener('scroll', this.scrollHandler);

      // function sectionObserver(dom, id) {
      //   const observer = new IntersectionObserver(
      //     entries => {
      //       entries.forEach(entry => {
      //         if (entry.isIntersecting) {
      //           this.activeMenuIndex = id;
      //         }
      //       });
      //     },
      //     {
      //       rootMargin: '0px 0px 0px 0px',
      //       threshold: 0.7,
      //     }
      //   );
      //   observer.observe(dom);
      // }
      // for (const item of this.menuList) {
      //   let dom = this.$refs[item.refName].$el ?? this.$refs[item.refName];
      //   sectionObserver.call(this, dom, item.id);
      // }
    },

    // 处理容器滚动事件
    scrollHandler(e) {
      let root = e.target;
      let doms = [];

      for (const item of this.menuList) {
        let dom = this.$refs[item.refName].$el || this.$refs[item.refName] || null;
        if (dom) {
          doms.push({
            id: item.id,
            refName: item.refName,
            dom: dom,
          });
        }
      }
      if (root.scrollTop == 0) {
        this.activeMenuIndex = doms[0].id;
      } else if (root.scrollTop + root.clientHeight > root.scrollHeight - 1) {
        this.activeMenuIndex = doms[doms.length - 1].id;
      } else {
        for (const item of doms) {
          let dom = item.dom;
          let rect = dom.getBoundingClientRect();
          if (rect.top >= 0) {
            this.activeMenuIndex = item.id;
            break;
          }
        }
      }
    },

    /**
     * @name:初始化数据
     */
    async initData() {
      //获取上次选取的对比考试
      let compareExamIds = localSaveUnique.get('compareExamIds');
      if (compareExamIds && compareExamIds.length != 0) {
        this.getData(compareExamIds);
      } else {
        // 如果为空则获取最近两场考试
        this.gradeList = await UserRole.getGradeList({
          subjectId: this.subjectId,
        });
        this.gradeValue = this.gradeList[0].id;
        localSaveUnique.set('compareExamGrade', this.gradeValue);
        this.getExamList();
      }
    },

    // 获取数据
    async getData(ids) {
      this.menuList = this.getMenuList();
      this.getTrackData(ids);
      this.getOnlineData(ids);
      this.getHisFiveRate(ids);
    },

    getHisFiveRate(ids) {
      this.$bus.$emit('onGetData', {
        ids: ids || localSaveUnique.get('compareExamIds'),
        filterInfo: this.filterInfo,
      });
    },

    getMenuList() {
      const filterInfo = (this.filterInfo = this.$listeners.getParams());
      let menuList = [];
      if (filterInfo?.subjectIdTrack == '') {
        menuList = this.tabList;
      } else {
        let names = ['score', 'deviation', 'fiverate'];
        menuList = this.tabList.filter((item, index) => {
          return names.includes(item.refName);
        });
      }
      return menuList;
    },

    /**
     * @name:获取考试列表
     */
    async getExamList(type) {
      try {
        const res = await getExamReportList({
          schoolId: this.$sessionSave.get('schoolInfo').id,
          acadYearsId: '',
          acadTermId: '',
          gradeId: this.gradeValue || '',
          subjectId: '',
          categoryId: '',
          keyWord: '',
          page: 1, // 页码
          pageSize: 10,
          importScoreState: 1, // 导入成绩状态(-1:全部 0:未导入 1:已导入)
        });
        // 初始获取最近两场考试对比
        const list = res.data.list.splice(0, 2);
        let ids = list.map(item => {
          return item.examId;
        });
        localSaveUnique.set('compareExamIds', ids);
        // localSaveUnique.set('compareExamList', list);
        this.getData(ids);
      } catch (error) {
        console.error(error);
      }
    },
    /**
     * @name:获取学情追踪数据
     */
    async getTrackData(ids) {
      try {
        if(ids && ids.length == 0) {
          return;
        }
        const res = await getTrackHistory({
          examIds: ids.join(','),
          subjectId: this.filterInfo.subjectIdTrack || '',
        });
        this.scoreScatter = res.data;
        this.avgDifData = res.data.clzList.filter(item => {
          return item.clzName.indexOf('年级平均') != 0;
        });
      } catch (error) {
        console.error(error);
        this.scoreScatter = [];
        this.avgDifData = [];
      }
    },
    /**
     * @name:获取上线分布数据
     */
    async getOnlineData(ids) {
      try {
        if(ids && ids.length == 0) {
          return;
        }
        const res = await getOnlineHisList({
          examIds: ids.join(','),
        });
        this.onLineDataList = res.data;
        this.onLineData = res.data.map(item => {
          return {
            clz_name: item.clz_name,
            hisLine: item.hisLine.filter(
              item => item.line == this.selectedLine[this.selectedLine.length - 1] - 1
            ),
          };
        });
      } catch (error) {
        console.error(error);
        this.onLineDataList = [];
        this.onLineData = [];
      }
    },

    // async getHisFiveRate(ids) {
    //   try {
    //     const res = await getHisFiveRateAPI({
    //       examIds: ids.join(','),
    //       subjectId: this.filterInfo.subjectIdTrack || '',
    //     });
    //     if (res.code == 1) {
    //       let fiveRateData = [];
    //       const allExamList = removeDuplicates(res.data.map(item => item.examList).flat());
    //       this.fiveRateHeader = [
    //         { name: '优秀率', value: 'fine', list: allExamList },
    //         { name: '优良率', value: 'good', list: allExamList },
    //         { name: '及格率', value: 'pass', list: allExamList },
    //         { name: '不及格率', value: 'fail', list: allExamList },
    //         { name: '低分率', value: 'lower', list: allExamList },
    //       ];
    //       res.data.forEach(item => {
    //         let obj = {};
    //         obj.clzName = item.clzId == 'all' ? '年级' : item.clzName;
    //         let avgList = [];
    //         for (let i = 0; i < item.examList.length; i++) {
    //           let exam = item.examList[i];
    //           let rate = item.fiveRateList[i];
    //           if (!exam || !rate) continue;
    //           if (item.clzId == 'all') {
    //             avgList[exam.id] = rate.avgScore;
    //           } else {
    //             obj['tip'] = '1';
    //           }
    //           obj[exam.id + 'avgScore'] = rate.avgScore == '-1' ? '--' : rate.avgScore;
    //           obj[exam.id + 'passAvgRate'] = rate.passAvgRate == '-1' ? '--' : rate.passAvgRate;
    //           obj[exam.id + 'fail'] = rate.fail == '-1' ? '--' : rate.fail;
    //           obj[exam.id + 'fine'] = rate.fine == '-1' ? '--' : rate.fine;
    //           obj[exam.id + 'good'] = rate.good == '-1' ? '--' : rate.good;
    //           obj[exam.id + 'lower'] = rate.lower == '-1' ? '--' : rate.lower;
    //           obj[exam.id + 'pass'] = rate.pass == '-1' ? '--' : rate.pass;
    //         }
    //         fiveRateData.push(obj);
    //       });
    //       this.fiveRateData = fiveRateData;
    //     }
    //   } catch (error) {
    //     console.error(error);
    //     this.fiveRateHeader = [];
    //     this.fiveRateData = [];
    //   }
    // },
    /**
     * @name:导出
     */
    downloadReport() {
      let ids = localSaveUnique.get('compareExamIds');
      let token = getToken();
      let downloadUrl = `${this.fsUrl}/pexam/_/his/sheet?examIds=${ids.join(',')}&token=${token}`;
      window.open(`${downloadUrl}&response-content-type=application%2Foctet-stream`, '_self');
    },
  },
};
</script>

<style lang="scss" scoped>
.track-main {
  width: 100%;
}

.track-main-nav {
  padding: 0;
  list-style: none;
}

.track-main-right {
  width: 100%;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #f6f6f6;
  overflow-x: hidden;
  overflow-y: auto;
}

.left-menu {
  position: -webkit-sticky;
  position: sticky;
  flex-shrink: 0;
  top: 0px;
  width: 166px;
  // height: 195px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #f6f6f6;
  margin-right: 20px;

  li {
    cursor: pointer;
    width: 100%;
    height: 48px;
    line-height: 48px;
    font-size: 16px;
    color: #3f4a54;
    text-align: center;

    &.active {
      background-color: #f7fbff;
      color: #409eff;
      position: relative;

      &:before {
        content: '';
        position: absolute;
        width: 4px;
        height: 48px;
        background: #409eff;
        border-radius: 2px;
        left: 0;
        top: 0;
      }
    }
  }
}

.track-container {
  padding-bottom: 20px;

  .title {
    position: relative;

    display: flex;
    align-items: center;

    width: 100%;
    line-height: 38px;
    padding: 0 20px;
    padding-top: 10px;

    color: #3f4a54;
    font-size: 16px;
    font-weight: bold;

    background: #fff;

    // border-radius: 6px;
    &:before {
      content: '';
      display: block;
      width: 5px;
      height: 17px;
      background: #409eff;
      border-radius: 3px;
      margin-right: 8px;
    }

    .tips {
      margin-left: 5px;
      font-size: 14px;
      color: red;
      font-weight: normal;
    }
  }

  .export-button {
    position: absolute;
    right: 20px;
  }
}
</style>

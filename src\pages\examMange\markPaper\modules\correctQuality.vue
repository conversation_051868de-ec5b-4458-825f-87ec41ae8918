<!--
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2024-07-25 11:27:24
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-05-21 17:24:11
-->
<template>
    <div class="correct-quality-container">
        <!--阅卷质量操作栏-->
        <div class="correct-quality-handle">
            <span>题块：</span>
            <el-select v-model="currentQues" value-key="quesId" placeholder="请选择">
                <el-option v-for="item in quesList" :key="item.title" :label="item.title" :value="item">
                </el-option>
            </el-select>
            <div class="info-right">
                <el-button type="text" @click="exportDetail">导出阅卷质量明细表</el-button>
            </div>
        </div>
        <!--阅卷质量直线图-->
        <div style="width: 100%;height: 400px;" id="line-chart"></div>
        <!--阅卷质量明细表-->
        <div class="correct-quality-table">
            <div style="font-size: 18px; margin: 10px 0; font-weight: bold;">阅卷质量明细表</div>
            <el-row class="table-header">
                <el-col :span="8" class="col-li">题块</el-col>
                <el-col :span="2" class="col-li"> 满分 </el-col>
                <el-col :span="2" class="col-li"> 总体均分 </el-col>
                <el-col :span="2" class="col-li"> 阅卷老师</el-col>
                <el-col :span="2" class="col-li"> 已阅量 </el-col>
                <el-col :span="2" class="col-li"> 平均分 </el-col>
                <el-col :span="2" class="col-li ques-col"> 最高分 </el-col>
                <el-col :span="2" class="col-li ques-col"> 最低分 </el-col>
                <el-col :span="2" class="col-li ques-col"> 操作 </el-col>
            </el-row>
            <!--内容-->
            <div class="content-height progress-height" v-if="qualityData">
                <!-- <template v-for="(item, qindex) in qualityData"> -->
                    <el-row class="table-content" :style="{
                        height: qualityData.teaList.length == 0 ? 45 + 'px' : qualityData.teaList.length * 45 + 'px',
                    }">
                        <el-col :span="8" class="col-li summary-col" :title="qualityData.title">
                            {{ qualityData.title?.length > 20 ? qualityData.title.substring(0, 20) + '...' : qualityData.title }}
                        </el-col>
                        <el-col :span="2" class="col-li">
                            {{ qualityData.totalScore }}
                        </el-col>
                        <el-col :span="2" class="col-li">
                            {{ qualityData.quesAverageScore }}
                        </el-col>
                        <el-col :span="12">
                            <template v-for="(subItem, subIndex) in qualityData.teaList">
                                <el-row style="width: 100%; height: 45px; line-height: 45px">
                                    <el-col :span="4" class="col-li">
                                        {{ subItem.userName }}
                                        <span v-if="subItem.teaType == 2">（仲裁）</span>
                                    </el-col>
                                    <el-col :span="4" class="col-li">
                                        <span>{{ subItem.completeStus }}</span>
                                    </el-col>
                                    <el-col :span="4" class="col-li" style="font-size: 16px">
                                        {{ subItem.completeStus ? subItem.averageScore : '-' }}</el-col>
                                    <el-col :span="4" class="col-li" style="font-size: 16px">
                                        {{ subItem.completeStus ? subItem.highestScore : '-' }}
                                    </el-col>
                                    <el-col :span="4" class="col-li" style="font-size: 16px">
                                        {{ subItem.completeStus ? subItem.lowestScore : '-' }}
                                    </el-col>
                                    <el-col :span="4" class="col-li">
                                    <el-button :disabled="!currentQues.operate" type="text" @click="spotCheck(subItem)">抽查</el-button>
                                    <el-button :disabled="!currentQues.operate" type="text" @click="resultLook(subItem)">结果</el-button>
                                    <el-button :disabled="!currentQues.operate" type="text" @click="resetCorrect(subItem)">重阅</el-button>
                                </el-col>
                                </el-row>
                            </template>
                        </el-col>
                       
                    </el-row>
                <!-- </template> -->
            </div>
            <div class="content-height" v-else>
                <div class="no-data">暂无数据</div>
            </div>
        </div>
        <correct-spot-check-log-dialog v-if="dialogVisible" :dialogVisible="dialogVisible" :quesList="quesList" :currentQues="currentQues" :workId="workId" :teaId="currentTeaId"
            @closeDialog="dialogVisible = false"></correct-spot-check-log-dialog>
    </div>
</template>

<script>
import {
    getExamMarkingQualityData,
    getExamMarkingQues,
    rejectExamCorrect
} from '@/service/api';
import correctSpotCheckLogDialog from './correctSpotCheckLogDialog.vue';
export default {
    name: 'correctQuality',
    props: {
        workId: ''
    },
    components: {
        correctSpotCheckLogDialog
    },
    watch: {
        currentQues: {
            async handler(newVal, oldVal) {
                if (newVal) {
                    await this.getExamMarkingQualityData();
                    const data = this.buildChartData();
                    this.initChart(data);
                    this.$sessionSave.set('speedlist_correct_quesid_'+this.workId, newVal);
                }
            },
            deep: true
        }
    },
    data() {
        return {
            currentQues: null,
            currentTeaId: '',
            qualityData: null,
            quesList:[],
            dialogVisible: false,
            chartDom:null,
            myChart:null
        }
    },
    async created() {
        await this.getExamMarkingQuesList()
        this.currentQues = this.$sessionSave.get('speedlist_correct_quesid_'+this.workId) || this.quesList[0];
    },
    mounted() {
        this.chartDom = document.getElementById('line-chart');
        this.myChart = this.$echarts.init(this.chartDom);
    },
    destroyed() {
        // this.$sessionSave.remove('speedlist_correct_quesid');
    },
    methods: {

        async initChart(data) {   
            if (this.myChart) {
                this.myChart.dispose();
            }
            this.myChart = this.$echarts.init(this.chartDom);
            let option = {
                title: {
                    text: '阅卷质量曲线图',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function (params) {
                        // params 是一个数组，包含当前 hover 数据点的信息
                        var result = `<div style="margin: 10px 0 0;line-height:1;">分数区间${params[0].axisValue}</div>`;
                        for (var i = 0; i < params.length; i++) {
                            result += `<div style="margin: 10px 0 0;line-height:1;">${params[i].marker}
                                <span style="margin-left:5px;">${params[i].seriesName}:</span>
                                <span style="margin-left:20px;float: right;color: #666;font-weight: 900;">${params[i].data.value}%&nbsp;&nbsp;${params[i].data.num}人</span>
                                </div>`;
                        }
                        return result;
                    }
                },
                legend: {
                    data: data.map(item=>{return item.name}),
                    bottom: 10,
                    padding: 5
                },
                grid: {
                    left: '5%',
                    right: '5%',
                    //  bottom: '5%',
                    containLabel: true
                },
                graphic: [
                    {
                        type: 'text',
                        right: '5%',
                        top: 20,
                        style: {
                            text: `总体均分：${this.qualityData.quesAverageScore}分`,
                        }
                    }
                ],
                xAxis: {
                    type: 'category',
                    boundaryGap: true,
                    data: this.qualityData?.both,
                    name: "分数区间"
                },
                yAxis: {
                    type: 'value',
                    name: "占比",
                    axisLabel: {
                        formatter: '{value}%'
                    },
                },
                series: data
            };

            option && this.myChart.setOption(option);
        },
        async getExamMarkingQualityData() {
            let params = {
                schoolId: this.$sessionSave.get('schoolInfo').id,
                workId: this.workId,
                quesId:this.currentQues.quesId
            };
            let res = await getExamMarkingQualityData(params).catch(e => {
            });
            if (res.code == 1) {
                this.qualityData = res.data;
            }
        },
        buildChartData(){
            let data = [];
            this.qualityData.tboth.forEach((item)=>{
                let objs = [];
                item.nums.forEach((num,i)=>{
                    objs.push({
                        num:num,
                        value: Number(item.percents[i])
                    })
                })
                data.push({
                    name: item.name,
                    type: 'line',
                    data: objs
                })
            })
            return data;
        },
        async getExamMarkingQuesList(){
            let params = {
                schoolId: this.$sessionSave.get('schoolInfo').id,
                workId: this.workId,
                userId:this.$sessionSave.get('loginInfo').id
            };
            let res = await getExamMarkingQues(params).catch(e => {
            });
            if (res.code == 1) {
                this.quesList = res.data;
            }
        },
        spotCheck(subitem) {
            this.$router.push({
                name: 'correctSpotCheck',
                query: {
                    workId: this.workId,
                    quesId: this.currentQues.quesId,
                    quesName: this.currentQues.title,
                    correctId: subitem.userId,
                    correctName: subitem.userName,
                    teaType: subitem.teaType,
                },
            });
        },
        resultLook(item) {
            this.currentTeaId = item.userId;
            this.dialogVisible = true;
        },
        async resetCorrect(item) {
            await this.$confirm(`确定要将阅卷老师（${item.userName}）的阅卷任务打回吗？`, '提示');
            let params = {
                schoolId: this.$sessionSave.get('schoolInfo').id,
                workId: this.workId,
                quesId: this.currentQues.quesId,
                userId: item.userId,
                teaType: item.teaType
            };
            let res = await rejectExamCorrect(params)
            if(res.code == 1){
               this.$message.success('任务已打回！'); 
            }
        },
        exportDetail(){
            let url = process.env.VUE_APP_TESTBANK + `/homework/cloudexam/examMarkingQualityExport?workId=${this.workId}&schoolId=${this.$sessionSave.get('schoolInfo').id}`;
            window.open(url, 'blank');
        }
    }
}
</script>

<style lang="scss" scoped>
.correct-quality-container {
    .correct-quality-handle{
        .info-right{
            float: right;
        }
    }
    .correct-quality-table {
        .col-li {
            text-align: center;
            border-right: 1px solid #f3f3f3;
            // border-bottom: 1px solid #f3f3f3;
            height: 100%;
            vertical-align: middle;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .ques-col {
            flex-direction: column;
            justify-content: unset;

            >span {
                line-height: 25px;
            }
        }

        .summary-col:first-child {
            border-left: 1px solid #f3f3f3;
        }
        .table-content{
            border-bottom: 1px solid #f3f3f3;
            &:first-child {
                border-top: 1px solid #f3f3f3;
            }
        }

        .table-header {
            height: 50px;
            line-height: 50px;
            background: #f8f8f9;
            border-radius: 5px 5px 0 0;
        }

        .content-height {
            // height: 612px;
            margin-top: 1px;
            overflow-y: auto;
        }
    }
}
</style>
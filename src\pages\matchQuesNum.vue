<template>
  <div class="match-container" v-loading="isLoading">
    <!-- 顶部导航 -->
    <div class="match-header">
      <div class="header-back" @click="goBack">
        <i class="el-icon-arrow-left"></i>
        <span class="exam-name">{{ queryInfo.examName }}</span>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="match-main">
      <div class="match-card">
        <!-- 顶部统计 -->
        <div class="stat-header">
          <div class="stat-item left">
            <div class="stat-label">
              <i class="el-icon-notebook-2"></i>
              <span>试卷</span>
            </div>
            <div class="stat-count">{{ quesInfos.length }} 题</div>
          </div>
          <div class="stat-divider"></div>
          <div class="stat-item right">
            <div class="stat-label">
              <i class="el-icon-tickets"></i>
              <span>成绩</span>
            </div>
            <div class="stat-count">{{ quesNoList.filter(item => !item.isBig).length }} 题</div>
          </div>
        </div>

        <!-- 题目列表 -->
        <div class="questions-list">
          <template v-for="(ques, index) in quesInfos">
            <div class="question-item" :key="index">
              <!-- 题目内容 -->
              <div class="question-content" v-if="ques.content" style="width: 80%;" v-html="ques.content"></div>

              <div class="question-main">
                <div class="question-left">
                  <div class="question-no">{{ index + 1 }}</div>
                  <div class="question-text" v-html="ques.topic"></div>
                </div>

                <div class="question-right">
                  <el-select v-model="ques.statQuesNo" multiple placeholder="请选择题号" class="match-select"
                    value-key="tQuesNo">
                    <template #prefix>
                      <div class="selected-tags">
                        <el-tag v-for="item in getSelectedOptions(ques.statQuesNo)" :key="'tag_' + item.tQuesNo" closable
                          @close="removeTag(ques, item.tQuesNo)">
                          <el-tooltip v-if="item.bigTitle" :content="item.bigTitle" placement="top">
                            <span>{{ item.quesLabel }}</span>
                          </el-tooltip>
                          <span v-else>{{ item.quesLabel }}</span>
                        </el-tag>
                      </div>
                    </template>
                    <el-option v-for="item in quesNoList" :key="'option_' + item.bigTitle + item.tQuesNo"
                      :label="item.quesLabel" :value="item.tQuesNo" :disabled="item.isBig">
                      <el-tooltip v-if="item.bigTitle" class="item" effect="dark" :content="item.bigTitle"
                        placement="top">
                        <div style="min-width: 100px;">{{ item.quesLabel }}</div>
                      </el-tooltip>
                      <div v-else>{{ item.quesLabel }}</div>
                    </el-option>
                  </el-select>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="action-footer">
      <el-button class="cancel-btn" @click="cancelSave">取 消</el-button>
      <el-button type="primary" class="submit-btn" @click="save">完成匹配</el-button>
    </div>
    <el-backtop class="backtop" target=".scrollContainer" :right="26" :bottom="220">
      <img :src="topIcon" />
    </el-backtop>
  </div>
</template>

<script>
import { chooseQuesSearch } from '@/service/pbook';
import { getExamQuesNoList, listExamSubject } from "@/service/pexam";
import { chooseQuesInfo } from "@/service/pbook";
import {
  getQuestions,
  getPersonBookQues,
  bindStatQuesNo,
  getQueBindNo
} from "@/service/ptask";
export default {
  data() {
    return {
      queryInfo: {},
      //题目信息
      quesInfos: [],
      //题号列表
      quesNoList: [],
      //题目ids
      quesIds: [],
      isLoading: false,
        // 置顶图标
      topIcon: require('@/assets/icon_top.png')
    };
  },
  computed: {},
  mounted() {
    this.queryInfo = this.$route.query;
    this.getQuesList();
  },
  methods: {
    goBack() {
      this.$router.back();
    },
    /**
     * @name:获取题目列表
     */
    getQuesList() {
      this.isLoading = true;
      getPersonBookQues({
        personBookId: this.queryInfo.personBookId,
      }).then((res) => {
        this.quesIds = res.data.map((item) => {
          return item.qId;
        });
        this.getQuesInfo(this.quesIds);
      });
    },
    /**
     * @name:获取考试题号列表
     */
    getQuesNoList() {
      getExamQuesNoList({
        examId: this.queryInfo.examId,
        subjectId: this.queryInfo.subjectId,
        abPaper: this.queryInfo.abCardSheetType
      })
        .then((res) => {
          let bigTitle = '';
          res.data.forEach((res) => {
            if (res.isBig) {
              bigTitle = res.quesNoDesc;
            }
            res.bigTitle = bigTitle;
            this.$set(
              res,
              "quesLabel",
              res.quesNoDesc == "" ? res.tQuesNo : res.quesNoDesc
            );
          });
          this.quesNoList = res.data;
          //未匹配过题号获取默认对应题号
          if (this.queryInfo.bindQueState == 0) {
            const quesNoSet = new Set(
              this.quesNoList.map((item) => item.tQuesNo)
            );
            this.quesInfos.forEach((item) => {
              if (quesNoSet.has(item.sort)) {
                item.statQuesNo.push(item.sort);
              }
            });
          }
        })
        .catch((err) => {
          this.quesNoList = [];
        });
    },
    /**
     * @name:获取题目信息
     */
    getQuesInfo(ids) {
      chooseQuesSearch({
        qIds: ids.join(","),
      })
        .then((res) => {
          const dataMap = new Map(res.data.map(item => [item.id, item]));
          // 按照原始 ids 顺序重建数组
          const sortedData = ids.map(id => dataMap.get(id));

          let quesInfos = [];
          let quesIds = [];
          sortedData.forEach(item => {
            if (item.data.levelcode) {
              item.data.qs.forEach((ite, i) => {
                let obj = {
                  quesId: ite.qId,
                  topic: ite.topic,
                  sort: item.sortOrder,
                  statQuesNo: [],
                }
                if (i == 0) {
                  obj.content = item.topic
                }
                quesIds.push(ite.qId)
                quesInfos.push(obj)
              })
            } else {
              quesInfos.push({
                quesId: item.id,
                topic: item.topic,
                sort: item.sortOrder,
                statQuesNo: [],
              })
              quesIds.push(item.id)
            }
          });
          this.quesIds = quesIds;
          this.quesInfos = quesInfos;
          this.isLoading = false;
          this.getQuesNoList();
          //获取已经匹配的题号
          if (this.queryInfo.bindQueState == 1) this.getQueBindNo();
        })
        .catch((err) => {
          this.isLoading = false;
        });
    },
    /**
     * @name:获取已绑定的题号
     */
    getQueBindNo() {
      getQueBindNo({
        pBookId: this.queryInfo.personBookId,
        quesIds: this.quesIds.join(","),
      }).then((res) => {
        this.quesInfos.forEach((item) => {
          res.data.forEach((ite) => {
            if (item.quesId == ite.queId) {
              item.statQuesNo = ite.queNoList;
            }
          });
        });
      });
    },
    /**
     * @name:取消保存
     */
    cancelSave() {
      this.goBack();
    },
    /**
     * @name:保存匹配
     */
    async save() {
      //题号列表
      let quesNos = this.quesNoList.filter(item => !item.isBig).map(item => item.tQuesNo);
      //匹配的题号
      let selectNos = this.quesInfos.flatMap((item) => {
        return item.statQuesNo;
      });
      let isAllEqual = quesNos.every((item) => {
        return selectNos.includes(item);
      });
      //如果存在没有设置匹配的题号
      if (!isAllEqual) {
        this.$message({
          message: "请保证所有题号均已匹配",
          type: "warning",
          duration: "2000",
        });
        return;
      }
      let jsonList = this.quesInfos.map((item) => ({
        quesId: item.quesId,
        sort: item.sort,
        statQuesNo: item.statQuesNo.join(","),
      }));
      await bindStatQuesNo({
        pBookId: this.queryInfo.personBookId,
        objList: JSON.stringify(jsonList),
      })
      // 刷新体面
      this.$bus.$emit('desktop-change-filter', { type: "subject", refresh: true })
      this.$message({
        message: "匹配成功！",
        type: "success",
        duration: "2000",
      });
    },

    // 获取选中的选项完整信息
    getSelectedOptions(selectedValues) {
      return selectedValues.map(value => {
        const option = this.quesNoList.find(item => item.tQuesNo === value);
        return option || { quesNo: value, quesLabel: value };
      });
    },
    // 移除标签
    removeTag(ques, value) {
      ques.statQuesNo = ques.statQuesNo.filter(v => v !== value);
    }
  },
};
</script>

<style lang="scss" scoped>
.match-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px;

  .match-header {
    margin-bottom: 24px;

    .header-back {
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        opacity: 0.8;
      }

      i {
        font-size: 20px;
        color: #1890ff;
        margin-right: 8px;
      }

      .exam-name {
        font-size: 18px;
        font-weight: 600;
        color: #1f2329;
      }
    }
  }

  .match-main {
    .match-card {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      padding: 24px;

      .stat-header {
        display: flex;
        align-items: center;
        padding: 16px 24px;
        background: #f8f9fc;
        border-radius: 6px;
        margin-bottom: 24px;

        .stat-item {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;

          &.left {
            flex: 5;
          }

          &.right {
            flex: 1;
          }

          .stat-label {
            display: flex;
            align-items: center;
            margin-right: 12px;

            i {
              font-size: 20px;
              color: #1890ff;
              margin-right: 8px;
            }

            span {
              font-size: 16px;
              font-weight: 500;
              color: #1f2329;
            }
          }

          .stat-count {
            font-size: 16px;
            color: #1890ff;
            font-weight: 600;
          }
        }

        .stat-divider {
          width: 1px;
          height: 24px;
          background: #e8e8e8;
          margin: 0 48px;
        }
      }

      .questions-list {
        .question-item {
          padding: 24px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .question-content {
            margin-bottom: 16px;
            color: #666;
          }

          .question-main {
            display: flex;
            align-items: flex-start;

            .question-left {
              flex: 1;
              margin-right: 24px;
              display: flex;
              align-items: flex-start;

              .question-no {
                min-width: 28px;
                height: 28px;
                line-height: 26px;
                text-align: center;
                border: 1px solid #1890ff;
                border-radius: 50%;
                color: #1890ff;
                font-weight: 500;
                margin-right: 12px;
                margin-top: 2px;
              }

              .question-text {
                max-width: 1100px;
                flex: 1;
                color: #1f2329;
              }
            }

            .question-right {
              width: 280px;

              .match-select {
                width: 100%;
              }
            }
          }
        }
      }
    }
  }

  .action-footer {
    position: sticky;
    bottom: 0;
    display: flex;
    justify-content: flex-end;
    padding: 16px 0;
    margin-top: 24px;
    background: #fff;
    box-shadow: 0 -2px 12px 0 rgba(0, 0, 0, 0.05);
    z-index: 9;

    .el-button {
      width: 120px;
      height: 40px;
      font-size: 14px;

      &:not(:last-child) {
        margin-right: 16px;
      }
    }

    .submit-btn {
      background: #1890ff;
      border-color: #1890ff;

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    }
  }
}
</style>

<style lang="scss">
.match-container {
  .el-select__tags {
    max-width: calc(100% - 30px);
  }

  .el-select__tags-text {
    display: inline-block;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .el-tag {
    margin-right: 4px;
    margin-top: 2px;

    .el-tag__close {
      top: -1px;
      right: -4px;
      color: #666;

      &:hover {
        background-color: #666;
        color: #fff;
      }
    }
  }

  .el-select-dropdown__item {
    padding: 0 12px;
    height: 36px;
    line-height: 36px;
  }

  .match-select {
    .el-select__tags {
      display: none; // 隐藏默认的标签显示
    }

    .selected-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      padding: 2px;

      .el-tag {
        margin-right: 4px;
        margin-bottom: 2px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .el-select-dropdown__item {
    padding: 0 12px;
    height: 36px;
    line-height: 36px;

    &.is-disabled {
      color: #999;
      cursor: not-allowed;
    }
  }
  .backtop {
    width: 60px;
    height: 60px;
  }
}
</style>
<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-04-08 09:50:39
 * @LastEditors: 小圆
-->
<template>
  <div class="target-result">
    <div class="target-result__tip">
      <!-- <span class="tip-text"> 提示：此处指标均按总分统计，各科根据总分统计规则，按比例自动生成。 </span> -->
      <el-button class="reset-btn" type="primary" @click="reset" :loading="resetLoading"> 恢复默认 </el-button>
    </div>

    <div class="target-result__content">
      <ScoreLineSetting ref="scoreLineSetting" class="target-result__setting" :examId="examId"></ScoreLineSetting>
    </div>

    <div class="target-result__footer">
      <el-button v-if="customExamId" type="primary" @click="$emit('updateActiveStep', activeStep - 1, customExamId)">
        上一步</el-button
      >
      <div style="flex: 1"></div>
      <el-button :disabled="getIsDisabled" type="primary" @click="save" :loading="saveLoading">
        {{ customExamId ? '下一步' : '保存设置' }}
      </el-button>
    </div>
  </div>
</template>

<script lang="ts">
import ScoreLineSetting from '@/pages/schoolSetting/components/ScoreLineSetting/index.vue';
import { SchoolSettingType } from '@/pages/schoolSetting/types';
import { restoreSchCfgAPI, setSchCfgListAPI } from '@/service/pexam';
import UserRole from '@/utils/UserRole';
import { Component, Ref, Vue } from 'vue-property-decorator';

@Component({
  components: {
    ScoreLineSetting,
  },
})
export default class TargetOnline extends Vue {
  @Ref('scoreLineSetting') scoreLineSetting: ScoreLineSetting;
  // 考试id
  examId = this.$sessionSave.get('reportDetail').examId;
  // 当前步骤
  activeStep = 3;
  // 自定义考试id
  customExamId = '';
  // 保存loading
  saveLoading = false;
  // 恢复默认loading
  resetLoading = false;

  // 是否禁用保存按钮
  get getIsDisabled() {
    if (this.customExamId) {
      return false;
    } else {
      return !(UserRole.isOperation || UserRole.isSchoolLeader || UserRole.isGradeLeader);
    }
  }

  created() {
    if (this.$route.query.customExamId) {
      this.customExamId = this.$route.query.customExamId as string;
      this.examId = Number(this.customExamId);
    } else {
      this.examId = this.$sessionSave.get('reportDetail').examId;
    }
  }

  // 恢复默认
  async reset() {
    this.resetLoading = true;

    try {
      await restoreSchCfgAPI({
        schId: this.$sessionSave.get('schoolInfo').id,
        type: [SchoolSettingType.ScoreLineSetting].join(','),
        examId: this.examId,
      });
      this.$notify.success({
        title: '成功',
        message: '恢复成功',
      });
      this.scoreLineSetting.getConfig();
    } catch (error) {
    } finally {
      this.resetLoading = false;
    }
  }

  // 保存设置
  async save() {
    await this.scoreLineSetting.checkSizeColumn();
    await this.scoreLineSetting.checkConfig();

    this.saveLoading = true;
    const gradeId = this.$sessionSave.get('reportDetail').gradeCode;

    const jCfgs = [this.scoreLineSetting.getCfg()];

    await setSchCfgListAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      schName: this.$sessionSave.get('schoolInfo').schoolName,
      type: SchoolSettingType.Rate,
      gradeId,
      jCfgs: jCfgs,
      examId: this.examId,
    });

    this.$notify.success({
      title: '成功',
      message: '保存成功',
    });
    this.saveLoading = false;
    if (this.customExamId) {
      this.$emit('updateActiveStep', this.activeStep + 1, this.customExamId);
    }
  }
}
</script>

<style scoped lang="scss">
.target-result {
  position: relative;
  font-size: 14px;
  color: #606266;
}

.target-result__tip {
  margin-bottom: 20px;
  font-size: 16px;
}

.target-result__setting {
  margin-bottom: 20px;
}

.target-result__footer {
  display: flex;
  justify-content: space-between;
}

.reset-btn {
  position: absolute;
  top: 20px;
  right: 20px;
}

.tip-text {
  color: #fe5d50;
  font-weight: 700;
}

::v-deep {
  .setting-header {
    .title {
      &::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 24px;
        background: #409eff;
        border-radius: 3px;
        margin-right: 10px;
        vertical-align: middle;
      }
    }
  }
}
</style>

<template>
  <div :style="{ top: stickyTop }" class="ques-filter-wrapper">
    <div class="ques-filter-condition">
      <div v-if="c30BaseConditions.length">
        <!--题型，难度，类型-->
        <div ref="baseCondition">
          <div class="filter-type" v-for="item in c30BaseConditions" :key="item.title">
            <span class="typeName">{{ item.title }}：</span>
            <div class="typeList">
              <span
                class="typeOpts"
                @click="clickMoreFliterItem(typeItem, item)"
                v-for="typeItem in item.list"
                :key="typeItem.name"
                :class="{ active: item.activeIndex == typeItem.id }"
              >
                {{ typeItem.name }}
              </span>
            </div>
          </div>
        </div>
        <!--更多-->
        <div class="filter-type">
          <span class="typeName">更多：</span>
          <div class="filter-type-item-box more-condition-box" v-if="c30MoreConditions.length">
            <div class="filter-type-item" v-for="(item, index) in c30MoreConditions" :key="index">
              <el-popover
                :placement="item.placement"
                :width="item.width"
                :open-delay="1"
                :close-delay="1"
                trigger="hover"
              >
                <div class="area-box" v-if="item.title == '地区'">
                  <div class="area-row" v-for="(areaItem, j) in item.list" :key="j">
                    <div class="area-label">{{ ["#", "A-G", "H-K", "L-S", "T-Z"][j] }}</div>
                    <div class="area-content">
                      <span
                        class="typeOpts"
                        @click="clickMoreFliterItem(typeItem, item)"
                        v-for="typeItem in areaItem"
                        :key="typeItem.name"
                        :class="{ active: item.activeIndex === typeItem.id }"
                      >
                        {{ typeItem.name }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="typeList" v-else>
                  <span
                    class="typeOpts"
                    @click="clickMoreFliterItem(typeItem, item)"
                    v-for="typeItem in item.list"
                    :key="typeItem.name"
                    :class="{ active: item.activeIndex == typeItem.id }"
                  >
                    {{ typeItem.name }}
                  </span>
                </div>
                <span class="dropdown-link" slot="reference">
                  <span class="text-box" v-if="!item.activeTitle">
                    <span class="text">
                      {{ item.title }}
                    </span>
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                  <span class="text-box" v-else>
                    <span class="text">{{ item.activeTitle }}</span>
                    <i class="el-icon-circle-close" @click="clearCurrentSelect(item)"></i>
                  </span>
                </span>
              </el-popover>
            </div>
            <!--往下滚动到一定位置，题型，难度，类型显示在右边-->
            <div
              class="filter-type-item animated"
              :class="{ fadeIn: stuck }"
              v-show="stuck"
              v-for="(item, index) in c30BaseConditions"
              :key="index + '1'"
            >
              <el-popover
                :placement="item.placement"
                :width="item.width"
                :open-delay="1"
                :close-delay="1"
                trigger="hover"
              >
                <div class="typeList">
                  <span
                    class="typeOpts"
                    @click="clickMoreFliterItem(typeItem, item)"
                    v-for="typeItem in item.list"
                    :key="typeItem.name"
                    :class="{ active: item.activeIndex == typeItem.id }"
                  >
                    {{ typeItem.name }}
                  </span>
                </div>
                <span class="dropdown-link" slot="reference">
                  <span class="text-box" v-if="!item.activeTitle">
                    <span class="text">{{ item.title }}</span>
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                  <span class="text-box" v-else>
                    <span class="text">{{ item.activeTitle }}</span>
                    <i class="el-icon-circle-close" @click="clearCurrentSelect(item)"></i>
                  </span>
                </span>
              </el-popover>
            </div>
          </div>
        </div>
        <!--搜索-->
        <div class="filter-type" style="margin-top: 10px">
          <span class="typeName">搜索：</span>
          <div class="typeList">
            <el-input
              v-model="topic"
              @change="changeFilter"
              clearable
              placeholder="题干搜索"
              size="mini"
            >
            </el-input>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { localSave, sessionSave } from "@/utils/index.js";
import { mapGetters } from "vuex";
import { findDictionaryInfo } from "@/service/ptask";
import { getQueTypeListAPI } from "@/service/testbank";

export default {
  name: "QuesFilter",
  components: {},
  props: {
    stuck: {
      type: Boolean,
      default: false,
    },
    filterList: null,
  },
  mounted() {
    this.initYear();
    this.readC30FilterCondition();
    this.$bus.$on("topic", (value) => {
      this.topic = "";
    });
  },
  watch: {
    c30BaseConditions: {
      handler(newVal, oldVal) {
        this.changeFilter(newVal);
      },
      deep: true,
    },
    c30MoreConditions: {
      handler(newVal, oldVal) {
        this.changeFilter(newVal);
      },
      deep: true,
    },
  },
  computed: {
    ...mapGetters(["provinceList", "gradeList"]),
  },
  updated() {
    if (this.$refs.baseCondition) {
      this.stickyTop = -this.$refs.baseCondition.clientHeight + "px";
    }
  },
  data() {
    return {
      // c30基础刷选条件
      c30BaseConditions: [],
      // c30更多刷选条件
      c30MoreConditions: [],
      // 年份
      yearList: [],
      // c30筛选条件
      c30Filterondition: [],
      topic: "",
      stickyTop: "",
    };
  },
  methods: {
    // 初始化筛选项年份
    initYear() {
      let currentYear = new Date().getFullYear();
      for (let i = 0; i < 6; i++) {
        let year = currentYear - i;
        this.yearList.push({
          name: year,
          id: year,
        });
      }
    },
    // 从缓存读取C30筛选条件
    readC30FilterCondition() {
      let subject = sessionSave.get("currentSubject");
      if (subject) {
        this.c30Filterondition = localSave.get("c30_filter_condition_" + subject.id);
        let flag = !this.c30Filterondition;
        if (flag) {
          this.findDictionaryInfo();
        } else {
          this.preprocessC30Filter(this.c30Filterondition);
        }
      }
    },
    async findDictionaryInfo() {
      let subject = sessionSave.get("currentSubject");
      const subjectId = subject.subjectId || subject.id;
      await findDictionaryInfo({
        subjectId: subjectId,
        phaseId: subject.phaseId,
      }).then((data) => {
        localSave.set("c30_filter_condition_" + subject.id, data.data);
        this.readC30FilterCondition();
      });
    },

    // 预处理筛选项
    async preprocessC30Filter(data) {
      let arrIndex = 0,
        dictionaryList = [],
        titleList = ["难度", "题类", "类型", "解题方法", "学科思想", "核心素养"];

      let dictMap = {};
      let subject = sessionSave.get("currentSubject");

      data.forEach((it) => {
        let dict = dictMap[it.type];
        if (!dict) {
          dict = {
            title: titleList[it.type - 1],
            list: [],
            type: it.type,
          };
          dictMap[it.type] = dict;
        }
        dict.list.push(it);
      });

      titleList.forEach((value, index) => {
        dictionaryList.push(dictMap[index + 1]);
      });

      // 将难度重新排序
      dictionaryList[0].list = dictionaryList[0].list.sort((a, b) => {
        return a.id - b.id;
      });
      //获取二级题型
      let quesTypeList = [];
      await getQueTypeListAPI({
        parentId: -1,
        subjectId: subject.id,
      }).then((res) => {
        quesTypeList = res.data;
        quesTypeList.unshift({ name: "全部", id: 0, parentId: "", subjectId: "" });
      });
      // 中等改为一般
      this.handleC30BaseFilterCondition(dictionaryList, quesTypeList);
      this.handleC30MoreFilterCondition(dictionaryList);
      if (this.filterList) {
        let filterMap = {};
        this.filterList.forEach((it) => {
          filterMap[it.typeKey] = it;
        });

        this.c30BaseConditions.forEach((it) => {
          let info = filterMap[it.typeKey];
          if (info) {
            it.activeIndex = info.activeIndex;
            it.activeTitle = info.activeTitle;
          }
        });

        this.c30MoreConditions.forEach((it) => {
          let info = filterMap[it.typeKey];
          if (info) {
            it.activeIndex = info.activeIndex;
            it.activeTitle = info.activeTitle;
          }
        });
      }
    },
    // 处理C30基础筛选项（题型、难度、类型）
    handleC30BaseFilterCondition(dictionaryList, quesTypeList) {
      // console.log('dictionaryList-->', dictionaryList)
      // return
      this.c30BaseConditions = [];
      this.c30BaseConditions.push({
        title: "题型",
        activeIndex: 0,
        typeKey: "quesType",
        list: quesTypeList,
        spareKey: "subType",
      });
      let objList = dictionaryList.find((obj) => obj.type == 1);
      if (objList) {
        objList.list.sort((a, b) => a.id - b.id);
        this.c30BaseConditions.push({
          title: "难度",
          activeIndex: 0,
          typeKey: "difficultyCode",
          list: objList.list,
        });
        this.c30BaseConditions[1].list.unshift({ name: "全部", id: 0 });
      }
      objList = dictionaryList.find((obj) => obj.type == 3);
      if (objList) {
        this.c30BaseConditions.push({
          title: "类型",
          width: 460,
          activeIndex: 0,
          typeKey: "category",
          list: objList.list,
        });
        this.c30BaseConditions[2].list.unshift({ name: "全部", id: 0 });
      }
    },
    // 处理C30更多筛选项（更多：解题方法-4、学科思想-5、核心素养-6、年份、地区、题类-2等等）
    handleC30MoreFilterCondition(dictionaryList) {
      dictionaryList = dictionaryList.filter((item) => item);
      let moreCondition = [],
        objList;

      let methodList = [
        {
          type: 4,
          key: "solutionId",
        },
        {
          type: 5,
          key: "mathIdeaId",
        },
        {
          type: 6,
          key: "mathLiteraryId",
        },
      ];
      // 解题方法等
      methodList.forEach((it) => {
        objList = dictionaryList.find((obj) => {
          return obj.type === it.type;
        });
        if (objList) {
          objList.list.unshift({ name: "全部", id: 0 });
          moreCondition.push({
            width: 620,
            placement: "bottom-start",
            title: objList.title,
            activeIndex: 0,
            typeKey: it.key,
            activeTitle: "",
            list: objList.list,
            type: it.type,
          });
        }
      });

      // TODO：加入年份
      let yearListCopy = JSON.parse(JSON.stringify(this.yearList));
      yearListCopy.unshift({ name: "全部", id: 0 });
      moreCondition.push({
        width: 600,
        placement: "bottom",
        title: "年份",
        activeIndex: 0,
        typeKey: "year",
        activeTitle: "",
        list: yearListCopy,
      });
      // TODO：加入地区
      let provinceArr = [[], [], [], []],
        letter = ["a", "h", "l", "t", "zz"];
      // this.provinceMoreList = this.provinceList.slice(1, 32);
      this.provinceMoreList = this.provinceList;
      for (let j = 0, i = 0; j < letter.length - 1; j++) {
        this.provinceMoreList.map((item) => {
          if (item.pinyin > letter[j] && item.pinyin < letter[j + 1]) {
            provinceArr[j].push({
              name: item.areaName,
              id: item.region_code,
              pinyin: item.pinyin,
            });
          }
        });
      }
      provinceArr.forEach((item) => {
        item.sort((a, b) => {
          return a.region_code - b.region_code;
        });
      });
      provinceArr.unshift([{ name: "全国", id: "000000", list: [] }]);
      provinceArr[0].unshift({ name: "全部地区", id: 0, list: [] });
      moreCondition.push({
        width: 600,
        placement: "bottom",
        title: "地区",
        activeIndex: 0,
        typeKey: "provinceId",
        activeTitle: "",
        list: provinceArr,
      });
      // TODO: 加入题类
      objList = dictionaryList.find((obj) => obj.type == 2);
      if (objList) {
        objList.list.unshift({ name: "全部", id: 0 });
        moreCondition.push({
          width: 620,
          placement: "bottom-start",
          title: "题类",
          activeIndex: 0,
          typeKey: "typeCode",
          activeTitle: "",
          list: objList.list,
          type: 2,
        });
      }
      // let subject = localSave.get('currentSubject');
      let subject = sessionSave.get("currentSubject");

      // 年级
      let gradeList = this.gradeList.filter((it) => it.phaseId === subject.phaseId);
      gradeList.unshift({ name: "全部", id: 0 });
      moreCondition.push({
        width: 460,
        placement: "bottom",
        title: "年级",
        typeKey: "grade",
        activeIndex: 0,
        activeTitle: "",
        list: gradeList,
      });
      this.c30MoreConditions = moreCondition;
    },
    // 点击更多筛选项(题型，难度，类型)
    clickMoreFliterItem(item, parent) {
      // console.log(item, index, this.c30MoreConditions[index].activeTitle)
      parent.activeIndex = item.id;
      if (item.id === 0) {
        parent.activeTitle = "";
        return;
      }
      parent.activeTitle = item.name;
    },
    // 清除已选项
    clearCurrentSelect(item) {
      item.activeIndex = 0;
      item.activeTitle = "";
    },
    // 改变筛选条件
    changeFilter() {
      let list = this.c30BaseConditions.concat(this.c30MoreConditions);
      list.push({
        typeKey: "topic",
        activeIndex: this.topic,
      });
      this.$bus.$emit("changeC30Filter", "filter", {
        filterList: list,
      });
    },
    resetC30Filter() {
      this.c30BaseConditions = this.c30BaseConditions.map((item) => {
        item.activeIndex = 0;
        return item;
      });
      this.c30MoreConditions = this.c30MoreConditions.map((item) => {
        item.activeIndex = 0;
        item.activeTitle = "";
        return item;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.ques-filter-wrapper {
  background: #fff;
}

.more-condition-box {
  margin-top: 4px;
}

.ques-filter-condition {
  margin-left: 11px;
  // margin-top     : 15px;
  padding-bottom: 8px;
  position: relative;

  .filter-type {
    display: flex;
    // margin: 10px 0;
    .typeName,
    .typeOpts {
      text-align: center;
      white-space: nowrap;
    }

    .typeName {
      font-size: 16px;
      color: #646464;
      font-weight: bold;
      margin-top: 4px;
    }

    .typeOpts {
      font-size: 16px;
      color: #5a6474;
      cursor: pointer;
      padding: 4px 12px;
      margin-bottom: 5px;
      border-radius: 3px;

      &.active {
        background-color: #3f86f9;
        color: #fff;
      }
    }

    .typeList {
      display: flex;
      flex-wrap: wrap;
    }
  }

  .filter-type {
    .filter-type-item-box {
      display: flex;
      flex-wrap: wrap;

      .filter-type-item {
        margin: 0 10px;
        // &:hover {
        //   .dropdown-link .el-icon-arrow-down {
        //     transition: all .3s;
        //     transform: rotateZ(180deg);
        //   }
        // }
        .dropdown-link .text-box {
          // width: 6em;
          display: flex;
          align-items: center;
        }

        .dropdown-link .text {
          min-width: 2em;
          max-width: 4em;
          display: inline-block;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .dropdown-link .el-icon-circle-close {
          margin-left: 6px;
        }
      }
    }
  }

  .shrink-text {
    font-size: 15px;
    position: absolute;
    bottom: 10px;
    right: 30px;
    cursor: pointer;

    .shrink-block {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .text {
      color: #008dea;
    }

    .icon-down {
      width: 14px;
      height: 14px;
      margin-left: 6px;
      background: url("../../assets/icon/icon-down.png") no-repeat;
    }

    .icon-up {
      width: 14px;
      height: 14px;
      margin-left: 6px;
      background: url("../../assets/icon/icon-up.png") no-repeat;
    }
  }
}
</style>

<style lang="scss">
.dropdown-link {
  font-size: 16px;
  cursor: pointer;
  color: #5a6474;
}

.el-icon-arrow-down {
  font-size: 12px;
}

.el-popover.el-popper {
  .typeOpts {
    display: inline-block;
    text-align: center;
    white-space: nowrap;
    font-size: 16px;
    color: #5a6474;
    cursor: pointer;
    padding: 4px 12px;
    border-radius: 3px;

    &.active {
      background-color: #3f86f9;
      color: #fff;
    }
  }

  .area-box .area-row {
    display: flex;

    .area-label {
      width: 40px;
      text-align: center;
      line-height: 32px;
      white-space: nowrap;
    }
  }
}
</style>

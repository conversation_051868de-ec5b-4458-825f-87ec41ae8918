<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-05-20 16:07:11
 * @LastEditors: 小圆
-->
<template>
  <el-submenu
    v-if="item.children && item.children.length > 0"
    class="base-menu-submenu"
    :index="item.index || ''"
  >
    <template slot="title">
      <span :title="item.title">{{ item.title }}</span>
    </template>
    <report-menu-item
      v-for="(data, i) in item.children"
      :key="data.index || i"
      :item="data"
      :oftenMenuIndexList="oftenMenuIndexList"
      v-on="$listeners"
    ></report-menu-item>
  </el-submenu>

  <el-menu-item v-else :index="item.index" :disabled="item.disabled" class="base-menu-item">
    <template slot="title">
      <div class="menu-title">
        <span :title="item.title">{{ item.title }}</span>
        <i
          v-if="isShowAdd && !item.disabled"
          title="添加到常用报表"
          class="action-button add-button el-icon-circle-plus-outline"
          circle
          @click="add"
        ></i>
        <i
          v-if="isShowRemove && !item.disabled"
          title="从常用报表移出"
          class="action-button remove-button el-icon-remove-outline"
          circle
          @click="remove"
        ></i>
      </div>
    </template>
  </el-menu-item>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import BaseMenuItem from '../Base/menu/BaseMenuItem.vue';
import { getDefaultOftenMenuIndexList, oftenName } from './constant';

const defaultOftenMenuIndexList = getDefaultOftenMenuIndexList();
@Component({
  name: 'ReportMenuItem',
})
export default class ReportMenuItem extends BaseMenuItem {
  @Prop({ type: Array, default: () => [] }) oftenMenuIndexList: string[];
  // 含有add事件
  hasAdd: boolean = false;
  // 含有remove事件
  hasRemove: boolean = false;

  // 是否显示添加按钮
  get isShowAdd() {
    if (!this.hasAdd) return false;
    // 包含在常用报表中，不显示添加按钮
    return !this.oftenMenuIndexList.includes(this.item.index);
  }

  // 是否显示移出按钮
  get isShowRemove() {
    if (!this.hasRemove) return false;
    const oftenList = defaultOftenMenuIndexList.map(item => oftenName + item);
    return !oftenList.includes(this.item.index);
  }

  mounted() {
    this.hasAdd = !!this.$listeners.add;
    this.hasRemove = !!this.$listeners.remove;
  }

  add() {
    const index = this.item.index;
    this.$emit('add', index);
  }

  remove() {
    const index = this.item.index;
    this.$emit('remove', index);
  }
}
</script>

<style lang="scss" scoped>
.base-menu-item {
  &:hover {
    .action-button {
      display: block;
    }
  }
}

.action-button {
  font-size: 16px;
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  margin: auto;
  color: #909399;
  display: none;
}
</style>

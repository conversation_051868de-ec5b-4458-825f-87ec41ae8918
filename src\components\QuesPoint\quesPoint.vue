<template>
  <div class="ques-point-wrapper">
    <div class="content-catalog">
      <div class="catalog-header display_flex align-items_center justify-content_space-between">
        <span class="catalog-title">知识点目录</span>
      </div>
      <div class="catalog-treeBox" v-loading="isLoadingPoint">
        <!-- 知识点 -->
        <div class="pointer-type">
          <div class="pointer-type-group">
            <div class="pointer-type-pane" :class="{'active': currentPonitType==item.type}" v-for="item in pointerType"
                 :key="item.label" @click="changePointType(item.type)"><span>{{item.label}}</span></div>
          </div>
        </div>
        <div class="point-tree-box">
          <el-tree class="catalog-tree" :data="treeData"
                   ref="pointerTreeList"
                   show-checkbox
                   :props="defaultProps"
                   empty-text="暂无数据"
                   :check-strictly="false"
                   :expand-on-click-node="false"
                   :default-checked-keys="defaultChecked"
                   node-key="code"
                   @check="handleMoreClick">
              <span class="el-tree-node__label demo" slot-scope="{ node, data }">
                <span class="text-ellipsis" :title="node.label">{{ node.label }}</span>
              </span>
          </el-tree>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import {localSave, sessionSave} from "@/utils/index.js"
  import {findPointSubject} from "@/service/pexam"

  export default {
    name: 'QuesPoint',
    props: {
      pointInfo: null,
      //1：个册知识点，2：网络知识点
      type:1
    },
    data() {
      return {
        // 知识点数据
        treeData: [],
        // tree默认值类型
        defaultProps: {
          children: 'children',
          label: 'name'
        },
        // 当前知识点选择的是哪种集合(交集和并集)
        currentPonitType: 0,
        // 知识点交集和并集
        pointerType: [{type: 0, label: '知识点并集'}, {type: 1, label: '知识点交集'}],
        //
        knowledgeCode: '',
        // 是否在加载知识点
        isLoadingPoint: false,
        // 知识点来源
        pointOrigin: '',
        defaultChecked: []
      }
    },
    mounted() {
      this.workbookInfo = this.$route.query
    },
    watch: {},
    methods: {
      // 清除已选的节点
      clearCheckedKeys() {
        this.$refs.pointerTreeList.setCheckedKeys([])
      },
      // 处理多选节点
      handleMoreClick(node, checkedStatus) {
        let needKeys = []
        // TODO: 获取需要的节点。子节点全选就选择根节点
        checkedStatus.checkedNodes.forEach(item => {
          if (checkedStatus.checkedKeys.indexOf(item.parentCode) < 0) {
            needKeys.push(item.code)
          }
        })
        // console.log('needKeys-->', needKeys);
        this.knowledgeCode = needKeys.join(',')
        this.changePoint()
      },
      // 切换知识点类型（交集和并集）
      changePointType(type) {
        if (this.currentPonitType == type) {
          return
        }
        this.currentPonitType = type
        this.changePoint()
      },
      // 切换知识点类型或触发节点时 获取新的题目列表
      changePoint() {
          this.$bus.$emit('changePoint', this.pointOrigin, {
            ponitType: this.currentPonitType,
            knowledgeCode: this.knowledgeCode
          })
      },
      // 查询知识点三级结构数据
      getC30Point() {
        let subject = sessionSave.get('currentSubject')
        let pointData = localSave.get('C30Point_' + subject.id)
        if (pointData && pointData.time
          && pointData.time - new Date().getTime() > 0) {

          this.currentPonitType = 0
          // this.knowledgeCode = '';
          if (this.pointInfo) {
            this.knowledgeCode = this.pointInfo.knowledgeCode
            if (this.knowledgeCode) {
              //this.defaultChecked = this.knowledgeCode.split(',')
            }
          }
          this.treeData = pointData.data
          this.pointOrigin = 'c30Point'
          this.changePoint()
          return
        }
        this.isLoadingPoint = true
        findPointSubject({
          subjectId: subject.id
        }).then(data => {
          localSave.set('C30Point_' + subject.id, {
            time: new Date().getTime() + 8640000,
            data: data.data
          })
          this.isLoadingPoint = false

          this.currentPonitType = 0
          this.knowledgeCode = ''
          if (this.pointInfo) {
            this.knowledgeCode = this.pointInfo.knowledgeCode
            if (this.knowledgeCode) {
              this.defaultChecked = this.knowledgeCode.split(',')
            }
          }
          this.treeData = data.data

          this.pointOrigin = 'c30Point'
          this.changePoint()
        }).catch(err => {
          console.log(err)
        })

      },
    },
  }
</script>

<style lang="scss" scoped>
  .content-catalog {
    height: 100%;

    width: 300px;
    background: #fff;
    border: 1px solid #e8ebed;
    border-radius: 3px;

    .catalog-header {
      width: 298px;
      height: 48px;
      line-height: 48px;
      background: #f5f6fa;
      border-radius: 4px 4px 0 0;
      font-size: 16px;
      font-weight: 400;
      color: #666;
      padding: 0 15px 0 13px;

      .catalog-title {
        position: relative;
        padding-left: 10px;

        &:before {
          content: '';
          position: absolute;
          width: 4px;
          height: 16px;
          background: #008dea;
          border-radius: 2px;
          left: 0;
          top: 17px;
        }
      }
    }

    .catalog-treeBox {
      height: 100%;

      .pointer-type {
        margin: 10px auto;

        .pointer-type-group {
          display: flex;
          align-items: center;
          justify-content: space-around;
          border-bottom: 2px solid #E4E8ED;

          .pointer-type-pane {
            width: 50%;
            line-height: 48px;
            text-align: center;
            font-size: 16px;
            color: #4C5866;
            position: relative;

            &:hover {
              color: #409EFF;
              cursor: pointer;
            }

            &.active {
              color: #409EFF;

              &::before {
                content: '';
                width: 100%;
                height: 0;
                border-bottom: 2px solid #409EFF;
                position: absolute;
                bottom: -2px;
                left: 0;
              }
            }
          }
        }
      }

      .point-tree-box {
        height: 100%;
      }

      .catalog-tree {
        width: 100%;
        // min-height : 500px;
        // height: 530px;
        /*height: calc(100vh - 340px);*/
        height: calc(100% - 118px);
        overflow-x: hidden;
        overflow-y: auto;
      }
    }
  }
</style>

<style lang="scss">
  .catalog-tree {
    margin-top: 6px;

    .el-tree-node__expand-icon.expanded {
      transform: none;
    }

    .el-icon-caret-right:before {
      content: "\e7a0" !important;
    }

    .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {
      content: "\e7a2" !important;
    }

    .el-tree-node__expand-icon {
      font-size: 17px;
    }

    .el-tree-node__label {
      font-size: 16px;
    }

    .el-tree-node__content {
      height: 26px;
    }

    .el-tree-node__content {
      display: flex;
    }

    .el-tree-node__content > span:last-child {
      flex: 1;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .el-checkbox.is-checked + .el-tree-node__label {
      color: #409EFF;
    }

    &::-webkit-scrollbar, .selected-catalog-box::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }

    &::-webkit-scrollbar-thumb, .selected-catalog-box::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 10px;
      background-color: rgba(144, 147, 153, .3);
    }

    &::-webkit-scrollbar-track, .selected-catalog-box::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      // box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: #ffffff;
      border-radius: 10px;
    }
  }

  // 树节点选中高亮
  .catalog-treeBox .catalog-tree.chapter__tree .el-tree-node.is-current.is-focusable > .el-tree-node__content {
    background-color: #ffffff;

    & > .el-tree-node__label {
      color: #008DEA;
    }
  }
</style>

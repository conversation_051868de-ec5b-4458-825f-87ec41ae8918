<!--
 * @Description: 资源添加|查看列表
 * @Author: qmzhang
 * @Date: 2024-07-29 10:11:14
 * @LastEditTime: 2024-12-26 18:13:26
 * @FilePath: \personal-bigdata\src\components\AddQuesResource\views\ResAddViewList.vue
-->
<template>
    <el-container class="res-view-list" :class="{ 'add-wk-mode': onlyWeike && viewMode == 'add' }">
        <el-main style="padding: 0 20px;" v-loading="confirmLoading" :element-loading-text="confirmLoadingText">
            <br>
            <el-tabs v-model="activeTabName" type="card">
                <el-tab-pane :label="this.imageTabLabel" name="image">
                    <ImageListComponent ref="imageListRef" :originList="originImageList" :mode="viewMode"
                        :maxLength="maxResourceLength" :disableAdd="resourcelength >= maxResourceLength"
                        @res-add="handleResAdded" @res-remove="handleResRemove" @turnAddMode="turnAddMode"
                        :vModel="vModel" />
                </el-tab-pane>

                <el-tab-pane :label="linkTabLabel" name="link">
                    <LinkListComponent ref="linkListRef" :originList="originLinkList" :mode="viewMode"
                        :disableAdd="resourcelength >= maxResourceLength" @res-add="handleResAdded"
                        @res-remove="handleResRemove" @turnAddMode="turnAddMode" :vModel="vModel" />
                </el-tab-pane>

                <el-tab-pane :label="wkTabLabel" name="video">
                    <WeikeListComponent ref="weikeListRef" @change="handleVideochange" @res-add="handleResAdded"
                        @res-remove="handleResRemove" :vModel="vModel" v-if="viewMode == 'add'" />
                    <WeikeAddedListComponent ref="weikeListRef" @change="handleVideochange" @res-remove="handleResRemove"
                        :vModel="vModel" v-else />
                    <br>
                </el-tab-pane>
            </el-tabs>
        </el-main>

        <el-footer class="dialog-footer clearfix">
            <div class="pull-left" style="padding-top: 14px;">
                已选择：{{ activeTabName === 'video' ? wkLength : resourcelength }}/{{ activeTabName === 'video' ? maxWKLength : maxResourceLength }}（{{ activeTabName === 'video' ? "微课" : "图片+链接" }}）
            </div>
            <div class="pull-right" v-if="viewMode === 'add'">
                <el-button @click="closeDialog" :disabled="confirmLoading">取消</el-button>
                <el-button type="primary" @click="confirm" :loading="confirmLoading">确定</el-button>
            </div>
            <div class="pull-right" v-else>
                <el-button type="primary" style="margin-left: 15px;" icon="el-icon-video-camera" @click="recordWeike"
                    v-if="onlyWeike || isDesktop">录制微课</el-button>
                <el-button type="primary" icon="el-icon-plus"
                    @click="turnAddMode">{{ (onlyWeike || isDesktop) ? "网盘微课" : "添加资源" }}</el-button>
                <el-button @click="closeDialog">关闭</el-button>
            </div>
        </el-footer>
    </el-container>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import WeikeListComponent from '@/components/AddWeike/views/WeikeList.vue'
import WeikeAddedListComponent from '@/components/AddWeike/views/AddedList.vue'
import ImageListComponent from './ImageList.vue'
import LinkListComponent from './LinkList.vue'
import { getQueryString } from '@/utils';
import { sleep } from '@/utils/index';
import { saveOneQuestion } from '@/service/testbank';

// 题目中的资源
export interface QuesResource {
    resourceId: string
    resourceName: string
    resourceType: string
    resourceUrl: string
    resourceExt: string
    uploadDate: string
    thumbnailUrl?: string
    name?: string
    url?: string
    fileSize?: number
}

@Component({
    components: {
        WeikeListComponent,
        WeikeAddedListComponent,
        ImageListComponent,
        LinkListComponent,
    }
})
export default class ResViewList extends Vue {
    @Prop() vModel;
    public $refs!: {
        weikeListRef: WeikeListComponent;
        imageListRef: ImageListComponent;
        linkListRef: LinkListComponent;
    };
    // 是否桌面
    isDesktop = (!!window.cef  && this.$route.name != 'paperComment') || getQueryString('isDesktop') == 1;

    viewMode = 'add';

    confirmLoading: boolean = false;
    confirmLoadingText: string = "";

    // 当前正在添加的类型
    activeTabName: string = "image";

    // 微课最大数量
    maxWKLength = 9;
    // 当前添加的微课数量
    wkLength = 0;

    // 普通资源(微课之外)的资源最大数量
    maxResourceLength: number = 5;
    // 图片数量
    imgsLength = 0;
    // 链接数量
    linksLength = 0;
    // 普通资源数量
    get resourcelength() {
        return this.imgsLength + this.linksLength;
    }

    // 原题所有普通资源列表
    get originResourceList(): QuesResource[] {
        if (!this.vModel.qContent) return [];

        return this.vModel.qContent.data.url;
    }

    // 原题上的图片列表
    get originImageList() {
        let originList = this.originResourceList.filter(it => it.resourceType == '4');

        return originList.map(it => {
            return {
                ...it,
                name: it.resourceName,
                url: it.resourceUrl,
            }
        })
    }

    // 原题上的链接列表
    get originLinkList() {
        return this.originResourceList.filter(it => it.resourceType == '7')
    }

    // 剩余其他资源（图片和链接之外）列表
    get originRemainResourceList(): QuesResource[] {
        if (!this.vModel.qContent) return [];

        return this.vModel.qContent.data.url.filter(it => {
            return it.resourceType != '4' && it.resourceType != '7'
        });
    }

    get imageTabLabel() {
        return this.viewMode == 'add' ? '添加图片' : `图片（${this.imgsLength}）`
    }
    get linkTabLabel() {
        return this.viewMode == 'add' ? '添加链接' : `链接（${this.linksLength}）`
    }
    get wkTabLabel() {
        return this.viewMode == 'add' ? '添加微课' : `微课（${this.wkLength}）`
    }

    // 已添加的微课数量
    get addedCourseListLength() {
        return this.vModel.courseList.length;
    }

    get onlyWeike() {
        return this.vModel.onlyWeike
    }

    async created() {
        this.viewMode = this.vModel.mode;
        this.wkLength = this.vModel.courseList.length;
        // 如果只有一个类型的资源，自动定位到对应资源上
        this.activeTabName = this.onlyWeike && this.viewMode == 'add' ? 'video' : this.checkOnlyResType()
    }

    /**
     * @description: 检查是否只有一种类型的资源，并返回该类型
     * @param {*} list
     * @return {*}
     */
    checkOnlyResType(): string {
        let totalList = [...this.originLinkList, ...this.originImageList];
        if (!totalList.length && !this.vModel.courseList.length) return 'image';

        if (totalList.length) {
            if (totalList.some(it => it.resourceType == '4')) {
                return 'image'
            } else {
                return 'link'
            }
        } else if (this.vModel.courseList.length) {
            return 'video';
        }
    }

    /**
     * @description: 处理添加资源
     * @param {*} data
     * @return {*}
     */
    handleResAdded(type: string) {
        if (type == 'video') {
            this.wkLength++;
        } else if (type == 'image') {
            this.imgsLength++;
        } else if (type == 'link') {
            this.linksLength++;
        }
    }

    /**
     * @description: 处理删除资源
     * @return {*}
     */
    handleResRemove(type: String) {
        if (type == 'video') {
            this.wkLength--;
        } else if (type == 'image') {
            this.imgsLength--;
        } else if (type == 'link') {
            this.linksLength--;
        }
    }

    /**
     * @description: 保存普通资源到题目上
     * @return {*}
     */
    async saveResources2Question(imgList: QuesResource[], linkList: QuesResource[]) {
        let qContent = this.vModel.qContent;
        qContent.data.url = [
            ...this.originRemainResourceList,
            ...imgList, ...linkList];
        let qJson = [
            {
                data: qContent.data,
                qId: qContent.qId,
                bigTitle: qContent.bigTitle,
                subject: this.vModel.subjectId,
                type: qContent.data.type,
                sortOrder: qContent.sortOrder,
            },
        ];
        let res = await saveOneQuestion({ qJson: JSON.stringify(qJson) })
        if (res.code != 1) throw res;
    }

    /**
     * @description: 确认添加微课
     * @return {*}
     */
    async confirm() {
        try {
            this.confirmLoading = true;
            if (!this.onlyWeike && !this.isDesktop) {
                // 资源保存逐级确认
                this.confirmLoadingText = "正在保存图片"
                let imgList = await this.$refs.imageListRef.confirm();
                this.confirmLoadingText = "正在保存链接"
                let linkList = this.$refs.linkListRef.confirm();
                await this.saveResources2Question(imgList, linkList);
            }
            // 微课保存确认
            this.confirmLoadingText = "正在保存微课"
            await this.$refs.weikeListRef.confirm();
            this.confirmLoadingText = "资源保存完成！";
            await sleep(500);
            this.$notify({
                title: "保存成功",
                message: "资源全部保存成功",
                type: 'success',
                offset: 100,
            })
            this.closeDialog();

        } catch (error) {
            console.error("保存资源失败：", error)
            this.$notify({
                title: "保存失败",
                message: error.msg,
                type: 'error',
                offset: 100,
                duration: 5000
            })
        } finally {
            this.confirmLoading = false;
            this.confirmLoadingText = ""
        }
    }

    closeDialog() {
        if (this.viewMode == 'add' && this.vModel.mode == 'look') {
            // 从预览模式进入的添加模式，结束后返回预览模式
            this.viewMode = 'look';
        } else {
            this.$emit('close');
        }
    }

    /**
     * @description: 切换到添加模式
     * @return {*}
     */
    turnAddMode() {
        this.viewMode = 'add';
        if (this.onlyWeike) {
            this.activeTabName = 'video';
        }
    }

    // 启动录制微课
    recordWeike() {
        if (this.wkLength >= 9) {
            this.$notify({
                title: "无法录制",
                message: "每道题最多支持添加9个微课！",
                type: 'error',
                offset: 100,
            })
            return;
        }

        this.$emit('change', { type: 'recordWeike' });
        this.$emit('close');
    }

    /**
     * @description: 预览视频
     * @param {*} data
     * @return {*}
     */
    handleVideochange(data: any) {
        this.$emit("change", data)
    }

}


</script>

<style lang="scss" scoped>
@import "../styles/index.scss";

.list-container {
    height: 375px;
}

.dialog-footer {
    padding-top: 10px;
    border-top: 1px solid #f0f0f0;
}

.res-view-list {
    min-height: 500px;

    &.add-wk-mode {
        ::v-deep .el-tabs__header {
            display: none;
        }
    }
}
</style>
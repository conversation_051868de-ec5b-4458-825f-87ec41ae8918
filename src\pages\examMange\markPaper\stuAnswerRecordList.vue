<template>
    <div class="record-mk-container">
        <bread-crumbs :title="'已处理记录'"></bread-crumbs>
        <template v-if="tableData.length > 0">
            <el-table :data="tableData" stripe="true" border style="width: 100%">
                <el-table-column align="center" prop="time" label="修改时间">
                </el-table-column>
                <el-table-column align="center" prop="subject" label="学科">
                </el-table-column>
                <el-table-column align="center" prop="name" label="姓名">
                </el-table-column>
                <el-table-column align="center" prop="stuNo" label="考号">
                </el-table-column>
                <el-table-column align="center" prop="quesNos" label="题号">
                </el-table-column>
                <el-table-column align="center" label="操作">
                    <template slot-scope="scope">
                        <el-button @click="gotoDetail(scope.row)" type="text" size="small">查看</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination style="margin-top: 20px; display: flex; justify-content: center;" hide-on-single-page
                background layout="prev, pager, next" :page-size="pageSize" :page-count="pageCount"
                @current-change="pageChange">
            </el-pagination>
        </template>
        <el-empty v-else description="暂无处理记录"></el-empty>
        <look-paper-dialog v-if="isShowPaperImg" :stuTableData="stuTableData" :title="detailTitle" :defaultRotate="0"
            @close-dialog="isShowPaperImg = false"></look-paper-dialog>
    </div>
</template>

<script>
import BreadCrumbs from '@/components/Breadcrumbs/index.vue';
import { getExamAnswerReplaceRecords, getQuesPointSourceList } from '@/service/api.js';
import { getQueryString } from '@/utils';
import LookPaperDialog from '@/components/LookPaperDialog.vue';
import {
    replaceALiUrl,
} from "@/utils/common";

export default {
    name: 'stuAnswerRecordList',
    components: {
        BreadCrumbs,
        LookPaperDialog
    },
    data() {
        return {
            workId: getQueryString("workId"),
            examId: getQueryString("examId"),
            tableData: [],
            pageSize: 10,
            pageIndex: 1,
            pageCount: 1,
            isShowPaperImg: false,
            stuTableData: [],
            detailTitle:""
        }
    },
    created() {
        this.pageIndex = 1;
        this.pageCount = 1;
        this.getData();
    },
    methods: {
        async getData() {
            let params = {
                schoolId: this.$sessionSave.get('schoolInfo').id,
                examId: this.examId,
                text: "",
                page: this.pageIndex,
                pageSize: this.pageSize
            };
            let res = await getExamAnswerReplaceRecords(params).catch(err => {
            });
            if (res.code == 1) {
                this.pageCount = res.data.page_count;
                let list = [];
                res.data.rows.forEach((item) => {
                    list.push({
                        time: item.modifyTime,
                        subject: item.subjectName,
                        name: item.stuName,
                        stuNo: item.stuNo,
                        quesNos: item.quesNos,
                        quesNo: item.quesNo
                    })
                    this.tableData = list;
                })
            } else {
                this.$message.error("处理记录获取失败，请重试")
            }
        },
        pageChange(val) {
            this.pageIndex = val;
            this.getData();
        },
        async gotoDetail(item) {
            this.stuTableData = [];
            this.detailTitle = item.name + " - " + item.quesNos;
            let params = {
                schoolId: this.$sessionSave.get('schoolInfo').id,
                workId: this.workId,
                stuId: item.id,
                stuNo: item.stuNo,
                quesNo: item.quesNo,
                quesNos: item.quesNos,
            }

            const res = await getQuesPointSourceList(params).catch(err => {
            });
            if (res.code == 1) {
                if (res.data.length) {
                    res.data.forEach(item => {
                        let path = replaceALiUrl(item);
                        this.stuTableData.push({
                            origin: path,
                        });
                    });
                    this.isShowPaperImg = true;
                } else {
                    this.$message.error("未获取到当前题目的作答图片")
                }

            } else {
                this.$message.error(res.msg)
            }
        }
    }

}
</script>

<style></style>
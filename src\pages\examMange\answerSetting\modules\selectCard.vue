<template>
  <el-dialog
    custom-class="seled-tea-container"
    :visible="modalVisible"
    width="1010px"
    :before-close="handleClose"
    :modal-append-to-body="false"
    :close-on-click-modal="false"
  >
    <div slot="title" class="dialog-title">
      <span style="margin-right: 10px">选择答题卡</span>
    </div>
    <p style="margin-bottom: 10px">请选择需要关联的答题卡</p>
    <el-tabs v-model="pageType" type="card" @tab-click="changePageType">
      <el-tab-pane label="我的答题卡" name="1"></el-tab-pane>
      <el-tab-pane label="校内答题卡" name="5">
        <span slot="label"
          >校内答题卡
          <el-tooltip
            class="item"
            effect="dark"
            content="校内其他老师已经完成的答题卡，点击了【完成制卡】。"
            placement="top-start"
          >
            <i class="help"></i>
          </el-tooltip>
        </span>
      </el-tab-pane>
      <!-- <el-tab-pane label="校本卷库" name="4"></el-tab-pane> -->
    </el-tabs>
    <el-row class="header__select" :gutter="20">
      <el-col :span="pageType == 5 ? 6 : 8">
        <el-select
          v-model="filter.curGradeId"
          @change="handleSearch"
          clearable
          class="grade-select"
          placeholder="请选择年级"
        >
          <el-option
            v-for="item in gradeList"
            :key="item.id"
            :label="item.grade_name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="pageType == 5 ? 6 : 8">
        <el-select
          v-model="filter.curSubjectId"
          @change="handleSearch"
          clearable
          class="grade-select"
          placeholder="请选择学科"
        >
          <el-option
            v-for="item in subjectList"
            :key="item.id"
            :label="item.subject_name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="pageType == 5 ? 6 : 8" v-if="pageType == 5">
        <el-select
          v-model="timeSlot"
          @change="changeTime"
          class="grade-select"
          placeholder="请选择查询时间"
        >
          <el-option v-for="item in timeList" :key="item.id" :label="item.label" :value="item.id">
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="6" v-if="pageType == 5">
        <el-button type="primary" @click="handleSearch">查询</el-button>
      </el-col>
      <el-col :span="8" v-if="pageType == 1">
        <div class="header__serarch clearfix display_flex" style="width: 250px">
          <el-input
            class="search__text"
            placeholder="输入考试名称、试卷号搜索"
            v-model="filter.keyWord"
            @keyup.enter.native="handleSearch"
            clearable
          >
          </el-input>
          <div
            class="search__icon el-icon-search display_flex align-items_center justify-content_flex-center"
            @click="handleSearch"
          ></div>
        </div>
      </el-col>
    </el-row>
    <div class="sele-tea-area">
      <el-table
        class="seled-tea-container"
        ref="multipleTable"
        :data="tableData"
        @row-click="tableRowClick"
      >
        <el-table-column prop="tbName" label="答题卡名称" width="260" show-overflow-tooltip>
          <template slot-scope="scope">
            <!-- <el-radio v-model="scope.row.isSelected" @input="tableRowClick(scope.row, scope.$index)"></el-radio> -->
             <el-radio-group v-model="selCardId[scope.row.id]">
            <el-radio
              class="select-card"
              :label="scope.row.id"
              :disabled="
                scope.row.isRelatedWork == 1 ||
                !scope.row.subjectId.split(',').includes(firstSubId) ||
                (subjectId.split('-').length > 1 && (scope.row.cardType != 0 && scope.row.cardType != 3))
              "
              >{{ '' }}</el-radio
            ></el-radio-group>
            <span>{{ scope.row.tbName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="correctType" label="批阅类型">
          <template slot-scope="scope">
            <span>{{
              scope.row.correctType == 1 ? '手阅' : scope.row.correctType == 2 ? '网阅' : '暂无'
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="paperNo" label="试卷号"> </el-table-column>
        <el-table-column prop="gradeName" label="年级">
          <template slot-scope="scope">
            <span>{{ scope.row.gradeName != '' ? scope.row.gradeName : '暂无' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="creatorName" label="创建人">
          <template slot-scope="scope">
            <span>{{ scope.row.creatorName != '' ? scope.row.creatorName : '暂无' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="subjectName" label="学科"> </el-table-column>
        <el-table-column prop="pageLayout" label="页面布局">
          <template slot-scope="scope">
            <span>{{
              scope.row.pageLayout == 1
                ? 'A4'
                : scope.row.pageLayout == 2
                ? 'A3两栏'
                : scope.row.pageLayout == 3
                ? 'A3三栏'
                : scope.row.pageLayout == 4
                ? '正3反2'
                : scope.row.pageLayout == 5
                ? '正2反3'
                : '暂无'
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime.substr(0, 16) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <!--分页器-->
      <el-pagination
        background
        style="margin-top: 20px"
        :hide-on-single-page="!tableData.length"
        class="text-center"
        layout="total, prev, pager, next"
        @current-change="handleCurrentChange"
        :current-page.sync="filter.page"
        :page-size="filter.limit"
        :total="filter.total"
      >
      </el-pagination>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeModal">取 消</el-button>
      <el-button type="primary" :loading="isComfirming" v-prevent-re-click="1000" @click="sureClick"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { getSubjectsAPI, getSchoolGrade, saveBankJSONAPI } from '@/service/api';
import { getCardPaperList, getViewPaper } from '@/service/testbank';
import { relateAnswerCard } from '@/service/pexam';
import { getQueryString } from '@/utils';
import moment from 'moment';
import { IAB_CARD_TYPE, ICARD_STATE,IAB_CARD_SHEEFT_TYPE } from '@/typings/card';

export default {
  name: 'sele-more-tea',
  props: {
    modalVisible: {
      type: Boolean,
      required: true,
    },
    examId: {
      type: Number,
      default: 0,
    },
    examName: {
      type: String,
      default: '',
    },
    personBookId: {
      type: String,
      default: '',
    },
    gradeId: {
      type: String,
      required: true,
    },
    subjectId: {
      type: String,
      required: true,
    },
    //source:
    // 0:一起作业，1:大精，2 广州奥体，3 手阅 4.网阅
    //correctType:
    // 1：手阅， 2，网阅
    source: {
      type: Number,
      default: 3,
    },
    examInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
    abCardType: {
      type: Number,
      default: IAB_CARD_TYPE.default,
    },
    relateCardType: {
      type: Number,
      default: ICARD_STATE.default,
    },
    abCardSheetType:{
      type: String,
      default: 0
    },
  },
  watch: {
  },
  data() {
    return {
      //查询参数
      filter: {
        keyWord: '',
        page: 1,
        pageCount: 0,
        limit: 8,
        total: 0,
        //年级id
        curGradeId: '',
        //学科id
        curSubjectId: '',
        times: [],
      },
      //时间筛选类型
      timeSlot: 1,
      //年级列表
      gradeList: [],
      //学科列表
      subjectList: [],
      //答题卡数据
      tableData: [],
      // 提交按钮是否在加载中
      isComfirming: false,
      //表格加载中状态
      tableLoading: false,
      //已选答题卡ids
      selCardId: {},
      selSubjectId: [],
      pageType: '1',
      timeList: [
        {
          id: 1,
          label: '近1周',
        },
        {
          id: 2,
          label: '近1月',
        },
        {
          id: 3,
          label: '近3月',
        },
      ],
      firstSubId: '',
    };
  },
  async mounted() {
    await this.getGradeSubjectInfo(this.$sessionSave.get('schoolInfo').id);
    this.getCardList();
    this.changeTime();
  },
  methods: {
    /**
     * @name：关闭弹窗
     */
    handleClose(done) {
      this.$emit('confirm-cancel');
      done();
    },
    /**
     * @name：查询
     */
    handleSearch() {
      this.filter.page = 1;
      this.getCardList();
    },
    /**
     * @name:改变时间筛选范围
     */
    changeTime() {
      const start = moment()
        .subtract(this.timeSlot == 1 ? 7 : this.timeSlot == 2 ? 30 : 90, 'days')
        .format('YYYY-MM-DD');
      const end = moment().format('YYYY-MM-DD');
      this.filter.times = [start, end];
      this.handleSearch();
    },
    /**
     * 获取年级学科列表
     * @param schoolId
     */
    async getGradeSubjectInfo(schoolId) {
      if (!schoolId) return;
      // 获取年级列表
      if (this.gradeList.length === 0) {
        const res = await getSchoolGrade({ schoolId });
        this.gradeList = [{ id: '', grade_name: '全部年级' }, ...res.data];
      }
      if (this.subjectList.length === 0) {
        const res = await getSubjectsAPI({ schoolId });
        this.subjectList = [{ id: '', subject_name: '全部学科' }, ...res.data];
        this.processSubjectId(this.subjectId);
      } else {
        this.processSubjectId(this.subjectId);
      }
    },
    processSubjectId(subjectId) {
      if (subjectId) {
        if (this.subjectId.indexOf('-') !== -1) {
          this.firstSubId = subjectId.split('-')[0];
          this.filter.curSubjectId = subjectId.split('-')[0];
        } else {
          this.firstSubId = subjectId;
          this.filter.curSubjectId = subjectId;
        }
      }
    },
    /**
     * @name：获取答题卡列表
     */
    getCardList(type) {
      // this.tableLoading = true;
      this.filter.page = type === 'changePage' ? this.filter.page : 1;
      let params = {
        // pageType: (1：我的卷库（我的答题卡+ 我的试卷） 2：学校模板卡 4：校本试卷,5:校内答题卡)
        // 相加取全部7
        type: this.pageType,
        schoolId: this.$sessionSave.get('schoolInfo').id,
        userId: this.$sessionSave.get('loginInfo').id,
        name: this.filter.keyWord || '',
        page: this.filter.page,
        limit: this.filter.limit,
        subjectCode: this.filter.curSubjectId,
        gradeCode: this.filter.curGradeId,
        showAll: 0,
        startTime: this.pageType == 5 ? this.filter.times[0] : '',
        endTime: this.pageType == 5 ? this.filter.times[1] : '',
        abCardType: this.abCardType
      };
      getCardPaperList(params)
        .then(data => {
          // this.tableLoading = false;
          data.data.rows.forEach(item => {
            item.isSelected = false;
          });
          this.tableData = data.data.rows || [];
          this.filter.total = data.data.total_rows;
        })
        .catch(err => {
          // this.tableLoading = false;
        });
    },
    /**
     * @name：点击确定
     */
    sureClick() {
      if (!Object.keys(this.selCardId).length) {
        this.$message({
          message: '请选择需要关联的答题卡！',
          type: 'error',
          duration: 1000,
        });
        return;
      }
      let selSubj = this.selSubjectId.split(',').sort();
      let examSubj = this.subjectId.split('-').sort();
      //综合学科
      if (JSON.stringify(selSubj) != JSON.stringify(examSubj)) {
        let subjectName = this.$route.query.subjectName;
        this.$confirm(`答题卡所属学科与考试科目（${subjectName}）不一致，确定关联吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.relateCard();
          })
          .catch(() => {});
      } else {
        this.relateCard();
      }
    },
    /**
     * @name：确认已选择的答题卡
     */
    async relateCard() {
      this.isComfirming = true;
      let params = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        examId: this.examId,
        personalBookId: this.personBookId,
        relateCardType: this.relateCardType
      };
      let ids = [];
      for (let key in this.selCardId) {
        ids.push(key);
      }
      if(this.relateCardType=== ICARD_STATE.abCard){
        if(ids[1]){
          params.tbId = ids[0];
          params.tbId1 = ids[1];
        }else{
          if(this.abCardSheetType == IAB_CARD_SHEEFT_TYPE.aCard){
            params.tbId = ids[0];
          }else{
            params.tbId1 = ids[0];
          }
        }
      }else if(this.relateCardType=== ICARD_STATE.abPaperTwo){
        if(this.abCardSheetType == IAB_CARD_SHEEFT_TYPE.aCard){
          params.tbId = ids[0];
        }else{
          params.tbId1 = ids[0];
        }
      }else{
        params.tbId = ids[0];
      }
        await relateAnswerCard(params)
        .then(res => {
          this.$message({
            message: '关联成功！',
            type: 'success',
            duration: 1000,
          });
          this.initParams();
          this.$emit('confirm-sure',ids);
          this.isComfirming = false;
        })
        .catch(err => {
          this.isComfirming = false;
        });
    },
    /**
     * @name：取消选择的教师
     */
    closeModal() {
      this.initParams();
      this.$emit('confirm-cancel');
    },
    /**
     * @name：表格行点击
     */
    tableRowClick(val) {
      //批阅类型、学科一致且未被关联过考试的题卡可以选择
      if (
        val.isRelatedWork == 1 ||
        !val.subjectId.split(',').includes(this.firstSubId) ||
        (this.subjectId.split('-').length > 1 && (val.cardType != 0 && val.cardType != 3))
      )
        return;
      this.selSubjectId = val.subjectId;
      let ids = (val.abCardId||val.id).split(',');
      let obj = {};
      ids.forEach(id => {
        obj[id] = id;
      })
      this.selCardId = obj;
    },
    // 分页查询
    handleCurrentChange(val) {
      this.filter.page = val;
      this.getCardList('changePage');
    },
    /**
     * @name：重置查询栏
     */
    initParams() {
      this.filter.keyWord = '';
      this.filter.page = 1;
      this.filter.pageCount = 0;
      this.filter.total = 0;
      this.filter.curGradeId = '';
      this.filter.curSubjectId = '';
      this.selCardId = {};
    },
    changePageType() {
      this.getCardList();
    },
  },
};
</script>

<style lang="scss" scoped>
.sele-tea-area {
  margin-top: 15px;
  position: relative;
}

.seled-tea-container {
  border: 1px solid #ddd;
  border-bottom: 0;
  border-radius: 5px;
}

.header__select {
  display: inline-block;
  margin-right: 10px;
  line-height: 40px;

  .role-select,
  .year-select,
  .term-select,
  .grade-select {
    // width: 150px;
  }
}

.header__serarch {
  display: flex;
  width: 240px;

  .search__icon {
    width: 38px;
    font-size: 18px;
    color: #fff;
    background: #409eff;
    border-radius: 0 3px 3px 0;
    outline: none;
    cursor: pointer;
  }
}
.help {
  background: url(../../../../assets/exam/help.png) no-repeat;
  width: 20px;
  height: 20px;
  position: absolute;
  cursor: pointer;
}
</style>
<style lang="scss">
.seled-tea-container {
  .search__text {
    .el-input__inner {
      border-right: none;
      border-radius: 3px 0 0 3px;
    }
  }

  .ivu-table-body {
    max-height: 255px;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .el-dialog__header {
    height: 45px;

    .dialog-title {
      line-height: 45px;
      color: #484949;
    }
  }

  .el-dialog__body {
    padding: 15px 20px;
  }

  .el-checkbox {
    &.el-checkbox + .el-checkbox {
      margin-left: 0;
    }
  }

  .el-radio {
    margin-right: unset;
  }
  .el-radio__input.is-disabled .el-radio__inner {
    background-color: #dcdfe6;
    border-color: #c5c8ce;
  }
}

.sele-tea-area {
  .ivu-transfer-operation .ivu-btn {
    width: 30px !important;
  }

  .ivu-input-small {
    height: 24px !important;
  }

  .ivu-input-wrapper {
    padding-top: 3px;
  }

  .ivu-table-cell {
    padding-right: 0 !important;
  }
}
</style>

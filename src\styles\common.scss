@import "_config.scss";

body {
  margin: 0;
  padding: 0;
  font       : {
    family: $fontFamily;
    size: $fontSize;
  }
  line-height: $lineHeight;
  color: $color;
  background-color: #f7fafc;
  overflow-x: hidden;
}

.page-header {
  position: relative;
  z-index: 200;
  background-color: $mainColor;
  color: #fff;
  font-size: 36px;
  text-align: center;
  line-height: 100px;
}

.page-container {
  position: relative;
  z-index: 100;
  height: 100%;
  margin: -100px auto -80px;
  padding: 100px 0 80px;
  .page-content {
    position: relative;
    height: 100%;
    overflow : {
      y: auto;
      x: hidden;
    }
  }
}

.page-footer {
  position: relative;
  z-index: 200;
  height: 80px;
  background-color: #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}

button {
  font-family: $fontFamily;
}

.el-button {
  font-family: $fontFamily;
}

.el-radio-button__orig-radio:checked + .el-radio-button__inner {
  background-color: $mainColor !important;
}

// DatePicker 日期选择器消除中间间距
.el-date-editor .el-range-separator {
  padding: 0 !important;
}

.platform-dialog {
  border-radius: 4px;
  overflow: hidden;
  .el-dialog__header {
    padding: 0 24px;
    line-height: 46px;
    background: rgba(240, 243, 245, 1);
    .el-dialog__title {
      font-size: 18px;
      color: rgba(78, 86, 104, 1);
    }
    .el-dialog__headerbtn {
      top: 7px;
      font-size: 24px;
      right: 15px;
    }
  }
  .el-dialog__body {
    padding: 20px;
  }
  .el-dialog__footer {
    padding-top: 0;
  }
}

// 单行省略
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 弹出框暗黑主题
.el-popover.is-dark {
  padding: 10px;
  background: #303133;
  color: #fff;

  .popper__arrow {
    &::after {
      border-top-color: #303133 !important;
    }
  }
}

.el-popover.is-text {
  pointer-events: none;
}

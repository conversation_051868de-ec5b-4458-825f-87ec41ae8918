<template>
  <!-- 上线设置 -->
  <div class="target-container">
    <div style="display: flex; line-height: 40px; margin-left: -20px">
      <p class="tip-txt">提示：此处指标均按总分统计，各科根据总分统计规则，按比例自动生成</p>
      <el-button
        type="primary"
        class="btn-item"
        style="position: absolute; right: 20px"
        :loading="loading"
        @click="resetCustom"
        :disabled="isDisabled"
      >
        恢复默认</el-button
      >
    </div>
    <!-- 五率设置 -->
    <div class="target-box">
      <div class="title">五率设置（按分数计算）</div>
      <div class="content-box">
        <el-form
          :ref="'fourRateRef'"
          :hide-required-asterisk="true"
          :inline="true"
          :model="fourRateRule"
        >
          <div class="content-main" v-for="(rate, i) in fourRateRule.fourRate" :key="i">
            <div class="content-form clearfix">
              <el-tooltip effect="dark" :content="rate.name" placement="left">
                <div class="content-from-label" style="display: inline-block">
                  {{ rate.name + ':' }}
                </div>
              </el-tooltip>

              <template v-for="(item, index) in rate.data">
                <template v-if="!item.isCustom">
                  <el-form-item
                    label=""
                    :prop="`fourRate[${i}].data[${index}].end`"
                    :rules="rules.numberMaxRules"
                    v-if="index === 0"
                  >
                    <el-input-number
                      v-model="item.end"
                      class="w-60 m-2"
                      :controls="false"
                      :step="1"
                      :step-strictly="true"
                      :min="0"
                      :max="100"
                      size="small"
                      @change="changeFourRate(i, index, 'end')"
                    >
                    </el-input-number
                    >%
                  </el-form-item>
                  <el-form-item
                    :label="index === 0 ? `≥${item.name}≥` : `，${item.name}≥`"
                    :prop="`fourRate[${i}].data[${index}].start`"
                    :rules="rules.numberMaxRules"
                    v-if="index !== rate.data.length - 2"
                  >
                    <el-input-number
                      v-model="item.start"
                      class="w-60 m-2"
                      :controls="false"
                      :step="1"
                      :step-strictly="true"
                      :min="0"
                      :max="100"
                      size="small"
                      @change="changeFourRate(i, index)"
                    >
                    </el-input-number
                    >%
                  </el-form-item>
                  <el-form-item
                    v-if="index == rate.data.length - 2"
                    :label="`，${item.name}<`"
                    :prop="`fourRate[${i}].data[${index}].end`"
                    :rules="rules.numberMaxRules"
                  >
                    <el-input-number
                      v-model="item.end"
                      class="w-60 m-2"
                      :controls="false"
                      :step="1"
                      :step-strictly="true"
                      :min="0"
                      :max="100"
                      size="small"
                      @change="changeFourRate(i, index, 'end')"
                    >
                    </el-input-number
                    >%
                  </el-form-item>
                </template>
                <template v-else>
                  <div style="display: inline-block; vertical-align: middle">
                    <el-form-item
                      label="，"
                      :prop="`fourRate[${i}].data[${index}].end`"
                      :rules="rules.numberMaxRules"
                    >
                      <el-input-number
                        v-model="item.end"
                        class="w-60 m-2"
                        :controls="false"
                        :step="1"
                        :step-strictly="true"
                        :min="0"
                        :max="100"
                        size="small"
                        @change="changeFourRate(i, index, 'end')"
                      >
                      </el-input-number
                      >%
                    </el-form-item>
                    <el-form-item :label="`>${item.name}`" class="custom-form-item"> </el-form-item>
                    <el-form-item
                      label="≥"
                      :prop="`fourRate[${i}].data[${index}].start`"
                      :rules="rules.numberMaxRules"
                    >
                      <el-input-number
                        v-model="item.start"
                        class="w-60 m-2"
                        :controls="false"
                        :step="1"
                        :step-strictly="true"
                        :min="0"
                        :max="100"
                        size="small"
                        @change="changeFourRate(i, index)"
                      >
                      </el-input-number
                      >%
                    </el-form-item>
                  </div>
                </template>
              </template>
            </div>
          </div>
        </el-form>
      </div>

      <!-- <div class="content-box">
        <el-form :inline="true" :model="fourRate" :hide-required-asterisk="true" ref="fourRateRef">
          <div class="content-main">
            <div class="content-form clearfix">
              <template v-for="(item, index) in fourRate.school">
                <template v-if="!item.isCustom">
                  <el-form-item label="" :prop="`school.${index}.end`" :rules="rules.numberMaxRules" v-if="index === 0">
                    <el-input v-model.number="item.end" class="w-60 m-2" size="large" @blur="
                      $event.target.value = $event.target.value.replace(
                        /^\s+|\s+$/gm,
                        ''
                      )
                      " ></el-input>%
                  </el-form-item>
                  <el-form-item :label="index === 0 ? `≥${item.name}≥` : `＞${item.name}≥`" :prop="`school.${index}.start`"
                    :rules="rules.numberMaxRules">
                    <el-input v-model.number="item.start" class="w-60 m-2" size="large" @blur="
                      $event.target.value = $event.target.value.replace(
                        /^\s+|\s+$/gm,
                        ''
                      )
                      " @change="changeFourRate('school', index)" />%
                  </el-form-item>
                </template>
                <div class="custom-sec" v-else>
                  <el-form-item label="" :prop="`school.${index}.end`" :rules="rules.numberMaxRules">
                    <el-input v-model.number="item.end" class="w-60 m-2" size="large" @blur="
                      $event.target.value = $event.target.value.replace(
                        /^\s+|\s+$/gm,
                        ''
                      )
                      " />%
                  </el-form-item>
                  <el-form-item :label="`>${item.name}`" :prop="`school.${index}.name`" :rules="rules.nameRules"
                    class="custom-form-item">
                  </el-form-item>
                  <el-form-item label="≥" :prop="`school.${index}.start`" :rules="rules.numberMaxRules">
                    <el-input v-model.number="item.start" class="w-60 m-2" size="large" @blur="
                      $event.target.value = $event.target.value.replace(
                        /^\s+|\s+$/gm,
                        ''
                      )
                      " @change="changeFourRate('school', index)" />%
                  </el-form-item>
                </div>
              </template>
            </div>
          </div>
        </el-form>
      </div> -->
    </div>
    <!-- 等级分布 -->
    <div class="target-box">
      <div class="title">年级成绩分布&等级分布设置</div>
      <div class="content-box">
        <el-form
          :inline="true"
          :model="levelDist"
          :hide-required-asterisk="true"
          ref="levelDistRef"
        >
          <div class="content-main">
            <div class="content-form clearfix">
              <template v-for="(item, index) in levelDist.school">
                <template>
                  <el-form-item
                    v-if="index === 0"
                    label=""
                    :prop="`school.${index}.end`"
                    :rules="rules.numberMaxRules"
                    style="padding-top: 10px"
                  >
                    <el-input
                      v-model.number="item.end"
                      class="w-60 m-2"
                      size="small"
                      @blur="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm, '')"
                    />%
                  </el-form-item>
                  <el-form-item
                    :label="index === 0 ? `≥${item.name} 等≥` : `＞${item.name} 等≥`"
                    :prop="`school.${index}.start`"
                    :rules="rules.numberMaxRules"
                    style="padding-top: 10px"
                  >
                    <el-input
                      v-model.number="item.start"
                      class="w-60 m-2"
                      size="small"
                      @blur="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm, '')"
                      @change="changelevelDist('school', index)"
                    />%
                  </el-form-item>
                </template>
              </template>
            </div>
          </div>
          <el-form-item label="计算规则 :" class="col-title">
            <el-radio-group v-model="lvType" class="ml-4">
              <el-radio :label="1" size="large">按得分率</el-radio>
              <el-radio :label="2" size="large">按排名</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!-- 优困生设置 -->
    <div class="target-box">
      <div class="title">优困生设置</div>
      <div class="content-box">
        <el-form
          :inline="true"
          :hide-required-asterisk="true"
          :model="poorStuForm"
          ref="poorStuRef"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="计算规则 :" class="col-title">
                <el-radio-group v-model="poorStuForm.rule" class="ml-4">
                  <el-radio :label="1" size="large">按名次</el-radio>
                  <el-radio :label="2" size="large">按得分率</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 15px">
            <el-col :span="24">
              <template v-if="poorStuForm.rule === 1">
                <el-form-item
                  label="学优生：前"
                  prop="rankSchool.before"
                  :rules="rules.numberMaxRules"
                  class="m-60"
                >
                  <el-input
                    v-model.number="poorStuForm.rankSchool.before"
                    class="w-60 m-2"
                    size="large"
                    @blur="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm, '')"
                  />%
                </el-form-item>
                <el-form-item
                  label="学困生：后"
                  prop="rankSchool.after"
                  :rules="rules.numberMaxRules"
                  style="margin-left: 15px"
                >
                  <el-input
                    v-model.number="poorStuForm.rankSchool.after"
                    class="w-60 m-2"
                    size="large"
                    @blur="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm, '')"
                  />%
                </el-form-item>
              </template>
              <template v-else>
                <el-form-item
                  label="学优生：≥"
                  prop="scoreSchool.before"
                  :rules="rules.numberMaxRules"
                  class="m-60"
                >
                  <el-input
                    v-model.number="poorStuForm.scoreSchool.before"
                    class="w-60 m-2"
                    size="large"
                    @blur="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm, '')"
                  />%
                </el-form-item>
                <el-form-item
                  label="学困生：<"
                  prop="scoreSchool.after"
                  :rules="rules.numberMaxRules"
                >
                  <el-input
                    v-model.number="poorStuForm.scoreSchool.after"
                    class="w-60 m-2"
                    size="large"
                    @blur="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm, '')"
                  />%
                </el-form-item>
              </template>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <!-- 上线分析 -->
    <div class="target-box">
      <div class="title">上线设置（按{{ selectedOption == 1 ? '分数' : '名次' }}计算）</div>
      <div style="margin: 20px">
        <el-radio-group v-model="onlineType">
          <el-radio label="online">上线设置</el-radio>
          <el-select v-model="selectedOption" style="width: 150px; margin-right: 20px">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-radio label="target">目标设置</el-radio>
        </el-radio-group>
      </div>
      <div class="content-box" v-show="onlineType == 'online'">
        <el-form :model="onlineForm" :hide-required-asterisk="true" ref="onlineRef">
          <table class="target-table">
            <thead>
              <th width="180px">学科</th>
              <th v-for="item in tempOnlineList" :key="item.key">
                {{ item.name }}
              </th>
              <th width="130px">操作</th>
            </thead>
            <tr v-for="(item, index) in onlineForm.tableData" :key="item.index">
              <td>{{ item.subName }}({{ item.defaultValue }})</td>
              <td
                v-for="(subItem, subIndex) in tempOnlineList"
                :key="subItem.key"
                class="custom-col"
              >
                <el-form-item
                  label=""
                  :prop="'tableData.' + index + '.value.' + subIndex"
                  :rules="rules.floatNumRules"
                  class="w-100 custom-form-item"
                >
                  <el-input
                    v-model="item.value[subIndex]"
                    class="w-100"
                    size="large"
                    @blur="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm, '')"
                  />
                </el-form-item>
              </td>
              <td :rowspan="onlineForm.tableData.length" v-if="index === 0" style="padding: 0">
                <i
                  class="icon-item icon-addItem click-element"
                  @click="addOnlineTarget"
                  v-if="tempOnlineList.length < 4"
                ></i>
                <i
                  class="icon-item icon-del click-element"
                  @click="delOnlineTarget"
                  v-if="tempOnlineList.length > 1"
                ></i>
                <i
                  class="icon-item icon-edit click-element"
                  @click="opeanEditDialog"
                  v-if="selectedOption == 1"
                ></i>
              </td>
            </tr>
          </table>
        </el-form>
      </div>
      <div class="content-box" v-show="onlineType == 'target'">
        <el-form :model="targetForm" :hide-required-asterisk="true" ref="targetRef">
          <table class="target-table">
            <thead>
              <th width="180px">班级</th>
              <th v-for="item in tempTargetList" :key="item.key">
                {{ item.name }}
              </th>
              <th width="130px">操作</th>
            </thead>
            <tr v-for="(item, index) in targetForm.tableData" :key="item.id">
              <td>{{ item.name }}</td>
              <td
                v-for="(subItem, subIndex) in tempTargetList"
                :key="subItem.key"
                class="custom-col"
              >
                <el-form-item
                  label=""
                  :prop="'tableData.' + index + '.value.' + subIndex"
                  :rules="rules.floatNumRules"
                  class="w-100 custom-form-item"
                >
                  <el-input
                    v-model="item.value[subIndex]"
                    class="w-100"
                    size="large"
                    @blur="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm, '')"
                  />
                </el-form-item>
              </td>
              <td :rowspan="targetForm.tableData.length" v-if="index === 0" style="padding: 0">
                <i
                  class="icon-item icon-addItem click-element"
                  @click="addTarget"
                  v-if="tempTargetList.length < 4"
                ></i>
                <i
                  class="icon-item icon-del click-element"
                  @click="delTarget"
                  v-if="tempTargetList.length > 1"
                ></i>
              </td>
            </tr>
          </table>
        </el-form>
      </div>
    </div>
    <!-- 临界生设置 -->
    <div class="target-box">
      <div class="title">临界生设置</div>
      <div class="content-box">
        <el-form :model="critStu" :inline="true" :hide-required-asterisk="true" ref="critStuRef">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="上浮值" prop="up" :rules="rules.numberRules" class="m-60">
                <el-input
                  v-model.number="critStu.up"
                  class="w-60 m-2"
                  size="large"
                  @blur="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm, '')"
                />{{ selectedOption == 1 ? '分' : '名' }}
              </el-form-item>
              <el-form-item label="下浮值" prop="down" :rules="rules.numberRules">
                <el-input
                  v-model.number="critStu.down"
                  class="w-60 m-2"
                  size="large"
                  @blur="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm, '')"
                />{{ selectedOption == 1 ? '分' : '名' }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <!-- 均分统计设置 -->
    <div class="target-box">
      <div class="title">
        均分统计设置
        <span class="title-tips">
          （注意：未勾选情况下，总分相关指标默认不计算全科缺考的学生、计算部分科目缺考的学生。）
        </span>
      </div>
      <div class="content-box">
        <el-checkbox v-model="isAllSubject" @change="changeSubject"
          >参考科目少于
          <el-input
            v-model.number="subjectNum"
            @blur="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm, '')"
            class="w-60 m-2"
            size="large"
          />
          科，不纳入全科统计</el-checkbox
        >
        <el-checkbox v-model="isZero">不统计实考0分的学生</el-checkbox>
      </div>
    </div>
    <div class="target-box" style="position: relative">
      <div class="save-btn" style="left: 20px; right: unset" v-if="customExamId">
        <el-button
          type="primary"
          @click="$emit('updateActiveStep', activeStep - 1, customExamId)"
          :disabled="isDisabled"
        >
          上一步</el-button
        >
      </div>
      <div class="save-btn">
        <el-button type="primary" @click="save" :disabled="isDisabled">
          {{ customExamId ? '下一步' : '保存设置' }}</el-button
        >
      </div>
    </div>
    <!-- 编辑上线分析占比弹窗 -->
    <set-online-dialog
      v-if="isShowEditDialog"
      :tempOnlineList="tempOnlineList"
      @confirmSetOnline="confirmSetOnline"
      :selectedOption="selectedOption"
      @closeDialog="closeDialog"
    ></set-online-dialog>
  </div>
</template>

<script>
import { checkFloatNum, checkNumberVal } from '@/utils/common.js';
import {
  setAllConfAPI,
  getConfAPI,
  getDefaultFracLineConfAPI,
  getExamSubject,
} from '@/service/pexam';
import setOnlineDialog from '../modules/setOnlineDialog.vue';
import UserRole from '@/utils/UserRole';

export default {
  components: { setOnlineDialog },
  props: {},
  data() {
    return {
      activeStep: 3,
      customExamId: this.$route.query.customExamId,
      options: [
        {
          value: '1',
          label: '按成绩统计',
        },
        {
          value: '2',
          label: '按名次统计',
        },
      ],
      selectedOption: '',
      // 分数线指标集合
      tempOnlineList: [],
      copiedTempOnlineList: [],
      onlineList: [],
      targetList: [],
      tempTargetList: [],
      // 上线表单
      onlineForm: {
        tableData: [],
      },
      onlineData: [],

      fourRateRule: {
        fourRate: [],
      },

      // 一分四率
      // fourRate: [
      //   {
      //     subjectName: '数学',
      //     subjectId: '1',
      //     list: [
      //       {
      //         isCustom: false,
      //         name: '优秀率',
      //         start: 85,
      //         end: 100,
      //       },
      //       {
      //         isCustom: false,
      //         name: '优良率',
      //         start: 70,
      //         end: 85,
      //       },
      //       {
      //         isCustom: false,
      //         name: '及格率',
      //         start: 60,
      //         end: 70,
      //       },
      //       {
      //         isCustom: false,
      //         name: '不及格率',
      //         start: 0,
      //         end: 60,
      //       },
      //       {
      //         isCustom: true,
      //         name: '低分率',
      //         start: 0,
      //         end: 30,
      //       },
      //     ],
      //   },
      //   {
      //     subjectName: '语文',
      //     subjectId: '2',
      //     list: [
      //       {
      //         isCustom: false,
      //         name: '优秀率',
      //         start: 85,
      //         end: 100,
      //       },
      //       {
      //         isCustom: false,
      //         name: '优良率',
      //         start: 70,
      //         end: 85,
      //       },
      //       {
      //         isCustom: false,
      //         name: '及格率',
      //         start: 60,
      //         end: 70,
      //       },
      //       {
      //         isCustom: false,
      //         name: '不及格率',
      //         start: 0,
      //         end: 60,
      //       },
      //       {
      //         isCustom: true,
      //         name: '低分率',
      //         start: 0,
      //         end: 30,
      //       },
      //     ],
      //   },
      // ],
      //等级分布
      levelDist: {
        school: [],
      },
      // 临界生
      critStu: {
        up: '',
        down: '',
      },
      // 表单验证规则
      rules: {
        // 数字和必填项验证规则 不超过100
        numberMaxRules: [
          {
            required: true,
            message: '必填项',
            trigger: 'blur',
          },
          {
            type: 'number',
            message: '请输入0-100',
            trigger: 'blur',
          },
          { validator: checkNumberVal, trigger: 'blur' },
        ],
        // 数字和必填项验证规则
        numberRules: [
          {
            required: true,
            message: '必填项',
            trigger: 'blur',
          },
          {
            type: 'number',
            message: '请输入数字',
            trigger: 'blur',
          },
          { validator: checkFloatNum, trigger: 'blur' },
        ],
        // 数字 可以是小数
        floatNumRules: [
          {
            required: true,
            message: '必填项',
            trigger: 'blur',
          },
          {
            validator: checkFloatNum,
            trigger: 'blur',
          },
        ],
      },
      //优困生
      poorStuForm: {
        rule: 1,
        //按名次
        rankSchool: {
          rule: 1,
          before: 15,
          after: 5,
        },
        // 按得分率
        scoreSchool: {
          rule: 2,
          before: 90,
          after: 30,
        },
      },
      // 英文字母集合
      letter: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I'],
      //按钮加载状态
      loading: false,
      //上线设置类型
      onlineType: 'online',
      // 等级类型 1：按得分率 2：按排名
      lvType: 1,
      subjectNum: '',
      isAllSubject: false,
      isZero: false,
      // 目标设置表单
      targetForm: {
        tableData: [],
      },
      // 均分统计设置
      avgStat: {
        min: -1,
        zero: -1,
      },
      isShowEditDialog: false,
      //分数线名称集合
      onlineName: [],
      isgetDefaultFracLineConf: true,

      // 学科列表
      subjectList: [],
    };
  },
  computed: {
    isDisabled() {
      return !(UserRole.isOperation || UserRole.isSchoolLeader || UserRole.isGradeLeader);
    },
  },
  watch: {
    selectedOption(newVal, oldVal) {
      //页面初始加载不恢复默认值
      if (this.isgetDefaultFracLineConf) {
        this.getDefaultFracLineConf();
      }
      this.isgetDefaultFracLineConf = true;
    },
  },
  mounted() {
    this.geConf();
    this.getExamSubject();
  },
  methods: {
    // 获取学科
    async getExamSubject() {
      const res = await getExamSubject({
        examId: this.customExamId
          ? this.customExamId
          : this.$sessionSave.get('reportDetail').examId,
      });
      this.subjectList = res.data;
    },
    /**
     * @name:获取默认上线设置数据
     */
    async getDefaultFracLineConf() {
      await getDefaultFracLineConfAPI({
        examId: this.customExamId
          ? this.customExamId
          : this.$sessionSave.get('reportDetail').examId,
        type: this.selectedOption,
      }).then(res => {
        this.onlineForm.tableData = res.data;
        this.tempOnlineList = this.onlineForm.tableData[0].names.map(name => ({ name }));
        // this.initOnline();
        if (this.selectedOption == 2) {
          this.onlineForm.tableData.forEach(item => {
            item.value = item.value.map(() => '');
          });
          this.critStu = {
            up: '',
            down: '',
          };
        } else if (this.selectedOption == 1) {
          this.tempOnlineList = this.copiedTempOnlineList;
          this.critStu = {
            up: 10,
            down: 10,
          };
        }
      });
    },

    /**
     * @name:获取默认指标数据
     */
    async geConf() {
      await getConfAPI({
        examId: this.customExamId
          ? this.customExamId
          : this.$sessionSave.get('reportDetail').examId,
        type: 100,
      }).then(res => {
        this.targetData = JSON.parse(res.data);
        //一份五率
        // this.fourRate.school = this.targetData.fiveRate;
        this.fourRateRule.fourRate = this.targetData.fiveRate;
        //等级分布
        this.levelDist.school = this.targetData.dist;
        //上线分析数据
        this.onlineForm.tableData = this.targetData.fracLine;
        //上线比例数据
        this.tempOnlineList = this.targetData.lineRate;
        //复制一份源数据
        this.copiedTempOnlineList = JSON.parse(JSON.stringify(this.tempOnlineList));
        // 临界生
        this.critStu = this.targetData.critStu;
        //目标设置数据
        this.targetForm.tableData = this.targetData.tarLine;
        // 等级类型
        this.lvType = this.targetData.lvType;
        //上线设置排序方式
        this.selectedOption = String(this.targetData.lineType);
        this.avgStat = this.targetData.avgStat;
        this.isgetDefaultFracLineConf = false;
        if (this.avgStat && this.avgStat.min != -1) {
          this.isAllSubject = true;
          this.subjectNum = this.avgStat.min;
        }
        if (this.avgStat && this.avgStat.zero == 1) {
          this.isZero = true;
        }
        //初始化优困生数据
        this.initPoorStu();
        //初始化分数线数据
        // this.initOnline();
        // 初始化目标设置
        this.initTargetData();
        this.$emit('updateActiveStep', this.activeStep, this.customExamId);
      });
    },
    /**
     * @name:初始化优困生数据
     */
    initPoorStu() {
      this.poorStuForm.rule = this.targetData.poolStu.rule;
      if (this.poorStuForm.rule == 1) {
        this.poorStuForm.rankSchool = this.targetData.poolStu;
      } else {
        this.poorStuForm.scoreSchool = this.targetData.poolStu;
      }
    },
    changeSubject() {
      this.subjectNum = this.isAllSubject ? this.subjectList.length : '';
    },
    /**
     * @name:恢复默认
     */
    resetCustom() {
      this.loading = true;
      setAllConfAPI({
        examId: this.customExamId
          ? this.customExamId
          : this.$sessionSave.get('reportDetail').examId,
        type: 100,
        content: '{}',
        lineType: 2,
      }).then(res => {
        setTimeout(() => {
          this.loading = false;
          const reportDetail = this.$sessionSave.get('reportDetail');
          this.$sessionSave.set('reportDetail', {
            ...reportDetail,
            v: reportDetail.v + 1,
          });
          this.$message({
            message: '恢复默认成功！',
            type: 'success',
            duration: 1500,
          });
          this.geConf();
        }, 1000);
      });
    },
    /**
     * @name 新增四率指标
     */
    // addFourRateTarget(key) {
    //   this.fourRate[key].push({
    //     name: "",
    //     start: 0,
    //     end: 0,
    //     isCustom: true,
    //   });
    // },
    /**
     * @name 删除四率指标
     */
    // delFourRateTarget(key) {
    //   this.fourRate[key].splice(this.fourRate[key].length - 1, 1);
    // },
    /**
     * @name 改变一分四率开始值时校验
     */
    // changeFourRate(key, index) {
    //   const start = this.fourRate[key][index].start;
    //   const end = this.fourRate[key][index].end;
    //   const isCustom = this.fourRate[key][index].isCustom;
    //   if (isCustom) {
    //     if (end < start) {
    //       this.fourRate[key][index].end = null;
    //     }
    //   } else {
    //     // 修改当前值 如果后面值小于当前值则置为空
    //     this.fourRate[key].forEach((item, curIndex) => {
    //       if (curIndex > index && !item.isCustom) {
    //         if (item.start > start) {
    //           item.start = null;
    //         }
    //       }
    //     });
    //   }
    // },

    changeFourRate(i, index, type = 'start') {
      const start = this.fourRateRule.fourRate[i].data[index].start || 0;
      const end = this.fourRateRule.fourRate[i].data[index].end || 0;

      // 自定义选项
      if (this.fourRateRule.fourRate[i].data[index].isCustom) {
        let item = this.fourRateRule.fourRate[i].data[index];
        if (start > end) {
          if (type == 'end') {
            item.start = undefined;
          } else {
            item.end = undefined;
          }
        }
      } else {
        // 修改当前值 如果后面值小于当前值则置为空
        for (let curIndex = 0; curIndex < index; curIndex++) {
          const item = this.fourRateRule.fourRate[i].data[curIndex];
          if ((item.start || 0) < (start || 0) && !item.isCustom) {
            item.start = undefined;
            item.end = undefined;
          }
        }

        for (
          let curIndex = index;
          curIndex < this.fourRateRule.fourRate[i].data.length;
          curIndex++
        ) {
          const item = this.fourRateRule.fourRate[i].data[curIndex];
          if (item.isCustom) break;
          if (type == 'end') {
            if ((item.start || 0) > (end || 0)) {
              item.start = undefined;
            }
          } else if ((item.start || 0) > (start || 0)) {
            item.start = undefined;
            item.end = undefined;
          } else if ((item.end || 0) < (item.start || 0)) {
            item.end = undefined;
          }
        }
      }

      // 更新每段区间
      for (let curIndex = index; curIndex < this.fourRateRule.fourRate[i].data.length; curIndex++) {
        let item = this.fourRateRule.fourRate[i].data[curIndex];
        let nextItem = this.fourRateRule.fourRate[i].data[curIndex + 1];
        if (nextItem && !nextItem.isCustom) {
          nextItem.end = item.start;
        }
      }
    },

    // this.fourRateRule.fourRate[i].list.forEach((item, curIndex) => {
    //   if(item.end < item.start) {
    //     item.end = item.start;
    //   }
    // })
    // this.fourRateRule.fourRate[i].list.forEach((item, curIndex) => {

    // this.fourRateRule.fourRate[i].list.forEach((item, curIndex) => {
    //   if (type == 'end') {
    //     if (!item.isCustom) {
    //       if (item.start > end) {
    //         item.start = undefined;
    //       }
    //     }
    //   } else {
    //     if (curIndex > index && !item.isCustom) {
    //       if (item.start > start) {
    //         item.start = undefined;
    //       }
    //     }
    //   }
    // });

    /**
     * @name:改变等级分布开始值时校验
     */
    changelevelDist(key, index) {
      const start = this.levelDist[key][index].start;
      // 修改当前值 如果后面值小于当前值则置为空
      this.levelDist[key].forEach((item, curIndex) => {
        if (curIndex > index && !item.isCustom) {
          if (item.start > start) {
            item.start = null;
          }
        }
      });
    },
    /**
     * @name: 初始化分数线配置
     */
    // initOnline() {
    //   let onlineLen = 0;
    //   this.onlineName = [];
    //   if (this.onlineForm.tableData && this.onlineForm.tableData.length > 0) {
    //     this.onlineName = this.onlineForm.tableData[0].names ? this.onlineForm.tableData[0].names : ['分数线1', '分数线2', '分数线3', '分数线4'];
    //     onlineLen = this.onlineForm.tableData[0].value ? this.onlineForm.tableData[0].value.length : 0;
    //   }
    //   this.onlineList = [];
    //   for (let i = 0; i < onlineLen; i++) {
    //     const indexName = i + 1;
    //     this.onlineList.push({
    //       id: i,
    //       name: this.onlineName[i],
    //       key: "online" + indexName,
    //       rate: 100 - indexName * 10,
    //     });
    //   }
    // this.tempOnlineList = this.onlineList.concat();

    // },
    /**
     * @name:初始化目标设置
     */
    initTargetData() {
      let onlineLen = 0;
      if (this.targetForm.tableData && this.targetForm.tableData.length > 0) {
        onlineLen = this.targetForm.tableData[0].value.length;
      }
      this.targetList = [];
      for (let i = 0; i < onlineLen; i++) {
        const indexName = i + 1;
        this.targetList.push({
          id: i,
          name: this.onlineForm.tableData[0].names
            ? this.onlineForm.tableData[0].names[i]
            : ['分数线1', '分数线2', '分数线3', '分数线4'][i],
          key: 'taget' + indexName,
        });
      }
      this.tempTargetList = this.targetList.concat();
    },
    /**
     * @name: 添加分数线指标
     */
    addOnlineTarget() {
      const id = this.tempOnlineList.length;
      const indexName = id + 1;
      this.tempOnlineList.push({
        name: (this.selectedOption == 1 ? '分数线' : '名次') + indexName,
        rate: this.selectedOption == 1 ? 100 - indexName * 10 : '',
      });
    },
    /**
     * @name: 删除分数线指标
     */
    delOnlineTarget() {
      // 删除分数线对应数据
      this.onlineForm.tableData.forEach(item => {
        if (item.value.length > 1 && item.value.length === this.tempOnlineList.length)
          item.value.pop();
      });
      this.tempOnlineList.pop();
    },
    /**
     * @name:打开设置分数线名称弹窗
     */
    opeanEditDialog() {
      this.isShowEditDialog = true;
    },
    /**
     * @name:确认设置分数线
     */
    confirmSetOnline(onlieData) {
      this.isShowEditDialog = false;
      // 保留onlieData的name与rate，去除其他属性
      this.tempOnlineList = onlieData.map(item => ({
        name: item.name,
        rate: item.rate,
      }));
      this.tempOnlineList.forEach((item, index) => {
        this.onlineForm.tableData.forEach(ite => {
          this.$set(ite.names, index, item.name); //分数线名
          this.$set(ite.value, index, (ite.defaultValue * (item.rate / 100)).toFixed(1)); //分数线
        });
      });
      this.$refs.onlineRef.clearValidate();
    },
    /**
     * @name:关闭弹窗
     */
    closeDialog() {
      this.isShowEditDialog = false;
    },
    /**
     * @name: 添加目标分数线指标
     */
    addTarget() {
      const id = this.tempTargetList[this.tempTargetList.length - 1].id + 1;
      const indexName = id + 1;
      this.tempTargetList.push({
        id: id,
        name: '分数线' + indexName,
        key: 'target' + indexName,
      });
    },
    /**
     * @name: 删除目标分数线指标
     */
    delTarget() {
      // 删除分数线对应数据
      this.targetForm.tableData.forEach(item => {
        if (item.value.length > 1 && item.value.length === this.tempTargetList.length)
          item.value.pop();
      });
      this.tempTargetList.pop();
    },
    /**
     * @name:保存设置
     */
    save() {
      const fourRate = new Promise((resolve, reject) => {
        this.$refs.fourRateRef.validate((valid, message) => {
          if (valid) {
            resolve(this.fourRateRule.fourRate);
          } else {
            reject(message);
          }
        });
      });
      const levelDist = new Promise((resolve, reject) => {
        this.$refs.levelDistRef.validate((valid, message) => {
          if (valid) {
            resolve(this.levelDist);
          } else {
            reject(message);
          }
        });
      });
      const poorStu = new Promise((resolve, reject) => {
        this.$refs.poorStuRef.validate((valid, message) => {
          if (valid) {
            resolve(this.poorStuForm);
          } else {
            reject(message);
          }
        });
      });
      const online = new Promise((resolve, reject) => {
        this.$refs.onlineRef.validate((valid, message) => {
          if (valid) {
            resolve(this.onlineForm);
          } else {
            reject(message);
          }
        });
      });
      const crit = new Promise((resolve, reject) => {
        this.$refs.critStuRef.validate((valid, message) => {
          if (valid) {
            resolve(this.critStu);
          } else {
            reject(message);
          }
        });
      });
      const targetSet = new Promise((resolve, reject) => {
        this.$refs.targetRef.validate((valid, message) => {
          if (valid) {
            resolve(this.targetForm);
          } else {
            reject(message);
          }
        });
      });
      const arr = [fourRate, levelDist, poorStu, online, crit, targetSet];
      Promise.all(arr)
        .then(async () => {
          const obj = {};
          obj.fiveRate = this.fourRateRule.fourRate; //一分五率
          obj.dist = this.levelDist.school; //等级分布
          obj.poolStu =
            this.poorStuForm.rule == 1 ? this.poorStuForm.rankSchool : this.poorStuForm.scoreSchool; //优困生
          obj.lineType = this.selectedOption;
          obj.fracLine = this.onlineForm.tableData; //上线分析
          obj.critStu = this.critStu; //临界生
          obj.tarLine = this.targetForm.tableData;
          obj.lineRate = this.selectedOption == 1 ? this.tempOnlineList : ''; //上线分析比例
          obj.lvType = this.lvType; //等级类型
          if (this.isAllSubject) {
            this.avgStat.min = this.subjectNum;
          } else {
            this.avgStat.min = -1;
          }
          if (this.isZero) {
            this.avgStat.zero = 1;
          } else {
            this.avgStat.zero = -1;
          }
          obj.avgStat = this.avgStat;
          // 处理上线设置分数字符串转数字
          this.converScoreTypeOf(obj.fracLine);
          this.converValueTypeOf(obj.tarLine);
          // 区间数值赋值
          obj.fiveRate.forEach(item => {
            this.copyEndVal(item.data, 'end', 'start');
          });
          this.copyEndVal(obj.dist, 'end', 'start');
          const json = JSON.stringify(obj);
          setAllConfAPI({
            examId: this.customExamId
              ? this.customExamId
              : this.$sessionSave.get('reportDetail').examId,
            type: 100,
            content: json,
          }).then(res => {
            const reportDetail = this.$sessionSave.get('reportDetail');
            this.$sessionSave.set('reportDetail', {
              ...reportDetail,
              v: reportDetail.v + 1,
            });
            this.$message({
              message: '保存成功！',
              type: 'success',
              duration: 1500,
            });
            this.$emit('updateActiveStep', this.activeStep + 1, this.customExamId);
          });
        })
        .catch(err => {
          console.error(err);
        });
    },
    /**
     * @name 保存时将四率设置非第一个元素的结束值赋值为上一个元素的开始值
     */
    copyEndVal(data, targetKey, sourceKey, key) {
      data.forEach((item, index) => {
        if (key === 'level') {
          item.name = this.letter[index];
        }
        if (index > 0 && !item.isCustom) {
          item[targetKey] = data[index - 1][sourceKey];
        }
      });
    },
    /**
     * @name 处理上线设置分值字符串转数字
     */
    converScoreTypeOf(data) {
      data.forEach(item => {
        const newValue = [];
        item.value.forEach(subItem => {
          newValue.push(Number(subItem));
        });
        item.value = newValue;
      });
    },
    /**
     * @name 处理目标设置分值字符串转数字
     */
    converValueTypeOf(data) {
      data.forEach(item => {
        const newvalue = [];
        item.value.forEach(subItem => {
          newvalue.push(Number(subItem));
        });
        item.value = newvalue;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.target-table td {
  padding: 14px 36px;
}

.target-container {
  .content-box {
    padding: 0 20px;

    .content-main {
      position: relative;

      .content-form {
        width: 100%;
      }

      .content-from-label {
        display: inline-block;
        max-width: 5em;
        margin-right: 1em;
        font-size: 14px;
        line-height: 40px;
        color: #606266;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
      }
    }
  }
}

.target-table {
  width: 100%;
  border: 1px solid #e4e8eb;
  border-collapse: collapse;
  border-spacing: 0;

  thead {
    height: 64px;
    background: #f7fafc;
    border-radius: 6px 6px 0px 0px;

    th {
      text-align: center;
      font-weight: bold;
      color: #3f4a54;
      border: unset;
      border-right: 1px solid #e4e8eb;
    }
  }

  tr {
    height: 60px;

    td {
      text-align: center;
      border: 1px solid #e4e8eb;
    }
  }

  .icon-item {
    vertical-align: unset;

    &:nth-child(2) {
      margin-left: 28px;
    }

    &:nth-child(3) {
      margin-left: 28px;
    }
  }

  .icon-del {
    background: url('../../../assets/icon_shanchu.png');
  }

  .icon-addItem {
    width: 18px;
    height: 18px;
    background: url('../../../assets/icon-add.png');
    margin-left: unset !important;
  }
}

.set-online {
  display: flex;
  margin-bottom: 30px;
}

.set-online-input {
  width: 20%;
  margin-left: 20px;
  margin-right: 10px;
}

.w-60 {
  width: 60px;
  margin-right: 10px;
}

.target-container {
  ::v-deep .el-form-item {
    .el-form-item__label {
      font-size: 14px;
    }
  }
}
</style>
<style lang="scss" lang="scss">
.custom-col .el-form-item {
  display: inline-block;
}

.custom-col .el-form-item__content {
  width: fit-content;
}

.target-table .el-input {
  width: unset;
}

.target-container {
  font-size: 16px !important;
  padding-bottom: 80px !important;

  .el-form-item {
    margin-bottom: 0px !important;
  }

  .el-input__inner {
    text-align: center;
  }

  .tip-txt,
  .title-tips {
    color: #fe5d50;
    font-weight: bold;
  }

  .tip-txt {
    padding: 0 20px;
  }

  .title {
    position: relative;
    margin: 20px 0;
    padding: 0 10px;
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;

    &:before {
      overflow: hidden;
      position: absolute;
      left: 0;
      display: block;
      content: '';
      width: 4px;
      height: 24px;
      top: -3px;
      background: #409eff;
      border-radius: 3px;
    }

    .sub-title {
      color: #7e94a8;
      font-weight: 400;
      margin-left: 10px;
    }
  }

  .m-10 {
    margin-bottom: 10px;
  }

  .btn_item {
    height: 30px;
    width: 70px;
    padding: unset;
  }

  .save-btn {
    position: absolute;
    right: 20px;
    text-align: center;
    margin-top: 20px;

    .el-button {
      width: 136px;
      height: 36px;
    }
  }

  .icon-del {
    background: url('../../../assets/icon_delete.png');
    margin-top: -2px;
  }

  .icon-addItem {
    margin-left: 20px;
    background: url('../../../assets/icon-add.png');
  }

  .icon-edit {
    width: 19px !important;
    background: url('../../../assets/icon_edit.png');
  }

  .icon-item {
    width: 16px;
    height: 18px;
    display: inline-block;
    vertical-align: middle;
  }

  .content-box {
    padding: 0 20px;

    .content-main {
      position: relative;

      margin-top: 20px;

      .content-form {
        width: 92%;
      }
    }
  }

  .tool-sec {
    position: absolute;
    right: 0;
    top: 10px;
    display: inline-block;
  }

  .custom-sec {
    float: right;
    margin-right: 25px;
    margin-top: 20px;
  }

  .custom-form-item {
    margin-right: unset !important;
  }

  .btn_del {
    border-color: #409eff !important;
    color: #409eff !important;
  }

  .col-title {
    margin-right: unset !important;
  }
}

.m-2 {
  // margin-top: 10px !important;
}

.clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: ' ';
  clear: both;
  height: 0;
}
</style>

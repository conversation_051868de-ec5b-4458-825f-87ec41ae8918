<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2022-03-18 15:57:19
 * @LastEditTime: 2025-06-09 15:22:21
 * @LastEditors: 小圆
-->

<template>
  <div class="phone-form pointer-auto">
    <!-- 手机号 -->
    <el-input
      ref="accountInput"
      class="phone-form__input"
      :maxlength="11"
      oninput="this.value = this.value.replace(/[^0-9]/g, '');"
      placeholder="请输入手机号码"
      v-model="phoneNumber"
      pattern=".*\S.*"
      required
      autocomplete="off"
    >
      <i slot="prefix" class="el-input__icon iconfont icon-shouji"></i>
    </el-input>

    <!-- 验证码 -->
    <el-input
      ref="psdInput"
      class="phone-form__input phone-form__input--code"
      oninput="this.value = this.value.replace(/[^0-9]/g, '');"
      :maxlength="6"
      placeholder="请输入验证码"
      v-model="pin"
      autocomplete="off"
    >
      <i slot="prefix" class="el-input__icon iconfont icon-yanzhengma"></i>
      <span
        slot="suffix"
        class="el-input__text--code click-element"
        :class="{ '_text-disable': phoneNumber.length !== 11 }"
        @click="getPinCode"
        v-show="!hasPinCode"
        >获取验证码</span
      >
      <span slot="suffix" class="el-input__text--code _black" v-show="hasPinCode">{{ waitTime }}秒后重试</span>
    </el-input>

    <!-- 登录按钮 -->
    <el-button class="next-button click-element" type="primary" :disabled="disableNext" @click="next">下一步</el-button>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import md5 from 'js-md5';
import { checkSMSCodeAPI, getSMSCodeAPI, getVerificationIdAPI } from '@/service/api';
import UseForgetPwdState from './UseLoginState';

@Component({
  components: {},
})
export default class Phone extends Vue {
  @Prop() guid: any;

  // 手机号码
  phoneNumber: any = '';
  // 验证码
  pin: any = '';
  // 固定字符串
  fiexCode: any = 'datedu6738b274c68384f3';
  // 最终校验码 guid+fiexCode
  encryptedCodeMD5: any = '';
  // 腾讯图片验证应用ID
  appid: any = '';
  // 腾讯图片验证应用对象
  captcha: any = null;

  UseForgetPwdState = UseForgetPwdState;

  get hasPinCode() {
    return this.UseForgetPwdState.hasPinCode;
  }

  get waitTime() {
    return this.UseForgetPwdState.waitTime;
  }

  async mounted() {
    this.appid = JSON.stringify((await getVerificationIdAPI({})).data); // 获取应用appid
  }

  get disableNext() {
    return !this.phoneNumber || !this.pin;
  }

  /**
   * @description: 获取手机验证码
   */
  async getPinCode() {
    if (this.phoneNumber == '') {
      this.$message.warning('请输入手机号');
      return;
    }
    if (!this.checkPhone(this.phoneNumber)) {
      this.$message.warning('请输入正确的手机号');
      return;
    }
    if (!this.guid) {
      this.$message.warning('服务器错误');
      return;
    }
    await this.setCaptcha();
  }

  /**
   * @description: 设置验证码
   * @return {*}
   */
  async setCaptcha() {
    this.encryptedCodeMD5 = md5(this.guid + this.fiexCode); // 生成guid和固定字符串的md5校验
    this.captcha = new (window as any).TencentCaptcha(
      this.appid,
      async res => {
        // 用户滑动结束或者关闭弹窗，腾讯返回的内容
        console.log(res);
        if (res.ret === 0) {
          //成功，传递数据给后台进行验证
          console.log('成功', res);
          const verificationCode = res.ticket;
          const randstr = res.randstr;
          // 发送手机短信验证码
          try {
            await getSMSCodeAPI({
              guid: this.guid,
              guidEncrypted: this.encryptedCodeMD5,
              mobile: this.phoneNumber,
              isRegister: 0, // 0找回密码，1注册用户
              userType: 1, // 0注册，1教师，2学生，3家长
              verificationCode: verificationCode,
              randstr: randstr,
            });

            this.UseForgetPwdState.startTimer();
            this.$message.success('验证码已发送');
          } catch (error) {
            console.log(error);
            // this.$message.error(error.msg);
          }
        } else {
          // 提示用户完成验证
        }
      },
      {
        needFeedBack: false,
      }
    );
    // 滑块显示
    this.captcha.show();
  }

  /**
   * @description: 下一步
   */
  async next() {
    if (this.phoneNumber == '') {
      this.$message.warning('请输入手机号');
      return;
    }
    if (!this.checkPhone(this.phoneNumber)) {
      this.$message.warning('请输入正确的手机号');
      return;
    }

    try {
      await checkSMSCodeAPI({
        guid: this.guid,
        mobile: this.phoneNumber,
        mobilecode: this.pin, // 验证码
        isRegister: 0, // 0：找回密码 1：注册使用
        userType: 1, // 0：注册用户，1：教师，2：学生，3：家长
      });
      this.$emit('nextPass', true, this.phoneNumber);
    } catch (error) {
      console.log(error);
      // this.$message.error(error.msg);
    }
  }

  /**
   * @description: 校验手机号码规则
   * @param {*} val
   */
  checkPhone(val) {
    return /^1[3-9][0-9]{9}$/.test(val);
  }
}
</script>
<style lang="scss" scoped>
.phone-form {
  width: 360px; // vw(360, $win_width)
  margin: 0 auto;

  .phone-form__input {
    margin-bottom: 22px; // vw(22, $win_width)
  }

  ._text-disable {
    color: #bfcadb;
    pointer-events: none;
  }
}

.el-input__text--code {
  color: #1e6ffe;
  font-size: 16px; // vw(16, $win_width)
}

._black {
  color: #bfcadb;
}

.next-button {
  display: block;
  margin: 20px auto 50px; // vh(20, $win_height) auto vh(50, $win_height)
  width: 100%;
  height: 48px; // vw(48, $win_width)
  border: none;
  border-radius: 50px; // vw(50, $win_width)
  font-size: 16px; // vw(18, $win_width)
}

.form-validate {
  color: #fe5d50;
}
</style>

<style lang="scss">
.phone-form {
  .phone-form__input {
    margin-bottom: 22px; // vw(22, $win_width)

    &.active {
      .el-input__inner {
        border-color: #2574ff;
        color: #2574ff;

        ~ .el-input__prefix {
          .el-input__icon {
            color: #2574ff;
          }
        }
      }
    }

    .el-input__inner {
      height: 48px; // vw(48, $win_width)
      padding: 0 55px; // vw(55, $win_width)
      border-radius: 50px; // vw(50, $win_width)
      font-size: 16px; // vw(18, $win_width)
      line-height: 50px; // vw(50, $win_width)
      letter-spacing: 2px;
      color: #2a3034;

      &:focus {
        caret-color: #2574ff;

        ~ .el-input__prefix {
          .el-input__icon {
            color: #2574ff;
          }
        }

        ~ .el-input__suffix {
          .el-input__icon {
            &.icon-keyboard {
              opacity: 1;
            }
          }
        }
      }
    }

    .el-input__icon {
      line-height: 47px; // vw(47, $win_width)
      color: #bfcadb;

      &.el-icon-arrow-up {
        color: #2574ff;
      }
    }

    .el-input__prefix {
      pointer-events: none;
      left: 20px; // vw(20, $win_width)

      .el-input__icon {
        font-size: 16px; // vw(32, $win_width)
      }
    }

    .el-input__suffix {
      right: 20px; // vw(20, $win_width)

      &:hover {
        .el-input__icon {
          color: #2574ff;
        }
      }

      .el-input__icon {
        display: inline-block;
        vertical-align: middle;
        margin-left: 5px; // vw(5, $win_width)
        line-height: 48px; // vw(48, $win_width)
        right: 20px; // vw(20, $win_width)
        font-size: 16px; // vw(22, $win_width)

        &.icon-keyboard {
          opacity: 0;
          font-size: 16px; // vw(24, $win_width)
        }

        &.active {
          color: #2574ff;
        }
      }
    }
  }

  .phone-form__input--code {
    .el-input__suffix {
      display: flex;
      align-items: center;
    }
    .el-input__inner {
      letter-spacing: 2px;
    }
  }
}
</style>

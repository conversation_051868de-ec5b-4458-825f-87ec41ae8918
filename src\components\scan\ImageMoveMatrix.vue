<template>
  <div
      @mousedown="mouseDown"
      @mouseup="mouseUp"
      @mousemove="mouseMove"
      @wheel="zoomWhell">
  </div>
</template>

<script>
import Matrix2D from "matrix2d.js";

export default {
  props: {
    //题目
    anchor_list: {
      type: Array,
      default: [],
    },
    scale: {
      type: Number,
      default: 1
    },
    image_url: {
      type: String,
      default: ''
    },
    imageMatrix: {
      type: Matrix2D,
      default: null
    },
    offsetX: {
      type: Number,
      default: 0
    },
    offsetY: {
      type: Number,
      default: 0
    }
  },

  data() {
    return {
      currentImageMatrix: null,
      fsUrl: process.env.VUE_APP_FS_URL,
      isDragging: false,
      move_pos: 0,
      move_type: 0, //0：移动图片，1：移动定位点
      pt2_angle: 90,
      pt3_angle: 90,
      pt3_distance: 0,
      imagePt: {
        x: 0,
        y: 0
      },
      startPt: {
        x: 0,
        y: 0
      },
      endPt: {
        x: 0,
        y: 0
      }
    };
  },

  mounted() {

  },

  methods: {
    getDistance(x1, y1, x2, y2) {
      let dx = x1 - x2
      let dy = y1 - y2
      return Math.sqrt(dx * dx + dy * dy)
    },
    getAnglePoint(angle, distance) {
      return {
        x: distance * Math.cos(angle),
        y: distance * Math.sin(angle)
      }
    },
    getRotatePoint(start, end, angle, distance) {
      let start_angle = Math.atan2(end.dest_y - start.dest_y, end.dest_x - start.dest_x)
      let end_angle = start_angle + angle
      return {
        x: distance * Math.cos(end_angle) + start.dest_x,
        y: distance * Math.sin(end_angle) + start.dest_y
      }
    },
    getAffineTransform(points1, points2) {
      let a, b, c, d, tx, ty;

      a = (points2[0].x * points1[1].y + points2[1].x * points1[2].y + points2[2].x * points1[0].y - points2[0].x * points1[2].y - points2[1].x * points1[0].y - points2[2].x * points1[1].y) / (points1[0].x * points1[1].y + points1[1].x * points1[2].y + points1[2].x * points1[0].y - points1[0].x * points1[2].y - points1[1].x * points1[0].y - points1[2].x * points1[1].y);
      b = (points2[0].y * points1[1].y + points2[1].y * points1[2].y + points2[2].y * points1[0].y - points2[0].y * points1[2].y - points2[1].y * points1[0].y - points2[2].y * points1[1].y) / (points1[0].x * points1[1].y + points1[1].x * points1[2].y + points1[2].x * points1[0].y - points1[0].x * points1[2].y - points1[1].x * points1[0].y - points1[2].x * points1[1].y);
      c = (points1[0].x * points2[1].x + points1[1].x * points2[2].x + points1[2].x * points2[0].x - points1[0].x * points2[2].x - points1[1].x * points2[0].x - points1[2].x * points2[1].x) / (points1[0].x * points1[1].y + points1[1].x * points1[2].y + points1[2].x * points1[0].y - points1[0].x * points1[2].y - points1[1].x * points1[0].y - points1[2].x * points1[1].y);
      d = (points1[0].x * points2[1].y + points1[1].x * points2[2].y + points1[2].x * points2[0].y - points1[0].x * points2[2].y - points1[1].x * points2[0].y - points1[2].x * points2[1].y) / (points1[0].x * points1[1].y + points1[1].x * points1[2].y + points1[2].x * points1[0].y - points1[0].x * points1[2].y - points1[1].x * points1[0].y - points1[2].x * points1[1].y);
      tx = (points1[0].x * points1[1].y * points2[2].x + points1[1].x * points1[2].y * points2[0].x + points1[2].x * points1[0].y * points2[1].x - points1[0].x * points1[2].y * points2[1].x - points1[1].x * points1[0].y * points2[2].x - points1[2].x * points1[1].y * points2[0].x) / (points1[0].x * points1[1].y + points1[1].x * points1[2].y + points1[2].x * points1[0].y - points1[0].x * points1[2].y - points1[1].x * points1[0].y - points1[2].x * points1[1].y);
      ty = (points1[0].x * points1[1].y * points2[2].y + points1[1].x * points1[2].y * points2[0].y + points1[2].x * points1[0].y * points2[1].y - points1[0].x * points1[2].y * points2[1].y - points1[1].x * points1[0].y * points2[2].y - points1[2].x * points1[1].y * points2[0].y) / (points1[0].x * points1[1].y + points1[1].x * points1[2].y + points1[2].x * points1[0].y - points1[0].x * points1[2].y - points1[1].x * points1[0].y - points1[2].x * points1[1].y);

      return [a, b, c, d, tx, ty];
    },
    getAngle(x1, y1, x2, y2) {
      const dot = x1 * x2 + y1 * y2
      const det = x1 * y2 - y1 * x2
      const angle = Math.atan2(det, dot) / Math.PI * 180
      // console.log('angle:' + angle)
      return Math.atan2(det, dot)
    },
    mouseDown(e) {
      console.log('imageMoveDown')
      if (e.button !== 0) return
      e.stopPropagation()
      this.isDragging = true;

      this.startPt.x = e.clientX
      this.startPt.y = e.clientY
      this.endPt.x = e.clientX
      this.endPt.y = e.clientY
      let imageX = e.offsetX
      let imageY = e.offsetY


      let min_pos = -1
      let anchor0 = this.anchor_list[0]

      for (let i = 0; i < 4; i++) {
        let anchor = this.anchor_list[i]
        let dis = this.getDistance(anchor.dest_x + this.offsetX, anchor.dest_y + this.offsetY, imageX, imageY)
        anchor.src_x = anchor.dest_x
        anchor.src_y = anchor.dest_y
        // console.log('dis_' + i + "," + dis)
        if (dis < 50) {
          min_pos = i
        }

        if (i > 0) {
          anchor.angle = Math.atan2(anchor.dest_y - anchor0.dest_y, anchor.dest_x - anchor0.dest_x)
          anchor.distance = this.getDistance(anchor.dest_x, anchor.dest_y, anchor0.dest_x, anchor0.dest_y)
        }
      }
      let anchor1 = this.anchor_list[1]
      let anchor2 = this.anchor_list[2]
      let anchor3 = this.anchor_list[3]

      this.pt2_angle = this.getAngle(anchor0.dest_x - anchor1.dest_x, anchor0.dest_y - anchor1.dest_y,
          anchor2.dest_x - anchor1.dest_x, anchor2.dest_y - anchor1.dest_y)
      this.pt3_angle = this.getAngle(anchor1.dest_x - anchor0.dest_x, anchor1.dest_y - anchor0.dest_y,
          anchor3.dest_x - anchor0.dest_x, anchor3.dest_y - anchor0.dest_y)
      this.pt3_distance = this.getDistance(anchor3.dest_x, anchor3.dest_y, anchor0.dest_x, anchor0.dest_y)
      // console.log(this.anchor_list)
      // console.log(this.pt3_angle)
      if (min_pos >= 0) {
        this.move_type = 1
        this.move_pos = min_pos
      } else {
        this.move_type = 0
      }

      document.addEventListener("mousemove", this.mouseMove);
      document.addEventListener("mousedown", this.mousedown);
      document.addEventListener("mouseup", this.mouseUp);

    },
    /**
     * @name：鼠标移动
     */
    mouseMove(e) {
      if (!this.isDragging) return

      if (this.move_type === 0) {
        this.$emit('onMatrixImageMove', {x: e.clientX - this.endPt.x, y: e.clientY - this.endPt.y})
        this.endPt.x = e.clientX
        this.endPt.y = e.clientY

        return
      }

      let moveX = e.clientX - this.startPt.x; // x向移动距离
      let moveY = e.clientY - this.startPt.y; // y向移动距离

      moveX = moveX / this.scale
      moveY = moveY / this.scale

      let srcCorners = [];
      let dstCorners = [];
      let pos = this.move_pos
      if (pos === 0) {
        // for (let i = 0; i < 4; i++) {
        //   this.anchor_list[i].dest_x = this.anchor_list[i].src_x + moveX
        //   this.anchor_list[i].dest_y = this.anchor_list[i].src_y + moveY
        // }
        let anchor0 = this.anchor_list[0]
        let anchor1 = this.anchor_list[1]
        let anchor2 = this.anchor_list[2]
        let anchor3 = this.anchor_list[3]
        anchor0.dest_x = anchor0.src_x + moveX
        anchor0.dest_y = anchor0.src_y + moveY


        let newPt2 = this.getRotatePoint(anchor1, anchor0, this.pt2_angle, this.pt3_distance)
        let newPt3 = this.getRotatePoint(anchor0, anchor1, this.pt3_angle, this.pt3_distance)

        anchor2.dest_x = newPt2.x
        anchor2.dest_y = newPt2.y

        anchor3.dest_x = newPt3.x
        anchor3.dest_y = newPt3.y

      } else if (pos === 1) {
        let anchor0 = this.anchor_list[0]
        let anchor1 = this.anchor_list[1]
        let anchor2 = this.anchor_list[2]
        let anchor3 = this.anchor_list[3]
        anchor1.dest_x = anchor1.src_x + moveX
        anchor1.dest_y = anchor1.src_y + moveY

        let angle_change = Math.atan2(anchor1.dest_y - anchor0.dest_y, anchor1.dest_x - anchor0.dest_x) - anchor1.angle
        let scale = this.getDistance(anchor1.dest_x, anchor1.dest_y, anchor0.dest_x, anchor0.dest_y) / anchor1.distance

        let pt2 = this.getAnglePoint(anchor2.angle + angle_change, anchor2.distance * scale)
        anchor2.dest_x = anchor0.dest_x + pt2.x
        anchor2.dest_y = anchor0.dest_y + pt2.y

        let pt3 = this.getAnglePoint(anchor3.angle + angle_change, anchor3.distance * scale)
        anchor3.dest_x = anchor0.dest_x + pt3.x
        anchor3.dest_y = anchor0.dest_y + pt3.y
      } else {
        this.anchor_list[2].dest_x = this.anchor_list[2].src_x + moveX
        this.anchor_list[2].dest_y = this.anchor_list[2].src_y + moveY

        this.anchor_list[3].dest_x = this.anchor_list[3].src_x + moveX
        this.anchor_list[3].dest_y = this.anchor_list[3].src_y + moveY
      }

      for (let i = 0; i < 3; i++) {
        let it = this.anchor_list[i]

        srcCorners.push({
          x: it.cx,
          y: it.cy
        })
        dstCorners.push({
          x: it.dest_x,
          y: it.dest_y
        })
      }

      // console.log(srcCorners)
      // console.log(dstCorners)
      let matrix = this.getAffineTransform(srcCorners, dstCorners)
      let imageMatrix = new Matrix2D(matrix[0], matrix[1], matrix[2], matrix[3], matrix[4], matrix[5])
      // console.info(imageMatrix)
      this.currentImageMatrix = imageMatrix
      this.$emit('onMatrixChange', imageMatrix)

    },
    mouseUp(e) {
      this.mouseMove(e)
      this.isDragging = false;
      document.removeEventListener("mousemove", this.mouseMove);
      document.removeEventListener("mouseup", this.mouseUp);
      let anchor0 = this.anchor_list[0]
      let anchor1 = this.anchor_list[1]
      // console.log(`x:${anchor1.dest_y - anchor0.dest_x},y:${anchor1.dest_x - anchor0.dest_y}`)
      // console.log(this.anchor_list[2])
      this.$emit('onMatrixChangeEnd')


    },
    /**
     * @name:滚轮缩放
     */
    zoomWhell(e) {
      e.stopPropagation()
      let imageX = e.offsetX
      let imageY = e.offsetY

      // if (this.divMatrix) {
      //   let pt = this.divMatrix.transformPoint(imageX, imageY);
      //   imageX = pt.x
      //   imageY = pt.y
      // }

      if (e.deltaY < 0) {
        // 放大DELTA倍
        this.$emit('onZoomWheel', 'add', imageX, imageY)
      } else {
        // 缩小DELTA倍
        this.$emit('onZoomWheel', 'sub', imageX, imageY)
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.image-move-matrix {
  position: relative;
}

.point-anchor-pos {
  position: absolute;
  width: 5px;
  height: 5px;
  background: #f00;
  border-radius: 5px;
}
</style>

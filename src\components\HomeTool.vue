<template>
  <div class="tools" id="draggableTool">
    <div
      v-if="$route.path.includes('/reportDetail') && (isOperation || isSchoolAdmin)"
      class="tool-item"
      title="工具"
      @click="handleTool"
    >
      <img class="tool-icon" :src="toolIcon" />
      <div class="tool-text">工具</div>
    </div>
    <div v-if="!$route.path.includes('/help')" class="tool-item" title="帮助中心" @click="gotoHelp">
      <img class="tool-icon" :src="helpIcon" />
      <div class="tool-text">帮助</div>
    </div>
    <div class="tool-item" title="AI客服" @click="gotoAIHelp">
      <img class="tool-icon" :src="aiIcon" />
      <div class="tool-text">客服</div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import { isCef } from '@/utils/index';
import UserRole from '@/utils/UserRole';

@Component
export default class HomeTool extends Vue {
  // 帮助图标
  helpIcon = require('@/assets/icon_help.png');
  // 工具图表
  toolIcon = require('@/assets/icon_tool.png');
  aiIcon = require('@/assets/icon_aihelp.png');
  // 是否大屏环境
  isCef = isCef();

  isOperation = UserRole.isOperation; // 运营
  isSchoolAdmin = UserRole.isSchoolLeader; // 校管
  // 工具DOM
  draggableTool: HTMLDivElement = null;

  startTime = null;
  isClick = true;
  isDragging = false;
  offsetX = 0;
  offsetY = 0;

  @Watch('$route.path')
  onRouteChange() {
    this.isOperation = UserRole.isOperation;
    this.isSchoolAdmin = UserRole.isSchoolLeader;
  }
  mounted() {
    this.draggableTool = document.getElementById('draggableTool') as HTMLDivElement;
    window.addEventListener('resize', this.constrainLimits);
    this.draggableTool.addEventListener('mousedown', this.handleDown);
  }

  beforeDestroy() {
    window.removeEventListener('resize', this.constrainLimits);
    this.draggableTool.removeEventListener('mousedown', this.handleDown);
  }

  handleDown(e: MouseEvent) {
    e.preventDefault();
    this.startTime = Date.now();
    this.isClick = true;
    this.isDragging = true;
    this.offsetX = e.clientX - this.draggableTool.offsetLeft;
    this.offsetY = e.clientY - this.draggableTool.offsetTop;

    document.addEventListener('mousemove', this.handleMove);
    document.addEventListener('mouseup', this.handleUp);
  }

  handleMove(e: MouseEvent) {
    if (this.isDragging) {
      e.preventDefault();
      const x = e.clientX - this.offsetX;
      const y = e.clientY - this.offsetY;
      this.draggableTool.style.left = `${x}px`;
      this.draggableTool.style.top = `${y}px`;
      this.constrainLimits();
    }
  }

  handleUp(e: MouseEvent) {
    const endTime = Date.now();
    const timeDiff = endTime - this.startTime;
    if (timeDiff < 200) {
      this.isClick = true;
    } else {
      this.isClick = false;
    }
    this.isDragging = false;

    document.removeEventListener('mousemove', this.handleMove);
    document.removeEventListener('mouseup', this.handleUp);
  }

  constrainLimits() {
    if (this.draggableTool.offsetLeft < 0) {
      this.draggableTool.style.left = '0px';
    }
    if (this.draggableTool.offsetTop < 0) {
      this.draggableTool.style.top = '0px';
    }
    if (this.draggableTool.offsetLeft + this.draggableTool.clientWidth > window.innerWidth) {
      this.draggableTool.style.left = `${window.innerWidth - this.draggableTool.clientWidth}px`;
    }
    if (this.draggableTool.offsetTop + this.draggableTool.clientHeight > window.innerHeight) {
      this.draggableTool.style.top = `${window.innerHeight - this.draggableTool.clientHeight}px`;
    }
  }

  // 处理工具按钮
  handleTool() {
    if (!this.isClick) return;
    this.$bus.$emit('handle-tool');
  }

  // 跳转到帮助
  gotoHelp() {
    if (!this.isClick) return;
    this.$router.push({ path: '/help' });
  }
  gotoAIHelp(){
    if (!this.isClick) return;
    this.$emit('toggleAI',true)
  }
}
</script>

<style scoped lang="scss">
.tools {
  position: fixed;
  right: 21px;
  bottom: 300px;
  width: 70px;
  height: fit-content;
  background: #ffffff;
  box-shadow: 0px 1px 7px 0px rgba(71, 136, 255, 0.14);
  border-radius: 10px;
  z-index: 9999;
  user-select: none;
  -webkit-user-select: none;
  overflow: hidden;
}

.tool-item {
  height: 80px;
  position: relative;
  text-align: center;
  cursor: pointer;

  &:not(:first-of-type) {
    &::before {
      content: '';
      position: absolute;

      top: 0;
      left: 0;
      right: 0;
      margin: auto;

      width: 50px;
      height: 1px;
      background: #f6f6f6;
      border-radius: 1px;
    }
  }

  &:hover {
    background-color: #f2f6fc;
  }
}

.tool-icon {
  margin-top: 14px;

  width: 30px;
  height: 30px;
}

.tool-text {
  margin-top: 4px;

  text-align: center;
  font-family: Microsoft YaHei;
  font-weight: 400;
  font-size: 15px;
  color: #676767;

  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>

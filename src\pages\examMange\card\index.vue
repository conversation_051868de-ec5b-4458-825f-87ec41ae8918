<template>
  <div class="card-container">
    <div class="exam-card-header">
      <el-row style="margin-bottom: 15px">
        <el-button type="primary" @click="addNewCard">新建答题卡</el-button>
        <el-button type="default" @click="uploadPaper"
          v-if="$sessionSave.get('loginInfo') && $sessionSave.get('loginInfo').account_type != 5">上传试卷制卡</el-button>
      </el-row>
      <div class="header-search-box">
        <div class="header-serarch clearfix display_flex">
          <el-input class="search__text" placeholder="输入名称搜索" v-model="filter.keyWord" @keyup.enter.native="handleSearch"
            clearable>
          </el-input>
          <div class="search-icon el-icon-search display_flex align-items_center justify-content_flex-center"
            @click="handleSearch"></div>
        </div>
      </div>
      <div class="tab-main">
        <el-tabs v-model="activeName" @tab-click="handleSearch">
          <el-tab-pane label="我的答题卡" name="my"></el-tab-pane>
          <el-tab-pane label="学校模板卡" name="school"></el-tab-pane>
        </el-tabs>
      </div>
      <el-table class="custome-table" v-loading="tableLoading" ref="tableRef" :data="tableData"
        :row-style="{ padding: 0 }" :cell-style="{ position: 'relative' }" stripe>
        <el-table-column type="index" label="序号" width="100" :key="Math.random()">
        </el-table-column>
        <el-table-column prop="tbName" label="答题卡名称" :key="Math.random()">
          <template slot="header">
            <span>{{ activeName == 'my' ? '答题卡名称' : '模板名称' }}</span>
          </template>
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.tbName" placement="top">
              <span class="showOverTooltip">
                <div class="tb-name">{{ scope.row.tbName }}<i v-if="scope.row.needCheckPassword" style="color:#ff0000;"
                    class="el-icon-lock"></i> </div>
                <div class="card-mark third" v-if="scope.row.originCardType == 3">三方卡</div>
                <div class="card-mark abCard" v-if="scope.row.abCardType == IAB_CARD_TYPE.abCard">
                  {{ scope.row.abCardSheetType ? 'B' : 'A' }}卡</div>
                <div class="card-mark abCard" v-if="scope.row.abCardType == IAB_CARD_TYPE.abPaper">AB卷</div>
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="paperNo" show-overflow-tooltip label="试卷号" width="120" :key="Math.random()">
          <template slot-scope="scope">
            <span>{{ scope.row.paperNo != '' ? scope.row.paperNo : '暂无' }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="subjectName" :key="Math.random()" show-overflow-tooltip label="学科" width="120">
        </el-table-column>
        <el-table-column prop="pageLayout" :key="Math.random()" label="页面布局" width="110">
          <template slot-scope="scope">
            <span>{{
              scope.row.pageLayout == 1
              ? 'A4'
              : scope.row.pageLayout == 2
                ? 'A3两栏'
                : scope.row.pageLayout == 3
                  ? 'A3三栏'
                  : scope.row.pageLayout == 4
                    ? '正3反2'
                    : scope.row.pageLayout == 5
                      ? '正2反3'
                      : '暂无'
            }}</span>
          </template>
        </el-table-column>
        <el-table-column :key="Math.random()" prop="qcount" label="题量" width="100" v-if="activeName == 'school'">
        </el-table-column>
        <el-table-column :key="Math.random()" prop="correctType" label="批阅类型" width="110" v-if="activeName == 'my'">
          <template slot-scope="scope">
            <span>{{
              scope.row.correctType == 1 ? '手阅' : scope.row.correctType == 2 ? '网阅' : scope.row.correctType == 3 ? '拍改' : '暂无'
            }}</span>
          </template>
        </el-table-column>
        <el-table-column :key="Math.random()" prop="createTime" label="创建时间" width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime.substr(0, 16) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="creatorName" label="创建人" width="100" :key="Math.random()" v-if="activeName == 'school'">
        </el-table-column>
        <el-table-column :key="Math.random()" prop="action" label="操作" width="230">
          <template slot-scope="scope">
            <div class="editCell display_flex align-items_center justify-content_flex-around">
              <template v-if="activeName == 'my'">
                <span v-if="scope.row.isUse == 0 && (scope.row.cardType == 1 || scope.row.fileUrl != '')
                  " placement="top">
                  <span @click="createPaper(scope.row)" :class="{ disabled: scope.row.state == -1 }" :style="{
                    color: scope.row.state == -1 ? 'red' : scope.row.state == 1 ? '#E6A23C' : '',
                  }">
                    {{
                      scope.row.state == 2 ? '制卷' : scope.row.state == -1 ? '解析失败' : '解析中'
                    }}
                  </span>
                </span>
                <span type="text" @click="goCreateCard(scope.row)" :class="{
                      disabled:
                        (scope.row.isUse == 0 && scope.row.cardType == 1) ||
                        (scope.row.originCardType == 3 && scope.row.isOverCard == 1),
                    }">
                  制卡
                </span>
                <span type="text" @click="goPreview(scope.row)" style="color: #606266;"
                  v-if="scope.row.isUse == 1 && scope.row.correctType != 3">
                  预览
                </span>

                <span @click="downloadPaper(scope.row)"
                  :class="{ disabled: scope.row.originCardType == 3 || scope.row.isUse != 1 }" style="color: #606266">
                  <span
                    v-if="scope.row.isUse == 1 && scope.row.originCardType != 3 && inArr(scope.row.source, [1, 3, 5, 14, 15])">
                    下载
                  </span>
                  <el-tooltip v-else effect="dark" content="试卷正在生成中" placement="top">
                    <p>下载</p>
                  </el-tooltip>
                </span>
              </template>
              <template v-if="activeName == 'school'">
                <span @click="goCreateCard(scope.row, true)">查看</span>
                <span @click="goCopyTemplatePaper(scope.row)">复用</span>
              </template>
              <el-dropdown
                v-if="(activeName == 'school' && (isAdmin || isCreate(scope.row.creatorId))) || activeName == 'my'"
                @command="handlerCommand">
                <span class="el-dropdown-link">
                  <i slot="reference" class="el-icon-more"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <template v-if="activeName == 'my'">
                    <!-- <el-dropdown-item :command="'supplementPaper_' + scope.$index" v-if="scope.row.isRelatedWork == 1 &&
                      (scope.row.cardType == 0 ||
                        scope.row.cardType == 3 ||
                        scope.row.supportPatchAnswer == 1) &&
                      !scope.row.subjectId.includes(',')
                      ">补录试卷
                    </el-dropdown-item> -->

                    <!-- 纯卡 -->
                    <template
                      v-if="(scope.row.cardType == 0 || scope.row.cardType == 5) && scope.row.abCardType == IAB_CARD_TYPE.default">
                      <el-dropdown-item :command="'goCopyPaper_' + scope.$index">复制
                      </el-dropdown-item>
                      <el-dropdown-item v-if="scope.row.isOverCard" :command="'setTemplate_' + scope.$index">设为模板
                      </el-dropdown-item>
                    </template>
                    <el-dropdown-item :command="'openEditDialog_' + scope.$index" v-show="scope.row.isUse == 1">修改属性
                    </el-dropdown-item>
                    <el-dropdown-item :command="'delXBResource_' + scope.$index">删除
                    </el-dropdown-item>
                  </template>
                  <!-- v-if="isAdmin || isCreate(item.createUserId)" -->
                  <template v-if="activeName == 'school' && (isAdmin || isCreate(scope.row.creatorId))">
                    <!-- <el-dropdown-item
                    :command="'goCreateCard_' + scope.$index"
                    >编辑
                  </el-dropdown-item> -->
                    <el-dropdown-item :command="'delXBResource_' + scope.$index">删除
                    </el-dropdown-item>
                  </template>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!--分页器-->
      <el-pagination background style="margin: 20px auto 0" :hide-on-single-page="filter.page <= 1" class="text-center"
        layout="total, prev, pager, next" @current-change="handleCurrentChange" :current-page.sync="filter.page"
        :page-size="filter.limit" :total="filter.total">
      </el-pagination>
    </div>
    <!--下载试卷弹窗-->
    <downloadDialog :downItem="downItem" :list="tableData" v-if="showDialog" @close="closeDialog"></downloadDialog>
    <!-- 上传试卷弹窗 -->
    <upload-word-teacher ref="uploadWord" :formVisible="uploadWordShow" :editParamsData="editWordInfo"
      :uploadGradeList="uploadGradeList" :isSupplement="isSupplement" @closeDialog="closeUpload"></upload-word-teacher>
    <sel-card-type ref="selectCardType" :modalVisible="modalVisible" @confirm-sure="confirmAddCard"
      @confirm-cancel="confirmCancel">
    </sel-card-type>
    <!-- 服务到期提示 -->
    <date-expired-dialog :dialogVisible="isShowExpiredDialog" :tips="tips"></date-expired-dialog>
  </div>
</template>

<script>
import {
  getCardPaperList,
  copyCardPaper,
  saveTestBanks,
  delCardPaper,
  setTemplateCard,
  checkTbPassword
} from '@/service/testbank';
import { getToken } from '@/service/auth';
import { getUserInfoToPersonalityTest } from '@/service/api';
import downloadDialog from '@/components/paper/downloadDialog';
import UploadWordTeacher from '@/components/uploadWordTeacher';
import { inArr } from '@/utils/common';
import UserRole from '@/utils/UserRole';
import SelCardType from './modules/selCardType.vue';
import DateExpiredDialog from '@/components/TestItem/dateExpiredDialog.vue';
import { sleep } from '@/utils/index'
import { IAB_CARD_TYPE } from '@/typings/card';
import { getQueryString } from '@/utils';

export default {
  name: 'card-index',
  data() {
    return {
      IAB_CARD_TYPE,
      //页面类型
      activeName: 'my',
      //查询参数
      filter: {
        keyWord: '',
        page: 1,
        pageCount: 0,
        limit: 10,
        total: 0,
      },
      tableData: [],
      cardListTimer: null,
      timerGap: 5000,
      tableLoading: false,
      cardUrl: process.env.VUE_APP_CARDURL,
      abCardUrl: process.env.VUE_APP_ABCARDURL,
      uploadWordShow: false,
      editWordInfo: {},
      uploadGradeList: [],
      showDialog: false,
      //新建答题卡,返回的id
      testBankId: '',
      inArr: inArr,
      //新建答题卡弹窗
      modalVisible: false,
      //是否是补录试卷
      isSupplement: false,
      isShowExpiredDialog: false,
    };
  },

  components: {
    UploadWordTeacher,
    downloadDialog,
    SelCardType,
    DateExpiredDialog,
  },
  watch: {
    $route(to, from) {
      clearTimeout(this.cardListTimer);
    },
  },
  beforeUpdate() {
    this.$nextTick(() => {
      this.$refs.tableRef?.doLayout();
    });
  },
  computed: {
    //是否到期
    isExpired() {
      let vipInfo = this.$sessionSave.get('vipInfo');
      return !vipInfo.isVip || !this.$store.getters.examEnabled;
    },
    // 提示
    tips() {
      if (!this.isExpired) return '';
      let vipInfo = this.$sessionSave.get('vipInfo');
      if (!vipInfo.isVip)  return '非常抱歉，贵校服务已到期，请与商务联系进行延期。';
      if (!this.$store.getters.examEnabled) return '非常抱歉，贵校考试次数已达上限。';
    },
    isAdmin() {
      return (
        this.$sessionSave.get('loginInfo') &&
        (this.$sessionSave.get('loginInfo').user_type == 5 ||
          this.$sessionSave.get('loginInfo').admin_type == 2)
      );
    },
  },
  beforeDestroy() {
    clearTimeout(this.cardListTimer);
  },
  methods: {
    async checkTbPower(item) {
      return new Promise((resolve, reject) => {
        if (!item.needCheckPassword) return resolve(true);
        let pwdId = 'pwd_' + new Date().getTime();
        const h = this.$createElement;
        this.$msgbox({
          title: '提示',
          message: h('div', null, [
            h('p', null, '该文件已加密，请输入密码后操作 '),
            h('input', {
              attrs: {
                'id': pwdId,
                'placeholder': '请输入密码',
                'type': 'password',
                'autocomplete': 'new-password',
                'autoComplete': 'new-password'
              },
              style: {
                'height': '30px',
                'border-radius': '5px',
                'border': '1px solid gray',
              },
            }),
            h('p', null, '发布成绩后自动解密'),
          ]),
          beforeClose: async (action, instance, done) => {
            if (action === 'confirm') {
              const pwd = document.getElementById(pwdId).value;
              let isSuccess = await this.checkTbPassword(pwd, item.id);
              if (isSuccess) {
                document.getElementById(pwdId).value = '';
                resolve(true)
                done();
              }
            } else {
              document.getElementById(pwdId).value = '';
              resolve(false)
              done();
            }
          },
          showCancelButton: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        });
      });
    },
    async checkTbPassword(pwd, id) {
      let res = await checkTbPassword({
        tbId: id,
        password: pwd
      }).catch(e => {
      });
      return res?.code == 1
    },
    async initSchool(cb) {
      var $this = this;
      const ret = await UserRole.getUserInfoPersonalityTest();
      ret.userSubList.unshift({
        id: '',
        name: '全部',
        subjectId: '',
        phaseId: '',
      });
      $this.uploadGradeList = ret.schGrdList.filter(ite => {
        return ite.id != '';
      });
      if (cb) {
        cb();
      }
    },
    /**
     * @name: 上传试卷制卡
     */
    uploadPaper() {
      if (this.isExpired) {
        this.isShowExpiredDialog = true;
        setTimeout(() => {
          this.isShowExpiredDialog = false;
        }, 1500);
      } else {
        this.editWordInfo = {};
        this.isSupplement = false;
        this.uploadWordShow = true;
      }
    },
    /**
     * @name:是否为当前登录用户创建的考试
     */
    isCreate(createUserId) {
      return createUserId == this.$sessionSave.get('loginInfo').id;
    },
    /**
     * @name：查询
     */
    handleSearch() {
      clearTimeout(this.cardListTimer);
      this.filter.page = 1;
      this.getCardList();
    },
    // 分页查询
    handleCurrentChange(val) {
      clearTimeout(this.cardListTimer);
      this.filter.page = val;
      // this.clearTimer();
      this.getCardList('changePage');
    },
    /**
     * @name：获取答题卡列表
     */
    getCardList(type) {
      clearTimeout(this.cardListTimer);
      let loginInfo = this.$sessionSave.get('loginInfo');
      this.tableLoading = true;
      this.filter.page = type == 'changePage' ? this.filter.page : 1;
      let params = {
        type: this.activeName == 'my' ? 1 : 2,
        schoolId: this.$sessionSave.get('schoolInfo').id,
        userId: loginInfo.id,
        name: this.filter.keyWord || '',
        page: this.filter.page,
        limit: this.filter.limit,
        showAll: 1, //0:不展示已关联的答题卡 1:展示全部
        joinDomain: 1, //文件地址是否拼接域名
      };
      getCardPaperList(params)
        .then(data => {
          this.tableLoading = false;
          this.tableData = data.data.rows || [];
          this.filter.total = data.data.total_rows;
          if (this.tableData.length) {
            setTimeout(() => {
              this.getPaperState(params);
            }, 3000);
          }
        })
        .catch(err => {
          this.tableLoading = false;
        });
    },
    /**
     * @name:获取试卷转换状态
     */
    async getPaperState(params) {
      const res = await getCardPaperList(params);
      if (res.code == 1 && res.data.rows.length) {
        res.data.rows.forEach(item => {
          this.tableData.forEach(ite => {
            if (item.id == ite.id) {
              this.$set(ite, 'state', item.state);
              this.$set(ite, 'paperNum', item.paperNum);
              this.$set(ite, 'dlStatus', item.dlStatus);
            }
          });
        });
        if (!this.checkPaperState(this.tableData)) {
          this.cardListTimer = setTimeout(() => {
            this.getPaperState(params);
          }, this.timerGap);
        } else {
          this.clearTimer();
        }
      }
    },
    /**
     * @name:获取试卷转换状态
     */
    checkPaperState(data) {
      let dataLength = data.length;
      for (let i = 0; i < dataLength; i++) {
        // 转换状态 -1:失败 0:待转换 2:转换成功 3:格式不支持
        let item = data[i];
        if (item.state === 0 && item.cardType == 1) {
          return false;
        }
      }
      return true;
    },
    /**
     * @name: 预览
     */
    async goPreview(item) {
      let isSuccess = await this.checkTbPower(item)
      if (!isSuccess) return;
      this.$router.push({
        path: '/previewDetail',
        query: {
          tbId: item.id,
          schoolId: item.schoolId,
          subId: item.subjectId,
          userId: this.$sessionSave.get('loginInfo').id,
          year: item.year || '',
          source: item.source,
          dlStatus: item.dlStatus,
          fileUrl: item.fileUrl,
          fromPage: 'card',
          cardType: item.cardType,
          correctType: item.correctType,
          supportPatchAnswer: item.supportPatchAnswer
        },
      });
    },
    /**
     * @name: 去制卡
     */
    async goCreateCard(item, isTpl = false) {
      let isSuccess = await this.checkTbPower(item)
      if (!isSuccess) return;
      if (this.isExpired) {
        this.isShowExpiredDialog = true;
        setTimeout(() => {
          this.isShowExpiredDialog = false;
        }, 1500);
      } else {
        if (
          (item.isUse == 0 && item.cardType == 1) ||
          (item.originCardType == 3 && item.isOverCard == 1)
        ) {
          return;
        }
        if (item.cardType == 3) {
          this.$router.push({
            path: '/home/<USER>',
            query: {
              tbId: item.id,
              paperNo: item.paperNo,
              schoolId: item.schoolId,
              abCardType: item.abCardType,
              abCardSheetType: item.abCardSheetType,
            },
          });
          return;
        }
        let hasEditPower = 1;
        if (isTpl) {
          hasEditPower = this.isAdmin || this.isCreate(item.creatorId) ? 1 : 0;
        }
        let token = getToken();
        let routeData = `&examName=${item.tbName}&cardType=${item.cardType}&correctType=${item.correctType}&pageLayout=${item.pageLayout}&subjectId=${item.subjectId}&editPower=${hasEditPower}&token=${token}`;
        if (item.abCardType == IAB_CARD_TYPE.abCard) {
          routeData =
            this.abCardUrl +
            `?id=${item.abCardId}` + routeData;
        } else {
          routeData =
            this.cardUrl +
            `?id=${item.id}` + routeData;
        }
        window.open(routeData, '_blank');
        if (item.isCreate) {
          this.$confirm('是否制卡完成，刷新数据', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              this.getCardList();
            })
            .catch(() => { });
        }
      }
    },
    /**
     * @name:编辑试卷
     */
    async goEditPaper(item) {
      let isSuccess = await this.checkTbPower(item)
      if (!isSuccess) return;
      let loginInfo = this.$sessionSave.get('loginInfo');
      let routeData = `${process.env.VUE_APP_CUTPAPERURL}cutTestPaper/matchQues.html?id=${item.id}&userid=${loginInfo.id}&url=${process.env.VUE_APP_CUT_URL}&subject=${item.subjectId}&phase=${item.phase}&token=${loginInfo.token}`;
      window.open(routeData, '_blank');
    },
    // 去制卷
    async createPaper(item) {
      let isSuccess = await this.checkTbPower(item)
      if (!isSuccess) return;
      let loginInfo = this.$sessionSave.get('loginInfo');
      if (item.state == -1) {
        return;
      }
      if (item.state !== 2) {
        this.$message({
          message: '请刷新页面！',
          type: 'warning',
        });
        return;
      }
      if (item.state == 2 && item.isUse) {
        this.$emit('updateTab', -1);
        this.$router.push({
          path: '/workBook/previewTestQues',
          query: {
            testBankId: item.id,
            testBankName: item.title,
            subjectId: item.subjectCode,
            phase: item.phase,
          },
        });
        return;
      }
      let routeData = `${process.env.VUE_APP_CUTPAPERURL}cutTestPaper/matchQues.html?id=${item.id}&userid=${loginInfo.id}&url=${process.env.VUE_APP_CUT_URL}&subject=${item.subjectId}&phase=${item.phase}&token=${loginInfo.token}`;
      window.open(routeData, '_blank');
      await sleep(500);
      this.$confirm('是否制卷完成，刷新数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.getCardList();
        })
        .catch(() => { });
    },
    /**
     * @name: 复制题卡
     */
    async goCopyPaper(item) {
      const res = await copyCardPaper({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        tbId: item.id,
      });
      if (res && res.code == 1) {
        this.getCardList();
      } else {
        this.$message({
          message: res.msg,
          type: 'error',
        });
      }
    },
    async goCopyTemplatePaper(item) {
      const res = await copyCardPaper({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        tbId: item.id,
        toUserId: this.$sessionSave.get('loginInfo').id
      });
      if (res && res.code == 1) {
        let data = res.data;
        let token = getToken();
        let routeData = this.cardUrl + `?id=${data.tbId}&token=${token}`;
        window.open(routeData, '_blank');
        this.activeName = 'my';
        this.$confirm('是否制卡完成，刷新数据', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.getCardList();
          })
          .catch(() => { });
      } else {
        this.$message({
          message: res.msg,
          type: 'error',
        });
      }
    },
    /**
     * @name:设置为模板卡
     * @param {*} item
     */
    async setTemplate(item) {
      const res = await setTemplateCard({
        tbId: item.id,
      });
      if (res && res.code == 1) {
        this.$message({
          message: '设置成功',
          type: 'success',
        });
        this.getCardList();
      } else {
        this.$message({
          message: res.msg,
          type: 'error',
        });
      }
    },
    /**
     * @name:打开补录试卷弹窗
     */
    supplementPaper(item) {
      this.editWordInfo = {};
      this.editWordInfo.subjectId = item.subjectId;
      this.editWordInfo.gradeCode = item.gradeCode;
      this.editWordInfo.tbId = item.id;
      this.editWordInfo.tbName = item.tbName;
      this.editWordInfo.typeCode = item.typeCode;
      this.editWordInfo.year = item.year;
      this.isSupplement = true;
      this.uploadWordShow = true;
    },
    /**
     * @name:删除答题卡
     */
    async delXBResource(item) {
      // if (item.isRelatedPaper == 1) {
      //   this.$message({
      //     message: "已关联考试，不支持删除",
      //     type: "warning",
      //   });
      //   return;
      // }
      this.$confirm('确定删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        await delCardPaper({
          ids: item.id,
        })
          .then(res => {
            this.$message({
              message: '删除成功！',
              type: 'success',
            });
            this.getCardList();
          })
          .catch(err => {
          });
      });
    },
    /**
     * @name:打开修改试卷属性弹窗
     */
    async openEditDialog(item) {
      let isSuccess = await this.checkTbPower(item)
      if (!isSuccess) return;
      // if (item.isRelatedWork == 1) {
      //   this.$message({
      //     message: `答题卡${item.tbName}已关联考试 ，请先解除关联再修改`,
      //     type: 'warning',
      //     duration: '1000',
      //   });
      //   return;
      // }
      this.editWordInfo = item;
      // if(item.supportPatchAnswer == 1){
      //   //补录试卷
      //   this.isSupplement = true;
      // }else{
      //   this.isSupplement = false;
      // }
      this.isSupplement = false;
      this.uploadWordShow = true;
    },
    /**
     * @name: 新建答题卡
     */
    async addNewCard() {
      if (this.isExpired) {
        this.isShowExpiredDialog = true;
        setTimeout(() => {
          this.isShowExpiredDialog = false;
        }, 1500);
      } else {
        this.modalVisible = true;
      }
    },
    /**
     * @name:确定新建答题卡
     */
    confirmAddCard(data) {
      this.modalVisible = false;
      let _this = this;
      let loginInfo = this.$sessionSave.get('loginInfo');
      let params = {
        tbName: data.subjectName + '答题卡',
        schoolId: this.$sessionSave.get('schoolInfo').id,
        userId: loginInfo.id,
        userName: loginInfo.realname,
        abCardType: data.abCardType,
        sCode: data.subjectId,
        sName: data.subjectName
      };
      saveTestBanks(params)
        .then(function (result) {
          if (result && parseInt(result.code) == 1) {
            if (result.data && result.data.tbInfo && result.data.tbInfo.tbId) {
              _this.testBankId = result.data.tbInfo.tbId;
              let obj = {
                id: _this.testBankId,
                abCardType: data.abCardType,
                tbName: data.subjectName + '答题卡',
                cardType: 0,
                isCreate: true,
                correctType: data.correctType,
                pageLayout: data.pageLayout,
                subjectId: data.subjectId,
              }
              if (data.abCardType) {
                obj.abCardId = [_this.testBankId, result.data.tbInfo1.tbId].join(',');
              }
              _this.goCreateCard(obj);
            }
          } else {
            _this.$message.error(result.msg);
          }
        })
        .catch(function (error) {
        });
    },
    /**
     * @name:关闭新建答题卡弹窗
     */
    confirmCancel() {
      this.modalVisible = false;
    },
    /**
     * @name：更多按钮事件
     */
    handlerCommand(command) {
      let commands = command.split('_');
      this[commands[0]](this.tableData[commands[1]]);
    },
    /**
     * @name：点击下载试卷显示下载弹窗
     */
    async downloadPaper(item) {
      let isSuccess = await this.checkTbPower(item)
      if (!isSuccess) return;
      if (item.cardType == 3 || item.isUse != 1) return;

      this.downItem = item;
      this.showDialog = true;
    },
    closeDialog() {
      this.showDialog = false;
    },
    /**
     * @name：关闭上传弹窗
     */
    closeUpload(success) {
      this.uploadWordShow = false;
      if (success) {
        this.filter.page = 1;
        this.getCardList();
      }
    },
    /**
     * @name：清除定时器
     */
    clearTimer() {
      window.clearTimeout(this.cardListTimer);
      clearTimeout(this.cardListTimer);
      this.cardListTimer = null;
    },
  },
  mounted() {
    clearTimeout(this.cardListTimer);
    this.initSchool();
    this.getCardList();
  },
};
</script>

<style lang="scss" scoped>
.card-container {
  overflow-y: auto;
  height: 100%;

  .tab-main {
    position: relative;
    margin-bottom: 15px;
  }

  .exam-card-header {
    position: relative;
    padding: 25px 15px;
    background: #fff;
    font-size: 14px;
    // border: 1px solid #e4e7ed;
    border-top: unset;

    .header-search-box {
      position: absolute;
      right: 31px;
      top: 20px;
    }

    .header-serarch {
      display: flex;
      width: 240px;

      .search-icon {
        width: 38px;
        font-size: 18px;
        color: #fff;
        background: #409eff;
        border-radius: 0 3px 3px 0;
        outline: none;
        cursor: pointer;
        border: 1px solid #dcdfe6;
        border-left: 0;
      }
    }
  }
}

.showOverTooltip {
  display: -webkit-box;
  position: relative;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;

  .tb-name {
    width: 350px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.third-card-icon {
  position: absolute;
  right: 0px;
}
</style>
<style lang="scss">
.search__text {
  .el-input__inner {
    border-right: none;
    border-radius: 3px 0 0 3px;

    &:focus {
      border-color: #dcdfe6 !important;
    }
  }
}

.card-container {
  // .el-table th.el-table__cell{
  //     background-color: #F5F7FA !important;
  // }

  .custome-table {
    .editCell {
      >span {
        color: #409eff;
        display: inline-block;
        margin-right: 10px;
        cursor: pointer;

        &:last-child {
          margin-right: 0;
        }

        &.disabled {
          opacity: 0.6;
          cursor: no-drop;
        }
      }

      .el-icon-more {
        width: 40px;
        text-align: center;
        transform: rotate(90deg);
      }
    }
  }

  .custome-table {
    border: 1px solid #e4e8eb;

    // &.el-table th,
    // &.el-table td {
    //     text-align: center;
    // }

    // &.el-table thead {
    //     font-size: 16px;
    //     color: rgb(63, 74, 84);
    // }

    // .el-table td,
    // .el-table th.is-leaf {
    //     border: 0.5px solid #ebeef5;
    // }
    .card-mark {
      font-size: 11px;
      right: 0px;
      top: 0px;
      width: 40px;
      height: 20px;
      text-align: center;
      position: absolute;
      border-radius: 5px;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;

      &.third {
        background-color: #67c23a;
        border: 1px #c2e7b0 solid;
      }

      &.abCard {
        right: 45px;
        background-color: #FF8C1D;
        border: 1px #FFD6B0 solid;
      }
    }

    .square {
      width: 0;
      height: 0;
      border: 16px solid transparent;
      border-top: 16px solid #409eff;
      border-left: 16px solid #409eff;
      z-index: 100;
      border-radius: 10px 0 0 0;
      position: absolute;
      right: 0;
      top: 0;

      .square-word {
        position: absolute;
        left: -12px;
        top: -16px;
        color: #ffffff;
        font-size: 13px;
      }
    }
  }

  .el-tabs__active-bar {
    display: none;
  }

  .el-tabs__nav-wrap::after {
    display: none;
  }

  .el-tabs__item.is-active {
    font-weight: bold;
  }

  .el-table thead {
    color: #606266;
    font-size: 16px;
  }

  .el-table th,
  .el-table td {
    text-align: center;
  }

  .el-table th.el-table__cell {
    background-color: #f5f7fa !important;
  }

  .el-table th.el-table__cell>.cell {
    padding-left: 20px !important;
    padding-right: 20px !important;
  }

  .el-table .el-table__cell {
    // padding: 8px 0 !important;
  }

  .el-table .el-table__row .cell {
    padding-left: 20px !important;
    padding-right: 20px !important;
    // line-height: 55px !important;
  }

  .el-table {
    .el-button {
      width: 32px;
      height: 32px;
      padding: unset !important;
    }
  }
}
</style>
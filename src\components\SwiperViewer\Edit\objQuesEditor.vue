<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-06-25 14:32:51
 * @LastEditors: 小圆
-->
<template>
  <div class="obj-edit">
    <!-- 客观题手写识别 -->
    <template v-if="quesItem.type == 16">
      <div
        style="position: absolute"
        :style="getBoxStyle(quesItem.list[0].pos)"
        :class="{ 'hover-border': hoverQuesNo == quesItem.question_no }"
      >
        <div class="obj-option-box" @mouseenter="mouseEnter(-1)" @mouseleave="mouseLeave">
          <div
            v-for="(item, optionIndex) in quesItem.list"
            :key="optionIndex"
            class="obj-option"
            :class="{
              active: item.fill,
              error: item.fill && (Math.pow(2, optionIndex) & quesItem.answer_int) == 0,
              right: item.fill && (Math.pow(2, optionIndex) & quesItem.answer_int) != 0,
            }"
            @click="check(item, optionIndex)"
          >
            {{ String.fromCharCode(65 + optionIndex) }}
          </div>
        </div>
      </div>
    </template>

    <template
      v-else-if="
        quesItem.question_type == IQUES_TYPE.judge ||
        quesItem.question_type == IQUES_TYPE.singleChoice ||
        quesItem.question_type == IQUES_TYPE.choice
      "
    >
      <div
        style="position: absolute; z-index: 0"
        :style="getBoxStyle(quesItem.pos)"
        :class="{
          'hover-border': hoverQuesNo == quesItem.question_no,
        }"
      ></div>
      <div
        class="obj-edit-pos"
        v-for="(item, optionIndex) in quesItem.list"
        :style="getBoxStyle(item.pos)"
        @mouseenter="mouseEnter(-1)"
        @mouseleave="mouseLeave"
        :class="{
          active: item.fill,
          error: item.fill && (Math.pow(2, optionIndex) & quesItem.answer_int) == 0,
          right: item.fill && (Math.pow(2, optionIndex) & quesItem.answer_int) != 0,
        }"
        @click="check(item, optionIndex)"
      ></div>
    </template>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { List, Question } from '../StuPaperInterface';
import { IQUES_TYPE } from '@/typings/card';
import QuesEditStore from './QuesEditStore';

@Component({
  components: {},
})
export default class ObjQuesEditor extends Vue {
  IQUES_TYPE = IQUES_TYPE;
  @Prop() quesItem: Question;
  @Prop({ default: 1 }) scale: number;
  // 分数映射
  @Prop({
    default: () => {
      return {};
    },
  })
  scoreMap: any;

  quesEditStore = QuesEditStore;

  // 悬停的题目
  get hoverQuesNo() {
    return this.quesEditStore.quesNo;
  }

  // 悬停的选项索引
  get hoverOptionIndex() {
    return this.quesEditStore.optionIndex;
  }

  getBoxStyle(pos: number[]) {
    if (!pos) return;
    return {
      left: pos[0] * this.scale + 'px',
      top: pos[1] * this.scale + 'px',
      width: pos[2] * this.scale + 'px',
      height: pos[3] * this.scale + 'px',
    };
  }

  // 选择
  check(item: List, optionIndex: number) {
    if (this.quesItem.question_type == IQUES_TYPE.choice) {
      this.checkMultiple(item, optionIndex);
    }

    if (this.quesItem.question_type == IQUES_TYPE.singleChoice || this.quesItem.question_type == IQUES_TYPE.judge) {
      this.checkSingle(item, optionIndex);
    }

    this.updataScore();
  }

  // 选择单选
  checkSingle(item: List, optionIndex: number) {
    item.fill = !item.fill;
    this.quesItem.list.forEach((item, index) => {
      if (index !== optionIndex) {
        item.fill = false;
      }
    });
  }

  // 选择多选
  checkMultiple(item: List, optionIndex: number) {
    item.fill = !item.fill;
  }

  // 更新分数
  updataScore() {
    let optionInt = 0;
    this.quesItem.list.forEach((t, i) => {
      if (t.fill) {
        optionInt += Math.pow(2, i);
      }
    });

    // 是否含有自定义规则
    if (this.scoreMap[this.quesItem.question_no]) {
      this.quesItem.score = this.scoreMap[this.quesItem.question_no][optionInt] || 0;
      return;
    }

    if (optionInt == this.quesItem.answer_int) {
      //全对
      this.quesItem.score = this.quesItem.total_score;
    } else if (
      //半对
      (optionInt & this.quesItem.answer_int) != 0 &&
      (optionInt & this.quesItem.answer_int) == optionInt
    ) {
      this.quesItem.score = this.quesItem.miss_score;
    } else {
      //全错
      this.quesItem.score = 0;
    }
  }

  mouseEnter(index) {
    this.quesEditStore.setHoverQues(this.quesItem.question_no, index);
  }

  mouseLeave() {
    this.quesEditStore.removeHoverQues();
  }
}
</script>

<style scoped lang="scss">
.obj-edit {
}

.obj-edit-pos {
  position: absolute;
  border: 2px solid #409eff;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(#409eff, 0.3);
  }

  &.active {
    border: solid 3px #01cc7d !important;
  }
  &.error {
    border: solid 3px red !important;
  }
  &.right {
    border: solid 3px #01cc7d !important;
  }
}

.obj-option-box {
  position: absolute;
  right: -5px;
  transform: translateX(100%);
  z-index: 4;

  .obj-option {
    display: inline-block;
    width: 25px;
    height: 25px;
    border: 2px solid #409eff;
    border-radius: 4px;
    text-align: center;
    line-height: 25px;
    cursor: pointer;
    margin-right: 2px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(#409eff, 0.3);
    }

    &.active {
      border: solid 3px #01cc7d !important;
    }
    &.error {
      border: solid 3px red !important;
    }
    &.right {
      border: solid 3px #01cc7d !important;
    }
  }
}

.hover-border {
  background-color: rgba(#409eff, 0.06);

  &-blue {
    background-color: rgba(#004080, 0.1);
  }
}
</style>

<template>
  <div class="schoolHome">
    <el-radio-group v-model="activeTab" class="radioGroup" @change="changeRadioTab">
      <el-radio-button :label="item.path" v-for="(item, index) in tabList" :key="index"
        >{{ item.name }}
      </el-radio-button>
    </el-radio-group>
    <keep-alive>
      <router-view
        ref="schoolHomeRef"
        v-if="$route.meta.keepAlive"
        :key="$route.path"
        style="padding-top: 16px"
      ></router-view>
    </keep-alive>
    <router-view
      style="padding-top: 16px"
      ref="schoolHomeRef"
      v-if="!$route.meta.keepAlive"
    ></router-view>
    <!-- <router-view ref="schoolHomeRef" style="padding-top: 16px"></router-view> -->
  </div>
</template>

<script>
export default {
  name: 'home',
  data() {
    return {
      tabList: [
        { name: '学科知识点', path: 'search' },
        { name: '校本教辅', path: 'test' },
        { name: '校本卷库', path: 'school' },
        { name: '校内错题', path: 'wrongques' },
        { name: '我的卷库', path: 'paper' },
      ],
      activeTab: '',
    };
  },
  watch: {
    $route(to, from) {
      // let tab = this.$localSave.get("paper-tab");
      // this.activeTab = tab ? tab : "paper";
    },
  },
  created() {
    if(this.$sessionSave.get('tbAICorrect') == "1"){
      this.tabList.splice(4, 0, { name: '英语专项', path: 'english' });
    }
    if(this.$sessionSave.get('schoolChapter') == "1"){
      this.tabList.splice(1, 0,  { name: '教材章节', path: 'chapter' });
    }
  },
  mounted() {
    if (this.$route.query['paper-tab']) {
      this.$localSave.set('paper-tab', this.$route.query['paper-tab']);
    }
    let tab = this.$localSave.get('paper-tab');
    this.activeTab = tab ? tab : 'paper';
    let quesDetail = this.$sessionSave.get('testDetail');
    if (quesDetail && quesDetail != '') {
      this.$router.push({ path: `/home/<USER>/${quesDetail}` });
    } else {
      this.changeRadioTab();
    }
  },
  activated() {
    let tab = this.$localSave.get('paper-tab');
    this.activeTab = tab ? tab : 'paper';
    // let quesDetail = this.$sessionSave.get("testDetail");
    // if (quesDetail && quesDetail != "") {
    //   this.$router.push({ path: `/home/<USER>/${quesDetail}` });
    // } else {
    // }
    this.changeRadioTab();
  },
  beforeDestroy() {
    // this.$sessionSave.set("testDetail", "");
  },
  methods: {
    matchingRoutes() {
      let localPath = this.$route.path;
      for (let item of this.tabList) {
        if (localPath.indexOf(item.path) !== -1) {
          this.activeTab = item.path;
          break;
        }
      }
    },
    changeRadioTab() {
      this.$sessionSave.set('testDetail', '');
      this.$localSave.set('paper-tab', this.activeTab);
      this.$router.push({ path: `/home/<USER>/${this.activeTab}` });
    },
    changeSchool() {
      this.$refs.schoolHomeRef.changeSchool();
    },
  },
};
</script>

<style lang="scss" scoped>
.schoolHome {
  background: #FFF;
}
</style>

<style lang="scss">
.schoolHome {
  .radioGroup{
    width: 100%;
    background: #E9F4FF;
  }
  .el-radio-button{
    &.is-active{
      .el-radio-button__inner{
        color: #409EFF;
        background: #fff !important;
        box-shadow:unset;
        font-weight: bold;
      }
    }
    .el-radio-button__inner{
      height: 50px;
      line-height: 25px;
      background-color: #E9F4FF !important;
      color: #303133;
      font-size: 18px !important;
      border: unset;
      font-weight: 500;
      border-radius: 0;
      &:hover{
        color: #409EFF;
      }
    }
  }
  .search__text {
  .el-input__inner {
    border-right: none;
    border-radius: 3px 0 0 3px;

    &:focus {
      border-color: #409EFF !important;
    }
  }
}
}
</style>

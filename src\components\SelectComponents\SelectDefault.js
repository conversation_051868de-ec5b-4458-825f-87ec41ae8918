/*
 * @Descripttion: 选择默认值
 * @Author: 小圆
 * @Date: 2024-01-19 14:33:52
 * @LastEditors: 小圆
 */
import { getPublicConfigBySchoolInfo } from '@/service/api';
import { sessionSave } from '@/utils';
import UserRole from '@/utils/UserRole';
import moment from 'moment';

// 获取最近一周时间范围
export function getLastWeekRange() {
  const start = moment().subtract(7, 'days').format('YYYY-MM-DD');
  const end = moment().format('YYYY-MM-DD');
  return [start, end];
}

// 获取最近一个月时间范围
export function getLastMonthRange() {
  const start = moment().startOf('month').format('YYYY-MM-DD');
  const end = moment().endOf('month').format('YYYY-MM-DD');
  return [start, end];
}

/**
 * @description: 根据时间类型获取默认日期
 * @param {*} type 1：按日，2：按月，3：按年
 */
export function getDefaultDateRangeByType(type) {
  type = Number(type);
  let dateRange = '';
  switch (type) {
    // 按日
    case 1:
      dateRange = getLastWeekRange();
      break;
    // 按月
    case 2:
      dateRange = getLastMonthRange();
      break;
    default:
      break;
  }
  return dateRange;
}

// 获取最近一学年字符串
export function getLastYearStr() {
  let date = new Date(),
    y = date.getFullYear(),
    M = date.getMonth(),
    d = date.getDate();
  let y1 = y - 1;
  let y2 = y;
  if (Number(`${M + 1}${d < 10 ? `0${d}` : d}`) >= 810) {
    y1 = y;
    y2 = y + 1;
  }
  return `${Number(y1.toString().substring(1))}${Number(y2.toString().substring(1))}`;
}
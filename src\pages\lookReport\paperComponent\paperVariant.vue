<!--
 * @Descripttion: 
 * @Author: 小圆
 * @Date: 2023-11-14 09:18:47
 * @LastEditors: Please set LastEditors
-->
<template>
    <!-- 变式练习 -->
    <div class="ques-detail">

        <el-tabs class="similar-tabs" :addable="variantEditable" editBtnIcon="el-icon-edit-outline" editBtnName=" 修改"
            editBtnPosition="left" v-model="item.active" type="card" @tab-add="editLinks"
            v-if="item.linkQues && item.linkQues.length">
            <el-tab-pane :label="lItem.name" :name="'link_' + lIndex" v-for="(lItem, lIndex) in item.linkQues"
                :key="lItem.id">
                <div class="ques-content">
                    <div class="question_content">
                        <div class="question_body" v-html="lItem.content.topic"></div>
                    </div>
                </div>
                <br>
                <div class="click-element" @click="lItem.showAnswer = !lItem.showAnswer">
                    <strong>【答案解析<i
                            :class="lItem.showAnswer ? 'el-icon-caret-top active' : 'el-icon-caret-bottom'"></i>】</strong>
                    <!-- <span class="flex_1 answer_box__tip click-element">{{ !lItem.showAnswer ? "点击展开" : "点击收起" }}</span> -->
                </div>

                <div v-show="lItem.showAnswer">
                    <div class="answer_box display_flex align-items_flex-start">
                        <strong class="flex_shrink_0 click-element">
                            【答案】
                        </strong>
                        <div class="flex_1">
                            <div class="answer_content" v-if="content.quesType === 2">
                                {{
                                    lItem.content.answer.split(",")[0] === "A"
                                    ? "正确"
                                    : "错误"
                                }}
                            </div>
                            <div class="answer_content" v-else-if="lItem.content.answer" v-html="lItem.content.answer">
                            </div>
                            <div class="answer_content" v-else>略。</div>
                        </div>
                    </div>

                    <div class="answer_box display_flex align-items_flex-start" v-if="lItem.points && lItem.points.length">
                        <span class="flex_shrink_0 click-element"><strong>【知识点】</strong></span>
                        <span>{{ lItem.points.map(it => it.name).join('，') }}</span>
                    </div>

                    <div class="answer_box display_flex align-items_flex-start">
                        <span class="flex_shrink_0 click-element"><strong>【解析】</strong></span>
                        <div class="answer_content flex_1" v-html="'' + lItem.content.analysis">
                        </div>
                    </div>
                </div>

            </el-tab-pane>
        </el-tabs>

        <el-empty v-else description="暂无变式题"></el-empty>
    </div>
</template>

<script>
import { getQueryString } from '@/utils';

export default {
    props: {
        item: {
            type: Object,
        },

        content: {
            type: Object,
        },
        // 是否代课班级
        isSubstituteClass: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            isDesktop: (!!window.cef && this.$route.name != 'paperComment') || getQueryString('isDesktop') == 1,
        }
    },
    computed: {
        // 支持编辑变式题
        variantEditable() {
            return !( this.item.isPBook || !this.isSubstituteClass)
        }
    },
    methods: {
        // 修改变式题
        editLinks() {
            this.$bus.emit("editLinks", this.item)
        }
    }
}
</script>

<style lang="scss" scoped>
.ques-detail {
    position: relative;
    z-index: 10;
    padding: 20px;

    >div {
        margin-bottom: 10px;
    }

    .resourceList {
        margin-top: 10px;
        margin-bottom: 0;
    }

    .answer-imgList {
        display: inline-block;
        width: 85px;
        height: 50px;
        margin-right: 10px;
        cursor: pointer;
        margin-bottom: 10px;
        border: 1px solid #cecece;
    }

    .sub {
        span {
            line-height: 28px;
        }

        .ans-btn {
            width: 155px;
            min-width: 155px;
            height: 28px;
        }

        .last {
            width: calc(100% - 110px);
        }
    }

    .obj {
        span {
            line-height: 28px;
        }

        .ans-btn {
            width: 100px;
            min-width: 100px;
            height: 28px;
        }

        .last {
            width: calc(100% - 110px);
        }
    }

    .ans {
        display: flex;
        padding: 4px 0;

        .ans-btn {
            display: inline-block;
            border: 1px solid #ccc;
            line-height: 26px;
            text-align: center;
        }

        .blue {
            color: #409effff;
            padding-left: 10px;
            display: inline-block;
        }

        .item {
            padding-left: 10px;
            display: inline-block;
        }
    }
}

.answer_box__tip {
    color: #999;
}

.similar-tabs {
    ::v-deep {
        .el-icon-edit-outline {
            vertical-align: middle;
        }

        .el-tabs__new-tab {
            font-size: 14px;
            margin-top: 8px;
            padding: 0 10px;
            width: auto;
            height: 24px;
            line-height: 24px;
            color: #333;
            user-select: none;
            border: none;
        }
    }
}
</style>
<!--
 * @Descripttion: 学年选择组件
 * @Author: 小圆
 * @Date: 2024-01-17 11:10:45
 * @LastEditors: 小圆
-->
<template>
  <div class="header__select">
    <span class="select__label">学年：</span>
    <el-select v-model="yearValue" class="select year-select" @change="handleChange" placeholder="请选择">
      <el-option v-for="item in yearList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
    </el-select>
  </div>
</template>

<script>
import { getSchoolYearListAPI } from '@/service/api';

export default {
  props: {
    value: {
      type: String,
      default: "",
    },

    // 是否显示全部
    noAll: {
      type: Boolean,
      default: false,
    },
  },

  watch: {
    value(val, oldVal) {
      this.yearValue = val;
    },
  },

  data() {
    return {
      yearList: [],
      yearValue: "",
    };
  },

  mounted() {
    // this.initYearsList();
  },

  methods: {
    // 初始化学年列表
    async initYearsList() {
      const res = await getSchoolYearListAPI({
        schoolId: this.$sessionSave.get('schoolInfo').id,
      });
      let yearList = res.data || [];
      yearList = yearList.map(item => {
        return {
          id: item.schoolYearId,
          name: item.schoolYear,
          startTime: item.startTime,
        }
      })
      this.yearList = yearList;
      this.yearValue = this.yearList[0].value;
      if (this.value) {
        this.yearValue = this.value;
      }
      this.$emit('input', this.yearValue);
      return this.yearList;
    },
    // 更换学年
    handleChange(val) {
      this.$emit("input", val);
      this.$emit("change", val);
    },
  },
};
</script>

<style lang="scss" scoped>
@import './select.scss';
</style>

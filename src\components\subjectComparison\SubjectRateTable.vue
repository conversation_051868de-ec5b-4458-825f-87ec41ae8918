<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-05-27 15:06:14
 * @LastEditors: 小圆
-->
<template>
  <el-table
    class="subject-rate-table el-table--group"
    :data="data"
    stripe
    ref="SubjectRateTable"
    :header-cell-class-name="'table-sort-cell'"
    :header-cell-style="{
      fontSize: '14px',
      color: '#3F4A54',
      backgroundColor: '#f5f7fa',
    }"
    v-drag-table
    v-sticky-table="0"
    style="width: 100%"
    max-height="550"
  >
    <el-table-column align="center" prop="subjectName" fixed label="学科" :min-width="90"> </el-table-column>
    <el-table-column
      align="center"
      prop="teaName"
      :label="filterData && filterData.classId == '' ? '学科组长' : '任课老师'"
      width="90"
      :show-overflow-tooltip="true"
    >
      <template #default="scope">
        <span>{{ scope.row.teaName ? scope.row.teaName : '--' }}</span>
      </template>
    </el-table-column>
    <el-table-column align="center" prop="totalNum" label="实考人数" width="80"> </el-table-column>
    <el-table-column align="center" prop="absentNum" label="缺考人数" width="80"> </el-table-column>
    <el-table-column align="center" prop="fullScore" label="满分" width="80"></el-table-column>
    <el-table-column align="center" prop="maxScore" label="最高分" width="80"></el-table-column>
    <el-table-column align="center" prop="minScore" label="最低分" width="80"></el-table-column>
    <el-table-column align="center" prop="zScore" label="Z分数" sortable width="90">
      <template #default="scope">
        <span :class="{ red: scope.row.zScore < 0 }">{{ scope.row.zScore }}</span>
      </template>
    </el-table-column>
    <el-table-column align="center" prop="avgScore" label="平均分" sortable> </el-table-column>
    <el-table-column align="center" label="优秀率" prop="fineRate" sortable>
      <template #default="scope">
        <span>{{ formatRate(scope.row.fineRate) }}</span>
      </template>
    </el-table-column>
    <el-table-column align="center" label="优良率" prop="goodRate" sortable>
      <template #default="scope">
        <span>{{ formatRate(scope.row.goodRate) }}</span>
      </template>
    </el-table-column>
    <el-table-column align="center" label="及格率" prop="passRate" sortable>
      <template #default="scope">
        <span>{{ formatRate(scope.row.passRate) }}</span>
      </template>
    </el-table-column>
    <el-table-column align="center" label="不及格率" prop="failRate" sortable width="100">
      <template #default="scope">
        <span>{{ formatRate(scope.row.failRate) }}</span>
      </template>
    </el-table-column>
    <el-table-column align="center" label="低分率" prop="lowRate" sortable>
      <template #default="scope">
        <span>{{ formatRate(scope.row.lowRate) }}</span>
      </template>
    </el-table-column>
  </el-table>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

import { ISubjectRateData, IFilterData } from '@/pages/lookReport/reportOverview/subjectComparison.vue';

@Component({})
export default class SubjectRateTable extends Vue {
  @Prop() tableData: ISubjectRateData[];
  @Prop() filterData: IFilterData;
  // 数据
  data: ISubjectRateData[] = [];

  @Watch('tableData', {
    deep: true,
    immediate: true,
  })
  onTableDataChange(val: ISubjectRateData[]) {
    this.data = this.$deepClone(val) as ISubjectRateData[];
  }

  // 格式化比率
  formatRate(val) {
    return val + '%';
  }

  // 格式化排名
  formatRank(val) {
    if (!val) return '--';
    return val;
  }
}
</script>

<style scoped lang="scss">
.red {
  color: red;
}
</style>

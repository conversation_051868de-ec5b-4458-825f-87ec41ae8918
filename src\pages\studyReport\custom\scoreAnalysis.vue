<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-04-29 16:44:45
 * @LastEditors: 小圆
-->
<template>
  <div>
    <div v-loading="tableLoading">
      <base-table
        v-if="tableData.length"
        :data="tableData"
        :column="tableColumns"
        :span-method="handleSpanMethod"
        v-bind="getTableAttr()"
        v-drag-table
      ></base-table>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';

@Component({
  components: {},
})
export default class Index extends Mixins(TableCommon) {
  // 表格左侧固定列
  tableLeftFixed: any[] = ['subjectName', 'className'];

  /**
   * 合并单元格句柄方法
   */
  handleSpanMethod({
    row, // 行
    column, // 列
    rowIndex, // 行索引
    columnIndex, // 列索引
  }) {
    const rowspanArr = this.formatRowspanAndColspan(this.tableData, 'subjectName');
    if (columnIndex === 0) {
      return {
        rowspan: rowspanArr[rowIndex],
        colspan: 1,
      };
    }
  }
  render(h) {
    const table = this.renderTable(h);
    return table;
  }
}
</script>

<style scoped lang="scss"></style>

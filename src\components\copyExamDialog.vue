<template>
    <el-dialog class="copy-exam-dialog" title="复制测评" :visible.sync="dialogVisible" width="550px"
        :before-close="handleClose">
        <el-form class="copy-exam-form" label-width="80px">
            <span class="warn-tip">仅支持复制已经全科发布成绩的测评</span>
            <div style="margin: 0 0 20px 10px;">
                <span style="margin: 20px;">年级</span>
                <el-select v-model="gradeValue" style="width: 140px;" @change="changeGrade">
                    <el-option v-for="item in grdList" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                </el-select>
                <span style="margin: 20px;">学科</span>
                <el-select v-model="subjectValue" style="width: 140px;" @change="changeSubject">
                    <el-option v-for="item in subjectList" v-if="item.show || item.show == undefined" :key="item.id"
                        :label="item.name" :value="item.id">
                    </el-option>
                </el-select>
            </div>
            <el-form-item label="目标测评">
                <el-select style="width:100%" v-model="examInfo" value-key="id" filterable remote reserve-keyword
                    placeholder="请输入测评名称" :popper-append-to-body="false" :remote-method="getExamList"
                    @change="examChange" :loading="loading">
                    <el-option v-for="item in examList" :key="item.id" :label="item.name" :value="item">
                        <div style="float: left">
                            <span>{{ item.name }}（{{ item.grade_name }}）</span>
                        </div>
                        <div style="float: right;">
                            <div class="select-opts-info">
                                {{ item.exam_time }}</div>
                        </div>
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="复用模块">
                <el-checkbox v-model="hasStu">参考学生</el-checkbox>
                <!-- <el-checkbox v-model="hasCard" :disabled="isPhotoCard">答题卡<i class="el-icon-question" v-popover:popover></i></el-checkbox>
                <el-popover ref="popover" placement="right" width="200" trigger="hover"
                    content="系统卡复用后，扫描数据默认匹配最新关联的测评">
                </el-popover> -->
                <el-checkbox v-model="hasReport">报告指标设置<i class="el-icon-question"
                        v-popover:popoverReport></i></el-checkbox>
                <el-popover ref="popoverReport" placement="right" width="200" trigger="hover" content="不包含上线设置和分数换算">
                </el-popover>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="confirm" :loading="confirmLoading">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script lang="ts">
import { getPublishScoreExamListAPI, reuseExamtAPI } from '@/service/pexam';
import UserRole from '@/utils/UserRole';
export default {
    name: 'copyExamDialog',
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            loading: false,
            confirmLoading: false,
            examList: [],
            examInfo: null,
            hasStu: false,
            hasCard: false,
            hasReport: false,
            // 年级列表
            grdList: [],
            gradeValue: '',
            subjectList: [],
            // 选中的学科索引
            subjectValue: '',
        }
    },
    computed: {
        isPhotoCard() {
            return this.examInfo && this.examInfo.source == "6";
        }
    },
    async created() {
        let loginInfo = this.$sessionSave.get('loginInfo');
        const ret = await UserRole.getUserInfoPersonalityTest();
        this.subjectList = [{ id: '', name: '全部' }, ...ret.schSubList];
        this.grdList = [{ id: '', name: '全部' }, ...ret.schGrdList];
        this.subjectValue = loginInfo.subjectid;
        this.gradeValue = "";
        this.getExamList();
    },
    methods: {
        handleClose() {
            this.$emit('closeDialog');
        },
        confirm() {
            this.reuseExamt();
        },
        // 切换年级，联动显示学科
        changeGrade(data) {
            if (data == '') {
                this.subjectList.forEach(v => {
                    v.show = true;
                });
            } else {
                let grd = this.grdList.find(q => q.id == data);
                this.subjectList.forEach(v => {
                    if (!v.id) return;
                    v.show = v.phaseId == grd.phaseId || v.phaseId == grd.phase + 2;
                });
            }
            this.subjectValue = "";
            this.getExamList();
        },
        /**
         * @name:切换学科
         */ 
        changeSubject() {
            this.getExamList();
        },
        async getExamList(keyword = '') {
            let res = await getPublishScoreExamListAPI({
                schoolId: this.$sessionSave.get('schoolInfo').id,
                keyWord: keyword,
                gradeId: this.gradeValue,
                subjectId: this.subjectValue,
                page: 1,
                limit: 50
            })
            this.examList = res.data.rows;
        },
        examChange() {
            if (this.isPhotoCard) {
                this.hasCard = true;
            }
        },
        async reuseExamt() {
            this.confirmLoading = true;
            let reuseType = 0;
            if (this.hasStu) {
                reuseType += 1;
            }
            if (this.hasCard) {
                reuseType += 2;
            }
            if (this.hasReport) {
                reuseType += 4;
            }
            try {
                let res = await reuseExamtAPI({
                    examId: this.examInfo.id,
                    reuseOptions: reuseType,
                })
                this.confirmLoading = false;
                this.$emit('copyExam', res.data);
            } catch (error) {
                this.confirmLoading = false;
                this.handleClose();
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.copy-exam-dialog {
    .el-dialog {
        .copy-exam-form {
            .warn-tip {
                display: block;
                color: #f56c6c;
                font-size: 13px;
                margin-bottom: 16px;
                padding: 8px 16px;
                background-color: #fef0f0;
                border-radius: 4px;
                border: 1px solid #fde2e2;

                &::before {
                    content: "⚠";
                    margin-right: 8px;
                }
            }

            .el-form-item {
                .el-select {
                    .el-select-dropdown__wrap {
                        .el-select-dropdown__list {
                            .el-select-dropdown__item {
                                .select-opts-info {
                                    color: #909399;
                                    padding: 0 5px;
                                    background-color: #f4f4f5;
                                    display: inline-block;
                                    height: 26px;
                                    line-height: 24px;
                                    font-size: 12px;
                                    border: 1px solid #e9e9eb;
                                    border-radius: 4px;
                                    box-sizing: border-box;
                                    white-space: nowrap;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

}
</style>
<!--
 * @Description: 我的试卷详情
 * @Author: qmzhang
 * @Date: 2024-12-09 17:38:12
 * @LastEditTime: 2025-05-12 16:19:38
 * @FilePath: \personal-bigdata\src\components\paper\myQuestionBank\myPaperDetail.vue
-->
<template>
    <div class="my-paper-bank" v-loading="loading" element-loading-text="数据加载中...">
        <TransitionGroup name="list" tag="span" v-if="usedLinkQuesList.length != 0">
            <div class="quesList-item display_flex flex-direction_column" v-for="item in usedLinkQuesList" :key="item.qId">
                <div class="queList-content over-hidden">
                    <div class="ques-list">
                        <div class="ques-content display_flex flex-direction_column">
                            <div class="question_content flex_1" style="width: 100%">
                                <LatexHtml class="question_body" :html="item.topic || item.data.q_html" :subject="item.subject" />
                            </div>
                        </div>
                        <div class="queList-footer">
                            <div class="difficult-star pull-left display_flex align-items_center">
                                难度:
                                <span class="star" v-for="i in item.data.difficulty" :key="i + item.qId"></span>
                            </div>
                            <template>
                                <el-button type="primary"
                                    :disabled="maxVariantLength ? selectIds.length >= maxVariantLength : false"
                                    @click="addVariant(item)" :loading="item.loading"
                                    v-show="!selectIds.includes(item.qId)">+
                                    选入</el-button>
                                <el-button class="remove" @click="removeVariant(item)"
                                    v-show="selectIds.includes(item.qId)">-
                                    移除</el-button>
                            </template>
                            <span class="edit-text detail" :class="{ active: item.showDetails }"
                                @click="getQuesDetails(item)">详情</span>
                        </div>

                        <div class="ques-detail" v-if="item && item.showDetails">
                            <!--答案-->
                            <div class="answer_box display_flex align-items_flex-start">
                                <strong class="flex_shrink_0">【答案】</strong>
                                <div class="flex_1">
                                    <div class="answer_content" v-if="item.type == 2 || item.quesType == 2">
                                        {{ item.answer.split(',')[0] == 'A' ? '正确' : '错误' }}
                                    </div>
                                    <div v-if="item.data.levelcode != ''" class="answer_content">
                                        <p v-for="(subItem, index) in item.answer" :key="index" style="display: flex">
                                            <span>&nbsp;&nbsp;({{ index + 1 }})&nbsp;&nbsp;&nbsp;</span>
                                            <span v-html="subItem"></span>
                                        </p>
                                    </div>
                                    <LatexHtml class="answer_content" v-else :html="item.answer" :subject="item.subject">
                                    </LatexHtml>
                                </div>
                            </div>
                            <!--考点-->
                            <div v-if="item.knowledgeName.length">
                                <strong>【知识点】</strong>{{ item.knowledgeName.join('，') }}
                            </div>
                            <!--解析-->
                            <div v-if="item.analysis" class="answer_box display_flex align-items_flex-start">
                                <span class="flex_shrink_0"><strong>【解析】</strong></span>
                                <LatexHtml class="answer_content flex_1" :html="item.analysis" :subject="item.subject">
                                </LatexHtml>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </TransitionGroup>

        <el-empty v-else description="暂无变式题"></el-empty>
    </div>
</template>

<script lang="ts">
import { State, Getter } from 'vuex-class';
import { Component, Vue, Prop } from 'vue-property-decorator';
import { getSchoolTbQuestionList, chooseQuesSearch, covertQuesList } from '@/service/pbook'
import LatexHtml from '@/components/LatexHtml.vue';
import { getViewPaper } from '@/service/testbank';

@Component({ components: { LatexHtml } })
export default class MyPaperDetail extends Vue {
    @Getter loginInfo;

    @Prop({ default: () => [] }) selectIds: string[];
    @Prop() maxVariantLength: number;
    @Prop() paperId: string;
    // 已添加的题目列表
    @Prop({ default: () => [] }) addedList: any[];
    // 仅展示已添加的列表
    @Prop({ default: false }) onShowAdded: boolean;

    loading = false
    isShowDetail = true
    // 题目列表
    expandQuesList = [];
    // 父级参数
    parentParams = null;

    get usedLinkQuesList() {
        return this.onShowAdded ? this.addedList : this.expandQuesList;
    }

    async created() {
        let $listeners: any = this.$listeners
        this.parentParams = $listeners.getParams();
        if (!this.onShowAdded) {
            await this.getMyQuesList();
        }
    }

    // 获取我的题库列表
    async getMyQuesList() {
        try {
            this.loading = true;
            let res1 = await getViewPaper({
                id: this.paperId,
                optUserId: this.loginInfo.id,
            })
            const { quesInfo: quesInfoStr } = res1.data;
            if (res1.code != 1) throw res1;
            if (!quesInfoStr) throw "未查询到题目信息！";

            const quesInfo = JSON.parse(quesInfoStr);
            let ids = this.getQuesinfoIds(quesInfo)
            let res2 = await chooseQuesSearch({
                qIds: ids.join(','),
            })
            if (res2.code != 1) throw res2;
            if (!res2.data) throw "未查询到题目信息！";

            this.expandQuesList = res2.data;
        } catch (error) {
            console.error(error);
            this.expandQuesList = [];
        } finally {
            this.loading = false
        }
    }

    /**
     * @description: 获取小题id合集
     * @param {*} quesInfo
     * @return {*}
     */
    getQuesinfoIds(quesInfo: any[]) {
        let ids = [];
        quesInfo.forEach(qs => {
            if (!qs.data || !qs.data.length) {
                ids.push(qs.id)
            } else {
                ids = ids.concat(this.getQuesinfoIds(qs.data))
            }
        })

        return ids;
    }

    // 查看详情
    async getQuesDetails(item) {
        try {
            if (item.showDetails) {
                item.showDetails = !item.showDetails;
                return;
            }

            if (item.detailFlag) {
                item.showDetails = true;
                return;
            }
            // await covertQuesList([item])

            let data = item;
            this.$set(item, 'data', data.data);
            this.$set(item, 'detailFlag', true);
            this.$set(item, 'showDetails', true);
            this.$set(item, 'answer', data.answer);
            this.$set(item, 'knowledgeName', data.data.tag_ids.map(it => it.name));
            this.$set(item, 'analysis', data.analysis);
            this.$set(item, 'content', data.content);
            this.$forceUpdate();
        } catch (error) {
            console.log(error);
        }
    }

    // 添加变式题
    async addVariant(item) {
        // await covertQuesList([item])
        this.$emit('selectQues', item);
    }

    // 移除变式题
    removeVariant(item) {
        this.$emit('selectQues', item, item)
    }
}
</script>

<style lang="scss" scoped>
.list-move,
/* 对移动中的元素应用的过渡 */
.list-enter-active,
.list-leave-active {
    transition: all 0.3s ease;
}

.list-enter-from,
.list-leave-to {
    opacity: 0;
    transform: translateX(30px);
}

/* 确保将离开的元素从布局流中删除
  以便能够正确地计算移动的动画。 */
.list-leave-active {
    position: absolute !important;
}



.question_body {
    padding: 20px;
}

.quesList-item {
    width: 100%;
    background: #fff;
    border: 1px solid #e8ebed;
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;

    &:hover {
        border: 1px solid #008dea;
    }

    .queList-content {
        &:hover {
            cursor: pointer;
        }

        q ol {
            list-style: none;
        }

        .option-box {
            .row-four {
                display: flex;
                flex-wrap: wrap;

                .flex-four {
                    width: 20%;
                    margin: 10px;

                    img {
                        width: 100%;
                    }
                }
            }

            .row-two {
                display: flex;
                flex-wrap: wrap;

                .flex-two {
                    width: 42%;
                    margin: 10px;
                }
            }

            .option-item {
                display: flex;

                .option-item-letter {
                    margin-right: 4px;
                }
            }
        }
    }

    .ques-detail {
        background-color: #fcfcfc;
        padding: 15px;
    }

    .ques-detail-box {
        background: #f8f9fc;
        padding: 10px;
        border-top: 1px solid #eeeeee;
        color: #333;

        .point-box,
        .answer-box,
        .analysis-box {
            display: flex;
        }

        .auxiliary {

            .pt3,
            .pt4,
            .pt5,
            .pt6,
            .pt7 {
                padding-left: 10px;
                padding-bottom: 5px;
            }

            b {
                font-weight: 500;
            }

            a {
                color: #333;
            }
        }
    }

    .queList-footer {
        width: 100%;
        height: 40px;
        background: #f8f9fc;
        border-radius: 0 0 6px 6px;
        padding: 0 20px 0 0;
        line-height: 40px;
        font-size: 16px;
        font-weight: 400;
        color: #525766;

        .difficult-star {
            font-size: 14px;
            margin-left: 30px;

            .star {
                width: 16px;
                height: 16px;
                background: url("../../../assets/star.png") center center/100% 100% no-repeat;
                margin-left: 5px;

                &:first-child {
                    margin-left: 8px;
                }
            }
        }

        .el-button {
            min-width: 80px;
            height: 30px;
            // background: #3a84f9;
            border-radius: 4px;
            margin-left: 30px;
            float: right;
            // color: #fff;
            cursor: pointer;
            margin-top: 5px;
            padding: 0;
            font-size: 16px;
            font-weight: 400;

            &.remove {
                border: 1px solid #3a84f9;
                background: #fff;
                color: #3a84f9;
            }
        }

        .edit-text {
            margin-left: 18px;
            float: right;
            cursor: pointer;

            &.disclose,
            &.error-correction,
            &.detail {
                position: relative;

                &:before {
                    content: "";
                    position: absolute;
                    top: 11px;
                    left: 0;
                    background-image: url("../../../assets/analyzeQuesIcons.png");
                }
            }

            &.detail.active::before {
                transform: rotateZ(180deg);
                top: 14px;
            }

            &.disclose {
                padding-left: 15px;

                &:before {
                    background-position: -34px -15px;
                    width: 10px;
                    height: 10px;
                }
            }

            &.error-correction {
                padding-left: 15px;

                &:before {
                    background: url("../../../assets/eraser.png") center center/100% 100% no-repeat;
                    width: 16px;
                    height: 16px;
                    top: 12px;
                    left: -2px;
                }
            }

            &.detail {
                padding-left: 15px;

                &:before {
                    background-image: url("../../../assets/analyzeQuesIcons.png");
                    background-position: -31px 4px;
                    width: 14px;
                    height: 14px;
                    left: -2px;
                }
            }

            &.similar {
                padding-left: 15px;
                position: relative;

                &:before {
                    content: "";
                    width: 16px;
                    height: 16px;
                    background: url("../../../assets/icon/search.png") no-repeat;
                    background-size: 100% 100%;
                    position: absolute;
                    left: -3px;
                    top: 12px;
                }
            }
        }
    }
}
</style>
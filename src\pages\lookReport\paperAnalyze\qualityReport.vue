<!--
 * @Descripttion: 
 * @Author: 小圆
 * @Date: 2023-12-04 20:29:37
 * @LastEditors: 小圆
-->
<template>
  <div>
    <!--命题质量-基础信息-->
    <div class="quality-base-info-box">
      <p class="base-info-p">
        本学科试卷共<strong>{{ qualityInfo.examInfo.totalQuesNum || '-' }}</strong
        >道题， 客观题<strong>{{ qualityInfo.examInfo.objCount }}</strong
        >道，分值占比<strong>{{ qualityInfo.examInfo.objScoreRate + '%' }}</strong
        >， 主观题<strong>{{ qualityInfo.examInfo.subCount }}</strong
        >道<span v-if="doQuesList.length">(<span class="mixin-dochoice-text">{{ getDoQuesChoiceTipText() }}</span>)</span>，分值占比<strong>{{ qualityInfo.examInfo.subScoreRate + '%' }}</strong
        >； 学科总分<strong>{{ qualityInfo.examInfo.fullScore || '-' }}</strong>
      </p>
      <div class="base-info-box">
        <div class="col">
          <span class="title">
            <span style="margin-left: 10px">信度</span>
            <el-tooltip
              class="item"
              effect="dark"
              content="表示测验结果是否反映了被测者的稳定的、一贯性的真实特征，介于0-1之间，值越大越好(0.9以上为优秀，0.7~0.9为良好，0.35~0.7为中等，0.35以下为低信度)"
              placement="top"
            >
              <el-icon class="el-icon-info"></el-icon>
            </el-tooltip>
          </span>
          <!-- Math.round((num + Number.EPSILON) * 100) / 100 -->
          <strong class="info">{{
            qualityInfo.grade.reliability === ''
              ? '-'
              : Math.round((Number(qualityInfo.grade.reliability) + Number.EPSILON) * 100) / 100
          }}</strong>
        </div>
        <div class="col">
          <span class="title">
            <span style="margin-left: 10px">区分度</span>
            <el-tooltip
              class="item"
              effect="dark"
              content="表示试题对考生能力的区分程度，越大表明试题区分不同能力考生的效果越好，试题采用的价值也越大。取值范围介于-1.00至+1.00之间，一般情况下，区分度应为正值"
              placement="top"
            >
              <el-icon class="el-icon-info"></el-icon>
            </el-tooltip>
          </span>
          <strong class="info">{{
            qualityInfo.grade.distinguish === ''
              ? '-'
              : Math.round((Number(qualityInfo.grade.distinguish) + Number.EPSILON) * 100) / 100
          }}</strong>
        </div>
        <div class="col">
          <span class="title">
            <span style="margin-left: 10px">难度</span>
            <el-tooltip
              class="item"
              effect="dark"
              content="表示试题的难易程度。介于0-1之间，值越大表示题目越简单"
              placement="top"
            >
              <el-icon class="el-icon-info"></el-icon>
            </el-tooltip>
          </span>
          <strong class="info">{{
            qualityInfo.grade.scoreRate === ''
              ? '-'
              : Number(qualityInfo.grade.scoreRate).toFixed(2)
          }}</strong>

          <!--<strong class="info">{{qualityInfo.grade.scoreRate===''?'-':Number(qualityInfo.grade.scoreRate).toFixed(2)}}</strong>-->
        </div>
        <div class="col">
          <span class="title">满分人数</span>
          <strong class="info">{{
            qualityInfo.grade.fullNum === '' ? '-' : qualityInfo.grade.fullNum
          }}</strong>
        </div>
        <div class="col">
          <span class="title">不及格人数</span>
          <strong class="info">{{
            qualityInfo.grade.failNum === '' ? '-' : qualityInfo.grade.failNum
          }}</strong>
        </div>
      </div>
    </div>

    <div class="quality-report-box">
      <div class="titleLine">难度分布</div>
      <div class="diff-section-chart">
        <DiffSectionChart ref="diffSectionChart" :qualityInfo="qualityInfo"></DiffSectionChart>
      </div>
      <div class="titleLine">知识点分布</div>
      <div class="point-section-chart">
        <KnowledgeSectionChart
          ref="knowledgeSectionChart"
          :qualityInfo="qualityInfo"
        ></KnowledgeSectionChart>
      </div>
    </div>
  </div>
</template>

<script>
import DiffSectionChart from '@/components/qualityReport/DiffSectionChart.vue';
import KnowledgeSectionChart from '@/components/qualityReport/KnowledgeSectionChart.vue';
import { getQualityInfo } from '@/service/pexam';
import DochoiceMixin from '../mixin/DochoiceMixin.vue';

export default {
  name: 'qualityReport',
  props: ['filterData'],
  mixins: [DochoiceMixin],
  components: {
    DiffSectionChart,
    KnowledgeSectionChart,
  },
  data() {
    return {
      isLoading: false,
      qualityInfo: {
        grade: {
          reliability: '',
          distinguish: '',
          difficulty: '',
          failNum: '',
          fullNum: '',
          scoreRate: '',
        },
        examInfo: {
          objScoreRate: '',
          subScoreRate: '',
          totalQuesNum: '',
        },
        diff: {},
        knowledge: {
          knowledges: [],
        },
      },
    };
  },
  watch: {
    filterData: {
      immediate: true,
      handler(newValue, oldValue) {
        if (newValue) this.getQualityInfo();
        this.getPaperChoice(this.$sessionSave.get('reportDetail').examId, this.filterData.subjectId);
      },
    },
  },

  mounted() {},

  activated() {
    if (
      this.$refs.diffSectionChart.diffSectionChart &&
      this.$refs.knowledgeSectionChart.knowledgeSectionChart
    ) {
      this.$nextTick(() => {
        this.$refs.diffSectionChart.diffSectionChart.resize();
        this.$refs.knowledgeSectionChart.knowledgeSectionChart.resize();
      });
    }
  },
  methods: {
    resetQualityInfo() {
      this.qualityInfo = {
        grade: {
          reliability: '',
          distinguish: '',
          difficulty: '',
          failNum: '',
          fullNum: '',
          scoreRate: '',
        },
        examInfo: {
          objScoreRate: '',
          subScoreRate: '',
          totalQuesNum: '',
        },
        diff: {},
        knowledge: {
          knowledges: [],
        },
      };
    },
    // 命题质量信息
    async getQualityInfo() {
      try {
        const data = await getQualityInfo({
          subjectId: this.filterData.subjectId,
          examId: this.$sessionSave.get('reportDetail').examId,
          abPaper: this.filterData.abPaper
        });
        if (data.code == 1 && typeof data.data === 'object' && JSON.stringify(data.data) != '{}') {
          this.qualityInfo = data.data;
        } else {
          this.resetQualityInfo();
        }
      } catch (error) {
        console.log(error);
        this.resetQualityInfo();
      }
    },

    updateFilter(data) {},
    updateData({ isLoading }) {
      this.isLoading = isLoading;
    },
  },
};
</script>

<style lang="scss" scoped>
.quality-base-info-box {
  font-size: 16px;
  margin: 10px 10px 10px;
  padding: 20px 10px;
  background: #f7fafc;
  border-radius: 8px;

  .base-info-p {
    color: #8f9ca8;

    strong {
      color: #3f4a54;
    }
  }

  .base-info-box {
    font-size: 16px;
    margin-top: 20px;
    display: flex;
    justify-content: space-around;
    align-items: center;

    .col {
      display: flex;
      flex-direction: column;
      text-align: center;

      .title {
        color: #8f9ca8;
      }

      strong {
        margin-top: 6px;
        font-size: 20px;
      }
    }
  }
}
.quality-report-box {
  .titleLine {
    display: inline-block;
    position: relative;
    height: 54px;
    line-height: 54px;
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;
    padding-left: 16px;
    &:before {
      content: '';
      width: 6px;
      height: 24px;
      background: #409eff;
      border-radius: 3px;
      position: absolute;
      left: 0;
      top: 15px;
    }
  }
  .diff-section-chart,
  .point-section-chart {
    margin: 10px 0;
    border: 1px solid #e4e8eb;
    border-radius: 3px;
  }
  .point-section-chart {
    min-height: 200px;
    padding-bottom: 10px;
  }
}
</style>

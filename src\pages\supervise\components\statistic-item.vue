<!--
 * @Descripttion: 
 * @Author: 小圆
 * @Date: 2023-11-29 14:35:25
 * @LastEditors: 小圆
-->
<template>
  <div class="statistic-card" :style="{ backgroundImage: `url(${imgSrc})` }">
    <a class="statistic-link" @click="$emit('goto')"></a>
    <div class="statistic-title">{{ title }}</div>
    <div class="statistic-number">
      <span class="number ellipsis" :title="value">{{ value }}</span>
      <span class="unit">{{ unitText }} </span>
    </div>
    <div class="statistic-link">
      <el-link class="link" @click="$emit('goto')">查看详情 <i class="el-icon-arrow-right"></i></el-link>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Number,
      default: 0,
    },
    title: {
      type: String,
      default: "",
    },
    unitText: {
      type: String,
      default: "",
    },
    imgSrc: {
      type: String,
      defualt: "",
    },
  },
};
</script>

<style lang="scss" scoped>
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
}
.statistic-card {
  position: relative;
  height: 210px;
  border-radius: 10px;
  padding-left: 34px;
  padding-right: 34px;
  background-color: #eef7ff;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.statistic-title {
  padding-top: 37px;
  line-height: 1;
  font-size: 24px;
  font-weight: 700;
  color: #3b4a68;
}
.statistic-number {
  padding-top: 29px;
  font-size: 36px;
  font-weight: 700;
  line-height: 1;
  display: flex;
  align-items: baseline;

  .number {
    font-size: 36px;
    font-family: Source Han Sans CN;
    font-weight: 700;
    color: #101019;
  }

  .unit {
    margin-left: 5px;
    font-size: 16px;
    font-weight: 400;
    color: #101019;
  }
}

.statistic-link {
  padding-top: 47px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1;
  color: #606266;

  .link {
    cursor: pointer;
  }
}
</style>

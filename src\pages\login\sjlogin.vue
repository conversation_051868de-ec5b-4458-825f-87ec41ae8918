<template>
  <div class="loginPage sjloginPage">
    <!-- <img class="loginLogo" :src="loginLogo" /> -->
    <img class="loginBg" :src="loginBg" />
    <div class="loginBox">
      <p class="loginBox__title">绥江县智慧教育服务平台</p>
      <div class="loginBox__account display_flex align-items_center">
        <span class="avator"></span>
        <el-input class="rightInput" v-model="accountValue" :autofocus="true" placeholder="请输入用户名或手机号登录"></el-input>
      </div>
      <div class="loginBox__password display_flex align-items_center">
        <span class="avator"></span>
        <el-input class="rightInput" v-model="passwordValue" placeholder="请输入密码"
          :autocomplete="dialogVisible ? 'new-password' : 'on'" show-password></el-input>
      </div>
      <!--<el-checkbox class="rememberPassword" v-model="rememberPassword">记住密码-->
      <!--</el-checkbox>-->
      <el-button class="loginBtn" @click="loginWithNoType" :loading="isLoading">登录</el-button>
    </div>

    <!--<el-button type="text" @click="dialogVisible = true">点击打开 Dialog</el-button>-->
    <!--学科网账号退出-->
    <iframe v-if="loadXKWLogout" style="display: none" src="https://zjse.xkw.com/logout/"></iframe>

    <el-dialog title="" :visible.sync="dialogVisible" :show-close="true" width="500px">
      <div class="dialog-body">
        <p class="tip">请选择您的登录身份</p>
        <ul class="selection-list">
          <li v-for="item in userTypes" :key="item.name" :class="{ active: item.type == currentSelType }"
            @click="selectType(item)">
            {{ item.name }}
          </li>
        </ul>
        <div style="text-align: center">
          <el-button class="submit-btn" type="primary" :disabled="!canSubmit" @click="confirmSubmit">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import loginMixin from '@/mixins/loginMixin';

export default {
  name: 'login',
  mixins: [loginMixin],
  data() {
    return {
      loginBg: require('@/assets/sj_loginBg.png'),
      // loginLogo: require('@/assets/sj_loginLogo.png'),
    };
  }
};
</script>

<style lang="scss" scoped>
.loginPage {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .loginBg {
    width: 100%;
    height: auto;
  }

  .loginLogo {
    position: absolute;
    left: 100px;
    top: 50px;
  }

  .loginBox {
    position: absolute;
    right: 200px;
    top: 50%;
    margin-top: -220px;
    width: 480px;
    height: 400px;
    background: #fff;
    box-shadow: 0px 6px 33px 0px rgba(0, 129, 213, 0.2);
    border-radius: 20px;
    padding: 20px 70px 0 75px;

    .loginBox__title {
      font-size: 26px;
      letter-spacing: 3px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #333;
    }

    .title_desc {
      color: #b5b5b5;
      line-height: 28px;
      margin-right: 20px;
    }

    .loginBox__account,
    .loginBox__password {
      width: 340px;
      height: 60px;
      border: 1px solid #e1e3e6;
      border-radius: 4px;

      .avator {
        position: relative;
        width: 80px;
        height: 100%;

        &:before {
          position: absolute;
          left: 50%;
          top: 50%;
          margin-left: -12px;
          margin-top: -14px;
          content: '';
          background: url('../../assets/personWorkIcon.png');
          background-position: 0px -46px;
          width: 26px;
          height: 30px;
        }

        &:after {
          position: absolute;
          content: '';
          right: 0;
          top: 13px;
          width: 2px;
          height: 34px;
          background: #e9edee;
          box-shadow: 1px 0px 0px 0px rgba(255, 255, 255, 0.2);
        }
      }

      .rightInput {
        margin-left: 20px;
      }
    }

    .loginBox__account {
      margin: 40px 0;

      .avator {
        &:before {
          background-position: -31px -46px;
          width: 26px;
          height: 30px;
          margin-left: -13px;
          margin-top: -15px;
        }
      }
    }

    .rememberPassword {
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #008eeb;
      margin-top: 25px;
    }

    .loginBtn {
      width: 100%;
      height: 54px;
      background: #008eeb;
      border-radius: 4px;
      margin-top: 70px;
      font-size: 18px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #fff;
    }
  }

  .dialog-body {
    .tip {
      text-align: center;
      font-size: 18px;
      color: #555;
    }

    .selection-list {
      list-style: none;
      text-align: center;
      margin: 30px auto;

      li {
        width: 200px;
        margin: 15px auto;
        font-size: 16px;
        border: 1px solid #b3b3b3;
        padding: 5px 0;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
          color: #fff;
          border: 1px solid #008eeb;
          background-color: #008eeb;
        }

        &.active {
          color: #fff;
          border: 1px solid #008eeb;
          background-color: #008eeb;
        }
      }
    }

    .submit-btn {
      width: 200px;
      font-size: 16px;
      margin-bottom: 10px;
    }
  }

  .beian {
    position: sticky;
    bottom: 10px;
    text-align: center;

    a {
      color: #000;
      font-size: 14px;
    }
  }
}
</style>
<style lang="scss">
.loginPage {
  .el-dialog__header {
    height: 0;
    background-color: #ffffff;
  }
}

.sjloginPage {
  .el-button {
    background: #008eeb;
    border-color: #008eeb;
  }
}

.rightInput {
  .el-input__inner {
    border: none;
  }
}
</style>

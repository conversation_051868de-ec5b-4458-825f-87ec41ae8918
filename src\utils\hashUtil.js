/* eslint-disable one-var */
import qs from 'query-string'

const Hash<PERSON>til = {}

HashUtil.get_query = key => {
  var querys = qs.parse(location.search)
  return querys[key] || ''
}

HashUtil.get_hash = key => {
  var hashs = qs.parse(location.hash)
  return hashs[key] || ''
}

HashUtil.set_hashchange = (data) => {
  let hash = window.location.hash

  if (!hash && !data) return false

  let hashs = qs.parse(window.location.hash)

  for (var key in data) {
    let value = data[key]
    if (!value && value !== 0) {
      delete hashs[key]
    } else {
      hashs[key] = data[key]
    }
  }

  if (hashs != null) window.location.hash = qs.stringify(hashs)

  return false
}

HashUtil.get_hashchange = () => {
  return qs.parse(location.hash)
}

export default HashUtil

<template>
  <div class="box-plot-box">
    <div class="titleLine">箱线图</div>
    <div class="table-box" v-loading="isLoading">
      <BoxplotTable :tableData="tableData"></BoxplotTable>
    </div>
    <div class="chart-box" v-loading="isLoading" id="pane-chart">
      <BoxplotChart ref="boxplotChart" :currentSubId="filterData?.subjectId" :tableData="tableData"></BoxplotChart>
    </div>
  </div>
</template>

<script>
import BoxplotTable from '@/components/boxplot/BoxplotTable.vue';
import BoxplotChart from '@/components/boxplot/BoxplotChart.vue';
import { getBoxData } from '@/service/pexam';

export default {
  name: 'boxplot',
  components: {
    BoxplotChart,
    BoxplotTable,
  },
  props: ['filterData'],
  watch: {
    filterData: {
      deep: true,
      handler(newValue) {
        if (!newValue) return;
        this.clsList = this.$sessionSave.get('innerClassList');
        this.getBoxData();
      },
    },
  },
  data() {
    return {
      activeName: 'chart',
      isLoading: false,
      clsList: [],
      tableData: [],
    };
  },
  activated() {},

  async mounted() {
    this.clsList = this.$sessionSave.get('innerClassList');
    this.getBoxData();
  },
  methods: {
    // 获取箱体图数据
    async getBoxData() {
      try {
        this.tableData = [];
        const data = await getBoxData({
          subjectId: this.filterData.subjectId,
          examId: this.$sessionSave.get('reportDetail').examId,
          qType: this.filterData.qType,
        });
        if (!data.data.classInfo.length) {
          this.tableData = [];
          return;
        }
        data.data.grdInfo.classId = '';
        data.data.grdInfo.className = '年级';
        var list = [data.data.grdInfo];
        this.clsList.forEach(item => {
          var clz = data.data.classInfo.find(x => x.classId == item.id);
          if (clz != null) {
            clz.classId = item.id;
            clz.className = item.class_name;
            list.push(clz);
          }
        });

        this.tableData = list;
      } catch (error) {
        console.log(error);
        this.tableData = [];
      }
    },
    updateData({ isLoading }) {
      // console.log('updatting', isLoading)
      this.isLoading = isLoading;
    },
    handleClick(tab, event) {
      if (tab.name == 'chart') {
        this.$nextTick(() => {
          if (this.$refs.boxplotChart.boxplotChart) {
            this.$refs.boxplotChart.boxplotChart.resize();
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.box-plot-box {
  border-radius: 6px;
  padding-bottom: 30px;
  background-color: #fff;

  .chart-box {
    height: 475px;
    background: #ffffff;
    border: 1px solid #e4e8eb;
    border-radius: 3px;
    margin-top: 20px;
  }

  .table-box {
    border: 1px solid #e4e8eb;
    border-radius: 3px;
  }
}
.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;
  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 15px;
  }
}
</style>
<style lang="scss">
.box-plot-box {
  .el-tabs {
    .el-tabs__header {
      margin: 0 0 1px 4px;
    }
    .el-tabs__nav-wrap::after {
      width: 160px;
    }
    .el-tabs__item {
      width: 80px;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      &.is-top:last-child {
        padding-left: 0;
      }
      &.is-top:nth-child(2) {
        padding-right: 0;
      }
    }
  }
}
</style>

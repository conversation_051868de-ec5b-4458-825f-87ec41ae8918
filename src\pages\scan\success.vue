<template>
  <div class="paper-container">

    <div class="header-back el-icon-arrow-left"><span @click="$router.go(-1)">返回</span></div>

    <scan-student-list :paper-id="paperId" :paper-info="paperInfo" :status="0"></scan-student-list>
  </div>
</template>

<script>
import {mapGetters} from 'vuex';
import {getPaperInfo, getPaperStudent} from "@/service/pexam";
import ScanStudent from "@/components/scan/ScanStudent";
import ScanStudentList from "@/components/scan/ScanStudentList";

export default {
  name: 'scan-success',
  data() {
    return {
      paperId: this.$route.query.paperId,
      paperInfo: null
    };
  },
  computed: {
    ...mapGetters([
      'filterSubjectList'
    ])
  },
  components: {ScanStudentList, ScanStudent},
  mounted() {
    this.init()
  },
  watch: {
    'currentStudent': function (newValue) {

    }
  },
  methods: {
    init() {
      this.getPaperInfo()
    },
    async getPaperInfo() {
      let res = await getPaperInfo({
        id: this.paperId
      })

      this.paperInfo = res.data
    }
  }
};
</script>

<style lang="scss" scoped>
.paper-header {
  text-align: center;

  .header-back {
    float: left;
  }

  .header-title {

  }
}

.paper-scan-info {
  width: 100%;
  height: calc(100% - 20px);
  margin-top: 10px;
  background: white;
  display: flex;

  .student-paper-detail {
    width: 100%;
  }

  .left_title {
    text-align: center;
    padding: 10px;
    font-family: "Microsoft YaHei", serif;
    font-size: 18px;
  }

  .student-list {
    height: 100%;
    border: solid 1px #EAEAEA;

    .student-list-ul {
      list-style: none;
      width: 150px;
      background: #FAFAFA;
      height: calc(100% - 48px);
      padding: 10px 0;
      overflow-y: auto;

      .student-item {
        padding: 5px 10px;

        &.active {
          background: #dbe9ff;
        }

        .student-score {
          float: right;


        }
      }
    }
  }
}
</style>

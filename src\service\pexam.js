import { sessionSave } from '@/utils';
import * as httpApi from './index';
import UserRole from '@/utils/UserRole';

const kklUrl = process.env.VUE_APP_KKLURL;
const baseUrl = process.env.VUE_APP_BASE_API;
const url17 = process.env.VUE_WX;
/**
 * api转换
 * @type {{POST: API.POST, GET: API.GET}}
 */
const API = {
  GET: function (url, params) {
    return httpApi.GET(url, params, kklUrl);
  },
  POST: function (url, params, BASEURL = kklUrl) {
    return httpApi.POST(url, params, BASEURL);
  },
  POSTJson: function (url, params) {
    return httpApi.POSTJson(url, params, kklUrl);
  },
  POSTForm: function (url, params) {
    return httpApi.POSTForm(url, params, kklUrl);
  },
};
// 个性化练习，获取班级概况
export const getClassData = params => {
  return API.GET('/pexam/personaliz/getClassData', params);
};
// 个性化练习，获取知识点分析
export const getKnowledgeData = params => {
  return API.GET('/pexam/personaliz/getKnowledgeData', params);
};
// 个性化练习，获取小题分析
export const getQuestionData = params => {
  return API.GET('/pexam/personaliz/getQuestionData', params);
};
// 个性化练习，获取学生作答详情
export const getStudentData = params => {
  return API.GET('/pexam/personaliz/getStudentData', params);
};
// 个性化练习，获取获取班级学生列表
export const getClassStudentData = params => {
  return API.GET('/pexam/personaliz/getClassStudentData', params);
};
// 个性化练习，获取获学生答题
export const getStuExamData = params => {
  return API.GET('/pexam/personaliz/getStuExamData', params);
};
// 个性化练习，获取班级、学科
export const getExamRelation = params => {
  return API.GET('/pexam/personaliz/getExamRelation', params);
};
//上传word检查是否已经上传试卷
export const verifyWordFile = params => {
  return API.POST('/pexam/exam/verifyWordFile', params);
};
//保存WordFile
export const saveWordFile = params => {
  return API.POST('/pexam/exam/saveWordFile', params);
};
//下载成绩导入模板之前验证
export const getTemplateVerify = params => {
  return API.GET('/pexam/personalBook/getTemplateVerify', params);
};
//下载已导入word的成绩导入模板
export const getDownloadTemplate = params => {
  return API.GET('/pexam/personalBook/getDownloadTemplate', params);
};

// 获取默认分值
export const getQueTypeListBySubId = params => {
  return httpApi.GET('/questionbank/17question/getQueTypeListBySubId', params, url17);
};

// 获取学年学期
export const getAcadData = params => {
  return API.GET('/pexam/exam/getAcadData', params);
};

// 获取班级
export const getExamClass = params => {
  return API.GET('/pexam/exam/getExamClass', params);
};

// 获取班级
export const getExamClass2 = params => {
  return API.GET('/pexam/exam/getExamClass2', params);
};

// 导入成绩
export const importStuResult = params => {
  return API.POST('/pexam/exam/importStuResult', params);
};
// 导入成绩
export const getImportState = params => {
  return API.POST('/pexam/exam/getImportState', params);
};
// 获取学科
export const getUseSubject = params => {
  return API.GET('/pexam/exam/getUseSubject', params);
};

// 获取考试自定义赋分的学科
export const getCustomSubject = params => {
  return API.GET('/pexam/_/getCustomSubject', params);
};

// 是否可以编辑考试信息
export const editWhether = params => {
  return API.POST('/pexam/exam/editWhether', params);
};
// 创建考试报告列表
export const createdExam = params => {
  return API.POST('/pexam/exam/createdExam', params);
};
// 获取考试报告列表
export const getExamReportList = params => {
  return API.POST('/pexam/exam/getExamReportList', params);
};
// 获取子报告列表
export const getChildReportList = async params => {
  const customSort = (a, b) => {
    const order = [105, 101, 102, 103];

    const indexA = order.indexOf(a.source);
    const indexB = order.indexOf(b.source);

    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB;
    }
    if (indexA !== -1) {
      return -1;
    }
    if (indexB !== -1) {
      return 1;
    }
    return 0;
  };

  let examList = [];
  let hasCampus = false;
  try {
    const res = await getExamReportList({
      schoolId: sessionSave.get('schoolInfo').id,
      parentId: params.examId,
      acadYearsId: '',
      acadTermId: '',
      gradeId: '',
      subjectId: '',
      categoryId: '',
      keyWord: '',
      page: 1, // 页码
      pageSize: 100,
    });
    examList = res.data.list;
    hasCampus = examList.some(item => item.campusCode);
    if (hasCampus) {
      examList = examList.filter(item => {
        if (UserRole.isOperation || UserRole.isSchoolLeader) {
          return true;
        }
        if (!item.campusCode && [101, 102, 103].includes(item.source)) {
          return true;
        }
        const campusCode = item.campusCode;
        return UserRole.campusList.some(t => t.code == campusCode);
      });
    }
    examList.sort(customSort);
    examList.forEach(item => {
      if (item.source == 101 || item.source == 102 || item.source == 103) {
        if (params.examName) item.examName = params.examName;
      }
    });
  } catch (error) {
    console.log(error);
    examList = [];
  }
  return {
    hasCampus,
    examList,
  };
};

// 获取考试讲评列表
export const getCommentExamList = params => {
  return API.POST('/pexam/exam/getCommentExamList', params);
};

// 设置学生评价：优秀作答|典型错误
export const setExamStuEvaluate = params => {
  return API.POST('/homework/cloudexam/setExamStuEvaluate', params, baseUrl);
};

//获取考试报告得分率平均分
export const statGrdRate = params => {
  return API.POST('/pexam/exam/statGrdRate', params);
};
// 删除考试报告列表
export const deleteExamInfo = params => {
  return API.POST('/pexam/exam/deleteExamInfo', params);
};
// 获取默认临界值
export const getDefaultData = params => {
  return API.POST('/pexam/exam/getDefaultData', params);
};
// 获取总览页 头部统计数据
export const getStatisticData = params => {
  return API.GET('/pexam/_/getStatisticData', params);
};
// 获取总览页 重点关注学生数据
export const getAttentionStuList = params => {
  return API.GET('/pexam/_/exam/getAttentionStuList', params);
};
// 获取总览页 高频错题数据
export const getHighMIstakes = params => {
  return API.GET('/pexam/_/getHighMIstakes', params);
};
// 获取总览页 成绩分布数据
export const getScoreDistribute = params => {
  // return API.POST('/pexam/exam/getScoreDistribute', params);
  return API.GET('/pexam/_/getScoreDistribute', params);
};
// 获取总览页 学生等级分布数据
export const getGradeDistribute = params => {
  return API.GET('/pexam/_/getGradeDistribute', params);
};
// 获取学优生学困生表格数据
export const getTopAndDiffStu = params => {
  return API.GET('/pexam/_/list/fine-bad', params);
};
// 获取知识点列表
export const getKnowledgePoint = params => {
  return API.GET('/pexam/_/getKnowledgePoint', params);
};
// 获取知识点分析——题目详情
export const getQuesDetail = params => {
  return API.GET('/pexam/exam/getQuesDetail', params);
};
// 获取知成绩单 ——学生表格数据
export const getReportCard = params => {
  return API.POST('/pexam/exam/getReportCard', params);
  // return API.GET("/pexam/_/getReportCard", params);
};
// 获取知成绩单 ——学生表格数据
export const getNewReportCard = params => {
  return API.GET('/pexam/_/getReportCard', params);
};

// 获取成绩单 —— 学生在相邻三次考试中，个人得分率和班级得分率折线图
export const getThreeTestData = params => {
  return API.GET('/pexam/_/getThreeTestData', params);
};
// 获取成绩单 —— 某个学生 所有题目 得分分析
export const getStuQuesAnalyze = params => {
  return API.GET('/pexam/_/getStuQuesAnalyze', params);
};
// 获取成绩单 —— 某个学生的知识点掌握情况
export const getStuknowlPointAnalyze = params => {
  return API.POST('/pexam/exam/getStuknowlPointAnalyze', params);
};
// 获取成绩单 —— 获取对比考试列表
export const getContrastTestList = params => {
  return API.GET('pexam/_/getContrastTestList', params);
};
// 获取成绩单 ——确定添加对比考试接口
export const setContrastTest = params => {
  return API.POST('/pexam/exam/setContrastTest', params);
};
// 获取成绩单 ——获取试卷讲评学科
export const getTestPaperSub = params => {
  return API.GET('/pexam/exam/getTestPaperSub', params);
};
// 获取成绩单 ——获试卷讲评
export const getTestPaperComment = params => {
  return API.GET('/pexam/_/exam/getTestPaperComment', params);
};
// 获取考试班级
export const listExamStu = params => {
  return API.GET('/pexam/_/listExamStu', params);
};

// 班级对比 ——一分五率
export const getScoreRate = params => {
  return API.GET('/pexam/_/getScoreRate', params);
};

/**
 * @description: 一分五率（教师）
 */
export const getTeaScoreRate = params => {
  return API.GET('/pexam/_/getTeaScoreRate', params);
};

// 班级对比 ——一分五率
export const getScoreRate2 = params => {
  return API.GET('/pexam/_/getScoreRate', params);
};
// 班级对比 ——题型均分
export const getQuesTypeAvg = params => {
  return API.GET('/pexam/_/exam/getQuesTypeAvg', params);
};
// 班级对比 ——小题均分
export const getSmallGrdQuesAvg = params => {
  return API.GET('/pexam/_/getSmallGrdQuesAvg', params);
  return API.POST('/pexam/exam/getSmallGrdQuesAvg', params);
};
export const getSmallClsQuesAvg = params => {
  return API.POST('/pexam/exam/getSmallClsQuesAvg', params);
};
// 班级对比 ——分数段分布
export const getGrdScoreSection = params => {
  return API.POST('/pexam/exam/getGrdScoreSection', params);
};
export const getClsScoreSection = params => {
  return API.POST('/pexam/exam/getClsScoreSection', params);
};
// 班级对比 ——总名次分布
export const getTotalRanking = params => {
  return API.GET('/pexam/_/exam/getTotalRanking', params);
};
// 班级对比 ——各名次分布
export const getRankSection = params => {
  return API.POST('/pexam/exam/getRankSection', params);
};
// 班级对比 ——箱体图
export const getBoxData = params => {
  return API.GET('/pexam/_/getBoxData', params);
};
// 临界生 ——整体概况
export const getAllSurvey = params => {
  return API.POST('/pexam/exam/getAllSurvey', params);
};
// 临界生 ——学生列表
export const getAllSurveyStuList = params => {
  return API.POST('/pexam/exam/getAllSurveyStuList', params);
};
// 试卷分析 ——命题质量：获取信度、区分度、难度、获取难度和知识点分布等等**
export const getQualityInfo = params => {
  return API.GET('/pexam/_/getQualityInfo', params);
};

// 试卷分析 ——双向细目表
export const getBothWayList = params => {
  return API.GET('/pexam/_/exam/getBothWayList', params);
};
// 试卷分析 ——小题分析
export const getReportSmallAnalysis = params => {
  return API.GET('/pexam/_/getReportSmallAnalysis', params);
};
// 试卷分析 ——作答详情：获取班级列表
export const getClassList = params => {
  return API.POST('/pexam/exam/getClassList', params);
};
// 试卷分析 ——作答详情：作答详情列表
export const getReportStuAnswerDetails = params => {
  return API.POST('/pexam/exam/getReportStuAnswerDetails ', params);
};

// 查询知识点
export function findPointSubject(data) {
  return API.POST('/pexam/catalog/getPointTree', data);
}

/**
 * @name: 获取匹配知识点树
 */
export const getMatchPointTreeAPI = params => {
  return API.GET('/pexam/catalog/getMatchPointTree', params);
};

// 获取学科知识点树
export const getPointTreeAPI = params => {
  return API.GET('/pexam/catalog/getPointTree', params);
};

// 保存知识点
export const savePointDetailAPI = params => {
  return API.GET('/pexam/catalog/savePointDetail', params);
};

// 移除知识点
export const removePointAPI = params => {
  return API.GET('/pexam/catalog/removePoint', params);
};

// 移动知识点
export const moveChildPointAPI = params => {
  return API.GET('/pexam/catalog/moveChildPoint', params);
};

// 自动匹配知识点
export const automaticMatchPointAPI = params => {
  return API.GET('pexam/catalog/automaticMatchPoint', params);
};

// 薄弱知识点列表
export const listClsWeakPoint = params => {
  return API.GET('/pexam/learn/listClsWeakPoint', params);
};
// 获取年级和班级级联数据
export const listGrdOrCls = params => {
  return API.GET('/pexam/exam/listGrdOrCls', params);
};
// 获取班级学生
export const listClsStu = params => {
  return API.GET('/pexam/learn/listClsStu', params);
};
// 获取学生成绩趋势
export const getStuScoreTrend = params => {
  return API.GET('/pexam/learn/getStuScoreTrend', params);
};
// 学生关注
export const followStu = params => {
  return API.GET('/pexam/learn/followStu', params);
};
// 知识点掌握情况
export const getStuPoint = params => {
  return API.GET('/pexam/learn/getStuPoint', params);
};
// 学生知识点掌握情况
export const getStuWeakPoint = params => {
  return API.GET('/pexam/learn/getStuWeakPoint', params);
};
// 获取共性错题
export const listErrQues = params => {
  return API.GET('/pexam/_/listErrQues', params);
};
// 获取学生错题本
export const listStuErrQues = params => {
  return API.GET('/pexam/_/listStuErrQues', params);
};
// 加入移除试卷袋
export const savePaper = params => {
  return API.POST('/pexam/learn/savePaper', params);
};
// 获取试卷袋
export const getPaper = params => {
  return API.GET('/pexam/learn/getPaper', params);
};
// 获取预览基本信息
export const getViewPaper = params => {
  return API.GET('/pexam/learn/getViewPaper', params);
};
// 获取校本题库中试卷列表
export const listPaper = params => {
  return API.GET('/pexam/learn/listPaper', params);
};

// 删除试卷
export const delPaper = params => {
  return API.GET('/pexam/learn/delPaper', params);
};

//
export const openPaper = params => {
  return API.GET('/pexam/learn/openPaper', params);
};
//获取班级
export const ClassList = params => {
  return API.GET('/pexam/_/list/exam/clz', params);
};
/**
 * @name: 试卷回收
 * @param schoolId 学校id
 * @param sourceId 试卷或者个册ID
 * @param createType 1：普通老师 2：备课组长
 */
export const getPaperClassAPI = params => {
  return API.GET('/pexam/scan/getPaperClass', params);
};
/**
 * @name: 试卷回收 - 保存试卷
 * @param schoolId 学校id
 * @param userId 用户ID
 * @param title 试卷或者个册名称
 * @param sourceId 试卷或者个册ID
 * @param type 1：个册，2：试卷
 * @param subjectId 学科id
 * @param gradeId 年级ID
 * @param reviewType 1：手阅 2：网阅 3：在线作业
 * @param createType 1：普通老师 2：备课组长
 * @param classIds 班级ID（多个班级使用英文逗号隔开）
 */
export const saveScanPaperAPI = params => {
  return API.POST('/pexam/scan/saveScanPaper', params);
};
/**
 * @name: 试卷回收 - 获取作业id
 * @param schoolId 学校id
 * @param userId 用户ID
 * @param sourceId 试卷或者个册ID
 * @param createType 1：普通老师 2：备课组长
 */
export const getScanPaperWorkIdAPI = params => {
  return API.GET('/pexam/scan/getScanPaperWorkId', params);
};
/**
 * @name: 试卷回收 -试卷导出
 * @param schoolId 学校id
 * @param sourceId 试卷ID
 * @param title 试卷名称
 * @param exportType 导出类型
     stu_paper_no_answer：学生用卷（无答案解析）
     tea_paper_ans_on_ques_after：教师用卷（答案在每一题目后面）
     paper_ans_on_end: 普通用卷(答案在最后面)
     ans_paper：纯答案用卷
 * @param paperType 纸张类型 A4, B5
 */
export const exportPaperAPI = params => {
  return API.GET('/pexam/scan/exportPaper', params);
};
/**
 * 扫描记录列表
 * @param params
 * @returns {*}
 */
export const scanRecordList = params => {
  return API.GET('/pexam/scan/recordList', params);
};

/**
 * 获取试卷下的学生列表
 * @param params
 * @returns {*}
 */
export const getPaperStudent = params => {
  return API.GET('/pexam/scan/paperStudent', params);
};

/**
 * 获取试卷信息
 * @param params
 * @returns {*}
 */
export const getPaperInfo = params => {
  return API.GET('/pexam/scan/getPaperInfo', params);
};

/**
 * 获取学生个册
 * @param params
 * @returns {*}
 */
export const getStudentBook = params => {
  return API.GET('/pexam/scan/getStudentBook', params);
};

/**
 * 获取学生扫描的试卷信息
 * @param params
 * @returns {*}
 */
export const getStudentPaperInfo = params => {
  return API.GET('/pexam/scan/studentPaperInfo', params);
};

/**
 * 保存新的学生考号
 * @param params
 * @returns {*}
 */
export const updateStudentNo = params => {
  return API.POST('/pexam/scan/updateStudentNo', params);
};

/**
 * 删除学生
 * @param params
 * @returns {*}
 */
export const deleteStudent = params => {
  return API.POST('/pexam/scan/deleteStudent', params);
};

/**
 *
 * @param paperId
 * @returns {*}
 */
export const updatePaperScore = paperId => {
  return API.POST('/pexam/scan/updatePaperScore', { paperId });
};

/**
 * @name: 考号设置 - 获取学校列表
 * @param provinceId 省id(可不传)
 * @param cityId 市id(可不传)
 * @param districtId 区id(可不传)
 * @param userId 用户ID(可不传)
 * @param userType 用户类型(可不传) 1：教师 2：学生 3：家长 0：注册用户 4：代理商
 * @param keyWord 关键词
 * @param page 页码
 * @param limit 请求条目
 */
export const getSchoolListAPI = params => {
  return API.GET('/pexam/examNumber/getSchoolList', params);
};
/**
 * @name: 考号设置 -一键生成学校考号
 * @param schoolId 学校id
 */
export const generateAPI = params => {
  return API.GET('/pexam/examNumber/generate', params);
};
/**
 * @name: 考号设置 -刷新考号生成状态
 * @param schoolId 学校id
 */
export const updateSchoolExamNumberAPI = params => {
  return API.GET('/pexam/examNumber/updateSchoolExamNumber', params);
};
/**
 * @name: 考号设置 -考号导出
 * @param schoolId 学校id
 * @param emailAddress 邮箱
 * @param optUserId 操作人ID
 */
export const exportNumberAPI = params => {
  return API.GET('/pexam/examNumber/sendExportSchoolStudentMessage', params);
};

/**
 * 获取班级考号设置信息
 * @param params
 * @returns {*}
 */
export const getClassStudentNumber = params => {
  return API.POST('/pexam/examNumber/getClassStudentNumber', params);
};

// 保存个册的学生作答信息
export const examImportStu = params => {
  return API.POSTJson('/pexam/_/import/save', params);
};

// 获取个册导入成绩信息
export const getImportInfo = params => {
  return API.GET('/pexam/_/exam/getImportInfo', params);
};

// 获取导入班级学生信息
export const getExamClsStu = params => {
  return API.GET('/pexam/_/getExamClsStu', params);
};
// 获取上线分析数据
export const getOnlineAPI = params => {
  return API.GET('/pexam/_/line/getData', params);
};
// 获取分数线配置
export const getLineConfAPI = params => {
  return API.GET('/pexam/_/line/getConf', params);
};
/**
 * @name: 获取默认上线设置
 * @param examIds 考试id
 * @param type type：1:成绩 2:排名
 */
export const getDefaultFracLineConfAPI = params => {
  return API.GET('/pexam/_/exam/getDefaultFracLineConf', params);
};
// 获取临界生数据
export const getCriticalConfAPI = params => {
  return API.GET('/pexam/_/crisis/getData', params);
};
// 设置配置
export const setConfAPI = params => {
  return API.GET('/pexam/_/line/setConf', params);
};
// 临界生初始化
export const getConfCrisisAPI = params => {
  return API.GET('/pexam/_/crisis/getConf', params);
};
// 获取配置
export const getConfAPI = params => {
  return API.GET('/pexam/_/exam/getConf', params);
};
// 设置配置
export const setAllConfAPI = params => {
  return API.POST('/pexam/_/exam/setConf', params);
};
// 获取考试成绩确认学生列表
export const getScoreConfirmStuListAPI = params => {
  return API.GET('/pexam/exam/getScoreConfirmStuList', params);
};
// 获取考试确认学生统计
export const getScoreConfirmStatAPI = params => {
  return API.GET('/pexam/exam/getScoreConfirmStat', params);
};
// 获取广奥班级
export const clzListAPI = params => {
  return API.GET('/pexam/_/line/clzList', params);
};
// 获取学情追踪数据
export const getTrackHistory = params => {
  return API.POST('/pexam/_/export/history', params);
};
// 获取学生历次统计分析
export const getStuStat = params => {
  return API.GET('/pexam/_/exam/stu/stat', params);
};
// 获取学生列表
export const getStuList = params => {
  return API.GET('/pexam/_/exam/stu/list', params);
};
// 获取班级历史考试平均分
export const getClzHistoryScore = params => {
  return API.GET('/pexam/_/history/clz', params);
};
// 获取班级历史考试平均分
export const getOnlineHisList = params => {
  return API.GET('/pexam/_/line/his/list', params);
};
// 获取班级历史一分五率
export const getHisFiveRateAPI = params => {
  return API.GET('/pexam/_/his-five-rate', params);
};
// 获取考试题号列表
export const getExamQuesNoList = params => {
  return API.GET('/pexam/_/getExamQuesNoList', params);
};
// 根据考试获取扫描数据集（题目数据）
export const getQuesScanImg = params => {
  return API.GET('/pexam/_/get-paper-scan-img', params);
};
// 保存扫描数据集
export const savePaperScanImg = params => {
  return API.POSTJson('/pexam/_/save-paper-scan-img', params);
};
// 保存扫描数据集
export const saveInnerPaperScanImg = params => {
  return API.POSTJson('/pexam/_/save-paper-scan-img-inner', params);
};
// 根据考试获取异常统计
export const getErrorNums = params => {
  return API.GET('/pexam/_/ex-card-stat', params);
};

//获取扫描批次列表
export function getScanTaskList(params) {
  return API.GET('/pscan/task/getTaskList', params);
}

/**
 * 获取扫描进度
 * @param params
 * @returns {*}
 */
export function getTaskProcess(params) {
  return API.GET('/pscan/task/getTaskProcess', params);
}

/**
 * 删除批次
 * @param params
 * @returns {*}
 */
export function deleteScanTask(params) {
  return API.POST('/pscan/task/deleteTask', params);
}

/**
 * 重新识别
 * @param params
 * @returns {*}
 */
export function taskReDetect(params) {
  return API.POST('/pscan/task/taskReDetect', params);
}

/**
 * 更新任务考试信息
 * @param params
 * @returns {*}
 */
export function updateTaskExam(params) {
  return API.POST('/pscan/task/updateTask', params);
}

/**
 * 异常图片进行重新识别
 * @param params
 * @returns {*}
 */
export function imageCodeDetect(params) {
  return API.POST('/pscan/scanImage/imageCodeDetect', params);
}

//获取批次图片数据
export function getScanPaperDataAPI(params) {
  return API.GET('/pexam/_/scan-paper-data', params);
}

//获取批次列表
export function getBatchListAPI(params) {
  return API.GET('/pscan/task/getBatchList', params);
}
//根据考试获取扫描数据集（考号异常）
export function getExamNoAPI(params) {
  return API.GET('/pexam/_/scan-exam-no-ex', params);
}
//根据考试获取重复考号学生
export function getExamRepeatNoAPI(params) {
  return API.GET('/pexam/_/scan-exam-stu-ex', params);
}
//获取学生信息
export function searchStuInfoAPI(params) {
  return API.GET('/pexam/examNumber/searchStuInfo', params);
}
//保存考号异常数据
export function saveExamNoImgAPI(params) {
  return API.POSTJson('/pexam/_/save-ex-scan-img', params);
}
//保存考号异常数据
export function saveExamNoAPI(params) {
  return API.POSTJson('/pscan/scanImage/saveStuNo', params);
}
//异常处理完毕
export function sendMergeMsgAPI(params) {
  return API.GET('/pexam/_/send-merge-msg', params);
}
//异常处理完毕
export function saveScanPageAPI(params) {
  return API.POSTJson('/pexam/_/save-scan-page', params);
}
/**
 * @name: 导出答题卡
 * @param paperId 试卷id
 * @param type 板式类型 A4 A3
 */
export const examCard = params => {
  params.v = 'v2';
  return API.GET('/pexam/_/print/card', params);
};
/**
 * @name: 获取试卷信息
 */
export const getExamInfoAPI = params => {
  return API.GET('/pexam/exam/getExamInfo', params);
};
/**
 * @name: 获取试卷发布状态
 */
export const getPublishScoreStateAPI = params => {
  return API.GET('/pexam/exam/getPublishScoreState', params);
};
/**
 * @name: 删除扫描数据
 */
export const deleteScanPageAPI = params => {
  return API.POSTJson('/pexam/_/rem-scan-page', params);
};
/**
 * @name: 保存处页码异常数据
 */
export const savePageAPI = params => {
  return API.POSTJson('/pexam/_/save-ex-scan-page', params);
};
/**
 * @name: 依据考号获取学生扫描数据
 */
export const getStuScanDataAPI = params => {
  return API.GET('/pexam/_/scan-paper-stu-no', params);
};
/**
 * @name: 获取全部题目数据
 */
export const getAllQuestions = params => {
  return API.GET('/pexam/_/ex-card-ques-stat', params);
};
/**
 * @name: 获取全部题目作答图片
 */
export const getAllQuestionsImg = params => {
  return API.GET('/pexam/_/get-paper-scan-img-all', params);
};
/**
 * @name: 标记答卷为正常
 */
export const markNormalAPI = params => {
  return API.POSTJson('/pexam/_/save-ex-paper', params);
};
/**
 * @name: 关联答题卡
 */
export const relateAnswerCard = params => {
  return API.POST('/pexam/exam/relateAnswerCard', params);
};
/**
 * @name: 更新进度接口
 */
export const updateProgress = params => {
  return API.POST('/pexam/exam/updateProgress', params);
};
export const getStuAnswerDetails = params => {
  return API.GET('/pexam/_/getReportStuAnswerDetails', params);
};
/**
 * @name:取消关联答题卡
 */
export const cancelRelateAnswerCard = params => {
  return API.POST('/pexam/exam/cancelRelateAnswerCard', params);
};
/**
 * @name:根据pbookid获取examId
 */
export const getPersonalBookInfo = params => {
  return API.GET('/pexam/exam/getPersonalBookInfo', params);
};
/**
 * @name:根据id获取考试信息
 */
export const getPreTaskInfo = params => {
  return API.GET('/pscan/task/getPreTaskInfo', params);
};
/**
 * @name:获取教学监管考试统计数据
 */
export const getStatScanExam = params => {
  return API.GET('/pscan/_/stat-scan-exam', params);
};
/**
 * @name:获取扫描考试统计数据
 */
export const getStatScanData = params => {
  return API.GET('/pscan/_/stat-scan-data', params);
};
/**
 * @name:导出扫描数据
 */
export const exportScanData = params => {
  return API.GET('/pscan/_/export-scan-data', params);
};
/**
 * @name:一键处理全部题目
 */
export const deleteAllQues = params => {
  return API.GET('/pscan/_/ex-ques-all', params);
};

/**
 * @name:保存参考信息
 */
export const saveExamSubjectInfo = params => {
  return API.POST('/pexam/exam/saveExamSubjectInfo', params);
};

/**
 * @name:获取大精学生成绩
 */
export const listStuScore = params => {
  return API.GET('/pexam/_/list-stu-score', params);
};
/**
 * @name:获取大精单个学生成绩
 */
export const getStuScore = params => {
  return API.GET('/pexam/_/get-stu-score', params);
};

/**
 * @name:设置大精单个学生成绩
 */
export const setStuScore = params => {
  return API.POSTJson('/pexam/_/set-stu-score', params);
};

/**
 * @name:设置大精单个学生成绩
 */

/**
 * @description: 设置学生批改类型
 * @param {*} params {id, type} id：学生成绩id，type：0:已批改(已扫描) 1:未扫描 2:缺考 3:批改不完整
 */
export const setStuType = params => {
  return API.POST('/pexam/_/set-stu-type', params);
};

/**
 * @name:发布成绩
 */
export const publishScore = params => {
  const urlSearch = new URLSearchParams(params);
  return API.POST('/pexam/_/publish-score?' + urlSearch.toString(), params);
};

/**
 * @name:获取缺考名单
 */
export const listAbsentStu = params => {
  return API.GET('/pexam/_/list-absent-stu', params);
};

/**
 * 根据id获取图片信息
 * @param id
 * @returns {*}
 */
export function getScanImageAPI(params) {
  return API.GET('/pexam/_/scan-point-ex', params);
}

/**
 * 设置图片为定位点异常
 * @param id
 * @returns {*}
 */
export function setImagePointError(params) {
  return API.POST('/pexam/_/set-point-weight', params);
}
/**
 * 设置图片为定位点异常
 * @param id
 * @returns {*}
 */
export function setInnerImagePointError(params) {
  return API.POST('/pexam/_/set-point-weight-inner', params);
}
/**
 * 设置缺考
 * @param id
 * @returns {*}
 */
export function setAbsentAPI(params) {
  return API.POSTJson('/pexam/_/set-absent', params);
}
/**
 * 设置ab卷
 * @param id
 * @returns {*}
 */
export function setABPaperAPI(params) {
  return API.POSTJson('/pexam/_/set-ab-card', params);
}
/**
 * 获取已完成制卡的列表
 * @returns {*}
 */
export function getOverCardListAPI(params) {
  return API.GET('/paper/_/stat-paper', params);
}
/**
 * 设置计分方式
 * @returns {*}
 */
export function setScoringModeAPI(params) {
  return API.GET('/pexam/_/mod-scoring-mode', params);
}

/**
 * 获取计分方式
 * @returns {*}
 */
export function getScoringModeAPI(params) {
  return API.GET('/pexam/_/get-scoring-mode', params);
}

/**
 * 获取批次图片
 * @param params
 * @returns {*}
 */
export function getTaskImages(params) {
  return API.GET('/pscan/task/getTaskImages', params);
}

/**
 * 获取扫描设置
 * @param id
 * @returns {*}
 */
export function getTaskSetting(id) {
  return API.GET('/pscan/task/getTaskInfo', { id: id });
}

/**
 * @description: 获取考试满分（包含附加题）
 * @param {*} params  {examId, subjectId}
 */
export function getExamFull(params) {
  return API.GET('/pexam/_/get-exam-full', params);
}
/**
 * @description: 获取赋分规则
 */
export function getExamScoreRuleList(params) {
  return API.GET('/pexam/exam/getExamScoreRuleList', params);
}

/**
 * @description: 根据规则ID获取赋分规则
 * @param {*} params {id}
 */
export function getExamScoreRuleByRuleId(params) {
  return API.GET('/pexam/exam/getExamScoreRuleByRuleId', params);
}
/**
 * @description: 获取会员信息
 * @param {*} params {id}
 */
export function getVipInfo(params) {
  return API.GET('/psocket/vip/getVipInfo', params);
}
/**
 * @description: 获取有异常未处理考试数据
 * @param {*} params type: 1 //1有异常  0无异常
 * @param {*} params schoolId
 * @param {*} params page: 1
 * @param {*} params pageSize: 10
 */
export function getErrorExamNums(params) {
  return API.GET('/pscan/task/examList', params);
}
/**
 * @description: 获取已发布成绩的考试列表
 * @param {*} params {schoolId}
 * @param {*} params {acadYearsId}
 * @param {*} params {subjectId}
 * @param {*} params {keyWord}
 * @param {*} params {clzId}
 * @param {*} params {page}
 * @param {*} params {pageSize}
 * @param {*} params {orderType}
 * @param {*} params {desc}
 * @param {*} params {orderType}
 */
export function getPublishExamList(params) {
  return API.GET('/pexam/exam/getPublishExamList', params);
}
/**
 * 获取待处理考试列表
 * @param {*} params
 * @returns
 */
export function getTeacherExams(params) {
  return API.GET('/pscan/task/getTeacherExams', params);
}
/**
 * 获取合并状态
 * @param {*} params
 * @returns
 */
export function getMergeStatus(params) {
  return API.GET('/pscan/task/getMergeStatus', params);
}

/**
 * 延迟合并
 * @param {*} params
 * @returns
 */
export function examDelayMerge(params) {
  return API.GET('/pscan/scanImage/examDelayMerge', params);
}

/**
 * 获取某个异常所有图片
 * @param {*} params
 * @returns
 */
export function getErrorImages(params) {
  return API.GET('/pscan/scanImage/getErrorImages', params);
}

/**
 * 转换图片并重新识别
 * @param {*} params
 * @returns
 */
export function imagesMove(params) {
  return API.POST('/pscan/scanImage/imagesMove', params);
}

/**
 * 获取学科列表，包含综合学科
 * @param {*} params
 * @returns
 */
export function getExamSubjectMulti(params) {
  return API.GET('/pexam/_/getExamSubjectMulti', params);
}

// 获取常用报表
export function listOftenReport(params) {
  return API.GET('pexam/_/list-often-report', params);
}

// 保存常用报表
export function saveOftenReport(params) {
  return API.POSTJson('pexam/_/save-often-report', params);
}

/**
 * @description: 获取学科对比数据
 * @param {*} params {examId, classId, v}
 * @return {*}
 */
export function getSubjectRate(params) {
  return API.GET('pexam/_/getSubjectRate', params);
}

/**
 * @description: 获取小题均分列表
 * @param {*} params {examId, subjectId, v}
 * @return {*}
 */
export function listQuesAvg(params) {
  return API.GET('pexam/_/listQuesAvg', params);
}

/**
 * @description: 根据学科ID获取赋分规则
 * @param {*} params 
 * @param sourceId   来源ID
    @param sourceType 来源类型 0:省 1:考试
    @param subjectId  学科ID
    @param divType 赋分类型 1:分值按比例 2:分值按分数 3:区间按比例 4:区间按分数
 * @return {*} 
 */
export function getExamScoreRuleBySubjectId(params) {
  return API.GET('pexam/exam/getExamScoreRuleBySubjectId', params);
}

/**
 * @description: 设置赋分规则ID
 * @param {*} params
 * @param examId 考试ID
 * @param ruleId 赋分规则ID,自定义赋分时传:custom
 * @param type   0:设置赋分规则ID 1:设置自定义赋分规则ID
 * @return {*}
 */
export function setScoreRuleId(params) {
  return API.POST('pexam/exam/setScoreRuleId', params);
}

/**
 * @description: 设置赋分规则
 * @param {*} params
 * @param id            主键,有值为更新,无值为新增
    @param sourceId      来源ID
    @param name          规则名称 (自定义赋分给值:custom)
    @param mode          选考模式 (自定义赋分给值:custom)
    @param subjectIds    学科ID,多个逗号分隔
    @param subjectNames  学科名称,多个逗号分隔
    @param rule          赋分规则 json
    @param includedScore 是否计入总分 0:不计入 1:计入
    @param divType       赋分类型 1:分值按比例 2:分值按分数 3:区间按比例 4:区间按分数
 * @return {*}
 */
export function setExamScoreRule(params) {
  return API.POST('pexam/exam/setExamScoreRule', params);
}

/**
 * @description: 保存赋分规则设置
 * @param examId 考试ID
 * @return {*}
 */
export function saveScoreRuleSet(params) {
  return API.POST('pexam/exam/saveScoreRuleSet', params);
}

/**
 * @description: 根据学科ID删除赋分规则
 * @param {*} params
 * @param sourceId   来源ID
 * @param sourceType 来源类型 0:省 1:考试
 * @param subjectId  学科ID
 * @return {*}
 */
export function delExamScoreRuleBySubjectId(params) {
  return API.GET('pexam/exam/delExamScoreRuleBySubjectId', params);
}

/**
 * @description: 获取已扫描学科
 * @param examId 考试ID
 * @return {*}
 */
export function getAlreadyScanSubjectList(params) {
  return API.GET('/pexam/exam/getAlreadyScanSubjectList', params);
}

/**
 * @description: 获取考试缺考名单
 * @param examId 考试ID
 * @return {*}
 */
export function getMissExamListAPI(params) {
  return API.GET('pexam/exam/getMissExamList', params);
}

/**
 * @description: 导出一分五率（班级对比）
 * @param {*} params
 * @param examId   考试ID
 * @param contrastId 对比考试ID
 * @param subjectId  学科ID
 * @param v
 * @return {*}
 */
export const exportScoreRate = params => {
  return API.GET('pexam/_/exportScoreRate', params);
};

/**
 * @description: 导出一分五率（班级对比）
 * @param {*} params
 * @param vos   [{bookId: "",roles: []}]
 */
export const setExamRole = params => {
  return API.POSTJson('pexam/_/set-exam-role', params);
};

/**
 * @description: 获取学生已发布的学科
 * @param {*} params
 * @param examId
 */
export const listStuSubject = params => {
  return API.GET('pexam/_/list-stu-subject', params);
};

// 获取发布的学科
export const getScoreSubject = params => {
  params.statType = 1;
  return listExamSubject(params);
};

// 获取发布的学科
export const publishedSubject = params => {
  params.statType = 1;
  return listExamSubject(params);
};

// 获取考试学科
export const getExamSubject = params => {
  params.statType = 0;
  return listExamSubject(params);
};

// 获取考试学科
export const getExamSubjectList = params => {
  return API.GET('/pexam/exam/getExamSubjectList', params);
};

/**
 * 获取考试学科
 * @param {Object} params
 * @param params.examId 考试ID
 * @param {=} params.statType 0：全部学科 1：已发布成绩学科 默认为0
 * @param {=} params.role 角色列表
 * @param {=} params.v 缓存版本号
 * @returns
 */
export const listExamSubject = async params => {
  if (!params.v) {
    try {
      let { data } = await getExamCache({ examId: params.examId });
      params.v = data.v;
    } catch (error) {}
  }
  return API.GET('pexam/_/list-exam-subject', params);
};

/**
 * 获取考试版本号 v
 * @param {Object} params
 * @param params.examId 考试Id
 * @param {=} params.subjectId 学科Id
 */
export const getExamCache = params => {
  return API.GET('pexam/_/get-exam-cache', params);
};

// 添加考试学科
export const addExamSubject = params => {
  return API.POSTJson('pexam/_/add-exam-subject', params);
};

// 删除考试学科
export const remExamSubject = params => {
  return API.GET('pexam/_/rem-exam-subject', params);
};

// 删除已补录试卷
export const remExamPaper = params => {
  return API.POST('pexam/_/rem-paper', params);
};

// 获取考试组合榜学科
export const getGroupSubject = params => {
  return API.GET('pexam/_/getGroupSubject', params);
};

/**
 * 获取组合榜
 * @param examId 考试Id
 * @param classId 班级Id
 * @param aliasName 组合别名
 * @param qType 0: 得分 1: 赋分
 * @param text 查询条件
 * @param sort 排序 排序stuNo, total,subject_2, subject_3等，倒序-stuNo,-total等
 * @param page 当前页
 * @param pageSize 每页显示条数
 * @param v 缓存版本号
 **/
export const listStuGroup = params => {
  return API.GET('pexam/_/list-stu-group', params);
};

// 获取考试年份
export const listExamYear = params => {
  return API.GET('pexam/_/list-exam-year', params);
};

// 添加、删除考试管理员
export const addOrDelExamLeaderIds = params => {
  return API.POST('pexam/exam/addOrDelExamLeaderIds', params);
};

// 获取考试管理员
export const getExamLeader = params => {
  return API.GET('pexam/exam/getExamLeader', params);
};

// 获取选做题
export const getPaperChoice = params => {
  return API.GET('pexam/_/get-paper-choice', params);
};

// 获取学生历次考试
export const listStuHisExam = params => {
  return API.GET('pexam/_/list-stu-his-exam', params);
};
/**
 * 获取考试统计数据接口
 * @param schoolId   学校ID，用于指定要查询数据的学校
 * @param gradeId    年级ID，用于指定要查询数据的年级
 * @param subjectId  学科ID，用于指定要查询数据的学科
 * @param startTime  开始时间，格式为"yyyy-MM-dd HH:mm:ss"
 * @param endTime    结束时间，格式为"yyyy-MM-dd HH:mm:ss"
 * @param type       数据类型，0表示学科分析，1表示扫描分析
 */
export const getExamStat = params => {
  return API.GET('pexam/scanExam/getExamStat', params);
};

/**
 * 获取测评统计详情
 * @param {Object} params - 参数对象
 * @param  params.schoolId - 学校ID
 * @param  params.gradeId - 年级ID
 * @param  params.subjectId - 学科ID
 * @param  params.categoryId - 类别ID
 * @param  params.startTime - 开始时间，格式为yyyy-MM-dd HH:mm:ss
 * @param  params.endTime - 结束时间，格式为yyyy-MM-dd HH:mm:ss
 * @param  params.keyWord - 搜索关键字
 * @param  params.page - 当前第几页
 * @param  params.limit - 每页多少条
 */
export const getExamStatDetails = params => {
  return API.GET('pexam/scanExam/getExamStatDetails', params);
};

/**
 * 获取考试教师统计详情
 *
 * @param {Object} params - 查询参数
 * @param  params.schoolId - 学校ID
 * @param  params.gradeId - 年级ID
 * @param  params.subjectId - 学科ID
 * @param  params.categoryId - 类别ID
 * @param  params.startTime - 开始时间，格式为yyyy-MM-dd HH:mm:ss
 * @param  params.endTime - 结束时间，格式为yyyy-MM-dd HH:mm:ss
 * @param  params.keyWord - 搜索关键字（可选）
 * @param  params.orderType - 排序类型：0-按测评数排序，1-手阅数排序，2-网阅数排序，3-提交率排序
 * @param  params.desc - 是否降序排序：0-否，1-是
 * @param  params.page - 当前页数
 * @param  params.limit - 每页条数
 */
export const getExamTeaStatDetails = params => {
  return API.GET('pexam/scanExam/getExamTeaStatDetails', params);
};

/**
 * 获取学生考试统计详情
 *
 * @param {Object} params - 请求参数对象
 * @param params.schoolId - 学校ID
 * @param params.classId - 班级ID
 * @param params.gradeId - 年级ID
 * @param params.subjectId - 学科ID
 * @param params.categoryId - 类别ID
 * @param params.startTime - 开始时间，格式:yyyy-MM-dd HH:mm:ss
 * @param params.endTime - 结束时间，格式:yyyy-MM-dd HH:mm:ss
 * @param params.keyWord - 搜索关键字（可选）
 * @param params.orderType - 排序类型 0:按照练习数排序 1:按照提交数排序 2:按照未交数排序 3:按照提交率排序 4:按照优秀次数排序 5:按照不合格次数排序 6:错题数排序
 * @param params.desc - 是否降序排序，0:否 1:是
 */
export const getExamStuStatDetails = params => {
  return API.GET('pexam/scanExam/getExamStuStatDetails', params);
};

/**
 * 发布学校报告
 * @param {Object} params
 * @param params.examId 考试ID
 * @param params.optUserId 操作人ID
 * @returns
 */
export const publishSchoolReportAPI = params => {
  return API.POST('pexam/scanExam/publishSchoolReport', params);
};

/**
 * 获取当前考试最近一场考试
 * @param {Object} params
 * @param {*} params.examId
 * @param {*} params.subjectId
 * @param {*} params.v
 * @returns
 */
export const getHisExam = params => {
  return API.GET('pexam/_/get-his-exam', params);
};

/**
 * @description: 讲评添加变式题
 * @param {*} params
 * @return {*}
 */
export const addQsLink = params => {
  return API.GET('pexam/_/add-qs-link', params);
};

/**
 * @description: 讲评移除变式题
 * @param {*} params
 * @return {*}
 */
export const remQsLink = params => {
  return API.GET('pexam/_/rem-qs-link', params);
};

/**
 * 获取历次考试列表
 * @param {Object} params - 请求参数对象
 * @param {any} params.schId - 学校ID
 * @param {any} params.categoryId - 类别ID
 * @param {any} params.grdId - 年级ID
 * @param {any} params.subjectId - 学科ID
 * @param {=} params.role - 角色
 * @param {=} params.stuId - 学生ID (可选)
 * @param {any} params.begin - 开始时间
 * @param {string} params.end - 结束时间
 * @param {number} params.page - 当前页码
 * @param {number} params.size - 每页条数
 */
export const listHisExamAPI = params => {
  return API.GET('pexam/_/list-his-exam', params);
};

/**
 * 获取用户历次考试数据
 */
export const getUserDataAPI = params => {
  return API.GET('pexam/_/get-user-data', params);
};

/**
 * 设置用户历次考试数据
 * @param {Object} params - 请求参数对象
 * @param {string} params.userId - 用户ID
 * @param {number} params.type - 数据类型：1-历次学情，2-常用报表
 * @param {Object} params.jCfg - 配置数据对象
 * @param {Array} params.jCfg.data - 考试数据数组
 * @param {string} params.jCfg.data[].id - 考试ID
 * @param {string} params.jCfg.data[].name - 考试名称
 * @param {number} params.jCfg.data[].start - 是否为起点：0-默认，1-起点
 * @returns {Promise} 返回请求Promise对象
 */
export const setUserDataAPI = params => {
  return API.POSTJson('pexam/_/set-user-data', params);
};

/**
 * 获取用户历次考试数据
 * @param {*} params
 * @param {string?} params.stuId - 学生ID
 * @param {string?} params.modify
 * @param {string?} params.v - 版本号
 * @returns
 */
export const listUserHisExamAPI = params => {
  return API.GET('pexam/_/list-user-his-exam', params);
};

/**
 * 获取学校配置
 * @param {Object} params
 * @param {*} params.schId 学校Id
 * @param {=} params.phase 学段：0幼儿园 1小学 2初中 3高中 4中职 5高职 6大学 7特殊学校 8其它
 * @param {*} params.type 类型：1:变式题 2:讲评列表 3:优秀作答 4：学生报告 5：试卷讲评 6：五率 7：等级 8：优困生 9：上线设置 10：分数段 11：名次设置 12：成绩统计规则
 * @param {=} params.examId 考试id
 * @param {=} params.gradeId 年级Id
 * @returns
 */
export const getSchCfgAPI = params => {
  return API.GET('pexam/_/get-sch-cfg', params);
};

/**
 * 设置学校配置
 * @param {*} vo
 * @returns
 */
export const setSchCfgAPI = params => {
  return API.POSTJson('pexam/_/set-sch-cfg', params);
};

/**
 * 批量设置学校配置列表
 * @param {*} vo
 * @returns
 */
export const setSchCfgListAPI = params => {
  return API.POSTJson('pexam/_/set-sch-cfg-list', params);
};
/**
 * 恢复学校配置
 * @param {Object} params
 * @param {=} params.schId 学校Id
 * @param {=} params.type 类型：1:变式题 2:讲评列表 3:优秀作答 4：学生报告 5：试卷讲评 6：五率 7：等级 8：优困生 9：上线设置 10：分数段 11：名次设置 12：成绩统计规则
 * @param {=} params.phase 学段：0幼儿园 1小学 2初中 3高中 4中职 5高职 6大学 7特殊学校 8其它
 * @param {=} params.gradeId 年级Id
 * @param {=} params.examId 考试Id
 * @returns
 */
export const restoreSchCfgAPI = params => {
  return API.GET('pexam/_/restore-sch-cfg', params);
};
/**
 * 取消识别
 * @param {*} vo
 * @returns
 */
export const cancelDetectAPI = params => {
  return API.POST('pscan/task/cancelDetect', params);
};

/**
 * 获取扣子token
 * @param {*} vo
 * @returns
 */
export const getCozeTokenAPI = params => {
  return API.POST('pscan/coze/getToken', params);
};

/**
 * 获取作文题目
 * https://doc.apipost.net/docs/detail/3fb34b2d2408000?target_id=30412565fbf005
 */
export const getCompositionQsAPI = params => {
  return API.GET('/pexam/_/get-composition-qs', params);
};

/**
 * 获取作文题号
 * @param {*} params
 * params.examId 考试ID
 * params.subjectId 学科ID
 * params.v 缓存版本号
 * params.abPaper 是否ab卷
 */
export const getCompositionQsnosAPI = params => {
  return API.GET('/pexam/_/get-composition-qsnos', params);
};
/**
 * 成绩分段分析
 * @param {*} params
 * @param {string} params.examId 考试ID
 * @param {string} params.subjectId 学科ID
 * @param {number} params.type 类型
 * @param {number} params.v 版本号
 * @returns
 */
export const statSegmentAPI = params => {
  return API.GET('/pexam/_/stat-segment', params);
};

/**
 * 获取目录提交率，得分率，小题得分率
 * @param {*} params
 * @returns
 */
export const getDirRateAPI = params => {
  return API.GET('pexam/_/get-dir-rate', params);
};

// 获取目录总览
export const getDirOverviewAPI = params => {
  return API.GET('pexam/_/get-dir-overview', params);
};

/**
 * 获取书架书籍
 */
export const getShelfBooksAPI = params => {
  return API.GET('pscan/book/getShelfBooks', params);
};

/**
 * 添加书架书籍
 */
export const addToShelfAPI = params => {
  return API.POST('pscan/book/addToShelf', params);
};

/**
 * 移除书架书籍
 * @param {*} params
 * @param {string} params.id
 * @param {string} params.userId
 */
export const removeFromShelfAPI = params => {
  return API.GET('pscan/book/removeFromShelf', params);
};

/**
 * 获取单个教辅目录扫描数据
 * @param {*} params
 * @param {string} params.schoolId
 * @param {string} params.teacherId
 * @param {string} params.bookCode
 * @param {string} params.catalogCode
 * @returns
 */
export const getCatalogScanDataAPI = params => {
  return API.GET('pscan/book/getCatalogScanData', params);
};

/**
 * 获取教师教辅扫描数据
 * @param {*} params
 * @param {string} params.schoolId
 * @param {string} params.teacherId
 * @param {string} params.bookCode
 */
export const getTeacherBookScanDataAPI = params => {
  return API.GET('pscan/book/getTeacherBookScanData', params);
};

/**
 * 获取扫描批次的第一组图片信息
 * @param {*} vo
 * @returns
 */
export const getTaskFirstImageAPI = params => {
  return API.GET('pscan/scanImage/getTaskFirstImage', params);
};

/**
 * 获取排名统计
 * @param {*} params
 * @param {string} params.examId 考试ID
 * @param {string} params.subjectId 学科ID
 * @param {number} params.type 类型 0按名次分析 1按比例分析
 * @param {number} params.qType 赋分类型 1得分 2赋分（不传默认得分）
 * @param {number} params.v 缓存版本号
 * @returns
 */
export const statRankAPI = params => {
  return API.GET('/pexam/_/stat-rank', params);
};

/**
 * 获取高频错词列表
 * @param {*} params
 * @param {string} params.schoolId 学校ID
 * @param {string} params.startTime 开始时间 格式:yyyy-MM-dd HH:mm:ss
 * @param {string} params.endTime 结束时间 格式:yyyy-MM-dd HH:mm:ss
 * @param {string} params.gradeId 年级ID 全部时传空
 * @param {string} params.classId 班级ID 全部时传空
 * @param {number} params.minRate 最小得分率 示例:0
 * @param {number} params.maxRate 最大得分率 示例:0.6
 * @param {number} params.page 当前第几页
 * @param {number} params.limit 每页多少条
 * @returns
 */
export const getWrongWordListAPI = params => {
  return API.GET('/pexam/word/getWrongWordList', params);
};
/**
 * 获取学生列表
 * @param {*} params
 * @param {string} params.pBookId 考试ID
 * @param {string} params.classId 班级ID
 * @param {string} params.keyWord 搜索关键字
 * @param {string} params.type  0:未参考学生 1:已参考学生
 * @returns
 * */
export const getExamStuListAPI = params => {
  return API.GET('/pexam/scanExam/getExamStuList', params);
};
/**
 * 新增参考学生
 * @param {*} params
 * @param {string} params.pBookId 考试ID
 * @param {string} params.classId 班级ID
 * @param {string} params.stuIds 学生ids
 * @returns
 * */
export const addExamStuAPI = params => {
  return API.POST('/pexam/scanExam/addExamStu', params);
};
/**
 * 删除参考学生
 * @param {*} params
 * @param {string} params.pBookId 考试ID
 * @param {string} params.classId 班级ID
 * @param {string} params.stuIds 学生ids
 * @returns
 * */
export const delExamStuAPI = params => {
  return API.POST('/pexam/scanExam/delExamStu', params);
};
/**
 * 获取已发布成绩的考试列表
 * @param {*} params
 * @param {string} params.schoolId 学校id
 * @param {string} params.keyWord 关键字
 * @param {string} params.page 页面
 * @param {string} params.limit 每页条数
 * @returns
 * */
export const getPublishScoreExamListAPI = params => {
  return API.GET('pexam/exam/getPublishScoreExamList', params);
};

/**
 * 复用考试
 * @param {*} params
 * @param {string} params.examId 复用的考试ID
 * @param {string} params.reuseOptions 0:不启用任何复用 1:仅复用参考学生 2:仅复用参考答题卡 3:同时复用参考学生和参考答题卡
 * @param {string} params.optUserId 操作人ID
 * @returns
 * */
export const reuseExamtAPI = params => {
  return API.POST('pexam/exam/reuseExam', params);
};

/**
 * 导入临时学生考号
 * @param {*} params
 * @param {string} params.file 文件流
 * @param {string} params.examId 考试id
 * @param {string} params.campusCode 校区编码
 * @returns
 * */
export const importTempStudentExamNoAPI = params => {
  return API.POSTForm('/pexam/scanExam/importTempStudentExamNo', params);
};

/**
 * 参考学生批量导入
 * @param {*} params
 * @param {string} params.tplUrl  模板url
 * @param {string} params.examId 考试id
 * @param {string} params.campusCode 校区编码
 * @returns
 * */
export const importExamStuAPI = params => {
  return API.POST('/pexam/scanExam/importExamStu', params);
};

/**
 * 获取操作记录
 * @param {*} params
 * @param {string} params.examId 考试id
 * @param {string} params.schoolId 学校id
 * @param {string} params.page 页面
 * @param {string} params.limit 每页条数
 * @returns
 * */
export const getLogListAPI = params => {
  return API.GET('/pexam/exam/getLogList', params);
};

// 获取分析状态
export const getExamAnalyzeState = params => {
  return API.GET('/pexam/_/get-exam-analyze-state', params);
};

// 获取智批学科权限
export const getAIScanSubjectAPI = params => {
  return API.GET('/psocket/vip/getSubjectAiInfo', params);
};

// 获取学校所有智批学科权限
export const getSchoolAIScanSubjectAPI = params => {
  return API.GET('/psocket/vip/getSchoolAiInfo', params);
};

// 获取多校区一分五率
export const getCampusScoreRateAPI = params => {
  return API.GET('/pexam/_/campus-score-rate', params);
};

// 获取多校区等级分布
export const getCampusLvAPI = params => {
  return API.GET('/pexam/_/campus-lv', params);
};

// 获取校区上线分析
export const getCampusLineDataAPI = params => {
  return API.GET('/pexam/_/campus-line-data', params);
}

// 获取校区对比分数段分析
export const getCampusStatSegmentAPI = params => {
  return API.GET('/pexam/_/campus-stat-segment', params);
}

// 获取校区对比名次段分析
export const getCampusStatRankAPI = params => {
  return API.GET('/pexam/_/campus-stat-rank', params);
}

// 修改考号
export const changeStuNoAPI = params => {
  return API.POST('/pscan/scanImage/changeStuNo', params);
}

// 获取报告指标配置
export function getReportCfgAPI(params) {
  return API.GET('/pexam/_/get-report-cfg', params);
}

// 设置报告指标配置
export function setReportCfgAPI(params) {
  return API.POSTJson('/pexam/_/set-report-cfg', params);
}
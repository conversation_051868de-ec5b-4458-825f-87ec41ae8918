<!--
 * @Description: 全科成绩
 * @Author: 小圆
 * @Date: 2024-04-02 09:02:32
 * @LastEditors: 小圆
-->

<template>
  <div>
    <div class="score-type">
      <div class="header-item">
        <el-radio-group v-model="type" @input="onTypeChange">
          <el-radio-button :label="0">按人数</el-radio-button>
          <el-radio-button :label="1">按选中率</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <div v-loading="tableLoading">
      <base-table
        v-if="tableData.length"
        :data="tableData"
        :column="tableColumns"
        v-bind="getTableAttr()"
        v-drag-table
      ></base-table>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>
  </div>
</template>

<script lang="tsx">
import { Component, Vue, Mixins, Prop } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';
import FilterModule from '../plugins/FilterModule';
import {
  getDefaultPercentAxis,
  getDefaultToolBox,
  getDefaultTooltipFormatter,
} from '../plugins/DefaultEchartsOption';

const QuesScorePrefix = 'QuesScore_';
const QuesErrorPrefix = 'QuesError_';

function getGrid() {
  return {
    left: '3%',
    containLabel: true,
  };
}

function getToolBox() {
  return getDefaultToolBox();
}

function getYAxis(type) {
  if (type == 0) {
    return {
      type: 'value',
      minInterval: 1,
      axisLabel: {
        formatter: '{value}人',
      },
    };
  } else {
    return getDefaultPercentAxis();
  }
}

@Component({
  components: {},
})
export default class Index extends Mixins(TableCommon) {
  // 0:按人数1:按选中率
  type: 0 | 1 = 0;

  // 获取数据
  async getTableData(): Promise<any> {
    this.tableColumns = [];
    this.tableData = [];
    this.tableLoading = true;
    await this.$nextTick();
    try {
      const apiName = 'objOptionDiagnosis';
      const queryParams = FilterModule.getQueryParams(apiName);
      const res = await FilterModule.getTeacherReport(apiName, { ...queryParams, type: this.type });
      if (res.code != 1) {
        return this.$message.error(res.msg);
      }
      const data = res.data;
      data.table = this.setEchartsColumn(data.table);
      data.table = this.setColumnProp(data.table);

      this.tableColumns = data.table as IColumn[];
      this.tableData = data.result;

      await this.$nextTick();
      this.setQuesScoreEcharts();
      this.setQuesErrorEcharts();
    } catch (error) {
      console.error(error);
      this.tableColumns = [];
      this.tableData = [];
    } finally {
      this.tableLoading = false;
    }
  }

  setQuesScoreEcharts() {
    this.tableData.forEach(item => {
      const dom = document.getElementById(QuesScorePrefix + item.quesNo);
      const echarts = this.$echarts.init(dom);

      echarts.setOption({
        grid: getGrid(),
        toolbox: getToolBox(),
        tooltip: {
          trigger: 'item',
          formatter: params => {
            return this.getTooltipContent(params, item);
          },
        },
        xAxis: {
          type: 'category',
          data: [this.type == 0 ? '正答人数' : '正答率'],
        },
        yAxis: getYAxis(this.type),
        series: [
          {
            data: [
              this.type == 0
                ? Number.parseFloat(item.rightAnswerTotalNum)
                : Number.parseFloat(item.rightAnswerRate),
            ],
            type: 'bar',
            barWidth: 70,
            label: {
              show: true, // 显示数值
              position: 'top', // 数值显示的位置
              formatter: `{c}${this.type == 0 ? '人' : '%'}`,
            },
          },
        ],
      });
    });
  }

  setQuesErrorEcharts() {
    this.tableData.forEach(item => {
      const columns = this.tableColumns
        .find(t => t.title == '选项人数')
        .children.filter(t => t.title !== item.rightAnswer);
      const titles = columns.map(item => item.title);
      const prop = columns.map(item => item.prop);
      const datas = prop.map(p => {
        return Number.parseFloat(item[p]) || 0;
      });
      const dom = document.getElementById(QuesErrorPrefix + item.quesNo);
      const echarts = this.$echarts.init(dom);
      echarts.setOption({
        grid: getGrid(),
        toolbox: getToolBox(),
        tooltip: {
          trigger: 'item',
          formatter: params => {
            return this.getTooltipContent(params, item);
          },
        },
        xAxis: {
          type: 'category',
          data: titles,
        },
        yAxis: getYAxis(this.type),
        series: [
          {
            data: datas,
            type: 'bar',
            barWidth: 20,
            label: {
              show: true, // 显示数值
              position: 'top', // 数值显示的位置
              formatter: `{c}${this.type == 0 ? '人' : '%'}`,
            },
          },
        ],
      });
    });
  }

  getTooltipContent(params, item) {
    return getDefaultTooltipFormatter(params.name, item.quesNos, params.value);
  }

  setEchartsColumn(columns) {
    const quesScoreRateColumn: IColumn = {
      title: this.type == 0 ? '小题得分人数' : '小题得分率',
      prop: 'quesScoreRate',
      minWidth: 300,
      render: (h, scoped) => {
        return (
          <div
            id={QuesScorePrefix + scoped.row.quesNo}
            style={{ width: '300px', height: '250px', margin: 'auto' }}
          ></div>
        );
      },
    };
    const quesErrorRateColumn = {
      title: this.type == 0 ? '小题错选人数' : '小题错选率分布',
      prop: 'quesErrorRate',
      minWidth: 300,
      render: (h, scoped) => {
        return (
          <div
            id={QuesErrorPrefix + scoped.row.quesNo}
            style={{ width: '300px', height: '250px', margin: 'auto' }}
          ></div>
        );
      },
    };
    columns = [quesScoreRateColumn, quesErrorRateColumn].concat(columns);
    return columns;
  }

  onTypeChange(val) {
    this.getTableData();
  }
}
</script>

<style scoped lang="scss">
.score-type {
  margin: 20px 0;
}
</style>

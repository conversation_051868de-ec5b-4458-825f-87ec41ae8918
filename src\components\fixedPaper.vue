<template>
  <div class="fixed-box" :style="{ right: showPaper ? '0' : '-220' + 'px' }">
    <!--试卷袋左侧展开按钮-->
    <section class="title" @click="showPaper = !showPaper">
      <span class="badge animated" :class="showAnimate ? 'shake' : ''">{{ selectIds.length }}</span>
      <i class="dn-icon icon-basket" :class="{ close: showPaper }"></i>
      <span class="text">试卷袋</span>
    </section>
    <!--试卷袋右侧加入的题目列表-->
    <section class="content">
      <div class="clear-btn-box display_flex align-items_center justify-content_space-between">
        <span>题型（题目数量）</span>
        <span>操作</span>
      </div>
      <ul class="list-none ques-ul">
        <li
          v-for="item in quesList"
          :key="item.type"
          class="clearfix display_flex align-items_center justify-content_space-between"
        >
          <div v-if="item.data.length" class="li-inner">
            <span>{{ item.type }}（{{ item.data && item.data.length }}）</span>
            <i class="el-icon-delete pull-right" @click="deletePaper(item)"></i>
          </div>
        </li>
      </ul>
      <div class="primary-btn-box display_flex justify-content_space-between">
        <el-button size="small" @click="cleanPaper">全部清空</el-button>
        <el-button size="small" type="primary" @click="createPaper" :disabled="!selectIds.length">
          <span>预览试卷 &gt;</span>
        </el-button>
      </div>
    </section>
  </div>
</template>

<script>
import { getPersonalTestBank, savePersonalTestBank } from '@/service/testbank';
import { mapState } from 'vuex';

export default {
  name: 'fixed-paper',
  data() {
    return {
      showAnimate: false,
      showPaper: false,
      subjectiveList: ['语音题', '简答题'],
      // 卷一客观题，卷二主观题
      quesInfo: [],
      paperObj: {},
    };
  },
  props: {
    quesIds: {
      type: Array,
      default: () => [],
    },
  },
  // teachParams需要包括schoolId，gradeId，subjectId
  computed: {
    ...mapState(['teachParams', 'subjectMap', 'gradeList']),
    // 试卷袋左上角显示的加入总题数
    totalNum() {
      let isInit = this.$sessionSave.get('isInit');
      sessionStorage.removeItem('isInit');
      let totalList = [],
        total = 0;
      this.quesInfo.length &&
        this.quesInfo.forEach(item => {
          totalList = totalList.concat(item.data);
        });
      totalList.length &&
        totalList.forEach(item => {
          total += item.data.length;
        });
      return total || 0;
    },
    // 试卷袋题目列表
    quesList() {
      let totalList = [],
        typeList = [];
      this.quesInfo.length &&
        this.quesInfo.forEach(item => {
          item.data.length &&
            item.data.forEach(subItem => {
              subItem.data.length &&
                subItem.data.forEach(third => {
                  let curObj = {
                    type: third.type,
                    id: third.id,
                  };
                  if (typeList.length && typeList.indexOf(third.type) >= 0) {
                    totalList.forEach(totalItem => {
                      if (totalItem.type == third.type) {
                        totalItem.data.push(curObj);
                      }
                    });
                  } else {
                    typeList.push(third.type);
                    totalList.push({
                      type: third.type,
                      data: [curObj],
                    });
                  }
                });
            });
        });
      // totalList = this.orderType(totalList);
      return totalList;
    },
    // 已选中的id数组
    selectIds() {
      if (!this.quesInfo.length) {
        this.$emit('update', this.quesInfo);
        return [];
      }
      let totalList = [],
        ids = [],
        index = 1;
      this.quesInfo.length &&
        this.quesInfo.forEach(item => {
          totalList = totalList.concat(item.data);
        });
      totalList.forEach(item => {
        if (item.data.length) {
          ids = ids.concat(
            item.data.map(sub => {
              return sub.id;
            })
          );
        }
      });
      this.$emit('update', this.quesInfo);
      return ids;
    },
  },
  watch: {
    totalNum(newval, oldval) {
      let isInit = this.$sessionSave.get('isInit');
      sessionStorage.removeItem('isInit');
      if (
        !isInit &&
        String(newval) &&
        String(oldval) &&
        String(oldval) !== 'undefined' &&
        newval !== oldval
      ) {
        this.showAnimate = false;
        setTimeout(() => {
          this.showAnimate = true;
        });
      }
    },
    quesInfo(newval, oldval) {
      this.$emit('update', this.quesInfo);
    },
  },
  mounted() {
    this.paperObj = {};
  },
  methods: {
    getQuesList() {
      let totalList = [];
      this.quesInfo.length &&
        this.quesInfo.forEach(item => {
          totalList = totalList.concat(item.data);
        });
      return totalList;
    },
    getQuesName(num,name){
      return this.$sectionToChinese(num) + '、' +name
    },
    // 全部选入
    selectAll(quesList, selectAll) {
      // 全部移除
      if (selectAll) {
        let pageRemoveIds = [];
        if (quesList && quesList.length) {
          pageRemoveIds = quesList.map(item => {
            return item.quesId || item.qId;
          });
        } else {
          pageRemoveIds = this.quesIds.join(',');
        }
        this.quesInfo.forEach(item => {
          item.data.forEach(sub => {
            let dataLen = sub.data.length;
            for (let i = dataLen - 1; i >= 0; i--) {
              let subItem = sub.data[i];
              if (pageRemoveIds.indexOf(subItem.id) >= 0) {
                sub.data.splice(i, 1);
              }
            }
          });
        });
      } else {
        // 全部选入
        let typeNum = 0;
        quesList.forEach(item => {
          // let quesType = item.quesType || item.content.quesType;
          let contentArr = [];
          if (Array.isArray(item.content)) {
            contentArr = item.content;
          } else {
            contentArr = [item.content];
          }
          contentArr.forEach(content => {
            const quesType =
              item.quesType ||
              (content && content.data && content.data.type) ||
              (item.data && item.data.type) ||
              item.typeId ||
              item.type;
            const quesId = (content && content.qId) || item.quesId || item.qId || item.id;
            this.$set(item, 'quesTypeName', this.$getQuesType(quesType));
            this.$set(item, 'quesTypeId', quesType);
            this.$set(item, 'quesId', quesId);
            // 如果本页当前题目没有被选入，则加入quesInfo
            if (!this.selectIds.length || this.selectIds.indexOf(item.quesId) < 0) {
              this.quesInfo = this.$deepClone(this.insertQuesInfo(this.quesInfo, item));
              let isSubjective = this.subjectiveList.indexOf(item.quesTypeName) >= 0,
                list = [],
                isTypeExit = false,
                curObj = {
                  id: item.quesId,
                  name: 1,
                  isChange: false,
                  type: item.quesTypeName,
                  typeId: item.quesTypeId,
                  score: content.score || item.score || 0,
                };
              let smallData = [];
              let levelcode = content.levelcode || content.data.levelcode;
              let qs = content.qs || content.data.qs;
              if (levelcode != '') {
                smallData = qs.map((it, inde) => {
                  let obj = {
                    id: it.qId,
                    isChange: false,
                    name: inde + 1,
                    score: it.score,
                    type: this.$getQuesType(it.type),
                    typeId: it.type,
                  };
                  return obj;
                });
                curObj.data = smallData;
              }
              list = this.quesInfo[0].data;
              // 所有已选中的题型
              let typeList = list.length
                ? list.filter(item => !item.levelcode)
                    .map(item => item.typeId)
                : [];
              let nameList = list.length
                ? list.filter(item => !item.levelcode)
                    .map(item => item.name)
                : [];
                let isNew = false;
                if(this.paperObj.sortType == 1){
                  //3.10日讨论以大题名称为标识进行区隔  原判断为：typeList[typeList.length-1] == item.quesTypeId
                  if(item.bigTitle){
                    isTypeExit = nameList[nameList.length-1] ==  this.getQuesName(typeNum,item.bigTitle);
                  }else{
                    isTypeExit = typeList[typeList.length-1] == item.quesTypeId;
                  }                  
                  isNew = !isTypeExit || levelcode != '' || list[list.length - 1].levelcode != levelcode;
                }else{
                  isTypeExit = typeList.includes(item.quesTypeId);
                  isNew = !isTypeExit || levelcode != '';
                }
              // 当前题型已选中，在最里层添加数据
              if (isNew) {
                typeNum += 1;
                let quesTitle = this.getQuesName(typeNum,item.bigTitle || item.quesTypeName);
                // 没有此题型，新增题型
                list.push({
                  data: [curObj],
                  name: quesTitle,
                  type: item.quesTypeName,
                  isChange: false,
                  typeId: item.quesTypeId || 0,
                  levelcode:levelcode
                });
              } else {
                if(this.paperObj.sortType == 1){
                  list[list.length - 1].data.push(curObj);
                }else{
                  list.forEach(sub => {
                    if (sub.typeId == item.quesTypeId) {
                      curObj.name = sub.data.length + 1;
                      sub.data.push(curObj);
                    }
                  });
                }
              }
            }
          });
        });
      }
      console.log('全部选入、移除======>', this.quesInfo);
      this.savePersonalTestBank();
    },
    insertQuesInfo(quesInfo, item) {
      let isSubjective = this.subjectiveList.indexOf(item.quesTypeName) >= 0;
      let quesLen = quesInfo.length;
      if (quesLen == 1) {
        // 主观题且没有第二卷
        if (quesInfo[0].id == 0) {
          this.quesInfo.push({
            id: 0,
            show: 1,
            data: [],
          });
        }
      } else if (!quesLen) {
        this.quesInfo.push({
          id: 0,
          show: 1,
          data: [],
        });
      }
      return quesInfo;
    },
    // 题目加入、移除讲评
    selectQues(item, isES = false, content = null) {
      if (isES) {
        const quesType = (item.data && item.data.type) || item.typeId || item.type;
        const quesId = (content && content.qId) || item.qId || item.id;
        this.$set(item, 'quesTypeName', this.$getQuesType(quesType));
        this.$set(item, 'quesTypeId', Number(quesType));
        this.$set(item, 'quesId', quesId);
        const isSubjective = this.subjectiveList.indexOf(item.quesTypeName) >= 0;
        // 主观题类别数组
        let totalList = [],
          isTypeExit = false;
        this.quesInfo = this.$deepClone(this.insertQuesInfo(this.quesInfo, item));
        let totalTypes = [];
        totalList = this.quesInfo[0].data;
        totalTypes = totalList.map(total => {
          if(!total.levelcode){
            return total.typeId;
          }
        });
        // 移除题目
        if (this.selectIds.includes(quesId)) {
          this.quesInfo.forEach(firstItem => {
            firstItem.data.length &&
              firstItem.data.forEach(sub => {
                let dataLen = sub.data.length;
                for (let i = dataLen - 1; i >= 0; i--) {
                  let subItem = sub.data[i];
                  if (subItem.id == quesId) {
                    sub.data.splice(i, 1);
                  }
                }
              });
          });
        } else {
          let score = 0;
          let qs =
            (item.data && item.data.qs) || item.qs || (item.content.data && item.content.data.qs);
          qs.forEach(sq => {
            score += sq.score;
          });
          // 添加题目
          let curObj = {
            id: quesId,
            isChange: false,
            name: 1,
            type: item.quesTypeName || item.typeName,
            typeId: Number(item.quesTypeId) || Number(item.type) || 0,
            score: score,
          };
          let smallData = [];
          let levelcode = item?.data?.levelcode || item?.levelcode || '';
          if (levelcode != '') {
            smallData = qs.map((it, inde) => {
              let obj = {
                id: it.qId,
                isChange: false,
                name: inde + 1,
                score: it.score,
                type: this.$getQuesType(it.type),
                typeId: it.type,
              };
              return obj;
            });
            curObj.data = smallData;
          }
          let isNew = false;
          if(this.paperObj.sortType == 1){
            isTypeExit = totalTypes[totalList.length-1] == item.quesTypeId;
            isNew = !isTypeExit || levelcode != '' || totalList[totalList.length - 1].levelcode != levelcode;
          }else{
            isTypeExit = totalTypes.includes(item.quesTypeId);
            isNew = !isTypeExit || levelcode != '';
          }
          // 当前题型不存在，新建题型
          if (isNew) {
            let quesTitle = this.getQuesName(totalList.length + 1,item.quesTypeName);
            totalList.push({
              data: [curObj],
              name: quesTitle,
              type: item.quesTypeName,
              typeId: Number(item.quesTypeId) || Number(item.type) || 0,
              isChange: false,
              levelcode:levelcode
            });
          } else {
            if(this.paperObj.sortType == 1){
              totalList[totalList.length - 1].data.push(curObj);
            }else{
              // 当前题型已经存在，往里层data push数据
              totalList.forEach(sub => {
                if (sub.typeId == item.quesTypeId) {
                  curObj.name = sub.data.length + 1;
                  sub.data.push(curObj);
                }
              });
            }
          }
        }
        console.log('加入题======>', this.quesInfo);
        this.savePersonalTestBank();
        return;
      }
      let quesType = '';
      if (!content) {
        quesType =
          item.typeId ||
          item.quesType ||
          (item.content && item.content.data && item.content.data.type) ||
          '';
      } else {
        quesType =
          item.typeId || item.quesType || (content && content.data && content.data.type) || '';
      }
      this.$set(item, 'quesTypeName', this.$getQuesType(Number(quesType)));
      this.$set(item, 'quesTypeId', Number(quesType));
      this.$set(item, 'quesId', (content && content.qId) || item.quesId || item.id || '');
      let isSubjective = this.subjectiveList.indexOf(item.quesTypeName) >= 0;
      // 主观题类别数组
      let totalList = [],
        isTypeExit = false;
      this.quesInfo = this.$deepClone(this.insertQuesInfo(this.quesInfo, item));
      let totalTypes = [];
      totalList = this.quesInfo[0].data;
      totalTypes = totalList.map(item => {
        if(!item.levelcode){
            return item.typeId;
          }
      });
      // 移除题目
      if (this.selectIds.indexOf((content && content.qId) || item.quesId) >= 0) {
        this.quesInfo.forEach(firstItem => {
          firstItem.data.length &&
            firstItem.data.forEach(sub => {
              let dataLen = sub.data.length;
              for (let i = dataLen - 1; i >= 0; i--) {
                let subItem = sub.data[i];
                if (subItem.id == ((content && content.qId) || item.quesId)) {
                  sub.data.splice(i, 1);
                }
              }
            });
        });
      } else {
          let qs =
            (item?.content?.data && item?.content?.data.qs) || item?.content?.qs || (item.content.data && item.content.data.qs);
        // 添加题目
        let curObj = {
          id: (content && content.qId) || item.quesId,
          isChange: false,
          name: 1,
          type: item.quesTypeName || item.typeName,
          typeId: Number(item.quesTypeId) || Number(item.typeId) || Number(item.type) || 0,
          score: (content && content.score) || item.score || item.content.score || 0,
        };
        let smallData = [];
        let levelcode = item?.content?.data?.levelcode || item?.content?.levelcode || '';
        if (levelcode != '') {
            smallData = qs.map((it, inde) => {
              let obj = {
                id: it.qId,
                isChange: false,
                name: inde + 1,
                score: it.score,
                type: this.$getQuesType(it.type),
                typeId: it.type,
                
              };
              return obj;
            });
            curObj.data = smallData;
          }
          let isNew = false;
          if(this.paperObj.sortType == 1){
            isTypeExit = totalTypes[totalList.length-1] == item.quesTypeId;
            isNew = !isTypeExit || levelcode != '' || totalList[totalList.length - 1].levelcode != levelcode;
          }else{
            isTypeExit = totalTypes.includes(item.quesTypeId);
            isNew = !isTypeExit || levelcode != '';
          }
        // 当前题型不存在，新建题型
        if (isNew) {
          totalList.push({
            data: [curObj],
            name: this.getQuesName(totalList.length + 1,item.quesTypeName),
            type: item.quesTypeName,
            typeId: Number(item.quesTypeId) || Number(item.typeId) || Number(item.type) || 0,
            isChange: false,
            levelcode:levelcode
          });
        } else {
          if(this.paperObj.sortType == 1){
            totalList[totalList.length - 1].data.push(curObj);
          }else{
            // 当前题型已经存在，往里层data push数据
            totalList.forEach(sub => {
              if (sub.type == item.quesTypeName) {
                curObj.name = sub.data.length + 1;
                sub.data.push(curObj);
              }
            });
          }
        }
      }
      console.log('加入题======>', this.quesInfo);
      this.savePersonalTestBank();
    },

    updateOrder() {
      let quesInfo = this.quesInfo,
        quesIndex = 0,
        typeIndex = 0;
      quesInfo.forEach(item => {
        if (this.paperObj.name) {
          let noChangeList = item.data.filter(sub => {
            return !sub.isChangeSort;
          });
          let changeList = item.data.filter(sub => {
            return sub.isChangeSort;
          });
          // noChangeList = this.orderType(noChangeList);
          item.data = changeList.concat(noChangeList);
        } else {
          // item.data = this.orderType(item.data);
        }
        item.data.length &&
          item.data.forEach(sub => {
            if (!sub.isChange) {
              typeIndex += 1;
              sub.name = this.getQuesName(typeIndex,sub.name.replace(/^([一二三四五六七八九十百千]+)、/,""));
            }
            sub.data.length &&
              sub.data.forEach(third => {
                // if (!third.isChange) {
                  // quesIndex += 1;
                  // third.name = quesIndex;
                  // third.quesNo = Number(quesIndex);
                  // third.quesNos = quesIndex;
                // }
                if (third.data) {
                  third.data.forEach(fourth => {
                    // if (!fourth.isChange) {
                      quesIndex += 1;
                      fourth.name = quesIndex;
                      fourth.quesNo = Number(quesIndex);
                      fourth.quesNos = quesIndex;
                    // }
                  });
                }else{
                  quesIndex += 1;
                  third.name = quesIndex;
                  third.quesNo = Number(quesIndex);
                  third.quesNos = quesIndex;
                }
              });
          });
      });
    },
    // 卷一，卷二中题类按照单选题，多选题，判断题，填空题，填空题自动批改，简答题顺序排序
    orderType(data) {
      let arr = this.$deepClone(data);
      arr.forEach(item => {
        this.$set(item, 'orderIndex', this.$getSortType(item.type));
      });
      arr = arr.sort(this.$creatCompare('orderIndex'));
      arr.forEach(item => {
        delete item.orderIndex;
      });
      return arr;
    },

    // 接口保存当前试卷袋数据
    savePersonalTestBank(type) {
      let pm = this.$listeners.getParams() || this.$emit('getParams');
      let subjectId = pm.subjectId;
      if (this.teachParams.sourceUrl) {
        pm.sourceUrl = this.teachParams.sourceUrl;
      }

      this.$store.commit('saveTeachParams', pm);

      this.deleteNoData();
      let loginInfo = this.$sessionSave.get('loginInfo');
      //let currentSubject = this.$localSave.get('currentSubject');
      if (type !== 'clean') {
        this.updateOrder();
        this.paperObj.sortType = 1;
      }
      let quesInfoRes = [];
      if (this.quesInfo.length && this.quesInfo.length == 1) {
        quesInfoRes = this.quesInfo[0].data;
      } else if (this.quesInfo.length && this.quesInfo.length == 2) {
        quesInfoRes = [].concat(this.quesInfo[0].data, this.quesInfo[1].data);
      }
      let qCount = 0;
      quesInfoRes.map(it => {
        if (it.data && it.data.length) {
          qCount += it.data.length;
        }
      });
      let gName = '';
      // isBase为true表示保存试卷基本信息
      if (this.gradeList) {
        let grd = this.gradeList.find(
          q => q.id == this.paperObj.gradeId || q.id == this.teachParams.gradeId
        );
        if (grd) {
          gName = grd.name;
        }
      }

      savePersonalTestBank({
        tbId: this.paperObj.tbId || '', //试卷id//
        tbName: this.paperObj.name || '', //试卷名称
        gCode: this.paperObj.gradeId || this.teachParams.gradeId, //年级编码
        gName: gName, //年级名称
        sCode: subjectId, //学科编码
        sName: this.subjectMap[subjectId].name, //学科名称//
        phase: this.accountPhase, //学段
        userId: loginInfo.id,
        userName: loginInfo.realname,
        schoolId: this.$sessionSave.get('schoolInfo').id,
        typeCode: this.paperObj.categoryId || '', //试卷类型编码//
        //typeName  : 2,//试卷类型名称
        shareType: this.paperObj.shapeType, //分享类别 0不分享 1分享到校本
        source: this.$route.query.source || 5, // 校本制作来源 1校本上传 2课课练同步 3个性化练习册 4个册考试报考 5个册试题组卷
        quesInfo: JSON.stringify(quesInfoRes),
        queCount: qCount, //试题数
        isSave: 0, // 除了预览里头的保存试卷，其他都传0
        year: this.$route.query.num || '',
        cardType: 1, //0纯答题卡 1题卡合一 2题卡分离
        sortType: this.paperObj.sortType,
      })
        .then(res => {})
        .catch(err => {
          // console.log(err)
        });
    },
    // 生成试卷
    async createPaper() {
      if (!this.selectIds.length) {
        this.$message.error('没有可以清空的题目');
        return;
      }
      let pms = this.$listeners.getParams() || this.$emit('getParams');
      pms.sourceUrl = this.$route.path + location.search + location.hash;
      this.$store.commit('saveTeachParams', pms);
      this.goToPreview();
    },
    // 跳转到预览页
    async goToPreview() {
      if (this.paperObj == 1 || !this.paperObj.id) {
        await this.getPersonalTestBank('routerLink');
        //  await this.getPaper('routerLink');
      } else {
        this.routerLink();
      }
    },
    // 获取试卷袋数据
    getPersonalTestBank(isGo) {
      let pms = this.$listeners.getParams() || this.$emit('getParams');
      getPersonalTestBank({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        userId: this.$sessionSave.get('loginInfo').id,
        subjectId: pms.subjectId,
      })
        .then(data => {
          this.$sessionSave.set('isInit', true);
          let dataRes = JSON.parse(data.data && data.data.quesInfo);
          let newData = [
            {
              name: '第一卷',
              data: [],
              id: 0,
              show: 1,
            },
            // {
            //   name: "第二卷",
            //   data: [],
            //   id: 1,
            //   show: 1,
            // },
          ]; // 长度为2，第一卷和第二卷
          dataRes.forEach(item => {
            newData[0].data.push({
              isChangeSort: true,
              isAverage: true,
              ...item,
            });
            // if (item.name.includes("单选") || item.name.includes("多选")) {
            //   newData[0].data.push({
            //     isChangeSort: true,
            //     isAverage: true,
            //     ...item,
            //   });
            // } else {
            //   newData[1].data.push({
            //     isChangeSort: true,
            //     isAverage: true,
            //     ...item,
            //   });
            // }
          });
          data.data.quesInfo = JSON.stringify(newData);
          this.paperObj = data.data;
          this.quesInfo = this.paperObj == 1 ? [] : JSON.parse(this.paperObj.quesInfo);
          this.$emit('update', this.quesInfo);
          if (isGo) {
            this.routerLink();
          }
        })
        .catch(err => {
          // console.log('获取paperObj失败======>', err);
          this.paperObj = {sortType:1};
          this.quesInfo = [];
        });
    },

    routerLink() {
      this.$router.push({
        path: '/previewPaper',
        query: {
          tbId: this.paperObj.tbId || '',
          subId: this.paperObj.subjectId || '',
          userId: this.$sessionSave.get('loginInfo').id,
          schoolId: this.$sessionSave.get('schoolInfo').id,
          fromPaper: 'paperBag',
          shareType: this.paperObj.shareType || '',
        },
      });
    },
    // 试卷袋中删除当前题类数据
    deletePaper(item) {
      let deletIds = item.data.map(item => {
        return item.id;
      });
      this.quesInfo.forEach(firstItem => {
        firstItem.data.length &&
          firstItem.data.forEach(sub => {
            for (let i = sub.data.length - 1; i >= 0; i--) {
              let subItem = sub.data[i];
              if (deletIds.indexOf(subItem.id) >= 0) {
                sub.data.splice(i, 1);
              }
            }
          });
      });
      this.savePersonalTestBank();
    },
    deleteNoData() {
      let quesInfo = this.quesInfo,
        dataLen = quesInfo.length;
      for (let i = dataLen - 1; i >= 0; i--) {
        if (quesInfo[i] && quesInfo[i].data) {
          let item = quesInfo[i].data;
          if (item.length) {
            for (let j = item.length - 1; j >= 0; j--) {
              let subItem = item[j].data;
              if (!subItem.length) {
                item.splice(j, 1);
                if (!item.length) {
                  this.deleteNoData();
                }
              }
            }
          } else {
            quesInfo.splice(i, 1);
          }
        }
      }
    },
    // 清空试卷
    cleanPaper() {
      if (!this.selectIds.length) {
        this.$message.error('没有可以清空的题目');
        return;
      }
      this.$confirm(`确定要清空试卷袋吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.quesInfo = [];
        this.savePersonalTestBank('clean');
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.fixed-box {
  position: fixed;
  top: 250px;
  right: 0;
  margin: auto;
  /*width              : 270px;*/
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  z-index: 9999;
  .title {
    position: relative;
    float: left;
    width: 56px;
    min-height: 224px;
    font-size: 16px;
    padding: 40px 15px;
    line-height: 24px;
    color: #fff;
    font-weight: 400;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
    background-color: #409eff;
    border-radius: 15px 0 0 15px;
    cursor: pointer;
    .badge {
      position: absolute;
      top: -15px;
      left: -15px;
      background-color: #ee4334;
      padding: 0 5px;
      border-radius: 50%;
      color: #fff;
      font-size: 14px;
      min-width: 30px;
      line-height: 28px;
      height: 30px;
      text-align: center;
    }
    .text {
      display: block;
      font-size: 16px;
      width: 16px;
      margin: 20px auto;
    }
    .icon-basket {
      display: block;
      margin: 0 auto;
      width: 24px;
      height: 24px;
      background: url('../assets/bag_open.png') center center/100% 100% no-repeat;
      &.close {
        background: url('../assets/bag_close.png') center center/100% 100% no-repeat;
      }
    }
    .close-btn {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      text-align: center;
      font-weight: 700;
      font-size: 20px;
      color: #fff;
      height: 50px;
      line-height: 50px;
      background-color: #188aff;
      border-radius: 0 0 0 15px;
      cursor: pointer;
    }
  }
  .content {
    width: 220px;
    height: 100%;
    display: inline-block;
    background-color: #fff;
    -webkit-box-shadow: 0 0 24px rgb(0 0 0 / 15%);
    box-shadow: 0 0 24px rgb(0 0 0 / 15%);
    .clear-btn-box {
      width: 100%;
      color: #515a6e;
      font-size: 14px;
      padding: 15px 20px 10px;
    }
    .ques-ul {
      min-height: 117px;
      overflow: auto;
      max-height: 404px;
      > li {
        display: block;
        padding: 0 20px;
        color: #515a6e;
        font-size: 14px;
        .li-inner {
          margin-bottom: 10px;
        }
      }
      .el-icon-delete {
        font-size: 14px;
        color: #666;
        cursor: pointer;
      }
    }
    .primary-btn-box {
      text-align: center;
      padding: 10px 20px 20px;
    }
  }
}
</style>

<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-03-18 15:39:50
 * @LastEditors: 小圆
-->
<template>
  <div class="study-report">
    <router-view v-if="FilterModule.isInit" class="study-report__main"></router-view>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import { IClassInfo, IExamReportInfo, FilterDataKey, ISubjectInfo } from '@/pages/studyReport/plugins/types';
import FilterModule from '@/pages/studyReport/plugins/FilterModule';

import ReportHeader from '../components/ReportHeader.vue';

@Component({
  components: {
    ReportHeader,
  },
})
export default class StudentReport extends Vue {
  // 监听筛选单例
  FilterModule: typeof FilterModule = FilterModule;
}
</script>

<style scoped lang="scss">
.study-report__main {
  margin-top: 20px;
}
</style>

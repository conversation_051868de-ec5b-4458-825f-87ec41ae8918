/*
 * @Description:
 * @Author: 小圆
 * @Date: 2024-03-20 17:27:52
 * @LastEditors: 小圆
 */

import { menuOption } from '@/pages/studyReport/plugins/types';

export const Teacher_Option_Map: Map<string, menuOption> = new Map([
  [
    'singleSubject',
    {
      title: 'A1-单科成绩',
      path: 'singleSubject',
      name: 'singleSubject',
      filters: ['examId', 'subjectId', 'classInfo', 'gradeId', 'keyWord'],
      component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
    },
  ],
  [
    'allSubject',
    {
      title: 'A2-全科成绩',
      path: 'allSubject',
      name: 'allSubject',
      filters: ['examId', 'gradeId', 'classList', 'limit', 'scoreType'],
      component: () => import('@/pages/studyReport/teacher/allSubject.vue'),
    },
  ],
  [
    'classRank',
    {
      title: 'A3-班级排名',
      path: 'classRank',
      name: 'classRank',
      filters: ['examId', 'subjectId', 'gradeId', 'classList'],
      component: () => import('@/pages/studyReport/teacher/classRank.vue'),
    },
  ],
  [
    'smallQuesTable',
    {
      title: 'A4-小题分表',
      path: 'smallQuesTable',
      name: 'smallQuesTable',
      filters: ['examId', 'schoolId', 'gradeId', 'subjectId', 'classInfo'],
      component: () => import('@/pages/studyReport/teacher/smallQuesTable.vue'),
    },
  ],
  // [
  //   'courseRecommendation',
  //   {
  //     title: 'A7-选课推荐',
  //     path: 'courseRecommendation',
  //     name: 'courseRecommendation',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  // [
  //   'notStatistics',
  //   {
  //     title: 'A8-不统计学生成绩',
  //     path: 'notStatistics',
  //     name: 'notStatistics',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  // [
  //   'questionTypeScore',
  //   {
  //     title: 'B1-题型得分',
  //     path: 'questionTypeScore',
  //     name: 'questionTypeScore',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  [
    'rankDistribution',
    {
      title: 'B3-名次分布',
      path: 'rankDistribution',
      name: 'rankDistribution',
      filters: ['schoolId', 'examId', 'gradeId', 'subjectId', 'classList', 'rank'],
      component: () => import('@/pages/studyReport/teacher/rankDistribution.vue'),
    },
  ],
  // [
  //   'levelDistribution',
  //   {
  //     title: 'B4-等级分布',
  //     path: 'levelDistribution',
  //     name: 'levelDistribution',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  [
    'classDisparity',
    {
      title: 'B5-班内差距',
      path: 'classDisparity',
      name: 'classDisparity',
      filters: ['examId', 'gradeId', 'classInfo'],
      component: () => import('@/pages/studyReport/teacher/classDisparity.vue'),
    },
  ],
  [
    'schoolDisparity',
    {
      title: 'B6-校内差距',
      path: 'schoolDisparity',
      name: 'schoolDisparity',
      filters: ['examId', 'gradeId', 'classList'],
      component: () => import('@/pages/studyReport/teacher/schoolDisparity.vue'),
    },
  ],
  [
    'maxScoreRoster',
    {
      title: 'B7-最高分名册',
      path: 'maxScoreRoster',
      name: 'maxScoreRoster',
      filters: ['examId', 'gradeId'],
      component: () => import('@/pages/studyReport/teacher/maxScoreRoster.vue'),
    },
  ],
  [
    'singleSubjectMaxScoreStu',
    {
      title: 'B8-单科尖子生',
      path: 'singleSubjectMaxScoreStu',
      name: 'singleSubjectMaxScoreStu',
      filters: ['examId', 'subjectId', 'gradeId', 'classList', 'limit'],
      component: () => import('@/pages/studyReport/teacher/singleSubjectMaxScoreStu.vue'),
    },
  ],
  // [
  //   'singleSubjectTopX',
  //   {
  //     title: 'B9-单科前x人各科成绩对比',
  //     path: 'singleSubjectTopX',
  //     name: 'singleSubjectTopX',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  // [
  //   'singleSubjectTopXSmallQuestion',
  //   {
  //     title: 'B10-单科前x人小题分对比',
  //     path: 'singleSubjectTopXSmallQuestion',
  //     name: 'singleSubjectTopXSmallQuestion',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  // [
  // 'paperReview',
  // {
  // title: 'C1-试卷讲评',
  // path: 'paperReview',
  // name: 'paperReview',
  // component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  // },
  // ],
  [
    'smallQuesDiagnosis',
    {
      title: 'D1-小题诊断',
      path: 'smallQuesDiagnosis',
      name: 'smallQuesDiagnosis',
      filters: ['schoolId', 'examId', 'gradeId', 'subjectId', 'classInfo'],
      component: () => import('@/pages/studyReport/teacher/smallQuesDiagnosis.vue'),
    },
  ],
  // [
  //   'knowledgePoint',
  //   {
  //     title: 'D2-知识点诊断',
  //     path: 'knowledgePoint',
  //     name: 'knowledgePoint',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  // [
  //   'abilityPointDiagnosis',
  //   {
  //     title: 'D3-能力点诊断',
  //     path: 'abilityPointDiagnosis',
  //     name: 'abilityPointDiagnosis',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  // [
  //   'questionTypeDiagnosis',
  //   {
  //     title: 'D4-题型诊断',
  //     path: 'questionTypeDiagnosis',
  //     name: 'questionTypeDiagnosis',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  [
    'objOptionDiagnosis',
    {
      title: 'D5-客观题选项诊断',
      path: 'objOptionDiagnosis',
      name: 'objOptionDiagnosis',
      filters: ['schoolId', 'examId', 'gradeId', 'subjectId', 'classInfo'],
      component: () => import('@/pages/studyReport/teacher/objOptionDiagnosis.vue'),
    },
  ],
  // [
  //   'objectiveQuestionAnalysis',
  //   {
  //     title: 'D6-客观题错因分析',
  //     path: 'objectiveQuestionAnalysis',
  //     name: 'objectiveQuestionAnalysis',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  // [
  //   'sPTableDiagnosis',
  //   {
  //     title: 'D7-S-P表诊断',
  //     path: 'sPTableDiagnosis',
  //     name: 'sPTableDiagnosis',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  // [
  //   'nonStandardFillingStatistics',
  //   {
  //     title: 'D8-不规范填涂统计',
  //     path: 'nonStandardFillingStatistics',
  //     name: 'nonStandardFillingStatistics',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  [
    'quesQualityDiagnosis',
    {
      title: 'D9-试题质量诊断',
      path: 'quesQualityDiagnosis',
      name: 'quesQualityDiagnosis',
      filters: ['schoolId', 'examId', 'gradeId', 'subjectId', 'classInfo'],
      component: () => import('@/pages/studyReport/teacher/quesQualityDiagnosis.vue'),
    },
  ],
  [
    'singleSubjectComprehensiveScore',
    {
      title: 'E1-单科综合指标',
      path: 'singleSubjectComprehensiveScore',
      name: 'singleSubjectComprehensiveScore',
      filters: ['examId', 'gradeId', 'subjectId', 'limit'],
      component: () => import('@/pages/studyReport/teacher/singleSubjectComprehensiveScore.vue'),
    },
  ],
  [
    'gradeSingleSubjectComprehensiveScore',
    {
      title: 'E2-单科综合指标-年级汇总',
      path: 'gradeSingleSubjectComprehensiveScore',
      name: 'gradeSingleSubjectComprehensiveScore',
      filters: ['examId', 'gradeId'],
      component: () =>
        import('@/pages/studyReport/teacher/gradeSingleSubjectComprehensiveScore.vue'),
    },
  ],
  // [
  //   'comprehensiveIndexAllCourse',
  //   {
  //     title: 'E3-全科综合指标',
  //     path: 'comprehensiveIndexAllCourse',
  //     name: 'comprehensiveIndexAllCourse',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  [
    'subjectDifficulty',
    {
      title: 'E4-科目难度',
      path: 'subjectDifficulty',
      name: 'subjectDifficulty',
      filters: ['examId', 'gradeId', 'contrastExamId'],
      component: () => import('@/pages/studyReport/teacher/subjectDifficulty.vue'),
    },
  ],
  // [
  //   'sectionStatistics',
  //   {
  //     title: 'E5-分段统计-实力对比',
  //     path: 'sectionStatistics',
  //     name: 'sectionStatistics',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  [
    'subjectEquilibrium',
    {
      title: 'E6-学科均衡性',
      path: 'subjectEquilibrium',
      name: 'subjectEquilibrium',
      filters: ['examId', 'gradeId'],
      component: () => import('@/pages/studyReport/teacher/subjectEquilibrium.vue'),
    },
  ],
  [
    'subjectContributionIndex',
    {
      title: 'E7-学科贡献指数',
      path: 'subjectContributionIndex',
      name: 'subjectContributionIndex',
      filters: ['examId', 'gradeId', 'subjectId'],
      component: () => import('@/pages/studyReport/teacher/subjectContributionIndex.vue'),
    },
  ],
  // [
  //   'changeTrendGroup',
  //   {
  //     title: 'E8-变化趋势-团体',
  //     path: 'changeTrendGroup',
  //     name: 'changeTrendGroup',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  // [
  //   'groupStructureChange',
  //   {
  //     title: 'E9-团体结构变化-桑基图',
  //     path: 'groupStructureChange',
  //     name: 'groupStructureChange',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  [
    'valueAddEvaluation',
    {
      title: 'E10-增值评价-学生',
      path: 'valueAddEvaluation',
      name: 'valueAddEvaluation',
      filters: ['examId', 'gradeId', 'subjectId', 'contrastExamId', 'classList', 'limit'],
      component: () => import('@/pages/studyReport/teacher/valueAddEvaluation.vue'),
    },
  ],
  // [
  //   'onlineNumberStatistics',
  //   {
  //     title: 'E11-上线数统计',
  //     path: 'onlineNumberStatistics',
  //     name: 'onlineNumberStatistics',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  // [
  //   'doubleOnlineNumberStatistics',
  //   {
  //     title: 'E12-双上线数统计',
  //     path: 'doubleOnlineNumberStatistics',
  //     name: 'doubleOnlineNumberStatistics',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  // [
  //   'valueAddEvaluation',
  //   {
  //     title: 'E13-增值评价-教师',
  //     path: 'valueAddEvaluation',
  //     name: 'valueAddEvaluation',
  //     filters: ['examId', 'gradeId', 'subjectId', 'contrastExamId', 'classList', 'limit'],
  //     component: () => import('@/pages/studyReport/teacher/valueAddEvaluation.vue'),
  //   },
  // ],
  // [
  //   'comprehensiveEvaluation',
  //   {
  //     title: 'E14-RSRW综合评价',
  //     path: 'comprehensiveEvaluation',
  //     name: 'comprehensiveEvaluation',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  // [
  //   'subjectCombinationQuery',
  //   {
  //     title: 'F5-选科组合查询',
  //     path: 'subjectCombinationQuery',
  //     name: 'subjectCombinationQuery',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
  // [
  //   'scoreComparisonTable',
  //   {
  //     title: 'F6-赋分对照表',
  //     path: 'scoreComparisonTable',
  //     name: 'scoreComparisonTable',
  //     component: () => import('@/pages/studyReport/teacher/singleSubject.vue'),
  //   },
  // ],
] as [string, menuOption][]);

const Teacher_Option_Value = Array.from(Teacher_Option_Map.values());
Teacher_Option_Value.forEach(item => {
  item.meta ? (item.meta.title = item.title) : (item.meta = { title: item.title });
});
export const Teacher_A_Menu = Teacher_Option_Value.filter(item => item.title[0].includes('A'));
export const Teacher_B_Menu = Teacher_Option_Value.filter(item => item.title[0].includes('B'));
export const Teacher_C_Menu = Teacher_Option_Value.filter(item => item.title[0].includes('C'));
export const Teacher_D_Menu = Teacher_Option_Value.filter(item => item.title[0].includes('D'));
export const Teacher_E_Menu = Teacher_Option_Value.filter(item => item.title[0].includes('E'));
export const Teacher_F_Menu = Teacher_Option_Value.filter(item => item.title[0].includes('F'));

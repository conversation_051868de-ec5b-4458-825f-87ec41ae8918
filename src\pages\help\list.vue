<!--
 * @Description: 
 * @Author: l<PERSON><PERSON><PERSON> y<PERSON><PERSON>@class30.com
 * @Date: 2024-05-11 09:35:15
 * @LastEditors: l<PERSON><PERSON><PERSON> y<PERSON><PERSON>@class30.com
 * @LastEditTime: 2024-05-31 17:37:34
-->
<template>
    <div class="help-content">
        <div class="help-content-header">
            <div v-if="title" class="help-content-title">{{ title }}</div>
            <div v-if="content" class="help-content-desc">{{ content }}</div>
            <el-button v-if="hasAdminPower" @click="addHelp" class="add-help">新增</el-button>
        </div>
        <div class="help-content-list" v-if="list.length">
            <ul>
                <li v-for="(item, index) in list" :key="index" @click.stop="() => { goDetail(item) }">
                    <span class="item-no">{{ index + 1 }}</span>
                    <span class="item-title"> {{ item.title }}</span>
                    <div v-if="hasAdminPower" class="item-handle">
                        <span class="edit" @click.stop="() => { editHelp(item) }">编辑</span>
                        <span class="delete" @click.stop="() => { deleteHelp(item) }">删除</span>
                    </div>
                </li>
            </ul>
        </div>
        <el-empty v-else :description="'暂无帮助说明'"></el-empty>
    </div>
</template>

<script>
import { getQueryString } from '@/utils'
import {  getHelpArticleListAPI, deleteHelpArticleAPI, } from '@/service/phelp';

export default {
    data() {
        return {
            title: "",
            desc: "",
            list: [],
            hasAdminPower:false,
        }
    },
    created() {
        this.hasAdminPower = this.$sessionSave.get('hasAdminPower') || false;
        this.getHelpArticle();
    },
    watch: {
        $route(to, from) {
            if(to.query.categoryId !== from.query.categoryId){
                this.getHelpArticle();
            }
        }
    },
    methods: {
        async getHelpArticle() {
            let res = await getHelpArticleListAPI({
                categoryId: this.$route.query.categoryId
            })
            this.list = res.data.articleList;
            this.title = res.data.category.title;
            this.content = res.data.category.content;
        },
        async deleteHelpArticle(id) {
            let res = await deleteHelpArticleAPI({
                id: id
            })
            if (res.code == 1) {
                this.$message.success("删除成功");
                this.getHelpArticle()
            }
        },
        goDetail(item) {
            this.$router.push({
                path: '/help/detail',
                query: {
                    categoryId: this.$route.query.categoryId,
                    id: item.id
                }
            })
        },
        addHelp() {
            this.$router.push({
                path: '/help/editor',
                query: {
                    categoryId: this.$route.query.categoryId
                }
            })
        },
        editHelp(item) {
            this.$router.push({
                path: '/help/editor',
                query: {
                    categoryId: this.$route.query.categoryId,
                    id: item.id
                }
            })
        },
        deleteHelp(item) {
            this.$confirm("确定删除 <strong>" + item.title + "</strong> 吗？", "提示", {
                dangerouslyUseHTMLString: true
            }).then(() => {
                this.deleteHelpArticle(item.id)
            });
        }
    }
}
</script>

<style scoped lang="scss">
.help-content {
    margin: 10px;
    height: calc(100% - 20px);
    overflow: auto;

    .help-content-header {
        margin-bottom: 10px;
        padding-bottom: 15px;
        border-bottom: 1px solid #f0f0f0;

        .help-content-title {
            font-size: 24px;
            font-weight: 600;
        }

        .help-content-desc {
            margin-top: 5px;
            color: gray;
            font-size: 16px;
        }

        .add-help {
            position: absolute;
            right: 50px;
            top: 20px;
        }
    }

    .help-content-list {
        ul {
            list-style: none;
            min-height: 400px;

            li {
                padding: 8px 0;
                border-bottom: 1px solid #f0f0f0;
                cursor: pointer;

                .item-no {
                    display: inline-block;
                    line-height: 20px;
                    background: rgb(39 168 244 / 20%);
                    border-radius: 50%;
                    font-size: 14px;
                    color: #1888ff;
                    text-align: center;
                    min-width: 20px;
                    padding: 0 3px;
                    vertical-align: 1px;
                    margin-right: 8px;
                }

                .item-title {
                    line-height: 32px;
                    font-size: 16px;
                    color: #333;
                }

                .item-handle {
                    float: right;

                    span {
                        padding: 5px;

                        &.edit {
                            color: #1888ff;
                        }

                        &.delete {
                            color: #ff0000;
                        }
                    }
                }
            }
        }
    }
}
</style>
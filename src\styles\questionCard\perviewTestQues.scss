.perview-container{
  //position: relative;
  a {
    color: #323232;
  }
}
.rc-main {
  position: absolute;
  left: 0;
  width: 100%;
  overflow: hidden;
  text-align: left;
}

.rc-main.isExam {
  left: 0;
  z-index: 10
}

.rc-main-nav {
  position: relative;
  z-index: 2;
  width: 100%;
  padding: 15px 0;
  border-bottom: 10px solid #eff1fa;
  height: auto
}

.area-div .search,.rc-main-nav .search {
  line-height: 34px;
  height: auto
}

.area-div .search-type,.rc-main-nav .search-type,.search-tip-div .search-type {
  float: left;
  padding-left: 30px;
  padding-right: 20px;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  line-height: 40px
}

.area-div ul,.rc-main-nav ul:not(.ques-type-childs),.search-tip-div ul {
  padding-right: 10px;
  overflow: hidden;
  height: auto
}

.area-div ul li.type-item,.rc-main-nav ul li.type-item,.search-tip-div ul li {
  float: left;
  height: 100%;
  text-align: center;
  font-size: 16px;
  line-height: 40px;
  color: rgba(78,86,104,1)
}

.rc-main-nav ul li .ques-type-childs {
  display: none
}

.rc-main-nav ul li .ques-type-childs li {
  max-height: 40px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis
}

.area-div ul li.type-item a,.rc-main-nav ul li.type-item a,.search-tip-div ul li.type-item a {
  padding: 6px 20px 8px;
  color: rgba(78,86,104,1)
}

.area-div ul li.type-item a.active,.rc-main-nav ul li.type-item a.active,.search-tip-div ul li.type-item a.active {
  background: linear-gradient(-36deg,rgba(81,129,247,1),rgba(92,138,252,1));
  border-radius: 3px;
  color: #fff
}

.rc-main-nav ul li.type-item .ques-type-childs {
  position: absolute;
  overflow: auto;
  width: 16%;
  z-index: 10;
  max-height: 480px;
  border: 1px solid #ccc;
  border-radius: 5px;
  text-align: left;
  text-indent: 20px;
  background: rgba(255,255,255,1);
  box-shadow: 0 1px 2px 0 rgba(0,0,0,.29);
  cursor: pointer
}

.rc-main-nav ul li.type-item .ques-type-childs li:hover {
  background: rgba(239,241,250,1)
}

.rc-main-nav ul li.type-item .arrow {
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #999;
  overflow: hidden;
  position: relative;
  top: 14px;
  left: 3px
}

.rc-main-nav ul li.type-item .active .arrow {
  border-top: 6px solid #fff
}

.rc-main-left {
  float: left;
  width: 100%;
  height: calc(100% - 140px);
  overflow-y: auto
}

.rc-main-left ul {
  width: 100%;
  margin-top: 20px;
  overflow-x: hidden
}

.paper-content>li,.rc-main-left ul>li {
  border: 1px solid rgba(226,229,239,1);
  overflow: initial
}

.rc-main-left ul>li {
  position: relative;
  width: calc(100% - 75px);
  min-height: 40px;
  margin: 0 auto 15px;
  background: rgba(255,255,255,1);
  border-radius: 4px
}

.paper-content-left>a {
  color: rgba(117,124,140,1);
  font: 14px/1 tahoma,å¾®è½¯é›…é»‘,\5b8b\4f53
}

.paper-content-left>p {
  width: calc(100% - 160px)
}

.paper-content-left>a:not(:last-child):after {
  display: inline-block;
  content: '';
  width: 1px;
  height: 10px;
  margin: 0 10px;
  background: rgba(226,229,239,1)
}

.paper-content>li,.rc-main-right {
  background: rgba(255,255,255,1);
  position: relative
}

.paper-content-left .exam-every-title {
  color: rgba(33,37,48,1)
}

.paper-content>li {
  width: calc(100% - 75px);
  padding: 10px 30px;
  margin: 0 auto 15px;
  border-radius: 4px
}

.paper-content-right {
  position: absolute;
  right: 30px;
  top: 50%;
  margin-top: -12px
}

.paper-content-right a:not(.download-paper) {
  width: 60px;
  padding: 10px 30px;
  border-radius: 4px;
  background: linear-gradient(-36deg,rgba(81,129,247,1),rgba(92,138,252,1));
  color: #fff;
  cursor: pointer
}

.item,.ques-item,.rc-main-left .page-box .button {
  border: 1px solid rgba(204,208,220,1)
}

.ques-list {
  border: 1px solid rgba(226,229,239,1);
  border-radius: 2px;
  border-bottom: none
}

.item,.ques-item {
  margin: 0 20px 10px 30px
}

.item .ques-content,.ques-item .ques-content {
  width: 100%;
  line-height: 2em;
}

.item .ques-tip,.ques-item .ques-tip {
  width: 100%;
  height: 40px;
  background: rgba(247,248,251,1);
  border-top: 1px solid rgba(226,229,239,1)
}

.item .ques-tip .ques-tip-left,.ques-item .ques-tip .ques-tip-left {
  float: left;
  height: 100%;
  font-size: 13px;
  line-height: 40px;
  margin-left: 20px;
  max-width: calc(100% - 380px);
  overflow: hidden
}
.ques-tip-left-item{
  margin-right: 30px;
}

.ques-tip-right {
  float: right;
  height: 100%;
  margin-right: 30px;
  max-width: 320px
}

.ques-tip .ques-tip-right a {
  display: inline-block;
  padding: 0 15px;
  text-align: center;
  line-height: 30px;
  font-size: 16px;
  margin-top: 4px;
  border: 1px solid rgba(247,248,251,1);
  border-radius: 4px;
  cursor: pointer
}

.ques-tip .ques-tip-right a.detail-item{
  padding-left:30px;
  background: url("../assets/questionCard/ques_detail_close.png") no-repeat 10px/14px;
}

.ques-tip .ques-tip-right a.detail-item.active{
  background: url("../assets/questionCard/ques_detail_close_hover.png")  no-repeat 10px 9px/14px !important;
}
.ques-tip .ques-tip-right a.detail-item.active:hover{
  background: url("../assets/questionCard/ques_detail_close_hover.png")  no-repeat 10px 9px/14px !important;
}
.ques-tip .ques-tip-right a.detail-item:hover{
  color:rgba(62,115,246,1);
  background: url("../assets/questionCard/ques_detail_open.png")  no-repeat 10px/14px;
}
.perview-container .big-title {
  margin-left: 30px;
  padding-top: 20px;
}

.perview-container .ques-ul {
  counter-reset: ques-index;
}
.perview-container .article-container {
  padding: 20px;
}
.perview-container .auxiliary>div {
  padding: 5px 20px;
}

.exam-nav .exam-title p {
  color: rgb(78, 86, 104);
  text-align: center;
  font: 600 20px/2.5 tahoma, 微软雅黑, 宋体;
}

.exam-nav a.exam-ques-count {
  cursor: default;
  margin-left: 40px;
}

.exam-nav a {
  color: rgb(117, 124, 140);
  font: 16px/1 tahoma, 微软雅黑, 宋体;
}

.perview-container-back-icon {
  margin-right: 5px;
  margin-left: 10px;
  vertical-align: text-bottom;
  cursor: pointer;
}
.perview-container-back-txt:hover {
  color: #038ae4;
}

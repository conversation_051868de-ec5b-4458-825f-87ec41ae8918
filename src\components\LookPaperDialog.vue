<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      custom-class="paper-dialog"
      :before-close="handleClose"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <slot></slot>

      <div class="side-btns">
        <div class="side-btn">
          <i class="el-icon-full-screen" title="点击全屏" @click="toggleFullscreen()"></i>
        </div>
        <div class="side-btn">
          <i class="el-icon-close" title="关闭" @click="handleClose()"></i>
        </div>
      </div>

      <div class="image-box">
        <el-button v-if="fullscreen" @click="toggleFullscreen()" class="exit-btn"
          >退出全屏</el-button
        >
        <div v-if="stuTableData && stuTableData.length">
          <img-list
            :isShowSubmitBtn="false"
            v-for="(stuimg, index) in stuTableData"
            :index="index"
            :currentRow="stuimg"
            :defaultRotate="defaultRotate"
            :defaultScore="defaultScore"
            :key="index"
          ></img-list>
        </div>
        <no-data text="当前学生无作答图片" v-else></no-data>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ImgList from '@/components/scan/ImgList.vue';
import NoData from '@/components/noData.vue';

export default {
  name: 'look-paper-dialog',
  components: {
    ImgList,
    NoData,
  },
  props: {
    stuTableData: {
      type: Array,
      default: () => [],
    },

    defaultRotate: {
      type: Number,
      default: 90,
    },

    defaultScore: {
      type: String,
      default: '',
    },
    title:{
      type: String,
      default: '查看原卷',
    }
  },
  data() {
    return {
      //是否展示原卷
      dialogVisible: true,
      //是否全屏
      fullscreen: false,
    };
  },
  mounted() {
    // 退出全屏后要执行的动作
    window.onresize = () => {
      let isFull =
        document.mozFullScreen ||
        document.fullScreen ||
        //谷歌浏览器及Webkit内核浏览器
        document.webkitIsFullScreen ||
        document.webkitRequestFullScreen ||
        document.mozRequestFullScreen ||
        document.msFullscreenEnabled;
      if (isFull === undefined) {
        this.fullscreen = false;
      }
    };
  },
  methods: {
    // 全屏、退出全屏切换
    toggleFullscreen() {
      let element = document.getElementsByClassName('image-box')[0];
      if (this.fullscreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      } else {
        if (element.requestFullscreen) {
          element.requestFullscreen();
        } else if (element.webkitRequestFullScreen) {
          element.webkitRequestFullScreen();
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen();
        } else if (element.msRequestFullscreen) {
          // IE11
          element.msRequestFullscreen();
        }
      }
      this.fullscreen = !this.fullscreen;
    },
    handleClose() {
      this.dialogVisible = false;
      this.$emit('close-dialog');
    },
  },
};
</script>

<style>
.paper-dialog[role='dialog'] {
  overflow: visible;
}
</style>

<style lang="scss" scoped>
.paper-dialog {
  position: relative;
  overflow: visible;

  .full-screen-btn {
    font-size: 18px;
    cursor: pointer;
    display: inline-flex;
    position: absolute;
    top: 18px;
    right: 45px;
  }
  .image-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 650px;
    overflow-y: auto;
    border: 1px solid #e6e6e6;
    background-color: hsla(0, 0%, 90.2%, 0.5);
    position: relative;
    .exit-btn {
      position: fixed;
      top: 0;
      right: 0;
    }
  }
}

.side-btns {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 50px;
  margin-right: -50px;
  background: rgb(250, 251, 255);

  .side-btn {
    width: 100%;
    height: 50px;
    cursor: pointer;
    border: 1px solid #ccc;

    &:hover {
      background: rgba(70, 136, 255, 0.7);
      color: #fff;
      border: 0;
    }

    i {
      display: grid;
      place-items: center;
      width: 100%;
      height: 100%;
      font-size: 20px;
    }
  }
}
</style>


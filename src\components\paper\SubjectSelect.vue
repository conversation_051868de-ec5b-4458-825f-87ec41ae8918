﻿<template>
  <el-select
      v-model="curSubject"
      value-key="subjectName"
      class="subject-select-div"
      @change="switchSubject"
      :title="`${curSubject.subjectName} (${curSubject.subjectType===CLASS_TYPE.substitute?'个人授课':'备课组长'})`">
    <template v-if="subjectInfo.substituteSubjectList.length">
      <span class="option-label">个人授课</span>
      <el-option
          v-for="item in subjectInfo.substituteSubjectList"
          :key="item.subjectId+item.type"
          :label="`${item.subjectName} (个人授课)`"
          :value="item"
          :title="item.subjectName"
          class="subject-select-item">
        {{item.subjectName}}
      </el-option>
    </template>

    <template v-if="subjectInfo.lessonsSubjectList.length">
      <span class="option-label">备课组长</span>
      <el-option
          v-for="(item, index) in subjectInfo.lessonsSubjectList"
          :key="index+item.subjectId"
          :label="`${item.subjectName} (备课组长)`"
          :value="item"
          :title="item.subjectName"
          class="subject-select-item"
      >
        {{item.subjectName}}
      </el-option>
    </template>
  </el-select>
</template>

<script>
import {getTeacherSubjectListAPI} from "@/service/api";

/** 班级类型 */
export const CLASS_TYPE = {
  // 备课
  lessons: 1,
  // 授课
  substitute: 2
}

export default {
  name: "subject-select",

  data() {
    return {
      // 当前学科对象
      curSubject: {},
      // 学科信息
      subjectInfo: {
        // 备课列表
        lessonsSubjectList: [],
        // 授课列表
        substituteSubjectList: []
      },
      // 授课班级类型
      CLASS_TYPE: CLASS_TYPE
    }
  },

  computed: {
    userId(){
      return this.$store.state.loginInfo.id
    },
    schoolId(){
      return this.$store.state.loginInfo.schoolid
    }
  },

  created() {
    this.getSubjectList()
  },

  methods: {

    /**
     * @name: 获取代/备课学科列表
     */
    getSubjectList() {
      getTeacherSubjectListAPI({
        userId: this.userId,
        schoolId: this.schoolId
      }).then((res)=>{
        if( res.code === 1 ){
          let curSubject = localStorage.getItem(`select_subject_${this.userId}`)
          curSubject = curSubject ? JSON.parse(curSubject) : res.data.selectedSubject
          this.subjectInfo.lessonsSubjectList = res.data.lessonsSubjectList
          this.subjectInfo.substituteSubjectList = res.data.substituteSubjectList
          this.switchSubject(curSubject)
        }
      }).catch((err)=>{
      })
    },

    /**
     * @name: 学科切换
     * @param subItem 学科对象
     */
    switchSubject(subItem){
      localStorage.setItem(`select_subject_${this.userId}`, JSON.stringify(subItem))
      this.curSubject = subItem
      this.$emit('switch-subject', subItem)
    }
  }
}
</script>

<style lang="scss" scoped>
.subject-select-div{
  width: 220px;
}
.subject-select-item{
  font-size: 16px !important;
  padding-left: 10px;
}
.option-label{
  color: #c8c9cc;
  line-height: 40px;
  margin-left: 10px;
  font-size: 14px;
}
</style>
<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-08-02 09:08:47
 * @LastEditors: 小圆
-->
<template>
  <div class="report-wrapper">
    <!-- 教辅 -->
    <book-module v-if="isShowBookModule"></book-module>

    <div class="report">
      <div class="report__header">
        <div class="report__header-filter">
          <exam-list-filter
            :reportType="reportType"
            ref="filter"
            class="left"
            v-model="examQuery"
            @change="onQuery"
          ></exam-list-filter>
          <div class="right">
            <el-button @click="getCommentExamList">刷新</el-button>
          </div>
        </div>
      </div>

      <div class="report__main" v-loading="isLoading">
        <div v-if="examReportList.length">
          <ul class="examReport__list list-none">
            <div class="examReport__item" v-for="item in examReportList" :key="item.examId">
              <div class="exam-item">
                <div class="exam-item--left">
                  <div class="exam-name">
                    <div class="exam-title">
                      {{ item.examName }}
                    </div>
                    <div class="exam-tag">
                      <el-tag size="small" class="tag-item" type="info">{{ item.categoryName }}</el-tag>
                      <el-tag size="small" v-if="item.analysisMode == 1" type="warning">新高考</el-tag>
                    </div>
                  </div>
                  <div class="exam-detail display_flex align-items_center">
                    <span class="exam-grade">年级：{{ item.gradeName }}</span>
                    <span class="exam-time">考试时间：{{ item.examDateTime }}</span>
                  </div>
                  <div class="exam-detail display_flex align-items_center">
                    <span class="exam-subject">学科：{{ item.subjectName }}</span>
                  </div>
                  <div class="exam-detail display_flex align-items_center">
                    <span class="exam-class">班级：{{ item.classNames }}</span>
                  </div>
                </div>
                <div class="exam-item--right">
                  <el-button v-if="item.published == 1" type="primary" @click.stop="lookReport(item)">
                    查看报告
                  </el-button>
                  <div class="no-publish" v-else>未发布</div>
                </div>
              </div>
            </div>

            <el-pagination
              background
              style="margin-bottom: 30px"
              class="text-center"
              layout="total, prev, pager, next"
              @current-change="onPageChange"
              :current-page.sync="pagination.page"
              :page-size="pagination.pageSize"
              :total="pagination.total"
            >
            </el-pagination>
          </ul>
        </div>
        <no-data v-else></no-data>
      </div>
    </div>

    <look-report-dialog
      v-if="isShowSelReportDialog"
      :reportInfo="currentReportInfo"
      @closeDialog="isShowSelReportDialog = false"
      @lookReport1="sureLookReport"
    ></look-report-dialog>
  </div>
</template>

<script lang="ts">
import { getChildReportList, getCommentExamList, getExamReportList } from '@/service/pexam';
import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator';

import lookReport from '@/components/lookReport.vue';
import NoData from '@/components/noData.vue';
import UserRole from '@/utils/UserRole';
import ExamListFilter, { ExamQuery } from './components/exam-list-filter.vue';
import BookModule from './components/book-module.vue';
import { getPublicConfigBySchoolInfo } from '@/service/api';

export interface IResData {
  total: number;
  code: number;
  hasMore: boolean;
  list: IExam[];
}

export interface IExam {
  createUserId: string;
  year: number;
  yqExamId: string;
  classNames: string;
  paperList: IPaperList[];
  source: number;
  categoryName: string;
  subjectId: string;
  comprehensiveSubjectCardSet: string;
  workInfo: any;
  scoreRuleId: string;
  analysisMode: number;
  comprehensiveSubjectIds: string;
  fileUrl: string;
  scoreConfirm: number;
  subjectName: string;
  gradeName: string;
  statState: number;
  schoolTermId: string;
  classIds: string;
  examName: string;
  dataState: number;
  persRecoveryType: number;
  dateModified: Date;
  categoryCode: string;
  published: number;
  examDateTime: Date;
  schoolYearId: string;
  isPublish: number;
  workId: string;
  comprehensiveSubjectNames: string;
  paperNo: string;
  comprehensiveSubjectRealNames: string;
  v: number;
  examId: number;
  clzType: string;
  scanExamSource: number;
  gradeCode: string;
  customScoreRuleId: string;
  classType: string;
}

export interface IPaperList {
  progressState: number;
  subectName: string;
  subectId: string;
  progress: number;
  progressList: IProgressList[];
  subjectRealName: string;
  workId: string;
}

export interface IProgressList {
  id: string;
  examId: number;
  relationId: string;
  schoolId: string;
  progress: number;
  progressName: IProgressName;
  progressState: number;
}

export enum IProgressName {
  发布成绩 = '发布成绩',
  开始阅卷 = '开始阅卷',
  扫描答卷 = '扫描答卷',
  答卷设置 = '答卷设置',
  结束阅卷 = '结束阅卷',
  重新阅卷 = '重新阅卷',
  阅卷已结束 = '阅卷已结束',
  阅卷设置 = '阅卷设置',
}

@Component({
  components: {
    NoData,
    LookReportDialog: lookReport,
    ExamListFilter,
    BookModule,
  },
})
export default class ReportCenterList extends Vue {
  @Prop() reportType: string;
  @Ref() filter: ExamListFilter;
  // 考试查询条件
  examQuery: ExamQuery = {
    acadYearsId: '',
    year: '',
    gradeId: '',
    subjectId: '',
    categoryId: '',
    keyWord: '',
    classId: '',
    clzId: '',
    layeredClassIds: '',
  };
  // 分页器
  pagination = {
    page: 1,
    pageSize: 10,
    total: 0,
  };

  // 考试报告列表
  examReportList: IExam[] = [];
  // 当前考试报告
  currentReportInfo: IExam = null;
  // 考试报告列表加载状态
  isLoading: boolean = false;
  // 是否显示选择报告对话框
  isShowSelReportDialog: boolean = false;
  // 是否初始化
  initMounted: boolean = false;
  // 是否显示教辅模块
  isShowBookModule: boolean = false;

  // keepAlive 缓存数据
  keepCache = {
    schoolId: this.$sessionSave.get('schoolInfo').id,
    userId: this.$sessionSave.get('loginInfo').id,
  };

  @Watch('reportType')
  onIdChange(val, oldVal) {
    if (val == oldVal) return;
    this.$nextTick(() => {
      this.filter.initSelect();
    });
  }

  async mounted() {
    this.initSessionStorage();
    this.getBookModuleConfig();
    this.initMounted = true;
  }

  async activated() {
    if (!this.initMounted) return;
    if (
      this.keepCache.schoolId !== this.$sessionSave.get('schoolInfo').id ||
      this.keepCache.userId !== this.$sessionSave.get('loginInfo').id
    ) {
      this.keepCache.schoolId = this.$sessionSave.get('schoolInfo').id;
      this.keepCache.userId = this.$sessionSave.get('loginInfo').id;
      this.isShowBookModule = false;
      this.getBookModuleConfig();
    }

    this.getCommentExamList();
    this.initSessionStorage();
  }

  // 初始化sessionStorage
  initSessionStorage() {
    this.$sessionSave.set('currentRoles', null);
  }

  // 获取教辅权限
  async getBookModuleConfig() {
    try {
      const res = await getPublicConfigBySchoolInfo({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        userId: this.$sessionSave.get('loginInfo').id,
        dictCode: '143',
      });
      res.data.forEach(item => {
        // 推送大精成绩到钉钉
        if (item.dictCode == '143') {
          this.isShowBookModule = item.state == '1';
        }
      });
    } catch (error) {
      this.isShowBookModule = false;
      console.error(error);
    }
  }

  // 获取考试列表
  async getCommentExamList() {
    this.isLoading = true;

    // 获取角色
    let roles = this.getRole(this.examQuery.year);

    try {
      let res = await getCommentExamList({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        acadYearsId: this.examQuery.acadYearsId,
        gradeId: this.examQuery.gradeId,
        subjectId: this.examQuery.subjectId,
        categoryId: this.examQuery.categoryId || '',
        keyWord: this.examQuery.keyWord && this.examQuery.keyWord.trim(),
        role: roles,
        year: this.examQuery.acadYearsId ? '' : this.examQuery.year,

        clzId: this.examQuery.clzId, // 行政班
        layeredClassIds: this.examQuery.layeredClassIds, // 分层班

        page: this.pagination.page,
        pageSize: this.pagination.pageSize,

        source: 1, // 来源 0: 试卷讲评 1: 报告
        shieldWork: 1, // 是否屏蔽作业数据 0:否 1:是
      });

      let resData: IResData = res.data;
      this.examReportList = resData.list;
      this.pagination.total = resData.total;
    } catch (error) {
      this.examReportList = [];
    }
    this.isLoading = false;
  }

  // 获取角色
  getRole(year) {
    let roleSubjectMap = UserRole.utils.getRoleSubjectListMapByYear(year);
    let roles: string;
    if (UserRole.isOperation) {
      roles = ''; // 运营获取所有权限
    } else {
      let roleSub = {}; // 角色的学科
      if (this.reportType === 'school') {
        roleSubjectMap[1] = UserRole.utils.getSchoolLeaderSubjectList(year);
      }
      for (const key in roleSubjectMap) {
        if (Object.prototype.hasOwnProperty.call(roleSubjectMap, key)) {
          let item = roleSubjectMap[key];
          roleSub[key] = item
            .map(item => item.subjectId)
            .filter(t => t)
            .join(',');
        }
      }
      roles = JSON.stringify(roleSub);
    }
    return roles;
  }

  // 点击进入报告，判断是否需要选择报告
  async lookReport(item) {
    if (!item.published) {
      return this.$message.warning('该考试未发布');
    }
    this.$sessionSave.set('reportParent', item);
    if (this.reportType == 'school') {
      const roles = this.filter.roles;
      this.$sessionSave.set('currentRoles', roles.join(','));
      this.$sessionSave.set('reportType', this.reportType);
    } else {
      this.$sessionSave.set('currentRoles', null);
      this.$sessionSave.set('reportType', this.reportType);
    }

    const { examList, hasCampus } = await getChildReportList({
      examId: item.examId,
      examName: item.examName,
    });

    if (hasCampus && !UserRole.isOperation && !UserRole.isSchoolLeader) {
      if (examList.length == 0) {
        this.$message.warning('暂无所在校区的考试报告');
        return;
      }
      if (examList.length == 1) {
        this.sureLookReport(examList[0]);
        return;
      }
    }
    if (examList.length) {
      this.currentReportInfo = item;
      this.isShowSelReportDialog = true;
    } else {
      this.sureLookReport(item);
    }
  }

  // 进入报告
  sureLookReport(item) {
    this.isShowSelReportDialog = false;
    this.$sessionSave.set('lookReportFrom', this.$route.path);
    this.$sessionSave.set('innerClassList', null);
    this.$sessionSave.set('innerSubjectList', null);
    this.$sessionSave.set('loadComment', true);
    this.$sessionSave.set('reportDetail', item);
    this.$sessionSave.remove('subjectId');
    this.$sessionSave.set('downLoadState', item.statState);
    this.$sessionSave.remove('contrastObj');
    this.$router.push({ path: '/home/<USER>' });
  }

  // 查询
  onQuery(examQuery: ExamQuery) {
    this.pagination.page = 1;
    this.getCommentExamList();
  }

  // 分页查询
  onPageChange(val) {
    this.pagination.page = val;
    this.getCommentExamList();
  }
}
</script>

<style scoped lang="scss">
@import './components/select.scss';

.report-wrapper {
}

.report {
  border-radius: 10px;
  background-color: #fff;
  padding: 20px;
}

.report__header {
  margin-bottom: 20px;
  background: #fff;

  .report__header-tip {
    color: red;
  }

  .report__header-filter {
    display: flex;
    .left {
      flex: 1;
    }

    .right {
    }
  }
}

.examReport__list {
  position: relative;
  width: 100%;
  height: calc(100vh - 240px);
  overflow-y: auto;

  &.examReport__list--skeleton {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 10;
  }

  .examReport__item {
    width: 100%;
    min-height: 120px;
    box-sizing: border-box;
    background: #fff;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 14px;
    color: #222;
    font-size: 14px;
    position: relative;
    border: 1px solid #e4e7eb;
    box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.15);
    border-radius: 6px;

    .exam-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      // justify-content: space-between;

      .exam-name {
        display: flex;
        flex-direction: row;
        align-items: center;
        font-size: 18px;
        font-weight: bold;
        color: #161e26;
        line-height: 21px;
        margin-right: 100px;
        width: 90%;
        justify-content: flex-start;
        .exam-title {
          margin-right: 15px;
          // width: calc(100% - 125px);
        }
        .exam-tag {
          display: flex;
          .tag-item {
            margin-right: 10px;
          }
          .el-tag {
            background-color: #fff;
          }
        }
        .exam-category {
          margin-right: 15px;
          height: 24px;
          line-height: 24px;
          background: #f0f2f5;
          border-radius: 12px;
          color: #606266;
          text-align: center;
          font-weight: 400;
          font-size: 14px;
          padding: 0 11px;
          width: 120px;
        }
      }

      .exam-detail {
        margin-top: 15px;

        .line-gap {
          width: 32px;
          position: relative;

          &:before {
            content: '';
            position: absolute;
            left: 50%;
            margin-left: -1px;
            top: 50%;
            margin-top: -7px;
            display: inline-block;
            width: 2px;
            height: 14px;
            background-color: #97a0a8;
          }
        }

        .exam-grade {
          margin-right: 20px;
        }

        > span {
          display: inline-block;
          // font-size: 16px;
          color: #3f4a54;
        }
      }
    }

    .exam-item--left {
      flex: 1;
    }

    .exam-item--right {
      min-width: 150px;
      padding-left: 20px;
      text-align: center;
    }

    .scannet {
      width: 80%;
    }

    .scanhand {
      width: 70%;
    }

    .upload-btn {
      display: inline-block;
      width: 130px;
      height: 42px;
      font-size: 16px;
      margin-left: 25px;
      cursor: pointer;

      &.is-disabled {
        border-color: #80c6f5;
      }
    }

    .line {
      display: inline-block;
      height: 42px;
      font-size: 16px;
      color: #000000;
      pointer-events: none;
      cursor: pointer;
    }

    .score-btn {
      display: inline-block;
      width: 80px;
      height: 42px;
      font-size: 16px;
      margin-left: 25px;
      cursor: pointer;

      &.is-disabled {
        border-color: #b8b8b8;
        pointer-events: none;
        cursor: pointer;
      }
    }

    .report-btn,
    .download-btn {
    }

    .no-publish {
      color: #d9001b;
      font-size: 18px;
      font-weight: 700;
    }

    .light-btn {
      background: #409eff;
    }

    .canlink {
      color: #409eff;
      cursor: pointer;
    }
    .click-item {
      cursor: pointer;
    }
  }
}
::-webkit-scrollbar {
  display: none;
}
</style>

# BaseTable（二次封装的el-table）
## 使用

``` javascript
<base-table :column="tableData.column"
    :data="tableData.data">
</base-table>


const column = [
  {
    // el官网column属性,
    prop: 'date', 
    label: '日期',  
    render: (h, scope)=> {  // 自定义渲染
      return <span><i class='el-icon-time'></i></span>  // JSX渲染，Tip：注意避免Babel中使用多种JSX解析
      return h('div', {}, [
        h('i', { class: 'el-icon-time' }),
        h('span', { style: { marginLeft: '10px' } }, [scope.row.date]),
      ]);  // 或vue渲染函数 
    }
    chidren: [{     // 多级表头
      ...
    }]
  },
  {
    label: '操作',
    // 自定义渲染
    render: (h, scope) => {
      return (
        <div>
          <el-button
            size="mini"
            onClick={ () => { this.handleEdit(scope.$index, scope.row) } }>编辑</el-button>
          <el-button
            size="mini"
            type="danger"
            onClick={ () => { this.handleDelete(scope.$index, scope.row) } }>删除</el-button>
        </div>
      )
    }
  }
  
]

const data: [
    // 对应el-table的data属性
          {
            date: '2016-05-02',
            name: '王小虎1',
            address: '上海市普陀区金沙江路 1518 弄'
          },
          {
            date: '2016-05-02',
            name: '王小虎2',
            address: '上海市普陀区金沙江路 1518 弄'
          },
          {
            date: '2016-05-02',
            name: '王小虎3',
            address: '上海市普陀区金沙江路 1518 弄'
          }
]
```
<template>
  <el-dialog
    custom-class="seled-tea-container"
    :visible="modalVisible"
    width="890px"
    :before-close="handleClose"
    :modal-append-to-body="false"
    :close-on-click-modal="false"
  >
    <div slot="title" class="dialog-title">
      <span style="margin-right: 10px">选择老师</span>
    </div>
    <el-row>
      <el-col :span="6" style="width: 160px">
        <el-select
          clearable
          v-model="subjectId"
          ref="clzzSection"
          filterable
          placeholder="请选择学科"
          @change="selectSubjectChange"
        >
          <el-option
            v-for="item in subjectList"
            :key="item.id"
            :label="item.subject_name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>
    <div class="sele-tea-area">
      <el-transfer
        ref="selectTeaTransfer"
        :titles="['学科全部老师', '已选老师']"
        :data="subTeacherList"
        :render-content="renderFunc"
        v-model="currSeleTeaId"
        filterable
        :filter-method="searchTeacher"
        @change="selTeacher"
      ></el-transfer>
      <div class="selected-teachers-panel">
        <div class="selected-teachers-title">已选老师</div>
        <div class="selected-teachers-content">
          <el-tag
            class="teacher-tag"
            v-for="(tea,index) in selectedTea"
            :key="tea.key"
            :closable="!tea.isSelected"
            :effect="tea.isSelected ? 'plain' : 'light'"
            @close="seleTeacher(index, 'left', tea.key)">
            <span class="teacher-name">{{tea.label}}</span>
            <span v-if="tea.mobile" class="teacher-mobile">（{{tea.mobile}}）</span>
          </el-tag>
        </div>
      </div>
      <!-- <el-table class="seled-tea-container" style="width: 180px" :data="selectedTea" height="288">
        <el-table-column prop="label" label="已选老师"> 
          <template slot-scope="scope">
            <el-tooltip class="item" effect="dark" :content="scope.row.label+'('+scope.row.mobile+')'" placement="top-start">
              <div style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{{ scope.row.label }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="action" label="操作">
          <template slot-scope="scope">
            <el-icon
              class="el-icon-close"
              v-if="!scope.row.isSelected"
              @click.native="seleTeacher(scope.$index, 'left', scope.row.key)"
            ></el-icon>
          </template>
        </el-table-column>
      </el-table> -->
    </div>
    <!-- </div> -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeModal">取 消</el-button>
      <el-button type="primary" :loading="isComfirming" @click="sureClick">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getSubjectsAPI, findTeacherAssignmentList } from "@/service/api";

export default {
  name: "sele-more-tea",
  props: {
    modalVisible: {
      type: Boolean,
      required: true,
    },
    titleName: {
      type: String,
      default: "",
    },
    subjectid: {
      type: String,
      required: true,
    },
    workId: {
      type: String,
      required: true,
    },
    teaList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      subjectId: "",
      subjectList: [],
      subTeacherList: [],
      currSeleTeaId: [],
      listStyle: {
        height: "300px",
      },
      tarKeys: [],
      moveKey: [],
      selectedTea: [], //已选的教师
      left: "",
      // 提交按钮是否在加载中
      isComfirming: false,
      renderFunc: (h, option) => {
        return <div>
          <div>{ option.label }</div>
          <div style="font-size: 12px;color: #999;">{ option.mobile }</div>
        </div>;
      }
    };
  },
  methods: {
    // 关闭弹窗
    handleClose(done) {
      this.$emit("close-sele-tea-modal");
      done();
    },
    /**
     * 获取学科列表
     */
    getSubjectList() {
      let _this = this;
      let params = {
        schoolId: this.$sessionSave.get("schoolInfo").id,
      };
      getSubjectsAPI(params)
        .then(
          async (result) => {
            if (result && parseInt(result.code) === 1) {
              _this.subjectList = result.data;
              // this.$nextTick(async () => {
              //请求学科下的教师
              if(_this.subjectid.includes('-')){
                _this.subjectid = _this.subjectid.split('-')[0]
              }
              await _this.selectSubjectChange(_this.subjectid);
              //获取已选择的教师
              _this.getSelectedTea();
              // })
            } else {
              _this.$Message.error(result.msg);
            }
          },
          function (err) {
            _this.$Message.error(err.toString());
          }
        )
        .catch(function (error) {
          console.log(error);
        })
    },
    /**
     * 根据学科获取教师列表
     */
    getTeacherAssignmentList() {
      let _this = this;
      let params = {
        schoolId: this.$sessionSave.get("schoolInfo").id,
        userId: this.$sessionSave.get("loginInfo").id,
        workId: _this.workId,
        subjectId: _this.subjectId,
        other: 1, //是否是选择其他老师 0：否 1：是 默认1
      };
      findTeacherAssignmentList(params)
        .then(function (result) {
          if (result.code == 1) {
            let teaData = result.data;
            _this.subTeacherList = [];
            for (let i = 0; i < teaData.length; i++) {
              let curObj = _this.teaList.filter((sitem) => {
                return teaData[i].teaId === sitem.teaId;
              })[0];
              if (!curObj) {
                let teaObj = {};
                teaObj.key = teaData[i].teaId;
                teaObj.label = teaData[i].teaName;
                teaObj.mobile = teaData[i].mobile;
                _this.subTeacherList.push(teaObj);
              }
            }
          }
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    /**
     * 获取已选择的教师
     */
    getSelectedTea() {
      this.selectedTea = [];
      for (let i = 0; i < this.teaList.length; i++) {
        let teaObj = {};
        teaObj.key = this.teaList[i].teaId;
        teaObj.label = this.teaList[i].teaName;
        teaObj.mobile = this.teaList[i].mobile;
        teaObj.isSelected = true;
        this.selectedTea.push(teaObj);
      }
      // console.log(this.selectedTea);
    },
    /**
     * 确认已选择的教师
     */
    sureClick() {
      this.$emit("confirm-sele-tea", this.selectedTea);
    },
    /**
     * 取消选择的教师
     */
    closeModal() {
      // this.$refs.clzzSection.clearSingleSelect();
      this.$emit("close-sele-tea-modal");
    },
    /**
     * 切换学科
     */
    async selectSubjectChange(val) {
      this.subjectId = val.toString();
      //  获取教师列表
      await this.getTeacherAssignmentList();
    },
    /**
     * 查询老师
     */
    searchTeacher(query, data) {
      return data.label.indexOf(query) > -1;
    },
    /**
     * 确定选择老师
     */
    selTeacher(targetKeys, direction, moveKeys) {
      let _this = this;
      if (direction === "right") {
        let currSeleTea = [];
        for (let key of targetKeys) {
          for (let item of _this.subTeacherList) {
            if (key == item.key) {
              currSeleTea.push({ key: item.key, label: item.label, mobile: item.mobile });
            }
          }
        }
        for (let i = 0; i < currSeleTea.length; i++) {
          let curObj = _this.selectedTea.filter((sitem) => {
            return currSeleTea[i].key === sitem.key;
          })[0];
          if (!curObj) {
            let obj = {};
            obj.key = currSeleTea[i].key;
            obj.label = currSeleTea[i].label;
            obj.mobile = currSeleTea[i].mobile;
            _this.selectedTea.push(obj);
          }
        }
      } else if (direction === "left") {
        // js 倒叙循环删除数据
        for (let i = moveKeys.length - 1; i >= 0; i--) {
          for (let j = _this.selectedTea.length - 1; j >= 0; j--) {
            if (moveKeys[i] === _this.selectedTea[j].key) {
              _this.selectedTea.splice(j, 1);
            }
          }
        }
      }
      _this.currSeleTeaId = targetKeys;
      _this.tarKeys = targetKeys;
      _this.moveKey = moveKeys;
      _this.left = direction;
    },
    /**
     * 选择老师
     */
    seleTeacher(index, left, moveKey) {
      let _this = this;
      if (left === "left") {
        // js 倒叙循环删除数据
        for (let j = _this.selectedTea.length - 1; j >= 0; j--) {
          if (moveKey === _this.selectedTea[j].key) {
            _this.selectedTea.splice(index, 1);
          }
        }
        for (let i = _this.currSeleTeaId.length - 1; i >= 0; i--) {
          if (moveKey === _this.currSeleTeaId[i]) {
            _this.currSeleTeaId.splice(i, 1);
          }
        }
      }
    },
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
.seled-tea-container {
  width: 180px;
  position: absolute;
  right: 60px;
  top: 0;
  border: 1px solid #ddd;
  border-bottom: 0;
  border-radius: 5px;
  .sele-tea-area {
    margin-top: 20px;
    position: relative;
  }
}

.selected-teachers-panel {
  width: 200px;
  position: absolute;
  right: 60px;
  top: 0;
  border: 1px solid #ebeef5;
  border-radius: 5px;
  background-color: #fff;
  overflow: hidden;
  
  .selected-teachers-title {
    padding: 0 15px;
    font-size: 15px;
    height: 40px;
    line-height: 40px;
    color: #303133;
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
  }
  
  .selected-teachers-content {
    padding: 10px;
    height: 260px;
    overflow-y: auto;
    
    .teacher-tag {
      margin: 5px;
    }
  }
}
</style>
<style lang="scss">
.seled-tea-container {
  .ivu-table-body {
    max-height: 255px;
    overflow-x: hidden;
    overflow-y: auto;
  }

  .el-dialog__header {
    height: 45px;

    .dialog-title {
      line-height: 45px;
    }
  }

  .el-checkbox {
    &.el-checkbox + .el-checkbox {
      margin-left: 0;
    }
  }
}

.sele-tea-area {
  .ivu-transfer-operation .ivu-btn {
    width: 30px !important;
  }

  .ivu-input-small {
    height: 24px !important;
  }

  .ivu-input-wrapper {
    padding-top: 3px;
  }

  .ivu-table-cell {
    padding-right: 0 !important;
  }
  .el-transfer{
      .el-transfer-panel__body{
        .el-transfer-panel__item{
          height: 40px;
          .el-checkbox__label{
            line-height: 18px;
          }
        }
      }
    }
}
</style>

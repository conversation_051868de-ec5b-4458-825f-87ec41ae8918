<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-06-25 16:07:56
 * @LastEditors: 小圆
-->
<template>
  <div class="subjective-edit">
    <!-- 智批主观题 -->
    <template v-if="quesItem.type == 15 || quesItem.ai_mode == IQUES_SCAN_MODE.AI_FILL">
      <div
        class="ques-pos"
        :style="{ ...getBoxStyle(quesItem.pos) }"
        :class="{ 'hover-border': hoverQuesNo == quesItem.question_no }"
      ></div>
      <div
        v-for="(item, index) in quesItem.score_list"
        class="ques-pos"
        :class="{ 'hover-border-blue': hoverQuesNo == quesItem.question_no && hoverOptionIndex == index }"
        :key="index"
        :style="{ ...getBoxStyle(item.pos) }"
      >
        <div
          v-if="item.total_score > 0 && !disabledEdit"
          class="online-ques"
          @click="checkOnline(item)"
          @mouseenter="mouseEnter(index)"
          @mouseleave="mouseLeave"
        >
          <img :src="iconCheckImg" alt="" class="symbol-svg" v-if="item.score == item.total_score" />
          <img :src="iconErrorImg" alt="" class="symbol-svg" v-else-if="item.score == 0" />
          <img :src="iconHalfcheckImg" alt="" class="symbol-svg" v-else />
        </div>
      </div>
    </template>

    <!-- 手写打分题 -->
    <template v-else-if="quesItem.type == 13">
      <div
        class="score-input-box"
        :style="getBoxStyle(quesItem.pos)"
        :class="{ 'hover-border': hoverQuesNo == quesItem.question_no }"
      >
        <div
          v-if="quesItem.total_score > 0 && !disabledEdit"
          class="score-input-content"
          @mouseenter="mouseEnter(-1)"
          @mouseleave="mouseLeave"
        >
          <span>修改得分：</span>
          <el-input-number
            v-model="quesItem.score"
            class="score-input"
            size="small"
            placeholder="0"
            :controls="false"
            :min="0"
            :max="quesItem.total_score"
            @mousedown.native.stop
          ></el-input-number>

          <span class="score-unit">分</span>
        </div>
      </div>
    </template>

    <!-- 画线打分主观题 -->
    <template v-else-if="scoreRowArr && scoreRowArr.length">
      <div
        class="ques-pos"
        :style="{ ...getBoxStyle(quesItem.pos) }"
        :class="{ 'hover-border': hoverQuesNo == quesItem.question_no }"
      ></div>
      <div v-for="(item, index) in scoreRowArr" :key="index" @mouseenter="mouseEnter(index)" @mouseleave="mouseLeave">
        <div class="subject-row" v-for="(line, lineIndex) in item" :key="lineIndex" :style="getBoxStyle(line.pos)">
          <div
            v-for="(scoreItem, scoreIndex) in line.score_list"
            v-show="!disabledEdit"
            :key="scoreIndex"
            :style="{ width: getColWidth(line.cols) + 'px' }"
            class="subject-col"
            :class="{
              active: scoreItem.fill,
            }"
            @click="!disabledEdit && checkSubjectCol(item, index, line, lineIndex, scoreItem, scoreIndex)"
          ></div>
        </div>
      </div>
    </template>

    <!-- 填空题 -->
    <div
      v-else-if="quesItem.question_type == IQUES_TYPE.fill || quesItem.question_type == IQUES_TYPE.fillEva"
      class="ques-pos"
      :class="{ 'hover-border': hoverQuesNo == quesItem.question_no }"
      :style="getBoxStyle(quesItem.pos)"
    >
      <div
        v-if="quesItem.total_score > 0 && !disabledEdit"
        class="fill-eva-ques"
        @click="checkFill"
        @mouseenter="mouseEnter(-1)"
        @mouseleave="mouseLeave"
      >
        <img :src="iconCheckImg" alt="" class="symbol-svg" v-if="quesItem.score == quesItem.total_score" />
        <img :src="iconErrorImg" alt="" class="symbol-svg" v-else-if="quesItem.score == 0" />
        <img :src="iconHalfcheckImg" alt="" class="symbol-svg" v-else />
      </div>
    </div>

    <!-- 简答题 -->
    <template v-else>
      <div
        class="score-input-box"
        :class="{ 'hover-border': hoverQuesNo == quesItem.question_no }"
        :style="getBoxStyle(quesItem.pos)"
      >
        <div
          v-if="quesItem.total_score > 0 && !disabledEdit"
          class="score-input-content"
          @mouseenter="mouseEnter(-1)"
          @mouseleave="mouseLeave"
        >
          <span>修改得分：</span>
          <el-input-number
            v-model="quesItem.score"
            class="score-input"
            size="small"
            placeholder="0"
            :controls="false"
            :min="0"
            :max="quesItem.total_score"
            @mousedown.native.stop
          ></el-input-number>

          <span class="score-unit">分</span>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { Question, ScoreList } from '../StuPaperInterface';
import { IQUES_SCAN_MODE, IQUES_TYPE } from '@/typings/card';
import QuesEditStore from './QuesEditStore';
import { getSubjectCenterCode } from '@/utils/UserRoleUtils';

@Component({
  components: {},
})
export default class subjectiveQuesEditor extends Vue {
  IQUES_SCAN_MODE = IQUES_SCAN_MODE;
  iconCheckImg = require('@/assets/icon_check.png');
  iconErrorImg = require('@/assets/icon_error.png');
  iconHalfcheckImg = require('@/assets/icon_halfcheck.png');

  IQUES_TYPE = IQUES_TYPE;
  @Prop() quesItem: Question;
  @Prop({ default: 1 }) scale: number;
  // 分数映射
  @Prop({
    default: () => {
      return {};
    },
  })
  scoreMap: any;

  // 打分行
  scoreRowArr: { pos: number[]; score_list: { value: number; fill: boolean }[]; cols: number }[][] = [];

  quesEditStore = QuesEditStore;

  get hoverQuesNo() {
    return this.quesEditStore.quesNo;
  }

  get hoverOptionIndex() {
    return this.quesEditStore.optionIndex;
  }

  get disabledEdit() {
    return this.quesItem.special_type == 1 || this.quesItem.special_type == 2;
  }

  mounted() {
    this.scoreRowArr = this.getScoreArr();
  }

  // 获取列宽
  getColWidth(cols: number) {
    return (this.quesItem.pos[2] * this.scale) / cols;
  }

  // 获取打分条
  getScoreArr() {
    let arr = [];
    if (this.quesItem.ai_mode === IQUES_SCAN_MODE.AI_SUBJECT || this.quesItem.ai_mode === IQUES_SCAN_MODE.AI_ESSAY) {
      return [];
    }
    if (this.quesItem.score_list && this.quesItem.score_list.length) {
      for (const item of this.quesItem.score_list) {
        let scores = [];
        item.scores?.forEach(it => {
          let fill = item.score == it && this.quesItem.miss_score != this.quesItem.score ? true : false;
          scores.push({ value: it, fill: fill });
        });
        //每一行的高度
        let rowHeight = item.pos[3] / (item.rows || 1);
        const newArray = [];
        for (let i = 0; i < scores.length; i += item.cols) {
          const subArray = scores.slice(i, i + item.cols);
          newArray.push(subArray);
        }
        let array1 = [];
        newArray.forEach((everyRow, index) => {
          let tempObj = { pos: [], score_list: [], cols: 0 };
          tempObj.pos = [item.pos[0], item.pos[1] + index * rowHeight, item.pos[2], rowHeight];
          tempObj.score_list = [...everyRow];
          tempObj.cols = item.cols;
          array1.push(tempObj);
        });
        arr.push(array1);
      }
    }
    return arr;
  }

  // 获取题块区域样式
  getBoxStyle(pos: number[]) {
    if (!pos) return;
    return {
      left: pos[0] * this.scale + 'px',
      top: pos[1] * this.scale + 'px',
      width: pos[2] * this.scale + 'px',
      height: pos[3] * this.scale + 'px',
    };
  }

  // 选择填空题
  checkFill() {
    this.quesItem.score = this.quesItem.score == 0 ? this.quesItem.total_score : 0;
  }

  // 选择线上批改
  checkOnline(item: ScoreList) {
    const subjectCenterCode = getSubjectCenterCode(this.$route.query.subjectId as string);
    let scoreList = [item.total_score, item.total_score / 2, 0];
    if (subjectCenterCode == 'ENGLISH') {
      scoreList = [item.total_score, 0];
    }
    let nextIndex = scoreList.findIndex(it => it == item.score) + 1;
    if (nextIndex >= scoreList.length) {
      nextIndex = 0;
    }
    item.score = scoreList[nextIndex];
    this.quesItem.score = this.quesItem.score_list.reduce((acc, cur) => acc + cur.score, 0);
  }

  // 选择打分条
  checkSubjectCol(
    item: { pos: number[]; score_list: { value: number; fill: boolean }[]; cols: number }[],
    index: number,
    line: { pos: number[]; score_list: { value: number; fill: boolean }[]; cols: number },
    lineIndex: number,
    scoreItem: { value: number; fill: boolean },
    scoreIndex: number
  ) {
    for (const [lIndex, line] of this.scoreRowArr[index].entries()) {
      for (const [sIndex, score] of line.score_list.entries()) {
        if (sIndex == scoreIndex && lIndex == lineIndex) {
          score.fill = !score.fill;
        } else {
          score.fill = false;
        }
      }
    }

    if (this.quesItem.miss_score == -1) {
      // 正常得分
      this.quesItem.score_list[index].score = Math.max(scoreItem.fill ? scoreItem.value : 0, 0);
    } else {
      // 仅标识错误
      this.quesItem.score_list[index].score = scoreItem.fill ? scoreItem.value : this.quesItem.miss_score;
    }

    let sumScore = this.quesItem.score_list.reduce((acc, cur) => acc + cur.score, 0);
    if (sumScore > this.quesItem.total_score) {
      this.$notify({
        title: '提示',
        message: '分数超出题目总分！',
        type: 'warning',
      });
      this.setScoreChoice(this.quesItem.total_score);
      sumScore = this.quesItem.score_list.reduce((acc, cur) => acc + cur.score, 0);
    }
    if (this.quesItem.scoringMode == 1) {
      // 减分制
      this.quesItem.score = Math.max(this.quesItem.total_score - sumScore, 0);
    } else {
      // 加分制
      this.quesItem.score = Math.min(sumScore, this.quesItem.total_score);
    }
  }

  // 设置打分条分数
  setScoreChoice(quesScore: number) {
    for (const [lineIndex, line] of this.scoreRowArr.entries()) {
      this.quesItem.score_list[lineIndex].score = 0;
      for (const score of line) {
        score.score_list.forEach(it => {
          if (it.value == quesScore) {
            it.fill = true;
            this.quesItem.score_list[lineIndex].score = quesScore;
          } else {
            it.fill = false;
          }
        });
      }
    }
  }

  mouseEnter(index) {
    this.quesEditStore.setHoverQues(this.quesItem.question_no, index);
  }

  mouseLeave() {
    this.quesEditStore.removeHoverQues();
  }
}
</script>

<style scoped lang="scss">
.ques-pos {
  position: absolute;
}

.fill-eva-ques {
  position: absolute;
  bottom: 0;
  right: 0;

  cursor: pointer;
}

.online-ques {
  position: absolute;
  bottom: 0;
  right: 0;

  cursor: pointer;

  &:hover {
    // border: 1px dashed #409eff;

    .symbol-svg {
      opacity: 1;
    }
  }
}

.symbol-svg {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 30px;
  height: 30px;
  z-index: 10;
  opacity: 0.9;

  &:hover {
    opacity: 1;
  }
}

.subject-row {
  position: absolute;
  z-index: 2;
  display: flex;
}

.subject-col {
  height: 100%;
  text-align: center;
  border: solid 2px #409eff;
  border-right: unset;
  z-index: 3;
  cursor: pointer;

  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(#409eff, 0.3);
  }

  &.active {
    border: solid 4px #01cc7d;
    &:last-child {
      border-right: solid 4px #01cc7d;
    }
  }
}

.score-input-box {
  position: absolute;

  .score-input-content {
    position: absolute;
    // bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    font-size: 14px;
    font-weight: 700;

    .score-input {
      width: 70px !important;
    }

    ::v-deep {
      .el-input__inner {
        color: #f56c6c;
        font-weight: 700;
      }
    }

    .score-unit {
      position: absolute;
      top: 0;
      line-height: 32px;
      right: 4px;
    }
  }
}

.hover-border {
  background-color: rgba(#409eff, 0.06);

  &-blue {
    background-color: rgba(#004080, 0.1);
  }
}
</style>

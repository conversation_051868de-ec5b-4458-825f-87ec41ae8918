/*
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-11-20 14:54:49
 * @LastEditors: 小圆
 */
export default [
  {
    path: 'examOverview',
    name: '考试总览',
    component: resolve => require(['../../pages/lookReport/examOverview.vue'], resolve),
    children: [
      {
        path: 'overview', // 学情概览
        component: resolve => require(['../../pages/lookReport/reportOverview/overview.vue'], resolve),
      },
      {
        path: 'fiveRate', // 一分五率
        component: resolve => require(['../../pages/lookReport/reportOverview/fiveRate.vue'], resolve),
      },
      {
        path: 'subjectComparison', // 科目对比
        component: resolve =>
          require(['../../pages/lookReport/reportOverview/subjectComparison.vue'], resolve),
      },
      {
        path: 'scoreSheet', // 学生小分表
        component: resolve =>
          require(['../../pages/lookReport/reportOverview/scoreSheet.vue'], resolve),
      },
      {
        path: 'questionAnalysis', // 学生小分表
        component: resolve =>
          require(['../../pages/lookReport/reportOverview/questionAnalysis.vue'], resolve),
      },
      {
        path: 'gradeDistribute', // 等级分布
        component: resolve =>
          require(['../../pages/lookReport/reportOverview/gradeDistribute.vue'], resolve),
      },
      {
        path: 'topAndDiff', // 优困生分布
        component: resolve =>
          require(['../../pages/lookReport/reportOverview/topAndDiff.vue'], resolve),
      },
      {
        path: 'online', // 上线分析
        component: resolve => require(['../../pages/lookReport/reportOverview/online.vue'], resolve),
      },
      {
        path: 'limitStu', // 临界生分布
        component: resolve => require(['../../pages/lookReport/reportOverview/limitStu.vue'], resolve),
      },
    ],
  },
  {
    path: 'paperComment',
    name: 'paperComment',
    component: resolve => require(['../../pages/lookReport/paperComment.vue'], resolve),
    meta: {
      // keepAlive: true,
    },
  },
  {
    path: 'paperCommentDetail',
    name: '试卷讲评详情',
    component: resolve => require(['../../pages/lookReport/paperCommentDetail.vue'], resolve),
  },
  {
    path: 'classCompare',
    component: resolve => require(['../../pages/lookReport/classCompare.vue'], resolve),
    children: [
      {
        path: 'cardHome',
        component: resolve => require(['../../pages/lookReport/classCompare/reportCard.vue'], resolve),
      },
      {
        path: 'cardDetail',
        component: resolve =>
          require(['../../pages/lookReport/classCompare/reportCard/cardDetail.vue'], resolve),
      },
      {
        path: 'scoreSection', // 分数段分布
        component: resolve =>
          require(['../../pages/lookReport/classCompare/scoreSection.vue'], resolve),
      },
      {
        path: 'totalRank', // 总名次分布
        component: resolve => require(['../../pages/lookReport/classCompare/totalRank.vue'], resolve),
      },
      {
        path: 'rankSection', // 各名次段分布
        component: resolve =>
          require(['../../pages/lookReport/classCompare/rankSection.vue'], resolve),
      },
      {
        path: 'boxplot', // 箱线图
        component: resolve => require(['../../pages/lookReport/classCompare/boxplot.vue'], resolve),
      },
    ],
  },
  {
    path: 'paperAnalyze',
    component: resolve => require(['../../pages/lookReport/paperAnalyze.vue'], resolve),
    children: [
      {
        path: 'qualityReport', // 命题质量
        component: resolve =>
          require(['../../pages/lookReport/paperAnalyze/qualityReport.vue'], resolve),
      },
      {
        path: 'bothWayReport', // 双向细目表
        component: resolve =>
          require(['../../pages/lookReport/paperAnalyze/bothWayReport.vue'], resolve),
      },
      {
        path: 'quesTypeAvg', // 题型均分
        component: resolve =>
          require(['../../pages/lookReport/paperAnalyze/quesTypeAvg.vue'], resolve),
      },
      {
        path: 'smallQuesType', // 小题均分
        component: resolve =>
          require(['../../pages/lookReport/paperAnalyze/smallQuesAvg.vue'], resolve),
      },
      {
        path: 'smallQuesReport', // 小题分析
        component: resolve =>
          require(['../../pages/lookReport/paperAnalyze/smallQuesReport.vue'], resolve),
      },
      {
        path: 'answerDetail', // 作答详情
        component: resolve =>
          require(['../../pages/lookReport/paperAnalyze/answerDetail.vue'], resolve),
      },
      {
        path: 'knowledgeAnalyze', //知识点分析
        component: resolve =>
          require(['../../pages/lookReport/paperAnalyze/knowledgeAnalyze.vue'], resolve),
      },
    ],
  },
  {
    path: 'personalPracticeReport',
    name: '个性化练习报告',
    component: resolve =>
      require(['../../pages/lookReport/personalPracticeReport/reportHome.vue'], resolve),
    redirect: '/home/<USER>/personalPracticeReport/practiceReport',
    children: [
      {
        path: 'practiceReport',
        component: resolve => require(['../../pages/lookReport/practiceReport.vue'], resolve),
      },
      {
        path: 'reportDetail',
        component: resolve =>
          require(['../../pages/lookReport/personalPracticeReport/reportDetail.vue'], resolve),
      },
    ],
  },
  // 作文分析-班级学情
  {
    path: 'compositionClassSituation',
    component: resolve => require(['../../pages/lookReport/compositionAnalyse/compositionClassSituation.vue'], resolve),
  },
  // 作文分析-词汇分析
  {
    path: 'lexicalAnalysis',
    component: resolve => require(['../../pages/lookReport/compositionAnalyse/lexicalAnalysis.vue'], resolve),
  },
  // 作文分析-语法分析
  {
    path: 'grammarAnalysis',
    component: resolve => require(['../../pages/lookReport/compositionAnalyse/grammarAnalysis.vue'], resolve),
  },
  // 作文分析-参考范文
  {
    path: 'referenceSample',
    component: resolve => require(['../../pages/lookReport/compositionAnalyse/referenceSample.vue'], resolve),
  },
  // 校区对比
  {
    path: 'campusComparison',
    component: resolve => require(['../../pages/lookReport/campusComparison/index.vue'], resolve),
  },
];

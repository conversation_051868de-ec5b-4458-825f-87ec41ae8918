<template>
  <!--视频播放弹窗-->
  <el-dialog :title="title" append-to-body custom-class="video-player-dialog" :visible="true" :fullscreen="fullscreen"
    @close="closeDialog">
    <div v-show="loading" v-loading="loading" class="video-player-dialog-loading" height="100%" width="100%"></div>
    <video :src="videoSrc" width="100%" height="100%" controls :autoplay="true" v-if="isVideo" v-show="!loading">
      您的浏览器不支持 video 标签
    </video>
    <iframe v-else v-show="!loading" height="100%" width="100%" :src="videoSrc" frameborder="0"
      @load="loading = false"></iframe>
  </el-dialog>
</template>

<script>
import { getHuohuaPreviewLink } from '@/utils';

let filterTimer = null;
export default {
  props: ["src", "title", "fullscreen"],
  data() {
    return {
      videoSrc: "",
      loading: true,
      isVideo: false,
    };
  },
  async created() {
    if (this.src.startsWith("https://www.huohuaschool.com/")) {
      let loginInfo = this.$sessionSave.get('loginInfo');
      this.videoSrc = await getHuohuaPreviewLink(this.src, loginInfo.huohua_id);
    } else {
      let match = this.src.match(
        /\/cooperation\/tencent\/let\/download_video.php\?id=([^&]*)/
      );
      if (match) {
        let id = match[1];
        let LLKT_URL = "https://gc.iclass30.com/";
        if (id) {
          this.videoSrc = `${LLKT_URL}llkt/${id}.mp4`;
        }
      } else {
        this.videoSrc = this.src;
      }
    }
    this.isVideo = !/videoPlay\?src=/gi.test(this.videoSrc) && /.mp4/g.test(this.videoSrc);
    this.loading = !this.isVideo;
  },
  methods: {
    closeDialog() {
      this.$emit("close-dialog");
    },
  },
};
</script>

<style lang="scss">
.video-player-dialog {

  // margin-top: 0 !important;
  // height: 100% !important;
  .el-dialog__body {
    position: relative;
    padding: 0;
    min-height: 350px;
    padding-top: 56.25%;

    >* {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 1;
      top: 0;
      left: 0;
    }

    // min-height: 650px !important;
    .video-player-dialog-loading {
      width: 100%;
      height: 100%;
    }
  }
}
</style>


<template>
  <div class="right-quesList-inner">
    <div v-for="(item, index) in expandQuesList" :key="index">
      <template v-if="item.isEs">
        <div class="quesList-item display_flex flex-direction_column">
          <div class="queList-content over-hidden" @click="showMoreDetail(item)">
            <!--大题题面-->
            <div
              class="content"
              v-if="item.data.levelcode != ''"
              v-html="convertHtml(item.data.desc_html)"
            ></div>
            <!--小题-->
            <div class="ques-detail-small-ques">
              <!--小题序号-->
              <div class="ques-detail-button" v-if="item.data.qs.length > 1">
                <el-button
                  v-for="(sq, sindex) in item.data.qs"
                  :key="sindex"
                  :class="['ques-detail-small-button', { active: sindex == item.smallQuesIndex }]"
                  circle
                  @click.stop="viewBigSmallQues(index, sindex)"
                >
                  {{ sindex + 1 }}
                </el-button>
              </div>

              <template v-for="(sq, sindex) in item.data.qs">
                <div
                  class="point-box"
                  style="display: contents"
                  v-if="item.smallQuesIndex == sindex"
                >
                  <!--小题题面-->
                  <div v-html="convertHtml(sq.desc_html)"></div>
                  <!--小题选项-->
                  <div v-if="item.data.type == 8 || item.data.type == 1" class="option-box">
                    <div
                      class="option-item"
                      v-for="(opt, optindex) in sq.opts_htmls"
                      :key="optindex"
                    >
                      <span class="option-item-letter">{{
                        String.fromCharCode(65 + optindex)
                      }}</span>
                      <span v-html="convertHtml(opt)"> </span>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>

          <div class="queList-footer">
            <div class="difficult-star pull-left display_flex align-items_center">
              难度:
              <span class="star" v-for="i in item.data.difficulty" :key="i"></span>
            </div>
            <template>
              <span
                class="add edit-btn edit-text selectIn text-center"
                @click.stop="selectQues(item)"
                :class="[
                  selectIds.length && selectIds.indexOf(item.qId) >= 0
                    ? 'hasAdd el-icon-minus'
                    : 'el-icon-plus',
                ]"
              >
                {{ selectIds.length && selectIds.indexOf(item.qId) >= 0 ? ' 移除' : ' 选入' }}
              </span>
              <span class="edit-text detail" @click="showMoreDetail(item)">详情</span>
              <!--          <span class="edit-text error-correction" @click="setDebunk(item)">纠错</span>-->
            </template>
          </div>

          <div class="ques-detail-box" v-show="item.showDetails">
            <template v-for="(sq, sindex) in item.data.qs">
              <div class="ques-detail-small-ques" v-if="item.smallQuesIndex === sindex">
                <div class="point-box">
                  <span style="width: 55px; white-space: nowrap">知识点：</span>
                  <div>
                    <template v-if="sq.tag_ids && sq.tag_ids.length">
                      <span v-for="(tag, tagIndex) in sq.tag_ids" :key="'tag' + tagIndex"
                        >{{ tag.name }}；</span
                      >
                    </template>
                    <span v-else>略</span>
                  </div>
                </div>
                <div class="answer-box">
                  <span style="width: 46px; white-space: nowrap">答案：</span>
                  <!-- 判断题 -->
                  <div v-if="item.data.type == 2">
                    {{ sq.ans == 'A' ? '正确' : '错误' }}
                  </div>
                  <!--多选题 -->
                  <div v-else-if="item.data.type == 1">
                    <div v-for="(ans, ansindex) in sq.ans" :key="ansindex" style="display: flex">
                      <span>{{ ans }}</span>
                    </div>
                  </div>
                  <!-- 填空智批题 -->
                  <div v-else-if="item.data.type == 7">
                    <div v-for="(ans, ansindex) in sq.ans" :key="ansindex">
                      <div v-html="convertHtml(ans)"></div>
                    </div>
                  </div>
                  <div v-else v-html="convertHtml(isString(sq.ans) ? sq.ans : sq.ans[0])"></div>
                </div>
                <div class="analysis-box">
                  <span style="width: 46px; white-space: nowrap">解析：</span>
                  <div v-html="convertHtml(sq.exp)"></div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </template>
      <template v-else>
        {{ item.isEs }}
        <div class="quesList-item display_flex flex-direction_column">
          <div class="queList-content over-hidden" @click="showMoreDetail(item)">
            <div v-if="item.topic">
              <div v-html="item.topic"></div>
              <div v-if="item.quesType == 1 || item.quesType == 8" class="option-box">
                <!-- <div v-if="item.optionArrange==1"> -->
                <div :class="['', 'row-four', 'row-two'][item.optionArrange - 1]">
                  <div
                    class="option-item"
                    v-for="(it, index) in item.optionText"
                    :key="index"
                    :class="['', 'flex-four', 'flex-two'][item.optionArrange - 1]"
                  >
                    <span class="option-item-letter"
                      >{{ String.fromCharCode(65 + index) + '.' }}&nbsp;</span
                    >
                    <span v-html="it"></span>
                  </div>
                </div>
              </div>
            </div>
            <div v-else="item.content" v-html="item.content"></div>
          </div>
          <div class="queList-footer">
            <div class="difficult-star pull-left display_flex align-items_center">
              难度:
              <template v-if="item.difficultyCode">
                <!-- 网络题 -->
                <span class="star" v-for="i in item.difficultyCode" :key="i + item.quesCode"></span>
              </template>
              <template v-else>
                <!-- 个册题 -->
                <span class="star" v-for="i in item.data.difficulty" :key="i + item.id"></span>
              </template>
            </div>
            <template>
              <span
                class="add edit-btn edit-text selectIn text-center"
                @click.stop="selectQues(item)"
                :class="[
                  selectIds.length && selectIds.indexOf(item.id) >= 0
                    ? 'hasAdd el-icon-minus'
                    : 'el-icon-plus',
                ]"
              >
                {{ selectIds.length && selectIds.indexOf(item.id) >= 0 ? ' 移除' : ' 选入' }}
              </span>
              <span class="edit-text detail" @click="showMoreDetail(item)">详情</span>
              <span class="edit-text error-correction" @click="setDebunk(item)">纠错</span>
            </template>
          </div>
          <div class="ques-detail-box" v-show="item.showDetails">
            <div class="point-box">
              <span style="width: 55px; white-space: nowrap">知识点：</span>
              <div>{{ item.knowledgeName }}</div>
            </div>
            <div class="answer-box">
              <span style="width: 46px; white-space: nowrap">答案：</span>
              <div v-html="item.answer"></div>
            </div>
            <div class="analysis-box">
              <span style="width: 46px; white-space: nowrap">解析：</span>
              <div v-html="item.analysis"></div>
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- 分页 -->
    <div v-if="expandQuesList.length != 0">
      <el-pagination
        class="ques-pagination"
        background
        layout="prev, pager, next, jumper"
        :current-page="currentPage"
        @current-change="paginationChange"
        :hide-on-single-page="true"
        :page-size="10"
        :total="paginationTotal"
      >
      </el-pagination>
    </div>
    <div class="no-ques" v-if="expandQuesList.length == 0 && isLoadingQues == false">
      <div class="bg"></div>
      <p>暂无相关题目，换其他条件试试吧~</p>
    </div>
    <div class="no-ques" v-if="isLoadingQues">
      <p>正在获取题目...</p>
    </div>

    <!--试卷袋-->
    <fixedPaper
      v-on="$listeners"
      ref="fixedPaper"
      class="fixed-box"
      v-if="$route.path.indexOf('teaching/classDetail') >= 0"
      @update="updateData"
    ></fixedPaper>
    <!--试卷袋-->
    <!-- 纠错弹窗 -->
    <el-dialog
      title="题目纠错"
      :visible.sync="debunkDialogVisible"
      width="700px"
      custom-class="debunkDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :append-to-body="true"
      :before-close="handleCloseDebunkDialog"
    >
      <div class="error-item">
        <span>错误类型</span>
        <el-checkbox-group v-model="checkList" @change="handleCheckedErrorChange">
          <el-checkbox :label="item.id" v-for="item in allCauseError" :key="item.name"
            >{{ item.name }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="error-item">
        <span>错误描述</span>
        <el-input
          type="textarea"
          placeholder="请输入错误内容，不超过100字"
          v-model="textarea"
          maxlength="100"
          show-word-limit
          resize="none"
          :rows="3"
        >
        </el-input>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseDebunkDialog">取 消</el-button>
        <el-button type="primary" @click="confirmError" :loading="isSubmitError">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getAllCauseError, quesSearch, setDebunk } from '@/service/ptask';
import { findPersonBookQuesBank, convertHtml } from '@/service/pbook';

import fixedPaper from '@/components/fixedPaper';

let filterTimer = null;
export default {
  name: 'ReplaceQuesCard',
  components: {
    fixedPaper,
  },
  props: ['currentStep', 'onlyView', 'pointId'],
  data() {
    return {
      // 是否ES搜索题目
      isES: false,
      // 查看详情
      visibleDetailId: '',
      // 错因列表
      allCauseError: [],
      // 选错列表
      checkList: [],
      textarea: '',
      // 当前需要纠错的题目
      currentErrorQuesInfo: '',
      // 纠错弹窗可见性
      debunkDialogVisible: false,
      expandQuesList: [],
      // 是否正在加载题目
      isLoadingQues: false,
      // 分页总条数
      paginationTotal: 1,
      // 当前分页位置
      currentPage: 1,
      // 是否正在请求错因
      isSubmitError: false,
      // 知识点信息
      pointInfo: {},
      // 网络题筛选条件
      filterNetInfo: {},
      // C30个册筛选条件
      filterC30Info: {},

      // 卷一客观题，卷二主观题
      quesInfo: [],
    };
  },
  mounted() {},
  computed: {
    selectIds() {
      let totalList = [],
        ids = [];
      this.quesInfo.length &&
        this.quesInfo.forEach(item => {
          totalList = totalList.concat(item.data);
        });
      totalList.length &&
        totalList.forEach(item => {
          if (item.data.length) {
            ids = ids.concat(
              item.data.map(sub => {
                return sub.id;
              })
            );
          }
        });
      return ids;
    },
  },
  methods: {
    isString(str) {
      return typeof str === 'string';
    },
    convertHtml,
    // 题目加入、移除讲评
    selectQues(item) {
      this.$refs.fixedPaper.selectQues(item, item.isEs);
    },
    // 更新加入试卷的数据
    updateData(quesInfo) {
      this.quesInfo = quesInfo || this.quesInfo;
    },
    toEditQuestion(item) {
      this.$router.push({
        path: 'editQuestion',
        query: {
          id: item.id,
        },
      });
    },
    /**
     * 更新过滤信息
     */
    updateFilterInfo({ type, filterList, filterNetInfo, pointInfo, page }) {
      if (filterList) {
        filterList.forEach(it => {
          this.filterC30Info[it.typeKey] = it.activeIndex;
        });
      }

      if (filterNetInfo) {
        this.filterNetInfo = filterNetInfo;
      }

      if (pointInfo) {
        this.pointInfo = pointInfo;
      }

      this.currentPage = page || 1;

      if (type === 'c30') {
        this.debounceFindQuesBank();
      } else {
        this.quesSearch();
      }
    },

    // 防抖 搜索个册题目
    debounceFindQuesBank() {
      clearTimeout(filterTimer);
      filterTimer = setTimeout(() => {
        this.findPersonBookQuesBank();
      }, 300);
    },
    // 搜索个册题目
    findPersonBookQuesBank(pageNum) {
      this.isLoadingQues = true;
      this.$emit('currentPage', pageNum);
      findPersonBookQuesBank({
        knowledgeCode: this.pointInfo.knowledgeCode || this.pointId || '',
        quesType: this.filterC30Info.quesType || '',
        difficultyCode: this.filterC30Info.difficultyCode || '',
        category: this.filterC30Info.category || '',
        solutionId: this.filterC30Info.solutionId || '',
        mathIdeaId: this.filterC30Info.mathIdeaId || '',
        mathLiteraryId: this.filterC30Info.mathLiteraryId || '',
        year: this.filterC30Info.year || '',
        provinceId: this.filterC30Info.provinceId || '',
        typeCode: this.filterC30Info.typeCode || '',
        grade: this.filterC30Info.grade || '',
        keyWord: this.filterC30Info.topic || '',
        page: pageNum || this.currentPage || 1,
        pageSize: 10,
        subjectId: this.$listeners.getParams().subjectId || '',
        type: this.pointInfo.ponitType || 0,
        schoolId:
          this.$sessionSave.get('schoolInfo').id ||
          this.$sessionSave.get('loginInfo').schoolid ||
          '',
        isPoint: 1,
        version: 2,
        sortFiled: 'time',
      })
        .then(data => {
          data.rows.forEach(it => {
            it.showDetails = false;
            it.activeIndex = 0;
            it.smallQuesIndex = 0;
          });
          this.paginationTotal = data.total;
          this.expandQuesList = data.rows;
          this.isLoadingQues = false;
        })
        .catch(err => {
          console.log(err);
        });
      this.$nextTick(() => {
        // this.$refs.fixedPaper.getPaper();
        this.$refs.fixedPaper.getPersonalTestBank();
      });
    },
    // 搜索网络题目
    quesSearch(pageNum) {
      this.isLoadingQues = true;
      // let knowledgeType = (typeof this.knowledgeType) == "number" ? this.knowledgeType : '';
      quesSearch({
        knowledgeCode: this.pointInfo.knowledgeCode || '', // 知识点
        chapterCode: '', // 章节
        examCode: this.filterNetInfo.examCode || '', // 题类
        content: this.filterNetInfo.content, // 关键词
        cateCode: this.filterNetInfo.xfSectionCode || '', // 题型
        difficultyCode: this.filterNetInfo.difficultyCode || '', //难度
        year: this.filterNetInfo.yearCode || '',
        type: this.pointInfo.ponitType || 0,
        pageNum: pageNum || this.currentPage || 1,
        pageSize: 10,
      })
        .then(data => {
          data.rows.forEach(it => {
            it.showDetails = false;
            it.activeIndex = 0;
            it.smallQuesIndex = 0;
          });
          this.paginationTotal = data.total;
          this.expandQuesList = data.rows;
          this.isLoadingQues = false;
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 分页变化
    paginationChange(e) {
      // this.currentPage = e
      // this.quesSearch()
      this.currentPage = e;
      if (this.currentStep == 0) {
        this.findPersonBookQuesBank(e);
      } else {
        this.quesSearch(e);
      }
    },
    // 选入题目前进行预处理
    prehandleAddQues(item) {
      this.$emit('selectOneQues', item);
    },
    // 查看详情
    showMoreDetail(item) {
      item.showDetails = !item.showDetails;
    },
    // 关闭纠错弹窗
    handleCloseDebunkDialog() {
      this.checkList = [];
      this.textarea = '';
      this.currentErrorQuesInfo = null;
      this.debunkDialogVisible = false;
    },
    // 获取错因
    getAllCauseError() {
      if (this.allCauseError.length) {
        return;
      }
      getAllCauseError()
        .then(data => {
          this.allCauseError = data.data;
        })
        .catch();
    },
    // 纠错选择
    handleCheckedErrorChange(val) {
      this.checkList = val;
      console.log(this.checkList);
    },
    // 提交纠错
    confirmError() {
      if (!this.textarea && this.checkList.length == 0) {
        this.$message({
          message: '请填写纠错信息！',
          type: 'warning',
          offset: '90',
        });
      } else {
        this.isSubmitError = true;
        let info = this.$parent.queryInfo;
        setDebunk({
          personBookId: '',
          questionId: this.currentErrorQuesInfo.id,
          type: 1,
          state: 0,
          causeErrorId: this.checkList.join(','),
          errorDescription: this.textarea,
        })
          .then(data => {
            this.handleCloseDebunkDialog();
            this.$message({
              message: '纠错信息发送成功',
              type: 'success',
              offset: '90',
            });
            this.isSubmitError = false;
          })
          .catch(err => {
            this.isSubmitError = false;
            console.log(err);
          });
      }
    },
    // 设置错因
    setDebunk(item) {
      console.log('item-->', item);
      this.getAllCauseError();
      this.isSubmitError = false;
      this.debunkDialogVisible = true;
      this.currentErrorQuesInfo = item;
    },
  },
};
</script>

<style lang="scss" scoped>
.right-quesList-inner {
  // width: 100%;
  // height: 100%;
  margin: 10px;
  position: relative;

  .ques-pagination {
    text-align: center;
    margin: 0 auto;
    margin: 30px 0;
  }
}

.quesList-item {
  width: 100%;
  min-height: 122px;
  background: #fff;
  border: 1px solid #e8ebed;
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;

  .queList-content {
    padding: 10px;
    min-height: 80px;

    .option-box {
      .row-four {
        display: flex;
        flex-wrap: wrap;

        .flex-four {
          width: 20%;
          margin: 10px;

          img {
            width: 100%;
          }
        }
      }

      .row-two {
        display: flex;
        flex-wrap: wrap;

        .flex-two {
          width: 42%;
          margin: 10px;
        }
      }

      .option-item {
        display: flex;

        .option-item-letter {
          margin-right: 4px;
        }
      }
    }
  }

  .ques-detail-box {
    background: #f8f9fc;
    padding: 10px;
    border-top: 1px solid #eee;
    overflow-x: auto;

    .point-box,
    .answer-box,
    .analysis-box {
      display: flex;
    }
  }

  .queList-footer {
    width: 100%;
    height: 40px;
    background: #f8f9fc;
    border-radius: 0 0 6px 6px;
    padding: 0 20px 0 0;
    line-height: 40px;
    font-size: 16px;
    font-weight: 400;
    color: #525766;

    .difficult-star {
      font-size: 14px;
      margin-left: 30px;

      .star {
        width: 16px;
        height: 16px;
        background: url('../../assets/star.png') center center/100% 100% no-repeat;
        margin-left: 5px;

        &:first-child {
          margin-left: 8px;
        }
      }
    }

    .el-button {
      width: 80px;
      height: 30px;
      background: #3a84f9;
      border-radius: 4px;
      margin-left: 14px;
      float: right;
      color: #fff;
      cursor: pointer;
      margin-top: 5px;
      padding: 0;
      font-size: 16px;
      font-weight: 400;

      &.remove {
        border: 1px solid #3a84f9;
        background: #fff;
        color: #3a84f9;
      }
    }

    .edit-text {
      margin-left: 18px;
      float: right;
      cursor: pointer;

      &.disclose,
      &.error-correction,
      &.detail {
        position: relative;

        &:before {
          content: '';
          position: absolute;
          top: 11px;
          left: 0;
          background-image: url('../../assets/analyzeQuesIcons.png');
        }
      }

      &.disclose {
        padding-left: 15px;

        &:before {
          background-position: -34px -15px;
          width: 10px;
          height: 10px;
        }
      }

      &.error-correction {
        padding-left: 15px;

        &:before {
          background: url('../../assets/eraser.png') center center/100% 100% no-repeat;
          width: 16px;
          height: 16px;
          top: 12px;
          left: -2px;
        }
      }

      &.detail {
        padding-left: 15px;

        &:before {
          background-image: url('../../assets/analyzeQuesIcons.png');
          background-position: -31px 4px;
          width: 14px;
          height: 14px;
          left: -2px;
        }
      }

      &.similar {
        padding-left: 15px;
        position: relative;

        &:before {
          content: '';
          width: 16px;
          height: 16px;
          background: url('../../assets/icon/search.png') no-repeat;
          background-size: 100% 100%;
          position: absolute;
          left: -3px;
          top: 12px;
        }
      }
    }

    .edit-btn {
      width: 80px;
      height: 32px;
      border-radius: 4px;
      padding: 0;
      line-height: 32px;
      color: #fff;
      margin-top: 4px !important;
      margin-left: 20px;
      cursor: pointer;

      &.add {
        background: #409effff;
      }

      &.hasAdd {
        background-color: #fff;
        border: 1px solid #468fffff;
        color: #468fffff;
      }
    }
  }
}

.no-ques {
  padding-top: 30px;
  font-size: 16px;
  text-align: center;

  .bg {
    width: 352px;
    height: 250px;
    margin: 0 auto;
    background: url('../../assets/no-res.png') no-repeat;
    background-size: 100% 100%;
  }
}

.edit-ques-dlg {
  position: fixed;
  top: 0;
  left: 0;
  background: rgb(0, 0, 0, 0.6);
  overflow-y: scroll;
  height: 100%;
  width: 100%;
  /* text-align: center; */
  z-index: 20;
  padding: 5vh 0;

  .edit-ques-mark {
    width: 100%;
    height: 100%;
  }

  .backtop_btn {
    width: 24px;
    height: 24px;

    .icon-toup {
      width: 24px;
      height: 24px;
      background: url(../../assets/icon/toup.png) no-repeat;
    }
  }

  .edit-ques-body {
    width: 80%;
    margin: auto;
    /* height: 80%; */
    background: white;
    border-radius: 2px;
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 20px;
  }

  .edit-ques-btns {
    text-align: center;

    .el-button {
      width: 100px;
    }
  }
}
</style>

<style lang="scss" scoped>
.error-item {
  margin-bottom: 10px;
  display: flex;
  align-items: center;

  > span {
    width: 65px;
    margin-right: 10px;
  }

  label.el-checkbox {
    margin-right: 10px;

    .el-checkbox__label {
      padding-left: 5px;
    }
  }
}

.debunkDialog {
  .el-checkbox + .el-checkbox {
    margin-left: 10px;
  }
}

.queList-content {
  overflow-x: auto;
  font-size: 14px;
  color: #515a6e;

  q ol {
    list-style: none;
    margin: 20px 0 20px 0px;
  }

  div > ol {
    list-style: none;
    margin: 20px 0 20px 0px;
  }

  .intermediate-q {
    margin-bottom: 0;
    margin-left: 25px;
  }
}
</style>

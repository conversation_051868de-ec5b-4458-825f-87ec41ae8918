/*
 * @Description:
 * @Author: 小圆
 * @Date: 2024-11-20 14:13:42
 * @LastEditors: 小圆
 */
import { router } from './syRouter';
import NProgress from 'nprogress';
import { clearHttpRequestingList } from '@/utils';
import { sessionSave } from '../utils';
import { loginByUserId } from '@/utils/login';
import { getPlatFormInfoByUserIdAPI } from '@/service/api';

export const routerIntercept = () => {
  router.beforeEach(async (to, from, next) => {
    document.title = '考阅系统';
    clearHttpRequestingList();
    NProgress.start();
    try {
      await login({
        userId: to.query.userId || to.query.userid,
        token: to.query.token,
      });
    } catch (error) {
      next('/noLogin');
      return;
    }

    next();
    NProgress.done();
  });
};

// 登录
let isHasToken = false;
async function login({ userId, token }) {
  let loginInfo: any = sessionSave.get('loginInfo');
  let isHasLoginInfo = loginInfo && String(loginInfo.account_type) !== 'undefined';
  let isHasRegionInfo = sessionSave.get('regionInfo');
  let isSameUser = userId && isHasLoginInfo ? userId == loginInfo.id : true;

  if (isHasLoginInfo && isSameUser && isHasRegionInfo && isHasToken) {
    return true;
  } else if (userId) {
    await loginByUserId(userId);
    await getRegionInfo();
    isHasToken = true;
  }
}

/**
 * 检查用户区域信息
 */
async function getRegionInfo() {
  let loginInfo: any = sessionSave.get('loginInfo');
  const adminType = loginInfo.admin_type;
  const userType = loginInfo.user_type;
  const userId = loginInfo.id;
  const schoolId = loginInfo.schoolid;
  // 区域用户
  if (userType == 6) {
    let id = adminType == 4 ? userId : schoolId ? schoolId : ''; // 区管取用户id，非区管取学校id
    if (id) {
      let { data } = await getPlatFormInfoByUserIdAPI({ userId: id });
      sessionSave.set('regionInfo', data);
    }
    return true;
  }
  return false;
}

<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-03-18 15:08:06
 * @LastEditors: 小圆
-->
<template>
  <el-menu-item-group v-if="item.group && item.children && item.children.length > 0">
    <template slot="title"
      ><span :title="item.title">{{ item.title }}</span></template
    >
    <base-menu-item
      v-for="(data, i) in item.children"
      :key="i"
      :item="data"
      :level="level + 1"
      :baseRoute="baseRoute + '/' + item.path"
    ></base-menu-item>
  </el-menu-item-group>

  <el-submenu
    v-else-if="item.children && item.children.length > 0"
    class="base-menu-submenu"
    :index="baseRoute + '/' + item.path"
    :level="level + 1"
  >
    <template slot="title">
      <span :title="item.title" :style="{ paddingLeft: paddingLeft }">{{ item.title }}</span>
    </template>
    <base-menu-item
      v-for="(data, i) in item.children"
      :key="i"
      :item="data"
      :level="level + 1"
      :baseRoute="baseRoute + '/' + item.path"
    ></base-menu-item>
  </el-submenu>

  <el-menu-item
    v-else
    :index="baseRoute + '/' + item.path"
    :disabled="item.disabled"
    :level="level + 1"
    class="base-menu-item"
  >
    <template slot="title">
      <span :title="item.title" :style="{ paddingLeft: paddingLeft }">{{ item.title }}</span>
    </template>
  </el-menu-item>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { IMenuConfig } from './type';

@Component({
  name: 'BaseMenuItem',
})
export default class BaseMenuItem extends Vue {
  @Prop({ type: Object }) item!: IMenuConfig;
  @Prop({ type: String, default: '' }) baseRoute!: string;
  @Prop({ type: Number, default: 0 }) level!: number;

  get paddingLeft() {
    return 13 * (this.level + 1) + 'px';
  }
}
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>

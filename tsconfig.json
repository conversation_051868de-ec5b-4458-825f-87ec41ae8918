{"compilerOptions": {"outDir": "dist", "target": "es6", "module": "ESNext", "strict": false, "jsx": "preserve", "importHelpers": true, "moduleResolution": "Node", "experimentalDecorators": true, "esModuleInterop": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "allowJs": true, "checkJs": false, "downlevelIteration": true, "sourceMap": true, "types": ["webpack-env"], "baseUrl": "./", "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "paths": {"@/*": ["src/*"], "element-ui": ["node_modules/@iclass/element-ui"]}}, "exclude": ["node_modules", "dist/", "public/"]}
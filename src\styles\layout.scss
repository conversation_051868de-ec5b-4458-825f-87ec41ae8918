@import '_config.scss';
@import 'animate/animate';
@import 'element_theme';
@import 'components/dialog';
@import 'components/messages';
@import 'transition.scss';

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

body {
  font-family: $fontFamily;
}

[app-drag='true'] {
  -webkit-app-region: drag;
  pointer-events: none;

  &:after {
    content: '';
  }
}

[app-drag='none'] {
  -webkit-app-region: no-drag;
  pointer-events: auto;
}

iframe {
  @extend [app-drag='none'];
}

.list-none {
  margin: 0 auto;
  padding: 0;
  list-style: none;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.pull-left {
  float: left;
}

.pull-right {
  float: right;
}

.over-hide {
  overflow: hidden !important;
}

.dis-table {
  display: table;

  &.ver-mid {
    .dis-tab-cell {
      vertical-align: middle;
    }
  }

  &.ver-top {
    .dis-tab-cell {
      vertical-align: top;
    }
  }

  &.ver-bot {
    .dis-tab-cell {
      vertical-align: bottom;
    }
  }
}

.dis-inline {
  display: inline-block;
}

.dis-block {
  display: block;
}

.dis-none {
  display: none;
}

// 垂直居中辅助
.middle-helper {

  >*,
  &:before {
    display: inline-block;
    vertical-align: middle;
  }

  &:before {
    width: 0;
    height: 100%;
    content: '';
  }
}

// 居中
.position-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 宽高100%
.wh100 {
  width: 100%;
  height: 100%;
}

// padding margin置0
.pm0 {
  padding: 0;
  margin: 0;
}

.ver-mid {
  vertical-align: middle;
}

.ver-top {
  vertical-align: top;
}

.no-wrap {
  white-space: nowrap;
}

.dis-tab-header-group {
  display: table-header-group;
}

.dis-tab-group {
  display: table-row-group;
}

.dis-tab-row {
  display: table-row;
}

.dis-tab-cell {
  display: table-cell;
}

// 点击元素，添加点击透明效果
.click-element {
  user-select: none;
  cursor: pointer;

  &:active {
    opacity: 0.6;
  }

  &.active {
    cursor: default;

    &:active {
      opacity: 1;
    }
  }
}

/* 放开禁止鼠标选中文本 */
.noselect {
  -webkit-touch-callout: text;
  /* iOS Safari */
  -webkit-user-select: text;
  /* Chrome/Safari/Opera */
  -khtml-user-select: text;
  /* Konqueror */
  -moz-user-select: text;
  /* Firefox */
  -ms-user-select: text;
  /* Internet Explorer/Edge */
  user-select: text;
}

/* 点击穿透 */
.pointer-none {
  pointer-events: none !important;
}

.pointer-auto {
  pointer-events: auto !important;
}

.clearfix {

  &:before,
  &:after {
    display: table;
    content: ' ';
  }

  &:after {
    clear: both;
  }
}

.pos-relative {
  position: relative;
}

.layout-line {
  background-color: #ccc;
  border: none;
  height: 1px;
}

.el-popup-parent--hidden {
  padding-right: 0 !important;
}

// 不显示触屏高亮
.tap-highlight-none {
  -webkit-tap-highlight-color: transparent;
}

// 全尺寸，宽高100%
.full-size {
  width: 100%;
  height: 100%;
}

// 文字垂直排列
.vertical-text {
  -webkit-writing-mode: vertical-lr;
  writing-mode: vertical-lr;
}

// 不可点击样式
.no-click {
  opacity: 0.5;
  cursor: not-allowed;
}

.font-bold {
  font-weight: bold !important;
}

button {
  font-family: $fontFamily;
}

// css三角箭头
.caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 2px;
  vertical-align: middle;
  border-top: 4px dashed;
  border-top: 4px solid\9;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;

  &.caret-up {
    content: '';
    border-top: 0;
    border-bottom: 4px dashed;
    border-bottom: 4px solid\9;
  }
}

// 数字输入框
input[type='number'] {

  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }

  -moz-appearance: textfield;
}

// 取消抗锯齿
.anti-aliasing-none {
  image-rendering: optimizeSpeed; // Older versions of FF
  image-rendering: -moz-crisp-edges; // FF 6.0+
  image-rendering: -webkit-optimize-contrast; // Webkit (non standard naming)
  image-rendering: -o-crisp-edges; // OS X & Windows Opera (12.02+)
  image-rendering: crisp-edges; // Possible future browsers.
  -ms-interpolation-mode: nearest-neighbor; // IE (non standard naming)
}

/*display*/
.display_flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}

//.display_flex > *{
//    display: block;
//}
.display_inline-flex {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flex;
}

.display_inline-flex>* {
  display: block;
}

.flex-wrap_wrap {
  -webkit-box-orient: vertical;
  -ms-flex-wrap: column;
  -webkit-flex-wrap: column;
  flex-wrap: wrap;
}

.flex-shrink_0 {
  -ms-flex-shrink: 0;
  -webkit-flex-shrink: 0;
  flex-shrink: 0;
}

/*伸缩流方向*/
.flex-direction_column {
  -webkit-box-orient: vertical;
  -ms-flex-direction: column;
  -webkit-flex-direction: column;
  flex-direction: column;
}

/*主轴对齐*/
.justify-content_flex-center {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.justify-content_flex-end {
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
}

.justify-content_space-between {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.justify-content_flex-around {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  -webkit-justify-content: space-around;
  justify-content: space-around;
}

.justify-content_space-evenly {
  -webkit-box-pack: space-evenly;
  -ms-flex-pack: space-evenly;
  -webkit-justify-content: space-evenly;
  justify-content: space-evenly;
}

/*侧轴对齐*/
.align-items_flex-start {
  -webkit-box-align: start;
  -ms-flex-align: start;
  -webkit-align-items: flex-start;
  align-items: flex-start;
}

.align-items_flex-end {
  -webkit-box-align: end;
  -ms-flex-align: end;
  -webkit-align-items: flex-end;
  align-items: flex-end;
}

.align-items_center {
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
}

.align-items_baseline {
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  -webkit-align-items: baseline;
  align-items: baseline;
}

/*伸缩性*/
.flex_auto {
  -webkit-box-flex: 1;
  -ms-flex: auto;
  -webkit-flex: auto;
  flex: auto;
}

.flex_1 {
  width: 0;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  -webkit-flex: 1;
  flex: 1;
}

/*显示顺序*/
.order_2 {
  -webkit-box-ordinal-group: 2;
  -ms-flex-order: 2;
  -webkit-order: 2;
  order: 2;
}

.order_3 {
  -webkit-box-ordinal-group: 3;
  -ms-flex-order: 3;
  -webkit-order: 3;
  order: 3;
}

// 微软雅黑字体的粗体字 - Start
//@font-face {
//    font-family : 'Microsoft YaHei Bold';
//    src         : url("fonts/Standard Script Italic.ttf") format("opentype");
//    font-weight : normal;
//    font-style  : normal;
//}
//
//.yahei-bold {
//    font-family : "Microsoft YaHei Bold";
//}

// 微软雅黑字体的粗体字 - End

// el-table中滚动条width和height需要一致,否则scrollbar-width.js计算错误
$scrollSize: 9px;

.el-table--scrollable-x .el-table__body-wrapper,
.el-table--scrollable-y .el-table__body-wrapper {
  &::-webkit-scrollbar-track-piece {
    background: #f5f7fa;
  }

  &::-webkit-scrollbar-thumb {
    // background: #d3dce6;
    min-width: $scrollSize;
    min-height: $scrollSize;
    width: $scrollSize;
    height: $scrollSize;
    border-radius: $scrollSize / 2;
  }

  &::-webkit-scrollbar {
    width: $scrollSize;
    height: $scrollSize;
  }
}

.el-scrollbar__wrap {
  &::-webkit-scrollbar-track-piece {
    background: #f5f7fa;
  }

  &::-webkit-scrollbar-thumb {
    // background: #d3dce6;
    min-width: $scrollSize;
    min-height: $scrollSize;
    width: $scrollSize;
    height: $scrollSize;
    border-radius: $scrollSize / 2;
  }

  &::-webkit-scrollbar {
    width: $scrollSize;
    height: $scrollSize;
  }
}

.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.text-ellipsis-two {
  display: -webkit-box;
  overflow: hidden;
  /*! autoprefixer: ignore next */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-height: normal;
}

.text-ellipsis-three {
  display: -webkit-box;
  overflow: hidden;
  /*! autoprefixer: ignore next */
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
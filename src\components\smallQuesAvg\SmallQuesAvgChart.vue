<template>
  <div>
    <div v-show="!showDefault" id="smallQuesAvgChart" style="width: 100%; height: 400px"></div>
    <div v-show="showDefault">
      <div style="text-align: center"><img style="width: 350px" :src="noResImg" alt="" /></div>
      <p style="text-align: center; font-size: 16px; margin-top: 10px">暂无数据!</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SmallQuesAvgChart',
  props: ['tableData', 'currentQuesNumber', 'currentTQuesNo', 'clzList'],
  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      showDefault: true,
      smallQuesAvgChart: null,
      classNames: [],
      classAvgList: [],
      upperAvgList: [],
      lowerAvgList: [],
      gradeAvgList: [],
    };
  },
  mounted() {
    let _this = this;
    window.onresize = () => {
      return (() => {
        if (_this.smallQuesAvgChart) {
          this.resetDomSize('smallQuesAvgChart', 400);
          _this.smallQuesAvgChart.resize();
        }
      })();
    };
    if (!this.smallQuesAvgChart) {
      this.drawImg();
    }
  },
  beforeDestroy() {
    if (
      this.smallQuesAvgChart != null &&
      this.smallQuesAvgChart != '' &&
      this.smallQuesAvgChart != undefined
    ) {
      this.smallQuesAvgChart.dispose();
      this.smallQuesAvgChart = null;
    }
  },
  watch: {
    tableData: {
      handler(newVal) {
        if (newVal.length) {
          this.showDefault = false;
        }
        this.$nextTick(() => {
          this.drawImg();
        });
      },
      deep: true,
    },
    currentTQuesNo() {
      this.$nextTick(() => {
        this.drawImg();
      });
    },
  },
  methods: {
    // handleChartData() {
    // let data = this.tableData && this.tableData.length ? this.tableData : [];
    // let grdData = {};
    // if (data.length) {
    //   if (!this.tableData[0].questionList) {
    //     return;
    //   }
    //   grdData = this.tableData[0].questionList.filter(
    //     (item) => item.quesNumber == this.currentQuesNumber
    //   )[0];
    //   data = this.tableData.slice(1);
    // } else {
    //   this.showDefault = true;
    // }
    // this.classNames = data.map((item) => item.classId);
    // let subData = data.map((item) => {
    //   let obj = item.questionList.filter((it) => it.quesNumber == this.currentQuesNumber)[0];
    //   return obj;
    // });
    // this.classAvgList = subData.map((item) => {
    //   if (item.averageSubtraction <= 0) {
    //     return item.average;
    //   } else {
    //     return (item.average - Math.abs(item.averageSubtraction)).toFixed(2);
    //   }
    // });
    // this.upperAvgList = subData.map((item) => {
    //   if (item.averageSubtraction <= 0) {
    //     return 0;
    //   }
    //   return item.averageSubtraction;
    // });
    // this.lowerAvgList = subData.map((item) => {
    //   if (item.averageSubtraction >= 0) {
    //     return 0;
    //   }
    //   return Math.abs(item.averageSubtraction);
    // });
    // this.gradeAvgList = data.map((item) => {
    //   return grdData.grdAvgScore;
    // });
    // },
    async drawImg() {
      if (
        this.smallQuesAvgChart != null &&
        this.smallQuesAvgChart != '' &&
        this.smallQuesAvgChart != undefined
      ) {
        this.smallQuesAvgChart.dispose();
        this.smallQuesAvgChart = null;
      }
      this.showDefault = true;
      const currentQues = this.tableData.find(item => item.tQuesNo === this.currentTQuesNo);
      const clzList = this.$deepClone(this.clzList).filter(item => item.id !== '');

      if (!clzList.length) return;
      if (!currentQues) return;
      this.showDefault = false;
      await this.$nextTick();

      const classNames = clzList.map(item => item.name);
      const classAvgList = clzList.map(item => {
        const avg = currentQues.data[item.id].avg;
        const grdAvg = currentQues.data[''].avg;
        let averageSubtraction = avg - grdAvg;
        if (averageSubtraction <= 0) {
          return avg;
        } else {
          return (avg - Math.abs(averageSubtraction)).toFixed(2);
        }
      });

      const upperAvgList = clzList.map(item => {
        const avg = currentQues.data[item.id].avg;
        const grdAvg = currentQues.data[''].avg;

        if (avg - grdAvg <= 0) {
          return 0;
        }
        return ((avg - grdAvg) * 1).toFixed(2);
      });

      const lowerAvgList = clzList.map(item => {
        const avg = currentQues.data[item.id].avg;
        const grdAvg = currentQues.data[''].avg;

        if (avg - grdAvg >= 0) {
          return 0;
        }
        return (Math.abs(avg - grdAvg) * 1).toFixed(2);
      });

      const gradeAvgList = clzList.map(item => {
        return currentQues.data[''].avg;
      });

      let _this = this;
      // 基于准备好的dom，初始化echarts实例
      this.smallQuesAvgChart = this.$echarts.init(document.getElementById('smallQuesAvgChart'));
      // 绘制图表
      this.smallQuesAvgChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'none', // 默认为直线，可选为：'line' | 'shadow'
          },
          // formatter: params => {
          // console.log(params);
          // let obj = _this.tableData.filter(item => {
          // return params[0].name == item.classId;
          // });
          // if (!obj.length) {
          // return '';
          // }
          // console.log(obj)
          // let avg = obj[0].questionList.filter(it => it.quesNumber == _this.currentQuesNumber);
          // return `${obj[0].classId}: ${avg[0].average}`;
          // },
        },
        color: ['#B3DBFA', '#409EFF', '#83AEDA', '#FBA110'],
        legend: {
          icon: 'circle',
          top: 10,
          right: 70,
          textStyle: {
            color: '#757C8C',
            fontSize: '14',
          },
          data: ['班级平均分', '高于年级平均分', '低于年级平均分', '年级平均分'],
        },
        grid: {
          left: '3%',
          right: '8%',
          bottom: '12%',
          containLabel: true,
        },
        yAxis: {
          type: 'value',
          name: '均分',
        },
        xAxis: {
          type: 'category',
          name: '班级',
          axisLine: {
            lineStyle: {
              color: '#757C8C',
            },
          },
          data: classNames,
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: (6 / classNames.length) * 100,
          },
        ],
        series: [
          {
            name: '班级平均分',
            type: 'bar',
            stack: '总量',
            label: {
              show: false,
            },
            z: 2,
            data: classAvgList,
          },
          {
            name: '高于年级平均分',
            type: 'bar',
            stack: '总量',
            label: {
              // show: false,
              show: true,
              position: 'top',
              textStyle: {
                color: '#757C8C',
                fontSize: 14,
              },
              fontWeight: 'bold',
              formatter: params => {
                // console.log(params)
                if (!params.value) {
                  return '';
                }
                return '+' + params.value;
              },
            },
            z: 2,
            data: upperAvgList,
          },
          {
            name: '低于年级平均分',
            type: 'bar',
            stack: '总量',
            label: {
              // show: false,
              show: true,
              position: 'top',
              textStyle: {
                color: '#757C8C',
                fontSize: 14,
              },
              fontWeight: 'bold',
              formatter: params => {
                // console.log(params)
                if (!params.value) {
                  return '';
                }
                return '-' + params.value;
              },
            },
            z: 2,
            data: lowerAvgList,
          },
          {
            name: '年级平均分',
            type: 'line',
            data: gradeAvgList,
            itemStyle: {
              opacity: 0,
            },
            lineStyle: {
              opacity: 0,
            },
            markLine: {
              data: [
                {
                  type: 'average',
                  name: '均分',
                },
              ],
              label: {
                formatter: '均分:{c}',
              },
            },
          },
        ],
      });
    },
  },
};
</script>

<style scoped></style>

/*
 * @Description: el-table二次封装
 * @Author: 小圆
 * @Date: 2024-03-16 08:55:30
 * @LastEditors: 小圆
 */

import BaseTableColumn from './BaseTableColumn';
export default {
  name: 'BaseTable',
  props: {
    column: Array,
    data: Array,
    spanMethod: Function,
    pagination: {
      type: Boolean,
      default: false,
    },
    paginationTop: {
      type: String,
      default: '15px',
    },
    paginationAlign: {
      type: String,
      default: 'right',
    },
    merge: Array,
  },
  components: {
    BaseTableColumn,
  },
  data() {
    return {
      mergeLine: {},
      mergeIndex: {},
    };
  },
  created() {
    this.getMergeArr(this.data, this.merge);
  },
  computed: {
    dataLength() {
      return this.data?.length || 0;
    },
    elTable() {
      return this.$refs.elTable;
    }
  },
  methods: {
    clearSelection() {
      this.$refs.elTable.clearSelection();
    },
    toggleRowSelection(row, selected) {
      this.$refs.elTable.toggleRowSelection(row, selected);
    },
    toggleAllSelection() {
      this.$refs.elTable.toggleAllSelection();
    },
    toggleRowExpansion(row, expanded) {
      this.$refs.elTable.toggleRowExpansion(row, expanded);
    },
    setCurrentRow(row) {
      this.$refs.elTable.setCurrentRow(row);
    },
    clearSort() {
      this.$refs.elTable.clearSort();
    },
    clearFilter(columnKey) {
      this.$refs.elTable.clearFilter(columnKey);
    },
    doLayout() {
      this.$refs.elTable.doLayout();
    },
    sort(prop, order) {
      this.$refs.elTable.sort(prop, order);
    },
    paginationCurrentChange(val) {
      this.$emit('p-current-change', val);
    },
    getMergeArr(tableData, merge) {
      if (!merge) return;
      this.mergeLine = {};
      this.mergeIndex = {};
      merge.forEach((item, k) => {
        tableData.forEach((data, i) => {
          if (i === 0) {
            this.mergeIndex[item] = this.mergeIndex[item] || [];
            this.mergeIndex[item].push(1);
            this.mergeLine[item] = 0;
          } else {
            if (data[item] === tableData[i - 1][item]) {
              this.mergeIndex[item][this.mergeLine[item]] += 1;
              this.mergeIndex[item].push(0);
            } else {
              this.mergeIndex[item].push(1);
              this.mergeLine[item] = i;
            }
          }
        });
      });
    },
    mergeMethod({ row, column, rowIndex, columnIndex }) {
      const index = this.merge.indexOf(column.property);
      if (index > -1) {
        const _row = this.mergeIndex[this.merge[index]][rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
  },
  watch: {
    merge() {
      this.getMergeArr(this.data, this.merge);
    },
    dataLength() {
      this.getMergeArr(this.data, this.merge);
    },
  },
  render(h) {
    const columns =
      this.column && this.column.length
        ? this.column.map((item, index) => (
            <base-table-column attrs={this.$attrs} key={index} column={item} />
          ))
        : [];

    return (
      <div class="base-table">
        <el-table
          ref="elTable"
          attrs={this.$attrs}
          on={this.$listeners}
          data={this.data}
          span-method={this.merge ? this.mergeMethod : this.spanMethod}
        >
          {columns}
        </el-table>
        {this.pagination && (
          <el-pagination
            class="base-table-pagination"
            attrs={this.$attrs}
            on={this.$listeners}
            on-current-change={this.paginationCurrentChange}
            style={{ 'margin-top': this.paginationTop, 'text-align': this.paginationAlign }}
          />
        )}
      </div>
    );
  },
};

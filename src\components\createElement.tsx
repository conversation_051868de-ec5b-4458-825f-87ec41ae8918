/*
 * @Descripttion: 
 * @Author: Doudou
 * @Date: 2020-08-03 10:52:48
 * @LastEditors: Doudou
 */
import Vue from 'vue'
import {v1 as uuid} from 'uuid';

export default function createElement(option: any) {
  var dom = document.createElement('div');
  if (option.appendToBody) {
    document.getElementsByTagName('body')[0].appendChild(dom);
  } else {
    document.getElementById(option.appendToId || 'body').appendChild(dom);
  }
  let cuuid = option.id || uuid();

  var vue = new Vue({
    el: dom,
    data: function () {
      return {
        elementData: option.data,
        change: option.change || null,
        isshow: true,
      };
    },
    store: option.store,
    render(h) {
      return h("elementContent", {
        ref: "elementContent",
        attrs: {
          id: cuuid,
          ...this.elementData
        },
        on: {
          change: this.change,
          hide: this.hide,
          remove: this.remove,
          show: this.show
        }
      })
    },
    methods: {
      show() {
        this.getElement().style.display = "block";
        this.isshow = true
        return this;
      },
      hide() {
        this.getElement().style.display = "none";
        this.isshow = false
        return this;
      },
      close(){
        this.remove();
      },
      remove() {
        this.$destroy(true);
        let eleBody = this.getElement();
        if (eleBody) eleBody.parentNode.removeChild(eleBody);
      },
      changeData(data) {
        this.elementData = data;
        return this;
      },
      getElement() {
        return document.getElementById(cuuid)
      }
    },
    components: {
      elementContent: option.component,
    },
  });
  return vue;
}
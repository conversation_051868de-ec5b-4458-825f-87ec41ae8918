/*
 * @Description:
 * @Author: 小圆
 * @Date: 2025-02-11 10:43:05
 * @LastEditors: 小圆
 */
import { Component, Prop, Vue } from 'vue-property-decorator';
import { GradeInfo, SchoolSettingEvent, SettingChangeParams } from '../types';

@Component
export default class SchoolSettingMixin extends Vue {
  /** 当前年级列表 */
  @Prop() currentGradeList!: GradeInfo[];
  /** 当前学段 */
  @Prop() currentPhase!: string | number;
  /** 当前年级 */
  @Prop() currentGradeId!: string;
  /** 当前年级信息 */
  @Prop() currentGradeItem!: GradeInfo;
  /** 考试ID */
  @Prop() examId!: string;

  created() {
    // 注册事件
    this.$bus.$on(SchoolSettingEvent.SettingChange, this.handleSettingChange);
  }

  beforeDestroy() {
    this.$bus.$off(SchoolSettingEvent.SettingChange, this.handleSettingChange);
  }

  // 注册监听设置变化
  handleSettingChange(data: SettingChangeParams) {
    this.onSettingChange && this.onSettingChange(data);
  }

  // 覆盖此方法
  onSettingChange(data: SettingChangeParams) {}

  // 获取基础数据学段
  getPhase() {
    return this.currentPhase;
  }

  // 获取考试（大精）学段
  getExamPhase() {
    if (this.currentPhase === '') {
      return '';
    }
    return Number(this.currentPhase) + 2;
  }
}

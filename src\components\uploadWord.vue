<template>
  <div>
    <dt-modal
      :modalVisible="formVisible"
      :title="'上传word'"
      :modalWidth="700"
      :sureBtnLoading="sureBtnLoading"
      @click-sure="addSubmit"
      @click-cancel="cancel"
    >
      <template slot="customSlot">
        <div class="quesCard-upload-div">
          <div class="upload-main">
            <div class="upload-main-top">
              <a
                class="download-paper"
                :href="`${fsUrl}/aliba/resources/testbank/template/Word试卷模板.docx?response-content-type=application%2Foctet-stream`"
                >下载word模板</a
              >
              <span>按模板上传，制作更智能、更快捷~</span>
            </div>
            <el-upload
              ref="uploadFile"
              :action="uploadUrl"
              :accept="coverAccept"
              :show-file-list="false"
              :on-success="uploadSuccess"
              :before-upload="beforeUpload"
              :http-request="ossUpload"
              :data="uploadData"
            >
              <div class="upload-main-search" style="position: relative">
                <span type="button" id="upload-file" style="z-index: 1"
                  >选择文件
                  <label class="file-name">
                    <p class="limit-info" v-if="formItem.filePath == ''">
                      请选择100M以内的文档上传（doc、docx）
                    </p>
                    <p class="limit-info" v-else>{{ formItem.tbName }}</p>
                  </label>
                </span>
              </div>
            </el-upload>
          </div>
        </div>
      </template>
    </dt-modal>
  </div>
</template>

<script>
import { saveTestBanks, delXBResource } from "../service/api";
import { selectAllType } from "../service/pbook";
import { saveWordFile } from "../service/pexam";
import { mapState } from "vuex";
import DtModal from "../ui/dt-modal";
import ossUploadFile from '@/utils/ossUploadFile';
import { guid,get_suffix } from '@/utils/index';

export default {
  name: "upload-word",
  components: {
    DtModal,
  },
  props: [
    "formVisible",
    "formDataParam",
    "editParamsData",
    "isUploadAgain",
    "subjectInfo",
  ],
  data() {
    return {
      sureBtnLoading: false,
      fsUrl: process.env.VUE_APP_FS_URL,
      formItem: {
        tbId: "", //wordId
        tbName: "", //word名称
        gCode: "", //年级编码
        gName: "", //年级名称
        sCode: "", //学科编码
        sName: "", //学科名称
        year: "", //年份
        phase: "", //学段
        typeCode: "", //word类型编码
        typeName: "", //word类型名称
        shareType: 0, //分享类别(0:不分享 1:分享到校本)
        bookCode: "", //word所挂书本code
        catalogCode: "", //word所挂章节目录code
        catalogName: "", //word所挂章节目录名称
        fileName: "", //文件名称
        filePath: "", //文件路径
        fileSize: 0, //文件大小
      },
      coverAccept: '.doc,.docx',
      // coverAccept: "application/msword",
      uploadUrl: "", //word上传地址
      uploadData: {}, //word上传所附带的参数
      filePath:'aliba/resources/testbank',
      typeList: [
        {
          id: "101",
          name: "练习",
        },
        {
          id: "1",
          name: "单元测试",
        },
      ],
      showDlg: false,
      selectTypeCode: 1,
      testBankInfo: null,
    };
  },
  watch: {
    // 监测改变
    // formVisible: "initdata",
  },
  computed: {
    ...mapState(["loginInfo", "schoolInfo"]),
  },
  created() {
    let isPdfParse = this.$sessionSave.get('tbPdfParse')  == "1";
    if(isPdfParse){
      this.coverAccept ='.doc,.docx,.pdf';
    }
    this.initdata()
  },
  methods: {
    handleClose() {
      this.$emit("closeDialog");
    },
    /**
     * 监听窗口是否打开
     */
    initdata() {
      let subjectList = this.$localSave.get("SUBJECT_LIST");
      let subjectName = subjectList.filter((item) => {
        return item.id == this.subjectInfo?.subectId;
      })[0]?.name;
        this.formItem.gCode = this.formDataParam.gradeCode;
        this.formItem.gName = this.formDataParam.gradeName;
        this.formItem.sCode = this.subjectInfo.subectId;
        this.formItem.sName = subjectName;
        this.formItem.year = new Date().getFullYear();
        this.formItem.schoolId = this.schoolInfo.id;
        this.formItem.userId = this.loginInfo.id;
        this.formItem.userName = this.loginInfo.realname;
        this.formItem.phase = this.formDataParam.phase || "";
        // this.formItem.phase = parseInt(this.formDataParam.phaseId) - 2
        this.testBankInfo = null;

        this.getTypeList();
    },
    /**
     * 获取类别下拉框
     */
    getTypeList() {
      let _this = this;
      let params = {};
      selectAllType(params)
        .then(function (result) {
          if (result && parseInt(result.code) === 1) {
            _this.typeList = result.data;
            _this.typeList.forEach((item) => {
              if (item.name === _this.formDataParam.categoryName) {
                _this.selectTypeCode = item.id;
              }
            });

            if (!_this.selectTypeCode) {
              _this.selectTypeCode = _this.typeList[0].id;
            }
          }
        })
        .catch(function (error) {
        });
    },
    /**
     * word上传前
     */
     async beforeUpload(file) {
      let promise = new Promise(async (resolve, reject) => {
        if(file.size > 100*1024*1024 ){
          this.$message.error('文件大小不能超过100MB');
          reject()
          return;
        }
        if(!this.coverAccept.split(',').includes(get_suffix(file.name).toLocaleLowerCase())){
          this.$message.error('文件格式不支持，请选择'+this.coverAccept+'格式的文档');
          reject()
          return;
        }
        let path = this.getDateForPath();
        await ossUploadFile.getSTSToken(path);
        resolve(true);
        });
      return promise; // 通过返回一个promis对象解决
    },
    ossUpload(data){
      return new Promise((resolve, reject) => {
        let path = this.getDateForPath() + guid() + '/f' + get_suffix(data.file.name);
        ossUploadFile.uploadFile(data.file, path, (res) => {
          if (res.code == 1) {
            resolve(res.res)
          } else {
            this.$message.error('上传失败')
            reject(res)
          }
        })
      });
    },
    /**
     * 上传成功回调
     */
    uploadSuccess(response, file, fileList) {
      this.formItem.filePath = "/" + response.name;
      this.formItem.tbName = file.name.substring(0, file.name.lastIndexOf("."));
      this.formItem.fileName = file.name;
      this.formItem.fileSize = file.size;
    },
    /**
     * 根据当前时间当前用户的学校id和用户id拼接文件夹路径
     * @returns {string}
     */
     getDateForPath() {
      let date = new Date()
      let y = date.getFullYear()
      let m = date.getMonth() + 1
      m = m < 10 ? ('0' + m) : m
      let d = date.getDate()
      d = d < 10 ? ('0' + d) : d
      if (this.loginInfo.schoolid) {
        // 当用户学校id不为空时
        return this.filePath + '/' + y + '/' + m + '/' + d + '/' + this.loginInfo.schoolid + '/' + this.loginInfo.id + '/';
      } else {
        // 当用户学校id为空时
        return this.filePath + '/' + y + '/' + m + '/' + d + '/' + this.loginInfo.id + '/';
      }
    },
    /**
     * word文件太大的提示
     */
    handleMaxSize(file) {
      this.$Message.error("文件" + file.name + "太大，不允许超过100M");
    },
    /**
     * 上传的文件格式验证失败的提示
     */
    handleFormat(file) {
      this.$Message.error(
        "文件" + file.name + "格式不支持，请选择doc、docx格式的文档"
      );
    },
    /**
     * 确定保存
     */
    addSubmit() {
      if (this.testBankInfo) {
        this.bindBook(this.testBankInfo);
      }
      let _this = this;
      if (!this.editParamsData.id) {
        if (!this.formItem.filePath) {
          this.$Message.error("请上传word文档");
          return;
        }
      } else {
        this.formItem.tbId = this.editParamsData.id;
        this.formItem.tbName = this.editParamsData.tbName || this.formItem.tbName;
      }
      //战哥要求补录全部增加参数reUpload 巻卡分离导致的补录后originCardType变化问题
      this.formItem.reUpload = true;

      // if (!this.selectTypeCode) {
      //     this.$Message.error("请选择所属类别")
      //     return
      // }
      this.sureBtnLoading = true;
      // this.formItem.typeName = this.typeList.find(it => it.id === this.selectTypeCode).name
      this.formItem.typeCode = this.formItem.phase + "0" + this.selectTypeCode;

      // 根据typeCode 查找类别标题
      let curType = this.typeList.filter((item) => {
        return this.selectTypeCode === item.id;
      })[0];
      if (curType) {
        this.formItem.typeName = curType.name;
      }

      let para = Object.assign({}, _this.formItem);

      _this.toSaveInfo(para);
    },
    async toSaveInfo(para) {
      let _this = this;
      //试卷转成公式
      para.isConvertFormula = 1;
      para.shareType = 1;
      para.source = 3;
      para.cardType = 2;
      let code = "";
      if (this.isUploadAgain == "again") {
        let res = await delXBResource({
          schoolId: para.schoolId || "",
          id: this.formDataParam.testBankId || "",
        });
        code = res.code || "";
        // if(code != 1) {
        //   return
        // }
      }
      console.log("code--->", code);
      saveTestBanks(para)
        .then(function (result) {
          if (result && parseInt(result.code) === 1) {
            if (!_this.editParamsData.id) {
              _this.testBankInfo = result.data;
              //去绑定个册
              _this.bindBook(result.data);
            } else {
              _this.$Message.success("修改成功");
              _this.cancel();
            }
          } else {
            _this.$message.error(result.msg);
            _this.showLoad();
          }
        })
        .catch(function (error) {
          _this.showLoad();
        });
    },
    // 绑定个册
    bindBook(data) {
      let _this = this;
      let schoolName =
        this.$sessionSave.get("schoolInfo").schoolName ||
        this.$sessionSave.get("schoolInfo").school_name;
      let params = {
        examId: this.formDataParam.examId,
        subjectId: this.subjectInfo.subectId,
        fileId: data.fileId,
        tbId: data.tbInfo.tbId,
        tbTime: data.tbInfo.tbTime,
        fileSize: this.formItem.fileSize,
        schoolId: this.$sessionSave.get("schoolInfo").id,
        schoolName: schoolName,
        relateCardType: this.subjectInfo.relateCardType || 0,
        abCardSheetType: this.subjectInfo.abCardSheetType || 0,
      };
      saveWordFile(params)
        .then((data) => {
          _this.$message({
            type: "success",
            message: "提交成功！",
            duration: "2000",
          });
          _this.$emit("refresh-table");
          _this.cancel();
        })
        .catch(function (error) {
          _this.showLoad();
        });
    },
    // 表单提交时报错后不显示提交按钮load
    showLoad() {
      let _this = this;
      setTimeout(function () {
        _this.sureBtnLoading = true;
        _this.$nextTick(() => {
          _this.sureBtnLoading = false;
        });
      }, 100);
    },
    /**
     * 取消
     */
    cancel() {
      this.$refs.formItem && this.$refs.formItem.resetFields();
      this.formItem.tbId = "";
      this.formItem.tbName = "";
      this.formItem.gCode = "";
      this.formItem.gName = "";
      this.formItem.sCode = "";
      this.formItem.sName = "";
      this.formItem.year = "";
      this.formItem.phase = "";
      this.formItem.bookCode = "";
      this.formItem.catalogCode = "";
      this.formItem.catalogName = "";
      this.formItem.typeCode = "";
      this.formItem.typeName = "";
      this.formItem.fileName = "";
      this.formItem.filePath = "";
      this.formItem.fileSize = "";
      this.testBankInfo = null;

      this.sureBtnLoading = false;

      this.$refs.uploadFile.clearFiles();
      //关闭弹窗
      this.$emit("closeDialog");
    },
  },
};
</script>

<style lang="scss" scoped></style>
<style lang="css" scoped>
/*@import "../../../library/css/base.css";*/
@import "../styles/questionCard/upload.scss";
</style>
<style lang="scss">
.upload-dialog .el-dialog__body {
  padding: 0 20px 50px;
}

.quesCard-upload-div .upload-main-search {
  // display: unset !important;
}

.quesCard-upload-div .el-upload {
  margin-bottom: 10px !important;
  display: block;
}

.quesCard-upload-div .ivu-form-item {
  margin-bottom: 10px !important;
}

.quesCard-upload-div .ivu-form-item-content {
  line-height: 34px !important;
  font-size: 16px !important;
}

.quesCard-upload-div .ivu-select-selected-value {
  font-size: 16px !important;
}

.quesCard-upload-div .ivu-select-item {
  font-size: 16px !important;
}
</style>

<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-11-13 09:42:57
 * @LastEditors: 小圆
-->
<template>
  <div>
    <div class="header__select" v-if="isSyRegion">
      <span class="select__label">学校：</span>
      <el-select
        style="width: 160px"
        ref="select"
        v-model="schoolId"
        class="select"
        @change="onChangeSchool"
        placeholder="请选择"
      >
        <el-option v-for="item in schoolList" :key="item.id" :label="item.schoolName" :value="item.id"> </el-option>
      </el-select>
    </div>

    <!-- 学年、入学年份 -->
    <div class="header__select" v-if="showYearType">
      <el-select v-model="yearType" style="width: 140px" @change="onChangeYearType">
        <el-option :label="'按学年查'" :value="'acadYearsId'"> </el-option>
        <el-option :label="'按入学年份查'" :value="'year'"> </el-option>
      </el-select>

      <el-select
        v-if="yearType == 'acadYearsId'"
        ref="select"
        v-model="examQuery.acadYearsId"
        style="width: 160px; margin-right: 10px"
        @change="onChangeAcadYearsId"
      >
        <el-option
          v-for="item in acadYearsIdList"
          :key="item.schoolYearId"
          :label="item.schoolYear"
          :value="item.schoolYearId"
        >
        </el-option>
      </el-select>
      <el-select
        v-if="yearType == 'year'"
        ref="select"
        v-model="examQuery.year"
        style="width: 160px; margin-right: 10px"
        @change="onChangeYear"
      >
        <el-option v-for="item in yearList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
      </el-select>
    </div>
    <!-- 年级筛选 -->
    <div class="header__select" v-if="showGrade && yearType == 'acadYearsId'">
      <span class="select__label">年级：</span>
      <el-select
        ref="select"
        v-model="currectGrade"
        class="select"
        placeholder="请选择"
        value-key="key"
        @change="onChangeGrade"
      >
        <el-option v-for="(item, index) in gradeList" :key="index" :label="item.name" :value="item"> </el-option>
      </el-select>
    </div>
    <!-- 学科筛选 -->
    <div class="header__select" v-if="showSubject">
      <span class="select__label">学科：</span>
      <el-select
        ref="select"
        v-model="examQuery.subjectId"
        class="select"
        @change="onChangeSubject"
        placeholder="请选择"
      >
        <el-option v-for="item in subjectList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
      </el-select>
    </div>

    <!-- 班级 -->
    <div class="header__select" v-if="showClass">
      <span class="select__label">班级：</span>
      <el-select
        v-model="examQuery.classId"
        style="width: 120px"
        class="select"
        placeholder="请选择班级"
        filterable
        @change="onChangeClass"
      >
        <el-option v-for="item in classList" :key="item.classId" :label="item.class_name" :value="item.classId">
        </el-option>
      </el-select>
    </div>

    <!-- 分类筛选 -->
    <div class="header__select" v-if="showCategory">
      <span class="select__label">类别：</span>
      <el-select
        ref="select"
        v-model="examQuery.categoryId"
        class="select"
        @change="onChangeCategory"
        placeholder="请选择类型"
      >
        <el-option v-for="item in categoryList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
      </el-select>
    </div>
    <el-input
      v-if="showKeyword"
      style="width: 180px"
      v-model="examQuery.keyWord"
      placeholder="请输入考试名称搜索"
      @keyup.enter.native="onQuery"
    >
      <i slot="suffix" class="el-input__icon el-icon-search" @click="onQuery"></i>
    </el-input>
  </div>
</template>

<script lang="ts">
import { listExamYear } from '@/service/pexam';
import UserRole from '@/utils/UserRole';
import { Component, Mixins, Prop, Vue, Watch } from 'vue-property-decorator';
import SyFilterMixin from './sy-filter-mixin.vue';
import { getSchoolYearListAPI } from '@/service/api';
import moment from 'moment';

export interface ExamQuery {
  acadYearsId: string;
  year: number | string;
  gradeId: string;
  subjectId: string;
  categoryId: string;
  keyWord: string;
  classId: string;
  clzId: string;
  layeredClassIds: string;
}

interface IAcadYearsId {
  schoolYearId: string;
  schoolYear: string;
  updateYearTime: string;
  startTime: string;
  endTime: string;
}

interface ICategory {
  id: string | number;
  name: string;
}

interface IGrade {
  id: number | string;
  name: string;
  phaseId?: number | string;
  year?: number;
  systemCode?: string;
}

interface ISubject {
  id: string;
  name: string;
  subject_name?: string;
  subject_code?: string;
  phase?: number;
  center_code?: string;
  isCustom?: number;
  phaseId?: number;
  systemCode?: string;
}

interface IClass {
  classId: string;
  year: number;
  system_code: string;
  className: string;
  class_order: number;
  class_name: string;
  classType: number;
}

@Component({
  components: {},
})
export default class ExamListFilter extends Mixins(SyFilterMixin) {
  @Prop() reportType: string;
  @Prop() value: any;
  @Prop({ default: () => ({}) }) filterConfig: {
    showYearType?: boolean;
    showGrade?: boolean;
    showSubject?: boolean;
    showClass?: boolean;
    showCategory?: boolean;
    showKeyword?: boolean;
  };
  @Watch('examQuery', { deep: true })
  onExamQuery() {
    this.$emit('input', this.examQuery);
  }

  examQuery: ExamQuery = {
    /** 学年 */
    acadYearsId: '',
    /** 入学年份 */
    year: '',
    /** 年级id */
    gradeId: '',
    /** 学科id */
    subjectId: '',
    /** 分类id */
    categoryId: '',
    /** 关键字 */
    keyWord: '',
    /** 当前所选班级id */
    classId: '',
    /** 行政班ids */
    clzId: '',
    /** 分层班ids */
    layeredClassIds: '',
  };

  // 当前年级
  currectGrade: IGrade = null;

  // 学年列表
  acadYearsIdList: IAcadYearsId[] = [];
  // 入学年份列表
  yearList = [];
  // 分类列表
  categoryList: ICategory[] = [];
  // 年级列表
  gradeList: IGrade[] = [];
  // 学科列表
  allSubjectList: ISubject[] = [];
  // 学科列表
  get subjectList() {
    let subjectList = [];
    if (this.examQuery.gradeId == '') {
      subjectList = this.allSubjectList;
    } else {
      let grade = this.gradeList.find(item => item.id == this.examQuery.gradeId);
      if (grade) subjectList = this.allSubjectList.filter(item => item.phaseId == grade.phaseId);
    }
    if (subjectList.length > 1) {
      subjectList.unshift({
        id: subjectList.map(t => t.id).join(','),
        name: '全部学科',
      });
    }
    return subjectList;
  }
  // 班级列表
  classList: IClass[] = [];
  // 学年类型
  yearType: 'acadYearsId' | 'year' = 'acadYearsId';

  // keepAlive 缓存数据
  keepCache = {
    schoolId: this.$sessionSave.get('schoolInfo').id,
    userId: this.$sessionSave.get('loginInfo').id,
  };
  // 是否初始化
  initMounted = false;

  // 学校列表
  schoolList = [];
  schoolId = '';

  roles = [];

  get showYearType() {
    return this.filterConfig.showYearType !== false;
  }

  get showGrade() {
    return this.filterConfig.showGrade !== false;
  }

  get showSubject() {
    return this.filterConfig.showSubject !== false;
  }

  get showClass() {
    return this.filterConfig.showClass !== false;
  }

  get showCategory() {
    return this.filterConfig.showCategory !== false;
  }

  get showKeyword() {
    return this.filterConfig.showKeyword !== false;
  }

  async mounted() {
    if (this.isSyRegion) {
      this.schoolList = await this.getSySchoolList();
      this.schoolId = this.$sessionSave.get('schoolInfo').id;
    }
    this.initSelect();
    this.initMounted = true;
  }

  async activated() {
    if (!this.initMounted) return;
    if (
      this.keepCache.schoolId !== this.$sessionSave.get('schoolInfo').id ||
      this.keepCache.userId !== this.$sessionSave.get('loginInfo').id
    ) {
      this.keepCache.schoolId = this.$sessionSave.get('schoolInfo').id;
      this.keepCache.userId = this.$sessionSave.get('loginInfo').id;
      this.initSelect();
    }
  }

  // 初始化选择器
  async initSelect() {
    this.roles = UserRole.examRolesTypes.split(',').map(r => Number(r));

    if (this.reportType == 'school') {
      let roles = [1].concat(UserRole.examRolesTypes.split(',').map(r => Number(r)));
      this.roles = roles.filter((r, index, self) => self.indexOf(r) === index);
    }
    this.examQuery.categoryId = '';
    this.examQuery.acadYearsId = '';
    this.examQuery.keyWord = '';
    this.examQuery.year = '';
    this.examQuery.gradeId = '';
    this.examQuery.subjectId = '';
    this.examQuery.classId = '';
    this.yearType = 'acadYearsId';

    await this.getAcadYearsIdList();
    this.examQuery.acadYearsId = this.acadYearsIdList[0].schoolYearId;
    this.getYearList();
    this.getCategoryList();
    await this.getGradeList();
    await this.onChangeGrade(this.gradeList[0]);
  }

  // 获取学年
  async getAcadYearsIdList() {
    const res = await getSchoolYearListAPI({
      schoolId: this.$sessionSave.get('schoolInfo').id,
    });
    let acadYearsIdList: IAcadYearsId[] = res.data || [];
    this.acadYearsIdList = acadYearsIdList;
  }

  // 获取入学年份列表
  async getYearList() {
    let res = await listExamYear({
      schoolId: this.$sessionSave.get('schoolInfo').id,
    });

    this.yearList = res.data.map(item => {
      return {
        label: item + '级',
        value: item,
      };
    });
  }

  // 获取分类
  async getCategoryList() {
    let list: any = await UserRole.getAllType();
    list.unshift({
      id: '',
      name: '全部类型',
    });
    this.categoryList = list;
  }

  // 获取年级
  async getGradeList() {
    let year: any = '';
    if (this.yearType == 'acadYearsId') {
      if (this.examQuery.acadYearsId) {
        const acadYearItem = this.acadYearsIdList.find(item => item.schoolYearId == this.examQuery.acadYearsId);
        year = acadYearItem.startTime;
      }
    } else {
      year = this.examQuery.year;
    }

    let list: any = await UserRole.getGradeList({
      schoolYear: year,
    });
    if (UserRole.isSchoolLeader || UserRole.isOperation) {
      list.unshift({
        id: '',
        name: '全部年级',
      });
    }
    list.forEach((item, index) => {
      item.key = index;
    });

    this.gradeList = list;
  }

  // 获取学科
  async getSubjectList() {
    let systemCode = this.currectGrade?.systemCode || '';
    let list = await UserRole.getSubjectList({
      year: this.examQuery.year,
      systemCode: systemCode,
      roles: this.roles,
    });
    this.allSubjectList = list;
  }

  // 获取班级
  async getClassList() {
    this.classList = [];
    this.examQuery.classId = '';

    if (!this.subjectList.length) {
      return;
    }

    let subjectId = this.examQuery.subjectId.split(',').length > 1 ? '' : this.examQuery.subjectId;
    let subject = this.subjectList.find(item => item.id == subjectId);
    let year = this.examQuery.year;
    let systemCode = this.yearType == 'acadYearsId' ? this.currectGrade?.systemCode : subject?.systemCode;

    let classList: any = await UserRole.getClassList({
      subjectId: subjectId,
      year: year,
      systemCode: systemCode,
      classType: -1,
      roles: this.roles,
    });

    if (classList.length) {
      classList.unshift({
        classId: '',
        class_name: '全部',
      });
    }

    this.classList = classList;
  }

  // 更改学年类型
  async onChangeYearType(val) {
    this.yearType = val;
    if (this.yearType == 'acadYearsId') {
      await this.onChangeAcadYearsId(this.acadYearsIdList[0].schoolYearId);
    } else {
      await this.onChangeYear(this.yearList[0].value);
    }
  }

  // 更改学年
  async onChangeAcadYearsId(val) {
    this.examQuery.acadYearsId = val;
    await this.getGradeList();
    await this.onChangeGrade(this.gradeList[0]);
  }

  // 更改入学年份
  async onChangeYear(val) {
    this.examQuery.year = val;
    this.examQuery.acadYearsId = '';
    this.examQuery.gradeId = '';
    await this.getSubjectList();
    await this.onChangeSubject(this.subjectList[0]?.id || '');
  }

  // 更改年级
  async onChangeGrade(val) {
    this.currectGrade = val;
    this.examQuery.gradeId = val?.id;
    this.examQuery.year = val?.year;
    await this.getSubjectList();
    await this.onChangeSubject(this.subjectList[0]?.id || '');
  }

  // 更改学科
  async onChangeSubject(val) {
    this.examQuery.subjectId = val;
    await this.getClassList();
    await this.onChangeClass(this.classList[0]?.classId || '');
  }

  // 更改班级
  async onChangeClass(val) {
    this.examQuery.classId = val;
    this.examQuery.clzId = this.getClassId().clzId;
    this.examQuery.layeredClassIds = this.getClassId().layeredClassIds;
    this.onQuery();
  }

  // 更改分类
  async onChangeCategory(val) {
    this.examQuery.categoryId = val;
    this.onQuery();
  }

  onQuery() {
    this.$emit('change', this.examQuery);
  }

  // 获取班级id
  getClassId() {
    let classType; // 1： 行政班 3： 分层班
    let clzId = '';
    let layeredClassIds = '';

    // 选择班级获取所选班级
    if (this.examQuery.classId) {
      let classObj = this.classList.find(item => item.classId == this.examQuery.classId);
      classType = classObj?.classType;
      clzId = classType == '1' ? this.examQuery.classId : '';
      layeredClassIds = classType == '3' ? this.examQuery.classId : '';
    }
    // 未选择班级，运营和校领导获取全部班级
    else if (UserRole.isOperation || UserRole.isSchoolLeader) {
      clzId = '';
      layeredClassIds = '';
    }
    // 获取全部行政班和分层班
    else {
      clzId = this.classList
        .filter(item => item.classType == 1)
        .map(item => item.classId)
        .join(',');
      layeredClassIds = this.classList
        .filter(item => item.classType == 3)
        .map(item => item.classId)
        .join(',');
    }
    return {
      clzId,
      layeredClassIds,
    };
  }

  // 更改学校
  async onChangeSchool(id) {
    let schoolInfo = this.schoolList.find(item => item.id == id);
    schoolInfo = {
      id: schoolInfo.id,
      schoolId: schoolInfo.id,
      school_name: schoolInfo.schoolName,
      schoolName: schoolInfo.schoolName,
    };
    this.$sessionSave.set('schoolInfo', schoolInfo);
    await UserRole.setSchoolInfo(schoolInfo);
    this.$store.commit('saveSchoolInfo', schoolInfo);
    this.initSelect();
  }
}
</script>

<style scoped lang="scss">
@import './select.scss';
</style>

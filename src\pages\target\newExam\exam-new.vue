<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-02-21 10:40:18
 * @LastEditors: 小圆
-->
<template>
  <div>
    <div class="exam-table">
      <div class="exam-table-item">
        <div class="exam-table-item__label">请选择区域：</div>
        <div class="exam-table-item__content">
          <el-select v-model="currentScoreRuleId" placeholder="请选择赋分方式">
            <el-option
              v-for="item in scoreRuleList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
      </div>

      <div v-if="currentScoreRule">
        <div class="exam-table-item">
          <div class="exam-table-item__label">选考模式：</div>
          <div class="exam-table-item__content">{{ currentScoreRule.mode }}</div>
        </div>
        <div class="exam-table-item">
          <div class="exam-table-item__label">选考科目：</div>
          <div class="exam-table-item__content">{{ currentScoreRule.subjectNames }}</div>
        </div>
        <div class="exam-table-item">
          <div class="exam-table-item__label">赋分规则：</div>
          <div class="exam-table-item__content">
            <table class="rule-table">
              <tr>
                <th>等级</th>
                <th>比例</th>
                <th>累计比例</th>
                <th>赋分成绩</th>
              </tr>
              <tr v-for="item in JSON.parse(currentScoreRule.rule)" :key="item.level">
                <td>{{ item.level }}</td>
                <td>{{ item.ratio }}%</td>
                <td>{{ item.addRatio }}%</td>
                <td>{{ item.start }}~{{ item.end }}</td>
              </tr>
            </table>
          </div>
        </div>
      </div>

      <!-- <div class="exam-table-item">
        <div class="exam-table-item__label">赋分规则：</div>
        <div class="exam-table-item__content subject-name">{{ scoreRuleParams.name }}</div>
      </div> -->
    </div>

    <div style="text-align: right; margin-right: 200px">
      <el-button type="primary" @click="saveRule" :loading="saveLoading">生成报告</el-button>
    </div>
  </div>
</template>

<script>
import {
  getExamScoreRuleByRuleId,
  getExamScoreRuleList,
  setScoreRuleId,
  saveScoreRuleSet,
} from '@/service/pexam';
export default {
  data() {
    return {
      // 新高考参数
      scoreRuleParams: {
        name: '',
        mode: '',
        subjectNames: '',
        rule: [],
      },
      // 新高考规则列表
      scoreRuleList: [],
      // 当前新高考规则ID
      currentScoreRuleId: '',

      // 保存按钮loading
      saveLoading: false
    };
  },

  computed: {
    currentScoreRule() {
      return this.scoreRuleList.find(item => item.id === this.currentScoreRuleId);
    },
  },

  mounted() {
    this.getExamScoreRuleList();
  },

  methods: {
    // 获取新高考规则列表
    async getExamScoreRuleList() {
      let res = await getExamScoreRuleList();
      this.scoreRuleList = res.data;
      this.currentScoreRuleId = this.$sessionSave.get('reportDetail').scoreRuleId;
    },

    // 获取新高考规则
    async getExamScoreRuleByRuleId(id) {
      const { data } = await getExamScoreRuleByRuleId({ id });
      data.rule = JSON.parse(data.rule);
      this.scoreRuleParams = data;
    },

    // 保存高考规则
    async saveRule() {
      if (!this.currentScoreRuleId) {
        this.$message.warning('请选择赋分区域');
        return;
      }

      this.saveLoading = true;
      const examId = this.$sessionSave.get('reportDetail').examId;
      const ruleId = this.currentScoreRuleId;

      try {
        await setScoreRuleId({
          examId,
          ruleId: '',
          type: 1, // 0:设置赋分规则ID ; 1:设置自定义赋分规则ID
        });
        const res = await setScoreRuleId({
          examId,
          ruleId,
          type: 0, // 0:设置赋分规则ID ; 1:设置自定义赋分规则ID
        });
        if (res.code == 1) {
          const reportDetail = this.$sessionSave.get('reportDetail');
          await saveScoreRuleSet({
            examId: this.$sessionSave.get('reportDetail').examId,
          });
          this.$sessionSave.set('reportDetail', {
            ...reportDetail,
            scoreRuleId: ruleId,
            customScoreRuleId: '',
            v: reportDetail.v + 1,
          });
          this.$message.success('设置成功');
        }
      } catch (error) {
        console.error(error);
      }
      this.saveLoading = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.exam-table {
  display: flex;
  flex-direction: column;

  &-item {
    display: flex;
    gap: 5px;
    margin-bottom: 12px;

    &__label {
      text-align: right;
      margin-right: 5px;
    }

    &__content {
      flex: 1;
    }
  }
}

.rule-table {
  width: 600px;
  border-collapse: collapse;

  td,
  th {
    text-align: center;
    border: 1px solid black;
  }

  th {
    background: rgb(217, 217, 217);
    color: black;
  }

  tr:hover {
  }
}

.subject-name {
  color: #02a7f0;
}
</style>

<template>
  <div>
    <div class="ques-edit-wrapper">
      <div v-if="item.isEdit" class="ques-save" @click="savePoint(item)">保存</div>
      <div v-if="!item.isEdit" class="ques-edit" @click.stop="editPoint(item)">编辑</div>
      <div class="ques-cancel" @click.stop="deletePoint(item)">删除</div>
    </div>
    <template v-if="item.isEdit">
      <div class="resize-dot top"></div>
      <div class="resize-dot bottom"></div>
      <div class="resize-dot left"></div>
      <div class="resize-dot right"></div>
      <div class="resize-dot top-left"></div>
      <div class="resize-dot top-right"></div>
      <div class="resize-dot bottom-left"></div>
      <div class="resize-dot bottom-right"></div>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default() {
        return {};
      },
    },
    posIndex: {
      type: Number,
      default() {
        return 0;
      },
    },
  },
  methods: {
    savePoint(item) {
      this.$emit('save-point', item);
      this.$forceUpdate();
    },
    editPoint(item) {
      this.$emit('edit-point', item, this.posIndex);
      this.$forceUpdate();
    },
    deletePoint(item) {
      this.$emit('delete-point', item, this.posIndex);
      this.$forceUpdate();
    },
  },
};
</script>

<style lang="scss" scoped>
.notop {
  display: block !important;
}
.resize-dot {
  position: absolute;
  width: 8px;
  height: 8px;
  border: 1px solid;
  background: #fff;
  box-sizing: border-box;

  &.top {
    cursor: n-resize;
    top: -4px;
    left: 50%;
    margin-left: -4px;
  }

  &.bottom {
    cursor: s-resize;
    bottom: -4px;
    left: 50%;
    margin-left: -4px;
  }

  &.left {
    top: 50%;
    margin-top: -4px;
    cursor: w-resize;
    left: -4px;
  }

  &.right {
    cursor: e-resize;
    right: -4px;
    top: 50%;
    margin-top: -4px;
  }

  &.top-left {
    margin-top: -4px;
    cursor: nw-resize;
    margin-left: -4px;
  }

  &.top-right {
    cursor: ne-resize;
    right: 0;
    margin-right: -4px;
    margin-top: -4px;
  }

  &.bottom-left {
    cursor: sw-resize;
    bottom: 0;
    margin-left: -4px;
    margin-bottom: -4px;
  }

  &.bottom-right {
    cursor: se-resize;
    right: -4px;
    bottom: 0;
    margin-bottom: -4px;
  }
}
.ques-edit-wrapper {
  display: flex;
  background: rgba(0, 0, 0, 0.5);
  font-size: 12px;
  z-index: 9;
  position: absolute;
  right: 5px;
  bottom: -16px;
  justify-content: space-around;
  color: #fff;
  min-width: 56px;
}
.ques-cancel,
.ques-save,
.ques-edit {
  text-align: center;
  // width: 30px;
  padding: 0 2px;
  &:hover {
    background: rgba(0, 0, 0, 0.5);
    cursor: pointer;
  }
}
</style>
<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2022-03-18 15:57:19
 * @LastEditTime: 2025-05-30 17:03:41
 * @LastEditors: 小圆
-->

<template>
  <!-- 输入密码界面 -->
  <div class="password-form pointer-auto">
    <el-form :model="passwordList" ref="ruleForm" class="password-ruleForm">
      <el-form-item prop="newPass">
        <el-input
          ref="accountInput"
          class="password-form__input password-form__input--code"
          placeholder="请输入8-16位数字、字母或特殊字符"
          title="请输入8-16位数字、字母或特殊字符"
          pattern=".*\S.*"
          autocomplete="off"
          v-model.trim="passwordList.newPass"
          :type="newPasswordType"
          @input="checkInput"
        >
          <span slot="prefix" class="el-input__text--label">新的密码</span>
          <i
            slot="suffix"
            class="el-input__icon el-icon-view"
            :style="{ color: newPasswordType == 'text' ? '#2574FF' : '' }"
            @click="showNewPass"
          ></i>
        </el-input>
      </el-form-item>

      <el-form-item prop="confirmPass">
        <el-input
          ref="psdInput"
          class="password-form__input password-form__input--code"
          placeholder="请再次输入新密码"
          autocomplete="off"
          v-model.trim="passwordList.confirmPass"
          :type="confirmPasswordType"
          @input="checkInput"
        >
          <span slot="prefix" class="el-input__text--label">确认密码</span>
          <i
            slot="suffix"
            class="el-input__icon el-icon-view"
            :style="{ color: confirmPasswordType == 'text' ? '#2574FF' : '' }"
            @click="showConfirmPass"
          ></i>
        </el-input>
      </el-form-item>
    </el-form>

    <div class="form-validate">
      <span>{{ validateText }}</span>
    </div>

    <el-button class="success-button click-element" type="primary" :disabled="disableSuccess" @click="finish"
      >完成</el-button
    >
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { resetPassWordByMobileAPI } from '@/service/api';
import { checkPasswordStrength } from '@/utils/index';

@Component({
  components: {},
})
export default class ChangePwd extends Vue {
  // guid
  @Prop() guid: any;
  // 手机号码
  @Prop() phoneNumber: any;

  // 密码对象
  passwordList = {
    newPass: '',
    confirmPass: '',
    isAll: false,
  };
  // 校验显示文字
  validateText: any = '';
  // 新密码输入框文字类型
  newPasswordType: any = 'password';
  // 确认密码输入框文字类型
  confirmPasswordType: any = 'password';

  // 按钮可用
  get disableSuccess() {
    return !this.passwordList.isAll;
  }

  /**
   * @description: 限制输入框只能输入数字、字母、下划线
   */
  @Watch('passwordList.newPass')
  checkNewPassInput(newVal: any, oldVal: any) {
    this.passwordList.newPass = newVal.replace(/[^!-~]/g, '');
  }

  @Watch('passwordList.confirmPass')
  checkConfirmPassInput(newVal, oldVal) {
    this.passwordList.confirmPass = newVal.replace(/[^!-~]/g, '');
  }

  /**
   * @description: 设置提示文本
   * @param {*} val
   */
  setValidateText(val) {
    this.validateText = val;
  }

  /**
   * @description: 检查输入
   * @param {*} value
   */
  checkInput(value) {
    this.validateText = '';
    if (!value) {
      this.passwordList.isAll = false;
    } else {
      if (!this.passwordList.newPass || !this.passwordList.confirmPass) {
        this.passwordList.isAll = false;
      } else {
        this.passwordList.isAll = true;
      }
    }
  }

  /**
   * @description: 切换新密码明文
   */
  showNewPass() {
    //点击图标是密码隐藏或显示
    if (this.newPasswordType == 'text') {
      this.newPasswordType = 'password';
    } else {
      this.newPasswordType = 'text';
    }
  }

  /**
   * @description: 切换旧密码明文
   */
  showConfirmPass() {
    if (this.confirmPasswordType == 'text') {
      this.confirmPasswordType = 'password';
    } else {
      this.confirmPasswordType = 'text';
    }
  }

  /**
   * @description: 完成按钮
   */
  async finish() {
    const pwdCheck = checkPasswordStrength(this.passwordList.newPass);
    if (!pwdCheck.valid) {
      this.setValidateText(pwdCheck.message);
      return;
    }

    if (this.passwordList.newPass !== this.passwordList.confirmPass) {
      this.setValidateText('两次输入的密码不一致');
      return;
    }

    this.setValidateText('');
    try {
      await resetPassWordByMobileAPI({
        guid: this.guid, // 安全吗
        mobile: this.phoneNumber, // 手机号
        password: this.passwordList.newPass, // 密码
        repassword: this.passwordList.confirmPass, // 重复密码
        userType: 1, // 0：注册;1：教师
      });
      this.$message({
        type: 'success',
        message: '密码修改成功',
        duration: 1000,
      });
      this.$emit('backUpdatePassword', this.passwordList.newPass, this.phoneNumber);
      setTimeout(() => {
        this.$emit('back');
      }, 1000);
    } catch (error) {
      console.log(error);
      this.$message.error(error.msg);
    }
  }
}
</script>
<style lang="scss" scoped>
.password-form {
  width: 360px; // width: vw(360, $win_width);
  margin: 0 auto;

  .password-form__input {
    margin-bottom: 22px; // margin-bottom: vw(22, $win_width);
  }

  .el-input__text--label {
    font-size: 16px; // font-size: 16px; // vw(16, $win_width);
    color: #2a3034;
    line-height: 48px; // line-height: vw(48, $win_width);
  }

  .success-button {
    display: block;
    margin: 20px auto 50px; // margin: vh(20, $win_height) auto vh(50, $win_height);
    width: 100%;
    height: 48px; // height: vw(48, $win_width);
    border: none;
    border-radius: 50px; // border-radius: vw(50, $win_width);
    font-size: 16px; // font-size: 16px; // vw(18, $win_width);
  }

  .form-validate {
    height: 14px; // height: vw(14, $win_width);
    font-size: 14px; // font-size: 16px; // vw(14, $win_width);
    text-indent: 2em;
    color: #fe5d50;
    margin-top: -10px;
    margin-bottom: 15px;
    text-align: left;
  }
}
</style>

<style lang="scss">
.password-form {
  .password-ruleForm {
    .el-form-item {
      margin: 0;
    }
  }

  .password-form__input {
    &.active {
      .el-input__inner {
        border-color: #2574ff;
        color: #2574ff;

        ~ .el-input__prefix {
          .el-input__icon {
            color: #2574ff;
          }
        }
      }
    }

    .el-input__inner {
      height: 48px; // height: vw(48, $win_width);
      padding: 0 55px; // padding: 0 vw(55, $win_width);
      border-radius: 50px; // border-radius: vw(50, $win_width);
      font-size: 16px; // font-size: 16px; // vw(18, $win_width);
      letter-spacing: 2px;
      line-height: 50px; // line-height: vw(50, $win_width);
      color: #2a3034;

      &:focus {
        caret-color: #2574ff;

        ~ .el-input__prefix {
          .el-input__icon {
            color: #2574ff;
          }
        }

        ~ .el-input__suffix {
          .el-input__icon {
            &.icon-keyboard {
              opacity: 1;
            }
          }
        }
      }
    }

    .el-input__icon {
      line-height: 47px; // line-height: vw(47, $win_width);
      color: #bfcadb;

      &.el-icon-arrow-up {
        color: #2574ff;
      }
    }

    .el-input__prefix {
      pointer-events: none;
      left: 20px; // left: vw(20, $win_width);

      .el-input__icon {
        font-size: 16px; // font-size: 16px; // vw(32, $win_width);
      }
    }

    .el-input__suffix {
      right: 20px; // right: vw(20, $win_width);

      &:hover {
        .el-input__icon {
          // color: #2574ff;
        }
      }

      .el-input__icon {
        // display: inline-block;
        vertical-align: middle;
        margin-left: 5px; // margin-left: vw(5, $win_width);
        line-height: 48px; // line-height: vw(48, $win_width);
        right: 20px; // right: vw(20, $win_width);
        font-size: 16px; // font-size: 16px; // vw(22, $win_width);

        &.icon-keyboard {
          opacity: 0;
          font-size: 16px; // font-size: 16px; // vw(24, $win_width);
        }

        &.active {
          color: #2574ff;
        }
      }
    }
  }

  .password-form__input--code {
    .el-input__inner {
      letter-spacing: 2px;
      padding-left: 95px; // padding-left: vw(95, $win_width);

      &::placeholder {
        letter-spacing: 0px;
        font-size: 16px; // font-size: 16px; // vw(13, $win_width);
      }
    }
  }
}
</style>

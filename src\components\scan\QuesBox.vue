<template>
  <div class="ques_box" :style="box.style">
    <span v-if="box.isFirst" class="ques_no_scroll"></span>
    <span v-if="box.isFirst" class="ques_no">{{ box.quesSort }}</span>
    <div v-show="!pointer.showDrawBox" v-if="box.answerStyle" class="ques_answer_box" :style="box.answerStyle">
      <div class="rec_result">
        <span v-if="inArr(box.ques.typeId,[1,8])">{{ box.ques.answer }}</span>
        <span v-else>{{ box.ques.score }}</span>
      </div>
    </div>
    <div v-show="pointer.show&&canEdit" class="pointer pointer-x" :style="{left: pointer.x+'px'}"></div>
    <div v-show="pointer.show&&canEdit" class="pointer pointer-y" :style="{top: pointer.y+'px'}"></div>
    <div v-show="pointer.showDrawBox&&canEdit" class="draw_ques_answer_box" :style="drawBoxStyle"></div>

    <div ref="drawImage"
         v-show="canEdit"
         @mousemove="mousemove"
         @mouseleave="mouseleave"
         @mouseenter="mouseenter"
         class="ques-inner-draw"></div>
    <i v-show="canEdit" @click="deleteQues(box)" class="ques_delete el-icon-circle-close"></i>
  </div>
</template>

<script>

import {inArr} from "@/utils/common";

export default {
  name: 'ques-box',
  props: ['box', 'canEdit', 'index'],
  data() {
    return {
      inArr: inArr,
      //指针位置
      pointer: {
        x: 0,
        y: 0,
        show: true,
        showDrawBox: false,
        drawBox: false
      },
      start: {
        x: 0,
        y: 0
      },
      end: {
        x: 0,
        y: 0
      },
      drawBoxStyle: {
        display: 'none'
      }
    };
  },

  mounted() {
    this.pointer.show = false
  },
  methods: {
    endAddBox() {
      this.pointer.showDrawBox = false
      this.drawBoxStyle = {
        display: 'none'
      }
    },
    setDrawBoxStyle() {
      this.drawBoxStyle = {
        left: Math.min(this.start.x, this.end.x) + "px",
        top: Math.min(this.start.y, this.end.y) + "px",
        width: Math.abs(this.end.x - this.start.x) + "px",
        height: Math.abs(this.end.y - this.start.y) + "px",
      };
    },
    deleteQues() {
      this.$emit('delete-ques-box')
    },
    getBoxStyle(boxes, i) {
      let len = boxes.length
      let box = boxes[i].box
      let style = {
        left: (box[0] - box[2] / 2) * 100 + "%",
        top: (box[1] - box[3] / 2) * 100 + "%",
        width: box[2] * 100 + "%",
        height: box[3] * 100 + "%",
      }
      if (len > 1) {
        if (i < len - 1) {
          style.borderBottom = 'none'
        }

        if (i > 0) {
          style.borderTop = 'none'
        }
      }
      return style;
    },
    mouseenter(e) {
      if (!this.canEdit) {
        return
      }
      this.pointer.show = true
      document.addEventListener('mousedown', this.mousedown)
    },
    mouseleave(e) {
      if (!this.canEdit) {
        return
      }
      this.pointer.show = false
      document.removeEventListener('mousedown', this.mousedown)
    },
    mousemove(e) {
      if (!this.canEdit) {
        return
      }
      if (e.button === 0) {
        this.pointer.x = e.layerX;
        this.pointer.y = e.layerY;
        this.end.x = this.pointer.x;
        this.end.y = this.pointer.y;
        if (this.pointer.drawBox) {
          this.setDrawBoxStyle();
        }
      }
    },
    mousedown(e) {
      if (!this.canEdit) {
        return
      }
      if (e.button === 0) {
        this.pointer.showDrawBox = true
        this.pointer.drawBox = true
        this.start.x = this.end.x
        this.start.y = this.end.y
        document.addEventListener('mouseup', this.mouseup)
      }
    },
    mouseup(e) {
      if (!this.canEdit) {
        return
      }
      document.removeEventListener('mouseup', this.mouseup)

      if (e.button === 0) {
        this.pointer.drawBox = false
        let width = this.$refs.drawImage.clientWidth
        let height = this.$refs.drawImage.clientHeight
        let box = [
          (this.end.x + this.start.x) / 2 / width,
          (this.end.y + this.start.y) / 2 / height,
          Math.abs(this.end.x - this.start.x) / width,
          Math.abs(this.end.y - this.start.y) / height,
        ]

        if (box[2] > 0.03) {
          this.$emit('add-ques-answer-box', this.index, box);
        }
        this.endAddBox()
      }
    },
    getImage() {
      return this.$refs.image
    }
  }
};
</script>

<style lang="scss" scoped>
.ques_box {
  position: absolute;
  border: solid 1px red;

  .pointer {
    position: absolute;
    background: yellow;
  }

  //指针位置
  .pointer-x {
    left: -100px;
    top: 0;
    width: 1px;
    height: 100%;
  }

  .pointer-y {
    left: 0;
    top: -100px;
    width: 100%;
    height: 1px;
  }

  .ques-inner-draw {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .ques_no_scroll {
    position: absolute;
    top: -30px;
  }

  .ques_delete {
    position: absolute;
    top: -15px;
    right: 5px;
    color: red;
    background: white;
    border-radius: 50%;
    font-size: 30px;
  }

  .ques_no {
    border: 3px solid red;
    border-radius: 50%;
    position: absolute;
    text-align: center;
    font-weight: 700;
    color: red;
    width: 30px;
    height: 30px;
    font-size: 15px;
    line-height: 25px;
    left: 4px;
    background: white;
    top: -16px;
  }

  .draw_ques_answer_box {
    position: absolute;
    border: solid 2px yellow;
  }

  .ques_answer_box {
    position: absolute;
    border: solid 2px red;

    .rec_result {
      position: absolute;
      color: red;
      font-size: 20px;
      left: 0;
      top: -23px;
      background: #ffffffaa;
      line-height: 1em;
    }

    .rec_error {
      position: absolute;
      color: red;
      font-size: 20px;
      left: 0;
      bottom: -23px;
      background: #ffffffaa;
      line-height: 1em;

      :before {
        content: '错误：';
      }
    }
  }
}

img {
  width: 100%;
  padding: 0;
}

</style>

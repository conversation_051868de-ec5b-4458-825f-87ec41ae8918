<template>
  <div class="exam-mk-container" v-if="!loadingStatus">
    <div class="ques-setting-container">
      <div class="ques-setting-top">
        <div class="top-back el-icon-back">
          <span class="back-word" @click="backReport"> 返回 </span>
        </div>
        <div style="display: flex">
          <div v-for="(item, key, index) in steps" :key="index" class="top-steps"
            :class="{ active: currentStep == item.type }">
            <div class="top-steps-item" @click="changeStep(item)">
              <span class="step-number" :class="{ active: currentStep == item.type }">{{
                index + 1
              }}</span>
              <span class="step-name">框选{{ item.name }}</span>
            </div>
            <span class="step-tag" :class="{ 'last-step': item.id == '6' }"></span>
          </div>
        </div>
        <div class="top-clear" @click="clearAll">
          <i class="el-icon-delete"></i>
          <span>全部清空</span>
        </div>
      </div>
      <div class="ques-setting-left">
        <!-- 操作栏 -->
        <div class="ques-setting-left-top">
          <div class="top-tip">提示：{{ steps[currentStep].tips }}</div>
          <div class="step-clear">
            <i class="el-icon-delete"></i>
            <span @click="clearStep">清空{{ steps[currentStep].name }}</span>
          </div>
          <div class="recognition-mode" v-if="
            currentStep == 'anchordot' ||
            currentStep == 'number' ||
            currentStep == 'others'
          ">
            <el-radio-group v-model="recognitionHandle" @change="changeRecognitionMode" size="mini">
              <el-radio-button label="ai">智能</el-radio-button>
              <el-radio-button label="hand">手动</el-radio-button>
            </el-radio-group>
          </div>
          <div class="step-zoom">
            <el-tooltip content="放大" placement="top">
              <div class="add-zoom" @click="btnOperateZoom('add')">
                <i class="el-icon-zoom-in"></i>
              </div>
            </el-tooltip>
            <el-tooltip content="缩小" placement="top">
              <div class="sub-zoom" @click="btnOperateZoom('sub')">
                <i class="el-icon-zoom-out"></i>
              </div>
            </el-tooltip>
          </div>
          <!-- 页数切换 -->
          <div v-if="pageCount > 1" class="top-page">
            <div :class="['page', currPage == page - 1 ? 'active' : '']" v-for="(page, pageIndex) in pageCount"
              :key="pageIndex" @click="changePage(page)">
              {{ page }}
            </div>
          </div>
        </div>
        <div class="exam-pdf-container">
          <!-- <el-scrollbar style="height: 100%"> </el-scrollbar> -->
          <div class="page-layout none-select" :style="{ transform: `scale(${this.divScale})` }"
            :class="[pageLayout == IPAGELAYOUT.A4 ? 'a4' : 'a3']" @mousedown="mousedown" @mousemove="mousemove"
            @mouseup="mouseup" @mouseleave="mouseup">
            <!-- 图片 -->
            <img class="img" :src="pageUrl" draggable="false" />
            <!-- 当前绘制画框 -->
            <div class="ques_box draw" :style="drawBoxStyle"></div>
            <!-- 已框选 -->
            <div class="page-points-wrapper">
              <!-- 定位点 -->
              <point-dot ref="anchordot" :currentType="steps[currentStep].item_type" :currentStep="currentStep"
                :ques="pointList[currPage]" :currPage="currPage" @save-point="savePoint" @edit-point="editPoint"
                @delete-point="deletePoint"></point-dot>
              <!-- 考号 -->
              <exam-number ref="number" :currentType="steps[currentStep].item_type" :currentStep="currentStep"
                :ques="examNoList[currPage]" :currPage="currPage" @save-point="savePoint" @edit-point="editPoint"
                @delete-point="deletePoint">
              </exam-number>
              <!-- 条形码考号坐标 -->
              <title-head ref="barcode" :currentType="steps[currentStep].item_type" :currentStep="currentStep"
                :itemType="6" :ques="barcodeExamNoList[currPage]" :currPage="currPage" :title="'条形码'"
                @save-point="savePoint" @edit-point="editPoint" @delete-point="deletePoint">
              </title-head>
              <!-- 客观题 -->
              <object-ques ref="object" :currentType="steps[currentStep].item_type" :currentStep="currentStep"
                :ques="objectQues[currPage]" :currPage="currPage" @save-point="savePoint" @edit-point="editPoint"
                @delete-point="deletePoint"></object-ques>
              <!-- 主观题 -->
              <subject-ques ref="subject" :currentType="steps[currentStep].item_type" :currentStep="currentStep"
                :ques="subjectQues[currPage]" :currPage="currPage" @save-point="savePoint" @edit-point="editPoint"
                @delete-point="deletePoint"></subject-ques>
              <!-- 标题坐标 -->
              <title-head ref="title" :currentType="steps[currentStep].item_type" :currentStep="currentStep"
                :ques="titlePos[currPage]" :currPage="currPage" :title="'标题'" @save-point="savePoint"
                @edit-point="editPoint" @delete-point="deletePoint">
              </title-head>
              <!-- 页码标记 -->
              <title-head ref="page" :currentType="steps[currentStep].item_type" :currentStep="currentStep"
                :ques="pagePos[currPage]" :currPage="currPage" :title="'页码'" @save-point="savePoint"
                @edit-point="editPoint" @delete-point="deletePoint">
              </title-head>
              <!-- 二维码坐标 -->
              <title-head ref="qrCode" :currentType="steps[currentStep].item_type" :currentStep="'qrCode'"
                :ques="qrCodePos[currPage]" :currPage="currPage" :title="'二维码'" @save-point="savePoint"
                @edit-point="editPoint" @delete-point="deletePoint">
              </title-head>
              <!-- 缺考标记 -->
              <title-head ref="others" :currentType="steps[currentStep].item_type" :currentStep="currentStep"
                :ques="missMarkPos[currPage]" :currPage="currPage" :title="'缺考'" @save-point="savePoint"
                @edit-point="editPoint" @delete-point="deletePoint">
              </title-head>
              <!-- ab卷标记 -->
              <title-head ref="others" :currentType="steps[currentStep].item_type" :currentStep="currentStep"
                :ques="abCardPos[currPage]" :currPage="currPage" :title="'AB卷'" @save-point="savePoint"
                @edit-point="editPoint" @delete-point="deletePoint">
              </title-head>
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧信息栏 -->
      <right-setting class="ques-setting-right" :paperInfo="paperInfo" :subjectQues="subjectQues"
        :objectQues="objectQues" :pointList="pointList" :examNoList="examNoList" :missMarkPos="missMarkPos"
        :barcodeExamNoList="barcodeExamNoList" :titlePos="titlePos" :pagePos="pagePos" :currPage="currPage"
        :currentStep="currentStep" :pageCount="pageCount" @save-data="openCardInfoDialog" @next-page="nextPage"
        @drag-object-end="dragObjectEnd" @drag-subject-end="dragSubjectEnd"></right-setting>
    </div>
    <!-- 框选题目弹窗 -->
    <el-drawer title="框选题目" :visible.sync="isShowDrawer">
      <div class="drawer-content">
        <el-form :label-position="'right'" ref="form" :model="newQuesInfo" @submit.native.prevent>
          <el-form-item label="考号类型" v-if="currentStep == 'number'">
            <el-radio-group size="small" v-model="numberType">
              <el-radio-button label="2">填涂式</el-radio-button>
              <el-radio-button label="6">条形码</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="题号" v-if="
            (currentStep == 'object' && recognitionHandle == 'handle') || currentStep == 'subject'
          ">
            <el-input v-if="currentStep == 'object'" v-model="newQuesInfo.question_nos" placeholder="请输入题号"
              autofocus="true" ref="input"></el-input>
            <el-input v-else ref="input" v-model="newSubQues.question_nos" placeholder="请输入题号"></el-input>
          </el-form-item>
          <el-form-item label="起始题号" v-if="currentStep == 'object' && recognitionHandle == 'ai'">
            <span slot="label">起始题号<span style="color:darkgrey">（系统具备自动识别题号功能，可无需手动输入。）</span></span>
            <el-input ref="input" v-model="newQuesInfo.start_no" placeholder="请输入题号"
              @keyup.enter.native="checkSubmit"></el-input>
          </el-form-item>
          <el-form-item label="识别模式" v-if="currentStep == 'object' && recognitionHandle == 'ai'">
            <el-radio-group size="small" v-model="newQuesInfo.detect_type">
              <el-radio-button label="0">自动</el-radio-button>
              <el-radio-button label="1">AI</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="选项个数" v-if="currentStep == 'object'">
            <el-input-number v-model="newQuesInfo.option_num" :min="0" :max="26" size="small"></el-input-number>
          </el-form-item>
          <el-form-item label="合并一题" v-if="currentStep == 'object' && recognitionHandle == 'ai'">
            <el-radio-group size="small" v-model="newQuesInfo.mergeQues">
              <el-radio-button label="1">是</el-radio-button>
              <el-radio-button label="0">否</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="题型" v-if="
            (currentStep == 'object' && recognitionHandle == 'handle') || currentStep == 'subject'
          ">
            <el-radio-group v-if="currentStep == 'object'" v-model="newQuesInfo.question_type" size="small">
              <el-radio-button v-for="ques in objectQueesType" :label="ques.id" :key="ques.id">{{
                ques.name
              }}</el-radio-button>
            </el-radio-group>
            <el-radio-group v-else v-model="newSubQues.question_type" size="small">
              <el-radio-button v-for="ques in subjectQuesType" :label="ques.id" :key="ques.id">{{
                ques.name
              }}</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="排列方式" v-if="
            currentStep == 'object' &&
            recognitionHandle == 'handle' &&
            newQuesInfo.question_type != 2
          ">
            <el-radio-group v-model="newQuesInfo.align" size="small">
              <el-radio-button label="1">横排</el-radio-button>
              <el-radio-button label="2">竖排</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <template v-if="currentStep == 'number' && numberType == '2'">
            <!-- <el-form-item label="行数">
              <el-input-number ref="input" v-model="newExamNo.rows" :min="1" :max="20" :step="1" step-strictly
                placeholder="请输入行数"></el-input-number>
            </el-form-item> -->
            <el-form-item label="列数">
              <el-input-number v-model="newExamNo.cols" :min="1" :max="20" :step="1" step-strictly
                placeholder="请输入列数"></el-input-number>
            </el-form-item>
          </template>
          <template v-if="currentStep == 'others'">
            <el-form-item label="类型">
              <el-radio-group size="small" v-model="otherType">
                <el-radio-button label="1">缺考</el-radio-button>
                <el-radio-button label="2">AB卷</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </template>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button class="el-drawer__footer-button" @click="cancelAddQues">取 消</el-button>
          <el-button class="el-drawer__footer-button" type="primary" @click="sureAddQues">确 定</el-button>
        </div>
      </div>
    </el-drawer>
    <!-- 题卡信息弹窗 -->
    <card-info-dialog v-if="isShowCardInfo" :modal-visible.sync="isShowCardInfo" :subjectQues="subjectQues"
      :objectQues="objectQues" :pointList="pointList" :examNoList="examNoList" :barcodeExamNoList="barcodeExamNoList"
      :missMarkPos="missMarkPos" :abCardPos="abCardPos" :titlePos="titlePos" :pagePos="pagePos" :currPage="currPage"
      :pageCount="pageCount" :abCardType="abCardType" @confirm-card-info="confirmCardInfo"
      @close-card-info="closeCardInfo"></card-info-dialog>
  </div>
</template>

<script>
import BreadCrumbs from '@/components/Breadcrumbs/index.vue';
import { getViewPaper, updateViewPaper, getTestBankInfo, overCard } from '@/service/testbank.js';
import { relateAnswerCard } from '@/service/pexam';
import { partDetect, page_info_check, refreshPaperInfo } from '@/service/xueban';
import { mmConversionPx, pxConversionMm } from '@/utils/common';
import { getQueryString } from '@/utils';
import { generateUUID } from '@/utils/index.js';
import { ThirdCardStep } from '@/typings/scan';
import PointDot from './modules/pointDot.vue';
import EditContainer from './modules/editContainer.vue';
import ObjectQues from './modules/objectQues.vue';
import SubjectQues from './modules/subjectQues.vue';
import ExamNumber from './modules/examNumber.vue';
import RightSetting from './modules/rightSetting.vue';
import TitleHead from './modules/titleHead.vue';
import { Loading } from '@iclass/element-ui';
import CardInfoDialog from './modules/cardInfoDialog.vue';
import { IAB_CARD_TYPE, IPAGELAYOUT } from '@/typings/card';
import { sortObjectArrayByField } from './objectArraySorter';

const HAND_TYPE = {
  DRAW: 0,
  MOVE: 1,
  LEFT: 2,
  TOP: 3,
  RIGHT: 4,
  BOTTOM: 5,
  LEFT_TOP: 6,
  RIGHT_TOP: 7,
  LEFT_BOTTOM: 8,
  RIGHT_BOTTOM: 9,
};
let scleRatio = 1.1; // 每次放大/缩小的倍数
export default {
  name: 'thirdcard',
  components: {
    BreadCrumbs,
    PointDot,
    EditContainer,
    ObjectQues,
    SubjectQues,
    ExamNumber,
    RightSetting,
    TitleHead,
    CardInfoDialog,
  },
  data() {
    return {
      IPAGELAYOUT,
      paperNo: getQueryString('paperNo') || '105932',
      tbId: getQueryString('tbId') || '',
      schoolId: getQueryString('schoolId') || '',
      examId: getQueryString('examId') || '',
      personalBookId: getQueryString('personalBookId') || '',
      relateCardType: getQueryString('relateCardType') || '',
      //AB卡或者AB卷时,此字段生效 0:A卡(卷) 1:B卡(卷)
      abCardSheetType: getQueryString('abCardSheetType') || '',
      //ab卡类型
      abCardType: Number(getQueryString('abCardType')) || IAB_CARD_TYPE.default,
      //加载中状态
      loadingStatus: null,
      pagePoints: [],
      drawBox: [[]],
      quesInfos: [],
      //区域匹配题目
      matchQues: [],
      currPage: 0,
      pageCount: 0,
      pageLayout: 1,
      currQues: null,
      groupMap: new Map(),
      handType: HAND_TYPE.DRAW,
      drawBoxStyle: {
        display: 'none',
        transform: '',
      },
      start: {
        offsetLeft: 0,
        offsetTop: 0,
        x: 0,
        y: 0,
      },
      end: {
        x: 0,
        y: 0,
      },
      isDrag: false,
      dragBox: null,
      pageUrl: '',
      divScale: 1,
      // ①②③④⑤⑥
      steps: ThirdCardStep,
      //当前步骤
      currentStep: 'anchordot',
      //原图信息
      originImage: [],
      //相比原图需要缩放的倍数
      wScale: 0,
      hScale: 0,
      //客观题
      objectQues: [],
      //主观题
      subjectQues: [],
      //定位点坐标
      pointList: [],
      //考号坐标
      examNoList: [],
      //条形码考号坐标
      barcodeExamNoList: [],
      // 标题坐标
      titlePos: [],
      //缺考标记
      missMarkPos: [],
      //页码标记
      pagePos: [],
      //ab卷标记
      abCardPos: [],
      //二维码
      qrCodePos: [],
      //客观题题型
      objectQueesType: [
        {
          id: 8,
          name: '单选题',
        },
        {
          id: 1,
          name: '多选题',
        },
        {
          id: 2,
          name: '判断题',
        },
      ],
      //主观题题型
      subjectQuesType: [
        {
          id: 3,
          name: '填空题',
        },
        {
          id: 6,
          name: '简答题',
        },
      ],
      isShowDrawer: false,
      //客观题
      newQuesInfo: {
        question_nos: '', //题号
        question_type: 8, //题型
        align: 1, //排列方式 1横排 2竖排
        start_no: '',
        detect_type: 0, //0，自动，1：AI
        mergeQues: 0,//选择题合并  0：否 1：是
        option_num: 0, //选项个数
      },
      //主观题
      newSubQues: {
        question_nos: '', //题号
        question_type: 6, //题型
      },
      // 考号
      newExamNo: {
        cols: 1, //考号列
        rows: 10, //考号行
      },
      objQues: {},
      subQues: {},
      examNo: {},
      points: {},
      others: {},
      isShowCardInfo: false,
      //是否更新teaminfo
      isUpdateTeamInfo: false,
      //题型集合
      totalTypes: [],
      //组装的题目数据
      quesInfo: [],
      //题型是否已存在
      isTypeExit: false,
      cardInfo: {},
      recognitionHandle: 'hand',
      recognitionLoading: null,
      //考号类型
      numberType: '2', //2填涂考号  6条形码
      //试卷信息
      paperInfo: {},
      //其他类型
      otherType: '1',//1 缺考 2 ab卷
    };
  },
  watch: {
    currPage: {
      handler(newval, oldval) {
        this.pageUrl = this.pagePoints[newval][0].url;
        document.getElementsByClassName('exam-pdf-container')[0].scrollTop = 0;
      },
    },
    pageCount: {
      handler(newval, oldval) {
        if (newval > 2) {
          // 框选页码标记
          const temp = {};
          for (const key in this.steps) {
            temp[key] = this.steps[key];
            if (key === "title") {
              temp['page'] = {
                id: '7',
                type: 'page',
                tips: '每张只需框选一个页码标记，需和另一张有区分度。',
                name: '页码标记',
                item_type: 22,
              };
            }
          }
          this.steps = temp;
        }
      },
    },
  },

  async created() {
    await this.getTestBankInfo();
    // await this.getPaperData();
  },
  mounted() { },
  methods: {
    /***
     * @name:返回上一页
     */
    backReport() {
      this.$router.back();
    },
    mmConvertPx(point) {
      if (this.pageLayout == IPAGELAYOUT.A4) {
        return mmConversionPx(point);
      } else {
        return (mmConversionPx(point) / 4) * 3;
      }
    },
    pxConvertMm(point) {
      if (this.pageLayout == IPAGELAYOUT.A4) {
        return pxConversionMm(point);
      } else {
        return (pxConversionMm(point) / 3) * 4;
      }
    },
    isSubjectiv(typeid) {
      return ![1, 2, 8, '1', '2', '8'].includes(typeid);
    },
    getPageLayoutTxt() {
      if (this.pageLayout == IPAGELAYOUT.A4) {
        return 'A4';
      } else {
        return 'A3';
      }
    },
    changeRecognitionMode() {
      if (this.currentStep == 'number') {
        this.numberType = "2"
      }
    },

    /**
     * @name:切换步骤
     */
    changeStep(item) {
      this.currPage = 0;
      this.currentStep = item.type;
      //考号、客观题和定位点智能
      if (item.item_type == 2 || item.item_type == 3 || item.item_type == 8) {
        this.recognitionHandle = 'ai';
      } else {
        this.recognitionHandle = 'hand';
      }
    },
    /**
     * @name:切换右侧基本信息展开
     */
    handleChange(val) { },
    /**
     * @name:绘制定位框
     */
    converPoints2Draw() {
      this.pagePoints.forEach((point, page) => {
        // let width = this.originImage[page][0].width;
        let height = (this.originImage[page][0].height * 96) / 25.4;
        //a4图片区域宽限制为210mm>>793.7px   a3 315mm>> 1190.55px  算原图缩放倍数
        //a4图片区域高限制为297mm>>1122.52px   a3 315mm>> 1190.55px  算原图缩放倍数
        // this.wScale = (width / (this.pageLayout == 1 ? 793.7 : 1190.55)).toFixed(4);
        this.hScale = (height / (this.pageLayout == 1 ? 1122.52 : 844.35)).toFixed(4);
        console.log('hScale', this.hScale);
        point.forEach((item, index) => {
          if (!this.drawBox[page]) {
            this.drawBox[page] = [];
          }
          if (item.item_type != 0) {
            if (item.option_list) {
              item.option_list = item.option_list.map(it =>
                it.map(i => ((i * 96) / 25.4 / this.hScale).toFixed(6))
              );
            }
            // 定位点位置
            if (item.pos_list) {
              item.pos_list = item.pos_list.map(it => {
                return this.unscalePos(it);
              });
            }
            if (item.one_pos) {
              item.one_pos = this.unscalePos(item.one_pos);
              item.last_pos = this.unscalePos(item.last_pos);
            }
            if (item.pos) {
              item.pos = this.unscalePos(item.pos);
            }
            this.drawBox[page].push({
              id: item.question_id || generateUUID(),
              active: false,
              ...item,
            });
          }
        });
      });
      console.log('drawBox', this.drawBox);
      //定位点
      this.pointList = this.drawBox.map(group => {
        return group.filter(item => item.item_type == 10);
      });
      //考号（填涂）
      this.examNoList = this.drawBox.map(group => {
        return group.filter(item => item.item_type == 2);
      });
      //考号（条形码）
      this.barcodeExamNoList = this.drawBox.map(group => {
        return group.filter(item => item.item_type == 6);
      });
      //客观题
      this.objectQues = this.drawBox.map(group => {
        return group.filter(item => item.item_type == 3);
      });
      //主观题
      this.subjectQues = this.drawBox.map(group => {
        return group.filter(item => item.item_type == 4);
      });
      //标题
      this.titlePos = this.drawBox.map(group => {
        return group.filter(item => item.item_type == 11);
      });
      //页码标记
      this.pagePos = this.drawBox.map(group => {
        return group.filter(item => item.item_type == 22);
      });
      //缺考标记
      this.missMarkPos = this.drawBox.map(group => {
        return group.filter(item => item.item_type == 8);
      });
      //ab卷标记
      this.abCardPos = this.drawBox.map(group => {
        return this.abPointsSplit(group.filter(item => item.item_type == 19));
      });
      //二维码标记
      this.qrCodePos = this.drawBox.map(group => {
        return group.filter(item => item.item_type == 12);
      });
    },
    /**
     * @name:获取原图信息
     */
    filterImageInfo() {
      return this.pagePoints.map(item => item.filter(ite => ite.item_type == 0));
    },
    /**
     * @name:获取转换状态
     */
    async getTestBankInfo() {
      let res = await getTestBankInfo({ id: this.tbId })
      if (res.data.thirdPartyCardRecognitionState != 2) {
        this.loadingStatus = Loading.service({
          lock: true,
          text: '正在智能裁切，请稍候，预计5-20s，若长时间未成功，请刷新页面重试',
          background: 'rgba(0, 0, 0, 0.7)',
        });
        this.checkingStatus();
      } else {
        await this.getPaperData();
      }
    },
    /**
     * @name: 检测智能裁切状态
     */
    checkingStatus() {
      setTimeout(() => {
        getTestBankInfo({ id: this.tbId })
          .then(res => {
            if (res.data.thirdPartyCardRecognitionState == 2) {
              this.loadingStatus.close();
              this.loadingStatus = null;
              this.getPaperData();
            } else {
              this.checkingStatus();
            }
          })
          .catch(err => {
            this.loadingStatus.close();
          });
      }, 5000);
    },
    /**
     * @name: 获取试卷数据
     */
    async getPaperData() {
      let params = { paperNo: this.paperNo };
      let res = await getViewPaper(params);
      if (res.code == 1) {
        this.paperInfo = res.data;
        this.cardInfo = JSON.parse(res.data.cardInfo);
        this.points = this.cardInfo.points;
        this.pageLayout = res.data.pageLayout;
        this.pagePoints = this.cardInfo.points.pages;
        this.pageUrl = this.pagePoints[0][0].url;
        this.pageCount = this.pagePoints.length;
        this.originImage = await this.filterImageInfo();
        this.converPoints2Draw();
      }
    },
    setDrawBoxStyle() {
      this.drawBoxStyle = {
        left: this.start.x + 'px',
        top: this.start.y + 'px',
        width: this.end.x + 'px',
        height: this.end.y + 'px',
        pos: [this.start.x, this.start.y, this.end.x, this.end.y],
      };
    },
    clearDrawBoxStyle() {
      this.start = {
        x: 0,
        y: 0,
      };
      this.end = {
        x: 0,
        y: 0,
      };
      this.drawBoxStyle = {
        display: 'none',
      };
    },
    /*
     * @name: 删除画框
     */
    deletePoint(item, index) {
      switch (this.currentStep) {
        // 定位点
        case 'anchordot':
          const points = this.pointList[this.currPage].find(info => info.id == item.id);
          points.pos_list.splice(index, 1);
          break;
        //考号
        case 'number':
          if (item.item_type == 2) {
            this.$set(
              this.examNoList,
              this.currPage,
              this.examNoList[this.currPage].filter(info => info.id != item.id)
            );
          } else {
            this.$set(
              this.barcodeExamNoList,
              this.currPage,
              this.barcodeExamNoList[this.currPage].filter(info => info.id != item.id)
            );
          }
          break;
        //客观题
        case 'object':
          this.$set(
            this.objectQues,
            this.currPage,
            this.objectQues[this.currPage].filter(info => info.id != item.id)
          );
          break;
        //主观题
        case 'subject':
          this.$set(
            this.subjectQues,
            this.currPage,
            this.subjectQues[this.currPage].filter(info => info.id != item.id)
          );
          //如果删除对象为多区域题目的主题目，设置下一个为主题目
          if (!item.isSplitQues) {
            for (let i = 0; i < this.subjectQues.length; i++) {
              let page = this.subjectQues[i];
              let nextQ = page.find(ques => ques.question_id == item.question_id);
              if (nextQ) {
                delete nextQ.isSplitQues;
                this.$set(
                  this.subjectQues,
                  i,
                  page
                );
                break;
              }
            }
          }
          break;
        //标题
        case 'title':
          this.$set(
            this.titlePos,
            this.currPage,
            this.titlePos[this.currPage].filter(info => info.id != item.id)
          );
          break;
        //页码标记
        case 'page':
          this.$set(
            this.pagePos,
            this.currPage,
            this.pagePos[this.currPage].filter(info => info.id != item.id)
          );
          break;
        //其他标记
        case 'others':
          if (item.item_type == 19) {
            this.$set(
              this.abCardPos,
              this.currPage,
              this.abCardPos[this.currPage].filter(info => info.id != item.id)
            );
          } else {
            this.$set(
              this.missMarkPos,
              this.currPage,
              this.missMarkPos[this.currPage].filter(info => info.id != item.id)
            );
          }
          break;
        default:
          break;
      }
      this.$forceUpdate();
    },
    /**
     * @name:清除当前步骤的框选
     */
    clearStep() {
      for (let page = 0; page < this.pageCount; page++) {
        switch (this.currentStep) {
          // 定位点
          case 'anchordot':
            this.pointList[page][0].pos_list = [];
            break;
          //考号
          case 'number':
            this.examNoList[page] = [];
            this.barcodeExamNoList[page] = [];
            break;
          //客观题目
          case 'object':
            this.$set(
              this.objectQues,
              page,
              this.objectQues[page].filter(
                info => info.item_type != this.steps[this.currentStep].item_type
              )
            );
            break;
          //主观题目
          case 'subject':
            this.$set(
              this.subjectQues,
              page,
              this.subjectQues[page].filter(
                info => info.item_type != this.steps[this.currentStep].item_type
              )
            );
            break;
          //标题
          case 'title':
            this.$set(
              this.titlePos,
              page,
              this.titlePos[page].filter(
                info => info.item_type != this.steps[this.currentStep].item_type
              )
            );
            break;
          //页码标记
          case 'page':
            this.$set(
              this.pagePos,
              page,
              this.pagePos[page].filter(
                info => info.item_type != this.steps[this.currentStep].item_type
              )
            );
            break;
          //缺考
          case 'others':
            this.$set(
              this.missMarkPos,
              page,
              this.missMarkPos[page].filter(
                info => info.item_type != this.steps[this.currentStep].item_type
              )
            );
            this.$set(
              this.abCardPos,
              page,
              this.abCardPos[page].filter(
                info => info.item_type != this.steps[this.currentStep].item_type
              )
            );
            break;
          default:
            break;
        }
      }
      this.$forceUpdate();
    },
    /**
     * 清空所有框
     */
    clearAll() {
      for (let page = 0; page < this.pageCount; page++) {
        if (this.pointList[page].length == 0) {
          this.pointList[page].push({
            id: generateUUID(),
            item_type: 10,
            pos_list: [],
          });
        } else {
          this.pointList[page][0].pos_list = [];
        }

        this.$set(this.examNoList, page, []);
        this.$set(this.barcodeExamNoList, page, []);
        this.$set(this.objectQues, page, []);
        this.$set(this.subjectQues, page, []);
        this.$set(this.titlePos, page, []);
        this.$set(this.pagePos, page, []);
        this.$set(this.missMarkPos, page, []);
        this.$set(this.abCardPos, page, []);
        // this.examNoList[page] = [];
        // this.barcodeExamNoList[page] = [];
        // this.objectQues[page] = [];
        // this.subjectQues[page] = [];
        // this.titlePos[page] = [];
        // this.missMarkPos[page] = [];
      }
      this.$forceUpdate();
    },
    /*
     * @name: 编辑画框
     */
    editPoint(item, index) {
      item.isEdit = true;
      let list = [];
      switch (this.currentStep) {
        // 定位点
        case 'anchordot':
          list = this.pointList;
          break;
        //考号
        case 'number':
          list = item.item_type == 6 ? this.barcodeExamNoList : this.examNoList;
          break;
        //客观题目
        case 'object':
          list = this.objectQues;
          break;
        //主观题目
        case 'subject':
          list = this.subjectQues;
          break;
        //标题
        case 'title':
          list = this.titlePos;
          break;
        //页码标记
        case 'page':
          list = this.pagePos;
          break;
        case 'others':
          list = item.item_type == 19 ? this.abCardPos : this.missMarkPos;
          break;
        default:
          break;
      }
      this.dragBox = list[this.currPage].filter(info => {
        return info.id == item.id;
      })[0];
      if (this.dragBox.pos) {
        this.dragBox.pos = this.dragBox.pos.map(item => Number(item));
      }
      if (this.dragBox.pos_list) {
        this.dragBox.index = index;
        this.dragBox.pos = this.dragBox.pos_list[index].map(item => Number(item));
      }
      console.log('dragBox', this.dragBox);
      this.$forceUpdate();
    },
    /*
     * @name: 保存画框
     */
    savePoint(item) {
      item.isEdit = false;
      this.$forceUpdate();
    },
    mousedown(e) {
      this.clearDrawBoxStyle();
      let boxRect = e.currentTarget.getBoundingClientRect();
      let targ = e.srcElement;
      if (targ.classList.contains('top')) {
        this.handType = HAND_TYPE.TOP;
      } else if (targ.classList.contains('bottom')) {
        this.handType = HAND_TYPE.BOTTOM;
      } else if (targ.classList.contains('left')) {
        this.handType = HAND_TYPE.LEFT;
      } else if (targ.classList.contains('right')) {
        this.handType = HAND_TYPE.RIGHT;
      } else if (targ.classList.contains('top-left')) {
        this.handType = HAND_TYPE.LEFT_TOP;
      } else if (targ.classList.contains('top-right')) {
        this.handType = HAND_TYPE.RIGHT_TOP;
      } else if (targ.classList.contains('bottom-left')) {
        this.handType = HAND_TYPE.LEFT_BOTTOM;
      } else if (targ.classList.contains('bottom-right')) {
        this.handType = HAND_TYPE.RIGHT_BOTTOM;
      } else if (targ.classList.contains('edit')) {
        this.handType = HAND_TYPE.MOVE;
        this.start.offsetTop = this.dragBox.pos[1] - (e.clientY - boxRect.y) / this.divScale;
        this.start.offsetLeft = this.dragBox.pos[0] - (e.clientX - boxRect.x) / this.divScale;
      } else {
        this.handType = HAND_TYPE.DRAW;
      }
      this.start.x = (e.clientX - boxRect.x) / this.divScale;
      this.start.y = (e.clientY - boxRect.y) / this.divScale;
      this.isDrag = true;
    },
    mousemove(e) {
      if (!this.isDrag) return;
      let boxRect = e.currentTarget.getBoundingClientRect();
      const nowX = (e.clientX - boxRect.x) / this.divScale,
        nowY = (e.clientY - boxRect.y) / this.divScale;
      const disX = nowX - this.start.x,
        disY = nowY - this.start.y;
      let moveY = 0;
      let moveX = 0;
      switch (this.handType) {
        case HAND_TYPE.TOP:
          moveY = this.dragBox.pos[1] - nowY;
          this.dragBox.pos[1] = nowY;
          this.dragBox.pos[3] = this.dragBox.pos[3] + moveY;
          break;
        case HAND_TYPE.BOTTOM:
          moveY = this.dragBox.pos[1] + this.dragBox.pos[3] - nowY;
          this.dragBox.pos[3] = this.dragBox.pos[3] - moveY;
          break;
        case HAND_TYPE.LEFT:
          moveX = this.dragBox.pos[0] - nowX;
          this.dragBox.pos[0] = nowX;
          this.dragBox.pos[2] = this.dragBox.pos[2] + moveX;
          break;
        case HAND_TYPE.RIGHT:
          moveX = this.dragBox.pos[0] + this.dragBox.pos[2] - nowX;
          this.dragBox.pos[2] = this.dragBox.pos[2] - moveX;
          break;
        case HAND_TYPE.LEFT_TOP:
          moveX = this.dragBox.pos[0] - nowX;
          moveY = this.dragBox.pos[1] - nowY;
          this.dragBox.pos[1] = nowY;
          this.dragBox.pos[3] = this.dragBox.pos[3] + moveY;
          this.dragBox.pos[0] = nowX;
          this.dragBox.pos[2] = this.dragBox.pos[2] + moveX;
          break;
        case HAND_TYPE.LEFT_BOTTOM:
          moveX = this.dragBox.pos[0] - nowX;
          moveY = this.dragBox.pos[1] + this.dragBox.pos[3] - nowY;
          this.dragBox.pos[0] = nowX;
          this.dragBox.pos[2] = this.dragBox.pos[2] + moveX;
          this.dragBox.pos[3] = this.dragBox.pos[3] - moveY;
          break;
        case HAND_TYPE.RIGHT_TOP:
          moveX = this.dragBox.pos[0] + this.dragBox.pos[2] - nowX;
          this.dragBox.pos[2] = this.dragBox.pos[2] - moveX;
          moveY = this.dragBox.pos[1] - nowY;
          this.dragBox.pos[1] = nowY;
          this.dragBox.pos[3] = this.dragBox.pos[3] + moveY;
          break;
        case HAND_TYPE.RIGHT_BOTTOM:
          moveY = this.dragBox.pos[1] + this.dragBox.pos[3] - nowY;
          this.dragBox.pos[3] = this.dragBox.pos[3] - moveY;
          moveX = this.dragBox.pos[0] + this.dragBox.pos[2] - nowX;
          this.dragBox.pos[2] = this.dragBox.pos[2] - moveX;
          break;
        case HAND_TYPE.MOVE:
          moveY = nowY + this.start.offsetTop;
          moveX = nowX + this.start.offsetLeft;
          this.dragBox.pos[1] = moveY / this.divScale;
          this.dragBox.pos[0] = moveX / this.divScale;
          break;
        case HAND_TYPE.DRAW:
          this.end.x = disX;
          this.end.y = disY;
          this.setDrawBoxStyle();
          break;
        default:
          break;
      }
      if (this.handType != HAND_TYPE.DRAW) {
        if (this.dragBox.item_type == 6) {
          //条形码
          this.$refs['barcode'].update(this.dragBox);
        } else {
          this.$refs[this.currentStep].update(this.dragBox);
        }
      }
    },
    /**
     * @name: 鼠标松开保存数据
     */
    mouseup(e) {
      this.isDrag = false;
      if (this.handType != HAND_TYPE.DRAW) return;
      if (this.end.x < 10 && this.end.y < 10) {
        this.clearDrawBoxStyle();
        return;
      }
      let boxRect = e.currentTarget.getBoundingClientRect();
      let uid = generateUUID();
      let pos = [this.start.x, this.start.y, this.end.x, this.end.y];
      try {
        if (!this.drawBox[this.currPage]) {
          this.drawBox[this.currPage] = [];
        }
        switch (this.currentStep) {
          // 定位点
          case 'anchordot':
            if (this.recognitionHandle == 'ai') {
              this.recognitionLoading = Loading.service({
                lock: true,
                text: '正在识别中',
                background: 'rgba(0, 0, 0, 0.7)',
              });
              this.partDetect(pos, this.steps[this.currentStep].item_type);
            } else {
              if (this.pointList[this.currPage].length == 0) {
                this.pointList[this.currPage].push({
                  id: uid,
                  item_type: 10,
                  pos_list: [pos],
                });
              } else {
                const points = this.pointList[this.currPage].find(info => info.item_type == 10);
                points.pos_list.push(pos);
              }
            }
            break;
          //考号
          case 'number':
            this.examNo = {
              id: uid,
              active: false,
              item_type: 2,
              one_pos: [],
              pos: [this.start.x, this.start.y, this.end.x, this.end.y],
            };
            if (this.recognitionHandle == 'ai') {
              this.recognitionLoading = Loading.service({
                lock: true,
                text: '正在识别中',
                background: 'rgba(0, 0, 0, 0.7)',
              });
              this.partDetect(this.examNo.pos, this.numberType);
            } else {
              this.isShowDrawer = true;
              if (this.numberType == '2') {
                this.$nextTick(() => {
                  this.$refs.input.focus();
                });
              }
            }
            break;
          // 客观题
          case 'object':
            this.objQues = {
              id: uid,
              question_id: uid,
              answers: '',
              active: false,
              item_type: 3,
              total_score: 5,
              miss_score: 0,
              multi: false,
              pos: [this.start.x, this.start.y, this.end.x, this.end.y],
            };
            if (this.recognitionHandle == 'ai') {
              this.isShowDrawer = true;
            } else {
              this.isShowDrawer = true;
              this.$nextTick(() => {
                this.$refs.input.focus();
              });
            }
            break;
          // 主观题
          case 'subject':
            this.subQues = {
              id: uid,
              question_id: uid,
              active: false,
              item_type: 4,
              score_list: [],
              miss_score: -1,
              total_score: 5,
              answers: '',
              pos: [this.start.x, this.start.y, this.end.x, this.end.y],
            };
            this.isShowDrawer = true;
            this.$nextTick(() => {
              this.$refs.input.focus();
            });
            break;
          case 'title':
            if (this.titlePos[this.currPage].length == 0) {
              let titleDiv = {
                id: uid,
                active: false,
                item_type: 11,
                pos: [this.start.x, this.start.y, this.end.x, this.end.y],
              };
              this.titlePos[this.currPage].push(titleDiv);
            } else {
              this.$message.warning({
                message: '本张答题卡已有标题，请先删除后再重新框选！',
                type: 'warning',
                duration: 1000,
              });
            }
            break;
          //页码标记
          case 'page':
            if (this.pagePos[this.currPage].length == 0) {
              let pageDiv = {
                id: uid,
                active: false,
                item_type: 22,
                pos: [this.start.x, this.start.y, this.end.x, this.end.y],
              };
              this.recognitionLoading = Loading.service({
                lock: true,
                text: '正在识别中',
                background: 'rgba(0, 0, 0, 0.7)',
              });
              this.partDetectPage(pageDiv.pos);
            } else {
              this.$message.warning({
                message: '本张答题卡已有页码标记，请先删除后再重新框选！',
                type: 'warning',
                duration: 1000,
              });
            }
            break;
          case 'others':
            this.others = {
              id: uid,
              active: false,
              pos: [this.start.x, this.start.y, this.end.x, this.end.y],
            };
            this.isShowDrawer = true;
            break;
          default:
            break;
        }
        this.clearDrawBoxStyle();
      } catch (e) {
        this.drawBox[this.currPage].push({
          id: uid,
          title: '',
          active: false,
          x: this.start.x,
          y: this.start.y,
          w: this.end.x,
          h: this.end.y,
          pos: [this.start.x, this.start.y, this.end.x, this.end.y],
        });
      }
      this.clearDrawBoxStyle();
    },

    async partDetectPage(pos) {
      pos = this.scalePos(pos);
      let params = {
        image_url: this.pageUrl,
        pos: pos.join(','),
        page: this.currPage + 1,
      };
      try {
        let res = await page_info_check(params);
        if (res.code == 1 && res.data.length != 0) {
          let uid = generateUUID();
          let pageDiv = {
            id: uid,
            active: false,
            item_type: 22,
            pos: this.unscalePos(res.data.pos),
          };
          this.pagePos[this.currPage].push(pageDiv);
        }
      } catch (error) {
        console.error('partDetectPage error:', error);
      } finally {
        this.recognitionLoading.close();
      }
    },

    /**
     * 识别
     */
    async partDetect(pos, type) {
      // 将位置坐标进行缩放转换
      pos = this.scalePos(pos);
      let params = {
        image_url: this.pageUrl,
        pos: pos.join(','),
        type: type,
        start_no: this.newQuesInfo.start_no,
        detect_type: this.newQuesInfo.detect_type,
        one_ques: this.newQuesInfo.mergeQues,
      };
      if (this.currentStep == 'object') {
        params.option_num = this.newQuesInfo.option_num;
      }
      try {
        let res = await partDetect(params);
        if (res.code == 1 && res.data.length != 0) {
          let uid = generateUUID();
          // 根据类型处理结果
          let item = res.data[0];
          if (type == 2) {
            //考号
            item.pos = this.unscalePos(item.pos);
            item.last_pos = this.unscalePos(item.last_pos);
            item.one_pos = this.unscalePos(item.one_pos);
            this.examNoList[this.currPage].push(item);
          } else if (type == 3) {
            //客观题
            let id = generateUUID();
            res.data.forEach(ite => {
              let id = generateUUID();
              ite.pos = this.unscalePos(ite.pos);
              // 客观题选项
              ite.option_list = ite.option_list.map(it =>
                it.map(i => ((i * 96) / 25.4 / this.hScale).toFixed(6))
              );
              ite.id = id;
              ite.question_id = id;
              this.objectQues[this.currPage].push(ite);
              // this.$set(this.objectQues,this.currPage,sortObjectArrayByField(this.objectQues[this.currPage], 'question_nos'));
            });
          } else if (type == 10) {
            //定位点
            item.pos = this.unscalePos(item.pos);
            if (!this.pointList[this.currPage]) {
              this.pointList[this.currPage] = [];
            }
            if (this.pointList[this.currPage].length == 0) {
              this.pointList[this.currPage].push({
                id: uid,
                item_type: 10,
                pos_list: [item.pos],
              });
            } else {
              const points = this.pointList[this.currPage].find(info => info.item_type == 10);
              points.pos_list.push(item.pos);
            }
          }
          else if (type == 8) {
            if (this.currPage != 0) {
              this.$message.warning({
                message: '缺考标记只能在第一页框选',
                type: 'warning',
                duration: 1000,
              });
              return;
            }
            if (this.missMarkPos[this.currPage].length == 0) {
              item.pos = this.unscalePos(item.pos);
              this.others.pos = item.pos;
              this.missMarkPos[this.currPage].push({ ...this.others, item_type: type });
            } else {
              this.$message.warning({
                message: '本张答题卡已有缺考标记，请先删除后再重新框选！',
                type: 'warning',
                duration: 1000,
              });
            }
          } else if (type == 19) {
            if (this.currPage != 0 && this.currPage != 2) {
              let message = 'AB标记只能在第一页框选';
              if(this.pageCount > 2){
                message = 'AB标记只能在第一页和第三页框选'
              }
              this.$message.warning({
                message: message,
                type: 'warning',
                duration: 1000,
              });
              return;
            }
            if (this.abCardPos[this.currPage].length < 2) {
              item.pos = this.unscalePos(item.pos);
              this.others.pos = item.pos;
              let title = this.abCardPos[this.currPage].length == 0 ? 'A' : 'B';
              this.abCardPos[this.currPage].push({ ...this.others, item_type: type, title });
            } else {
              this.$message.warning({
                message: '本张答题卡已有AB标记，请先删除后再重新框选！',
                type: 'warning',
                duration: 1000,
              });
            }
          }
        }
      } catch (error) {
        console.error('partDetect error:', error);
      } finally {
        this.recognitionLoading.close();
        this.newQuesInfo.start_no = '';
      }
    },
    cancelAddQues() {
      this.isShowDrawer = false;
    },
    /**
     * @name: 确定添加题目
     */
    sureAddQues() {
      this.isShowDrawer = false;
      if (this.currentStep == 'object') {
        if (this.recognitionHandle == 'ai') {
          this.recognitionLoading = Loading.service({
            lock: true,
            text: '正在识别中',
            background: 'rgba(0, 0, 0, 0.7)',
          });
          this.partDetect(this.objQues.pos, this.steps[this.currentStep].item_type);
        } else {
          this.objectQues[this.currPage].push({ ...this.objQues, ...this.newQuesInfo });
          // this.$set(this.objectQues,this.currPage,sortObjectArrayByField(this.objectQues[this.currPage], 'question_nos'));
        }
      }
      if (this.currentStep == 'subject') {
        this.subjectQues.forEach((page) => {
          page.forEach((ques) => {
            if (ques.question_nos == this.newSubQues.question_nos && ques.question_type == this.newSubQues.question_type) {
              //如果题目名称与题型一致，则认定是同一题多区域，增加标识
              this.newSubQues.isSplitQues = true;
              this.subQues.question_id = ques.question_id;
            }
          })
        })
        this.subjectQues[this.currPage].push({ ...this.subQues, ...this.newSubQues });
        // this.$set(this.subjectQues,this.currPage,sortObjectArrayByField(this.subjectQues[this.currPage], 'question_nos'));
      }
      if (this.currentStep == 'number') {
        if (this.numberType == '2') {
          if (this.examNoList[this.currPage].length > 0) {
            this.$message.warning({
              message: '本页已有考号框，请先删除后再框选',
              type: 'warning',
              duration: 1000,
            });
            return;
          }
          this.examNoList[this.currPage].push({ ...this.examNo, ...this.newExamNo });
        } else {
          if (this.barcodeExamNoList[this.currPage].length > 0) {
            this.$message.warning({
              message: '本页已有条形码，请先删除后再框选',
              type: 'warning',
              duration: 1000,
            });
            return;
          }
          this.examNo.item_type = 6;
          this.barcodeExamNoList[this.currPage].push({ ...this.examNo });
        }
      }
      if (this.currentStep == 'others') {
        const itemType = this.otherType == '1' ? 8 : 19;
        if (this.recognitionHandle == 'ai') {
          this.recognitionLoading = Loading.service({
            lock: true,
            text: '正在识别中',
            background: 'rgba(0, 0, 0, 0.7)',
          });
          this.partDetect(this.others.pos, itemType);
        } else {
          if (this.otherType == '1') {
            if (this.currPage != 0) {
              this.$message.warning({
                message: '缺考标记只能在第一页框选',
                type: 'warning',
                duration: 1000,
              });
              return;
            }
            if (this.missMarkPos[this.currPage].length > 0) {
              this.$message.warning({
                message: '本页已有缺考标记，请先删除后再框选',
                type: 'warning',
                duration: 1000,
              });
              return;
            }
            this.missMarkPos[this.currPage].push({ ...this.others, item_type: itemType });
          } else {
            if (this.currPage != 0 && this.currPage != 2) {
              let message = 'AB标记只能在第一页框选';
              if(this.pageCount > 2){
                message = 'AB标记只能在第一页和第三页框选'
              }
              this.$message.warning({
                message: message,
                type: 'warning',
                duration: 1000,
              });
              return;
            }
            if (this.abCardPos[this.currPage].length >= 2) {
              this.$message.warning({
                message: '本页已有AB标记，请先删除后再框选',
                type: 'warning',
                duration: 1000,
              });
              return;
            }
            let title = this.abCardPos[this.currPage].length == 0 ? 'A' : 'B';
            this.abCardPos[this.currPage].push({ ...this.others, item_type: itemType, title });
          }
        }
      }
      this.newQuesInfo.question_nos = '';
      this.newSubQues.question_nos = '';
      this.newQuesInfo.option_num = 0;
      this.newQuesInfo.mergeQues = 0;
      delete this.newSubQues.isSplitQues;
    },
    btnOperateZoom(type) {
      let oDiv = document.querySelector('.exam-pdf-container');
      let x = oDiv.clientWidth / 2;
      let y = 0;
      this.operateZoom(type, x, y);
    },
    operateZoom(type, x, y) {
      let oDiv = document.querySelector('.page-layout');
      let transf = this.getTransform(oDiv);
      if (type === 'add') {
        if (transf.multiple > 1.5) return;
        transf.multiple *= scleRatio; // 放大DELTA倍
      } else {
        if (transf.multiple < 0.3) return;
        transf.multiple /= scleRatio; // 缩小DELTA倍
      }
      this.divScale = transf.multiple;
      console.log('divScale', this.divScale);
    },
    getTransform(DOM) {
      let arr = window.getComputedStyle(DOM).transform.split(',');
      return {
        transX: isNaN(+arr[arr.length - 2]) ? 0 : +arr[arr.length - 2], // 获取translateX
        transY: isNaN(+arr[arr.length - 1].split(')')[0]) ? 0 : +arr[arr.length - 1].split(')')[0], // 获取translateX
        multiple: +arr[3], // 获取图片缩放比例
      };
    },
    /**
     * @name: 切换下一页
     */
    nextPage() {
      if (!this.checkPoints()) return false;
      if (!this.checkTitle()) return false;
      if (!this.checkPage()) return false;
      if (this.currPage + 1 >= this.pageCount) {
        const keys = Object.keys(this.steps);
        let currentIndex = keys.indexOf(this.currentStep);
        if (currentIndex < keys.length - 1) {
          this.currPage = 0;
          currentIndex++;
          this.currentStep = keys[currentIndex];
          this.changeStep(this.steps[this.currentStep])
        } else {
          alert('已经是最后一个步骤');
        }
        return;
      }
      this.currPage++;
    },
    /**
     * @name: 切换指定页
     */
    changePage(page) {
      // if (!this.checkPoints()) return false;
      this.currPage = page - 1;
    },
    /**
     * 检查当前页面是否满足至少选择三个定位点的条件
     * @returns {boolean} 是否满足条件
     */
    checkPoints() {
      if (
        (this.currentStep == 'anchordot' && this.pointList[this.currPage].length == 0) ||
        (this.pointList[this.currPage] &&
          this.pointList[this.currPage][0] &&
          this.pointList[this.currPage][0].pos_list.length != 4)
      ) {
        this.$message({
          message: '请在本页框选四个定位点!',
          type: 'warning',
          duration: 1000,
        });
        return false;
      }
      return true;
    },
    checkTitle() {
      if (this.currentStep == 'title' && this.titlePos[this.currPage].length == 0) {
        this.$message({
          message: '请在本页框选标题!',
          type: 'warning',
          duration: 1000,
        });
        return false;
      }
      return true;
    },
    checkPage() {
      // 只有当前步骤为页码标记时才进行检查
      if (this.currentStep !== 'page' || this.pageCount < 2) {
        return true;
      }

      // 检查是否为奇数页，且当前页或前一页没有页码标记
      const isOddPage = this.currPage % 2 === 1;
      const currentPageEmpty = !this.pagePos[this.currPage]?.length;
      const previousPageEmpty = !this.pagePos[this.currPage - 1]?.length;

      if (isOddPage && currentPageEmpty && previousPageEmpty) {
        this.$message({
          message: '请在本页框选页码标记!',
          type: 'warning',
          duration: 1000
        });
        return false;
      }

      return true;
    },
    dragObjectEnd(data) {
      console.log(this.objectQues);
    },
    dragSubjectEnd(data) {
      console.log(this.subjectQues);
    },
    checkSubmit() { },
    /**
     * 关闭弹窗
     */
    closeCardInfo() {
      this.isShowCardInfo = false;
    },
    /**
     * 确定保存数据
     */
    confirmCardInfo() {
      this.saveData();
    },
    /**
     * 打开弹窗
     */
    openCardInfoDialog(isUpdate) {
      this.isUpdateTeamInfo = isUpdate;
      if (this.currentStep == 'others') {
        this.isShowCardInfo = true;
      } else {
        this.isShowCardInfo = false;
        this.saveData();
      }
    },
    scalePos(pos) {
      return pos?.map(item => (((item * 25.4) / 96) * this.hScale).toFixed(6));
    },
    unscalePos(pos) {
      return pos?.map(item => ((item * 96) / 25.4 / this.hScale).toFixed(6));
    },
    abPointsMerge(points) {
      points.forEach(point => {
        if (point.length) {
          let options = [];
          let a = point[0];
          if (a) {
            options.push(a.pos)
          }
          let b = point[1];
          if (b) {
            options.push(b.pos)
            point.splice(1, 1)
          } else {
            b = a;
          }
          point[0].item_type = 19;
          point[0].pos = [Number(a.pos[0]), Number(a.pos[1]), Number(b.pos[0]) + Number(b.pos[2]) - Number(a.pos[0]), Number(b.pos[1]) + Number(b.pos[3]) - Number(a.pos[1])]
          point[0].option_list = options;
        }
      });
      return points;
      if (!points.length || !points[0][0]?.length) {
        return [[], []];
      }
      let a = points[0][0];
      let b = points[0][1];
      let options = [a.pos, b.pos]
      let point = [[{
        item_type: 19,
        option_list: options,
        pos: [Number(a.pos[0]), Number(a.pos[1]), Number(b.pos[0]) + Number(b.pos[2]), Number(b.pos[1]) + Number(b.pos[3])]
      }]];
      return point;
    },
    abPointsSplit(points) {
      if (!points?.length) {
        return [];
      }

      const abPoint = points[0];
      const titles = ['A', 'B'];

      return abPoint.option_list.map((pos, index) => ({
        item_type: 19,
        pos,
        id: generateUUID(),
        active: false,
        title: titles[index]
      }));
    },
    /**
     * @name 保存框选数据
     */
    async saveData() {
      let pointData = [];
      // 深拷贝相关数组
      const pointListCopy = this.$deepClone(this.pointList);
      const examNoListCopy = this.$deepClone(this.examNoList);
      const barcodeExamNoListCopy = this.$deepClone(this.barcodeExamNoList);
      const titlePosCopy = this.$deepClone(this.titlePos);
      const pagePosCopy = this.$deepClone(this.pagePos);
      const missMarkPosCopy = this.$deepClone(this.missMarkPos);
      const abCardPosCopy = this.abPointsMerge(this.$deepClone(this.abCardPos));
      const objectQuesCopy = this.$deepClone(this.objectQues);
      const subjectQuesCopy = this.$deepClone(this.subjectQues);
      const qrCodePosCopy = this.$deepClone(this.qrCodePos);
      for (let i = 0; i < this.pageCount; i++) {
        if (pointListCopy[i].length != 0) {
          pointListCopy[i][0].pos_list = pointListCopy[i][0].pos_list.map(pos =>
            this.scalePos(pos)
          );
          delete pointListCopy[i][0].pos
        }
        if (examNoListCopy[i].length != 0) {
          examNoListCopy[i][0].pos = this.scalePos(examNoListCopy[i][0].pos);
          examNoListCopy[i][0].last_pos = this.scalePos(examNoListCopy[i][0].last_pos);
          examNoListCopy[i][0].one_pos = this.scalePos(examNoListCopy[i][0].one_pos);
        }
        if (barcodeExamNoListCopy[i].length != 0) {
          barcodeExamNoListCopy[i][0].pos = this.scalePos(barcodeExamNoListCopy[i][0].pos);
        }
        if (titlePosCopy[i].length != 0) {
          titlePosCopy[i][0].pos = this.scalePos(titlePosCopy[i][0].pos);
        }
        if (pagePosCopy[i].length != 0) {
          pagePosCopy[i][0].pos = this.scalePos(pagePosCopy[i][0].pos);
        }
        if (missMarkPosCopy[i].length != 0) {
          missMarkPosCopy[i][0].pos = this.scalePos(missMarkPosCopy[i][0].pos);
        }
        if (abCardPosCopy[i].length != 0) {
          abCardPosCopy[i].forEach(item => {
            item.pos = this.scalePos(item.pos);
            item.option_list = item.option_list.map(option => {
              return this.scalePos(option);
            });
          });
        }
        if (objectQuesCopy[i].length != 0) {
          objectQuesCopy[i].forEach(item => {
            item.pos = this.scalePos(item.pos);
            item.option_list = item.option_list.map(option => {
              return this.scalePos(option);
            });
          });
        }
        if (subjectQuesCopy[i].length != 0) {
          subjectQuesCopy[i].forEach(item => {
            item.pos = this.scalePos(item.pos);
          });
        }
        if (qrCodePosCopy[i].length != 0) {
          qrCodePosCopy[i].forEach(item => {
            item.pos = this.scalePos(item.pos);
          });
        }
        let page = [];
        page = [
          ...this.originImage[i],
          ...pointListCopy[i],
          ...examNoListCopy[i],
          ...barcodeExamNoListCopy[i],
          ...titlePosCopy[i],
          ...pagePosCopy[i],
          ...missMarkPosCopy[i],
          ...abCardPosCopy[i],
          ...objectQuesCopy[i],
          ...subjectQuesCopy[i],
          ...qrCodePosCopy[i],
        ];
        pointData.push(page);
      }
      await this.makeQuesInfo();
      let points = { ...this.points };
      const quesNoMap = new Map();
      this.quesInfo.forEach(big => {
        big.data.forEach(small => {
          quesNoMap.set(small.id, small.quesNo);
        });
      });
      for (let i = 0; i < pointData.length; i++) {
        pointData[i].forEach(point => {
          const quesNo = quesNoMap.get(point.id);
          if (quesNo !== undefined) {
            point.question_no = quesNo;
          }
        });
      }
      points.pages = pointData;
      let cardInfo = {
        points: points,
        cardType: 3, //三方卡
        pageLayout: this.pageLayout,
      };
      let params = {
        paperNo: this.paperNo,
        cardInfo: cardInfo,
        paperNum: this.pageCount,
        quesInfo: this.quesInfo,
        abCardType: this.abCardType,
        source: 1,
      };
      if (this.isUpdateTeamInfo) {
        params.updateTeamInfo = 1;
      }
      console.log('params', params);
      let result = await updateViewPaper({ json: JSON.stringify(params) });
      if (result && result.code == 1) {
        this.$message({
          message: '修改成功!',
          type: 'success',
          duration: 1000,
        });
        if (this.currentStep == 'others' && !this.isUpdateTeamInfo) {
          await this.completeCard();
        }
        if (this.isUpdateTeamInfo) {
          this.refreshPaperPoints();
        }
      } else {
        this.$message({
          message: result.msg,
          type: 'error',
          duration: 1000,
        });
      }
    },
    /**
     * @name：完成制卡
     */
    async completeCard() {
      //完成制卡
      const res = await overCard({
        schoolId: this.schoolId,
        tbId: this.tbId,
        optUserId: this.$sessionSave.get('loginInfo')?.id || '',
        personalBookId: this.personalBookId,
        abCardSheetType: this.abCardSheetType
      });
      if (res.code == 1) {
        if (this.examId != '') {
          let params = {
            schoolId: this.schoolId,
            examId: this.examId,
            tbId: "",
            paperNo: "",
            personalBookId: this.personalBookId,
            relateCardType: this.relateCardType
          };
          if (this.abCardSheetType == 1) {
            params.tbId1 = this.tbId;
            params.paperNo1 = this.paperNo;
          } else {
            params.tbId = this.tbId;
            params.paperNo = this.paperNo;
          }
          //关联题卡
          await relateAnswerCard(params)
            .then(res => {
              this.$router.go(-1);
            })
            .catch(err => {
            });
        } else {
          this.$router.go(-1);
        }
      }
    },
    refreshPaperPoints() {
      const params = {
        paper_no: this.paperNo,
        refresh_points: true
      };
      refreshPaperInfo(params);
    },
    /**
     * @name: 组装题目数据
     */
    async makeQuesInfo() {
      this.quesInfo = [];
      this.isTypeExit = false;
      this.totalTypes = [];
      //题型集合
      for (let i = 0; i < this.pageCount; i++) {
        await this.foreachQues(this.objectQues[i]);
        await this.foreachQues(this.subjectQues[i]);
      }
      // 初始化当前quesNo
      let currentQuesNo = 1;
      this.quesInfo.forEach(item => {
        item.data.forEach(ite => {
          ite.quesNo = currentQuesNo;
          currentQuesNo++;
        });
      });
      console.log('quesInfo', this.quesInfo);
    },
    getQuesNo() {
      const firstOccurrences = new Map();
      this.quesInfo.forEach(item => {
        item.data.forEach(ite => {
          if (!firstOccurrences.has(ite.quesNos)) {
            firstOccurrences.set(ite.quesNos, { id: ite.id, quesNo: ite.quesNo });
          }
        });
      });
      let currentQuesNo = 1;
      // 更新quesNo和id
      this.quesInfo.forEach(item => {
        item.data.forEach(ite => {
          const { id, quesNo } = firstOccurrences.get(ite.quesNos) || {};
          ite.id = id;
          ite.quesNo = quesNo;
        });
      });
      // 将quesNo调整为连续的序列
      let uniqueQuesNos = new Set();
      this.quesInfo.forEach(item => {
        item.data.forEach(ite => {
          if (uniqueQuesNos.has(ite.quesNo)) {
            // 如果quesNo已存在，找到下一个可用的quesNo
            while (uniqueQuesNos.has(currentQuesNo)) {
              currentQuesNo++;
            }
            ite.quesNo = currentQuesNo++;
          } else {
            // 如果quesNo未出现过，添加到集合中
            uniqueQuesNos.add(ite.quesNo);
          }
        });
      });
      // 最后，将当前QuesNo还原为1，重新分配
      currentQuesNo = 1;
      this.quesInfo.forEach(item => {
        item.data.forEach(ite => {
          if (uniqueQuesNos.has(ite.quesNo)) {
            ite.quesNo = currentQuesNo++;
          }
        });
      });
    },
    /**
     * @name:遍历题目
     */
    foreachQues(data) {
      data.forEach(item => {
        if (item.isSplitQues)
          return;
        this.$set(item, 'quesTypeName', this.$getQuesType(Number(item.question_type)));
        this.totalTypes = this.quesInfo.map(total => {
          return total.typeId;
        });
        this.isTypeExit = this.totalTypes.indexOf(item.question_type) >= 0;
        // 添加题目
        let curObj = {
          id: item.question_id,
          name: 1,
          type: item.quesTypeName,
          typeId: Number(item.question_type),
          score: 0,
          answer: [''],
          quesNos: item.question_nos,
        };
        //客观题选项数
        if (item.question_type == 1 || item.question_type == 8) {
          curObj.optionCount = item.option_list.length;
        }
        if (!this.isTypeExit) {
          this.quesInfo.push({
            id: generateUUID(),
            data: [curObj],
            name: item.quesTypeName,
            type: item.quesTypeName,
            typeId: Number(item.question_type),
          });
        } else {
          // 当前题型已经存在，往里层data push数据
          this.quesInfo.forEach(sub => {
            if (sub.typeId == item.question_type) {
              curObj.name = sub.data.length + 1;
              sub.data.push(curObj);
            }
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.exam-mk-container {
  padding: 0 !important;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
}

.ques-setting-container {
  height: calc(100% - 40px);

  .ques-setting-top {
    display: flex;
    align-items: center;
    height: 40px;
    font-size: 16px;
    background-color: #fff;

    .top-back {
      margin-right: 15px;

      .back-word {
        cursor: pointer;
      }
    }

    .top-steps {
      display: flex;
      margin: 0 10px;
      cursor: pointer;
      align-items: center;

      .step-number {
        font-size: 14px;
        border-radius: 50%;
        border: 1px solid #666;
        color: #666;
        background: #fff;
        padding: 0px 4px;
        margin-right: 5px;

        &.active {
          font-weight: bold;
          color: #409eff;
          border: 1px solid #409eff;
        }
      }

      .step-tag {
        margin-left: 20px;
        color: #666 !important;

        &::before {
          content: '>';
        }
      }

      &.active {
        font-weight: bold;
        color: #409eff;
      }
    }

    .top-clear {
      margin-left: 15%;
      color: red;
      cursor: pointer;
    }
  }

  .last-step {
    &::before {
      content: '' !important;
    }
  }

  .ques-setting-left {
    position: relative;
    display: inline-block;
    vertical-align: top;
    width: 80%;
    height: 100%;

    .ques-setting-left-top {
      height: 28px;
      margin-left: 15px;
      display: flex;
      align-items: center;

      .top-tip {
        display: inline-block;
      }

      .step-clear {
        margin-left: 20px;
        color: red;
        cursor: pointer;
      }

      .recognition-mode {
        margin-left: 10px;
      }

      .step-zoom {
        display: flex;
        margin-left: 30px;
        font-size: 18px;

        .add-zoom,
        .sub-zoom {
          padding: 0 5px;
          cursor: pointer;
        }
      }

      .top-page {
        position: absolute;
        right: 0;
        display: inline-block;

        div {
          display: inline-block;
        }

        .page {
          width: 30px;
          height: 28px;
          margin-left: 2px;
          line-height: 24px;
          border: 1px solid;
          border-radius: 5px;
          text-align: center;

          &.active {
            color: #fff;
            border: 1px solid #5f9eff;
            background-color: #5f9eff;
          }
        }
      }
    }

    .exam-pdf-container {
      width: 100%;
      height: calc(100% - 50px);
      overflow: auto;
      background: #dcdcdc;

      .page-layout {
        position: relative;
        height: calc(100%);
        width: 100%;
        border: 1px solid #cccccc;
        overflow: hidden;
      }

      .a3 {
        margin: 0 auto;
        // width: 3308px;
        // height: 2339px;
        width: fit-content;
        height: 223.4mm;
        position: relative;
        transform-origin: top left;
        transition: transform 0.3s ease;
      }

      .a4 {
        margin: 0 auto;
        width: 210mm;
        height: 297mm;
        position: relative;
      }

      .img {
        height: 100%;
        -webkit-user-drag: none;
      }

      .ques_box {
        position: absolute;
        border: 0.6mm solid #03c617;
        font-size: 22px;
        color: #03c617;

        &.draw {
          border: 0.6mm solid #ff6600;
          color: #ff6600;
          background-color: rgba(245, 201, 172, 0.4);
        }

        &.active {
          border: 1mm solid #409eff;
          color: #409eff;

          &:hover {

            .edit-container,
            .ques-edit-wrapper {
              display: flex;
            }
          }

          .option_box {
            background-color: rgba(167, 204, 241, 0.5);
          }
        }

        .option_box {
          position: fixed;
          font-size: 22px;
          background-color: rgba(91, 242, 116, 0.4);
        }

        .notop {
          position: absolute;
          top: -5px;
          left: 2px;
          min-width: 55px;
          font-size: 14px;

          &.point {
            top: -20px;
            left: 0px;
          }
        }

        .nobottom {
          position: absolute;
          bottom: 2px;
          right: 2px;
        }

        .resize-dot {
          position: absolute;
          width: 8px;
          height: 8px;
          border: 1px solid;
          background: #fff;
          box-sizing: border-box;

          &.top {
            cursor: n-resize;
            top: -4px;
            left: 50%;
            margin-left: -4px;
          }

          &.bottom {
            cursor: s-resize;
            bottom: -4px;
            left: 50%;
            margin-left: -4px;
          }

          &.left {
            top: 50%;
            margin-top: -4px;
            cursor: w-resize;
            left: -4px;
          }

          &.right {
            cursor: e-resize;
            right: -4px;
            top: 50%;
            margin-top: -4px;
          }

          &.top-left {
            margin-top: -4px;
            cursor: nw-resize;
            margin-left: -4px;
          }

          &.top-right {
            cursor: ne-resize;
            right: 0;
            margin-right: -4px;
            margin-top: -4px;
          }

          &.bottom-left {
            cursor: sw-resize;
            bottom: 0;
            margin-left: -4px;
            margin-bottom: -4px;
          }

          &.bottom-right {
            cursor: se-resize;
            right: -4px;
            bottom: 0;
            margin-bottom: -4px;
          }
        }

        .edit-container {
          display: none;
        }

        .ques-edit-wrapper {
          display: none;
          background: rgba(0, 0, 0, 0.5);
          font-size: 12px;
          z-index: 9;
          position: absolute;
          right: 5px;
          bottom: -8px;
          justify-content: space-around;
          color: #fff;
        }

        .ques-cancel,
        .ques-save,
        .ques-edit {
          text-align: center;
          // width: 30px;
          padding: 0 2px;

          &:hover {
            background: rgba(0, 0, 0, 0.5);
            cursor: pointer;
          }
        }
      }

      .pos_box {
        position: fixed;
        border: 0.6mm solid #03c617;
        font-size: 22px;
        color: #03c617;
        background-color: rgba(91, 242, 116, 0.4);
      }
    }
  }

  .ques-setting-right {
    display: inline-block;
    position: relative;
    height: calc(100% - 20px);
    width: 19%;
    margin-left: 1%;
    background: #fff;
    padding: 5px;
  }
}

.avatar {
  position: relative;
  transform: translate(0px, 0px);
  transform-origin: 0 0;
}

.page-points-wrapper {
  position: absolute;
  left: 0;
  top: 0;
  padding: 100px;
  transform-origin: 0 0;
}

.drawer-content {
  display: flex;
  flex-direction: column;
  margin: 0 30px;
  height: 100%;
}

.demo-drawer__footer {
  display: flex;

  .el-drawer__footer-button {
    flex: 1;
  }
}

.none-select {
  user-select: none;
  /* iOS Safari */
  -webkit-user-select: none;
  /* Chrome/Safari/Opera */
  -khtml-user-select: none;
  /* Konqueror */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
}
</style>
<style lang=scss>
.ques-setting-container .ques-setting-left .el-dropdown-link {
  font-size: 22px;
  color: #03c617;
}

.ques-setting-container .ques-setting-left .active .el-dropdown-link {
  font-size: 22px;
  color: #ff6600;
}

.custom-drop-menu {
  max-height: 400px;
  overflow-y: auto;
}

.right-ques-list {
  .el-collapse-item__header {
    font-size: 18px;
    font-weight: bolder;
  }

  .el-collapse-item__content {
    color: #606266;
  }
}
</style>

<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-08-28 14:59:21
 * @LastEditors: 小圆
-->
<template>
  <div class="offline-home-work">
    <div class="offline-home-work-top">
      <div class="offline-home-work-module question-bank">
        <div class="question-bank-title">
          我的题库
          <el-button class="title-btn create-work-card-btn" round icon="el-icon-plus" size="small" @click="goCard">
            制作答题卡
          </el-button>
        </div>
        <div class="question-bank-list" :class="{ long: !bookModuleEnabled }">
          <div class="question-bank-item school-ques-card" @click="goSchoolQues"></div>
          <div class="question-bank-item xueke-card" @click="goXueke"></div>
          <div class="question-bank-item paper-card" @click="goPaper"></div>
          <div class="question-bank-item wrong-ques-card" @click="goWrongQues"></div>
        </div>
      </div>

      <div class="offline-home-work-module book" v-if="bookModuleEnabled">
        <div class="book-title">
          我的教辅
          <el-button class="title-btn book-switch-btn" round size="small" @click="goBook" v-if="books.length">
            <img src="@/assets/desktop/hw_toggle_icon.png" class="btn-icon" />
            切换</el-button
          >
        </div>
        <div class="book-module-content">
          <div v-if="books.length === 0" class="empty-book">
            <img class="empty-book-img" src="@/assets/no-res.png" />
            <p class="empty-book-title">
              暂未添加教辅，<span class="add-book-btn" @click="showAddBook">立即添加 > </span>
            </p>
          </div>

          <div class="book-item" v-if="currentBook" @click="goBookReport(currentBook)">
            <div class="book-cover">
              <el-image :src="currentBook.cover" fit="cover" />
            </div>
            <div class="book-info">
              <div class="book-name" :title="currentBook.bookName">{{ currentBook.bookName }}</div>
              <div class="book-meta">
                <span class="meta-item" v-if="currentBook.subjectName">{{ currentBook.subjectName }}</span>
                <span class="meta-item" v-if="currentBook.gradeName">{{ currentBook.gradeName }}</span>
                <span class="meta-item" v-if="currentBook.volumeName">{{ currentBook.volumeName }}</span>
                <span class="meta-item" v-if="currentBook.year">{{ currentBook.year }}</span>
              </div>
              <div class="book-action">
                <div class="book-question-count">
                  <img src="@/assets/desktop/hw_count_icon.png" alt="" /> 题目数：{{ currentBook.quesCount }}
                </div>
                <el-button class="book-action-btn" type="primary" size="small" @click="goBookReport(currentBook)"
                  >查看报告</el-button
                >
              </div>
            </div>
            <!-- <i title="删除" class="book-delete delect-icon el-icon-error"></i> -->
          </div>
        </div>
      </div>
    </div>
    <offline-exam-list class="offline-home-work-module" :defaultCategory="[12, 1]"></offline-exam-list>
    <BookAddDialog
      ref="bookAddDialog"
      v-if="addBookDialogVisible"
      @add="refreshBookList"
      @closed="addBookDialogVisible = false"
    ></BookAddDialog>
  </div>
</template>

<script lang="ts">
import { Component, Ref, Vue } from 'vue-property-decorator';
import OfflineExamList from './components/offlineExamList.vue';
import { getPublicConfigBySchoolInfo } from '@/service/api';
import { getShelfBooksAPI } from '@/service/pexam';
import BookAddDialog from '@/components/BookAddDialog.vue';
import { openRoute } from './desktopUtils';
import NoData from '@/components/noData.vue';

interface Book {
  editionCode: string;
  bookCode: string;
  year: string;
  isonshelf: boolean;
  quesCount: number;
  bookIsbn: string;
  volumeCode: string;
  subjectId: number;
  cover: string;
  schoolId: string;
  rowNum: number;
  publishername: string;
  id: string;
  subjectName: string;
  phase: string;
  gradeName: string;
  bookAlias: string;
  volumename: string;
  editionName: string;
  ebookId: string;
  bookName: string;
  bookId: number;
  subId: string;
  createTime: Date;
  dateUpdate: Date;
  volumeName: string;
  gradeCode: string;
  volumecode: string;
  subjectCode: string;
}

interface List {
  bookCode: string;
  gradeId: string;
  userId: string;
  subjectId: string;
  bookId: string;
  dateUpdated: Date;
  deleted: number;
  dateCreated: Date;
  schoolId: string;
  id: string;
}

@Component({
  components: {
    OfflineExamList,
    BookAddDialog,
    NoData,
  },
})
export default class OfflineHomeWork extends Vue {
  @Ref('bookAddDialog') bookAddDialog: BookAddDialog;
  books: Book[] = [];
  list: List[] = [];
  // 当前教辅
  currentBook = null;
  // 添加教辅弹窗
  addBookDialogVisible = false;
  // 教辅模块是否启用
  bookModuleEnabled = false;
  // 校本题库模块是否启用
  schoolQuesModuleEnabled = false;
  // 学科网模块是否启用
  xuekeModuleEnabled = false;

  async mounted() {
    await this.getSchoolConfig();
    if (this.bookModuleEnabled) {
      this.getShelfBooks();
    }
  }

  // 获取学校中控配置
  async getSchoolConfig() {
    let code = [
      '143', // 我的教辅
      '009', // 校本题库, 班级错题， 我的卷库
      '034', // 学科网
    ];
    try {
      const res = await getPublicConfigBySchoolInfo({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        userId: this.$sessionSave.get('loginInfo').id,
        dictCode: code.join(','),
      });
      res.data.forEach(item => {
        // 教辅
        if (item.dictCode == '143') {
          this.bookModuleEnabled = item.state == '1';
        }
        if (item.dictCode == '009') {
          this.schoolQuesModuleEnabled = item.state == '1';
        }
        if (item.dictCode == '034') {
          this.xuekeModuleEnabled = item.state == '1';
        }
      });
    } catch (error) {
      console.error(error);
    }
  }

  // 获取我的教辅
  async getShelfBooks() {
    const res = await getShelfBooksAPI({
      userId: this.$sessionSave.get('loginInfo').id,
    });
    const books = res.data.books || [];
    const list = res.data.list || [];

    this.books = books;
    this.list = list;

    this.currentBook = books[0];
  }

  // 刷新列表
  refreshBookList() {
    this.books = [];
    this.list = [];
    this.getShelfBooks();
  }

  // 添加教辅
  showAddBook() {
    this.addBookDialogVisible = true;
    this.$nextTick(() => {
      this.bookAddDialog.addIds = this.books.map(t => t.bookId);
    });
  }

  goSchoolQues() {
    if (this.schoolQuesModuleEnabled) {
      openRoute('/home/<USER>/school', {
        'paper-tab': 'school',
      });
    } else {
      this.$message.warning('尚未开通此服务，请联系商务开通');
    }
  }

  goXueke() {
    if (this.xuekeModuleEnabled) {
      openRoute('/home/<USER>');
    } else {
      this.$message.warning('尚未开通此服务，请联系商务开通');
    }
  }

  goPaper() {
    if (this.schoolQuesModuleEnabled) {
      openRoute('/home/<USER>/paper', {
        'paper-tab': 'paper',
      });
    } else {
      this.$message.warning('尚未开通此服务，请联系商务开通');
    }
  }

  goWrongQues() {
    if (this.schoolQuesModuleEnabled) {
      openRoute('/home/<USER>/wrongques', {
        'paper-tab': 'wrongques',
      });
    } else {
      this.$message.warning('尚未开通此服务，请联系商务开通');
    }
  }

  goCard() {
    openRoute('/home/<USER>', {
      pageType: 'cardTool',
    });
  }

  goBook() {
    openRoute('/home/<USER>');
  }

  goBookReport(item: Book) {
    this.$router.push({
      path: '/home/<USER>',
      query: { bookCode: item.bookCode },
    });
  }
}
</script>

<style lang="scss" scoped>
.offline-home-work {
}

.offline-home-work-module {
  padding: 20px;
  background-color: #fff;
  box-shadow: 0px 4px 4px 0px rgba(217, 222, 232, 0.25);
  border-radius: 12px;

  &:last-of-type {
    margin-bottom: 0;
  }
}

.offline-home-work-top {
  display: flex;
  margin-bottom: 10px;
}

.question-bank {
  flex: 1;
  padding: 20px;
  padding-top: 15px;
  margin-right: 10px;

  .question-bank-title {
    position: relative;
    font-weight: bold;
    font-size: 20px;
    color: #000000;
    margin-bottom: 15px;

    .create-work-card-btn {
      position: absolute;
      right: 0;
      bottom: 0;
    }
  }

  .question-bank-list {
    display: flex;
    flex-wrap: wrap;

    .question-bank-item {
      flex: 1;
      max-width: 187px;
      min-width: 150px;
      height: 130px;
      text-align: center;
      background-color: #fff;
      margin-right: 20px;

      border-radius: 12px;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      cursor: pointer;

      &:hover {
        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.1);
      }

      &:last-child {
        margin-right: 0;
      }

      &.school-ques-card {
        background-image: url('~@/assets/desktop/hw_schoolques_banner.png');
      }
      &.xueke-card {
        background-image: url('~@/assets/desktop/hw_xueke_banner.png');
      }

      &.paper-card {
        background-image: url('~@/assets/desktop/hw_paper_banner.png');
      }

      &.wrong-ques-card {
        background-image: url('~@/assets/desktop/hw_wrongques_banner.png');
      }
    }

    &.long {
      .question-bank-item {
        max-width: 315px;
        &.school-ques-card {
          background-image: url('~@/assets/desktop/hw_schoolques_long_banner.png');
        }
        &.xueke-card {
          background-image: url('~@/assets/desktop/hw_xueke_long_banner.png');
        }

        &.paper-card {
          background-image: url('~@/assets/desktop/hw_paper_long_banner.png');
        }

        &.wrong-ques-card {
          background-image: url('~@/assets/desktop/hw_wrongques_long_banner.png');
        }
      }
    }
  }
}

.book {
  display: flex;
  flex-direction: column;
  flex: none;
  width: 500px;
  padding: 20px;
  padding-top: 15px;

  .book-title {
    flex: none;
    position: relative;
    margin-bottom: 15px;
    font-weight: bold;
    font-size: 20px;
    color: #000000;
  }

  .book-module-content {
    flex: 1;
    position: relative;
  }

  .book-switch-btn {
    position: absolute;
    right: 0;
    bottom: 0;
  }
}

.title-btn {
  background: rgba(186, 214, 255, 0.3);
  border-radius: 18px;
  border: 1px solid rgba(30, 111, 254, 0.6);
  font-weight: 400;
  font-size: 14px;
  color: #2574ff;

  &:hover {
    background: rgba(186, 214, 255, 0.5);
  }
}

.btn-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
  vertical-align: bottom;
}

.book-item {
  position: relative;
  display: flex;
  height: 132px;
  background-color: #fff;
  border-radius: 8px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;

  &:hover {
  }

  &.selected {
    border: 2px solid #409eff;
  }
  .book-cover {
    width: 93px;
    height: 100%;
    overflow: hidden;
    border-radius: 4px;

    :deep(.el-image) {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .book-info {
    position: relative;
    flex: 1;
    margin-left: 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .book-action {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .book-name {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    font-weight: bold;
    font-size: 18px;
    color: #303233;
    overflow: hidden;
    line-height: 1.4;
  }

  .book-meta {
    .meta-item {
      display: inline-block;
      padding: 4px 14px;
      margin-right: 8px;
      margin-bottom: 8px;
      background: #f0f5f9;
      color: #606266;
      font-weight: 400;
      color: #606266;
      border-radius: 4px;
      font-size: 12px;
    }
  }

  .book-question-count {
    font-size: 14px;
    color: #666666;

    img {
      width: 18px;
      height: 18px;
      vertical-align: bottom;
    }
  }

  .book-action-btn {
    padding: 0;
    width: 86px;
    height: 30px;
    font-size: 15px;
    line-height: 30px;
    text-align: center;
    background: #2574ff;
    border-radius: 15px;
    border: 0;

    &:hover {
      background: rgba(#2574ff, 0.8);
    }
  }

  .book-delete {
    display: none;
    position: absolute;
    right: 0;
    top: 0;
  }

  .delect-icon {
    font-size: 24px;
    color: #f56c6c;
    cursor: pointer;
  }
}

.empty-book {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .empty-book-img {
    position: absolute;
    top: -73px;
    height: 180px;
    pointer-events: none;
  }

  .empty-book-title {
    position: absolute;
    bottom: 10px;
    font-size: 14px;
    color: #303133;
  }

  .empty-book-action {
    margin-top: 12px;
  }

  .add-book-btn {
    color: #2574ff;
    cursor: pointer;

    &:hover {
      color: rgba(#2574ff, 0.8);
    }
  }
}
</style>

<template>
  <div class="score-table">
    <div class="tip-text" v-if="isShowTip && tableData.length">注：红色表示低于年级均分</div>
    <el-table
      :data="listData"
      class="score-table-container"
      :headerCellStyle="{
        fontSize: '14px',
        color: '#3F4A54',
        backgroundColor: '#f5f7fa',
      }"
      border
      ref="tableRef"
      v-if="tableData.length"
      @mousedown.native="mouseDownHandler"
      @mouseup.native="mouseUpHandler"
      @mousemove.native="mouseMoveHandler"
    >
      <el-table-column
        :label="checktype == 'subject' || checktype == 'diff' ? '考试' : '班级'"
        fixed
        :width="checktype == 'subject' || checktype == 'diff' ? 220 : 170"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="checktype == 'subject' || checktype == 'diff'">{{ scope.row.examName }}</span>
          <span v-else-if="checktype == 'online'">{{ scope.row.clz_name }}</span>
          <span v-else>{{ scope.row.clzName }}</span>
        </template>
      </el-table-column>
      <!-- 上线分布 -->
      <template v-if="checktype == 'online'">
        <template v-for="(ite, inde) in hisLine">
          <el-table-column
            v-for="(it, ind) in ite.data"
            :label="it.lineName"
            :key="ind"
            align="center"
          >
            <el-table-column label="目标" min-width="170">
              <template slot-scope="scope">
                {{ getDefaultByTable(scope.row.hisLine[inde].data[ind].tar) }}
              </template>
            </el-table-column>
            <el-table-column :label="it.examName" min-width="220">
              <template slot-scope="scope">
                {{ getDefaultByTable(scope.row.hisLine[inde].data[ind].num) }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="完成次数">
            <template slot-scope="scope">
              {{ getDefaultByTable(scope.row.hisLine[inde].num) }}
            </template>
          </el-table-column>
        </template>
      </template>
      <template v-else>
        <el-table-column
          prop=""
          v-for="(item, index) in targetData"
          :key="index"
          :label="`${item.name}`"
          min-width="100"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <!-- 分数分布 -->
            <span
              v-if="checktype == 'avg'"
              :style="{
                color:
                  Number(scope.row.data[index].avg) <
                  Number(tableData[tableData.length - 1].data[index].avg)
                    ? 'red'
                    : '',
              }"
            >
              {{ getDefaultByTable(scope.row.data[index].avg) }}
            </span>
            <span v-if="checktype == 'subject'">{{ scope.row.data[index].avg }}</span>
            <!-- 均差分布 -->
            <span
              v-if="checktype == 'avgDif'"
              :style="{ color: scope.row.data[index].avgDif < 0 ? 'red' : '' }"
              >{{ getDefaultByTable(scope.row.data[index].avgDif, true) }}</span
            >
            <!-- 试卷难度 -->
            <span v-if="checktype == 'diff'">{{
              getDefaultByTable(scope.row.data[index].rate)
            }}</span>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <div class="nodata flex_1" v-else>
      <img :src="noResImg" alt="" />
      <p class="text-center">暂无数据</p>
    </div>
    <div class="score-table-more" @click="checkMore" v-if="allData.length > 1">
      <span class="more-button"
        >{{ !isShowMore ? `展 开 ` : '收 起 ' }}
        <i :class="[isShowMore ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"></i>
        <!-- <span class="more-button-arrow" :class="{ showLess: isShowMore }"></span> -->
      </span>
    </div>
    <!--分页器-->
    <!-- <el-pagination
      background
      style="margin: 20px 0 30px 0"
      :hide-on-single-page="tableData.length <= 1"
      class="text-center"
      layout="total, prev, pager, next"
      :current-page.sync="pagination.page"
      :page-size="pagination.limit"
      :total="pagination.total_rows"
    >
    </el-pagination> -->
  </div>
</template>

<script>
export default {
  name: 'scoreTable',
  props: {
    tableData: {
      type: Array,
      default() {
        return [];
      },
    },
    targetData: {
      type: Array,
      default() {
        return [];
      },
    },
    checktype: {
      type: String,
      default() {
        return '';
      },
    },
  },
  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      mouseFlag: false,
      mouseOffset: 0,
      pagination: {
        page: 1,
        limit: 10,
        total_rows: 0,
      },
      loadingTable: false,
      allData: [],
      listData: [],
      //是否查看更多
      isShowMore: false,
    };
  },
  watch: {
    tableData(newval, oldval) {
      if (newval) {
        this.isShowMore = false;
        this.allData = this.handelMoreData(newval);
        this.listData = this.allData[0];
      }
    },
  },

  computed: {
    hisLine() {
      if (!this.listData[0]) return [];
      if (!this.listData[0].hisLine) return [];
      return this.listData[0].hisLine;
    },

    // 是否显示提示文字
    isShowTip() {
      return this.checktype === 'avg' || this.checktype === 'avgDif';
    },
  },
  created() {},
  mounted() {
    // this.pagination.total_rows = this.tableData.length;
  },
  methods: {
    // 处理默认显示值
    getDefaultByTable(num, isShowNegative = false) {
      let score = Number(num);
      if (isNaN(score)) return '--';
      if (score >= 0 || isShowNegative) {
        return score;
      } else {
        return '--';
      }
    },

    /**
     * @name:处理更多数据隐藏
     */
    handelMoreData(data) {
      let newArray = [];
      if (data.length > 8) {
        let array1 = data.slice(0, 8);
        let array2 = data.slice(8, data.length);
        newArray.push(array1, array2);
      } else {
        newArray.push(data);
      }
      return newArray;
    },
    checkMore() {
      this.isShowMore = !this.isShowMore;
      if (this.isShowMore) {
        this.listData = this.allData[0].concat(this.allData[1]);
      } else {
        this.listData = this.allData[0];
      }
    },
    // 按下鼠标记录鼠标位置
    mouseDownHandler(e) {
      this.mouseOffset = e.clientX;
      this.mouseFlag = true;
    },
    mouseUpHandler(e) {
      this.mouseFlag = false;
    },
    mouseMoveHandler(e) {
      // 这里面需要注意，通过ref需要那个那个包含table元素的父元素
      let divData = this.$refs.tableRef.bodyWrapper;
      if (this.mouseFlag) {
        // 设置水平方向的元素的位置
        divData.scrollLeft -= -this.mouseOffset + (this.mouseOffset = e.clientX);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.tip-text {
  color: red;
  margin-bottom: 5px;
}
.score-table {
  padding: 20px;
}
.score-table-container {
  cursor: move;
}
.score-table-more {
  display: flex;
  justify-content: center;
  background-color: #f7fbff;
  color: #409eff;
  cursor: pointer;
}
.more-button {
  line-height: 40px;
  font-size: 16px;
}
.more-button-arrow {
  display: inline-block;
  transform: rotate(90deg);
  &.showLess {
    transform: rotate(270deg);
  }
}
</style>

<template>
  <div class="target-score">
    <div class="target-score__tip">
      <span class="tip-text"> 提示：此处指标均按总分统计，各科根据总分统计规则，按比例自动生成。</span>
      <el-button class="reset-btn" type="primary" @click="reset" :loading="resetLoading"> 恢复默认 </el-button>
    </div>

    <div class="target-score__content">
      <ScoreRangeSetting ref="scoreRangeSetting" class="target-score__setting" :examId="examId"></ScoreRangeSetting>
      <RankSetting ref="rankSetting" class="target-score__setting" :examId="examId"></RankSetting>
    </div>

    <div class="target-result__footer">
      <el-button v-if="customExamId" type="primary" @click="$emit('updateActiveStep', activeStep - 1, customExamId)">
        上一步</el-button
      >
      <div style="flex: 1"></div>
      <el-button :disabled="getIsDisabled" type="primary" @click="save" :loading="saveLoading">
        {{ customExamId ? '下一步' : '保存设置' }}
      </el-button>
    </div>
  </div>
</template>

<script lang="ts">
import RankSetting from '@/pages/schoolSetting/components/RankSetting/index.vue';
import ScoreRangeSetting from '@/pages/schoolSetting/components/ScoreRangeSetting/index.vue';
import { SchoolSettingType } from '@/pages/schoolSetting/types';
import { restoreSchCfgAPI, setSchCfgListAPI } from '@/service/pexam';
import UserRole from '@/utils/UserRole';
import { Component, Ref, Vue } from 'vue-property-decorator';

@Component({
  components: {
    ScoreRangeSetting,
    RankSetting,
  },
})
export default class TargetScore extends Vue {
  @Ref('scoreRangeSetting') scoreRangeSetting: ScoreRangeSetting;
  @Ref('rankSetting') rankSetting: RankSetting;
  // 考试id
  examId = this.$sessionSave.get('reportDetail').examId;
  // 自定义考试id
  customExamId = '';
  // 当前步骤
  activeStep = 4;
  // 保存loading
  saveLoading = false;
  // 恢复默认loading
  resetLoading = false;

  // 是否禁用保存按钮
  get getIsDisabled() {
    if (this.customExamId) {
      return false;
    } else {
      return !(UserRole.isOperation || UserRole.isSchoolLeader || UserRole.isGradeLeader);
    }
  }

  created() {
    if (this.$route.query.customExamId) {
      this.customExamId = this.$route.query.customExamId as string;
      this.examId = Number(this.customExamId);
    } else {
      this.examId = this.$sessionSave.get('reportDetail').examId;
    }
  }

  // 恢复默认
  async reset() {
    this.resetLoading = true;

    await restoreSchCfgAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      type: [SchoolSettingType.ScoreRangeSetting, SchoolSettingType.RankSetting].join(','),
      examId: this.examId,
    });

    this.$notify.success({
      title: '成功',
      message: '恢复成功',
    });

    this.scoreRangeSetting.getConfig();
    this.rankSetting.getConfig();

    this.resetLoading = false;
  }

  // 保存
  async save() {
    if (!this.rankSetting.checkSave()) return;

    this.saveLoading = true;
    const gradeId = this.$sessionSave.get('reportDetail').gradeCode;

    const jCfgs = [this.scoreRangeSetting.getCfg(), this.rankSetting.getCfg()];

    await setSchCfgListAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      schName: this.$sessionSave.get('schoolInfo').schoolName,
      type: SchoolSettingType.Rate,
      gradeId,
      jCfgs: jCfgs,
      examId: this.examId,
    });

    this.$notify.success({
      title: '成功',
      message: '保存成功',
    });
    this.saveLoading = false;
    if (this.customExamId) {
      this.$emit('updateActiveStep', this.activeStep + 1, this.customExamId);
    }
  }
}
</script>

<style scoped lang="scss">
.target-score {
  position: relative;
  font-size: 14px;
  color: #606266;
}

.target-score__tip {
  margin-bottom: 20px;
  font-size: 16px;
}

.target-score__setting {
  margin-bottom: 20px;
}

.target-result__footer {
  display: flex;
  justify-content: space-between;
}

.tip-text {
  color: #fe5d50;
  font-weight: 700;
}

.reset-btn {
  position: absolute;
  top: 20px;
  right: 20px;
}

::v-deep {
  .setting-header {
    .title {
      &::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 24px;
        background: #409eff;
        border-radius: 3px;
        margin-right: 10px;
        vertical-align: middle;
      }
    }
  }
}
</style>

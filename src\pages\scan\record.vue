<template>
  <div class="paper-container">
    <!--头部筛选项-->
    <div class="header-filter clearfix">
      <!--年级 -->
      <div class="header__select">
        <span class="select__label">年级：</span>
        <el-select v-model="filterData.gradeId"
                   class="grade-select"
                   placeholder="请选择">
          <el-option
              v-for="(item, index) in gradeList"
              :key="'grade_'+index"
              :label="item.greadeName"
              :value="item.greadeCode">
          </el-option>
        </el-select>
      </div>
      <!--学科-->
      <div class="header__select">
        <span class="select__label">学科：</span>
        <el-select v-model="filterData.subjectId"
                   class="grade-select"
                   placeholder="请选择">
          <el-option
              v-for="(item, index) in subjectList"
              :key="'subject_'+index"
              :label="item.subjectName"
              :value="item.subjectId">
          </el-option>
        </el-select>
      </div>

      <!--年份 -->
      <div class="header__select">
        <span class="select__label">年份：</span>
        <el-select v-model="filterData.year"
                   class="year-select"
                   placeholder="请选择">
          <el-option
              v-for="item in yearList"
              :key="'year_'+item.value"
              :label="item.name"
              :value="item.value">
          </el-option>
        </el-select>
      </div>
      <!--搜索-->
      <div class="header__select">
        <div class="header__serarch clearfix display_flex">
          <el-input class="search__text"
                    placeholder="输入名称搜索"
                    v-model="inputTbName"
                    @clear="finishInputName"
                    @keyup.enter.native="finishInputName"
                    clearable>
          </el-input>
          <div class="search__icon el-icon-search display_flex align-items_center justify-content_flex-center"
               @click="getRecordList"></div>
        </div>
      </div>
    </div>
    <!--表格列表-->
    <div class="examReport__content flex_1"
         :class="recordList.length?'':'display_flex align-items_center'">
      <!--个册列表数据-->
      <ul id="popoverUl" class="examReport__list list-none flex_1" v-if="recordList.length"
          v-loading="listLoading"
          element-loading-text="加载中...">

        <li class="examReport__item display_flex align-items_center"
            v-for="(item, index) in recordList"
            :key="index">
          <div class="exam-name-area">
            <div class="display_flex align-items_center exam-name">
              <div style="display: inline-block;margin-right: 20px">{{ item.title }}
              </div>
            </div>
            <div class="exam-detail display_flex align-items_center">
              <span class="exam-grade">{{ item.gradeName }}</span>
              <span class="line-gap"></span>
              <span class="exam-subject text-ellipsis"
                    :title="item.subjectName"
                    style="max-width:200px">{{ item.subjectName }}</span>
              <span class="line-gap"></span>
              <span class="exam-time">{{ item.dateCreated }}</span>
            </div>
          </div>
          <div class="scan-record-info">
            <div class="scan-result flex_1">
              <p class="scan-num"> {{ item.stuUpload }}/{{ item.stuNum }}</p>
              <el-button type="primary" size="small">上传总数</el-button>
            </div>
            <div class="scan-result flex_1">
              <p class="scan-num">{{ item.stuUpload - item.stuError }}</p>
              <el-button type="primary" size="small" @click="viewSuccess(item)">识别成功</el-button>

            </div>
            <div class="scan-result flex_1">
              <p class="scan-num">{{ item.stuError }}</p>
              <el-button type="primary" @click="viewError(item)" size="small">识别异常</el-button>
            </div>

            <el-dropdown class="more_setting" @command="handlerCommand">
              <span class="el-dropdown-link">
                <i slot="reference" class="el-icon-more"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="'updatePaperScore_'+index">同步成绩
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </li>
      </ul>
      <!--没有个册数据缺省图-->
      <!-- <div class="nodata flex_1" v-else></div> -->
      <div class="nodata flex_1" v-if="!listLoading && !recordList.length">
        <img :src="noResImg" alt="">
        <p class="text-center">暂无数据</p>
      </div>
      <!--分页器-->
      <el-pagination
          background
          style="margin-bottom:30px;"
          :hide-on-single-page="!recordList.length"
          class="text-center"
          layout="total, prev, pager, next"
          @current-change="handleCurrentChange"
          :current-page.sync="pagination.page"
          :page-size="pagination.limit"
          :total="pagination.total_rows">
      </el-pagination>
    </div>

  </div>
</template>

<script>
import {getTBSearchInfo} from '@/service/testbank';
import {getSchoolById} from '@/service/api';
import {mapGetters} from 'vuex';
import {scanRecordList, updatePaperScore} from "@/service/pexam";

export default {
  name: 'scan-record',
  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      tableLoading: false,
      // 表格数据
      tableData: [],
      // 分页
      pagination: {
        page: 1,
        pageSize: 10,
        total_rows: 0
      },
      // 筛选条件
      filterData: {
        schoolId: '',
        category: '',
        year: '',
        name: '',
        subjectId: '',
        gradeId: ''
      },
      inputTbName: '',
      // 开始计算的年份
      startYear: 2021,
      categoryList: [],
      yearList: [],
      phaseId: 1,
      typeCode: '',
      recordList: [],
      listLoading: false,
      subjectList: [],
      gradeList: [{
        greadeCode: '',
        greadeName: '全部'
      }, {
        greadeCode: 7,
        greadeName: '七年级'
      }, {
        greadeCode: 8,
        greadeName: '八年级'
      }, {
        greadeCode: 9,
        greadeName: '九年级'
      }, {
        greadeCode: 10,
        greadeName: '高一'
      }, {
        greadeCode: 11,
        greadeName: '高二'
      }, {
        greadeCode: 12,
        greadeName: '高三'
      },]
    };
  },
  computed: {
    ...mapGetters([
      'filterSubjectList'
    ])
  },
  components: {},
  mounted() {
    this.init()
  },
  watch: {
    filterData: {
      deep: true,
      handler(newValue) {
        this.pagination.page = 1
        this.getRecordList()
      }
    }
  },
  methods: {
    /**
     * dropdown命令
     */
    handlerCommand(command) {
      let commands = command.split('_')
      this[commands[0]](this.recordList[commands[1]])
    },
    updatePaperScore(item) {
      if (item.loading) {
        return
      }
      item.loading = true;
      updatePaperScore(item.paperId)
          .then(res => {
            this.$message.success('开始同步')
            item.loading = false
          })
          .catch(res => {
            item.loading = false
          })
    },
    viewSuccess(item) {
      if (item.stuUpload > item.stuError) {
        this.$router.push({
          path: '/scan/success',
          query: {
            paperId: item.paperId
          }
        })
      } else {
        this.$message.success('暂无数据')
      }
    },
    viewError(item) {
      if (item.stuError) {
        this.$router.push({
          path: '/scan/error',
          query: {
            paperId: item.paperId
          }
        })
      } else {
        this.$message.success('暂无异常')
      }
    },
    async init() {
      this.yearList = [];
      let now = new Date().getFullYear();
      for (let i = now; i > now - 6; i--) {
        this.yearList.push({
          name: i,
          value: i
        });
      }
      this.yearList.unshift({
        name: '全部',
        value: ''
      });
      this.filterData.year = '';
      await this.getSchoolById();
    },
    /**
     * 布置作业
     */
    sendHomeWork(info) {
      this.sendHwInfo = info
      this.showSendHw = true
    },
    closeDialog() {
      this.showDialog = false;
    },
    // 获取登录账号学段
    async getSchoolById() {
      let schoolId = this.$sessionSave.get('schoolInfo').id
      let res = await getSchoolById({
        schoolId: schoolId
      })
      this.phaseId = Number(res.data.phase);
      this.filterData.schoolId = schoolId
      this.readC30FilterCondition()
    },

    changeSchool() {
      this.getSchoolById()
    },

    // 从缓存读取试卷类型
    readC30FilterCondition() {
      let phase = this.phaseId || ''
      let $this = this;
      getTBSearchInfo({phase: phase}, function (data) {
        $this.updateCategoryList(data);
      })
    },
    /**
     * 更新类型
     * @param categoryList
     */
    updateCategoryList(data) {
      if (!data) {
        return
      }
      let categoryList = data.sources
      // 获取题类
      let otherIndex = -1, otherObj = {};
      categoryList.forEach((item, index) => {
        if (item.name === '其他') {
          otherIndex = index;
          otherObj = item;
        }
      });
      if (otherIndex !== -1) {
        categoryList.splice(otherIndex, 1);
        categoryList.push(otherObj);
      }

      // 获取题类
      this.categoryList = categoryList;
      this.categoryList.unshift({
        id: '',
        name: '全部'
      });

      data.subList.unshift({
        subjectId: '',
        subjectName: '全部'
      });

      this.subjectList = data.subList
      // this.yearList = data.years
    },
    // 分页查询
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getRecordList()
    },

    finishInputName() {
      this.filterData.name = this.inputTbName
    },
    /**
     * 获取列表list
     */
    getRecordList() {
      if (this.filterData.name !== this.inputTbName) {
        this.finishInputName()
        return
      }
      let params = Object.assign(this.filterData, {page: this.pagination.page, pageSize: this.pagination.pageSize});
      // delete params.schoolId
      scanRecordList(params).then(res => {
        this.pagination.total_rows = res.data.total
        this.recordList = res.data.list
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.paper-container {
  .header-filter, .body-content {
    padding: 20px 28px;
    background: #fff;
    box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
    border-radius: 6px;
  }

  .header-filter {
    .header__select {
      display: inline-block;
      margin-right: 29px;
      float: left;

      .year-select, .grade-select, .subject-select {
        width: 150px;
      }
    }

    .header__serarch {
      display: flex;
      width: 240px;

      .search__icon {
        width: 38px;
        font-size: 18px;
        color: #fff;
        background: #409eff;
        border-radius: 0 3px 3px 0;
        outline: none;
        cursor: pointer;
      }
    }
  }

  .body-content {
    margin-top: 20px;
  }
}

.examReport__content {
  width: 100%;
  margin-top: 20px;

  .examReport__list {
    width: 100%;

    .examReport__item {
      width: 100%;
      height: 95px;
      box-sizing: border-box;
      background: #fff;
      padding: 16px 43px 16px 26px;
      border: 1px solid #e4e7eb;
      box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.15);
      border-radius: 6px;
      margin-bottom: 30px;
      color: #222;
      font-size: 14px;
      position: relative;

      .square1 {
        width: 62px;
        height: 18px;
        background: rgba(62, 115, 246, 0.435294117647059);
        // position: absolute;
        left: 0;
        top: 0;

        .square-word1 {
          position: absolute;
          margin-left: 10px;
          color: #ffffff;
          font-size: 13px;
        }
      }

      .square {
        width: 0;
        height: 0;
        border: 16px solid transparent;
        border-top: 16px solid #409eff;
        border-left: 16px solid #409eff;
        z-index: 100;
        border-radius: 10px 0 0 0;
        position: absolute;
        left: 0;
        top: 0;

        .square-word {
          position: absolute;
          left: -12px;
          top: -16px;
          color: #ffffff;
          font-size: 13px;
        }
      }

      .exam-name-area {
        flex: 2;

        .exam-name {
          font-size: 20px;
          color: #3f4a54;;
          margin-bottom: 15px;
          line-height: 21px;
        }

        .exam-detail {
          .line-gap {
            width: 32px;
            position: relative;

            &:before {
              content: '';
              position: absolute;
              left: 50%;
              margin-left: -1px;
              top: 50%;
              margin-top: -7px;
              display: inline-block;
              width: 2px;
              height: 14px;
              background-color: #97a0a8;
            }
          }

          > span {
            display: inline-block;
            font-size: 16px;
            color: #3f4a54;
          }
        }

      }

      .scan-record-info {
        display: flex;
        flex: 1;

        .scan-result {
          text-align: center;
        }

        .more_setting {
          .el-dropdown-link {
            position: relative;
            font-size: 18px;
            top: 27px;
          }

          .el-icon-more {
            width: 40px;
            text-align: center;
            transform: rotate(90deg);
          }
        }
      }

      .moreEdit {
        width: 38px;
        display: inline-block;
        height: 38px;
        cursor: pointer;
        color: #008dea;
        font-size: 18px;
        background: url("../../assets/moreEdit.png") center center no-repeat;
      }

      .report-btn {
        display: inline-block;
        width: 120px;
        height: 42px;
        font-size: 14px;
        margin-left: 25px;
        cursor: pointer;
        margin-right: 43px;
        background-color: #409eff;

        &.is-disabled {
          background-color: #80c6f5;
          border-color: #80c6f5;
        }
      }
    }
  }

  .nodata {
    width: 100%;
    height: auto;
    /*background : url("../assets/no-res.png") center center no-repeat;*/
    img {
      display: block;
      margin: 0 auto;
    }
  }
}

.nodata {
  width: 100%;
  height: auto;
  /*background : url("../assets/no-res.png") center center no-repeat;*/
  img {
    display: block;
    margin: 0 auto;
  }
}


.search__text {
  .el-input__inner {
    border-radius: 4px 0 0 4px;
  }
}
</style>

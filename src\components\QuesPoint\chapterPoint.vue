<template>
  <div class="ques-point-wrapper">
    <div class="content-catalog">
      <div class="catalog-header display_flex align-items_center justify-content_space-between">
        <span class="catalog-title">章节目录</span>
        <div class="pointer-type">
          <div class="pointer-type-group">
            <el-select style="width:110px;" size="mini" v-model="currentCatalogId" @change="getTeachingCatalog" placeholder="请选择教材">
              <el-option
                v-for="item in chapterList"
                :key="item.catalogId"
                :label="item.catalogName"
                :value="item.catalogId">
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="catalog-treeBox" v-loading="isLoadingPoint">
        <!-- 知识点 -->
        <div class="point-tree-box">
          <el-tree class="catalog-tree" :data="treeData"
                   ref="pointerTreeList"
                   :props="defaultProps"
                   empty-text="暂无数据"
                   node-key="code"
                   @node-click="handleMoreClick">
              <span class="el-tree-node__label demo" slot-scope="{ node, data }">
                <span class="text-ellipsis" :title="node.label">{{ node.label }}</span>
              </span>
          </el-tree>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import {localSave, sessionSave} from "@/utils/index.js"
  import {findPointSubject} from "@/service/pexam"
  import {getTeachingCatalogAPI} from "@/service/ptask"
  import {getSchChapterAPI} from "@/service/pbook"

  export default {
    name: 'ChapterPoint',
    props: {
      pointInfo: null,
    },
    data() {
      return {
        // 知识点数据
        treeData: [],
        chapterList: [],
        // tree默认值类型
        defaultProps: {
          children: 'children',
          label: 'name'
        },
        // 是否在加载知识点
        isLoadingPoint: false,
        //当前教材id
        currentCatalogId:"",
        //当前章节id
        currentChapterId: '',
        subjectId: '',
        schId: '',
      }
    },
    created() {
      this.subjectId = sessionSave.get('currentSubject').id
      this.schId = this.$sessionSave.get('schoolInfo').id
      this.getSchChapter()
    },
    watch: {},
    methods: {
      getSchChapter() {
        getSchChapterAPI({
          subjectId: this.subjectId,
          schId: this.schId,
          type: 2
        }).then(data => {
          this.chapterList = data.data;
          this.currentCatalogId = this.chapterList[0]?.catalogId;
          this.currentCatalogId && this.getTeachingCatalog(this.currentCatalogId);
        })
      },
      getTeachingCatalog(parentId) {
        if(!parentId) return;
        getTeachingCatalogAPI({
          parentId: parentId,
        }).then(data => {
          this.treeData = data.data;
          this.handleMoreClick(this.treeData[0],null)
        })
      },
      // 切换知识点类型或触发节点时 获取新的题目列表
      changePoint() {
          this.$bus.$emit('changePoint', "c30Point", {
            catalogId: this.currentCatalogId,
            chapterId: this.currentChapterId
          })
      },
      // 查询知识点三级结构数据
      getC30Point() {
        this.changePoint();
      },
      handleMoreClick(data,node){
        // const parents = [];
        // let current = node?.parent;
        // while (current) {
        //   if(current.data.code){
        //     parents.push(current.data.code);
        //   }
        //   current = current.parent;
        // }
        // // 反转数组，使顺序为根节点到直接父节点
        // parents.reverse();
        // // 包含当前节点和所有父节点
        // const fullPath = [...parents, data.code];
        // this.currentChapterId = fullPath.join(",");
        this.currentChapterId = data.code;
        this.changePoint();
      }
    },
  }
</script>

<style lang="scss" scoped>
  .content-catalog {
    height: 100%;

    width: 300px;
    background: #fff;
    border: 1px solid #e8ebed;
    border-radius: 3px;

    .catalog-header {
      width: 298px;
      height: 48px;
      line-height: 48px;
      background: #f5f6fa;
      border-radius: 4px 4px 0 0;
      font-size: 16px;
      font-weight: 400;
      color: #666;
      padding: 0 15px 0 13px;

      .catalog-title {
        position: relative;
        padding-left: 10px;

        &:before {
          content: '';
          position: absolute;
          width: 4px;
          height: 16px;
          background: #008dea;
          border-radius: 2px;
          left: 0;
          top: 17px;
        }
      }
    }

    .catalog-treeBox {
      height: 100%;

      .pointer-type {
        margin: 10px auto;

        .pointer-type-group {
          display: flex;
          align-items: center;
          justify-content: space-around;
          border-bottom: 2px solid #E4E8ED;

          .pointer-type-pane {
            width: 50%;
            line-height: 48px;
            text-align: center;
            font-size: 16px;
            color: #4C5866;
            position: relative;

            &:hover {
              color: #409EFF;
              cursor: pointer;
            }

            &.active {
              color: #409EFF;

              &::before {
                content: '';
                width: 100%;
                height: 0;
                border-bottom: 2px solid #409EFF;
                position: absolute;
                bottom: -2px;
                left: 0;
              }
            }
          }
        }
      }

      .point-tree-box {
        height: 100%;
      }

      .catalog-tree {
        width: 100%;
        // min-height : 500px;
        // height: 530px;
        /*height: calc(100vh - 340px);*/
        height: calc(100% - 118px);
        overflow-x: hidden;
        overflow-y: auto;
      }
    }
  }
</style>

<style lang="scss">
  .catalog-tree {
    margin-top: 6px;

    .el-tree-node__expand-icon.expanded {
      transform: none;
    }

    .el-icon-caret-right:before {
      content: "\e7a0" !important;
    }

    .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {
      content: "\e7a2" !important;
    }

    .el-tree-node__expand-icon {
      font-size: 17px;
    }

    .el-tree-node__label {
      font-size: 16px;
    }

    .el-tree-node__content {
      height: 26px;
    }

    .el-tree-node__content {
      display: flex;
    }

    .el-tree-node__content > span:last-child {
      flex: 1;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .el-checkbox.is-checked + .el-tree-node__label {
      color: #409EFF;
    }

    &::-webkit-scrollbar, .selected-catalog-box::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }

    &::-webkit-scrollbar-thumb, .selected-catalog-box::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 10px;
      background-color: rgba(144, 147, 153, .3);
    }

    &::-webkit-scrollbar-track, .selected-catalog-box::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      // box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: #ffffff;
      border-radius: 10px;
    }
  }

  // 树节点选中高亮
  .catalog-treeBox .catalog-tree.chapter__tree .el-tree-node.is-current.is-focusable > .el-tree-node__content {
    background-color: #ffffff;

    & > .el-tree-node__label {
      color: #008DEA;
    }
  }
</style>

<template>
    <div class="batch-absent-component">
        <div v-if="stuList.length" class="images-grid">
            <div v-for="(stu, index) in stuList" :key="index" class="image-item" :class="{'is-active':stu.id == examNoInfo.id }">
                <div class="image-wrapper">
                    <!-- 左切换按钮 -->
                    <div class="switch-btn left" @click="switchImage(stu, 'prev')" v-if="stu.origin.length > 1">
                        <i class="el-icon-arrow-left"></i>
                    </div>
                    <!-- 图片显示 -->
                    <el-image :src="stu.image[stu.currentIndex || 0] || stu.origin[stu.currentIndex || 0]" :fit="'scale-down'"
                        :preview-src-list="stu.image" alt="">
                    </el-image>

                    <!-- 右切换按钮 -->
                    <div class="switch-btn right" @click="switchImage(stu, 'next')" v-if="stu.image.length > 1">
                        <i class="el-icon-arrow-right"></i>
                    </div>

                    <!-- 缺考标记 -->
                    <div class="absent-mark" v-if="!stu.isUnAbsent">
                        缺考
                    </div>
                </div>
                <div class="image-info">
                    {{ stu.stuName || '--' }} {{ stu.stuNo || '' }}
                </div>
                <div class="image-select">
                    <el-checkbox v-model="stu.isUnAbsent">设为非缺考</el-checkbox>
                </div>
            </div>
        </div>
        <no-data :type="'zan'"  v-else text="异常已全部处理，辛苦啦~"></no-data>
    </div>
</template>

<script>
import { getExamNoAPI } from '@/service/pexam';
import { HANDLE_TYPE } from '@/typings/scan';
import NoData from '@/components/noData';

export default {
    name: 'BatchAbsentComponent',
    components: {
        NoData
    },
    props: {
        examId: {
            type: String,
            default: ''
        },
        handleInfo: {
            type: Object,
            default: {}
        },
        examNoInfo: {
            type: Object,
            default: {}
        },
        requestInfo: {
            type: Object,
            default: {}
        }
    },
    data() {
        return {
            stuList: [],
        };
    },
    computed: {
        list() {
            return this.requestInfo.status == HANDLE_TYPE.pending
                ? this.handleInfo.wait
                : this.handleInfo.waited;
        },
        // 添加新的计算属性，结合两个需要监听的对象
        watchCombined() {
            return {
                examNo: this.examNoInfo,
                request: this.requestInfo
            };
        }
    },
    watch: {
        watchCombined(newVal, oldVal) {
            this.getAbsentList();
        },
        list(newVal, oldVal) {
            if(newVal.length == 0){
                this.stuList = [];
            }
        }
    },
    created() {
        this.$bus.$on('submitAbsent', () => {
            this.confirmSetting();
        });
        this.getAbsentList();
    },
    destroyed() {
        this.$bus.$off('submitAbsent');
    },
    methods: {
        generateIdxList(id) {
            const startIndex = this.list.findIndex(item => item.id === id);
            if (startIndex === -1) {
                return [];
            }
            return this.list.slice(startIndex, startIndex + 6);
        },
        async getAbsentList() {
            try {
                let id = "";
                if (this.examNoInfo.idx) {
                    id = this.examNoInfo.id
                } else {
                    id = this.list?.length && this.list[0].id
                }
                if(id == ""){
                    this.stuList = [];
                    return;
                }
                let isExit = this.stuList.find(item => item.id === id);
                if(isExit){
                    return;
                }
                let list = this.generateIdxList(id)
                //经与服务端沟通 缺考待处理状态 默认都是缺考数据
                this.stuList = list.map(item => {
                    return {
                        ...item,
                        currentIndex: 0,
                        isUnAbsent: this.requestInfo.status == HANDLE_TYPE.pending ? false : item.status == 1
                    }
                })
            } catch (error) {
                console.error('获取缺考学生列表失败:', error);
                this.$message.error('获取缺考学生列表失败');
            }
        },
        confirmSetting() {
            let absentData = this.stuList.map(item => {
                return {
                    ...item,
                    isAbsent: !item.isUnAbsent
                }
            });
            this.$emit('set-absent', absentData);
        },
        switchImage(stu, direction) {
            if (!stu.currentIndex) {
                stu.currentIndex = 0;
            }

            if (direction === 'next') {
                this.$set(stu, 'currentIndex', (stu.currentIndex + 1) % stu.origin.length);
            } else {
                this.$set(stu, 'currentIndex', (stu.currentIndex - 1 + stu.origin.length) % stu.origin.length);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.batch-absent-component {
    position: absolute;
    top: 100px;
    height: calc(100% - 110px);
    width: 100%;

    .images-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        padding: 0 10px;
        // min-height: 300px;
        // max-height: 460px;
        overflow-y: auto;

        .image-item {
            width: 300px;
            display: flex;
            flex-direction: column;
            border: 1px solid #ebeef5;
            border-radius: 4px;
            overflow: hidden;
            &.is-active{
                    border: 2px solid #409EFF;
                }

            .image-wrapper {
                height: 220px;
                position: relative;
                overflow: hidden;
                display: flex;
                justify-content: center;
                align-items: center;
                background-color: #f5f7fa;

                .absent-mark {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%) rotate(-30deg);
                    color: #ff4949;
                    font-size: 26px;
                    font-weight: bold;
                    border: 4px solid #ff4949;
                    padding: 5px;
                    border-radius: 4px;
                    z-index: 1;
                    opacity: 0.8;
                }

                .switch-btn {
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                    background-color: rgba(0, 0, 0, 0.3);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                    transition: all 0.3s;
                    z-index: 2;

                    &:hover {
                        background-color: rgba(0, 0, 0, 0.5);
                    }

                    i {
                        color: #fff;
                        font-size: 16px;
                    }

                    &.left {
                        left: 8px;
                    }

                    &.right {
                        right: 8px;
                    }
                }

                ::v-deep .el-image {
                    width: 100%;
                    height: 100%;

                    img {
                        max-width: 100%;
                        max-height: 100%;
                        object-fit: contain;
                    }
                }
            }

            .image-info {
                padding: 8px;
                font-size: 13px;
                text-align: center;
                color: #606266;
                border-top: 1px solid #ebeef5;
                border-bottom: 1px solid #ebeef5;
                background-color: #fff;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .image-select {
                padding: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #f5f7fa;

                .el-checkbox {
                    margin-right: 0;

                    ::v-deep .el-checkbox__label {
                        font-size: 13px;
                        color: #606266;
                    }
                }
            }
        }
    }
}
</style>

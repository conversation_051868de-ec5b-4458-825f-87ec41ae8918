<!--
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2024-10-28 10:51:20
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-06-25 14:31:26
-->
<template>
  <div v-loading="loading" element-loading-text="数据加载中...">
    <template v-if="expandQuesList.length != 0">
      <div class="quesList-item display_flex flex-direction_column" v-for="item in expandQuesList" :key="item.qId">
        <div class="queList-content over-hidden">
          <div class="ques-list">
            <div class="ques-content display_flex flex-direction_column">
              <div class="question_content flex_1" style="width: 100%">
                <LatexHtml class="question_body" :html="item.data.q_html" :subject="item.subject" />
                <!-- <template v-if="['301', '302'].includes(item.data.levelcode)">
                <LatexHtml v-for="(qs,qindex) in item.data.qs" class="question_body" :html="'('+(qindex+1)+')'+qs.q_html" :subject="item.subject"/>
              </template> -->
              </div>
            </div>
            <div class="queList-footer">
              <div class="difficult-star pull-left display_flex align-items_center">
                难度:
                <span class="star" v-for="i in item.data.difficulty" :key="i + item.qId"></span>
              </div>
              <template>
                <el-button type="primary" @click="$emit('selectQues', item, item)" :loading="item.loading"
                  :disabled="maxVariantLength ? selectIds.length >= maxVariantLength : false"
                  v-show="!selectIds.includes(item.qId)">+
                  选入</el-button>
                <el-button class="remove" @click="$emit('selectQues', item, item)" v-show="selectIds.includes(item.qId)">-
                  移除</el-button>
              </template>
              <span class="edit-text detail" :class="{ active: item.showDetails }" @click="getQuesDetails(item)">详情</span>
            </div>
            <div class="ques-detail" v-if="item && item.showDetails">
              <!--答案-->
              <div class="answer_box display_flex align-items_flex-start">
                <strong class="flex_shrink_0">【答案】</strong>
                <div class="flex_1">
                  <div class="answer_content" v-if="item.type == 2 || item.quesType == 2">
                    {{ item.answer.split(',')[0] == 'A' ? '正确' : '错误' }}
                  </div>
                  <div v-if="item.data.levelcode != ''" class="answer_content">
                    <p v-for="(item, index) in item.answer" :key="index" style="display: flex">
                      <span>&nbsp;&nbsp;({{ index + 1 }})&nbsp;&nbsp;&nbsp;</span>
                      <span v-html="item"></span>
                    </p>
                  </div>
                  <LatexHtml class="answer_content" v-else :html="item.answer" :subject="item.subject" />
                </div>
              </div>
              <!--考点-->
              <div v-if="item.knowledgeName && item.knowledgeName.length">
                <strong>【知识点】</strong>{{ item.knowledgeName.join('，') }}
              </div>
              <!--解析-->
              <div v-if="item.analysis" class="answer_box display_flex align-items_flex-start">
                <span class="flex_shrink_0"><strong>【解析】</strong></span>
                <LatexHtml class="answer_content flex_1" :html="item.analysis" :subject="item.subject" />
              </div>
            </div>
          </div>
        </div>

      </div>
    </template>
    <el-empty v-else description="暂无变式题"></el-empty>
  </div>
</template>

<script>
import { getQuesSearchConvert, syncJYEooQueInfo, chooseQuesSearch, findPersonBookQuesBank, } from "@/service/pbook";
import { findLikeQues, } from "@/service/ptask";
import QuesItem from "@/components/ClassTest/index.vue";
import QuesSurface from "@/components/ClassTest/surface.vue";
import LatexHtml from '@/components/LatexHtml';

export default {
  name: 'jyeoo-ques-dialog',
  components: {
    QuesItem,
    QuesSurface,
    LatexHtml
  },
  data() {
    return {
      loading: true,
      expandQuesList: [],
      //变式题来源
      similarForm: 0,
      //变式题来源类型
      SIMILAR_TYPE: {
        GC: 1,//加工
        JY: 2,//菁优
        ES: 3, //题库搜索
        POINT: 4, //知识点搜索
      },
      isShowDetail: false
    };
  },
  props: [
    'originQues',
    'selectIds',
    // 是否正在组卷
    'isComposingPaper',
    'maxVariantLength'
  ],
  async created() {
    if (this.originQues.linkIds?.length && this.isComposingPaper) {
      this.similarForm = this.SIMILAR_TYPE.GC;
    } else if (this.$sessionSave.get('jyeooQues') == 1) {
      this.similarForm = this.SIMILAR_TYPE.JY;
    } else if (this.originQues.pointIds) {
      this.similarForm = this.SIMILAR_TYPE.POINT;
    } else {
      this.similarForm = this.SIMILAR_TYPE.ES;
    }
    this.getQuesList();
  },

  methods: {
    async getQuesList() {
      try {
        this.loading = true;
        let data = [];
        switch (this.similarForm) {
          case this.SIMILAR_TYPE.GC:
            data = await this.getGcQuesList();
            break;
          case this.SIMILAR_TYPE.JY:
            data = await this.getJyQuesList();
            break;
          case this.SIMILAR_TYPE.POINT:
            data = await this.getQuesWithPoint();
            if (!data.length) {
              data = await this.getEsQuesList();
            }
            break
          case this.SIMILAR_TYPE.ES:
            data = await this.getEsQuesList();
            break;
        }
        data.forEach(item => {
          item.showDetails = false;
        })
        this.expandQuesList = data;
        this.loading = false;
      } catch (error) {
        this.expandQuesList = [];
        this.loading = false;
      }
    },
    async getGcQuesList() {
      const data = await chooseQuesSearch({
        qIds: this.originQues.linkIds.join(','),
      }).catch(err => {
      });
      return data.data;
    },
    async getJyQuesList() {
      try {
        const qId = (this.originQues.bigId && !this.originQues.bigId.includes(','))
          ? (this.originQues.bigId || this.originQues.quesId)
          : (this.originQues.id || this.originQues.quesId);

        // 如果当前为合并题，则拿出所有小题的推题
        const httpReqs = qId.split(',').map(id => {
          return getQuesSearchConvert({
            schoolId: this.$listeners.getParams().schoolId,
            subjectId: this.$listeners.getParams().subjectId,
            year: "2024",
            userId: this.$sessionSave.get('loginInfo').id,
            id: id
          })
        });

        const resList = await Promise.all(httpReqs)
        let dataList = [];
        resList.forEach(res => {
          if (res.code != 1) return;

          dataList = dataList.concat(res.data.rows)
        })

        return dataList;
      } catch (error) {
        return [];
      }
    },

    async getEsQuesList() {
      let content = this.originQues.content[0] || this.originQues.content
      let topic = content.topic;
      const data = await findLikeQues({
        schoolId: this.$listeners.getParams().schoolId,
        subjectId: this.$listeners.getParams().subjectId,
        topic: topic.replace(/<(.)*?>/g, ''),
      }).catch(err => {
      });
      return data.data.rows;
    },

    async getQuesWithPoint() {
      let res = await findPersonBookQuesBank({
        knowledgeCode: this.originQues.pointIds,
        page: 1,
        type: 0,
        schoolId: this.$listeners.getParams().schoolId,
        optUserId: this.$sessionSave.get('loginInfo').id,
        sortFiled: "time",
        subjectId: this.$listeners.getParams().subjectId
      })

      return res.rows;
    },

    // 查看详情
    getQuesDetails(item) {
      if (this.similarForm != this.SIMILAR_TYPE.JY || item.showDetails) {
        item.showDetails = !item.showDetails;
        return;
      }
      if (item.detailFlag) {
        item.showDetails = true;
        return;
      }
      syncJYEooQueInfo({
        schoolId: this.$listeners.getParams().schoolId,
        userId: this.$sessionSave.get('loginInfo').id,
        subjectId: this.$listeners.getParams().subjectId,
        qIds: item.qId,
      })
        .then((result) => {
          if (result.data) {
            this.$set(item, 'data', result.data.data);
            this.$set(item, 'detailFlag', true);
            this.$set(item, 'showDetails', true);
            this.$set(item, 'answer', result.data.answer);
            this.$set(item, 'knowledgeName', result.data.data.tag_ids.map(it => it.name));
            this.$set(item, 'analysis', result.data.analysis);
            this.$set(item, 'content', result.data.content);
            this.$forceUpdate();
          } else {
            this.$message({
              message: `该题没有详情！`,
              type: "warning",
            });
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    /**
     * @name:替换公式
     * @param {*} html
     */
    addMathTag(html) {
      let subject = this.$listeners.getParams().subjectId || "02";
      let subjectRealId = this.$listeners.getParams().subjectRealId || "11";
      const subjectCodes = ["01", "03"];
      const subjectIds = ["1", "3", "10", "12", "24", "25"];
      //语文英语不替换公式
      if (
        (subjectCodes.indexOf(subject) != -1 ||
          subjectIds.indexOf(subjectRealId) != -1) &&
        !html.includes("\\underline")
      ) {
        return html;
      }
      html = html.replace(
        /\$(.*?)\$/g,
        '<span class="math-tex">\\($1\\)</span>'
      );
      return html;
    },
    mathJaxUpdate() {
      let success = false;
      if (MathJax && MathJax.startup) {
        try {
          MathJax.startup.getComponents();
          MathJax.typeset();
          success = true;
        } catch (e) { }
      }

      if (!success) {
        setTimeout(() => {
          this.mathJaxUpdate();
        }, 100);
      }
    },
    katexUpdate() {
      MathJax.typeset();
    },
  },
};
</script>

<style lang="scss" scoped>
.question_body {
  padding: 20px;
}

.quesList-item {
  width: 100%;
  background: #fff;
  border: 1px solid #e8ebed;
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;

  &:hover {
    border: 1px solid #008dea;
  }

  .queList-content {
    &:hover {
      cursor: pointer;
    }

    q ol {
      list-style: none;
    }

    .option-box {
      .row-four {
        display: flex;
        flex-wrap: wrap;

        .flex-four {
          width: 20%;
          margin: 10px;

          img {
            width: 100%;
          }
        }
      }

      .row-two {
        display: flex;
        flex-wrap: wrap;

        .flex-two {
          width: 42%;
          margin: 10px;
        }
      }

      .option-item {
        display: flex;

        .option-item-letter {
          margin-right: 4px;
        }
      }
    }
  }

  .ques-detail {
    background-color: #fcfcfc;
    padding: 15px;
  }

  .ques-detail-box {
    background: #f8f9fc;
    padding: 10px;
    border-top: 1px solid #eeeeee;
    color: #333;

    .point-box,
    .answer-box,
    .analysis-box {
      display: flex;
    }

    .auxiliary {

      .pt3,
      .pt4,
      .pt5,
      .pt6,
      .pt7 {
        padding-left: 10px;
        padding-bottom: 5px;
      }

      b {
        font-weight: 500;
      }

      a {
        color: #333;
      }
    }
  }

  .queList-footer {
    width: 100%;
    height: 40px;
    background: #f8f9fc;
    border-radius: 0 0 6px 6px;
    padding: 0 20px 0 0;
    line-height: 40px;
    font-size: 16px;
    font-weight: 400;
    color: #525766;

    .difficult-star {
      font-size: 14px;
      margin-left: 30px;

      .star {
        width: 16px;
        height: 16px;
        background: url("../../assets/star.png") center center/100% 100% no-repeat;
        margin-left: 5px;

        &:first-child {
          margin-left: 8px;
        }
      }
    }

    .el-button {
      min-width: 80px;
      height: 30px;
      // background: #3a84f9;
      border-radius: 4px;
      margin-left: 30px;
      float: right;
      // color: #fff;
      cursor: pointer;
      margin-top: 5px;
      padding: 0;
      font-size: 16px;
      font-weight: 400;

      &.remove {
        border: 1px solid #3a84f9;
        background: #fff;
        color: #3a84f9;
      }
    }

    .edit-text {
      margin-left: 18px;
      float: right;
      cursor: pointer;

      &.disclose,
      &.error-correction,
      &.detail {
        position: relative;

        &:before {
          content: "";
          position: absolute;
          top: 11px;
          left: 0;
          background-image: url("../../assets/analyzeQuesIcons.png");
        }
      }

      &.detail.active::before {
        transform: rotateZ(180deg);
        top: 14px;
      }

      &.disclose {
        padding-left: 15px;

        &:before {
          background-position: -34px -15px;
          width: 10px;
          height: 10px;
        }
      }

      &.error-correction {
        padding-left: 15px;

        &:before {
          background: url("../../assets/eraser.png") center center/100% 100% no-repeat;
          width: 16px;
          height: 16px;
          top: 12px;
          left: -2px;
        }
      }

      &.detail {
        padding-left: 15px;

        &:before {
          background-image: url("../../assets/analyzeQuesIcons.png");
          background-position: -31px 4px;
          width: 14px;
          height: 14px;
          left: -2px;
        }
      }

      &.similar {
        padding-left: 15px;
        position: relative;

        &:before {
          content: "";
          width: 16px;
          height: 16px;
          background: url("../../assets/icon/search.png") no-repeat;
          background-size: 100% 100%;
          position: absolute;
          left: -3px;
          top: 12px;
        }
      }
    }
  }
}
</style>
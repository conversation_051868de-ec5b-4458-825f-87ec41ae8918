<script lang="ts">
import { isSy } from '@/utils/channel';
import axios from 'axios';
import { Component, Vue } from 'vue-property-decorator';

@Component
export default class SyFilterMixin extends Vue {

  get isSyRegion() {
    return isSy();
  }


  async getSySchoolList() {
    let platformId = this.$sessionSave.get('regionInfo').id;
    const path = `${
      process.env.VUE_APP_FS_URL
    }/yiqi/exam/config/${platformId}/school.json?timestamp=${new Date().getTime()}`;
    const res = await axios.get(path);

    let schoolList = [];
    if (res.status === 200) {
      schoolList = res.data.data;
    }
    return schoolList;
  }
}
</script>

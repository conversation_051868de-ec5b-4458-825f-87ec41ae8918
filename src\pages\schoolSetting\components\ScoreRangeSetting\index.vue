<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-03-04 00:12:15
 * @LastEditors: 小圆
-->
<template>
  <div>
    <div class="setting-header">
      <div class="title">分数段设置</div>
      <div class="header-right" v-if="!examId">
        <el-button type="primary" size="small" @click="reset" :loading="resetLoading">恢复默认</el-button>
        <el-button type="primary" size="small" @click="save" :loading="saveLoading">保存</el-button>
      </div>
    </div>

    <el-form :model="jCfg" label-position="left" label-suffix="：" inline hide-required-asterisk>
      <el-form-item
        label="总分分段"
        :prop="`total`"
        :rules="[{ required: true, message: '必填项', trigger: 'blur' }]"
        style="margin-right: 20px"
      >
        <el-input-number
          v-model="jCfg.total"
          class="score-range-input"
          placeholder="请输入总分分段"
          size="small"
          :precision="0"
          :controls="false"
          :min="1"
          :max="150"
        />
        <span>分/段</span>
      </el-form-item>

      <el-form-item
        label="单学科分段"
        :prop="`subject`"
        :rules="[{ required: true, message: '必填项', trigger: 'blur' }]"
      >
        <el-input-number
          v-model="jCfg.subject"
          class="score-range-input"
          placeholder="请输入单学科分段"
          size="small"
          :precision="0"
          :controls="false"
          :min="1"
          :max="50"
        />
        <span>分/段</span>
      </el-form-item>
    </el-form>

    <SettingSaveDialog
      v-if="isShowSaveDialog"
      :currentPhase="currentPhase"
      :currentGradeId="currentGradeId"
      :currentGradeItem="currentGradeItem"
      :currentGradeList="currentGradeList"
      :examId="examId"
      @save="handleSave"
      @closed="isShowSaveDialog = false"
    ></SettingSaveDialog>
  </div>
</template>

<script lang="ts">
import { Component, Mixins, Prop, Vue } from 'vue-property-decorator';
import SchoolSettingMixin from '../SchoolSetting.mixin';
import { getSchCfgAPI, restoreSchCfgAPI, setSchCfgAPI } from '@/service/pexam';
import { SchoolSettingType, SettingChangeParams } from '../../types';
import SettingSaveDialog from '../SettingSaveDialog.vue';

interface JCfg {
  total: number;
  subject: number;
}

@Component({
  components: {
    SettingSaveDialog,
  },
})
export default class ScoreRangeSetting extends Mixins(SchoolSettingMixin) {
  // 配置项
  private jCfg: JCfg = {
    total: 50,
    subject: 20,
  };
  // 重置loading
  private resetLoading = false;
  // 保存loading
  private saveLoading = false;
  // 保存对话框
  private isShowSaveDialog = false;

  mounted() {
    this.getConfig();
  }

  // 获取配置项
  async getConfig() {
    const res = await getSchCfgAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      type: SchoolSettingType.ScoreRangeSetting,
      phase: this.currentPhase,
      gradeId: this.currentGradeId,
      examId: this.examId,
    });
    if (res.data && res.data.jCfg) {
      this.jCfg = res.data.jCfg;
    } else {
      this.jCfg = {
        total: 50,
        subject: 20,
      };
    }
  }

  // 重置
  async reset() {
    this.resetLoading = true;

    try {
      await this.restoreSchCfg();
      this.$notify.success({
        title: '成功',
        message: '恢复成功',
      });
      this.getConfig();
    } catch (error) {
      this.$notify.error({
        title: '失败',
        message: '恢复失败',
      });
    } finally {
      this.resetLoading = false;
    }
  }

  // 保存
  async save() {
    if (this.currentGradeId == '0') {
      this.isShowSaveDialog = true;
    } else {
      this.handleSave([this.currentGradeId]);
    }
  }

  // 执行保存
  async handleSave(gradeIds: string[]) {
    this.saveLoading = true;
    this.isShowSaveDialog = false;
    try {
      await this.setSchCfg(gradeIds);
      this.$notify.success({
        title: '成功',
        message: '保存成功',
      });
    } catch (error) {
      this.$notify.error({
        title: '失败',
        message: '保存失败',
      });
    } finally {
      this.saveLoading = false;
    }
  }

  // 恢复学校配置
  public restoreSchCfg() {
    return restoreSchCfgAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      phase: this.currentPhase,
      type: SchoolSettingType.ScoreRangeSetting,
      gradeId: this.currentGradeId,
      examId: this.examId,
    });
  }

  // 设置学校配置
  public setSchCfg(gradeIds: string[]) {
    const jCfg = this.getCfg().jCfg;
    const promises = [];
    for (const gradeId of gradeIds) {
      promises.push(
        setSchCfgAPI({
          schId: this.$sessionSave.get('schoolInfo').id,
          schName: this.$sessionSave.get('schoolInfo').schoolName,
          type: SchoolSettingType.ScoreRangeSetting,
          phase: this.currentPhase,
          gradeId,
          jCfg,
          examId: this.examId,
        })
      );
    }
    return Promise.all(promises);
  }

  // 获取配置信息
  public getCfg() {
    return {
      type: SchoolSettingType.ScoreRangeSetting,
      jCfg: this.jCfg,
    };
  }

  // 设置更改
  onSettingChange(data: SettingChangeParams) {
    this.getConfig();
  }
}
</script>

<style lang="scss" scoped>
@import '../page-style.scss';

.score-range-input {
  width: 80px;
  margin: 0 10px;
}
</style>

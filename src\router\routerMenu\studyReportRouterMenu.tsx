/*
 * @Description:
 * @Author: 小圆
 * @Date: 2024-03-16 09:08:30
 * @LastEditors: 小圆
 */

import {
  Teacher_<PERSON>_<PERSON>u,
  Teacher_<PERSON>_<PERSON>,
  Teacher_<PERSON>_<PERSON>,
  Teacher_<PERSON>_<PERSON>u,
  Teacher_<PERSON>_<PERSON>,
  Teacher_F_Menu,
} from '@/pages/studyReport/plugins/menu/teacher';
import { Student_Menu } from '@/pages/studyReport/plugins/menu/student';
import { Custom_Menu } from '@/pages/studyReport/plugins/menu/custom';

const EmptyComponent = {
  render: (h, ctx) => {
    return (
      <transition name="slide-fade" mode="out-in">
        <router-view></router-view>
      </transition>
    );
  },
};

const menuList = [
  {
    title: '教师报表',
    path: 'teacher',
    name: 'studyReportTeacher',
    redirect: to => {
      return { name: menuList[0].children[0].name };
    },
    component: () => import('../../pages/studyReport/teacher/index.vue'),
    children: [
      {
        title: 'A-成绩查询',
        path: 'A',
        name: 'Teacher<PERSON>',
        component: EmptyComponent,
        redirect: to => {
          return { name: Teacher_A_Menu[0].name };
        },
        // redirect: `/home/<USER>/teacher/A/${Teacher_A_Menu[0].path}`,
        children: Teacher_A_Menu,
      },
      {
        title: 'B-成绩统计',
        path: 'B',
        name: 'TeacherB',
        component: EmptyComponent,
        redirect: to => {
          return { name: Teacher_B_Menu[0].name };
        },
        children: Teacher_B_Menu,
      },
      // {
      //   title: 'C-试卷讲评',
      //   path: 'C',
      //   name: 'TeacherC',
      //   group: true,
      //   component: EmptyComponent,
      //   children: Teacher_C_Menu,
      // },
      {
        title: 'D-教学诊断',
        path: 'D',
        name: 'TeacherD',
        component: EmptyComponent,
        redirect: to => {
          return { name: Teacher_D_Menu[0].name };
        },
        children: Teacher_D_Menu,
      },
      {
        title: 'E-质量监测',
        path: 'E',
        name: 'TeacherE',
        component: EmptyComponent,
        redirect: to => {
          return { name: Teacher_E_Menu[0].name };
        },
        children: Teacher_E_Menu,
      },
      // {
      //   title: 'F-其它报表',
      //   path: 'F',
      //   name: 'TeacherF',
      //   group: true,
      //   component: EmptyComponent,
      //   redirect: to => {
      //    return {name: Teacher_F_Menu[0].name}
      //   },
      //   children: Teacher_F_Menu,
      // },
    ],
  },
  // {
  //   title: '学生报表',
  //   path: 'student',
  //   name: 'studyReportStudent',
  //   component: () => import('../../pages/studyReport/student/index.vue'),
  //   children: Student_Menu,
  // },
  {
    title: '定制报表',
    path: 'custom',
    name: 'studyReportCustom',
    component: () => import('../../pages/studyReport/custom/index.vue'),
    redirect: to => {
      return { name: Custom_Menu[0].name };
    },
    children: Custom_Menu,
  },
];

export default {
  path: 'studyReport',
  name: 'studyReport',
  redirect: to => {
    return { name: menuList[0].name };
  },
  component: () => import('../../pages/studyReport/index.vue'),
  children: menuList,
};

export { menuList };

<template>
  <fieldset style="text-align: left" ref="stemHtml" class="item">
    <div
      class="pt1 xb"
      v-if="surface.levelcode != ''"
      v-html="replacePracticeImgSrc(surface.desc_html)"
    ></div>
    <!--音频-->
    <a
      class="listening"
      :href="surface.listen_url"
      v-if="surface.levelcode === '' && surface.listen_url"
    ></a>
    <template v-for="(sq, sqIndex) in surface.qs">
      <div
        class="pt1 xb"
        :key="'q_' + sqIndex"
        v-html="rebuildXBHtml(sq, sqIndex, surface.qs.length)"
      ></div>
      <div class="pt2" :key="'opt_' + sqIndex">
        <!-- 针对中学题库动态控制选项列数 -->
        <!-- <template v-if="openType === OPEN_TYPE.BANK"> -->
        <el-row>
          <el-col
            :span="sq.showType === 3 ? 6 : sq.showType === 2 ? 12 : 24"
            v-for="(opt, optIndex) in sq.optionText"
            :key="optIndex"
            class="surface-option"
          >
            <span style="display: inline-block; width: 20px; vertical-align: top"
              >{{ String.fromCharCode(optIndex + 65) }}.</span
            >
            <label
              v-html="replacePracticeImgSrc(opt)"
              class="opt-label"
              style="display: inline-block; width: calc(100% - 25px)"
            ></label>
          </el-col>
        </el-row>
      </div>
    </template>

    <div class="auxiliary" v-if="isShowDetail">
      <div class="diffculty">
        <div class="dt"><b>【难度】</b></div>
        <el-rate
          :value="getDiffStart(surface.difficulty * 1)"
          disabled
          style="display: inline-block"
        ></el-rate>
      </div>

      <div class="q_tags">
        <div class="dt"><b>【知识点】</b></div>
        <template v-if="surface.levelcode == ''">
          <template v-if="surface.tag_ids && surface.tag_ids.length != 0">
            <span v-for="(tag, tagIndex) in surface.tag_ids" :key="'tag' + tagIndex"
              >{{ tag.name }}；</span
            >
          </template>
          <span v-else>略</span>
        </template>
        <template v-else>
          <div class="dd" :key="'tags_' + sqIndex" v-for="(sq, sqIndex) in surface.qs">
            <span style="float: left" v-if="surface.levelcode !== ''"> ({{ sqIndex + 1 }}) </span>
            <template v-if="sq.tag_ids && sq.tag_ids.length != 0">
              <span v-for="(tag, tagIndex) in sq.tag_ids" :key="sqIndex + '_' + tagIndex"
                >{{ tag.name }}；</span
              >
            </template>
            <span v-else :key="sqIndex">略</span>
          </div>
        </template>
      </div>
      <!-- v-if="$store.getters.permis.hasQuesResourcePower" -->
      <div class="q_tags">
        <div class="dt"><b>【题目资源】</b></div>
        <template v-if="surface.url">
          <template v-if="surface.url.length > 0">
            <span
              class="q_tag_resource"
              @click="previewRes(item)"
              v-for="(item, index) in surface.url"
              :key="index"
            >
              <img
                :src="getCoverByType(item.resourceType, item.resourceExt)"
                :alt="item.resourceName"
              />
            </span>
          </template>
          <span v-else>略</span>
        </template>
        <span v-else>略</span>
      </div>
      <!-- v-if="$store.getters.permis.hasKnowledgeResourcePower" -->
      <div class="q_tags">
        <div class="dt"><b>【知识点资源】</b></div>
        <template v-if="tagResourceList.length">
          <span
            class="q_tag_resource is-video"
            @click="showVideoPlayer(item)"
            v-for="(item, index) in tagResourceList"
            :key="index"
          >
            <img :src="replaceAliUrl(item.thumbnailUrl)" :alt="item.resourceName" />
          </span>
        </template>
        <span v-else>略</span>
      </div>
      <div class="listen_text" v-if="surface.listen_text">
        <div class="dt"><b>【听力原文】</b></div>
        <div class="dd" v-html="surface.listen_text"></div>
      </div>

      <div class="quality">
        <div class="dt"><b>【核心素养】</b></div>
        <template v-if="surface.levelcode == ''">
          <template v-if="surface.quality && surface.quality.length != 0">
            <span v-for="(qua, quaIndex) in surface.quality" :key="'qua' + quaIndex"
              >{{ qua.name }}；</span
            >
          </template>
          <span v-else>略</span>
        </template>
        <template v-else>
          <template v-if="surface.qs && surface.qs.length > 0">
            <template v-for="(sq, sqindex) in surface.qs">
              <template v-if="sq.quality && sq.quality.length != 0">
                <span v-for="(qua, quaIndex) in sq.quality" :key="sqindex + '_' + quaIndex"
                  >{{ qua.name }}；</span
                >
              </template>
              <template v-else>
                <span v-if="!surface.quality && surface.quality.length === 0" :key="sqindex"
                  >({{ sqindex + 1 }})略</span
                >
              </template>
            </template>
          </template>
          <template v-if="surface.quality && surface.quality.length != 0">
            <span v-for="(qua, quaIndex) in surface.quality" :key="'qua' + quaIndex"
              >{{ qua.name }}；</span
            >
          </template>
          <span v-else>略</span>
        </template>
      </div>

      <div class="answer">
        <div class="dt"><b>【答案】</b></div>
        <div class="dd" v-for="(sq, sqIndex) in surface.qs" :key="'ans_' + sqIndex">
          <span style="float: left" v-if="surface.levelcode !== ''"> ({{ sqIndex + 1 }}) </span>
          <div class="dd" v-if="!sq.ans || sq.ans.length == 0">略</div>
          <template v-else>
            <template v-if="typeof sq.ans === 'object'">
              <template v-for="(ans, ansIndex) in sq.ans">
                <template v-if="isHtmlTag(ans)">
                  <span
                    v-if="ansIndex != sq.ans.length - 1"
                    v-html="replacePracticeImgSrc(ans) + '；'"
                    :key="sqIndex + '_' + ansIndex"
                    style="margin-right: 5px"
                  ></span>
                  <span v-else v-html="replacePracticeImgSrc(ans)" style="margin-right: 5px"></span>
                </template>
                <template v-else>
                  <span
                    v-if="isJudgeType(surface.type)"
                    :key="sqIndex + '_' + ansIndex"
                    style="margin-right: 5px"
                  >
                    {{ ans === "A" ? "正确" : "错误" }}
                  </span>
                  <span :key="sqIndex + '_' + ansIndex" style="margin-right: 5px" v-else>
                    {{
                      replaceMathHtmls(
                        isJSON(ans)
                          ? replacePracticeImgSrc(JSON.parse(ans)[0])
                          : replacePracticeImgSrc(ans)
                      )
                    }}；
                  </span>
                </template>
              </template>
            </template>

            <template v-else>
              <span :key="sqIndex + '__'" style="margin-right: 5px">
                {{
                  replaceMathHtml(
                    isJSON(sq.ans)
                      ? replacePracticeImgSrc(JSON.parse(sq.ans)[0])
                      : replacePracticeImgSrc(sq.ans)
                  )
                }}
              </span>
            </template>
          </template>
        </div>
      </div>
      <div class="exp">
        <div class="dt"><b>【解析】</b></div>
        <div class="dd" :key="'exp_' + sqIndex" v-for="(sq, sqIndex) in surface.qs">
          <span style="float: left" v-if="surface.levelcode !== ''"> ({{ sqIndex + 1 }}) </span>
          <span v-if="sq.exp == ''" :key="sqIndex">略</span>
          <template v-else>
            <span
              v-if="isHtmlTag(sq.exp)"
              :key="sqIndex"
              v-html="replacePracticeImgSrc(sq.exp)"
            ></span>
            <span v-else :key="sqIndex">{{ replacePracticeImgSrc(sq.exp) }}</span>
          </template>
        </div>
      </div>
    </div>
    <!--视频预览弹窗-->
    <video-player
      v-if="defaultPlayerResource.show"
      :src="defaultPlayerResource.url"
      :title="defaultPlayerResource.title"
      @close-dialog="defaultPlayerResource.show = false"
    ></video-player>
    <!--资源预览弹窗-->
    <!-- <preview-res-view v-if="defaultPreview.isShow" :list="defaultPreview.list" :id="defaultPreview.id"
        @close-dialog="defaultPreview.isShow = false" :disabled=true></preview-res-view> -->
  </fieldset>
</template>

<script>
import { getXBPreviewQues, getQuesUseCount } from "@/service/testbank";
import { listResAPI } from "@/service/ptask";
import { chooseQuesSearch } from "@/service/pbook";
import { isNullOrUndefined } from "@/utils";
import {
  replaceFillevaAnsSign,
  replaceMathHtml,
  getDiffStartStar,
  replaceALiUrl,
} from "@/utils/common";
import VideoPlayer from "@/components/VideoPlayer/VideoPlayer.vue";
import { convertHtml } from "@/service/pbook";

export default {
  props: {
    stem: {
      type: String,
      defalut: "",
    },
    id: {
      type: String,
      defalut: "",
    },
    triggerDetail: {
      type: Boolean,
      defalut: false,
    },
  },
  components: {
    VideoPlayer,
  },
  data() {
    return {
      // 是否展示17,校本,课课练题库题目详情
      isShowDetail: false,
      // 是否展示编辑按钮
      isShowEditAnswerView: true,
      // 题目数据
      surface: {},
      // 知识点关联资源
      tagResourceList: [],
      // 当前播放资源
      defaultPlayerResource: {
        url: "",
        title: "",
        show: false,
      },
      // 资源预览参数
      defaultPreview: {
        list: [],
        id: "",
        isShow: false,
      },
    };
  },
  watch: {
    // stem: {
    //   handler(newValue) {
    //     this.surface = newValue === "" ? "" : JSON.parse(newValue);
    //     this.$nextTick(() => {
    //       this.surface = this.converQues(this.surface);
    //     });
    //   },
    //   deep: true,
    //   immediate: true,
    // },
    stem(newValue) {
      this.surface = newValue === "" ? "" : JSON.parse(newValue);
      this.$nextTick(() => {
        this.surface = this.converQues(this.surface);
      });
    },
    triggerDetail(newValue, oldVal) {
      this.isShowDetail = newValue;
    },
    async isShowDetail(newValue) {
      if (newValue) {
        await this.getDetail();
        this.renderHtml(true);
      } else {
        this.closeQuesDetail();
      }
    },
  },
  created() {
    this.surface = this.stem === "" ? "" : JSON.parse(this.stem);
    this.isShowDetail = this.triggerDetail;
    if (!this.isShowDetail) {
      this.renderHtml();
    }
  },
  methods: {
    replaceAliUrl(url) {
      return replaceALiUrl(url);
    },
    getDetail() {
      chooseQuesSearch({
        qIds: this.id,
      }).then(async (data) => {
        if (data.data.length) {
          this.surface = data.data[0].data;
          this.$nextTick(() => {
            this.surface = this.converQues(this.surface);
          });
          this.getTagResource();
          await this.setQuesUseCount();
          this.renderHtml(true);
        }
      });
    },
    converQues(data) {
      data.desc_html = convertHtml(data.desc_html);
      data.q_html = convertHtml(data.q_html);
      data.qs.forEach((it) => {
        it.desc_html = convertHtml(it.desc_html);
        it.q_html = convertHtml(it.q_html);
        if (it.opts_htmls) {
          let list = [];
          for (let j = 0; j < it.opts_htmls.length; j++) {
            list.push(convertHtml(it.opts_htmls[j]));
          }
          it.optionText = list;
        }
        if (it.ans) {
          let list = [];
          for (let j = 0; j < it.ans.length; j++) {
            list.push(convertHtml(it.ans[j]));
          }
          it.ans = list;
        }
        if (it.exp) {
          it.exp = convertHtml(it.exp);
        }
      });
      return data;
    },
    /**
     * @name: 关闭题目详情
     * */
    closeQuesDetail() {
      this.isShowDetail = false;
      this.surface = this.stem == "" ? "" : JSON.parse(this.stem);
      this.tagResourceList = [];
      this.renderHtml();
    },
    /**
     * @name: 公式替换
     * @param html 待替换文本
     * @return: 替换后的文本
     */
    replaceMathHtmls(html) {
      html = replaceMathHtml(html);
      return html;
    },
    /**
     * @name: 公式,语音渲染
     * @param isDetail 是否展示详情
     */
    renderHtml(isDetail) {
      this.$nextTick(async () => {
        let dom = this.$refs.stemHtml;
        if (!isNullOrUndefined(dom)) {
          if (this.isShowDetail) {
            $(".auxiliary", dom).show();
          } else {
            $(".auxiliary", dom).hide();
          }
        }
        if (isDetail) {
          await this.setQuesUseCount();
          this.$emit("render-finish", JSON.stringify(this.surface));
        }
      });
      // this.mathJaxUpdate();
    },
    isJudgeType(data) {
      return false;
    },
    mathJaxUpdate() {
      let success = false;
      if (MathJax && MathJax.startup) {
        try {
          MathJax.startup.getComponents();
          MathJax.typeset();
          success = true;
        } catch (e) {}
      }
    },
    /**
     * @name: 校本题面渲染
     * @param sq 小题
     * @param sqIndex 对应小题索引
     * @param length 对应答题下的所有小题数
     * @return: 题面
     */
    rebuildXBHtml(sq, sqIndex, length) {
      let res;
      let html = this.replacePracticeImgSrc(sq.desc_html);
      if (length > 1) {
        if (isNullOrUndefined(sq.quesNo)) {
          res = `<span class="qs-sort" style="float: left;display: inline-block;">${
            sqIndex + 1
          }、</span>${html}`;
        } else {
          res = `<span class="qs-sort" style="float: left;display: inline-block;">${sq.quesNo}、</span>${html}`;
        }
      } else {
        res = `${html}`;
      }
      return res;
    },
    /**
     * @name: 获取难度星星等级
     * @param difficuity 难度
     * @param isJY 是否优选题库,默认false
     * @return: 难度等级
     */
    getDiffStart(difficuity, isJY = false) {
      return getDiffStartStar(difficuity, isJY);
    },
    /**
     * @name: 强化练习(大数据)图片路径替换
     * @param html 待替换文本
     * @return: 替换后的文本
     */
    replacePracticeImgSrc(html) {
      if (isNullOrUndefined(html)) {
        return "";
      }
      // this.mathJaxUpdate();
      html = html.replace(/&nbsp;/g, " ");
      html = replaceFillevaAnsSign(html);
      return html;
    },
    /**
     * @name: 检测是否含有特定标签
     * @param html 待检测文本内容
     * @return: 是否含有特定标签
     */
    isHtmlTag(html) {
      return (
        new RegExp(/<[^0-9|>]+>/g).test(html) ||
        new RegExp(/(&w+;)/gi).test(html) ||
        /(<veido>)|(<audio>)|(<bdo>)(<p>)|(<span>)|(<a>)|(\[nn\])|(\[\[img\]\])|(<img)|\/\(/g.test(
          html
        )
      );
    },
    /**
     * @name: 是否JSON
     * @param param 待检测片段
     */
    isJSON(param = "") {
      return this.$isJson(param);
    },
    /**
     * @name: 设置题目使用次数
     */
    async setQuesUseCount() {
      if (this.surface.useCount) {
        return;
      }
      const res = await getQuesUseCount({
        teaId: this.$sessionSave.get("loginInfo").id,
        subId: this.$route.query.subjectId,
        queIds: this.id,
        schoolId:
          this.$sessionSave.get("schoolInfo").id || this.$sessionSave.get("loginInfo").schoolid,
      });
      if (res.data && res.data[0]) {
        this.surface.useCount = res.data[0].useCount;
      }
    },
    /**
     * @name: 展示资源播放弹窗
     * @param item 播放资源对象
     */
    showVideoPlayer(item) {
      this.defaultPlayerResource = {
        url: item.resourceUrl,
        title: item.resourceName,
        show: true,
      };
    },
    /**
     * @name: 获取知识点关联资源
     */
    async getTagResource() {
      this.tagResourceList = [];
      let tags = [];
      if (this.surface.levelcode == "") {
        tags = this.surface.tag_ids;
      } else {
        if (this.surface.qs && this.surface.qs.length > 0) {
          this.surface.qs.forEach((sq) => {
            if (sq.tag_ids && sq.tag_ids.length != 0) {
              tags = [...tags, ...sq.tag_ids];
            }
          });
        }
      }
      if (!tags) {
        tags = [];
      } else {
        tags = this.$arrayUnique(tags, "id");
      }
      if (tags.length) {
        await this.getTagResource1(tags);
      }
    },
    async getTagResource1(tags) {
      if (tags.length == 0) {
        return;
      }
      const res = await listResAPI({ pointId: tags[0].id });
      this.tagResourceList = [...this.tagResourceList, ...res.data];
      tags.shift();
      if (tags.length > 0) {
        await this.getTagResource1(tags);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.item ::v-deep q p {
  word-break: break-word;
}

.pt2,
.pt4.pt5,
.pt6.pt7,
.pt8 {
  padding-left: 20px;
}

.pt1 ::v-deep del {
  color: #f56c6c;
}

.selectoption {
  &.isAnswer {
    display: table-row-group;
    border: 1px solid #3e73f6;
    border-radius: 3px;
    color: #3e73f6;
    background: rgba(62, 114, 244, 0.05);
  }
}

.q_tag_resource {
  position: relative;
  height: 66px;
  width: 66px;
  display: inline-block;
  margin-right: 5px;
  margin-bottom: 5px;
  cursor: pointer;

  img {
    display: inline-block;
    height: 100%;
    width: 100%;
  }

  &:hover {
    &:before {
      display: block;
    }
  }
}

.is-video {
  &:before {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    display: none;
    width: 100%;
    height: 100%;
    content: "";
    cursor: pointer;
    background: rgba(0, 0, 0, 0.5) url("../../assets/bank/play.png") no-repeat 50%/20px;
  }
}

.diffculty ::v-deep .el-rate {
  line-height: 20px;
  margin-top: 3px;
}
.surface-option {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}
</style>

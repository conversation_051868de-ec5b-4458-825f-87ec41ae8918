<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-11-05 11:13:58
 * @LastEditors: 小圆
-->
<template>
  <div class="statistic-scan clearfix">
    <div class="statistic-scan--left fl">
      <div ref="chart" class="chart"></div>
    </div>
    <div class="statistic-scan--right fr">
      <el-table
        stripe
        :data="examStatList"
        :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
        style="width: 100%; border: 1px solid #ebeef5"
        :row-style="rowStyle"
        :max-height="tableHeight"
      >
        <el-table-column prop="className" label="班级" align="center" fixed min-width="130">
        </el-table-column>
        <el-table-column prop="examNum" label="测评数" align="center"> </el-table-column>
        <el-table-column prop="totalNum" label="应扫" align="center"> </el-table-column>
        <el-table-column prop="scanNum" label="实扫" align="center"> </el-table-column>
        <el-table-column prop="noScanNum" label="未扫" align="center">
          <template #header>
            <span class="red">未扫</span>
          </template>

          <template #default="scope">
            <span class="red">{{ scope.row.noScanNum }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { QueryData } from './types';

import * as echarts from 'echarts';
import { ECharts, EChartsOption } from 'echarts';
import moment from 'moment';
import { getExamStat } from '@/service/pexam';

export interface ExamStat {
  classId: string;
  className: string;
  examNum: number;
  scanNum: string;
  totalNum: string;
  noScanNum: string;
}


const HeaderLineHeight = 48;
const RowLineHeight = 48;
const TableHeight = 600;

@Component
export default class StatisticAnalysisScan extends Vue {
  @Prop({ default: {} }) queryData: QueryData;
  public $refs!: {
    chart: HTMLDivElement;
  };

  // 数据列表
  examStatList: ExamStat[] = [];
  // 图表
  chart: ECharts = null;

  mounted() {
    this.getExamStatList();
    this.$bus.$on('statistic-change', this.getExamStatList);
    window.addEventListener('resize', this.resizeChart);
  }

  beforeDestroy() {
    this.$bus.$off('statistic-change', this.getExamStatList);
    window.removeEventListener('resize', this.resizeChart);
  }

  async getExamStatList() {
    this.examStatList = [];
    let res = await getExamStat({
      schoolId: this.$sessionSave.get('schoolInfo').id,
      gradeId: this.queryData.gradeValue,
      subjectId: this.queryData.subjectValue,
      startTime: moment(this.queryData.dateRange[0]).format('YYYY-MM-DD'),
      endTime: moment(this.queryData.dateRange[1]).format('YYYY-MM-DD'),
      type: 1,
    });
    this.examStatList = res.data;
    this.setRowLineHeight();
    this.initChart();
  }

  resizeChart() {
    if (this.chart) {
      this.chart.resize();
    }
  }

  initChart() {
    if (!this.chart) {
      this.chart = echarts.init(this.$refs.chart);
    }

    const option: EChartsOption = {
      tooltip: {
        trigger: 'item',
      },
      legend: {
        top: 10,
        right: 70,
        show: true,
      },
      xAxis: {
        type: 'category',
        data: this.examStatList.map(item => item.className),
        name: '班级',
      },
      yAxis: {
        type: 'value',
        name: '实扫',
      },
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 0,
          end: 100,
        },
      ],
      series: [
        {
          name: '实扫',
          data: this.examStatList.map(item => item.scanNum),
          type: 'bar',
          barMaxWidth: 30,
          color: '#409EFF',
          label: {
            show: true,
            position: 'top',
            offset: [0, -2],
            color: '409EFF',
          },
        },
      ],
    };

    this.chart.setOption(option);
  }

  tableHeight = TableHeight;
  rowLineHeight = RowLineHeight;

  setRowLineHeight() {
    let row = this.examStatList.length;
    let rowLineHeight = RowLineHeight;
    let rowsHeight = row * rowLineHeight;
    // 铺满表格，高度不够时，自动增加行高
    if (rowsHeight < TableHeight - HeaderLineHeight) {
      rowLineHeight = (TableHeight - HeaderLineHeight) / row - 9 / row; // 9为滚动条长度
    }
    this.rowLineHeight = rowLineHeight;
  }

  rowStyle({ row, rowIndex }) {
    return {
      height: this.rowLineHeight + 'px',
    };
  }
}
</script>

<style scoped lang="scss">
.fl {
  float: left;
}

.fr {
  float: right;
}

.statistic-scan {
  width: 100%;
  padding: 20px;

  .statistic-scan--left {
    width: 60%;
  }

  .statistic-scan--right {
    width: 40%;
  }
}

.chart {
  width: 100%;
  height: 600px;
}

.red {
  color: #f56c6c;
}
</style>

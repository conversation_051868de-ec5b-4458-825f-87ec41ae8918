<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-04-29 16:27:33
 * @LastEditors: 小圆
-->
<template>
  <div>
    <div class="word-header">
      <div class="header__select">
        <TimeSelect v-model="params.time" @init="initTime" @change="changeTime"></TimeSelect>
      </div>
      <div class="header__select">
        <span class="select__label">年级：</span>
        <el-select
          v-model="params.gradeId"
          style="width: 120px"
          class="source-select"
          @change="changeGrade"
          placeholder="请选择"
        >
          <el-option v-for="item in gradeList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
        </el-select>
      </div>
      <div class="header__select">
        <span class="select__label">班级：</span>
        <el-select
          v-model="params.classId"
          style="width: 120px"
          class="source-select"
          @change="changeClass"
          placeholder="请选择"
        >
          <el-option v-for="item in classList" :key="item.clsId" :label="item.clsName" :value="item.clsId"> </el-option>
        </el-select>
      </div>

      <div class="header__select">
        <span class="select__label">得分率：</span>

        <div class="score-input-container">
          <el-input-number
            class="score-input"
            v-model="params.minRate"
            :step="1"
            :step-strictly="true"
            :controls="false"
            :min="0"
            :max="params.maxRate"
          >
          </el-input-number>
          <div class="score-input-unit">%</div>
        </div>

        <span class="score-input-split"> - </span>
        <div class="score-input-container">
          <el-input-number
            class="score-input"
            v-model="params.maxRate"
            :step="1"
            :step-strictly="true"
            :controls="false"
            :min="params.minRate"
            :max="100"
          ></el-input-number>
          <div class="score-input-unit">%</div>
        </div>
      </div>

      <div class="header__select">
        <el-button icon="el-icon-search" type="primary" @click="handleCurrentChange(1)"> 查询 </el-button>
      </div>
    </div>

    <div v-loading="loading">
      <el-table
        ref="elTable"
        class="word-table"
        row-key="wordId"
        :data="wrongWordList"
        :header-cell-style="defaultHeader"
        :row-style="{ cursor: 'pointer' }"
        @row-click="handleRowClick"
        @select="handleSelect"
        @select-all="handleSelectAll"
      >
        <el-table-column type="selection" width="80"> </el-table-column>
        <el-table-column label="词汇" prop="word" :resizable="false" show-overflow-tooltip />
        <el-table-column label="得分率" prop="scoreRate" align="center" :resizable="false" show-overflow-tooltip>
          <template #default="{ row }">
            <span :class="{ red: Number(row.scoreRate) < 60 }"> {{ row.scoreRate }}% </span>
          </template>
        </el-table-column>
        <el-table-column label="考察次数" prop="examNum" align="center" :resizable="false" show-overflow-tooltip>
          <template #default="{ row }">
            <span> {{ row.examNum }}次 </span>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        class="pagination"
        style="margin-top: 20px; text-align: center"
        background
        layout="total, prev, pager, next"
        :total="pagination.total"
        :page-size="pagination.limit"
        :current-page="pagination.page"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script lang="ts">
import NumericInput from '@/components/NumericInput.vue';
import TimeSelect from '@/components/SelectComponents/timeSelect.vue';
import { ClassList, getWrongWordListAPI } from '@/service/pexam';
import UserRole from '@/utils/UserRole';
import { ElTable } from '@iclass/element-ui/types/table';
import { CSSProperties } from 'vue';
import { Component, Ref, Vue } from 'vue-property-decorator';
import WordStore from './WordStore';

export interface WrongWord {
  id: string;
  wordId: string;
  word: string;
  examId: number;
  scoreRate: string;
  examNum: string;
  translation: string;
}

@Component({
  components: {
    TimeSelect,
    NumericInput,
  },
})
export default class ErrWord extends Vue {
  defaultHeader = {
    background: 'rgba(234, 242, 255, 0.5)',
    color: '#2E4B81',
    fontWeight: 'bold',
    fontSize: '16px',
    width: '100%',
    height: '51px',
  } as CSSProperties;
  @Ref('elTable') elTable: ElTable;

  // 高频错词列表
  wrongWordList: WrongWord[] = [];
  // 查询参数
  params = {
    time: '',
    gradeId: '',
    classId: '',
    minRate: 0,
    maxRate: 60,
  };
  // 年级列表
  gradeList = [];
  // 班级列表
  classList = [];
  // 分页器
  pagination = {
    page: 1,
    limit: 10,
    total: 0,
  };
  // 是否加载中
  loading: boolean = false;
  // 单词商店
  WordStore = WordStore;


  async mounted() {

  }
  
  async initTime() {
    await this.getGradeList();
    await this.getClassList();
    this.getWrongWordList();
  }

  // 获取年级列表
  async getGradeList() {
    const ret = await UserRole.getUserInfoPersonalityTest();
    this.gradeList = ret.schGrdList;
    this.params.gradeId = this.gradeList[0].id;
  }

  // 获取班级列表
  async getClassList() {
    const res = await ClassList({
      schoolId: this.$sessionSave.get('schoolInfo').id,
      subjectId: '',
      gradeId: this.params.gradeId || '',
      startTime: this.params.time[0],
      endTime: this.params.time[1],
    });
    const classList = res.data || [];
    classList.unshift({
      clsId: '',
      clsName: '全部',
    });
    this.classList = classList;
    this.params.classId = this.classList[0].clsId;
  }

  // 获取高频错词列表
  async getWrongWordList() {
    this.loading = true;
    const res = await getWrongWordListAPI({
      schoolId: this.$sessionSave.get('schoolInfo').id,
      startTime: this.params.time[0],
      endTime: this.params.time[1],
      gradeId: this.params.gradeId,
      classId: this.params.classId,
      minRate: this.params.minRate / 100,
      maxRate: this.params.maxRate / 100,
      page: this.pagination.page,
      limit: this.pagination.limit,
    });
    this.wrongWordList = res.data.rows || [];
    this.pagination.total = res.data.total_rows;
    this.loading = false;

    this.$nextTick(() => {
      this.setDefaultRowSelection();
    });
  }

  // 设置默认选中
  setDefaultRowSelection() {
    this.wrongWordList.forEach(item => {
      if (WordStore.choseErrList.find(word => word.id == item.wordId)) {
        this.elTable.toggleRowSelection(item, true);
      }
    });
  }

  // 点击行
  handleRowClick(row: WrongWord) {
    if (WordStore.choseErrList.find(item => item.id == row.wordId)) {
      WordStore.choseErrList = WordStore.choseErrList.filter(item => item.id != row.wordId);
      this.elTable.toggleRowSelection(row, false);
    } else {
      this.pushWordStore(row);
      this.elTable.toggleRowSelection(row, true);
    }
  }

  // 选中
  handleSelect(selection: WrongWord[], row: WrongWord) {
    this.handleRowClick(row);
  }

  // 全选
  handleSelectAll(selection: WrongWord[]) {
    if (selection.length == 0) {
      WordStore.choseErrList = WordStore.choseErrList.filter(item => {
        return !this.wrongWordList.find(word => word.wordId == item.id);
      });
    } else {
      selection.forEach(item => {
        if (!WordStore.choseErrList.find(word => word.id == item.wordId)) {
          this.pushWordStore(item);
        }
      });
    }
  }

  // 添加到单词商店
  pushWordStore(item: WrongWord) {
    WordStore.choseErrList.push({
      id: item.wordId,
      word: item.word,
      translation: item.translation,
    });
  }

  // 更改分页
  handleCurrentChange(page: number) {
    this.pagination.page = page;
    this.getWrongWordList();
  }

  // 时间选择
  async changeTime() {
    await this.getClassList();
    this.handleCurrentChange(1);
  }

  // 年级选择
  async changeGrade() {
    await this.getClassList();
    this.handleCurrentChange(1);
  }

  // 班级选择
  async changeClass() {
    this.handleCurrentChange(1);
  }
}
</script>

<style scoped lang="scss">
.word-header {
  display: flex;
  margin: 10px 0;

  .header__select {
    display: flex;
    align-items: center;
    margin-right: 10px;

    ::v-deep {
      .el-input__inner {
        font-size: 16px;
        color: #7f8795;
        height: 34px;
        border-radius: 6px;
        border: 1px solid #e9e9e9;
      }

      .el-button {
        width: 86px;
        height: 34px;
        font-size: 16px;
        border-radius: 6px;
        padding: 0;
      }
    }
  }

  .score-input-container {
    display: flex;
    border-radius: 6px;

    .score-input {
      width: 60px;
      line-height: 1;

      ::v-deep {
        .el-input__inner {
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
        }
      }
    }

    .score-input-unit {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 26px;

      font-size: 16px;
      color: #7f8795;
      text-align: center;
      background: #e9eced;
      border-radius: 0 6px 6px 0;
    }
  }

  .score-input-split {
    margin: 0 10px;
  }
}

.word-table {
  border: 1px solid #f6f6f6;
  border-radius: 10px 10px 0 0;
}

.red {
  color: red;
}
</style>

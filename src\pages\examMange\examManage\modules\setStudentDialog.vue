<template>
  <el-dialog :title="title" :visible.sync="modalVisible" width="800px" :before-close="handleClose"
    class="set-student-dialog">
    <div class="dialog-content">
      <el-tabs v-model="activeTab" class="student-tabs">
        <el-tab-pane label="已参考学生" name="参考学生"></el-tab-pane>
        <el-tab-pane label="未参考学生" name="未参考学生"></el-tab-pane>
      </el-tabs>

      <!-- 已选学生显示区域 -->
      <div class="selected-students-section" v-if="selectedStudents.length > 0">
        <div class="selected-students-container">
          <span class="student-tag">已选 ({{ selectedStudents.length }}人)</span>：<el-tag
            v-for="(student, index) in selectedStudents" :key="index" class="student-tag" closable
            @close="removeSelectedStudent(student)">
            {{ student.realName }}({{ student.stuNumber }})
          </el-tag>
        </div>
      </div>

      <div class="filter-section">
        <el-select v-model="selectedClass" placeholder="班级" @change="handleCurrentChange(1)" class="class-select">
          <el-option v-for="item in classList" :key="item.id" :label="item.class_name" :value="item.id">
              {{ item.class_name }}
              <span v-if="item.campusName" style="font-size: 12px; color: #999;">（{{ item.campusName }}）</span>
          </el-option>
        </el-select>
        <el-input placeholder="搜索姓名或考号" v-model="searchKeyword" clearable class="search-input">
          <el-button slot="append" icon="el-icon-search" @click="handleCurrentChange(1)"></el-button>
        </el-input>
        <el-button v-if="activeTab == '参考学生'" class="delete-btn" type="danger"
          @click="handleBatchDelete">删除所选学生</el-button>
        <el-button v-else class="add-btn" type="primary" @click="handleBatchAdd">新增所选学生</el-button>
      </div>

      <div class="table-section">
        <el-table :data="studentList" style="width: 100%" @selection-change="handleSelectionChange" :empty-text="emptyText" ref="studentTable">
          <el-table-column prop="className" label="班级"></el-table-column>
          <el-table-column prop="realName" label="姓名"></el-table-column>
          <el-table-column prop="stuNumber" label="考号"></el-table-column>
          <el-table-column label="全选" type="selection" width="120"></el-table-column>
        </el-table>
      </div>
      <el-pagination
        style="text-align: center"
        background
        layout="prev, pager, next, jumper"
        :hide-on-single-page="true"
        :total="pagination.total_rows"
        :page-size="pagination.limit"
        :current-page="pagination.page"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <!-- <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </span> -->
  </el-dialog>
</template>

<script>
import { classList } from '@/service/api';
import { getExamStuListAPI, addExamStuAPI, delExamStuAPI } from '@/service/pexam';
export default {
  name: 'SetStudentDialog',
  props: {
    modalVisible: {
      type: Boolean,
      default: false
    },
    subjectInfo: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    emptyText() {
      return !(this.activeTab === '未参考学生' &&  this.selectedClass == "" && !this.studentList?.length) ? '暂无数据' : '请输入学生姓名或切换班级查询'
    }
  },
  data() {
    return {
      title: '',
      activeTab: '参考学生',
      selectedClass: '',
      searchKeyword: '',
      checkAll: false,
      classList: [],
      studentList: [],
      selectedStudents: [],
      pagination: {
        total_rows: 0,
        limit: 10,
        page: 1
      },
      isLoading: false, // 添加一个加载标志
    }
  },
  watch: {
    activeTab: {
      handler(newVal) {
        // 切换标签时，应该清空已选学生，因为这是两个不同的列表
        this.selectedStudents = [];
        this.searchKeyword = '';
        this.pagination.page = 1
        this.getStuListByClassId()
      },
      immediate: true
    }
  },
  async created() {
    this.title = `参考学生管理（${this.subjectInfo.subjectName}）`
    await this.getClassList()
    this.getStuListByClassId()
  },
  methods: {
    handleClose() {
      this.$emit('close-set-stu')
    },
    handleConfirm() {
      this.$emit('confirm-set-stu', this.selectedStudents)
    },
    handleCurrentChange(page) {
      this.pagination.page = page
      this.getStuListByClassId()
    },
    async getClassList() {
      let result = await classList(this.subjectInfo.year, this.subjectInfo.phase, 1);
      // 调用接口获取班级列表
      this.classList = [{ id: '', class_name: '全部' }, ...result.data.rows,];
      this.selectedClass = this.classList[0].id;
    },
    async getStuListByClassId() {
      this.isLoading = true; // 设置加载标志为 true
      
      let isJoin = this.activeTab === "参考学生";
      let params = {
        pBookId: this.subjectInfo.personalBookId,
        classId: this.selectedClass,
        keyWord: this.searchKeyword,
        type: isJoin ? 1 : 0,
        page: this.pagination.page,
        limit: this.pagination.limit
      };
      
      let result = await getExamStuListAPI(params);
      this.studentList = result.data.rows;
      this.pagination.total_rows = result.data.total_rows;
      
      // 在表格数据加载后，恢复已选学生的选中状态
      this.$nextTick(() => {
        this.restoreSelections();
        this.isLoading = false; // 数据加载和选择恢复完成后，设置加载标志为 false
      });
    },
    handleCheckAllChange(val) {
      this.$refs.studentTable.toggleAllSelection()
    },
    handleSelectionChange(val) {
      // 如果正在加载数据，不处理选择变化
      if (this.isLoading) return;
      
      // 合并新选择的学生与已有的选择，避免清空
      const newSelectedMap = new Map();

      // 保留原有已选学生
      this.selectedStudents.forEach(student => {
        newSelectedMap.set(student.userId, student);
      });

      // 添加新选择的学生或移除取消选择的学生
      const currentIds = this.studentList.map(item => item.userId);
      const valIds = val.map(item => item.userId);

      // 处理当前页面上的学生选择状态
      currentIds.forEach(id => {
        const student = this.studentList.find(item => item.userId === id);
        if (valIds.includes(id)) {
          // 如果当前页面上选中了，就添加到已选列表
          newSelectedMap.set(id, student);
        } else {
          // 如果当前页面上取消了选中，就从已选列表中移除
          newSelectedMap.delete(id);
        }
      });

      // 检查选择的学生数量是否超过100人
      const newSelectedStudents = Array.from(newSelectedMap.values());
      if (newSelectedStudents.length > 100) {
        this.$message.warning('最多只能选择100名学生，已自动取消多余选择');
        
        // 只保留前100个选中的学生
        const limitedStudents = newSelectedStudents.slice(0, 100);
        newSelectedMap.clear();
        limitedStudents.forEach(student => {
          newSelectedMap.set(student.userId, student);
        });
        
        // 在表格中更新选择状态
        this.$nextTick(() => {
          // 清除所有选择
          this.$refs.studentTable.clearSelection();
          
          // 只选择前100个学生
          this.studentList.forEach(row => {
            if (limitedStudents.some(s => s.userId === row.userId)) {
              this.$refs.studentTable.toggleRowSelection(row, true);
            }
          });
        });
      }

      // 更新已选学生列表
      this.selectedStudents = Array.from(newSelectedMap.values());
      this.checkAll = val.length === this.studentList.length && this.studentList.length > 0;
    },
    // 恢复已选学生的选中状态
    restoreSelections() {
      if (!this.$refs.studentTable) return;
      
      // 临时禁用 selection-change 事件处理
      const originalSelectionChange = this.$refs.studentTable.$on;
      this.$refs.studentTable.$on = function(event, callback) {
        if (event !== 'selection-change') {
          originalSelectionChange.call(this, event, callback);
        }
      };
      
      const selectedIds = this.selectedStudents.map(item => item.userId);
      // 遍历当前表格数据，恢复选中状态
      this.studentList.forEach(row => {
        if (selectedIds.includes(row.userId)) {
          this.$refs.studentTable.toggleRowSelection(row, true);
        }
      });
      
      // 恢复 selection-change 事件处理
      this.$refs.studentTable.$on = originalSelectionChange;
    },
    removeSelectedStudent(student) {
      // 从选择列表中移除特定学生
      this.selectedStudents = this.selectedStudents.filter(item => item.userId !== student.userId)

      // 更新表格中的选择状态
      const rowIndex = this.studentList.findIndex(item => item.userId === student.userId)
      if (rowIndex !== -1) {
        this.$refs.studentTable.toggleRowSelection(this.studentList[rowIndex], false)
      }
    },
    async handleBatchAdd() {
      if (this.selectedStudents.length === 0) {
        this.$message.warning('请选择要添加的学生')
        return
      }
      let params = {
        pBookId: this.subjectInfo.personalBookId,
        classIds: this.selectedStudents.map(item => item.classId).join(","),
        stuIds: this.selectedStudents.map(item => item.userId).join(","),
      };
      await addExamStuAPI(params);
      this.selectedStudents = []
      this.$message.success('添加成功')
      this.getStuListByClassId()
    },
    handleBatchDelete() {
      if (this.selectedStudents.length === 0) {
        this.$message.warning('请选择要删除的学生')
        return
      }
      this.$confirm('确认删除所选学生?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let params = {
          pBookId: this.subjectInfo.personalBookId,
          classIds: this.selectedStudents.map(item => item.classId).join(","),
          stuIds: this.selectedStudents.map(item => item.userId).join(","),
        };
        await delExamStuAPI(params);
        this.selectedStudents = []
        this.$message.success('删除成功')
        this.getStuListByClassId()
      }).catch(() => { })
    }
  }
}
</script>

<style lang="scss" scoped>
.set-student-dialog {
  .dialog-content {
    padding: 0 20px;
  }

  .filter-section {
    display: flex;
    margin-bottom: 20px;
    gap: 15px;
    position: relative;

    .class-select {
      width: 150px;
    }

    .search-input {
      width: 200px;
    }

    .delete-btn,
    .add-btn {
      position: absolute;
      right: 0;
    }
  }

  .selected-students-section {
    margin-bottom: 20px;
    border: 1px solid #EBEEF5;
    border-radius: 4px;

    .section-header {
      padding: 5px;
      background-color: #F5F7FA;
      border-bottom: 1px solid #EBEEF5;
      font-weight: bold;
      color: #606266;
      font-size: 14px;
    }

    .selected-students-container {
      max-height: 90px;
      /* 3行的高度 */
      overflow-y: auto;
      padding: 10px 15px;

      .student-tag {
        margin-right: 0;
        margin-bottom: 0;
        margin: 5px;
      }
    }
  }

  .table-section {
    max-height: 450px;
    overflow: auto;

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }
  }


  :deep(.el-dialog__body) {
    padding-top: 10px;

    .el-table__header-wrapper {
      .el-table-column--selection {
        .el-checkbox__input::after {
          content: "全选";
          color: #606266;
          font-size: 14px;
          font-weight: 600;
          position: absolute;
          top: 0;
          left: 20px;
        }
      }
    }
  }

  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
  }

  :deep(.el-tabs__item) {
    font-size: 14px;
    height: 40px;
    line-height: 40px;
  }

  :deep(.el-table) {
    th {
      background-color: #f5f7fa;
      color: #606266;
    }
  }
}
</style>
<template>
    <el-dialog class="batch-transfer-dialog" title="批量转移" :visible.sync="dialogVisible" width="880px"
        :before-close="handleClose">
        <div class="dialog-content">
            <div class="selection-header">
                <span>请选择需要转移的图片</span>
                <el-checkbox v-model="selectedAll" @change="selectAll">全选</el-checkbox>
            </div>

            <div class="images-grid">
                <div v-for="(image, index) in imageList" :key="index" class="image-item">
                    <div class="image-wrapper">
                        <!-- 左切换按钮 -->
                        <div class="switch-btn left" @click="switchImage(image, 'prev')" v-if="image.images.length > 1">
                            <i class="el-icon-arrow-left"></i>
                        </div>

                        <!-- 图片显示 -->
                        <el-image :src="image.images[image.currentIndex || 0]" :fit="'scale-down'"
                            :preview-src-list="image.images" alt="">
                        </el-image>

                        <!-- 右切换按钮 -->
                        <div class="switch-btn right" @click="switchImage(image, 'next')"
                            v-if="image.images.length > 1">
                            <i class="el-icon-arrow-right"></i>
                        </div>

                        <!-- 图片序号指示器 -->
                        <div class="image-indicator" v-if="image.images.length > 1">
                            {{ (image.currentIndex || 0) + 1 }}/{{ image.images.length }}
                        </div>
                    </div>
                    <div class="image-info" :title="`${image.batchId}-第${image.idx}张`">
                        {{ image.batchId }}-第{{ image.idx }}张
                    </div>
                    <div class="image-select">
                        <el-checkbox v-model="image.selected" @change="updateSelection">选择</el-checkbox>
                    </div>
                </div>
            </div>
        </div>

        <span slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="confirmTransfer" :disabled="selectedCount === 0">转移</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { getErrorImages } from '@/service/pexam';
export default {
    name: 'BatchTransferDialog',
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
        examId: {
            type: String,
            default: ''
        },
        errType: {
            type: String,
            default: ''
        },
        schoolId: {
            type: String,
            default: ''
        },
    },
    data() {
        return {
            imageList: [],
            selectedAll: false
        };
    },
    computed: {
        selectedCount() {
            return this.imageList.filter(img => img.selected).length;
        }
    },
    watch: {
        images: {
            handler(newVal) {
                if (newVal && newVal.length) {
                    this.imageList = this.initImageList(newVal);
                }
            },
            immediate: true
        }
    },
    created() {
        this.getErrorImages();
    },
    methods: {
        async getErrorImages() {
            let params = {
                examId: this.examId,
                code: this.errType == 'paper' ? 1 : 2,//1：答卷异常，2：定位异常
            }
            let res = await getErrorImages(params);
            this.imageList = res.data.map(img => ({
                ...img,
                images: (img.imageList.map(item => {
                    return item.image || item.origin
                })),
                currentIndex: 0,
                selected: false
            }));
        },
        handleClose() {
            this.$emit('update:dialogVisible', false);
            this.$emit('close');
        },
        selectAll() {
            this.updateSelection();
            this.selectedAll = !this.selectedAll;
            this.imageList.forEach(img => {
                img.selected = this.selectedAll;
            });
        },
        updateSelection() {
            this.selectedAll = this.imageList.every(img => img.selected);
        },
        confirmTransfer() {
            const selectedImages = this.imageList.filter(img => img.selected);
            this.$emit('confirm', selectedImages);
        },
        // 切换图片方法
        switchImage(image, direction) {
            if (!image.currentIndex) {
                image.currentIndex = 0;
            }

            if (direction === 'next') {
                image.currentIndex = (image.currentIndex + 1) % image.images.length;
            } else {
                image.currentIndex = (image.currentIndex - 1 + image.images.length) % image.images.length;
            }
        },
        // 初始化图片列表时为每个图片对象添加当前索引
        initImageList(images) {
            return images.map(img => ({
                ...img,
                currentIndex: 0,
                selected: false
            }));
        }
    }
};
</script>

<style lang="scss" scoped>
.batch-transfer-dialog {
    .dialog-content {
        padding: 0 10px;
    }

    .selection-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        span {
            font-size: 14px;
            color: #606266;
        }
    }

    .images-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        max-height: 460px;
        overflow-y: auto;

        .image-item {
            width: 180px;
            display: flex;
            flex-direction: column;
            border: 1px solid #ebeef5;
            border-radius: 4px;
            overflow: hidden;

            .image-wrapper {
                height: 120px;
                position: relative;
                overflow: hidden;
                display: flex;
                justify-content: center;
                align-items: center;
                background-color: #f5f7fa;

                .switch-btn {
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                    background-color: rgba(0, 0, 0, 0.3);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                    transition: all 0.3s;
                    z-index: 2;

                    &:hover {
                        background-color: rgba(0, 0, 0, 0.5);
                    }

                    i {
                        color: #fff;
                        font-size: 16px;
                    }

                    &.left {
                        left: 8px;
                    }

                    &.right {
                        right: 8px;
                    }
                }

                .image-indicator {
                    position: absolute;
                    bottom: 8px;
                    left: 50%;
                    transform: translateX(-50%);
                    background-color: rgba(0, 0, 0, 0.3);
                    color: #fff;
                    padding: 2px 8px;
                    border-radius: 10px;
                    font-size: 12px;
                    z-index: 2;
                }

                ::v-deep .el-image {
                    width: 100%;
                    height: 100%;

                    img {
                        max-width: 100%;
                        max-height: 100%;
                        object-fit: contain;
                    }
                }
            }

            .image-info {
                padding: 8px;
                font-size: 12px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                color: #606266;
                border-top: 1px solid #ebeef5;
                border-bottom: 1px solid #ebeef5;
            }

            .image-select {
                padding: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #f5f7fa;

                span {
                    margin-left: 5px;
                    font-size: 12px;
                    color: #606266;
                }
            }
        }
    }
}
</style>

import Cookies from 'js-cookie'

const TokenKey = 'class30_token'
const UserIdKey = 'class30_userid'

export function getToken () {
    return Cookies.get(TokenKey)
}

export function getUserId () {
    return Cookies.get(UserIdKey)
}

export function setToken (token) {
    return Cookies.set(Token<PERSON>ey, token)
}

export function setUserId (userId) {
    return Cookies.set(User<PERSON>d<PERSON><PERSON>, userId)
}

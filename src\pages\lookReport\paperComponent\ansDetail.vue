<!--
 * @Descripttion: 
 * @Author: 小圆
 * @Date: 2023-11-14 09:20:32
 * @LastEditors: Please set LastEditors
-->
<template>
  <div class="ques-detail">
    <el-alert :title="chooseAlertText" type="warning" :closable="false" v-if="item.choice" />

    <div class="display_flex align-items_flex-start" style="margin-top: 5px">
      <div v-if="'01258'.includes(quesType)" class="obj">
        <div v-for="(ans, k) in item.ansMap" :key="k" class="ans">
          <span class="ans-btn" :class="{ 'is-right': rightAnswer.indexOf(k) >= 0 }">{{ k }}</span><span
            class="blue first">{{ ans.length }}</span><span>人</span>
          <div>
            <span class="item" v-for="(val, valIndex) in ans"
              :key="valIndex">{{ showAnonymousName ? val.anonymous : val.name }}</span>
          </div>
        </div>
      </div>

      <div v-if="'3467'.includes(quesType)" class="sub">
        <div class="score-split-lalel">点击下方学生姓名可查看作答情况 <el-button icon="el-icon-edit" type="text"
            @click="showCustomScoreSplit = true" v-if="quesType != '7'">自定义分数段</el-button></div>
        <div v-for="(ans, k) in item.ansMap" :key="k" class="ans" v-show="ans.length">
          <span class="ans-btn">{{ k }}</span><span class="blue first">{{ ans.length }}</span><span>人</span>
          <div class="score-stu-list">
            <span class="item" :class="canViewPaper ? ['blue', 'click-element'] : 'pointer-none'" v-for="(val, valIndex) in ans"
              :key="valIndex" @click="goToPaper(val, item)">{{ showAnonymousName ? val.anonymous : val.name }}
              {{ showStudentScore && k.includes(']') ? `(${val.score}分)` : '' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 多选题展示选择统计 -->
    <div class="display_flex align-items_flex-start" style="margin-top: 15px;" v-if="quesType == '1'">
      <div class="obj" style="width: 100%;">
        <div class="label" style="padding: 10px 0;border-top:1px solid #e4e7ed;">作答统计：</div>
        <div v-for="(ans, k) in item.optionStusMap" :key="k" class="ans">
          <span class="ans-btn" :class="[
            rightAnswer == k ? 'is-right' : ans[0].score > 0 ? 'is-half-right' : 'is-wrong'
          ]">{{ k }}</span><span class="blue first">{{ ans.length }}</span><span>人</span>
          <div>
            <span class="item" v-for="(val, valIndex) in ans"
              :key="valIndex">{{ showAnonymousName ? val.anonymous : val.name }}</span>
          </div>
        </div>
      </div>
    </div>

    <el-tabs type="border-card" v-model="currentAnsName"
      v-if="isShowGoodAnsList || isShowBadAnsList || isShowGrdGoodAnsList">
      <el-tab-pane name="good" :label="'优秀作答' + `(${goodAnsList.length})`" v-if="isShowGoodAnsList">
        <AnsImgItem v-for="(ansItem, index) in goodAnsList" :key="ansItem.url" :ansItem="ansItem"
          @open-preview="openSpecialPreview"></AnsImgItem>
      </el-tab-pane>
      <el-tab-pane name="bad" :label="'典型错误' + `(${badAnsList.length})`" v-if="isShowBadAnsList">
        <AnsImgItem v-for="(ansItem, index) in badAnsList" :key="ansItem.url" :ansItem="ansItem"
          @open-preview="openSpecialPreview"></AnsImgItem>
      </el-tab-pane>
      <el-tab-pane name="grdgood" :label="'年级优秀' + `(${grdGoodAnsList.length})`" v-if="isShowGrdGoodAnsList">
        <AnsImgItem v-for="(ansItem, index) in grdGoodAnsList" :key="ansItem.url" :ansItem="ansItem"
          @open-preview="openGrdGoodPreview"></AnsImgItem>
      </el-tab-pane>
    </el-tabs>

    <StuQuesPreview :stuList="stuList" :imgs="stuImgs" :initialStuIndex="stuIndex" :queryParams="{
      workId: personBookId,
      schoolId: schoolId,
      quesNo: quesNo,
      tQuesNo: tQuesNo,
    }" @close="isShowPaperImg = false" @evaluate="handelStuEvaluate" v-if="isShowPaperImg">
    </StuQuesPreview>

    <!-- 自定义分数段弹窗 -->
    <el-dialog title="自定义分数段" :visible.sync="showCustomScoreSplit" width="500px" center>
      <br>
      <div class="custom-score-form">
        分数段：
        <el-input-number v-model="scoreSplit" :precision="0" :min="1" :step="1" :max="item.fullScore" label="请输入分数"
          size="small" />
        分一段
        <div class="custom-tip" v-text="`注：该题满分 ${item.fullScore} 分`"></div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showCustomScoreSplit = false">取 消</el-button>
        <el-button type="primary" @click="confirmScoreSplit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import AnsImgItem from './ansImgItem.vue';
import LookPaperDialog from '@/components/LookPaperDialog.vue';
import StuQuesPreview from '@/components/SwiperViewer/StuQuesPreview.vue';
import { getBatchQuesPointSourceList } from '@/service/api';
import { mapState } from "vuex";

export default {
  components: {
    AnsImgItem,
    LookPaperDialog,
    StuQuesPreview,
  },
  props: {
    item: {
      type: Object,
    },

    item: {
      type: Boolean,
    },

    canViewPaper: {
      type: Boolean,
    },

    stusCount: {
      type: Number,
    },

    personBookId: {
      type: String,
    },
  },
  data() {
    return {
      isShowPaperImg: false,
      stuTableData: [],
      // 学生列表
      stuList: [],
      // 当前学生对象
      stuItem: null,
      // 当前学生索引
      stuIndex: 0,
      // 当前题目是否特殊作文题
      isSpecialWriting: false,
      // 学生图片列表
      stuImgs: [],
      // 当前tab
      currentAnsName: 'good',
      // 作答优秀列表
      goodAnsList: [],
      // 典型错误列表
      badAnsList: [],
      // 年级优秀
      grdGoodAnsList: [],
      // 显示自定义分数段
      showCustomScoreSplit: false,
      // 分段值
      scoreSplit: 1
    };
  },

  watch: {
    item() {
      this.updateGoodBadList();
    },
  },

  mounted() {
    this.scoreSplit = this.item.scoreSplit || 1;
    this.updateGoodBadList();
  },

  computed: {
    ...mapState(["showAnonymousName", "showStudentScore"]),
    // 选做题提示
    chooseAlertText() {
      let choice = this.item.choice;
      if (!choice) return "";

      let doStuCount = 0;
      for (const key in this.item.ansMap) {
        const stus = this.item.ansMap[key];
        doStuCount += stus.length
      }
      let doStuRate = Math.round(doStuCount / this.stusCount * 100);
      return `选做题（${choice.ids.length}选${choice.doCount}），${doStuCount}人已选，占比${doStuRate}%`;
    },
    // 正确答案
    rightAnswer() {
      if (this.item.type == 2) {
        return this.item.rightAnswer === 'A' ? this.item.judgeNames[0] : this.item.judgeNames[1];
      }
      if (this.item && this.item.rightAnswer) {
        return this.item.rightAnswer;
      }
      if (this.content && this.content.answer) {
        return this.content.answer;
      }
      return '';
    },

    // 是否显示典型错误
    isShowBadAnsList() {
      return !!this.badAnsList.length;
    },

    // 是否显示优秀作答
    isShowGoodAnsList() {
      return !!this.goodAnsList.length;
    },
    // 是否显示年级优秀
    isShowGrdGoodAnsList() {
      return !!this.grdGoodAnsList.length;
    },

    // 题目序号
    quesNo() {
      return this.item.quesNumber;
    },

    tQuesNo() {
      return this.item.tQuesNo;
    },

    // 学校id
    schoolId() {
      return (
        this.$route.query.schoolId ||
        this.$sessionSave.get('schoolInfo')?.id ||
        this.$sessionSave.get('configInfo')?.schoolid ||
        this.$sessionSave.get('loginInfo')?.schoolid
      );
    },

    // 题型
    quesType() {
      return String((this.content && this.content.data && this.content.data.type) || this.item.quesType)
    }
  },

  methods: {
    // 根据题目切换优秀典型数据
    async updateGoodBadList() {
      // 作答优秀列表
      this.goodAnsList = await this.initAnsList('goodAnsList');
      // 典型错误列表
      this.badAnsList = await this.initAnsList('badAnsList');
      // 年级优秀
      this.grdGoodAnsList = await this.initAnsList('grdGoodAnsList');

      if (this.isShowGoodAnsList) {
        this.currentAnsName = 'good';
      } else if (this.isShowBadAnsList) {
        this.currentAnsName = 'bad';
      } else if (this.isShowGrdGoodAnsList) {
        this.currentAnsName = 'grdgood';
      }
    },
    /**
     * @description: 初始化合并 优秀、典型错误列表
     * @param {*} key goodAnsList、badAnsList、grdGoodAnsList
     */
    async initAnsList(key) {
      if (!this.item) return [];
      if (!this.item[key]) return [];
      if (!this.item[key].length) return [];

      const ansList = this.item[key];
      if (key === "grdGoodAnsList") {
        await this.setAnsListImgs(ansList)
        return ansList;
      }

      // 根据stuId分组
      const stuItem = ansList.reduce((prev, current) => {
        const key = current['stuId'];
        const curGroup = prev[key] ?? [];
        return { ...prev, [key]: [...curGroup, current] };
      }, {});

      // 合并图片
      const arr = [];
      ansList.forEach((item, index) => {
        const ansItemList = stuItem[item.stuId];
        if (!ansItemList) return;
        const obj = {
          evaluateState: item.evaluateState,
          id: item.id,
          stuId: item.stuId,
          stuName: item.stuName,
          anonymous: item.anonymous,
          resList: [],
          url: [],
        };
        ansItemList.forEach(item => {
          obj.resList.push({
            resId: item.resId,
            url: item.url,
          });
          let url = item.url;
          obj.url.push(url);
        });
        arr.push(obj);
        stuItem[item.stuId] = null;
      });
      return arr;
    },

    // 手动设置优秀作答图片
    async setAnsListImgs(ansList) {
      let stus = ansList.filter(item => !item.url);
      if (stus.length === 0) return;

      const res = await getBatchQuesPointSourceList({
        schoolId: this.schoolId,
        workId: this.personBookId,
        stuNos: stus.map((item) => item.stuNo).join(","),
        quesNo: this.quesNo,
        tQuesNo: this.tQuesNo,
        version: "2.0",
      });

      let resData = res.data || [];
      stus.forEach(stu => {
        const resItem = resData.find(item => item.stuNo === stu.stuNo);
        if (resItem) {
          stu.url = resItem.imgList[0];
        }
      });
    },

    /**
     * @description: 确认分段值
     */
    confirmScoreSplit() {
      this.item.scoreSplit = this.scoreSplit
      this.showCustomScoreSplit = false;
      this.$emit("updateScoreSplit")
    },

    // 查看原卷
    async goToPaper(stuItem, item) {
      this.stuList = [];
      this.stuTableData = [];
      this.stuItem = null;
      this.stuImgs = [];

      let stuList = [];
      for (const key in this.item.ansMap) {
        let stus = this.item.ansMap[key];
        stuList = [...stuList, ...stus];
      }
      stuList.forEach(stu => {
        stu.state = this.getStuCommentState(stu.id);
      })
      this.stuList = stuList;
      this.stuIndex = this.stuList.findIndex(stu => stuItem.id === stu.id);
      this.stuItem = stuItem;
      this.isShowPaperImg = true;
    },

    // 打开优秀/典型预览
    openSpecialPreview([stuId, imgs]) {
      this.stuList = [];
      this.stuTableData = [];
      this.stuItem = null;
      this.stuImgs = [];

      let specialList = this.goodAnsList.concat(this.badAnsList);
      let stuMap = new Map();
      for (const key in this.item.ansMap) {
        let stus = this.item.ansMap[key];
        stus.forEach(stu => stuMap.set(stu.id, stu));
      }

      for (const ansItem of specialList) {
        let stu = stuMap.get(ansItem.stuId);
        this.stuList.push({
          id: ansItem.stuId,
          name: ansItem.stuName,
          anonymous: ansItem.anonymous,
          score: stu.score,
          uName: stu.uName,
          state: this.getStuCommentState(ansItem.stuId)
        });

        if (ansItem.stuId === stuId) this.stuItem = stu;
      }

      this.stuIndex = this.stuList.findIndex(stu => stuId === stu.id);
      this.isShowPaperImg = true;
    },

    // 打开年级优秀
    openGrdGoodPreview([stuId, imgs]) {
      this.stuTableData = [];
      this.stuItem = null;
      this.stuImgs = [];
      this.stuList = [];

      for (const ansItem of this.grdGoodAnsList) {
        this.stuList.push({
          id: ansItem.stuId,
          name: ansItem.stuName,
          anonymous: ansItem.anonymous,
          uName: ansItem.stuNo,
          url: ansItem.url,
          score: ansItem.score,
          // 禁用评价
          state: -1
        });
      }

      this.stuIndex = this.stuList.findIndex(stu => stuId === stu.id);
      this.isShowPaperImg = true;
    },

    // 获取学生评价状态
    getStuCommentState(stuId) {
      if (this.item.goodAnsList.some(item => item.stuId == stuId)) {
        return 1;
      }
      else if (this.item.badAnsList.some(item => item.stuId == stuId)) {
        return 2;
      } else {
        return 0;
      }
    },

    // 处理学生评价
    async handelStuEvaluate({ state, data }) {
      // 取消评价
      this.goodAnsList = this.item.goodAnsList = this.item.goodAnsList.filter(item => item.stuId != data.stuId);
      this.badAnsList = this.item.badAnsList = this.item.badAnsList.filter(item => item.stuId != data.stuId);

      if (state == 0) {
        if (!this.goodAnsList.length) {
          this.currentAnsName = 'bad';
        }
        if (!this.badAnsList.length) {
          this.currentAnsName = 'good';
        }
        if (!this.goodAnsList.length && !this.badAnsList.length) {
          this.currentAnsName = 'grdgood';
        }
      } else {
        // 设置学生评价
        if (state == 1) {
          this.currentAnsName = 'good';
          this.item.badAnsList = this.item.badAnsList.filter(item => item.stuId != data.stuId);
          this.item.goodAnsList.push(data);
          this.goodAnsList = await this.initAnsList('goodAnsList');
        } else {
          this.currentAnsName = 'bad';
          this.item.goodAnsList = this.item.goodAnsList.filter(item => item.stuId != data.stuId);
          this.item.badAnsList.push(data);
          this.badAnsList = await this.initAnsList('badAnsList');
        }
      }
    }
  },

};
</script>

<style lang="scss" scoped>
.custom-score-form {
  margin: 0 auto;
  width: 240px;

  .el-input-number {
    width: 120px;
    margin-right: 10px;
  }

  .custom-tip {
    margin-top: 15px;
    font-size: 14px;
    color: #F56C6C;
  }
}

.ques-detail {
  padding: 20px;

  .score-split-lalel {
    margin-top: -15px;
    font-size: 14px;
    color: #666;

    >.el-button {
      margin-left: 10px;
    }
  }

  .score-stu-list {
    margin-top: -2px;

    >.item {
      margin-bottom: 10px;
    }
  }

  >div {
    margin-bottom: 10px;
  }

  .resourceList {
    margin-top: 10px;
    margin-bottom: 0;
  }

  .answer-imgList {
    display: inline-block;
    width: 85px;
    height: 50px;
    margin-right: 10px;
    cursor: pointer;
    margin-bottom: 10px;
    border: 1px solid #cecece;
  }

  .sub {
    span {
      line-height: 28px;
    }

    .ans-btn {
      width: 80px;
      min-width: 80px;
      height: 28px;
    }

    .last {
      width: calc(100% - 110px);
    }
  }

  .obj {
    span {
      line-height: 28px;
    }

    .ans-btn {
      width: 100px;
      min-width: 100px;
      height: 28px;
    }

    .last {
      width: calc(100% - 110px);
    }
  }

  .ans {
    display: flex;
    padding: 4px 0;

    .ans-btn {
      display: inline-block;
      border: 1px solid #ccc;
      line-height: 26px;
      text-align: center;
      border-radius: 2px;

      &.is-right {
        color: #fff;
        background-color: rgb(57, 206, 177);
        border-color: rgb(7, 194, 157);
      }

      &.is-half-right {
        color: #fff;
        background-color: rgb(255, 187, 25);
        border-color: rgb(255, 187, 25);
      }

      &.is-wrong {
        color: #fff;
        background-color: rgb(247, 137, 137);
        border-color: rgb(247, 137, 137);
      }
    }

    .blue {
      color: #409effff;
      padding-left: 10px;
      display: inline-block;
    }

    .item {
      padding-left: 10px;
      display: inline-block;
    }
  }
}

.pagination-tab {
  display: flex;
  align-items: center;

  .pagination-tab__text {
    flex: 1;
    text-align: center;
    font-size: 18px;
  }
}
</style>

<!--
 * @Descripttion: 
 * @Author: 小圆
 * @Date: 2023-11-29 20:07:03
 * @LastEditors: 小圆
-->
<template>
  <div class="statistic-li clearfix" :class="{ active: active }">
    <div class="statistic-li-box clearfix">
      <div class="statistic-li--left">
        <div class="statistic-li__icon">
          <i class="iconfont" :class="[icon]"></i>
        </div>
      </div>
      <div class="statistic-li--right">
        <div class="statistic-li__title">{{ title }}</div>
        <div class="statistic-li__number">
          共 <span class="number">{{ value }}</span> {{ unitText }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    active: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Number,
      default: 0,
    },
    title: {
      type: String,
      default: "",
    },
    unitText: {
      type: String,
      default: "",
    },
    imgSrc: {
      type: String,
      default: "",
    },
    icon: {
      type: String,
      default: "",
    },
  },
};
</script>

<style lang="scss" scoped>
.statistic-li {
  position: relative;
  padding-left: 1px;
  width: 100%;
  height: 140px;
  background: #f8f8f8;
  border-bottom: 1px solid rgba(195, 195, 195, 0.21);
  cursor: pointer;

  &:last-of-type {
    border-bottom: 0;
  }

  &:last-child {
    border-bottom: 0;
  }

  .statistic-li-box {
    padding-left: 20px;
    margin-top: 34px;
    margin-bottom: 46px;
  }
  .statistic-li--left {
    float: left;
  }

  .statistic-li--right {
    float: left;
    margin-left: 10px;
  }

  .statistic-li__icon {
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    background: #ffffff;
    border-radius: 8px;
    & > .iconfont {
      font-size: 28px;
      color: #606266;
    }
  }

  .statistic-li__title {
    padding-top: 6px;
    font-size: 20px;
    font-weight: 400;
    color: #303233;
    line-height: 1;
  }

  .statistic-li__number {
    padding-top: 16px;
    font-size: 14px;
    font-weight: 400;
    color: #606266;
    line-height: 1;
    .number {
      color: #303233;
    }
  }

  &.active {
    background: #ffffff;
    border: 0;

    &::after {
      content: "";
      display: block;
      position: absolute;
      top: 0;
      left: 1px;

      width: 5px;
      height: 123px;

      border-radius: 2px;
      background-color: #008dea;
    }
    .statistic-li__title {
      color: #008dea;
      font-weight: 700;
    }

    .statistic-li__icon {
      background: rgba(222, 242, 255, 0.52);
      & > .iconfont {
        color: #008dea;
      }
    }
  }
}
</style>

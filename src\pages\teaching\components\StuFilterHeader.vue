<template>
  <div class="score-header">
    <!-- 类别 -->
    <div class="header__select">
      <span class="select__label">类别：</span>
      <el-select
        v-model="filterData.categoryId"
        :multiple="categoryMultiple"
        collapse-tags
        style="width: 135px"
        class="source-select"
        placeholder="请选择类别"
        @change="changeCategory"
      >
        <div style="padding: 5px" v-if="categoryMultiple">
          <el-button @click="checkAllCategory(true)">全选</el-button>
          <el-button @click="checkAllCategory(false)">全不选</el-button>
        </div>
        <el-option v-for="item in categoryList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
      </el-select>
    </div>

    <!-- 学科 -->
    <div class="header__select">
      <span class="select__label">学科：</span>
      <el-select v-model="filterData.subjectId" style="width: 120px" placeholder="请选择学科" @change="changeSubject">
        <el-option v-for="item in subjectList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
      </el-select>
    </div>

    <!-- 年级 -->
    <div class="header__select">
      <span class="select__label">年级：</span>
      <el-select
        v-model="filterData.gradeId"
        style="width: 120px"
        class="source-select"
        placeholder="请选择年级"
        @change="changeGrade"
      >
        <el-option v-for="item in gradeList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
      </el-select>
    </div>

    <!-- 班级 -->
    <div class="header__select" v-if="!hideClass">
      <span class="select__label">班级：</span>
      <el-select
        v-model="filterData.classId"
        style="width: 120px"
        class="source-select"
        placeholder="请选择班级"
        @change="changeClass"
      >
        <el-option v-for="item in classList" :key="item.classId" :label="item.class_name" :value="item.classId">
        </el-option>
      </el-select>
    </div>

    <!--时间类型-->
    <div class="header__select" v-if="hideClass || classList.length">
      <span class="select__label">时间：</span>
      <el-select v-model="filterData.timeType" style="width: 100px; margin-right: 25px" @change="changeTimeType">
        <el-option v-for="item in timeTypeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
      </el-select>

      <span v-if="filterData.timeType === 3">
        <!-- 学年 -->
        <el-select v-model="filterData.startYear" style="width: 130px; margin-right: 10px" @change="changeStartYear">
          <el-option v-for="item in startYearList" :key="item.id" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <span>-&nbsp;&nbsp;</span>
        <el-select v-model="filterData.endYear" style="width: 130px; margin-right: 10px" @change="changeEndYear">
          <el-option v-for="item in endYearList" :key="item.id" :label="item.label" :value="item.value"> </el-option>
        </el-select>
      </span>

      <!-- 按月 -->
      <el-date-picker
        v-if="filterData.timeType === 2"
        popper-class="datePicker__time"
        style="margin-right: 10px; width: 230px"
        v-model="filterData.timeSlot"
        :clearable="false"
        type="monthrange"
        align="right"
        range-separator="-"
        start-placeholder="开始月份"
        end-placeholder="结束月份"
        @change="changeMonth"
        :picker-options="timeSelect.pickerOptions"
      >
      </el-date-picker>
      <!-- 按日 -->
      <el-date-picker
        v-if="filterData.timeType === 1"
        popper-class="datePicker__time"
        style="margin-right: 10px; width: 230px"
        v-model="filterData.timeSlot"
        :clearable="false"
        type="daterange"
        align="right"
        unlink-panels
        format="yyyy-MM-dd"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="changeDate"
        :picker-options="timeSelect.pickerOptionsDay"
      >
      </el-date-picker>
    </div>
    <el-button type="primary" :loading="isLoading" @click="$emit('onSearch')">查询</el-button>
  </div>
</template>

<script lang="ts">
import { getSchoolYearListAPI } from '@/service/api';
import UserRole, { IUserGrdList, IUserSubList } from '@/utils/UserRole';
import moment from 'moment';
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';

export interface FilterData {
  /** 来源 */
  source: number[];
  /** 类别 */
  categoryId: string | number | string[] | number[];
  /** 学科 */
  subjectId: string;
  /** 年级 */
  gradeId: string | number;
  /** 入学年份 */
  year: string | number;
  /** 班级 */
  classId: string;
  /** 学生姓名 */
  realname: string;
  /** 学生ID */
  stuId: string;
  // 时间类型
  timeType: number;
  // 开始时间
  startYear: string;
  // 结束时间
  endYear: string;
  // 时间范围
  timeSlot: string[];
}

@Component
export default class StuFilterHeader extends Vue {
  // 双向绑定
  @PropSync('value', {
    type: Object,
    default: () => {
      return {
        /** 来源 */
        source: [1],
        /** 类别 */
        categoryId: '' as any,
        /** 学科 */
        subjectId: '' as any,
        /** 年级 */
        gradeId: '' as any,
        /** 入学年份 */
        year: null,
        /** 班级 */
        classId: '',
        /** 学生姓名 */
        realname: '',
        /** 学生ID */
        stuId: '',
        // 时间类型
        timeType: 3,
        // 开始时间
        startYear: '',
        // 结束时间
        endYear: '',
        // 时间范围
        timeSlot: [],
      } as FilterData;
    },
  })
  filterData: FilterData;
  // 是否隐藏班级
  @Prop({ type: Boolean, default: false }) hideClass: boolean;
  // 是否多选类别
  @Prop({ type: Boolean, default: false }) categoryMultiple: boolean;

  // 时间筛选类型
  timeTypeList = [
    { value: 1, label: '按日' },
    { value: 2, label: '按月' },
    { value: 3, label: '学年' },
  ];
  // 时间类型
  timeType = 3;
  timeSelect = {
    startYear: '',
    endYear: '',
    defTimeSlot: [],
    timeSlot: [],
    yearTimeSlot: '',
    pickerMinDate: '',
    pickerOptions: {
      //选择当前日期之前的时间
      onPick: obj => {
        this.timeSelect.pickerMinDate = new Date(obj.minDate).getTime();
      },
      //选择时间范围为三年
      disabledDate: time => {
        if (this.timeSelect.pickerMinDate) {
          const day1 = 3 * 365 * 24 * 3600 * 1000;
          let maxTime = this.timeSelect.pickerMinDate + day1;
          let minTime = this.timeSelect.pickerMinDate - day1;
          return time.getTime() > maxTime || time.getTime() < minTime;
        }
      },
    },
    pickerOptionsDay: {
      //选择当前日期之前的时间
      onPick: obj => {
        this.timeSelect.pickerMinDate = new Date(obj.minDate).getTime();
      },
      //可选择的时间范围为三年
      disabledDate: time => {
        if (this.timeSelect.pickerMinDate) {
          const day1 = 3 * 365 * 24 * 3600 * 1000;
          let maxTime = this.timeSelect.pickerMinDate + day1;
          let minTime = this.timeSelect.pickerMinDate - day1;
          return time.getTime() > maxTime || time.getTime() < minTime;
        }
      },
    },
  };
  /** 学年列表 */
  yearList = [];
  get startYearList() {
    return this.yearList;
  }

  get endYearList() {
    if (!this.filterData.startYear) return [];
    return this.yearList.filter(item => {
      return item.value > this.filterData.startYear;
    });
  }

  /** 来源列表 */
  sourceList = [
    { id: 1, name: '考试' },
    { id: 2, name: '个册' },
    { id: 3, name: '作业' },
  ];
  /** 分类列表 */
  categoryList = [];
  /** 学科列表 */
  subjectList: IUserSubList[] = [];
  /** 年级列表 */
  allGradeList: IUserGrdList[] = [];
  get gradeList() {
    if (this.filterData.subjectId == '') return this.allGradeList;
    let subject = this.subjectList.find(q => q.id == this.filterData.subjectId);
    let grdList = this.allGradeList.filter(q => q.phaseId == subject.phaseId);
    return grdList;
  }

  /** 班级列表 */
  classList = [];

  isLoading = false;

  async mounted() {
    await this.getCategoryList();
    await this.getSubjectList();
    this.filterData.subjectId = this.subjectList[0]?.id;
    await this.getGradeList();
    this.filterData.gradeId = this.gradeList[0]?.id;
    this.filterData.year = this.gradeList[0]?.year;

    if (!this.hideClass) {
      await this.getClassList();
      this.filterData.classId = this.classList[0]?.classId;
    }
    await this.getYearList();
    this.$emit('onMounted', this.filterData);
  }

  // 获取分类列表
  async getCategoryList() {
    this.categoryList = await UserRole.getAllType();
    if (!this.categoryMultiple) {
      this.categoryList.unshift({
        id: '',
        name: '全部',
      });
      this.filterData.categoryId = this.categoryList[0]?.id;
    } else {
      this.filterData.categoryId = [this.categoryList[0]?.id];
    }
  }

  // 获取学科列表
  async getSubjectList() {
    let subjectList = await UserRole.getSubjectList();
    if (UserRole.isOperation || UserRole.isSchoolLeader || UserRole.isGradeLeader || UserRole.isClassLeader) {
      subjectList.unshift({
        id: '',
        name: '全部学科',
      });
    }
    this.subjectList = subjectList;
  }

  // 获取年级列表
  async getGradeList() {
    let params = {
      subjectId: this.filterData.subjectId,
    };
    // 全部学科时，只获取年级主任、班主任角色
    if (!params.subjectId) {
      params.excludeRoles = [3, 4, 6];
    }
    this.allGradeList = await UserRole.getGradeList(params);
  }

  // 获取班级列表
  async getClassList() {
    this.isLoading = true;
    this.classList = [];
    this.filterData.classId = '';
    let grade = this.allGradeList.find(item => item.id == this.filterData.gradeId);
    try {
      let excludeRoles = [];
      // 全部学科时，只获取年级主任、班主任角色
      if (!this.filterData.subjectId) {
        excludeRoles = [3, 4, 6];
      }
      this.classList = await UserRole.getClassList({
        subjectId: this.filterData.subjectId,
        systemCode: grade?.systemCode,
        year: grade?.year,
        classType: -1,
        excludeRoles: excludeRoles,
      });
    } catch (error) {
      console.error(error);
      this.classList = [];
    }
    this.isLoading = false;
  }

  // 生成学年列表
  async getYearList() {
    // let yearList = [];
    // let date = new Date(),
    //     y = date.getFullYear(),
    //     M = date.getMonth() + 1,
    //     d = date.getDate();
    // let beginYear; // 入学年份
    // let endYear;
    // let phaseYear;

    // if (!this.hideClass) {
    //     let classObj = this.classList.find(item => item.classId == this.filterData.classId);
    //     if (classObj) {
    //         beginYear = classObj.year;
    //         phaseYear = parseInt(classObj.system_code.match(/\d+/)[0]) || 3;
    //     }
    // }
    // if (beginYear) {
    //     if (beginYear + phaseYear > y) {
    //         if (Number(`${M}${d < 10 ? `0${d}` : d}`) >= 810) {
    //             endYear = y + 1;
    //         } else {
    //             endYear = y;
    //         }
    //     } else {
    //         endYear = beginYear + phaseYear;
    //     }
    // } else {
    //     if ((M == 8 && d >= 10) || M >= 9) {
    //         beginYear = y + 1 - 5;
    //         endYear = y + 1;
    //     } else {
    //         beginYear = y - 5;
    //         endYear = y;
    //     }
    // }
    // for (let i = endYear; i >= beginYear; i--) {
    //     yearList.push({
    //         id: i,
    //         value: Number(`20${Number(i.toString().substring(1))}`),
    //         label: `${i}学年`,
    //     });
    // }

    const res = await getSchoolYearListAPI({
      schoolId: this.$sessionSave.get('schoolInfo').id,
    });
    let yearList = res.data || [];
    let list = [];
    yearList.forEach((item, index) => {
      if (index == 0) {
        list.push({
          id: moment(item.endTime).year(),
          value: moment(item.endTime).add(1, 'days').format('YYYY-MM-DD'),
          label: moment(item.endTime).format('YYYY') + '学年',
        });
        list.push({
          id: moment(item.startTime).year(),
          value: moment(item.startTime).format('YYYY-MM-DD'),
          label: moment(item.startTime).format('YYYY') + '学年',
        });
      } else {
        list.push({
          id: moment(item.startTime).year(),
          value: moment(item.startTime).format('YYYY-MM-DD'),
          label: moment(item.startTime).format('YYYY') + '学年',
        });
      }
    });
    if (!this.hideClass) {
      let classObj = this.classList.find(item => item.classId == this.filterData.classId);
      if (classObj) {
        let beginYear = classObj.year;
        let phaseYear = parseInt(classObj.system_code.match(/\d+/)[0]) || 3;
        let years = new Array(phaseYear + 1).fill(beginYear).map((item, index) => item + index);
        list = list.filter(item => years.includes(item.id));
      }
    }

    this.yearList = list;
    if (this.filterData.timeType == 3) this.setDefaultTime();
  }

  // 改变学科
  async changeSubject(id) {
    this.filterData.subjectId = id;
    await this.getGradeList();
    this.changeGrade(this.gradeList[0]?.id);
  }

  // 改变年级
  async changeGrade(id) {
    this.filterData.gradeId = id;
    this.filterData.year = this.gradeList.find(item => item.id == id)?.year;
    await this.getClassList();
    this.changeClass(this.classList[0]?.classId);
  }

  // 更改班级
  async changeClass(id) {
    this.filterData.classId = id;
    this.getYearList();
    this.$emit('change', this.filterData);
    this.$emit('change-class', this.filterData);
  }

  //切换时间筛选类型，按日默认最近一周，按月默认当前月
  changeTimeType() {
    this.timeSelect.pickerMinDate = '';
    let date = new Date(),
      y = date.getFullYear(),
      M = date.getMonth(),
      d = date.getDate();

    this.setDefaultTime();
    this.$emit('change', this.filterData);
    this.$emit('change-time', this.filterData);
  }

  changeStartYear(val) {
    this.filterData.endYear = this.endYearList[0]?.value;
    this.$emit('change', this.filterData);
    this.$emit('change-time', this.filterData);
  }

  changeEndYear(val) {
    this.filterData.endYear = val;
    this.$emit('change', this.filterData);
    this.$emit('change-time', this.filterData);
  }

  setDefaultTime() {
    if (this.filterData.timeType == 1) {
      const start = moment().subtract(7, 'days').format('YYYY-MM-DD');
      const end = moment().format('YYYY-MM-DD');
      this.filterData.timeSlot = [start, end];
    } else if (this.filterData.timeType == 2) {
      const start = moment().startOf('month').format('YYYY-MM-DD');
      const end = moment().endOf('month').format('YYYY-MM-DD');
      this.filterData.timeSlot = [start, end];
    } else if (this.filterData.timeType == 3) {
      this.filterData.startYear = this.yearList[1].value;
      this.filterData.endYear = this.yearList[0].value;
    }
  }

  changeMonth(val) {
    this.filterData.timeSlot = [
      moment(val[0]).startOf('month').format('YYYY-MM-DD'),
      moment(val[1]).endOf('month').format('YYYY-MM-DD'),
    ];
    this.changeTime();
  }

  changeDate() {
    this.changeTime();
  }
  changeTime() {
    this.$emit('change', this.filterData);
    this.$emit('change-time', this.filterData);
  }

  changeCategory(val) {
    this.filterData.categoryId = val;
    this.$emit('change', this.filterData);
  }

  checkAllCategory(flag) {
    if (flag) {
      this.filterData.categoryId = this.categoryList.map(item => item.id);
    } else {
      this.filterData.categoryId = [];
    }
    this.$emit('change', this.filterData);
  }
}
</script>

<style scoped lang="scss">
.score-header {
  position: relative;
  width: 100%;
  background: #fff;

  ::v-deep {
    .el-button {
      font-size: 16px;
    }

    .el-input__inner {
      font-size: 16px;
    }
  }

  .header__select {
    display: inline-block;
    margin-right: 20px;
    color: #2e4b81;
    font-size: 16px;
  }

  .filter-text {
    font-size: 16px;
    color: #666666;
    margin-left: 15px;
  }

  .grade-text {
    font-weight: 700;
  }

  .export-button {
    font-weight: 700;
  }
}
</style>

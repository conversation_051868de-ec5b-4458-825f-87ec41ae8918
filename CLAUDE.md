# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 Vue 2 的教育平台，用于考试管理、报告生成和数据分析。代码库使用 TypeScript，通过 Vue CLI 模式支持多种部署环境。

## 开发命令

### 开发服务器
```bash
npm run dev              # 标准开发模式，端口 1001
npm run dev:yc          # 生产环境模拟 (yc 模式)
npm run dev:sj          # 生产环境模拟 (sj 模式) 
npm run dev:sy          # 生产环境模拟 (sy 模式)
npm run dev:prod        # 生产模式测试
```

### 构建命令
```bash
npm run build           # 标准生产构建
npm run build:yc        # yc 环境构建
npm run build:sy        # sy 环境构建
npm run build:sj        # sj 环境构建
npm run build:test      # 测试环境构建
npm run build:analyz    # 带包分析器的构建
```

### 部署
```bash
npm run deploy:test     # 构建并部署到测试环境
```

### 包管理
```bash
npm install             # 安装依赖
npm run postinstall     # 安装后应用补丁 (自动运行)
```

## 架构

### 多环境支持
应用支持通过环境变量确定的不同部署渠道：
- **C30/YC/SJ**: 使用 `baseRouter` 处理标准教育平台功能
- **SY**: 使用 `syRouter` 处理区域学情分析功能

### 核心结构
- **Router**: 模块化路由系统，针对不同环境使用独立的路由文件
  - `baseRouter.ts`: 主应用路由
  - `syRouter.ts`: 区域学情分析路由
  - `routerMenu/` 中的路由模块用于不同功能区域
- **Store**: Vuex 存储，使用独立文件管理 state、actions、mutations、getters 的模块化结构
- **Services**: 按功能域组织的 API 层 (pexam、pbook、ptask 等)
- **Components**: 基于功能的组件组织，包含共享的 Base 组件

### 关键依赖
- **UI 框架**: 自定义 Element UI (`@iclass/element-ui`) 而非标准 Element UI
- **图表**: ECharts 用于数据可视化
- **数学公式**: MathLive 用于数学公式编辑，KaTeX 和 MathJax 用于渲染
- **文件处理**: ExcelJS 用于 Excel 操作，html2canvas 用于截图
- **存储**: 通过自定义 idb-store 插件集成 IndexedDB

### 构建配置
- **Webpack**: 对 Element UI、ECharts 和 Lodash 进行自定义块分割
- **代码分割**: 基于优先级的自动供应商块分割
- **压缩**: 生产构建启用 Gzip 压缩
- **包分析**: 通过 `npm run build:analyz` 可用

### 开发代理
为本地开发配置的 API 代理：
- `/pexam` → localhost:9806 (考试管理)
- `/pbook` → localhost:9800 (书本/作业本数据)
- `/ptask` → localhost:9801 (任务管理)
- `/eReport` → localhost:9999 (考试报告)

### 文件上传和存储
- 阿里云 OSS 集成，使用 STS 令牌认证进行文件上传
- 应用初始化时自动刷新令牌

## 重要说明

### 环境检测
应用使用渠道检测工具 (`isC30()`、`isYc()`、`isSj()`、`isSy()`) 来确定部署环境并相应调整功能。

### TypeScript 配置
- 宽松的 TypeScript 配置，`strict: false`
- 为 `@/*` (src 目录) 和自定义 Element UI 路径配置的路径别名

### 性能特性
- 基于路由 meta.keepAlive 的 Vue keep-alive 路由缓存
- 大数据集的虚拟滚动组件
- 自定义懒加载和无限滚动实现

### 数学公式支持
应用异步加载多个数学渲染库：
- KaTeX 用于快速公式渲染
- MathJax 用于全面的 LaTeX 支持
- MathLive 用于交互式公式编辑
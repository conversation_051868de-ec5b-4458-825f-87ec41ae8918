<template>
  <div class="workBook">
    <div class="workBook__header" v-if="!isCef">
      <div class="workBook__header__container display_flex justify-content_space-between align-items_center">
        <div class="header__left--logo">
          <img style="max-height: 50px" :src="logo" @dblclick="checkCard" />
          <el-select
            class="select_school"
            ref="schoolSelect"
            v-model="schoolId"
            v-if="$sessionSave.get('loginInfo') && $sessionSave.get('loginInfo').user_type == 5"
            filterable
            remote
            :remote-method="getOperSchoolList"
            placeholder="学校"
            size="mini"
          >
            <el-option v-for="item in schoolList" :key="item.schoolId" :label="item.schoolName" :value="item.schoolId">
            </el-option>
          </el-select>
          <div v-else class="top-school-name" :title="schoolInfo.school_name">
            <span v-if="schoolInfo && schoolInfo.school_name" class="showOverTooltip">
              <!-- <span class="line">|</span> -->
              <strong>{{ schoolInfo.school_name }}</strong>
            </span>
          </div>
        </div>
        <template v-if="!isNotShowTab">
          <div class="header__center--tab">
            <ul class="tab-container list-none">
              <li
                class="tab-item"
                v-for="item in tabList"
                :key="item.title"
                :class="{ active: item.id == activeId }"
                @click="gotoWorkBook(item)"
              >
                {{ item.title }}
              </li>
            </ul>
          </div>
          <div
            class="header__right--avator display_flex align-items_center"
            v-if="isShowAvator && $sessionSave.get('loginInfo')"
          >
            <el-tooltip
              content="专业版"
              v-if="JSON.stringify(vipInfo) != '{}' && !vipInfo.isNotUsed"
              placement="bottom"
              effect="light"
            >
              <div slot="content" class="vip-tips" style="font-size: 14px">
                <p>服务期：</p>
                <p style="font-weight: bold">{{ vipInfo.dateStart }} - {{ vipInfo.dateExpired }}</p>
                <!-- <br /> -->
                <!-- <p>考试使用情况：</p>
                <p style="font-weight: bold">
                  已使用 <span>{{ vipInfo.examUsed || 0 }}</span> /
                  <span>{{ vipInfo.examCount || 0 }}</span>
                </p> -->
                <br />
                <p>商务咨询：</p>
                <p style="font-weight: bold">{{ vipInfo.mobile }}</p>
                <br />
                <p>温馨提示：</p>
                <p style="font-weight: bold">
                  到期后，将<span style="color: red">无法继续上传试卷、创建答题卡和创建考试。</span
                  >为保障正常教学请提前咨询商务。
                </p>
              </div>
              <div class="vip-container">
                <div class="professional-edition" :class="{ trialEdition: vipInfo.roleId == 1 }">
                  <div class="professional-edition-title" :class="{ trialEditionTitle: vipInfo.roleId == 1 }">
                    <p>{{ vipInfo.roleId == 1 ? '标准版' : '专业版' }}</p>
                  </div>
                  <!-- <div class="professional-edition-desc" :class="{ trialEditionDesc: vipInfo.roleId == 1 }">
                    服务期限：<span>{{ vipInfo.dateExpired }}</span>
                  </div> -->
                </div>
              </div>
            </el-tooltip>

            <el-dropdown @command="handleComand">
              <span class="avator-name el-dropdown-link" v-if="$sessionSave.get('loginInfo')"
                >{{ $sessionSave.get('loginInfo').realname }}<i class="el-icon-caret-bottom el-icon--right"></i
              ></span>
              <el-dropdown-menu slot="dropdown" class="home-dropdown">
                <el-dropdown-item command="toZP" v-if="showZP">综合素质评价</el-dropdown-item>
                <el-dropdown-item command="toSchoolSetting" v-if="isAdminLeader">学校设置</el-dropdown-item>
                <el-dropdown-item command="toSchoolManage" v-if="isAdminorOperate">学校管理</el-dropdown-item>
                <el-dropdown-item command="changePassword">修改密码</el-dropdown-item>
                <el-dropdown-item command="exitLogin"><span style="color: red">退出登录</span></el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <!-- <span class="line">|</span>
            <span class="exitLogin" @click="exitLogin">帮助中心</span> -->
          </div>
        </template>
      </div>
    </div>
    <div class="workBook__body" :class="{ 'hide-header': isCef }">
      <el-alert v-if="weakPassword" type="warning" center show-icon :closable="false">
        <template slot="title">
          <p>
            为响应国家信息安全要求，需整改系统简单密码。请您于7月15日前修改登录密码（8-16位且至少包含大小写字母、数字、特殊符号两种），逾期未修改将无法登录。
            <el-button type="text" style="padding: 0" @click="changePassword">立即修改 ></el-button>
          </p>
        </template>
      </el-alert>
      <div class="routerBox">
        <div class="scrollContainer" ref="parentDiv" v-if="isInit">
          <keep-alive>
            <router-view
              class="router-view"
              ref="routerView"
              @scrollToTop="scrollToTop"
              v-if="$route.meta.keepAlive"
              :key="$route.path"
            ></router-view>
          </keep-alive>
          <router-view
            class="router-view"
            ref="routerView"
            @scrollToTop="scrollToTop"
            v-if="!$route.meta.keepAlive"
          ></router-view>
        </div>
      </div>
    </div>

    <!-- 侧边工具 -->
    <home-tool v-if="!isCef" @toggleAI="toggleAI"></home-tool>
    <ai-help v-if="isShowAI" @toggleAI="toggleAI"></ai-help>

    <change-pwd-dialog
      :visible.sync="showPasswordDialog"
      :isWeak="weakPassword"
      @success="handlePasswordSuccess"
      @cancel="handlePasswordCancel"
    />
  </div>
</template>

<script>
import UserRole from '@/utils/UserRole';
import { mapState } from 'vuex';
import { getOperSchoolList, getPublicConfigBySchoolInfo } from '../service/api';
import { isNullOrUndefined } from '@/utils';
import { isCef } from '@/utils/index';
import { getVipInfo, getSchoolAIScanSubjectAPI } from '../service/pexam';
import { getLogo, isYc } from '../utils/channel.js';
import HomeTool from '@/components/HomeTool.vue';
import AiHelp from '@/pages/help/components/AiHelp.vue';
import watermark from 'watermark-dom';
import ChangePwdDialog from '@/components/changePwdDialog.vue';

export default {
  name: 'App',
  components: {
    HomeTool,
    AiHelp,
    ChangePwdDialog,
  },
  data() {
    return {
      isCef: isCef(),
      logo: getLogo(),
      schoolId: this.$sessionSave.get('schoolInfo')?.id || '',
      allTabList: [
        {
          id: '0',
          title: '总览',
          link: 'supervise',
          childLink: [],
          roles: [1, 2, 3, 4],
          state: '1',
        },
        {
          id: '4',
          title: '校本题库',
          link: 'question',
          childLink: [],
          dictCode: '009',
          state: '1',
        },
        {
          id: '6',
          title: '学科网',
          link: 'xueke',
          childLink: [],
          dictCode: '034',
          state: '1',
        },
        {
          id: '2',
          title: '测评中心',
          link: 'examReport',
          childLink: [
            'previewTestQues',
            'scoreconfirom',
            'matchquesnum',
            'errornew',
            'answerSetting',
            'markPaperSetting',
            'markPaperSpeed',
          ],
          dictCode: '094',
          state: '1',
        },
        {
          id: '8',
          title: '学情分析',
          link: 'reportCenter',
          childLink: [],
          dictCode: '094',
          state: '1',
        },
        {
          id: '7',
          title: '诊断监测',
          link: 'studyReport',
          childLink: [],
          dictCode: '109',
          state: '1',
        },
        // {
        //   id: '1',
        //   title: '学情追踪',
        //   link: 'teaching',
        //   childLink: [],
        //   dictCode: '093',
        //   state: '1',
        // },
        {
          id: '3',
          title: '推题审核',
          link: 'personalAudit',
          childLink: [],
          dictCode: '039',
          state: '1',
        },
        {
          id: '5',
          title: '外部题库',
          link: 'externalQues',
          childLink: [],
          dictCode: '097',
          state: '1',
        },
      ],
      tabList: [],
      activeId: '2',
      showSchoolList: false,
      schoolList: [],
      timer: null,
      loadMounted: false,
      //一起题库是否配置
      yiqiQuestion: [],
      isShowAvator: true,
      //管理后台页面
      backStagePage: process.env.VUE_APP_BACKSTAGE,
      // 学校是否初始化完成
      isInit: false,
      showZP: isYc(),
      // 运营或校领导
      isAdminLeader: UserRole.isOperation || UserRole.isSchoolLeader,
      isShowAI: false,

      // 是否弱密码
      weakPassword: false,
      // 是否显示修改密码弹窗
      showPasswordDialog: false,
      // 是否强制修改密码
      isForceChangePassword: false,
    };
  },
  computed: {
    ...mapState(['schoolInfo', 'roleList', 'vipInfo']),
    //是否为校管、运营
    isAdminorOperate() {
      return (
        this.$sessionSave.get('loginInfo') &&
        (this.$sessionSave.get('loginInfo').user_type == 5 || this.$sessionSave.get('loginInfo').admin_type == 2)
      );
    },
    //不展示头部菜单栏
    isNotShowTab() {
      return this.$route.path == '/scan/deal' || this.$sessionSave.get('lookReportPage') == 'deal';
    },
  },
  watch: {
    $route(to, from) {
      this.isAdminLeader = UserRole.isOperation || UserRole.isSchoolLeader;
      this.matchingRoutes();
    },
    schoolId(val) {
      this.changeSchool(val);
    },
  },
  async mounted() {
    this.initCheckWeakPassword();

    // 进识别处理隐藏头像区
    if (this.$route.path == '/scan/insider') {
      this.isShowAvator = false;
      // if (!this.$route.query.isShowAvator) {}
    }
    this.matchingRoutes();
    this.loadMounted = true;
    await this.renderData();
    if (this.$sessionSave.get('loginInfo') && this.$sessionSave.get('loginInfo').user_type !== 5) {
      this.getPublicConfigBySchoolInfo();
      this.getEnglishAIScanPermission();
    }
    this.isInit = true;
    // let userInfo = this.$sessionSave.get('loginInfo')
    // watermark.init({
    //   watermark_txt: `${userInfo.realname}${userInfo.mobile||''}`,
    //   watermark_x:10,                     //水印起始位置x轴坐标
    //   watermark_y:10,                     //水印起始位置Y轴坐标
    //   watermark_x_space:400,              //水印x轴间隔
    //   watermark_y_space:200,               //水印y轴间隔
    //   watermark_fontsize:'18px',          //水印字体大小
    //   watermark_color:'black',            //水印字体颜色
    //   watermark_alpha:0.1,               //水印透明度，要求设置在大于等于0.005
    //   watermark_width:150,
    //   watermark_parent_node:null
    // })
  },
  activated() {
    // if (this.schoolId != this.$sessionSave.get('schoolInfo').id) {
    //   this.schoolId = this.$sessionSave.get('schoolInfo').id;
    // }
    // this.matchingRoutes();
    if (!this.loadMounted) {
      this.renderData();
    } else {
      this.loadMounted = false;
    }
    this.isInit = true;
  },
  methods: {
    initCheckWeakPassword() {
      this.weakPassword = !!this.$sessionSave.get('weakPassword');
      this.showPasswordDialog = !!this.$sessionSave.get('changePwdVisible');
      this.isForceChangePassword = !!this.$sessionSave.get('changePwdVisible');
    },

    toggleAI(isShow) {
      this.isShowAI = isShow;
    },
    scrollToTop() {
      this.$refs.parentDiv.scrollTop = 0;
    },
    handleComand(command) {
      this[command]();
    },

    // 修改密码
    changePassword() {
      this.isForceChangePassword = false;
      this.showPasswordDialog = true;
    },

    toSchoolSetting() {
      this.$router.push({ path: '/home/<USER>' });
    },

    toScanRecord() {
      this.$router.replace({ path: '/scan/' });
    },
    /**
     * @name: 考号设置
     */
    toSettingNumber() {
      this.$router.push({ path: '/home/<USER>/' });
    },
    async renderData() {
      this.matchingRoutes();
      let loginInfo = this.$sessionSave.get('loginInfo'); // && !this.schoolList.length
      if (loginInfo && loginInfo.user_type === 5 && !this.schoolList.length) {
        await this.getOperSchoolList();
      }
    },
    /**
     * @name:跳转到学校管理
     */
    toSchoolManage() {
      window.open(this.backStagePage, '_blank');
    },
    toZP() {
      let url = `${process.env.VUE_APP_ZP}?token=${this.$sessionSave.get('loginInfo').token}`;
      window.open(url, '_blank');
    },
    matchingRoutes(data) {
      if (data) {
        this.activeId = data;
        return;
      }
      let localPath = data || this.$route.path;
      for (let item of this.allTabList) {
        let hasLink = false;
        item.childLink.forEach(sub => {
          if (localPath.indexOf(sub) >= 0) {
            hasLink = true;
          }
        });
        if (localPath.indexOf('/home/' + item.link) !== -1 || hasLink) {
          this.activeId = item.id;
          return;
        }
      }
      this.activeId = '';
    },
    // 设置学校
    changeSchool(item) {
      this.$router.push({ query: {} });
      let routerPath = this.$route.path;
      let scanInfoPage = /scan\/.+/.test(routerPath);
      if (routerPath.indexOf('reportDetail') >= 0 || routerPath.indexOf('classDetail') >= 0 || scanInfoPage) {
        if (item !== this.schoolInfo.schoolId) {
          // this.selectDisabled = true;
          this.$confirm(`确定要退出当前页面吗?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              this.$refs.schoolSelect.blur();
              this.$sessionSave.set('changeSchool', true);

              this.$nextTick(() => {
                this.handleData(item);

                if (scanInfoPage) {
                  this.$router.replace('/scan');
                } else {
                  this.$router.push({
                    path: `/home/<USER>'reportDetail') >= 0 ? 'examReport' : 'teaching'}`,
                  });
                }
              });
            })
            .catch(() => {
              this.schoolId = this.schoolInfo.schoolId;
              this.$refs.schoolSelect.blur();
            });
        }
      } else {
        this.handleData(item);
      }
    },
    handleData(item) {
      this.schoolId = item;
      let schoolInfo = this.schoolList.find(it => it.schoolId === item);
      if (!schoolInfo.id) {
        this.$set(schoolInfo, 'id', schoolInfo.schoolId);
      }
      this.$store.commit('saveSchoolInfo', schoolInfo);
      this.$sessionSave.set('schoolInfo', schoolInfo);
      this.getVipInfo(schoolInfo);
      this.$nextTick(() => {
        if (this.$route.path.indexOf('teaching') != -1 || this.$route.path.indexOf('supervise') != -1) {
          this.$bus.emit('changeSchool');
          this.$refs.routerView && this.$refs.routerView.changeSchool && this.$refs.routerView.changeSchool('patch');
        } else {
          this.lookProgress();
        }
      });
      this.getPublicConfigBySchoolInfo('changeSchool', item);
      this.$sessionSave.remove('currentSubject');
      this.$store.dispatch('initData');
    },
    lookProgress() {
      // 如果已经跳转到对应tab页面, 开始初始化头部筛选项，请求数据
      if (
        this.$route.path.indexOf('examReport') !== -1 &&
        this.$refs.routerView &&
        this.$refs.routerView.changeSchool
      ) {
        clearTimeout(this.timer);
        this.timer = null;
        this.$bus.emit('changeSchool');
        this.$refs.routerView && this.$refs.routerView.changeSchool && this.$refs.routerView.changeSchool();
      } else {
        this.timer = setTimeout(() => {
          this.lookProgress();
        }, 500);
      }
    },
    // 获取代理商学校列表
    getOperSchoolList(keyword = '') {
      return getOperSchoolList({
        userId: this.$sessionSave.get('loginInfo').id,
        userType: 5,
        page: 1,
        limit: 100,
        keyWord: keyword,
      }).then(data => {
        this.schoolList = data.data.list[0].schools;
        this.$store.commit('saveSchoolList', this.schoolList);
        if (!this.schoolList.length) return;
        if (!this.schoolId) {
          if (!this.schoolList[0].id) {
            this.$set(this.schoolList[0], 'id', this.schoolList[0].schoolId);
          }
          this.$store.commit('saveSchoolInfo', this.schoolList[0]);
          this.getVipInfo(this.schoolList[0]);
          this.schoolId = this.schoolList[0].id;
        } else {
          this.schoolId = this.schoolId;
        }
        this.$nextTick(() => {
          // if (this.$route.path.indexOf("examReport") >= 0) {
          //   this.$refs.routerView && this.$refs.routerView.initSchool();
          // }
          if (this.$route.path.indexOf('teaching') >= 0) {
            this.$refs.routerView && this.$refs.routerView.changeSchool();
          }
          this.getPublicConfigBySchoolInfo('initialize', this.schoolId);
        });
      });
    },
    // 切换路由
    gotoWorkBook(item) {
      if (this.activeId == item.id) return;
      // 在考试报告内页面点击切换推题审核时提示，是否要退出考试报告
      if (item.id === '2' && this.$route.path.indexOf('reportDetail') !== -1) {
        this.$confirm('确定要退出当前考试报告吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.activeId = item.id;
            this.$router.push({ path: `/home/<USER>
          })
          .catch(() => {});
      } else {
        this.activeId = item.id;
        this.$router.push({ path: `/home/<USER>
      }
    },
    // 点击空白处隐藏音量设置弹框
    hideFun(ev) {
      let $box = this.$parentIndexOf(document.elementFromPoint(ev.x, ev.y), 'schoolList__ul');
      if (!$box) {
        this.showSchoolList = false;
      }
    },
    /**
     * @name:获取学校是否开通一起题库,菜单权限
     */
    getPublicConfigBySchoolInfo(type, schoolId) {
      let code = [
        '00201',
        '00202',
        '093',
        '094',
        '039',
        '009',
        '097',
        '034',
        '105',
        '109',
        '113',
        '115',
        '116',
        '125',
        '127',
        '084',
        '138',
        '147',
      ];
      getPublicConfigBySchoolInfo({
        schoolId: schoolId || this.$sessionSave.get('schoolInfo').id || this.$sessionSave.get('loginInfo').schoolid,
        dictCode: code.join(','),
        userId: this.$sessionSave.get('loginInfo').id,
      })
        .then(data => {
          this.handlePermissions(data.data, type);
        })
        .catch(err => {
          console.error(err);
        });
    },
    getEnglishAIScanPermission() {
      //获取英语学科权限
      getSchoolAIScanSubjectAPI({
        schoolId: this.$sessionSave.get('schoolInfo').id || this.$sessionSave.get('loginInfo').schoolid,
      }).then(res => {
        let subjectIds = res.data.filter(
          item => item.aiCorrect == 1 && item.quesType == 1 && [3, 12, 25].includes(Number(item.subjectId))
        );
        this.$sessionSave.set('tbAICorrect', subjectIds.length > 0 ? '1' : '0');
      });
    },
    /**
     * @name:处理菜单权限
     */
    handlePermissions(data, type) {
      let loginInfo = this.$sessionSave.get('loginInfo');
      //外部题库权限
      let outQuesState = data.filter(item => {
        return item.dictCode == '097';
      })[0].state;
      this.allTabList.forEach(item => {
        data.forEach(ite => {
          if (item.dictCode == ite.dictCode) {
            item.state = ite.state;
          }
        });
      });
      // 筛选列表权限
      let examRoles = UserRole.examRolesTypes.split(',');
      this.tabList = this.allTabList.filter(item => {
        if (item.state == 1) {
          if (item.roles) {
            if (UserRole.isOperation) return true;
            let intersection = examRoles.filter(value => item.roles.includes(Number(value)));
            return intersection.length > 0;
          } else {
            return true;
          }
        }
        return false;
      });
      // 运营无推题审核
      if (loginInfo && loginInfo.user_type === 5) {
        this.tabList = this.tabList.filter(item => {
          return item.dictCode != '039';
        });
      }
      if (outQuesState == 1) {
        let state1 = 0;
        let state2 = 0;
        data.forEach(item => {
          if (item.dictCode == '00201') {
            state1 = Number(item.state);
          }
          if (item.dictCode == '00202') {
            state2 = Number(item.state);
          }
        });
        if (state1 == 1 && state2 == 1) {
          sessionStorage.setItem('outQuestore', true);
        }
        if (state1 == 0 || state2 == 0) {
          //外部题库根据一起题库权限
          sessionStorage.setItem('outQuestore', false);
          this.tabList = this.tabList.filter(item => {
            return item.dictCode != '097';
          });
        }
      } else {
        sessionStorage.setItem('outQuestore', false);
      }
      //三方卡权限
      let thirdCardState = data.filter(item => {
        return item.dictCode == '113';
      })[0].state;
      this.$sessionSave.set('thirdCardState', thirdCardState);
      //智批改
      // let tbAICorrect = data.filter(item => {
      //   return item.dictCode == '115';
      // })[0].state;
      // this.$sessionSave.set('tbAICorrect', tbAICorrect);
      //英语作文智批改
      // let essayAICorrect = data.filter(item => {
      //   return item.dictCode == '138';
      // })[0].state;
      // this.$sessionSave.set('essayAICorrect', essayAICorrect);
      // //语文作文智批改
      // let chineseEssayAICorrect = data.filter(item => {
      //   return item.dictCode == '147';
      // })[0].state;
      // this.$sessionSave.set('chineseEssayAICorrect', chineseEssayAICorrect);
      //教材章节
      let schoolChapter = data.filter(item => {
        return item.dictCode == '084';
      })[0].state;
      this.$sessionSave.set('schoolChapter', schoolChapter);
      //新高考
      let newExamRules = data.filter(item => {
        return item.dictCode == '105';
      })[0].state;
      this.$sessionSave.set('newExamRules', newExamRules);
      //上传pdf解析权限
      let tbPdfParse = data.filter(item => {
        return item.dictCode == '116';
      })[0].state;
      this.$sessionSave.set('tbPdfParse', tbPdfParse);
      //菁优题库
      let jyeooQues = data.filter(item => {
        return item.dictCode == '125';
      })[0].state;
      this.$sessionSave.set('jyeooQues', jyeooQues);
      if (type === 'changeSchool') {
        this.$router.push({
          path: `/home/<USER>
          query: { time: Date.now() }, //解决因路由重复报错
        });
      }

      data = data.map(it => {
        return {
          dictCode: it.dictCode,
          state: it.state,
        };
      });
      this.$sessionSave.set(`config_auth_${loginInfo.id}`, data);
    },
    // 退出登录
    exitLogin() {
      this.$confirm('确定退出登录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          sessionStorage.clear();
          this.$destroy(true);
          setTimeout(() => {
            this.$router.push({ path: '/login' });
          }, 100);
        })
        .catch(() => {});
    },
    /**
     * @name:点击进入查看题卡制作列表
     */
    checkCard() {
      //不是运营无权限
      if (!this.isAdminorOperate) {
        return;
      }
      this.$router.push({
        path: '/home/<USER>',
      });
    },
    /**
     * @name:获取学校vip信息
     */
    getVipInfo(schoolInfo) {
      getVipInfo({ schoolId: schoolInfo.id })
        .then(res => {
          const vipInfo = res.data;
          vipInfo.isVip = false;
          if (JSON.stringify(vipInfo) != '{}') {
            vipInfo.dateExpired = vipInfo.dateExpired.substring(0, 10);
            vipInfo.dateStart = vipInfo.dateStart.substring(0, 10);
            //当前时间
            let curTime = res.responsetime;
            //服务到期时间
            let dateExpired = new Date(vipInfo.dateExpired);
            if (curTime < dateExpired.getTime()) {
              vipInfo.isVip = true;
              vipInfo.showTip = (dateExpired.getTime() - curTime) / (1000 * 60 * 60 * 24) <= 15;
            }
          } else {
            vipInfo.dateExpired = '1999-01-01';
            //未设置过期时间
            vipInfo.isNotUsed = true;
          }
          store.commit('savevipInfo', vipInfo);
        })
        .catch(err => {});
    },

    // 密码修改成功
    handlePasswordSuccess() {
      this.showPasswordDialog = false;
      this.isForceChangePassword = false;
      this.weakPassword = false;
      this.$sessionSave.remove('weakPassword');
      this.$sessionSave.remove('changePwdVisible');
      this.$router.push('/login');
    },

    // 密码修改取消
    handlePasswordCancel() {
      const now = new Date();
      const deadline = new Date('2025-07-15');
      this.$sessionSave.remove('changePwdVisible');
      if (this.isForceChangePassword && now.getTime() >= deadline.getTime()) {
        sessionStorage.clear();
        this.$destroy(true);
        this.$router.push({ path: '/login' });
      } else {
        this.showPasswordDialog = false;
        this.isForceChangePassword = false;
      }
    },
  },

  // 离开时销毁body绑定的点击事件
  beforeDestroy() {
    document.body.removeEventListener('click', this.hideFun);
  },
};
</script>

<style lang="scss" scoped>
@import '../styles/config';

.workBook {
  width: 100%;
  height: 100%;
  background-color: #f7fafc;
  overflow: hidden;

  .workBook__header {
    position: relative;
    width: 100%;
    height: 60px;
    background-color: #fff;
    /*box-shadow       : 0px 2px 3px 0px rgba(0, 0, 0, 0.08);*/
    margin: 0 auto;
    border-bottom: 1px solid #e4e8eb;

    .workBook__header__container {
      min-width: 1300px;
      max-width: 1500px;
      height: 100%;
      margin: 0 auto;
      padding: 0 0;
    }

    .header__left--logo {
      display: flex;
      align-items: center;
      width: auto;
      height: 100%;

      .logo__img {
        display: inline-block;
        background: url('../assets/logo.png');
        width: 200px;
        height: 50px;
      }

      .top-school-name {
        padding-left: 15px;
        font-size: 16px;
        max-width: 220px;

        .line {
          padding-right: 5px;
          color: #c0c4cc;
        }
      }

      .logo__text {
        flex: 1;
        height: auto;

        .text__top {
          font-size: 22px;
          font-family: Source Han Sans CN;
          font-weight: bold;
          color: #3f4a54;
          padding-left: 10px;
        }

        .text__bottom {
          position: relative;
          cursor: pointer;
          padding-left: 10px;

          .schoolName {
            font-size: 14px;
            font-weight: 400;
            color: #545454;
            width: auto;
            margin-right: 13px;
          }

          .downIcon {
            display: block;
            background-image: url('../assets/personWorkIcon.png');
            background-position: -60px -67px;
            width: 12px;
            height: 6px;
            flex-shrink: 0;
            margin-top: 3px;

            &.expand {
              transform: rotate(180deg);
            }
          }

          .schoolList__ul {
            position: absolute;
            left: 0;
            top: 28px;
            width: 200px;
            min-height: 40px;
            max-height: 224px;
            background: #fff;
            border: 1px solid #e6eaed;
            box-shadow: 0px 1px 6px 0px rgba(84, 84, 84, 0.2);
            border-radius: 4px;
            padding: 0;
            z-index: 3;
            overflow: hidden scroll;

            .schoolList__item {
              width: 100%;
              display: block;
              height: 40px;
              line-height: 40px;
              text-align: left;
              font-size: 14px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #666;
              padding-left: 18px;

              &.active {
                /*background-color: #f0f6fa;*/
                /*color: #008dea;*/
              }

              &:hover {
                background-color: #e6f4fd;
                color: #33a4ee;
              }
            }
          }
        }
      }
    }

    .header__center--tab {
      flex: 1;
      height: 100%;
      display: flex;
      justify-content: center;

      .tab-container {
        // margin-left: 130px;
        // width: 780px;
        // margin : 0 auto;

        .tab-item {
          display: inline-block;
          width: 110px;
          height: 100%;
          line-height: 60px;
          font-size: 19px;
          font-weight: 400;
          color: #434d54;
          text-align: center;
          border-bottom: 3px solid transparent;
          cursor: pointer;
          margin-right: 25px;

          &.active {
            position: relative;
            color: #008dea;
            font-weight: bold;

            &:before {
              content: '';
              display: block;
              position: absolute;
              width: 100%;
              bottom: 0;
              height: 3px;
              background-color: #008dea;
              border-radius: 2px;
            }
          }

          &:first-child {
            /*margin-right : 25px;*/
          }

          &:last-child {
            margin-right: 0px;
          }
        }
      }
    }

    .header__right--avator {
      width: auto;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #888fa0;
      margin-right: 5px;
      font-size: 18px;

      .avator-name {
        color: #3f4a54;
        font-weight: bold;
        font-size: 19px;
        max-width: 180px;
      }

      .avator__img {
        display: block;
        width: 47px;
        height: 47px;
        margin-right: 15px;
      }

      .avator__name {
        cursor: pointer;
        width: auto;
        height: 47px;
        line-height: 47px;
        position: relative;
      }

      .line {
        width: 26px;
        text-align: center;
      }

      .exitLogin {
        /*color: #f56353;*/
        color: #008dea;
        cursor: pointer;
      }
    }
  }

  .workBook__body {
    display: flex;
    flex-direction: column;

    width: 100%;
    height: 100%;
    padding: 60px 0 0;
    margin: -60px 0 0;
    z-index: 999;

    &.hide-header {
      padding: 0 0 0;
      margin: 0 0 0;
    }

    .router-view {
      position: relative;
      min-width: 1300px;
      max-width: 1500px;
      margin: 0 auto;
      margin-top: 14px;
      // background-color: #fff;
      height: calc(100% - 14px);

      &::-webkit-scrollbar-thumb {
        min-height: 50px;
        border-radius: 0px;
      }
    }

    .routerBox {
      width: 100%;
      height: 100%;
      flex: 1;
      overflow: auto;

      .scrollContainer {
        width: 100%;
        height: 100%;
        //padding-bottom: 20px;
        overflow-y: scroll;
        overflow-x: auto;
      }
    }

    .wrap-outer {
      /*width        : 100vw;*/
      /*overflow     : hidden;*/
      padding-left: calc(100vw - 100%) !important;
    }
  }
}

.vip-container {
  font-size: 14px;

  .professional-edition {
    //黄金
    background: linear-gradient(115deg, #f6ddb1, #e4bd80);
    padding: 10px 0;
    text-align: center;
    width: 80px;
    display: flex;
    height: 32px;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-direction: column;
    border-radius: 5px;
  }

  .trialEdition {
    //白银
    // background: linear-gradient(to top, #cfd9df 0%, #e2ebf0 100%);
    background-color: #0093e9;
    background-image: linear-gradient(160deg, #0093e9 0%, #80d0c7 100%);
  }

  .professional-edition-title,
  .professional-edition-desc {
    //铂金
    background: linear-gradient(115deg, #f6ddb1, #e4bd80);
    color: #865812;
    background-clip: text;
    -webkit-background-clip: text;
    font-weight: bolder;
    text-align: center;
  }

  .trialEditionTitle,
  .trialEditionDesc {
    //白银
    // background: linear-gradient(135deg, #f2f5fb, #e5e8ee);
    // color: unset;
    color: #fff;
  }

  .professional-edition-desc {
    font-weight: 500;
    font-size: 11px;
  }
}

.showOverTooltip {
  display: -webkit-box;
  position: relative;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.vip-tips {
  width: 200px;
}
</style>
<style lang="scss">
.avart-school-name {
  text-align: center;
  // display: inline-block;
  width: 120px;
  font-size: 12px;
  color: #909399;
  margin: 0 5px;

  .name-text {
    text-align: center;
    word-wrap: break-word;
    white-space: pre-line;
  }
}

.home-dropdown {
  .el-dropdown-menu__item {
    text-align: center;
  }
}

.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}

.schoolList__popper {
  width: 148px;
  min-height: 40px;
  max-height: 224px;
  padding: 0;

  .schoolList__ul {
    width: 100%;
    height: 100%;

    .schoolList__item {
      width: 100%;
      display: block;
      height: 40px;
      line-height: 40px;
      text-align: left;
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #666;
      padding-left: 18px;

      &.active {
        background-color: #f0f6fa;
        color: #008dea;
      }
    }
  }

  .popper__arrow {
    display: none;
  }
}

.text__bottom {
  .school__select {
    margin-right: 10px;

    input {
      border: none;
      background: transparent;
      padding: 0;
      height: 30px;
      width: 100px;
    }

    .el-input__suffix {
      display: none;
    }
  }
}

.select_school {
  margin-left: 12px;
  width: 155px;
  height: 36px;

  .el-input__inner {
    height: 36px;
  }

  .el-icon-arrow-up:before {
    content: '\e78f';
  }
}
</style>

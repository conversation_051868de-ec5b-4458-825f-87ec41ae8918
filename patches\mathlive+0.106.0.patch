diff --git a/node_modules/mathlive/mathlive.js b/node_modules/mathlive/mathlive.js
index d576e2f..269256e 100644
--- a/node_modules/mathlive/mathlive.js
+++ b/node_modules/mathlive/mathlive.js
@@ -75,103 +75,102 @@ var MathLive = (() => {
   // src/editor/l10n-strings.ts
   var STRINGS = {
     "en": {
-      "keyboard.tooltip.symbols": "Symbols",
-      "keyboard.tooltip.greek": "Greek Letters",
-      "keyboard.tooltip.numeric": "Numeric",
-      "keyboard.tooltip.alphabetic": "Roman Letters",
-      "tooltip.copy to clipboard": "Copy to Clipboard",
-      "tooltip.cut to clipboard": "Cut to Clipboard",
-      "tooltip.paste from clipboard": "Paste from Clipboard",
-      "tooltip.redo": "Redo",
-      "tooltip.toggle virtual keyboard": "Toggle Virtual Keyboard",
-      "tooltip.menu": "Menu",
-      "tooltip.undo": "Undo",
-      "menu.borders": "Borders",
-      "menu.insert matrix": "Insert Matrix",
-      "menu.array.add row above": "Add Row Before",
-      "menu.array.add row below": "Add Row After",
-      "menu.array.add column after": "Add Column After",
-      "menu.array.add column before": "Add Column Before",
-      "menu.array.delete row": "Delete Row",
-      "menu.array.delete rows": "Delete Selected Rows",
-      "menu.array.delete column": "Delete Column",
-      "menu.array.delete columns": "Delete Selected Columns",
-      "menu.mode": "Mode",
-      "menu.mode-math": "Math",
-      "menu.mode-text": "Text",
-      "menu.mode-latex": "LaTeX",
-      "menu.insert": "Insert",
-      "menu.insert.abs": "Absolute Value",
+      "keyboard.tooltip.symbols": "符号",
+      "keyboard.tooltip.greek": "希腊字母",
+      "keyboard.tooltip.numeric": "数字",
+      "keyboard.tooltip.alphabetic": "罗马字母",
+      "tooltip.copy to clipboard": "复制到剪贴板",
+      "tooltip.cut to clipboard": "剪切到剪贴板",
+      "tooltip.paste from clipboard": "从剪贴板粘贴",
+      "tooltip.redo": "重做",
+      "tooltip.toggle virtual keyboard": "公式键盘",
+      "tooltip.menu": "菜单",
+      "tooltip.undo": "撤销",
+      "menu.borders": "边框",
+      "menu.insert matrix": "插入矩阵",
+      "menu.array.add row above": "上方添加行",
+      "menu.array.add row below": "下方添加行",
+      "menu.array.add column after": "后方添加列",
+      "menu.array.add column before": "前方添加列",
+      "menu.array.delete row": "删除行",
+      "menu.array.delete rows": "删除选中行",
+      "menu.array.delete column": "删除列",
+      "menu.array.delete columns": "删除选中列",
+      "menu.mode": "模式",
+      "menu.mode-math": "数学模式",
+      "menu.mode-text": "文本模式",
+      "menu.mode-latex": "LaTeX 模式",
+      "menu.insert": "插入",
+      "menu.insert.abs": "绝对值",
       "menu.insert.abs-template": "\\left|x\\right|",
-      "menu.insert.nth-root": "n<sup>th</sup> Root",
+      "menu.insert.nth-root": "n 次方根",
       "menu.insert.nth-root-template": "\\sqrt[n]{x}",
-      "menu.insert.log-base": "Logarithm base a",
+      "menu.insert.log-base": "对数底数 a",
       "menu.insert.log-base-template": "\\log_a(x)",
-      "menu.insert.heading-calculus": "Calculus",
-      "menu.insert.derivative": "Derivative",
+      "menu.insert.heading-calculus": "微积分",
+      "menu.insert.derivative": "导数",
       "menu.insert.derivative-template": "\\dfrac{\\mathrm{d}}{\\mathrm{d}x}f(x)\\bigm|_{x=a}",
-      "menu.insert.nth-derivative": "n<sup>th</sup> derivative",
+      "menu.insert.nth-derivative": "n 次导数",
       "menu.insert.nth-derivative-template": "\\dfrac{\\mathrm{d}^n}{\\mathrm{d}x^n}f(x)\\bigm|_{x=a}",
-      "menu.insert.integral": "Integral",
+      "menu.insert.integral": "积分",
       "menu.insert.integral-template": "$\\int_a^b f(x)\\,\\mathrm{d}x$",
-      "menu.insert.sum": "Sum",
+      "menu.insert.sum": "求和",
       "menu.insert.sum-template": "$\\sum_{i=1}^n x_i$",
-      "menu.insert.product": "Product",
+      "menu.insert.product": "连乘积",
       "menu.insert.product-template": "\\prod_{i=1}^n x_i",
-      "menu.insert.heading-complex-numbers": "Complex Numbers",
-      "menu.insert.modulus": "Modulus",
+      "menu.insert.heading-complex-numbers": "复数",
+      "menu.insert.modulus": "模",
       "menu.insert.modulus-template": "\\lvert z \\rvert",
-      "menu.insert.argument": "Argument",
+      "menu.insert.argument": "辐角",
       "menu.insert.argument-template": "\\arg(z)",
-      "menu.insert.real-part": "Real Part",
+      "menu.insert.real-part": "实部",
       "menu.insert.real-part-template": "\\Re(z)",
-      "menu.insert.imaginary-part": "Imaginary Part",
+      "menu.insert.imaginary-part": "虚部",
       "menu.insert.imaginary-part-template": "\\Im(z)",
-      "menu.insert.conjugate": "Conjugate",
+      "menu.insert.conjugate": "共轭",
       "menu.insert.conjugate-template": "\\overline{z}",
-      "tooltip.blackboard": "Blackboard",
-      "tooltip.bold": "Bold",
-      "tooltip.italic": "Italic",
-      "tooltip.fraktur": "Fraktur",
-      "tooltip.script": "Script",
-      "tooltip.caligraphic": "Caligraphic",
-      "tooltip.typewriter": "Typewriter",
-      "tooltip.roman-upright": "Roman Upright",
-      "tooltip.row-by-col": "%@ \xD7 %@",
-      "menu.font-style": "Font Style",
-      "menu.accent": "Accent",
-      "menu.decoration": "Decoration",
-      "menu.color": "Color",
-      "menu.background-color": "Background",
-      "menu.evaluate": "Evaluate",
-      "menu.simplify": "Simplify",
-      "menu.solve": "Solve",
-      "menu.solve-for": "Solve for %@",
-      "menu.cut": "Cut",
-      "menu.copy": "Copy",
-      "menu.copy-as-latex": "Copy as LaTeX",
-      "menu.copy-as-typst": "Copy as Typst",
-      "menu.copy-as-ascii-math": "Copy as ASCII Math",
-      "menu.copy-as-mathml": "Copy as MathML",
-      "menu.paste": "Paste",
-      "menu.select-all": "Select All",
-      // Colors (accessible labels in color swatches)
-      "color.red": "Red",
-      "color.orange": "Orange",
-      "color.yellow": "Yellow",
-      "color.lime": "Lime",
-      "color.green": "Green",
-      "color.teal": "Teal",
-      "color.cyan": "Cyan",
-      "color.blue": "Blue",
-      "color.indigo": "Indigo",
-      "color.purple": "Purple",
-      "color.magenta": "Magenta",
-      "color.black": "Black",
-      "color.dark-grey": "Dark Grey",
-      "color.grey": "Grey",
-      "color.light-grey": "Light Grey",
-      "color.white": "White"
+      "tooltip.blackboard": "黑板字体",
+      "tooltip.bold": "加粗",
+      "tooltip.italic": "斜体",
+      "tooltip.fraktur": "哥特字体",
+      "tooltip.script": "手写体",
+      "tooltip.caligraphic": "书法体",
+      "tooltip.typewriter": "打字机字体",
+      "tooltip.roman-upright": "直立罗马体",
+      "tooltip.row-by-col": "%@ \u00D7 %@",
+      "menu.font-style": "字体样式",
+      "menu.accent": "重音符号",
+      "menu.decoration": "装饰",
+      "menu.color": "颜色",
+      "menu.background-color": "背景颜色",
+      "menu.evaluate": "求值",
+      "menu.simplify": "化简",
+      "menu.solve": "求解",
+      "menu.solve-for": "求解 %@",
+      "menu.cut": "剪切",
+      "menu.copy": "复制",
+      "menu.copy-as-latex": "复制为 LaTeX",
+      "menu.copy-as-typst": "复制为 Typst",
+      "menu.copy-as-ascii-math": "复制为 ASCII 数学格式",
+      "menu.copy-as-mathml": "复制为 MathML",
+      "menu.paste": "粘贴",
+      "menu.select-all": "全选",
+      "color.red": "红色",
+      "color.orange": "橙色",
+      "color.yellow": "黄色",
+      "color.lime": "酸橙色",
+      "color.green": "绿色",
+      "color.teal": "青色",
+      "color.cyan": "青蓝色",
+      "color.blue": "蓝色",
+      "color.indigo": "靛蓝色",
+      "color.purple": "紫色",
+      "color.magenta": "品红色",
+      "color.black": "黑色",
+      "color.dark-grey": "深灰色",
+      "color.grey": "灰色",
+      "color.light-grey": "浅灰色",
+      "color.white": "白色"
     },
     // Arabic
     "ar": {
@@ -10498,18 +10497,18 @@ M500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z`
     // Differentials
     // According to ISO31/XI (ISO 80000-2), differentials should be upright
     // (not this is not universal: https://tex.stackexchange.com/questions/14821/whats-the-proper-way-to-typeset-a-differential-operator/637613#637613)
-    "dx": {
-      after: "nothing+digit+function+frac+surd+binop+relop+punct+array+openfence+closefence+space+text",
-      value: "\\differentialD x"
-    },
-    "dy": {
-      after: "nothing+digit+function+frac+surd+binop+relop+punct+array+openfence+closefence+space+text",
-      value: "\\differentialD y"
-    },
-    "dt": {
-      after: "nothing+digit+function+frac+surd+binop+relop+punct+array+openfence+closefence+space+text",
-      value: "\\differentialD t"
-    },
+    // "dx": {
+    //   after: "nothing+digit+function+frac+surd+binop+relop+punct+array+openfence+closefence+space+text",
+    //   value: "\\differentialD x"
+    // },
+    // "dy": {
+    //   after: "nothing+digit+function+frac+surd+binop+relop+punct+array+openfence+closefence+space+text",
+    //   value: "\\differentialD y"
+    // },
+    // "dt": {
+    //   after: "nothing+digit+function+frac+surd+binop+relop+punct+array+openfence+closefence+space+text",
+    //   value: "\\differentialD t"
+    // },
     // Logic
     "AA": "\\forall",
     "EE": "\\exists",
@@ -23750,21 +23749,23 @@ Note there are a different set of tooltip rules for the keyboard toggle
           "[/]",
           "[separator-5]",
           {
-            latex: "\\exponentialE",
+            latex: "\\left\\{\\begin{matrix}{#?} &{#?} \\\\{#?}  &{#?} \\end{matrix}\\right.",
             shift: "\\ln",
             variants: [
-              "\\exp",
-              "\\times 10^{#?}",
-              "\\ln",
-              "\\log_{10}",
-              "\\log",
-              "\\lg",
-              "\\operatorname{lb}"
-            ]
+              { latex: "\\begin{bmatrix}{#?} &{#?} \\\\{#?}  &{#?} \\end{bmatrix}", class: "small" },
+              { latex: "\\begin{vmatrix}{#?} &{#?} \\\\{#?}  &{#?} \\end{vmatrix}", class: "small" },
+              { latex: "\\begin{Vmatrix}{#?} &{#?} \\\\{#?}  &{#?} \\end{Vmatrix}", class: "small" },
+              { latex: "\\begin{Bmatrix}{#?} &{#?} \\\\{#?}  &{#?} \\end{Bmatrix}", class: "small" },
+              { latex: "\\left.\\begin{matrix}{#?} &{#?} \\\\{#?}  &{#?} \\end{matrix}\\right\\}", class: "small" },
+              { latex: "\\begin{matrix}{#?} &{#?} \\\\{#?}  &{#?} \\end{matrix}", class: "small" },
+            ],
+            class:"hide-shift small"
           },
           {
-            latex: "\\imaginaryI",
-            variants: ["\\Re", "\\Im", "\\imaginaryJ", "\\Vert #0 \\Vert"]
+            latex: "\\frac{#0}{#0}",
+            shift: "\\imaginaryI",
+            variants: ["\\Re", "\\Im", "\\imaginaryJ", "\\Vert #0 \\Vert"],
+            class:"hide-shift small"
           },
           {
             latex: "\\pi",

<template>
  <el-dialog class="upload-dialog" :title="isUpload?$t('uploadEditPaper.uploadExam'):$t('uploadEditPaper.editExam')"
             :visible="true" @close="close">
    <ul style="width: 80%;margin: 0 auto;">
      <template v-if="isUpload">
        <li>
          <el-button type="text" class="download-exam" @click="downLoadPaperTemplate">{{$t('uploadEditPaper.download')}}</el-button>
          <span class="tip-text download">{{$t('uploadEditPaper.tplText')}}</span>
        </li>
        <li class="exam-input-li">
          <div class="exam-name tip-text" v-if="name===''">{{$t('uploadEditPaper.examLimitText')}}</div>
          <div class="exam-name ellipsis" v-else :title="name">{{name}}</div>
          <input
            accept="doc、docx"
            ref="examInput"
            type="file"
            id="examInput"
            name="examInput"
            class="exam-input"
            @change="beforeUploadExam"
          >
          <label class="exam-label" for="examInput">{{$t('uploadEditPaper.chooseFile')}}</label>
        </li>
      </template>

      <li v-else>
        <span style="width: 90px; text-align: left;"> 试卷名称:</span>
        <el-input v-model="name"></el-input>
      </li>

      <li class="select">
        <div style="width: 50%;text-align: left;">
          <span style="margin-right: 10px;">{{$t('uploadEditPaper.grade')}}:</span>
          <el-select v-model="curGrade" value-key="greadeCode" style="width: 70%;">
            <el-option
              v-for="item in $store.getters.question.xbGrades"
              :key="item.greadeCode"
              :label="item.greadeName"
              :value="item"
            ></el-option>
          </el-select>
        </div>
        <div style="width: 50%;text-align: left;">
          <span style="margin-right: 10px;">{{$t('uploadEditPaper.subject')}}:</span>
          <el-select disabled v-model="curSubject" value-key="value" style="width: 70%;">
            <el-option
              :label="curSubject.name"
              :value="curSubject"
            ></el-option>
          </el-select>
        </div>
      </li>
      <li class="select">
        <div style="width: 50%;text-align: left;">
          <span style="margin-right: 10px;">{{$t('uploadEditPaper.type')}}:</span>
          <el-select v-model="curSource" value-key="id" style="width: 70%;">
            <el-option
              v-for="item in $store.getters.question.xbSources"
              :key="item.id"
              :label="item.name"
              :value="item"
            ></el-option>
          </el-select>
        </div>
        <div style="width: 50%;text-align: left;">
          <span style="margin-right: 10px;">{{$t('uploadEditPaper.year')}}:</span>
          <el-select v-model="curYear" style="width: 70%;">
            <el-option
              v-for="item in $store.getters.question.xbYears"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
        </div>
      </li>
      <li>
        <el-checkbox v-model="isChecked">{{$t('uploadEditPaper.together')}}</el-checkbox>
      </li>
    </ul>
    <el-progress :stroke-width="10" :percentage="parseFloat((progress*100).toFixed(1))" v-if="isUploadIng"></el-progress>
    <div slot="footer">
      <el-button style="width: 100px;height: 45px;" @click="close">{{$t('common.cancle')}}</el-button>
      <el-button style="width: 100px;height: 45px;" type="primary" @click="confirm">{{isUpload?$t('uploadEditPaper.upload'):$t('common.confirm')}}</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import {Component, Vue, Prop} from 'vue-property-decorator'
  import {BaseModule} from '@/store/modules/base'
  import {UserModule} from '@/store/modules/user'
  import {saveTestBanks, getTestBankInfo} from '@/service/API/xbQuesBank'
  import {getUuid, downLoadFile, trimStr} from '@/utils/utils'
  import OSSUpload from '@/utils/ossUpload'
  import {isNullOrUndefined} from "@/utils/utils";
  import Local from './local'
  import {IObject} from '@/model/IBaseModel'
  @Component({})
  export default class UploadEditPaper extends Vue {
    // 是否上传
    @Prop({default: false}) isUpload: boolean
    // 年级,来源,年份 json字符串格式
    @Prop({default: ''}) optionJson: string
    // 试卷id
    @Prop({default: ''}) id: string
    // 年级
    curGrade: {name: string,value: string,greadeCode:string,greadeName:string} = {name: '', value: '',greadeCode:'',greadeName:''}
    // 学科
    curSubject: IObject<string> = {name: UserModule.subjectInfo.subjectName, value: UserModule.subjectInfo.subjectId}
    // 来源
    curSource: {id:string,name: string, value: string} = {id:'',name: '', value: ''}
    // 年份
    curYear: number = new Date().getFullYear()
    // 试卷名称
    name: string = ''
    // 待上传文件
    file: any = null
    // 是否同步至校本题库
    isChecked: boolean = false
    // 是否正在上传
    isUploadIng: boolean = false
    // 上传进度
    progress: number = 0

    created() {
      this.$i18n.mergeLocaleMessage('zh', Local.zh)
      this.$i18n.mergeLocaleMessage('en', Local.en)
      this.init()
    }

    /**
     * @name: 初始化
     * */
    init(): void {
      if (this.optionJson === '') {
        return
      }
      let option: any = JSON.parse(this.optionJson)
      this.curGrade = option.grade
      this.curSource = option.source
      this.curYear = option.year
      if (this.isUpload) {
        this.isChecked = true
        OSSUpload.getSTSToken('aliba')
      } else {
        getTestBankInfo({id: this.id}).then((res: any) => {
          this.isChecked = res.ShapeType === 1
          this.name = res.TB_Name
        })
      }
    }
    /**
     * @name: 下载模板
     * */
    downLoadPaperTemplate(): void {
      let url: string = BaseModule.sourceUrl + "aliba/resources/testbank/template/卷库资源上传模板.docx?v=1.8"
      if (window.isWindow) {
        window.callCplus('micro.ExecFunc', 'download', url)
      } else {
        downLoadFile(url)
      }
    }
    /**
     * @name: 保存接口数据
     * */
    saveUploadFile(): void {
      let name:string = trimStr(this.name)
      if( name ==='' ){
        this.$message({message: `${this.$t('uploadEditPaper.emptyFileNameText')}`, type: 'warning', duration: 1000})
        return
      }
      let params: any = {
        tbId: this.id,
        tbName: name,
        gCode: this.curGrade.greadeCode,
        gName: this.curGrade.greadeName,
        sCode: this.curSubject.value,
        sName: this.curSubject.name,
        year: this.curYear,
        phase: UserModule.subjectInfo.phase,
        schoolId: UserModule.schoolId,
        userId: UserModule.userId,
        userName: UserModule.userName,
        typeCode: this.curSource.id,
        typeName: this.curSource.name,
        shareType: this.isChecked ? 1 : 0
      }
      if (this.isUpload) {
        params.fileSize = this.file.size
        params.fileName = this.file.name
        params.filePath = this.file.path
      }

      saveTestBanks(params).then(() => {
        this.isUploadIng = false
        this.close()
      })
    }
    /**
     * @name: 关闭上传弹窗
     * */
    close(): void {
      this.$emit('close-upload-edit', this.isUpload)
    }
    /**
     * @name: 确认
     * */
    confirm(): void {
      if (this.isUpload) {
        if(!this.file){
          this.$message({message: '请先选择文件!', type: 'warning', duration: 1000})
          return
        }
        this.isUploadIng = true
        OSSUpload.uploadFile(this.file, this.file.path,(progress:any)=>{
          this.progress = progress
        }).then(() => {
          let dom: any = this.$refs.examInput
          if(!isNullOrUndefined(dom)){
            dom.value = ''; // 流文件清空,避免同流文件无法再次上传
          }
          this.saveUploadFile()
        })
      } else {
        this.saveUploadFile()
      }
    }
    /**
     * @name: 上传前校验
     * @param e mouseEvent
     * */
    beforeUploadExam(e: any): void {
      let fileList: Array<File> = [...e.currentTarget.files]
      if (fileList.length === 0) {
        this.$message({message: `${this.$t('uploadEditPaper.noFileText')}`, type: 'warning', duration: 1000})
        return
      }
      let file: any = fileList[0]
      let reg: RegExp = /doc|docx/g
      let fileExt: string = file.name.substr(file.name.lastIndexOf('.') + 1).toLowerCase()
      let title: string = file.name.substr(0, file.name.lastIndexOf('.'))

      if (!reg.test(fileExt)) {
        this.$message({message: `${this.$t('uploadEditPaper.errFileText')}`, type: 'warning', duration: 1000})
        return
      }
      if (title === '') {
        this.$message({message: `${this.$t('uploadEditPaper.noFileNameText')}`, type: 'warning', duration: 1000})
        return;
      }
      let pattern: RegExp = RegExp("[`!@$^&':;'?%！@￥……&‘；：'？]");
      if (pattern.test(title)) {
        this.$message({message: `${this.$t('uploadEditPaper.errFileNameText')}`, type: 'warning', duration: 1000})
        return;
      }
      if (title.length > 50) {
        this.$message({message: `${this.$t('uploadEditPaper.longFileNameText')}`, type: 'warning', duration: 1000})
        return;
      }

      // 构建额外保存参数
      file.id = getUuid()
      file.path = `/aliba/resources/testbank/${new Date().toLocaleDateString()}/${getUuid()}/${file.id}.${fileExt}`
      this.file = file
      this.name = title
      this.id = ''
    }
  }
</script>

<style lang="scss" scoped>
  .upload-dialog {
    ul {
      width: 80%;
      margin: 0 auto;
      li {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 20px;
        .download-exam {
          padding-left: 20px;
          background: url("../../../../../static/img/bank/icon_download_org.png") no-repeat left center/18px 15px;
        }
        &.exam-input-li {
          border: 1px solid $mainColor;
          height: 45px;
          border-radius: 4px;
          position: relative;
          .exam-name {
            position: absolute;
            left: 0;
            top: 0;
            width: calc(100% - 100px);
            height: 100%;
            text-align: left;
            text-indent: 20px;
            line-height: 45px;
            cursor: pointer;
          }
          .exam-input {
            width: 100%;
            height: 100%;
            opacity: 0;
          }
          .exam-label {
            float: right;
            height: 100%;
            width: 100px;
            line-height: 45px;
            background-color: $mainColor;
            border-left: 1px solid $mainColor;
            color: #fff;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
          }
        }
        &.select {
          justify-content: space-between;
        }
        .tip-text {
          color: $tipTextColor;
          &.download {
            font-size: 14px;
            margin-left: 10px;
          }
        }
      }
    }
  }
</style>

<template>
  <div class="previewPaper display_flex flex-direction_column">
    <div class="detail-header">
      <div class="back-btn">
        <i class="el-icon-arrow-left" style="margin-right: 6px" @click="closePaper"></i>
        <div class="previewName" :title="previewObj.name || '预览试卷'" @click="closePaper">
          {{ previewObj.name || '预览试卷' }}
        </div>
      </div>
      <ul class="edit-ul">
        <!-- <li
          class="edit-btn addQues"
          v-show="!readonly"
          @click="modifyPaper"
          v-if="paramsData.source == '5'"
        >
          修改试卷
        </li> -->
        <li class="edit-btn cleanQues" @click="modifyBaseInfo">试卷信息</li>
      </ul>
    </div>
    <!-- 网阅或手阅纯答题卡 -->
    <div v-if="isShowImg">
      <el-row
        class="info-row"
        v-loading="loadingImg"
        v-if="cardInfo.paperNum && cardInfo.paperNum > 0"
      >
        <div class="img-container">
          <div class="img-list" v-for="(img, index) in imgList" :key="index">
            <el-image
              class="img"
              :style="drawImgStyles()"
              :src="img + '?rand=' + Math.random()"
              :preview-src-list="imgList"
            >
            </el-image>
            <p>第 {{ index + 1 }} 页</p>
          </div>
        </div>
      </el-row>
      <no-data v-else text="暂无可预览图片"></no-data>
    </div>
    <div
      v-else
      class="detail-body flex_1"
      :style="{ height: bodyHeight + 'px' }"
      v-loading="listLoading"
    >
      <!--左侧知识点列表-->
      <div class="left-list display_flex flex-direction_column">
        <!--标题-->
        <!--<el-input class="paper-title" v-model="previewObj.name" type="text" maxlength="20"></el-input>-->
        <p class="paper-title">{{ previewObj.name }}</p>
        <div v-loading="quesLoading" class="quesList">
          <div v-for="(item, idx) in previewObj.quesInfo" :key="idx">
            <div class="third-box" v-for="(subItem, subIdx) in item.data" :key="subIdx">
              <!--题目类型-->
              <h3 class="third-title">
                {{ subItem.name }}
                <span class="defaultScore"
                  >（共{{ subItem.data.length }}题，共{{ subItem.defaultScore }}分）</span
                >
              </h3>
              <!--题面-->
              <div
                class="quesContent"
                v-for="(third, thirdIdx) in subItem.data"
                :key="thirdIdx"
                :class="{ active: detailId == third.id }"
                @click="detailId = third.id"
              >
                <div class="hover-btn" v-if="third.content">
                  <span
                    class="showAnswer"
                    :class="{ open: third.content.showDetail }"
                    @click="
                      $set(third.content, 'showDetail', !third.content.showDetail),
                        getOutQuesDetails(third)
                    "
                    >{{ third.content.showDetail ? '收起' : '展开' }}解析</span
                  >
                  <div
                    class="select-btn"
                    @click.stop="selectQues(third)"
                    :class="[
                      selectIds.length && selectIds.indexOf(third.id) >= 0
                        ? 'hasAdd el-icon-minus'
                        : 'el-icon-plus',
                    ]"
                  >
                    <span style="color: #333333">
                      {{ selectIds.length && selectIds.indexOf(third.id) >= 0 ? ' 移除' : ' 选入' }}
                    </span>
                  </div>
                </div>
                <div class="ques-list">
                  <div class="ques-content display_flex flex-direction_column" v-if="third.content">
                    <span style="flex-shrink: 0">
                      {{ third.name }}
                      <span class="defaultScore">（{{ third.defaultScore }}分）</span>
                    </span>
                    <div class="question_content flex_1" style="width: 100%">
                      <LatexHtml
                        class="question_body"
                        :html="
                          third.content.data.levelcode == '302'
                            ? third.content.topic
                            : third.content.topic
                        "
                        :subject="third.content.subject"
                        v-show="third.normalDetail"
                      ></LatexHtml>
                      <LatexHtml
                        v-show="third.content.showDetail"
                        class="question_body"
                        :html="third.content.details"
                        :subject="third.content.subject"
                      >
                      </LatexHtml>
                      <div
                        class="question_body"
                        v-if="(third.content.data.qs.length && third.content.data.levelcode == '302') || third.content.data.qs.length > 1"
                        v-show="third.normalDetail"
                      >
                        <p
                          v-for="(item, index) in third.content.optionText"
                          :key="index"
                          style="display: flex"
                        >
                          <span>&nbsp;&nbsp;&nbsp;({{ index + 1 }})</span>
                          <span v-html="item"></span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="ques-detail"
                    :class="{ active: !third.normalDetail }"
                    v-if="third.content && third.content.showDetail"
                  >
                    <!--答案-->
                    <div class="answer_box display_flex align-items_flex-start">
                      <strong class="flex_shrink_0">【答案】</strong>
                      <div class="flex_1">
                        <!-- <div
                          class="answer_content"
                          v-if="third.content.type == 2 || third.content.quesType == 2"
                        >
                          {{ third.content.answer.split(',')[0] == 'A' ? '正确' : '错误' }}
                        </div> -->
                        <div v-if="third.content.data.levelcode != ''" class="answer_content">
                          <p
                            v-for="(item, index) in third.content.answer"
                            :key="index"
                            style="display: flex"
                          >
                          <span>&nbsp;&nbsp;({{ index + 1 }})&nbsp;&nbsp;&nbsp;</span>
                          <template v-if="third.content.type == 2 || third.content.quesType == 2">
                            {{ item == 'A' ? '正确' : '错误' }}
                          </template>
                          <template v-else>
                            <span v-html="item"></span>
                          </template>
                          </p>
                        </div>
                        <LatexHtml
                          class="answer_content"
                          v-else
                          :html="third.content.answer"
                          :subject="third.content.subject"
                        ></LatexHtml>
                      </div>
                    </div>
                    <!--考点-->
                    <div v-if="third.content.knowledgeName">
                      <strong>【考点】</strong
                      >{{ third.content.knowledgeName.split(',').join('，') }}
                    </div>
                    <!--解析-->
                    <div
                      v-if="third.content.analysis"
                      class="answer_box display_flex align-items_flex-start"
                    >
                      <span class="flex_shrink_0"><strong>【解析】</strong></span>
                      <LatexHtml
                        class="answer_content flex_1"
                        :html="third.content.analysis"
                        :subject="third.content.subject"
                      ></LatexHtml>
                      <!-- <div v-else class="answer_content flex_1">
                        <p
                          v-for="(item, index) in third.content.analysis"
                          :key="index"
                          style="display: flex"
                        >
                          <span>&nbsp;&nbsp;({{ index + 1 }})&nbsp;&nbsp;&nbsp;</span>
                          <span v-html="item"></span>
                        </p>
                      </div> -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 试卷袋 -->
    <fixedPaper
      v-if="!isShowImg"
      v-on="$listeners"
      ref="fixedPaper"
      class="fixed-box"
      @update="updateData"
      @getParams="getParams"
    ></fixedPaper>
    <!-- 试卷信息弹窗 -->
    <formDialog
      v-if="showBaseDialog"
      :paramsData="previewObj"
      :quesInfo="quesInfo"
      :forbidOperate="true"
      @close="closeDialog"
      @saveMsg="saveBaseMessage"
    ></formDialog>
    <!--下载试卷弹窗-->
    <downloadDialog
      :downItem="previewObj"
      :paramsData="paramsData"
      v-if="showDownloadDialog"
      @close="closeDialogDown"
    ></downloadDialog>
  </div>
</template>

<script>
import { getQueTypeListBySubId, getScanPaperWorkIdAPI } from '@/service/pexam';
import {
  openPersonalTestBank,
  savePersonalTestBank,
  getViewPaper,
  getTBSearchInfo,
} from '@/service/testbank';
import { chooseQuesSearch } from '@/service/pbook';
import LatexHtml from '@/components/LatexHtml';
import draggable from 'vuedraggable';
import { mapGetters } from 'vuex';
import formDialog from '@/components/paper/formDialog';
import downloadDialog from '@/components/paper/downloadDialog';
import { getQuesDetails } from '@/service/yiqi';
import fixedPaper from '@/components/fixedPaper';
import NoData from '@/components/noData.vue';
import { drawImgStyle } from '@/utils/common';
import { getPaperImgMark } from '@/service/xueban';

export default {
  name: 'preview-paper',
  components: {
    LatexHtml,
    draggable,
    formDialog,
    downloadDialog,
    fixedPaper,
    NoData,
  },
  data() {
    return {
      readonly: false,
      showDownloadDialog: false,
      detailId: '',
      titleList: ['试题', '基本信息'],
      activeTitle: 0,
      previewObj: {},
      quesLoading: false,
      bodyHeight: 300,
      optionProps: {
        value: 'name',
        label: 'name',
        children: 'data',
      },
      // 从url上获取的参数列表
      paramsData: {},
      // 默认分值
      scoreList: [],
      // 题类总分与小题不一致的题类id数组
      errorList: [],
      // 最后保存提交的题目数组
      quesInfo: [],
      showBaseDialog: false,
      listLoading: false,
      //试卷袋题目
      quesInfopaper: [],
      cardInfo: {},
      fsUrl: process.env.VUE_APP_FS_URL,
      imgList: [],
      loadingImg: false,
    };
  },
  mounted() {
    this.paramsData = this.$route.query;
    this.getCurTitle();
    this.getQueTypeListBySubId();
    this.canEditJudge();
    this.getViewPaper();
    this.$refs.fixedPaper.getPersonalTestBank();
  },
  computed: {
    ...mapGetters(['subjectMap', 'gradeList']),
    //获取选入试卷袋的试题
    selectIds() {
      let totalList = [],
        ids = [];
      this.quesInfopaper.length &&
        this.quesInfopaper.forEach(item => {
          totalList = totalList.concat(item.data);
        });
      totalList.length &&
        totalList.forEach(item => {
          if (item.data.length) {
            ids = ids.concat(
              item.data.map(sub => {
                return sub.id;
              })
            );
          }
        });
      return ids;
    },
    isShowImg() {
      return (this.paramsData.cardType == 0 || this.paramsData.cardType == 3 || this.paramsData.cardType == 4) &&
        (this.paramsData.correctType == 1 || this.paramsData.correctType == 2) ||
        this.paramsData.supportPatchAnswer == 1
    },
  },
  methods: {
    /**
     * @name:关闭预览
     */
    closePaper() {
      if (this.$route.query.fromPage == 'paper') {
        this.$router.push({
          path: '/home/<USER>/paper',
        });
      } else if (this.$route.query.fromPage == 'card') {
        this.$router.push({
          path: '/home/<USER>',
        });
      } else {
        this.$router.go(-1);
      }
    },
    // 更新加入试卷的数据
    updateData(quesInfo) {
      this.quesInfopaper = quesInfo || this.quesInfopaper;
    },
    drawImgStyles() {
      return drawImgStyle(this.cardInfo);
    },
    /**
     * @name:获取生成的pdf图片
     */
    async getImgMarkUrls() {
      this.loadingImg = true;
      const params = {
        paper_no: this.cardInfo.paperNo,
        show_answer: true,
      };
      const res = await getPaperImgMark(params);
      if (res.code == 1) {
        this.imgList = res.data;
      } else {
        this.imgList = [];
      }
      this.loadingImg = false;
    },
    /**
     * @name:获取试卷袋所需信息
     */
    getParams() {
      return {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        subjectId: this.paramsData.subId,
      };
    },
    // 题目加入、移除讲评
    selectQues(item) {
      this.$refs.fixedPaper.selectQues(item);
    },
    /**
     * @name: 获取来源于一起题库题目详情
     * @param item 题目信息
     */
    getOutQuesDetails(item) {
      let loginData = this.$sessionSave.get('loginInfo');
      // if(loginData.user_type == 5 && loginData.admin_type!=2){
      //   //运营账号
      //   return
      // }
      if (!item.content.showDetail) {
        item.content.showDetail = item.content.showDetail;
        item.normalDetail = true;
        return;
      }
      let tokenInfo = loginData.token;
      getQuesDetails({
        headers: {
          token: tokenInfo,
        },
        data: {
          userId: loginData.id,
          questionId: item.id,
          subjectId: item.content.subject,
        },
      })
        .then(result => {
          let res = result;
          let data = res.data && res.data.data;
          item.content.details = data.html;
          item.content.data.qs = data.qs;
          this.$nextTick(() => {
            item.content.showDetail = true;
            item.normalDetail = false;
          });
        })
        .catch(err => {});
    },
    canEditJudge() {
      const params = {
        schoolId: this.$route.query.schoolId,
        sourceId: this.$route.query.tbId,
      };
      getScanPaperWorkIdAPI(params).then(res => {
        this.readonly = !!res.data;
      });
    },
    closeDialogDown() {
      this.showDownloadDialog = false;
    },
    getCurTitle() {
      let nowDate = new Date(),
        year = nowDate.getFullYear(),
        month =
          nowDate.getMonth() + 1 < 10 ? '0' + (nowDate.getMonth() + 1) : nowDate.getMonth() + 1,
        day = nowDate.getDate() < 10 ? '0' + nowDate.getDate() : nowDate.getDate(),
        dateStr = year + month + day + '手动组卷';
      this.previewObj.name = dateStr;
    },
    // 更新加入试卷的数据
    // updateData(quesInfo) {
    //     this.quesInfo = quesInfo || this.quesInfo;
    // },
    // 获取题型的默认分值
    getQueTypeListBySubId() {
      this.listLoading = true;
      getQueTypeListBySubId({
        subId: this.paramsData.subId,
        schoolId: this.$sessionSave.get('schoolInfo').id,
        serviceVersion: 4.0,
        hwTypeCode: 104,
      })
        .then(data => {
          this.scoreList = data.data;
          if (!data.data.length) {
            this.listLoading = false;
          }
        })
        .catch(err => {
          this.listLoading = false;
        });
    },
    // 获取预览列表数据
    getViewPaper(type) {
      getViewPaper({
        id: this.paramsData.tbId || '',
      })
        .then(res => {
          this.cardInfo = res.data;
          if (this.isShowImg) {
            this.getImgMarkUrls();
          }
          let dataRes = JSON.parse(res.data && res.data.quesInfo);
          dataRes = dataRes.filter(item => item.typeId != 10)
          let newData = [
            {
              name: '第一卷',
              data: [],
              id: 0,
              show: 1,
            },
            // {
            //   name: "第二卷",
            //   data: [],
            //   id: 1,
            //   show: 1,
            // },
          ];
          // 长度为2，第一卷和第二卷
          dataRes.forEach(item => {
            newData[0].data.push({
              isChangeSort: true,
              isAverage: true,
              ...item,
            });
          });
          res.data.quesInfo = JSON.stringify(newData);

          if (res.data.quesInfo) {
            res.data.quesInfo = JSON.parse(res.data.quesInfo);
          } else {
            res.data.quesInfo = [];
          }
          if (type == 'update' && this.previewObj.quesInfo) {
            let oldQuesInfo = this.$deepClone(this.previewObj.quesInfo);
            this.previewObj = res.data;
            this.previewObj.quesInfo = oldQuesInfo;
            return;
          } else {
            this.previewObj = res.data;
          }
          let quesInfo = this.previewObj.quesInfo || [],
            quesIds = [];
          quesInfo.length &&
            quesInfo.forEach((item, index) => {
              item.data.forEach((subItem, subIndex) => {
                subItem.data.forEach((thirdItem, thirdIndex) => {
                  quesIds.push(thirdItem.id);
                });
              });
            });

          if (!quesIds.length) {
            this.listLoading = false;
          } else {
            this.chooseQuesSearch(quesIds.join(','));
          }
        })
        .catch(err => {
          this.listLoading = false;
        });
    },
    // 获取题目的题面
    chooseQuesSearch(ids) {
      this.listLoading = true;
      chooseQuesSearch({
        qIds: ids,
      })
        .then(data => {
          let res = data.data;
          if (!this.previewObj.quesInfo.length) {
            this.quesLoading = false;
            return;
          }
          this.previewObj.quesInfo.forEach(obj => {
            this.$set(obj, 'level', 1);
            obj.data.length &&
              obj.data.forEach(subObj => {
                this.$set(subObj, 'level', 2);
                this.$set(subObj, 'isAverage', true);
                subObj.data.length &&
                  subObj.data.forEach(third => {
                    this.$set(third, 'level', 3);
                    res.forEach(item => {
                      if (third.id == item.qId) {
                        this.$set(third, 'content', item);
                        this.$set(third, 'normalDetail', true);
                      }
                    });
                  });
              });
          });
          this.previewObj.quesInfo.forEach(item => {
            item.data.forEach(ite => {
              ite.data.forEach(it => {
                //若为三级题型则获取每小题分值之和
                if (it.content.data.levelcode == '301') {
                  it.score = 0;
                  it.content.data.qs.forEach(qscore => {
                    it.score += qscore.score;
                  });
                }
              });
            });
          });
          let quesInfo = this.previewObj.quesInfo || [];
          quesInfo.length &&
            quesInfo.forEach((item, index) => {
              item.data.forEach((subItem, subIndex) => {
                // 获取当前题型总分
                let typeScore = 0;
                subItem.data.forEach((thirdItem, thirdIndex) => {
                  // 若当前小题没有分值
                  if (!Number(thirdItem.score)) {
                    let score = this.getDefaultScore(subItem.type);
                    this.$set(thirdItem, 'defaultScore', Number(score));
                  } else {
                    this.$set(thirdItem, 'defaultScore', Number(thirdItem.score));
                  }
                  typeScore += thirdItem.defaultScore;
                });
                this.$set(subItem, 'defaultScore', typeScore);
              });
            });
          console.log('this.previweObj.quesInfo======>', this.previewObj.quesInfo);
          if (this.paramsData.source !== '5') {
            let indexId = 0;
            this.previewObj.quesInfo.forEach(item => {
              item.data.forEach(it => {
                indexId++;
                // it.name = this.setIndexId(indexId) + it.name;
              });
            });
          }

          this.$nextTick(() => {
            this.listLoading = false;
            this.$katexUpdate();
            this.quesLoading = false;
          });
        })
        .catch(err => {
          this.listLoading = false;
          this.quesLoading = false;
        });
    },
    setIndexId(index) {
      let number = '';
      switch (index) {
        case 1:
          number = '一、';
          break;
        case 2:
          number = '二、';
          break;
        case 3:
          number = '三、';
          break;
        case 4:
          number = '四、';
          break;
      }
      return number;
    },
    // 跳转到编辑试卷页面
    modifyPaper() {
      openPersonalTestBank({
        tbId: this.paramsData.tbId,
        schoolId: this.previewObj.schoolId,
      }).then(data => {
        let tp = {
          schoolId: this.previewObj.schoolId,
          userId: this.previewObj.userId,
          subjectId: this.previewObj.subjectId,
          gradeId: this.previewObj.gradeId,
        };

        this.$store.commit('saveTeachParams', tp);

        this.$router.push({
          path: '/previewPaper',
          query: {
            tbId: this.previewObj.tbId,
            schoolId: this.previewObj.schoolId,
            userId: this.previewObj.userId,
            subId: this.previewObj.subjectId,
            fromName: 'previewDetail',
            year: this.paramsData.year || '',
            categoryId: this.previewObj.categoryId || '',
            gradeId: this.previewObj.gradeId,
            shapeType: this.previewObj.shapeType,
            source: this.paramsData.source,
            showBaseDialog: 'false',
          },
        });
      });
    },
    // 点击修改基本信息
    modifyBaseInfo() {
      this.showBaseDialog = true;
    },
    // 基本信息弹窗点击确认
    saveBaseMessage(data) {
      this.previewObj.name = data.name;
      this.previewObj.subjectId = data.subjectId;
      this.previewObj.subjectName = data.subjectName;
      this.previewObj.gradeId = data.gradeId;
      this.previewObj.gradeName = data.gradeName;
      this.previewObj.categoryId = data.categoryId;
      this.previewObj.categoryName = data.categoryName;
      this.previewObj.remark = data.remark;
      this.previewObj.year = data.year;

      this.closeDialog();
      this.savePersonalTestBank();
    },
    closeDialog() {
      this.showBaseDialog = false;
    },
    // 获取基本信息弹窗中数据
    getBaseInfo() {
      // 获取学科
      this.previewObj.subjectName = this.subjectMap[this.previewObj.subjectId].name;

      let grd = '';
      // 获取年级
      if (this.previewObj.gradeId) {
        grd = this.gradeList.find(item => item.id == gradeId);
        this.previewObj.gradeName = grd.name;
      }
      let categoryId = this.previewObj.categoryId;

      if (grd && categoryId) {
        let phaseId = grd.phaseId;
        let $this = this;
        getTBSearchInfo({ phase: Number(phaseId) - 2 }, function (data) {
          let c30 = data.sources;
          $this.previewObj.categoryName =
            c30.filter(obj => {
              return obj.id == categoryId;
            })[0].name || '';
        });
      }
      this.previewObj.year = new Date().getFullYear();
    },

    // 保存试卷
    downloadPaper() {
      if (this.paramsData.dlStatus != 1) {
        this.$message.error('正在生成PDF文件，请稍后下载！');
        return;
      }
      if (this.paramsData.source != 5) {
        this.showDialog = false;
        window.open(`${this.paramsData.fileUrl}`);
      } else {
        this.showDownloadDialog = true;
      }
    },
    // 去掉quesInfo中多余的数据
    getQuesInfo() {
      let quesInfo = this.previewObj.quesInfo;
      this.quesInfo = [];
      // 检查题类总分和题类下小题分数总和是否对的上
      quesInfo.forEach((item, index) => {
        this.quesInfo.push({ name: item.name, data: [], show: item.show });
        item.data.forEach((sub, subIdx) => {
          this.quesInfo[index].data.push({
            name: sub.name,
            type: sub.type,
            data: [],
          });
          sub.data.forEach(third => {
            this.quesInfo[index].data[subIdx].data.push({
              id: third.id,
              name: third.name,
              quesNo: third.name,
              quesNos: third.name,
              type: third.type,
              score: third.defaultScore,
            });
          });
        });
      });
    },

    // 保存基本信息
    savePersonalTestBank() {
      let loginInfo = this.$sessionSave.get('loginInfo');
      this.getQuesInfo();
      savePersonalTestBank({
        userId: loginInfo.id,
        userName: loginInfo.realname,
        schoolId: this.$sessionSave.get('schoolInfo').id,
        name: this.previewObj.name || '',
        categoryId: this.previewObj.categoryId || '',
        gradeId: this.previewObj.gradeId || '',
        subjectId: this.previewObj.subjectId || '',
        quesInfo: JSON.stringify(this.quesInfo),
        isSave: type == 'savePersonalTestBank' ? 1 : 0, // 除了预览里头的保存试卷传1，其他都传0
        year: this.previewObj.year || '',
        typeCode: this.previewObj.categoryId || '', //试卷类型编码
        typeName: this.previewObj.categoryName || '', //试卷类型名称
        tbId: this.previewObj.tbId || '', //试卷id
        sName: this.previewObj.subjectName || '', //学科名称
        gCode: this.previewObj.gradeId, //年级编码
        gName: this.previewObj.gradeName || '', //年级名称
        sCode: this.previewObj.subjectId || '', //学科编码
        // phase     : 3,//学段
        tbName: this.previewObj.name || '', //试卷名称
      }).then(res => {
        this.getViewPaper('update');
      });
    },
    //获取题型默认分值
    getDefaultScore(type) {
      let score = 0;
      this.scoreList.forEach(item => {
        if (item.dtTypeName == type) {
          score = item.score;
        }
      });
      return score;
    },
  },
};
</script>

<style lang="scss" scoped>
.router-view {
  display: flex;
  flex-direction: column;
}

.previewPaper {
  width: 100%;
  height: 100%;
  padding: 20px;
  background-color: #f1f5f8;
  font-family: Microsoft YaHei;
  .detail-header {
    position: relative;
    font-size: 16px;
    color: #3f4a54;
    margin: 4px 0 20px;
    height: 30px;
    line-height: 30px;
    > span {
      cursor: pointer;
    }
    .back-btn {
      display: flex;
      align-items: center;
      display: -webkit-box;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      cursor: pointer;
      font-size: 18px;
      color: #333;
      font-weight: 400;
      .el-icon-arrow-left {
        margin-right: 8px;
      }
    }
    .edit-ul {
      position: absolute;
      left: 50%;
      top: 0;
      transform: translateX(-50%);
      font-size: 16px;
      font-weight: 400;
      color: #3f4a54;
      padding-left: 26px;
      > li {
        display: inline-block;
        cursor: pointer;
        margin-right: 50px;
        &:last-child {
          margin-right: 0;
        }
      }
      .saveQues.disabled {
        opacity: 0.6;
        cursor: no-drop;
      }
    }
  }
  .detail-body {
    width: 100%;
    min-height: 300px;
    height: auto;
    > div {
      height: 100%;
      background: #fff;
      box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
      border-radius: 6px;
    }
    .left-list {
      min-height: 300px;
      flex-shrink: 0;
      margin-right: 15px;
      padding: 25px 24px 0 24px;
      overflow-x: hidden;
      overflow-y: auto;
      .paper-title {
        font-size: 18px;
        font-weight: bold;
        color: #3f4a54;
        // line-height: 52px;
        // height: 52px;
        text-align: center;
      }
      .second-title {
        position: relative;
        font-size: 18px;
        font-weight: 400;
        color: #3f4a54;
        line-height: 44px;
        height: 44px;
        text-align: center;
        border-radius: 4px 0 4px 4px;
        border: 1px solid #fff;
        margin: 36px 0 0;
      }
      .third-box {
        width: 100%;
        box-sizing: border-box;
        margin-bottom: 20px;
        .third-title {
          position: relative;
          line-height: 44px;
          // height: 44px;
          margin: 7px 0;
          font-size: 16px;
          font-weight: 400;
          color: #3f4a54;
          text-align: left;
          // padding-left: 20px;
          border-radius: 4px;
          border: 1px solid #fff;
          .title-score {
            color: #717980;
          }
        }
        .quesContent {
          width: 100%;
          box-sizing: border-box;
          position: relative;
          padding: 7px 5px;
          border-radius: 4px 0 4px 4px;
          border: 1px solid #fff;
          /*margin-bottom : 36px;*/
          .hover-btn {
            display: none;
            height: 36px;
            position: absolute;
            right: -1px;
            // top: -36px;
            border: 1px solid #409eff;
            border-radius: 4px 4px 0 0;
            // border-bottom: 1px solid #fff;
            border-top: 1px solid #fff;
            border-left: 1px solid #fff;
            bottom: -1px;
            background: #fff;
            > span {
              cursor: pointer;
              position: relative;
              height: 36px;
              line-height: 36px;
              padding-left: 36px;
              padding-right: 17px;
              display: inline-block;
              &:before {
                content: '';
                position: absolute;
                left: 13px;
                top: 7px;
                width: 24px;
                height: 24px;
                background: url('../../assets/preview_icon.png');
                background-position: -29px -58px;
              }
              &:nth-child(1) {
                &:before {
                  background-position: 0px -58px;
                }
                &.open:before {
                  left: 12px;
                  top: 8px;
                  background-position: -58px -29px;
                }
              }
            }
          }
          &:hover,
          &.active {
            border: 1px solid #409eff;
            .hover-btn {
              display: inline-block;
            }
          }
          .ques-list {
            .ques-detail {
              margin-top: 20px;
              &.active {
                display: none !important;
              }
            }
          }
          .select-btn {
            height: 36px;
            line-height: 36px;
            padding-left: 16px;
            padding-right: 17px;
            display: inline-block;
          }
          .el-icon-plus,
          .el-icon-minus {
            color: #409eff;
          }
        }
      }
    }
    .right-content {
      width: 520px;
      flex-shrink: 0;
      min-height: 300px;
      .right-content__header {
        height: 47px;
        padding: 0 23px 0 32px;
        border: 1px solid #e4e8eb;
        .left {
          li {
            cursor: pointer;
            line-height: 45px;
            display: inline-block;
            width: auto;
            &.active {
              color: #409eff;
              border-bottom: 2px solid #409eff;
            }
            &:first-child {
              margin-right: 45px;
            }
            &:last-child {
              width: auto;
            }
          }
        }
        .right {
          line-height: 47px;
        }
      }
      .right-content__body {
        width: 100%;
        overflow-x: hidden;
        overflow-y: auto;
        .baseInfo {
          width: 100%;
          padding-left: 30px;
          font-size: 16px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #4e5668;
          line-height: 36px;
          overflow-x: hidden;
          overflow-y: auto;
          padding-top: 10px;
          > div {
            text-align: left;
          }
          > button {
            margin-top: 20px;
            margin-bottom: 20px;
          }
          .twoWords {
            letter-spacing: 32px;
          }
        }
        .quesCard {
          .quesCard-ul {
            height: 60px;
            line-height: 60px;
            > li {
              display: inline-block;
              height: 100%;
              &:nth-child(1) {
                width: 147px;
                padding-left: 28px;
              }
              &:nth-child(2) {
                margin-left: 20px;
              }
              &:nth-child(3) {
                margin-left: 60px;
              }
              &:nth-child(4) {
                margin-left: 35px;
              }
            }
          }
        }
      }
    }
  }
}

.defaultScore {
  font-size: 16px;
  color: #717980;
}
.img-container {
  display: flex;
  flex-wrap: wrap;
  .img-list {
    margin: 10px 10px;
    .img {
      border: 1px solid #ebeef1;
    }
    p {
      text-align: center;
      // margin-top: -15px;
    }
  }
}
.previewName {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  width: 500px;
}
</style>
<style lang="scss">
.paper-title {
  height: 52px;
  .el-input__inner {
    height: 52px;
    line-height: 52px;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: #3f4a54;
    border: none;
  }
}

.quesCard {
  .el-tree-node__content {
    height: 40px;
    line-height: 40px;
    margin-bottom: 14px;
    margin-top: 1px;
    .el-tree-node__expand-icon {
      font-size: 18px;
      transform: rotate(0deg);
      color: #acb9c3;
      &:before {
        content: '\e7a0';
        /*content : "\e7a2";*/
      }
      &.expanded:before {
        content: '\e7a2';
      }
      &.is-leaf:before {
        content: none;
      }
    }
    .el-input {
      width: auto;
    }
    .el-input__inner {
      width: 120px;
    }
    &:hover {
      border: 1px solid #409eff;
      -webkit-box-shadow: 0 0 20px #eaeee9;
      box-shadow: 0 0 20px #eaeee9;
      background: #fff;
    }
  }
  .el-tree-node__children {
    .el-tree-node__content {
      .el-input {
        width: auto;
      }
      .el-input__inner {
        width: 101px;
        padding: 0 7px;
      }
    }
  }
}

.foldIcon {
  font-size: 18px;
  color: #acb9c3;
  margin-right: 8px;
  cursor: pointer;
}

.treeDrag {
  font-family: Microsoft YaHei;
  font-size: 14px;
  font-weight: 400;
  color: #545454;
  .nodeBox {
    padding: 10px;
    border: 1px solid transparent;
    border-radius: 2px;
    &:hover {
      border: 1px solid #409eff;
      -webkit-box-shadow: 0 0 20px #eaeee9;
      box-shadow: 0 0 20px #eaeee9;
    }
    &.error {
      border: 1px solid #ee4334;
    }
    .el-input__inner {
      height: 100%;
      padding: 0 10px;
      font-size: 14px;
      font-weight: 400;
      color: #545454;
    }
    > div {
      width: 100%;
      height: 34px;
      .foldIcon {
        font-size: 18px;
        color: #acb9c3;
        margin-right: 8px;
      }
    }
    .nodeEle {
      height: 34px;
      .node__input {
        width: 91px;
        height: 100%;
        /*margin-right : 16px;*/
        .el-input__inner {
          width: 91px;
        }
        &.firstNode__input {
          width: 120px;
          .el-input__inner {
            width: 120px;
          }
        }
      }
      .quesType {
        display: inline-block;
        width: 56px;
        margin-left: 14px;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .totalScoreSpan {
        display: inline-block;
        width: 96px;
      }
      .checkboxSpan {
        display: inline-block;
        width: 76px;
        margin-left: 10px;
      }
      .single-total {
        width: 50px;
        .el-input__inner {
          width: 50px;
          height: 34px;
          padding: 0 10px;
        }
      }
      > .el-checkbox {
        margin-left: 10px;
        .el-checkbox__label {
          padding-left: 6px;
        }
      }
      .editbtn {
        width: 76px;
        > span {
          cursor: pointer;
        }
      }
      .hiddenSpan {
        font-size: 14px;
        color: #409eff;
        margin-left: 10px;
      }
      .deleteSpan {
        font-size: 14px;
        color: #ee4334;
        margin-left: 10px;
      }
    }
  }
  .node__child {
    padding-left: 26px;
  }
}

.ghostClass {
  border-radius: 2px;
  border: 1px solid #409eff;
}

.chosenClass {
  opacity: 0.5;
}
.question_body ol ::marker {
  color: white;
}
.question_body ol li ::marker {
  color: #545454;
}
</style>

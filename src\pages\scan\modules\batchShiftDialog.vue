<template>
    <el-dialog title="批量转移" :visible.sync="visible" width="600px" :before-close="handleClose"
        custom-class="batch-shift-dialog">
        <div class="batch-shift-container">
            <el-form :model="dlgExamInfo" label-width="80px">
                <div class="form-row">
                    <el-form-item label="年级：">
                        <el-select v-model="dlgExamInfo.gradeId" @change="onSelectGradeChange" clearable
                            placeholder="请选择年级" class="custom-select">
                            <el-option v-for="item in gradeList" :key="item.gradeId" :label="item.grade_name"
                                :value="item.gradeId">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="学科：">
                        <el-select v-model="dlgExamInfo.subjectId" placeholder="请选择学科" @change="getSchoolExamList"
                            clearable class="custom-select">
                            <el-option v-for="item in subjectList" :key="item.id" :label="item.subject_name"
                                :value="item.id" />
                        </el-select>
                    </el-form-item>
                </div>

                <el-form-item label="测评：">
                    <el-select v-model="dlgExamInfo.paperInfo" value-key="paperNo" filterable remote reserve-keyword
                        :remote-method="queryExamList" placeholder="请选择测评" class="custom-select"
                        @change="getTargetCardImg">
                        <el-option v-for="(item, index) in examList" :key="item.paperNo" :label="item.tbName"
                            class="batch-shift-exam-option" :value="item">
                            <div :title="item.tbName" class="line-text" style="overflow: auto;text-overflow: ellipsis;">
                                <div>{{ item.paperNo }}</div>
                                <div>{{ item.tbName }}</div>
                            </div>
                            <div :title="item.examTitle" class="line-text"
                                style=" color: #8492a6; font-size: 13px;overflow: auto;text-overflow: ellipsis;">测评名称：{{
        item.examTitle }}</div>
                        </el-option>
                    </el-select>
                </el-form-item>

                <div class="tip-box">
                    <i class="el-icon-warning-outline"></i>
                    <span class="tip-text">
                        转移后所选扫描批次将按照所选测评的定位点进行识别。
                    </span>
                </div>

                <el-form-item v-if="targetCardImgList.length" label="" label-width="0px">
                    <div>图片对比验证：</div>
                    <div class="img-box">
                        <div class="img-box-item cur-img-box">
                            <div>待转移批次首份图片</div>
                            <el-image style="width: 150px;" :src="currentCardImgList[0]"
                                :preview-src-list="currentCardImgList">
                            </el-image>
                        </div>
                        <div class="img-box-item target-img-box">
                            <div>目标答题卡图片</div>
                            <el-image style="width: 150px;" :src="targetCardImgList[0]"
                                :preview-src-list="targetCardImgList">
                            </el-image>
                        </div>
                    </div>

                </el-form-item>
            </el-form>
        </div>

        <span slot="footer" class="dialog-footer">
            <el-button @click="handleClose" class="cancel-btn">取 消</el-button>
            <el-button type="primary" @click="handleConfirm" :loading="loading" class="confirm-btn">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import {
    getSchoolGrade,
    getSubjectsAPI,
} from '@/service/api';
import { getTaskFirstImageAPI } from '@/service/pexam';
import { getCardPaperList } from '@/service/testbank';
import { getPaperImgMark } from '@/service/xueban';
import {
    updateTaskExam,imagesMove
} from '@/service/pexam';
export default {
    name: 'BatchShiftDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        schoolId: {
            type: String,
            default: ""
        },
        taskList: {
            type: Array,
            default: () => {
                return [];
            }
        },
        imageList: {
            type: Array,
            default: () => {
                return [];
            }
        }
    },
    data() {
        return {
            dlgExamInfo: {
                gradeId: '',
                subjectId: '',
                paperInfo: null
            },
            allSubjectList: [],
            gradeList: [],
            subjectList: [],
            examList: [],
            currentCardImgList: [],
            targetCardImgList: [],
            loading: false
        }
    },
    mounted() {
        this.getGradeSubjectInfo(this.schoolId)
        if(this.imageList.length){
            this.currentCardImgList = this.imageList.map(item => item.images).flat()
        }else{
            this.getCurrentCardImg();
        }
    },
    methods: {
        /**
         * 获取年级学科列表
         * @param schoolId
         */
        getGradeSubjectInfo(schoolId) {
            if (!schoolId) return;
            // 获取年级列表
            if (this.gradeList.length === 0) {
                getSchoolGrade({
                    schoolId: schoolId,
                }).then(res => {
                    this.gradeList = res.data;
                });
            }

            if (this.allSubjectList.length === 0) {
                getSubjectsAPI({
                    schoolId: schoolId,
                }).then(res => {
                    this.allSubjectList = res.data;
                    // console.log(res)
                });
            }
        },
        onSelectGradeChange(gradeId) {
            let phase = 0;
            if (gradeId >= 1 && gradeId <= 6) {
                phase = 1;
            } else if (gradeId >= 7 && gradeId <= 9) {
                phase = 2;
            } else if (gradeId >= 10 && gradeId <= 12) {
                phase = 3;
            }
            this.subjectList = this.allSubjectList.filter(item => {
                return item.phase === phase;
            });
            this.dlgExamInfo.subjectId = this.subjectList[0].id
            this.getSchoolExamList();
        },
        /**
         * 获取考试列表
         */
        getSchoolExamList() {
            this.queryExamList(this.dlgExamInfo.query);
        },
        queryExamList(query) {
            this.dlgExamInfo.query = query;
            if (this.dlgExamInfo.loading) return;
            if (!this.dlgExamInfo.gradeId || !this.dlgExamInfo.subjectId) {
                // return
            }
            this.dlgExamInfo.loading = true;
            getCardPaperList({
                type: 5,
                schoolId: this.schoolId,
                gradeCode: this.dlgExamInfo.gradeId,
                subjectCode: this.dlgExamInfo.subjectId,
                relatedWorkState: 1,
                name: query || '',
                page: 1,
                limit: 50,
            })
                .then(res => {
                    this.dlgExamInfo.loading = false;
                    this.examList = res.data.rows;

                    // if (this.dlgExamInfo.examId) {
                    //     let examInfo = this.examList.find(it => it.workId === this.dlgExamInfo.examId);
                    //     if (!examInfo) {
                    //         this.dlgExamInfo.examId = '';
                    //     }
                    // }

                    // 查询条件变化，再次查询
                    if (query !== this.dlgExamInfo.query) {
                        this.queryExamList(this.dlgExamInfo.query);
                    }
                })
                .catch(data => {
                    this.dlgExamInfo.loading = false;
                });
        },
        async getCurrentCardImg() {
            const params = {
                taskId: this.taskList[0].id,
            };
            const res = await getTaskFirstImageAPI(params);
            if (res.code == 1) {
                let list = res.data.sort((a, b) => {
                   return a.idx_num > b.idx_num 
                })
                this.currentCardImgList = list.map(item => item.image||item.origin)
            } else {
                this.currentCardImgList = [];
                this.$message.error("批次图片获取失败");
            }
        },
        async getTargetCardImg(item) {
            const params = {
                paper_no: item.paperNo,
                show_answer: false,
            };
            const res = await getPaperImgMark(params);
            if (res.code == 1) {
                this.targetCardImgList = res.data;
            } else {
                this.targetCardImgList = [];
                this.$message.error("图片获取失败");
            }
        },
        async updateBatch() {
            const taskId = this.taskList.map(item => item.id)
            let params = {
                taskId: taskId.join(','),
                examId: this.dlgExamInfo.paperInfo.workId,
                paperNo: this.dlgExamInfo.paperInfo.paperNo
            };
            let res = await updateTaskExam(params);
            if (res.code == 1) {
                await this.$confirm('扫描批次已转移成功，是否跳转到新的测评？', '批量转移', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });
                location.href = `${location.pathname}?examId=${this.dlgExamInfo.paperInfo.workId}&isEdit=2&teacherId=&schoolId=${this.schoolId}`
                this.loading = false;
            }else{
                this.$message.error(res.msg)
            }
        },
        async imagesMove(){
            let jsonImageArr = this.imageList.map(item => {
                return {
                    "idx": item.idx,
                    "pre_task_id": item.pre_task_id,
                    "exam_id": item.exam_id,
                    "task_id": item.task_id
                }
            })
            let params = {
                jsonImageArr: JSON.stringify(jsonImageArr),
                examId: this.dlgExamInfo.paperInfo.workId,
                paperNo: this.dlgExamInfo.paperInfo.paperNo
            }
            let res = await imagesMove(params);
            if (res.code == 1) {
                this.$confirm('扫描批次已转移成功，是否跳转到新的测评？', '批量转移', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    location.href = `${location.origin}/bigdata/scan_task?examId=${this.dlgExamInfo.paperInfo.workId}&isEdit=2&teacherId=&schoolId=${this.schoolId}`
                }).catch(()=>{
                    this.$emit("confirm")
                    this.handleClose()
                });
            }else{
                this.$message.error(res.msg)
            }
        },
        handleClose() {
            this.close(true)
        },
        async handleConfirm() {
            this.loading = true;
            if(this.imageList.length){
                await this.imagesMove();
            }else{
                // 处理确认逻辑
                await this.updateBatch();
            }
        },
        close(isCancel) {
            this.$emit('cancel', isCancel)
        }
    }
}
</script>

<style lang="scss" scoped>
.batch-shift-dialog {
    border-radius: 8px;

    ::v-deep .el-dialog__header {
        padding: 20px 24px;
        border-bottom: 1px solid #ebeef5;

        .el-dialog__title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
        }
    }
}

.batch-shift-container {
    padding: 24px 24px 0;

    .form-row {
        display: flex;
        gap: 20px;
        margin-bottom: 24px;

        .el-form-item {
            flex: 1;
            margin-bottom: 0;
        }
    }

    .custom-select {
        width: 100%;
    }

    .el-form-item {
        margin-bottom: 24px;
    }

    .tip-box {
        display: flex;
        align-items: flex-start;
        padding: 12px 16px;
        background-color: #fff9f9;
        border-radius: 4px;
        margin-bottom: 24px;

        .el-icon-warning-outline {
            color: #f56c6c;
            font-size: 16px;
            margin-right: 8px;
            margin-top: 2px;
        }

        .tip-text {
            color: #f56c6c;
            font-size: 14px;
            line-height: 1.5;
        }
    }

    .img-box {
        width: 100%;
        display: flex;
        justify-content: space-around;
    }
}

.dialog-footer {
    padding: 16px 24px;

    .cancel-btn {
        margin-right: 12px;
    }

    .confirm-btn {
        padding: 9px 20px;
    }
}
</style>
<style lang="scss">
.batch-shift-exam-option {
    &.el-select-dropdown__item {
        height: auto;

        .line-text {
            div {
                display: inline-block;
                margin-right: 20px;
            }
        }
    }
}
</style>

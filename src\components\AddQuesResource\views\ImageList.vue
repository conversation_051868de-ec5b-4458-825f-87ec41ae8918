<!--
 * @Description: 图片管理列表，支持新增和预览
 * @Author: qmzhang
 * @Date: 2024-12-12 09:53:22
 * @LastEditTime: 2025-01-10 08:43:27
 * @FilePath: \personal-bigdata\src\components\AddQuesResource\views\ImageList.vue
-->
<template>
    <div class="image-list">
        <el-upload ref="uploadRef" class="image-list__uploader"
            :class="{ 'disable-add': disableAdd, 'view-mode': mode == 'look' }" :file-list="uploadedList" action="#"
            :accept="accepts" :limit="limitLength" list-type="picture-card" :on-change="handlePicChange"
            :on-exceed="handleOnExceed" :auto-upload="false" :http-request="handleHttpUpload" multiple
            v-if="mode == 'add' || selectFileList.length || originList.length">
            <i slot="default" class="el-icon-plus"></i>
            <div class="wh100" slot="file" slot-scope="{file}">
                <el-image class="el-upload-list__item-thumbnail click-element" :src="file.url" fit="cover"
                    @click="handlePictureCardPreview(file)" v-if="isDesktop" />
                <el-image class="el-upload-list__item-thumbnail" :src="file.url" fit="cover" v-else />
                <span class="el-upload-list__item-actions" v-if="!isDesktop">
                    <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                    </span>
                    <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                        <i class="el-icon-delete"></i>
                    </span>
                </span>
            </div>
        </el-upload>

        <el-empty description="暂未添加图片资源" v-else></el-empty>


        <el-dialog title="图片预览" :modal="false" :visible.sync="dialogVisible">
            <img width="100%" class="dis-block" :src="dialogImageUrl" alt="">
        </el-dialog>
    </div>
</template>
<script lang="ts">
import { ElUpload, HttpRequestOptions } from '@iclass/element-ui/types/upload';
import ajaxUpload from '@iclass/element-ui/packages/upload/src/ajax';
import { Component, Vue, Prop } from 'vue-property-decorator';
import { QuesResource } from './ResAddViewList.vue';
import { deepClone } from '@/utils/index';
import moment from 'moment';
import { getQueryString } from '@/utils';

@Component
export default class ResViewList extends Vue {
    @Prop() vModel;
    // 模式：add|view:添加|预览
    @Prop() mode: string
    // 禁用添加
    @Prop({ default: false }) disableAdd: boolean
    @Prop({ default: 0 }) maxLength: number
    @Prop() originList: QuesResource[]
    public $refs!: {
        uploadRef: ElUpload;
    };

    dialogImageUrl = ''
    dialogVisible = false
    disabled = false
    // 选择的图片列表
    selectFileList = [];
    accepts = "image/png, image/jpeg,image/png,image/png, image/webp"
    // 已上传列表
    uploadedList: QuesResource[] = [];
    // 是否桌面
    isDesktop = (!!window.cef  && this.$route.name != 'paperComment') || getQueryString('isDesktop') == 1;

    limitLength: number = 0;
    // 准备中的文件数量
    readyListLength: number = 0;

    created() {
        this.uploadedList = deepClone(this.originList)
        this.uploadedList.forEach(it => this.$emit('res-add', 'image'));
        this.readyListLength = this.uploadedList.length;
        this.limitLength = this.maxLength - this.readyListLength;
    }

    /**
     * @description: 处理添加图片
     * @param {*} file
     * @param {*} fileList
     * @return {*}
     */
    handlePicChange(file, fileList) {
        this.selectFileList = fileList;
        this.$emit('res-add', 'image')
        this.readyListLength = fileList.filter(it => it.status == 'ready').length;
    }

    handleRemove(file) {
        let fileId = file.resourceId || file.uid
        let findeIndex = this.selectFileList.findIndex(it => (it.resourceId || it.uid) == fileId);
        if (findeIndex != -1) {
            this.selectFileList.splice(findeIndex, 1);
        }
        findeIndex = this.uploadedList.findIndex(it => it.resourceId == fileId);
        this.readyListLength = this.selectFileList.filter(it => it.status == 'ready').length;
        if (findeIndex != -1) {
            this.uploadedList.splice(findeIndex, 1);
        }
        this.$emit('res-remove', 'image')
        this.$emit('turnAddMode')
        console.debug("handleRemove", file);
    }

    /**
     * @description: 预览退案
     * @param {*} file
     * @return {*}
     */
    handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        this.dialogVisible = true;
    }

    /**
     * @description: 超出选择数量
     * @return {*}
     */
    handleOnExceed() {
        this.$notify({
            title: '无法添加',
            offset: 100,
            message: `超出选择数量，当前最多只能选择${this.maxLength - this.readyListLength}张！`,
            type: 'warning'
        });
    }

    /**
     * @description: 处理自定义上传
     * @return {*}
     */
    handleHttpUpload(options: HttpRequestOptions) {
        const self = this;
        const uploadAction = `https://service.iclass30.com/testbank/testBank/uploadEditorImgNew?htmlurl=${location.href}&responseType=json`;
        const file: any = options.file;
        ajaxUpload({
            ...options,
            action: uploadAction,
            filename: 'upload',
            file: options.file,
            onSuccess(res) {
                if (res.code == -1) {
                    self.$notify({
                        title: "上传失败",
                        message: `图片 ${file.name} 上传失败，请稍后重试！`,
                        type: 'error',
                        offset: 100,
                        duration: 10000
                    })
                    self.handleRemove(file);
                } else {
                    const now = moment();
                    const formattedDate = now.format('YYYY-MM-DD HH:mm');
                    self.uploadedList.push({
                        resourceType: '4',
                        resourceName: file.name,
                        resourceId: file.uid,
                        fileSize: file.size,
                        resourceUrl: res.url,
                        resourceExt: file.name.substring(file.name.lastIndexOf('.') + 1),
                        uploadDate: formattedDate,
                        name: file.name,
                        url: res.url,
                    });
                }

                if (self.uploadedList.length === self.selectFileList.length) {
                    self.$bus.$emit("uploadFinish")
                }
            },
        });
    }

    async confirm(): Promise<any[]> {
        if (!this.selectFileList.length) return this.uploadedList;

        this.$refs.uploadRef.submit();
        return new Promise(async (resolve, reject) => {
            this.$bus.$off("uploadFinish")
            this.$bus.$on("uploadFinish", () => {
                resolve(this.uploadedList);
            })
        })
    }
}

</script>

<style lang="scss" scoped>
.el-upload-list__item-thumbnail {
    object-fit: cover;
}

.image-list__uploader {
    &.disable-add {
        ::v-deep .el-upload--picture-card {
            pointer-events: none;
            opacity: .4;
        }
    }

    &.view-mode {
        ::v-deep .el-upload--picture-card {
            display: none;
        }
    }

    ::v-deep .el-upload-list__item {
        transition-duration: .3s;
    }
}
</style>
<template>
  <el-dialog
    custom-class="miss-stu-dialog"
    :visible.sync="isVisible"
    width="1020px"
    :modal-append-to-body="false"
    :close-on-click-modal="false"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <div slot="title" class="el-dialog__title">
      <span style="margin-right: 10px">{{ title || '缺考名单' }}</span>
    </div>
    <span
      > {{ countLabel || '缺考人数' }} ：<span style="font-weight: bold">{{ tableData.length }}</span></span
    >
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      ref="missTableRef"
      style="width: 100%"
      stripe
      class="reportCard-table"
      :header-cell-style="{ fontSize: '16px', color: '#3F4A54' }"
      max-height="350"
    >
      <el-table-column prop="stuNo" label="学号" fixed></el-table-column>
      <el-table-column prop="clzName" label="班级" fixed></el-table-column>
      <el-table-column prop="stuName" label="姓名" fixed></el-table-column>
      <el-table-column prop="typeCode" label="类型" fixed>
        <template slot-scope="scope">
          <span>{{ getTypeTextByTypeCode(scope.row.typeCode) }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="isComfirming" @click="closeModal">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { listAbsentStu } from '@/service/pexam';
export default {
  props: {
    title: {
      type: String,
      required: false,
      default: '缺考名单',
    },
    countLabel: {
      type: String,
      required: false,
      default: '缺考人数',
    },
    subjectId: {
      type: [String, Number],
      required: false,
    },
    clzId: {
      type: String,
      required: false,
    },
    examId: {
      type: String,
      required: false,
    },
  },
  data() {
    return {
      isVisible: true,
      isComfirming: false,
      tableLoading: false,
      tableData: [],
    };
  },
  mounted() {
    this.tableData = [];
    this.getMissStu();
    this.$nextTick(() => {
      this.$refs.missTableRef.bodyWrapper.scrollTop = 0;
    });
  },
  methods: {
    /**
     * @name:获取缺考学生名单
     */
    getMissStu() {
      this.tableLoading = true;
      listAbsentStu({
        examId: this.examId || this.$sessionSave.get('reportDetail').examId,
        subjectId: this.subjectId,
        clzId: this.clzId,
        v: this.$sessionSave.get('reportDetail').v,
      })
        .then(res => {
          this.tableData = res.data;
          this.tableLoading = false;
        })
        .catch(err => {});
    },

    // 根据类型值获取类型文本
    getTypeTextByTypeCode(code) {
      if (code == 1) {
        return '未扫描';
      } else if (code == 2) {
        return '缺考';
      } else if (code == 3) {
        return '批改不完整';
      } else if (code == 4) {
        return '零分卷';
      } else if (code == 5) {
        return '不参与统计';
      }
      return code;
    },

    handleClose() {
      this.$emit('close-miss-dialog');
    },
    closeModal() {
      this.isVisible = false;
      // this.$emit('close-miss-dialog');
    },
  },
};
</script>

<style lang="scss" scoped></style>

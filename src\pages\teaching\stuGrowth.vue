<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-12-17 16:30:00
 * @LastEditors: 小圆
-->
<template>
  <div class="stu-growth">
    <div class="stu-growth-main" v-if="selectExamList.length" v-loading="isLoading">
      <div class="stu-wrapper" v-loading="stuLoading">
        <div class="stu-class-select">
          <span class="stu-class-select__title">当前班级：</span>
          <el-select
            class="stu-class-select__select"
            v-model="filterData.classId"
            style="width: 120px"
            placeholder="请选择班级"
            @change="changeClass"
          >
            <el-option v-for="item in classList" :key="item.classId" :label="item.class_name" :value="item.classId">
            </el-option>
          </el-select>
        </div>

        <div class="stu-search">
          <el-input
            v-model="filterData.realname"
            class="stu-search__input"
            placeholder="请输入学生姓名"
            @keyup.enter.native="getStuList"
          >
            <i slot="suffix" class="el-input__icon el-icon-search" @click="getStuList"></i>
          </el-input>
        </div>
        <div class="stu-ul" v-if="stuList.length" ref="stuList">
          <div
            v-for="item in stuList"
            class="stu-li"
            :key="item.id"
            :class="{ active: item.id == filterData.stuId }"
            :title="item.username"
            @click="switchStu(item)"
          >
            <div class="stu-name">
              {{ item.username }}
            </div>
          </div>
        </div>
      </div>

      <div class="score-wrapper" v-loading="loading">
        <div class="score-main">
          <div class="score-header">
            <div class="score-header-left">
              <div class="score-header-title">成绩趋势变化</div>
              <el-button class="sel-exam-btn" type="text" @click="isShowExamGrowthDialog = true">选择测评</el-button>
            </div>
            <!-- <div class="score-header-right">
                            <el-button type="primary" @click="isShowExportDialog = true">导出</el-button>
                        </div> -->
          </div>
          <div class="score-tip">注：点击学科得分可查看作答原卷</div>

          <div class="score-type-wrapper">
            <el-radio-group class="score-type-radio" v-model="examScoreType" @change="initExamScoreChart">
              <el-radio-button label="table">表格</el-radio-button>
              <el-radio-button label="chart">图表</el-radio-button>
            </el-radio-group>
          </div>
          <el-table
            v-if="examScoreType == 'table'"
            style="width: 100%"
            :data="examTable"
            :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
            v-drag-table
            v-sticky-table="0"
            row-key="id"
          >
            <el-table-column prop="examTime" label="测评时间" min-width="180" align="center" fixed="left">
            </el-table-column>
            <el-table-column
              prop="name"
              label="测评名称"
              min-width="200"
              header-align="center"
              align="left"
              fixed="left"
            >
              <template #default="scope">
                <el-tag v-if="scope.row.start == 1" size="mini" class="start-tag">起点</el-tag>
                <span>{{ scope.row.name }}</span>
                <el-tag v-if="scope.row.source" size="mini" type="warning" class="source-tag">{{
                  scope.row.source
                }}</el-tag>
              </template>
            </el-table-column>

            <el-table-column v-for="item in column" :key="item.subjectId" :label="item.subjectName" align="center">
              <el-table-column label="得分" align="center" :min-width="90">
                <template #default="scope">
                  <span v-if="typeof scope.row.data[item.subjectId].score === 'number'">
                    <el-button type="text" v-if="item.subjectId" @click="setShowPaper(scope.row, item)">{{
                      scope.row.data[item.subjectId].score
                    }}</el-button>
                    <span v-else> {{ scope.row.data[item.subjectId].score }}</span>
                  </span>
                  <span v-else>--</span>
                </template>
              </el-table-column>
              <el-table-column label="升降" align="center" :min-width="90">
                <template #default="scope">
                  <span :class="{ red: scope.row.data[item.subjectId].scoreDiff < 0 }">
                    {{
                      typeof scope.row.data[item.subjectId].scoreDiff === 'number'
                        ? scope.row.data[item.subjectId].scoreDiff
                        : '--'
                    }}
                  </span>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
          <div v-if="examScoreType == 'chart'" ref="examScoreChart" class="chart-wrapper"></div>
        </div>

        <div class="score-main">
          <div class="score-header">
            <div class="score-header-title">排名趋势变化</div>
          </div>
          <div class="score-tip">注：若是3+1+2类型的考试，排名展示对应方向排名</div>

          <div class="score-type-wrapper" v-if="isClsRankEnable">
            <el-radio-group class="score-type-radio" v-model="examRankType" @change="initExamRankChart">
              <el-radio-button label="table">表格</el-radio-button>
              <el-radio-button label="chart">图表</el-radio-button>
            </el-radio-group>
          </div>

          <el-table
            v-if="examRankType == 'table'"
            style="width: 100%"
            :data="examTable"
            :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
            v-drag-table
            v-sticky-table="0"
            row-key="id"
          >
            <el-table-column prop="examTime" label="测评时间" min-width="180" align="center" fixed="left">
            </el-table-column>
            <el-table-column
              prop="name"
              label="测评名称"
              min-width="200"
              header-align="center"
              align="left"
              fixed="left"
            >
              <template #default="scope">
                <el-tag v-if="scope.row.start == 1" size="mini" class="start-tag">起点</el-tag>
                <span>{{ scope.row.name }}</span>
                <el-tag v-if="scope.row.source" size="mini" type="warning" class="source-tag">{{
                  scope.row.source
                }}</el-tag>
              </template>
            </el-table-column>

            <el-table-column v-for="item in column" :key="item.subjectId" :label="item.subjectName" align="center">
              <el-table-column label="校排" align="center" :min-width="90" v-if="isShowGrdRank">
                <template #default="scope">
                  <span v-if="isGrdRankEnable">
                    {{
                      typeof scope.row.data[item.subjectId].grdRank === 'number'
                        ? scope.row.data[item.subjectId].grdRank
                        : '--'
                    }}
                  </span>
                  <span v-else>
                    <el-tooltip effect="dark" content="应相关部门/学校要求，该数据不予展示" placement="top">
                      <i class="el-icon-lock"></i>
                    </el-tooltip>
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="升降" align="center" :min-width="90" v-if="isShowGrdRank">
                <template #default="scope">
                  <span v-if="isGrdRankEnable" :class="{ red: scope.row.data[item.subjectId].grdDiff < 0 }">
                    {{
                      typeof scope.row.data[item.subjectId].grdDiff === 'number'
                        ? scope.row.data[item.subjectId].grdDiff
                        : '--'
                    }}
                  </span>
                  <span v-else>
                    <el-tooltip effect="dark" content="应相关部门/学校要求，该数据不予展示" placement="top">
                      <i class="el-icon-lock"></i>
                    </el-tooltip>
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="班排" align="center" :min-width="90">
                <template #default="scope">
                  <span v-if="isClsRankEnable">
                    {{
                      typeof scope.row.data[item.subjectId].clzRank === 'number'
                        ? scope.row.data[item.subjectId].clzRank
                        : '--'
                    }}
                  </span>
                  <span v-else>
                    <el-tooltip effect="dark" content="应相关部门/学校要求，该数据不予展示" placement="top">
                      <i class="el-icon-lock"></i>
                    </el-tooltip>
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="升降" align="center" :min-width="90">
                <template #default="scope">
                  <span v-if="isClsRankEnable">
                    <span :class="{ red: scope.row.data[item.subjectId].clzDiff < 0 }">
                      {{
                        typeof scope.row.data[item.subjectId].clzDiff === 'number'
                          ? scope.row.data[item.subjectId].clzDiff
                          : '--'
                      }}
                    </span>
                  </span>
                  <span v-else>
                    <el-tooltip effect="dark" content="应相关部门/学校要求，该数据不予展示" placement="top">
                      <i class="el-icon-lock"></i>
                    </el-tooltip>
                  </span>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>

          <div v-if="examRankType == 'chart'" ref="examRankChart" class="chart-wrapper"></div>
        </div>
      </div>
    </div>
    <div class="no-exam" v-else v-loading="initLoading">
      您目前没有添加要分析的测评，
      <span @click="isShowExamGrowthDialog = true">请点击此处进行添加！</span>
    </div>

    <StuPaperPreview
      v-if="paperImage.show"
      :queryParams="paperImage.params"
      :defaultScore="paperImage.score"
      @close="paperImage.show = false"
    ></StuPaperPreview>
    <exam-growth-dialog
      v-if="isShowExamGrowthDialog"
      @confirm="getSelectExamList"
      @closed="isShowExamGrowthDialog = false"
    ></exam-growth-dialog>

    <el-dialog title="导出设置" :visible.sync="isShowExportDialog" width="400px" custom-class="export-dialog">
      <div class="export-content">
        <el-radio-group v-model="exportType">
          <div class="radio-item">
            <el-radio label="student">单个学生导出</el-radio>
          </div>
          <div class="radio-item">
            <el-radio label="class">本班学生导出</el-radio>
          </div>
        </el-radio-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowExportDialog = false">取 消</el-button>
        <el-button type="primary" @click="handleExport" :loading="exportLoading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { getStuListByClassId } from '@/service/api';
import { getUserDataAPI, listUserHisExamAPI } from '@/service/pexam';
import { Component, Vue } from 'vue-property-decorator';
import ExamGrowthDialog from './components/ExamGrowthDialog.vue';
import StuPaperPreview from '@/components/SwiperViewer/StuPaperPreview.vue';
import { getToken } from '@/service/auth';
import UserRole, { ClassList } from '@/utils/UserRole';
import service from '@/service';
import { listRoleMenuAPI } from '@/service/pstat';
import { findIntersection } from '@/utils/index';
import { FuncIdEnum } from '@/utils/examReportUtils';
interface IClassList extends ClassList {
  roles: number[];
}

export interface Stu {
  classid: string;
  examNo: string;
  user_name: string;
  mobile: string;
  pinyin_first: string;
  id: string;
  avatar: string;
  stuNo: string;
  username: string;
  realname: string;
}

export interface UserHisExam {
  id: number;
  name: string;
  examTime: Date;
  start: number;
  heads: Heads[];
  source: string;
  data: { [key: string]: UserHisExamData };
}

export interface UserHisExamData {
  abPaper: string | number;
  score: null;
  scoreDiff: null;
  clzRank: null;
  clzDiff: null;
  grdRank: null;
  grdDiff: null;
}

export interface Heads {
  subjectId: string;
  subjectName: string;
  testBankId: string;
  workId: string;
}

@Component({
  components: {
    StuPaperPreview,
    ExamGrowthDialog,
  },
})
export default class StuGrowth extends Vue {
  public $refs!: {
    examScoreChart: HTMLDivElement;
    examRankChart: HTMLDivElement;
  };
  filterData = {
    classId: '',
    realname: '',
    stuId: '',
    stuNo: '',
    year: '',
    gradeId: '',
    subjectId: '',
  };
  // 班级列表
  classList: IClassList[] = [];
  /** 学生列表 */
  stuList: Stu[] = [];
  // 筛选测评列表
  selectExamList: any[] = [];
  // 版本号
  v: number = null;

  // 历次测评数据
  examTable: UserHisExam[] = [];
  // 成绩趋势表格类型
  examScoreType: 'table' | 'chart' = 'table';
  // 排名趋势表格类型
  examRankType: 'table' | 'chart' = 'table';
  // 成绩趋势图表
  examScoreChart = null;
  // 排名趋势图表
  examRankChart = null;
  // 表格列
  column: Heads[] = [];

  // 初始化加载状态
  initLoading: boolean = false;
  // 学生列表加载状态
  stuLoading: boolean = false;
  // 加载状态
  loading: boolean = false;
  // 加载状态
  isLoading: boolean = false;
  // 是否显示添加测评弹窗
  isShowExamGrowthDialog: boolean = false;
  // 是否显示导出弹窗
  isShowExportDialog: boolean = false;

  // 查看原卷
  paperImage = {
    show: false,
    params: {
      workId: '',
      examId: '' as number | string,
      classId: '',
      subjectId: '' as number | string,
      studentNo: '',
      studentId: '',
      cardType: '',
      abPaper: '' as string | number,
    },
    score: 0,
  };
  // 导出类型
  exportType: 'student' | 'class' = 'student';
  // 导出加载状态
  exportLoading: boolean = false;

  // 校排功能是否启动
  isGrdRankEnable: boolean = true;
  // 班排功能是否启动
  isClsRankEnable: boolean = true;
  // 学校设置
  schoolPermission: ISchoolReportPermission = {
    funcRoles: [],
    funcIds: [],
    menuIds: [],
    menuRoles: [],
  };

  // 是否显示校排
  get isShowGrdRank() {
    return UserRole.isOperation || UserRole.isSchoolLeader;
  }

  async mounted() {
    await this.getSchoolPermission();
    this.getSelectExamList();
  }

  // 获取班级列表
  async initFuncPermission() {
    this.isGrdRankEnable = this.getFuncEnable(FuncIdEnum.GrdRankAscDesc);
    this.isClsRankEnable = this.getFuncEnable(FuncIdEnum.ClsRankAscDesc);
  }

  // 获取学校权限
  async getSchoolPermission() {
    if (UserRole.isOperation) return;
    const res = await listRoleMenuAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      role: UserRole.examRolesTypes,
    });
    this.schoolPermission = res.data;
  }

  // 获取功能权限
  getFuncEnable(funcId: string) {
    if (UserRole.isOperation) return true;

    let classList = this.classList;
    const classItem = classList.find(item => item.classId === this.filterData.classId);
    if (!classItem) return false;

    const classRoleIds = classItem.roles.map(role => Number(role));
    if ([1, 2, 3, 4].some(role => classRoleIds.includes(role))) return true;

    const funcRoles = this.schoolPermission.funcRoles || [];
    const func = funcRoles.find(item => item.funcId === funcId);
    if (!func) return false;

    const funcRoleIds = func.roleIds.map(item => Number(item));
    return findIntersection(funcRoleIds, classRoleIds).length > 0;
  }

  // 获取学生列表
  async getStuList() {
    this.stuLoading = true;
    try {
      this.stuList = [];
      if (!this.filterData.classId) {
        this.stuLoading = false;
        return;
      }
      let res = await getStuListByClassId({
        classId: this.filterData.classId,
        realname: this.filterData.realname,
      });
      this.stuList = Array.isArray(res.data) ? res.data : [];
    } catch (error) {
      this.stuList = [];
    } finally {
      this.stuLoading = false;
    }
  }

  // 更改学生
  async switchStu(item) {
    if (item) {
      this.filterData.stuId = item.id;
      this.filterData.stuNo = item.examNo;
    } else {
      this.filterData.stuId = '';
      this.filterData.stuNo = '';
    }
    await this.getUserHisExamList();
  }

  // 获取测评列表
  async getSelectExamList() {
    this.isShowExamGrowthDialog = false;
    this.initLoading = true;
    try {
      const res = await getUserDataAPI({
        userId: this.$sessionSave.get('loginInfo').id,
      });
      if (res.code == 1 && res.data) {
        this.selectExamList = res.data.jCfg.data;
        this.v = res.data.v;
        this.filterData.year = res.data.year;
        this.filterData.gradeId = res.data.grdId;
        this.filterData.subjectId = res.data.subjectId;
        await this.getClassList({
          year: this.filterData.year,
          gradeId: this.filterData.gradeId,
          subjectId: this.filterData.subjectId,
        });
        this.changeClass(this.classList[0]?.classId);
      }
    } catch (error) {
      console.error(error);
    } finally {
      this.initLoading = false;
    }
  }

  // 获取用户历次测评数据
  async getUserHisExamList() {
    if (!this.filterData.stuId) {
      this.column = [];
      this.examTable = [];
      return;
    }

    this.loading = true;
    try {
      const res = await listUserHisExamAPI({
        stuId: this.filterData.stuId,
        v: this.v,
      });
      let column = [];
      res.data.forEach(item => {
        item.heads.forEach(head => {
          column.push({
            subjectId: head.subjectId,
            subjectName: head.subjectName,
          });
        });
      });
      column = [...new Map(column.map(item => [item.subjectId, item])).values()];
      this.column = await this.getSubjectColumn(column);
      this.examTable = res.data;
      this.initExamScoreChart();
      this.initExamRankChart();
    } catch (error) {
      console.log(error);
      this.column = [];
      this.examTable = [];
    } finally {
      this.loading = false;
    }
  }

  /**
   * 获取学科列表
   * 具有运营、校管、班主任、年级主任权限，取全部学科
   * 其余取角色具有权限的学科
   */
  async getSubjectColumn(column) {
    let isAllSubject;
    // 运营、校领导、班主任
    if (
      UserRole.isOperation ||
      UserRole.isSchoolLeader ||
      UserRole.leaderClassList.find(item => item.id == this.filterData.classId)
    ) {
      isAllSubject = true;
    } else {
      // 年级主任
      let roles = UserRole.utils.getRolesByYear(this.filterData.year);
      if (roles.includes(2)) {
        isAllSubject = true;
      } else {
        isAllSubject = false;
      }
    }

    if (isAllSubject) {
      return column;
    }
    // 获取具有权限的学科列表
    let subjectList = await UserRole.getSubjectList({
      year: this.filterData.year,
    });
    return column.filter(item => subjectList.find(subject => subject.id == item.subjectId));
  }

  // 显示作答原卷
  async setShowPaper(item: UserHisExam, subjectItem: Heads) {
    let heads = item.heads.find(head => head.subjectId == subjectItem.subjectId);
    let workId = heads?.workId;
    let testBankId = heads?.testBankId;
    let abPaper = item.data[subjectItem.subjectId].abPaper;

    this.paperImage.params = {
      examId: item.id,
      workId: workId,
      subjectId: subjectItem.subjectId,
      classId: this.filterData.classId,
      studentNo: this.filterData.stuNo,
      studentId: this.filterData.stuId,
      abPaper: abPaper,
    };
    this.paperImage.score = item.data[subjectItem.subjectId].score;
    this.paperImage.show = true;
  }

  // 初始化成绩趋势图表
  initExamScoreChart() {
    if (this.examScoreType != 'chart') return;

    if (this.examScoreChart) {
      this.examScoreChart.dispose();
      this.examScoreChart = null;
    }

    this.$nextTick(() => {
      if (!this.$refs.examScoreChart) return;
      this.examScoreChart = this.$echarts.init(this.$refs.examScoreChart);
      this.examScoreChart.setOption({
        title: {
          left: 'center',
          text: '学生成绩变化趋势',
        },
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          data: this.column.filter(item => item.subjectId).map(item => item.subjectName),
          orient: 'vertical',
          left: 'right',
          top: 'middle',
          itemGap: 20,
          type: 'scroll',
        },
        grid: {
          left: '3%',
          right: '6%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: this.examTable.map((item, index) => {
            let name = item.name;
            if (index == 0) {
              name = '起点测评：' + item.name;
            }
            return name;
          }),
          axisLabel: {
            width: 100,
            interval: 0,
            rotate: 30,
            overflow: 'truncate',
            ellipsis: '...',
          },
        },
        yAxis: {
          type: 'value',
          name: '分数',
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100,
          },
        ],
        series: this.column
          .filter(item => item.subjectId)
          .map(subjectItem => {
            return {
              name: subjectItem.subjectName,
              type: 'line',
              data: this.examTable.map(item => item.data[subjectItem.subjectId].score),
              label: {
                show: true,
                position: 'top',
              },
            };
          }),
      });
    });
  }

  // 初始化排名趋势图表
  initExamRankChart() {
    if (this.examRankType != 'chart') return;

    if (this.examRankChart) {
      this.examRankChart.dispose();
      this.examRankChart = null;
    }

    this.$nextTick(() => {
      if (!this.$refs.examRankChart) return;
      this.examRankChart = this.$echarts.init(this.$refs.examRankChart);
      this.examRankChart.setOption({
        title: {
          left: 'center',
          text: '学生班排变化趋势',
        },
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          data: this.column.filter(item => item.subjectId).map(item => item.subjectName),
          orient: 'vertical',
          left: 'right',
          top: 'middle',
          itemGap: 20,
          type: 'scroll',
        },
        grid: {
          left: '3%',
          right: '6%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: this.examTable.map((item, index) => {
            let name = item.name;
            if (index == 0) {
              name = '起点测评：' + item.name;
            }
            return name;
          }),
          axisLabel: {
            width: 100,
            interval: 0,
            rotate: 30,
            overflow: 'truncate',
            ellipsis: '...',
          },
        },
        yAxis: {
          type: 'value',
          name: '名次',
          minInterval: 1,
          inverse: true,
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100,
          },
        ],
        series: this.column
          .filter(item => item.subjectId)
          .map(subjectItem => {
            return {
              name: subjectItem.subjectName,
              type: 'line',
              data: this.examTable.map(item => item.data[subjectItem.subjectId].clzRank),
              label: {
                show: true,
                position: 'top',
              },
            };
          }),
      });
    });
  }

  // 导出
  async handleExport() {
    let role = UserRole.examRolesTypes;
    if (UserRole.isOperation) {
      role = '';
    }
    let code = this.exportType === 'class' ? 1 : 2;
    let codeId = this.exportType === 'class' ? this.filterData.classId : this.filterData.stuId;
    let params: any = {
      code: code,
      codeId: codeId,
      role: role,
      v: this.v,
      token: getToken(),
    };

    this.exportLoading = true;

    await service('/pexam/_/exp-user-his-exam', {
      params: params,
      baseURL: process.env.VUE_APP_KKLURL,
      method: 'get',
    })
      .then(res => {
        let urlSearch = new URLSearchParams(params);
        let url = process.env.VUE_APP_KKLURL + `/pexam/_/exp-user-his-exam?${urlSearch.toString()}`;
        window.open(url);
        this.isShowExportDialog = false;
      })
      .catch(err => {})
      .finally(() => {
        this.exportLoading = false;
      });
  }

  // 获取班级列表
  async getClassList({ year, gradeId, subjectId }) {
    this.isLoading = true;
    this.classList = [];
    this.filterData.classId = '';
    let allGradeList = await UserRole.getGradeList();
    let grade = allGradeList.find(item => item.year == year && item.id == gradeId);
    try {
      let excludeRoles = [];
      if (!subjectId) {
        excludeRoles = [3, 4, 6];
      }
      this.classList = await UserRole.getClassList({
        subjectId: subjectId,
        systemCode: grade?.systemCode,
        year: grade?.year,
        classType: -1,
        excludeRoles: excludeRoles,
      });
    } catch (error) {
      console.error(error);
      this.classList = [];
    }
    this.isLoading = false;
  }

  // 更改班级
  async changeClass(val) {
    this.filterData.classId = val;
    this.filterData.realname = '';
    this.initFuncPermission();
    if (!this.isClsRankEnable) {
      this.examRankType = 'table';
    }
    await this.getStuList();
    await this.switchStu(this.stuList[0]);
  }
}
</script>

<style scoped lang="scss">
.red {
  color: #ff1744;
}

.stu-growth {
  padding: 20px;
  min-height: 100vh;
  font-size: 14px;

  .no-exam {
    text-align: center;
    padding: 100px 0;
    color: #666;
    font-size: 14px;
    background: #f5f6fa;

    span {
      color: #3370ff;
      cursor: pointer;
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.stu-growth-main {
  display: flex;
  margin-top: 20px;

  .stu-wrapper {
    position: sticky;
    top: 0;

    display: flex;
    flex-direction: column;

    width: 225px;
    min-width: 225px;
    height: calc(100vh - 80px);

    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 6px 0 rgba(32, 112, 254, 0.1);
    margin-right: 20px;

    .stu-class-select {
      padding: 10px;
      background: #f5f5f5;
      border-bottom: 1px solid #e7eaed;
      .stu-class-select__label {
        margin-right: 10px;
      }
      .stu-class-select__select {
        width: 120px;
      }
    }

    .stu-search {
      padding: 10px;
      background: #f5f5f5;
    }

    .stu-ul {
      flex: 1;
      padding: 10px 0;
      overflow: auto;

      .stu-li {
        padding: 0 28px;
        height: 55px;
        line-height: 55px;

        text-align: center;
        font-size: 16px;
        color: #3f4a54;

        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;

        cursor: pointer;

        &:hover {
          color: lighten(#2574ff, 0.2);
          background: lighten(#f0f5f9, 0.5);
        }

        &.active {
          color: #2574ff;
          background: #f0f5f9;
        }

        .stu-name {
          position: relative;

          &::after {
            content: '';
            display: block;
            position: absolute;
            bottom: 0;
            height: 1px;
            width: 100%;
            background-color: #e7eaed;
          }
        }
      }
    }
  }

  .score-wrapper {
    flex: 1;
    width: 0;

    .score-main {
      &:not(:first-child) {
        margin-top: 20px;
      }
    }

    .start-tag {
      margin-right: 5px;
    }

    .source-tag {
      margin-left: 5px;
    }

    .score-tip {
      font-size: 13px;
      color: #ff1744;
    }

    .score-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .score-header-left {
        display: flex;

        .sel-exam-btn {
          margin-left: 10px;
        }
      }

      .score-header-right {
      }

      .score-header-title {
        flex: 1;

        position: relative;

        display: flex;
        align-items: center;

        line-height: 32px;
        font-size: 16px;
        font-weight: bold;
        color: #3f4a54;

        &::before {
          content: '';
          width: 6px;
          height: 24px;
          background: #409eff;
          border-radius: 3px;
          margin-right: 10px;
        }
      }
    }

    .score-type-wrapper {
      margin-bottom: 10px;
      display: flex;
      justify-content: center;
    }

    .chart-wrapper {
      width: 100%;
      height: 500px;
    }
  }
}

.export-content {
  .radio-item {
    margin: 15px 0;

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>

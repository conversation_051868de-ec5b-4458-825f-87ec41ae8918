<template>
    <div class="classDetail">
        <div class="detail-header"><span
                @click="$router.push({path: '/home/<USER>/class'})"><i class="el-icon-arrow-left"></i> 返回班级学情</span></div>
        <div class="detail-body display_flex align-items_flex-start">
            <!--左侧知识点列表-->
            <div class="left-list display_flex flex-direction_column" :style="{height: listHeight + 'px'}">
                <div class="header__serarch clearfix display_flex">
                    <el-input class="search__text"
                              placeholder="请输入知识点名称"
                              v-model="searchValue"
                              @keyup.enter.native="searchReport"
                              clearable>
                    </el-input>
                    <div class="search__icon el-icon-search display_flex align-items_center justify-content_flex-center"
                         @click="searchReport"
                    ></div>
                </div>
                <ul class="pointList list-none flex_1">
                    <li v-for="(item, index) in pointList" :key="index"
                        :class="item.pointId === activePointId?'active':''"
                        :title="item.pointName"
                        @click="changePoint(item)">
                        <div class="li-inner display_flex justify-content_space-between">
                            <span class="text-ellipsis">{{item.pointName}}</span>
                            <span v-if="classId">{{Number(item.clsRate*100).toFixed(2)}}%</span>
                            <span v-else>{{Number(item.grdRate*100).toFixed(2)}}%</span>
                        </div>
                    </li>
                </ul>
                <el-pagination
                        small
                        layout="prev, pager, next"
                        style="margin:15px auto 0"
                        :hide-on-single-page="!pointList || !pointList.length"
                        class="text-center"
                        @current-change="handleCurrentChange"
                        :current-page.sync="leftPagination.page"
                        :page-size="leftPagination.limit"
                        :total="leftPagination.total_rows">
                </el-pagination>
            </div>
            <!--右侧-->
            <div class="right-content flex_1" :style="{minHeight: listHeight + 'px'}">
                <div class="right-header">
                    <span>{{ classId ? '学生' : '班级' }}掌握情况</span>
                    <ul class="list-none pull-right color-ul">
                        <li v-for="(item, index) in colorList" :key="index">{{item}}</li>
                    </ul>
                </div>
                <!--已关注学生-->
                <div class="attention-box" v-if="attentionList.length">
                    <div class="title">已关注 <span class="number">{{attentionList.length}}</span>人</div>
                    <el-row type="flex" :gutter="20" class="attention-list flex-wrap_wrap">
                        <el-col :span="8" v-for="(item, index) in attentionList" :key="index"
                                class="attention-col display_flex align-items_center">
                            <span class="stuname text-ellipsis text-left" :title="item.name">{{item.name}}</span>
                            <el-progress :color="getProcessColor(item.rate)" :stroke-width="8" class="flex_1"
                                         :percentage="Math.round(item.rate*100)"></el-progress>
                        </el-col>
                    </el-row>
                </div>
                <!--全部学生-->
                <div class="attention-box total" v-if="rangList.length">
                    <div class="title" v-if="classId">全部学生 <span class="number">{{totalLen}}</span>人</div>
                    <div class="title" v-else>全部班级 <span class="number">{{totalLen}}</span>个班</div>
                    <div class="total-box">
                        <ul class="total-header display_flex">
                            <li v-for="(item, index) in rangList" :key="index"
                                class="flex_1 display_flex align-items_center justify-content_space-between"
                                :class="activeRang === index?'active':''"
                                @click="changeRang(index)">
                                <span class="left-text">{{rangTabList[index]}}</span>
                                <span class="right-text">{{item.stuList.length}} <span v-if="classId">人</span><span v-else>个班</span></span>
                            </li>
                        </ul>
                        <div class="total-body">
                            <el-row type="flex" :gutter="20" class="attention-list flex-wrap_wrap"
                                    v-if="rangList[activeRang] && rangList[activeRang].stuList.length">
                                <el-col :span="8" v-for="(item, index) in curList" :key="index"
                                        class="attention-col display_flex align-items_center">
                                    <span class="stuname text-ellipsis text-center" :title="item.name">{{item.name}}</span>
                                    <el-progress :color="getProcessColor(item.rate)" class="flex_1"
                                                 :percentage="Math.round(item.rate*100)"></el-progress>
                                </el-col>
                            </el-row>
                            <!--分页器-->
                            <el-pagination
                                    background
                                    style="margin:15px auto"
                                    :hide-on-single-page="!curList.length || curList.length<=pagination.pageSize"
                                    class="text-center"
                                    layout="total, prev, pager, next"
                                    :current-page.sync="pagination.page"
                                    :page-size="pagination.pageSize"
                                    :total="pagination.total_rows">
                            </el-pagination>
                            <div class="nodata flex_1" v-if="!rangList[activeRang].stuList.length">
                                <img :src="noResImg" alt="">
                                <p class="text-center">暂无数据</p>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="right-header" style="margin:20px 0 10px">
                    <span>{{ classId ? '班级' : '年级' }}知识点错题</span>
                </div>
                <el-tabs v-model="activeName" @tab-click="handleClick">
                    <el-tab-pane :label="item.label" :name="item.name" v-for="item in mistackTabList" :key="item.name">
                        <!--知识点错题-->
                        <clsWrong v-on="$listeners" :pointId="activePointId"
                                  ref="clsWrongDetail"
                                  :classId="classId"
                                  v-if="item.name==='0'"></clsWrong>
                        <!--错题强化-->
                        <wrongIntensify v-on="$listeners"
                                :pointId="activePointId"
                                ref="wrongIntensify" v-else
                        ></wrongIntensify>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
    </div>
</template>

<script>
    import {listClsWeakPoint, getStuWeakPoint} from '@/service/pexam';
    import clsWrong from '@/components/paper/clsWrong';
    import wrongIntensify from '@/components/paper/wrongIntensify';

    export default {
        name      : 'class-detail',
        components: {
            clsWrong,
            wrongIntensify,
        },
        props: ['classId'],
        data () {
            return {
                mistackTabList       : [{label: '知识点错题', name: '0'}, {label: '强化推题', name: '1'}],
                activeName           : '0',
                noResImg             : require('@/assets/no-res.png'),
                // 左侧知识点列表
                pointList            : [],
                // 左侧列表默认高度
                listHeight           : 300,
                // 选中的知识点索引
                activePointId        : 0,
                // 知识点搜索值
                searchValue          : '',
                // 色值列表
                colorList            : ['0', '(0-0.6)', '[0.6-0.7)', '[0.7-0.9)', '[0.9-1]'],
                rangTabList          : ['0', '(0~0.6)', '[0.6~0.7)', '[0.7~0.9)', '[0.9~1]'],
                attentionList        : [],
                totalLen             : 0,
                rangList             : [],
                activeRang           : 1,
                // 知识点掌握分页配置
                pagination           : {
                    page      : 1,
                    pageSize  : 12,
                    total_rows: 0
                },
                // 分页
                leftPagination       : {
                    page      : 1,
                    pageSize  : 10,
                    total_rows: 0
                },
                wrongStrongPagination: {
                    page      : 1,
                    pageSize  : 10,
                    total_rows: 0
                },
                sortType             : '',
                quesInfo             : ''
            };
        },
        created () {

        },
        mounted () {
            
            console.log('classDetail-mounted=========>',);
            this.activePointId = this.$route.query.pointId;
            this.leftPagination = JSON.parse(this.$route.query.pagination);
            this.sortType = this.$route.query.sortType;
            this.listClsWeakPoint();
            this.$nextTick(() => {
                this.listHeight = document.getElementsByClassName('scrollContainer')[0].clientHeight - 130;
            });
        },
        computed  : {
            curList () {
                let stuList = this.$deepClone(this.rangList[this.activeRang].stuList),
                    startIndex = (this.pagination.page - 1) * this.pagination.pageSize,
                    endIndex = startIndex + this.pagination.pageSize;
                this.pagination.total_rows = stuList.length;
                endIndex = endIndex > stuList.length ? stuList.length : endIndex;
                return stuList.slice(startIndex, endIndex);
            }
        },
        methods   : {
            // 清空数据
            emptyData () {

            },

            handleClick (val) {
                this.$nextTick(() => {
                    //知识点错题
                    if (this.activeName === '0') {
                        this.$refs.clsWrongDetail[0].listErrQues();
                    } else {
                        // 错题强化
                        this.$refs.wrongIntensify && this.$refs.wrongIntensify[0].initTimeout();
                    }
                });
            },
            getProcessColor (val) {
                let color = '';
                val = Number(val);
                if (val >= 0 && val < 0.6) {
                    color = '#ff6a68';
                } else if (val >= 0.6 && val < 0.7) {
                    color = '#ffb400';
                } else if (val >= 0.7 && val < 0.9) {
                    color = '#409eff';
                } else {
                    color = '#07c29d';
                }
                return color;
            },
            // 获取薄弱知识点列表
            listClsWeakPoint (initIndex) {
                listClsWeakPoint({
                    page    : this.leftPagination.page,
                    pageSize: this.leftPagination.pageSize,
                    sort    : this.sortType,
                    q       : this.searchValue.trim(),
                    ...this.$listeners.getParams()
                }).then(res => {
                    this.pointList = res.data.list;
                    this.leftPagination.total_rows = res.data.total;
                    if (initIndex && this.pointList.length) {
                        this.activePointId = this.pointList[0].pointId;
                    }
                    this.getStuWeakPoint();
                }).catch(err => {
                    console.log('err=========>', err);
                    this.pointList = [];
                    this.leftPagination.total_rows = 0;
                });
            },
            // 学生知识点掌握情况
            getStuWeakPoint () {
                getStuWeakPoint({
                    pointId: this.activePointId,
                    teaId  : this.$sessionSave.get('loginInfo').id,
                    ...this.$listeners.getParams()
                }).then(res => {
                    this.attentionList = [];
                    this.totalLen = 0;
                    this.rangList = res.data;
                    res.data.length && res.data.forEach(item => {
                        this.totalLen += item.stuList.length;
                        item.stuList.length && item.stuList.forEach(subItem => {
                            if (subItem.isFollow) {
                                this.attentionList.push(subItem);
                            }
                        });
                    });
                }).catch(err => {
                    this.rangList = [];
                    this.attentionList = [];
                    this.totalLen = 0;
                });
                this.handleClick();
            },
            // 切换分页
            handleCurrentChange (val) {
                this.leftPagination.page = val;
                this.listClsWeakPoint('init');
            },
            // 搜索知识点
            searchReport () {
                this.leftPagination.page = 1;
                this.listClsWeakPoint('search');
            },
            // 切换等级显示学生
            changeRang (index) {
                this.activeRang = index;
            },
            // 切换知识点
            changePoint (item) {
                this.activePointId = item.pointId;
                this.getStuWeakPoint();
                this.$bus.$emit('changePoint', 'c30Point', {
                    ponitType    : 0,
                    knowledgeCode: item.pointId
                });
            }
        },
    };
</script>

<style lang="scss" scoped>
    .classDetail {
        font-family : Microsoft YaHei;
        .detail-header {
            font-size   : 16px;
            font-weight : bold;
            color       : #3f4a54;
            margin      : 4px 0 20px;
            > span {
                cursor : pointer;
            }
        }
        .detail-body {
            width : 100%;
            > div {
                background    : #fff;
                box-shadow    : 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
                border-radius : 6px;
            }
            .left-list {
                position     : -webkit-sticky;
                position     : sticky;
                top          : 0;
                width        : 320px;
                margin-right : 16px;
                padding      : 25px 0 20px;
                .header__serarch {
                    display : flex;
                    width   : 264px;
                    margin  : 0 auto;
                    .search__icon {
                        width         : 48px;
                        height        : 40px;
                        font-size     : 18px;
                        color         : #fff;
                        background    : #409eff;
                        border-radius : 0 3px 3px 0;
                        outline       : none;
                        cursor        : pointer;
                    }
                }
                .pointList {
                    margin-top : 10px;
                    width      : 100%;
                    overflow-y : auto;
                    overflow-x : hidden;
                    height     : 300px;
                    > li {
                        padding     : 0 28px;
                        cursor      : pointer;
                        height      : 70px;
                        line-height : 70px;
                        font-size   : 16px;
                        color       : #3f4a54;
                        &.active {
                            background : #e0efff;
                        }
                        &:last-child {
                            border : none;
                        }
                        .li-inner {
                            width         : 100%;
                            height        : 100%;
                            border-bottom : 1px solid #e7eaed;
                        }
                    }
                }
            }
            .right-content {
                //width         : 100%;
                padding       : 26px 35px 26px 40px;
                margin-bottom : 10px;
                .right-header {
                    margin-bottom : 40px;
                    > span {
                        position     : relative;
                        padding-left : 38px;
                        font-size    : 18px;
                        font-weight  : bold;
                        color        : #3f4a54;
                        &:before {
                            content    : '';
                            display    : inline-block;
                            width      : 30px;
                            height     : 19px;
                            background : url("../../assets/titleIcon.png") center center no-repeat;
                            position   : absolute;
                            left       : 0;
                            top        : 4px;
                        }
                    }
                    .color-ul {
                        font-size : 14px;
                        color     : #757c8c;
                        > li {
                            position     : relative;
                            padding-left : 17px;
                            display      : inline-block;
                            margin-right : 45px;
                            &:before {
                                content       : '';
                                position      : absolute;
                                left          : 0;
                                top           : 6px;
                                width         : 10px;
                                height        : 10px;
                                border-radius : 50%;
                            }
                            &:last-child {
                                margin-right : 0;
                            }
                            &:nth-child(1):after {
                                content  : '得分率';
                                position : absolute;
                                left     : -60px;
                                color    : #3f4a54;
                            }
                            &:nth-child(1):before {
                                background : #c6c6c6;
                            }
                            &:nth-child(2):before {
                                background : #ff6a68;
                            }
                            &:nth-child(3):before {
                                background : #ffb400;
                            }
                            &:nth-child(4):before {
                                background : #409eff;
                            }
                            &:nth-child(5):before {
                                background : #07c29d;
                            }
                        }
                    }
                }
                .attention-box {
                    .title {
                        font-size     : 16px;
                        font-weight   : bold;
                        color         : #3f4a54;
                        margin-bottom : 14px;
                        .number {
                            color       : #409eff;
                            margin-left : 10px;
                        }
                    }
                    .attention-list {
                        padding    : 22px;
                        background : #f5f7f9;
                        margin     : 0 0 0 !important;
                        .attention-col {
                            height : 44px;
                            .stuname {
                                width        : 80px;
                                font-size    : 16px;
                                color        : #3f4a54;
                                margin-right : 5px;
                            }
                        }
                    }
                    &.total {
                        margin-top : 25px;
                    }
                    .total-box {
                        border           : 1px solid #dde3e9;
                        background-color : #fff;
                        .total-header {
                            width      : 100%;
                            height     : 50px;
                            background : #f5f7f9;
                            > li {
                                position      : relative;
                                cursor        : pointer;
                                padding       : 0 22px 0 35px;
                                border-bottom : 1px solid #dde3e9;
                                border-right  : 1px solid #dde3e9;
                                font-size     : 14px;
                                &:last-child {
                                    border-right : none;
                                }
                                &.active {
                                    position         : relative;
                                    border-bottom    : none;
                                    background-color : #fff;
                                    border-top       : 4px solid #409eff;
                                }
                                &:before {
                                    content    : '';
                                    position   : absolute;
                                    left       : 17px;
                                    top        : 19px;
                                    width      : 12px;
                                    height     : 12px;
                                    background : #ff6a68;
                                }
                                &:nth-child(1):before {
                                    background : #c6c6c6;
                                }
                                &:nth-child(3):before {
                                    background : #ffb400;
                                }
                                &:nth-child(4):before {
                                    background : #409eff;
                                }
                                &:nth-child(5):before {
                                    background : #07c29d;
                                }
                                &:nth-child(6):before {
                                    background : #07c29d;
                                }
                                .left-text {
                                    color : #3f4a54
                                }
                                .right-text {
                                    color : #8a9096;
                                }
                            }
                        }
                        .total-body {
                            background-color : #fff;
                            padding          : 14px;
                        }
                    }
                }
            }
        }
    }

    html::-webkit-scrollbar {
        width  : 5px;
        height : 6px;
    }

    html::-webkit-scrollbar-track {
        background    : rgb(239, 239, 239);
        border-radius : 2px;
    }

    html::-webkit-scrollbar-thumb {
        background    : #bfbfbf;
        border-radius : 10px;
    }

    html::-webkit-scrollbar-thumb:hover {
        background : rgb(133, 133, 133);
    }

    html::-webkit-scrollbar-corner {
        background : #179a16;
    }

    div::-webkit-scrollbar {
        width  : 5px;
        height : 6px;
    }

    div::-webkit-scrollbar-track {
        background    : rgb(239, 239, 239);
        border-radius : 2px;
    }

    div::-webkit-scrollbar-thumb {
        background    : #bfbfbf;
        border-radius : 10px;
    }

    div::-webkit-scrollbar-thumb:hover {
        background : rgb(133, 133, 133);
    }

    div::-webkit-scrollbar-corner {
        background : #179a16;
    }
    .comment-ques {
        .ques-list {
            position      : relative;
            border        : 1px solid #e4e8eb;
            border-radius : 3px;
            margin-bottom : 20px;
            .title {
                padding-left : 50px;
                font-size    : 16px;
                font-weight  : bold;
                color        : #3f4a54;
                line-height  : 38px;

                .ques-type {
                    padding          : 4px 8px;
                    background-color : #c6c8c4;
                    color            : #fff;
                    margin-right     : 20px;
                    border-radius    : 4px;
                    font-size        : 15px;
                }
            }
            &:before {
                content     : attr(data-index);
                position    : absolute;
                left        : -1px;
                top         : -1px;
                width       : 32px;
                height      : 38px;
                background  : url("../../assets/flag_gray.png") center center no-repeat;
                font-size   : 16px;
                color       : #3f4a54ff;
                text-align  : center;
                line-height : 30px;
            }
            .ques-content {
                min-height : 100px;
                padding    : 15px 0 10px;
                line-height: 2em;
            }
            .edit-block {
                width         : 100%;
                height        : 48px;
                line-height   : 48px;
                padding       : 0 21px 0 11px;
                background    : #f5f8fa;
                border-radius : 3px;
                font-size     : 16px;
                color         : #4e5668ff;
                .edit-btn {
                    width         : 80px;
                    height        : 32px;
                    border-radius : 4px;
                    padding       : 0;
                    line-height   : 32px;
                    color         : #fff;
                    margin-top    : 8px !important;
                    margin-left   : 20px;
                    cursor        : pointer;
                    &.add {
                        background : #409effff;
                    }
                    &.hasAdd {
                        background-color : #fff;
                        border           : 1px solid #468fffff;
                        color            : #468fffff;
                    }
                }
                .ques-btn {
                    position      : relative;
                    padding-right : 20px;
                    cursor        : pointer;
                    i {
                        font-size   : 22px;
                        margin-left : 2px;
                        position    : absolute;
                        top         : 9px;
                    }
                    &.active {
                        color     : #409effff;
                        font-size : 16px;
                        i {
                            top : 13px;
                        }
                    }
                    .el-icon-caret-top {
                        color : #409effff;
                    }
                }
                .el-icon-caret-bottom {
                    color      : #b6b8bfff;
                    margin-top : 4px;
                }
            }
            .ques-detail {
                padding : 20px;
                > div {
                    margin-bottom : 10px;
                }
                .resourceList {
                    margin-top    : 10px;
                    margin-bottom : 0;
                }
                .answer-imgList {
                    display       : inline-block;
                    width         : 85px;
                    height        : 50px;
                    margin-right  : 10px;
                    cursor        : pointer;
                    margin-bottom : 10px;
                    border        : 1px solid #cecece
                }
            }
            &.active, &:hover {
                border : 1px solid #409eff;
                &:before {
                    background : url("../../assets/flag_active.png") center center no-repeat;
                    color      : #fff;
                }
            }
        }
    }
</style>
<style lang="scss">
    .search__text {
        .el-input__inner {
            border-right  : none;
            border-radius : 3px 0 0 3px;
        }
    }


</style>

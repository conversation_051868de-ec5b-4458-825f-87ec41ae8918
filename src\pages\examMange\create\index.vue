<template>
  <div class="examinfo-container">
    <bread-crumbs :title="title">
      <template slot="titleSlot">
        <el-popover placement="right" width="380" trigger="hover">
          <p>1.若本场考试包含多个学科，请一次性选择这些学科</p>
          <p>2.已关联个册的学科不支持取消，未关联的支持取消</p>
          <i class="el-icon-warning" slot="reference"></i>
        </el-popover>
      </template>
    </bread-crumbs>
    <div class="exam-add-box">
      <el-form :model="submitData">
        <el-form-item label="考试名称" :label-width="formLabelWidth">
          <el-input v-model="submitData.name" autocomplete="off" maxlength="500" style="width: 98%"></el-input>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="参考年级" :label-width="formLabelWidth" style="margin-bottom: 5px">
              <el-select v-model="submitData.gradeId" placeholder="请选择年级" @focus="gradeFocus" @change="changeGrade"
                style="width: 98%">
                <el-option v-for="item in dict.grdList" :key="item.id" :label="item.name" :value="item.id" :disabled="item.disabled">
                </el-option>
              </el-select> </el-form-item></el-col>
          <el-col :span="12">
            <el-form-item label="考试类别" :label-width="formLabelWidth">
              <el-select v-model="submitData.category" placeholder="请选择类别" style="width: 98%">
                <el-option v-for="item in dict.typeList" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select> </el-form-item></el-col>
        </el-row>
        <el-form-item label="考试时间" :label-width="formLabelWidth">
          <el-date-picker popper-class="datePicker__time" v-model="examTime" value-format="yyyy-MM-dd" format="yyyy-MM-dd"
            type="date" @change="changeData" :picker-options="pickerOptions" placeholder="选择日期">
          </el-date-picker>

          <el-select v-model="submitData.schoolYearId" placeholder="请选择日期" :disabled="true" style="margin-left: 6px">
            <el-option v-for="item in dict.years" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>

          <!-- <el-select v-model="submitData.schoolTermId" placeholder="请选择日期" :disabled="true" style="margin-left: 6px">
            <el-option v-for="item in dict.terms" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select> -->
        </el-form-item>

        <el-form-item label="考试学科" :label-width="formLabelWidth" style="margin-bottom: 5px">
          <el-checkbox v-model="checkAll" @change="handleCheckAllChange" v-if="!isPhotoCorrect">全选</el-checkbox>
          <span class="check-tip" v-if="!isPhotoCorrect">若本场考试包含多个学科，请一次性选择这些学科</span>
          <el-radio-group class="subjectCheckBox" :disabled="editObj.examId && editObj.examId != ''"
            v-model="checkSubjects" @change="handleCheckedSubjectChange" v-if="isPhotoCorrect">
            <template v-for="sub in dict.subjectList">
              <el-radio :label="sub.id" :key="sub.id" v-if="hasMap[sub.id]" :disabled="relatedSubids.indexOf(sub.id) != -1 || processSubids.indexOf(sub.id) != -1 || disabledSubjectList.indexOf(sub.id) != -1
                " style="position: relative">
                <div class="dialog-gece-tip" v-if="relatedSubids.indexOf(sub.id) != -1">个</div>
                {{ sub.name }}
              </el-radio>
            </template>
          </el-radio-group>

          <el-checkbox-group class="subjectCheckBox" v-model="checkSubjects" @change="handleCheckedSubjectChange" v-else>
            <template v-for="sub in dict.subjectList">
              <el-checkbox :label="sub.id" :key="sub.id" v-if="hasMap[sub.id]" :disabled="relatedSubids.indexOf(sub.id) != -1 || processSubids.indexOf(sub.id) != -1 || disabledSubjectList.indexOf(sub.id) != -1
                " style="position: relative">
                <div class="dialog-gece-tip" v-if="relatedSubids.indexOf(sub.id) != -1">个</div>
                {{ sub.name }}
              </el-checkbox>
            </template>
          </el-checkbox-group>
        </el-form-item>

        <template v-if="checkSubjects.length >= 2">
          <el-form-item label="" :label-width="formLabelWidth" style="margin-bottom: 5px">
            <el-button type="text" @click="openAddSubjectDialog">添加综合学科
              <el-popover placement="right" width="380" trigger="hover" style="color: #606266">
                <p>添加综合学科可实现综合学科的成绩分析，可设置一张答题卡或多张答题卡。</p>
                <i class="el-icon-warning" slot="reference"></i>
              </el-popover>
            </el-button>
            <div class="comper-subjects" v-if="selComperSubject.length != 0">
              <div class="comper-subjects-container">
                <span>已添加：</span>
                <div class="comper-subjects-item" v-for="(item, index) in selComperSubject" :key="index">
                  {{ item.showNames + '（' + item.subNames + '）'
                  }}<i v-if="!item.isUseCard" class="el-icon-close" @click.stop="cancelSubject(item)"></i>
                </div>
              </div>
            </div>
          </el-form-item>
        </template>
        <el-form-item v-if="newCollegeExam" label="分析模式" :label-width="formLabelWidth" style="margin-bottom: 5px">
          <el-radio-group v-model="submitData.analysisMode" :disabled="editObj.examId && editObj.examId != ''">
            <el-radio :label="0">普通</el-radio>
            <el-radio :label="1" v-if="[10, 11, 12, '10', '11', '12'].includes(submitData.gradeId)">新高考</el-radio>
            <el-select size="mini" :disabled="editObj.examId && editObj.examId != ''" v-model="submitData.scoreRuleId"
              placeholder="请选择新高考模式" @change="changeScoreRuleId" v-if="submitData.analysisMode == 1">
              <el-option v-for="item in scoreRuleList" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="阅卷方式" :label-width="formLabelWidth" style="margin-bottom: 5px">
          <el-radio-group v-model="submitData.source" @input="changeSource"
            :disabled="editObj.examId && editObj.examId != ''">
            <el-radio :label="4">网阅（先扫后阅）</el-radio>
            <el-radio :label="3">手阅（先阅后扫）</el-radio>
            <el-radio :label="6" v-if="editObj">拍改（手机拍照）</el-radio>
            <el-radio :label="1" :disabled="selComperSubject.find(item => item.useCardType == 0)">补录（导入成绩）
              <el-popover placement="right" width="380" trigger="hover">
                <p>当不使用该阅卷系统阅卷、但又想导入学生成绩时，可选择此模式，无需扫描及阅卷。</p>
                <i class="el-icon-warning" slot="reference"></i>
              </el-popover>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="submitData.source == 3 || submitData.source == 4">
          <el-form-item label="班级类型" :label-width="formLabelWidth" style="margin-bottom: 5px"> 
            <el-radio-group v-model="classType" @input="getClassList" :disabled="!!editObj.classIds">
              <el-radio :label="1">行政班</el-radio>
              <el-radio :label="3">分层班</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="参考班级" :label-width="formLabelWidth" style="margin-bottom: 5px" v-if="classType">
            <div v-if="classData.length > 0">
              <!-- <div v-if="classData.length > 1">若本场考试包含多个班级，请一次性选择这些班级</div> -->
              <template v-for="(area) in classData">
                <div :class="{ 'area-checkbox': classData.length > 1 }">
                  <el-checkbox v-model="area.checkAllClass" :disabled="!!editObj.classIds"
                    @change="handleCheckAllClass(area)">{{ classData.length == 1 ? '全部' : area.campusName }}</el-checkbox>
                  <el-checkbox-group :disabled="!!editObj.classIds" v-if="classTags.length > 0" class="class-tag-checkbox"
                    v-model="checkClassTags">
                    <el-checkbox v-for="tag in classTags" @change="handleCheckTagClass(area,tag)" :label="area.campusCode + '_' + tag.id" :key="tag.id" style="position: relative">
                      {{ tag.title }}
                    </el-checkbox>
                  </el-checkbox-group>
                  <span v-else class="check-tip">若本场考试包含多个班级，请一次性选择这些班级</span>
                </div>
                <el-checkbox-group :disabled="!!editObj.classIds" class="subjectCheckBox"
                  v-model="area.checkClasses" @change="handleCheckedClass(area)">
                  <el-checkbox v-for="item in area.classList" :label="item.id" :key="item.id" style="position: relative">
                    {{ item.grade_name }} {{ item.class_name }}
                  </el-checkbox>
                </el-checkbox-group>
              </template>

            </div>
            <span v-else>暂无班级</span>
          </el-form-item>
        </template>
        <!-- <el-form-item label="发布成绩" :label-width="formLabelWidth">
          <el-radio-group v-model="submitData.isPublish">
            <el-radio :label="0">否</el-radio>
            <el-radio :label="1">是</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="成绩确认" :label-width="formLabelWidth">
          <el-radio-group v-model="submitData.scoreConfirm" :disabled="editObj.examId && editObj.examId != ''">
            <el-radio :label="0">学生可查看，无需确认</el-radio>
            <el-radio :label="1">需学生确认</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button @click="cancelClick">取 消</el-button>
          <el-button type="primary" :loading="isComfirming" @click="addReport">保 存</el-button>
        </el-form-item>
      </el-form>
    </div>
    <add-subject-dialog :modalVisible="isShowAddSubject" :checkSubjectIds="checkSubjects" :subjectList="dict.subjectList"
      :selComperSubject="selComperSubject" :disabledSubjectList="disabledSubjectList" :source="submitData.source"
      @confirm-add-dialog="confirmAddSubject" @close-add-dialog="closeAddSubject">
    </add-subject-dialog>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import BreadCrumbs from '@/components/Breadcrumbs/index.vue';
import { classList, getSchoolTagList } from '@/service/api';
import { createdExam,getExamScoreRuleList } from '@/service/pexam';
import AddSubjectDialog from './modules/addSubjectDialog.vue';
import { localSaveUnique } from '@/utils/index';

let date = new Date(),
  y = Number(date.getFullYear().toString().substr(2)),
  M = date.getMonth(),
  d = date.getDate();

let min = new Date(`${date.getFullYear() - 5}-08-09`);
let max = new Date(`${date.getFullYear()}-08-09`);
let md = Number(`${M}${d < 10 ? `0${d}` : d}`);
if (md >= 710) {
  min = new Date(`${date.getFullYear() + 1 - 5}-08-09`);
  max = new Date(`${date.getFullYear() + 1}-08-09`);
}
export default {
  name: 'answer-setting-index',
  data() {
    return {
      title: '新建',
      hasMap: {},
      // 提交按钮是否在加载中
      isComfirming: false,
      // 考试时间
      examTime: '',
      // 考试时间选择器的配置项
      pickerOptions: {
        // 限制日期
        disabledDate: time => {
          return time.getTime() < min.getTime() || time.getTime() > max.getTime();
        },
      },
      // 提交数据
      submitData: {
        name: '',
        gradeId: '',
        subjectIds: '',
        category: '',
        type: '2',
        // 选择的具体时间数值 - 默认本周
        examTime: '',
        schoolId: '',
        schoolYearId: '',
        // schoolTermId: '',
        scoreConfirm: 0, //是否需确认成绩
        // isPublish: 1, //是否发布成绩
        source: '-1', //来源
        clzType: '', //班级类型
        classIds: '',
        classNames: '',
        analysisMode: 0, // 0:普通 1:新高考
        scoreRuleId: '', //赋分规则ID
        comprehensiveSubjectIds: '', //综合学科id
        comprehensiveSubjectNames: '', //综合学科name
        comprehensiveSubjectRealNames: '',
        comprehensiveSubjectCardSet: '',
      },
      scoreRuleList: [], //赋分规则列表
      newCollegeExam:false,
      // form项宽度
      formLabelWidth: '80px',
      // 学科是否全选
      checkAll: false,
      checkSubjects: [],
      // 上次选择的年级
      historyGrade: '',
      // 是否选择考试时间选择器
      showTimePicker: true,
      // 已关联个册的学科
      relatedSubids: [],
      //发送加工的学科
      processSubids: [],
      //试卷基本信息
      editObj: {},
      //字典数据
      dict: {},
      classType: 0,
      //班级列表
      classData: [],
      classHasMap: {},
      //年级入学年份
      gradeYear: '',
      //角色类型
      accountType: '',
      //是否显示添加学科弹窗
      isShowAddSubject: false,
      //已选择的综合学科
      selComperSubject: [],
      disabledSubjectList: [],
      //班级标签
      classTags:[],
      //已选择的班级标签
      checkClassTags:[]
    };
  },
  components: {
    BreadCrumbs,
    AddSubjectDialog,
  },
  computed: {
    ...mapGetters([]),
    timeSlotFormat() {
      return this.examTime ? this.$formatDate(new Date(this.examTime)) : '';
    },
    // 拍改模式
    isPhotoCorrect() {
      return this.submitData.source == 6;
    }
  },
  created() {
    this.submitData.scoreRuleId = localSaveUnique.get('createExamScoreRuleId') || '';
    this.getExamRuleList();
  },
  async mounted() {
    this.initData();
  },
  methods: {
    async initData() {
      let loginInfo = this.$sessionSave.get('loginInfo');
      this.accountType = loginInfo.account_type;
      this.hasMap = {};
      this.submitData.examTime = this.timeSlotFormat;
      this.submitData.schoolId = this.$sessionSave.get('schoolInfo').id;
      this.newCollegeExam = this.$sessionSave.get('newExamRules') == '1';
      this.editObj = this.$sessionSave.get('examInfo');
      let grd = '';
      this.dict = this.$sessionSave.get('examDict');
      this.pickerOptions.disabledDate = (time) => {
        return time.getTime() < new Date(this.dict.years[this.dict.years.length-1].startTime).getTime() || time.getTime() > new Date(this.dict.years[0].endTime).getTime();
      };
      // 若为编辑个册
      if (this.editObj) {
        //有综合学科
        if (this.editObj.comprehensiveSubjectIds != '') {
          let ids = this.editObj.comprehensiveSubjectIds.split(',');
          let names = this.editObj.comprehensiveSubjectNames.split(',');
          let subNames = this.editObj.comprehensiveSubjectRealNames.split(',');
          let useCardType = this.editObj.comprehensiveSubjectCardSet.split(',');
          let comperSubjects = [];
          for (let i = 0; i < ids.length; i++) {
            let paperInfo = this.editObj.paperList.filter((card) => {
              return card.subectId == ids[i]
            });
            comperSubjects.push({
              ids: ids[i].replace(/-/g, ','),
              showIds: ids[i],
              showNames: names[i],
              subNames: subNames[i],
              useCardType: useCardType[i],
              isUseCard: paperInfo.length && (paperInfo[0].progressList[0]?.progress == 1 && paperInfo[0].progressList[0].progressState == 1)
            });
          }
          this.selComperSubject = comperSubjects;
        }
        this.title = '编辑考试';
        this.submitData.examId = this.editObj.examId;
        this.submitData.name = this.editObj.examName;
        //备课组长权限获取的年级id为string
        this.submitData.gradeId = Number(this.editObj.gradeCode) || '';
        grd = this.dict.grdList.find(q => q.id == this.submitData.gradeId);

        this.submitData.category = Number(this.editObj.categoryCode);
        this.submitData.examTime = this.editObj.examDateTime;
        this.examTime = this.editObj.examDateTime;
        this.submitData.schoolYearId = this.editObj.schoolYearId;
        // this.submitData.schoolTermId = Number(this.editObj.schoolTermId);
        this.submitData.subjectIds = this.editObj.subjectId;
        this.submitData.source = this.editObj.source;
        this.submitData.classIds = this.editObj.classIds;
        this.submitData.classNames = this.editObj.classNames;
        this.submitData.clzType = this.editObj.clzType;
        this.submitData.analysisMode = this.editObj.analysisMode;
        this.submitData.scoreRuleId = this.editObj.scoreRuleId;
        this.gradeYear = this.editObj.year;
        this.classType = Number(this.submitData.clzType);
        this.editObj.paperList.forEach(item => {
          //已关联个册的学科
          if (item.isExamUse == 0) {
            this.relatedSubids.push(...item.subectId.split('-'));
          }
          //发送加工的学科
          if (item.processState > 0) {
            this.processSubids.push(...item.subectId.split('-'));
          }
          //已经关联答题卡的学科
          if (item.progressList[0]?.progress == 1 && item.progressList[0]?.progressState == 1) {
            this.disabledSubjectList.push(...item.subectId.split('-'));
          }
          // 补录已发布的学科
          if (item.source != 3 && item.source != 4 && item.importScoreState == 1) {
            this.disabledSubjectList.push(...item.subectId.split('-'));
          }
        });

        this.submitData.scoreConfirm = this.editObj.scoreConfirm;
        // this.submitData.isPublish = this.editObj.isPublish;
      }

      this.dict.subjectList.forEach(v => {
        // if (this.editObj) {
        if (grd) {
          if (v.phaseId == grd.phaseId || v.phaseId == grd.phase + 2) {
            this.hasMap[v.id] = true;
          } else {
            this.hasMap[v.id] = false;
          }
        } else {
          this.hasMap[v.id] = true;
        }
        // }
        // else {
        //   this.hasMap[v.id] = true;
        // }
      });
      if (this.editObj && this.editObj.subjectId) {
        let subjectIds = this.editObj.subjectId.split(',');
        // 显示单科和多科的已选择学科
        let subCount = subjectIds.length;
        this.submitData.type = subCount > 1 ? '2' : '1';
        let subjectList = this.dict.subjectList.filter(q => this.hasMap[q.id]);
        this.checkAll = subCount == subjectList.length;
        let ids = [];
        subjectIds.forEach(v => {
          ids.push(v);
        });
        this.checkSubjects = ids;
      }
      if (this.editObj && this.submitData.clzType) {
        await this.getClassList();
      }
      this.getClassTags();
    },
    async getExamRuleList() {
      //获取新高考模式类型
      let res = await getExamScoreRuleList();
      this.scoreRuleList = res.data; 
    },
    openAddSubjectDialog() {
      this.isShowAddSubject = true;
    },
    /**
     * @name:确定添加综合学科
     */
    confirmAddSubject(data) {
      this.selComperSubject = JSON.parse(JSON.stringify(data));
      if (this.submitData.source == 1) {
        //如果为补录模式添加综合学科共用一张卡，则修改为网阅
        let mergeComperSubject = this.selComperSubject.find(item => {
          return item.useCardType == 0
        })
        if (mergeComperSubject) {
          this.submitData.source = 4
          this.$message.warning('补录模式下不支持综合学科公用一张卡，已自动切换为网阅！');
        }
      }

      this.isShowAddSubject = false;
    },
    closeAddSubject() {
      this.isShowAddSubject = false;
    },
    /**
     * @name:取消综合学科选择
     */
    cancelSubject(item) {
      this.selComperSubject = this.selComperSubject.filter(ite => {
        return item.ids != ite.ids;
      });
    },
    /**
     * @name：初始化页面参数
     */
    init() {
      let data = this.$route.query;
      this.examId = data.examId || '';
      this.examName = data.examName || '';
      this.subjectName = data.subjectName || '';
      this.gradeCode = data.gradeCode || '';
    },
    // 考试时间筛选项的禁止范围
    disabledDate(time) {
      let date = new Date(),
        y = Number(date.getFullYear().toString().substr(2)),
        M = date.getMonth(),
        d = date.getDate();

      let min = new Date(`${date.getFullYear() - 5}-08-10`);
      let max = new Date(`${date.getFullYear()}-08-10`);
      let md = Number(`${M}${d < 10 ? `0${d}` : d}`);
      if (md >= 710) {
        min = new Date(`${date.getFullYear() + 1 - 5}-08-10`);
        max = new Date(`${date.getFullYear() + 1}-08-10`);
      }
      return time.getTime() < max.getTime() || time.getTime() > min.getTime();
    },
    // 切换考试时间
    changeData(val) {
      let time = this.timeSlotFormat;
      this.submitData.examTime = time;
      if (!time) {
        this.submitData.schoolYearId = '';
        // this.submitData.schoolTermId = '';
        return;
      }
      this.dict.years.forEach(item => {
        if (new Date(time) >= new Date(item.startTime) && new Date(time) <= new Date(item.endTime)) {
          this.submitData.schoolYearId = item.id;
        }
      });

      // let date = new Date(time),
      //   y = Number(date.getFullYear().toString().substr(2)),
      //   M = date.getMonth(),
      //   d = date.getDate();
      // // 判断学期时间范围-2021到2022年上学期[2021-8-10，2022-2-15], 下学期[2022-2-16，2022-8-9]
      // let md = Number(`${M}${d < 10 ? `0${d}` : d}`);
      // if (md >= 710) {
      //   this.submitData.schoolYearId = `${y}${y + 1}`;

      //   this.submitData.schoolTermId = 110;
      // } else {
      //   this.submitData.schoolYearId = `${y - 1}${y}`;
      //   if (md <= 115) {
      //     this.submitData.schoolTermId = 110;
      //   } else {
      //     this.submitData.schoolTermId = 111;
      //   }
      // }
    },
    // 全选
    handleCheckAllChange(val) {
      let ids = this.dict.subjectList.filter(q => this.hasMap[q.id]).map(q => q.id);
      this.checkSubjects = val ? ids : this.relatedSubids.concat(...this.processSubids, ...this.disabledSubjectList);
    },
    // 选择学科
    handleCheckedSubjectChange(value) {
      this.checkAll =
        this.checkSubjects.length == this.dict.subjectList.filter(q => this.hasMap[q.id]).length;
      //综合学科应包含于所选的考试学科
      this.selComperSubject = this.selComperSubject.filter(item => {
        let arr = item.ids.split(',');
        return arr.every(ite => this.checkSubjects.includes(ite));
      });
    },
    // 年级下拉框获取焦点事件
    gradeFocus(val) {
      this.historyGrade = this.submitData.gradeId;
    },
    // 切换年级，联动显示学科
    changeGrade(data) {
      this.checkAll = false;
      if(!this.editObj){
        //非编辑模式，清空学科
        this.checkSubjects = [];
      }
      let grd = this.dict.grdList.find(q => q.id == data);
      this.dict.subjectList.forEach(v => {
        this.hasMap[v.id] = false;
        if (v.id == '' || v.phaseId == grd.phaseId || v.phaseId == grd.phase + 2) {
          this.hasMap[v.id] = true;
        }
      });
      this.gradeYear = grd.year;
      if ((this.submitData.source == 3 || this.submitData.source == 4) && this.classType != 0) {
        this.getClassList();
      }
      //更换年级时，清空班级
      this.editObj.classIds = '';
      this.submitData.classIds = '';
    },
    // 创建，编辑考试
    addReport() {
      // 名称校验
      if (!this.submitData.name) {
        this.$message.error('考试名称不能为空！');
        return;
      }
      // 年级校验
      if (!this.submitData.gradeId) {
        this.$message.error('考试年级不能为空！');
        return;
      }
      // 学科校验
      if (this.checkSubjects.length == 0) {
        this.$message.error('考试学科不能为空！');
        return;
      }
      // 类别校验
      if (!this.submitData.category) {
        this.$message.error('考试类别不能为空！');
        return;
      }

      if (!this.submitData.examTime) {
        this.$message.error('考试时间不能为空！');
        return;
      }
      if (this.submitData.source == -1) {
        this.$message.error('请选择阅卷方式！');
        return;
      }
      if (this.submitData.analysisMode == 1 && !this.submitData.scoreRuleId) {
        this.$message.error('请选择新高考模式！');
        return;
      }

      //不是新高考
      if (this.submitData.analysisMode == 0) {
        this.submitData.scoreRuleId = '';
      }

      let grd = this.dict.grdList.find(q => q.id == this.submitData.gradeId);
      let ids = [];
      this.dict.subjectList.forEach(v => {
        if (
          (v.phaseId == grd.phaseId || v.phaseId == grd.phase + 2) &&
          this.checkSubjects.find(q => q == v.id)
        ) {
          ids.push(v.id);
        }
      });
      this.submitData.subjectIds = ids.join(',');
      if (this.submitData.source == 4 && this.classType == 0) {
        this.$message.error('请选择班级类型！');
        return;
      }
      // 综合学科
      this.submitData.comprehensiveSubjectIds = this.selComperSubject
        ?.map(item => {
          return item.showIds;
        })
        .join(',');
      this.submitData.comprehensiveSubjectNames = this.selComperSubject
        ?.map(item => {
          return item.showNames;
        })
        .join(',');
      this.submitData.comprehensiveSubjectRealNames = this.selComperSubject
        ?.map(item => {
          return item.subNames;
        })
        .join(',');
      this.submitData.comprehensiveSubjectCardSet = this.selComperSubject
        ?.map(item => {
          return item.useCardType;
        })
        .join(',');
      // 处理班级数据
      let classNames = [],
        classIds = [];
      this.classData.forEach(area => {
        area.classList.forEach(clz => {
          if (area.checkClasses.find(q => q == clz.id)) {
            classIds.push(clz.id);
            classNames.push(clz.grade_name + clz.class_name);
          }
        })
      })

      //当为扫描仪作业时必现选择班级
      this.submitData.year = this.gradeYear;
      if (this.submitData.source == 3 || this.submitData.source == 4) {
        if (classIds.length == 0) {
          this.$message.error('参考班级不能为空！');
          return;
        } else {

          this.submitData.classIds = classIds.join(',');
          this.submitData.classNames = classNames.join(',');
          this.submitData.clzType = this.classType;
          this.submitData.year = this.gradeYear;
        }
      }
      this.createReport();
    },
    // saveBankJSONAPI({
    //     darftId: "",
    //     state: 0, //0 定时 1:已发送 2：草稿箱[默认] 来源
    //     userId: this.$sessionSave.get("loginInfo").id,
    //     creatType: 1, // creat_type表示的是创建什么样的作业,是普通作业还是备课组长作业,云考试全为1
    //     sendSource: 2, // PC
    //     correctType: 0, //学生作业互看状态（0:允许互看优秀作答，1:允许互看 2:不允许互看）
    //     permissionType: 1, //学生作业互看状态（0:允许互看优秀作答，1:允许互看 2:不允许互看）
    //     autoSubmit: 0,
    //     firstType: 3,
    //     subjectId: subjectItem.subectId,
    //     workJson: workJson,
    //     sourceId: subjectItem.personBookId,
    //     paperNo: subjectItem.paperNo,
    //     paperNum: paperNum,
    //     relationId: this.item.examId,
    //   });
    createReport() {
      // 编辑考试报告
      console.log(this.submitData);
      // return;
      this.isComfirming = true;
      createdExam(this.submitData)
        .then(data => {
          this.$message({
            message: `考试${this.editObj ? '编辑' : '创建'}成功！`,
            type: 'success',
            duration: 1000,
          });
          // this.$emit("closeDialog");
          // this.$emit("updateData");
          this.isComfirming = false;
          this.$router.back(-1);
        })
        .catch(err => {
          this.isComfirming = false;
        });
    },
    /**
     * @name:切换班级类型
     */
    changeSource() {
      this.classType = 0;
    },

    // 切换新高考模式
    changeScoreRuleId(val) {
      localSaveUnique.set('createExamScoreRuleId', val);
    },
    groupByMap(array) {
      let choiceIds = this.submitData.classIds.split(',');
      let schoolAreaList = [];
      array.forEach(item => {
        let area = schoolAreaList.find(sch => { return sch.campusCode == item.campusCode });
        if (!area) {
          area = {
            campusCode: item.campusCode,
            campusName: item.campusName,
            classList: [],
            checkClasses: [],
            checkAllClass: false
          }
          schoolAreaList.push(area)
        }
        area.classList.push(item);
        if (choiceIds.length == 0 || choiceIds.includes(item.id)) {
          area.checkClasses.push(item.id);
        }
        if (area.checkClasses.length == area.classList.length) {
          area.checkAllClass = true;
        }
      });
      return schoolAreaList;
    },

    async getClassTags() {
      let params = {
        type: 0,
        schoolId: this.$sessionSave.get('schoolInfo').id,
        page:1,
        limit:50
      };
      let result = await getSchoolTagList(params);
      if (result && result.code == 1) {
        this.classTags = result.data.rows;
      }
    },

    /**
     * @name:根据年级和班级类型获取班级列表
     */
    async getClassList() {
      this.checkClassTags = [];
      let grade = this.dict.grdList.find(q => q.id == this.submitData.gradeId);
      if (grade) {
        let phase = grade.phaseId ? grade.phaseId - 2 : grade.phase;
        this.gradeYear = grade.year;
        let result = await classList(grade.year, phase, this.classType);
        if (result && result.code == 1) {
          this.classData = this.groupByMap(result.data.rows, 'campusCode');
        }
      } else {
        this.$message.error('请先选择年级！');
        return;
      }
    },
    /**
     * @name:根据年级id获取年级数据
     */
    getGradeById() {
      return this.dict.grdList.filter(item => {
        item.id == this.submitData.gradeId;
      })[0];
    },
    // 全选班级
    handleCheckAllClass(area) {
      if (area.checkAllClass) {
        let ids = area.classList.map(q => q.id);
        area.checkClasses = ids;
      } else {
        area.checkClasses = [];
      }
    },
    handleCheckTagClass(area,tag) {
      let ids = area.classList.filter(q => q.tagId == tag.id).map(q => q.id);
      if(this.checkClassTags.includes(area.campusCode + '_' + tag.id)){
        area.checkClasses = [...new Set(area.checkClasses.concat(ids))];
      }else{
        area.checkClasses = area.checkClasses.filter(q => !ids.includes(q));
      }
    },
    // 选择班级
    handleCheckedClass(area) {
      area.checkAllClass = area.checkClasses.length == area.classList.length;
    },
    /**
     * @name:取消
     */
    cancelClick() {
      this.$router.back(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.exam-title {
  font-size: 18px;
  font-weight: bold;
  margin-right: 20px;
}

.check-tip {
  margin-left: 20px;
}

.exam-add-box {
  background: #fff;
  padding: 20px;
  min-height: calc(100% - 40px);
  border-radius: 2px;

  .el-form {
    .area-checkbox {
      background: #dedede9c;
      border-radius: 5px;
      padding: 0 10px;
      width: 100%;
      .class-tag-checkbox{
        display: inline-block;
        margin-left: 15px;
      }
    }
  }
}

.examinfo-container {
  padding: 0 0 20px 0 !important;
}

.comper-subjects-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;

  .comper-subjects-item {
    height: 30px;
    line-height: 30px;
    border-radius: 15px;
    padding: 0 4px 0 8px;
    margin: 10px;
    background: #f5faff;
    cursor: pointer;

    &:hover {
      color: #409eff;
    }
  }
}

.dialog-gece-tip {
  position: absolute;
  top: -2px;
  left: -20px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: red;
  font-size: 12px;
  color: #fff;
  text-align: center;
}
</style>
<style lang="scss">
.examinfo-container {
  .el-checkbox+.el-checkbox {
    margin-left: unset;
  }

  // .header-bc {
  //     margin-bottom: unset !important;
  // }

  .exam-add-box {
    .el-form {
      width: 1000px;
    }
  }
}
</style>

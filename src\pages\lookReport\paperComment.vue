<template>
  <div class="paperComment">
    <!--头部班级和学科-->
    <headerFilter @updateFilter="updateFilter" @getSubjectList="getSubjectList" ref="headerFilter" class="header-filter"
      v-if="showHeader" />
    <div class="router-view">
      <div class="comment-container display_flex align-items_flex-start">
        <!--题目列表-->
        <div class="comment-ques flex_1" v-loading="listLoading" element-loading-text="讲评数据加载中..."
          style="min-height: 500px;">
          <div class="ques-list" v-for="(item, index) in quesList" :id="item.quesId || item.qId || item.tQuesNo"
            :key="`${item.quesName}_${item.level}_${index}` || index" :data-index="item.totalIndex"
            @click="selectQuesItem(item)" :class="{
              active: item.quesId ? item.quesId === detailId : item.totalIndex === detailId,
              'empty-ques': isEmptyPaperType,
            }">
            <div class="ques-tag" v-if="item.quesName">{{ item.quesName }}</div>

            <div v-if="item.smartData" class="ques-smart">
              <div class="ques-smart-word">{{ item.smartData[0] }} </div>
              <div class="ques-smart-answer">{{ item.smartData[1] }} </div>
            </div>

            <div class="ques-content" v-if="item.content">
              <div class="question_content" v-for="(content, i) in item.content" :key="i">
                <div class="ques-tag">{{ content.quesName }}</div>
                <div class="">
                  <div class="question_body" v-html="content.topic"></div>
                  <!-- 合并题组 -->
                  <PaperEditBlock :hasScaned="hasScaned" :isSubstituteClass="filterData.isSubstitute" :item="content"
                    :content="content" :courseCount="getAddedCourseListCount(content, content.sortOrder, 'merge')"
                    :isShowScoreRate="false" :isShowAnsDetail="false" :isShowVariant="false"
                    @openResourceDialog="openResourceDialog" @startComment="startComment"
                    @showQuesDetails="showQuesDetails" @showVariant="showVariant" @hideVariant="hideVariant"
                    @lookGGB="lookGGB" @getParams="getParams" :abPaper="filterData.abPaper"
                    v-if="getIsMergeQues(item)" />
                  <div>
                    <PaperDetail v-if="content.showDetail" :item="content" :content="content" @showQuesRes="showQuesRes"
                      @showImg="showImg" />
                  </div>
                </div>

                <!-- 三级题型 -->
                <div class="small-ques-list" v-if="content.data.levelcode != ''">
                  <div class="small-ques-content" v-for="(small, smallIndex) in content.data.qs" :id="small.quesId"
                    :key="small.quesId">
                    <div class="ques-tag" v-if="small.sortOrder || small.quesName || small.quesNo">
                      {{ small.quesName || small.sortOrder || small.quesNo }}</div>
                    <div class="question_body" v-html="small.topic"></div>
                    <!-- 底部操作栏 -->
                    <div class="small-edit-block clearfix">
                      <span v-if="small.classScoringRate !== undefined">班级得分率：
                        <span class="score-rate-display" :class="`level-${small.level}`">
                          {{
                            small.classScoringRate == 0
                              ? small.classScoringRate
                              : Number(small.classScoringRate * 100).toFixed(2)
                          }}%
                        </span>
                      </span>
                      <span v-if="small.gradeScoringRate !== undefined" style="margin-left: 22px">年级得分率：{{
                        small.gradeScoringRate == 0
                          ? small.gradeScoringRate
                          : Number(small.gradeScoringRate * 100).toFixed(2)
                      }}%</span>

                      <!-- 只有代课班级才显示微课 -->
                      <template v-if="getShowWKBtn(small)">
                        <span class="pull-right ques-btn" style="margin-right: 0;"
                          @click="openResourceDialog({ item: small, type: 'look' })" v-if="getAllResourceLength(small)">查看资源（{{
                            getAllResourceLength(small) }}）</span>
                        <span class="pull-right ques-btn" @click="openResourceDialog({ item: small, type: 'add' })"
                          v-else>添加资源<i class="el-icon-circle-plus"></i></span>
                      </template>
                      <span v-if="small.classScoringRate !== undefined" class="pull-right ques-btn"
                        :class="small.ansDetail ? 'active' : ''" @click="showSmallAnsDetail(small)">
                        答题统计<i :class="small.ansDetail ? 'el-icon-caret-top active' : 'el-icon-caret-bottom'"></i>
                      </span>
                      <span class="pull-right ques-btn" :class="small.showDetail ? 'active' : ''"
                        @click="showSmallQuesDetails(small)">题目详情<i :class="small.showDetail ? 'el-icon-caret-top active' : 'el-icon-caret-bottom'
                          "></i></span>
                    </div>

                    <PaperDetail v-if="small.showDetail" :item="small" :content="small" @showQuesRes="showQuesRes"
                      @showImg="showImg" />
                    <AnsDetail :canViewPaper="canViewPaper" @updateScoreSplit="handleUpdateScoreSplit(small, 'small')"
                      :item="small" :stusCount="stuList.length" :content="small" :personBookId="personBookId"
                      v-if="small.ansDetail" />
                  </div>
                </div>
              </div>
            </div>

            <PaperEditBlock :hasScaned="hasScaned" :paperStructType="getPaperStructType(item)" :item="item"
              :isShowScoreRate="true"
              :courseCount="getIsMergeQues(item) ? 0 : getAddedCourseListCount(item, item.sortOrder || item.tQuesNo, 'noraml')"
              :content="(item.content && item.content[0]) || null" :isSubstituteClass="filterData.isSubstitute"
              :isShowQuesDetails="isEmptyPaperType ? false : getIsMergeQues(item) ? false : true"
              :isShowAnsDetail="getIsThreeLevelQues(item) ? false : true"
              :isShowComment="isEmptyPaperType ? false : getIsMergeQues(item) ? false : true"
              :isShowVariant="isEmptyPaperType ? false : getIsMergeQues(item) ? true : true"
              @startComment="startComment" @showVariant="showVariant" @hideVariant="hideVariant"
              @openResourceDialog="openResourceDialog" @showAnsDetail="showAnsDetail" @showQuesDetails="showQuesDetails"
              @lookGGB="lookGGB" @getParams="getParams" :abPaper="filterData.abPaper" />
            <div>
              <PaperVariant :isSubstituteClass="filterData.isSubstitute" v-if="item.variant && !getIsSplitQues(item)"
                :item="item" :content="item.content[0]" />
              <PaperDetail v-if="item.showDetail && item.content && !getIsSplitQues(item)" :item="item"
                :content="item.content && item.content[0]" @showQuesRes="showQuesRes" @showImg="showImg" />
              <AnsDetail :canViewPaper="canViewPaper" @updateScoreSplit="handleUpdateScoreSplit(item, 'big')"
                :item="item" :stusCount="stuList.length" :content="item.content && item.content[0]"
                :personBookId="personBookId" v-if="item.ansDetail && !getIsSplitQues(item)" />
            </div>

            <!-- 拆分题组 -->
            <div v-if="getIsSplitQues(item)">
              <div v-show="item.ansDetail">
                <div class="split-tabs">
                  <span class="split-tab" :class="{ active: item.splitIndex == i }" @click="setSplitTab(item, i)"
                    v-for="(splitItem, i) in item.split" :key="i">
                    {{ splitItem.quesName }}
                  </span>
                </div>
                <PaperEditBlock :hasScaned="hasScaned" :isSubstituteClass="filterData.isSubstitute"
                  :item="item.split[item['splitIndex']]" :isSplitItem="true" :isShowScoreRate="true"
                  :isShowAnsDetail="false" :isShowVariant="false" :isShowQuesDetails="false" :isShowComment="false"
                  @startComment="startComment" @openResourceDialog="openResourceDialog" @showVariant="showVariant"
                  @hideVariant="hideVariant" @showQuesDetails="showQuesDetails" @lookGGB="lookGGB"
                  @getParams="getParams" :abPaper="filterData.abPaper" />
              </div>

              <div v-for="(splitItem, i) in [item.split[item['splitIndex']]]" :key="i">
                <PaperVariant :isSubstituteClass="filterData.isSubstitute" v-if="item.variant" :item="item"
                  :content="splitItem.content[0]" />
                <PaperDetail v-if="item.showDetail" :item="item" :content="splitItem.content[0]"
                  @showQuesRes="showQuesRes" @showImg="showImg" />
                <AnsDetail :canViewPaper="canViewPaper" @updateScoreSplit="handleUpdateScoreSplit(item, 'big')"
                  :stusCount="stuList.length" :item="item.split[item['splitIndex']]" :content="splitItem.content[0]"
                  :personBookId="personBookId" v-if="item.ansDetail" />
              </div>
            </div>
          </div>
        </div>

        <!--答题卡列表-->
        <div class="comment-card" ref="commentCard">
          <comment-outline class="comment-outline" blockSize="30px" blockFontSize="14px" blockMargin="12px"
            @anchor="anchorToTarget" :list="cardList" @change="onChangeLevelList" />
        </div>
      </div>
    </div>

    <div v-if="pageInited && !quesList.length && !listLoading" class="nodata">
      <img :src="noResImg" alt="" />
      <p class="text-center">{{ errorMsg }}</p>
    </div>

    <sparkDialog :curIndex="curIndex" :list="sparkList" @closeDialog="closeDialog" v-if="sparkVisible" />
    <!-- 查看资源弹窗 -->
    <video-player v-if="defaultPlayerResource.show" :src="defaultPlayerResource.url"
      :title="defaultPlayerResource.title" @close-dialog="defaultPlayerResource.show = false"
      :isVideo="defaultPlayerResource.resType != '7'" />
    <look-paper-dialog v-if="isShowPaperImg" :stuTableData="stuTableData" :defaultRotate="0"
      @close-dialog="isShowPaperImg = false" />
    <ImgPreview v-if="isShowImg" :imgList="imgList" @close="cancelShowImg"></ImgPreview>

    <!-- 强制页面刷新 -->
    <div style="display: none;">{{ refreshKey }}</div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import headerFilter from '@/components/headerFilter';
import {
  getTestPaperSub,
  getTestPaperComment,
  listExamStu,
  getStuScanDataAPI,
} from '@/service/pexam';
import { chooseQuesSearch } from '@/service/pbook';
import sparkDialog from '@/components/sparkDialog.vue';
import VideoPlayer from '@/components/VideoPlayer/VideoPlayer.vue';
import LookPaperDialog from '@/components/LookPaperDialog.vue';
import { getExamTeamQues, getViewPaper } from '@/service/testbank';
import { v1 as uuid } from 'uuid';
import PaperEditBlock from './paperEditBlock.vue';
import PaperVariant from './paperComponent/paperVariant.vue';
import PaperDetail from './paperComponent/paperDetail.vue';
import AnsDetail from './paperComponent/ansDetail.vue';
import CommentOutline from '@/components/CommentOutline/index.vue';
import { getHuohuaPreviewLink, deepClone } from '@/utils';
import createDialog from '@/components/createDialog';
import ImgPreview from '@/components/SwiperViewer/ImgPreview.vue';
import ResAddViewListComponent from '@/components/AddQuesResource/views/ResAddViewList.vue'
import { ISOURCE_TYPES } from "@/typings/card";

// 判断题形式列表
const TFNameList = {
  1: ['√', '×'], // √ ×
  2: ['T', 'F'],
  3: ['A', 'B'],
}
let loginInfo = null;
export default {
  name: 'paper-comment',
  components: {
    headerFilter,
    sparkDialog,
    VideoPlayer,
    LookPaperDialog,
    PaperEditBlock,
    PaperVariant,
    PaperDetail,
    AnsDetail,
    CommentOutline,
    ImgPreview
  },
  data() {
    return {
      sparkVisible: false,
      sparkList: [],
      cardHeight: 0,
      noResImg: require('@/assets/no-res.png'),
      // 选入的题目列表
      selectQuesList: [],
      showCommentPage: false,
      // 班级学科数据
      filterData: {},
      // 当前选中的题目id
      detailId: '',
      allowedSubjects: [],
      listLoading: false,
      // 答题卡数据
      cardList: [],
      // 题目列表
      quesList: [],
      showHeader: false,
      errorMsg: '未查询到该班级该次考试数据',
      stuList: [],
      quesMap: {},
      sourceMap: {},
      // 当前播放资源
      defaultPlayerResource: {
        url: '',
        title: '',
        resType: '',
        show: false,
      },

      tempQuesList: [],
      bigIds: [],
      stuTableData: [],
      //是否展示原卷
      isShowPaperImg: false,
      // 试卷题目列表
      paperList: [],
      // 题目Id列表
      quesIds: [],
      // 空题目试卷讲评列表
      emptyPaperList: [],
      // 讲评模式 'normal'普通，'empty'空试卷
      paperType: 'normal',
      // 考试题目初始列表
      examDefaultList: [],
      subjectList: [],
      examSubjectInfo: null,
      // 题目结构列表
      questionGroups: [],
      // 题目结构列表（扁平）
      questionGroupsFlat: [],
      // 得分率设置
      levelList: [0, 60, 70, 90, 100],

      pageInited: false,
      refreshKey: 0,
      // 当前学科信息，包含v,source
      innerSubject: null,
      imgList: [],
      isShowImg: false,
    };
  },

  computed: {
    ...mapState(["showAnonymousName"]),
    personBookId() {
      let subjectId = this.filterData.subjectId;
      let subjectList = this.$sessionSave.get('innerNoRoleSubjectList');
      let subject = subjectList.find(item => item.id == subjectId);
      if (subject) {
        return subject.workIds[Number(this.filterData.abPaper)];
      }
      return '';
    },

    // 是否空题目类型试卷
    isEmptyPaperType() {
      return this.paperType == 'empty';
    },
    // 是否展示微课
    hasScaned() {
      return !!this.filterData.classList.length;
    },

    // 是否支持题卡结构
    isSupportCardLayout() {
      if (!this.innerSubject) return false;

      // 补录和题卡分离不支持
      const { originCardType, source } = this.innerSubject;
      return (source == ISOURCE_TYPES.HAND || source == ISOURCE_TYPES.WEB) && (originCardType == 1 || originCardType == 2)
    },

    // 是否可以查看原卷
    canViewPaper() {
      const { source } = this.innerSubject;

      return source == ISOURCE_TYPES.HAND ||
        source == ISOURCE_TYPES.WEB ||
        source == ISOURCE_TYPES.PHOTO
    },
  },

  activated() {
    // 第一次进来需要加载
    if (this.$sessionSave.get('loadComment') || !this.quesList.length) {
      this.$sessionSave.set('loadComment', false);
      this.showHeader = false;
      this.$nextTick(() => {
        this.showHeader = true;
      });
    }
  },

  mounted() {
    loginInfo = this.$sessionSave.get('loginInfo');
    this.initLevelList();
    // 第一次进来需要加载
    if (this.$sessionSave.get('loadComment') || !this.quesList.length) {
      this.$sessionSave.set('loadComment', false);
      this.showHeader = false;
      this.$nextTick(() => {
        this.showHeader = true;
      });
    }
  },

  methods: {
    // 显示微课
    getShowWKBtn(item) {
      return this.hasScaned && item.qId === item.bigId && this.isSubstituteClass
    },
    getParams() {
      return {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        subjectId: this.filterData.subjectId,
      };
    },
    // 是否拆分题组
    getIsSplitQues(item) {
      if (item.split) {
        return item.split.length > 1;
      } else {
        return false;
      }
    },

    // 是否合并题组
    getIsMergeQues(item) {
      if (item.content && typeof item.content !== 'string') {
        return item.content.length > 1;
      } else {
        return false;
      }
    },

    // 是否三级题型
    getIsThreeLevelQues(item) {
      if (this.getIsMergeQues(item)) return false;
      if (this.getIsSplitQues(item)) return false;

      if (!item.content || typeof item.content === 'string') return false;
      if (!(item.content[0].data.levelcode != '' || item.content[0].data.qs.length > 1)) return false;

      const qs = item.content[0].data.qs;
      for (const smallItem of qs) {
        if (this.quesIds.includes(smallItem.quesId)) return true;
      }
      return false;
    },

    // 试卷结构类型 1：二级题型 2：三级题型 3：合并题型 4：拆分题型
    getPaperStructType(item) {
      return this.getIsSplitQues(item) ? 4 : this.getIsMergeQues(item) ? 3 : this.getIsThreeLevelQues(item) ? 2 : 1
    },

    closeDialog() {
      this.sparkVisible = false;
    },
    async showImg(list, index) {
      let copyList = this.$deepClone(list);
      copyList.forEach(async item => {
        let resType = item.resourceType;
        if (item.resourceUrl.startsWith('https://www.huohuaschool.com/')) {
          item.resourceUrl = await getHuohuaPreviewLink(item.resourceUrl, loginInfo.huohua_id);
        } else if (resType != '7') {
          let match = item.resourceUrl.match(
            /\/cooperation\/tencent\/let\/download_video.php\?id=([^&]*)/
          );
          if (match) {
            let id = match[1];
            let LLKT_URL = process.env.VUE_APP_LLKTURL;
            if (id) {
              item.resourceUrl = `${LLKT_URL}/llkt/${id}.mp4`;
            }
          }
        }
      });
      this.curIndex = index;

      let item = copyList[index];
      if (['png', 'jpg'].includes(item.resourceExt)) {
        this.imgList = [item.resourceUrl];
        this.isShowImg = true;
      } else {
        this.sparkList = copyList;
        this.sparkVisible = true;
      }
    },

    cancelShowImg() {
      this.isShowImg = false;
    },
    /**
     * @name: 查看题目资源
     */
    showQuesRes(list, index) {
      this.defaultPlayerResource = {
        url: list[index].resourceUrl,
        title: list[index].resourceName,
        resType: list[index].resourceType,
        show: true,
      };
    },
    getSubjectList(data = []) {
      this.subjectList = data;
    },
    async updateFilter(data, type) {
      this.filterData = data;
      this.questionGroups = [];
      this.questionGroupsFlat = [];
      let innerSubjectList = this.$sessionSave.get("innerSubjectList");
      if (innerSubjectList) {
        this.innerSubject = innerSubjectList.find(it => it.id == this.filterData.subjectId);
      }
      this.selectQuesList = [];
      if (type === 'class') {
        await this.getTestPaperComment('changeClass');
      } else {
        this.quesList = [];
        this.cardList = [];
        await this.getTestPaperComment();
      }
      this.pageInited = true;
    },

    // 锚点
    anchorToTarget(item) {
      this.detailId = item.quesId || item.totalIndex || item.tQuesNo;
      let jump = document.getElementById(this.detailId);

      jump.scrollIntoView({
        block: 'start',
        behavior: window == window.parent ? 'smooth' : 'instant',
      });
      this.$nextTick(() => {
        this.$katexUpdate();
      });
    },

    // 点击题目亮显
    selectQuesItem(item) {
      if (item.quesId) {
        this.detailId = item.quesId === this.detailId ? '' : item.quesId;
      } else {
        this.detailId = item.totalIndex === this.detailId ? '' : item.totalIndex;
      }
      this.$nextTick(() => {
        if (this.detailId) {
          this.$katexUpdate();
        }
      });
    },
    // 获取有数据的学科
    getTestPaperSub() {
      getTestPaperSub({
        examId: this.$sessionSave.get('reportDetail').examId,
      })
        .then(data => {
          if (data.data.length) {
            this.allowedSubjects = data.data.map(item => {
              return item.subjectId;
            });

            if (this.allowedSubjects.find(item => item == this.filterData.subjectId)) {
              this.getTestPaperComment();
            }
          } else {
            this.listLoading = false;
          }
        })
        .catch(err => {
          this.listLoading = false;
        });
    },

    // 获取题目结构接口
    async getExamTeamQues() {
      this.questionGroups = [];
      try {
        let examId = this.$sessionSave.get('reportDetail').examId,
          subjectId = this.filterData.subjectId;
        const res = await getExamTeamQues({
          schoolId: this.$sessionSave.get('schoolInfo').id,
          examId,
          subjectId,
          abCardSheetType: this.filterData.abPaper
        });
        const data = res.data;

        if (!data.length) {
          console.error(res.msg);
          return
        }

        if (data.length) {
          this.$sessionSave.set(`examTeamQues_${loginInfo.id}`, data);
          this.questionGroups = data;
          if (this.questionGroups.length > 1) {
            this.questionGroupsFlat = this.questionGroups.reduce((ac, cu) => {
              if (!ac) ac = [];
              if (ac.data) ac = ac.data
              return ac.concat(cu.data);
            })
          } else {
            this.questionGroupsFlat = this.questionGroups[0].data;
          }
        }
      } catch (error) {
        this.questionGroups = [];
      }
    },

    // 获取试卷
    async getTestPaperComment(changeClass) {
      listExamStu({
        examId: this.$sessionSave.get('reportDetail').examId,
        classId: this.filterData.classId,
        subjectId: this.filterData.subjectId,
      }).then(data => {
        this.stuList = data.data.map(it => {
          return {
            ...it,
            anonymous: it.name.slice(0, 1) + "**"
          }
        });
      });

      // this.$sessionSave.get('reportDetail')
      if (this.isSupportCardLayout) {
        // 补录不走ExamTeamQues
        await this.getExamTeamQues();
      }
      try {
        let v2 = "";
        if (this.innerSubject) v2 = this.innerSubject.v;
        let data = await getTestPaperComment({
          examId: this.$sessionSave.get('reportDetail').examId,
          classId: this.filterData.classId,
          subjectId: this.filterData.subjectId,
          abPaper: this.filterData.abPaper,
          v2,
        });

        data.data.forEach(item => {
          if (!item.smart) return

          // 对该字段排序，纯英文永运排在前面
          item.smartData = this.sortSmartData(item.smart);
        })

        this.quesList = [];
        this.cardList = [];
        this.paperList = [];
        data.data = this.addAnonymousNameToList(data.data);
        this.examDefaultList = this.$deepClone(data.data);
        // 全为quesId全为空，单独处理
        if (data.data.every(item => !item.quesId)) {
          this.setEmptyData(data);
          this.setCardListByDefault();
          this.quesList = this.markQuesLevel(this.quesList);
          await this.setCardListByViewPaper(this.quesList, true);
          return;
        }

        this.paperType = 'normal';

        //去除quesid为空的数据
        data.data = data.data.filter(item => {
          return item.quesId;
        });
        this.paperList = JSON.parse(JSON.stringify(data.data));
        this.errorMsg = '未查询到该班级该次考试数据';

        // 切换了班级
        let ids = data.data.map(item => {
          this.sourceMap[item.bigId] = item.links;
          let quesIds = [item.bigId];
          quesIds = quesIds.concat(item.links.slice(0, 6));
          return quesIds.join(',');
        });
        if (!ids.length) return;

        this.bigIds = [];
        this.quesIds = [];

        data.data.forEach(item => {
          if (!this.bigIds.some(id => item.bigId.includes(id))) {
            this.bigIds.push(item.bigId);
          }
          if (this.quesIds.indexOf(item.quesId) == -1) {
            this.quesIds.push(item.quesId);
          }
        });
        // 获取题面
        this.totalIds = [...new Set(ids)];
        this.chooseQuesSearch(data.data, this.totalIds);
      } catch (error) {
        console.log(error);
        this.listLoading = false;
        this.paperType = 'normal';
        this.quesList = [];
        this.cardList = [];
        this.errorMsg = '未查询到该班级该次考试数据';
      }
    },

    /**
     * @description: 对智能识别的字段重新排序
     * @param {*} arr
     * @return {*}
     */
    sortSmartData(obj) {
      const values = Object.values(obj);
      const englishValue = values.find(value => typeof value === 'string' && /^[a-zA-Z]+$/.test(value));
      const otherValue = values.find(value => value !== englishValue);
      return [englishValue, otherValue];
    },

    addAnonymousNameToList(list) {
      return list.map(it => {
        it.badAnsList.forEach(stuItem => {
          stuItem.anonymous = stuItem.stuName.slice(0, 1) + "**";
        })
        it.goodAnsList.forEach(stuItem => {
          stuItem.anonymous = stuItem.stuName.slice(0, 1) + "**";
        })
        it.grdGoodAnsList.forEach(stuItem => {
          stuItem.anonymous = stuItem.stuName.slice(0, 1) + "**";
        })
        return it;
      })
    },

    /**
     * @description: 初始化得分率等级
     * @return {*}
     */
    initLevelList() {
      let keypath = `levelList_${loginInfo.id}`;
      try {
        let data = this.$localSave.get(keypath);
        if (typeof data === 'string') throw "缓存格式错误";

        if (data) {
          this.levelList = data;
        }
      } catch (error) {
        localStorage.removeItem(keypath);
      }
    },

    /**
     * @description: 监听得分率等级改变
     * @param {*} val
     * @return {*}
     */
    onChangeLevelList(val) {
      this.levelList = val;
      this.quesList = this.markQuesLevel(this.quesList)
      this.quesList.forEach(item => {
        if (!item.content) return;

        item.content.forEach(sItem => {
          sItem.data.qs = this.markQuesLevel(sItem.data.qs)
        })
      })
      this.refreshKey++;
    },

    /**
     * @description: 标记题目等级
     * @param {*} list
     * @return {*}
     */
    markQuesLevel(list) {
      list.forEach(subItem => {
        subItem.level = 3;
        for (let i = 0; i < this.levelList.length; i++) {
          const lItem = this.levelList[i];
          if (Number(subItem.classScoringRate * 100) >= lItem) continue;

          subItem.level = i === 0 ? 0 : i - 1;
          break;
        }
      });

      return list;
    },

    /**
     * @description: 补全判断题的显示标志
     * @return {*}
     */
    fullJudgesName(smallItem) {
      let answer = smallItem.answer.split(",")[0];
      let answerLabel = [];
      let judgeNames = TFNameList[smallItem.judgeType];
      if (!judgeNames) return;

      answerLabel = answer === "A" ? judgeNames[0] : judgeNames[1]
      smallItem.answerLabel = answerLabel;
    },

    // 获取题目的题面
    async chooseQuesSearch(totalList, ids) {
      this.listLoading = true;
      await this.$nextTick();
      try {
        let data = await chooseQuesSearch({
          qIds: ids.join(','),
          subjectId: this.filterData.xfId,
          phaseId: this.filterData.phaseId,
        });

        this.quesMap = {};
        data.data.forEach(v => {
          if (this.questionGroupsFlat.length) {
            let findStuct = this.questionGroupsFlat.find(qItem => qItem.id === v.qId) || {};
            v = { ...findStuct, ...v }
            v.data.qs = v.data.qs.map(v2 => {
              let findStuct = this.questionGroupsFlat.find(qItem => qItem.id === v2.qId) || {};
              // if(!findStuct.id) debugger;
              v2 = { ...findStuct, ...v2 }
              if (v.data.type == 2) {
                // 判断题补全标签
                this.fullJudgesName(v2)
              }

              return v2;
            });
          };

          this.quesMap[v.qId] = {
            content: [v],
            points: (v.data && v.data.tag_ids) || [],
            type: v.data.type,
          };
        });

        this.tempQuesList = data.data;
        // 根据classScoringRate设置得分率显示等级
        totalList = this.markQuesLevel(totalList);
        for (let i = 0; i < totalList.length; i++) {
          let item = totalList[i];
          let ques = this.quesMap[item.bigId];
          item.index = i;
          if (item.bigId == item.quesId) {
            let itemArr = item.bigId.split(',');
            if (itemArr.length > 1) {
              this.setMerge(itemArr, item);
              ques = this.quesMap[item.bigId];
            }
            this.setSplit(item, ques);

            if (ques.content && !ques.content.length) {
              ques.content = [ques.content]
            }

            item.content = ques.content;
            item.points = ques.points;
            item.type = ques.type;
            item.links = this.sourceMap[item.quesId];
            this.finishingVariantList(item);

            this.quesMap[item.bigId].content = [
              ...ques.content,
              {
                ...ques.content,
                ...item,
              },
            ];

            this.quesMap[item.bigId] = {
              ...ques,
              ...item,
            };
          } else {
            ques = this.quesMap[item.bigId] = {
              ...item,
              ...ques
            };

            this.finishingVariantList(ques);
            ques.content.forEach(l => {
              for (let i = 0; i < l.data.qs.length; i++) {
                const qItem = l.data.qs[i];

                if (!item.quesId.includes(qItem.qId)) continue;

                l.data.qs[i] = {
                  ...qItem,
                  ...item,
                }
              }
            });
          }
        }

        await this.handleData(totalList);
        this.$nextTick(() => {
          this.$katexUpdate();
          this.listLoading = false;
        });
      } catch (error) {
        console.error(error);
        this.listLoading = false;
      }
    },

    // 整理题干上的变式题列表
    finishingVariantList(item) {
      item.linkQues = item.links.map((it, index) => {
        let linkQues = deepClone(this.quesMap[it]);
        if (!linkQues) return null;

        linkQues.name = item.linkNames[index];
        if (!linkQues.content[0]) return linkQues;

        linkQues.content = linkQues.content[0];
        linkQues.id = linkQues.content.id;
        linkQues.showAnswer = false;
        linkQues.showAnalysis = false;

        return linkQues
      }).filter(it => !!it)
    },

    // 设置空题目数据
    setEmptyData(data) {
      data.data.forEach(item => {
        item.quesId = '';
      });
      this.quesList = data.data;
      this.paperType = 'empty';

      if (this.questionGroups.length) {
        this.setCardListByGroup(this.quesList);
      } else {
        let quesTypeList = [];
        this.quesList.forEach((item, idnex) => {
          this.$set(item, 'totalIndex', idnex);

          let quesType = this.$getQuesType(item.quesType);

          if (quesTypeList.indexOf(quesType) === -1) {
            quesTypeList.push(quesType);
            let qIndex = quesTypeList.length;
            let fullTitle = quesType ? `${this.$sectionToChinese(qIndex)}、${quesType}` : '';
            this.cardList.push({
              title: quesType,
              fullTitle,
              index: qIndex,
              list: [item],
            });
          } else {
            this.cardList.forEach(card => {
              if (!quesType || card.title === quesType) {
                card.list.push(item);
              }
            });
          }
        });
      }
    },

    // 设置合并题组（content为2个及多个）
    setMerge(arr, ques) {
      arr.forEach(bigId => {
        this.tempQuesList.forEach(item => {
          if (bigId == item.qId) {
            if (this.quesMap[arr.join(',')]) {
              this.quesMap[arr.join(',')].content.push({ ...item, ...ques });
              this.quesMap[arr.join(',')] = {
                content: this.quesMap[arr.join(',')].content,
                points: (item.data && item.data.tag_ids) || [],
                type: item.data.type,
              };
            } else {
              this.quesMap[arr.join(',')] = {
                content: [{ ...item, ...ques }],
                points: (item.data && item.data.tag_ids) || [],
                type: item.data.type,
              };
            }
          }
        });
      });
    },

    // 设置拆分题组（split为2个及多个）
    setSplit(item) {
      const bigId = item.bigId;
      if (this.quesMap[bigId].split) {
        this.quesMap[bigId].split = [...this.quesMap[bigId].split, item];
      } else {
        this.quesMap[bigId].split = [item];
        this.quesMap[bigId].splitIndex = 0;
      }
      // item.split = item;
    },

    // 处理数据
    async handleData(allList) {
      this.quesList = [];
      this.bigIds.forEach((item, idnex) => {
        this.quesList.push(this.quesMap[item]);
      });
      this.quesList.forEach((item, index) => {
        this.setQuesName(item);
        this.setSplitQuesScore(item);
        this.setThreeQuesScore(item);
      });

      if (this.isSupportCardLayout) {
        this.setCardListByDefault();
      } else {
        // 从试卷信息中获取答题概览
        const matched = await this.setCardListByViewPaper();

        if (!matched) {
          this.cardList = []
          this.quesList.forEach((item, index) => {
            if (!item.content) return;

            const card = {
              fullTitle: '',
              index,
              list: [],
              tag: '',
              title: ''
            }

            item.content.forEach(content => {
              if (content.data.levelcode == '') return;

              content.data.qs.forEach(qs => card.list.push(qs))
            })
            if (!card.list.length) {
              card.list.push(item)
            }
            this.cardList.push(card)
          })
        }
      }
    },

    /**
     * @description: 从试卷信息中获取答题概览
     * @return {*}
     */
    async setCardListByViewPaper(smallQsList = null, isBlank = false) {
      try {
        const { paperNo, testBankId } = this.innerSubject;
        const params = {
          optUserId: loginInfo.id
        }
        if (testBankId) {
          params.id = testBankId;
        } else if (paperNo) {
          params.paperNo = paperNo;
        } else {
          return;
        }

        const res = await getViewPaper(params)
        const { quesInfo: quesInfoStr } = res.data;
        if (!quesInfoStr) return false;
        
        const quesInfo = JSON.parse(quesInfoStr);
        this.cardList = [];

        if (!smallQsList) {
          smallQsList = [];
          this.quesList.forEach(item => {
            if (!item.content) return;

            let qsCount = 0;
            item.content.forEach(content => {
              if (content.data.levelcode == '') return;

              content.data.qs.forEach(qs => {
                smallQsList.push(qs)
                qsCount++
              })
            })

            if (!qsCount) {
              smallQsList.push(item)
            }
          })
        }

        const getSQsCardList = (qsInfoList) => {
          let qslist = [];
          qsInfoList.forEach(it => {
            if (it.data && it.data.length) {
              const list = getSQsCardList(it.data)
              qslist = qslist.concat(list)
              return;
            }

            if (qslist.some(qs => qs.quesId.includes(it.id))) return;

            const qsInfo = smallQsList.find(qs => {
              return isBlank ? it.quesNo == qs.quesNumber :
                qs.quesId.includes(it.id)
            });
            if (!qsInfo) return;

            qslist.push(qsInfo)
          })

          return qslist;
        }

        quesInfo.forEach((bigQs, index) => {
          this.cardList.push({
            fullTitle: bigQs.name,
            index,
            list: getSQsCardList(bigQs.data),
            tag: '',
            title: ''
          })
        })

        return this.cardList.some(card => card.list.length)
      } catch (error) {
        console.error('setCardListByViewPaper: ', error)
        return false;
      }
    },

    setCardListByGroup(quesList) {
      this.cardList = [];
      quesList.forEach((item, idnex) => {
        this.$set(item, 'totalIndex', idnex);
        const questionGroup = this.questionGroups.find(group => {
          return group.data.find(dIt => dIt.tQuesNo == item.tQuesNo);
        });
        let bigTitle = '';
        if (questionGroup && questionGroup.showName == 1) {
          bigTitle = questionGroup.name;
        }
        let findQues = this.cardList.find(citem => {
          if (citem.fullTitle == bigTitle) return citem;
        });
        if (findQues) {
          findQues.list.push(item);
        } else {
          let qIndex = this.cardList.length;
          let quesType = this.$getQuesType(item.type);
          this.cardList.push({
            title: quesType,
            tag: bigTitle,
            fullTitle: bigTitle,
            index: qIndex,
            list: [item],
          });
        }
      });

      console.log("setCardListByGroup", deepClone(this.cardList));
    },

    /**
     * @description: 设置答题概览的结构数据
     * @return {*}
     */
    setCardListByDefault() {
      if (!this.questionGroups.length) {
        this.setCardListByDefaultWithoutCard();
        return;
      }

      this.cardList = [];
      const paperList = deepClone(this.paperList.length ? this.paperList : this.examDefaultList);
      this.questionGroups.forEach(item => {
        let qsList = item.data.map(qItem => {
          let filters = paperList.filter(pqItem => {
            return qItem.id.includes(pqItem.quesId)
          })
          if (filters.length) return filters;

          let filter = paperList.find(pqItem => {
            return qItem.tQuesNo == pqItem.tQuesNo || qItem.tQuesNo.split(',').includes(pqItem.tQuesNo);
          })
          if (!filter) return [];

          return [filter];
        }).flat()

        let cardItem = {
          fullTitle: (item.showName === "" || item.showName == 1) ? item.name : "",
          index: item.quesNos,
          list: qsList,
        }
        if (qsList.length) {
          cardItem.title = this.$getQuesType(qsList[0].quesType);
          this.cardList.push(cardItem);
        }
      })
    },

    /**
     * @description: 设置答题概览的结构数据（无卡的情况）
     * @return {*}
     */
    setCardListByDefaultWithoutCard() {
      if (!this.paperList.length) return;

      this.cardList = [];
      this.paperList.forEach((item, idnex) => {
        this.$set(item, 'totalIndex', idnex);
        const questionGroup = this.questionGroups.find(group => {
          return group.data.find(data => data.tQuesNo === item.tQuesNo);
        });
        let bigTitle = '';
        if (questionGroup && questionGroup.showName == 1) {
          bigTitle = questionGroup.name;
        }

        let bigId = item.bigId;
        if (item.bigId.includes(',')) {
          bigId = item.bigId.split(',')[0];
        }
        let detailItem = this.tempQuesList.find(tmpItem => tmpItem.id === bigId);
        let findQues = this.cardList.find(citem => {
          if (citem.id === detailItem.id) return true;

          if (citem.tag === detailItem.bigTitle) {
            return citem.list[citem.list.length - 1].quesNumber === item.quesNumber - 1;
          }
        });
        if (findQues) {
          findQues.list.push(item);
        } else {
          let qIndex = this.cardList.length;
          let quesType = this.$getQuesType(item.type);
          this.cardList.push({
            title: quesType,
            tag: detailItem.bigTitle,
            fullTitle: bigTitle,
            index: qIndex,
            list: [item],
          });
        }
      });
    },

    // 设置题目名称
    setQuesName(item) {
      if (item.quesName && !this.getIsSplitQues(item)) return item.quesName;

      let arr = [];
      if (this.getIsSplitQues(item)) {
        item.split.forEach(splitItem => {
          arr.push(splitItem.quesName);
        });
      } else {
        item.content.forEach(content => {
          if (content.data.qs && content.data.qs.length > 1) {
            content.data.qs.forEach(qs => {
              if (qs.quesName) arr.push(qs.quesName);
            });
          }
        });
      }
      if (arr.length) {
        item.quesName = arr.join('、');
      }
    },

    // 设置拆分题成绩
    setSplitQuesScore(item) {
      let isSplit = this.getIsSplitQues(item);
      if (!isSplit) return;

      let splitArr = item.split;
      let classScoringRateSum = 0;
      let gradeScoringRateSum = 0;
      splitArr.forEach(splitItem => {
        classScoringRateSum = splitItem.classScoringRate + classScoringRateSum;
        gradeScoringRateSum = splitItem.gradeScoringRate + gradeScoringRateSum;
      });
      item.classScoringRate = classScoringRateSum / splitArr.length;
      item.gradeScoringRate = gradeScoringRateSum / splitArr.length;
    },

    // 设置三级题大题总得分率
    setThreeQuesScore(item) {
      if (!this.getIsThreeLevelQues(item)) return;

      let clsAdded = 0;
      let grdAdded = 0;
      const qs = item.content[0].data.qs;
      for (const smallItem of qs) {
        clsAdded += smallItem.classScoringRate;
        grdAdded += smallItem.gradeScoringRate;
      }

      item.classScoringRate = clsAdded / qs.length;
      item.gradeScoringRate = grdAdded / qs.length;
    },

    // 选中所有题目
    selectAllComment() {
      this.selectQuesList = [];
      this.quesList.forEach(item => {
        this.$set(item, 'hasAdd', true);
        let isMergeQues = this.getIsMergeQues(item);
        if (isMergeQues) {
          item.content.forEach(content => {
            this.$set(content, 'hasAdd', true);
          });
        }
      });

      this.quesList.forEach(item => {
        let nId = '',
          hId = '',
          hId2 = '';
        item.linkQues.forEach((it, index) => {
          if (!it.content) return;

          if (index == 0) {
            nId = it.id;
          } else if (index == 1) {
            hId = it.id;
          } else {
            hId2 = it.id;
          }
        })

        let isMergeQues = this.getIsMergeQues(item);
        if (isMergeQues) {
          item.content.forEach(content => {
            this.selectQuesList.push({
              quesId: content.qId,
              clsFullNum: item.clsFullNum || content.clsFullNum,
              clsErrorNum: item.clsErrorNum || content.clsErrorNum,
              resList: item.resList || content.resList,
              totalIndex: item.totalIndex || content.sortOrder,
              normalId: nId,
              hardId: hId,
              hardId2: hId2,
              quesName: item.quesName,
            });
          });
        } else if (item.quesId || item.content) {
          this.selectQuesList.push({
            quesId: item.quesId || item.content[0].qId,
            clsFullNum: item.clsFullNum || item.content[0].clsFullNum,
            clsErrorNum: item.clsErrorNum || item.content[0].clsErrorNum,
            resList: item.resList || item.content[0].resList,
            totalIndex: item.totalIndex || item.content[0].sortOrder,
            normalId: nId,
            hardId: hId,
            quesName: item.quesName,
          });
        }
      });
    },

    // 获取小题题目资源和微课数量
    getAllResourceLength(small) {
      if (!small.data) return small.courseList ? small.courseList.length : 0;

      let resLength = small.data.url.filter(it => it.resourceType == '4' || it.resourceType == '7').length
      return resLength + small.courseList.length;
    },

    // 进入讲评
    async startComment(item = {}, content = {}) {
      let selectedQuesId = [
        item.bigId || item.content[0].qId,
        item.qId || item.quesId,
        item.quesName || item.content[0].quesName,
      ];
      let isThreeQues = this.getIsThreeLevelQues(item);
      if (isThreeQues) {
        let ques = item.content[0];
        selectedQuesId = [ques.qId, ques.data.qs[0].quesId];
      }

      // 加入讲评的考试信息
      let joinCommentExamInfo = {
        examName: this.$sessionSave.get('reportDetail').examName,
        classId: this.filterData.classId,
        subjectId: this.filterData.subjectId,
        qids: this.totalIds.join(','),
        phaseId: this.filterData.phaseId,
        abPaper: this.filterData.abPaper
      };

      // 同步变式题
      const itemDef = this.examDefaultList.find(qs => qs.tQuesNo == item.tQuesNo)
      itemDef.linkNames = item.linkNames;
      itemDef.linkIds = item.linkIds;
      itemDef.links = item.links;
      itemDef.linkQues = item.linkQues;

      // 讲评信息存入数据库
      let examId = this.$sessionSave.get('reportDetail').examId;
      this.examSubjectInfo = this.subjectList.find(item => item.id == this.filterData.subjectId);
      this.examSubjectInfo.personalBookId = this.personBookId;
      await window.idbStore.put('comment', {
        userid: loginInfo.id,
        loginInfo,
        joinCommentExamInfo,
        examDefaultList: this.examDefaultList,
        examStuList: this.stuList,
        examClassList: this.filterData.classList,
        examSubjectInfo: this.examSubjectInfo,
        quesDetailList: this.tempQuesList,
        // 当前选中的题目
        selectedQuesId: selectedQuesId,
      });
      console.log('commentDB 存入成功', loginInfo.id);
      console.log('===> startComment');
      let href = `${location.origin}/quescom/${loginInfo.id}_${examId}_${this.filterData.subjectId}`;
      if (window.cef) {
        location.href = href;
      } else {
        window.open(href, 'newWindow');
      }
    },
    // 清空讲评题目
    emptyQues() {
      this.quesList.forEach(item => {
        this.$set(item, 'hasAdd', false);
        let isMergeQues = this.getIsMergeQues(item);
        if (isMergeQues) {
          item.content.forEach(content => {
            this.$set(content, 'hasAdd', false);
          });
        }
      });
      this.selectQuesList = [];
    },

    // 题目加入、移除讲评
    selectQues(item, content) {
      this.$set(item, 'hasAdd', !item.hasAdd);
      if (!content) {
        content = item.content[0];
      }
      let quesList = this.selectQuesList.filter(sub => {
        return content.qId === sub.quesId || item.quesId === sub.quesId;
      });
      if (!item.hasAdd && quesList.length) {
        let dataLength = this.selectQuesList.length;
        for (let i = 0; i < dataLength; i++) {
          let curItem = this.selectQuesList[i];
          if (curItem.quesId === (content.qId || item.quesId)) {
            this.selectQuesList.splice(i, 1);
            break;
          }
        }
      } else {
        let nId = '',
          hId = '',
          hId2 = '';

        item.linkQues.forEach((it, index) => {
          if (!it.content) return;

          if (index == 0) {
            nId = it.id;
          } else if (index == 1) {
            hId = it.id;
          } else {
            hId2 = it.id;
          }
        })

        this.selectQuesList.push({
          quesId: content.qId || item.quesId,
          totalIndex: item.totalIndex || content.sortOrder,
          clsFullNum: content.clsFullNum || item.clsFullNum,
          clsErrorNum: content.clsErrorNum || item.clsErrorNum,
          resList: item.resList || content.resList,
          normalId: nId,
          hardId: hId,
          hardId2: hId2,
          quesName: item.quesName,
        });
      }
    },
    // 展开收起题目详情
    showQuesDetails(item) {
      this.$set(item, 'variant', false);
      this.$set(item, 'ansDetail', false);

      this.$set(item, 'showDetail', !item.showDetail);
      this.$nextTick(() => {
        this.$katexUpdate();
      });
    },

    // 展开收起答题详情
    showAnsDetail(item, forceShow) {
      this.$set(item, 'showDetail', false);
      this.$set(item, 'variant', false);
      this.$set(item, 'ansDetail', forceShow || !item.ansDetail);
      let judgeNames = ["正确", "错误"];
      if (this.questionGroupsFlat.length) {
        let findStuct = this.questionGroupsFlat.find(qItem => qItem.id === item.quesId);
        if (findStuct) {
          judgeNames = TFNameList[findStuct.judgeType]
        }
      }
      item.judgeNames = judgeNames;

      let isSplit = this.getIsSplitQues(item);
      if (isSplit) {
        let splitArr = item.split;
        for (const item of splitArr) {
          // if (item.ansMap) continue;

          let fullScore = parseFloat(item.fullScore);
          item.ansMap = {};
          let k = '';
          let stus = JSON.parse(item.stus);
          stus.sort((a, b) => b[1] - a[1]);
          for (let i = 0; i < stus.length; i++) {
            let stu = stus[i];
            let id = stu[0],
              score = parseFloat(stu[1]),
              option = stu.length > 2 ? stu[2] : '';
            let stu2 = this.stuList.find(q => q.id == id);

            for (const content of item.content) {
              if (
                '01258'.indexOf(content.data.type) >= 0 &&
                (option === null || option === undefined)
              ) {
                if (score == fullScore) {
                  k = '正确';
                } else {
                  k = '错误';
                }
              } else {
                if ('1258'.indexOf(content.data.type) >= 0) {
                  if (content.data.type == 2 || stu.length <= 2) {
                    if (!Object.keys(item.ansMap).length) {
                      item.ansMap[judgeNames[0]] = [];
                      item.ansMap[judgeNames[1]] = [];
                    }

                    k = '';
                    switch (option) {
                      case 'A':
                        k = judgeNames[0];
                        break;
                      case 'B':
                        k = judgeNames[1];
                        break;
                      case '':
                        k = "未选";
                        if (!item.ansMap[k]) item.ansMap[k] = [];
                        break;
                      default:
                        k = "多选";
                        if (!item.ansMap[k]) item.ansMap[k] = [];
                        break;
                    }
                  } else if (stu.length > 2) {
                    //选项
                    let optionText = content.optionText.map((option, oIndex) => {
                      return String.fromCharCode(oIndex + 65).toUpperCase();
                    });
                    if (!item.ansMap[optionText[0]]) {
                      optionText.forEach(val => {
                        item.ansMap[val] = [];
                      });
                      if (content.data.type == 1) {
                        item.ansMap[`全对`] = [];
                      } else {
                        item.ansMap[`多选`] = [];
                      }
                      item.ansMap[`未作答`] = [];
                    }
                    k = '';
                    optionText.forEach(val => {
                      if (option.indexOf(val) >= 0) {
                        k = val;
                        if (content.data.type == 1) {
                          if (!item.ansMap[k]) {
                            item.ansMap[k] = [];
                          }
                          // 跳过未选择此题的学生
                          if (stu[3] !== "0") {
                            item.ansMap[k].push({
                              ...stu2,
                              answer: stu[2],
                              score
                            });
                          }

                          if (score == fullScore) {
                            k = '全对';
                          }
                        } else {
                          if (score == 0 && option.length > 1) {
                            if (!item.ansMap[k]) {
                              item.ansMap[k] = [];
                            }
                            // // 跳过未选择此题的学生
                            // if (stu[3] !== "0") {
                            //   item.ansMap[k].push({
                            //     ...stu2,
                            //     answer: stu[2],
                            //     score
                            //   });
                            // }
                            k = '多选';
                          }
                        }
                      }
                    });
                    if (score == 0 && option == '') {
                      k = '未作答';
                    }
                  }
                }

                if ('3467'.indexOf(content.data.type) >= 0) {
                  k = this.getScoreSeciton(k, fullScore, score, item);
                }
              }

              if (k === '') {
                continue;
              }
              if (!item.ansMap[k]) {
                item.ansMap[k] = [];
              }
              // 跳过未选择此题的学生
              if (stu[3] !== "0") {
                item.ansMap[k].push({
                  ...stu2,
                  answer: stu[2],
                  score: score,
                });
              }
            }
          }
        }

        if (item.quesType === 1) {
          // 多选题设置选择统计
          item.optionStusMap = this.getMChoiceOptionStatistics(item);
        }
        return;
      }

      let fullScore = parseFloat(item.fullScore);
      item.ansMap = {};
      let k = '';

      let stus = JSON.parse(item.stus);
      let contentItem = item.content;
      const content = contentItem ? contentItem[0] : null;
      const quesType = content ? content.data.type : item.quesType;

      let answerSet = new Set();
      if ('1258'.indexOf(quesType) >= 0 && this.paperType == 'empty') {
        stus.forEach(stu => {
          if (stu[2]) answerSet.add(stu[2]);
        });
      }
      stus.sort((a, b) => b[1] - a[1]);
      for (let i = 0; i < stus.length; i++) {
        let stu = stus[i];
        let id = stu[0],
          score = parseFloat(stu[1]),
          option = stu.length > 2 ? stu[2] : '';
        let stu2 = this.stuList.find(q => q.id == id);

        if ('01258'.indexOf(quesType) >= 0 && (option === null || option === undefined)) {
          if (score == fullScore) {
            k = '正确';
          } else {
            k = '错误';
          }
        } else {
          if ('1258'.indexOf(quesType) >= 0) {
            if (quesType == 2 || stu.length <= 2) {
              if (!Object.keys(item.ansMap).length) {
                item.ansMap[judgeNames[0]] = [];
                item.ansMap[judgeNames[1]] = [];
              }
              k = '';
              switch (option) {
                case 'A':
                  k = judgeNames[0];
                  break;
                case 'B':
                  k = judgeNames[1];
                  break;
                case '':
                  k = "未选";
                  if (!item.ansMap[k]) item.ansMap[k] = [];
                  break;
                default:
                  k = "多选";
                  if (!item.ansMap[k]) item.ansMap[k] = [];
                  break;
              }
            } else if (stu.length > 2) {
              //选项
              let optionText;
              if (this.paperType == 'empty') {
                if (item.optionCount) {
                  optionText = [];
                  for (let i = 0; i < item.optionCount; i++) {
                    optionText.push(String.fromCharCode(i + 65).toUpperCase());
                  }
                } else {
                  optionText = [...answerSet].sort().map((option, oIndex) => {
                    return option;
                  });
                }
              } else {
                optionText = content.optionText.map((option, oIndex) => {
                  return String.fromCharCode(oIndex + 65).toUpperCase();
                });
              }
              if (!item.ansMap[optionText[0]]) {
                optionText.forEach(val => {
                  item.ansMap[val] = [];
                });
                if (quesType == 1) {
                  item.ansMap[`全对`] = [];
                } else {
                  item.ansMap[`多选`] = [];
                }
                item.ansMap[`未作答`] = [];
              }
              k = '';
              optionText.forEach(val => {
                if (option.includes(val)) {
                  k = val;
                  if (quesType == 1) {
                    if (!item.ansMap[k]) {
                      item.ansMap[k] = [];
                    }
                    if (stu[3] !== "0") {
                      item.ansMap[k].push({
                        ...stu2,
                        answer: stu[2],
                        score
                      });
                    }

                    if (score == fullScore) {
                      k = '全对';
                    } else {
                      k = '';
                    }
                  } else {
                    if (score == 0 && option.length > 1) {
                      if (!item.ansMap[k]) {
                        item.ansMap[k] = [];
                      }
                      // if (stu[3] !== "0") {
                      //   item.ansMap[k].push({
                      //     ...stu2,
                      //     answer: stu[2],
                      //     score
                      //   });
                      // }
                      k = '多选';
                    }
                  }
                }
              });
              if (score == 0 && option == '') {
                k = '未作答';
              }
            }
          }

          if ('3467'.indexOf(quesType) >= 0) {
            k = this.getScoreSeciton(k, fullScore, score, item);
          }
        }

        if (k === '') {
          continue;
        }
        if (!item.ansMap[k]) {
          item.ansMap[k] = [];
        }

        if (stu[3] !== "0") {
          item.ansMap[k].push({
            ...stu2,
            answer: stu[2],
            score: score,
          });
        }
      }

      if (item.quesType === 1) {
        // 多选题设置选择统计
        item.optionStusMap = this.getMChoiceOptionStatistics(item);
      }
    },

    /**
     * @description: 获取多选题的选项统计
     * @param {*} item
     * @return {*}
     */
    getMChoiceOptionStatistics({ rightAnswer, ansMap }) {
      let _optionStusMap = {};
      let scoreOptionMap = {};
      let noAnswerStus = [];
      Object.values(ansMap).forEach(group => {
        group.forEach(stu => {
          const key = stu.answer
          if (!key) {
            noAnswerStus.push(stu)
            return;
          }

          let optionStus = _optionStusMap[key];
          if (optionStus) {
            if (optionStus.some(it => it.id == stu.id)) return;
            optionStus.push(stu)
          } else {
            _optionStusMap[key] = [stu];
            let scoreOption = scoreOptionMap[stu.score] || "";
            scoreOptionMap[stu.score] = scoreOption + (scoreOption ? ',' : '') + key;
          }
        })
      });

      // 按分数排序，重新得到选项统计
      let optionStusMap = {};
      Object.keys(scoreOptionMap)
        .sort((a, b) => b - a)
        .forEach(score => {
          let ansStr = scoreOptionMap[score];
          const ansList = ansStr.split(',');
          ansList.forEach(ans => {
            optionStusMap[ans] = _optionStusMap[ans];
            delete _optionStusMap[ans];
          })
        });
      // 剩余0分需要额外补充
      optionStusMap = {
        ...optionStusMap,
        ..._optionStusMap
      }
      if (noAnswerStus.length) optionStusMap['未作答'] = noAnswerStus;

      return optionStusMap;
    },

    /**
    * @description: 获取分数区间
    * @param {string} k
    * @param {number} fullScore
    * @param {number} score
    * @param {any} item
    * @return {*}
    */
    getScoreSeciton(k, fullScore, score, item) {
      let scoreSplit = (item.scoreSplit && item.scoreSplit > 1) ? item.scoreSplit : 0.5;
      // 最小按0.5划分
      const isLowerSplit = scoreSplit === 0.5;
      // 智批改类型为7，返回正确、错误
      if (item.quesType == 7) {
        if (score == fullScore) {
          return '正确';
        } else {
          return '错误';
        }
      }

      if (fullScore > 0) {
        let addLabels = [];
        if (fullScore <= 1) {
          addLabels = isLowerSplit ? ['0分', `${fullScore}分}`] : [`[0,${fullScore}]`];

          k = addLabels[0];
        } else {
          let added = 0;
          while (added < fullScore) {
            let nextScore = added + scoreSplit;
            nextScore = nextScore >= fullScore ? fullScore : nextScore;
            let label = "";
            if (isLowerSplit) {
              label = `${added}分`;
              if (Number.isInteger(score)) {
                if (score === added) k = label;
              } else {
                // 如果分数为小数
                if (score > added && score <= nextScore) {
                  k = `${score}分`
                }
              }
            } else {
              if (added === 0) {
                label = `[${added},${nextScore}]`;
                if (score == 0) k = label;
              } else {
                label = `(${added},${nextScore}]`;
              }
              if (score > added && score <= nextScore) k = label;
            }
            addLabels.push(label);
            added = nextScore;
          }
        }

        if (isLowerSplit) {
          if (score == fullScore) {
            // k = '满分';
            k = `${fullScore}分`;
          }
          addLabels.push(`${fullScore}分`);
        }

        // 没有任何赋值说明当前是首次生成，需要倒序排列对象
        if (Object.values(item.ansMap).every((val) => !val.length)) {
          for (let i = addLabels.length - 1; i >= 0; i--) {
            const label = addLabels[i];
            item.ansMap[label] = [];
          }
        }
      } else {
        item.ansMap['0'] = item.ansMap['0'] || [];
        k = '0';
      }

      return k;
    },

    // 展开收起变式练习
    showVariant(item) {
      this.$set(item, 'showDetail', false);
      this.$set(item, 'variant', !item.variant);
      this.$set(item, 'ansDetail', false);
      this.$set(item, 'active', 'link_0');
      this.$nextTick(() => {
        this.$katexUpdate();
      });
    },

    // 收起变式练习
    hideVariant(item) {
      this.$set(item, 'variant', false);
      this.$set(item, 'active', 0);
      this.$nextTick(() => {
        this.$katexUpdate();
      });
    },

    //展开收起小题详情
    showSmallQuesDetails(item) {
      this.$set(item, 'variant', false);
      this.$set(item, 'ansDetail', false);
      this.$set(item, 'showDetail', !item.showDetail);
      this.$nextTick(() => {
        this.$katexUpdate();
      });
    },

    /**
     * @description: 获取已添加微课列表
     * @param {*} item
     * @return {*}
     */
    getAddedCourseList(qItem, tQuesNo, tag) {
      if (!qItem.courseList) return [];

      // 使用id匹配区分不同小题之间的微课
      let list = qItem.courseList.filter(item => {
        return (item.questionId == qItem.qId) || item.questionName == tQuesNo
      });
      return list;
    },

    getAddedCourseListCount(qItem, tQuesNo, tag) {
      return this.getAddedCourseList(qItem, tQuesNo, tag).length
    },

    // 打开资源弹窗
    openResourceDialog({ item, type }) {
      const { subjectId, classId } = this.filterData
      let subjectList = this.$sessionSave.get('innerNoRoleSubjectList');
      let subject = subjectList.find(item => item.id == subjectId);
      if (!subject) {
        this.$notify({
          title: "提示",
          message: "personalBookId缺失，考试信息错误！",
          type: 'error',
          offset: 100,
        })
        return;
      }

      const { personalBookId } = subject;
      let propsData = {
        loginInfo,
        pbookId: personalBookId,
        subjectId,
        qId: item.qId || item.quesId || uuid(),
        qNo: item.quesNumber,
        classId,
        qName: item.sortOrder || item.tQuesNo,
        qLevel: item.qId ? 1 : 0,
        courseList: this.getAddedCourseList(item, item.sortOrder || item.tQuesNo, 'openResourceDialog'),
        mode: type,
        qContent: typeof item.content === 'string' ? item : (item.content ? item.content[0] : null),
        // 是否仅仅添加微课
        onlyWeike: this.isDesktop || !item.content,
      }

      createDialog({
        title: type === 'add' ? '添加资源' : '查看资源',
        size: 'middle',
        appendToBody: true,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        customClass: 'el-dialog--globalVW',
        component: ResAddViewListComponent,
        store: this.$store,
        router: this.$router,
        width: '960px',
        data: propsData,
        close: () => {
          //关闭后触发
          console.log('dialog is closed');
        },
        change: ({ type, data }) => {
          if (type === "previewVideo") {
            this.defaultPlayerResource = data;
          } else if (type === 'addWK') {
            item.courseList = data;
            propsData.courseList = this.getAddedCourseList(item, item.sortOrder || item.tQuesNo, 'openResourceDialog');
            this.refreshKey++;
          } else if (type === 'deleteWK' || type === 'renameWK') {
            item.courseList = data;
            this.refreshKey++;
            propsData.courseList = this.getAddedCourseList(item, item.sortOrder || item.tQuesNo, 'openResourceDialog');
          }

        },
        confirm: (data) => {
          item.courseList = item.courseList.concat(data);
          this.refreshKey++;
        }
      });
    },

    /**
     * @description: 处理更新分数段，刷新统计结果
     * @param {*} item
     * @param {*} qsCate
     * @return {*}
     */
    handleUpdateScoreSplit(item, qsCate) {
      if (qsCate === 'small') {
        this.showSmallAnsDetail(item, true)
      } else {
        this.showAnsDetail(item, true)
      }
    },

    // 展开收起答题详情
    showSmallAnsDetail(item, forceShow) {
      this.$set(item, 'showDetail', false);
      this.$set(item, 'variant', false);
      this.$set(item, 'ansDetail', forceShow || !item.ansDetail);

      let judgeNames = ["正确", "错误"];
      if (this.questionGroupsFlat.length) {
        let findStuct = this.questionGroupsFlat.find(qItem => qItem.id === item.quesId);
        if (findStuct) {
          judgeNames = TFNameList[findStuct.judgeType]
        }
      }
      item.judgeNames = judgeNames;

      let fullScore = parseFloat(item.fullScore);
      item.ansMap = {};
      let k = '';
      let stus = JSON.parse(item.stus);
      stus.sort((a, b) => b[1] - a[1]);
      for (let i = 0; i < stus.length; i++) {
        let stu = stus[i];
        let id = stu[0],
          score = parseFloat(stu[1]);
        let option = stu.length > 2 ? stu[2] : '';

        let stu2 = this.stuList.find(q => q.id == id);

        if ('01258'.indexOf(item.type) >= 0 && (option === null || option === undefined)) {
          if (score == fullScore) {
            k = '正确';
          } else {
            k = '错误';
          }
        } else {
          if ('1258'.indexOf(item.type) >= 0) {
            if (item.type == 2 || stu.length <= 2) {
              if (!Object.keys(item.ansMap).length) {
                item.ansMap[judgeNames[0]] = [];
                item.ansMap[judgeNames[1]] = [];
              }

              k = '';
              switch (option) {
                case 'A':
                  k = judgeNames[0];
                  break;
                case 'B':
                  k = judgeNames[1];
                  break;
                case '':
                  k = "未选";
                  if (!item.ansMap[k]) item.ansMap[k] = [];
                  break;
                default:
                  k = "多选";
                  if (!item.ansMap[k]) item.ansMap[k] = [];
                  break;
              }
            } else if (stu.length > 2) {
              //选项
              let optionText = item.optionText.map((option, oIndex) => {
                return String.fromCharCode(oIndex + 65).toUpperCase();
              });
              if (!item.ansMap[optionText[0]]) {
                optionText.forEach(val => {
                  item.ansMap[val] = [];
                });
                if (item.type == 1) {
                  item.ansMap[`全对`] = [];
                } else {
                  item.ansMap[`多选`] = [];
                }
                item.ansMap[`未作答`] = [];
              }
              k = '';
              optionText.forEach(val => {
                if (option.indexOf(val) >= 0) {
                  k = val;
                  if (item.type == 1) {
                    if (!item.ansMap[k]) {
                      item.ansMap[k] = [];
                    }
                    if (stu[3] !== "0") {
                      item.ansMap[k].push({
                        ...stu2,
                        answer: stu[2],
                        score
                      });
                    }
                    if (score == fullScore) {
                      k = '全对';
                    } else {
                      k = '';
                    }
                  } else {
                    if (score == 0 && option.length > 1) {
                      if (!item.ansMap[k]) {
                        item.ansMap[k] = [];
                      }
                      // if (stu[3] !== "0") {
                      //   item.ansMap[k].push({
                      //     ...stu2,
                      //     answer: stu[2],
                      //     score
                      //   });
                      // }
                      k = '多选';
                    }
                  }
                }
              });
              if (score == 0 && option == '') {
                k = '未作答';
              }
            }
          }

          if ('3467'.indexOf(item.type) >= 0) {
            k = this.getScoreSeciton(k, fullScore, score, item);
          }
        }

        if (k === '') {
          continue;
        }
        if (!item.ansMap[k]) {
          item.ansMap[k] = [];
        }
        if (stu[3] !== "0") {
          item.ansMap[k].push({
            ...stu2,
            answer: stu[2],
            score: score,
          });
        }
      }

      if (item.quesType === 1) {
        // 多选题设置选择统计
        item.optionStusMap = this.getMChoiceOptionStatistics(item);
      }
    },

    // 获取班级得分率
    getBigQuesScoreRate(item, key) {
      if (!item.content[0].data.qs) return Number(item[key] * 100).toFixed(2);
      const smallQuesList = item.content[0].data.qs;
      // Number(small.classScoringRate * 100).toFixed(2)
      let sum = 0;
      smallQuesList.forEach(smallQuesItem => {
        sum = sum + Number(smallQuesItem[key]);
      });
      let avg = sum / smallQuesList.length;
      return (avg * 100).toFixed(2);
    },

    // 设置拆分索引
    setSplitTab(item, index) {
      item.splitIndex = index;
    },

    // 查看GGB
    lookGGB(ggbRes) {
      const { resourceUrl } = ggbRes;
      window.open(resourceUrl, '_blank');
    },
  },
};
</script>

<style lang="scss" scoped>
@import "src/styles/config";

.header-filter {
  padding-top: 20px;
  padding-left: 24px;
  padding-right: 24px;
}

.router-view {
  padding: 24px;
  padding-top: 0;
  height: 100%;
  // overflow: hidden;
  // overflow-y: auto;
}

.katex-display {
  font-size: 16px;
  display: inline-block;
  margin: 0;
}

.optionsClass {
  font-size: 16px;
}

.comment-container {
  .comment-ques {
    .ques-list {
      position: relative;
      border: 1px solid #e4e8eb;
      border-radius: 3px;
      margin-bottom: 20px;

      // &:before {
      //   content: attr(data-index);
      //   position: absolute;
      //   left: -1px;
      //   top: -1px;
      //   width: 32px;
      //   height: 38px;
      //   background: url("../../assets/flag_gray.png") center center no-repeat;
      //   font-size: 16px;
      //   color: #3f4a54ff;
      //   text-align: center;
      //   line-height: 30px;

      // }
      .ques-content {
        // min-height: 100px;
        padding: 40px 0 10px;
        padding-top: 0;
        padding-bottom: 0;
        line-height: 2em;
      }

      .question_content {
        padding-bottom: 0;
        border-bottom: 1px solid #e4e8eb;

        ::v-deep {
          u:not(.tb-wave) {
            text-decoration: none;
            min-width: 40px;
            height: auto;
            border-bottom: 1px solid;
            vertical-align: text-bottom;
          }
        }
      }

      .edit-block {
        width: 100%;
        // height: 48px;
        line-height: 48px;
        padding: 0 21px 0 11px;
        background: #f5f8fa;
        border-radius: 3px;
        font-size: 14px;
        color: #4e5668ff;
        overflow: hidden;

        .edit-btn {
          width: 80px;
          height: 32px;
          border-radius: 4px;
          padding: 0;
          line-height: 32px;
          color: #fff;
          margin-top: 8px !important;
          // margin-left: 20px;

          &.add {
            background: #409effff;
          }

          &.hasAdd {
            background-color: #fff;
            border: 1px solid #468fffff;
            color: #468fffff;
          }

          &.comment {
            background: #f5a033;
          }
        }

        .ques-btn {
          position: relative;
          padding-right: 18px;
          margin-right: 28px;
          cursor: pointer;

          i {
            font-size: 22px;
            position: absolute;
            top: 8px;
            right: -4px;

            &.el-icon-circle-plus {
              right: -3px;
              margin-top: 5px;
              font-size: 15px;
              color: #b6b8bfff;
            }
          }

          &.active {
            color: #409effff;

            i {
              top: 13px;
            }
          }

          .el-icon-caret-top {
            color: #409effff;
          }
        }

        .el-icon-caret-bottom {
          color: #b6b8bfff;
        }
      }

      .ques-detail {
        padding: 20px;
        overflow-x: auto;

        >div {
          margin-bottom: 10px;
        }

        .resourceList {
          margin-top: 10px;
          margin-bottom: 0;
        }

        .answer-imgList {
          display: inline-block;
          width: 85px;
          height: 50px;
          margin-right: 10px;
          cursor: pointer;
          margin-bottom: 10px;
          border: 1px solid #cecece;
        }

        .sub {
          span {
            line-height: 28px;
          }

          .ans-btn {
            width: 155px;
            min-width: 155px;
            height: 28px;
          }

          .last {
            width: calc(100% - 110px);
          }
        }

        .obj {
          span {
            line-height: 28px;
          }

          .ans-btn {
            width: 100px;
            min-width: 100px;
            height: 28px;
          }

          .last {
            width: calc(100% - 110px);
          }
        }

        .ans {
          display: flex;
          padding: 4px 0;

          .ans-btn {
            display: inline-block;
            border: 1px solid #ccc;
            line-height: 26px;
            text-align: center;
          }

          .blue {
            color: #409effff;
            padding-left: 10px;
            display: inline-block;
          }

          .item {
            padding-left: 10px;
            display: inline-block;
          }
        }
      }

      &.active,
      &:hover {
        border: 1px solid #409eff;

        &:before {
          background: url('../../assets/flag_active.png') center center no-repeat;
          color: #fff;
        }
      }
    }

    .ques-smart {
      text-indent: 1em;
      margin-bottom: 10px;
    }


    .ques-smart-answer {
      margin-top: 5px;
    }

    .empty-ques {
      padding-top: 30px;
    }
  }

  .small-ques-list {
    padding: 0 20px;

    .small-ques-content {
      position: relative;
      border: 1px solid #e4e8eb;
      border-radius: 3px;
      margin-bottom: 20px;

      &.active,
      &:hover {
        border: 1px solid #409eff;
      }
    }

    .small-edit-block {
      width: 100%;
      height: 38px;
      line-height: 38px;
      padding: 0 21px 0 11px;
      background: #f5f8fa;
      border-radius: 3px;
      font-size: 14px;
      color: #4e5668ff;

      .score-rate-display {
        font-weight: bold;

        &.level-0 {
          border-color: $level-color-0;
          color: $level-color-0;
        }

        &.level-1 {
          border-color: $level-color-1;
          color: $level-color-1;
        }

        &.level-2 {
          border-color: $level-color-2;
          color: $level-color-2;
        }

        &.level-3 {
          border-color: $level-color-3;
          color: $level-color-3;
        }
      }

      .edit-btn {
        width: 80px;
        height: 32px;
        border-radius: 4px;
        padding: 0;
        line-height: 32px;
        color: #fff;
        margin-top: 8px !important;
        margin-left: 20px;

        &.add {
          background: #409effff;
        }

        &.hasAdd {
          background-color: #fff;
          border: 1px solid #468fffff;
          color: #468fffff;
        }

        &.comment {
          background: #f5a033;
        }
      }

      .ques-btn {
        position: relative;
        padding-right: 18px;
        margin-right: 28px;
        cursor: pointer;

        i {
          font-size: 22px;
          position: absolute;
          top: 8px;
          right: -4px;

          &.el-icon-circle-plus {
            right: -3px;
            margin-top: 5px;
            font-size: 15px;
            color: #b6b8bfff;
          }
        }

        &.active {
          color: #409effff;

          i {
            top: 9px;
          }
        }

        .el-icon-caret-top {
          color: #409effff;
        }
      }

      .el-icon-caret-bottom {
        color: #b6b8bfff;
        // margin-top: 4px;
      }
    }
  }

  .comment-card {
    width: 290px;
    margin-bottom: 20px;
    margin-left: 20px;
    border: 1px solid #e4e8eb;
    border-radius: 3px;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: #fff;

    position: -webkit-sticky;
    position: sticky;
    top: 90px;
  }
}

.comment-page {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 111;
  background: #f1f5f8ff;
}

.question__detail {
  padding: 0px 20px;

  >div {
    margin: 10px 0;
    font-size: 14px;
  }
}

.split-tabs {
  .split-tab {
    display: inline-block;
    min-width: 50px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    border: 1px solid #ccc;
    margin: 0 10px;
    padding: 0 10px;
    cursor: pointer;

    &.active {
      background: #0396ff;
      color: #fff;
    }
  }
}

.ques-tag {
  position: absolute;
  top: 0;
  left: 0;
  padding: 0 10px;
  line-height: 18px;
  color: #fff;
  text-align: center;
  background: linear-gradient(135deg, #abdcff, #0396ff);
}

.question_body {
  padding-top: 20px;
  padding-bottom: 10px;
  overflow: auto;
}

.comment-outline {
  ::v-deep {
    .com-content {
      max-height: 55vh;
      overflow: auto;
    }
  }
}
</style>
<style lang="scss">
.katex-display {
  margin: 0;
}
</style>

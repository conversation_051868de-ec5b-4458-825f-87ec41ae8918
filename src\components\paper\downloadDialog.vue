<template>
  <el-dialog :title="`试卷下载 - ${downItem.tbName}`" custom-class="downDialog noselect" :visible.sync="showDialog"
    :before-close="handleClose" width="750px" :close-on-click-modal="!wattingDownload"
    :close-on-press-escape="!wattingDownload">
    <div :class="{ 'pointer-none': wattingDownload }" style="user-select: none;"
      v-if="canDownload && this.usedPaperTypeList.length">
      <template v-if="showFileTypeFilter">
        <p class="title">文件格式</p>
        <ul class="list-none set-ul paperSize-ul">
          <li :class="{ 'active': downOption.fileType === 'PDF' }" @click="onChangeFileType('PDF')"
            v-if="downItem.dlStatus == 1">PDF</li>
          <el-tooltip effect="dark" content="无可下载PDF" placement="top" v-else>
            <li class="disabled">PDF</li>
          </el-tooltip>
          <li :class="{ 'active': downOption.fileType === 'DOCX' }" @click="onChangeFileType('DOCX')">DOCX</li>
        </ul>
      </template>

      <p class="title">试卷类型</p>
      <ul class="list-none set-ul paperType-ul">
        <li v-for="(item, index) in usedPaperTypeList" :key="index"
          :class="{ active: item.typeId === downOption.paperType }" @click="paperTypeActive(item)">
          <div class="user">{{ item.user }}</div>
          <div class="type">{{ item.type }}</div>
        </li>
      </ul>

      <template v-if="curDownItem && curDownItem.limit.size">
        <p class="title">纸张大小</p>
        <ul class="list-none set-ul paperSize-ul">
          <li v-for="item in downOption.fileType === 'PDF' ? paperSizeListPdf : paperSizeListWord" :key="item.size"
            :class="{ active: item.size === downOption.paperSize }" @click="onChanagePaperSize(item)">
            {{ item.title }}
          </li>
        </ul>
      </template>
    </div>
    <div class="dialog-nores text-center" v-else-if="pageInited">
      <img src="../../assets/no-res.png" alt="" />
      暂无可下载文档
    </div>
    <div class="dialog-nores text-center" style="height: 260px;" v-else>
      <!-- <img src="../../assets/bank/loading_begin.gif" alt="" /> -->
      <!-- 空白位置 -->
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="downPaper" :loading="wattingDownload" :disabled="usedPaperTypeList.length == 0">
        {{ wattingDownload ? "正在生成文档..." : "下 载" }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { exportTestBank, getViewPaper, exportTestBankPDFByNodeAPI, addTbWordBuildTask, getTbWordBuildTaskDetails } from '@/service/testbank';
import { exportPaperAPI, examCard } from '@/service/pexam';
import { Loading } from '@iclass/element-ui';
import { downloadFile, getFileExtension, sleep } from '@/utils/index'

export default {
  name: 'download-dialog',
  props: ['downItem', 'paramsData'],
  data() {
    return {
      showDialog: true,
      fileTypes: ["PDF", "DOCX"],
      paperSizeListWord: [
        { size: 'A4', column: 1, title: "A4 (单栏)" },
        { size: 'B5', column: 1, title: "B5 (单栏)" },
        { size: 'A3', column: 2, title: "A3 (双栏)" },
      ],
      paperSizeListPdf: [
        { size: 'A4', column: 1, title: "A4 (单栏)" },
        { size: 'B5', column: 1, title: "B5 (单栏)" },
      ],
      paperTypeList: [
        {
          user: '学生用卷',
          type: '无答案',
          typeId: 'stu_paper_no_answer',
          limit: {
            size: 'A3,A4,B5',
            fileType: 'PDF,DOCX'
          },
        },
        {
          user: '教师用卷',
          type: '答案解析在每题后',
          typeId: 'tea_paper_ans_on_ques_after',
          limit: {
            size: 'A3,A4,B5',
            fileType: 'PDF,DOCX'
          },
        },
        {
          user: '普通用卷',
          type: '答案解析在卷尾',
          typeId: 'paper_ans_on_end',
          limit: {
            size: 'A3,A4,B5',
            fileType: 'PDF,DOCX'
          },
        },
        {
          user: '纯答案用卷',
          type: '只包含答案解析',
          typeId: 'ans_paper',
          limit: {
            size: 'A3,A4,B5',
            fileType: 'PDF,DOCX'
          },
        },
      ],
      // 可用试卷类型列表
      usedPaperTypeList: [],
      // 下载选项
      downOption: {
        fileType: 'PDF',
        paperType: 'stu_paper_no_answer',
        paperSize: 'A4',
        column: 1,
      },
      // 当前选择的下载纸张对象
      curDownItem: null,
      // 等待下载
      wattingDownload: false,

      examCanDownload: false,
      sourceActive: false,
      canDownload: false,
      cardInfo: null,
      fsUrl: process.env.VUE_APP_FS_URL,
      loginInfo: null,
      pageInited: false,
      // 开启word下载
      enableWordDownload: true
    };
  },
  computed: {
    /**
     * @description: 显示文件格式筛选
     * @return {*}
     */
    showFileTypeFilter() {
      return this.fileTypes.length > 1 && !(this.paperTypeList.length === 1 && this.paperTypeList[0].typeId === 'stu_exam_paper_no_answer')
    }
  },
  async mounted() {
    this.loginInfo = this.$sessionSave.get('loginInfo');
    this.checkEnableWord();
    await this.getViewPaper();
    this.examCanDownload = true;
    this.canDownload = this.downItem && this.downItem.isUse === 1;
    // if (this.canDownload) {
    //   this.getExamPaper();
    // }
    //上传试卷可下载原卷
    if (this.downItem.fileUrl && this.canDownload && this.downItem.cardType != 0) {
      this.paperTypeList.push({
        user: '原卷',
        type: '上传的word文档',
        typeId: 'origin_paper',
        limit: {
          size: '',
          fileType: !this.enableWordDownload ? 'PDF,DOCX' : 'DOCX'
        },
      });
      this.downOption.paperType = '';
    }
    //制卡完成，可下载扫描用卷
    if (this.downItem.paperNum != 0) {
      this.paperTypeList.unshift({
        user: '扫描答题卡',
        type: '有考号可采集数据',
        typeId: 'stu_exam_paper_no_answer',
        limit: {
          size: '',
          fileType: 'PDF'
        },
      });
      this.downOption.paperType = 'stu_exam_paper_no_answer';
    }
    //纯答题卡，拍改卡，仅可下载扫描用卷
    if (this.downItem.cardType == 0 || this.downItem.cardType == 3 || this.downItem.cardType == 5) {
      this.paperTypeList = [
        {
          user: '扫描答题卡',
          type: '有考号可采集数据',
          typeId: 'stu_exam_paper_no_answer',
          limit: {
            size: 'A4,A3',
            fileType: 'PDF'
          },
        },
      ];
      this.downOption.paperType = 'stu_exam_paper_no_answer';
    }

    if (this.downItem.dlStatus != 1) this.onChangeFileType('DOCX');
    //学科网组卷
    // if (this.downItem.source == 5) {
    //   this.paperTypeList.push({
    //     user: "word版",
    //     type: "答案解析在卷尾",
    //     typeId: "word_paper",
    //     limit: {
    //       size: "A4,A3,B5",
    //     },
    //   });
    //   this.downOption.paperType = "word_paper";
    // }

    this.updateUsedPaperTypeList();
    if (!this.curDownItem) {
      // 如果没有对应试卷类型，设置默认
      this.paperTypeActive(this.usedPaperTypeList[0]);
    }
    this.pageInited = true;
  },
  methods: {
    checkEnableWord() {
      let authList = this.$sessionSave.get(`config_auth_${this.loginInfo.id}`);
      if (authList) {
        let authFind = authList.find(it => it.dictCode == "127");
        if (authFind && authFind.state == "0") {
          this.enableWordDownload = false;
        }
      }
    },
    updateUsedPaperTypeList() {
      this.canDownload = this.downItem && this.downItem.isUse === 1;
      if (this.downItem.dlStatus != 1 && !this.cardInfo) {
        this.usedPaperTypeList = [];
        return;
      }

      if (this.downItem.fileUrl && !this.canDownload) {
        this.downOption.paperType = '';
        this.usedPaperTypeList = (this.paperTypeList = [
          {
            user: '原卷',
            type: '上传的word文档',
            typeId: 'origin_paper',
            limit: {
              size: '',
              fileType: !this.enableWordDownload ? 'PDF,DOCX' : 'DOCX'
            },
          },
        ]);
      }

      let paperTypeId = this.downOption.paperType;
      this.usedPaperTypeList = this.paperTypeList.filter(item => {
        return this.canDownload
          && this.examCanDownload
          && item.limit.fileType.includes(this.downOption.fileType)
          && (!item.limit.size || item.limit.size.includes(this.downOption.paperSize))
      });

      if (!this.enableWordDownload) {
        this.fileTypes = ["PDF"]
        this.downOption.fileType = "PDF"
        this.downOption.paperSize = "A4"
      }
    },
    /**
     * @description: 切换下载文件类型
     * @return {*}
     */
    onChangeFileType(value) {
      this.downOption.fileType = value;
      this.downOption.paperSize = "A4"
      this.updateUsedPaperTypeList();
      if (this.usedPaperTypeList.length && !this.usedPaperTypeList.some(it => it.typeId == this.downOption.paperType)) {
        this.downOption.paperType = this.usedPaperTypeList[0].typeId;
      }
    },

    /**
     * @description: 切换纸张尺寸
     * @param {*} item
     * @return {*}
     */
    onChanagePaperSize(item) {
      this.downOption.paperSize = item.size;
      this.downOption.column = item.column;
    },

    /**
     * @name:获取试卷信息
     */
    async getViewPaper() {
      let res = await getViewPaper({
        id: this.downItem.id,
      })

      if (res.data.cardInfo != '') {
        this.cardInfo = JSON.parse(res.data.cardInfo);
      }
    },
    sourceDownloadActive() {
      this.sourceActive = true;
      this.downOption.paperType = '';
    },
    paperTypeActive(item) {
      if (!item) return;

      this.curDownItem = this.$deepClone(item);
      this.downOption.paperType = item.typeId;
      this.sourceActive = false;
    },
    getExamPaper() {
      const params = {
        schoolId: this.downItem.schoolId,
        sourceId: this.downItem.id,
        title: this.downItem.tbName,
        paperType: 'A4',
        exportType: 'stu_exam_paper_no_answer',
      };
      exportPaperAPI(params).then(res => {
        if (
          this.$sessionSave.get('loginInfo') &&
          this.$sessionSave.get('loginInfo').user_type === 5
        ) {
          return;
        }
        if (res.code == 1 && res.data) {
          this.examCanDownload = true;
          this.downOption.paperType = 'stu_exam_paper_no_answer';
        }
      });
    },
    // 点击弹窗中下载
    async downPaper() {
      this.wattingDownload = true;
      let fileTypeItem = this.paperTypeList.find(it => it.typeId === this.downOption.paperType);
      let downFileName = `${this.downItem.tbName}【${fileTypeItem.user}】`;
      let downFileType = this.downOption.paperType !== 'origin_paper' ? this.downOption.fileType : 'ORIGIN';
      let loading;

      try {
        if (downFileType === 'PDF') {
          // 下载PDF文档

          downFileName = `${downFileName}.pdf`;
          if (this.downOption.paperType == 'stu_exam_paper_no_answer') {
            loading = Loading.service({
              lock: true,
              text: '试卷下载中',
              background: 'rgba(0, 0, 0, 0.7)',
            });
            const res = await examCard({
              paperId: this.downItem.paperNo,
              type: this.cardInfo.pageLayout == 1 ? 'A4' : 'A3',
              // isSend: 1,
            });
            if (res.code == 1) {
              this.base64toFile(res.data, downFileName);
            } else {
              throw ({ msg: "下载失败！" })
            }
            loading.close();
          }
          // else if (this.downOption.paperType == "word_paper") {
          //   let params = {
          //     schoolId: this.downItem.schoolId,
          //     id: this.downItem.id,
          //     type: 2, //1: 在线作业(id对应workId) 2: 大精组卷(id对应tbId/testBankId)
          //   };
          //   const res = await getXkwPaperDownloadUrl(params);
          // }
          else {
            let downloadUrl = await this.exportTestBank();
            let time = Date.now();
            if (downloadUrl) {
              let downUrl = `${downloadUrl}?response-content-type=application%2Foctet-stream&time=${time}`
              setTimeout(async() => {
                await downloadFile(downUrl, downFileName);
              }, 1000);
            } else {
              throw ({ msg: "下载失败！" })
            }
          }
        }
        else if (downFileType === 'DOCX') {
          // 下载DOCX文档

          downFileName = `${downFileName}.docx`;
          const downParams = {
            tbId: this.downItem.id,
            paperType: this.downOption.paperSize,
            exportType: this.downOption.paperType,
            columnCount: this.downOption.column,
          }
          // 建立下载任务
          let res = await addTbWordBuildTask({
            ...downParams,
            optUserId: this.loginInfo.id
          })
          if (res.code != 1) throw ({ msg: "下载失败！" });

          let downUrl = await this.getWordDownloadUrl(downParams);

          // 获取下载任务进度
          loading = Loading.service({
            lock: true,
            text: '试卷下载中',
            background: 'rgba(0, 0, 0, 0.7)',
          });
          await downloadFile(downUrl, downFileName);
          loading.close();
        } else if (downFileType === "ORIGIN") {
          // 下载原卷
          let ext = getFileExtension(this.downItem.fileUrl);
          downFileName = `${downFileName}.${ext}`;
          await downloadFile(this.downItem.fileUrl, downFileName);
        }
      } catch (error) {
        const errMsg = downFileName + " " + (error.msg || error)
        this.$notify.error({
          title: '下载错误',
          // position: 'top-left',
          offset: 100,
          message: errMsg
        });
        this.wattingDownload = false;
        console.warn(errMsg)
        return;
      } finally {
        loading && loading.close();
      }

      this.$notify({
        title: '下载成功',
        offset: 100,
        message: downFileName,
        // position: 'top-left',
        type: 'success'
      });
      await sleep(500);

      this.wattingDownload = false;
      this.closeDialog();
    },

    /**
     * @description: 获取word文档下载链接
     * @return {*}
     */
    getWordDownloadUrl(downParams) {
      return new Promise(async (resolve, reject) => {
        if (!this.showDialog) return;

        let res = await getTbWordBuildTaskDetails(downParams)
        if (res.code != 1) {
          reject({ msg: "下载失败！" })
          return
        };

        let data = res.data;
        console.log(`轮询：获取 ${this.downItem.tbName} 下载进度... state: ${data.state}`)
        if (data.state == 3) {
          reject({ msg: "DOCX文档生成失败！" })
          return
        }

        if (data.state != 2) {
          await sleep(2000);
          resolve(this.getWordDownloadUrl(downParams))
          return;
        }

        resolve(data.url);
      })
    },

    /**
     * @description: base64转文件
     * @param {*} base64String
     * @param {*} fileName
     * @return {*}
     */
    base64toFile(base64String, fileName) {
      // 去除编码前缀
      const byteCharacters = atob(base64String.substring(base64String.indexOf(',') + 1));
      const byteArrays = [];
      // 将每个字符转换为二进制数据
      for (let i = 0; i < byteCharacters.length; i++) {
        byteArrays.push(byteCharacters.charCodeAt(i));
      }
      // 创建Blob对象
      const blob = new Blob([new Uint8Array(byteArrays)], {
        type: 'application/octet-stream',
      });
      // 创建下载链接
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.download = fileName;
      // 模拟点击下载链接
      link.click();
    },
    async exportTestBank() {
      let downloadUrl = '';
      await exportTestBankPDFByNodeAPI({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        id: this.downItem.id || this.paramsData.tbId || '',
        size: this.downOption.paperSize,
        type: this.downOption.paperType,
        sourceType: 2,
        title: this.downItem.tbName,
      }).then(response => response.json())
        .then(data => {
          downloadUrl = data;
        })
        .catch(err => {
          this.$message.error('导出失败');
          console.log(err);
        });
      return downloadUrl;
    },
    closeDialog() {
      this.showDialog = false;
      this.$emit('close');
    },

    handleClose(done) {
      if (this.wattingDownload) {
        this.$confirm('取消下载文件，确认关闭？')
          .then(_ => {
            this.closeDialog();
            done();
          })
          .catch(_ => { });

        return
      }

      this.closeDialog();
      done();
    }
  },
};
</script>

<style lang="scss" scoped>
.dialog-nores {
  >img {
    display: block;
    margin: 0 auto 10px;
  }
}

.downDialog {
  .title {
    font-size: 16px;
    height: 40px;
    line-height: 40px;
  }

  .set-ul {
    margin-bottom: 0;

    >li {
      display: inline-block;
      font-size: 16px;
      text-align: center;
      background-color: #f4f4f5;
      color: #909399;
      border: 1px solid #f4f4f5;
      border-radius: 4px;
      margin-right: 30px;
      padding: 0 10px;
      cursor: pointer;

      &.active {
        color: #409eff;
        background: #ecf5ff;
        border: 1px solid #b3d8ff;
      }
    }
  }

  .paperSize-ul {
    >li {
      min-width: 90px;
      height: 40px;
      line-height: 40px;
      margin-bottom: 20px;

      &.disabled {
        cursor: default;
        opacity: .3;
      }
    }
  }

  .paperType-ul {
    >li {
      width: 145px;
      height: 60px;
      line-height: 60px;
      padding: 10px 0;
      text-align: center;
      margin-bottom: 20px;

      &:last-child {
        margin-right: 0;
      }

      .user {
        line-height: 20px;
        color: #3f4a54;
      }

      .type {
        line-height: 16px;
        font-size: 13px;
        margin-top: 3px;
      }

      &.active {
        .user {
          color: #409eff;
        }

        .type {
          color: #909399;
        }
      }
    }
  }
}

.baseMessage {
  font-family: Microsoft YaHei;
  font-size: 14px;

  .blockSpan {
    display: inline-block;
    min-width: 70px;
    height: 38px;
    padding: 0px 10px;
    border-radius: 4px;
    text-align: center;
    line-height: 38px;
    margin-right: 10px;
    cursor: pointer;
  }

  .subject-block {
    height: 38px;
    background: #fff;
    border: 1px solid #e4e7eb;
    color: #3f4a54;
    cursor: default;
  }

  .grade-block,
  .category-block {
    color: #3f4a54;

    &.active {
      background: #409eff;
      color: #fff;
    }
  }
}
</style>

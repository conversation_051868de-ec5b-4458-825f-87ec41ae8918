<template>
  <div>
    <el-radio-group
      v-model="activeTab"
      class="radioGroup"
      @change="changeRadioTab"
    >
      <el-radio-button
        :label="item.id"
        v-for="item in tabList"
        :key="item.id"
        >{{ item.name }}</el-radio-button
      >
    </el-radio-group>
    <div
      class="teachingPage-header"
      v-if="
        $route.path.indexOf('classDetail') < 0 &&
        $route.path.indexOf('previewPaper') < 0 &&
        activeTab != 3
      "
    >
      <div>
        <!--角色-->
        <div class="header__select" v-if="accountType != 5">
          <span class="select__label">角色：</span>
          <el-select
            v-model="roles"
            @change="changeRoles"
            :collapse-tags="false"
            style="width: 120px"
            class="source-select"
            placeholder="请选择"
          >
            <el-option
              v-for="item in roleList"
              :key="item.types"
              :label="item.status"
              :value="item.types"
            >
            </el-option>
          </el-select>
        </div>
        <!--来源-->
        <div class="header__select" v-show="activeTab">
          <span class="select__label">来源：</span>
          <el-select
            v-model="sourceVal"
            multiple
            @change="changeSource"
            :collapse-tags="false"
            style="width: 220px"
            class="source-select"
            placeholder="请选择"
          >
            <el-option
              v-for="item in sourceList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <!--时间-->
        <div class="header__select" v-show="activeTab">
          <span class="select__label">时间：</span>
          <el-select
            v-model="timeValue"
            style="width: 100px; margin-right: 25px"
            @change="changeTimeValue()"
          >
            <el-option
              v-for="item in timeType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <!-- 学年 -->
          <el-select
            v-model="yearTimeSlot"
            v-if="this.timeValue === 3"
            style="width: 250px; margin-right: 10px"
          >
            <el-option
              v-for="item in yearList"
              :key="item.id"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <!-- 按月 -->
          <el-date-picker
            v-if="this.timeValue === 2"
            popper-class="datePicker__time"
            style="margin-right: 10px; width: 250px"
            v-model="timeSlot"
            :clearable="false"
            type="monthrange"
            align="right"
            range-separator="--"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            @change="beforeFindPersonalBookListMonth"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
          <!-- 按日 -->
          <el-date-picker
            v-if="this.timeValue === 1"
            popper-class="datePicker__time"
            style="margin-right: 10px; width: 250px"
            v-model="timeSlot"
            :clearable="false"
            type="daterange"
            align="right"
            unlink-panels
            format="yyyy-MM-dd"
            range-separator=" -- "
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptionsDay"
          >
          </el-date-picker>
        </div>
        <!--学科-->
        <div class="header__select" v-if="activeTab != 0">
          <span class="select__label">学科：</span>
          <el-select
            v-model="subjectVal"
            style="width: 140px"
            class="source-select"
            @change="changeSubject"
            placeholder="请选择"
          >
            <el-option
              v-for="item in subjectList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <!-- 学情追踪学科 -->
        <div class="header__select" v-else>
          <span class="select__label">学科：</span>
          <el-select
            v-model="subjectTrack"
            style="width: 140px"
            class="source-select"
            @change="changeTrackSubject"
            placeholder="请选择"
          >
            <el-option
              v-for="item in subjectListTrack"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <!-- 年级 -->
        <div class="header__select" v-show="activeTab">
          <span class="select__label">年级：</span>
          <el-select
            v-model="gradeValue"
            style="width: 120px"
            class="source-select"
            @change="changeGrade"
            placeholder="请选择"
          >
            <el-option
              v-for="item in grdList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <!-- 班级 -->
        <div class="header__select" v-show="activeTab">
          <span class="select__label">班级：</span>
          <el-select
            v-model="classValue"
            style="width: 120px"
            class="source-select"
            @change="changeClass"
            placeholder="请选择"
          >
            <el-option
              v-for="item in classList"
              :key="item.id"
              :label="item.class_name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>

        <el-button type="primary" @click="findData">查询</el-button>
        <el-button
          v-if="!activeTab"
          type="primary"
          @click="openFilterExamDialog"
          >筛选考试</el-button
        >
        <span v-if="!activeTab" style="margin-left: 20px"
          >当前展示&nbsp;
          <span style="color: black; font-weight: bold">{{ gradeName }}</span
          >&nbsp;&nbsp;年级考试学情追踪数据，可通过筛选考试查看更多</span
        >
      </div>

      <div v-show="!activeTab && downloadExamIds && downloadExamIds.length > 0">
        <el-button class="export-button" type="text" @click="downloadReport"
          >一键导出<i class="el-icon-download el-icon--right"></i
        ></el-button>
      </div>
    </div>
    <filter-exam-dialog
      v-if="isFilterExamDialog"
      @close-dialog="closeFilterExamDialog"
      @get-exam-ids="getFilterExamIds"
    >
    </filter-exam-dialog>
    <router-view
      @getParams="getParams"
      ref="teachingRef"
      style="width: 100%"
      :subjectId="subjectVal"
      @updateData="findData"
    ></router-view>
  </div>
</template>

<script>
import {
  getUserInfoToPersonalityTest,
  classList,
  getUserRoles,
  getSubjectListByRole,
  getGradeListByRole,
} from "@/service/api";
import { mapState } from "vuex";
import filterExamDialog from "../../components/scoreDistribution/filterExamDialog.vue";
import { getToken } from "@/service/auth";
import UserRole from "@/utils/UserRole";

// 当前日期
let today = new Date();
// 当前月
// 当前年
let nowYear = today.getYear();
nowYear += nowYear < 2000 ? 1900 : 0;
export default {
  components: { filterExamDialog },
  name: "index",
  data() {
    return {
      casckey: 1,
      //时间筛选类型
      timeType: [
        { value: 1, label: "按日" },
        { value: 2, label: "按月" },
        { value: 3, label: "学年" },
      ],
      timeValue: 2,
      //学年
      yearList: [],
      tabList: [
        { id: 0, name: "年级学情", path: "track" },
        { id: 1, name: "班级学情", path: "class" },
        { id: 2, name: "学生学情", path: "student" },
        { id: 3, name: "学生成绩", path: "score" },
      ],

      activeTab: 0,
      sourceVal: [1, 2, 3],
      sourceList: [
        { id: 1, name: "考试" },
        { id: 2, name: "个册" },
        { id: 3, name: "作业" },
      ],
      // 选择的具体时间数值
      defTimeSlot: [],
      defMonthSlot: [],
      timeSlot: [],
      yearTimeSlot: "",
      pickerMinDate: "", //第一次选中的时间
      pickerOptions: {
        //选择当前日期之前的时间
        onPick: (obj) => {
          this.pickerMinDate = new Date(obj.minDate).getTime();
        },
        //选择时间范围为一年
        disabledDate: (time) => {
          if (this.pickerMinDate) {
            const day1 = 364 * 24 * 3600 * 1000;
            let maxTime = this.pickerMinDate + day1;
            let minTime = this.pickerMinDate - day1;
            return (
              time.getTime() > Date.now() ||
              time.getTime() > maxTime ||
              time.getTime() < minTime
            );
          }
        },
      },
      pickerOptionsDay: {
        //选择当前日期之前的时间
        onPick: (obj) => {
          this.pickerMinDate = new Date(obj.minDate).getTime();
        },
        //可选择的时间范围为一年
        disabledDate: (time) => {
          if (this.pickerMinDate) {
            const day1 = 365 * 24 * 3600 * 1000;
            let maxTime = this.pickerMinDate + day1;
            let minTime = this.pickerMinDate - day1;
            return (
              time.getTime() > Date.now() ||
              time.getTime() > maxTime ||
              time.getTime() < minTime
            );
          }
        },
      },
      cascaderVal: [],
      cascAll: [],
      cascGrds: [],
      subjectList: [],
      grdList: [],
      allGradeList: [],
      subjectVal: "",
      gradeValue: "",
      classList: [],
      classValue: "",
      paramsData: {},
      account_type: "",
      pms: {},
      //角色列表
      roleList: [],
      roles: "",
      //是否打开筛选考试弹窗
      isFilterExamDialog: false,
      //已选择的对比考试ids
      compareExamIds: [],
      subjectTrack: "",
      subjectListTrack: [],
      //班级名
      className: "",
      //需要导出的考试id
      downloadExamIds: [],
      fsUrl: process.env.VUE_APP_KKLURL,
      gradeName: "",
      accountType: "", //账号类型
    };
  },
  watch: {
    yearTimeSlot: function (v) {
      this.timeSlot = v.split("|");
    },
    $route(to, from) {
      this.matchingRoutes();
    },
  },
  computed: {
    ...mapState(["teachParams"]),
    // 对选择后的时间格式化
    timeSlotFormat() {
      return [
        this.timeSlot[0] ? this.$formatDate(new Date(this.timeSlot[0])) : "",
        this.timeSlot[0] ? this.$formatDate(new Date(this.timeSlot[1])) : "",
      ];
    },
  },
  mounted() {
    if (this.teachParams.schoolId != this.$sessionSave.get("schoolInfo").id) {
      this.$store.commit("saveTeachParams", {});
    }
    this.downloadExamIds = this.$localSave.get(
      "compareExamIds_" + this.$sessionSave.get("schoolInfo").id
    );
    this.gradeName = this.$localSave.get(
      "compareExamGradeName_" + this.$sessionSave.get("schoolInfo").id
    );
    this.accountType = this.$sessionSave.get("loginInfo").user_type;
    this.initSchool(function () {
      let tv = this.teachParams.timeValue;
      if (tv) {
        this.timeValue = tv;
        if (this.teachParams.startTime) {
          this.timeSlot = [
            this.teachParams.startTime,
            this.teachParams.endTime,
          ];
        }
      }

      if (tv == 3) {
        this.yearTimeSlot = this.timeSlot.join("|");
      }

      if (this.teachParams.sourceVal) {
        this.sourceVal = this.teachParams.sourceVal;
      }

      let sjtId = this.teachParams.subjectId;

      if (this.subjectList && this.subjectList.length && sjtId) {
        this.subjectVal = Number(sjtId);

        let sjt = this.subjectList.find((q) => q.id == this.subjectVal);
        if (!sjt) {
          sjt = this.subjectList[0];
          this.subjectVal = sjt.id;
        }
        this.cascGrds = this.cascAll.filter((q) => q.phase == sjt.phaseId);
        this.cascaderVal = [
          this.cascGrds[0].value,
          this.cascGrds[0].children[0].value,
        ];
        this.casckey++;
      }

      if (this.teachParams.gradeId) {
        let grd = this.cascGrds.find(
          (q) => q.value == this.teachParams.gradeId
        );
        if (grd) {
          this.cascaderVal = [grd.value, grd.children[0].value];
          this.casckey++;
        }
      }
      this.findData();
    });
  },
  methods: {
    init() {
      let $this = this;
      let date = new Date(),
        y = date.getFullYear(),
        M = date.getMonth(),
        d = date.getDate();
      let yearList = [];
      for (let i = 0; i < 5; i++) {
        let y1 = y - i - 1;
        let y2 = y - i;
        if (Number(`${M + 1}${d < 10 ? `0${d}` : d}`) >= 810) {
          y1 = y - i;
          y2 = y - i + 1;
        }

        let max = `${y2}-08-09`;
        if (i == 0) {
          max = `${y}-${M < 9 ? `0${M + 1}` : M + 1}-${d < 10 ? `0${d}` : d}`;
        }

        yearList.push({
          id: i,
          value: `${y1}-08-10|${max}`,
          label: `${y1}-${y2}学年`,
        });
      }

      $this.yearList = yearList;

      $this.timeValue = 2;
      $this.defTimeSlot = [new Date(y, M), new Date(y, M, d)];
      // $this.defTimeSlot = [new Date(y, M, d - 7),new Date(y, M, d)];
      $this.timeSlot = $this.defTimeSlot;
      $this.defMonthSlot = [new Date(y, M), new Date(y, M, d)];
      if ($this.subjectList.length > 0) {
        $this.subjectVal = $this.subjectList[0].id;
        $this.subjectTrack = $this.subjectListTrack[0].id;
      }
    },
    /**
     * @name:获取用户角色列表
     */
    getUserRoles() {
      getUserRoles({
        schoolId: this.$sessionSave.get("schoolInfo").id,
        userId: this.$sessionSave.get("loginInfo").id,
      }).then((res) => {
        let rolesStatus = res.data.status.split(",");
        let roles = res.data.types.split(",");
        rolesStatus.forEach((item, index) => {
          this.roleList.push({
            types: roles[index],
            status: item,
          });
        });
        this.roles = this.roleList[0].types;
        this.getSubjectList();
        this.getGradeList();
      });
    },
    /**
     * @name:切换角色
     */
    changeRoles() {
      this.subjectListTrack = "";
      this.subjectVal = "";
      this.gradeValue = "";
      this.classList = [];
      this.classValue = "";
      this.getSubjectList();
      this.getGradeList();
    },
    /**
     * @name:获取角色对应学科
     */
    getSubjectList() {
      getSubjectListByRole({
        schoolId: this.$sessionSave.get("schoolInfo").id,
        userId: this.$sessionSave.get("loginInfo").id,
        userType: this.roles,
      })
        .then((res) => {
          res.data.forEach((item) => {
            this.$set(item, "id", item.subjectId);
            this.$set(item, "name", item.subjectName);
          });
          this.subjectList = res.data;
          this.subjectVal =
            this.subjectList.length > 0 ? this.subjectList[0].id : "";
          this.subjectListTrack = JSON.parse(JSON.stringify(res.data));
          this.subjectListTrack.unshift({
            id: "",
            name: "全部学科",
          });
          this.subjectTrack = this.subjectListTrack[0].id;
        })
        .catch((err) => {
          this.subjectList = [];
        });
    },
    /**
     * @name:获取角色对应年级
     */
    getGradeList() {
      getGradeListByRole({
        schoolId: this.$sessionSave.get("schoolInfo").id,
        userId: this.$sessionSave.get("loginInfo").id,
        userType: this.roles,
      })
        .then((res) => {
          res.data.forEach((item) => {
            this.$set(item, "id", item.gradeId);
            this.$set(item, "name", item.gradeName);
          });
          this.grdList = res.data;

          this.gradeValue = this.grdList[0].id;
          if (
            !this.$localSave.get(
              "compareExamGradeName_" + this.$sessionSave.get("schoolInfo").id
            )
          ) {
            this.gradeName = this.grdList[0].gradeName;
          } else {
            this.gradeName = this.$localSave.get(
              "compareExamGradeName_" + this.$sessionSave.get("schoolInfo").id
            );
          }
          this.$sessionSave.set("teachingpage_grdList", this.grdList);
          this.getClassList(this.grdList[0].year, this.grdList[0].phase);
        })
        .catch((err) => {
          this.grdList = [];
        });
    },
    //切换时间筛选类型，按日默认最近一周，按月默认当前月
    changeTimeValue() {
      let date = new Date(),
        y = date.getFullYear(),
        M = date.getMonth(),
        d = date.getDate();
      if (this.timeValue === 1) {
        this.timeSlot = [new Date(y, M, d - 7), new Date(y, M, d)];
      } else if (this.timeValue === 2) {
        this.timeSlot = this.defMonthSlot;
      } else {
        this.yearTimeSlot = this.yearList[0].value;
        this.timeSlot = this.yearTimeSlot.split("|");
      }
    },
    /**
     * @name：切换年级
     */
    changeGrade() {
      let info = this.grdList.filter((item) => {
        return this.gradeValue == item.id;
      })[0];
      this.getClassList(
        info.year,
        info.phaseId ? info.phaseId - 2 : info.phase
      );
    },
    /**
     * @name:切换班级
     */
    changeClass() {
      this.className = this.classList.filter((item) => {
        return item.id == this.classValue;
      })[0].class_name;
      this.findData();
    },
    /**
     * @name:获取班级
     * @param {*} year  年份
     * @param {*} phase 学段
     */
    getClassList(year, phase) {
      this.classValue = "";
      this.className = "";
      classList(year, phase).then((data) => {
        this.classList = data.data.rows.filter((item) => {
          return item.student_count > 0;
        });
        if (this.classList.length > 0) {
          this.classValue = this.classList[0].id;
          this.className = this.classList[0].class_name;
        }
      });
    },
    /**
     * @name:导出
     */
    downloadReport() {
      let ids = this.$localSave.get(
        "compareExamIds_" + this.$sessionSave.get("schoolInfo").id
      );
      let token = getToken();
      let downloadUrl = `${this.fsUrl}/pexam/_/his/sheet?examIds=${ids.join(
        ","
      )}&token=${token}`;
      window.open(
        `${downloadUrl}?response-content-type=application%2Foctet-stream`,
        "_self"
      );
    },
    /**
     * @name:初始化数据
     */
    initSchool(cb) {
      let $this = this;
      this.matchingRoutes();

      if ($this.subjectList.length > 0) {
        if (cb) {
          cb.call($this);
        }
        return;
      }

      if (this.accountType == 5) {
        this.getSchoolAndGrade();
      } else {
        this.init();
        this.getUserRoles();
      }
    },
    async getSchoolAndGrade() {
      let $this = this;
      const ret = await UserRole.getUserInfoPersonalityTest();
      $this.subjectList = ret.userSubList;
      $this.subjectListTrack = JSON.parse(JSON.stringify(ret.userSubList));
      $this.subjectListTrack.unshift({
        id: "",
        name: "全部学科",
      });
      $this.allGradeList = ret.userGrdList;
      $this.grdList = ret.userGrdList;
      $this.$sessionSave.set("teachingpage_grdList", $this.grdList);
      if ($this.activeTab == 3) {
        $this.$refs.teachingRef &&
          $this.$refs.teachingRef.init &&
          $this.$refs.teachingRef.init();
      }
      $this.gradeValue = $this.grdList[0].id;
      if (
        !$this.$localSave.get(
          "compareExamGradeName_" + $this.$sessionSave.get("schoolInfo").id
        )
      ) {
        $this.gradeName = $this.grdList[0].name;
      } else {
        $this.gradeName = $this.$localSave.get(
          "compareExamGradeName_" + $this.$sessionSave.get("schoolInfo").id
        );
      }
      $this.getClassList($this.grdList[0].year, $this.grdList[0].phaseId - 2);
      let max = ret.userGrdList.length;
      $this.$sessionSave.set("cascGrds", ret.userGrdList);
      $this.cascAll = new Array(max);
      let m = 0;
      $this.init();
    },
    getParams(isSet) {
      if (
        this.$route.path != "/home/<USER>/class" &&
        this.teachParams.schoolId &&
        this.teachParams.subjectId
      ) {
        this.pms = this.teachParams;
      } else {
        this.pms = {
          schoolId: this.$sessionSave.get("schoolInfo").id,
          gradeId: this.gradeValue || "",
          classId: this.classValue || "",
          className: this.className || "",
          timeValue: this.timeValue,
          subjectId: this.subjectVal || "",
          subjectIdTrack: this.subjectTrack || "",
          startTime: this.timeSlotFormat[0],
          endTime: this.timeSlotFormat[1],
          sourceType: this.sourceVal.length
            ? this.sourceVal.length === 3
              ? ""
              : this.sourceVal.join(",")
            : "",
        };
      }
      if (isSet) {
        this.$store.commit("saveTeachParams", this.pms);
      }
      return this.pms;
    },
    // 切换学校
    changeSchool(type = "") {
      if (type) {
        this.subjectList = [];
        this.$store.commit("saveTeachParams", {});
        this.initSchool();
      }
    },
    matchingRoutes() {
      let localPath = this.$route.path;
      for (let item of this.tabList) {
        if (localPath.indexOf(item.path) !== -1) {
          this.activeTab = item.id;
          break;
        }
      }
      if (this.activeTab == 0) {
        //更新学情追踪数据
        this.subjectTrack = "";
        this.$bus.$emit("updateTrackData");
      }
    },
    // 切换班级学情和学生学情
    changeRadioTab(val) {
      this.$router.push({
        path: `/home/<USER>/${
          val == 0
            ? "track"
            : val == 1
            ? "class"
            : val == 2
            ? "student"
            : "score"
        }`,
      });
    },
    /**
     * @name:打开选择考试弹窗
     */
    openFilterExamDialog() {
      this.isFilterExamDialog = true;
    },
    /**
     * @name:关闭选择考试弹窗
     */
    closeFilterExamDialog() {
      this.isFilterExamDialog = false;
    },
    /**
     * @name:获取选择的考试Id
     */
    getFilterExamIds(data) {
      this.gradeName = this.$localSave.get(
        "compareExamGradeName_" + this.$sessionSave.get("schoolInfo").id
      );
      this.compareExamIds = data;
      this.$refs.teachingRef &&
        this.$refs.teachingRef.getTrackData &&
        this.$refs.teachingRef.getTrackData(data);
    },
    // 切换学科
    changeSubject(val) {
      if (this.accountType == 5) {
        let sjt = this.subjectList.find((q) => q.id == this.subjectVal);
        this.grdList = this.allGradeList.filter(
          (q) => q.phaseId == sjt.phaseId
        );
        this.gradeValue = this.grdList[0].id;
        this.getClassList(this.grdList[0].year, this.grdList[0].phaseId - 2);
      }
    },
    changeTrackSubject() {},
    // 切换年级、班级
    changeData(val) {
      this.findData();
    },
    // 查询数据
    findData(isChild) {
      if (this.activeTab && this.activeTa != 3) {
        if (!this.sourceVal || !this.sourceVal.length) {
          this.$message.error("请选择来源");
          this.emptyData();
          return;
        }
        if (!this.timeSlot) {
          this.$message.error("请选择查询时间范围");
          this.emptyData();
          return;
        }
      }
      this.$nextTick(() => {
        // 班级
        if (
          this.activeTab == 1 &&
          this.$route.path.indexOf("previewPaper") < 0
        ) {
          this.$refs.teachingRef &&
            this.$refs.teachingRef.listClsWeakPoint(
              "findData",
              this.initScore ? "initScore" : ""
            );
        }
        // 学生
        else if (
          this.activeTab == 2 ||
          this.$route.path.indexOf("previewPaper") > 0
        ) {
          this.$refs.teachingRef &&
            this.$refs.teachingRef.listClsStu &&
            this.$refs.teachingRef.listClsStu();
        } else if (this.activeTab == 3) {
          this.$refs.teachingRef &&
            this.$refs.teachingRef.init &&
            this.$refs.teachingRef.init();
        }
        //学情追踪
        else if (!this.activeTab) {
          let ids = this.$localSave.get(
            "compareExamIds_" + this.$sessionSave.get("schoolInfo").id
          );
          this.$refs.teachingRef &&
            this.$refs.teachingRef.getTrackData &&
            this.$refs.teachingRef.getTrackData(ids);
        }
      });
    },
    emptyData() {
      this.$nextTick(() => {
        this.$refs.teachingRef && this.$refs.teachingRef.emptyData();
      });
    },
    // 切换来源
    changeSource() {},
    // 让得分率恢复默认值全部
    initScoreRate() {
      this.$nextTick(() => {
        if (this.activeTab == 1) {
          this.$refs.teachingRef && this.$refs.teachingRef.initScoreRate();
        }
      });
    },
    // 选择月份筛选后
    beforeFindPersonalBookListMonth(val) {
      let now = new Date(),
        y = now.getFullYear(),
        M = now.getMonth(),
        d = now.getDate();

      let start = val[0],
        y0 = start.getFullYear(),
        M0 = start.getMonth();
      let end = val[1],
        y1 = end.getFullYear(),
        M1 = end.getMonth();

      if (
        Number(`${y}${M < 10 ? `0${M}` : M}`) ==
        Number(`${y1}${M1 < 10 ? `0${M1}` : M1}`)
      ) {
        this.timeSlot = [`${y0}-${M0 + 1}-01`, `${y1}-${M + 1}-${d}`];
      } else {
        let e2 = new Date(y1, M1 + 1, 0),
          y2 = e2.getFullYear(),
          M2 = e2.getMonth(),
          d2 = e2.getDate();

        this.timeSlot = [`${y0}-${M0 + 1}-01`, `${y2}-${M2 + 1}-${d2}`];
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.disFlex {
  display: flex;
  flex-direction: column;
}

.radioGroup {
  margin-bottom: 16px;
}

.teachingPage-header {
  position: relative;
  padding: 16px 40px 16px 28px;
  width: 100%;
  background: #fff;
  box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
  border-radius: 6px;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  .header__select {
    display: inline-block;
    margin-right: 20px;
  }
}
</style>

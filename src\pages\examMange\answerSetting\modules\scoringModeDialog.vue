<!--
 * @Description: 评分模式设置弹框
 * @Author: liuyue <EMAIL>
 * @Date: 2025-04-15 11:46:32
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-04-17 16:45:39
-->
<template>
  <el-dialog title="统分模式设置" :visible.sync="dialogVisible" width="400px" :close-on-click-modal="false"
    :append-to-body="true" @close="handleClose">
    <div class="scoring-mode-dialog">
      <el-radio v-model="mode" :label="0">加分制</el-radio>
      <el-radio v-model="mode" :label="1">减分制</el-radio>
      <template v-if="mode == 0">
        <div class="tip-text">题目实际得分 = 打分条分数总和</div>
        <div class="note-text">※切换统分模式后，成绩会按新的统分方式重新统计(仅针对打分条生效)，需重新发布成绩后生效</div>
      </template>
      <template v-if="mode == 1">
        <div class="tip-text">题目实际得分 = 题目总分 - 打分条分数总和</div>
        <div class="note-text">※切换统分模式后，成绩会按新的统分方式重新统计(仅针对打分条生效)，需重新发布成绩后生效</div>
      </template>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script lang="ts">
import { setScoringModeAPI, getScoringModeAPI } from '@/service/pexam';
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component
export default class ScoringModeDialog extends Vue {
  @Prop({ default: false }) dialogVisible!: boolean;
  @Prop({ default: '' }) workId: '';

  private mode: number = NaN;

  async created() {
    this.mode = await this.getScoringMode() || 0;
  }

  private readonly SCORING_ADD = 0;//加分制
  private readonly SCORING_SUB = 1;//减分制

  async getScoringMode() {
    try {
      const res = await getScoringModeAPI({ workId: this.workId });
      return res.data.scoringMode ?? this.SCORING_ADD;
    } catch (error) {
      console.error('获取评分模式失败:', error);
      this.$message.error('获取评分模式失败，将使用默认模式');
      return this.SCORING_ADD;
    }
  }

  async submitScoringMode() {
    let cardInfo = {
      workId: this.workId,
      scoringMode: this.mode,
    };
    await setScoringModeAPI(cardInfo)
  }

  handleClose() {
    this.$emit('close-dialog');
  }

  async handleConfirm() {
    await this.submitScoringMode();
    this.handleClose();
  }
}
</script>

<style lang="scss" scoped>
.scoring-mode-dialog {
  padding: 0 20px;

  .tip-text {
    margin-top: 15px;
    color: #666;
    font-size: 14px;
  }

  .note-text {
    margin-top: 10px;
    color: #999;
    font-size: 14px;
  }
}
</style>

<template>
  <div class="total-rank" v-loading="loading">
    <div class="total-rank-box">
      <div class="total-rank__title titleLine">{{ type == 'campus' ? '校区' : '' }}名次分析</div>
      <el-popover placement="bottom" width="370" trigger="click" popper-class="popover" v-model="isPopoverVisible">
        <div class="tip" v-if="tempCheckTypeList.length == 1">至少保留一项指标</div>

        <el-checkbox-group v-model="tempCheckTypeList">
          <el-checkbox
            :disabled="tempCheckTypeList.length == 1 && tempCheckTypeList.includes(item.value)"
            class="checkbox"
            v-for="item in typeList"
            :key="item.value"
            :label="item.value"
            >{{ item.label }}</el-checkbox
          >
        </el-checkbox-group>
        <div class="popover-footer">
          <el-button size="small" @click="isPopoverVisible = false">取消</el-button>
          <el-button type="primary" size="small" @click="handleCheckType">确定</el-button>
        </div>
        <el-button class="filtrate" slot="reference" type="text">指标筛选 <i class="el-icon-arrow-down"></i></el-button>
      </el-popover>

      <div class="total-rank__tool">
        <div>
          <el-radio-group v-model="rankType" @change="updateFilter">
            <el-radio-button :label="0">按名次分析</el-radio-button>
            <el-radio-button :label="1">按比例分析</el-radio-button>
          </el-radio-group>
          <!-- <span>
            显示累计：
            <el-switch v-model="isShowAdd"></el-switch>
          </span> -->
        </div>
        <el-button class="export-btn" type="primary" :disabled="statRankData.length == 0" @click="exportExcel"
          >导出</el-button
        >
      </div>

      <div v-if="statRankData.length">
        <el-table
          style="width: 100%"
          :data="statRankData"
          :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
          stripe
          border
          align="center"
          v-drag-table
          v-sticky-table="0"
        >
          <el-table-column
            :label="type == 'campus' ? '校区' : '班级'"
            prop="className"
            min-width="120"
            align="center"
            :resizable="false"
          ></el-table-column>
          <el-table-column
            label="实考人数"
            prop="num"
            min-width="120"
            align="center"
            :resizable="false"
          ></el-table-column>
          <el-table-column v-for="(item, index) in statRankColumn" :key="index" align="center" :resizable="false">
            <template slot="header" slot-scope="scope">
              <span v-if="rankType == 0">{{ `${item.open}-${item.close}` }}</span>
              <span v-if="rankType == 1">{{ index == 0 ? `前${item.close}%` : `前${item.open}%-${item.close}%` }}</span>
            </template>

            <el-table-column
              v-if="checkTypeList.includes('num')"
              label="本段人数"
              :prop="`items[${index}].num`"
              align="center"
              min-width="100"
              :resizable="false"
            >
            </el-table-column>
            <el-table-column
              label="本段比例"
              v-if="checkTypeList.includes('numRate')"
              :prop="`items[${index}].numRate`"
              align="center"
              min-width="100"
              :resizable="false"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.items[index].numRate }}%</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="checkTypeList.includes('avg')"
              label="本段均分"
              :prop="`items[${index}].avg`"
              align="center"
              min-width="100"
              :resizable="false"
            >
            </el-table-column>
            <el-table-column
              v-if="checkTypeList.includes('addNum')"
              label="累计人数"
              :prop="`items[${index}].addNum`"
              align="center"
              min-width="100"
              :resizable="false"
            >
            </el-table-column>
            <el-table-column
              v-if="checkTypeList.includes('addNumRate')"
              label="累计比例"
              :prop="`items[${index}].addNumRate`"
              align="center"
              min-width="100"
              :resizable="false"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.items[index].addNumRate }}%</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="checkTypeList.includes('addAvg')"
              label="累计均分"
              :prop="`items[${index}].addAvg`"
              align="center"
              min-width="100"
              :resizable="false"
            >
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>
      <no-data v-else></no-data>
    </div>

    <div class="total-rank-box">
      <div class="total-rank__title titleLine">{{ type == 'campus' ? '校区名次' : '' }}分段分析</div>
      <div class="total-rank__tool">
        <el-radio-group v-model="numType" @change="initNumChart">
          <el-radio-button :label="1">按{{ type == 'campus' ? '校区' : '班级' }}查看</el-radio-button>
          <el-radio-button :label="2">{{ rankType == 0 ? '按名次' : '按比例' }}查看</el-radio-button>
        </el-radio-group>
      </div>
      <div ref="numChartRef" style="width: 100%; height: 475px" v-show="statRankData.length"></div>
      <no-data v-show="!statRankData.length"></no-data>
    </div>

    <div class="total-rank-box">
      <div class="total-rank__title titleLine">{{ type == 'campus' ? '校区名次' : '' }}累计统计</div>
      <div class="total-rank__tool">
        <el-radio-group v-model="addType" @change="initAddChart">
          <el-radio-button :label="1">按{{ type == 'campus' ? '校区' : '班级' }}查看</el-radio-button>
          <el-radio-button :label="2">{{ rankType == 0 ? '按名次' : '按比例' }}查看</el-radio-button>
        </el-radio-group>
      </div>
      <div ref="addChartRef" style="width: 100%; height: 475px" v-show="statRankData.length"></div>
      <no-data v-show="!statRankData.length"></no-data>
    </div>
  </div>
</template>

<script lang="ts">
import NoData from '@/components/noData.vue';
import { getCampusStatRankAPI, statRankAPI } from '@/service/pexam';
import { indicatorManager } from '@/utils/examReportUtils';
import { getToRoles } from '@/utils/index';
import UserRole from '@/utils/UserRole';
import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator';

interface StatRank {
  subjectId: null;
  subjectName: null;
  classId: string;
  className: string;
  num: number;
  grdRank: number;
  items: Item[];
}

interface Item {
  open: number;
  close: number;
  num: number;
  numRate: number;
  addNum: number;
  addNumRate: number;
}

const typeList = indicatorManager.totalRankIndicatorList;

/** 类型 */
type RankType = 0 | 1;

/** 图表类型 */
type ChartType =
  /** 按班级查看 */
  | 1
  /** 按名次|比例查看 */
  | 2;

@Component({
  components: {
    NoData,
  },
})
export default class TotalRank extends Vue {
  @Prop() filterData;
  @Prop({ default: '' }) type: string;
  @Ref('numChartRef') numChartRef: HTMLDivElement;
  @Ref('addChartRef') addChartRef: HTMLDivElement;

  // 是否显示累计
  isShowAdd: boolean = true;
  // 名次类型 0按名次分析 1按比例分析
  rankType: RankType = 0;
  // 总名次数据
  statRankData: StatRank[] = [];
  // 总名次列
  statRankColumn: Item[] = [];

  // 分段统计类型
  numType: ChartType = 1;
  // 累计统计类型
  addType: ChartType = 1;

  // 指标筛选是否显示
  isPopoverVisible = false;
  // 指标列表
  typeList = typeList;
  // 指标筛选
  checkTypeList = typeList.map(item => item.value);
  // 指标筛选
  tempCheckTypeList = typeList.map(item => item.value);

  // 是否显示loading
  loading: boolean = false;

  @Watch('filterData', {
    deep: true,
  })
  filterDataHandler(newValue) {
    this.updateFilter();
  }

  @Watch('isPopoverVisible')
  isPopoverVisibleHandle(newValue) {
    if (!newValue) return;
    this.tempCheckTypeList = JSON.parse(JSON.stringify(this.checkTypeList));
  }

  mounted() {
    this.checkTypeList = indicatorManager.getIndicator('totalRank');
    this.updateFilter();
  }

  // 更新数据
  async updateFilter() {
    if (this.type == 'campus') {
      this.getCampusStatRank();
    } else {
      this.getStatRank();
    }
  }

  // 获取校区名次
  async getCampusStatRank() {
    this.loading = true;
    this.statRankData = [];
    this.statRankColumn = [];

    const res = await getCampusStatRankAPI({
      examId: this.$sessionSave.get('reportDetail').examId,
      subjectId: this.filterData.subjectId,
      type: this.rankType,
      qType: this.filterData.qType,
      v: this.$sessionSave.get('reportDetail').v,
    });
    if (res.data && res.data.length) {
      this.statRankData = res.data;
      this.statRankColumn = res.data[0].items;
    }
    this.loading = false;

    this.$nextTick(() => {
      this.initNumChart();
      this.initAddChart();
    });
  }

  // 获取总名次
  async getStatRank() {
    this.loading = true;
    this.statRankData = [];
    this.statRankColumn = [];

    const res = await statRankAPI({
      examId: this.$sessionSave.get('reportDetail').examId,
      subjectId: this.filterData.subjectId,
      type: this.rankType,
      qType: this.filterData.qType,
      v: this.$sessionSave.get('reportDetail').v,
    });
    if (res.data && res.data.length) {
      let classList = this.$sessionSave.get('innerClassList');
      this.statRankData = res.data.filter(item => classList.find(cls => cls.id === item.classId));
      this.statRankColumn = res.data[0].items;
    }
    this.loading = false;

    this.$nextTick(() => {
      this.initNumChart();
      this.initAddChart();
    });
  }

  // 初始化分段分析
  initNumChart() {
    if (!this.numChartRef) {
      return;
    }
    const chart = this.$echarts.init(this.numChartRef);

    let xAxisData = [];
    let series = [];

    if (this.numType == 1) {
      xAxisData = this.statRankData.map(item => item.className);
      series = this.statRankColumn.map((item, index) => {
        let name =
          this.rankType == 0
            ? `[${item.open}-${item.close}]`
            : index == 0
            ? `前${item.close}%`
            : `前${item.open}%-${item.close}%`;
        return {
          name: name,
          type: 'bar',
          data: this.statRankData.map(x => x.items[index].num),
          barMaxWidth: 20,
        };
      });
    } else if (this.numType == 2) {
      xAxisData = this.statRankColumn.map((item, index) => {
        let name =
          this.rankType == 0
            ? `[${item.open}-${item.close}]`
            : index == 0
            ? `前${item.close}%`
            : `前${item.open}%-${item.close}%`;
        return name;
      });
      series = this.statRankData.map(item => ({
        name: item.className,
        type: 'bar',
        data: item.items.map(x => x.num),
        barMaxWidth: 20,
      }));
    }

    chart.setOption(
      {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        legend: {
          type: 'scroll',
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '12%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
        },
        yAxis: {
          type: 'value',
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100,
          },
        ],
        series: series,
      },
      true
    );
  }

  // 初始化累计统计
  initAddChart() {
    if (!this.addChartRef) {
      return;
    }
    const chart = this.$echarts.init(this.addChartRef);
    let xAxisData = [];
    let series = [];

    if (this.addType == 1) {
      xAxisData = this.statRankData.map(item => item.className);
      series = this.statRankColumn.map((item, index) => {
        let name = this.rankType == 0 ? `前${item.close}名` : `前${item.close}%`;
        return {
          name,
          type: 'bar',
          data: this.statRankData.map(x => x.items[index].addNum),
          barMaxWidth: 20,
        };
      });
    } else if (this.addType == 2) {
      xAxisData = this.statRankColumn.map(item => {
        let name = this.rankType == 0 ? `前${item.close}名` : `前${item.close}%`;
        return name;
      });
      series = this.statRankData.map(item => ({
        name: item.className,
        type: 'bar',
        data: item.items.map(x => x.addNum),
        barMaxWidth: 20,
      }));
    }

    chart.setOption(
      {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        legend: {
          type: 'scroll',
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '12%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
        },
        yAxis: {
          type: 'value',
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100,
          },
        ],
        series: series,
      },
      true
    );
  }

  // 指标筛选
  handleCheckType() {
    this.checkTypeList = JSON.parse(JSON.stringify(this.tempCheckTypeList));
    this.isPopoverVisible = false;
    indicatorManager.setIndicator('totalRank', this.checkTypeList);
  }

  // 导出
  async exportExcel() {
    let role = '';
    if (!UserRole.isOperation) {
      const { year, campusCode } = this.$sessionSave.get('reportDetail');
      const map = await UserRole.utils.getRoleSubjectClassMap(year, campusCode, this.$sessionSave.get('reportType') == 'school' ? true : false);
      role = JSON.stringify(map);
    }

    const params = {
      examId: this.$sessionSave.get('reportDetail').examId,
      type: this.rankType,
      role: role,
      filter: this.checkTypeList.join(','),
      qType: this.filterData.qType,
      v: this.$sessionSave.get('reportDetail').v,
    } as any;
    const urlSearch = new URLSearchParams(params);
    const path = this.type == 'campus' ? '/pexam/_/exp-campus-stat-rank' : '/pexam/_/exp-stat-rank';
    let url = process.env.VUE_APP_KKLURL + path + `?${urlSearch.toString()}`;
    window.open(url);
  }
}
</script>

<style scoped lang="scss">
.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;
  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 15px;
  }
}

.total-rank {
  font-size: 14px;
}

.total-rank-box {
  margin-bottom: 20px;
}

.total-rank__tool {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  ::v-deep(.el-radio-group) {
    margin-right: 20px;
  }
}

.popover-footer {
  margin-top: 10px;
  text-align: right;
}

.tip {
  margin-bottom: 10px;
  color: red;
  font-size: 13px;
}

.filtrate {
  margin-left: 10px;
}
</style>

<!-- <template>
  <div class="total-rank-box">
    <div class="titleLine">总名次分析</div>
    <div class="table-box" v-loading="isLoading">
      <TotalRankTable :tableData="tableData" :targetData="targetData"></TotalRankTable>
    </div>
    <div class="chart-box" v-loading="isLoading" id="pane-chart">
      <TotalRankChart
        ref="totalRankChart"
        :tableData="tableData"
        :targetData="targetData"
      ></TotalRankChart>
    </div>
  </div>
</template>

<script>
import TotalRankChart from '@/components/totalRank/TotalRankChart.vue';
import TotalRankTable from '@/components/totalRank/TotalRankTable.vue';
import { getTotalRanking } from '@/service/pexam';

export default {
  name: 'totalRank',
  props: ['targetData', 'filterData'],
  components: {
    TotalRankChart,
    TotalRankTable,
  },
  data() {
    return {
      activeName: 'chart',
      isLoading: false,
      tableData: [],
      clsList: [],
    };
  },

  watch: {
    filterData: {
      deep: true,
      handler(newValue) {
        this.clsList = this.$sessionSave.get('innerClassList');
        this.getTotalRanking();
      },
    },
  },
  async mounted() {
    this.clsList = this.$sessionSave.get('innerClassList');
    await this.getTotalRanking();
  },
  methods: {
    // 总名次分布数据
    async getTotalRanking() {
      this.tableData = [];
      try {
        const data = await getTotalRanking({
          subjectId: this.filterData.subjectId,
          examId: this.$sessionSave.get('reportDetail').examId,
          qType: this.filterData.qType,
        });
        if (!data.data.length) {
          this.tableData = [];
          return;
        }
        var list = [];
        this.clsList.forEach(item => {
          var clz = data.data.find(x => x.classId == item.id);
          if (clz != null) {
            clz.classId = item.id;
            clz.className = item.class_name;
            list.push(clz);
          }
        });
        this.tableData = list;
      } catch (error) {
        console.log(error);
        this.tableData = [];
      }
    },
    updateData({ isLoading }) {
      // console.log('updatting', isLoading)
      this.isLoading = isLoading;
    },
    handleClick(tab, event) {
      if (tab.name == 'chart') {
        this.$nextTick(() => {
          this.$refs.totalRankChart.totalRankChart.resize();
          // this.$refs.fiveRateChart.fiveRateChart.resize();
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.total-rank-box {
  border-radius: 6px;
  padding-bottom: 30px;
  background-color: #fff;

  .chart-box {
    height: 475px;
    background: #ffffff;
    border: 1px solid #e4e8eb;
    border-radius: 3px;
    margin: 20px 0;
  }

  .table-box {
    border: 1px solid #e4e8eb;
    border-radius: 3px;
  }
}
.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;
  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 15px;
  }
}
</style>
<style lang="scss">
.total-rank-box {
  .el-tabs {
    .el-tabs__header {
      margin: 0 0 1px 4px;
    }
    .el-tabs__nav-wrap::after {
      width: 160px;
    }
    .el-tabs__item {
      width: 80px;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      &.is-top:last-child {
        padding-left: 0;
      }
      &.is-top:nth-child(2) {
        padding-right: 0;
      }
    }
  }
}
</style> -->

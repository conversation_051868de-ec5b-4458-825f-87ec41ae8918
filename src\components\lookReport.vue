<template>
  <div>
    <el-dialog
      custom-class="workbook-dialog"
      :visible="dialogFormVisible"
      width="800px"
      :before-close="handleClose"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
    >
      <div slot="title" class="dialog-title">
        <span style="margin-right: 10px">查看报告</span>
      </div>
      <div class="report-list">
        <div v-if="showMain" class="report-item" @click="lookReport(reportInfo)">
          <div class="type">系统报告</div>
          <div class="name" :title="reportInfo.examName">{{ reportInfo.examName }}</div>
        </div>
        <div
          class="report-item"
          @click="lookReport(item)"
          v-for="(item, i) in this.examList"
          :key="i"
          :title="getExamNamePrefix(item.source) + item.examName"
        >
          <template v-if="item.source == 101 || item.source == 102 || item.source == 103 || item.source == 105">
            <div class="type">系统报告</div>
            <div class="name">{{ getExamNamePrefix(item.source) }}{{ item.examName }}</div>
          </template>
          <template v-else>
            <div class="type custom-report">
              自定义报告
              <span class="close-icon" @click.stop="showDeleteConfirm(item)">✖</span>
            </div>
            <div class="name hover" :title="item.examName">{{ item.examName }}</div>
          </template>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getExamReportList, deleteExamInfo, getChildReportList } from '@/service/pexam';
import UserRole from '@/utils/UserRole';
export default {
  props: ['reportInfo'],
  data() {
    return {
      examList: [],
      dialogFormVisible: true,
      pagination: {
        page: 1,
        limit: 100,
        total_rows: 0,
      },
      // 报告中是否含有校区
      hasCampus: false,
    };
  },
  computed: {
    showMain() {
      if (this.hasCampus) {
        return UserRole.isOperation || UserRole.isSchoolLeader;
      }
      return true;
    },
  },
  mounted() {
    this.getExamList();
  },
  methods: {
    getExamNamePrefix(source) {
      if (source == 101) {
        return '【分层班】';
      }
      if (source == 102) {
        return '【行政班物理方向】';
      }
      if (source == 103) {
        return '【行政班历史方向】';
      }
      return '';
    },

    showDeleteConfirm(item) {
      this.$confirm('确定要删除这个报告吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.deleteReport(item);
        })
        .catch(() => {
          // 用户点击了取消，不做任何事情
        });
    },
    deleteReport(item) {
      deleteExamInfo({
        examId: item.examId,
      })
        .then(data => {
          this.$message({
            message: '删除成功！',
            type: 'success',
            duration: 2000,
          });
          this.getExamList();
        })
        .catch(err => {});
    },
    lookReport(data) {
      this.$sessionSave.set('reportParent', this.reportInfo);
      this.$emit('lookReport1', data);
    },
    handleClose(done) {
      this.$emit('closeDialog');
      this.dialogFormVisible = false;
      done();
    },
    /**
     * @name:获取考试报告列表
     */
    async getExamList() {
      const { examList, hasCampus } = await getChildReportList({
        examId: this.reportInfo.examId,
        examName: this.reportInfo.examName,
      });
      this.examList = examList;
      this.hasCampus = hasCampus;
    },

    // 自定义排序
    customSort(a, b) {
      const order = [105, 101, 102, 103];

      const indexA = order.indexOf(a.source);
      const indexB = order.indexOf(b.source);

      if (indexA !== -1 && indexB !== -1) {
        return indexA - indexB;
      }
      if (indexA !== -1) {
        return -1;
      }
      if (indexB !== -1) {
        return 1;
      }
      return 0;
    },
  },
};
</script>

<style lang="scss" scoped>
.report-list {
  height: 400px;
  overflow: auto;

  .report-item {
    height: 40px;
    display: flex;
    cursor: pointer;
    align-items: center;

    .custom-report {
      background: rgba(156, 134, 255, 0.1) !important;
      border: 1px solid #9c86ff !important;
      color: #9c86ff !important;
    }

    .close-icon {
      display: none;
      position: absolute;
      top: -10px;
      right: -7px;
      color: red;
    }

    .custom-report:hover .close-icon {
      display: block;
    }

    .type {
      position: relative;
      width: 100px;
      height: 32px;
      display: flex;
      background: rgba(64, 158, 255, 0.1);
      border: 1px solid #409eff;
      border-radius: 4px;
      color: #409eff;
      justify-content: center;
      align-items: center;
    }

    .name {
      width: calc(100% - 100px);
      padding-left: 25px;
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #3f4a54;
      line-height: 32px;

      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:hover {
        color: #409eff;
      }
    }

    .hover {
      &:hover {
        color: #9c86ff !important;
      }
    }
  }
}
</style>
<style lang="scss">
.workbook-dialog {
  .el-dialog__header {
    height: 45px;

    .dialog-title {
      line-height: 45px;
    }
  }

  .el-dialog__body {
    padding: 15px 20px 0px;
  }

  .el-form-item {
    margin-bottom: 15px;
  }

  .el-dialog__footer {
    padding: 10px 20px 15px;
  }
}

.subjectCheckBox {
  line-height: 26px;

  .el-checkbox {
    margin-right: 40px;

    &.el-checkbox + .el-checkbox {
      margin-left: 0;
    }
  }
}

.dialog-title {
  line-height: 54px;
}
</style>

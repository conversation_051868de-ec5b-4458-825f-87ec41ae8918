<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-11-05 11:13:58
 * @LastEditors: 小圆
-->
<template>
  <div class="statistic-scan clearfix" v-loading="isLoading">
    <el-table
      border
      :data="examStatDetailList"
      :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
      :cell-style="getCellStyle"
      :span-method="getSpanMethod"
      style="width: 100%; border: 1px solid #ebeef5"
      v-drag-table
      v-sticky-table="0"
    >
      <el-table-column prop="gradeName" label="年级" align="center" :min-width="80">
      </el-table-column>
      <el-table-column prop="subjectName" label="学科" align="center" :min-width="120">
      </el-table-column>
      <el-table-column
        prop="examName"
        label="测评名称"
        align="center"
        :min-width="150"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column prop="categoryName" label="类别" align="center" :min-width="120">
      </el-table-column>
      <el-table-column prop="sourceStr" label="阅卷方式" align="center" :min-width="100">
      </el-table-column>
      <el-table-column prop="cardTypeName" label="题卡" align="center" :min-width="100">
      </el-table-column>
      <el-table-column prop="examTime" label="测评时间" align="center" :min-width="130">
      </el-table-column>
      <el-table-column
        prop="className"
        label="班级"
        align="center"
        :min-width="120"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        prop="teaName"
        label="任教教师"
        align="center"
        :min-width="120"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column prop="submitNum" label="提交人数" align="center" :min-width="100">
      </el-table-column>
      <el-table-column prop="totalNum" label="总人数" align="center" :min-width="100">
      </el-table-column>
      <el-table-column prop="submitRate" label="提交率%" align="center" :min-width="90">
        <template #header>
          <span>提交率%</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="提交率=提交人数/总人数*100%，其中填涂缺考的计入提交人数"
            placement="top"
          >
            <i class="header-icon el-icon-question"></i>
          </el-tooltip>
        </template>
        <template #default="scope">
          <span>{{ scope.row.submitRate }}%</span>
        </template>
      </el-table-column>
      <el-table-column prop="scoreRate" label="得分率%" align="center" :min-width="90">
        <template #header>
          <span>得分率%</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="得分率=平均分/满分*100%，其中填涂缺考的不计入平均分统计"
            placement="top"
          >
            <i class="header-icon el-icon-question"></i>
          </el-tooltip>
        </template>
        <template #default="scope">
          <span>{{ scope.row.scoreRate }}%</span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-if="pagination.total > 0"
      background
      style="margin: 30px 0"
      class="text-center"
      layout="total, prev, pager, next, sizes"
      @current-change="changePage"
      @size-change="changeSize"
      :current-page.sync="pagination.page"
      :page-size="pagination.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="pagination.total"
    >
    </el-pagination>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { QueryData } from './types';
import { getExamStatDetails } from '@/service/pexam';
import moment from 'moment';
import NoData from '@/components/noData.vue';
import { getToken } from '@/service/auth';

export interface ExamStatDetail {
  gradeName: string;
  subjectName: string;
  examName: string;
  categoryName: string;
  cardType: number;
  cardTypeName: string;
  examTime: string;
  className: string;
  teaName: string;
  submitNum: number;
  totalNum: number;
  submitRate: number;
  submitRateStr: string;
  scoreRate: number;
  scoreRateStr: string;
  source: number;
  sourceStr: string;
  examId: number;
  subjectId: string;
}

interface TableSpanMethod {
  row: ExamStatDetail;
  column: any;
  rowIndex: number;
  columnIndex: number;
}

@Component({
  components: {
    NoData,
  },
})
export default class StatisticDetailExam extends Vue {
  @Prop({ default: {} }) queryData: QueryData;
  // 考试统计详情列表
  examStatDetailList: ExamStatDetail[] = [];
  // 分页器
  pagination = {
    page: 1,
    pageSize: 20,
    total: 0,
  };

  isLoading = false;

  mounted() {
    this.getExamStat();
    this.$bus.$on('statistic-change', this.onQueryDataChange);
    this.$bus.$on('handle-export', this.handleExport);
  }

  beforeDestroy() {
    this.$bus.$off('statistic-change', this.onQueryDataChange);
    this.$bus.$off('handle-export');
  }

  onQueryDataChange() {
    this.changePage(1);
  }

  async getExamStat() {
    try {
      this.isLoading = true;
      const res = await getExamStatDetails({
        ...this.getParams(),
        page: this.pagination.page,
        limit: this.pagination.pageSize,
      });
      let list = res.data.rows || [];

      list.forEach(item => {
        item.rowspan = 1;
        item.colspan = 1;
      });
      this.setTableSpan(list);
      this.examStatDetailList = list;
      this.pagination.total = res.data.total_rows;
    } catch (error) {
      console.error(error);
      this.examStatDetailList = [];
      this.pagination.total = 0;
    }
    this.isLoading = false;
  }

  // 设置表格跨栏数
  setTableSpan(list: ExamStatDetail[]) {
    function checkSameItem(item: ExamStatDetail, item2: ExamStatDetail) {
      return (
        item2.examId == item.examId &&
        item2.gradeName == item.gradeName &&
        item2.subjectName == item.subjectName &&
        item2.examName == item.examName &&
        item2.categoryName == item.categoryName &&
        item2.sourceStr == item.sourceStr &&
        item2.cardTypeName == item.cardTypeName &&
        item2.examTime == item.examTime
      );
    }

    let result: Array<ExamStatDetail[]> = []; // 连续相同的数据
    let temp: ExamStatDetail[] = [];

    // 获取连续相同数据
    for (let i = 0; i < list.length; i++) {
      if (i === 0 || checkSameItem(list[i], list[i - 1])) {
        temp.push(list[i]);
      } else {
        if (temp.length > 1) {
          result.push(temp);
        }
        temp = [list[i]];
      }
    }
    // 最后检查是否有剩余的连续元素
    if (temp.length > 1) {
      result.push(temp);
    }

    // 生成跨栏数
    for (let i = 0; i < result.length; i++) {
      let arr = result[i];

      arr.forEach((item, index) => {
        if (index == 0) {
          item.rowspan = arr.length;
        } else {
          item.rowspan = 0;
        }
      });
    }

    result = null;
    temp = null;
  }

  changePage(page = 1) {
    this.pagination.page = page;
    this.getExamStat();
  }

  changeSize(val) {
    this.pagination.page = 1;
    this.pagination.pageSize = val;
    this.getExamStat();
  }

  getParams() {
    let token = getToken();
    return {
      schoolId: this.$sessionSave.get('schoolInfo').id,
      subjectId: this.queryData.subjectValue,
      gradeId: this.queryData.gradeValue,
      categoryId: this.queryData.categoryValue,
      startTime: moment(this.queryData.dateRange[0]).format('YYYY-MM-DD 00:00:00'),
      endTime: moment(this.queryData.dateRange[1]).format('YYYY-MM-DD 23:59:59'),
      keyWord: '',
      token,
    };
  }

  handleExport() {
    if (!this.examStatDetailList.length) {
      this.$message.warning('没有数据可以导出');
      return;
    }

    const params = this.getParams();
    const query = new URLSearchParams(params);
    const url = `${
      process.env.VUE_APP_KKLURL
    }/pexam/scanExam/exportExamStatDetails?${query.toString()}`;
    window.open(url, '_blank');
  }

  getSpanMethod({ row, column, rowIndex, columnIndex }: TableSpanMethod) {
    if (
      column.property == 'gradeName' ||
      column.property == 'subjectName' ||
      column.property == 'examName' ||
      column.property == 'categoryName' ||
      column.property == 'sourceStr' ||
      column.property == 'cardTypeName' ||
      column.property == 'examTime'
    ) {
      return {
        rowspan: row.rowspan,
        colspan: row.colspan,
      };
    }
  }

  getCellStyle({ row, column, rowIndex, columnIndex }) {
    let redStyle = { color: 'red', backgroundColor: 'rgb(253 163 163 / 30%)' };
    if (
      (column.property == 'submitRate' && row.submitRate < 60) ||
      (column.property == 'scoreRate' && row.scoreRate < 60)
    ) {
      return redStyle;
    }
  }
}
</script>

<style scoped lang="scss">
.header-icon {
  margin-bottom: 0;
}
</style>

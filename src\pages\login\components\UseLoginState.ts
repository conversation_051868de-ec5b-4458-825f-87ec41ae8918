/*
 * @Description:
 * @Author: 小圆
 * @Date: 2025-05-30 10:43:42
 * @LastEditors: 小圆
 */
export default new (class UseForgetPwdState {
  // 等待时间
  waitTime: number = 0;
  // 是否是验证码
  hasPinCode: boolean = false;

  // 样例
  startTimer = () => {
    let hasPinCode = true;
    this.waitTime = 59;
    this.hasPinCode = hasPinCode;
    const Timer = setInterval(() => {
      this.waitTime--;
      if (this.waitTime <= 0) {
        this.hasPinCode = false;
        this.waitTime = 60;
        clearInterval(Timer);
      }
    }, 1000);
  };
})();

<template>
  <el-dialog title="导入临时考号" :visible.sync="dialogVisible" width="600px" :close-on-click-modal="false"
    :before-close="handleClose" custom-class="import-temp-dialog">
    <div class="dialog-content">
      <span class="warn-tip">导入临时考号将覆盖当前学生考号，仅对本次测评生效</span>
      <div class="form-item">
        <span class="label required">所属校区</span>
        <el-select v-model="selectedCampus" placeholder="校区" class="class-select">
          <el-option v-for="item in campusList" :key="item.code" :label="item.name" :value="item.code">
          </el-option>
        </el-select>
      </div>
      <div class="form-item">
        <span class="label">下载模板</span>
        <el-button type="primary" size="small" @click="downloadTemplate" class="template-btn">
          <i class="el-icon-download"></i>
          下载模板
        </el-button>
      </div>
      <div class="form-item upload-item">
        <span class="label required">导入文件</span>
        <el-upload class="upload-demo" action="" :auto-upload="false" :on-change="handleFileChange"
          :on-remove="handleFileRemove" :file-list="fileList" :limit="2" accept=".xlsx,.xls">
          <el-button type="primary" size="small">
            <i class="el-icon-upload2"></i>
            选择文件
          </el-button>
          <div slot="tip" class="el-upload__tip">
            <i class="el-icon-info"></i>
            只能上传xlsx/xls文件
          </div>
        </el-upload>
      </div>
      <div class="form-item err-tip" v-if="errTip || errFile">
        <span class="label">错误信息</span>
        <div>
          <span v-if="errTip">{{ errTip }}</span>
          <el-button type="danger" size="small" @click="downloadErrorFile" v-if="errFile">
            <i class="el-icon-download"></i>
            下载错误文件
          </el-button>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getCampusCodeList } from '@/service/api.js'
import { importTempStudentExamNoAPI } from '@/service/pexam.js'
import { downloadFileByIframe } from '@/utils/index';
export default {
  name: 'ImportTempStuDialog',
  data() {
    return {
      dialogVisible: true,
      campusList: [],
      selectedCampus: '',
      fileList: [],
      errTip: '',
      errFile: '',
      loading: false
    }
  },
  created() {
    this.getCampusList()
  },
  methods: {
    async getCampusList() {
      let params = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        page: 1,
        limit: 1000
      }
      const res = await getCampusCodeList(params);
      this.campusList = res.data.rows;
      this.selectedCampus = this.campusList[0].code;
    },
    handleClose() {
      this.dialogVisible = false
      this.$emit('close')
    },
    downloadTemplate() {
      this.$message.success('模板下载中...')
      let url = process.env.VUE_APP_KKLURL + '/pexam/scanExam/downloadStudentExamNoTpl';
      downloadFileByIframe(url);
    },
    handleFileChange(file) {
      this.errTip = '';
      this.errFile = '';
      const isExcel = file.raw.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.raw.type === 'application/vnd.ms-excel'
      if (!isExcel) {
        this.$message.error('只能上传Excel文件!')
        this.fileList = []
        return false
      }
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('文件大小不能超过 5MB!')
        this.fileList = []
        return false
      }
      this.fileList = [file]
    },
    handleFileRemove() {
      this.fileList = []
    },
    async importTempStudentExamNo() {
      let fileName = this.fileList[0].name;
      let param = new FormData();
      param.append('file', this.fileList[0].raw, fileName);
      param.append('examId', this.$route.query.examId);
      param.append('campusCode', this.selectedCampus);
      try {
        let res = await importTempStudentExamNoAPI(param);
        if (res.code === 1) {
          this.$message.success(res.msg);
          this.handleClose()
        } else if (res.code === 2) {
          this.errFile = `https://scanpaper.iclass30.com/${res.data.url}`;
        }
      } catch (err) {
        this.errTip = err.msg;
      }

    },
    downloadErrorFile() {
      window.open(this.errFile);
    },
    async handleConfirm() {
      if (!this.selectedCampus) {
        this.$message.warning('请选择所属校区')
        return
      }
      if (this.fileList.length === 0) {
        this.$message.warning('请选择要上传的文件')
        return
      }
      this.loading = true
      try {
        await this.importTempStudentExamNo();
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.import-temp-dialog {
  ::v-deep .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e4e7ed;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 500;
    }
  }

  .dialog-content {

    .warn-tip {
      display: block;
      color: #f56c6c;
      font-size: 13px;
      margin-bottom: 16px;
      padding: 8px 16px;
      background-color: #fef0f0;
      border-radius: 4px;
      border: 1px solid #fde2e2;

      &::before {
        content: "⚠";
        margin-right: 8px;
      }
    }

    .form-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 80px;
        line-height: 32px;
        color: #606266;

        &.required::before {
          content: '*';
          color: #f56c6c;
          margin-right: 4px;
        }
      }

      .select-input {
        width: 300px;
      }

      .template-btn {
        .el-icon-download {
          margin-right: 5px;
        }
      }

      &.upload-item {
        .upload-demo {
          margin-left: 0;

          ::v-deep .el-upload-list {
            margin-top: 10px;
            width: 400px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .el-icon-upload2 {
            margin-right: 5px;
          }

          .el-upload__tip {
            display: inline-block;
            color: #909399;
            font-size: 12px;
            margin: 8px;

            .el-icon-info {
              margin-right: 4px;
            }
          }
        }
      }

      &.err-tip {
        align-items: center;
        padding: 12px 16px;
        background-color: #fff2f0;
        border: 1px solid #ffccc7;
        border-radius: 4px;

        span {
          color: #ff4d4f;
          font-size: 14px;
          line-height: 1.5;
        }

        .el-button {
          .el-icon-download {
            margin-right: 5px;
          }
        }
      }
    }
  }

  ::v-deep .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #e4e7ed;
  }
}
</style>
<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-03-25 10:18:45
 * @LastEditors: 小圆
-->
<template>
  <div class="header-item">
    <span class="header-item__label">科目：</span>
    <el-select
      class="header-item__select short-select"
      value-key="id"
      :value="filterInfo.subjectInfo"
      @change="onChange"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <el-option
        v-for="item in selectOption.subjectList"
        :key="item.id"
        :title="item.name"
        :label="item.name"
        :value="item"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import ReportComponent from './ReportComponent';
import { IScoreSubjectInfo } from '@/pages/studyReport/plugins/types';

@Component
export default class ReportSubjectSelect extends ReportComponent {
  async onChange(value: IScoreSubjectInfo) {
    await this.FilterModule.setSubjectInfo(value);
    this.FilterModule.trigger('changeFilter');
  }
}
</script>

<style scoped lang="scss">
@import './reportSelect.scss';
</style>

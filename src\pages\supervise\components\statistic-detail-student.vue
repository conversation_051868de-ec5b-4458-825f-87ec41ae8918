<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-11-05 11:13:58
 * @LastEditors: 小圆
-->
<template>
  <div class="statistic-scan clearfix" v-loading="isLoading">
    <div>
      <el-select
        style="width: 130px; margin-right: 10px"
        v-model="classId"
        placeholder="请选择班级"
        @change="getExamStuStat"
      >
        <el-option
          v-for="item in classList"
          :key="item.id"
          :label="item.class_name"
          :value="item.id"
        >
        </el-option>
      </el-select>
      <el-input
        style="width: 220px"
        v-model.trim="keyWord"
        placeholder="请输入姓名搜索"
        @keyup.enter.native="getExamStuStat"
      >
        <i
          slot="suffix"
          class="input__icon el-input__icon el-icon-search"
          @click="getExamStuStat"
        ></i>
      </el-input>
    </div>

    <el-table
      stripe
      :data="examStuStatList"
      :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
      :header-cell-class-name="'table-sort-cell'"
      :default-sort="{ prop: 'examNum', order: 'descending' }"
      :cell-style="getCellStyle"
      style="margin-top: 10px; width: 100%; border: 1px solid #ebeef5"
      v-drag-table
      v-sticky-table="0"
      @sort-change="changeSort"
    >
      <el-table-column prop="no" label="序号" align="center"> </el-table-column>
      <el-table-column prop="stuName " label="姓名" align="center">
        <template #default="scope">
          <span>{{ scope.row.stuName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="examNum" label="测评数" align="center" sortable="custom">
      </el-table-column>
      <el-table-column prop="submitNum" label="提交数" align="center" sortable="custom">
      </el-table-column>
      <el-table-column prop="notSubmitNum" label="未交数" align="center" sortable="custom">
      </el-table-column>
      <el-table-column prop="submitRate" label="提交率" align="center" sortable="custom">
        <template #default="scope">
          <span>{{ scope.row.submitRate }}%</span>
        </template>
      </el-table-column>
      <el-table-column prop="goodNum" label="优秀次数" align="center" sortable="custom">
        <template #header="scope">
          <span>优秀次数</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="所选时间内，得分率在85%以上的作业次数"
            placement="top"
          >
            <i class="header-icon el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="unqualifiedNum" label="不合格次数" align="center" sortable="custom">
        <template #header="scope">
          <span>不合格次数</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="得所选时间内，得分率在60%以下的作业次数"
            placement="top"
          >
            <i class="header-icon el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="errorNum" label="收录错题数" align="center" sortable="custom">
        <template #header>
          <span>收录错题数</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="所选时间内，未得满分且收入错题本的题目数，其中未录入题目的不计入"
            placement="top"
          >
            <i class="header-icon el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="scan" label="操作" fixed="right" align="center">
        <template #default="scope">
          <el-button class="text-btn" type="text" @click="goDetails(scope.row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { QueryData } from './types';
import { classList } from '@/service/api';
import { getExamStuStatDetails } from '@/service/pexam';
import { getToken } from '@/service/auth';
import moment from 'moment';

export interface ExamStuStat {
  stuId: string;
  stuName: string;
  examNum: number;
  submitNum: number;
  notSubmitNum: number;
  submitRate: number;
  goodNum: number;
  unqualifiedNum: number;
  errorNum: number;
}

@Component
export default class StatisticDetailStudent extends Vue {
  @Prop({ default: {} }) queryData: QueryData;
  @Prop({ default: [] }) gradeList;
  // 班级列表
  classList = [];
  // 班级id
  classId = '';

  // 搜索
  keyWord = '';
  /** 0:按照练习数排序 1:按照提交数排序 2:按照未交数排序 3:按照提交率排序 4:按照优秀次数排序 5:按照不合格次数排序 6:错题数排序 */
  orderType = '0';
  /** 是否降序排序：0-否，1-是 */
  desc = '1';
  // 考试学生统计
  examStuStatList: ExamStuStat[] = [];

  // 年级ID
  tempGradeId = '';

  isLoading = false;

  async mounted() {
    this.tempGradeId = this.queryData.gradeValue;
    await this.getClassList();
    this.getExamStuStat();
    this.$bus.$on('statistic-change', this.onQueryDataChange);
    this.$bus.$on('handle-export', this.handleExport);
  }

  beforeDestroy() {
    this.$bus.$off('statistic-change', this.onQueryDataChange);
    this.$bus.$off('handle-export');
  }

  async onQueryDataChange(val: QueryData) {
    if (val.gradeValue !== this.tempGradeId) {
      await this.getClassList();
      this.tempGradeId = val.gradeValue;
    }
    this.getExamStuStat();
  }

  // 获取考试学生统计
  async getExamStuStat() {
    try {
      this.isLoading = true;
      const res = await getExamStuStatDetails(this.getParams());
      let list = res.data.rows || [];
      list.forEach((item, index) => {
        item.no = index + 1;
      });
      this.examStuStatList = res.data.rows;
    } catch (e) {
      console.error(e);
      this.examStuStatList = [];
    }
    this.isLoading = false;
  }

  // 获取班级列表列
  async getClassList() {
    this.classId = '';
    this.classList = [];

    const grade = this.gradeList.find(item => item.id == this.queryData.gradeValue);
    const { data } = await classList(grade.year, grade.phaseId - 2);
    const list = data.rows;
    list.sort((a, b) => {
      const numA = Number((a.class_name.match(/\d+/) && a.class_name.match(/\d+/)[0]) || 0);
      const numB = Number((b.class_name.match(/\d+/) && b.class_name.match(/\d+/)[0]) || 0);
      return numA - numB;
    });
    this.classList = list;
    this.classId = list[0]?.id;
  }

  // 查看详情
  async goDetails(stuStat: ExamStuStat) {
    if (!this.queryData.subjectValue) {
      this.$message.warning('请选择学科');
      return;
    }

    this.$sessionSave.set('studentDetail', {
      stuId: stuStat.stuId,
      stuName: stuStat.stuName,
    })
    this.$router.push({
      path: '/home/<USER>/teaching/studentDetail',
      query: {
        subjectId: this.queryData.subjectValue,
        gradeId: this.queryData.gradeValue,
        classId: this.classId,
        stuId: stuStat.stuId,
        categoryId: this.queryData.categoryValue,
      },
    });
  }

  // 排序
  changeSort({ column, prop, order }) {
    this.desc = order == 'descending' ? '1' : order == 'ascending' ? '0' : '';
    if (this.desc == '') {
      this.orderType = '0';
      this.desc = '1';
      this.getExamStuStat();
      return;
    }
    let propMap = {
      examNum: '0',
      submitNum: '1',
      notSubmitNum: '2',
      submitRate: '3',
      goodNum: '4',
      unqualifiedNum: '5',
      errorNum: '6',
    };
    this.orderType = propMap[prop];
    this.getExamStuStat();
  }

  handleExport() {
    if (!this.examStuStatList.length) {
      this.$message.warning('没有数据可以导出');
      return;
    }

    const params = this.getParams();
    const query = new URLSearchParams(params);
    const url = `${
      process.env.VUE_APP_KKLURL
    }/pexam/scanExam/exportExamStuStatDetails?${query.toString()}`;
    window.open(url, '_blank');
  }

  getParams() {
    let token = getToken();
    return {
      schoolId: this.$sessionSave.get('schoolInfo').id,
      gradeId: this.queryData.gradeValue,
      subjectId: this.queryData.subjectValue,
      categoryId: this.queryData.categoryValue,
      classId: this.classId,
      keyWord: this.keyWord,
      desc: this.desc,
      orderType: this.orderType,
      startTime: moment(this.queryData.dateRange[0]).format('YYYY-MM-DD 00:00:00'),
      endTime: moment(this.queryData.dateRange[1]).format('YYYY-MM-DD 23:59:59'),
      token,
    };
  }

  getCellStyle({ row, column, rowIndex, columnIndex }) {
    let redStyle = { color: 'red', backgroundColor: 'rgb(253 163 163 / 30%)' };
    if (column.property == 'submitRate' && row.submitRate < 60) {
      return redStyle;
    }
  }
}
</script>

<style scoped lang="scss">
.header-icon {
  margin-bottom: 0;
}

.input__icon {
  cursor: pointer;

  &:hover {
    color: #409eff;
  }
}

.text-btn {
  padding: 0;
}
</style>

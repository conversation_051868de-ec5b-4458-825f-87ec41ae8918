<template>
  <div>
    <div v-show="!showDefault" id="smallQuesChart"
         style="width: 100%; height: 400px"></div>
    <div v-show="showDefault">
      <div style="text-align: center"><img style="width: 350px;" :src="noResImg" alt=""></div>
      <p style="text-align: center;font-size: 16px;margin-top: 10px;">暂无数据!</p>
    </div>
  </div>
</template>

<script>
export default {
  name: "SmallQuesChart",
  props: ['tableData'],
  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      showDefault: false,
      smallQuesChart: null,
      totalData: []
    }
  },
  mounted(){
    let _this = this;
    window.onresize = ()  =>{
      return (() => {
        if(_this.smallQuesChart) {
          this.resetDomSize('smallQuesChart', 400);
          _this.smallQuesChart.resize();
        }
      })()
    };
    if(!this.smallQuesChart) {
      this.drawImg()
    }
  },
  beforeDestroy() {
    if(this.smallQuesChart != null && this.smallQuesChart != "" && this.smallQuesChart != undefined) {
      this.smallQuesChart.dispose();
      this.smallQuesChart = null;
    }
  },
  watch: {
    tableData: {
      handler(val) {
        if(val.length) {
          this.showDefault = false;
        }
        this.resetDomSize('smallQuesChart', 400);
        this.$nextTick(()=>{
          this.drawImg();
        })
      },
      deep: true
    }
  },
  methods: {
    resetDomSize(el, height) {
      let width = document.getElementById('pane-chart').clientWidth
      Object.defineProperty(document.getElementById(el),'clientWidth',
          {get:function(){return width;}, configurable: true})
      Object.defineProperty(document.getElementById(el),'clientHeight',
          {get:function(){return height;}, configurable: true})
    },
    handleChartData() {
      let data = this.tableData && this.tableData.length ? this.tableData : [] ;

      if(data.length) {
        this.totalData = data.map(it => {
          return {
            name: it.quesNo,
            value: [it.grdDistinguish===''?'-':it.grdDistinguish, it.difficult===''?'-':it.difficult]
          }
        })
      } else {
        this.totalData = [];
        this.showDefault = true;
      }
    },
    drawImg() {
      if(this.smallQuesChart != null && this.smallQuesChart != "" && this.smallQuesChart != undefined) {
        this.smallQuesChart.dispose();
        this.smallQuesChart = null;
      }
      this.handleChartData();
      let _this = this;
      // 基于准备好的dom，初始化echarts实例
      this.smallQuesChart = this.$echarts.init(
          document.getElementById("smallQuesChart")
      );
      // 绘制图表
      this.smallQuesChart.setOption({
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {            // 坐标轴指示器，坐标轴触发有效
            type: 'line'        // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: function (params) {
            // console.log(params)
            let str = '';
            params.map(item => {
              str += `题号:${item.name} 区分度:${item.value[0]} 难度:${item.value[1]} <br />`
            })
            return str
          },
        },
        grid: {
          left: '3%',
          right: '8%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          name: '区分度',
          min: '0',
          max: '1',
          interval: 0.1,
          splitLine: { show: false }, //去除网格线
          // axisLine:{
          //   lineStyle:{
          //     color:'#757C8C',
          //     fontSize: 16
          //   }
          // },
          nameTextStyle: {
            fontSize: 14,
            color: '#757C8C'
          },
          axisLabel: {
            fontSize: 16,
            color: '#757C8C'
          }
        },
        yAxis: {
          name: '难度',
          min: '0',
          max: '1',
          interval: 0.1,
          axisLine:{
            show: false,
            // lineStyle:{
            //   color:'#757C8C',
            //   fontSize: 16
            // }
          },
          nameTextStyle: {
            fontSize: 14,
            color: '#757C8C'
          },
          axisLabel: {
            fontSize: 16,
            color: '#757C8C'
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            symbolSize: 20,
            itemStyle: {
              color: '#418AFD'
            },
            data: _this.totalData,
            type: 'scatter',
            markLine: {
              label: {
                show: false
              },
              symbol: ['none', 'none'],
              silent: false,
              lineStyle: {
                type: 'dashed',
                color: '#FBA110',
                width: 2,
              },
              data: [
                { xAxis: 0.2,  },
                { xAxis: 0.9,  },
                { yAxis: 0.1,  },
                { yAxis: 0.9,  },
              ]
            }
          }
        ]
      });
    },

  }
}
</script>

<style scoped>

</style>

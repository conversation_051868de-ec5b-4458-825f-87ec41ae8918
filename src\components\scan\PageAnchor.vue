<template>
  <div class="anchor_point" :class="`anchor${info.pos}`" :style="{
                left: info.x + 'px',
                top: info.y + 'px',
              }"
       @mousedown="mouseBoxDown"
       @mouseup="mouseBoxUp"
       @mousemove="mouseBoxMove">
  </div>
</template>

<script>
export default {
  props: {
    //题目
    info: {
      type: Object,
      default: () => {
      },
    },
    scale: {
      type: Number,
      default: 1
    }
  },
  watch: {
    info(newVal, oldVal) {

    },
  },
  data() {
    return {
      isDragging: false,
      originPt: null,
      startPt: null,
      x: 0,
      y: 0
    };
  },

  mounted() {
    this.x = this.info.x
    this.y = this.info.y
  },
  methods: {
    mouseBoxDown(e) {
      this.isDragging = true
      console.log(`down:${e.screenX},${e.screenY}`)

      this.originPt = {
        x: this.info.x,
        y: this.info.y
      }
      this.startPt = {
        x: e.screenX,
        y: e.screenY
      }
      document.addEventListener("mouseup", this.mouseBoxUp);
      document.addEventListener("mousemove", this.mouseBoxMove);
    },
    mouseBoxUp(e) {
      this.originPt = null
      this.startPt = null
      this.isDragging = false
      console.log(`up:${e.screenX},${e.screenY}`)
      document.removeEventListener("mouseup", this.mouseBoxUp);
      document.removeEventListener("mousemove", this.mouseBoxMove);
    },
    mouseBoxMove(e) {
      if (!this.isDragging) {
        return
      }
      let dx = e.screenX - this.startPt.x
      let dy = e.screenY - this.startPt.y
      dx /= this.scale
      dy /= this.scale
      let x = this.originPt.x + dx
      let y = this.originPt.y + dy
      if (x < 0) x = 0;
      if (y < 0) y = 0;
      this.info.x = x
      this.info.y = y
      this.$emit('on-change')
    }
  },
};
</script>

<style lang="scss" scoped>
.anchor_point {
  width: 40px;
  height: 40px;
  border: 3px solid #f00;
  position: absolute;
}

.anchor_point.anchor0{

}

.anchor_point.anchor1{


}

.anchor_point.anchor2{


}

.anchor_point.anchor3{


}
</style>

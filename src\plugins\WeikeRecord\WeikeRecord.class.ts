import { cefMsg } from '@/callCplus/index';
import { subsEvents, sessionSave } from '@/utils';
import Observe from "@iclass/observe";
import { v1 as uuid } from 'uuid';
import Notification from '@iclass/element-ui/packages/notification';
import MessageBox from '@iclass/element-ui/packages/message-box';
import Loading from '@iclass/element-ui/packages/loading';
import { BatchSaveHomeWorkMicroCourse, BatchInsertResource, GetWeikeDetails } from '@/service/api';
import CreateDialog from '@/components/createDialog';
import CreateElement from '@/components/createElement';
import WeikeEditDialog from './WeikeEditDialog.vue'
import RecorderBangs from './RecorderBangs.vue'
import html2canvas from 'html2canvas';
import ajaxUpload from '@iclass/element-ui/packages/upload/src/ajax';

// 录制的微课信息
export interface RecordWkParams {
    classId: string
    examName: string
    workId: string
    qLevel: number
    qId: string
    // 大题名称
    bigTitle: string
    // 小题名称
    qName: string | number
}

// 已录制的微课信息
interface RecordWkInfo {
    // 录制状态 0:启动录制|1:开始录制|2:录制结束
    state: 0 | 1 | 2
    filepath: string
    uploadpath: string
    duration: number
    filesize: number
    filemd5: string
    // 视频封面
    imgurl: string
}

// 添加的微课Json数据
interface AddWeikeJson {
    id: string
    resourceId: string
    title: string
    microCourseUrl: string
    duration: number
    fileSize: number
    imgUrl: string
    targetType: number
    questionId: string
    questionName: string
    createTime: string
}

// 录制返回数据
export interface RecordReturn {
    type: 'success' | 'fail' | 'cancel'
    data?: AddWeikeJson
}

export class Events {
    // 微课添加完成
    static readonly WEIKE_FINALLY: string = 'weike_finally';
    static readonly RECORD_START: string = 'record_start';
    static readonly RECORD_END: string = 'record_end';
    static readonly WEIKE_UPLOADED: string = 'weike_uploaded';
}

/*
 * @Description: 微课录制
 * @Author: qmzhang
 * @Date: 2024-08-08 19:32:58
 * @LastEditTime: 2024-08-09 08:53:45
 * @FilePath: \personal-bigdata\src\plugins\WeikeRecord.class.ts
 */
export class WeikeRecord extends Observe {
    private static Instance: WeikeRecord;
    public static GetInstance(loginInfo?: LoginInfo): WeikeRecord {
        return WeikeRecord.Instance || new WeikeRecord(loginInfo);
    }

    // 指令的命名空间
    private readonly cmdNameSpace = 'micro.video_record'
    private loginInfo: LoginInfo;
    // 录制的微课信息
    private recordWeikeParams: RecordWkParams = null;
    // 已录制视频信息
    private weikeInfo: RecordWkInfo = {
        state: 0,
        filepath: 'string',
        uploadpath: 'string',
        duration: 0,
        filesize: 0,
        filemd5: '',
        imgurl: ''
    };
    // 录制中的刘海
    private recordBangs = null;
    private loadingInstance = null;
    // 通信广播
    private channel: BroadcastChannel = null;
    // 讲评页面录制程序心跳
    private recordHeartbeat: any = null;

    // 4:任教班级-错误学生 5:任教班级-所有学生 6:全部班级-错误学生 7:全部班级-所有学生
    static readonly ReciverList = [
        {
            label: "任教班级-错误学生",
            value: 4
        },
        {
            label: "任教班级-所有学生",
            value: 5
        },
        {
            label: "全部班级-错误学生",
            value: 6
        },
        {
            label: "全部班级-所有学生",
            value: 7
        }
    ]

    constructor(loginInfo: LoginInfo) {
        super();

        this.loginInfo = loginInfo;
        WeikeRecord.Instance = this;
        this.initialize();
    }

    private initialize() {
        this.listenRecordCallback();
        this.recoverWeikeRecord();
    }

    /**
     * @description: 恢复微课录制状态
     */
    private async recoverWeikeRecord() {
        console.debug('检查录制状态', localStorage.getItem(`recorder_${this.loginInfo.id}`));
        // 同步微课本地缓存信息
        let res = this.getLocalWKCacheInfo();
        if (!res) return;

        this.recordWeikeParams = res.params;
        this.weikeInfo = res.data;

        let isRecording = await this.GetWeikeRecordState();
        if (!isRecording) {
            this.clearLocalWKCacheInfo();
            return;
        }

        this.updateBackWnd();
        this.handleWeikeOnStart();
    }

    /**
     * @description: 获取本地缓存的录制信息
     * @return {*}
     */
    private getLocalWKCacheInfo() {
        try {
            return JSON.parse(localStorage.getItem(`recorder_${this.loginInfo.id}`));
        } catch (error) {
            return null;
        }
    }

    /**
     * @description: 缓存微课信息
     */
    private cacheWkInfo() {
        // 缓存当前需要录制微课的题目数据
        localStorage.setItem(`recorder_${this.loginInfo.id}`, JSON.stringify({
            params: this.recordWeikeParams,
            data: this.weikeInfo
        }));
    }

    /**
     * @description: 清除本地缓存的录制信息
     */
    private clearLocalWKCacheInfo() {
        localStorage.removeItem(`recorder_${this.loginInfo.id}`);
    }

    /**
     * @description: 监听录制回调：结束|上传完成
     * @return {*}
     */
    private listenRecordCallback(): void {
        subsEvents.listen('executePdu_videocallback', async (data) => {
            switch (data.type) {
                case 'onstart':
                    // 开始录制
                    this.handleWeikeOnStart();
                    break;
                case 'onend':
                    // 微课录制结束
                    this.handleWeikeOnEnd(data);
                    break;
                case 'upload':
                    // 微课上传完成
                    this.handleWeikeUploaded(data);
                    break;
            }
        });
    }

    /**
     * @description: base64转file
     * @param {*} base64String
     * @param {*} filename
     * @return {*}
     */
    private base64ToFile(base64String: string, filename: string): File {
        // 分离 Base64 字符串和 MIME 类型
        const [mimeString, base64] = base64String.split(',');
        const mime = mimeString.match(/:(.*?);/)[1];
        const byteString = atob(base64);
        const arrayBuffer = new ArrayBuffer(byteString.length);
        const uint8Array = new Uint8Array(arrayBuffer);

        // 将 Base64 解码为字节数组
        for (let i = 0; i < byteString.length; i++) {
            uint8Array[i] = byteString.charCodeAt(i);
        }

        // 创建 Blob 对象并返回 File 对象
        const blob = new Blob([uint8Array], { type: mime });
        return new File([blob], filename, { type: mime });
    }

    /**
     * @description: 上传视频封面
     * @param {string} base64
     * @return {*}
     */
    private uploadThumbnail(base64: string): Promise<any> {
        const uploadAction = `https://service.iclass30.com/testbank/testBank/uploadEditorImgNew?htmlurl=${location.href}&responseType=json`;
        return new Promise((resolve, reject) => {
            const options = {
                withCredentials: true,
                file: this.base64ToFile(base64, 'thumbnail.png'),
                filename: 'upload',
                action: uploadAction,
                onSuccess: res => {
                    resolve(res);
                },
                onError: err => {
                    reject(err);
                }
            };
            ajaxUpload(options)
        })

    }

    /**
     * @description: 处理微课开始录制
     * @return {*}
     */
    private async handleWeikeOnStart() {
        console.log('handleWeikeOnStart');

        this.weikeInfo.state = 1;
        this.cacheWkInfo();
        // 显示正在录制中的刘海
        this.recordBangs && this.recordBangs.close();
        this.recordBangs = CreateElement({
            component: RecorderBangs,
            id: uuid(),
            appendToBody: true,
            data: {
                title: this.recordWeikeParams.examName
            }
        })
        if (!this.weikeInfo.imgurl) {
            // 截屏作为封面
            let canvas = await html2canvas(document.body, {
                scale: 0.4
            });
            var base64 = canvas.toDataURL('image/jpeg', 0.6);
            // 上传封面图
            let res = await this.uploadThumbnail(base64);
            this.weikeInfo.imgurl = res.url;
        }
        this.bcPostMessage({ type: 'startRecord' });
        this.trigger(Events.RECORD_START);
    }

    /**
     * @description: 处理微课录制结束
     */
    private handleWeikeOnEnd(data) {
        this.weikeInfo.state = 2;
        this.weikeInfo.duration = data.record_time;
        this.cacheWkInfo();
        // 打开loading等待上传结束
        this.loadingInstance = Loading.service({ fullscreen: true, text: '等待微课视频上传...' });
        // 销毁正在录制中的刘海
        this.recordBangs.close();
        this.trigger(Events.RECORD_END, data);
        this.bcPostMessage({ type: 'stopRecord' });
    }

    /**
     * @description: 处理微课上传完成
     */
    private async handleWeikeUploaded(data) {
        this.loadingInstance.close();
        let editDialog = null;
        if (data.status == 1) {
            // 上传完成
            this.weikeInfo.filesize = data.filesize;
            this.weikeInfo.uploadpath = data.uploadpath;
            this.weikeInfo.filepath = data.filepath;
            this.weikeInfo.filemd5 = data.filemd5;
            this.trigger(Events.WEIKE_UPLOADED, data);
            // 打开保存微课弹窗
            editDialog = CreateDialog({
                title: '添加微课',
                size: 'middle',
                appendToBody: true,
                component: WeikeEditDialog,
                closeOnClickModal: false,
                width: '600px',
                data: {
                    weikeName: `${this.recordWeikeParams.examName}-${this.recordWeikeParams.bigTitle}-${this.recordWeikeParams.qName}`,
                },
                close: () => {
                    //关闭后触发
                    console.log('dialog is closed');
                },
            });
        } else {
            // 上传失败，提示用户本地路径，建议手动上传网盘
            await MessageBox.confirm(`微课上传失败，视频位置：${data.filePath}，你可以选择继续上传或手动上传到网盘`, '提示', {
                confirmButtonText: '继续上传',
                cancelButtonText: '取消',
                center: false,
                type: 'warning'
            }).catch(action => {
                editDialog.close();
            })
        }
        this.clearLocalWKCacheInfo();
    }

    /**
     * @description: 检查并处理微课录制状态
     * @return {*}
     */
    public async CheckResolveWeikeState() {
        if(!window.cef) return false;

        let isRecording = await this.GetWeikeRecordState();
        if (!isRecording) return false;

        // 提示“正在录制微课中...”
        let msg = '微课正在录制中，是否立即结束?';
        if (this.recordWeikeParams.examName) msg = this.recordWeikeParams.examName + msg;
        await MessageBox.confirm(msg, '提示', {
            confirmButtonText: '结束录制',
            cancelButtonText: '取消',
            center: false,
            type: 'warning'
        })
        this.StopWeikeRecord();
        return true;
    }

    /**
     * @description: 启动微课录制
     * @param {RecordWkParams} param1
     * @return {Promise<RecordReturn>}
     */
    public async StartWeikeRecord({ workId, qId, classId, examName, qName, qLevel, bigTitle }: RecordWkParams): Promise<RecordReturn> {
        let isRecording = await this.CheckResolveWeikeState();
        if (isRecording) return { type: 'cancel' };

        this.recordWeikeParams = { workId, qId, examName, classId, qName, qLevel, bigTitle };
        this.cacheWkInfo();

        return new Promise((resolve, reject) => {
            this.off(Events.WEIKE_FINALLY);
            cefMsg(
                this.cmdNameSpace,
                'open_videorecord',
                JSON.stringify({
                    uploadpath: `aliba/resources/video_record/${classId}/${workId}/${qId}/${qName}/${uuid()}.mp4`
                })
            )
            this.once(Events.WEIKE_FINALLY, resolve);
        })
    }

    /**
     * @description:用于改变录制工具回调窗口
     */
    private updateBackWnd() {
        cefMsg(this.cmdNameSpace, 'update_backwnd', JSON.stringify({
            uploadpath: this.weikeInfo.uploadpath
        }));
    }

    /**
     * @description: 停止微课录制
     * @return {*}
     */
    public StopWeikeRecord(): void {
        cefMsg(this.cmdNameSpace, 'stop_videorecord', '');
    }

    /**
     * @description: 获取微课录制状态
     * @return {*}
     */
    public async GetWeikeRecordState(): Promise<boolean> {
        let { isRecording } = await cefMsg(this.cmdNameSpace, 'get_videorecord_state', '', 'getVideoRecordState');
        return !!Number(isRecording);
    }

    /**
     * @description: 监听讲评页面录制心跳
     */
    public ListenCommentPageRecord() {
        if (this.weikeInfo.state == 1) {
            // 如果当前录制已经开始，销毁当前录制
            this.recordBangs.close();
        }
        this.StartBroadcastChannel();
        this.channel.addEventListener('message', (evt: MessageEvent) => {
            let data = evt.data;

            switch (data.type) {
                case 'startRecord':
                    this.recordHeartbeat = this.refreshHeartbeat();
                    break;
                case 'stopRecord':
                    clearInterval(this.recordHeartbeat);
                    this.recordHeartbeat = null;
                    break;
                case 'heartbeat':
                    console.debug('rc pong')
                    this.recordHeartbeat = this.refreshHeartbeat();
                    break;
            }
        })
    }

    /**
     * @description: 开启窗口广播
     * @return {*}
     */
    public StartBroadcastChannel() {
        if (this.channel) this.channel.close()
        this.channel = new BroadcastChannel('ch-comment');
    }

    /**
     * @description: 讲评页面启动心跳机制
     */
    public StartHeartbeat() {
        this.recordHeartbeat = setInterval(() => {
            this.channel.postMessage({ type: 'heartbeat' });
            console.debug('rc ping')
        }, 1000)
    }

    /**
     * @description: 如果存在广播，发送消息
     * @param {*} data
     */
    private bcPostMessage(data: any) {
        if (this.channel) this.channel.postMessage(data);
    }

    /**
     * @description: 刷新心跳时间
     * 即时2秒，如果超时直接认定窗口关闭
     * @return {any}
     */
    private refreshHeartbeat(): any {
        if (this.recordHeartbeat) clearInterval(this.recordHeartbeat);
        let count = 2;
        let timer = setInterval(() => {
            count--;
            if (count <= 0) {
                this.onCommentRecordBroken();
            }
        }, 1000)

        return timer;
    }

    /**
     * @description: 当讲评页面的录制被中断
     * 监听不到讲评窗口的心跳，则默认讲评窗口关闭，改用当前窗口来监听微课回调
     */
    private onCommentRecordBroken() {
        clearInterval(this.recordHeartbeat);
        this.recordHeartbeat = null;
        this.channel.close();
        this.channel = null;

        this.recoverWeikeRecord();
        this.trigger(Events.WEIKE_FINALLY, { type: 'fail' });
    }

    /**
     * @description: 添加微课到题目
     * @param {string} videoName 视频名称
     * @param {number} targetType 接收对象
     * @param {boolean} saveToNetdisk 是否保存至网盘
     * @return {Promise<boolean>}
     */
    public async AddWeikeToQuestion(videoName: string, targetType: number, saveToNetdisk: boolean): Promise<boolean> {
        try {
            let resourceId = '';
            if (saveToNetdisk) {
                let res = await BatchInsertResource({
                    schoolId: this.loginInfo.schoolid,
                    userId: this.loginInfo.id,
                    shareState: 0,
                    isDown: 0,
                    isRename: 0,
                    shareAllClass: -1,
                    appType: 0,
                    isSyncResource: 0,
                    files: JSON.stringify([{
                        "title": videoName,
                        "ext": "mp4",
                        "md5": this.weikeInfo.filemd5,
                        "size": this.weikeInfo.filesize,
                        "url": this.weikeInfo.uploadpath,
                        "fileType": "video/mp4"
                    }])
                })

                if (res.code && res.code != 1) throw (res);
                let resdata = !res.code ? res : res.data;
                resourceId = resdata[0].id;
            }

            let wkListJson = [{
                microCourseUrl: this.weikeInfo.uploadpath,
                duration: Math.round(this.weikeInfo.duration / 1000) * 1000,
                fileSize: this.weikeInfo.filesize,
                title: videoName.substring(0, 25),
                resourceId,
                imgUrl: this.weikeInfo.imgurl
            }];
            let tt = targetType;
            // classId支持存多个任教班级
            let saveClassIds = this.recordWeikeParams.classId;
            let substituteClassList: any = sessionSave.get('substituteClassList')
            if (tt == 4 || tt == 5) {
                saveClassIds = [...new Set(substituteClassList.map(item => item.classId))].join(',')
            }
            let res: any = await BatchSaveHomeWorkMicroCourse({
                teaId: this.loginInfo.id,
                teaName: this.loginInfo.realname,
                schoolId: this.loginInfo.schoolid,
                classId: saveClassIds,
                workId: this.recordWeikeParams.workId,
                cardQuestionLevel: this.recordWeikeParams.qLevel,
                cardQuestionId: this.recordWeikeParams.qId,
                questionId: this.recordWeikeParams.qId,
                questionName: this.recordWeikeParams.qName,
                targetType: tt,
                workTitle: this.recordWeikeParams.examName,
                microCourseListJson: JSON.stringify(wkListJson),
            })
            if (res.code && res.code != 1) throw (res);

            let reslist = !res.code ? res : res.data;
            let weikeRes = reslist[0];
            Notification({
                title: "提示",
                message: "添加微课成功！",
                type: 'success',
                offset: 100,
            })

            this.trigger(Events.WEIKE_FINALLY, {
                type: 'success',
                data: {
                    ...weikeRes,
                    microCourseUrl: weikeRes.url,
                    questionId: this.recordWeikeParams.qId,
                    questionName: this.recordWeikeParams.qName,
                    targetType,
                    imgUrl: this.weikeInfo.imgurl
                }
            });
            return true;
        } catch (error) {
            let msg = error.msg || '添加微课失败';
            await MessageBox.confirm(msg + `，视频位置：${this.weikeInfo.filepath}，你可以尝试继续添加或手动上传到网盘`, '提示', {
                confirmButtonText: '继续添加',
                cancelButtonText: '取消',
                center: false,
                type: 'warning'
            })
            console.error(error);
            return false;
        }
    }
}
<template>
  <div>
    <div v-show="!showDefault" id="boxplotChart" style="width: 100%; height: 400px"></div>
    <div v-show="showDefault">
      <div style="text-align: center"><img style="width: 350px" :src="noResImg" alt="" /></div>
      <p style="text-align: center; font-size: 16px; margin-top: 10px">暂无数据!</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BoxplotChart',
  props: ['tableData', 'currentSubId'],
  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      showDefault: false,
      boxplotChart: null,
      dataObj: {},
      q005Data: [], //第5%
      q1Data: [], //第一 四分位
      medianData: [], //中位数
      q3Data: [], //第三 四分位
      q095Data: [], //第95%
    };
  },
  mounted() {
    let _this = this;
    window.onresize = () => {
      return (() => {
        // this.resetDomSize('boxplotChart', 400);
        if (_this.boxplotChart) {
          _this.boxplotChart.resize();
        }
      })();
    };
    if (!this.boxplotChart) {
      this.drawImg();
    }
  },
  beforeDestroy() {
    if (this.boxplotChart != null && this.boxplotChart != '' && this.boxplotChart != undefined) {
      this.boxplotChart.dispose();
      this.boxplotChart = null;
    }
  },
  watch: {
    tableData: {
      handler(val) {
        if (!val || !val.length) {
          this.showDefault = true;
          return;
        }
        if (val.length) {
          this.showDefault = false;
        }
        // this.resetDomSize('boxplotChart', 400);
        this.$nextTick(() => {
          this.drawImg();
        });
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // resetDomSize(el, height) {
    //   let width = document.getElementById('pane-chart').clientWidth
    //   Object.defineProperty(document.getElementById(el),'clientWidth',
    //       {get:function(){return width;}, configurable: true})
    //   Object.defineProperty(document.getElementById(el),'clientHeight',
    //       {get:function(){return height;}, configurable: true})
    // },
    handleChartData() {
      let data = this.tableData && this.tableData.length ? this.tableData : [];
      // console.log(this.tableData)
      let boxData = [],
        q005Data = [],
        q1Data = [],
        medianData = [],
        q3Data = [],
        q095Data = [];
      if (data.length) {
        data.map(item => {
          let { className = '年级', q095, q1, median, q3, q005 } = item;
          let copy_q095 = q095,
            copy_q1 = q1,
            copy_median = median,
            copy_q3 = q3,
            copy_q005 = q005;
          if (q095 == q005 && Number(q005) == -1) {
            q095 = q3;
            q005 = q1;
            copy_q095 = copy_q005 = '-';
          } else if ((((q095 == q005) == q1) == median) == q3 && Number(q005) == -1) {
            q095 = q005 = q1 = median = q3 = 0;
            copy_q095 = copy_q005 = copy_q1 = copy_median = copy_q3 = '-';
          }
          let obj = {
            name: className,
            value: [q005, q1, median, q3, q095],
          };
          boxData.push(obj);
          this.dataObj[className] = [copy_q005, copy_q1, copy_median, copy_q3, copy_q095];

          // 处理其他数据
          q005Data.push({
            name: className,
            value: q005,
            itemStyle: {
              color: 'rgba(128, 128, 128, 0)',
            },
            label: {
              show:
                copy_q005 == '-' && (q005 == q1) != 0
                  ? true
                  : copy_q005 == '-' && q1 == 0
                  ? false
                  : true,
              color: copy_q005 == '-' && (q005 == q1) != 0 ? '#fff' : '#757C8C',
              // color: '#757C8C',
              position: 'top',
              offset: [0, 5],
              formatter: function () {
                return copy_q005 == '-' && (q005 == q1) != 0
                  ? q005
                  : copy_q005 == '-' && q1 == 0
                  ? ''
                  : copy_q005;
              },
            },
          });
          q1Data.push({
            name: className,
            value:
              copy_q1 == '-' && (q005 == q1) != 0
                ? 0
                : copy_q1 == '-' && q1 == 0
                ? false
                : Number(copy_q1 - copy_q005).toFixed(2),
            itemStyle: {
              color: 'rgba(128, 128, 128, 0)',
            },
            label: {
              show:
                copy_q1 == '-' && (q005 == q1) != 0
                  ? true
                  : copy_q1 == '-' && q1 == 0
                  ? false
                  : true,
              color: '#fff',
              position: 'top',
              offset: [0, 5],
              formatter: function () {
                return copy_q1 == '-' ? '' : copy_q1 + '';
              },
            },
          });
          medianData.push({
            name: className,
            value: copy_median == '-' ? '' : Number(copy_median - copy_q1).toFixed(2),
            itemStyle: {
              color: '#B3DBFA',
            },
            label: {
              show: copy_median == '-' ? false : true,
              color: '#fff',
              position: 'top',
              offset: [0, 5],
              formatter: function () {
                return copy_median == '-' ? '' : copy_median + '';
              },
            },
          });
          q3Data.push({
            name: className,
            value: copy_q3 == '-' ? '' : Number(copy_q3 - copy_median).toFixed(2),
            itemStyle: {
              color: '#409EFF',
            },
            label: {
              show: copy_q3 == '-' ? false : true,
              color: '#757C8C',
              position: 'top',
              offset: [0, 5],
              formatter: function () {
                return copy_q3 == '-' ? '' : copy_q3 + '';
              },
            },
          });
          q095Data.push({
            name: className,
            value:
              copy_q095 == '-' && q095 == q3
                ? q095
                : copy_q095 == '-' && q095 == 0
                ? false
                : Number(copy_q095 - copy_q3).toFixed(2),
            itemStyle: {
              color: 'rgba(128, 128, 128, 0)',
            },
            label: {
              show: copy_q095 == '-' ? false : true,
              color: '#757C8C',
              position: 'top',
              offset: [0, 0],
              formatter: function () {
                return copy_q095 == '-' ? '' : copy_q095 + '';
              },
            },
          });
        });
        // console.log('boxData-->', boxData)
        return [boxData, q005Data, q1Data, medianData, q3Data, q095Data];
      } else {
        this.showDefault = true;
        return [boxData, q005Data, q1Data, medianData, q3Data, q095Data];
      }
    },
    drawImg() {
      if (this.boxplotChart != null && this.boxplotChart != '' && this.boxplotChart != undefined) {
        this.boxplotChart.dispose();
        this.boxplotChart = null;
      }
      let [boxData, q005Data, q1Data, medianData, q3Data, q095Data] = this.handleChartData();
      let classNames = boxData.map(it => it.name);
      let _this = this;
      // 基于准备好的dom，初始化echarts实例
      this.boxplotChart = this.$echarts.init(document.getElementById('boxplotChart'));
      // 绘制图表
      this.boxplotChart.setOption({
        tooltip: {
          trigger: 'item',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
        },
        grid: {
          left: '3%',
          right: '10%',
          bottom: '12%',
          containLabel: true,
        },
        yAxis: {
          type: 'value',
          name: '均分',
          splitArea: {
            show: false,
          },
        },
        xAxis: {
          type: 'category',
          name: '班级',
          splitArea: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: '#757C8C',
            },
          },
          boundaryGap: true,
          nameGap: 30,
          data: classNames,
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: (6 / boxData.length) * 100,
            // minValueSpan: 1,
            // maxValueSpan: 12
          },
        ],
        series: [
          //  箱线图
          {
            // name: 'boxplot',
            type: 'boxplot',
            data: boxData,
            boxWidth: [40, 40],
            hoverAnimation: false,
            animation: false,
            silent: false,
            zlevel: 1,
            itemStyle: {
              color: 'rgba(128, 128, 128, 0)',
              borderColor: '#409EFF',
            },
            tooltip: {
              formatter: function (params) {
                // console.log(params.name, params.value)
                const className = params.name;
                let obj = _this.tableData.find(item => {
                  return className == item.className;
                });
                let teaLabel;
                let teaHtml;
                if (className == '年级') {
                  obj = _this.tableData.find(item => {
                    return !item.classId;
                  });
                  teaLabel = _this.currentSubId ? '学科组长' : '年级主任';
                } else {
                  teaLabel = _this.currentSubId ? '任课老师' : '班主任';
                }
                teaHtml = `${teaLabel}：${obj?.teaName || '--'}`;
                let param = _this.dataObj[params.name];
                return [
                  params.name + `(${teaHtml})` + ': ',
                  '第95%: ' + param[4],
                  '第三四分位: ' + param[3],
                  '中位数: ' + param[2],
                  '第一四分位: ' + param[1],
                  '第5%: ' + param[0],
                ].join('<br/>');
              },
            },
          },
          // 第5%柱状图
          {
            type: 'bar',
            stack: 2,
            tooltip: {
              show: false,
            },
            zlevel: 2,
            data: q005Data,
            animation: false,
          },
          // q1柱状图
          {
            type: 'bar',
            stack: 2,
            tooltip: {
              show: false,
            },
            zlevel: 1,
            data: q1Data,
            barWidth: 40,
            animation: false,
          },
          // median柱状图
          {
            type: 'bar',
            stack: 2,
            tooltip: {
              show: false,
            },
            hoverAnimation: false,
            silent: false,
            zlevel: 1,
            data: medianData,
            animation: false,
          },
          // q3柱状图
          {
            type: 'bar',
            stack: 2,
            tooltip: {
              show: false,
            },
            hoverAnimation: false,
            silent: false,
            zlevel: 1,
            data: q3Data,
            barWidth: 40,
            animation: false,
          },
          // 第95%柱状图
          {
            type: 'bar',
            stack: 2,
            tooltip: {
              show: false,
            },
            zlevel: 2,
            data: q095Data,
            animation: false,
          },
        ],
      });
    },
  },
};
</script>

<style scoped></style>

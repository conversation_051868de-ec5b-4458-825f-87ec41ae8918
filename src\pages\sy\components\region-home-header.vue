<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-11-18 17:02:57
 * @LastEditors: 小圆
-->
<template>
  <div class="region-home-header">
    <div class="logo-container header--left">
      <img class="logo" :src="require('@/assets/sylogo.png')" alt="" />
    </div>
    <div class="header--middle">
      <div class="header-tab">
        <ul class="tab-container list-none">
          <li
            class="tab-item"
            v-for="item in tabList"
            :key="item.title"
            :class="{ active: item.path == activeTab }"
            @click="gotoWorkBook(item)"
          >
            {{ item.title }}
          </li>
        </ul>
      </div>
    </div>
    <div class="header--right"></div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';

@Component
export default class RegionHomeHeader extends Vue {
  activeTab = 'schoolStudy';
  tabList = [
    {
      title: '学校学情',
      path: '/home/<USER>',
    },
    {
      title: '区域学情',
      path: '/home/<USER>',
    },
    {
      title: '区域学情对比',
      path: '/home/<USER>',
    },
  ];

  @Watch('$route.path')
  onRouteChange() {
    let tab = this.tabList.find(item => this.$route.path == item.path);
    this.activeTab = tab?.path;
  }

  mounted() {
    this.onRouteChange();
  }

  gotoWorkBook(item) {
    this.activeTab = item.path;
    this.$router.push(item.path);
  }
}
</script>

<style scoped lang="scss">
.region-home-header {
  display: flex;
}

.header-tab {
  flex: 1;
  height: 100%;
  display: flex;
  justify-content: center;

  .tab-container {
    .tab-item {
      display: inline-block;
      min-width: 110px;
      height: 100%;
      line-height: 60px;
      font-size: 19px;
      font-weight: 400;
      color: #434d54;
      text-align: center;
      border-bottom: 3px solid transparent;
      cursor: pointer;
      margin-right: 25px;

      &.active {
        position: relative;
        color: #008dea;
        font-weight: bold;

        &:before {
          content: '';
          display: block;
          position: absolute;
          width: 100%;
          bottom: 0;
          height: 3px;
          background-color: #008dea;
          border-radius: 2px;
        }
      }

      &:first-child {
        /*margin-right : 25px;*/
      }

      &:last-child {
        margin-right: 0px;
      }
    }
  }
}

.logo-container {
  display: flex;
  height: 100%;
  align-items: center;
}

.header--left {
  flex: 1;
}

.header--middle {
  flex: 1;
}

.header--right {
  flex: 1;
}
</style>

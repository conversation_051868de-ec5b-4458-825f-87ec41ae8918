<template>
  <!-- 客观题 -->
  <div
    :style="drawBoxStyle"
    @click.stop="choiceCorrect('')"
    class="box-main"
    :class="{ isboj: ques.is_obj,iswrite: ques.type == 16 && checkType == '' }"
    v-if="ques.is_obj"
  >
  <template v-if="ques.type == 16 && checkType == ''">
    {{String.fromCharCode(65+optionIndex)}}
  </template>
  <template v-else>
    <div
      v-for="(item, index) in points.new_score_list"
      @click.stop="choiceCorrect(item, index)"
      :key="index"
      class="subjet_col"
      :class="{ active: item.isChoice }"
      :style="{ width: colWidth, height: '100%' }"
    ></div>
  </template>
  </div>
  <!-- 主观题 -->
  <!-- <div v-else :style="drawBoxStyle" class="box-main">
    <template v-for="(rows, rowsIndex) in points.rows">
      <template v-for="(Item, ItemIndex) in points.newArray">
        <div
          v-for="(item, index) in Item"
          @click.stop="choiceCorrect(item, index)"
          :key="index"
          class="subjet_col"
          :class="{ active: item.isChoice }"
          :style="{ width: colWidth, height: '100%' }"
        ></div>
      </template>
    </template>
  </div> -->
  <!-- 主观题线上识别  type==15 -->
  <div v-else-if="!ques.is_obj && ques.type == 15" class="box-main">
    <div v-for="(item, index) in points.new_score_list" @click.stop="choiceCorrect(item, index)" :key="index"
      class="subjet_col online" :class="{ active: item.isChoice }">
      <span v-if="item.totalScore == item.value"><svg t="1722223836117" class="icon" viewBox="0 0 1024 1024"
          version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1509" width="28" height="28">
          <path
            d="M892.064 261.888a31.936 31.936 0 0 0-45.216 1.472L421.664 717.248l-220.448-185.216a32 32 0 1 0-41.152 48.992l243.648 204.704a31.872 31.872 0 0 0 20.576 7.488 31.808 31.808 0 0 0 23.36-10.112L893.536 307.136a32 32 0 0 0-1.472-45.248z"
            p-id="1510"></path>
        </svg></span>
      <span v-else-if="item.value == 0"><svg t="1722223846232" class="icon" viewBox="0 0 1024 1024" version="1.1"
          xmlns="http://www.w3.org/2000/svg" p-id="2345" width="28" height="28">
          <path
            d="M806.4 263.2l-45.6-45.6L512 467.2 263.2 217.6l-45.6 45.6L467.2 512 217.6 760.8l45.6 45.6L512 557.6l248.8 248.8 45.6-45.6L557.6 512z"
            p-id="2346"></path>
        </svg></span>
      <span v-else><svg t="1722224242634" class="icon" viewBox="0 0 1024 1024" version="1.1"
          xmlns="http://www.w3.org/2000/svg" p-id="8341" width="28" height="28">
          <path
            d="M976.457143 193.828571a35.108571 35.108571 0 0 0-51.2 0l-249.417143 248.685715-138.24-138.24a36.571429 36.571429 0 0 0-51.2 0 36.571429 36.571429 0 0 0 0 51.931428l138.24 138.24L365.714286 752.64 98.742857 486.4a35.108571 35.108571 0 0 0-51.2 0 35.108571 35.108571 0 0 0 0 51.2l292.571429 292.571429a35.108571 35.108571 0 0 0 51.2 0l284.525714-284.525715 103.131429 103.131429a36.571429 36.571429 0 1 0 51.2-51.931429L731.428571 494.445714l248.685715-249.417143a35.108571 35.108571 0 0 0-3.657143-51.2z"
            fill="#251A17" p-id="8342"></path>
        </svg></span>
    </div>
  </div>
  <!-- 主观题 -->
  <div v-else :style="drawBoxStyle" class="box-main">
    <div
      v-for="(item, index) in points.new_score_list"
      @click.stop="choiceCorrect(item, index)"
      :key="index"
      class="subjet_col"
      :class="{ active: item.isChoice, write: ques.type == 13 }"
      :style="{ width: colWidth, height: '100%', 'text-align': 'center' }"
    >
      <span class="num" v-if="ques.type == 13 && item.value >= 0">{{ item.value }}</span>
    </div>
</div>
</template>

<script>
export default {
  props: {
    //题目
    ques: {
      type: Object,
      default: () => {},
    },
    //选项坐标
    points: {
      type: Object,
      default: () => {},
    },
    //当前题目下标
    quesIndex: {
      type: Number,
      default: () => 0,
    },
    //当前选项下标
    optionIndex: {
      type: Number,
      default: () => 0,
    },
    //当前行下标
    rowIndex: {
      type: Number,
      default: () => 0,
    },
    //显示类型 detail:查看详情，'':题目异常
    checkType: {
      type: String,
      default: () => '',
    },
    //图片压缩率
    scale: {
      type: Number,
      default: () => 4.173,
    },
  },
  watch: {
    points(newVal, oldVal) {
      this.drawBoxStyle = this.getBoxStyle(newVal.pos);
      this.colWidth = (newVal.pos[2] * this.scale) / this.points.cols + 'px';
      this.$nextTick(() => {
        this.initBox();
      });
    },

    scale(newVal) {
      this.initBox();
    },
  },
  data() {
    return {
      drawBoxStyle: {
        display: 'none',
      },
      answerStyle: '',
      //打分框宽度
      boxWidth: '',
      //每一个分数格宽度
      colWidth: '',
      //当前分数格
      currentIndex: null,
    };
  },

  mounted() {
    setTimeout(() => {
      this.initBox();
    }, 200);
    // console.log("选项或打分框this.points", this.points);
    // console.log('题目this.ques', this.ques);
  },
  methods: {
    /**
     * @name:初始化打分框
     */
    initBox() {
      //主观题
      if (!this.ques.is_obj) {
        if (this.checkType == '') {
          //题目异常
          this.currentIndex = this.ques.list[this.optionIndex].scores.indexOf(
            this.ques.list[this.optionIndex].score
          );
        } else {
          //查看详情
          this.currentIndex = this.ques.score_list[this.optionIndex].scores.indexOf(
            this.ques.score_list[this.optionIndex].score
          );
        }
      }
      if(this.points.pos){
        this.drawBoxStyle = this.getBoxStyle(this.points.pos);
        this.colWidth = (this.points.pos[2] * this.scale) / this.points.cols + 'px';
      }
    },
    /**
     * 获取 box的相对位置
     */
    getBoxStyle(box) {
      if(!box) return;
      let queLeft = this.ques.pos[0];
      let queRight = this.ques.topY;
      if (this.checkType == 'detail' || this.checkType == 'errpage') {
        queLeft = 0;
        queRight = 0;
      }
      //客观题选择框高度加1.5
      let boxHeight = this.ques.is_obj ? box[3] + 1.5 : box[3];
      return {
        left: box[0] * this.scale - queLeft + 'px',
        top: box[1] * this.scale - queRight + 'px',
        width: box[2] * this.scale + 'px',
        height: boxHeight * this.scale + 'px',
      };
    },
    choiceCorrect(item, index) {
      if (this.checkType == 'errpage') {
        return;
      }
      if (!this.ques.is_obj) {
        item.isChoice = !item.isChoice;
        const scoreList =
          this.checkType === 'detail'
            ? this.ques.score_list[this.optionIndex].new_score_list
            : this.ques.list[this.optionIndex].new_score_list;
        scoreList.forEach((ite, inde) => {
          if (inde != index + this.rowIndex * this.ques.score_list[this.optionIndex].cols) {
            ite.isChoice = false;
          }
        });
      }
      let score = 0;
      if (!this.ques.is_obj) {
        if (this.ques.miss_score == -1) {
          //不是仅标识错误
          score = item.isChoice ? item.value : 0;
        } else {
          score = item.isChoice ? item.value : this.ques.miss_score;
        }
      }
      score = score == -1 ? 0 : score;
      this.currentIndex = index;
      this.$emit('choice-correct', {
        quesIndex: this.quesIndex,
        optionIndex: this.optionIndex,
        scoreColIndex: index,
        score: score,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
  .points-box {
    position: absolute;
    cursor: pointer;
    z-index: 2;
    &.active {
      border: solid 4px #01cc7d !important;
    }
    &.error {
      border: solid 4px red !important;
    }
    &.right {
      border: solid 4px #01cc7d !important;
    }  
    &.online{
      display: inline-block;
      position: relative;
      vertical-align: top;
    }
  }
.box-main {
  display: flex;
  &.isboj {
    border: solid 2px #409eff;
    display: unset;
    text-align: center;
  }
  &.iswrite {
   background: #fff; 
   border-radius: 5px;
  }
}
.subjet_col {
  border: solid 2px #409eff;
  border-right: unset;
  z-index: 2;
  &.active {
    // 0f0
    border: solid 4px #01cc7d;
    &:last-child {
      border-right: solid 4px #01cc7d;
    }
  }
  &.write {
    background: #fff;
  }
  &.online{
    width: 30px;
    height: 30px;
    display: inline-block;
    text-align: center;
  }
  &:last-child {
    border-right: solid 2px #409eff;
  }
  .num {
    font-size: 18px;
    font-weight: bold;
  }
}
</style>

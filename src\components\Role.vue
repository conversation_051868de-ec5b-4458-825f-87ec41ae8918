<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-06-24 10:55:54
 * @LastEditors: 小圆
-->
<template>
  <div class="role-page">
    <div class="role-page-header">
      <div class="role-page-title">个人角色权限查询</div>

      <div class="grade-selector">
        <span class="selector-label">校区：</span>
        <el-select
          v-model="campusCode"
          placeholder="请选择校区"
          @change="changeCampusCode"
          size="medium"
          class="grade-select"
        >
          <el-option v-for="item in campusList" :key="item.code" :value="item.code" :label="item.name">
            {{ item.name }}
          </el-option>
        </el-select>
      </div>

      <div class="grade-selector">
        <span class="selector-label">年级：</span>
        <el-select v-model="gradeId" placeholder="全部年级" @change="changeGradeId" size="medium" class="grade-select">
          <el-option v-for="item in schGrdList" :key="item.id" :value="item.id" :label="item.name">
            {{ item.name }}
          </el-option>
        </el-select>
      </div>
    </div>

    <div class="role-container">
      <div v-for="(item, index) in roleList" :key="index" class="role-card">
        <div class="role-title">
          <i class="role-icon" :class="getRoleIcon(item.roleType)"></i>
          {{ item.roleName }}
        </div>
        <div class="role-content">
          <div class="subject-list">
            <div class="sub-title">所带学科：</div>
            <div class="tag-group">
              <div v-for="(subjectItem, sIndex) in item.subjectList" :key="sIndex" class="subject-item">
                <span class="tag subject-tag"
                  >{{ subjectItem.year ? subjectItem.gradeName : '' }}{{ subjectItem.subjectName }}</span
                >
              </div>
              <div v-if="!item.subjectList || item.subjectList.length === 0" class="empty-data">所属权限无学科</div>
            </div>
          </div>

          <!-- 班主任 -->
          <div v-if="item.roleType == 5" class="class-list">
            <div class="sub-title">所带班级:</div>

            <div class="tag-group">
              <span v-for="(gradeItem, gradeIndex) in leaderList" :key="gradeIndex" class="grade-item">
                <span class="grade-name">{{ gradeItem.gradeName }}</span>
                <div class="tag-group">
                  <span v-for="(classItem, classIndex) in gradeItem.classList" :key="classIndex" class="tag class-tag">
                    {{ classItem.className }}
                  </span>
                </div>
              </span>
              <div v-if="!leaderList || leaderList.length === 0" class="empty-data">所属权限无所带班级</div>
            </div>
          </div>

          <!-- 任课教师 -->
          <div v-else-if="item.roleType == 6" class="class-list">
            <div class="sub-title">任课班级:</div>
            <div class="tag-group">
              <span v-for="(gradeItem, gradeIndex) in substituteList" :key="gradeIndex" class="grade-item">
                <span class="grade-name">{{ gradeItem.gradeName }}</span>
                <div class="subject-container">
                  <span
                    v-for="(subjectItem, subjectIndex) in gradeItem.subjectList"
                    :key="subjectIndex"
                    class="subject-wrapper"
                  >
                    <div class="tag subject-head">{{ subjectItem.subjectName }}</div>
                    <div class="tag-group">
                      <span
                        v-for="(classItem, classIndex) in subjectItem.classList"
                        :key="classIndex"
                        class="tag class-tag"
                      >
                        {{ classItem.className }}
                      </span>
                    </div>
                  </span>
                </div>
              </span>
              <div v-if="!substituteList || substituteList.length === 0" class="empty-data">所属权限无任教班级</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="classlist-container">
      <div class="class-section">
        <div class="section-title">行政班</div>
        <div class="class-items">
          <div v-for="item in xzClassList" :key="item.classId" class="class-item">
            {{ item.className }}
          </div>
          <div v-if="!xzClassList || xzClassList.length === 0" class="empty-data">无所带行政班</div>
        </div>
      </div>

      <div class="class-section">
        <div class="section-title">分层班</div>
        <div class="class-items">
          <div v-for="item in fcClassList" :key="item.classId" class="class-item">
            {{ item.className }}
          </div>
          <div v-if="!fcClassList || fcClassList.length === 0" class="empty-data">无所带分层班</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { getSubjectListByRole } from '@/service/api';
import { deepClone } from '@/utils';
import UserRole, {
  ClassList,
  IRoleList,
  IUserGrdList,
  LeaderClassList,
  RoleEnum,
  SubstituteClassList,
} from '@/utils/UserRole';
import UserRoleUtils from '@/utils/UserRoleUtils';
import { Component, Vue } from 'vue-property-decorator';

// 分组方法
function groupBy(objectArray, property) {
  return objectArray.reduce((acc, obj) => {
    const key = obj[property];

    if (Array.isArray(key)) {
      // 如果属性值是数组,则对数组中每个值都创建一个分组
      key.forEach(k => {
        const curGroup = acc[k] ?? [];
        acc[k] = [...curGroup, obj];
      });
      return acc;
    } else {
      // 如果属性值不是数组,则直接以该值作为分组key
      const curGroup = acc[key] ?? [];
      return { ...acc, [key]: [...curGroup, obj] };
    }
  }, {});
}

@Component
export default class Role extends Vue {
  // 考试角色
  examRolesTypes = [];
  // 角色列表
  roleList = [];
  // 校区列表
  campusList = [];
  // 当前校区
  campusCode = '';
  // 学校年级列表
  schGrdList: IUserGrdList[] = [];
  // 年级id
  gradeId: string | number = '';
  // 年级对象
  get gradeItem(): IUserGrdList {
    return this.schGrdList.find(item => item.id === this.gradeId);
  }

  // 任课教师班级
  substituteClassList: SubstituteClassList[] = [];
  // 班主任班级
  leaderClassList: LeaderClassList[] = [];
  // 班级列表
  classList: ClassList[] = [];

  get xzClassList() {
    return this.classList.filter(item => {
      if (item.classType == 1) {
        if (!this.gradeId) return true;
        return this.gradeItem.systemCode == item.system_code && this.gradeItem.year == item.year;
      }
    });
  }

  get fcClassList() {
    return this.classList.filter(item => {
      if (item.classType == 3) {
        if (!this.gradeId) return true;
        return this.gradeItem.systemCode == item.system_code && this.gradeItem.year == item.year;
      }
    });
  }

  get substituteList() {
    let result = [];
    let yearMap = {};

    // 按年级分组
    this.substituteClassList.forEach(item => {
      if (!yearMap[item.year]) {
        yearMap[item.year] = {
          year: item.year,
          gradeName: item.gradeName,
          systemCode: item.system_code,
          subjectList: [],
        };
        result.push(yearMap[item.year]);
      }

      // 查找该年级下是否已有该学科
      let subject = yearMap[item.year].subjectList.find(s => s.subjectId === item.subjectId);
      if (!subject) {
        subject = {
          subjectId: item.subjectId,
          subjectName: item.subjectName,
          classList: [],
        };
        yearMap[item.year].subjectList.push(subject);
      }
      subject.classList.push(item);
    });

    if (this.gradeId && this.gradeItem) {
      result = result.filter(item => item.year == this.gradeItem.year && item.gradeName == this.gradeItem.name);
    }

    return result;
  }

  get leaderList() {
    let result = [];
    let yearMap = {};

    // 按年级分组
    this.leaderClassList.forEach(item => {
      if (!yearMap[item.year]) {
        yearMap[item.year] = {
          year: item.year,
          gradeName: item.gradeName,
          systemCode: item.systemCode,
          classList: [],
        };
        result.push(yearMap[item.year]);
      }
      yearMap[item.year].classList.push(item);
    });

    if (this.gradeId && this.gradeItem) {
      result = result.filter(item => item.year == this.gradeItem.year && item.systemCode == this.gradeItem.systemCode);
    }

    return result;
  }

  // 根据角色类型返回对应图标
  getRoleIcon(roleType: number) {
    switch (roleType) {
      case 1:
        return 'el-icon-s-platform';
      case 2:
        return 'el-icon-s-management';
      case 3:
        return 'el-icon-s-custom';
      case 4:
        return 'el-icon-s-check';
      case 5:
        return 'el-icon-s-flag';
      case 6:
        return 'el-icon-s-opportunity';
      default:
        return 'el-icon-s-help';
    }
  }

  async mounted() {
    this.campusList = deepClone(UserRole.campusList);
    this.campusList.unshift({
      code: '',
      name: '全部',
      subjectList: UserRole.roleSubjectList,
      classList: UserRole.classList,
      substituteClassList: UserRole.substituteClassList,
      leaderClassList: UserRole.leaderClassList,
    });

    this.examRolesTypes = UserRole.examRolesTypes.split(',').sort((a, b) => Number(a) - Number(b));
    const ret = await UserRole.getUserInfoPersonalityTest();
    this.schGrdList = ret.schGrdList;
    this.schGrdList.unshift({
      id: '',
      name: '全部',
    });
    this.getRoleList();
    this.classList = await UserRole.getClassList({ classType: -1 });
  }

  // 获取列表
  async getRoleList() {
    const subjectList = this.campusList.find(item => item.code == this.campusCode).subjectList;
    const roleSubjectMap = groupBy(subjectList, 'userType');
    const roleList = this.examRolesTypes.map(item => {
      let roleType = item;
      let roleName = UserRoleUtils.getExamRoleName(item);
      return {
        roleType,
        roleName,
        subjectList: (roleSubjectMap[item] || [])
          .sort((a, b) => Number(b.year) - Number(a.year))
          .filter(t => {
            if (!this.gradeId || !this.gradeItem) {
              return true;
            } else {
              if (roleType == RoleEnum.SUBJECT_LEADER) {
                return t.userTypes == String(RoleEnum.SUBJECT_LEADER) && t.phase == this.gradeItem.phaseId - 2;
              }
              return t.year === this.gradeItem.year && t.phase == this.gradeItem.phaseId - 2;
            }
          }),
      };
    });
    this.roleList = roleList;
    this.classList = this.campusList.find(item => item.code == this.campusCode).classList;
    this.substituteClassList = this.campusList.find(item => item.code == this.campusCode).substituteClassList;
    this.leaderClassList = this.campusList.find(item => item.code == this.campusCode).leaderClassList;
  }

  // 改变年级ID
  changeGradeId(gradeId: string) {
    this.gradeId = gradeId;
    this.getRoleList();
  }

  // 改变校区
  changeCampusCode(campusCode: string) {
    this.campusCode = campusCode;
    this.getRoleList();
  }
}
</script>

<style scoped lang="scss">
.role-page {
  padding: 20px;
  background-color: #f7f9fc;
  min-height: 100vh;
  font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, 'Microsoft YaHei', Arial, sans-serif;
}

.role-page-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32px;

  .role-page-title {
    font-size: 26px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 24px;
    text-align: center;
    position: relative;
    padding-bottom: 16px;

    &:after {
      content: '';
      position: absolute;
      width: 70px;
      height: 4px;
      background: linear-gradient(90deg, #4b6cb7, #182848);
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 3px;
    }
  }

  .grade-selector {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);

    .selector-label {
      font-size: 15px;
      font-weight: 500;
      color: #606266;
      margin-right: 12px;
    }

    .grade-select {
      width: 180px;
    }
  }
}

.role-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  gap: 24px;
  margin-bottom: 32px;

  .role-card {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    background-color: #fff;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    border: 1px solid #ebeef5;
    height: 100%;
    display: flex;
    flex-direction: column;

    &:hover {
      transform: translateY(-6px);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
    }

    .role-title {
      padding: 18px;
      font-size: 18px;
      font-weight: bold;
      color: #fff;
      background: linear-gradient(135deg, #4b6cb7, #182848);
      display: flex;
      align-items: center;
      justify-content: center;
      letter-spacing: 1px;

      .role-icon {
        margin-right: 10px;
        font-size: 20px;
      }
    }

    .role-content {
      padding: 22px;
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .sub-title {
      font-size: 15px;
      color: #5a6678;
      margin-bottom: 12px;
      font-weight: 500;
      display: flex;
      align-items: center;

      &:before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 16px;
        background: #4b6cb7;
        margin-right: 8px;
        border-radius: 2px;
      }
    }

    .subject-list {
      margin-bottom: 16px;
    }

    .tag-group {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }

    .grade-item {
      display: flex;
      flex-direction: column;
      width: 100%;
      margin-bottom: 16px;
      background-color: #f9fafc;
      border-radius: 8px;
      padding: 12px;

      .grade-name {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 10px;
        padding-bottom: 8px;
        border-bottom: 1px dashed #e0e6ed;
      }

      .subject-container {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
      }

      .subject-wrapper {
        display: inline-flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 10px;
        background-color: #f0f5ff;
        border-radius: 6px;
        padding: 12px;
        width: 100%;

        .subject-head {
          font-weight: 500;
          font-size: 14px;
          color: #1d4ed8;
          background: rgba(29, 78, 216, 0.1);
          border: none;
        }
      }
    }

    .tag {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 6px 14px;
      border-radius: 20px;
      font-size: 13px;
      transition: all 0.2s;

      &:hover {
        transform: scale(1.05);
      }

      &.subject-tag {
        background-color: #ecf6ff;
        color: #1890ff;
        border: 1px solid #a8d0ff;
        font-weight: 500;
      }

      &.class-tag {
        background-color: #f0fff4;
        color: #52c41a;
        border: 1px solid #b7eb8f;
      }
    }

    .empty-data {
      width: 100%;
      padding: 15px;
      text-align: center;
      color: #909399;
      font-size: 14px;
      background-color: #f9fafc;
      border-radius: 6px;
    }
  }
}

.classlist-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;

  .class-section {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
    padding: 20px;

    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 16px;
      padding-bottom: 10px;
      border-bottom: 2px solid #ebeef5;
    }

    .class-items {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
    }

    .class-item {
      background-color: #f0f7ff;
      border-radius: 6px;
      padding: 10px 16px;
      color: #1d4ed8;
      font-size: 14px;
      transition: all 0.2s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      }
    }
  }
}

// 响应式设计优化
@media (max-width: 768px) {
  .role-page {
    padding: 15px;
  }

  .role-page-header {
    margin-bottom: 24px;

    .role-page-title {
      font-size: 22px;
      margin-bottom: 20px;
    }

    .grade-selector {
      width: 100%;
      padding: 10px 15px;

      .grade-select {
        width: 140px;
      }
    }
  }

  .role-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .classlist-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .role-card {
    .role-title {
      padding: 14px;
      font-size: 16px;
    }

    .role-content {
      padding: 16px;
      gap: 12px;
    }

    .tag {
      padding: 5px 10px;
      font-size: 12px;
    }

    .grade-item {
      padding: 10px;
      margin-bottom: 12px;

      .grade-name {
        margin-bottom: 8px;
        padding-bottom: 6px;
      }
    }
  }
}

// 平板电脑
@media (min-width: 769px) and (max-width: 1024px) {
  .role-container {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}
</style>

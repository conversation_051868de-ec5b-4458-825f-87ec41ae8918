import BaseRender from './BaseRender';
import forced from './forced.js';

export default {
  name: 'BaseTableColumn',
  props: {
    column: Object,
    headerAlign: String,
    align: String,
  },
  components: {
    BaseRender,
  },
  methods: {
    setColumn() {
      if (this.column.type) {
        this.column.renderHeader = forced[this.column.type].renderHeader;
        this.column.render = this.column.render || forced[this.column.type].renderCell;
      }

      if (this.column.formatter) {
        this.column.render = (h, scope) => {
          return h('span', {}, [
            scope.column.formatter(scope.row, scope.column, scope.row, scope.$index),
          ]);
        };
      }
      if (!this.column.render) {
        this.column.render = (h, scope) => {
          const value = scope.row[scope.column.property];
          if (value === undefined || value === null || value === '') {
            return h('span', {}, '--');
          } else {
            return h('span', {}, [scope.row[scope.column.property]]);
          }
        };
      }
    },
  },
  watch: {
    column: {
      handler() {
        this.setColumn();
      },
      immediate: true,
    },
  },
  render(h) {
    const columnChildren = this.column.children
      ? this.column.children.map((col, index) => (
          <base-table-column
            key={index}
            column={col}
            align={col.align || this.align || 'left'}
            header-align={col.headerAlign || this.headerAlign || col.align || this.align || 'left'}
          />
        ))
      : null;

    const scopedSlots = {
      default: scope => {
        return <base-render scope={scope} render={this.column.render} />;
      },
      header: scope => {
        if (this.column.renderHeader) {
          return <base-render scope={scope} render={this.column.renderHeader} />;
        } else {
          return <span>{this.column.label || this.column.title} </span>;
        }
      },
    };
    return (
      <el-table-column
        attrs={this.$attrs}
        on={this.$listeners}
        prop={this.column.prop}
        label={this.column.label}
        title={this.column.title}
        type={this.column.type}
        index={this.column.index}
        column-key={this.column.columnKey}
        width={this.column.width}
        min-width={this.column.minWidth}
        fixed={this.column.fixed}
        render-header={this.column.renderHeader}
        sortable={this.column.sortable || false}
        sort-method={this.column.sortMethod}
        sort-by={this.column.sortBy}
        sort-orders={this.column.sortOrders}
        resizable={this.column.resizable || false}
        formatter={this.column.formatter}
        show-overflow-tooltip={this.column.showOverflowTooltip || false}
        align={this.column.align || this.align || 'left'}
        header-align={
          this.column.headerAlign || this.headerAlign || this.column.align || this.align || 'left'
        }
        class-name={this.column.className}
        label-class-name={this.column.labelClassName}
        selectable={this.column.selectable}
        reserve-selection={this.column.reserveSelection || false}
        filters={this.column.filters}
        filter-placement={this.column.filterPlacement}
        filter-multiple={this.column.filterMultiple}
        filter-method={this.column.filterMethod}
        filtered-value={this.column.filteredValue}
        scopedSlots={scopedSlots}
      >
        {columnChildren}
      </el-table-column>
    );
  },
};

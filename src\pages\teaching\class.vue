<template>
  <div class="classPage">
    <div class="block score-trend">
      <div class="title">成绩趋势</div>
      <score-chart
        :clzSore="clzSore"
        :classId="classId"
        v-if="clzSore.length > 0"
        v-on="$listeners"
        ref="clsScore"
      ></score-chart>
      <div class="nodata flex_1" v-else>
        <img :src="noResImg" alt="" />
        <p class="text-center">暂无数据</p>
      </div>
    </div>
    <div class="block class-table">
      <div class="title">
        薄弱知识点
        <el-popover placement="top-start" title="" width="500" trigger="hover">
          <div style="line-height: 25px; font-size: 13px">

            {{  `标红薄弱知识点： ${classId ? '班级' : '年级'}掌握率低于60%的知识点。` }}<br />
            掌握率：阶段时间内历次考试个册作业中知识点的得分率即掌握率。<br />
            {{ `${classId ? '班级' : '年级'}未掌握人数：个人知识点得分率低于60%的学生人数。` }}
          </div>
          <i class="el-icon-question" slot="reference"></i>
        </el-popover>
      </div>
      <el-table
        class="classTable"
        v-if="tableData.length"
        ref="tableRef"
        :data="tableData"
        stripe
        @sort-change="sortChange"
      >
        <!--知识点-->
        <el-table-column prop="pointName" fixed label="知识点" width="200">
          <template slot-scope="scope">
            <span :style="{ color: classId ? (scope.row.clsRate < 0.6 ? 'red' : '') : (scope.row.grdRate < 0.6 ? 'red' : '') }">{{
              scope.row.pointName
            }}</span>
          </template>
        </el-table-column>
        <!--班级掌握率-->
        <el-table-column fixed sortable="custom" prop="clsScoreRate" label="班级掌握率" v-if="classId">
          <template slot-scope="scope">
            <span>{{ Number(scope.row.clsRate * 100).toFixed(2) }}%</span>
          </template>
        </el-table-column>
        <!--年级掌握率-->
        <el-table-column fixed sortable="custom" prop="grdScoreRate" label="年级掌握率">
          <template slot-scope="scope">
            <span>{{ Number(scope.row.grdRate * 100).toFixed(2) }}%</span>
          </template>
        </el-table-column>
        <!--班级未掌握人数-->
        <el-table-column fixed prop="failNum" :label="classId ? '班级未掌握人数' : '年级未掌握人数'"> </el-table-column>
        <!--考频-->
        <el-table-column fixed sortable="custom" prop="frequency" label="考频"> </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <el-button @click="handleClick(scope.row)" type="text" size="small"
              >查看详情 <i class="el-icon-arrow-right"></i
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="nodata flex_1" v-if="!tableData.length">
        <img :src="noResImg" alt="" />
        <p class="text-center">暂无数据</p>
      </div>
      <!--分页器-->
      <el-pagination
        background
        v-show="showPagination && tableData.length"
        :hide-on-single-page="!tableData.length"
        style="margin: 15px auto"
        class="text-center"
        layout="total, prev, pager, next"
        @current-change="handleCurrentChange"
        :current-page.sync="pagination.page"
        :page-size="pagination.limit"
        :total="pagination.total_rows"
      >
      </el-pagination>
    </div>
    <div class="block class-mistack clearfix">
      <div class="title"> {{ classId ? '班级共性错题' : '年级共性错题' }}</div>
      <clsWrong v-on="$listeners" ref="clsWrong" :classId="classId"></clsWrong>
    </div>
  </div>
</template>

<script>
import { listClsWeakPoint, getClzHistoryScore } from '@/service/pexam';
import clsWrong from '@/components/paper/clsWrong.vue';
import ScoreChart from '@/components/paper/scoreChart.vue';
export default {
  name: 'class',
  components: {
    clsWrong,
    ScoreChart,
  },
  props: ['subjectId', 'classId'],
  computed: {},
  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      listLoading: false,
      // 表格数据
      tableData: [],
      // 左侧知识点列表分页配置
      pagination: {
        page: 1,
        pageSize: 10,
        total_rows: 0,
      },
      showPagination: true,
      // 排序方式
      sortType: '',
      //考试成绩
      scoreData: [],
      //班级历史考试成绩
      clzSore: [],
    };
  },
  mounted() {
    this.$emit('updateData', 'child');
    // this.getClzHistoryScore();
  },
  methods: {
    getClzHistoryScore() {
      getClzHistoryScore({
        ...this.$listeners.getParams(),
      })
        .then(res => {
          this.clzSore = res.data;
        })
        .catch(err => {
          this.clzSore = [];
        });
    },
    // 获取薄弱知识点列表
    listClsWeakPoint(isFromParent, initScore) {
      this.listLoading = true;
      let curParams = {
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
        sort: this.sortType,
        q: '',
      };
      listClsWeakPoint({
        ...curParams,
        ...this.$listeners.getParams(),
      })
        .then(res => {
          this.listLoading = false;
          this.pagination.total_rows = res.data.total;
          this.tableData = res.data.list || [];
        })
        .catch(err => {
          this.listLoading = false;
        });
      if (isFromParent) {
        this.$nextTick(() => {
          if (initScore === 'initScore') {
            this.$refs.clsWrong.initScoreRate();
          } else {
            this.$refs.clsWrong.listErrQues();
          }
        });
      }
    },
    // 得分率恢复默认值全部
    initScoreRate() {
      this.$refs.clsWrong && this.$refs.clsWrong.initScoreRate();
    },
    // 清空数据
    emptyData() {
      this.pagination.total_rows = 0;
      this.tableData = [];
      this.$refs.clsWrong.emptyData();
    },
    // 排序
    sortChange({ prop, order }) {
      this.sortType = order === 'ascending' ? prop : '-' + prop;
      this.listClsWeakPoint();
    },
    // 表格中点击查看详情
    handleClick(item) {
      this.$sessionSave.set('pointDetailList', this.tableData);
      const params = this.$listeners.getParams(1);
      this.$router.push({
        path: '/home/<USER>/classDetail',
        query: {
          pointId: item.pointId,
          subId: this.subjectId || '',
          pagination: JSON.stringify(this.pagination),
          sortType: this.sortType,
          classId: params.classId,
          gradeId: params.gradeId,
          subjectId: params.subjectId,
          startTime: params.startTime,
          endTime: params.endTime,
        },
      });
    },
    // 表格中切换分页
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.listClsWeakPoint();
    },
  },
};
</script>

<style lang="scss" scoped>
.classPage {
  font-family: Microsoft YaHei;
  padding-bottom: 30px;
  .block {
    width: 100%;
    position: relative;
    padding: 16px 28px 16px 28px;
    background: #fff;
    box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
    border-radius: 6px;
    &:before {
      content: '';
      position: absolute;
      left: 28px;
      top: 24px;
      width: 6px;
      height: 24px;
      background: #409eff;
      border-radius: 3px;
    }
    .title {
      padding-left: 16px;
      font-size: 16px;
      font-weight: bold;
      color: #3f4a54;
      line-height: 38px;
    }
  }
  .class-table {
    margin-top: 20px;

    .classTable {
      margin-top: 16px;
      width: 100%;
      margin-bottom: 20px;
    }
  }
  .class-mistack {
    margin-top: 20px;
  }
}
.question_body {
  padding: 0 35px;
}
</style>
<style lang="scss">
.classTable {
  border: 1px solid #e4e8eb;
  &.el-table th,
  &.el-table td {
    text-align: center;
  }
  .el-table__header th {
    padding: 4px 0;
  }
  &.el-table thead {
    font-size: 16px;
    color: rgb(63, 74, 84);
  }
  &.el-table .warning-row {
    color: #f56353ff;
  }
}

.class-cascader {
  .el-cascader-menu {
    overflow-x: hidden;
    overflow-y: auto;
  }
}
</style>

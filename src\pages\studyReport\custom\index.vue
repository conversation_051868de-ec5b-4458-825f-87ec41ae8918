<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-03-18 15:39:50
 * @LastEditors: 小圆
-->
<template>
  <div class="study-report">
    <div class="study-report__title title-tag" v-if="menuTitle">{{ menuTitle }}</div>
    <transition name="slide-fade" mode="out-in">
      <router-view v-if="FilterModule.isInit" class="study-report__main"></router-view>
    </transition>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import {
  IClassInfo,
  IExamReportInfo,
  FilterDataKey,
  ISubjectInfo,
} from '@/pages/studyReport/plugins/types';
import FilterModule from '@/pages/studyReport/plugins/FilterModule';

import ReportHeader from '../components/ReportHeader.vue';

@Component({
  components: {
    ReportHeader,
  },
})
export default class CustomReport extends Vue {
  // 监听筛选单例
  FilterModule: typeof FilterModule = FilterModule;

  get menuTitle() {
    return this.$route.meta.title;
  }
}
</script>

<style scoped lang="scss">
@import '../index.scss';
</style>

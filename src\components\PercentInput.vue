<template>
  <div class="percent-input-box">
    <NumericInput
        v-model="value"
        :type="'digit'"
        :isPositive="true"
    ></NumericInput>
    <div class="icon">%</div>
  </div>
</template>

<script>
import NumericInput from "@/components/NumericInput";

export default {
  name: "PercentInput",
  components: {
    NumericInput
  },
  props: {
    percentValue: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      value: this.percentValue
    }
  },
  watch: {
    value(newVal) {
      this.$emit('update:percentValue', newVal)
    }
  }
}
</script>

<style lang="scss" scoped>
.percent-input-box {
  width: 90px;
  display: inline-flex;
  justify-content: center;
  align-items: center;

  .icon {
    padding: 0 10px;
    height: 30px;
    line-height: 30px;
    box-sizing: border-box;
    white-space: nowrap;
    background-color: #f5f7fa;
    color: #909399;
    position: relative;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    transform: translateX(-1px);
  }
}
</style>
<style lang="scss">
.percent-input-box {
  .numeric-input-box .numeric-input-inner {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
  }
}
</style>

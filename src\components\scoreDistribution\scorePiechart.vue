<template>
  <div>
    <div v-show="!showDefault" id="totalRankChart" class="echart" ref="echart" style="width: 100%; height: 400px"></div>
    <!-- <div v-show="showDefault">
      <div style="text-align: center">
        <img style="width: 350px" :src="noResImg" alt="" />
      </div>
      <p style="text-align: center; font-size: 16px; margin-top: 10px">暂无数据!</p>
    </div> -->
  </div>
</template>

<script>
export default {
  name: "scoreChart",
  props: {
    tableData: {
      type: Array,
      default() {
        return [];
      },
    },
    targetData: {
      type: Array,
      default() {
        return [];
      },
    },
    checktype: {
      type: String,
      default() {
        return "";
      },
    },
  },
  data() {
    return {
      noResImg: require("@/assets/no-res.png"),
      totalRankChart: null,
      showDefault: false,
      source: [],
      seriesData: [],
      xAxisTitle: [],
      indicatorData: [],
    };
  },
  watch: {
    tableData: {
      handler(val) {
        if (val.length) {
          this.showDefault = false;
        }
        this.resetDomSize("totalRankChart", 400);
        this.$nextTick(() => {
          this.drawImg();
        });
      },
      deep: true,
    },
  },
  beforeDestroy() {
    if (this.totalRankChart != null && this.totalRankChart != "" && this.totalRankChart != undefined) {
      this.totalRankChart.dispose();
      this.totalRankChart = null;
    }
  },
  mounted() {
    let _this = this;
    window.onresize = () => {
      return (() => {
        if (_this.totalRankChart) {
          this.resetDomSize("totalRankChart", 400);
          _this.totalRankChart.resize();
        }
      })();
    };
    if (!this.totalRankChart) {
      this.drawImg();
    }
  },
  methods: {
    resetDomSize(el, height) {
      let width = document.getElementById("totalRankChart").clientWidth;
      Object.defineProperty(document.getElementById(el), "clientWidth", {
        get: function () {
          return width;
        },
        configurable: true,
      });
      Object.defineProperty(document.getElementById(el), "clientHeight", {
        get: function () {
          return height;
        },
        configurable: true,
      });
    },
    handleChartData() {
      let data = this.tableData && this.tableData.length ? this.tableData : [];
      //分段名次
      if (data.length) {
        this.seriesData = [];
        this.indicatorData = [];
        data.forEach((ite, inde) => {
          let arr = [];
          ite.data.forEach((it, ind) => {
            arr.push(it.rate);
          });
          let obj = {
            name: ite.examName,
            value: arr,
          };
          this.seriesData.push(obj);
        });
        let subjectObj = {};
        this.targetData.forEach((item) => {
          subjectObj = {
            name: item.name,
            max: 1,
          };
          this.indicatorData.push(subjectObj);
        });
      } else {
        this.showDefault = true;
      }
    },
    drawImg() {
      if (this.totalRankChart != null && this.totalRankChart != "" && this.totalRankChart != undefined) {
        this.totalRankChart.dispose();
        this.totalRankChart = null;
      }
      this.handleChartData();
      let _this = this;

      let seriesItem = {
        type: "bar",
        itemStyle: { barBorderRadius: [3, 3, 0, 0] },
        barWidth: 14,
      };
      this.totalRankChart = this.$echarts.init(this.$refs.echart);
      this.totalRankChart.setOption({
        legend: {
          show: true,
          type: "scroll",
          orient: "vertical",
          // icon: "pin",
          right: "3%",
          top: "20%",
          height: "60%",
          textStyle: {
            fontSize: 14,
            color: "#757C8C",
          },
          formatter: function (name) {
            return name.length > 25 ? name.substr(0, 25) + "..." : name;
          }, //开启tooltip
          tooltip: {
            show: true,
          },
        },
        title: {
          subtext: `注：可点击考试名称查看/取消查看难度分布`,
          right: "12%",
          top: "11%",
          subtextStyle: {
            fontSize: 14,
            color: "#4C5866",
          },
        },
        tooltip: {
          //提示框组件
          trigger: "item", //item数据项图形触发，主要在散点图，饼图等无类目轴的图表中使用。
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          //{a}（系列名称），{b}（数据项名称），{c}（数值）, {d}（百分比）
          // formatter: function (params) {
          //   let str = " ";
          //   for (let item in _this.targetData) {
          //     str =
          //       str +
          //       _this.targetData[item].name +
          //       "&nbsp" +
          //       ":" +
          //       "&nbsp" +
          //       params.value[item] +
          //       "<br>";
          //   }
          //   return params.name + "<br>" + str;
          // },
        },
        radar: {
          indicator: this.indicatorData,
        },
        series: [
          {
            name: "",
            type: "radar",
            data: this.seriesData,
            // label: {
            //   normal: {
            //     show: true,
            //     formatter: function (params) {
            //       return params.value;
            //     },
            //   },
            // },
          },
        ],
      });
    },
  },
};
</script>

<style scoped></style>

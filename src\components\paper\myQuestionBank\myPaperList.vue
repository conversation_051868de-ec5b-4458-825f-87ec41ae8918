<!--
 * @Description: 我的卷库列表
 * @Author: qmzhang
 * @Date: 2025-05-12 10:44:12
 * @LastEditTime: 2025-05-20 09:06:47
 * @FilePath: \personal-bigdata\src\components\paper\myQuestionBank\myPaperList.vue
-->
<template>
    <div class="my-paper-list">
        <el-input class="paper-search" placeholder="请输入试卷名称搜索" v-model="keyword" clearable @clear="getTestPaperList()"
            @keydown.enter.native="getTestPaperList()">
            <el-button slot="append" icon="el-icon-search" @click="getTestPaperList()"></el-button>
        </el-input>

        <el-table class="paperTable" v-loading="tableLoading" ref="tableRef" :data="tableData" :row-style="{ padding: 0 }"
            :cell-style="{ position: 'relative' }" stripe border>
            <!--试卷标题-->
            <el-table-column prop="tbName" label="试卷名称" style="position: relative">
                <template slot-scope="scope">
                    {{ scope.row.tbName }}
                    <i v-if="scope.row.needCheckPassword" style="color:#ff0000;" class="el-icon-lock"></i>
                </template>
            </el-table-column>
            <el-table-column sortable="custom" prop="qcount" label="试题数" width="100" align="center" />
            <!--创建时间-->
            <el-table-column prop="createTime" label="创建时间" width="240" />
            <!--操作-->
            <el-table-column label="操作" fixed="right" width="100">
                <template slot-scope="scope">
                    <el-button type="text" @click="onSlelectQues(scope.row)">选择题目</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!--分页器-->
        <el-pagination background style="margin: 20px auto 0" :hide-on-single-page="!tableData.length" class="text-center"
            layout="total, prev, pager, next" @current-change="handleCurrentChange" :current-page.sync="pagination.page"
            :page-size="pagination.limit" :total="pagination.total_rows">
        </el-pagination>
    </div>
</template>

<script lang="ts">
import { State, Getter } from 'vuex-class';
import { Component, Vue } from 'vue-property-decorator';
import { checkTbPassword, getTestPaperList } from '@/service/testbank';

@Component
export default class myPaperList extends Vue {
    @Getter loginInfo;

    tableLoading: boolean = false;
    keyword: string = ''
    tableData = []
    pagination = {
        page: 1,
        pageSize: 10,
        total_rows: 0,
        limit: 10,
    }

    // 父级参数
    parentParams = null;

    async created() {
        let $listeners: any = this.$listeners
        this.parentParams = $listeners.getParams();
        this.getTestPaperList();
    }

    // 分页查询
    handleCurrentChange(val) {
        this.pagination.page = val;
        this.getTestPaperList('changePage');
    }

    async getTestPaperList(type?: string) {
        try {
            let loginInfo = this.$sessionSave.get('loginInfo');
            this.tableLoading = true;
            this.pagination.page = type === 'changePage' ? this.pagination.page : 1;
            let params = {
                type: 0,
                isUse: 1,
                schoolId: this.$sessionSave.get('schoolInfo').id,
                userId: loginInfo.id,
                serviceVersion: 5,
                page: this.pagination.page,
                limit: 10,
                subjectCode: this.parentParams.subjectId,
                tbName: this.keyword,
                name: this.keyword,
            };
            let data = await getTestPaperList(params)
            this.tableLoading = false;
            this.tableData = data.data.rows || [];
            this.pagination.total_rows = data.data.total_rows;
        } catch (error) {
            console.error(error)
        } finally {
            this.tableLoading = false;
        }
    }

    async onSlelectQues(data) {
        let isSuccess = await this.checkTbPower(data);
        isSuccess && this.$emit('select', data)
    }

    /**
     * @description: 检查是否加密文件
     * @param {*} item
     * @return {*}
     */
    async checkTbPower(item: any): Promise<boolean> {
        return new Promise((resolve, reject) => {
            if (!item.needCheckPassword) return resolve(true);
            let pwdId = 'pwd_' + new Date().getTime();
            const h = this.$createElement;
            this.$msgbox({
                title: '提示',
                message: h('div', null, [
                    h('p', null, '该文件已加密，请输入密码后操作 '),
                    h('input', {
                        attrs: {
                            'id': pwdId,
                            'placeholder': '请输入密码',
                            'type': 'password',
                            'autocomplete': 'new-password',
                            'autoComplete': 'new-password'
                        },
                        style: {
                            'height': '30px',
                            'border-radius': '5px',
                            'border': '1px solid gray',
                            'outline': '1px',
                            'padding': '5px'
                        }
                    }),
                    h('p', null, '发布成绩后自动解密'),
                ]),
                beforeClose: async (action, instance, done) => {
                    if (action === 'confirm') {
                        const pwd = document.getElementById(pwdId).value;
                        let isSuccess = await this.checkTbPassword(pwd, item.id);
                        if (isSuccess) {
                            document.getElementById(pwdId).value = '';
                            resolve(true)
                            done();
                        }
                    } else {
                        document.getElementById(pwdId).value = '';
                        resolve(false)
                        done();
                    }
                },
                showCancelButton: true,
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            });
        });
    }

    async checkTbPassword(pwd, id) {
        let res = await checkTbPassword({
            tbId: id,
            password: pwd
        });

        return res?.code == 1
    }
}
</script>

<style lang="scss" scoped>
.paper-search {
    margin-bottom: 15px;
    width: 400px;
}

.paperTable {
    border: 1px solid #e4e8eb;

    ::v-deep .el-table__cell {
        padding: 0;
    }

    &.el-table th,
    &.el-table td {
        text-align: center;
    }

    &.el-table th.el-table__cell {
        background-color: #f5f7fa !important;
    }

    &.el-table thead {
        font-size: 16px;
        color: rgb(63, 74, 84);
    }

    .el-table td,
    .el-table th.is-leaf {
        border: 0.5px solid #ebeef5;
    }

    .square {
        width: 0;
        height: 0;
        border: 16px solid transparent;
        border-top: 16px solid #409eff;
        border-left: 16px solid #409eff;
        z-index: 100;
        border-radius: 10px 0 0 0;
        position: absolute;
        left: 0;
        top: 0;

        .square-word {
            position: absolute;
            left: -12px;
            top: -16px;
            color: #ffffff;
            font-size: 13px;
        }
    }
}
</style>
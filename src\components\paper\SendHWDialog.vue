<template>
  <!--基本信息弹窗-->
  <el-dialog
    title="布置作业"
    :visible="true"
    :before-close="closeDialog"
    width="600px"
    custom-class="send-hw-dialog"
  >
    <el-form ref="form" v-if="paperInfo" label-width="120px">
      <el-form-item label="试卷名称：">
        <el-input
          v-model="paperInfo.tbName"
          disabled
          maxlength="20"
          readonly
        ></el-input>
      </el-form-item>

      <el-form-item label="发送类型：">
        <subject-select @switch-subject="switchSubject"></subject-select>
      </el-form-item>

      <el-form-item label="批阅类型：" style="pointer-events: none">
        <el-radio :label="1" v-model="defaultOptions.markingType"
          >手阅作业</el-radio
        >
        <el-link
          type="info"
          :underline="false"
          style="text-indent: 5px; color: #f56c6c"
        >
          纸质作业批改，扫描采集批改数据，生成在线报告
        </el-link>
      </el-form-item>

      <el-divider></el-divider>
      <div class="hw-select-class">
        <p class="label">选择班级：</p>
        <el-tree
          v-if="xzList[0].children.length > 0"
          ref="xzClass"
          show-checkbox
          :data="xzList"
          class="class-tree"
          :props="{ label: 'showName', children: 'children' }"
          node-key="id"
          :empty-text="''"
          :indent="20"
          :default-expanded-keys="[CLASS_NAME.XZ]"
          :highlight-current="true"
          :check-on-click-node="true"
          :expand-on-click-node="false"
        >
          <span
            slot-scope="{ node, data }"
            class="el-tree-node__label ellipsis"
            :title="node.label"
          >
            {{ node.label }}
            <span v-if="data.noNumber" class="class-no-number"
              >该班有学生未关联考号，请联系运营人员</span
            >
          </span>
        </el-tree>
      </div>

      <!--分层-->
      <!--        <el-tree-->
      <!--            v-if="fcList[0].children.length>0"-->
      <!--            ref="fcClass"-->
      <!--            show-checkbox-->
      <!--            class="class-tree"-->
      <!--            :data="fcList"-->
      <!--            :props="{ label: 'showName', children: 'children'}"-->
      <!--            node-key="id"-->
      <!--            :indent="20"-->
      <!--            :empty-text="''"-->
      <!--            :default-expanded-keys="[CLASS_NAME.FC]"-->
      <!--            :check-on-click-node="true"-->
      <!--            :highlight-current="true"-->
      <!--            :expand-on-click-node="false"-->
      <!--        >-->
      <!--            <span slot-scope="{ node, data }" class="el-tree-node__label ellipsis" :title="node.label">-->
      <!--             {{ node.label }}-->
      <!--            </span>-->
      <!--        </el-tree>-->
      <el-link
        type="info"
        :underline="false"
        style="text-indent: 30px; color: #f56c6c"
      >
        注：授课教师/备课组长已发送过的班级授课教师不支持再次发送。
      </el-link>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="sendHomeWork"
        >确 认</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import SubjectSelect, {
  CLASS_TYPE,
} from "@/components/paper/SubjectSelect.vue";
import {
  getClassListAPI,
  getQueWorkInfoAPI,
  saveBankJSONAPI,
} from "@/service/api";
import {
  exportPaperAPI,
  getClassStudentNumber,
  getPaperClassAPI,
  getScanPaperWorkIdAPI,
  saveScanPaperAPI,
} from "@/service/pexam";
import moment from "moment";
import { getByKey } from "@/utils/index";

const CLASS_NAME = {
  // 行政班
  XZ: "zxall",
  // 分层班
  FC: "fcall",
};

export default {
  name: "send-hw-dialog",

  components: { SubjectSelect },

  props: ["info", "visible"],

  data() {
    return {
      // 试卷信息
      paperInfo: {
        // 试卷名称
        tbName: "",
        // 试卷id
        id: "",
      },
      // 发送参数
      defaultOptions: {
        markingType: 1,
      },
      // 行政班级列表
      xzList: [
        {
          id: CLASS_NAME.XZ,
          showName: "行政班",
          children: [],
        },
      ],
      // 分层班级列表
      fcList: [
        {
          id: CLASS_NAME.FC,
          showName: "分层班",
          children: [],
        },
      ],
      // 已选班级列表
      disabledClassMap: new Map(),
      disabledClassTypeMap: {},
      // 当前学课
      curSubject: {},
      // 题目信息
      quesdatas: [],
      // 作业id
      workId: "",
      // 班级名称
      CLASS_NAME: CLASS_NAME,
      // 创建类型
      CREATE_TYPE: {
        // 普通老师
        TEA: 1,
        // 年级组长
        GRADE: 2,
      },
      // 是否新增作业试卷关系
      isAdd: true,
      classNumberMap: {},
      //保存中
      loading: false,
    };
  },

  computed: {
    // 是否年级组长
    isGrade() {
      return (
        this.curSubject.subjectType === CLASS_TYPE.substitute &&
        this.$store.state.isTeaLeader
      );
    },
    // 用户id
    userId() {
      return this.$store.state.loginInfo.id;
    },
    // 学校id
    schoolId() {
      return this.$store.state.loginInfo.schoolid;
    },
  },

  mounted() {
    this.paperInfo = this.info;
  },

  methods: {
    /**
     *
     */
    async getClassNumberInfo(classIds) {
      let ids = [];
      classIds.forEach((id) => {
        if (!this.classNumberMap[id]) {
          ids.push(id);
        }
      });

      await getClassStudentNumber({
        classIds: ids.join(","),
        schoolId: this.schoolId,
      })
        .then((data) => {
          if (data.code === 1) {
            data.data.forEach((id) => {
              this.classNumberMap[id] = true;
            });
          }
        })
        .catch((err) => {
          if (err.code === -1) {
            classIds.forEach((id) => {
              this.classNumberMap[id] = true;
            });
          }
        });
    },
    /**
     * @name: 获取班级列表
     */
    async getClassList() {
      const params = {
        userId: this.userId,
        subjectId: this.curSubject.subjectId,
        schoolId: this.schoolId,
        year: this.isGrade ? parseInt(this.curSubject.year) : undefined,
        systemCode: this.isGrade ? this.curSubject.systemCode : undefined,
        type: this.isGrade ? this.CREATE_TYPE.GRADE : this.CREATE_TYPE.TEA,
        page: 1,
        limit: 100,
      };
      const result = await getClassListAPI(params);
      if (result.code == 1) {
        let res = result.data;
        let groupXzClass = [];
        let groupFcClass = [];

        let classIds = [];
        // 行政
        for (let i = 0; i < res.xzList.length; i++) {
          let item = res.xzList[i];
          if (
            item.studentCount <= 0 ||
            item.phase != this.curSubject.phase ||
            getByKey(groupXzClass, "id", item.id) !== null
          ) {
            res.xzList.splice(i, 1);
            i--;
          } else {
            item.isXZ = true;
            item.classId = item.id;
            classIds.push(item.id);
            item.children = [];
            item.showName = `${item.fullClassName}  (${item.studentCount}人)`;

            let cloneItem = Object.assign({}, item);
            const choosedClass = this.disabledClassMap.get(item.classId);
            if (this.isGrade) {
              cloneItem.disabled =
                choosedClass !== undefined &&
                choosedClass.createType == this.CREATE_TYPE.GRADE;
            } else {
              cloneItem.disabled = choosedClass !== undefined;
            }
            groupXzClass.push(cloneItem);
          }
        }

        await this.getClassNumberInfo(classIds);

        groupXzClass.forEach((classInfo) => {
          classInfo.noNumber = this.classNumberMap[classInfo.classId];
          classInfo.disabled = classInfo.disabled || classInfo.noNumber;
        });
        // 分层
        // for (let i = 0; i < res.fcList.length; i++) {
        //   let item = res.fcList[i]
        //   if(item.studentCount<=0 || item.phase !=this.curSubject.phase || getByKey(groupFcClass,'id',item.id)){
        //     res.fcList.splice(i,1)
        //     i--
        //   }else{
        //     item.classId = item.id
        //     item.isXZ = false
        //     item.children = []
        //     item.showName = `${item.fullClassName}  (${item.studentCount}人)`
        //
        //     let cloneItem = Object.assign({},item)
        //     cloneItem.disabled = this.disabledClassMap.has(item.classId)
        //     groupFcClass.push(cloneItem)
        //   }
        // }
        this.xzList = [
          { showName: "行政班", children: groupXzClass, id: CLASS_NAME.XZ },
        ];
        // this.fcList = [{ showName: '分层班', children:groupFcClass, id: CLASS_NAME.FC}]

        this.$nextTick(() => {
          this.setDefaultChecked();
        });
      } else {
        this.$message(result.msg);
      }
    },
    /**
     * @name: 获取已选班级列表
     */
    async getPaperClassList() {
      const params = {
        sourceId: this.paperInfo.id,
        schoolId: this.schoolId,
        createType: this.isGrade
          ? this.CREATE_TYPE.GRADE
          : this.CREATE_TYPE.TEA,
      };
      const result = await getPaperClassAPI(params);
      this.disabledClassMap = new Map();
      if (result.code === 1) {
        result.data.forEach((item) => {
          if (item.classId) {
            this.disabledClassMap.set(item.classId, item);
            this.disabledClassTypeMap[item.classId + "_" + item.createType] =
              item;
          }
        });
      } else {
        this.$message(result.msg);
      }
    },
    /**
     * @name: 获取作业id
     */
    async getWorkId() {
      const params = {
        userId: this.userId,
        schoolId: this.schoolId,
        sourceId: this.paperInfo.id,
        createType: this.isGrade
          ? this.CREATE_TYPE.GRADE
          : this.CREATE_TYPE.TEA,
      };
      let res = await getScanPaperWorkIdAPI(params);
      if (res.code === 1) {
        this.workId = res.data;
      } else {
        this.$message.warning(res.msg);
      }
    },
    /**
     * @name: 获取作业信息
     */
    async getWorkInfo() {
      const result = await getQueWorkInfoAPI({
        id: this.workId,
      });

      if (result.code == 1) {
        let quesdatas = JSON.parse(result.data.workJson).quesdatas;
        try {
          quesdatas = JSON.parse(quesdatas);
        } catch (e) {
          quesdatas = quesdatas;
        }
        this.isAdd = result.data.isDelete == 1 ? true : false;
        this.quesdatas = this.isAdd ? [] : quesdatas;
      } else {
        this.isAdd = true;
        this.quesdatas = [];
      }
    },
    /**
     * @name: 学科切换
     * @param subItem 当前学科对象
     */
    async switchSubject(subItem) {
      this.curSubject = subItem;
      await this.getWorkId();
      this.workId && this.getWorkInfo();
      await this.getPaperClassList();
      await this.getClassList();
    },

    /**
     * @name: 试卷下载
     */
    async downloadPaper() {
      const params = {
        schoolId: this.schoolId,
        sourceId: this.info.id,
        title: this.info.tbName,
        paperType: "A4",
        exportType: "stu_exam_paper_no_answer",
      };
      const res = await exportPaperAPI(params);
      if (res.code === 1 && res.data) {
        window.open(
          `${res.data}&response-content-type=application%2Foctet-stream`
        );
      } else {
        this.$message(res.msg);
      }
    },
    /**
     * @name: 设置默认选中数据
     */
    setDefaultChecked() {
      const xzClass = this.$refs.xzClass;
      // const fcClass = this.$refs.fcClass
      const keys = [...this.disabledClassMap.keys()].filter((id) => {
        const choosedClass = this.disabledClassMap.get(id);
        if (this.isGrade) {
          return (
            choosedClass !== undefined &&
            choosedClass.createType === this.CREATE_TYPE.GRADE
          );
        } else {
          return choosedClass !== undefined;
        }
      });

      if (xzClass) {
        xzClass.setCheckedKeys(keys);
      }
      // if(fcClass){
      //   fcClass.setCheckedKeys(keys)
      // }
    },
    /**
     * @name: 构建题库提交数据
     * @description: 班级列表
     *     普通老师发，普通老师补发，选中的班级全要；
     普通老师发，备课组长补发，选中的班级全要；
     备课组长发，备课组长补发，选中的班级全要；
     备课组长发，普通老师补发，只要普通老师自己选中的
     */
    buildSubmitData() {
      const objectList = [];
      const xzClass = this.$refs.xzClass;
      // const fcClass = this.$refs.fcClass
      if (xzClass) {
        xzClass.getCheckedNodes().forEach((item) => {
          if (
            item.id !== CLASS_NAME.XZ &&
            !getByKey(objectList, "classId", item.id)
          ) {
            const objectItem = {
              classId: item.id,
              className: item.className,
              type: 1,
            };

            if (this.isGrade) {
              objectList.push(objectItem);
            } else if (
              this.disabledClassTypeMap[item.id + "_" + this.CREATE_TYPE.TEA] ||
              !this.disabledClassTypeMap[item.id + "_" + this.CREATE_TYPE.GRADE]
            ) {
              objectList.push(objectItem);
            }
          }
        });
      }
      // if(fcClass){
      //   fcClass.getCheckedNodes().forEach((item)=>{
      //     const canChoose = item.id != CLASS_NAME.FC && !getByKey(objectList,'classId', item.id) || this.disabledClassMap.has(item.id)
      //     canChoose && objectList.push({classId: item.id,className: item.className, type:1})
      //   })
      // }
      let quesData = {
        darftid: this.workId,
        cardid: this.workId,
        title: this.paperInfo.tbName,
        classes: [],
        objectList: objectList,
        classnames: [],
        groups: [],
        stuList: [],

        uid: this.userId,
        queSource: 0,
        sendType: 1,
        answerType: 0,

        startTime: `${moment().format("YYYY-MM-DD")} ${moment().format(
          "HH:mm"
        )}`,
        endtime: `${moment().add("days", 1).format("YYYY-MM-DD")} 08:00`,
        answerTime: "",

        hwTypeCode: "104",
        xkwPaperId: "",
        openId: "",
        remarks: "",
        docids: "",
        anwserdocids: "",
        year: this.curSubject.year,
        subjectId: this.curSubject.subjectId,
        markingType: 1,
        quesdatas: [...this.quesdatas],
      };

      return JSON.stringify(quesData);
    },
    /**
     * @name: 保存作业
     * */
    saveWorkJSON() {
      let workJson = this.buildSubmitData();
      return saveBankJSONAPI({
        darftId: this.workId,
        state: 0, //0 定时 1:已发送 2：草稿箱[默认] 来源
        userId: this.userId,
        creatType: this.isGrade ? this.CREATE_TYPE.GRADE : this.CREATE_TYPE.TEA, // creat_type表示的是创建什么样的作业,是普通作业还是备课组长作业,云考试全为1
        sendSource: 2, // PC
        correctType: 0, //学生作业互看状态（0:允许互看优秀作答，1:允许互看 2:不允许互看）
        permissionType: 1, //学生作业互看状态（0:允许互看优秀作答，1:允许互看 2:不允许互看）
        autoSubmit: 0,
        firstType: 3,
        subjectId: this.curSubject.subjectId,
        workJson: workJson,
        sourceId: this.paperInfo.id,
      });
    },
    /**
     * @name: 保存试卷
     * @description: 班级列表
     *    普通老师发，普通老师补发，选中的班级全要；
     普通老师发，备课组长补发，选中的班级全要；
     备课组长发，备课组长补发，选中的班级全要；
     备课组长发，普通老师补发，只要普通老师自己选中的
     */
    saveScanPaper() {
      let checkedClassList = [];
      const xzClass = this.$refs.xzClass;
      // const fcClass = this.$refs.fcClass
      if (xzClass) {
        let list = xzClass.getCheckedKeys().filter((id) => {
          return id !== CLASS_NAME.XZ;
        });

        checkedClassList.push(...list);
      }
      // if(fcClass){
      //   let list = fcClass.getCheckedKeys().filter((item)=>{
      //     return item != CLASS_NAME.FC && !this.disabledClassMap.has(item)
      //   })
      //   checkedClassList = [...checkedClassList, ...list]
      // }

      //普通教师过滤年级组长发的班级
      if (!this.isGrade) {
        checkedClassList = checkedClassList.filter((classId) => {
          return (
            this.disabledClassTypeMap[classId + "_" + this.CREATE_TYPE.TEA] ||
            !this.disabledClassTypeMap[classId + "_" + this.CREATE_TYPE.GRADE]
          );
        });
      }

      const params = {
        schoolId: this.schoolId,
        userId: this.userId,
        title: this.paperInfo.tbName,
        sourceId: this.paperInfo.id,
        type: 2,
        subjectId: this.paperInfo.subjectCode,
        gradeId: this.paperInfo.gradeCode,
        reviewType: 1,
        createType: this.isGrade
          ? this.CREATE_TYPE.GRADE
          : this.CREATE_TYPE.TEA,
        classIds: checkedClassList.join(","),
        isAdd: this.isAdd ? 1 : 0,
      };
      return saveScanPaperAPI(params).then((res) => {
        if (res.data && typeof res.data === "string") {
          this.workId = res.data;
        }
        return this.saveWorkJSON();
      });
    },
    /**
     * @name: 关闭弹窗
     */
    closeDialog() {
      this.$emit("close-dialog", false);
    },
    /**
     * @name: 表单校验
     * @return: Boolean
     */
    checkForm() {
      if (this.paperInfo.tbName === "") {
        this.$message.warning("请输入试卷名称");
        return false;
      }

      const xzClass = this.$refs.xzClass;
      // const fcClass = this.$refs.fcClass
      let list = [];
      if (xzClass) {
        list = [...list, ...xzClass.getCheckedKeys()];
      }
      // if(fcClass){
      //   list = [...list, ... fcClass.getCheckedKeys()]
      // }
      list = list.filter((item) => {
        return (
          [CLASS_NAME.XZ, CLASS_NAME.FC].indexOf(item) === -1 &&
          !this.disabledClassMap.has(item)
        );
      });

      if (list.length === 0) {
        this.$message.warning("请选择发送班级！");
        return false;
      }
      return true;
    },
    /**
     * @name: 发送作业
     */
    sendHomeWork() {
      if (this.checkForm()) {
        this.loading = true;
        this.saveScanPaper()
          .then(() => {
            // 下载
            // setTimeout(async () => {
            //   await this.downloadPaper();
            // }, 300);
            this.$message({
              type: "success",
              message: "布置成功！",
              duration: "2000",
            });
            this.closeDialog();
          })
          .catch((err) => {
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.class-tree {
  max-height: 200px;
  overflow-y: auto;
  margin: 10px 10px 10px 26px;
  font-size: 16px;

  .class-no-number {
    font-size: 12px;
    color: #f56c6c;
  }
}

.send-hw-dialog {
  .hw-select-class {
    .label {
      margin-left: 28px;
      font-size: 16px;
    }
  }
}

.send-hw-dialog {
  .class-tree {
    > .el-tree-node {
      .el-tree-node__label {
        font-size: 14px;
      }
    }
  }

  .el-dialog__footer {
    background-color: #fafbff;
  }
}
</style>
<style lang="scss">
.send-hw-dialog {
  .class-tree {
    > .el-tree-node {
      > .el-tree-node__content {
        position: sticky;
        top: 0;
        z-index: 2;
        height: 36px;
        background-color: #fff;
      }
    }
  }

  .el-dialog__footer {
    background-color: #fafbff;
  }
}
</style>

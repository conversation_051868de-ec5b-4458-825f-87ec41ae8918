<!--
 * @Description: 单科成绩
 * @Author: 小圆
 * @Date: 2024-03-30 09:13:47
 * @LastEditors: 小圆
-->

<template>
  <div>
    <div class="search-wrapper">
      <!-- <span> 姓名搜索：</span> -->
      <el-input
        class="search-input"
        v-model="filterData.keyWord"
        placeholder="输入学生姓名回车搜索"
        @keyup.enter.native="onSearch"
      >
      </el-input>
      <el-button type="primary" icon="el-icon-search" @click="onSearch">搜索</el-button>
    </div>

    <div v-loading="tableLoading">
      <base-table
        v-if="tableData.length"
        :data="tableData"
        :column="tableColumns"
        v-bind="getTableAttr()"
        v-drag-table
      ></base-table>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins, Prop } from 'vue-property-decorator';
// import TeacherTableCommon from './TeacherTableCommon';
import TableCommon from '../components/TableCommon';
import FilterModule from '../plugins/FilterModule';

@Component({
  components: {},
})
export default class SingleSubject extends Mixins(TableCommon) {
  @Prop({ default: 'teacher' }) apiModule;
  @Prop({ default: 'singleSubject' }) apiName;

  FilterModule: typeof FilterModule = FilterModule;

  get filterData() {
    return this.FilterModule.filterData;
  }

  get filterInfo() {
    return this.FilterModule.filterInfo;
  }

  mounted() {
    this.filterData.keyWord = '';
  }

  onSearch() {
    FilterModule.trigger('changeFilter');
  }
}
</script>

<style scoped lang="scss">
.search-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 10px;

  button {
    margin-left: 10px;
  }
}

.search-input {
  width: 200px;
}
</style>

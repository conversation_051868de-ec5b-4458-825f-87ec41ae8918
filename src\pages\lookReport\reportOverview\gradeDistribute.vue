<template>
  <div class="grade-dis-box" v-loading="isLoading">
    <div class="title-box">
      <div class="titleLine">{{ type == 'campus' ? '校区' : '' }}等级分布</div>
      <el-button class="pull-right" type="primary" @click="exportGradeDistribute"> 导出 </el-button>
    </div>
    <div style="min-height: 300px" v-show="tableData.length">
      <div class="table-box" v-if="headLeval">
        <el-table
          ref="elTable"
          class="grade-dis-table"
          :data="tableData"
          :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
          stripe
          style="width: 100%"
          v-drag-table
          v-sticky-table="0"
          @sort-change="sortTable"
          row-key="classId"
        >
          <el-table-column align="center" prop="className" :label="type == 'campus' ? '校区' : '班级'" min-width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.className }}</span>
            </template>
          </el-table-column>

          <el-table-column
            v-for="(item, index) in headLeval"
            align="center"
            :key="item.name"
            :label="item.name + '等' + item.section"
          >
            <el-table-column align="center" :prop="`${index}.num`" sortable="custom" label="人数">
              <template slot-scope="scope">
                <span>{{ scope.row.items[index]?.num ?? '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" :prop="`${index}.rate`" sortable="custom" label="比例">
              <template slot-scope="scope">
                <span>{{ scope.row.items[index]?.rate ?? '--' }}%</span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>
      <div class="chart-box">
        <div v-show="!showDefault" ref="gradeDistribute" style="width: 100%; height: 400px"></div>
        <div v-show="showDefault">
          <p style="text-align: center; font-size: 16px; margin-top: 80px">暂无数据!</p>
        </div>
      </div>
    </div>

    <div v-if="!tableData.length" class="nodata">
      <img :src="noResImg" alt="" />
      <p class="text-center">暂无数据</p>
    </div>
  </div>
</template>

<script>
import { getCampusLvAPI, getGradeDistribute } from '@/service/pexam';
import NumericInput from '@/components/NumericInput';
import UserRole from '@/utils/UserRole';

export default {
  name: 'grade-distribute',
  components: {
    NumericInput,
  },
  props: ['filterData', 'type'],
  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      gradeArray: ['A', 'B', 'C', 'D', 'E'],
      activeName: 'chart',
      isLoading: false,
      gradeDistribute: null,
      showDefault: false,
      chartData: [],
      // 表格头部等级字母
      headChar: ['A', 'B', 'C', 'D', 'E'],
      // 表格头部等级
      headLeval: null,
      tableData: [],
      originalData: [],
      classNames: [],
      stuAProList: [],
      stuBProList: [],
      stuCProList: [],
      stuDProList: [],
      stuEProList: [],
      // 默认值列表
      defaultList: {},
    };
  },

  watch: {
    filterData: {
      deep: true,
      handler(newValue, oldValue) {
        if (!newValue) return;
        this.updateFilter();
      },
    },
  },
  beforeDestroy() {
    if (this.gradeDistribute != null && this.gradeDistribute != '' && this.gradeDistribute != undefined) {
      this.gradeDistribute.dispose();
      this.gradeDistribute = null;
    }
  },
  mounted() {
    this.defaultList = this.$sessionSave.get('defaultList') || {};
    this.updateFilter();
    // this.getGradeDistribute();
    // 图表缩放
    let _this = this;
    window.onresize = () => {
      return (() => {
        _this.gradeDistribute.resize();
      })();
    };
  },
  methods: {
    // 获取到班级和学科后更新
    updateFilter(data) {
      if (this.type == 'campus') {
        this.getCampusLv();
      } else {
        this.getGradeDistribute();
      }
    },
    sureScoring() {
      if (this.defaultList.gradeLimit[0] > 100) {
        this.$message.error('得分率不能大于100');
        return;
      }
      let gradeLimit = this.defaultList.gradeLimit;
      let dataLength = gradeLimit.length;
      for (let i = 0; i < dataLength; i++) {
        let item = gradeLimit[i];
        if (i < dataLength - 1 && Number(item) < Number(gradeLimit[i + 1])) {
          this.$message.error('区间最大值不能小于最小值！');
          return;
        }
      }
      this.getGradeDistribute();
    },
    // 获取学生等级分布数据
    getGradeDistribute() {
      this.isLoading = true;
      getGradeDistribute({
        examId: this.$sessionSave.get('reportDetail').examId,
        subjectId: this.filterData.subjectId || '',
        v: this.$sessionSave.get('reportDetail').v,
        qType: this.filterData.qType,
      })
        .then(data => {
          let clzs = data.data.clzs;
          let clsList = this.$sessionSave.get('innerClassList');
          let list = [];
          clsList.forEach(item => {
            let clz = clzs.find(t => t.classId == item.id);
            if (clz != null) {
              list.push(clz);
            }
          });
          this.tableData = list;
          this.originalData = list;
          this.headLeval = data.data.secs;
          this.isLoading = false;
          this.$nextTick(() => {
            this.drawImg();
          });
        })
        .catch(err => {
          this.isLoading = false;
          this.tableData = [];
          this.originalData = [];
          // this.$nextTick(() => {
          //   this.drawImg();
          // });
        });
    },

    // 获取校区等级分布
    async getCampusLv() {
      const res = await getCampusLvAPI({
        examId: this.$sessionSave.get('reportDetail').examId,
        subjectId: this.filterData.subjectId || '',
        qType: this.filterData.qType,
        v: this.$sessionSave.get('reportDetail').v,
      }).catch(null);

      if (res && res.code == 1) {
        this.tableData = res.data.clzs;
        this.originalData = res.data.clzs;
        this.headLeval = res.data.secs;
        this.isLoading = false;
        this.$nextTick(() => {
          this.drawImg();
        });
      } else {
        this.isLoading = false;
        this.tableData = [];
        this.originalData = [];
      }
    },

    updateData({ isLoading }) {
      this.isLoading = isLoading;
    },
    // 切换显示图表和表格
    handleClick(tab, event) {
      if (tab.name == 'chart') {
        this.$nextTick(() => {
          // this.$refs.avgChart.avgChart.resize();
          // this.$refs.fiveRateChart.fiveRateChart.resize();
        });
      }
    },
    // 绘制图表
    drawImg() {
      let _this = this;
      let seriesItem = {
        type: 'bar',
        stack: '百分比',
        label: {
          show: true,
          position: 'left',
          formatter: '{c}%',
        },
      };

      const gradeDistribute = this.$refs.gradeDistribute;
      this.gradeDistribute = this.$echarts.init(gradeDistribute);

      this.gradeDistribute.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: params => {
            params.reverse();
            let html = '';
            let classHtml = `<div style="font-size:14px;color:#666;font-weight:400;line-height:1.25;">${params[0].name}</div>`;
            let obj = this.originalData.find(item => {
              return item.className == params[0].name;
            });

            let teaLabel = '';
            if (this.filterData?.subjectId) {
              teaLabel = obj.classId == '' ? '学科组长' : '任课老师';
            } else {
              teaLabel = obj.classId == '' ? '年级主任' : '班主任';
            }
            if (this.type == 'campus') {
              teaLabel = '年级主任';
            }

            let teaHtml = `<div style="font-size:14px;color:#666;font-weight:400;line-height:1.25;margin-top: 5px">${teaLabel}：${
              obj?.teaNames || '--'
            }</div>`;
            html = classHtml + teaHtml;
            params.forEach(item => {
              html += `<div class="clearfix" style="margin-top: 5px">${item.marker}<span>${
                item.seriesName
              }</span><span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${
                item.value + '%'
              }</span></div>`;
            });
            return html;
          },
        },
        color: ['#C6C9CC', '#FF6A68', '#FFB400', '#3E73F6', '#07C29D'],
        legend: {
          icon: 'circle',
          top: 10,
          right: 40,
          textStyle: {
            color: '#757C8C',
            fontSize: '14',
          },
          data: this.headLeval.map(t => t.name + '等'),
        },
        grid: {
          left: '3%',
          right: '6%',
          bottom: '9%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          name: '班级',
          axisLabel: {
            interval: 0,
            rotate: 30,
            margin: 20,
          },
          axisLine: {
            lineStyle: {
              color: '#757C8C',
              fontSize: 14,
            },
          },
          data: this.originalData.map(t => t.className),
        },
        yAxis: {
          type: 'value',
          name: '占比',
          axisLabel: {
            show: true,
            formatter: '{value}%',
          },
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100,
            // startValue: 0,
            // endValue: 15,
            // start: 0,
            // end: (6 / _this.classNames.length) * 100,
            // minValueSpan: 1,
            // maxValueSpan: 10,
          },
        ],
        series: this.headLeval
          .map((item, i) => {
            return {
              name: item.name + '等',
              ...seriesItem,
              barMaxWidth: 60,
              barGap: 20,
              data: this.originalData.map(t => {
                return t.items[i]?.rate ?? '--';
              }),
            };
          })
          .reverse(),
        // [
        //   {
        //     name: 'A等',
        //     ...seriesItem,
        //     barMaxWidth: 60,
        //     barGap: 20,
        //     data: _this.stuAProList,
        //   },
        //   {
        //     name: 'B等',
        //     type: 'bar',
        //     ...seriesItem,
        //     data: _this.stuBProList,
        //   },
        //   {
        //     name: 'C等',
        //     ...seriesItem,
        //     data: _this.stuCProList,
        //   },
        //   {
        //     name: 'D等',
        //     ...seriesItem,
        //     data: _this.stuDProList,
        //   },
        //   {
        //     name: 'E等',
        //     ...seriesItem,
        //     data: _this.stuEProList,
        //   },
        // ].reverse(),
      });
    },
    // 按照平均分排序
    sortTable({ column, prop, order }) {
      let data = this.$deepClone(this.originalData);
      let index = Number(prop.split('.')[0]);
      let name = prop.split('.')[1];
      let arr = [];

      if (order === 'descending') {
        arr = data.sort((a, b) => {
          let aItems = a['items'][index][name];
          let bItems = b['items'][index][name];
          return aItems - bItems;
        });
      } else if (order === 'ascending') {
        arr = data.sort((a, b) => {
          let aItems = a['items'][index][name];
          let bItems = b['items'][index][name];
          return bItems - aItems;
        });
      } else {
        arr = data;
      }
      this.tableData = this.$deepClone(arr);
    },

    // 导出等级分布
    async exportGradeDistribute() {
      const { examId, v } = this.$sessionSave.get('reportDetail');
      let role = '';
      if (!UserRole.isOperation) {
        const { year, campusCode } = this.$sessionSave.get('reportDetail');
        const map = await UserRole.utils.getRoleSubjectClassMap(year, campusCode, this.$sessionSave.get('reportType') == 'school' ? true : false);
        role = JSON.stringify(map);
      }
      const params = {
        examId,
        v,
        role
      };
      const urlSearch = new URLSearchParams(params);
      const path = this.type == 'campus' ? '/pexam/_/exp-campus-lv' : '/pexam/_/exp-lv';
      let url = process.env.VUE_APP_KKLURL + path + `?${urlSearch.toString()}`;
      window.open(url);
    },
  },
};
</script>

<style lang="scss" scoped>
.scoreInput {
  width: 50px;
  position: relative;
  margin-right: 26px;
  &:after {
    content: '%';
    position: absolute;
    right: -26px;
    top: 0;
    border: 1px solid #dcdfe6;
    border-radius: 0 4px 4px 0;
    width: 26px;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    text-align: center;
    line-height: 30px;
    z-index: 11;
    border-left: none;
  }
}

.sureScoreBtn {
  margin-left: 20px;
  background: #409eff;
  height: 30px;
  padding: 0;
  width: 60px;
  line-height: 30px;
  text-align: center;
}
.grade-dis-box {
  // margin-top: 10px;
  border-radius: 6px;
  padding-bottom: 30px;

  .chart-box {
    height: 430px;
    background: #fff;
    border: 1px solid #e4e8eb;
    border-radius: 3px;
    margin: 20px 0;
  }

  .table-box {
    border: 1px solid #e4e8eb;
    border-radius: 3px;
  }
}
.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;
  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 15px;
  }
}

.title-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
<style lang="scss">
.grade-dis-box {
  .el-tabs {
    .el-tabs__header {
      margin: 0 0 1px 4px;
    }
    .el-tabs__nav-wrap::after {
      width: 160px;
    }
    .el-tabs__item {
      width: 80px;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      &.is-top:last-child {
        padding-left: 0;
      }
      &.is-top:nth-child(2) {
        padding-right: 0;
      }
    }
  }
}

.scoreInput {
  .el-input-group__append {
    padding: 0 10px;
  }
  &.numeric-input-box .numeric-input-inner {
    border-radius: 4px 0 0 4px;
  }
}
</style>

<!--
 * @Description: 单科综合指标成绩
 * @Author: 小圆
 * @Date: 2024-04-02 09:03:08
 * @LastEditors: 小圆
-->
<template>
  <div>
    <div v-loading="tableLoading">
      <base-table
        v-if="tableData.length"
        :data="tableData"
        :column="tableColumns"
        v-bind="getTableAttr()"
        v-drag-table
      ></base-table>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>

    <!-- 前x名分布对比图 -->
    <div style="margin-top: 20px; width: 100%; height: 420px" id="LimitChart"></div>
    <!-- 四率分布对比图 -->
    <div style="margin-top: 20px; width: 100%; height: 380px" id="FourRateChart"></div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins, Prop } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';
import FilterModule from '../plugins/FilterModule';
import {
  getDefaultGrid,
  getDefaultLegend,
  getDefaultPercentAxis,
  getDefaultTitle,
  getDefaultToolBox,
  getDefaultTooltipFormatter,
} from '../plugins/DefaultEchartsOption';

@Component({
  components: {},
})
export default class SingleSubjectComprehensiveScore extends Mixins(TableCommon) {
  // 前x名分布图表
  limitChart: EChartsType = null;
  // 四率图表
  fourRateChart: EChartsType = null;
  // 表格左侧固定列
  tableLeftFixed: any[] = ['className'];


  callbackGetTableData() {
    this.renderLimitChart();
    this.renderFourRateChart();
  }

  // 渲染前x名分布对比图
  renderLimitChart() {
    if (this.limitChart) {
      this.limitChart.dispose();
      this.limitChart = null;
    }
    const dom = document.getElementById('LimitChart');
    this.limitChart = this.$echarts.init(dom);

    let option: EChartsOption = {
      toolbox: getDefaultToolBox(),
      title: {
        ...getDefaultTitle(),
        text: `前${FilterModule.filterData.limit}名分布对比图`,
      },
      tooltip: {
        trigger: 'item',
        formatter: params => {
          return getDefaultTooltipFormatter(
            params.name,
            params.seriesName,
            params.value,
            params.color
          );
        },
      },
      legend: getDefaultLegend(),
      grid: getDefaultGrid(),
      xAxis: {
        type: 'value',
        boundaryGap: [0, 0.01],
        minInterval: 1,
      },
      yAxis: {
        type: 'category',
        data: this.tableData.map(item => item.className),
      },
      series: [
        {
          name: '人数',
          type: 'bar',
          data: this.tableData.map(item => item.devoteNum),
          label: {
            show: true,
          },
          barMaxWidth: 50
        },
      ],
    };

    this.limitChart.setOption(option);
  }

  renderFourRateChart() {
    if (this.fourRateChart) {
      this.fourRateChart.dispose();
      this.fourRateChart = null;
    }

    const dom = document.getElementById('FourRateChart');
    this.fourRateChart = this.$echarts.init(dom);

    let option: EChartsOption = {
      grid: getDefaultGrid({ top: 80 }),
      title: getDefaultTitle({
        text: '四率分布对比图',
      }),
      legend: getDefaultLegend(),
      tooltip: {
        trigger: 'axis',
      },
      toolbox: getDefaultToolBox(),
      yAxis: getDefaultPercentAxis(),
      xAxis: {
        type: 'category',
        data: this.tableData.map(item => item.className),
      },
      series: [
        {
          name: '高分率（%）',
          data: this.tableData.map(item => parseFloat(item.findRate) || 0),
          type: 'line',
        },
        {
          name: '优秀率（%）',
          data: this.tableData.map(item => parseFloat(item.goodRate) || 0),
          type: 'line',
        },
        {
          name: '及格率（%）',
          data: this.tableData.map(item => parseFloat(item.passRate) || 0),
          type: 'line',
        },
        {
          name: '低分率（%）',
          data: this.tableData.map(item => parseFloat(item.lowRate) || 0),
          type: 'line',
        },
      ],
    };

    this.fourRateChart.setOption(option);
  }
}
</script>

<style scoped lang="scss"></style>

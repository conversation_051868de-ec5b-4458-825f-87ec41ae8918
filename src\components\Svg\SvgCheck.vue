<template>
  <svg
    t="1722223836117"
    class="icon online-fill-svg"
    viewBox="0 0 757.0286 541.25715"
    version="1.1"
    p-id="1509"
    width="20.700001"
    height="14.8"
    id="svg1"
    sodipodi:docname="duihao1.svg"
    inkscape:version="1.4.2 (f4327f4, 2025-05-13)"
    xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
    xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:svg="http://www.w3.org/2000/svg"
  >
    <defs id="defs1" />
    <sodipodi:namedview
      id="namedview1"
      pagecolor="#ffffff"
      bordercolor="#000000"
      borderopacity="0.25"
      inkscape:showpageshadow="2"
      inkscape:pageopacity="0.0"
      inkscape:pagecheckerboard="0"
      inkscape:deskcolor="#d1d1d1"
      inkscape:zoom="20.379828"
      inkscape:cx="8.3170476"
      inkscape:cy="11.310203"
      inkscape:window-width="1920"
      inkscape:window-height="1009"
      inkscape:window-x="1912"
      inkscape:window-y="-8"
      inkscape:window-maximized="1"
      inkscape:current-layer="svg1"
    />
    <path
      d="m 744.00424,8.667653 a 31.936,31.936 0 0 0 -45.216,1.472 L 273.60424,464.02765 53.156242,278.81165 a 32,32 0 1 0 -41.151999,48.992 l 243.647997,204.704 a 31.872,31.872 0 0 0 20.576,7.488 31.808,31.808 0 0 0 23.36,-10.112 l 445.888,-475.967997 a 32,32 0 0 0 -1.472,-45.248 z"
      p-id="1510"
      id="path1"
      style="stroke-width: 1"
    />
  </svg>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';

@Component
export default class SvgCheck extends Vue {}
</script>

<style scoped></style>

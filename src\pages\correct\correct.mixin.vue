<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-05-12 10:38:01
 * @LastEditors: 小圆
-->
<script lang="ts">
import ImgPreview from '@/components/SwiperViewer/ImgPreview.vue';
import { replaceALiUrl } from '@/utils/common';
import { setCroppedImageUrl } from '@/utils/index';
import { MessageBox } from '@iclass/element-ui';
import { Component, Vue } from 'vue-property-decorator';

@Component({
  components: {
    ImgPreview,
  },
})
export default class CorrectMixin extends Vue {
  // 预览图片列表
  previewImgList = [];
  // 是否显示预览
  isShowPreviewImg = false;

  // 预览图片
  previewImg(source, wScale = 2, hScale = 1.5) {
    source = setCroppedImageUrl(source, wScale, hScale);
    this.previewImgList = [replaceALiUrl(source)];
    this.isShowPreviewImg = true;
  }

  // 检查是否暂停阅卷
  checkPause(code, msg?) {
    if (code == 11010) {
      MessageBox.alert(msg || '管理员正在修改阅卷设置，请您休息片刻再阅~', '提示', {
        confirmButtonText: '返回',
      })
        .then(() => {
          this.$router.go(-1);
        })
        .catch(() => {
          this.$router.go(-1);
        });
      return true;
    }
  }
}
</script>

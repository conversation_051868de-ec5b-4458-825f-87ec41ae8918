<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-04-18 09:25:16
 * @LastEditors: 小圆
-->

<template>
  <div>
    <div v-loading="tableLoading">
      <base-table
        v-if="tableData.length"
        :data="tableData"
        :column="tableColumns"
        v-bind="getTableAttr()"
        v-drag-table
      ></base-table>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>
  </div>
</template>

<script lang="tsx">
import { Component, Mixins } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';
import FilterModule from '../plugins/FilterModule';

@Component({
  components: {},
})
export default class Index extends Mixins(TableCommon) {
  // 获取数据
  async getTableData(): Promise<any> {
    this.tableColumns = [];
    this.tableData = [];
    this.tableLoading = true;
    await this.$nextTick();
    try {
      const res = await FilterModule.getReportTableData(this.apiName || this.$route.name);
      if (res.code != 1) {
        return this.$message.error(res.msg);
      }
      const data = res.data;

      data.table.forEach(item => {
        if (item.prop == 'subjectTeacherNames') {
          item.width=250;
          // item.render = (h, scope) => {
          //   if (scope.row.subjectTeacherNames) {
          //     const subjectTeacherNames = scope.row.subjectTeacherNames.split(';');
          //     return subjectTeacherNames.map(item => <div>{item}</div>);
          //   }
          //   return <div>--</div>;
          // };
        }
      });

      data.table = this.setColumnProp(data.table);
      this.tableColumns = data.table as IColumn[];
      this.tableData = data.result;
    } catch (error) {
      console.error(error);
      this.tableColumns = [];
      this.tableData = [];
    } finally {
      this.tableLoading = false;
    }
  }
}
</script>

<style scoped lang="scss"></style>

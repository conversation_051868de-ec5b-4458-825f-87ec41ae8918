<template>
  <div class="online" v-loading="listLoading">
    <div class="header-box">
      <div class="titleLine">
        <span>{{ type == 'campus' ? '校区' : '' }}上线分析</span>
        <el-popover placement="top-start" title="" width="400" trigger="hover">
          <div style="line-height: 25px; font-size: 13px">
            1、总分上线：达到总分线的人数 <br />
            2、单科上线：单科达到单科分数线的人数<br />
            3、双上线：即达到总分分数线又达到单科分数线的人数<br />
            4、贡献率：双上线人数/总分分上线人数<br />
            5、命中率：双上线人数/单科上线人数
          </div>
          <i class="el-icon-question" slot="reference" style="margin-left: 5px"></i>
        </el-popover>
      </div>
    </div>

    <div class="online-content" v-if="onlineData.length > 0">
      <div class="online-select-box">
        <el-radio-group v-model="onlineDataIndex" @change="changeOnlineIndex">
          <el-radio-button v-for="(item, index) in onlineData" :key="index" :label="index">
            {{ item.lineName }}
          </el-radio-button>
        </el-radio-group>

        <el-button type="primary" @click="showExportDialog">导出</el-button>
      </div>

      <el-table
        class="online-table"
        :data="curOnlineData.data"
        :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
        stripe
        style="width: 100%"
        v-drag-table
        v-sticky-table="0"
      >
        <el-table-column
          align="center"
          prop="clsName"
          :label="type == 'campus' ? '校区' : '班级'"
          fixed
          min-width="150"
        >
          <template #default="scope">
            <span>{{ scope.row.clsName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="" :label="`${curOnlineData.total.name}(${curOnlineData.total.value})`">
          <el-table-column align="center" prop="tar" min-width="120" label="目标人数">
            <template #default="scope">
              <span>{{ filterData.qType == 1 ? scope.row.rule.tar : scope.row.tar }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="num" min-width="120" label="上线人数">
            <template #default="scope">
              <span>{{ filterData.qType == 1 ? scope.row.rule.num : scope.row.num }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="tarRate" min-width="120" label="达标率">
            <template #default="scope">
              <span>{{ filterData.qType == 1 ? scope.row.rule.tarRate : scope.row.tarRate }}%</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in curOnlineData.heads"
          prop=""
          :label="`${item.name}(${item.value})`"
          align="center"
          :key="index"
        >
          <el-table-column label="单科上线" align="center" min-width="120">
            <template #default="scope">
              {{
                filterData.qType == 1
                  ? scope.row.subject[item.id]?.rule.num ?? '--'
                  : scope.row.subject[item.id]?.num ?? '--'
              }}
            </template>
          </el-table-column>
          <el-table-column label="双上线" align="center" min-width="120">
            <template #default="scope">
              {{
                filterData.qType == 1
                  ? scope.row.subject[item.id]?.rule.two ?? '--'
                  : scope.row.subject[item.id]?.two ?? '--'
              }}
            </template>
          </el-table-column>
          <el-table-column label="贡献率" align="center" min-width="120">
            <template #default="scope">
              {{
                filterData.qType == 1
                  ? scope.row.subject[item.id]?.rule.devote ?? '--'
                  : scope.row.subject[item.id]?.devote ?? '--'
              }}%
            </template>
          </el-table-column>
          <el-table-column label="命中率" align="center" min-width="120">
            <template #default="scope">
              {{
                filterData.qType == 1
                  ? scope.row.subject[item.id]?.rule.hit ?? '--'
                  : scope.row.subject[item.id]?.hit ?? '--'
              }}%
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
    <no-data v-if="onlineData.length === 0"></no-data>

    <!-- 导出弹窗 -->
    <OnlineExportDialog
      :visible.sync="exportDialogVisible"
      :showQTypeOption="isRule"
      :currentQType="filterData.qType"
      @confirm="handleExportConfirm"
      @cancel="handleExportCancel"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import NoData from '@/components/noData.vue';
import OnlineExportDialog from './components/OnlineExportDialog.vue';
import { getCampusLineDataAPI, getOnlineAPI } from '@/service/pexam';
import UserRole from '@/utils/UserRole';

interface OnlineData {
  lineName: string;
  total: Total;
  heads: Head[];
  data: Datum[];
}

interface Datum {
  clsId: string;
  clsName: string;
  tar: number;
  tarRate: number;
  num: number;
  subject: {
    [x: string]: Data;
  };
}

interface Data {
  devote: number;
  hit: number;
  num: number;
  two: number;
}

interface Head {
  id: string;
  name: string;
  value: number;
}

interface Total {
  name: string;
  value: number;
}

@Component({
  components: {
    NoData,
    OnlineExportDialog,
  },
})
export default class Online extends Vue {
  // 筛选条件
  @Prop() filterData: any;
  // 组件类型
  @Prop() type: string;
  // 上线数据
  onlineData: OnlineData[] = [];
  // 当前查看的分数线
  onlineDataIndex: number = 0;
  // 当前数据线
  get curOnlineData(): OnlineData {
    let innerClassList = this.$sessionSave.get('innerClassList');
    let innerSubjectList = this.$sessionSave.get('innerSubjectList');

    let data = [];
    if (this.type != 'campus') {
      innerClassList.forEach(cls => {
        let item = this.onlineData[this.onlineDataIndex].data.find(item => item.clsId == cls.id);
        if (item) {
          data.push(item);
        }
      });
    } else {
      data = this.onlineData[this.onlineDataIndex].data;
    }

    let heads = this.onlineData[this.onlineDataIndex].heads.filter(item => {
      if (this.filterData.subjectId) {
        return item.id == this.filterData.subjectId;
      } else {
        return innerSubjectList.some(subject => subject.id === item.id);
      }
    });

    return {
      ...this.onlineData[this.onlineDataIndex],
      data,
      heads,
    };
  }

  // 列表loading
  listLoading = false;
  // 导出弹窗显示状态
  exportDialogVisible = false;

  // 是否显示分数来源选项
  get isRule() {
    // 检查当前学科是否支持赋分，参考headerFilter的实现
    let innerSubjectList = this.$sessionSave.get('innerSubjectList') || [];
    let currentSubject = innerSubjectList.find(item => item.id === this.filterData.subjectId);
    return currentSubject?.isRule;
  }

  @Watch('filterData', { deep: true })
  onFilterDataChange(newVal: any) {
    this.updateFilter();
  }

  mounted() {
    this.updateFilter();
  }

  updateFilter() {
    if (this.type == 'campus') {
      this.getCampusLineData();
    } else {
      this.getOnlineData();
    }
  }

  // 获取上线数据
  async getOnlineData() {
    this.onlineData = [];
    this.onlineDataIndex = 0;
    this.listLoading = true;
    const res = await getOnlineAPI({
      examId: this.$sessionSave.get('reportDetail').examId,
      qType: this.filterData.qType,
    });

    this.onlineData = res.data;
    this.listLoading = false;
  }

  // 获取校区上线分析数据
  async getCampusLineData() {
    this.onlineData = [];
    this.onlineDataIndex = 0;
    this.listLoading = true;
    const res = await getCampusLineDataAPI({
      examId: this.$sessionSave.get('reportDetail').examId,
      qType: this.filterData.qType,
      v: this.$sessionSave.get('reportDetail').v,
    });

    this.onlineData = res.data;
    this.listLoading = false;
  }

  // 改变查看的分数线
  changeOnlineIndex(index: number) {
    this.onlineDataIndex = index;
  }

  // 显示导出弹窗
  showExportDialog() {
    if (this.type == 'campus') {
      this.exportTable(this.filterData.qType);
      return;
    }
    if (this.isRule) {
      this.exportDialogVisible = true;
      return;
    }
    this.exportTable(this.filterData.qType);
  }

  // 处理导出确认
  async handleExportConfirm(options: { qType: number }) {
    await this.exportTable(options.qType);
  }

  // 处理导出取消
  handleExportCancel() {
    this.exportDialogVisible = false;
  }

  // 导出
  async exportTable(qType?: number) {
    const { examId, v } = this.$sessionSave.get('reportDetail');
    let role = '';
    if (!UserRole.isOperation) {
      const { year, campusCode } = this.$sessionSave.get('reportDetail');
      const map = await UserRole.utils.getRoleSubjectClassMap(
        year,
        campusCode,
        this.$sessionSave.get('reportType') == 'school' ? true : false
      );
      role = JSON.stringify(map);
    }
    const params: any = {
      examId,
      qType: qType !== undefined ? qType : this.filterData.qType,
      v,
      role,
    };
    const urlSearch = new URLSearchParams(params);
    const path = this.type == 'campus' ? '/pexam/_/exp-campus-line-data' : '/pexam/_/exp-line';
    let url = process.env.VUE_APP_KKLURL + path + `?${urlSearch.toString()}`;
    window.open(url);
  }
}
</script>

<style scoped lang="scss">
.header-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;
  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 15px;
  }
}

.online-content {
  .online-select-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }
}
</style>

<!-- <template>
  <div v-loading="listLoading">
    <div class="titleLine">
      <span>上线分析</span>
      <el-popover placement="top-start" title="" width="400" trigger="hover">
        <div style="line-height: 25px; font-size: 13px">
          1、总分上线：达到总分线的人数 <br />
          2、单科上线：单科达到单科分数线的人数<br />
          3、双上线：即达到总分分数线又达到单科分数线的人数<br />
          4、贡献率：双上线人数/总分分上线人数<br />
          5、命中率：双上线人数/单科上线人数
        </div>
        <i class="el-icon-question" slot="reference" style="margin-left: 5px"></i>
      </el-popover>
    </div>
    <div></div>
    <div class="filter" v-if="tableData.length > 0">
      <el-select
        v-model="onlineType"
        class="type-select"
        placeholder="请选择"
        @change="changeOnline"
      >
        <el-option
          v-for="(item, index) in lines"
          :key="index"
          :label="item.name"
          :value="index"
        >
        </el-option>
      </el-select>
    </div>
    <div style="min-height: 300px">
      <el-table
        v-if="tableData.length > 0"
        class="top-diff--table"
        :data="tableData"
        stripe
        :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
        style="width: 100%"
        v-drag-table
        v-sticky-table="0"
      >
        <el-table-column align="center" prop="clsName" label="班级" fixed min-width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.clsName }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="" :label="`总分(${fullScore})`">
          <el-table-column align="center" prop="tar" min-width="120" label="目标人数">
          </el-table-column>
          <el-table-column align="center" prop="total" min-width="120" label="上线人数">
          </el-table-column>
          <el-table-column align="center" min-width="120" label="达标率">
            <template slot-scope="scope">
              <span>{{ scope.row.tarRate }}%</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in onlineSubList"
          prop=""
          :label="`${item.name}(${item.value})`"
          align="center"
          :key="index"
        >
          <el-table-column label="单科上线" align="center" min-width="120">
            <template slot-scope="scope">
              <span v-if="onlineSubList.length === 1">{{
                scope.row.data[0].score[onlineSubList[0].id].num
              }}</span>
              <span v-else>{{ scope.row.data[0].score[item.id].num }}</span>
            </template>
          </el-table-column>
          <el-table-column label="双上线" align="center" min-width="120">
            <template slot-scope="scope">
              <span v-if="onlineSubList.length === 1">{{
                scope.row.data[0].score[onlineSubList[0].id].two
              }}</span>
              <span v-else>{{ scope.row.data[0].score[item.id].two }}</span>
            </template>
          </el-table-column>
          <el-table-column label="贡献率" align="center" min-width="120">
            <template slot-scope="scope">
              <span v-if="onlineSubList.length === 1"
                >{{ scope.row.data[0].score[onlineSubList[0].id].devote }}%</span
              >
              <span v-else>{{ scope.row.data[0].score[item.id].devote }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="命中率" align="center" min-width="120">
            <template slot-scope="scope">
              <span v-if="onlineSubList.length === 1"
                >{{ scope.row.data[0].score[onlineSubList[0].id].hit }}%</span
              >
              <span v-else>{{ scope.row.data[0].score[item.id].hit }}%</span>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
    <div v-if="!tableData.length" class="nodata">
      <img :src="noResImg" alt="" />
      <p class="text-center">暂无数据</p>
    </div>
  </div>
</template>

<script>
import NumericInput from '@/components/NumericInput';
import { SchoolSettingType } from '@/pages/schoolSetting/types';
import { getOnlineAPI, getSchCfgAPI, getUseSubject } from '@/service/pexam';
export default {
  name: 'online',
  components: {
    NumericInput,
  },
  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      onlineType: 0,
      // 表格数据
      tableData: [],
      lineData: [],
      listLoading: false,
      //学科列表
      subjectList: [],
      onlineSubList: [],
      //分数线配置
      onlineData: [],
      //学科总分
      fullScore: 0,
      // 配置
      config: null,
      // 分数线
      lines: [],
    };
  },
  props: ['filterData'],
  watch: {
    filterData: {
      deep: true,
      handler(newValue, oldValue) {
        if (!newValue) return;
        // this.updateFilter();
        this.getOnlineData();
      },
    },
  },
  async mounted() {
    await this.getLineConfig();
    this.getOnlineData();
  },
  methods: {
    // 获取分数线配置
    async getLineConfig() {
      const res = await getSchCfgAPI({
        examId: this.$sessionSave.get('reportDetail').examId,
        schId: this.$sessionSave.get('schoolInfo').id,
        type: SchoolSettingType.ScoreLineSetting,
      });
      this.config = res.data.jCfg.find(item => item.checked);
      this.lines = this.config.lines;
    },

    /**
     * @name:改变查看的分数线
     */
    changeOnline() {
      this.mateScore();
      let arr = JSON.parse(JSON.stringify(this.lineData));
      arr.forEach((item, inde) => {
        this.$set(item, 'total', item.data[this.onlineType].total);
        this.$set(item, 'tar', item.data[this.onlineType].tar);
        this.$set(item, 'tarRate', item.data[this.onlineType].tarRate);
        item.data = item.data.filter((ite, ind) => {
          return ind == this.onlineType;
        });
      });
      this.tableData = arr;
    },
    /**
     * @name:获取学科分数线
     */
    mateScore() {
      this.fullScore = 0;
      this.onlineData.forEach(item => {
        this.onlineSubList.forEach(ite => {
          if (item.subId == ite.id) {
            this.$set(ite, 'value', item.value[this.onlineType]);
          }
          if (item.subId == 0) {
            this.fullScore = item.value[this.onlineType];
          }
        });
      });
    },
    /**
     * @name:获取上线分析数据
     */
    getOnlineData() {
      this.lineData = [];
      this.listLoading = true;

      getOnlineAPI({
        examId: this.$sessionSave.get('reportDetail').examId,
        qType: this.filterData.qType,
      })
        .then(res => {
          let classIds = this.$sessionSave.get('innerClassList');
          classIds.forEach(item => {
            res.data.forEach(ite => {
              if (ite.clsId == item.id) {
                this.lineData.push(ite);
              }
            });
          });
          this.updateFilter(this.filterData);
          this.listLoading = false;
        })
        .catch(res => {
          this.lineData = [];
          this.tableData = [];
          this.listLoading = false;
        });
    },
    /**
     * @name: 获取到班级和学科后更新
     */
    updateFilter() {
      let data = this.filterData;
      this.listLoading = true;
      let arr = JSON.parse(JSON.stringify(this.lineData));
      if (data.subjectId != '') {
        this.onlineSubList = this.subjectList.filter(item => {
          return item.id == data.subjectId;
        });
        arr.forEach(item => {
          this.$set(item, 'total', item.data[this.onlineType].total);
          this.$set(item, 'tar', item.data[this.onlineType].tar);
          this.$set(item, 'tarRate', item.data[this.onlineType].tarRate);
          item.data = item.data.filter((ite, ind) => {
            return ind == this.onlineType;
          });
          //删除本次选择的学科之外的学科数据
          let value = Object.keys(item.data[0].score).filter(ite => {
            return ite != data.subjectId;
          });
          value.forEach(it => {
            delete item.data[0].score[it];
          });
        });
      } else {
        this.onlineSubList = this.subjectList.filter(item => {
          return item.id != '';
        });
        arr.forEach(item => {
          //删除本次选择的学科之外的学科数据
          this.$set(item, 'total', item.data[this.onlineType].total);
          this.$set(item, 'tar', item.data[this.onlineType].tar);
          this.$set(item, 'tarRate', item.data[this.onlineType].tarRate);
          item.data = item.data.filter((ite, ind) => {
            return ind == this.onlineType;
          });
        });
      }
      this.tableData =
        (data.subjectId != '' && this.onlineSubList.length > 0) || data.subjectId == '' ? arr : [];
      this.listLoading = false;
      setTimeout(() => {
        this.mateScore();
      }, 300);
    },
    /**
     * @name:获取学科列表
     */
    async getSubjectList(data) {
      this.subjectList = [];

      let examSubjectList = this.$sessionSave.get('innerSubjectList');

      let subject = (
        await getUseSubject({
          examId: this.$sessionSave.get('reportDetail').examId,
        })
      ).data;

      if (examSubjectList) {
        examSubjectList.forEach(item => {
          for (const sub of subject) {
            if (sub == item.id) {
              this.subjectList.push(item);
              break;
            }
          }
        });
      }
      this.subjectList.unshift({
        id: '',
        name: '全部',
        phaseId: '',
      });
      this.onlineSubList = this.subjectList.filter(item => {
        return item.id != '';
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.filter {
  line-height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .type-select {
    width: 110px;
    margin-right: 30px;
  }

  .dis_block {
    display: inline-block;
    margin-right: 20px;
  }
}

.scoreInput {
  width: 50px;
  position: relative;
  margin-right: 26px;

  &:after {
    content: '%';
    position: absolute;
    right: -26px;
    top: 0;
    border: 1px solid #dcdfe6;
    border-radius: 0 4px 4px 0;
    width: 26px;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    text-align: center;
    line-height: 30px;
    z-index: 11;
    border-left: none;
  }
}

.noPercent {
  margin-right: 3px;

  .numeric-input-inner {
    border-radius: 4px !important;
  }

  &:after {
    content: none;
  }
}

.sureScoreBtn {
  font-size: 16px;
}
.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;
  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 15px;
  }
}
</style>
<style lang="scss">
.scoreInput {
  .el-input-group__append {
    padding: 0 10px;
  }

  &.numeric-input-box .numeric-input-inner {
    border-radius: 4px 0 0 4px;
  }
}

.hasBorder {
  &.numeric-input-box .numeric-input-inner {
    border-radius: 4px !important;
  }
}
</style> -->

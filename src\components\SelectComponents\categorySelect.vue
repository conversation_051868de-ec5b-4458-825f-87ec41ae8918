<!--
 * @Descripttion: 类别选择组件
 * @Author: 小圆
 * @Date: 2024-01-17 11:10:45
 * @LastEditors: 小圆
-->
<template>
  <div class="header__select">
    <span class="select__label">类别：</span>
    <el-select v-model="typeValue" class="select" @change="handleChange" placeholder="请选择">
      <el-option v-for="item in typeList" :key="item.id" :label="item.name" :value="item.id">
      </el-option>
    </el-select>
  </div>
</template>

<script>
import UserRole from "@/utils/UserRole";
import { selectAllType } from "@/service/pbook";
export default {
  props: {
    value: {
      type: [String, Number],
      default: "",
    },
  },

  watch: {
    value(val, oldVal) {
      this.typeValue = val;
    },
  },

  data() {
    return {
      typeList: [],
      typeValue: "",
    };
  },

  mounted() {
    this.getAllType();
  },

  methods: {
    // 获取类别列表
    async getAllType() {
      this.typeList = await UserRole.getAllType();
      this.typeList.unshift({
        id: "",
        name: "全部",
      });
      this.typeValue = this.typeList[0].id;
      if (this.value) {
        this.typeValue = this.value;
      }
      this.$emit("input", this.typeValue);
    },

    handleChange(val) {
      this.$emit("input", val);
      this.$emit("change", val);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "./select.scss";
</style>

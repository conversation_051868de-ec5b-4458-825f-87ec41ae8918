<template>
    <div class="exam-mk-container">
      <bread-crumbs :title="'阅卷设置'"></bread-crumbs>
      <!-- 头部切换 -->
      <el-tabs v-model="pageType" type="card" @tab-click="changePageType">
        <el-tab-pane label="客观题设置" name="objectSetting">
          <!-- v-if="objectQuesData.length != 0" -->
        </el-tab-pane>
        <el-tab-pane label="主观题设置" name="subjectSetting"></el-tab-pane>
        <!-- v-if="subjectQuesData.length != 0" -->
        <el-tab-pane label="智批改设置" name="aiSetting" v-if="aiQuesData.length != 0"></el-tab-pane>
        <el-tab-pane
          label="阅卷老师设置"
          name="teacher"
          v-if="source == 4 || aiQuesData.length != 0"
        ></el-tab-pane>
      </el-tabs>
      <div class="exam-mk-main">
        <div
          class="exam-mk-ques"
          v-if="
            pageType == 'objectSetting' || pageType == 'subjectSetting' || pageType == 'aiSetting'
          "
        >
          <el-row style="margin-bottom: 20px; line-height: 50px" type="flex" align="middle"
            ><span class="exam-title">{{ examName }}</span>
            <div class="exam-category">
              {{ subjectName }}
            </div>
            <!-- <span>{{ subjectName }}</span> -->
          </el-row>
          <el-row class="exam-info" type="flex" align="middle" justify="space-between">
            <span
              >本试卷共<span class="blue-txt">{{ totalData.totalQuesNum }}</span
              >题：客观题<span class="blue-txt">{{ totalData.objectQuesNum }}</span
              >题，共<span class="blue-txt">{{ totalData.objectScore }}</span
              >分；主观题<span class="blue-txt">{{ totalData.subjectQuesNum }}</span
              >题，共<span class="blue-txt">{{ totalData.subjectScore }}</span
              >分；总分<span class="blue-txt">{{ totalData.totalScore }}</span
              >分</span
            >
            <div>
              <template v-if="pageType == 'subjectSetting' && correctType == 2">
                <el-button
                  type="primary"
                  @click="mergeQuesGroup"
                  :disabled="selectedQues.length <= 1 || progress >= 5"
                  >合并题块</el-button
                >
                <el-button :disabled="progress >= 5" type="warning" @click="jumpSetQuesBlock"
                  >题块框选</el-button
                >
              </template>
              <el-button
                type="text"
                :disabled="notOperate || (pageType == 'subjectSetting' && source == 3)"
                @click="openSetAnswerDialog"
                >{{ pageType == 'objectSetting' ? objectText : subjectText }}</el-button
              >
            </div>
          </el-row>
          <div class="table-container">
            <el-table
              :data="
                pageType == 'aiSetting'
                  ? aiQuesData
                  : pageType == 'objectSetting'
                  ? objectQuesData
                  : subjectQuesData
              "
              class="exam-ques-table"
              :key="
                pageType == 'aiSetting'
                  ? 'aiSetting'
                  : pageType == 'objectSetting'
                  ? 'objectSetting'
                  : 'subjectSetting'
              "
              @selection-change="handleSelectionChange"
              :height="elTableHeight"
            >
              <el-table-column
                type="selection"
                width="55"
                :selectable="checkSelectable"
                v-if="pageType == 'subjectSetting' && correctType == 2"
              >
              </el-table-column>
              <el-table-column prop="quesName" label="题目名称" width="280">
                <template slot-scope="scope">
                  <el-tooltip :content="scope.row.quesName" placement="top">
                    <span class="showOverTooltip"
                      >{{ scope.row.quesName }}
                      <div class="square" v-if="pageType == 'aiSetting'">
                        <span class="square-word">AI</span>
                      </div>
                    </span>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column label="题号" :width="pageType == 'subjectSetting' ? '400' : '150'">
                <template slot-scope="scope">
                  <!-- <el-form :model="scope.row" :rules="rules" ref="ruleForm" class="quesNos-ruleForm">
                    <el-form-item label="" prop="quesNos"> -->
                  <el-input
                    :disabled="notOperate"
                    v-model="scope.row.quesNos"
                    style="width: 80%; height: 32px"
                    @change="changeInput(scope.row)"
                  ></el-input>
                  <el-button
                    style="margin-left: 8px"
                    v-if="scope.row.isMerge"
                    :disabled="progress >= 5"
                    type="text"
                    @click="cancelMerge(scope.row, scope.$index)"
                    >取消合并</el-button
                  >
                  <!-- </el-form-item>
                  </el-form> -->
                </template>
              </el-table-column>
              <el-table-column label="题型" :width="pageType == 'objectSetting' ? '150' : '150'">
                <template slot-scope="scope">
                  <el-select
                    v-if="scope.row.typeId == 1 || scope.row.typeId == 8"
                    v-model="scope.row.typeId"
                    placeholder="请选择"
                    :disabled="notOperate"
                    style="width: 96px; height: 32px"
                    @change="changeQuesType(scope.row)"
                  >
                    <el-option
                      v-for="item in objectQuesType"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                  <span v-else>
                    {{ getQuesTypeName(Number(scope.row.typeId)) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="答案" v-if="pageType == 'objectSetting'">
                <template slot-scope="scope">
                  <!-- 单选、多选 -->
                  <template v-if="scope.row.typeId == 1 || scope.row.typeId == 8">
                    <el-button
                      :disabled="notOperate"
                      :type="
                        scope.row.answer.indexOf(String.fromCharCode(idx + 64)) > -1
                          ? 'primary'
                          : 'default'
                      "
                      v-for="idx of Number(scope.row.optionCount)"
                      @click="
                        changeAnswer(scope.$index, String.fromCharCode(idx + 64), scope.row.typeId)
                      "
                      :key="idx"
                      >{{ String.fromCharCode(idx + 64) }}</el-button
                    >
                  </template>
                  <!-- 判断题 -->
                  <template v-if="scope.row.typeId == 2">
                    <el-button
                      :disabled="notOperate"
                      :type="scope.row.answer == String.fromCharCode(65) ? 'primary' : 'default'"
                      @click="changeAnswer(scope.$index, String.fromCharCode(65), scope.row.typeId)"
                    >
                      {{ scope.row.judgeType == 1 ? '√' : scope.row.judgeType == 3 ? 'A' : 'T' }}
                    </el-button>
                    <el-button
                      :disabled="notOperate"
                      :type="scope.row.answer == String.fromCharCode(66) ? 'primary' : 'default'"
                      @click="changeAnswer(scope.$index, String.fromCharCode(66), scope.row.typeId)"
                    >
                      {{ scope.row.judgeType == 1 ? 'x' : scope.row.judgeType == 3 ? 'B' : 'F' }}
                    </el-button>
                  </template>
                </template>
              </el-table-column>
              <el-table-column prop="score" label="分值">
                <template slot-scope="scope">
                  <el-input
                    :disabled="notOperate || (pageType == 'subjectSetting' && source == 3)"
                    v-model="scope.row.score"
                    style="width: 86px; height: 32px"
                    @input="checkScore(scope.$index, 'score')"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column
                label="给分步骤"
                v-if="pageType == 'subjectSetting' && correctType == 2"
              >
                <template slot-scope="scope">
                  <el-button
                    :disabled="progress >= 5"
                    type="text"
                    @click="openSetScoreDialog(scope.row, scope.$index)"
                    v-if="scope.row.isMerge"
                    >{{ scope.row.scoreMode == 0 ? '合并给一个分' : '独立小题给分' }}</el-button
                  >
                  <el-button
                    :disabled="progress >= 5"
                    type="text"
                    @click="openSetScoreDialog(scope.row, scope.$index)"
                    v-else
                    >{{
                      scope.row.scoreStep == 1 ? '设置' : `步长${scope.row.scoreStep}分`
                    }}</el-button
                  >
                </template>
              </el-table-column>
              <el-table-column label="漏选分值" v-if="pageType == 'objectSetting'">
                <template slot-scope="scope">
                  <template v-if="scope.row.typeId == 1">
                    <el-input
                      :disabled="notOperate"
                      v-if="scope.row.ruleType != 1 && scope.row.ruleType != 2"
                      v-model="scope.row.halfScore"
                      style="width: 80px; height: 32px; margin-right: 10px"
                      @input="checkScore(scope.$index, 'halfScore')"
                    ></el-input>
                    <span v-if="scope.row.ruleType == 1" class="rule-type">新高考</span>
                    <span v-if="scope.row.ruleType == 2" class="rule-type">自定义</span>
                    <el-button type="text" @click="openSetHalfScoreDialog(scope.row, scope.$index)">{{
                      scope.row.ruleType == 2 ? '查看规则' : '自定义规则'
                    }}</el-button>
                  </template>
                  <template v-else>
                    <span>----</span>
                  </template>
                </template>
              </el-table-column>
              <el-table-column label="答案" v-if="pageType == 'aiSetting'">
                <template slot-scope="scope">
                  <el-input
                    :disabled="progress >= 5"
                    style="height: 32px"
                    v-model="scope.row.answer"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="createTime" label="特殊题型">
                <template slot-scope="scope">
                  <el-select
                    :disabled="progress >= 5"
                    v-model="scope.row.specialTypeId"
                    placeholder="请选择"
                    style="width: 96px; height: 32px"
                    @change="changeSpecialType(scope.row)"
                  >
                    <el-option
                      v-for="item in specialQuesList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <!--底部-->
          <div class="exam-ques-area-footer">
            <span style="color: red" v-if="pageType == 'subjectSetting' && correctType == 2"
              >温馨提示：合并题块后请重新进行题块框选。若已开始阅卷，所修改题块涉及题目的已阅任务将被清空，需要重新阅卷。</span
            >
            <div class="footer-btn-area">
              <el-button type="primary" :disabled="notOperate" @click="editPaper">保存修改</el-button>
            </div>
          </div>
        </div>
        <!-- 阅卷老师设置 -->
        <teacher-setting
          v-if="pageType == 'teacher'"
          :objectQuesData="objectQuesData"
          :examName="examName"
          @complete-assign="completeAssign"
        ></teacher-setting>
      </div>
      <!-- 批量设置分值 -->
      <set-score-batch
        ref="setScoreBatch"
        :modalVisible="batchFormVisible"
        :titleName="pageType == 'objectSetting' ? objectText : subjectText"
        :pageType="pageType"
        :quesInfo="quesInfo"
        @confirm-set-score="confirmSetScore"
        @close-set-score-modal="closeSetScoreModal"
      ></set-score-batch>
      <!-- 设置题组 -->
      <set-ques-group-dialog-old
        :modalVisible="isShowQuesGroup"
        :currentQues="currentQues"
        :currentSmallIndex="currentSmallIndex"
        @confirm-set-group="confirmSetGroup"
        @close-set-group="closeSetGroup"
      ></set-ques-group-dialog-old>
      <!-- 多选题特殊规则 -->
      <set-choice-scores
        :isShowDialog="showSetDialog"
        :item="smallModel"
        v-if="showSetDialog"
        @close-dialog="showSetDialog = false"
        @update-score-rules="updateRules"
      ></set-choice-scores>
    </div>
  </template>
  
  <script>
  import { mapGetters, mapState } from 'vuex';
  import BreadCrumbs from '@/components/Breadcrumbs/index.vue';
  import teacherSetting from './teacherSetting.vue';
  import {
    getViewPaper,
    updateViewPaper,
    getScanPaperPoints,
    changeViewPaper,
  } from '@/service/testbank';
  import { checkFloat } from '@/utils/number.js';
  import setScoreBatch from './modules/setScoreBatch.vue';
  import { SPECIAL_QUES } from '@/typings/scan';
  import SetQuesGroupDialogOld from './modules/setQuesGroupDialogOld.vue';
  import { getQueryString, getQuesType, isNullOrUndefined } from '@/utils';
  import { updateProgress } from '@/service/pexam';
  import setChoiceScores from './modules/setChoiceScores.vue';
  
  export default {
    name: 'mark-paper-index-old',
    data() {
      return {
        //页面类型
        pageType: 'objectSetting',
        //考试id
        examId: '',
        //考试名称
        examName: '',
        //学科
        subjectName: '',
        //试卷题卡id
        testBankId: '',
        //试卷类别
        source: 3,
        //作业id
        workId: '',
        //表格数据
        tableData: [],
        //题卡题目集合
        quesInfo: [],
        //题目统计数据
        totalData: {
          //总分
          totalScore: 0,
          //客观题
          objectScore: 0,
          //主观题分数
          subjectScore: 0,
          //总题数
          totalQuesNum: 0,
          //客观题数量
          objectQuesNum: 0,
          //主观题数量
          subjectQuesNum: 0,
        },
        //客观题数据
        objectQuesData: [],
        //主观题数据
        subjectQuesData: [],
        //智批改数据
        aiQuesData: [],
        specialQuesList: SPECIAL_QUES,
        objectText: '批量设置答案和分值',
        subjectText: '批量设置分值',
        //批量设置分值弹窗
        batchFormVisible: false,
        selectedQues: [],
        //设置题组弹窗
        isShowQuesGroup: false,
        tempSubjectQuesData: [],
        //坐标点
        pointsData: {},
        quesPoints: {},
        //当前大题序号
        bigIndex: null,
        currentQues: {},
        paperNo: getQueryString('paperNo') || '101678',
        progress: getQueryString('progress') || '1',
        progressState: getQueryString('progressState') || '1',
        source: getQueryString('source') || '3',
        //批阅类型 1：手阅 2：网阅
        correctType: 2,
        elTableHeight: 'calc(100%)',
        personalBookId: '',
        parentId: '',
        //当前所处的步骤
        progress: getQueryString('progress') || '',
        //完成状态
        progressState: getQueryString('progressState') || '',
        tips: '当前处于阅卷中状态，更新题目分值后教师按照最新分值批改，已阅任务中若有大于更新后的得分则取满分，支持教师按照最新分值回评！',
        isThreeLevel: false,
        rules: {
          quesNos: [{ required: true, message: '请输入题号', trigger: 'blur' }],
        },
        currentSmallIndex: 0,
        showSetDialog: false,
        smallModel: {},
        //多选题设置给分规则，当前小题
        smallIndex: 0,
        //quesInfo
        initQues: [],
        //是否保存过阅卷教师
        isAssignment: 0,
        isCompleteAssign: false,
        objectQuesType: [
          {
            value: 1,
            label: '多选题',
          },
          {
            value: 8,
            label: '单选题',
          },
        ],
      };
    },
    components: {
      BreadCrumbs,
      teacherSetting,
      setScoreBatch,
      SetQuesGroupDialogOld,
      setChoiceScores,
    },
    computed: {
      ...mapGetters([]),
      //不可操作
      notOperate() {
        return this.source == 4 && this.progress > 5;
      },
      //判断客观题是否都设置答案
      isAllHasAnswer() {
        if (this.objectQuesData.length > 0) {
          return !this.objectQuesData.some(item => {
            return item.answer == '';
          });
        } else {
          return true;
        }
      },
    },
    watch: {},
    created() {},
    mounted() {
      this.init();
      this.getCardInfo();
      // this.pageType =
      //   this.objectQuesData.length > 0 ? "objectSetting" : "subjectSetting";
    },
    methods: {
      /**
       * @name：初始化页面参数
       */
      init() {
        let data = this.$route.query;
        this.examId = data.examId || '';
        this.examName = data.examName || '';
        this.subjectName = data.subjectName || '';
        this.testBankId = data.testBankId || '';
        this.workId = data.workId || '';
        this.source = Number(data.source) || 3;
        this.personalBookId = data.personBookId;
      },
      /**
       * @name: 切换头部
       */
      changePageType(name) {
        // this.pageType = name;
        this.objectQuesData = [];
        this.subjectQuesData = [];
        this.aiQuesData = [];
        this.totalData.totalScore = 0;
        this.totalData.objectScore = 0;
        this.totalData.subjectScore = 0;
        this.totalData.totalQuesNum = 0;
        this.totalData.objectQuesNum = 0;
        this.totalData.subjectQuesNum = 0;
        this.orderByQuesType();
      },
      /**
       * 完成分配教师
       */
      completeAssign(data) {
        this.isCompleteAssign = data;
      },
      /**
       * @name: 根据testBankId 获取题卡详情
       */
      async getCardInfo() {
        let result = await getViewPaper({
          id: this.testBankId,
          personalBookId: this.personalBookId,
        });
        if (result && result.code == 1) {
          this.correctType = result.data.correctType;
          this.isAssignment = result.data.isAssignment;
          this.quesInfo = [];
          let quesJson = result.data.quesInfo;
          if (this.$isJson(quesJson)) {
            let quesInfo =
              result.data.teamInfo != ''
                ? JSON.parse(result.data.teamInfo)
                : JSON.parse(result.data.quesInfo);
            //去除非作答区
            quesInfo = quesInfo.filter(item => {
              return item.typeId != 10;
            });
            let initQuesInfo = JSON.parse(quesJson).filter(item => {
              return item.typeId != 10;
            });
            this.initQues = await this.handleQues(initQuesInfo);
            // this.orderInitQues();
            this.quesInfo = await this.handleQues(quesInfo);
            this.quesInfo.forEach(item => {
              item.data.forEach(ite => {
                this.$set(ite, 'halfScore', ite.halfScore || item.halfScore);
              });
            });
            // console.log('初始获取题目信息', this.quesInfo);
            // console.log('原始题目信息', this.initQues);
            this.orderByQuesType();
            this.setQuesUuid();
          }
        }
      },
      handleQues(quesInfo) {
        let data = [];
        quesInfo.forEach((item, index) => {
          let bigObj = {
            Index: !item.Index ? index : item.Index,
            data: [],
            halfScore: item.halfScore,
            id: item.id,
            name: item.name,
            quesNos: item.quesNos,
            quesScore: item.quesScore,
            score: item.score,
            type: item.type,
            typeId: Number(item.typeId),
            isMerge: item.isMerge || false,
            firstSmallId: item.data[0].id,
            points: item.points || [],
            scoreMode: item.scoreMode || 0,
            threeLevel: item.threeLevel || false,
            judgeType: item.judgeType || '',
            specialTypeId: item.specialTypeId || 0,
            scoreStep: item.scoreStep || 1,
            ruleType: item.ruleType || 0,
            // pId: item.pId || "",
          };
          item.data.forEach((ite, smallindex) => {
            let smallObj = {
              id: ite.id,
              name: ite.name,
              quesNo: ite.quesNo,
              quesNos: ite.quesNos,
              score: ite.score,
              type: ite.type,
              typeId: Number(ite.typeId),
              optionCount: ite.optionCount || 4,
              answer: ite.answer || [''],
              specialTypeId: ite.specialTypeId || 0,
              scoreStep: ite.scoreStep || 1,
              quesName: ite.quesName,
              points: ite.points,
              isMerge: ite.isMerge || false,
              threeLevel: ite.threeLevel || false,
              firstSmallId: (ite.data && ite.data.length > 0 && ite.data[0].id) || '',
              scoreMode: ite.scoreMode,
              halfScore: ite.halfScore,
              ruleType: ite.ruleType || 0,
              rules: ite.rules || [],
              // pId: ite.pId || "",
            };
            ite.data &&
              ite.data.forEach(sq => {
                sq.bigIndex = item.Index;
                sq.smallIndex = smallindex;
                sq.specialTypeId = sq.specialTypeId || 0;
                sq.scoreStep = sq.scoreStep || 1;
                sq.parentId = sq.parentId ? sq.parentId : ite.id;
                // sq.parentData = JSON.parse(JSON.stringify(ite));
                sq.ruleType = sq.ruleType || ite.ruleType || 0;
                sq.rules = sq.rules || ite.rules || [];
                sq.halfScore = sq.halfScore || ite.halfScore;
                sq.typeId = Number(sq.typeId);
                // sq.pId = sq.pId || "";
                // sq.quesNos = sq.quesNos;
              });
            if (ite.data) smallObj.data = ite.data;
            bigObj.data.push(smallObj);
          });
          data.push(bigObj);
        });
        return data;
      },
      orderInitQues(isOrder = true) {
        this.initQues.forEach((item, index) => {
          let obj = item;
          // 题组
          if (obj.isMerge) {
            //二级题型且合并题组
            let ids = obj.data.map(item => item.id);
            let quesNos =
              item.quesNos != '' ? item.quesNos : item.data.map(ite => ite.quesNos).join(',');
            let subItem = {
              data: obj.data,
              bigIndex: obj.Index,
              isMerge: true,
              scoreMode: obj.scoreMode || 0,
              quesName: obj.name,
              quesNos: quesNos,
              quesNo: obj.data[0].quesNo,
              score: obj.score,
              type: obj.type,
              typeId: Number(obj.typeId),
              ids: ids,
              specialTypeId: obj.specialTypeId || 0,
              scoreStep: obj.scoreStep || 1,
              firstSmallId: obj.data[0].id,
              points: obj.points || [],
              quesScore: obj.quesScore || 0,
            };
          } else {
            item.data.forEach(subItem => {
              //三级题型
              if (subItem.data && subItem.data.length > 0) {
                subItem.threeLevel = true;
                subItem.bigIndex = item.Index;
                subItem.firstSmallId = subItem.data[0].id;
                subItem.type = obj.type;
                subItem.quesNo = subItem.data[0].quesNo;
                if (subItem.isMerge) {
                  this.$set(subItem, 'quesName', item.name);
                } else {
                  subItem.data.forEach(sq => {
                    this.$set(sq, 'quesName', item.name);
                    this.$set(sq, 'bigIndex', item.Index);
                    this.$set(sq, 'threeLevel', true);
                    if (item.judgeType) this.$set(sq, 'judgeType', item.judgeType);
                  });
                }
                this.$set(item, 'threeLevel', true);
              } else {
                this.$set(item, 'quesNo', (item.data.length > 0 && item.data[0].quesNo) || '');
                this.$set(item, 'threeLevel', false);
                this.$set(subItem, 'threeLevel', false);
                this.$set(subItem, 'quesName', item.name);
                this.$set(subItem, 'bigIndex', item.Index);
                if (item.judgeType) this.$set(subItem, 'judgeType', item.judgeType);
                // if (!subItem.specialTypeId) this.$set(subItem, 'specialTypeId', 0);
                // if (!subItem.scoreStep) this.$set(subItem, 'scoreStep', 1);
              }
            });
          }
        });
      },
      /**
       * @name:将题目分离主、客观题
       */
      orderByQuesType(isOrder = true) {
        // console.log("%c主客观分离", "color:#409eff;font-weight:bold");
        this.quesInfo.forEach((item, index) => {
          let obj = JSON.parse(JSON.stringify(item));
          // 题组
          if (obj.isMerge) {
            //二级题型且合并题组
            let ids = obj.data.map(item => item.id);
            let quesNos =
              item.quesNos != '' ? item.quesNos : item.data.map(ite => ite.quesNos).join(',');
            let subItem = {
              data: obj.data,
              bigIndex: obj.Index,
              isMerge: true,
              scoreMode: obj.scoreMode || 0,
              quesName: obj.name,
              quesNos: quesNos,
              quesNo: obj.data[0].quesNo,
              score: obj.score,
              type: obj.type,
              typeId: Number(obj.typeId),
              ids: ids,
              specialTypeId: obj.specialTypeId || 0,
              scoreStep: obj.scoreStep || 1,
              firstSmallId: obj.data[0].id,
              points: obj.points || [],
              quesScore: obj.quesScore || 0,
            };
            this.subjectQuesData.push(subItem);
            this.totalData.totalScore += Number(subItem.score);
            this.totalData.totalQuesNum++;
            this.totalData.subjectQuesNum++;
            this.totalData.subjectScore += Number(subItem.score);
          } else {
            item.data.forEach(subItem => {
              //三级题型
              if (subItem.data && subItem.data.length > 0) {
                subItem.threeLevel = true;
                subItem.bigIndex = item.Index;
                subItem.firstSmallId = subItem.data[0].id;
                subItem.type = obj.type;
                subItem.quesNo = subItem.data[0].quesNo;
                if (subItem.isMerge) {
                  this.$set(subItem, 'quesName', item.name);
                  this.subjectQuesData.push(subItem);
                  this.totalData.totalScore += Number(subItem.score);
                  this.totalData.totalQuesNum++;
                  this.totalData.subjectQuesNum++;
                  this.totalData.subjectScore += Number(subItem.score);
                } else {
                  subItem.data.forEach(sq => {
                    // this.$set(sq, 'parentData', JSON.parse(JSON.stringify(subItem)));
                    this.$set(sq, 'quesName', item.name);
                    this.$set(sq, 'bigIndex', item.Index);
                    this.$set(sq, 'threeLevel', true);
                    if (item.judgeType) this.$set(sq, 'judgeType', item.judgeType);
                    let typeId = Number(sq.typeId);
                    let isObj = this.$isObjective(typeId);
                    this.totalData.totalScore += Number(sq.score);
                    this.totalData.totalQuesNum++;
                    if (typeId == 7) {
                      this.aiQuesData.push(sq);
                      this.totalData.objectQuesNum++;
                      this.totalData.objectScore += Number(sq.score);
                    } else {
                      if (isObj) {
                        if (isOrder) {
                          this.objectQuesData.push(sq);
                        }
                        this.totalData.objectQuesNum++;
                        this.totalData.objectScore += Number(sq.score);
                      }
                      if (!isObj) {
                        if (isOrder) {
                          this.subjectQuesData.push(sq);
                        }
                        this.totalData.subjectQuesNum++;
                        this.totalData.subjectScore += Number(sq.score);
                      }
                    }
                  });
                }
                this.$set(item, 'threeLevel', true);
              } else {
                this.$set(item, 'quesNo', (item.data.length > 0 && item.data[0].quesNo) || '');
                this.$set(item, 'threeLevel', false);
                this.$set(subItem, 'threeLevel', false);
                this.$set(subItem, 'quesName', item.name);
                this.$set(subItem, 'bigIndex', item.Index);
                if (item.judgeType) this.$set(subItem, 'judgeType', item.judgeType);
                if (!subItem.specialTypeId) this.$set(subItem, 'specialTypeId', 0);
                if (!subItem.scoreStep) this.$set(subItem, 'scoreStep', 1);
                this.totalData.totalScore += Number(subItem.score);
                this.totalData.totalQuesNum++;
                let typeId = Number(subItem.typeId);
                let isObj = this.$isObjective(typeId);
                if (typeId == 7) {
                  this.aiQuesData.push(subItem);
                  this.totalData.objectQuesNum++;
                  this.totalData.objectScore += Number(subItem.score);
                } else {
                  if (isObj) {
                    if (Array.isArray(subItem.answer)) {
                      subItem.answer = subItem.answer[0];
                    }
                    if (isOrder) {
                      this.objectQuesData.push(subItem);
                    }
                    this.totalData.objectQuesNum++;
                    this.totalData.objectScore += Number(subItem.score);
                  }
                  if (!isObj) {
                    if (isOrder) {
                      this.subjectQuesData.push(subItem);
                    }
                    this.totalData.subjectQuesNum++;
                    this.totalData.subjectScore += Number(subItem.score);
                  }
                }
              }
            });
          }
        });
        this.subjectQuesData.sort((a, b) => a.quesNo - b.quesNo);
        console.log('quesInfo', this.quesInfo);
        // console.log('题目初始客观题', this.objectQuesData);
        // console.log("题目初始主观题", this.subjectQuesData);
      },
      setQuesUuid() {
        this.quesInfo.forEach((item, index) => {
          this.$set(item, 'tempId', index);
        });
      },
      /**
       * @name: 改变单选题答案
       */
      changeAnswer(index, answer, typeId) {
        let sourceData =
          this.pageType == 'objectSetting' ? this.objectQuesData : this.subjectQuesData;
        //单选和判断
        if (typeId == 2 || typeId == 8) {
          sourceData[index].answer = answer;
        } else if (typeId == 1) {
          if (sourceData[index].ruleType == 2) {
            sourceData[index].ruleType = 0;
            sourceData[index].rules = [];
          }
          //多选
          let answerArr = [];
          if (sourceData[index].answer || sourceData[index].answer == '') {
            if (sourceData[index].answer.length > 1 && sourceData[index].answer.indexOf(',') == -1) {
              sourceData[index].answer = sourceData[index].answer.split('').join(',');
            }
            answerArr = sourceData[index].answer.split(',');
            answerArr = answerArr.filter(item => {
              return item != '';
            });
            let inde = answerArr.findIndex(obj => obj == answer);
            //存在则移除
            if (inde > -1) {
              answerArr.splice(inde, 1);
            } else {
              //不存在则添加
              answerArr.push(answer);
            }
          }
          sourceData[index].answer = answerArr.join(',');
        }
      },
      /**
       * @name: 分值检测
       */
      checkScore(index, key) {
        let sourceData =
          this.pageType == 'objectSetting' ? this.objectQuesData : this.subjectQuesData;
        let res = checkFloat(sourceData[index][key]);
        if (res > 100) {
          this.$message({
            message: '分值最大不超过100分!',
            type: 'error',
            duration: 1000,
          });
          res = 100;
        }
        sourceData[index][key] = res;
        if (sourceData[index].isMerge) {
          this.quesInfo.forEach(item => {
            if (sourceData[index].ids.includes(item.firstSmallId)) {
              item.score = sourceData[index][key];
            }
          });
        }
        //多选题自定义规则
        if (sourceData[index].typeId == 1) {
          if (sourceData[index].ruleType == 2) {
            sourceData[index].ruleType = 0;
            sourceData[index].rules = [];
          }
        }
        this.changePageType();
        // this.orderByQuesType();
      },
      /**
       * @name:打开设置分值
       */
      openSetHalfScoreDialog(row, index) {
        this.smallModel = row;
        this.showSetDialog = true;
      },
      /**
       * @name:修改规则
       */
      updateRules(data) {
        const targetId = data[0].id;
        const targetRules = data[0].rules;
  
        this.quesInfo.forEach(item => {
          if (item.data) {
            const targetItem = item.data.find(ite => ite.id === targetId);
            if (targetItem) {
              this.$set(targetItem, 'ruleType', 2);
              this.$set(targetItem, 'rules', targetRules);
            }
            item.data.forEach(small => {
              //三级题型
              if (small.data) {
                const targetItem = small.data.find(ite => ite.id == targetId);
                if (targetItem) {
                  this.$set(targetItem, 'ruleType', 2);
                  this.$set(targetItem, 'rules', targetRules);
                }
              }
            });
          }
        });
        this.changePageType();
      },
      changeQuesType(data) {
        const matchedItem = this.objectQuesType.find(item => item.value == data.typeId);
        if (matchedItem) {
          data.type = matchedItem.label;
          data.answer = '';
        }
      },
      /**
       * @name:保存修改
       */
      async editPaper() {
        //开始阅卷后
        if (this.progress == 4 && this.progressState == 1) {
          this.openTips();
        } else {
          this.saveQuesInfo();
        }
      },
      /**
       * @name:打开提示
       */
      async openTips() {
        let tips = '';
        let result = await this.requestAPI('after');
        if (result && result.code == 1 && result.data == 1) {
          tips =
            '现已开始阅卷，所修改题块涉及题目的已阅任务将被清空，需要重新设置阅卷教师、重新阅卷。确定修改题块吗？';
        } else {
          tips = this.tips;
        }
        this.$confirm(tips, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          this.saveQuesInfo();
        });
      },
      /**
       * @name:保存题目信息
       */
      async saveQuesInfo() {
        let result = await this.requestAPI();
        if (result && result.code == 1) {
          this.$message({
            message: '修改成功!',
            type: 'success',
            duration: 1000,
          });
          //手阅，纯客观题更新进度
          if (
            this.source == 3 ||
            (this.subjectQuesData.length == 0 && this.isAllHasAnswer) ||
            this.isAssignment > 0 ||
            this.isCompleteAssign
          )
            this.changeProcess();
        } else {
          this.$message({
            message: result.msg || '保存失败',
            type: 'error',
            duration: 1000,
          });
        }
      },
      /**
       * @name:请求接口
       */
      async requestAPI(type) {
        this.quesInfo.forEach(item => {
          if (!item.isMerge && !item.scoreMode == 0) {
            item.score = 0;
            item.data.forEach(ite => {
              if (ite.threeLevel && !ite.isMerge) {
                ite.score = 0;
                ite.data.forEach(it => {
                  ite.score += Number(it.score);
                });
              }
              item.score += Number(ite.score);
            });
          }
        });
        let cardInfo = {
          id: this.testBankId,
          teamInfo: JSON.stringify(this.quesInfo),
          examId: this.examId,
        };
        return await (type == 'after'
          ? changeViewPaper({ json: JSON.stringify(cardInfo) })
          : updateViewPaper({ json: JSON.stringify(cardInfo) }));
        // if (type == 'after') {
        //   return await changeViewPaper({ json: JSON.stringify(cardInfo) });
        // } else {
        //   return await updateViewPaper({ json: JSON.stringify(cardInfo) });
        // }
      },
      changeInput(row) {
        if (row.isMerge) {
          this.quesInfo.forEach(item => {
            if (item.Index == row.bigIndex && item.firstSmallId == row.firstSmallId) {
              this.$set(item, 'quesNos', row.quesNos);
            }
          });
        }
      },
      /**
       * @name：改变当前的进度
       */
      async changeProcess() {
        let params = {
          examId: this.examId,
          progress: 2,
          personalBookId: this.personalBookId,
          progressState: 1,
          schoolId: this.$sessionSave.get('schoolInfo').id,
        };
        await updateProgress(params);
      },
      /**
       * @name:打开批量设置弹窗
       */
      openSetAnswerDialog() {
        this.batchFormVisible = true;
      },
      /**
       * @name:打开设置给分步骤
       */
      openSetScoreDialog(data, index) {
        this.currentSmallIndex = index;
        this.currentQues = data;
        this.isShowQuesGroup = true;
      },
      /**
       * @name：合并题组
       */
      mergeQuesGroup() {
        // 合并后的数据
        let tempObj = {
          ids: this.selectedQues.map(item => item.id),
          parentId: [...new Set(this.selectedQues.map(item => item.parentId))],
          quesNos: this.selectedQues.map(item => item.quesNos).join(','),
          quesNo: this.selectedQues[0].quesNo,
          bigIndex: this.selectedQues[0].bigIndex,
          threeLevel: this.selectedQues[0].threeLevel,
          score: this.selectedQues.reduce((acc, item) => Number(acc) + Number(item.score), 0),
        };
        //三级结构
        if (tempObj.threeLevel) {
          this.quesInfo.forEach(ite => {
            if (ite.Index == tempObj.bigIndex) {
              ite.data.forEach(sq => {
                if (sq.data) {
                  sq.data = sq.data.filter(it => !tempObj.ids.includes(it.id));
                  sq.quesNo = sq.data.length > 0 && sq.data[0].quesNo;
                }
              });
            }
          });
          //大题下面有三级题型
          let isThreeLevel = this.quesInfo.some(item =>
            item.data.some(ite => ite.threeLevel && item.Index == tempObj.bigIndex)
          );
          let tempQuesInfo = this.quesInfo.filter(
            item =>
              !item.isMerge && item.Index == tempObj.bigIndex && (item.threeLevel || isThreeLevel)
          )[0];
          let bigData = tempQuesInfo?.data.filter(ite => {
            return tempObj.parentId.indexOf(ite.id) != -1;
          })[0];
          if (!bigData) {
            let parentData = null;
            let ques = JSON.parse(JSON.stringify(this.initQues));
            ques.some(big => {
              const foundMedium = big?.data.find(medium => medium.id == tempObj.parentId);
              if (foundMedium) {
                parentData = foundMedium;
                return true;
              }
              return false;
            });
            bigData = parentData;
          }
          let newBigQues = JSON.parse(JSON.stringify(bigData));
          newBigQues.quesNos = tempObj.quesNos;
          newBigQues.data = this.selectedQues;
          newBigQues.isMerge = true;
          //0 合并给分 1 独立小题给分
          newBigQues.scoreMode = 0;
          newBigQues.specialTypeId = 0;
          newBigQues.scoreStep = 1;
          newBigQues.points = [];
          newBigQues.quesNo = tempObj.quesNo;
          newBigQues.score = tempObj.score;
          tempQuesInfo.data.push(newBigQues);
          tempQuesInfo.data = tempQuesInfo.data.filter(ite => {
            return !ite.data || (ite.data && ite.data.length != 0);
          });
          tempQuesInfo.data.sort((a, b) => a.quesNo - b.quesNo);
        } else {
          //未合并的题
          this.quesInfo.forEach(ite => {
            if (ite.Index == tempObj.bigIndex && !ite.isMerge) {
              ite.data = ite.data.filter(it => !tempObj.ids.includes(it.id));
            }
          });
          let tempQuesInfo = this.quesInfo.filter(
            item => item.Index == tempObj.bigIndex && !item.isMerge
          )[0];
          //合并后构建大题数据
          let newBigQues = JSON.parse(JSON.stringify(tempQuesInfo));
          newBigQues.quesNos = tempObj.quesNos;
          newBigQues.data = this.selectedQues;
          newBigQues.isMerge = true;
          //0 合并给分 1 独立小题给分
          newBigQues.scoreMode = 0;
          newBigQues.specialTypeId = 0;
          //步长
          newBigQues.scoreStep = 1;
          newBigQues.points = [];
          newBigQues.score = tempObj.score;
          newBigQues.threeLevel = tempObj.threeLevel;
          this.quesInfo.push(newBigQues);
          this.quesInfo = this.quesInfo.filter(item => item.data.length != 0);
          this.quesInfo.forEach(item => {
            this.$set(item, 'quesNo', (item.data.length > 0 && item.data[0].quesNo) || '');
            this.$set(item, 'firstSmallId', (item.data.length > 0 && item.data[0].id) || '');
          });
        }
        this.quesInfo.sort((a, b) => a.quesNo - b.quesNo);
        this.changePageType();
        this.bigIndex = null;
      },
      changeSpecialType(ques) {
        this.quesInfo.forEach(item => {
          if (item.firstSmallId == ques.firstSmallId) {
            this.$set(item, 'specialTypeId', ques.specialTypeId);
          }
        });
      },
      /**
       * @name:取消合并
       */
      cancelMerge(item, index) {
        let bigIndex = item.bigIndex;
        let needCancelQues = item.data;
        let ids = item.ids;
        let smallId = item.id;
        if (item.threeLevel) {
          //大题下面有三级题型
          let isThreeLevel = this.quesInfo.some(item =>
            item.data.some(ite => ite.threeLevel && item.Index == bigIndex)
          );
          let tempQuesInfo = this.quesInfo.find(
            ques => !ques.isMerge && ques.Index === bigIndex && (ques.threeLevel || isThreeLevel)
          );
          //二级题
          const secondData = tempQuesInfo.data.filter(it => {
            return !it.threeLevel && smallId == it.id;
          });
          //三级题
          const thirdData = tempQuesInfo.data.filter(it => {
            return it.threeLevel && smallId == it.id;
          });
          //合并的小题
          let datas = tempQuesInfo?.data.find(it => it.id === smallId);
          const count = thirdData.filter(it => {
            return !it.isMerge;
          }).length;
          //合并前是否在同一个二级题下面
          const isSameQues = thirdData.every(it => {
            return it.parentId == smallId || it.id == smallId;
          });
          //题目都合并为题组
          if (count == 0 && isSameQues) {
            item.isMerge = false;
            datas = [
              ...thirdData.filter(ques => {
                return ques.firstSmallId == item.firstSmallId;
              }),
              ...secondData,
            ];
            datas[0].quesNos = datas[0].quesNo;
            tempQuesInfo.isMerge = false;
          } else {
            tempQuesInfo.data = tempQuesInfo.data.reduce((acc, ques) => {
              if (ques && ques.firstSmallId != item.firstSmallId) {
                acc.push(ques);
              }
              return acc;
            }, []);
            item.data.forEach(sq => {
              let found = false;
              tempQuesInfo.data.forEach(it => {
                if (it.id === sq.parentId && !it.isMerge && it.threeLevel) {
                  it.data.push(sq);
                  it.data.sort((a, b) => a.quesNo - b.quesNo);
                  found = true;
                }
              });
              let parentData = null;
              let ques = JSON.parse(JSON.stringify(this.initQues));
              ques.some(big => {
                const foundMedium = big?.data.find(medium => medium.id == sq.parentId);
                if (foundMedium) {
                  parentData = foundMedium;
                  return true;
                }
                return false;
              });
              parentData.data?.forEach(it => {
                if (it.id == sq.id) {
                  it.points = sq.points;
                }
              });
              if (!found && parentData) {
                tempQuesInfo.data.push(parentData);
              }
            });
            // notMergeData.data.push(...item.data);
            // notMergeData.data.sort((a, b) => a.quesNo - b.quesNo);
          }
        } else {
          //还原到未合并的数据
          this.quesInfo.forEach(ite => {
            if (ite.Index == bigIndex && !ite.isMerge) {
              ite.points = [];
              ite.data = [...ite.data, ...needCancelQues];
              ite.data.sort((a, b) => a.quesNo - b.quesNo);
            }
          });
          const count = this.quesInfo.filter(item => item.Index == bigIndex).length;
          const notMerge = this.quesInfo.filter(
            item => item.Index == bigIndex && !item.isMerge
          ).length;
          //如果是整题合并的
          if (count == 1) {
            this.quesInfo.forEach(ques => {
              if (ques.Index == bigIndex) {
                ques.isMerge = false;
              }
            });
          } else if (count > 1) {
            //一道题合并了多个题组
            if (notMerge > 0) {
              //存在未合并
              this.quesInfo = this.quesInfo.filter(ite => {
                return !ids.includes(ite.firstSmallId);
              });
            } else {
              this.quesInfo.forEach(ques => {
                if (ids.includes(ques.firstSmallId)) {
                  ques.isMerge = false;
                  ques.quesNos = '';
                }
              });
            }
          } else {
            this.quesInfo = this.quesInfo.filter(ite => {
              return !ids.includes(ite.firstSmallId);
            });
          }
          this.quesInfo.forEach(item => {
            this.$set(item, 'quesNo', (item.data.length > 0 && item.data[0].quesNo) || '');
          });
          this.quesInfo.sort((a, b) => a.quesNo - b.quesNo);
        }
        this.changePageType();
      },
      /**
       * @name:确定设置分数
       */
      confirmSetGroup(quesData, index) {
        const foundItem = this.subjectQuesData.find(
          item =>
            quesData.firstSmallId &&
            quesData.firstSmallId != '' &&
            item.firstSmallId == quesData.firstSmallId
        );
        if (foundItem) {
          Object.assign(foundItem, quesData);
        }
        //未合并题组
        if (!quesData.isMerge) {
          let sourceData =
            this.pageType == 'objectSetting' ? this.objectQuesData : this.subjectQuesData;
          sourceData[index]['scoreStep'] = quesData.scoreStep;
        }
        const { firstSmallId, isMerge, data, score, scoreMode, scoreStep } = quesData;
        this.quesInfo.forEach((ques, index) => {
          if (
            quesData.firstSmallId != '' &&
            ques.firstSmallId == quesData.firstSmallId &&
            ques.isMerge == quesData.isMerge
          ) {
            this.quesInfo[index] = {
              ...this.quesInfo[index],
              data,
              score,
              scoreMode,
              scoreStep,
            };
          }
        });
        this.changePageType();
        this.isShowQuesGroup = false;
      },
      /**
       * @name:关闭设置分数
       */
      closeSetGroup() {
        this.isShowQuesGroup = false;
      },
      /**
       * @name:批量设置分数
       * @param {*} data
       */
      confirmSetScore(data) {
        data.forEach(item => {
          item.score = 0;
          //答案
          let tempAnswerList = [];
          if (this.pageType == 'objectSetting' && item.answers != '') {
            tempAnswerList = item.typeId == 1 ? item.answers.split(',') : item.answers.split('');
            item.data.forEach((smalll, smallIndex) => {
              tempAnswerList.forEach((answer, answerIndex) => {
                if (smallIndex == answerIndex) {
                  if (answer == 'T') answer = 'A';
                  if (answer == 'F') answer = 'B';
                  smalll.answer = answer;
                }
              });
            });
          }
          //分数
          item.data.forEach((ite, index) => {
            ite.ruleType = item.ruleType;
            if (item.quesScore != '') {
              ite.data &&
                ite.data.forEach(it => {
                  it.score = item.quesScore;
                });
              ite.score = item.quesScore;
            }
            // item.typeId == 1 &&
            if (item.halfScore != '') {
              //多选题
              ite.halfScore = item.halfScore;
              ite.data &&
                ite.data.forEach(it => {
                  if (it.typeId == 1) {
                    it.halfScore = item.halfScore;
                  }
                });
            }
            //大题分数
            item.score += ite.score;
          });
          //题组
          if (item.isMerge && item.quesScore != '') {
            item.score = item.quesScore;
          }
          this.quesInfo.forEach((ques, index) => {
            if (ques.tempId == item.tempId && ques.isMerge == item.isMerge) {
              this.quesInfo[index] = item;
            }
          });
        });
        this.changePageType();
        this.batchFormVisible = false;
      },
      /**
       * @name:关闭批量设置分数弹窗
       */
      closeSetScoreModal() {
        this.batchFormVisible = false;
      },
      /**
       * @name:选中行
       * @param {*} val
       */
      handleSelectionChange(val) {
        this.bigIndex = (val.length > 0 && val[0].bigIndex + 1) || null;
        this.parentId = val.length && val[0].parentId;
        this.isThreeLevel = val.length && val[0].threeLevel;
        val.sort((a, b) => a.quesNo - b.quesNo);
        this.selectedQues = val;
      },
      /**
       * @name:设置禁选
       * @param {*} row
       */
      checkSelectable(row) {
        if (isNullOrUndefined(this.bigIndex) && (isNullOrUndefined(row.isMerge) || !row.isMerge)) {
          return true;
        }
        if (row.isMerge) {
          return false;
        }
        if (!isNullOrUndefined(this.bigIndex) && this.bigIndex == row.bigIndex + 1) {
          if (row.isMerge) {
            return false;
          } else {
            if (this.isThreeLevel == row.threeLevel) {
              return true;
            } else {
              return false;
            }
            // if (this.parentId) {
            //   if (this.parentId == row.parentId) {
            //     return true;
            //   } else {
            //     return false;
            //   }
            // } else {
            //   if (row.parentId) {
            //     return false;
            //   } else {
            //     return true;
            //   }
            // }
          }
        } else {
          return false;
        }
        // return (
        //   (this.bigIndex == "" || (this.bigIndex != "" && this.bigIndex == row.bigIndex + 1)) &&
        //   !row.isMerge
        // );
      },
      /**
       * @name:获取题目坐标
       */
      async getScanPaperPoints(data) {
        let result = await getScanPaperPoints({
          paperNo: this.paperNo,
        });
        if (result && result.code == 1) {
          this.quesPoints = {};
          this.pointsData = JSON.parse(result.data);
          this.pointsData.pages.forEach(item => {
            item.forEach(ite => {
              if (ite.question_id) {
                this.quesPoints[ite.question_id] = ite.pos;
              }
            });
          });
          data.forEach(item => {
            item.data.forEach(ite => {
              this.$set(ite, 'points', this.quesPoints[ite.id]);
            });
          });
        }
        return data;
      },
      /**
       * @name:跳转到题块框选
       */
      async jumpSetQuesBlock() {
        // this.$refs.ruleForm.validate(async (valid) => {
        //   if (valid) {
        await this.requestAPI();
        this.$router.push({
          path: 'settingQuesBlock',
          query: {
            paperNo: this.paperNo,
          },
        });
        //   } else {
        //     this.$message({
        //       message: "题号不能为空",
        //       type: "error",
        //       duration: 1000,
        //     });
        //     return false;
        //   }
        // });
      },
      getQuesTypeName(typeId) {
        return getQuesType(typeId);
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .exam-title {
    font-size: 18px;
    font-weight: bold;
    margin-right: 20px;
    color: #161e26;
  }
  
  .exam-category {
    margin-right: 15px;
    height: 24px;
    line-height: 24px;
    background: #f0f2f5;
    border-radius: 12px;
    color: #606266;
    text-align: center;
    font-weight: 400;
    font-size: 14px;
    padding: 0 11px;
  }
  
  .exam-info {
    font-size: 14px;
    color: #909299;
    line-height: 24px;
    margin-bottom: 20px;
  
    .blue-txt {
      color: #008dea;
    }
  }
  
  .exam-mk-main {
    background: #fff;
    // min-height: calc(100% - 80px);
    height: 100%;
    border-radius: 2px;
    border: 1px solid #ebeef1;
    border-top: 0px;
  
    .exam-mk-ques {
      padding: 20px;
      height: 100%;
      .exam-ques-area-footer {
        margin-top: 10px;
        width: 100%;
        height: 54px;
        line-height: 54px;
        // border-top: 1px solid #f3f3f3;
        .footer-btn-area {
          float: right;
          display: flex;
          align-items: center;
          height: 100%;
          .save-btn {
            width: 116px;
            margin-right: 40px;
          }
        }
      }
    }
  }
  .table-container {
    height: calc(90% - 200px);
    // overflow-y: auto;
  }
  
  .square {
    width: 0;
    height: 0;
    border: 12px solid transparent;
    border-top: 13px solid #409eff;
    border-left: 13px solid #409eff;
    z-index: 100;
    border-radius: 5px 0 0 0;
    position: absolute;
    left: 0;
    top: 0;
    .square-word {
      position: absolute;
      left: -13px;
      top: -23px;
      color: #ffffff;
      font-size: 13px;
    }
  }
  .ai-word {
    color: #409eff;
    margin-left: 10px;
    font-weight: bold;
    position: absolute;
    // bottom: 8px;
    // right: 3px;
    top: -15px;
    left: -9px;
  }
  .showOverTooltip {
    display: -webkit-box;
    position: relative;
    text-overflow: ellipsis;
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .rule-type {
    margin-right: 15px;
  }
  </style>
  <style lang="scss">
  .exam-mk-container {
    .el-tabs__header {
      margin: unset !important;
    }
  
    .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
      background: #fff;
      font-weight: bold;
    }
  
    .el-tabs__item {
      font-size: 16px !important;
    }
  
    .el-input__inner {
      height: 32px;
      line-height: 32px;
      border: 1px solid #dcdfe6;
    }
  
    .exam-mk-ques {
      .el-table thead {
        color: #606266;
      }
  
      .el-table th.el-table__cell {
        background-color: #f5f7fa !important;
      }
  
      .el-table th.el-table__cell > .cell {
        padding-left: 20px !important;
        padding-right: 20px !important;
      }
  
      .el-table .el-table__cell {
        padding: 8px 0 !important;
      }
  
      .el-table .el-table__row .cell {
        padding-left: 20px !important;
        padding-right: 20px !important;
        line-height: 35px !important;
      }
  
      .el-table {
        .el-button {
          width: 32px;
          height: 32px;
          padding: unset !important;
        }
      }
      .el-table__header-wrapper .el-checkbox {
        display: none;
      }
    }
  }
  .quesNos-ruleForm {
    .el-form-item {
      margin-bottom: 0px !important;
    }
  }
  </style>
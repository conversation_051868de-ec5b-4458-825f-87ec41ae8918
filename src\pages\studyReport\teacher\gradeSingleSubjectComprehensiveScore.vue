<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-04-02 09:02:32
 * @LastEditors: 小圆
-->
<template>
  <div>
    <div v-loading="tableLoading">
      <base-table
        v-if="tableData.length"
        :data="tableData"
        :column="tableColumns"
        v-bind="getTableAttr()"
        v-drag-table
      ></base-table>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>

    <!-- 学科难度图表 -->
    <div style="margin-top: 20px; width: 100%; height: 450px" id="DifficultyChart"></div>
    <!-- 学科平均分对比图 -->
    <div style="margin-top: 20px; width: 100%; height: 450px" id="AvgScoreChart"></div>
    <!-- 四率分布对比图 -->
    <div style="margin-top: 20px; width: 100%; height: 450px" id="FourRateChart"></div>
  </div>
</template>
<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';
import {
  getDefaultGrid,
  getDefaultLegend,
  getDefaultPercentAxis,
  getDefaultTitle,
  getDefaultToolBox,
} from '../plugins/DefaultEchartsOption';

@Component({
  components: {},
})
export default class GradeSingleSubjectComprehensiveScore extends Mixins(TableCommon) {
  // 难度图表
  difficultyChart: EChartsType = null;
  // 平均分图表
  avgScoreChart: EChartsType = null;
  // 四率图表
  fourRateChart: EChartsType = null;

  callbackGetTableData() {
    this.renderDifficultyChart();
    this.renderAvgScoreChart();
    this.renderFourRateChart();
  }

  // 渲染学科难度图表
  renderDifficultyChart() {
    if (this.difficultyChart) {
      this.difficultyChart.dispose();
      this.difficultyChart = null;
    }

    const dom = document.getElementById('DifficultyChart');
    this.difficultyChart = this.$echarts.init(dom);

    let option: EChartsOption = {
      grid: getDefaultGrid(),
      title: { ...getDefaultTitle(), text: '学科难度对比图' },
      legend: getDefaultLegend(),
      tooltip: {
        trigger: 'axis',
      },
      toolbox: getDefaultToolBox(),
      xAxis: {
        type: 'category',
        data: this.tableData.map(item => item.subjectName),
        boundaryGap: false,
        axisLabel: {},
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '难度',
          data: this.tableData.map(item => item.difficulty),
          type: 'line',
        },
      ],
    };

    this.difficultyChart.setOption(option);
  }

  // 渲染学科平均分图表
  renderAvgScoreChart() {
    if (this.avgScoreChart) {
      this.avgScoreChart.dispose();
      this.avgScoreChart = null;
    }

    const dom = document.getElementById('AvgScoreChart');
    this.avgScoreChart = this.$echarts.init(dom);

    let option: EChartsOption = {
      grid: getDefaultGrid(),
      title: { ...getDefaultTitle(), text: '学科平均分对比图' },
      legend: {
        ...getDefaultLegend(),
      },
      tooltip: {
        trigger: 'axis',
      },
      toolbox: getDefaultToolBox(),
      xAxis: {
        type: 'category',
        data: this.tableData.map(item => item.subjectName),
        boundaryGap: false,
        axisLabel: {},
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '平均分',
          data: this.tableData.map(item => item.avgScore),
          type: 'line',
        },
      ],
    };

    this.avgScoreChart.setOption(option);
  }

  // 渲染四率图表
  renderFourRateChart() {
    if (this.fourRateChart) {
      this.fourRateChart.dispose();
      this.fourRateChart = null;
    }

    const dom = document.getElementById('FourRateChart');
    this.fourRateChart = this.$echarts.init(dom);

    let option: EChartsOption = {
      grid: getDefaultGrid({ top: 80 }),
      title: getDefaultTitle({
        text: '四率分布对比图',
      }),
      legend: getDefaultLegend(),
      tooltip: {
        trigger: 'axis',
      },
      toolbox: getDefaultToolBox(),
      xAxis: {
        type: 'category',
        data: this.tableData.map(item => item.subjectName),
        boundaryGap: false,
      },
      yAxis: getDefaultPercentAxis(),
      series: [
        {
          name: '高分率（%）',
          data: this.tableData.map(item => parseFloat(item.topRate) || 0),
          type: 'line',
        },
        {
          name: '优秀率（%）',
          data: this.tableData.map(item => parseFloat(item.goodRate) || 0),
          type: 'line',
        },
        {
          name: '及格率（%）',
          data: this.tableData.map(item => parseFloat(item.passRate) || 0),
          type: 'line',
        },
        {
          name: '低分率（%）',
          data: this.tableData.map(item => parseFloat(item.lowRate) || 0),
          type: 'line',
        },
      ],
    };

    this.fourRateChart.setOption(option);
  }
}
</script>

<style scoped lang="scss"></style>

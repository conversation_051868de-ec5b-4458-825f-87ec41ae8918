<template>
    <div class="previewPaper">
        <div class="detail-body">
            <!--标题-->
            <div class="paper-title">{{previewObj.name}}</div>
            <div v-loading="quesLoading" class="quesList" v-if="paramsData.activeType != 3">
                <div v-for="(item,idx) in previewObj.quesInfo" :key="item.name">
                    <!--副标题 卷一、卷二-->
                    <h2 class="second-title">{{item.name}}</h2>
                    <div class="third-box" v-for="(subItem,subIdx) in item.data" :key="subItem.name">
                        <!--题目类型-->
                        <h3 class="third-title">{{subItem.name}}</h3>
                        <!--题面-->
                        <div class="quesContent" v-for="(third,thirdIdx) in subItem.data" :key="third.name">
                            <div class="ques-list">
                                <!--题面-->
                                <div class="ques-content clearfix" v-if="third.content ">
                                    <span class="pull-left">{{third.name}}</span>
                                    <div class="question_content">
                                        <LatexHtml class="question_body"
                                                   :html="third.content.topic"></LatexHtml>
                                    </div>
                                </div>
                                <!--答案解析-->
                                <div class="ques-detail" v-if="paramsData.activeType == 1 ||paramsData.activeType == 3">
                                    <!--答案-->
                                    <div class="answer_box clearfix">
                                        <strong class="pull-left">【答案】</strong>
                                        <div style="padding-left:10px;">
                                            <div class='answer_content' v-if="third.content.quesType===2">
                                                {{third.content.answer.split(',')[0]==='A'?'正确':'错误'}}
                                            </div>
                                            <LatexHtml class='answer_content' v-else
                                                       :html="third.content.answer"></LatexHtml>
                                        </div>
                                    </div>
                                    <!--考点-->
                                    <div class="answer_box clearfix" v-if="third.content.knowledgeName">
                                        <strong class="pull-left">【考点】</strong>
                                        <div style="padding-left:10px;">
                                            {{third.content.knowledgeName.split(',').join('，')}}
                                        </div>
                                    </div>
                                    <!--解析-->
                                    <div v-if="third.content.analysis"
                                         class="answer_box clearfix">
                                        <strong class="pull-left">【解析】</strong>
                                        <LatexHtml class='answer_content'
                                                   style="padding-left:10px;"
                                                   :html="third.content.analysis"></LatexHtml>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--答案解析在卷尾，或者只显示答案解析-->
            <div v-if="paramsData.activeType == 2||paramsData.activeType == 3">
                <div v-for="(item,idx) in previewObj.quesInfo" :key="item.name">
                    <!--副标题 卷一、卷二-->
                    <h2 class="second-title">{{item.name}}</h2>
                    <div class="third-box" v-for="(subItem,subIdx) in item.data" :key="subItem.name">
                        <!--题目类型-->
                        <h3 class="third-title">{{subItem.name}}</h3>
                        <!--题面-->
                        <div class="quesContent" v-for="(third,thirdIdx) in subItem.data" :key="third.name">
                            <div class="ques-list clearfix">
                                <span class="pull-left">{{third.name}}</span>
                                <!--答案解析-->
                                <div class="ques-detail" style="padding-left:20px;"
                                     v-if="paramsData.activeType == 2 ||paramsData.activeType == 3">
                                    <!--答案-->
                                    <div class="answer_box clearfix">
                                        <strong class="pull-left">【答案】</strong>
                                        <div style="padding-left:10px;">
                                            <div class='answer_content' v-if="third.content.quesType===2">
                                                {{third.content.answer.split(',')[0]==='A'?'正确':'错误'}}
                                            </div>
                                            <LatexHtml class='answer_content' v-else
                                                       :html="third.content.answer"></LatexHtml>
                                        </div>
                                    </div>
                                    <!--考点-->
                                    <div class="answer_box clearfix" v-if="third.content.knowledgeName">
                                        <strong class="pull-left">【考点】</strong>
                                        <div style="padding-left:10px;">
                                            {{third.content.knowledgeName.split(',').join('，')}}
                                        </div>
                                    </div>
                                    <!--解析-->
                                    <div v-if="third.content.analysis"
                                         class="answer_box clearfix">
                                        <strong class="pull-left">【解析】</strong>
                                        <LatexHtml class='answer_content'
                                                   style="padding-left:10px;"
                                                   :html="third.content.analysis"></LatexHtml>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import {getViewPaper, getQueTypeListBySubId} from '@/service/pexam';
    import {chooseQuesSearch} from '@/service/pbook';
    import LatexHtml from '@/components/LatexHtml';

    export default {
        name      : 'preview-paper',
        components: {
            LatexHtml,
        },
        data () {
            return {
                previewObj : {},
                quesLoading: false,
                // 从url上获取的参数列表
                paramsData : {},
                // 默认分值
                scoreList  : [],
            };
        },
        mounted () {
            /**返回参数中的activeSize和activeType取值说明
             * activeSize: this.activeSize, // 0: A4, 1: B5
             * activeType: this.activeType // 0: 无答案， 1：答案解析在每题后， 2：答案解析在卷尾， 3：只包含答案解析
             * **/
            this.paramsData = this.$route.query;
            this.getQueTypeListBySubId();
        },
        computed  : {},
        methods   : {
            // 获取题型的默认分值
            getQueTypeListBySubId () {
                getQueTypeListBySubId({
                    subId         : this.paramsData.subId,
                    schoolId      : this.$sessionSave.get('schoolInfo').id,
                    serviceVersion: 4.0,
                    hwTypeCode    : 104
                }).then(data => {
                    this.scoreList = data.data;
                    this.getViewPaper();
                });
            },
            // 获取预览列表数据
            getViewPaper (scoreList) {
                getViewPaper({
                    id: this.paramsData.paperId
                }).then(res => {
                    this.previewObj = res.data;
                    this.previewObj.quesInfo = this.previewObj.quesInfo ? JSON.parse(this.previewObj.quesInfo) : [];
                    let quesInfo = this.previewObj.quesInfo,
                        quesIds = [], paperTotalScore = 0;
                    quesInfo.length && quesInfo.forEach((item, index) => {
                        this.$set(item, 'onlyId', index);
                        item.data.forEach((subItem, subIndex) => {
                            // 获取当前题型总分
                            let score = this.getDefaultScore(subItem.type), typeScore = score * subItem.data.length;
                            this.$set(subItem, 'defaultScore', typeScore);
                            paperTotalScore += typeScore;
                            this.$set(subItem, 'onlyId', index + '-' + subIndex);
                            subItem.data.forEach((thirdItem, thirdIndex) => {
                                this.$set(thirdItem, 'onlyId', index + '-' + subIndex + '-' + thirdIndex);
                                // 获取当前小题分值
                                this.$set(thirdItem, 'defaultScore', score);
                                quesIds.push(thirdItem.id);
                            });
                        });
                    });
                    this.paperTotalScore = paperTotalScore;
                    this.chooseQuesSearch(quesIds.join(','));
                });
            },
            getDefaultScore (type) {
                let score = 0;
                this.scoreList.forEach(item => {
                    if (item.dtTypeName === type) {
                        score = item.score;
                    }
                });
                return score;
            },
            // 更新顺序
            updateOrder () {
                let quesInfo = this.previewObj.quesInfo, quesIndex = 0, typeIndex = 0;
                quesInfo.forEach(item => {
                    item.data.length && item.data.forEach(sub => {
                        typeIndex += 1;
                        sub.name = this.$sectionToChinese(typeIndex) + '、' + sub.name.split('、')[1];
                        sub.data.length && sub.data.forEach(third => {
                            if (!third.isChange) {
                                quesIndex += 1;
                                third.name = quesIndex;
                            }
                        });
                    });
                });
                console.log('333333=========>', this.previewObj.quesInfo);
            },
            // 获取题目的题面
            chooseQuesSearch (ids) {
                this.listLoading = true;
                chooseQuesSearch({
                    qIds: ids,
                }).then(data => {
                    let res = data.data;
                    if (!this.previewObj.quesInfo.length) {
                        this.quesLoading = false;
                        return;
                    }
                    this.previewObj.quesInfo.forEach(obj => {
                        this.$set(obj, 'level', 1);
                        obj.data.length && obj.data.forEach(subObj => {
                            this.$set(subObj, 'level', 2);
                            this.$set(subObj, 'isAverage', true);
                            subObj.data.length && subObj.data.forEach(third => {
                                this.$set(third, 'level', 3);
                                res.forEach(item => {
                                    if (third.id === item.id) {
                                        this.$set(third, 'content', item);
                                    }
                                });

                            });
                        });
                    });
                    console.log('this.previweObj.quesInfo=========>', this.previewObj.quesInfo);
                    this.$nextTick(() => {
                        this.$katexUpdate();
                        this.quesLoading = false;
                    });
                }).catch(err => {
                    this.quesLoading = false;
                });
            },
        }
    };
</script>

<style lang="scss" scoped>
    .previewPaper {
        width       : 100%;
        height      : 100%;
        overflow-x  : hidden;
        overflow-y  : auto;
        padding     : 0px 24px 0 24px;
        background-color: #ffffff;
        font-family : Microsoft YaHei;
        .detail-body {
            width      : 100%;
            height     : 100%;
            background : #fff;
            .paper-title {
                font-size   : 18px;
                font-weight : bold;
                color       : #3f4a54;
                line-height : 52px;
                height      : 52px;
                text-align  : center;
            }
            .second-title {
                font-size   : 18px;
                font-weight : 400;
                color       : #3f4a54;
                line-height : 44px;
                height      : 44px;
                text-align  : center;
                margin      : 20px 0 0;
            }
            .third-box {
                margin-bottom : 20px;
                .third-title {
                    line-height : 44px;
                    height      : 44px;
                    margin      : 7px 0;
                    font-size   : 16px;
                    font-weight : 400;
                    color       : #3f4a54;
                    text-align  : left;
                }
                .quesContent {
                    padding : 15px 5px;
                    .ques-detail {
                        > div {
                            margin-bottom : 5px;
                        }
                    }
                }
            }

        }
    }
</style>

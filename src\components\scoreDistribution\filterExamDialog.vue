<template>
  <div>
    <el-dialog
      custom-class="filter-dialog"
      title="选择考试"
      :visible="dialogFormVisible"
      width="1100px"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <div>
        <!--学年-->
        <!-- <div class="header__select">
          <span class="select__label">学年：</span>
          <el-select
            v-model="yearValue"
            class="year-select"
            @change="changeFilter"
            placeholder="请选择"
          >
            <el-option v-for="item in years" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </div> -->
        <!--年级 (仅校长和运营账号显示年级切换)-->
        <!-- <div class="header__select">
          <span class="select__label">年级：</span>
          <el-select
            v-model="gradeValue"
            class="grade-select"
            @change="onChangeGrade"
            placeholder="请选择"
          >
            <el-option v-for="item in gradeList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </div> -->

        <year-select ref="yearSelect" v-model="yearValue" :noAll="true" @change="changeYear"></year-select>
        <grade-select
          ref="gradeSelect"
          v-model="gradeValue"
          :year="startTime"
          :noAll="true"
          :excludeRoles="['5', '6']"
          @change="onChangeGrade"
        ></grade-select>
        <subject-select
          ref="subjectSelect"
          v-model="subjectId"
          :phase-id="phaseId"
          :enterYear="gradeYear"
          :systemCode="gradeSystemCode"
          :excludeRoles="['5', '6']"
          @change="onChangeSubject"
        ></subject-select>
        <category-select v-model="categoryId" @change="changeFilter"></category-select>

        <!--搜索-->
        <div class="header__select">
          <div class="header__serarch clearfix display_flex">
            <el-input
              class="search__text"
              placeholder="输入考试名称搜索"
              v-model="searchValue"
              @keyup.enter.native="changeFilter"
              clearable
            >
            </el-input>
            <div
              class="search__icon el-icon-search display_flex align-items_center justify-content_flex-center"
              @click="changeFilter"
            ></div>
          </div>
        </div>
      </div>
      <el-table
        ref="multipleTable"
        :data="examList"
        tooltip-effect="dark"
        row-key="examId"
        style="width: 100%; padding-top: 20px"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
          :reserve-selection="true"
          :selectable="selectable"
        >
        </el-table-column>
        <el-table-column prop="examName" label="考试名称" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="subjectName" label="学科" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="address" label="考试时间" width="120">
          <template slot-scope="scope">{{ scope.row.examDateTime.substring(0, 10) }}</template>
        </el-table-column>
        <el-table-column prop="categoryName" label="类别" width="120"> </el-table-column>
      </el-table>
      <!--分页器-->
      <el-pagination
        background
        style="margin: 20px 0 30px 0"
        :hide-on-single-page="!examList.length"
        class="text-center"
        layout="total, prev, pager, next"
        @current-change="handleCurrentChange"
        :current-page.sync="pagination.page"
        :page-size="pagination.limit"
        :total="pagination.total_rows"
      >
      </el-pagination>
      <!-- 已选择的考试 -->
      <div>
        <div class="tip" style="color: red; font-size: 16px">最多支持选取10场考试进行对比</div>
        <div v-if="selectExamList.length">
          <div class="filter-list">
            <p>已选择的考试：</p>
            <el-tag
              v-for="item in selectExamList"
              :key="item.examId"
              class="filter-tag"
              closable
              :disable-transitions="true"
              @close="removeSelected(item)"
            >
              {{ item.examName }}
            </el-tag>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="$emit('close-dialog')">取 消</el-button>
        <el-button type="primary" :loading="isComfirming" @click="addExam">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getExamReportList } from '@/service/pexam';
import { getUserInfoToPersonalityTest } from '@/service/api';
import { getYears, localSaveUnique } from '@/utils/index.js';
import UserRole from '@/utils/UserRole';
import SubjectSelect from '../SelectComponents/subjectSelect.vue';
import CategorySelect from '../SelectComponents/categorySelect.vue';
import YearSelect from '../SelectComponents/yearSelect.vue';
import GradeSelect from '../SelectComponents/gradeSelect.vue';
import { getLastYearStr } from '../SelectComponents/SelectDefault';

export default {
  components: {
    SubjectSelect,
    CategorySelect,
    YearSelect,
    GradeSelect,
  },
  data() {
    return {
      dialogFormVisible: true,
      //当前选择年级
      gradeValue: '',
      //考试列表
      examList: [],
      pagination: {
        page: 1,
        limit: 10,
        total_rows: 0,
      },
      //已选择的考试
      selectExamList: [],
      //限制多选的数量
      limitNum: 10,
      // 提交按钮是否在加载中
      isComfirming: false,
      //学年
      yearValue: '',
      // 学年开始年份
      startTime: '',
      //搜索关键字
      searchValue: '',
      // 学年列表
      years: [],
      //已选的考试id
      selectIds: [],
      // 当前学科Id
      subjectId: '',
      // 当前分类Id
      categoryId: '',
      phaseId: '',

      // 入学年份
      gradeYear: '',
      // 学制
      gradeSystemCode: '',
    };
  },
  async mounted() {
    this.$nextTick(() => {
      this.initData();
    });
  },
  methods: {
    /**
     * @name：初始化年级和学年
     */
    async initData() {
      this.years = await this.$refs.yearSelect.initYearsList();
      let yearItem = this.years.find(item => item.id == localSaveUnique.get('compareExamYearId'));
      if (!yearItem) {
        yearItem = this.years[0];
      }
      this.yearValue = yearItem.id;
      this.startTime = yearItem.startTime;
      
      await this.$nextTick();

      let gradeList = await this.$refs.gradeSelect.initGrade();
      let grade = gradeList.find(item => item.id == localSaveUnique.get('compareExamGrade'));
      if (!grade) {
        grade = gradeList[0];
      }
      this.gradeValue = grade.id;
      this.gradeYear = grade?.year;
      this.phaseId = grade?.phaseId || '';
      this.gradeSystemCode = grade?.systemCode;

      await this.$nextTick();

      let subjectList = await this.$refs.subjectSelect.initSubject();
      let subject = subjectList.find(
        item => item.id == localSaveUnique.get('compareExamSubjectId')
      );
      if (!subject) {
        subject = subjectList[0];
      }
      this.subjectId = subject.id;
      this.categoryId = localSaveUnique.get('compareExamCategoryId') || '';
      this.getExamList();
    },
    /**
     * @name:关闭弹窗
     */
    handleClose() {
      this.dialogFormVisible = false;
      this.$emit('close-dialog');
    },
    /**
     * @name:获取考试列表
     */
    async getExamList(type) {
      try {
        const res = await getExamReportList({
          schoolId: this.$sessionSave.get('schoolInfo').id,
          acadYearsId: this.yearValue,
          acadTermId: '',
          gradeId: this.gradeValue || '',
          parentId: 0, //获取自定义报告
          subjectId: this.subjectId || '',
          categoryId: this.categoryId || '',
          keyWord: this.searchValue || '',
          page: this.pagination.page, // 页码
          pageSize: this.pagination.limit,
          importScoreState: 1, // 导入成绩状态(-1:全部 0:未导入 1:已导入)
        });
        this.examList = res.data.list;
        this.examList = this.examList.map(item => {
          item.subjectName = item.paperList
            .filter(elem => elem.importScoreState == 1)
            .map(elem => elem.subectName)
            .join('/');
          return item;
        });
        this.pagination.total_rows = res.data.total;
        // if (type != 'change') this.checkedVaule();
      } catch (error) {
        console.warn(error);
        this.examList = [];
      }
    },
    /***
     * @name:设置默认选中
     */
    checkedVaule() {
      this.selectExamList = localSaveUnique.get('compareExamList') || [];
      this.selectExamList.forEach(item => {
        this.$refs.multipleTable.toggleRowSelection(item, true);
      });
    },
    /**
     * @name:分页
     */
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getExamList('change');
    },
    /**
     * @name:限制选择考试的数量
     */
    limitFn(list) {
      this.$refs.multipleTable.clearSelection();
      for (let i = 0; i < this.limitNum; i++) {
        this.$refs.multipleTable.toggleRowSelection(list[i]);
      }
    },

    // 清空选择
    clearSelected() {
      this.selectExamList = [];
      this.$refs.multipleTable.clearSelection();
    },

    // 根据考试对象移除选择
    removeSelected(item) {
      const selectExamList = this.$refs.multipleTable.selection || [];
      const selectExam = selectExamList.find(e => item.examId == e.examId);
      this.$refs.multipleTable.toggleRowSelection(selectExam, false);
    },

    /**
     * @name:判断复选框是否可以选择
     */
    selectable(row) {
      let index = this.selectExamList.findIndex(v => v.examId === row.examId);
      if (this.selectExamList.length >= this.limitNum) {
        if (index !== -1) {
          return true;
        } else {
          return false;
        }
      } else {
        return true;
      }
    },
    /**
     * @name:选择考试
     */
    handleSelectionChange(val) {
      if (val.length > this.limitNum) {
        this.limitFn(val);
        return;
      }
      this.selectExamList = [...val];
    },
    /**
     * @name：切换筛选项
     */
    changeFilter() {
      // this.$refs.multipleTable.clearSelection();
      // this.selectExamList = [];
      this.getExamList('change');
    },
    /**
     * @name:确定选择考试
     */
    addExam() {
      this.selectIds = this.selectExamList.map(item => {
        return item.examId;
      });
      if(this.selectIds.length == 0){
        this.$message.error('请选择考试');
        return;
      }

      let gradeList = this.$refs.gradeSelect.gradeList;
      let gradeName = gradeList.filter(item => {
        return item.id == this.gradeValue;
      })[0].name;
      localSaveUnique.set('compareExamGradeName', gradeName);
      localSaveUnique.set('compareExamGrade', this.gradeValue);
      localSaveUnique.set('compareExamSubjectId', this.subjectId);
      localSaveUnique.set('compareExamYearId', this.yearValue);
      localSaveUnique.set('compareExamCategoryId', this.categoryId);

      localSaveUnique.set('compareExamIds', this.selectIds);
      // localSaveUnique.set('compareExamList', this.selectExamList);

      this.$emit('get-exam-ids', this.selectIds);
      this.handleClose();
    },

    async onChangeGrade(id, grade) {
      this.gradeValue = id;
      this.gradeYear = grade?.year;
      this.gradeSystemCode = grade?.systemCode || '';
      this.phaseId = grade?.phaseId || '';
      await this.$nextTick();
      let list = await this.$refs.subjectSelect.initSubject();
      this.subjectId = list[0]?.id;
      this.selectExamList = [];
      this.clearSelected();
      this.pagination.page = 1;
      this.pagination.total_rows = 0;
      this.changeFilter();
    },

    onChangeSubject(id, val) {
      this.subjectId = id;
      this.selectExamList = [];
      this.clearSelected();
      this.pagination.page = 1;
      this.pagination.total_rows = 0;
      this.changeFilter();
    },

    async changeYear(year) {
      const yearItem = this.years.find(item => item.id == year);
      this.yearValue = year;
      this.startTime = yearItem.startTime;

      await this.$nextTick();
      let gradeList = await this.$refs.gradeSelect.initGrade();
      this.onChangeGrade(gradeList[0]?.id, gradeList[0]);
    },
  },
};
</script>

<style lang="scss" scoped>
.header__select {
  display: inline-block;
  margin-right: 29px;
  float: left;

  .year-select,
  .term-select,
  .grade-select {
    width: 150px;
  }
}

.header__serarch {
  display: flex;
  width: 240px;

  .search__icon {
    width: 38px;
    font-size: 18px;
    color: #fff;
    background: #409eff;
    /*background: #0dc2b3 url(../../static/img/pub-icon_reportlist.cfdb858.png) 9px 7px no-repeat;*/
    border-radius: 0 3px 3px 0;
    outline: none;
    cursor: pointer;
  }

  .search__text {
  }
}

.filter-choice {
  margin-bottom: 10px;
}

.filter-grade {
  margin-left: 20px;
  cursor: pointer;

  &.active {
    color: #409eff;
  }
}

.filter-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  font-size: 14px;

  .filter-tag {
    margin-right: 10px;
    margin-bottom: 5px;
  }
}

.filter-selected {
  margin-right: 20px;
}
.tip {
  color: red;
  font-size: 16px;
  margin: 5px 0;
}
</style>

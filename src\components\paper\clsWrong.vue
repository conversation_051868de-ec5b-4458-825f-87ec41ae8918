<template>
  <div class="knowledgeContainer" v-loading="quesLoading">
    <div class="mistack-filter clearfix text-right" v-if="type != 'wrongques'">
      <span>
        得分率
        <el-button class="rate-btn" v-for="(item, index) in rateList" :key="index"
          :class="{ active: activeRate === index }" @click="changeRate(index, item)">{{ item.name }}
        </el-button>
      </span>
      <span>
        共有错题：
        <span style="color: #409eff; margin-right: 20px">{{ quesPage.total_rows }}题</span>
      </span>
      <span>
        题型：
        <el-select style="width: 150px" v-model="quesType" multiple collapse-tags placeholder="请选择类型" @change="listErrQues">
          <el-option
            v-for="item in quesTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>

      </span>

      <span
        class="el-icon-plus"
        v-if="quesList.length && !isStuErr && !isCef"
        @click="selectAll"
        :class="isSelectAll ? 'selectAll el-icon-minus' : 'el-icon-plus'"
      >
        {{ isSelectAll ? '全部移除' : '全部选入' }}
      </span>
    </div>
    <div class="mistack-filter clearfix text-right" v-else>

      <span class="el-icon-plus" v-if="quesList.length && !isStuErr && !isCef" @click="selectAll"
        :class="isSelectAll ? 'selectAll el-icon-minus' : 'el-icon-plus'">
        {{ isSelectAll ? '全部移除' : '全部选入' }}
      </span>
    </div>
    <div style="min-height: 270px; margin-top: 20px">
      <!--题目列表-->
      <div class="comment-ques" v-if="quesList.length && !quesLoading">
        <div class="ques-list" v-for="(item, index) in quesList" :key="index" :data-index="item.totalIndex + 1"
          @click="selectQuesItem(item)" :class="{ active: item.quesId === detailId }">
          <div class="title">
            <span class="ques-type" v-if="item.quesTypeName">{{ item.quesTypeName }}</span>
            <!--<span>【{{item.examName}}】</span>-->
          </div>
          <div class="ques-content" v-if="item.content && !Array.isArray(item.content)">
            <div class="question_content">
              <LatexHtml class="question_body" :html="item.content.topic" :subject="item.content.subject"></LatexHtml>
            </div>
          </div>
          <div class="ques-content" v-if="item.content && Array.isArray(item.content)">
            <div v-for="(content, i) in item.content" :key="i">
              <div class="question_content" style="padding: 20px 0">
                <LatexHtml class="question_body" :html="content.topic" :subject="item.content.subject"></LatexHtml>
              </div>
              <div class="edit-block display_flex align-items_center clearfix">
                <span class="flex_1 text-ellipsis"
                  style="margin-left: 40px; margin-right: 20px">来自：【{{ item.examName }}】</span>
                <span class="ques-btn" :class="content.showDetail ? 'active' : ''"
                  @click.stop="showQuesDetails(content)">题目详情<i :class="content.showDetail ? 'el-icon-caret-top active' : 'el-icon-caret-bottom'
                    "></i></span>
                <span class="ques-btn" @click.stop="showSimilarQues(item)">变式练习</span>
                <span class="add edit-btn text-center" v-if="!isStuErr && !isCef" @click.stop="selectQues(item, content)"
                  :class="[
                    selectIds.length && selectIds.indexOf(content.qId) >= 0
                      ? 'hasAdd el-icon-minus'
                      : 'el-icon-plus',
                  ]">
                  {{ selectIds.length && selectIds.indexOf(content.qId) >= 0 ? ' 移除' : ' 选入' }}
                </span>
              </div>
              <div class="ques-detail" v-if="content.showDetail">
                <div>
                  <!--答案-->
                  <div class="answer_box display_flex align-items_flex-start">
                    <strong class="flex_shrink_0">【答案】</strong>
                    <div class="flex_1">
                      <div class="answer_content" v-if="content.quesType === 2">
                        {{ content.answer.split(',')[0] === 'A' ? '正确' : '错误' }}
                      </div>
                      <LatexHtml class="answer_content" v-else :html="content.answer"></LatexHtml>
                    </div>
                  </div>
                  <!--考点-->
                  <div v-if="item.pointNames" class="display_flex align-items_flex-start">
                    <strong class="flex_shrink_0">【考点】</strong>
                    <div class="flex_1">
                      {{ item.pointNames.split(',').join('，') }}
                    </div>
                  </div>
                  <!--解析-->
                  <div v-if="item.content.analysis" class="answer_box display_flex align-items_flex-start">
                    <strong class="flex_shrink_0">【解析】</strong>
                    <LatexHtml class="answer_content flex_1" :html="content.analysis" :subject="item.content.subject">
                    </LatexHtml>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="edit-block display_flex align-items_center clearfix"
            v-if="item.content && Array.isArray(item.content)">
            <span v-if="isStuErr">个人/</span><span>
              <span v-if="classId">
                班级/
              </span>

              年级得分率：<span v-if="isStuErr">{{ Number(item.scoreRate * 100).toFixed(2) }}% /</span>
              <span v-if="classId">
                {{ Number(item.clsScoreRate * 100).toFixed(2) }}% /
              </span>
              {{ Number(item.grdScoreRate * 100).toFixed(2) }}%</span>
            <span class="flex_1 text-ellipsis"
              style="margin-left: 40px; margin-right: 20px">来自：【{{ item.examName }}】</span>
          </div>
          <!-- 三级结构 -->
          <div v-if="item.content && !Array.isArray(item.content)">
            <div class="ques-content" v-if="item.content.data.levelcode != ''">
              <div class="question_content">
                <div class="question_body">
                  <!-- 小题序号 -->
                  <el-button :class="[
                    'ques-detail-small-button',
                    { active: smallIndex == item.smallQuesIndex },
                  ]" circle v-for="(small, smallIndex) in item.content.data.qs" :key="smallIndex"
                    @click.stop="viewBigSmallQues(index, smallIndex)">
                    {{ smallIndex + 1 }}
                  </el-button>
                  <div class="question_content">
                    <LatexHtml :subject="item.content.subject"
                      :html="quesList[index].content.data.qs[item.smallQuesIndex].q_html">
                    </LatexHtml>
                  </div>
                </div>
              </div>
            </div>

            <div class="edit-block display_flex align-items_center clearfix">
              <span v-if="isStuErr">个人/</span><span>
                <span v-if="classId">
                  班级/
                </span>

                年级得分率：<span v-if="isStuErr">{{ Number(item.scoreRate * 100).toFixed(2) }}% /</span>
                <span v-if="classId">
                  {{ Number(item.clsScoreRate * 100).toFixed(2) }}% /
                </span>


                {{ Number(item.grdScoreRate * 100).toFixed(2) }}%</span>
              <span class="flex_1 text-ellipsis"
                style="margin-left: 40px; margin-right: 20px">来自：【{{ item.examName }}】</span>
              <span class="ques-btn" :class="item.showDetail ? 'active' : ''" @click.stop="showQuesDetails(item)">题目详情<i
                  :class="item.showDetail ? 'el-icon-caret-top active' : 'el-icon-caret-bottom'"></i></span>
              <span class="ques-btn" @click.stop="showSimilarQues(item)">变式练习</span>
              <span class="add edit-btn text-center" v-if="!isStuErr && !isCef" @click.stop="selectQues(item)" :class="[
                selectIds.length && selectIds.indexOf(item.quesId) >= 0
                  ? 'hasAdd el-icon-minus'
                  : 'el-icon-plus',
              ]">
                {{ selectIds.length && selectIds.indexOf(item.quesId) >= 0 ? ' 移除' : ' 选入' }}
              </span>
            </div>
            <div class="ques-detail" v-if="item.showDetail && item.content">
              <!-- 三级结构 -->
              <div v-if="item.content.data.levelcode != ''">
                <!-- 小题序号 -->
                <el-button :class="['ques-detail-small-button', { active: smallIndex == item.activeIndex }]" circle
                  v-for="(small, smallIndex) in item.content.data.qs" :key="smallIndex"
                  @click="viewSmallQues(smallIndex, index)">
                  {{ smallIndex + 1 }}
                </el-button>
                <!--答案-->
                <div class="answer_box display_flex align-items_flex-start">
                  <strong class="flex_shrink_0">【答案】</strong>
                  <div class="flex_1">
                    <div class="answer_content" v-if="item.content.quesType === 2">
                      {{ item.content.answer.split(',')[0] === 'A' ? '正确' : '错误' }}
                    </div>
                    <LatexHtml class="answer_content" v-else
                      :html="quesList[index].content.data.qs[item.activeIndex].answer" :subject="item.content.subject">
                    </LatexHtml>
                  </div>
                </div>
                <!--考点-->
                <div v-if="item.pointNames" class="display_flex align-items_flex-start">
                  <strong class="flex_shrink_0">【考点】</strong>
                  <div class="flex_1">
                    {{ item.pointNames.split(',').join('，') }}
                  </div>
                </div>
                <!--解析-->
                <div v-if="quesList[index].content.data.qs[item.activeIndex].analysis"
                  class="answer_box display_flex align-items_flex-start">
                  <strong class="flex_shrink_0">【解析】</strong>
                  <LatexHtml class="answer_content flex_1"
                    :html="quesList[index].content.data.qs[item.activeIndex].analysis" :subject="item.content.subject">
                  </LatexHtml>
                </div>
              </div>
              <!-- 二级结构 -->
              <div v-else>
                <!--答案-->
                <div class="answer_box display_flex align-items_flex-start">
                  <strong class="flex_shrink_0">【答案】</strong>
                  <div class="flex_1">
                    <div class="answer_content" v-if="item.content.quesType === 2">
                      {{ item.content.answer.split(',')[0] === 'A' ? '正确' : '错误' }}
                    </div>
                    <LatexHtml class="answer_content" v-else :html="item.content.answer" :subject="item.content.subject">
                    </LatexHtml>
                  </div>
                </div>
                <!--考点-->
                <div v-if="item.pointNames" class="display_flex align-items_flex-start">
                  <strong class="flex_shrink_0">【考点】</strong>
                  <div class="flex_1">
                    {{ item.pointNames.split(',').join('，') }}
                  </div>
                </div>
                <!--解析-->
                <div v-if="item.content.analysis" class="answer_box display_flex align-items_flex-start">
                  <strong class="flex_shrink_0">【解析】</strong>
                  <LatexHtml class="answer_content flex_1" :html="item.content.analysis" :subject="item.content.subject">
                  </LatexHtml>
                </div>
              </div>

              <!--统计-->
              <div class="display_flex align-items_flex-start" v-if="checktype != 'stu'">
                <strong class="flex_shrink_0">【统计】</strong>
                <div class="flex_1">共有{{ item.rightNum }}人答对，{{ item.errNum }}人答错</div>
              </div>
            </div>
          </div>
        </div>
        <!--分页器-->
        <el-pagination background v-show="quesList.length" :hide-on-single-page="!quesList.length"
          style="margin: 15px auto" class="text-center" layout="total, prev, pager, next"
          @current-change="handleCurrentChange" :current-page.sync="quesPage.page" :page-size="quesPage.pageSize"
          :total="quesPage.total_rows">
        </el-pagination>
      </div>
      <div v-if="!quesList.length && !quesLoading" class="nodata">
        <img :src="noResImg" alt="" />
        <p class="text-center">暂无数据</p>
      </div>
    </div>

    <!--试卷袋-->
    <fixedPaper v-on="$listeners" ref="fixedPaper" class="fixed-box"
      v-if="(type == 'wrongques' || $route.path.indexOf('teaching/class') >= 0) && !isStuErr && !isCef" :quesIds="ids"
      @update="updateData"></fixedPaper>
    <!--试卷袋-->

    <!--设置自定义区间-->
    <setCustom v-if="custormVisible" :originData="originData" @cancelSet="cancelSet" @sureSet="sureSet"></setCustom>
    <!--设置自定义区间-->

    <!--变式题-->
    <el-dialog v-dragDialog title="变式练习" :visible.sync="isShowSimilarDialog" :modal-append-to-body="false"
    :append-to-body="true" width="720">
      <similar-ques-dialog v-on="$listeners" v-if="isShowSimilarDialog" :selectIds="selectIds" :originQues="originQues"
        @selectQues="selectQues" :isComposingPaper="true" />

      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowSimilarDialog = false">关闭</el-button>
      </span>
    </el-dialog>
    <!--变式题-->
  </div>
</template>

<script>
import { listErrQues, listStuErrQues } from '@/service/pexam';
import { chooseQuesSearch } from '@/service/pbook';
import fixedPaper from '@/components/fixedPaper.vue';
import setCustom from '@/components/setCustom';
import LatexHtml from '@/components/LatexHtml';
import { isCef } from '@/utils/index';
import similarQuesDialog from './similarQuesDialog';

export default {
  name: 'paper_mixins',
  components: {
    fixedPaper,
    setCustom,
    LatexHtml,
    similarQuesDialog
  },
  props: ['pointId', 'stuId', 'checktype', 'type', 'classId'],
  computed: {
    selectIds() {
      let totalList = [],
        ids = [];
      this.quesInfo.length &&
        this.quesInfo.forEach(item => {
          totalList = totalList.concat(item.data);
        });
      totalList.length &&
        totalList.forEach(item => {
          if (item.data.length) {
            ids = ids.concat(
              item.data.map(sub => {
                return sub.id;
              })
            );
          }
        });
      return ids;
    },
    isSelectAll() {
      let isSelect = true;
      if (!this.quesList.length) return;
      this.quesList.forEach(item => {
        if (this.selectIds.indexOf(item.quesId) < 0) {
          isSelect = false;
        }
      });
      return isSelect;
    },

    // 是否是学生错题本
    isStuErr() {
      return this.stuId;
    },
  },
  data() {
    return {
      // 题目列表loading
      quesLoading: false,
      // 当前选中的题目id
      detailId: '',
      // 渲染题目列表数组
      quesList: [],
      // 卷一客观题，卷二主观题
      quesInfo: [],
      // 得分率选项设置列表
      rateList: [
        { name: '全部', num: '0,100' },
        { name: '≤20%', num: '0,20' },
        { name: '≤40%', num: '0,40' },
        { name: '≤60%', num: '0,60' },
        { name: '≤85%', num: '0,85' },
        { name: '自定义', num: 'custom' },
      ],
      // 题型列表
      quesTypeList: [
        { label: '单选题', value: 8 },
        { label: '多选题', value: 1 },
        { label: '判断题', value: 2 },
        { label: '填空题', value: 3 },
        { label: '解答题', value: 6 },
      ],
      // 题型
      quesType: [1, 2, 3, 6, 8],

      // 得分率选中索引
      activeRate: 0,
      // 得分率绑定的数值
      finallyRateParam: [0, 100],
      // 初始化自定义弹窗的默认值
      originData: [0, 30],
      // 自定义弹窗是否显示
      custormVisible: false,
      // 没有数据时缺省图
      noResImg: require('@/assets/no-res.png'),
      // 题目列表分页配置
      quesPage: {
        page: 1,
        pageSize: 10,
        total_rows: 0,
      },

      // 合并题型Ids
      mergeIds: [],
      // 题目ID集合
      ids: [],
      // 是否cef环境
      isCef: isCef(),
      // 是否显示变式题
      isShowSimilarDialog: false,
      originQues: null,
    };
  },
  mounted() {
    let sessionRate = this.$sessionSave.get('customRate');
    if (!sessionRate) {
      this.$sessionSave.set('customRate', [0, 30]);
    }
  },
  methods: {
    emptyData() {
      this.quesList = [];
      this.quesPage.total_rows = 0;
    },
    // 得分率恢复默认值
    initScoreRate() {
      this.activeRate = 0;
      this.finallyRateParam = [0, 100];
      this.listErrQues();
    },
    // 获取班级共性错题列表
    listErrQues(type) {
      this.quesLoading = true;
      this.quesPage.page = type === 'changePage' ? this.quesPage.page : 1;

      if (!this.stuId) {
        this.getListErrQues();
      } else {
        this.getListStuErrQues();
      }

      this.$nextTick(() => {
        this.$refs.fixedPaper && this.$refs.fixedPaper.getPersonalTestBank();
      });
    },

    // 获取班级共性错题
    getListErrQues() {
      let curParams = {
        page: this.quesPage.page,
        pageSize: this.quesPage.pageSize,
        clsMinRate: this.finallyRateParam[0] / 100,
        clsMaxRate: this.finallyRateParam[1] / 100,
        pointId: this.pointId || '',
        stuId: this.stuId || '',
        quesType: this.quesType.join(','),
      };

      listErrQues({
        ...curParams,
        ...(this.$listeners.getParams() || this.$emit('getParams')),
      })
        .then(data => {
          this.quesList = data.data.rows || [];
          this.quesPage.total_rows = data.data.total_rows || 0;
          let ids = this.quesList.map(item => {
            return item.quesId;
          });
          this.ids = ids = Array.from(new Set(ids));
          if (!ids.length) {
            this.quesLoading = false;
          } else {
            // 获取题面
            this.chooseQuesSearch(ids.join(','));
          }
        })
        .catch(reason => {
          this.quesLoading = false;
        });
    },

    // 获取学生错题本
    async getListStuErrQues() {
      const curParams = {
        page: this.quesPage.page,
        pageSize: this.quesPage.pageSize,
        min: this.finallyRateParam[0] / 100,
        max: this.finallyRateParam[1] / 100,
        stuId: this.stuId || '',
      };

      const params = this.$listeners.getParams() || this.$emit('getParams');

      try {
        const data = await listStuErrQues({
          ...curParams,
          schoolId: params.schoolId,
          gradeId: params.gradeId,
          subjectId: params.subjectId,
          startTime: params.startTime,
          endTime: params.endTime,
        });
        this.quesList = data.data.rows || [];
        this.quesPage.total_rows = data.data.total_rows || 0;
        let ids = this.quesList.map(item => {
          return item.quesId;
        });
        this.ids = ids = Array.from(new Set(ids));
        if (!ids.length) {
          this.quesLoading = false;
        } else {
          // 获取题面
          this.chooseQuesSearch(ids.join(','));
        }
      } catch (error) {
        this.quesLoading = false;
      }
    },

    // 获取题目的题面
    async chooseQuesSearch(ids) {
      this.listLoading = true;
      try {
        const data = await chooseQuesSearch({
          qIds: ids,
        });

        let res = data.data;
        if (!this.quesList.length) {
          this.quesLoading = false;
          return;
        }

        let qsMap = new Map();
        // 获取小问
        res.forEach(item => {
          const qs = item.data.qs;
          qs.forEach(q => {
            qsMap.set(q.qId, q);
          });
        });
        this.quesList.forEach((item, index) => {
          this.$set(item, 'totalIndex', index + (this.quesPage.page - 1) * this.quesPage.pageSize);
          this.$set(item, 'showDetail', false);
          this.$set(item, 'activeIndex', 0);
          this.$set(item, 'smallQuesIndex', 0);
          res.forEach(subItem => {
            let ids = item.quesId.split(',');
            // 处理合并题组
            if (ids.length > 1) {
              if (ids.includes(subItem.qId)) {
                if (item.content) {
                  let arr = item.content;
                  arr.push(subItem);
                  this.$set(item, 'content', arr);
                } else {
                  this.$set(item, 'content', [subItem]);
                  this.$set(item, 'quesTypeName', this.$getQuesType(subItem.data.type));
                }
              }
            } else if (item.quesId === subItem.qId) {
              this.$set(item, 'content', subItem);
              // this.$set(item, 'quesTypeName', this.$getQuesType(subItem.quesType));
              this.$set(item, 'quesTypeName', this.$getQuesType(subItem.data.type));
            }
          });
        });

        this.quesList.forEach((item, i) => {
          if (item.content) return;
          const qs = qsMap.get(item.quesId);
          if (!qs) return;
          item.content = qsMap.get(item.quesId);
          item.content.data = qs;
          item.content.data.levelcode = '';
          this.$set(item, 'showDetail', false);
          this.$set(item, 'activeIndex', 0);
          this.$set(item, 'smallQuesIndex', 0);
          this.$set(item, 'quesTypeName', this.$getQuesType(qs.type));
        });
        this.$nextTick(() => {
          this.$katexUpdate();
          this.quesLoading = false;
        });
      } catch (error) {
        this.quesLoading = false;
      }
    },
    /**
     * @name:查看大题题面上的小题
     */
    viewBigSmallQues(bigIndex, index) {
      this.quesList[bigIndex].smallQuesIndex = index;
    },
    /**
     * @name:查看小题
     * @param index 小题序号
     * @param bigIndex 大题序号
     */
    viewSmallQues(index, bigIndex) {
      this.quesList[bigIndex].activeIndex = index;
    },
    // 点击题目亮显
    selectQuesItem(item) {
      this.detailId = item.quesId === this.detailId ? '' : item.quesId;
      this.$nextTick(() => {
        if (this.detailId) {
          this.$katexUpdate();
        }
      });
    },
    // 展开收起题目详情
    showQuesDetails(item) {
      this.$set(item, 'showDetail', !item.showDetail);
      this.$nextTick(() => {
        this.$katexUpdate();
      });
    },
    showSimilarQues(item) {
      this.originQues = item;
      this.isShowSimilarDialog = true;
    },
    // 全部选入
    selectAll() {
      this.$refs.fixedPaper && this.$refs.fixedPaper.selectAll(this.quesList, this.isSelectAll);
    },
    // 题目加入、移除讲评
    selectQues(item, content) {
      this.$refs.fixedPaper && this.$refs.fixedPaper.selectQues(item, false, content);
    },
    // 更新加入试卷的数据
    updateData(quesInfo) {
      this.quesInfo = quesInfo || this.quesInfo;
    },
    cancelSet() {
      this.custormVisible = false;
    },
    // 选择得分率范围
    changeRate(index, item) {
      // 如果选择了自定义
      if (item.num === 'custom') {
        let sessionRate = this.$sessionSave.get('customRate');
        this.originData = sessionRate || this.finallyRateParam;
        this.custormVisible = true;
      } else {
        this.activeRate = index;
        this.finallyRateParam = this.$deepClone(item.num.split(','));
        this.listErrQues();
      }
    },
    // 设置自定义得分率范围
    sureSet(rateParam) {
      let numList = this.rateList.map(item => {
        return item.num;
      });
      let hasList = numList.indexOf(rateParam.join(','));
      // 设置的范围已经存在
      if (hasList >= 0) {
        this.activeRate = hasList;
      } else {
        let name =
          rateParam[0] == rateParam[1]
            ? `=${rateParam[1]}%`
            : `${rateParam[0]}% - ${rateParam[1]}%`;
        if (this.rateList.length >= 7) {
          this.rateList[5] = {
            name: name,
            num: rateParam.join(','),
          };
        } else {
          this.rateList.splice(this.rateList.length - 1, 0, {
            name: name,
            num: rateParam.join(','),
          });
        }

        this.activeRate = this.rateList.length - 2;
      }
      this.finallyRateParam = rateParam;
      this.$sessionSave.set('customRate', rateParam);
      this.listErrQues();
      this.custormVisible = false;
    },
    // 表格中切换分页
    handleCurrentChange(val) {
      this.quesPage.page = val;
      this.listErrQues('changePage');
    },
  },
};
</script>

<style lang="scss" scoped>
.mistack-filter {
  .rate-btn:first-of-type {
    margin-left: 10px;
  }

  .rate-btn:last-of-type {
    margin-right: 10px;
  }

  .active {
    border: 1px solid #409eff;
    color: #409eff;
  }

  .el-icon-plus {
    color: rgb(64, 158, 255);
    cursor: pointer;
  }
}

.comment-ques {
  .ques-list {
    position: relative;
    border: 1px solid #e4e8eb;
    border-radius: 3px;
    margin-bottom: 20px;

    .title {
      padding-left: 50px;
      font-size: 16px;
      font-weight: bold;
      color: #3f4a54;
      line-height: 38px;

      .ques-type {
        padding: 4px 8px;
        background-color: #c6c8c4;
        color: #fff;
        margin-right: 20px;
        border-radius: 4px;
        font-size: 15px;
      }
    }

    &:before {
      content: attr(data-index);
      position: absolute;
      left: -1px;
      top: -1px;
      width: 32px;
      height: 38px;
      background: url('../../assets/flag_gray.png') center center no-repeat;
      font-size: 16px;
      color: #3f4a54ff;
      text-align: center;
      line-height: 30px;
    }

    .ques-content {
      min-height: 100px;
      padding: 15px 0 10px;
      padding-bottom: 0;
      line-height: 2em;
    }

    .question_content {
      padding-bottom: 15px;
    }

    .edit-block {
      width: 100%;
      height: 48px;
      line-height: 48px;
      padding: 0 21px 0 11px;
      background: #f5f8fa;
      border-radius: 3px;
      border-top: 1px solid #e4e8eb;
      font-size: 16px;
      color: #4e5668ff;

      .edit-btn {
        width: 80px;
        height: 32px;
        border-radius: 4px;
        padding: 0;
        line-height: 32px;
        color: #fff;
        margin-top: 0px !important;
        margin-left: 20px;
        cursor: pointer;

        &.add {
          background: #409effff;
        }

        &.hasAdd {
          background-color: #fff;
          border: 1px solid #468fffff;
          color: #468fffff;
        }
      }

      .ques-btn {
        position: relative;
        padding: 0 20px;
        cursor: pointer;

        i {
          font-size: 22px;
          margin-left: 2px;
          position: absolute;
          top: 9px;
        }

        &.active {
          color: #409effff;
          font-size: 16px;

          i {
            top: 13px;
          }
        }

        .el-icon-caret-top {
          color: #409effff;
        }
      }

      .el-icon-caret-bottom {
        color: #b6b8bfff;
        margin-top: 4px;
      }
    }

    .ques-detail {
      padding: 20px;

      >div {
        margin-bottom: 10px;
      }

      .resourceList {
        margin-top: 10px;
        margin-bottom: 0;
      }

      .answer-imgList {
        display: inline-block;
        width: 85px;
        height: 50px;
        margin-right: 10px;
        cursor: pointer;
        margin-bottom: 10px;
        border: 1px solid #cecece;
      }
    }

    &.active,
    &:hover {
      border: 1px solid #409eff;

      &:before {
        background: url('../../assets/flag_active.png') center center no-repeat;
        color: #fff;
      }
    }
  }
}

.ques-detail-small-button {
  width: 40px;
  height: 40px;

  &.active {
    background-color: #409eff;
    color: #fff;
  }
}
</style>
<style lang="scss"></style>
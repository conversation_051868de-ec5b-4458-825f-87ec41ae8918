<template>
  <el-dialog
    title="提示"
    :visible.sync="dialogVisible"
    width="50%"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <div class="publish-score">
      <span style="font-weight: bold">{{ examInfo.examName }}</span>
      <ul>
        <li>学科：{{ examInfo.paperInfo.subectName }}</li>
        <li>计划人数：{{ scanInfo.totalStuNum }}</li>
        <li>实际扫描：
          <template v-if="scanInfo1">
            {{ scanInfo.realScanNum + scanInfo1?.realScanNum }}
          </template>
          <template v-else>
            {{ scanInfo.realScanNum }}
          </template>
          <span v-if="scanInfo.aiCorrect > 0 && scanInfo.aiCorrectProgress > 0" style="color: #409eff;margin-left: 50px;">
            <span>AI批改进度（{{ scanInfo.aiCorrectProgress || '0' }}%）</span>
          </span>
        </li>
        <li style="color: red;">未扫描：{{ scanInfo.noScanNum }}</li>
        <li style="color: red;">未处理异常：{{ scanInfo.errorNum }}</li>
      </ul>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="sure">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getMultipleScanPaperWorkStat } from "@/service/api";
export default {
  props: {
    //考试信息
    examInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      dialogVisible: true,
      form: {},
      scanInfo: {},
      scanInfo1: {}
    };
  },
  mounted() {
    this.getPaperStat();
  },
  methods: {
    getPaperStat() {
      getMultipleScanPaperWorkStat({
        schoolId: this.examInfo.schoolId,
        workId: this.examInfo.paperInfo.personBookId,
      })
        .then((res) => {
          this.scanInfo = res.data[0];
          this.scanInfo1 = res.data[1];
        })
        .catch((err) => {});
    },
    handleClose() {
      this.dialogVisible = false;
      this.$emit("close-publish");
    },
    sure() {
      this.dialogVisible = false;
      this.$emit("publish-score");
    },
  },
};
</script>

<style lang="scss" scoped>
.publish-score {
  padding: 0 20px;
  font-size: 16px;
  ul {
    list-style-type: none;
    li {
      padding-top: 10px;
      cursor: pointer;
    }
  }
}
</style>
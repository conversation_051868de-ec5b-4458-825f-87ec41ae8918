<template>
  <div class="add-ques-wrapper" :class="{'block': currentStep==2}">
    <div class="add-ques-left">
      <div class="add-ques-kinds-box">
        <el-button-group>
          <el-button :type="currentStep==0?'primary':''" @click="changeMode(0)">C30个册题库</el-button>
          <el-button :type="currentStep==1?'primary':''" @click="changeMode(1)">网络题库</el-button>
          <el-button :type="currentStep==2?'primary':''" @click="changeMode(2)">添加题目</el-button>
        </el-button-group>
      </div>
      <div v-show="currentStep==0">
        <ques-point class="ques-point-list" ref="quesPoint"></ques-point>
      </div>
      <div v-show="currentStep==1">
        <!--<ques-point class="ques-point-list" ref="quesPointNet"></ques-point>-->
        <ques-net-point class="ques-point-list" ref="quesPointNet"></ques-net-point>
      </div>
      <div v-if="currentStep==2" class="edit-remark-box">
        <ques-edit ref="quesEdit" :workbook-info="queryInfo" :relationId="currentQuesInfo.id"></ques-edit>
        <ques-remark ref="quesRemark" :info="queryInfo"></ques-remark>
        <div class="save-ques-btn">
          <el-button @click="resetQuesInfo">重置</el-button>
          <el-button type="primary" @click="submitQuesInfo" :loading="isSavingQues">新建选入</el-button>
        </div>

        <el-backtop target=".edit-remark-box" :visibility-height="100" :right="200" :bottom="100">
          <div class="icon-toup" title="置顶"></div>
        </el-backtop>
      </div>
    </div>
    <div class="add-ques-right" v-show="currentStep!=2">
      <div id="sticky_container" class="right-wrapper">
        <div v-show="currentStep===0" class="add-ques-content sticky_c30">
          <ques-c30-filter data-stuck="stuck_c30" :stuck="stuckInfo.stuck_c30" class="sticky"
                           ref="c30Filter"></ques-c30-filter>
          <select-ques-card ref="quesCardC30" :currentStep="0" :needQuesNum="needQuesNum"></select-ques-card>
        </div>
        <div v-show="currentStep===1" class="add-ques-content add-ques-content-2 sticky_net">
          <ques-net-filter data-stuck="stuck_net" :stuck="stuckInfo.stuck_net" class="sticky" ref="netFilter"></ques-net-filter>

          <select-ques-card ref="quesCardNet"
                            :currentStep="1"
                            :needQuesNum="needQuesNum"
                            v-show="currentQuesBank=='xf'"></select-ques-card>
          <select-yiqi-ques-card ref="yiQuesCardNet"
                                 :currentStep="1"
                                 :tokenInfo="tokenInfo"
                                 :needQuesNum="needQuesNum"
                                 v-show="currentQuesBank=='yiqi'"></select-yiqi-ques-card>
          <select-jyeoo-ques-card ref="jyeooQuesCardNet"
                                  :currentStep="1"
                                  :tokenInfo="tokenInfo"
                                  :needQuesNum="needQuesNum"
                                  v-show="currentQuesBank=='jyeoo'"></select-jyeoo-ques-card>

        </div>
      </div>
    </div>

  </div>
</template>

<script>
import QuesRemark from '@/components/quesRemark/quesRemark.vue'
import QuesEdit from '@/components/QuesEdit/QuesEdit.vue'
import QuesPoint from '../QuesPoint/quesPoint.vue'
import QuesC30Filter from '../QuesC30Filter/QuesC30Filter.vue'
import QuesNetFilter from '../QuesNetFilter/QuesNetFilter.vue'
import SelectQuesCard from '../SelectQuesCard/SelectQuesCard.vue'

import { saveSimilarQues } from '@/api/ptask.js'
import { mapGetters, mapState } from 'vuex'
import QuesNetPoint from '@/components/QuesPoint/quesNetPoint'
import SelectYiqiQuesCard from '@/components/SelectQuesCard/SelectYiqiQuesCard'
import SelectJyeooQuesCard from '@/components/SelectQuesCard/SelectJyeooQuesCard'
import { getC30Token } from '@/api/ques'

export default {
  name: 'AddQues',
  components: {
    SelectJyeooQuesCard,
    SelectYiqiQuesCard,
    QuesNetPoint,
    QuesEdit,
    QuesRemark,
    QuesPoint,
    QuesC30Filter,
    QuesNetFilter,
    SelectQuesCard
  },
  props: ['currentQuesInfo', 'needQuesNum'],
  data() {
    return {
      // 当前处于哪种推题模式(0-C30个册题库，1-网络题库，2-添加题目)
      currentStep: 0,
      queryInfo: this.$route.query,
      // 是否是正在保存
      isSavingQues: false,
      // 题目来源
      quesOrigin: 2, // 2-自定义题目 1-网络题目
      // 上上步(0-C30个册题库，1-网络题库，2-添加题目)
      lastLastStep: 0,
      stuckInfo: {
        stuck_c30: false,
        stuck_net: false
      },
      // 当前题库类型—— 一起、xf
      currentQuesBank: 'jyeoo',
      tokenInfo: null,
    }
  },
  computed: {
    ...mapGetters([
      'selectedQuesIds',
      'selectedQuesList'
    ])
  },
  beforeDestroy() {
    this.$bus.$off('changeMode')
    this.$bus.$off('changeQuesBank')
  },
  created() {
    this.queryInfo = {
      ...this.$route.query,
      subjectId: this.$route.query.subject_id,
      phaseId: this.$route.query.phase_id
    }
  },
  mounted() {
    this.$bus.$on('changeMode', (modeType, value) => {
      if (modeType == 2) {
        this.quesOrigin = 1
        this.changeMode(2)
        this.$nextTick(() => {
          // this.$refs.quesEdit.resetBaseQuesInfo()
          this.$refs.quesEdit.receiveQuesInfo(value)
          this.$refs.quesRemark.receiveQuesInfo(value)
          // this.$refs.quesRemark.restoreQuesInfo();
        })
      }
    })
    this.onQuesBankChange()
    this.$refs.quesPoint.getC30Point()
    this.$refs.quesPointNet.getXfPointTree()

    observeStickyHeaderChanges(document.querySelector('#sticky_container'))
    document.addEventListener('sticky-change', e => {
      let node = e.detail.target
      if(this.stuckInfo.stuck_net) {
        this.$set(this.stuckInfo, 'stuck_net', true)
      }
      this.$set(this.stuckInfo, node.dataset['stuck'], e.detail.stuck)
    })

    // 请求一起作业 筛选条件和知识点
    this.initYiqRequest()
  },
  watch: {
    currentQuesInfo: {
      handler(newVal, oldVal) {
        if(newVal.quesId != oldVal.quesId) {
          this.currentStep = 0;
          this.$nextTick(()=>{
            this.clearAllCondition();
            this.$refs.quesCardNet.quesSearch()
            this.$refs.yiQuesCardNet.getQuesByCondition(this.tokenInfo)
            this.$refs.jyeooQuesCardNet.getQuesByCondition(this.tokenInfo)
          })
        }
      },
      deep: true
    }
  },
  methods: {
    async initYiqRequest() {
      const subInfo = this.$localSave.get('currentSubject');
      this.tokenInfo = await getC30Token({
        subjectId: subInfo.id || ''
      })

      this.$refs.quesPointNet.getAllPointTree(this.tokenInfo)
      this.$refs.netFilter.getFilterCondition(this.tokenInfo, subInfo)
      await this.$refs.yiQuesCardNet.getQuesByCondition(this.tokenInfo)

    },
    // 监听题库类型变化
    onQuesBankChange() {
      // 接收 quesNetPoint 组件的广播事件
      this.$bus.$on('changeQuesBank', (type) => {
        if (type === 'yiqi') {
          this.currentQuesBank = 'yiqi';
        } else if(type === 'xf') {
          this.currentQuesBank = 'xf';
        } else if(type === 'jyeoo') {
          this.currentQuesBank = 'jyeoo';
          this.$nextTick(() => {
            // this.$refs.jyeooQuesCardNet.getQuesByCondition(this.tokenInfo)
          })
        }
      })
    },
    // 切换原题时清空所有条件（包括知识点、筛选项）
    clearAllCondition() {
      this.$refs.quesPoint.clearCheckedKeys()
      this.$refs.quesPointNet.clearCheckedKeys()
      this.$refs.quesPoint.knowledgeCode = ''
      this.$refs.quesPointNet.knowledgeCode = ''
      this.$refs.c30Filter.resetC30Filter()
      this.$refs.netFilter.resetNetFilter()
      this.$refs.quesCardC30.filterNetInfo = {}
      this.$refs.quesCardC30.filterC30Info = {}
      this.$refs.quesCardNet.filterNetInfo = {}
      this.$refs.quesCardNet.filterC30Info = {}
      this.$refs.yiQuesCardNet.filterNetInfo = {}

      this.$refs.quesCardC30.pointInfo = {}
      this.$refs.quesCardNet.pointInfo = {}
      this.$refs.yiQuesCardNet.pointInfo = {}
      this.$refs.quesCardC30.currentPage = 1
      this.$refs.quesCardNet.currentPage = 1
      this.$refs.yiQuesCardNet.currentPage = 1
      this.$refs.jyeooQuesCardNet.filterNetInfo = {}
      this.$refs.jyeooQuesCardNet.pointInfo = {}
      this.$refs.jyeooQuesCardNet.currentPage = 1
    },
    // 改变推题模式(参数value: 0-C30个册题库，1-网络题库，2-添加题目)
    changeMode(value) {
      if (this.currentStep === value) {
        return
      }
      this.currentStep = value
    },
    // 改变条件获取题目列表
    changeCondition(type, value) {
      console.log(type, value)
    },
    // 重置编辑和标注
    resetQuesInfo() {
      this.$confirm('确认重置?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$refs.quesEdit.resetBaseQuesInfo()
        this.$refs.quesRemark.restoreQuesInfo()
      }).catch(() => {
      })
    },
    // 选入题目
    saveSimilarQues(item) {
      this.isSavingQues = true

      let sourceId = ''
      if (this.quesOrigin == 2) {
        sourceId = -1
      } else {
        sourceId = item.sourceId
      }
      console.log('sourceId-->', sourceId)
      saveSimilarQues({
        sourceId: sourceId, // 网络题库quesCode，网络题库弹窗则为baseId，个册题库为baseId
        personBookId: this.currentQuesInfo.personBookId,
        baseId: this.currentQuesInfo.baseQuestionId,
        similarQuesId: item.baseQuesId, // item.id为个册题库的 baseQuestionId
        difficulty: item.difficulty
      }).then(data => {
        this.quesOrigin = 2 // 还原题目来源信息
        this.$refs.quesEdit.resetBaseQuesInfo()
        this.$refs.quesRemark.restoreQuesInfo()
        this.$message({
          message: '选入成功！',
          type: 'success'
        })
        // 该触发事件位于父组件 processRecource.vue
        this.$bus.$emit('freshChooseSimilarQues')
        this.isSavingQues = false
      }).catch(err => {

      })
    },
    // 判断题目选择是否满足条件（已选的题目列表至少有两种难度以上）
    canSelectQues(quesInfo) {
      let limit = this.$route.query.need_audit + '' == 'true' ? 10 : 2
      let difficultyList = []
      this.selectedQuesList.map(item => {
        if (item.difficulty == 5 || item.difficulty == 4) {
          item.difficulty = 4 // 将容易和较易归为较易
        }
        difficultyList.push(item.difficulty)
      })
      let difficulty = quesInfo.difficulty
      if (difficulty == 5 || difficulty == 4) {
        difficulty = 4 // 将容易和较易归为较易
      }
      difficultyList.push(difficulty)
      let len = [...new Set(difficultyList)].length
      if (len < 2 && this.selectedQuesList.length == limit - 1) {
        this.$message({
          message: `请根据教学进度，选择${limit}道难度平均分布的题目！`,
          type: 'warning'
        })
        return false
      }
      if (this.selectedQuesList.length > limit - 1) {
        this.$message({
          message: `选入题目已达${limit}道！`,
          type: 'warning'
        })
        return false
      }
      return true
    },
    // 保存
    async submitQuesInfo() {
      let validateQuesRemarkRes = false
      let validateQuesBaseRes = this.$refs.quesEdit.validateQuesBaseDetail()
      if (validateQuesBaseRes) {
        validateQuesRemarkRes = this.$refs.quesRemark.validateQuesRemark()
      }
      if (validateQuesBaseRes && validateQuesRemarkRes) {
        // TODO:检测该题的难度是否符合教学进度
        let quesInfoDifficulty = this.$refs.quesRemark.dictionaryList[0].activeIndex
        if (!this.canSelectQues({ difficulty: quesInfoDifficulty })) {
          return
        }
        // obj.id为个册题库的 baseQuestionId
        let obj = {}
        let { baseQuesId, sourceId } = await this.$refs.quesEdit.postQuesData('', this.quesOrigin)
        obj.baseQuesId = baseQuesId
        obj.sourceId = sourceId
        let { difficulty } = await this.$refs.quesRemark.postQuesRemark(baseQuesId)
        obj.difficulty = difficulty

        // 不满足添加条件返回
        // if(!this.canSelectQues(obj)) {
        //   this.$refs.quesEdit.resetBaseQuesInfo();
        //   this.$refs.quesRemark.restoreQuesInfo();
        //   return;
        // }
        this.saveSimilarQues(obj)
      }
    }

  }

}
</script>

<style lang="scss" scoped>
.ques-point-list {
  height: calc(100vh - 203px);
}

.add-ques-wrapper {
  display: flex;

  &.block {
    display: block;
  }

  .edit-remark-box {
    overflow-y: auto;
    height: calc(100vh - 196px);

    .save-ques-btn {
      text-align: center;
      margin-bottom: 35px;

      & > button {
        width: 136px;
        font-size: 18px;
      }
    }
  }

  .add-ques-left {
    // overflow-y: auto;
    // height: calc(100vh - 134px);
    .add-ques-kinds-box {
      // text-align: center;
      margin: 12px 0;
      margin-left: 22px;

      .el-button {
        padding: 12px 10px;
      }
    }
  }

  .add-ques-right {
    flex: 1;

    .right-wrapper {
      height: calc(100vh - 132px);
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .add-ques-content {
        flex: 1;
        overflow-y: auto;
        // padding-right: 150px;
        position: relative;
      }
    }
  }
}

.icon-toup {
  width: 32px;
  height: 32px;
  background: url(../../assets/icon/toup.png) no-repeat;
}
</style>

<style lang="scss">
.edit-remark-box::-webkit-scrollbar {
  width: 5px;
  height: 6px;
}

.edit-remark-box::-webkit-scrollbar-track {
  background: rgb(239, 239, 239);
  border-radius: 2px;
}

.edit-remark-box::-webkit-scrollbar-thumb {
  background: #cecece;
  border-radius: 10px;
}

.edit-remark-box::-webkit-scrollbar-thumb:hover {
  background: rgb(133, 133, 133);
}

.edit-remark-box::-webkit-scrollbar-corner {
  background: #179a16;
}
</style>

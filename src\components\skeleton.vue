<!--
 * @Description: 
 * @Author: qmzhang
 * @Date: 2024-07-01 15:17:51
 * @LastEditTime: 2024-07-01 15:17:58
 * @FilePath: \personal-bigdata\src\components\skeleton.vue
-->
<!--
 * @Descripttion: 
 * @Author: 老李
 * @Date: 2020-12-15 09:27:08
 * @LastEditors: Please set LastEditors
-->
<template>
  <div class="skeleton" :style="{'width':width,'height':height,'border-radius':radius}">
  </div>
</template>
<script>
export default {
  name: 'skeleton',
  props: {
    width: {
      type: String,
      require: true,
    },
    height: {
      type: String,
      require: true,
    },
    radius: {
      type: String,
      require: false,
      default: '0px',
    },
  },
  data() {
    return {};
  },
};
</script>
<style lang="scss" scoped>
@-webkit-keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }

  to {
    background-position: 0 50%;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }

  to {
    background-position: 0 50%;
  }
}
.skeleton {
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    color-stop(25%, hsla(0, 0%, 74.5%, 0.2)),
    color-stop(37%, hsla(0, 0%, 50.6%, 0.24)),
    color-stop(63%, hsla(0, 0%, 74.5%, 0.2))
  );
  background: linear-gradient(
    90deg,
    hsla(0, 0%, 74.5%, 0.2) 25%,
    hsla(0, 0%, 50.6%, 0.24) 37%,
    hsla(0, 0%, 74.5%, 0.2) 63%
  );
  background-size: 400% 100%;
  -webkit-animation: skeleton-loading 1.4s ease infinite;
  animation: skeleton-loading 1.4s ease infinite;
}
</style>

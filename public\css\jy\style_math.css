@font-face { font-family: 'JyeMath'; src: url('https://img.jyeoo.net/fonts/jyeMath.eot?v=20190401'); src: url('https://img.jyeoo.net/fonts/jyeMath.eot?#iefix?v=20190401') format('embedded-opentype'), url('https://img.jyeoo.net/fonts/jyeMath.woff?v=20190401') format('woff'), url('https://img.jyeoo.net/fonts/jyeMath.ttf?v=20190401') format('truetype'), url('https://img.jyeoo.net/jyeMath.svg#JyeMath?v=20190401') format('svg'); font-weight: normal; font-style: normal; }
@font-face { font-family: 'JyeMathLetters'; src: url('https://img.jyeoo.net/fonts/letters/jyeMathLetters.eot?v=20190401'); src: url('https://img.jyeoo.net/fonts/letters/jyeMathLetters.eot?#iefix?v=20190401') format('embedded-opentype'), url('https://img.jyeoo.net/fonts/letters/jyeMathLetters.woff?v=20190401') format('woff'), url('https://img.jyeoo.net/fonts/letters/jyeMathLetters.ttf?v=20190401') format('truetype'), url('https://img.jyeoo.net/fonts/letters/jyeMathLetters.svg#JyeMathLetters?v=20190401') format('svg'); font-weight: normal; font-style: normal; }

.QUES_LI, .rpt_b, fieldset.quesborder { font-family: 'JyeMath','Times New Roman',"微软雅黑", Arial,"宋体"; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; }

table.edittable, table.edittable2, table.edittable3 { border-collapse: collapse; margin: 2px; }
table.edittable th, table.edittable td, table.edittable2 th, table.edittable2 td, table.edittable3 th, table.edittable3 td { line-height: 30px; padding: 5px; white-space: normal; word-break: break-all; border: 1px solid #000; vertical-align: middle; }
table.edittable { text-align: center; }
table.edittable2 { text-align: left; }
table.edittable3 { text-align: left; }
table.edittable3 tr:first-child td { text-align: center; }
table.composition { border-collapse: collapse; text-align: left; margin: 2px; width: 98%; }
table.composition th, table.composition td { line-height: 30px; white-space: normal; word-break: break-all; border-width: 0px; vertical-align: middle; }
table.composition2 { border-collapse: collapse; width: auto; }
table.composition2 th, table.composition2 td { text-align: left; line-height: 30px; white-space: normal; word-break: break-all; border: none; border-width: 0px; vertical-align: middle; }

.MathJye { border: 0 none; direction: ltr; line-height: normal; display: inline-block; float: none; font-family: 'JyeMath','JyeMathLetters','Times New Roman','宋体'; font-size: 15px; font-style: normal; font-weight: normal; letter-spacing: 5px; line-height: normal; margin: 0; padding: 0; text-align: left; text-indent: 0; text-transform: none; white-space: nowrap; word-spacing: normal; word-wrap: normal; -webkit-text-size-adjust: none; }
.MathJye div, .MathJye span { border: 0 none; margin: 0; padding: 0; line-height: normal; text-align: left; height: auto; _height: auto; white-space: normal; }
.MathJye table { border-collapse: collapse; margin: 0; padding: 0; text-align: center; vertical-align: middle; line-height: normal; font-size: inherit; *font-size: 100%; _font-size: 100%; font-style: normal; font-weight: normal; border: 0; float: none; display: inline-block; *display: inline; zoom: 0; }
.MathJye table td { padding: 0; font-size: inherit; line-height: normal; white-space: nowrap; border: 0 none; width: auto; _height: auto; }
/* 编辑题目样式 */
#tinymce table[cellpadding] { border-collapse: collapse; margin: 0; padding: 0; text-align: center; vertical-align: middle; line-height: normal; font-size: inherit; *font-size: 100%; _font-size: 100%; font-style: normal; font-weight: normal; border: 0; float: none; display: inline-block; *display: inline; zoom: 0; }
#tinymce table[cellpadding] td { padding: 0; font-size: inherit; line-height: normal; white-space: nowrap; border: 0 none; width: auto; _height: auto; }

.MathJye_mi { font-style: italic; }
.flipv { -ms-transform: scaleX(-1); -moz-transform: scaleX(-1); -webkit-transform: scaleX(-1); -o-transform: scaleX(-1); transform: scaleX(-1); filter: FlipH; }
.fliph { -ms-transform: scaleY(-1); -moz-transform: scaleY(-1); -webkit-transform: scaleY(-1); -o-transform: scaleY(-1); transform: scaleY(-1); filter: FlipV; }
.mathjye-bold { font-weight: 800; }
.mathjye-del { text-decoration: line-through; }
.mathjye-underline { border-bottom: 1px solid #000; padding-bottom: 1px; }
@-moz-document url-prefix() {
    .mathjye-underline { padding-bottom: 0px; }
}
.mathjye-underpline { border-bottom: 2px dotted #000; padding-bottom: 3px; }
@-moz-document url-prefix() {
    .mathjye-underpline { padding-bottom: 1px; }
}
.mathjye-underpoint { background: url(https://img.jyeoo.net/images/formula/point.png) no-repeat center bottom; padding-bottom: 4px; }
.mathjye-underpoint2 { border-bottom: 2px dotted #000; padding-bottom: 3px; }
@-moz-document url-prefix() {
    .mathjye-underpoint { padding-bottom: 1px; }
}
.mathjye-underwave { background: url(https://img.jyeoo.net/images/formula/wave.png) bottom repeat-x; padding-bottom: 4px; }
@-moz-document url-prefix() {
    .mathjye-underwave { padding-bottom: 1px; }
}
.mathjye-alignleft { display: block; text-align: left; }
.mathjye-aligncenter { display: block; text-align: center; }
.mathjye-alignright { display: block; text-align: right; }

.QUES_LI > h2 { font-size: 30px; margin-top: 20px; }
/*后加*/
fieldset.quesborder { font-size: 14px; }
/*body { -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; /*firefox*/ /*-webkit-text-stroke: 0.1px; }*/
/*抗锯齿渲染*/
sub, sup { font-family: 'JyeMath','JyeMathLetters','Times New Roman','宋体'; }
.MathJye .int { font-size: 20px; margin-right: -2px; font-style: normal; display: block; line-height: 2; transform: scale(1,1.3); -ms-transform: scale(1,1.3); -webkit-transform: scale(1,1.3); -moz-transform: scale(1,1.3); -o-transform: scale(1,1.3); }
.MathJye_mi { font-style: normal; }
.MathJye .int-sup { margin-bottom: 10px; }
/*积分号嵌入span加类名*/
/*body { text-shadow: 1px 1px 1px rgba(0,0,0,0.004); text-rendering: optimizeLegibility !important; -webkit-font-smoothing: antialiased !important; }*/
/*回归方程*/
.MathJye .hat { height: 3px; text-align: center; }
/*圆弧帽子只有两个字符*/
.MathJye .arc { height: 10px; text-align: center; display: inline-block; position: absolute; top: -10px; left: 50%; margin-left: -9px; width:18px; }

/*向量箭头*/
.MathJye .arrow { height: 8px; width: 100%; overflow: hidden; text-align: right; position: absolute; top: 3px; }
.MathJye .arrow > span { position: absolute; font-size: 14px; text-align: right; height: 8px; /*width: 18px;*/ line-height: 8px; right: 0; margin-right: -1px; list-style: none; top: -1px; }
.MathJye .arrow > span > i { position: absolute; width: 13px; height: 3px; background-color: #fff; top: 3px; z-index: 99; left: 0; border-top: 1px solid #000; left: -1px; }
.MathJye .arrow > i { height: 1.2px; width: 45px; position: absolute; right: 2px; top: 3px; font-size: 14px; line-height: 8px; font-style: normal; background-color: #000; }
.selectoption label.s span > i, div.s span > i { background-color: #deeeff; }
/*矩阵*/
.MathJye .matrix { }
.MathJye .matrixtop, .MathJye .matrixbtm { height: 11px; }
.MathJye .matrixmid { height: 7px; }
/*高度height动态计算*/

/*大花括号*/
.MathJye .brace1 { height: 12px; }
.MathJye .brace2 { height: 5px; }

/*向量*/

/*根号数字*/

/*根号*/
.MathJye .sqrt { transform-origin: center top; }
.MathJye .sqrt-num { position: absolute; top: -10px; left: 2px; }
/*空白行*/
.pt6 span, .ac td span.fleft { white-space: normal; }

/*IE hack*/
/*CSS-Hack for Internet Explorer 10+（IE10、IE11、Edge）*/
/*_:-ms-lang(x),
.ques-list li { list-style-type: circle; }*/

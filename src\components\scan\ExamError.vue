<template>
  <div class="error-main-container" v-loading="loading">
    <div
        class="img-box"
        v-if="currentImg"
        @mousedown="mouseDown"
        @mouseup="mouseUp"
        @mousemove="mouseMove"
        @wheel="zoomWhell"
    >
      <div class="avatar" ref="imageDivBox">
        <div class="img-container" style="display: inline-block">
          <div class="image-show-box">
            <!-- <img
              class="img transform-img"
              style="display: inline-block"
              :src="`${fsUrl}/${currentImg}?${ossImageProcess}`"
              draggable="false"
              id="pic"
            /> -->
            <picture>
              <source :srcset="`${replaceImgUrl(currentImg)}?${ossImageProcess}`" type="image,jpeg"/>
              <img
                  class="img transform-img"
                  style="display: inline-block"
                  draggable="false"
                  id="pic"
                  :src="removeAvifParam(`${replaceImgUrl(currentImg)}?${ossImageProcess}`)"
              />
            </picture>

            <ImageMoveMatrix
                v-if="isOriginImg"
                class="image_move_box img transform-img"
                :image-matrix="divMatrix"
                :anchor_list="this.image_anchor_list"
                :scale="divScale"
                :offset-x="200"
                :offset-y="200"
                @onMatrixChange="onMatrixChange"
                @onMatrixChangeEnd="onMatrixChangeEnd"
                @onMatrixImageMove="onMatrixImageMove"
                @onZoomWheel="operateZoom"
            >
            </ImageMoveMatrix>
          </div>
          <div class="page-points-wrapper" ref="pagePointInfo">
            <template v-if="pageQuestions" v-for="(ques, quesIndex) in pageQuestions">
              <!-- 客观题 -->
              <template v-if="ques.is_obj">
                <points-box
                    class="points-box"
                    :class="{
                    active: box.fill,
                    error: box.fill && (Math.pow(2, optionIndex) & ques.answer_int) == 0,
                    right: box.fill && (Math.pow(2, optionIndex) & ques.answer_int) != 0,
                  }"
                    v-for="(box, optionIndex) in ques.list"
                    :ques="ques"
                    :points="box"
                    :quesIndex="quesIndex"
                    :optionIndex="optionIndex"
                    :checkType="'errpage'"
                    :scale="scale"
                    :key="quesIndex + '_' + optionIndex + '_' + Math.random()"
                ></points-box>
              </template>
              <!-- 主观题 -->
              <template v-else>
                <template v-for="(scores, scoresIndex) in ques.score_list">
                  <template v-for="(box, optionIndex) in scores.newArray">
                    <points-box
                        class="points-box"
                        :class="{
                        active: box.fill,
                        error: box.fill && (Math.pow(2, optionIndex) & ques.answer_int) == 0,
                        right: box.fill && (Math.pow(2, optionIndex) & ques.answer_int) != 0,
                      }"
                        :ques="ques"
                        :points="box"
                        :quesIndex="quesIndex"
                        :optionIndex="scoresIndex"
                        :rowIndex="optionIndex"
                        :checkType="'errpage'"
                        :scale="scale"
                        :key="optionIndex + Math.random()"
                    ></points-box>
                  </template>
                </template>
                <div
                    v-if="!ques.score_list || !ques.score_list.length"
                    :key="ques.score_list + Math.random()"
                    :style="{
                    left: ques.pos[0] * 4.173 + 'px',
                    top: ques.pos[1] * 4.173 + 'px',
                    width: ques.pos[2] * 4.173 + 'px',
                    height: ques.pos[3] * 4.173 + 'px',
                  }"
                    class="ques-box-border"
                ></div>
              </template>
            </template>
            <template v-if="currentPage == 0">
              <div
                  :style="{
                  left: item.pos[0] * 4.173 + 'px',
                  top: item.pos[1] * 4.173 + 'px',
                  width: item.pos[2] * 4.173 + 'px',
                  height: item.pos[3] * 4.173 + 'px',
                }"
                  :key="index + Math.random()"
                  class="page-box-main"
                  :class="`page-type-${index}`"
                  v-for="(item, index) in currentInfo.pageBox"
              ></div>
            </template>

            <div
                v-for="(item, index) in anchor_list"
                class="point-anchor-pos"
                :key="index + Math.random()"
                :style="{
                left: item.px + 'px',
                top: item.py + 'px',
                width: item.width + 'px',
                height: item.height + 'px',
              }"
            ></div>

            <!--     可以显示答题卡原图，原图和答题卡进行对比       -->
            <div class="point-anchor-img-box" v-if="anchorSrcImageUrl">
              <div class="move-tips">拖动四个角的定位点，使透明闪烁的模板与作答图片尽量重合后重新识别</div>
              <img class="point-anchor-img"
                   :class="{'point-anchor-img-show':anchorSrcImageShow}"
                   :src="anchorSrcImageUrl"/>
            </div>
          </div>
        </div>
      </div>

      <div class="page-error-type">
        <span v-if="isSingleImage">{{ warning }}</span>
        <span v-else-if="isExamNoError">{{ getExamErrorTip(errorCode) }}</span>
        <span v-else-if="isABPaperError">{{ queErrorType[errorCode] }}</span>
        <span v-else>{{ imgErrorType[errorCode] }}</span>
      </div>
      <div class="page-list-one" :class="{ active: currentPage == 0 }" @click="changePage(0)">
        {{ examErrorData.length == 1 && currentInfo.idx_num == 2 ? '第二面' : '第一面' }}
      </div>
      <div
          v-if="examErrorData.length == 2"
          class="page-list-two"
          :class="{ active: currentPage == 1 }"
          @click="changePage(1)"
      >
        第二面
      </div>
      <el-button
          v-if="isPaperError || isPointError"
          class="reverse-button"
          size="mini"
          @click="reversePage"
      >正反切换
      </el-button>
      <el-button v-if="isPaperError" class="normal-button" size="mini" @click="makeImageError(2)"
      >定位异常
      </el-button>
      <el-button class="rotate-left" type="text" @click="rotateImg('left')">
        <i class="el-icon-refresh-left"></i>
      </el-button>
      <el-button class="rotate-right" type="text" @click="rotateImg('right')">
        <i class="el-icon-refresh-right"></i>
      </el-button>

      <div class="zoom-reset-button zoom-button" @click="divMatrixRest()">
        <i class="el-icon-aim"></i>
      </div>
      <div class="zoom-in-button zoom-button" @click="btnOperateZoom('add')">
        <i class="el-icon-zoom-in"></i>
      </div>
      <div class="zoom-out-button zoom-button" @click="btnOperateZoom('sub')">
        <i class="el-icon-zoom-out"></i>
      </div>
    </div>
    <no-data :type="'zan'"  v-else text="异常已全部处理，辛苦啦~"></no-data>
  </div>
</template>

<script>
import NoData from '@/components/noData';
import draggable from 'vuedraggable';
import ImgList from '@/components/scan/ImgList.vue';
import PointsBox from '@/components/scan/PointsBox.vue';
import Matrix2D from 'matrix2d.js';
import PageAnchor from '@/components/scan/PageAnchor';
import ImageMoveMatrix from '@/components/scan/ImageMoveMatrix';
import {getImgUrl, removeAvifParam} from '@/utils/index';
import {replaceALiUrl} from '@/utils/common';
import {IMG_ERRTYPE_WORD, QUES_ERRTYPE_WORD} from '@/typings/scan';
import {IAB_CARD_TYPE} from '@/typings/card';

let scleRatio = 1.1; // 每次放大/缩小的倍数
export default {
  components: {
    ImageMoveMatrix,
    PageAnchor,
    NoData,
    draggable,
    ImgList,
    PointsBox,
  },

  props: {
    //当前错误类型
    examErrorData: {
      type: Array,
      default: () => [],
    },
    //当前错误类型
    firstImage: {
      type: String,
      default: () => '',
    },
    // 坐标点数据
    pointsData: {
      type: Object,
      default: () => {
      },
    },
    //当前错误类型
    currentError: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      //图片异常类型
      imgErrorType: IMG_ERRTYPE_WORD,
      queErrorType: QUES_ERRTYPE_WORD,
      fsUrl: process.env.VUE_APP_FS_URL,
      scleX: 0,
      scleY: 0,
      imageMove: {
        start: {
          x: 0,
          y: 0,
        },
        imagePt: {
          x: 0,
          y: 0,
        },
      },
      isDragging: false,
      currentPage: 0,
      currentImg: '',
      isOriginImg: false,
      loading: false,
      //题号异常类型
      errorCode: '',
      warning: '',
      rotateRatio: 0,
      imageHeight: 1238,
      pageBoxList: [],
      scale: 4.173,
      currentInfo: {},
      drawStyle: {},
      isDraggBox: false,
      anchor_list: [],
      image_anchor_list: [],
      divScale: 1,
      divMatrix: new Matrix2D(),
      currentImageMatrix: new Matrix2D(),
      imageMatrixStr: '',
      divMatrixStr: '',
      removeAvifParam: removeAvifParam,
      anchorSrcImageShow: false,
      anchorSrcImageUrl: null,
      pageQuestions: null,
      pageSrcUrls: []
    };
  },
  computed: {
    ossImageProcess() {
      let size = 'h_1238';
      let rotate = '';
      if (this.rotateRatio !== 0) {
        if (this.rotateRatio === 90 || this.rotateRatio === 270) {
          size = 'w_1238';
        }
        rotate = `,image/rotate,${this.rotateRatio}`;
      }
      // return `x-oss-process=image/resize,${size}${rotate}`;
      return `x-oss-process=image/resize,${size}${rotate},image/format,jpeg`;
    },
    isPaperError() {
      return this.currentError === 'paper';
    },
    isExamNoError() {
      return this.currentError === 'exam';
    },
    isPointError() {
      return this.currentError === 'point';
    },
    isABPaperError() {
      return this.currentError === 'abCard';
    },
    isSingleImage() {
      return (
          this.currentError === 'point' ||
          this.currentError === 'paper' ||
          this.currentError === 'absent'
      );
    },
  },
  watch: {
    examErrorData(newVal, oldVal) {
      if (newVal) {
        this.currentPage = 0;
        this.rotateRatio = 0;
      }
    },
    pointsData(newVal, oldVal) {
      this.initAnchorList(true)
    }
  },
  beforeDestroy() {
    this.$bus.$off('get-image');
  },
  mounted() {
    this.onChange();
  },
  methods: {
    getExamErrorTip(errorCode) {
      let code = (Number(errorCode) & 8) || (Number(errorCode) & 16) || (Number(errorCode) & 32);
      return this.imgErrorType[code];
    },
    saveImageInfo() {
      let matrix_str = '';

      if (this.imageMatrixStr) {
        matrix_str = this.currentImageMatrix.invert() + '';
        matrix_str = matrix_str.substring(7, matrix_str.length - 1);
      }
      let info = {
        matrix: matrix_str,
        rotate: this.rotateRatio,
        height: this.imageHeight,
      };
      this.$store.commit('UPDATE_IMAGE_MATRIX', info);
    },
    //图片矩阵改变
    onMatrixChange(matrix) {
      this.currentImageMatrix = matrix;
      this.updateMatrixStr(matrix.toString())
      this.anchorSrcImageShow = true
      this.saveImageInfo();
    },
    onMatrixChangeEnd() {
      this.anchorSrcImageShow = false
    },
    updateMatrixStr(matrixStr) {
      this.imageMatrixStr = matrixStr
      if (this.$refs.pagePointInfo) {
        this.$refs.pagePointInfo.style.transform = matrixStr
      }
    },
    updateDivMatrixStr(matrixStr) {
      this.divMatrixStr = matrixStr
      if (this.$refs.imageDivBox) {
        this.$refs.imageDivBox.style.transform = matrixStr
      }
    },
    onMatrixImageMove(trans) {
      this.divMatrix.tx += trans.x;
      this.divMatrix.ty += trans.y;

      this.updateDivMatrixStr(this.divMatrix.toString())
    },
    // div各种矩阵重置
    divMatrixRest() {
      this.divMatrix = new Matrix2D();

      this.updateDivMatrixStr('')
      this.divScale = 1;
    },
    //页面重置
    pageReset() {
      this.matrixReset()
      this.rotateRatio = 0;
      this.saveImageInfo();
    },
    anchorListReset(){
      let image_anchor_list = [];
      this.anchor_list.forEach(it => {
        image_anchor_list.push({
          px: it.px,
          py: it.py,
          cx: it.cx,
          cy: it.cy,
          dest_x: it.cx,
          dest_y: it.cy,
        });
      });
      this.image_anchor_list = image_anchor_list;
    },
    // 所有数据矩阵重置
    matrixReset() {
      this.divMatrixRest();
      this.anchorListReset()
      this.updateMatrixStr('')
      this.currentImageMatrix = new Matrix2D();
    },
    initAnchorList(isUpdate = false) {
      let margin = this.pointsData.margin
      if (!margin) {

        margin = {
          width: 20,
          height: 8,
          pos: [6, 6, 6, 6]
        }
      }
      if (this.anchor_list.length && !isUpdate) return;

      if (this.pointsData.card_type === 3) {
        let page_urls = []
        this.pointsData.pages.forEach(page => {
          page.forEach(item => {
            if (item.item_type === 0) {
              page_urls.push(item.url)
            }
          })
        })
        this.pageSrcUrls = page_urls
      }

      let pxScale = 4.173;
      let width = this.pointsData.width * pxScale;
      let height = this.pointsData.height * pxScale;

      let mw = margin.width * pxScale;
      let mh = margin.height * pxScale;

      let pos = [];
      for (let i = 0; i < 4; i++) {
        pos[i] = margin.pos[i] * pxScale;
      }
      // ax：锚点位置，ox,试卷原点位置
      let anchor_list = [];
      anchor_list[0] = {
        ax: pos[0],
        ay: pos[1],
        px: pos[0],
        py: pos[1],
        width: mw,
        height: mh,
      };
      anchor_list[1] = {
        ax: width - pos[2],
        ay: pos[1],
        px: width - pos[2] - mw,
        py: pos[1],
        width: mw,
        height: mh,
      };

      anchor_list[2] = {
        ax: width - pos[2],
        ay: height - pos[3],
        px: width - pos[2] - mh,
        py: height - pos[3] - mw,
        width: mh,
        height: mw,
      };

      anchor_list[3] = {
        ax: pos[0],
        ay: height - pos[3],
        px: pos[0],
        py: height - pos[3] - mw,
        width: mh,
        height: mw,
      };

      for (let i = 0; i < 4; i++) {
        anchor_list[i].x = anchor_list[i].ax;
        anchor_list[i].y = anchor_list[i].ay;

        anchor_list[i].cx = anchor_list[i].px + anchor_list[i].width / 2;
        anchor_list[i].cy = anchor_list[i].py + anchor_list[i].height / 2;

        anchor_list[i].pos = i;
      }

      this.anchor_list = anchor_list;
    },
    getBoxStyle(box) {
      return {
        left: box[0] * this.scale + 'px',
        top: box[1] * this.scale + 'px',
        width: box[2] * this.scale + 'px',
        height: box[3] * this.scale + 'px',
      };
    },
    onChange() {
      this.$bus.$on('get-image', firstInfo => {

        this.updateCurrentInfo(firstInfo)
        this.currentImg = this.getPageImage(firstInfo);
        if (this.currentError == 'abCard') {
          //如果为ab卡或ab卷，取ab_info的code
          this.errorCode = (firstInfo.ab_card != IAB_CARD_TYPE.default ? firstInfo.ab_info?.code : firstInfo?.code) || '';
        } else {
          this.errorCode = firstInfo?.code || '';
        }
        this.imgWidth = firstInfo.width || 0;
        this.warning = firstInfo.warning;
        this.pageReset();
        this.initAnchorList();
      });
    },
    /**
     * @name:标记为答卷正常
     */
    makeImageError(weight) {
      this.$emit('mark-image-error', this.currentInfo, weight);
    },
    /**
     * 正反页切换重新识别
     */
    reversePage() {
      this.$emit('reverse-page', this.currentInfo);
    },

    /**
     * @name：初始化图片缩放比列
     */
    initImage(firstInfo) {
    },

    replaceImgUrl(url) {
      return replaceALiUrl(url)
    },

    //旋转图片
    rotateImg(direction) {
      let el = document.querySelector('.image-show-box');

      let angle = -90;
      if (direction === 'right') {
        angle = 90;
      }
      this.rotateRatio += angle + 360;
      this.rotateRatio = this.rotateRatio % 360;
      this.matrixReset()
      this.saveImageInfo();

    },
    getPageImage(info) {
      if (this.currentError === 'point') {
        this.isOriginImg = !!info.origin;
        return info.origin || '';
      }
      this.isOriginImg = !!(!info.image && info.origin);
      return info.image || info.origin || '';
    },
    changePage(page) {
      this.currentPage = page;
      this.currentImg = this.getPageImage(this.examErrorData[this.currentPage]);
      this.errorCode = this.examErrorData[this.currentPage].code;
      this.updateCurrentInfo(this.examErrorData[this.currentPage])
      this.pageReset();
      this.initAnchorList();
    },
    updateCurrentInfo(info){

      this.anchorSrcImageUrl = null

      this.pageQuestions = null
      // 三方卡的定位点异常，显示模板图片对比
      if (this.currentError === 'point'
          && this.pageSrcUrls
          && info.page <= this.pageSrcUrls.length) {
        this.anchorSrcImageUrl = this.pageSrcUrls[info.page - 1]
        if (this.pointsData.pageQuestionList
            && this.pointsData.pageQuestionList.length >= info.page) {
          this.pageQuestions = this.pointsData.pageQuestionList[info.page - 1];
        }
      }

      if(!this.pageQuestions){
        this.pageQuestions = info.questions
      }

      this.currentInfo = info
    },
    btnOperateZoom(type) {
      let oDiv = document.querySelector('.img-box');

      let x = oDiv.clientWidth / 2;
      let y = 0;
      this.operateZoom(type, x, y);
    },
    zoomWhell(e) {
      let imageX = e.offsetX;
      let imageY = e.offsetY;

      // if (this.divMatrix) {
      //   let pt = this.divMatrix.transformPoint(imageX, imageY);
      //   imageX = pt.x;
      //   imageY = pt.y;
      // }

      if (e.deltaY < 0) {
        // 放大DELTA倍
        this.operateZoom('add', imageX, imageY);
      } else {
        // 缩小DELTA倍
        this.operateZoom('sub', imageX, imageY);
      }
    },
    operateZoom(type, x, y) {
      let oDiv = document.querySelector('.avatar');
      let transf = this.getTransform(oDiv);

      if (type === 'add') {
        transf.multiple *= scleRatio; // 放大DELTA倍
      } else {
        transf.multiple /= scleRatio; // 缩小DELTA倍
      }

      this.divScale = transf.multiple;
      x = x || 0;
      y = y || 0;
      let divMatrix = new Matrix2D();
      if (this.divMatrix) {
        let pt = this.divMatrix.transformPoint(x, y);
        let dx = pt.x - x;
        let dy = pt.y - y;
        divMatrix.translate(dx, dy);
      }

      divMatrix.translate(x, y);
      divMatrix.scale(transf.multiple);
      divMatrix.translate(-x, -y);
      this.divMatrix = divMatrix;

      this.updateDivMatrixStr(divMatrix.toString())
    },
    /**
     * @name:鼠标按下
     */
    mouseDown(e) {
      if (e.button !== 0) return;
      e.stopPropagation();

      this.isDragging = true;

      this.imageMove.start.x = e.clientX;
      this.imageMove.start.y = e.clientY;
      this.imageMove.imagePt.x = this.divMatrix.tx;
      this.imageMove.imagePt.y = this.divMatrix.ty;

      document.addEventListener('mousemove', this.mouseMove);
      document.addEventListener('mouseup', this.mouseUp);
      // console.log('divMouseDown')
    },
    /**
     * @name：鼠标移动
     */
    mouseMove(e) {
      if (!this.isDragging) {
        return;
      }

      let moveX = e.clientX - this.imageMove.start.x; // x向移动距离
      let moveY = e.clientY - this.imageMove.start.y; // y向移动距离
      let tx = this.imageMove.imagePt.x + moveX;
      let ty = this.imageMove.imagePt.y + moveY;
      this.divMatrix.tx = tx;
      this.divMatrix.ty = ty;
      this.updateDivMatrixStr(this.divMatrix.toString())
    },
    mouseUp(e) {
      e.stopPropagation();
      this.isDragging = false;
      document.removeEventListener('mousemove', this.mouseMove);
      document.removeEventListener('mouseup', this.mouseUp);
      // console.log('divMouseUp')
    },

    limitBorder(innerDOM, outerDOM, moveX, moveY, multiple) {
      let {
        clientWidth: innerWidth,
        clientHeight: innerHeight,
        offsetLeft: innerLeft,
        offsetTop: innerTop,
      } = innerDOM;
      let {clientWidth: outerWidth, clientHeight: outerHeight} = outerDOM;
      let transX;
      let transY;
      // 放大的图片超出box时 图片最多拖动到与边框对齐
      if (innerWidth * multiple > outerWidth || innerHeight * multiple > outerHeight) {
        if (innerWidth * multiple > outerWidth && innerWidth * multiple > outerHeight) {
          transX = Math.min(
              Math.max(moveX, outerWidth - (innerWidth * (multiple + 1)) / 2 - innerLeft),
              -innerLeft + (innerWidth * (multiple - 1)) / 2
          );
          transY = Math.min(
              Math.max(moveY, outerHeight - (innerHeight * (multiple + 1)) / 2 - innerTop),
              -innerTop + (innerHeight * (multiple - 1)) / 2
          );
        } else if (innerWidth * multiple > outerWidth && !(innerWidth * multiple > outerHeight)) {
          transX = Math.min(
              Math.max(moveX, outerWidth - (innerWidth * (multiple + 1)) / 2 - innerLeft),
              -innerLeft + (innerWidth * (multiple - 1)) / 2
          );
          transY = Math.max(
              Math.min(moveY, outerHeight - (innerHeight * (multiple + 1)) / 2 - innerTop),
              -innerTop + (innerHeight * (multiple - 1)) / 2
          );
        } else if (!(innerWidth * multiple > outerWidth) && innerWidth * multiple > outerHeight) {
          transX = Math.max(
              Math.min(moveX, outerWidth - (innerWidth * (multiple + 1)) / 2 - innerLeft),
              -innerLeft + (innerWidth * (multiple - 1)) / 2
          );
          transY = Math.min(
              Math.max(moveY, outerHeight - (innerHeight * (multiple + 1)) / 2 - innerTop),
              -innerTop + (innerHeight * (multiple - 1)) / 2
          );
        }
      }
      // 图片小于box大小时 图片不能拖出边框
      else {
        transX = Math.max(
            Math.min(moveX, outerWidth - (innerWidth * (multiple + 1)) / 2 - innerLeft),
            -innerLeft + (innerWidth * (multiple - 1)) / 2
        );
        transY = Math.max(
            Math.min(moveY, outerHeight - (innerHeight * (multiple + 1)) / 2 - innerTop),
            -innerTop + (innerHeight * (multiple - 1)) / 2
        );
      }
      return {transX, transY};
    },
    getTransform(DOM) {
      let arr = window.getComputedStyle(DOM).transform.split(',');
      return {
        transX: isNaN(+arr[arr.length - 2]) ? 0 : +arr[arr.length - 2], // 获取translateX
        transY: isNaN(+arr[arr.length - 1].split(')')[0]) ? 0 : +arr[arr.length - 1].split(')')[0], // 获取translateX
        multiple: +arr[3], // 获取图片缩放比例
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.error-main-container {
  position: absolute;
  top: 100px;
  height: calc(100% - 110px);
  width: 100%;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  // .img-box {
  //   display: flex;
  //   justify-content: center;
  //   height: 100%;
  //   width: 1000px;
  //   .img {
  //     height: 100%;
  //     cursor: move;
  //     // transform: translate(0px, 0px);
  //   }
  // }
}

.img-box {
  position: relative;
  height: calc(100%);
  width: 100%;
  border: 1px solid #cccccc;
  overflow: hidden;
  cursor: move;
}

.avatar {
  // width: 500px;
  // height: 500px;
  // left: 50px;
  // top: 50px;
  position: relative;
  transform: translate(0px, 0px);
  transform-origin: 0 0;
}

.rotate-left,
.rotate-right {
  position: absolute;
  top: 0px;
  left: 20px;
  font-size: 25px;
}

.rotate-right {
  left: 45px;
}

.img {
  position: relative;
  // width: 100%;
}

.page-box-main {
  position: absolute;
  border: solid 2px #409eff;
  z-index: 5;
}

.page-error-type {
  position: absolute;
  right: 10px;
  height: 30px;
  top: 0px;
  color: red;
}

.page-list-one,
.page-list-two {
  position: absolute;
  right: 10px;
  width: 70px;
  height: 30px;
  border-radius: 6px;
  background-color: #ffffff;
  border: 1px solid #cccccc;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  &.active {
    background-color: #409eff;
    color: #ffffff;
  }
}

.page-list-one {
  top: 30px;
}

.page-list-two {
  top: 70px;
}

.zoom-button {
  position: absolute;
  font-size: 25px;
  display: flex;
  justify-content: center;
  border-radius: 50%;
  align-items: center;
  cursor: pointer;

  &:hover {
    background-color: #fff;
  }
}

.zoom-in-button {
  bottom: 50px;
  right: 10px;
}

.zoom-out-button {
  bottom: 10px;
  right: 10px;
}

.zoom-reset-button {
  bottom: 90px;
  right: 10px;
}

.zoom-move-button {
  bottom: 130px;
  right: 10px;
}

.reverse-button {
  position: absolute;
  right: 10px;
  top: 80px;
  width: 70px;
  height: 30px;
  border-radius: 6px;
  display: flex;
  justify-content: center;
}

.normal-button {
  position: absolute;
  right: 10px;
  top: 120px;
  width: 70px;
  height: 30px;
  border-radius: 6px;
  display: flex;
  justify-content: center;
}

.zoom-button:hover {
  background-color: #f2f2f2;
}

.ques-box-border {
  position: absolute;
  border: solid 1px red;
}

// .points-box {
//   position: absolute;
//   cursor: pointer;

//   &.active {
//     border: solid 4px #01cc7d;
//   }

//   &.error {
//     border: solid 4px red !important;
//   }

//   &.right {
//     border: solid 4px #01cc7d !important;
//   }
// }

.page-points-wrapper {
  position: absolute;
  left: 0;
  top: 0;
  padding: 100px;
  transform-origin: 0 0;
}

.point-anchor-pos {
  position: absolute;
  width: 40px;
  height: 20px;
  border: solid #f00 3px;
}

.point-anchor-img-box {
  position: absolute;
  margin-left: -100px;
  margin-top: -100px;
  border: solid #00ff80 3px;
  height: 1238px;

  .move-tips {
    position: absolute;
    left: 150px;
    width: 600px;
    top: 0;
    color: red;
  }

  .point-anchor-img {
    opacity: 0.08;
    height: 1238px;
    animation: fadeAnimation 0.8s infinite alternate ease-in-out;
    //-webkit-mask: radial-gradient(circle at center, transparent 50%, rgba(0, 0, 0, 0.1) 70%);
    //mask: radial-gradient(circle at center, transparent 50%, rgba(0, 0, 0, 0.1) 70%);
  }

  .point-anchor-img-show {
    opacity: 0.3;
    animation: unset;
    //-webkit-mask: radial-gradient(circle at center, transparent 50%, rgba(0, 0, 0, 0.3) 70%);
    //mask: radial-gradient(circle at center, transparent 50%, rgba(0, 0, 0, 0.3) 70%);
  }

  /* 关键帧动画：透明度在 0.1 到 0.3 之间变化 */
  @keyframes fadeAnimation {
    0% {
      opacity: 0.05; /* 最小透明度 */
    }
    100% {
      opacity: 0.1; /* 最大透明度 */
    }
  }
  @keyframes fadeAnimation2 {
    0% {
      opacity: 0.1; /* 最小透明度 */
    }
    100% {
      opacity: 0.3; /* 最大透明度 */
    }
  }
}


.img-container {
  margin-left: -200px;
  margin-top: -200px;
}

.transform-img {
  transform-origin: 0 0;
  padding: 200px;
  border: solid 2px red !important;
}

.image_move_box {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  z-index: 6;
  user-select: none;
  -webkit-user-drag: none;
}

.image-show-box {
  position: relative;
}
</style>

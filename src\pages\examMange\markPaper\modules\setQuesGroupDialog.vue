<template>
  <el-dialog custom-class="set-ques-group-dialog" :visible="modalVisible" width="650px" :before-close="handleClose"
    :modal-append-to-body="false" :close-on-click-modal="false" :close-on-press-escape="false">
    <div slot="title" class="dialog-title">
      <span style="margin-right: 10px">设置</span>
    </div>
    <div class="set-group-container">
      <div style="margin:-10px 0 10px 0px;font-size: 16px;">
        本题满分<span class="tips">{{ sumScore }}</span>分，各小题分累加之和为<span class="tips">{{ curScore }}</span>分
      </div>
      <template v-if="!isSplit">
        <el-radio-group v-model="currQuesInfo.scoreMode" v-if="currQuesInfo.isMerge">
          <el-radio v-if="!hasStride" :label="0">合并给一个分</el-radio>
          <el-radio :label="1">独立小题给分</el-radio>
          <el-radio v-if="!hasStride && !hasChoose" :label="2">自定义给分</el-radio>
        </el-radio-group>
        <div class="score-container">
          <div v-if="currQuesInfo.scoreMode == 0 || !currQuesInfo.isMerge">
            <span v-if="currQuesInfo.isMerge">
              <el-input v-model="currQuesInfo.score" class="input-container" @input="checkScore(currQuesInfo)"></el-input>&nbsp;分
            </span>
            <span class="set-scoreStep">
              步长
              <el-select v-model="currQuesInfo.scoreStep" placeholder="请选择" class="group-select">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
              &nbsp;分
              <!-- <el-input v-model="currQuesInfo.scoreStep" class="input-container"></el-input>&nbsp;分 -->
            </span>
          </div>
          <div v-else-if="currQuesInfo.scoreMode == 1" class="small-container">
            <div v-for="(item, index) in currQuesInfo.data" :key="index" class="small-ques">
              <span class="ques-nos">{{ item.quesNos }}</span>
              <span>
                <el-input v-model="item.score" class="input-container" style="" @input="checkScore(item)"></el-input>&nbsp;分
              </span>
              <span class="set-scoreStep">
                步长
                <el-select v-model="item.scoreStep" placeholder="请选择" class="group-select">
                  <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
                &nbsp;分
                <!-- <el-input v-model="item.scoreStep" class="input-container"></el-input>&nbsp;分 -->
              </span>
            </div>
          </div>
          <div v-else-if="currQuesInfo.scoreMode == 2" class="custom-container">
            <div class="ques-list">
              <span>请选择题目</span>
              <el-checkbox-group v-model="checkList">
                <el-checkbox v-for="(item, index) in currQuesInfo.data" :label="item" :disabled="item.disabled">{{
                  item.quesNos }}</el-checkbox>
              </el-checkbox-group>
              <div class="add-score" @click="addScore">+ 新建给分点</div>
            </div>
            <div class="ques-score-list">
              <template v-if="scoreList.length > 0">
                <span style="margin-left: 10px;">给分点</span>
                <div v-for="(item, index) in scoreList" class="small-score">
                  <span><el-input v-model="item.name" class="input-container"></el-input></span>
                  <span>
                    <el-input v-model="item.score" class="input-container" @input="checkScore(item)"></el-input>&nbsp;分
                  </span>
                  <span class="set-scoreStep">
                    步长
                    <el-select v-model="item.scoreStep" placeholder="请选择" class="group-select">
                      <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                      </el-option>
                    </el-select>
                    &nbsp;分
                  </span>
                  <span class="hand-item" v-if="index == (scoreList.length-1) && scoreList.length > 0" @click="deleteScore">删除</span>
                </div>
              </template>
              <el-empty v-else description="请添加给分点" :image-size="50"></el-empty>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <el-radio-group v-model="currQuesInfo.scoreMode">
          <el-radio :label="3">共用一个切图，独立小题给分</el-radio>
          <el-radio :label="4">每小题一个切图，分别给分</el-radio>
        </el-radio-group>
        <div class="score-container">
          <div v-if="currQuesInfo.data?.length" class="small-container">
            <div v-for="(item, index) in currQuesInfo.data" :key="index" class="small-ques">
              <span>
                <el-input v-model="item.quesNos" class="input-container"></el-input>
              </span>
              <span>
                <el-input v-model="item.score" class="input-container" @input="checkScore(item)"></el-input>&nbsp;分
              </span>
              <span class="set-scoreStep">
                步长
                <el-select v-model="item.scoreStep" placeholder="请选择" class="group-select">
                  <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
                &nbsp;分
              </span>
            </div>
            <div>
              <span class="hand-item" @click="addQues">添加</span>
              <span class="hand-item" v-if="currQuesInfo.data?.length > 2" @click="deleteQues">删除</span>
            </div>
          </div>
        </div>
      </template>
    </div>
    <div slot="footer" class="dialog-footer">
      <!-- <el-button @click="closeModal">取 消</el-button> -->
      <el-button type="primary" :loading="isComfirming" @click="sureClick">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { generateUUID } from '@/utils/index';
import { checkFloat } from '@/utils/number.js';
export default {
  props: {
    modalVisible: {
      type: Boolean,
      required: false,
    },
    currentQues: {
      type: Object,
      required: false,
    },
    currentSmallIndex: {
      type: Number,
      required: false,
    },
    isSplit: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  data() {
    return {
      isComfirming: false,
      currQuesInfo: {},
      options: [
        {
          value: 0.5,
          label: 0.5,
        },
        {
          value: 1,
          label: 1,
        },
        {
          value: 1.5,
          label: 1.5,
        },
        {
          value: 2,
          label: 2,
        },
        {
          value: 3,
          label: 3,
        },
        {
          value: 4,
          label: 4,
        },
        {
          value: 5,
          label: 5,
        },
      ],
      checkList: [],
      scoreList: [],
      //跨学科
      hasStride :false,
      //选做题
      hasChoose :false,
      sumScore: 0,
      curScore: 0
    };
  },
  watch: {
    modalVisible(newVal, oldVal) {
      if(!newVal) return;
      this.scoreList = [];
      this.currQuesInfo = JSON.parse(JSON.stringify(this.currentQues));
      if (this.isSplit) {
        if (this.currQuesInfo.scoreMode == 0) {
          this.currQuesInfo.scoreMode = 3;
        }
        this.sumScore = this.currQuesInfo.score;
        if (!this.currQuesInfo.data) {
          this.$set(this.currQuesInfo,"data",[]);
          this.addQues();
          this.addQues();
        }
      } else {
        if (this.currQuesInfo.scoreMode == 0) {
          this.sumScore = this.currQuesInfo.score;
          this.curScore = this.currQuesInfo.score;
        } else if (this.currQuesInfo.scoreMode == 1 || this.currQuesInfo.scoreMode == 3 || this.currQuesInfo.scoreMode == 4) {
          if (this.currQuesInfo.data && this.currQuesInfo.data.length > 0) {
            this.sumScore = this.currQuesInfo.data.reduce((acc, item) => acc + Number(item.score), 0);
          } else {
            this.sumScore = this.currQuesInfo.score;
          }
        } else {
          if (this.currQuesInfo.scoreMode == 2) {
            this.scoreList = this.currQuesInfo.scoreList || [];
          }else{
            this.scoreList = [];
          }
          this.sumScore = this.currQuesInfo.score;
        }
        let { isStride,hasChoose } =  this.checkQuesStride(this.currQuesInfo.data);
        this.hasStride = isStride;
        this.hasChoose = hasChoose;
      }
      this.checkScore()
      this.$forceUpdate()
    },
    'currQuesInfo.scoreMode'() {
      this.checkScore()
    }
  },
  mounted() {
  },
  methods: {
    checkScore(item) {
      if (item) {
        let score = checkFloat(item.score);
        if (score > 100) {
          this.$message({
            message: '分值最大不超过100分!',
            type: 'error',
            duration: 1000,
          });
          item.score = 100;
        } else {
          item.score = score;
        }
      }
      if (this.currQuesInfo.scoreMode == 0) {
        this.curScore = this.currQuesInfo.score;
      } else if (this.currQuesInfo.scoreMode == 1 || this.currQuesInfo.scoreMode == 3 || this.currQuesInfo.scoreMode == 4) {
        if(this.currQuesInfo.data && this.currQuesInfo.data.length > 0){
          this.curScore = this.currQuesInfo.data.reduce((acc, item) => acc + Number(item.score), 0);
        }else{
          this.curScore = this.currQuesInfo.score;
        }
      } else {
        this.curScore = this.scoreList.reduce((acc, item) => acc + Number(item.score), 0);
        this.currQuesInfo.data.forEach((ques) => {
          ques.disabled = false;
          this.scoreList.forEach((score) => {
            let quesNos = score.quesNo.split(",")
            if (quesNos.includes(String(ques.quesNo))) {
              ques.disabled = true;
            }
          })
        })
      }
    },
    addQues() {
      if(this.currQuesInfo.data.length>=20){
        this.$message.error("最多支持添加20题")
        return;
      }
      const currQuesInfo = JSON.parse(JSON.stringify(this.currQuesInfo));
      let ques = {
        "id": generateUUID(),
        "isTemp":true,
        "name": currQuesInfo.name,
        "quesNo": currQuesInfo.quesNo,
        "quesNos": `${currQuesInfo.quesNos}(${currQuesInfo.data.length+1})`,
        "score": '',
        "type": currQuesInfo.type,
        "typeId": currQuesInfo.typeId,
        "optionCount": 4,
        "answer": [
          ""
        ],
        "specialTypeId": currQuesInfo.specialTypeId,
        "scoreStep": currQuesInfo.scoreStep,
        "quesName": currQuesInfo.quesName,
        "points": [
        ],
        "isMerge": false,
        "threeLevel": false,
        "firstSmallId": "",
        "halfScore": currQuesInfo.score/2,
        "ruleType": 0,
        "rules": [],
        "bigIndex": currQuesInfo.bigIndex
      }
      this.currQuesInfo.data.push(ques);
      this.checkScore();
    },
    deleteQues(){
      this.currQuesInfo.data.pop();
      this.checkScore();
    },
    checkQuesStride(list){
      let sid = "";
      let isStride = false;
      let hasChoose = false;
      list.forEach((item) => {
        if(item.isChooseDo){
          hasChoose = true;
        }
        if(sid===""){
          sid = item.subjectId;
        }
        if (sid != item.subjectId) {
          isStride = true;
        }
      })
      return {isStride,hasChoose}
    },
    addScore() {
      if (this.checkList.length == 0) return;
      // if(this.checkQuesStride(this.checkList)){
      //   this.$message.error("小题与小问无法合并给分点")
      //   return;
      // }
      let name = [];
      let score = 0;
      let quesNo = [];
      this.checkList.forEach((item) => {
        name.push(item.quesNos);
        quesNo.push(item.quesNo);
        score += Number(item.score);
        item.disabled = true;
      })
      this.scoreList.push({
        name: name.join(','),
        quesNo: quesNo.join(','),
        score: Number(score),
        scoreStep: 1
      })
      this.checkList = []
      this.checkScore()
    },
    deleteScore(){
      this.scoreList.pop();
      this.checkScore();
    },
    // 关闭弹窗
    handleClose(done) {
      this.$emit("close-set-group");
      done();
    },
    /**
     * 取消
     */
    closeModal() {
      this.$emit("close-set-group");
    },
    sureClick() {
      if(this.curScore != this.sumScore){
        this.$message.error("分值不匹配，请重新设置")
        return;
      }
      
      if(this.currQuesInfo.data && (this.currQuesInfo.scoreMode == 1|| this.currQuesInfo.scoreMode==3|| this.currQuesInfo.scoreMode==4)){
        let zerolist = this.currQuesInfo.data.filter(item=>{
          return !item.score || item.score == 0
        })
        if(zerolist.length){
          this.$message.error("分值不可为0，请重新设置")
          return;
        }
      }
      if(this.scoreList && this.currQuesInfo.scoreMode == 2){
        let zerolist = this.scoreList.filter(item=>{
          return !item.score || item.score == 0
        })
        if(zerolist.length){
          this.$message.error("分值不可为0，请重新设置")
          return;
        }
      }
      
      if (this.currQuesInfo.scoreMode == 1) {
        this.currQuesInfo.score = this.currQuesInfo.data.reduce(
          (acc, item) => acc + Number(item.score),
          0
        );
      } else if (this.currQuesInfo.scoreMode == 2) {
        this.currQuesInfo.scoreList = this.scoreList;
        this.currQuesInfo.score = this.scoreList.reduce(
          (acc, item) => acc + Number(item.score),
          0
        );
      }else if((this.currQuesInfo.scoreMode == 3 || this.currQuesInfo.scoreMode == 4) && this.currQuesInfo.data.length<2){
        this.$message.error("最少需添加2题")
        return;
      }     
      this.currQuesInfo.isSplit = this.isSplit;
      this.$emit("confirm-set-group", this.currQuesInfo, this.currentSmallIndex);
    }
  },
};
</script>

<style lang="scss" scoped>
.set-group-container {
  display: flex;
  flex-direction: column;

  .tips {
    color: #409eff;
  }

  .score-container {
    margin-top: 20px;
    .hand-item{
      line-height: 32px;
    cursor: pointer;
    margin-left: 5px;
    color: #409eff;
    }
  }
}

.input-container {
  width: 86px;
  height: 32px;
  margin-left: 10px;
}

.small-container {
  display: flex;
  flex-wrap: wrap;

  .small-ques {
    display: flex;
    align-items: center;
    margin-right: 15px;
    margin-bottom: 10px;
    .ques-nos{
      min-width: 30px;
    }
  }
}

.custom-container {
  padding: 5px;
  background: aliceblue;
  min-height: 150px;

  .ques-list {
    width: 20%;
    display: inline-block;

    .add-score {
      color: #409eff;
      cursor: pointer;
      margin-top: 5px;
    }
  }

  .ques-score-list {
    width: 80%;
    display: inline-block;
    vertical-align: top;

    .small-score {
      margin-top: 5px;
    }
  }
}

.set-scoreStep {
  margin-left: 20px;
}
</style>
<style lang="scss">
.set-ques-group-dialog {
  .el-dialog__header {
    height: 45px;

    .dialog-title {
      line-height: 45px;
    }
    .el-dialog__close{
      // display: none;
    }
  }

  .custom-container {
    .ques-list {
      .el-checkbox-group {
        display: flex;
        flex-direction: column;

        .el-checkbox {
          font-size: 16px;

          .el-checkbox__label {
            font-size: 16px;
          }
        }
      }
    }
  }
}

.group-select {
  width: 86px;

  .el-input__suffix {
    display: flex;
    align-items: center;
  }
}
</style>
<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-03-25 10:38:56
 * @LastEditors: 小圆
-->
<template>
  <div class="header-item">
    <span class="header-item__label">名次步长：</span>
    <el-select
      class="header-item__select short-select"
      v-model="filterData.rank"
      @change="onChange"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <el-option
        v-for="item in selectOption.rankList"
        :key="item"
        :title="item"
        :label="item"
        :value="item"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import ReportComponent from './ReportComponent';

@Component
export default class ReportRankSelect extends ReportComponent {
  onChange(value: number) {
    this.FilterModule.trigger('changeFilter');
  }
}
</script>

<style scoped lang="scss">
@import './reportSelect.scss';
</style>

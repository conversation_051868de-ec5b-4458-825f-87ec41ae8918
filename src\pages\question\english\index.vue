<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-04-29 14:47:00
 * @LastEditors: liuyue <EMAIL>
-->
<template>
  <div class="english-index">
    <div class="english-index-header">
      <div class="english-index-tabs">
        <div
          v-for="item in tabList"
          class="english-index-tab"
          :key="item.id"
          :class="{ active: item.id == curTabId }"
          @click="changeTab(item.id)"
        >
          {{ item.name }}
        </div>
      </div>
      <div class="english-index-header-right">
        <span class="count-text"
          >已选择<span class="count-num"> {{ WordStore.wordList.length }} </span>个单词</span
        >
        <el-button type="primary" size="small" :disabled="WordStore.wordList.length == 0" @click="gotoCard"
          >去制卡</el-button
        >
      </div>
    </div>

    <div class="english-index-main">
      <Book v-if="curTabId === 'book'" ref="BookRef" />
      <ErrWord v-if="curTabId === 'errword'" ref="ErrWordRef" />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Ref, Vue } from 'vue-property-decorator';
import Book from './book.vue';
import ErrWord from './errword.vue';
import WordStore from './WordStore';

@Component({
  components: {
    Book,
    ErrWord,
  },
})
export default class EnglishIndex extends Vue {
  @Ref('BookRef') BookRef!: any;
  @Ref('ErrWordRef') ErrWordRef!: ErrWord;

  // 标签列表
  tabList = [
    {
      name: '教材同步',
      id: 'book',
    },
    {
      name: '高频错词',
      id: 'errword',
    },
  ];
  // 当前标签
  curTabId = 'book';
  // 单词
  WordStore = WordStore;

  created(){
    if(this.$sessionSave.get('tbAICorrect') != "1"){
      //如果没有智批权限，跳转至我的题库
      this.$router.push('/home/<USER>/paper');
    }
  }

  beforeDestroy() {
    WordStore.clear();
  }

  // 切换标签
  changeTab(id) {
    if (this.curTabId == id) return;
    this.curTabId = id;
  }

  // 获取英语学科
  getEnglishSubByGradeId(gradeId) {
    gradeId = Number(gradeId) || 7;
    let subjectId = 3;

    // 小学年级
    let primaryGrade = [1, 2, 3, 4, 5, 6];
    // 初中年级
    let juniorHighIds = [7, 8, 9];
    // 高中年级
    let highGradeIds = [10, 11, 12];

    if (primaryGrade.includes(gradeId)) {
      subjectId = 25;
    } else if (juniorHighIds.includes(gradeId)) {
      subjectId = 3;
    } else if (highGradeIds.includes(gradeId)) {
      subjectId = 12;
    }
    return subjectId;
  }

  // 去制卡
  gotoCard() {
    let subjectId = 3;
    if (this.curTabId == 'book') {
      subjectId = this.BookRef.matchSubjectByName();
    } else if (this.curTabId == 'errword') {
      let gradeId = this.ErrWordRef.params.gradeId;
      subjectId = this.getEnglishSubByGradeId(gradeId);
    }
    WordStore.gotoCard({ subjectId });
  }
}
</script>

<style scoped lang="scss">
.english-index {
  display: flex;
  flex-direction: column;
  height: calc(100% - 60px);
  padding: 20px;
  background-color: #fff;
  overflow: auto;
}

.english-index-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid #e9e9e9;

  .english-index-tabs {
    padding: 6px;
    background: #f5f6fa;
    border-radius: 6px;

    .english-index-tab {
      display: inline-block;
      width: 140px;
      height: 34px;
      color: #7f8795;
      font-size: 18px;

      line-height: 34px;
      text-align: center;
      border-radius: 6px;
      cursor: pointer;

      &.active {
        color: #3d9aff;
        background: #ffffff;
        border: 1px solid #e9e9e9;
      }
    }
  }

  .english-index-header-right {
    ::v-deep {
      .el-button {
        width: 80px;
        font-size: 16px;
        border-radius: 6px;
      }
    }
  }
}

.english-index-main {
  flex: 1;
  height: 100%;
  overflow: auto;
}

.count-text {
  color: #2e333b;
  font-size: 16px;
  font-weight: 400;
  margin-right: 20px;

  .count-num {
    color: #3d9aff;
    font-size: 16px;
    font-weight: bold;
  }
}
</style>

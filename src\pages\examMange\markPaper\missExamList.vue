<template>
    <div class="missexam-container">
        <el-page-header @back="back" content="缺考名单"> </el-page-header>
        <div class="miss-list-header">
            <el-select v-model="curSubject" multiple collapse-tags @change="changeSubject" placeholder="请选择学科">
                <el-option v-for="item in allSubjectList" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
            </el-select>
            <el-input placeholder="请搜索姓名或考号" v-model="searchVal" clearable @clear="getList" @keyup.enter.native="getList" style="width: 250px;margin-left: 10px;">
                <el-button slot="append" icon="el-icon-search" @click="getList"></el-button>
            </el-input>
            <el-checkbox v-if="this.curSubject.length > 1" class="error-miss" v-model="isError">异常缺考</el-checkbox>
            <el-button class="export-button" v-if="tableList.length > 0" @click="exportMiss">导出 </el-button>
        </div>
        <div class="miss-list-box" id="miss-list-box" v-if="tableList.length > 0">
            <!--扫描批次数据-->
            <el-table class="custome-table" :data="tableList" v-loading="listLoading" style="width: 100%" stripe>
                <el-table-column v-for="(column, index) in tableColumns" :key="index" :label="column.label"
                    :prop="column.prop">
                    <template slot-scope="scope">
                        <span v-if="scope.row[column.prop]?.error" class="error">{{ scope.row[column.prop].text
                            }}</span>
                        <span v-else>{{ scope.row[column.prop] }}</span>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="nodata flex_1" v-else>
            <img :src="noResImg" alt="" />
            <p class="text-center">暂无数据</p>
        </div>
    </div>
</template>

<script>
import { getMissExamListAPI, getAlreadyScanSubjectList } from '@/service/pexam';
import { getToken } from "@/service/auth"

export default {
    data() {
        return {
            tableList: [],
            tableColumns: [],
            listLoading: true,
            pageInfo: {
                current: 1,
                total: 0,
                pageSize: 50,
            },
            dialogVisible: false,
            noResImg: require('@/assets/no-res.png'),
            curSubject: [],
            allSubjectList: [],
            //是否异常缺考
            isError: true,
            searchVal:""
        };
    },
    watch: {
        isError(newV, oldV) {
            this.searchVal = "";
            this.getList();
        }
    },
    async created() {
        await this.getSubjectInfo();
        this.buildTableColumns();
        this.getList();
    },
    methods: {
        /***
         * @name:返回上一页
         */
        back() {
            this.$router.back();
        },
        async getSubjectInfo() {
            let params = {
                examId: this.$route.query.examId
            }
            let res = await getAlreadyScanSubjectList(params)
            if (res.code == 1) {
                this.allSubjectList = [...res.data]
                this.curSubject = res.data.map((item) => { return item.id })
            }
        },
        buildTableColumns() {
            this.tableColumns = [{ label: '班级', prop: 'className' }, { label: '姓名', prop: 'stuName' }, { label: '考号', prop: 'stuNo' }];
            this.allSubjectList.forEach((item, index) => {
                if (!this.curSubject.length || this.curSubject.includes(item.id)) {
                    this.tableColumns.push({ label: item.name, prop: 'subName' + item.id });
                }
            })
        },
        /**
         * @name:获取列表list
         */
        async getList() {
            this.tableList = [];
            let params = {
                examId: this.$route.query.examId,
                subjectId: this.curSubject.join(','),
                keyWord: this.searchVal
            }
            if (this.curSubject.length > 1) {
                params.isAbnormalMissExam = this.isError ? 1 : 0;
            }
            let res = await getMissExamListAPI(params)
            if (res.code == 1) {
                res.data.list.forEach(item => {
                    let user = { className: item.className, stuName: item.stuName, stuNo: item.stuNo };
                    this.allSubjectList.forEach(subj => {
                        let valTxt = item['subValue' + subj.id];
                        if (valTxt) {
                            if (['未扫描', '缺考'].includes(valTxt)) {
                                user['subName' + subj.id] = {};
                                user['subName' + subj.id].text = item['subValue' + subj.id]
                                user['subName' + subj.id].error = true;
                            } else {
                                user['subName' + subj.id] = item['subValue' + subj.id]
                            }
                        }
                    })
                    this.tableList.push(user)
                })
            }
            this.listLoading = false;
        },
        exportMiss() {
            let url =
                process.env.VUE_APP_KKLURL + `/pexam/exam/exportMissExamList?examId=${this.$route.query.examId}&subjectId=${this.curSubject.join(',')}&token=${getToken()}`;
            if (this.curSubject.length > 1) {
                url = url + '&isAbnormalMissExam=' + (this.isError ? '1' : '0');
            }
            window.open(url);
        },
        changeSubject() {
            this.buildTableColumns()
            this.getList()
        }
    },
};
</script>

<style lang="scss" scoped>
.miss-list-header {
    display: flex;
    align-items: center;
    margin: 5px 0;

    .error-miss,
    .export-button {
        margin-left: 10px;
    }
}

.miss-list-box {
    border: solid #f2f2f2;
    height: calc(100% - 70px);

    .error {
        color: red;
    }
}

.list-pagination {
    padding: 20px;
    margin: auto;
    width: 600px;
}
</style>
<style lang="scss">
.miss-list-box {
    .custome-table {
        .editCell {
            >span {
                color: #409eff;
                display: inline-block;
                margin-right: 10px;
                cursor: pointer;

                &:last-child {
                    margin-right: 0;
                }

                &.disabled {
                    opacity: 0.6;
                    cursor: no-drop;
                }
            }

            .el-icon-more {
                width: 40px;
                text-align: center;
                transform: rotate(90deg);
            }
        }
    }

    .custome-table {
        border: 1px solid #e4e8eb;
    }

    .el-tabs__active-bar {
        display: none;
    }

    .el-tabs__nav-wrap::after {
        display: none;
    }

    .el-tabs__item.is-active {
        font-weight: bold;
    }

    .el-table thead {
        color: #606266;
        font-size: 16px;
    }

    .el-table th,
    .el-table td {
        text-align: center;
    }

    .el-table th.el-table__cell {
        background-color: #f5f7fa !important;
    }

    .el-table th.el-table__cell>.cell {
        padding-left: 20px !important;
        padding-right: 20px !important;
    }

    .el-table .el-table__row .cell {
        padding-left: 20px !important;
        padding-right: 20px !important;
    }

    .el-table {
        .el-button {
            width: 32px;
            height: 32px;
            padding: unset !important;
        }
    }
}
</style>
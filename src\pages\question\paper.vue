<template>
  <div class="paper-container">
    <!--头部筛选项-->
    <div class="header-filter clearfix">
      <!--年级 -->
      <div class="header__select">
        <span class="select__label">年级：</span>
        <el-select v-model="gradeId" class="grade-select" @change="changeGrade" placeholder="请选择">
          <el-option v-for="(item, index) in gradeLists" :key="index" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </div>
      <!--学科-->
      <div class="header__select">
        <span class="select__label">学科：</span>
        <el-select v-model="subjectId" class="grade-select" @change="changeSubject" placeholder="请选择">
          <el-option v-for="(item, index) in subjectList" :key="index" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </div>

      <!--试卷类型 -->
      <div class="header__select">
        <span class="select__label">试卷类型：</span>
        <el-select v-model="typeCode" class="grade-select" @change="changeCategory" placeholder="请选择">
          <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </div>
      <!--搜索-->
      <div class="header__select">
        <div class="header__serarch clearfix display_flex">
          <el-input class="search__text" placeholder="输入试卷名称搜索" v-model="filterData.tbName"
            @keyup.enter.native="searchReport" clearable>
          </el-input>
          <div class="search__icon el-icon-search display_flex align-items_center justify-content_flex-center"
            @click="searchReport"></div>
        </div>
      </div>

      <el-button class="pull-right update-button" :loading="refreshLoading" @click="updateData">刷新
      </el-button>
      <el-button style="background: #409eff" type="primary" class="pull-right" v-if="loginInfo.user_type === 1"
        @click="uploadPaper">上传试卷
      </el-button>
    </div>
    <!--表格列表-->
    <div class="body-content">
      <el-table class="paperTable" v-loading="tableLoading" ref="tableRef" :data="tableData" :row-style="{ padding: 0 }"
        :cell-style="{ position: 'relative' }" stripe @sort-change="sortChange">
        <!--试卷标题-->
        <el-table-column prop="tbName" label="试卷名称" style="position: relative" width="360">
          <template slot-scope="scope">
            <!-- <div
              class="square"
              v-if="scope.row.qualityType && scope.row.qualityType != 0"
            >
              <span class="square-word">精</span>
            </div> -->
            <el-tooltip :content="scope.row.tbName" placement="top">
              <span class="showOverTooltip">{{ scope.row.tbName }} <i v-if="scope.row.needCheckPassword" style="color:#ff0000;" class="el-icon-lock"></i> </span>
            </el-tooltip>
            <!-- <p style="margin-top: 0px">{{ scope.row.tbName }}</p> -->
          </template>
        </el-table-column>
        <!--试题数-->
        <el-table-column sortable="custom" prop="qcount" label="试题数" width="100"></el-table-column>
        <!--学科-->
        <el-table-column sortable="custom" label="学科" prop="subjectName"></el-table-column>
        <!--年级-->
        <el-table-column sortable="custom" label="年级" width="100" prop="gradeName"></el-table-column>
        <!--创建人-->
        <el-table-column sortable="custom" prop="creatorName" label="创建人"></el-table-column>
        <!--创建时间-->
        <el-table-column sortable="custom" prop="createTime" label="创建时间">
          <!-- <template slot-scope="scope">
              <div>{{scope.row.dateCreated.split(' ')[0]}}</div>
          </template> -->
        </el-table-column>
        <!--操作-->
        <el-table-column label="操作" fixed="right" width="200">
          <template slot-scope="scope">
            <div class="editCell display_flex align-items_center justify-content_flex-around">
              <span v-if="scope.row.isUse === 0 && inArr(scope.row.source, [1, 3])" placement="top">
                <span @click="createPaper(scope.row)" :class="{ disabled: scope.row.state == -1 }" :style="{
                  color: scope.row.state == -1 ? 'red' : scope.row.state == 1 ? '#E6A23C' : '',
                }">
                  {{
                    scope.row.state == 2
                    ? '去制卷&nbsp;&nbsp;&nbsp;'
                    : scope.row.state == -1
                      ? '解析失败'
                      : '解析中&nbsp;&nbsp;&nbsp;'
                  }}
                </span>
              </span>
              <span v-if="loginInfo.user_type === 1 && scope.row.isUse === 1" type="text"
                @click="goCreateCard(scope.row)">布置
              </span>
              <span v-if="scope.row.isUse === 1" type="text" @click="goPreview(scope.row)" style="color: #606266">查看
              </span>

              <span @click="downloadPaper(scope.row)" :class="{ disabled: scope.row.dlStatus !== 1 }"
                style="color: #606266">
                <span v-if="scope.row.dlStatus === 1 && inArr(scope.row.source, [1, 3, 5, 15])">
                  下载
                </span>

                <el-tooltip v-else effect="dark" content="试卷正在生成中" placement="top">
                  <p>下载</p>
                </el-tooltip>
              </span>

              <el-dropdown @command="handlerCommand">
                <span class="el-dropdown-link">
                  <i slot="reference" class="el-icon-more"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <!-- <el-dropdown-item
                    :command="'goCreateCard_' + scope.$index"
                    v-show="scope.row.isUse === 1"
                    >去制卡
                  </el-dropdown-item> -->
                  <el-dropdown-item :command="'goEditPaper_' + scope.$index" v-show="scope.row.isUse === 1">编辑
                  </el-dropdown-item>
                  <el-dropdown-item :command="'openEditDialog_' + scope.$index" v-show="scope.row.isUse == 1">修改属性
                  </el-dropdown-item>
                  <el-dropdown-item :command="'delXBResource_' + scope.$index">删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!--分页器-->
      <el-pagination background style="margin: 20px auto 0" :hide-on-single-page="!tableData.length" class="text-center"
        layout="total, prev, pager, next" @current-change="handleCurrentChange" :current-page.sync="pagination.page"
        :page-size="pagination.limit" :total="pagination.total_rows">
      </el-pagination>
    </div>
    <!--下载试卷弹窗-->
    <downloadDialog :downItem="downItem" :list="tableData" v-if="showDialog" @close="closeDialog"></downloadDialog>
    <!--作业发送弹窗-->
    <send-hw-dialog :info="sendHwInfo" v-if="showSendHw" @close-dialog="showSendHw = false"></send-hw-dialog>
    <upload-word-teacher ref="uploadWord" :formVisible="uploadWordShow" :editParamsData="editWordInfo"
      :uploadGradeList="uploadGradeList" :isSupplement="isSupplement" @closeDialog="closeUpload"></upload-word-teacher>
    <!-- 服务到期提示 -->
    <date-expired-dialog :dialogVisible="isShowExpiredDialog" :tips="tips"></date-expired-dialog>
  </div>
</template>

<script>
import { delPaper } from '@/service/pexam';
import { delXBResource, getTBSearchInfo, getTestPaperList,checkTbPassword } from '@/service/testbank';
import { getSchoolById, getUserInfoToPersonalityTest } from '@/service/api';
import { mapGetters } from 'vuex';
import downloadDialog from '@/components/paper/downloadDialog';
import SendHwDialog from '../../components/paper/SendHWDialog';
import { inArr } from '@/utils/common';
import UploadWordTeacher from '@/components/uploadWordTeacher';
import { getToken } from '@/service/auth';
import UserRole from '@/utils/UserRole';
import DateExpiredDialog from '@/components/TestItem/dateExpiredDialog.vue';
import { sleep } from '@/utils/index'

export default {
  name: 'paper',
  data() {
    return {
      uploadWordShow: false,
      editWordInfo: {},
      inArr: inArr,
      tableLoading: false,
      // 表格数据
      tableData: [],
      // 分页
      pagination: {
        page: 1,
        pageSize: 10,
        total_rows: 0,
      },
      // 筛选条件
      filterData: {
        categoryName: '全部',
        year: '',
        tbName: '',
        subjectId: '',
      },
      subjectId: '',
      gradeId: '',
      // 开始计算的年份
      startYear: 2021,
      categoryList: [],
      yearList: [],
      gradeLists: [],
      userPSubMap: {},
      // subjectLists: [],
      sortType: '',
      orderType: '',
      reqData: {
        schoolId: '',
        gradeId: '',
        subjectId: '',
        userId: '',
      },
      showDialog: false,
      downItem: {},
      timer: null,
      timerGap: 5000,
      categoryName: '',
      typeCode: '',
      year: '',
      showSendHw: false,
      sendHwInfo: null,
      cardUrl: process.env.VUE_APP_CARDURL,
      //刷新按钮加载
      refreshLoading: false,
      uploadGradeList: [],
      isShowExpiredDialog: false,
      isSupplement: false
    };
  },
  computed: {
    ...mapGetters(['filterSubjectList', 'subjectMap', 'gradeList', 'loginInfo']),

    phaseId() {
      let phaseId = '';
      if (this.gradeId) {
        phaseId = this.gradeLists.filter(item => {
          return item.id == this.gradeId;
        })[0].phaseId;
      }
      if (!this.gradeId && this.subjectId) {
        let list = this.filterSubjectList.filter(item => {
          return item.id === this.subjectId;
        });
        if (list.length) {
          phaseId = list[0].phaseId;
        }
      }
      return phaseId;
    },
    subjectLists() {
      let allSubjectList = this.$localSave.get('SUBJECT_LIST');
      let tempList = [];
      if (this.loginInfo.multiple_phase && this.loginInfo.multiple_phase != '') {
        tempList = allSubjectList.filter(item => {
          return this.loginInfo.multiple_phase.indexOf(item.phase - 2) >= 0;
        });
      } else if (this.loginInfo.phase && this.loginInfo.phase != '') {
        tempList = allSubjectList.filter(item => {
          return this.loginInfo.phase.indexOf(item.phase - 2) >= 0;
        });
      } else {
        tempList = allSubjectList;
      }
      tempList.unshift({
        id: '',
        name: '全部',
        subjectId: '',
        phaseId: '',
      });
      return tempList;
    },
    subjectList() {
      let subList = [];
      if (this.phaseId && this.gradeId) {
        // let temList = this.userPSubMap[this.gradeId];
        // if (temList) {
        //   if (temList[0].name != "全部") {
        //     temList.unshift({
        //       id: "",
        //       name: "全部",
        //       subjectId: "",
        //       phaseId: "",
        //     });
        //   }

        //   subList = temList;
        // } else {
        // }
        subList = this.subjectLists.filter(item => {
          return item.phaseId === this.phaseId || item.name === '全部';
        });

        this.subjectId = subList[0].id;
      } else {
        subList = this.subjectLists;
      }
      return subList;
    },
    //是否到期
    isExpired() {
      let vipInfo = this.$sessionSave.get('vipInfo');
      return !vipInfo.isVip || !this.$store.getters.examEnabled;
    },

    // 提示
    tips() {
      if (!this.isExpired) return '';
      let vipInfo = this.$sessionSave.get('vipInfo');
      if (!vipInfo.isVip)  return '非常抱歉，贵校服务已到期，请与商务联系进行延期。';
      if (!this.$store.getters.examEnabled) return '非常抱歉，贵校考试次数已达上限。';
    },
  },
  components: {
    UploadWordTeacher,
    SendHwDialog,
    downloadDialog,
    DateExpiredDialog,
  },
  mounted() {
    console.log('loginInfo', this.loginInfo);
    this.yearList = [];
    let now = new Date().getFullYear();
    for (let i = now; i > now - 6; i--) {
      this.yearList.push(i);
    }
    this.yearList.unshift('全部');
    this.filterData.year = '全部';
    this.initSchool();
    this.getSchoolById();
  },
  methods: {
    async checkTbPower(item) {
      return new Promise((resolve, reject) => {
        if(!item.needCheckPassword) return resolve(true);
        let pwdId = 'pwd_' + new Date().getTime();
        const h = this.$createElement;
        this.$msgbox({
          title: '提示',
          message: h('div', null, [
            h('p', null, '该文件已加密，请输入密码后操作 '),
            h('input', {
              attrs: {
                'id': pwdId,
                'placeholder': '请输入密码',
                'type': 'password',
                'autocomplete': 'new-password',
                'autoComplete': 'new-password'
              },
              style: {
                'height': '30px',
                'border-radius': '5px',
                'border': '1px solid gray',
                'outline': '1px',
                'padding': '5px'
              }
            }),
            h('p', null, '发布成绩后自动解密'),
          ]),
          beforeClose: async (action, instance, done) => {
            if (action === 'confirm') {
              const pwd = document.getElementById(pwdId).value;
              let isSuccess = await this.checkTbPassword(pwd, item.id);
              if (isSuccess) {
                document.getElementById(pwdId).value = '';
                resolve(true)
                done();
              }
            } else {
              document.getElementById(pwdId).value = '';
              resolve(false)
              done();
            }
          },
          showCancelButton: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        });
      });
    },
    async checkTbPassword(pwd,id){
      let res = await checkTbPassword({
        tbId:id,
        password:pwd
      }).catch(e=>{
      });
      return res?.code == 1
    },
    uploadPaper() {
      if (this.isExpired) {
        this.isShowExpiredDialog = true;
        setTimeout(() => {
          this.isShowExpiredDialog = false;
        }, 1500);
      } else {
        this.editWordInfo = {};
        this.uploadWordShow = true;
      }
    },
    /**
     * @name：刷新数据
     */
    updateData() {
      this.refreshLoading = true;

      this.filterData.tbName = '';
      this.pagination.page = 1;
      this.getTestPaperList('', 'update');
    },
    closeUpload(success) {
      this.uploadWordShow = false;
      if (success) {
        this.pagination.page = 1;
        this.getTestPaperList();
      }
    },
    /**
     * 布置作业
     */
    sendHomeWork(info) {
      this.sendHwInfo = info;
      this.showSendHw = true;
    },
    closeDialog() {
      this.showDialog = false;
    },
    // 获取登录账号学段
    async getSchoolById() {
      await getSchoolById({
        schoolId: this.$sessionSave.get('schoolInfo').id,
      })
        .then(res => {
          this.accountPhase = res.data.phase;
          this.getTBSearchInfo();
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 初始化不同角色需要传的参数
    initReqData() {
      let loginInfo = this.loginInfo,
        accountType = loginInfo.account_type;
      this.reqData.schoolId = this.$sessionSave.get('schoolInfo').id;
      if (accountType !== 4 && accountType !== 5) {
        this.reqData.gradeId = this.gradeId;
      }
      if (accountType === 1 || accountType === 2) {
        this.reqData.subjectId = this.subjectId;
        this.reqData.userId = accountType === 1 ? loginInfo.id : '';
      }
    },
    // 切换学校后更新数据
    changeSchool() {
      let $this = this;
      this.initSchool(function () {
        $this.getTBSearchInfo();
        $this.getTestPaperList();
      });
    },
    // 点击下载试卷显示下载弹窗
    async downloadPaper(item) {
      if (item.dlStatus !== 1) return;
      let isSuccess = await this.checkTbPower(item)
      if(!isSuccess)return;
      this.downItem = item;
      /*if (this.downItem.source !== 5) {
          this.showDialog = false;
          window.open(`${item.fileUrl}`);
      } else {
      }*/
      this.showDialog = true;
    },
    handlerCommand(command) {
      let commands = command.split('_');
      this[commands[0]](this.tableData[commands[1]]);
    },
    // 点击试卷详情跳到试卷预览页
    async goPreview(item) {
      let isSuccess = await this.checkTbPower(item)
      if(!isSuccess)return;
      this.$router.push({
        path: '/previewDetail',
        query: {
          tbId: item.id,
          schoolId: item.schoolId,
          subId: item.subjectCode,
          userId: this.$sessionSave.get('loginInfo').id,
          // isEdit   : item.userid  ===  this.$sessionSave.get('loginInfo').id,
          year: item.year || '',
          source: item.source,
          dlStatus: item.dlStatus,
          fileUrl: item.fileUrl,
          fromPage: 'paper',
          cardType: item.cardType,
          correctType: item.correctType,
          supportPatchAnswer: item.supportPatchAnswer
        },
      });
    },
    //去制卡
    async goCreateCard(item) {
      let isSuccess = await this.checkTbPower(item)
      if(!isSuccess)return;
      if (this.isExpired) {
        this.isShowExpiredDialog = true;
        setTimeout(() => {
          this.isShowExpiredDialog = false;
        }, 1500);
      } else {
        let token = getToken();
        let routeData = this.cardUrl + `?id=${item.id}&examName=${item.tbName}&token=${token}`;
        window.open(routeData, '_blank');
      }
    },
    /**
     * @name:编辑试卷
     */
    async goEditPaper(item) {
      let isSuccess = await this.checkTbPower(item)
      if(!isSuccess)return;
      let loginInfo = this.$sessionSave.get('loginInfo');
      let url = `/admin/processWordPaper?id=${item.id}&testBankId=${item.id}&subjectId=${item.subjectCode}&phaseId=${item.phase}&type=enter&subjectRealId=${item.subjectCode}&regionType=1&regionId=&stateStr=&chapter=&topicInfo=&userId=${loginInfo.id}&userName=${loginInfo.user_name}&token=${loginInfo.token}&year=${item.year}&quesCategory=&gradeId=${item.gradeCode}&provinceId=&schoolId=${loginInfo.schoolid}&from=tb`;
      window.open(url);
    },
    /**
     * @name:打开修改试卷属性弹窗
     */
    async openEditDialog(item) {
      let isSuccess = await this.checkTbPower(item)
      if(!isSuccess)return;
      // if (item.isRelatedWork == 1) {
      //   this.$message({
      //     message: `答题卡${item.tbName}已关联考试 ，请先解除关联再修改`,
      //     type: 'warning',
      //     duration: '1000',
      //   });
      //   return;
      // }
      this.editWordInfo = item;
      // if (item.supportPatchAnswer == 1) {
      //   //补录试卷
      //   this.isSupplement = true;
      // } else {
      //   this.isSupplement = false;
      // }
      this.isSupplement = false;
      this.uploadWordShow = true;
    },
    // 去制卷
    async createPaper(item) {
      let isSuccess = await this.checkTbPower(item)
      if(!isSuccess)return;
      let loginInfo = this.$sessionSave.get('loginInfo');
      if (item.state == -1) {
        return;
      }
      if (item.state !== 2) {
        this.$message({
          message: '正在解析中！',
          type: 'warning',
        });
        return;
      }
      if (item.state == 2 && item.isUse) {
        this.$emit('updateTab', -1);
        this.$router.push({
          path: '/workBook/previewTestQues',
          query: {
            testBankId: item.id,
            testBankName: item.title,
            subjectId: item.subjectCode,
            phase: item.phase,
          },
        });
        return;
      }
      let routeData = `${process.env.VUE_APP_CUTPAPERURL}cutTestPaper/matchQues.html?id=${item.id}&userid=${loginInfo.id}&url=${process.env.VUE_APP_CUT_URL}&subject=${item.subjectCode}&phase=${item.phase}&token=${loginInfo.token}`;
      window.open(routeData, '_blank');
      await sleep(500);
      await this.$confirm('是否制卷完成，刷新数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      this.getTestPaperList();
    },
    async initSchool(cb) {
      var $this = this;
      const ret = await UserRole.getUserInfoPersonalityTest();
      ret.userSubList.unshift({
        id: '',
        name: '全部',
        subjectId: '',
        phaseId: '',
      });

      // $this.subjectLists = ret.userSubList;
      $this.uploadGradeList = ret.schGrdList.filter(ite => {
        return ite.id != '';
      });
      ret.schGrdList.unshift({
        id: '',
        name: '全部',
        phaseId: '',
      });

      $this.userPSubMap = ret.userPSubMap;
      $this.gradeLists = ret.schGrdList;

      if (cb) {
        cb();
      }
    },
    // 获取表格列表
    getTestPaperList(type, update) {
      let loginInfo = this.$sessionSave.get('loginInfo');
      this.tableLoading = true;
      this.pagination.page = type === 'changePage' ? this.pagination.page : 1;
      let year = this.filterData.year || '';
      if (year === '全部') {
        year = '';
      }
      let sCode = '';
      if (this.subjectId) {
        sCode = this.subjectId;
      } else {
        this.subjectList.forEach(function (v) {
          if (v.name !== '全部') {
            sCode += sCode ? ',' + v.id : v.id;
          }
        });
      }
      let params = {
        type: 0,
        schoolId: this.$sessionSave.get('schoolInfo').id,
        userId: loginInfo.id,
        serviceVersion: 5,
        page: this.pagination.page,
        limit: 10,
        qcount: this.pagination.qcount,
        gradeCode: this.gradeId,
        subjectCode: sCode,
        name: this.filterData.tbName || '',
        typeCode: this.typeCode,
        typeName: this.categoryName,
        sortType: this.sortType,
        sortOrder: this.orderType,
        ...this.filterData,
        year: year,
      };
      getTestPaperList(params)
        .then(data => {
          this.tableLoading = false;
          this.refreshLoading = false;
          this.tableData = data.data.rows || [];
          this.pagination.total_rows = data.data.total_rows;
          if (this.tableData.length && update != 'update') {
            setTimeout(() => {
              this.getPaperState(params);
            }, 3000);
          }
        })
        .catch(err => {
          this.tableLoading = false;
        });
    },
    clearTimer() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
    },
    /**
     * @name:获取试卷转换状态
     */
    async getPaperState(params) {
      const res = await getTestPaperList(params);
      if (res.code == 1 && res.data.rows.length) {
        res.data.rows.forEach(item => {
          this.tableData.forEach(ite => {
            if (item.id == ite.id) {
              this.$set(ite, 'state', item.state);
              this.$set(ite, 'paperNum', item.paperNum);
              this.$set(ite, 'dlStatus', item.dlStatus);
            }
          });
        });
        if (!this.checkPaperState(this.tableData)) {
          this.timer = setTimeout(() => {
            this.getPaperState(params);
          }, this.timerGap);
        } else {
          this.clearTimer();
        }
      }
    },
    /**
     * @name:获取试卷转换状态
     */
    checkPaperState(data) {
      let dataLength = data.length;
      for (let i = 0; i < dataLength; i++) {
        // 转换状态 -1:失败 0:待转换 2:转换成功 3:格式不支持
        let item = data[i];
        if (item.state === 0) {
          return false;
        }
      }
      return true;
    },
    // 获取学科列表
    getCategoryList(c30Filterondition) {
      // 获取题类
      this.categoryList = c30Filterondition;
      this.categoryList.unshift({
        id: '',
        name: '全部',
      });
      setTimeout(() => {
        this.getTestPaperList();
      }, 300);
    },
    // 分页查询
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.clearTimer();
      // this.listPaper('changePage');
      this.getTestPaperList('changePage');
    },
    // 根据gradeId获取gradeName
    getGradeName(id) {
      return this.gradeLists.filter(item => {
        return item.id == id;
      })[0].name;
    },
    // 删除试卷
    delXBResource(item) {
      // if (item.userId !== this.$sessionSave.get('loginInfo').id) {
      //     this.$message.error('您没有权限进行此操作！');
      //     return;
      // }
      this.$confirm('确定删除试卷吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        delXBResource({
          id: item.id,
        })
          .then(data => {
            this.$message.success('删除成功');
            // this.listPaper('changePage');
            this.getTestPaperList('changePage');
          })
          .catch(err => {
          });
      });
    },

    // 删除试卷
    deletePaper(item) {
      if (item.userId !== this.$sessionSave.get('loginInfo').id) {
        this.$message.error('您没有权限进行此操作！');
        return;
      }
      this.$confirm('确定删除试卷吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        delPaper({
          id: item.id,
        }).then(data => {
          this.$message.success('删除成功');
          // this.listPaper('changePage');
          this.getTestPaperList('changePage');
        });
      });
    },
    //排序

    sortChange({ prop, order }) {
      console.log(order);
      if (prop === 'createTime') {
        this.sortType = 0;
      } else if (prop === 'qcount') {
        this.sortType = 1;
      } else if (prop === 'score') {
        this.sortType = 2;
      } else if (prop === 'subjectName') {
        this.sortType = 3;
      } else if (prop === 'gradeName') {
        this.sortType = 4;
      } else if (prop === 'creatorName') {
        this.sortType = 5;
      }
      if (order == 'descending') {
        this.orderType = 'desc';
      } else if (order == 'ascending') {
        this.orderType = 'asc';
      } else {
        this.orderType = '';
      }
      this.getTestPaperList();
    },
    // 切换年级
    changeGrade(val) {
      this.subjectId = '';
      this.getTestPaperList();
    },
    // 切换学科
    changeSubject() {
      this.getTestPaperList();
    },
    // 切换试卷类型
    changeCategory() {
      this.getTestPaperList();
    },
    // 切换年份
    changeYear(info) {
      if (this.forbidOperate) return;
      this.filterData.year = info;
      this.$nextTick(() => {
        this.getTestPaperList();
      });
    },
    getTBSearchInfo() {
      let phase = this.loginInfo.userPhase || this.loginInfo.phase;
      // let phase = 3;
      // if (this.accountPhase) {
      //   phase = Number(this.accountPhase.split(',')[0]);
      // }
      let $this = this;
      getTBSearchInfo({ phase: phase }, function (data) {
        $this.yearList = (data && data.years) || [];
        $this.yearList.unshift('全部');
        $this.filterData.year = '全部';

        let c30 = data.sources;
        // 获取题类
        let otherIndex = -1,
          otherObj = {};
        c30.forEach((item, index) => {
          if (item.name === '其他') {
            otherIndex = index;
            otherObj = item;
          }
        });
        if (otherIndex !== -1) {
          c30.splice(otherIndex, 1);
          c30.push(otherObj);
        }
        $this.getCategoryList(c30);
      });
    },
    // 搜索
    searchReport() {
      this.filterData.tbName = this.filterData.tbName.trim();
      this.pagination.page = 1;
      // this.listPaper();
      this.getTestPaperList('', 'update');
    },
  },
  beforeDestroy() {
    this.clearTimer();
  },
};
</script>

<style lang="scss" scoped>
.paper-container {

  .header-filter,
  .body-content {
    padding: 16px 18px;
    background: #fff;
    // box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
    border-radius: 6px;
  }

  .header-filter {
    .header__select {
      display: inline-block;
      margin-right: 29px;
      float: left;

      .year-select,
      .grade-select,
      .subject-select {
        width: 150px;
      }
    }

    .header__serarch {
      display: flex;
      width: 240px;

      .search__icon {
        width: 38px;
        font-size: 18px;
        color: #fff;
        background: #409eff;
        border-radius: 0 3px 3px 0;
        outline: none;
        cursor: pointer;
      }
    }
  }

  .body-content {
    // margin-top: 20px;
  }

  .update-button {
    margin-left: 20px;
  }
}

.showOverTooltip {
  display: -webkit-box;
  position: relative;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
<style lang="scss">
.downDialog {
  .title {
    font-size: 16px;
    height: 40px;
    line-height: 40px;
  }

  .set-ul {
    margin-bottom: 20px;

    >li {
      display: inline-block;
      font-size: 16px;
      text-align: center;
      background-color: #f4f4f5;
      color: #909399;
      border: 1px solid #f4f4f5;
      border-radius: 4px;
      margin-right: 30px;
      cursor: pointer;

      &.active {
        color: #409eff;
        background: #ecf5ff;
        border: 1px solid #b3d8ff;
      }
    }
  }

  .paperSize-ul {
    >li {
      min-width: 90px;
      height: 40px;
      line-height: 40px;
    }
  }

  .paperType-ul {
    >li {
      width: 145px;
      height: 60px;
      line-height: 60px;
      padding: 10px 0;
      text-align: center;

      &:last-child {
        margin-right: 0;
      }

      .user {
        line-height: 20px;
        color: #3f4a54;
      }

      .type {
        line-height: 16px;
        font-size: 13px;
        margin-top: 3px;
      }

      &.active {
        .user {
          color: #409eff;
        }

        .type {
          color: #909399;
        }
      }
    }
  }
}

.search__text {
  .el-input__inner {
    border-radius: 4px 0 0 4px;
  }
}

.paperTable {
  .editCell {
    >span {
      color: #409eff;
      display: inline-block;
      margin-right: 20px;
      cursor: pointer;

      &:last-child {
        margin-right: 0;
      }

      &.disabled {
        opacity: 0.6;
        cursor: no-drop;
      }

      >span {
        &.disabled {
          opacity: 0.6;
          cursor: no-drop;
        }
      }
    }

    .el-icon-more {
      width: 40px;
      text-align: center;
      transform: rotate(90deg);
    }
  }
}

.paperTable {
  border: 1px solid #e4e8eb;

  &.el-table th,
  &.el-table td {
    text-align: center;
  }

  &.el-table th.el-table__cell {
    background-color: #f5f7fa !important;
  }

  &.el-table thead {
    font-size: 16px;
    color: rgb(63, 74, 84);
  }

  .el-table td,
  .el-table th.is-leaf {
    border: 0.5px solid #ebeef5;
  }

  .square {
    width: 0;
    height: 0;
    border: 16px solid transparent;
    border-top: 16px solid #409eff;
    border-left: 16px solid #409eff;
    z-index: 100;
    border-radius: 10px 0 0 0;
    position: absolute;
    left: 0;
    top: 0;

    .square-word {
      position: absolute;
      left: -12px;
      top: -16px;
      color: #ffffff;
      font-size: 13px;
    }
  }
}

.editPopover {
  width: 100px !important;
  min-width: initial;
  margin-top: 0 !important;

  .edit-ul {
    >li {
      cursor: pointer;

      &.disabled {
        opacity: 0.6;
        cursor: no-drop;
      }
    }
  }
}

// .cell{
//     width: 212px;
// }
.editCell {
  width: 192px;
}
</style>

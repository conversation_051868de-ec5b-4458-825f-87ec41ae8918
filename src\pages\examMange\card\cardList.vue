<template>
  <div class="examReport__main flex_1 display_flex flex-direction_column">
    <div
      class="examReport__content flex_1"
      :class="examReportList.length ? '' : 'display_flex align-items_center'"
    >
      <!--个册列表数据-->
      <ul
        id="popoverUl"
        class="examReport__list list-none"
        v-if="examReportList.length"
        v-loading="loading"
        element-loading-text="加载中..."
      >
        <li v-for="(item, index) in examReportList" :key="index">
          <task-item :item="item" :index="index"></task-item>
        </li>
      </ul>
      <!--没有个册数据缺省图-->
      <div class="nodata flex_1" v-if="!loading && !examReportList.length">
        <img :src="noResImg" alt="" />
        <p class="text-center">暂无数据</p>
      </div>
      <!--分页器-->
      <el-pagination
        background
        style="margin-bottom: 30px"
        :hide-on-single-page="!examReportList.length"
        class="text-center"
        layout="total, prev, pager, next"
        @current-change="handleCurrentChange"
        :current-page.sync="pagination.page"
        :page-size="pagination.limit"
        :total="pagination.total_rows"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import TaskItem from "@/components/TaskItem/index.vue";
import { getExamReportList } from "@/service/pexam";
export default {
  name: "mark-paper-index",
  data() {
    return {
      // 需要阅卷的考试列表
      examReportList: [],
      // 加载中
      loading: false,
      // 分页
      pagination: {
        page: 1,
        limit: 10,
        total_rows: 0,
      },
      noResImg: require("../../../assets/no-res.png"),
    };
  },
  components: {
    TaskItem,
  },
  computed: {
    ...mapGetters([]),
  },
  created() {},
  mounted() {
    this.getExamReportList();
  },
  methods: {
    /**
     * @name: 获取考试报告列表
     */
    getExamReportList() {
      this.loading = true;
      getExamReportList({
        schoolId: this.$sessionSave.get("schoolInfo").id,
        acadYearsId: "",
        acadTermId: "",
        gradeId: "",
        subjectId: "",
        categoryId: "",
        keyWord: "",
        page: this.pagination.page, // 页码
        pageSize: this.pagination.limit,
      })
        .then((res) => {
          let testBankIds = [];
          this.examReportList = res.data.list;
          this.examReportList.forEach((item) => {
            item.isActive = this.isActive == item.examId ? true : false;
            item.paperList.forEach((it) => {
              if (it.testBankId != "") {
                testBankIds.push(it.testBankId);
              }
            });
            if (item.dataState >= 1 && item.statState != 1) this.getInitData(item.examId);
          });
          this.pagination.total_rows = res.data.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 分页查询
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getExamReportList("changePage");
    },
  },
};
</script>

<style lang="scss" scoped>
.examReport__main {
  width: 100%;
  margin-top: 20px;
  padding: 10px 20px 20px;
  background-color: #fff;
  font-size: 16px;
  overflow: auto;

  .examReport__content {
    width: 100%;

    .examReport__list {
      width: 100%;
    }

    .nodata {
      width: 100%;
      height: auto;

      img {
        display: block;
        margin: 0 auto;
      }
    }
  }
  .nodata {
    width: 100%;
    height: auto;

    img {
      display: block;
      margin: 0 auto;
    }
  }
}
</style>
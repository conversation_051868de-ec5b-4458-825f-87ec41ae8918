<!--
 * @Description: 
 * @Author: l<PERSON><PERSON><PERSON> y<PERSON>@class30.com
 * @Date: 2024-05-11 09:35:26
 * @LastEditors: liu<PERSON><PERSON> y<PERSON><PERSON>@class30.com
 * @LastEditTime: 2024-05-23 20:27:12
-->
<template>
  <div class="detaile-container">
    <div class="title">{{ title }}</div>
    <div class="content" v-html="content"></div>
  </div>
</template>

<script>
import { getHelpArticleAPI } from '@/service/phelp';
export default {
  name: 'Detail',
  data() {
    return {
      title: '',
      content: ''
    }
  },
  created() {
    this.getHelpArticle()
  },
  methods: {
    async getHelpArticle() {
      let res = await getHelpArticleAPI({
        id: this.$route.query.id
      }).catch(err => {
      })
      this.title = res.data.title;
      this.content = res.data.content;
    }
  }
}
</script>

<style scoped lang="scss">
.detaile-container{
    padding: 20px;
    background: #fff;
  .title{
    font-size: 24px;
    text-align: center;
  }
  .content{
    margin-top: 10px;
  }
}
</style>
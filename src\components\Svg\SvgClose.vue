<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-06-11 17:20:31
 * @LastEditors: 小圆
-->
<template>
  <svg
    t="1722223846232"
    class="icon online-fill-svg"
    viewBox="0 0 552.22856 552.22858"
    version="1.1"
    p-id="2345"
    width="15.1"
    height="15.1"
    id="svg1"
    sodipodi:docname="cuohao1.svg"
    inkscape:version="1.4.2 (f4327f4, 2025-05-13)"
    xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
    xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:svg="http://www.w3.org/2000/svg"
  >
    <defs id="defs1" />
    <sodipodi:namedview
      id="namedview1"
      pagecolor="#ffffff"
      bordercolor="#000000"
      borderopacity="0.25"
      inkscape:showpageshadow="2"
      inkscape:pageopacity="0.0"
      inkscape:pagecheckerboard="0"
      inkscape:deskcolor="#d1d1d1"
      inkscape:zoom="28.821429"
      inkscape:cx="14"
      inkscape:cy="11.224287"
      inkscape:window-width="1920"
      inkscape:window-height="1009"
      inkscape:window-x="1912"
      inkscape:window-y="-8"
      inkscape:window-maximized="1"
      inkscape:current-layer="svg1"
      showgrid="false"
    />
    <path
      d="M 552.22856,42.767701 509.46086,0 276.11428,234.09689 42.767701,0 0,42.767701 234.09689,276.11428 0,509.46086 42.767701,552.22856 276.11428,318.88198 509.46086,552.22856 552.22856,509.46086 318.88198,276.11428 Z"
      p-id="2346"
      id="path1"
      style="stroke-width: 0.937888"
    />
  </svg>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';

@Component
export default class SvgClose extends Vue {}
</script>

<style scoped></style>

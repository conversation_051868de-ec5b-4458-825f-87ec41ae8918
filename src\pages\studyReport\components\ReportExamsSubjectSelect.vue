<!--
 * @Description: 多个考试筛选时，学科选择
 * @Author: 小圆
 * @Date: 2024-03-25 10:18:45
 * @LastEditors: 小圆
-->
<template>
  <div class="header-item">
    <span class="header-item__label">科目：</span>
    <el-select
      class="header-item__select short-select"
      value-key="id"
      :value="examsFilterInfo.subjectInfo"
      @change="onChange"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <el-option
        v-for="item in subjectList"
        :key="item.id"
        :title="item.name"
        :label="item.name"
        :value="item"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import ReportComponent from './ReportComponent';
import { IScoreSubjectInfo } from '@/pages/studyReport/plugins/types';

@Component
export default class ReportExamsSubjectSelect extends ReportComponent {
  get examsSelectOption() {
    return this.FilterModule.examsSelectOption;
  }

  get examsFilterInfo() {
    return this.FilterModule.examsFilterInfo;
  }

  get examsFilterData() {
    return this.FilterModule.examsFilterData;
  }

  get subjectList() {
    const gradeMap = this.examsSelectOption.gradeMap;
    const grade = this.examsFilterData.gradeId;
    return gradeMap[grade] || [];
  }

  onChange(value: IScoreSubjectInfo) {
    this.FilterModule.setExamsSubjectInfo(value);
    this.FilterModule.trigger('changeFilter');
  }
}
</script>

<style scoped lang="scss">
@import './reportSelect.scss';
</style>

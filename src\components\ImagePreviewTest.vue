<template>
  <div class="test-container">
    <h2>图片预览功能测试</h2>
    <el-button @click="openDialog" type="primary">打开答题卡设置</el-button>
    
    <create-third-card-dialog
      :dialogVisible="dialogVisible"
      @close-dialog="closeDialog"
      @confirm-create="handleConfirm"
    />
  </div>
</template>

<script>
import CreateThirdCardDialog from '@/pages/examMange/answerSetting/modules/createThirdCardDialog.vue'

export default {
  name: 'ImagePreviewTest',
  components: {
    CreateThirdCardDialog
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  methods: {
    openDialog() {
      this.dialogVisible = true
    },
    closeDialog() {
      this.dialogVisible = false
    },
    handleConfirm(data) {
      console.log('确认创建:', data)
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
}
</style>

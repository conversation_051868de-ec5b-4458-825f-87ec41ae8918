<template>
    <div>
        <dt-modal
                :modalVisible="formVisible"
                title="导入成绩"
                :modalWidth="700"
                :sureBtnLoading="sureBtnLoading"
                :disabledCancelBtn="disabledCancelBtn"
                @click-sure="addSubmit"
                @click-cancel="cancel"
        >
            <template slot="customSlot">
                <div class="quesCard-upload-div">
                    <div class="upload-main">
                        <div class="upload-main-top">
                            <el-dialog
                                    title="下载Excel模板"
                                    :visible.sync="dialogVisible"
                                    :show-close="false"
                                    append-to-body
                                    width="30%">
                                <div v-for="(item, index) in subjectList" :key="index" style="margin-bottom:15px;">
                                    输入{{item.subName}}学科题目数量：
                                    <NumericInput
                                            class="numberComponent"
                                            v-model="item.quesCount"
                                            :type="'digit'"
                                            :isPositive="true"
                                            :placeholder="'请输入题目数量'"
                                    ></NumericInput>
                                </div>

                                <span slot="footer" class="dialog-footer">
                                    <el-button @click="dialogVisible = false">取消</el-button>
                                    <el-button type="primary" @click="sureUpload">下载</el-button>
                                </span>
                            </el-dialog>
                            <span class="download-paper" @click="dialogVisible = true">下载成绩excel模板</span>
                            <span>必须按照模板上传~</span>
                        </div>
                        <Upload
                                ref="uploadFile"
                                :action="uploadUrl"
                                :max-size="102400"
                                :accept="coverAccept"
                                :format="['xls', 'xlsx']"
                                :show-upload-list="false"
                                :on-success="uploadSuccess"
                                :before-upload="beforeUpload"
                                :on-format-error="handleFormat"
                                :on-exceeded-size="handleMaxSize"
                                :data="uploadData">
                            <div class="upload-main-search" style="position: relative">
                                <span type="button" id="upload-file" style="z-index: 1">
                                    选择文件
                                  <label class="file-name">
                                    <p class="limit-info" v-if="formItem.tbName == ''">
                                      请选择100M以内的文档上传（xlsx）
                                    </p>
                                    <p class="limit-info" v-else>{{ formItem.tbName }}</p>
                                  </label>
                                </span>
                            </div>
                        </Upload>
                        <el-dialog
                                title="错误描述"
                                :visible.sync="errorDialogVisible"
                                :show-close="false"
                                append-to-body
                                width="30%">
                            <div v-for="(item, index) in errorList" :key="index" style="margin-bottom:15px;">
                                <div class="display_flex align-items_flex-start" style="margin-bottom: 10px;color: #fe6c6f">
                                    <div class="order">{{index+1}}、</div>
                                    <p class="flex_1">{{item}}</p>
                                </div>
                            </div>

                            <span slot="footer" class="dialog-footer">
                                    <el-button @click="errorDialogVisible = false">关闭</el-button>
                                </span>
                        </el-dialog>
                    </div>
                </div>
            </template>
        </dt-modal>
    </div>
</template>

<script>
    import {getToken} from "@/service/auth"
    import DtModal from '../ui/dt-modal'
    import NumericInput from './NumericInput';
    import {getImportState} from '@/service/pexam'

    export default {
        name: "upload-excel",
        components: {
            DtModal,
            NumericInput
        },
        props: ['formVisible', 'excellData'],
        data() {
            return {
                errorDialogVisible: false,
                message: '',
                placeholder: '请输入题目数量',
                dialogVisible: false,
                disabledCancelBtn: false,
                sureBtnLoading: false,
                formItem: {
                    tbName: '',//word名称
                },
                coverAccept: "*",
                uploadUrl: process.env.VUE_APP_KKLURL + "/pexam/exam/importStuResult",//excel上传地址
                uploadData: {},
                testBankInfo: null,
                timer: null,
                subjectList: [],
                errorList: []
            }
        },
        mounted(){
            this.uploadData = {
                schoolId: this.excellData.schoolId,
                gradeId: this.excellData.gradeId,
                examId: this.excellData.examId,
                token: getToken(),
            };
            let subNameList = this.excellData.subjectName.split('/');
            subNameList.forEach((subItem, subIndex)=>{
                this.subjectList.push({
                    subName: subItem,
                    quesCount: ''
                })
            })
        },
        computed: {
            excelTemplateUrl() {
                return process.env.VUE_APP_KKLURL
                    + `/pexam/exam/getTemplate?token=${getToken()}&examId=${this.excellData.examId}&subQuesInfo=${encodeURI(JSON.stringify(this.subjectList))}`
            }
        },
        methods: {
            sureUpload(){
                let count = 0;
                for(let i=0;i<this.subjectList.length;i++){
                    let numberVal = this.subjectList[i].quesCount;
                    if(numberVal && Number(numberVal)>0){
                        count++;
                        if(count === this.subjectList.length){
                            this.dialogVisible = false;
                            window.open(this.excelTemplateUrl);
                        }
                    }else{
                        this.message = '题目数量需为大于0的整数值';
                    }
                }
                this.message && this.$Message.error('题目数量需为大于0的整数值');
            },
            /**
             * excel上传前
             */
            beforeUpload(file) {
                this.errorList = [];
                console.log('beforeUploadfile-->', file)
                this.file = file
                this.formItem.tbName = file.name
                return false
            },
            /**
             * 上传成功回调
             */
            uploadSuccess(response, file, fileList) {
                console.log(response, file, fileList)
                if (response.code != 1) {
                    this.$Message.error(`${response.msg}`)
                } else if (response.code == 1) {
                    this.getImportState();
                    this.timer = setInterval(()=>{
                        this.getImportState();
                    }, 4000);
                }
                this.disabledCancelBtn = false;
            },
            getImportState(){
                getImportState({
                    examId: this.excellData.examId
                }).then(res=>{
                    // 如果返回为null,则继续请求分析接口，若部位null,获取state的值判断分析是否成功
                    if(res.data.length){
                        clearInterval(this.timer);
                        this.sureBtnLoading = false;
                        if(res.data[0].state === 1){
                            //分析成功
                            this.$message({
                                message: '导入成绩成功！',
                                type: 'success',
                                duration: 1500
                            })
                            this.cancel('success');
                        }else if(res.data[0].state === -1){
                            // todo 提示导入失败
                            res.data.forEach(item=>{
                                this.errorList.push(item.errorInfo);
                            });
                            this.errorDialogVisible = true;
                            this.$nextTick(()=>{
                                this.$refs.uploadFile.clearFiles()
                            })
                            // this.$message.error(res.data[0].errorInfo)
                        }
                    }
                }).catch(err=>{
                    console.log('=========>', err);
                })
            },
            /**
             * excel文件太大的提示
             */
            handleMaxSize(file) {
                this.$Message.error('文件' + file.name + '太大，不允许超过100M')
            },
            /**
             * 上传的文件格式验证失败的提示
             */
            handleFormat(file) {
                this.$Message.error('文件' + file.name + '格式不支持，请选择xls、xlsx格式的文档')
            },
            /**
             * 确定保存
             */
            addSubmit() {
                if (!this.file) {
                    this.$message({
                        message: '请上传文件！',
                        type: 'warning',
                        duration: 1500
                    })
                    return
                }
                this.disabledCancelBtn = true
                this.sureBtnLoading = true
                this.uploadData.pid = this.personBookId
                this.$nextTick(() => {
                    this.$refs.uploadFile.post(this.file);
                })
            },
            /**
             * 取消
             */
            cancel(type) {
                this.$refs.formItem && this.$refs.formItem.resetFields()
                this.formItem.tbName = ''
                this.testBankInfo = null

                this.$refs.uploadFile.clearFiles()
                //关闭弹窗
                this.$emit('closeDialog', type==='success')
            }
        },
        beforeDestroy:function() {
            //关闭窗口后清除定时器
            clearInterval(this.timer);
        }
    }
</script>

<style lang="scss" scoped>

</style>
<style lang="css" scoped>
    @import "../styles/questionCard/upload.scss";
</style>
<style lang="scss">
    .numberComponent{
        .numeric-input-inner{
            text-align: left!important;
            padding-left: 10px!important;
        }
    }
    .upload-dialog .el-dialog__body {
        padding: 0 20px 50px;
    }

    .quesCard-upload-div .ivu-upload-select {
        display: unset !important;
    }

    .quesCard-upload-div .ivu-upload {
        margin-bottom: 10px !important;
    }

    .quesCard-upload-div .ivu-form-item {
        margin-bottom: 10px !important;
    }

    .quesCard-upload-div .ivu-form-item-content {
        line-height: 34px !important;
        font-size: 16px !important;
    }

    .quesCard-upload-div .ivu-select-selected-value {
        font-size: 16px !important;
    }

    .quesCard-upload-div .ivu-select-item {
        font-size: 16px !important;
    }
    .upload-main {
        .append_div {
            padding-bottom: 10px;

            .tips {
                color: rgba(165, 172, 189, 1);
                font-size: 13px;
            }
        }
    }
</style>

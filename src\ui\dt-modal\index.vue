<template>
    <Modal
            :value="modalVisible"
            :mask-closable="false"
            :closable="false"
            :width="modalWidth"
            :title="title"
            :footer-hide="!isShowFooter"
            :fullscreen="fullscreenState">
        <h3 slot="header" class="modal-header">
            {{title}}
            <span class="header-right">
                <!-- <i class="dt-modal-full-screen-icon"
                   :class="fullscreenState ? 'dt-modal-full-screen-small-icon':'dt-modal-full-screen-big-icon'"
                   :title="fullscreenState ? '点击取消全屏':'点击全屏'"
                   v-on:click="closeFullscreenState(!fullscreenState)"></i> -->
                <i class="dt-modal-close-icon" v-on:click="closeModal"></i>
            </span>
        </h3>
        <slot name="customSlot"></slot>
        <div slot="footer" class="content-footer">
            <template v-if="!isCustomFooter">
                <Button type="default" @click="closeModal" :disabled="disabledCancelBtn">取消</Button>
                <Button type="primary" @click="sureClick" :loading="sureBtnLoading">确认</Button>
            </template>
            <template v-else>
                <slot name="customFooterSlot"></slot>
            </template>
        </div>
    </Modal>

</template>

<script>

    export default {
        name: 'dt-modal',
        props: {
            //是否显示弹框
            modalVisible: {
                type: Boolean,
                default: false
            },
            //是否显示底部按钮
            isShowFooter: {
                type: Boolean,
                default: true
            },
            //弹框标题
            title: {
                type: String,
                default: ""
            },
            //弹框宽度
            modalWidth: {
                type: Number,
                default: 500
            },
            //确定按钮的load
            sureBtnLoading: {
                type: Boolean,
                default: false
            },
            //弹框是否全屏-通过参数默认
            fullscreen: {
                type: Boolean,
                default: false
            },
            //是否自定义底部按钮
            isCustomFooter: {
                type: Boolean,
                default: false
            },
            //禁用取消按钮
            disabledCancelBtn: {
                type: Boolean,
                default: false
            }
        },
        watch:{
            'modalVisible':'initModalData'
        },
        data () {
            return {
                fullscreenState: false,//弹框是否全屏-通过点击事件改变
            };
        },
        methods: {
            /**
             * 初始化弹框参数
             */
            initModalData(){
                if(this.modalVisible){
                    this.fullscreenState = this.fullscreen;
                }
            },
            /**
             * 关闭弹框
             */
            closeModal () {
                this.$emit("click-cancel");
            },
            /**
             * 点击确认
             */
            sureClick () {
                this.$emit("click-sure");
            },
            /**
             * 改变全屏状态
             */
            closeFullscreenState(state){
                this.fullscreenState=state;
            }
        }
    };
</script>

<style lang="scss" scoped>
    $main-font-normal-color:#545454;//用于未选中的默认颜色
    .modal-header {
        font-size : 18px;
        color     : $main-font-normal-color;
        .header-right {
            float   : right;
            cursor  : pointer;
            display : inline-flex;
        }
    }

    .dt-modal-close-icon {
        width      : 14px;
        height     : 14px;
        cursor     : pointer;
        background : url("./assets/error.png") no-repeat;
        display    : block;
        margin-top : 2px;
    }

    .dt-modal-full-screen-icon{
        width      : 16px;
        height     : 16px;
        cursor     : pointer;
        display    : block;
        margin-top : 2px;
        margin-right: 20px;
    }
    .dt-modal-full-screen-small-icon{
        background : url("./assets/full_screen_small.png") no-repeat;
    }

    .dt-modal-full-screen-big-icon{
        background : url("./assets/full_screen_big.png") no-repeat;
    }
</style>
<style lang="scss">
    .ivu-modal-header {
        background : #fafafa;
    }
</style>

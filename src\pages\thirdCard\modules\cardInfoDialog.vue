<template>
  <el-dialog custom-class="card-info-dialog" :visible="modalVisible" width="70%" :before-close="handleClose"
    :modal-append-to-body="false" :close-on-click-modal="false">
    <div slot="title" class="dialog-title">
      <span style="margin-right: 10px">答题卡信息</span>
    </div>
    <div class="card-info-container">
      <div class="card-info">
        <div class="base-info">
          <strong>基本信息</strong>
          <el-form ref="baseInfoForm" label-width="80px">
            <el-form-item label="定位信息" v-if="pointList.length > 0">
              <div class="column-list">
                <span v-for="(item, index) in pointList" :key="index">
                  第{{ index + 1 }}面
                  <span v-if="!item?.[0]?.pos_list?.length" class="point-num">
                    未框选
                  </span>
                  <template v-else>
                    <span v-if="item?.[0]?.pos_list?.length == 4">{{ (item?.[0]?.pos_list?.length || 0) + '个' }}</span>
                    <span v-else class="point-num">{{ (item?.[0]?.pos_list?.length || 0) + '个' }}</span>
                  </template>
                </span>
              </div>

            </el-form-item>
            <el-form-item label="标题" v-if="titlePos.length > 0">
              <div class="column-list">
                <span v-for="(item, index) in titlePos" :key="index">
                  第{{ index + 1 }}面
                  <span v-if="item && item.length"> 已框选 </span>
                  <span v-else class="point-num">未框选</span>
                </span>
              </div>
            </el-form-item>
            <el-form-item label="页码标记" v-if="pageCount > 2">
              <span>
                第一张
                <span v-if="(pagePos[0] && pagePos[0].length) || (pagePos[1] && pagePos[1].length)"> 已框选 </span>
                <span v-else class="point-num">未框选</span>
              </span>
              ,
              <span>
                第二张
                <span v-if="(pagePos[2] && pagePos[2].length) || (pagePos[3] && pagePos[3].length)"> 已框选 </span>
                <span v-else class="point-num">未框选</span>
              </span>
            </el-form-item>

            <el-form-item label="考号" v-if="examNoList.length > 0">
              <template v-if="pageCount > 2">
                <span>第一张 - 填涂
                  <span v-if="examNoList[0] && examNoList[0].length">已框选</span>
                  <span class="point-num" v-else>未框选</span>
                </span>，
                <span>条形码
                  <span v-if="barcodeExamNoList[0] && barcodeExamNoList[0].length">已框选</span>
                  <span class="point-num" v-else>未框选</span>
                </span> 
                <br>
                <span>第二张 - 填涂
                  <span v-if="examNoList[2] && examNoList[2].length">已框选</span>
                  <span class="point-num" v-else>未框选</span>
                </span>，
                <span>条形码
                  <span v-if="barcodeExamNoList[2] && barcodeExamNoList[2].length">已框选</span>
                  <span class="point-num" v-else>未框选</span>
                </span>
              </template>
              <template v-else>
                <span>填涂
                <span v-if="examNoList[0] && examNoList[0].length">已框选</span>
                <span class="point-num" v-else>未框选</span>
              </span>，
              <span>条形码
                <span v-if="barcodeExamNoList[0] && barcodeExamNoList[0].length">已框选</span>
                <span class="point-num" v-else>未框选</span>
              </span>
              </template>
            </el-form-item>

            <el-form-item label="缺考标记" v-if="missMarkPos.length > 0">
              <span v-if="missMarkPos[0] && missMarkPos[0].length">已框选</span>
              <span class="point-num" v-else>未框选</span>
            </el-form-item>

            <el-form-item label="AB卷" v-if="abCardType == IAB_CARD_TYPE.abPaper && abCardPos.length > 0">
              <template v-if="pageCount > 2">
                <span>第一张
                  <span v-if="abCardPos[0] && abCardPos[0].length == 2">已框选</span>
                  <span v-else-if="abCardPos[0] && abCardPos[0].length == 0" class="point-num">未框选</span>
                  <span class="point-num" v-else>框选异常</span>
                </span>，
                <br>
                <span>第二张
                  <span v-if="abCardPos[2] && abCardPos[2].length == 2">已框选</span>
                  <span v-else-if="abCardPos[2] && abCardPos[2].length == 0" class="point-num">未框选</span>
                  <span class="point-num" v-else>框选异常</span>
                </span>
              </template>
              <template v-else>
                <span v-if="abCardPos[0] && abCardPos[0].length == 2">已框选</span>
                <span v-else-if="abCardPos[0] && abCardPos[0].length == 0" class="point-num">未框选</span>
                <span class="point-num" v-else>框选异常</span>
              </template>
            </el-form-item>
          </el-form>
        </div>
        <div class="ques-info">
          <strong>题目信息</strong>
          <el-form ref="quesInfoForm" label-width="80px">
            <el-form-item label="客观题">共{{ objectQuesNums }}题</el-form-item>
            <el-form-item label="主观题">共{{ subjectQuesNums }}题</el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeModal">取 消</el-button>
      <el-button type="primary" @click="sureClick">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { IAB_CARD_TYPE } from '@/typings/card';
export default {
  props: {
    modalVisible: {
      type: Boolean,
      required: false,
    },
    //主观题
    subjectQues: {
      type: Array,
      default() {
        return [];
      },
    },
    //当前页
    currPage: {
      type: Number,
      default() {
        return 0;
      },
    },
    //客观题
    objectQues: {
      type: Array,
      default() {
        return [];
      },
    },
    //总页数
    pageCount: {
      type: Number,
      default() {
        return 0;
      },
    },
    //ab卡类型
    abCardType: {
      type: Number,
      default() {
        return IAB_CARD_TYPE.default;
      },
    },
    //定位点
    pointList: {
      type: Array,
      default() {
        return [];
      },
    },
    //考号
    examNoList: {
      type: Array,
      default() {
        return [];
      },
    },
    //条形码
    barcodeExamNoList: {
      type: Array,
      default() {
        return [];
      },
    },
    //缺考
    missMarkPos: {
      type: Array,
      default() {
        return [];
      },
    },
    //标题
    titlePos: {
      type: Array,
      default() {
        return [];
      },
    },
    //AB卷
    abCardPos: {
      type: Array,
      default() {
        return [];
      },
    },
    pagePos: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      IAB_CARD_TYPE,
      subjectQuesNums: 0,
      objectQuesNums: 0,
    };
  },
  watch: {
    modalVisible(newVal, oldVal) {
      this.handleQuesNum();
    },
  },
  computed: {
    //框选是否完成
    isComplete() {
      for (let i = 0; i < this.pageCount; i++) {
        if (this.pointList[i]?.[0]?.pos_list.length != 4) {
          return { code: 0, message: '定位点框选异常' };
        }
        if (this.titlePos[i]?.length == 0) {
          return { code: 0, message: '标题框选异常' };
        }
      }
      if (this.pageCount > 2 && ((!this.pagePos[0]?.length && !this.pagePos[1]?.length) || (!this.pagePos[2]?.length && !this.pagePos[3]?.length))) {
        return { code: 0, message: '页码标记框选异常' };
      }
      if (this.abCardPos[0]?.length != 2 && this.abCardType == IAB_CARD_TYPE.abPaper) {
        return { code: 0, message: 'AB标记框选异常' };
      }
      if (this.pageCount > 2 && this.abCardPos[0]?.length == 2 && this.abCardPos[2]?.length != 2) {
        return { code: 0, message: '第二张AB标记框选异常' };
      }
      if (this.examNoList[0]?.length == 0 && this.barcodeExamNoList[0]?.length == 0) {
        return { code: 0, message: '考号框选异常' };
      }
      if (this.pageCount > 2 && this.examNoList[2]?.length == 0 && this.barcodeExamNoList[2]?.length == 0) {
        return { code: 0, message: '第二张考号框选异常' };
      }
      return { code: 1, message: '框选完成' };
    },
  },
  created() {
    this.handleQuesNum();
  },
  methods: {
    /**
     * 计算题目数量
     */
    handleQuesNum() {
      this.subjectQuesNums = 0;
      this.objectQuesNums = 0;
      for (let i = 0; i < this.pageCount; i++) {
        this.subjectQuesNums += this.subjectQues[i].filter(item => {
          //过滤单题多框
          return !item.isSplitQues
        }).length;
        this.objectQuesNums += this.objectQues[i].length;
      }
    },
    /**
     * 校验题目名称不能为空
    */
    checkQuesNameNotNull() {
      let nullCount = 0;
      for (let i = 0; i < this.pageCount; i++) {
        nullCount += this.subjectQues[i].filter(item => {
          return item.question_nos == ""
        }).length;
        nullCount += this.objectQues[i].filter(item => {
          return item.question_nos == ""
        }).length;
      }
      return nullCount > 0;
    },
    // 关闭弹窗
    handleClose(done) {
      this.$emit('close-card-info');
      done();
    },
    /**
     * 取消
     */
    closeModal() {
      this.$emit('close-card-info');
    },
    sureClick() {
      let result = this.isComplete;
      if (result.code == 0) {
        this.$message({
          message: "框选未完成，" + result.message,
          type: 'warning',
        });
        return;
      }
      if (this.checkQuesNameNotNull()) {
        this.$message({
          message: '题号不能为空，请确认!',
          type: 'warning',
        });
        return;
      }

      this.$emit('close-card-info');
      this.$emit('confirm-card-info');
    },
  },
};
</script>

<style lang="scss" scopeed>
.card-info-container {
  display: flex;
  flex-direction: column;

  .card-info {
    display: flex;

    .base-info {
      width: 50%;
    }
  }

  .score-container {
    margin-top: 20px;
  }

  .point-num {
    color: #f5222d;
    font-weight: bold;
  }

  .right-ques-list {
    height: calc(100% - 36px);
    font-size: 18px;
    overflow: auto;
  }

  .right-ques-hand {
    height: 40px !important;
    text-align: center;
    line-height: 40px !important;
  }

  .big-ques {
    .small-ques-row {
      margin-bottom: 5px;
    }
  }
}
</style>
<style lang="scss">
.card-info-dialog {
  .el-dialog__header {
    height: 45px;

    .dialog-title {
      line-height: 45px;
    }
  }
}
</style>
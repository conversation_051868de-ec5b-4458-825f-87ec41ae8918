<template>
    <div class="publish-setting">
        <bread-crumbs :title="'成绩发布'">
            <template slot="titleSlot"> </template>
        </bread-crumbs>
        <el-card class="publish-setting__card box-card" shadow="hover">
            <div class="publish-setting__header" slot="header">
                <div style="flex: 1">
                    <span class="exam-name">考试：{{ reportDetail.examName }}</span>
                </div>
                <el-button
                    v-if="isShowPublishDingTalk"
                    class="publish-setting__dingtalk"
                    type="text"
                    @click="isDialogVisible = true"
                    >发布成绩到钉钉</el-button
                >
            </div>
            <div class="publish-setting__all">
                请选择各学科要发布成绩的角色（被勾选的角色将会发布成绩）
                <el-checkbox v-model="isAllStudent" @change="val => changeAllCheck([allStudentRole.value], val)"
                    >全选学生</el-checkbox
                >
                <el-checkbox
                    v-model="isAllTeacher"
                    @change="val => changeAllCheck([allTeacherRole.value, ...allTeacherRoles.map(t => t.value)], val)"
                    >全选老师</el-checkbox
                >
            </div>

            <div class="subject-ul">
                <div v-for="item in examSubjectList" :key="item.id" class="subject-li">
                    <span class="name">{{ item.name }}</span>
                    <el-checkbox-group class="roles" v-model="item.roles" @change="judgeAllCheck">
                        <el-checkbox :label="allStudentRole.value">{{ allStudentRole.label }}</el-checkbox>
                        <el-checkbox
                            :label="allTeacherRole.value"
                            @change="val => changeSubjectAllTeacherRoleCheck(item, val)"
                            >{{ allTeacherRole.label }}</el-checkbox
                        >
                        <el-checkbox v-for="t in allTeacherRoles" :key="t.value" :label="t.value">{{
                            t.label
                        }}</el-checkbox>
                    </el-checkbox-group>
                </div>
            </div>

            <div class="publish-setting__footer">
                <div class="publish-setting__tip">
                    <div class="tip">提示：</div>
                    <div>
                        <p>1、被勾选的学科和角色代表发布成绩，不勾选的学科和角色代表成绩屏蔽；</p>
                        <p>2、全部老师包含所有的校领导、年级主任、学科主任、备课组长、班主任和任课老师；</p>
                        <p>3、全不选时，发布成绩后学情所有角色不可见</p>
                    </div>
                </div>

                <div class="flex_1 text-right">
                    <el-button class="set-btn" type="primary" @click="saveSetting">保存设置</el-button>
                </div>
            </div>
        </el-card>
        <el-dialog title="发布成绩到钉钉" :visible.sync="isDialogVisible" width="30%">
            <div class="dialog-content">
                <p class="dialog-title">
                    确定将<span class="exam-name">《{{ reportDetail.examName }}》</span>成绩推送到钉钉班级群吗？
                </p>
                <p class="dialog-desc">推送后相关任教老师和学生/家长均可查看成绩，且不可删除。</p>
                <div class="dialog-tip">
                    <i class="el-icon-warning-outline"></i>

                    <span>温馨提示：建议完成全部扫描和批改后再发布成绩至钉钉。为避免打扰，建议在07:00-22:00进行消息推送，其他时间推送消息将被屏蔽。</span>
                </div>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="isDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="publishToDingTalk" :loading="isPublishLoading">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import BreadCrumbs from '@/components/Breadcrumbs/index.vue';
import { getExamSubject, publishSchoolReportAPI, setExamRole } from '@/service/pexam';
import { getPublicConfigBySchoolInfo } from '@/service/api';

interface IExamSubject {
    id: string;
    name: string;
    phaseId: number;
    xfId: number;
    xkId: number;
    progress: number;
    progressState: number;
    code: null;
    status: number;
    personalBookId: string;
    workId: null;
    testBankId: string;
    paperNo: string;
    fullScore: number;
    markingPapersProgress: number;
    roles: number[]; // -1:所有学生 -2:所有老师 1:校领导 2:年级主任 3:学科主任 4:备课组长 5:班主任 6:任课教师
}

@Component({
    components: {
        BreadCrumbs,
    },
})
export default class PublishSetting extends Vue {
    // 所有学生
    allStudentRole = {
        label: '所有学生',
        value: -1,
    };

    // 所有老师
    allTeacherRole = {
        label: '所有老师',
        value: -2,
    };
    // 老师角色列表
    allTeacherRoles = [
        {
            label: '校领导',
            value: 1,
        },
        {
            label: '年级主任',
            value: 2,
        },
        {
            label: '学科主任',
            value: 3,
        },
        {
            label: '备课组长',
            value: 4,
        },
        {
            label: '班主任',
            value: 5,
        },
        {
            label: '任课教师',
            value: 6,
        },
    ];

    // 是否全选老师
    isAllTeacher: boolean = false;
    // 是否全选学生
    isAllStudent: boolean = false;
    // 学科列表
    examSubjectList: IExamSubject[] = [];
    // 是否显示发布成绩到钉钉按钮
    isShowPublishDingTalk: boolean = false;
    // 是否显示发布成绩到钉钉对话框
    isDialogVisible: boolean = false;
    // 发布成绩到钉钉loading
    isPublishLoading: boolean = false;


    // 考试信息
    get reportDetail(): IExamReportInfo {
        return this.$sessionSave.get('reportDetail');
    }

    mounted() {
        this.getExamSubject();
        this.getDingTalkConfig();
    }

    // 获取考试学科列表
    async getExamSubject() {
        try {
            const res = await getExamSubject({
                examId: this.reportDetail.examId,
            });
            this.examSubjectList = res.data;
            this.judgeAllCheck();
        } catch (error) {
            console.error(error);
            this.$message.warning(error.msg);
        }
    }

    // 获取钉钉权限
    async getDingTalkConfig() {
        try {
            const res = await getPublicConfigBySchoolInfo({
                schoolId: this.$sessionSave.get('schoolInfo').id,
                userId: this.$sessionSave.get('loginInfo').id,
                dictCode: '139',
            });
            res.data.forEach(item => {
                // 推送大精成绩到钉钉
                if (item.dictCode == '139') {
                    this.isShowPublishDingTalk = item.state == '1';
                }
            });
        } catch (error) {
            console.error(error);
        }
    }

    // 改变所有学科权限
    changeAllCheck(allRoles: number[], all) {
        // 添加所有学科权限
        if (all) {
            this.examSubjectList.forEach(item => {
                let set = new Set(item.roles);
                allRoles.forEach(role => {
                    set.add(role);
                });
                item.roles = Array.from(set);
            });
        } else {
            // 添加所有学科权限
            this.examSubjectList.forEach(item => {
                item.roles = item.roles.filter(role => !allRoles.includes(role));
            });
        }
    }

    // 改变学科全部教师权限
    changeSubjectAllTeacherRoleCheck(list: IExamSubject, all) {
        // 添加当前学科全部教师权限
        if (all) {
            let set = new Set(list.roles);
            set.add(this.allTeacherRole.value);
            this.allTeacherRoles.forEach(role => {
                set.add(role.value);
            });
            list.roles = Array.from(set);
        } else {
            // 删除当前学科全部教师权限
            let set = new Set(list.roles);
            set.delete(this.allTeacherRole.value);
            this.allTeacherRoles.forEach(role => {
                set.delete(role.value);
            });
            list.roles = Array.from(set);
        }
    }

    // 判断当前学科老师、学生是否全选
    judgeAllCheck() {
        let isAllStudent = true;
        let isAllTeacher = true;
        this.examSubjectList.forEach(item => {
            if (!item.roles.includes(this.allStudentRole.value)) isAllStudent = false;
            if (!this.allTeacherRoles.map(t => t.value).every(t => item.roles.includes(t))) {
                isAllTeacher = false;
                let set = new Set(item.roles);
                set.delete(this.allTeacherRole.value);
                item.roles = Array.from(set);
            } else {
                let set = new Set(item.roles);
                set.add(this.allTeacherRole.value);
                item.roles = Array.from(set);
            }
        });

        this.isAllStudent = isAllStudent;
        this.isAllTeacher = isAllTeacher;
    }

    // 保存设置
    async saveSetting() {
        try {
            let vos = [];
            this.examSubjectList.forEach(item => {
                vos.push({
                    examId: this.reportDetail.examId,
                    bookId: item.personalBookId,
                    roles: item.roles,
                });
            });
            await setExamRole(vos);

            this.$message({
                type: 'success',
                message: '设置成功',
                offset: 60,
            });
        } catch (error) {
            console.error(error);
            this.$message({
                type: 'warning',
                message: error.msg,
                offset: 60,
            });
        }
    }

    // 发布成绩到钉钉
    async publishToDingTalk() {
        this.isPublishLoading = true;
        try {
            await publishSchoolReportAPI({
                examId: this.reportDetail.examId,
                optUserId: this.$sessionSave.get('loginInfo').id,
            });
            this.$message({
                type: 'success',
                message: '发布成功',
                offset: 60,
            });
            this.isDialogVisible = false;
        } catch (error) {
            console.error(error);
            this.$message({
                type: 'warning',
                message: error.msg,
                offset: 60,
            });
        } finally {
            this.isPublishLoading = false;
        }
    }
}
</script>

<style scoped lang="scss">
$containerWidth: 1000px;
$padding: 20px;

::v-deep {
    .el-checkbox__label {
        font-size: 15px;
        padding-left: 8px;
    }

    .el-checkbox__inner {
        width: 16px;
        height: 16px;
        &::after {
            top: 0;
            width: 5px;
            height: 9px;
            border-width: 2px;
        }
    }
}

.publish-setting {
    display: flex;
    flex-direction: column;
    font-size: 14px;
    line-height: 1.5;

    .publish-setting__card {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: auto;

        ::v-deep {
            .el-card__body {
                display: flex;
                flex-direction: column;
                flex: 1;
                overflow: hidden;
                padding: 0;
            }
        }
    }

    .publish-setting__header {
        display: flex;
        align-items: center;

        .exam-name {
            font-size: 16px;
            font-weight: 700;
        }

        .publish-setting__dingtalk {
            font-size: 15px;
        }
    }

    .publish-setting__all {
        padding: $padding;
        padding-bottom: 10px;
        font-weight: 400;
        font-size: 16px;
        color: #333333;
    }

    .publish-setting__footer {
        display: flex;
        align-items: center;
        padding: $padding;
        border-top: 1px solid #ebeef5;
    }

    .publish-setting__tip {
        display: flex;
        font-size: 16px;
        color: #333333;
        line-height: 24px;

        .tip {
            color: #409eff;
            font-weight: 700;
        }
    }
}

.subject-ul {
    flex: 1;
    height: 100%;
    overflow: auto;
    width: 100%;
    padding: $padding;
    padding-top: 0;
    .subject-li {
        display: flex;
        padding: 10px;
        padding-left: 25px;
        line-height: 28px;
        border-bottom: 1px solid #f6f6f6;

        &:hover {
            background-color: #edf4ff;
        }

        &:first-child {
            border-top: 1px solid #f6f6f6;
            margin-top: 0;
        }

        &:nth-child(odd) {
        }

        &:nth-child(even) {
            // background-color: #f2f2f2;
        }

        .name {
            min-width: 120px;
            max-width: 120px;
            text-align: left;
            font-weight: 700;
            font-size: 16px;
            margin-right: 20px;
        }

        .roles {
            font-size: 0;
            display: flex;
            flex: 1;
            justify-content: space-around;
        }
    }
}

.dialog-content {
    .dialog-title {
        font-size: 15px;
        font-weight: 500;
        line-height: 1.5;
        margin-bottom: 10px;

        .exam-name {
            font-weight: 700;
            color: #333;
        }
    }

    .dialog-desc {
        font-size: 15px;
        color: #666;
        margin-bottom: 10px;
        line-height: 1.5;
    }

    .dialog-tip {
        display: flex;
        align-items: center;
        background: #fff9ed;
        padding: 12px;
        border-radius: 4px;

        i {
            color: #e6a23c;
            font-size: 16px;
            margin-right: 8px;
        }

        span {
            color: #666;
            font-size: 14px;
            line-height: 1.4;
        }
    }
}

.dialog-footer {
    text-align: right;
    padding-top: 10px;
}
</style>

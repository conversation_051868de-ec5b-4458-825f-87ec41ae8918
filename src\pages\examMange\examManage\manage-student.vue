<template>
  <div class="student-container">
    <div class="student-main">
      <div class="student-title">
        <div class="action-buttons">
          <button class="action-btn update-btn" @click="refreshStuInfo">
            <i class="el-icon-refresh"></i>
            <span>更新考生信息</span>
            <el-popover placement="bottom" width="380" trigger="hover" popper-class="info-popover">
              <p>参考班级无变化，班级内学生有变化时点此更新</p>
              <i class="el-icon-question icon-help" slot="reference"></i>
            </el-popover>
          </button>
          <button class="action-btn import-btn" @click="openImportStuDialog">
            <i class="el-icon-s-custom"></i>
            <span>学生批量导入</span>
          </button>
          <button class="action-btn temp-btn" @click="openImportTempDialog">
            <i class="el-icon-upload2"></i>
            <span>导入临时考号</span>
          </button>
        </div>
        <el-button class="handle-student record-btn" type="primary" @click="openRecordList">操作记录 ></el-button>
      </div>
      <!-- 列表 -->
      <div class="student-table">
        <el-table :data="subjectData" class="exam-ques-table">
            <el-table-column align="center" label="学科" prop="subjectName" width="130" show-overflow-tooltip>
            </el-table-column>
            <el-table-column align="center" prop="classNames" label="参考班级">
              <template slot-scope="scope">
                <el-tooltip :content="scope.row.classNames" placement="top">
                  <span class="showOverTooltip">{{ scope.row.classNames }} </span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column align="center" label="参考学生" prop="num" width="180"> </el-table-column>
            <el-table-column align="center" label="操作" width="250">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click="openClassDialog(scope.row)"
                  >参考班级管理</el-button
                >
                <el-button
                  type="text"
                  @click="openStuDialog(scope.row)"
                  >参考学生管理</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <set-class-dialog
        v-if="isShowClass"
        :modalVisible="isShowClass"
        :subjectInfo="subjectInfo"
        @close-set-class="closeSetDialog"
        @confirm-set-class="confirmSet"
      >
      </set-class-dialog>
      <set-student-dialog
        v-if="isShowStudent"
        :modalVisible="isShowStudent"
        :subjectInfo="subjectInfo"
        @close-set-stu="closeSetDialog"
        @confirm-set-stu="confirmSet">
        </set-student-dialog>
        <import-temp-stu-dialog
        v-if="isShowImportTemp"
        @close="isShowImportTemp=false">
        </import-temp-stu-dialog>
        <import-stu-dialog
        v-if="isShowImportStu"
        @close="closeImportStuDialog">
        </import-stu-dialog>
    </div>
  </template>
  
  <script>
  import { getExamSubjectList, saveExamSubjectInfo } from '@/service/pexam';
  import BreadCrumbs from '@/components/Breadcrumbs/index.vue';
  import { getQueryString } from '@/utils';
  import { updateExamHomeWorkInfo } from '@/service/api';
  import SetClassDialog from './modules/setClassDialog.vue';
  import SetStudentDialog from './modules/setStudentDialog.vue';
  import ImportTempStuDialog from './modules/importTempStuDialog.vue';
  import ImportStuDialog from './modules/importStuDialog.vue';
  
  export default {
    data() {
      return {
        examId: getQueryString('examId') || '',
        title: getQueryString('examName') || '考生管理',
        source: getQueryString('source') || '',
        //参考学科、班级数据
        subjectData: [],
        elTableHeight: 'calc(100%)',
        isShowStudent: false,
        isShowClass: false,
        isShowImportStu: false,
        isShowImportTemp: false,
        subjectInfo: {},
        year: '',
      };
    },
    components: {
      BreadCrumbs,
      SetClassDialog,
      SetStudentDialog,
      ImportTempStuDialog,
      ImportStuDialog
    },
    created() {
      this.year = getQueryString('year');
    },
    mounted() {
      this.getExamSubjectList();
    },
    methods: {
      /**
       * @name:获取考试学科
       */
      getExamSubjectList() {
        getExamSubjectList({
          examId: this.examId,
        })
          .then(res => {
            // console.log(res.data);
            this.subjectData = res.data;
          })
          .catch(err => {
            this.subjectData = [];
          });
      },
      /**
       * @name:打开设置学生弹窗
       */
      openClassDialog(item) {
        this.subjectInfo = item;
        this.subjectInfo.year = this.year;
        this.isShowClass = true;
      },
      openStuDialog(item){
        this.subjectInfo = item;
        this.subjectInfo.year = this.year;
        this.isShowStudent = true;
      },
      /**
       * @name:关闭设置考生弹窗
       */
      closeSetDialog() {
        this.isShowClass = false;
        this.isShowStudent = false;
        this.getExamSubjectList();
      },
      /**
       * @name:确定选择考生
       */
       confirmSet() {
        this.closeSetDialog()
        this.getExamSubjectList();
      },
      /**
       * @name:导入临时考号
       */
      openImportTempDialog(){
        this.isShowImportTemp = true;
      },
      /**
       * @name:学生批量导入
       */
      openImportStuDialog(){
        this.isShowImportStu = true;
      },
      closeImportStuDialog(isSuccess){
        this.isShowImportStu = false;
        if(isSuccess){
          this.openRecordList();
        }
      },
      /**
       * @name:操作记录
       */
      openRecordList(){
        this.$router.push({
          path: '/home/<USER>',
          query: {
            examId: this.examId,
            examName: this.title,
          }
        });
      },
      /**
       * @name:更新考试作业师生关系信息
       */
      async refreshStuInfo() {
        this.$confirm(
          `更新后，当前考试参考班级的考生信息会被后台最新的考生信息所覆盖，是否继续？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        ).then(async () => {
          let res = await updateExamHomeWorkInfo({ examId: this.examId }).catch(err => {
          });
          if (res.code == 1) {
            this.$message({
              message: '更新成功！',
              type: 'success',
              duration: 1000,
            });
          } else {
            this.$message({
              message: '更新失败',
              type: 'error',
              duration: 1000,
            });
          }
        });
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .showOverTooltip {
    display: -webkit-box;
    position: relative;
    text-overflow: ellipsis;
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .student-main {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .student-title {
    font-size: 18px;
    font-weight: bold;
    color: #161e26;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .action-buttons {
      display: flex;
      gap: 15px;
    }
    
    .action-btn {
      display: inline-flex;
      align-items: center;
      padding: 8px 16px;
      border-radius: 4px;
      border: none;
      background-color: #f5f7fa;
      color: #606266;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      
      i {
        margin-right: 6px;
        font-size: 16px;
      }
      
      &:hover {
        background-color: #ecf5ff;
        color: #409EFF;
        transform: translateY(-2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      
      &.update-btn {
        border-left: 3px solid #c23a3a;
        
        &:hover {
          background-color: #f9ebeb;
          color: #c23a3a;
        }
        
        .icon-help {
          margin-left: 6px;
        }
      }
      
      &.import-btn {
        border-left: 3px solid #409EFF;
        
        &:hover {
          background-color: #ecf5ff;
          color: #409EFF;
        }
      }
      
      &.temp-btn {
        border-left: 3px solid #E6A23C;
        
        &:hover {
          background-color: #fdf6ec;
          color: #E6A23C;
        }
      }
    }
    
    .record-btn {
      font-weight: normal;
      padding: 9px 16px;
    }
  }
  </style>
  <style lang="scss">
  .info-popover {
    .el-popover__title {
      color: #409EFF;
      font-weight: bold;
    }
    
    p {
      margin: 0;
      line-height: 1.5;
      color: #606266;
    }
  }

  .student-table {
    .el-table thead {
      color: #606266;
    }
  
    .el-table th.el-table__cell {
      background-color: #f5f7fa !important;
    }
  
    .el-table th.el-table__cell > .cell {
      padding-left: 20px !important;
      padding-right: 20px !important;
    }
  
    .el-table .el-table__cell {
      padding: 8px 0 !important;
    }
  
    .el-table .el-table__row .cell {
      padding-left: 20px !important;
      padding-right: 20px !important;
      line-height: 35px !important;
    }
    .el-table__header-wrapper .el-checkbox {
      display: none;
    }
  }
  </style>
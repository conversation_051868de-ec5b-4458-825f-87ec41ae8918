<template>
  <div
    class="image-viewer-container"
    ref="container"
    @mousedown="startDrag"
    @mousemove="onDrag"
    @mouseup="endDrag"
    @mouseleave="endDrag"
    @wheel="handleWheel"
    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove"
    @touchend="handleTouchEnd"
    @touchcancel="handleTouchEnd"
  >
    <div
      class="image-viewer-canvas"
      ref="canvas"
      :style="{
        transform: `translate(${translateX}px, ${translateY}px) scale(${scale})`,
        transition: animate ? (isDragging ? 'none' : 'transform 0.3s') : 'none',
      }"
    >
      <slot></slot>
    </div>
    <div class="zoom-controls" @mousedown.stop @touchstart.stop>
      <slot name="toolLeft"></slot>

      <div class="icon-imageTools icon-zoomin" @click="zoomIn()"></div>
      <div class="icon-imageTools icon-zoomout" @click="zoomOut()"></div>
      <div class="icon-imageTools icon-recover" @click="resetZoom()"></div>

      <slot name="toolRight"></slot>

      <!-- <el-button type="primary" icon="el-icon-zoom-in" circle @click="zoomIn"></el-button> -->
      <!-- <el-button type="primary" icon="el-icon-zoom-out" circle @click="zoomOut"></el-button> -->
      <!-- <el-button type="primary" icon="el-icon-refresh" circle @click="resetZoom"></el-button> -->
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Ref } from 'vue-property-decorator';

/**
 * 图片查看器组件
 *
 * 支持功能：
 * 1. 缩放功能 - 通过鼠标滚轮(按住Ctrl)、触摸手势或按钮
 * 2. 平移功能 - 通过鼠标拖动、触摸滑动或滚轮
 * 3. 精确的缩放中心点计算 - 确保缩放以鼠标位置或触摸中心为基准点
 * 4. 边界限制 - 防止内容超出可视区域
 * 5. 支持触摸设备 - 适配移动端操作
 */
@Component
export default class ImageViewer extends Vue {
  @Ref('container') container: HTMLDivElement;
  @Prop({ default: 0.1 }) readonly zoomStepProp!: number;
  @Prop({ default: 0.1 }) readonly minScaleProp!: number;
  @Prop({ default: 3.0 }) readonly maxScaleProp!: number;
  @Prop({ default: 100 }) readonly scrollSpeed!: number; // 滚动速度

  // 拖动和缩放相关属性
  scale: number = 1;
  translateX: number = 0;
  translateY: number = 0;
  isDragging: boolean = false;
  startX: number = 0;
  startY: number = 0;
  lastTranslateX: number = 0;
  lastTranslateY: number = 0;
  // 是否开启动画
  animate: boolean = true;

  // 触摸相关属性
  initialPinchDistance: number = 0;
  lastScale: number = 1;
  initialTouchX: number = 0;
  initialTouchY: number = 0;
  touchZoomCenter = { x: 0, y: 0 };

  // 组件挂载后的初始化
  mounted() {
    // 组件挂载后，延迟一帧执行，确保DOM已渲染
    this.$nextTick(() => {
      // 初始化时不应用居中逻辑，保持初始位置
      this.calcBoundary();
    });
  }

  get zoomStep(): number {
    return this.zoomStepProp;
  }

  get minScale(): number {
    return this.minScaleProp;
  }

  get maxScale(): number {
    return this.maxScaleProp;
  }

  // 开始拖动
  startDrag(event: MouseEvent) {
    // 检查是否点击了控制按钮
    if (this.isControlsElement(event.target as HTMLElement)) {
      return;
    }

    this.isDragging = true;
    this.startX = event.clientX;
    this.startY = event.clientY;
    this.lastTranslateX = this.translateX;
    this.lastTranslateY = this.translateY;
  }

  // 检查元素是否在控制按钮范围内
  isControlsElement(element: HTMLElement): boolean {
    const controlsElement = this.$el.querySelector('.zoom-controls');
    return controlsElement?.contains(element) || false;
  }

  // 拖动中
  onDrag(event: MouseEvent) {
    if (!this.isDragging) return;
    event.preventDefault();

    const deltaX = event.clientX - this.startX;
    const deltaY = event.clientY - this.startY;

    // 计算新的位置
    this.translateX = this.lastTranslateX + deltaX;
    this.translateY = this.lastTranslateY + deltaY;

    // 应用边界限制
    this.calcBoundary();
  }

  // 结束拖动
  endDrag() {
    if (this.isDragging) {
      this.isDragging = false;
      // 确保拖动结束时也应用边界限制
      this.calcBoundary();
    }
  }

  // 处理触摸开始事件
  handleTouchStart(event: TouchEvent) {
    event.preventDefault();

    // 检查是否点击了控制按钮
    if (this.isControlsElement(event.target as HTMLElement)) {
      return;
    }

    if (event.touches.length === 1) {
      // 单指触摸 - 移动
      this.handleSingleTouchStart(event);
    } else if (event.touches.length === 2) {
      // 双指触摸 - 缩放
      this.handlePinchStart(event);
    }
  }

  // 处理触摸移动事件
  handleTouchMove(event: TouchEvent) {
    event.preventDefault();

    if (event.touches.length === 1 && this.isDragging) {
      // 单指触摸移动
      this.handleSingleTouchMove(event);
    } else if (event.touches.length === 2) {
      // 双指触摸缩放
      this.handlePinchMove(event);
    }
  }

  // 处理触摸结束事件
  handleTouchEnd(event: TouchEvent) {
    this.isDragging = false;
    this.initialPinchDistance = 0;
    // 确保触摸结束时应用边界限制
    this.calcBoundary();
  }

  // 处理单指触摸开始
  handleSingleTouchStart(event: TouchEvent) {
    this.isDragging = true;
    this.startX = event.touches[0].clientX;
    this.startY = event.touches[0].clientY;
    this.lastTranslateX = this.translateX;
    this.lastTranslateY = this.translateY;
  }

  // 处理单指触摸移动
  handleSingleTouchMove(event: TouchEvent) {
    const touch = event.touches[0];
    const deltaX = touch.clientX - this.startX;
    const deltaY = touch.clientY - this.startY;

    // 计算新的位置
    this.translateX = this.lastTranslateX + deltaX;
    this.translateY = this.lastTranslateY + deltaY;

    // 应用边界限制
    this.calcBoundary();
  }

  // 处理双指缩放开始
  handlePinchStart(event: TouchEvent) {
    const touch1 = event.touches[0];
    const touch2 = event.touches[1];

    // 计算两个触摸点之间的距离
    this.initialPinchDistance = this.getPinchDistance(touch1, touch2);
    this.lastScale = this.scale;

    // 计算两个触摸点的中心点
    const centerX = (touch1.clientX + touch2.clientX) / 2;
    const centerY = (touch1.clientY + touch2.clientY) / 2;

    // 获取容器的位置
    const containerRect = (this.$refs.container as HTMLElement).getBoundingClientRect();

    // 计算触摸中心点相对于容器的位置
    this.touchZoomCenter.x = centerX - containerRect.left;
    this.touchZoomCenter.y = centerY - containerRect.top;

    // 计算触摸中心点在未缩放canvas上的相对位置
    // 这里需要考虑当前的缩放比例和偏移量
    this.initialTouchX = this.touchZoomCenter.x / this.scale - this.translateX;
    this.initialTouchY = this.touchZoomCenter.y / this.scale - this.translateY;
  }

  // 处理双指缩放移动
  handlePinchMove(event: TouchEvent) {
    const touch1 = event.touches[0];
    const touch2 = event.touches[1];

    // 计算当前两个触摸点之间的距离
    const currentDistance = this.getPinchDistance(touch1, touch2);

    if (this.initialPinchDistance <= 0) {
      this.initialPinchDistance = currentDistance;
      return;
    }

    // 计算缩放比例
    const pinchRatio = currentDistance / this.initialPinchDistance;
    const newScale = Math.max(this.minScale, Math.min(this.maxScale, this.lastScale * pinchRatio));

    // 计算两个触摸点的中心点
    const centerX = (touch1.clientX + touch2.clientX) / 2;
    const centerY = (touch1.clientY + touch2.clientY) / 2;

    // 获取容器的位置
    const containerRect = (this.$refs.container as HTMLElement).getBoundingClientRect();

    // 计算触摸中心点相对于容器的位置
    const touchCenterX = centerX - containerRect.left;
    const touchCenterY = centerY - containerRect.top;

    // 计算缩放前后的中心点偏移
    const oldCenterXInCanvas = (touchCenterX - this.translateX) / this.scale;
    const oldCenterYInCanvas = (touchCenterY - this.translateY) / this.scale;

    // 更新缩放
    this.scale = newScale;

    // 更新位置，保持触摸中心点不变
    const newCenterXInCanvas = (touchCenterX - this.translateX) / this.scale;
    const newCenterYInCanvas = (touchCenterY - this.translateY) / this.scale;

    this.translateX += (newCenterXInCanvas - oldCenterXInCanvas) * this.scale;
    this.translateY += (newCenterYInCanvas - oldCenterYInCanvas) * this.scale;

    // 应用边界限制
    // this.calcBoundary();
  }

  // 计算两个触摸点之间的距离
  getPinchDistance(touch1: Touch, touch2: Touch): number {
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    return Math.sqrt(dx * dx + dy * dy);
  }

  // 处理滚轮事件
  handleWheel(event: WheelEvent) {
    event.preventDefault(); // 阻止默认滚动

    // 如果按住Ctrl键，则执行缩放
    if (event.ctrlKey) {
      this.handleZoomWheel(event);
    } else {
      // 否则执行上下移动
      this.handleScroll(event);
    }
  }

  // 处理普通滚动
  handleScroll(event: WheelEvent) {
    // 向下滚动时deltaY为正，所以我们需要反向应用移动
    const moveDistance = (event.deltaY > 0 ? 1 : -1) * this.scrollSpeed;
    this.translateY -= moveDistance;
    this.calcBoundary();
  }

  // 计算并应用边界限制
  calcBoundary() {
    // 确保有内容并且容器存在
    if (!this.$slots.default || !this.$slots.default[0] || !this.container) return;

    const element = this.$slots.default[0].elm as HTMLDivElement;
    const containerElement = this.container;

    // 获取元素和容器的尺寸信息
    const contentRect = element.getBoundingClientRect();
    const containerRect = containerElement.getBoundingClientRect();

    // 计算缩放后的内容尺寸
    const scaledWidth = contentRect.width;
    const scaledHeight = contentRect.height;

    // 计算容器尺寸
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;

    // 水平方向边界限制
    // 不再居中显示，允许自由拖动
    // 只有当内容宽度大于容器宽度时才限制范围
    if (scaledWidth > containerWidth) {
      const minTranslateX = containerWidth - scaledWidth;
      const maxTranslateX = 0;

      if (this.translateX < minTranslateX) {
        this.translateX = minTranslateX;
      } else if (this.translateX > maxTranslateX) {
        this.translateX = maxTranslateX;
      }
    } else {
      const minTranslateX = 0;
      const maxTranslateX = containerWidth - scaledWidth;

      if (this.translateX < minTranslateX) {
        this.translateX = minTranslateX;
      } else if (this.translateX > maxTranslateX) {
        this.translateX = maxTranslateX;
      }
    }

    // 垂直方向边界限制
    // 不再居中显示，允许自由拖动
    // 只有当内容高度大于容器高度时才限制范围
    if (scaledHeight > containerHeight) {
      const minTranslateY = containerHeight - scaledHeight;
      const maxTranslateY = 0;

      if (this.translateY < minTranslateY) {
        this.translateY = minTranslateY;
      } else if (this.translateY > maxTranslateY) {
        this.translateY = maxTranslateY;
      }
    } else {
      const minTranslateY = 0;
      const maxTranslateY = containerHeight - scaledHeight;

      if (this.translateY < minTranslateY) {
        this.translateY = minTranslateY;
      } else if (this.translateY > maxTranslateY) {
        this.translateY = maxTranslateY;
      }
    }
  }

  // 处理缩放
  handleZoomWheel(event: WheelEvent) {
    // 确定缩放方向和比例，根据deltaY的大小调整缩放步长
    // 使用较小的缩放步长让缩放更平滑
    const zoomFactor = 0.05; // 基础缩放因子
    const delta =
      Math.abs(event.deltaY) < 50
        ? event.deltaY > 0
          ? -zoomFactor
          : zoomFactor
        : event.deltaY > 0
        ? -this.zoomStep
        : this.zoomStep;

    // 获取当前canvas元素和容器
    const containerRect = (this.$refs.container as HTMLElement).getBoundingClientRect();

    // 计算鼠标指针相对于容器的位置
    const mouseX = event.clientX - containerRect.left;
    const mouseY = event.clientY - containerRect.top;

    // 计算鼠标在canvas上的相对位置
    const canvasX = (mouseX - this.translateX) / this.scale;
    const canvasY = (mouseY - this.translateY) / this.scale;

    // 计算新的缩放值，确保平滑过渡
    const newScale = Math.max(this.minScale, Math.min(this.maxScale, this.scale * (1 + delta)));

    if (newScale !== this.scale) {
      // 计算缩放前后的偏移量
      const scaleChange = newScale / this.scale;

      // 更新缩放
      this.scale = newScale;

      // 计算新的位置，保持鼠标下的点不变
      // 当缩放比例改变时，需要调整translate，使鼠标位置下的点保持不变
      this.translateX = mouseX - canvasX * this.scale;
      this.translateY = mouseY - canvasY * this.scale;

      // 应用边界限制
      // this.calcBoundary();
    }
  }

  // 放大按钮
  zoomIn() {
    if (this.scale < this.maxScale) {
      // 使用乘法而不是加法来计算新的缩放值，使缩放更平滑
      const newScale = Math.min(this.maxScale, this.scale * (1 + this.zoomStep));

      // 获取容器
      const container = this.$refs.container as HTMLElement;
      if (!container) return;

      const containerRect = container.getBoundingClientRect();

      // 计算容器中心点
      const centerX = containerRect.width / 2;
      const centerY = containerRect.height / 2;

      // 计算当前中心点对应到canvas上的相对位置
      const canvasCenterX = (centerX - this.translateX) / this.scale;
      const canvasCenterY = (centerY - this.translateY) / this.scale;

      // 更新缩放
      this.scale = newScale;

      // 计算新的位置，保持中心点不变
      this.translateX = centerX - canvasCenterX * this.scale;
      this.translateY = centerY - canvasCenterY * this.scale;

      // 应用边界限制
      this.calcBoundary();
    }
  }

  // 缩小按钮
  zoomOut() {
    if (this.scale > this.minScale) {
      // 使用乘法而不是减法来计算新的缩放值，使缩放更平滑
      const newScale = Math.max(this.minScale, this.scale / (1 + this.zoomStep));

      // 获取容器
      const container = this.$refs.container as HTMLElement;
      if (!container) return;

      const containerRect = container.getBoundingClientRect();

      // 计算容器中心点
      const centerX = containerRect.width / 2;
      const centerY = containerRect.height / 2;

      // 计算当前中心点对应到canvas上的相对位置
      const canvasCenterX = (centerX - this.translateX) / this.scale;
      const canvasCenterY = (centerY - this.translateY) / this.scale;

      // 更新缩放
      this.scale = newScale;

      // 计算新的位置，保持中心点不变
      this.translateX = centerX - canvasCenterX * this.scale;
      this.translateY = centerY - canvasCenterY * this.scale;

      // 应用边界限制
      this.calcBoundary();
    }
  }

  // 重置缩放和位置
  resetZoom(animate = true) {
    // 保存当前位置的相对比例
    const element =
      this.$slots.default && this.$slots.default[0] ? (this.$slots.default[0].elm as HTMLDivElement) : null;
    const containerElement = this.container;
    this.animate = animate;
    this.scale = 1;
    this.translateX = 0;
    this.translateY = 0;

    if (!element) return;
    if (!containerElement) return;

    const contentRect = {
      width: element.clientWidth,
      height: element.clientHeight,
    };
    const containerRect = containerElement.getBoundingClientRect();
    if (contentRect.width < containerRect.width) {
      this.translateX = containerRect.width / 2 - contentRect.width / 2;
    } else {
      this.fitZoom();
    }

    setTimeout(() => {
      this.animate = true;
    }, 300);
  }

  async fitZoom() {
    this.scale = 1;
    this.translateX = 0;
    this.translateY = 0;

    const element =
      this.$slots.default && this.$slots.default[0] ? (this.$slots.default[0].elm as HTMLDivElement) : null;
    const containerElement = this.container;

    if (!element) return;
    if (!containerElement) return;

    const contentRect = {
      width: element.clientWidth,
      height: element.clientHeight,
    };
    const containerRect = containerElement.getBoundingClientRect();
    const scale = containerRect.width / contentRect.width;
    this.scale = scale;
  }
}
</script>

<style scoped lang="scss">
.image-viewer-container {
  width: 100%;
  height: 100%;
  position: relative;
  cursor: grab;
  user-select: none;
  -webkit-user-drag: none;
  overflow: hidden; /* 去掉滚动条 */
  touch-action: none; /* 禁用浏览器默认的触摸操作 */

  &:active {
    cursor: grabbing;
  }
}

.image-viewer-canvas {
  transform-origin: 0 0;
  transition: transform 0.3s;

  ::v-deep {
    img {
      -webkit-user-drag: none;
    }
  }
}

.zoom-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  display: flex;

  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 8px;

  cursor: default;
  overflow: hidden;
}

.icon-imageTools {
  float: left;
  width: 46px;
  height: 60px;
  background: url('~@/assets/img-preview-btn.png') no-repeat;
  cursor: pointer;
  margin: 0 10px;

  &:hover {
    background: url('~@/assets/img-preview-btn-hover.png') no-repeat;
  }

  &:active {
    opacity: 0.4;
  }

  &.swiper-button-disabled {
    opacity: 0.2;
    cursor: default;
  }

  &.icon-pre {
    background-position: -85px 11px;
    border-right: 1px solid rgba(255, 255, 255, 0.17);
  }

  &.icon-zoomin {
    background-position: -136px 15px;
  }

  &.icon-zoomout {
    background-position: 8px 15px;
  }

  &.icon-recover {
    background-position: -64px 15px;
  }

  &.icon-close {
    background-position: -317px 15px;
  }

  &.icon-rotateL {
    background-position: -246px 15px;
  }

  &.icon-rotateR {
    background-position: -227px 15px;
  }

  &.icon-next {
    background-position: -195px 15px;
    border-left: 1px solid rgba(255, 255, 255, 0.17);
  }
}
</style>

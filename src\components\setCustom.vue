<template>
    <!--设置自定义区间-->
    <el-dialog
            title="自定义得分率"
            :append-to-body="true"
            :destroy-on-close="true"
            :visible.sync="custormVisible"
            :before-close="handleClose"
            width="30%">
        <div class="score-range display_flex">
            <NumericInput
                    class="scoreInput flex_1"
                    v-model="rateParam[0]"
                    :type="'digit'"
                    :isPositive="true"
            ></NumericInput>
            <span class="gapline">-</span>
            <NumericInput
                    class="scoreInput flex_1"
                    v-model="rateParam[1]"
                    :type="'digit'"
                    :isPositive="true"
            ></NumericInput>
        </div>
        <span slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取 消</el-button>
                <el-button type="primary" @click="setCustom">确 定</el-button>
            </span>
    </el-dialog>
    <!--设置自定义区间-->
</template>

<script>
    import NumericInput from './NumericInput';

    export default {
        name      : 'set-custom',
        props     : ['originData'],
        components: {
            NumericInput,
        },
        data () {
            return {
                rateParam     : [0, 30],
                custormVisible: true
            };
        },
        mounted () {
            this.rateParam = this.$deepClone(this.originData);
        },
        methods   : {
            // 点击dialog右上角关闭按钮,直接关闭恢复未更改之前的值
            handleClose () {
                this.$emit('cancelSet');
                this.rateParam = this.$deepClone(this.originData);
            },
            // 设置自定义得分率范围
            setCustom () {
                if (Number(this.rateParam[0]) >= 100) {
                    this.$message.error('得分率最小值不能大于等于100');
                    return;
                }
                if (Number(this.rateParam[0]) > Number(this.rateParam[1])) {
                    this.$message.error('得分率最小值不能大于最大值');
                    return;
                }
                if (Number(this.rateParam[0]) > 100 || Number(this.rateParam[1]) > 100) {
                    this.$message.error('得分率不能大于100%');
                    return;
                }
                this.rateParam = [Number(this.rateParam[0]), Number(this.rateParam[1])];
                this.$emit('sureSet', this.rateParam);
            },
        }
    };
</script>

<style lang="scss">
    .score-range {
        padding     : 0 20px;
        line-height : 40px;
        .gapline {
            margin : 0 10px;
        }
    }

    .scoreInput {
        width        : 50px;
        position     : relative;
        margin-right : 26px;
        .numeric-input-inner {
            border-radius : 4px 0 0 4px !important;
            height : 40px !important;
            &:focus{
                border-color: #dcdfe6!important;
            }
        }
        &:after {
            content       : "%";
            position      : absolute;
            right         : 0px;
            top           : 0;
            border        : 1px solid #dcdfe6;
            border-radius : 0 4px 4px 0;
            width         : 26px;
            height        : 100%;
            background    : #f5f7fa;
            color         : #909399;
            text-align    : center;
            line-height   : 40px;
            z-index       : 11;
            border-left   : none;
        }
    }


</style>

<!--
 * @Description: 微课查看列表
 * @Author: qmzhang
 * @Date: 2024-07-29 10:11:14
 * @LastEditTime: 2025-02-27 19:05:51
 * @FilePath: \personal-bigdata\src\components\AddWeike\views\WeikeList.vue
-->
<template>
    <el-container style="padding: 5px 0 0;">
        <el-header class="clearfix" height="50px" style="padding: 0;">
            <div class="pull-left">
                接收对象：
                <el-select v-model="selectedReciveType" placeholder="请选择" size="small" @change="onChangeReciveType">
                    <el-option v-for="item in reciverList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </div>
            <div class="pull-right">
                <el-input placeholder="搜索文件名" v-model="keyword" @keypress.enter.native="enterUpdate" @clear="updateData"
                    clearable size="small">
                    <el-button slot="append" icon="el-icon-search" @click="updateData"></el-button>
                </el-input>
            </div>
        </el-header>

        <!-- 视频列表页面 -->
        <el-main class="list-container" v-loading="loading" style="padding: 0;">
            <el-row :gutter="20" v-if="courseList.length">
                <el-col :span="6" v-for="(item, index) in courseList" :key="item.id + '_' + index">
                    <div class="video-container">
                        <el-image class="video-image" :src="item.imgUrl" fit="cover">
                            <div slot="error" class="image-slot">
                                <i class="el-icon-picture-outline"></i>
                            </div>
                        </el-image>
                        <div class="video-time">{{ item.time }}</div>
                        <!-- 播放按钮 -->
                        <div class="play-btn-area click-element wh100 middle-helper" title="点击预览视频"
                            @click="previewVideo(item)">
                            <i class="el-icon-video-play"></i>
                        </div>
                    </div>

                    <div class="video-checkbox" @click="onClickCheckbox(item)">
                        <el-checkbox @change="onChangeCheckbox(item)" v-model="item.checked" :disabled="item.locked">
                            <div class="video-title text-ellipsis" :title="item.title">{{ item.title }}</div>
                            <div class="video-desc clearfix">
                                {{ item.checked ? item.targetTypeLabel : item.createTime }}
                            </div>
                        </el-checkbox>
                    </div>
                </el-col>
            </el-row>

            <el-empty class="my-iframe" description="暂无未添加微课资源" v-else />
        </el-main>

        <el-footer class="text-center" height="40px" v-if="courseList.length">
            <el-pagination background layout="total,prev, pager, next" :hide-on-single-page="true" :page-size="8"
                :total="listResult.total_rows" @current-change="handleCurrentChange">
            </el-pagination>
        </el-footer>

        <!-- <el-footer class="dialog-footer clearfix">
            <div class="pull-left" style="padding-top: 14px;">已选择：{{ addedList.length }}/9</div>
            <div class="pull-right">
                <el-button @click="$emit('close')">取消</el-button>
                <el-button type="primary" @click="confirm" :disabled="!hasNewWeike" :loading="confirmLoading">添加</el-button>
            </div>
        </el-footer> -->
        <!-- 强制页面刷新 -->
        <div style="display: none;">{{ refreshKey }}</div>
    </el-container>
</template>

<script lang="ts">
import { BatchSaveHomeWorkMicroCourse, DeleteQuesWk } from '@/service/api';
import { ReqStructure } from '@/service/types';
import { CoursewareResource } from '@/service/types/commonReslist';
import { sessionSave } from '@/utils';
import { Component, Vue, Prop } from 'vue-property-decorator';
import { AddWeikeListJson, AddWeikeVM } from '../AddWeikeVM.class'

@Component
export default class AddWeike extends Vue {
    @Prop() vModel;

    vm: AddWeikeVM;
    loading: boolean = true;
    confirmLoading: boolean = false;
    // 已添加微课列表
    addedList: AddWeikeListJson[] = [];
    get hasNewWeike() {
        return this.addedList.some(item => item.isNew)
    }
    // 微课列表返回数据
    listResult: ReqStructure<CoursewareResource> = null;
    // 网盘微课列表
    get courseList(): Array<CoursewareResource> {
        return this.listResult ? this.listResult.rows : [];
    };

    // 接收对象列表
    // 4:任教班级-错误学生 5:任教班级-所有学生 6:全部班级-错误学生 7:全部班级-所有学生
    reciverList = AddWeikeVM.ReciverList;
    // 已选择的接收类型
    selectedReciveType = 7;
    keyword: string = "";
    page: number = 1;
    refreshKey = 0;

    async created() {
        this.vm = new AddWeikeVM(this.vModel);
        this.addedList = this.vModel.courseList;
        await this.updateData();
    }

    async updateData() {
        this.loading = true;
        try {
            this.listResult = await this.vm.GetWeikeList(this.page, this.keyword, this.addedList);
        } finally {
            this.loading = false;
        }
    }

    async enterUpdate() {
        if (!this.keyword) return;

        this.page = 1;
        this.updateData();
    }

    handleCurrentChange(val: number) {
        this.page = val;
        this.updateData();
    }

    /**
     * @description: 确认添加微课
     * @return {*}
     */
    async confirm() {
        this.confirmLoading = true;
        if (!this.addedList.length) return;

        let wkListJson: AddWeikeListJson[] = this.addedList.filter(item => {
            item.title = item.title.substring(0, 25);

            return item.isNew;
        });
        if (!wkListJson.length) return;

        let tt = this.selectedReciveType;
        // classId支持存多个任教班级
        let saveClassIds = this.vModel.classId;
        let substituteClassList: any = sessionSave.get('substituteClassList')
        if (tt == 4 || tt == 5) {
            saveClassIds = [...new Set(substituteClassList.map(item => item.classId))].join(',')
        }
        try {
            let res: any = await BatchSaveHomeWorkMicroCourse({
                teaId: this.vModel.loginInfo.id,
                teaName: this.vModel.loginInfo.realname,
                schoolId: this.vModel.loginInfo.schoolid,
                classId: saveClassIds,
                workId: this.vModel.pbookId,
                cardQuestionLevel: this.vModel.qLevel,
                cardQuestionId: this.vModel.qId,
                questionId: this.vModel.qId,
                questionName: this.vModel.qName,
                microCourseListJson: JSON.stringify(wkListJson),
                targetType: tt,
            })
            if (res.code && res.code != 1) throw (res);

            let reslist = !res.code ? res : res.data;
            let saveList = this.addedList.filter(item => {
                let findItem = reslist.find(rItem => rItem.resourceId == item.resourceId);
                if (!findItem) return true;

                item.id = findItem.id;
                item.createTime = findItem.createTime;
                item.questionId = this.vModel.qId;
                item.questionName = this.vModel.qName;
                delete item.isNew;
                return true;
            })
            this.$emit("change", {
                type: "addWK",
                data: saveList
            })
        } catch (error) {
            this.$notify({
                title: "提示",
                message: error.msg || "添加微课失败，请稍后重试！",
                type: 'error',
                offset: 100,
            })
        } finally {
            this.confirmLoading = false;
        }
    }

    onChangeReciveType() {
        this.addedList.forEach(item => {
            if (!item.isNew) return;

            item.targetType = this.selectedReciveType;
            item.targetTypeLabel = AddWeikeVM.ReciverList.find(rItem => item.targetType == rItem.value).label;
            let findItem = this.listResult.rows.find(rowItem => rowItem.id === item.resourceId);
            findItem.targetTypeLabel = item.targetTypeLabel;
        })
    }

    /**
     * @description: 转化资源为已添加格式
     * @return {*}
     */
    formatAddedWeike(wkItem: CoursewareResource): AddWeikeListJson {
        return {
            resourceId: wkItem.id,
            title: wkItem.title,
            microCourseUrl: wkItem.resultUrl + '/video.mp4',
            duration: wkItem.timeLength,
            imgUrl: wkItem.imgUrl,
            fileSize: wkItem.fileSize,
            targetType: this.selectedReciveType,
            targetTypeLabel: wkItem.targetTypeLabel,
            createTime: wkItem.createTime,
            time: wkItem.time,
            isNew: true
        }
    }

    /**
     * @description: 切换选中项
     * @param {*} wkItem
     * @return {*}
     */
    onChangeCheckbox(wkItem: CoursewareResource) {
        if (wkItem.checked && this.addedList.length >= 9) {
            wkItem.checked = false;
            this.$notify({
                title: "无法添加",
                message: "每道题最多支持添加9个微课！",
                type: 'error',
                offset: 100,
            })
            return;
        }

        wkItem.isNew = true;
        if (wkItem.checked) {
            wkItem.targetType = this.selectedReciveType;
            wkItem.targetTypeLabel = AddWeikeVM.ReciverList.find(rItem => wkItem.targetType == rItem.value).label;
            this.addedList.push(this.formatAddedWeike(wkItem));
            this.$emit('res-add', 'video')
        } else {
            this.addedList = this.addedList.filter(item => item.resourceId !== wkItem.id);
            this.$emit('res-remove', 'video')
        }
    }

    /**
     * @description: 点击选中项，支持取消未选中项目
     * @param {*} wkItem
     * @return {*}
     */
    async onClickCheckbox(wkItem: CoursewareResource) {
        if (!wkItem.checked || !wkItem.locked) return;

        await this.$confirm(`该微课已被添加至${wkItem.targetTypeLabel}可见，确定删除吗？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });

        try {
            // 直接删除微课
            await DeleteQuesWk({
                schoolId: this.vModel.loginInfo.schoolid,
                id: wkItem.addedId,
                targetType: wkItem.targetType
            })
            this.$notify({
                title: "提示",
                message: '微课删除成功！',
                type: 'success',
                offset: 100,
            });

            this.addedList = this.addedList.filter(item => item.id != wkItem.addedId);
            this.$emit('change', { type: 'deleteWK', data:this.addedList })
        } catch (error) {
            this.$notify({
                title: "提示",
                offset: 100,
                type: 'error',
                message: error.msg ? error.msg : '微课删除失败，请稍后重试！'
            });
        } finally {
            wkItem.locked = false;
            wkItem.checked = false;
        }

    }

    /**
     * @description: 预览微课
     * @param {*} wkItem
     * @return {*}
     */
    previewVideo(wkItem: CoursewareResource) {
        this.$emit("change", {
            type: 'previewVideo',
            data: {
                url: wkItem.resultUrl + '/video.mp4',
                title: wkItem.title,
                show: true
            }
        })
    }
}
</script>

<style lang="scss" scoped>
@import "./index.scss";

.list-container {
    height: 375px;
    overflow-x: hidden;
}

.video-image {
    position: relative;
    width: 100%;
    padding-top: 56.25%;
    z-index: 1;

    ::v-deep img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    .el-icon-picture-outline {
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 10;
        margin: -25px;
        color: #ddd;
        font-size: 50px;
    }
}

.video-checkbox {
    width: 100%;
    padding-top: 5px;
    margin-bottom: 15px;

    .el-checkbox {
        width: 100%;

        &.is-checked {
            .video-desc {
                color: #40a0ff9a;
            }
        }

        &.is-disabled {
            .video-desc {
                color: #C0C4CC;
            }
        }
    }

    ::v-deep {
        .el-checkbox__label {
            width: 100%;
            padding-right: 10px;
            vertical-align: middle;
        }
    }

    .video-title {
        margin-bottom: 2px;
        font-size: 14px;
    }

    .video-desc {
        font-size: 12px;
        color: #999;
    }
}

.dialog-footer {
    padding-top: 10px;
    border-top: 1px solid #f0f0f0;
}
</style>
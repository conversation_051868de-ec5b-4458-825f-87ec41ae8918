<template>
  <div class="not-scanned-container">
    <el-page-header @back="goBack" content="缺考名单"> </el-page-header>
    <div class="not-scanned-head">
      <el-button type="primary" @click="exportXls"
        >导出<i class="el-icon-download el-icon--right"></i
      ></el-button>
    </div>
    <div class="notscan-table-container">
      <el-table
        :data="classList"
        style="width: 30%; margin-right: 20px"
        :header-cell-style="headerStyle"
        :load-more-disabled="false"
        :height="tableHeight"
        ref="calssTable"
        highlight-current-row
        @current-change="handleCurrentClassChange"
      >
        <el-table-column type="index" label="序号" width="100" align="center"> </el-table-column>
        <el-table-column prop="class_name" label="班级" align="center"> </el-table-column>
        <el-table-column prop="scanCount" label="已扫人数" align="center"> </el-table-column>
        <el-table-column prop="missExamCount" label="缺考人数" align="center">
          <template slot="header">
            缺考人数
            <el-tooltip
              class="item"
              effect="dark"
              content="缺考人数 = 未扫描 + 填涂缺考 + 考号异常未处理"
              placement="top"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <span style="color:red;">{{ scope.row.missExamCount }}</span>
        </template>
        </el-table-column>
      </el-table>
      <el-table
        :data="stuInfos"
        style="width: 100%"
        :header-cell-style="headerStyle"
        v-load-more.expand="{
          func: loadmore,
          target: '.el-table__body-wrapper',
          distance: 20,
          delay: 100,
        }"
        :load-more-disabled="false"
        :height="tableHeight"
        ref="stuTable"
      >
        <el-table-column prop="className" label="班级" align="center"> </el-table-column>
        <el-table-column prop="realName" label="姓名" align="center"> </el-table-column>
        <el-table-column prop="stuNumber" label="考号" align="center"> </el-table-column>
        <el-table-column label="备注" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.scanState == 0 ? '未扫' : '缺考' }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getClassListByWorkIdAPI, getScanStuInfosAPI } from '@/service/api';
import { getQueryString } from '@/utils';

export default {
  data() {
    return {
      // 表格头部样式
      headerStyle: {
        background: '#f5f7fa',
        color: '#3F4A54',
        fontWeight: 'bold',
        fontSize: '15px',
        width: '100%',
        height: '51px',
      },
      classId: '',
      tableHeight: 'calc(100% - 40px)',
      page: 1,
      pageSize: 50,
      pageCount: 1,
      queryInfo: {},
      classList: [],
      stuInfos: [],
      userId: getQueryString('userId') || '',
      schoolId: getQueryString('schoolId') || '',
      allStu: 0,
      downloadUrl: process.env.VUE_APP_KKLURL,
    };
  },

  mounted() {
    this.queryInfo = this.$route.query;
    this.getClassList();
  },
  methods: {
    changeClass() {
      // this.$refs.stuTable.scrollTo({
      //   top: 0,
      //   behavior: "smooth",
      // });
      this.page = 1;
      this.getStuData();
    },
    /**
     * @name:获取班级列表
     */
    async getClassList() {
      let params = {
        schoolId: this.schoolId,
        workId: this.queryInfo.workId,
        teaId: this.userId,
      };
      let res = await getClassListByWorkIdAPI(params);
      if (res.code == 1) {
        this.classList = res.data;
        this.classId = this.classList[0].id;
        this.$nextTick(() => {
          this.$refs.calssTable.setCurrentRow(this.classList[0]);
        });
      }
    },
    /**
     * @name:当前选中行
     */
    handleCurrentClassChange(val) {
      this.page = 1;
      this.stuInfos = [];
      this.classId = val.id;
      this.getStuData();
    },
    /**
     * @name:获取学生名单
     */
    async getStuData() {
      let params = {
        workId: this.queryInfo.workId,
        schoolId: this.queryInfo.schoolId,
        classId: this.classId,
        state: 0,
        page: this.page,
        limit: this.pageSize,
        state: '0,2', // -1 ：全部 0：未扫描 1：已扫描 2：缺考 多个逗号分隔
      };
      const res = await getScanStuInfosAPI(params);
      if ((res.code = 1)) {
        if (this.page == 1) {
          this.stuInfos = res.data.rows;
        } else {
          this.stuInfos = this.stuInfos.concat(res.data.rows);
        }
        this.pageCount = res.data.page_count;
        this.allStu = res.data.total_rows;
      }
    },
    goBack() {
      this.$router.back();
    },
    /**
     * @name:加载更多
     */
    loadmore() {
      console.log('滚动到底部了', this.page);
      if (this.page >= this.pageCount) return;
      this.page++;
      this.getStuData();
    },
    /**
     * @name:导出未扫描名单
     */
    exportXls() {
      let url =
        this.downloadUrl +
        `/pexam/scanExam/exportScanStuInfos?workId=${this.queryInfo.workId}&schoolId=${this.queryInfo.schoolId}&state=0,2&classId=`;
      console.log(url);
      window.open(url, '_self');
    },
  },
};
</script>

<style lang="scss" scoped>
.not-scanned-container {
  padding: 20px 80px 80px 80px;
  height: 100%;
  .not-scanned-back {
    font-size: 16px;
    cursor: pointer;
    margin-bottom: 10px;
  }
  .not-scanned-head {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
    .not-scanned-total {
      margin-left: 10px;
      font-weight: bold;
    }
  }
}
.notscan-table-container {
  display: flex;
  flex-direction: row;
  height: 100%;
}
</style>
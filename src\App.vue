<template>
  <div style="width: 100%; height: 100%" class="noselect">
    <keep-alive :include="keepAliveRouteNames">
      <router-view id="app" class="page-wrapper"></router-view>
    </keep-alive>
  </div>
</template>

<script>
import { loadScript } from '@/utils/index';

// 获取所有keepAlive为Ture的路由name数组
function getKeepAliveRouteNames(routes) {
  const keepAliveRouteNames = [];
  routes.forEach(route => {
    if (route.meta && route.meta.keepAlive) {
      keepAliveRouteNames.push(route.name || '');
    }
    if (route.children) {
      // 如果路由有子路由，递归调用
      keepAliveRouteNames.push(...getKeepAliveRouteNames(route.children));
    }
  });
  return keepAliveRouteNames;
}

export default {
  components: {},
  data() {
    return {
      keepAliveRouteNames: [],
    };
  },
  created() {
    let routes = this.$router.options.routes;
    this.keepAliveRouteNames = getKeepAliveRouteNames(routes);
    this.loadMathjaxScripts();
    // console.log('keepAliveRouteNames', this.keepAliveRouteNames);
  },
  methods: {
    /**
     * @description: 异步载入公式脚本文件
     * @return {*}
     */
    loadMathjaxScripts() {
      return Promise.all([
        loadScript('https://fs.iclass30.com/package/katex@0.15.2/dist/katex.min.js'),
        loadScript('https://fs.iclass30.com/package/katex@0.15.2/dist/contrib/auto-render.min.js'),
        loadScript('https://fs.iclass30.com/package/mathjax@3.2.0/es5/tex-svg-full.js')
      ])
    },
  },
};
</script>

<style lang="scss">
@import 'styles/layout';
@import 'styles/iconfonts/iconfont.css';
@import 'styles/iconfonts/extend_icon/iconfont.css';


// 具有排序的表头垂直、水平居中
.table-sort-cell {
  >.cell {
    display: flex !important;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
  }
}

// 选择题样式
.choice-container {
  margin-top: 10px;
  width: 100%;

  .option-sort {
    vertical-align: middle;
  }

  .selectoption {
    margin: 5px 0;
    min-height: 30px;
    white-space: nowrap;
    font-size: inherit;
  }

  .opt-label {
    white-space: normal;
    vertical-align: middle;
    display: inline-block;

    mjx-container[jax='SVG'] {
      line-height: normal !important;
    }

    p:empty {
      display: none;
    }
  }
}

.loadingMask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #fff url('./assets/loading.gif') center center no-repeat;
  z-index: 999;
}

.answer_box {
  img {
    vertical-align: middle;
  }
}

.math-tex-img {
  vertical-align: middle;
  zoom: 0.8;
}

.sticky {
  position: sticky;
  z-index: 100;
  top: 10px;
}

.sticky_sentinel {
  background: yellow;
  opacity: 0.8;
  position: absolute;
  left: 0;
  right: 0;
  visibility: hidden;
}

.sticky_sentinel--top {
  height: 40px;
  top: -24px;
}

/*修复table线条错位*/
body .el-table th.gutter {
  display: table-cell !important;
}

// .el-form-item {
//   margin-bottom: 20px;

//   .el-form-item__label {
//     text-align: right;
//     vertical-align: middle;
//     float: left;
//     font-size: 16px;
//     color: #606266;
//     line-height: 40px;
//     padding: 0 12px 0 0;
//     box-sizing: border-box;
//   }
// }

.el-dialog__wrapper [role='dialog'] {
  overflow: hidden;
  border-radius: 4px;
}

.el-dialog__header {
  height: 54px;
  background-color: #fafbff;
  padding: 0 20px;
}

.el-dialog__header .el-dialog__title {
  display: inline-block;
  line-height: 54px;
  font-size: 16px;
  font-weight: 600;
  color: #545454;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 90%;
}

.el-dialog__headerbtn {
  top: 12px;
  right: 12px;
  font-size: 20px;
}

.catalog-tree-box .el-tree-node__expand-icon.expanded {
  transform: none;
}

.edition-box .el-icon-arrow-up:before {
  content: '\e78f' !important;
}

.catalog-tree-box .el-icon-caret-right:before {
  content: '\e7a0' !important;
}

.catalog-tree-box .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {
  content: '\e7a2' !important;
}

.catalog-tree-box .el-tree-node__expand-icon {
  font-size: 17px;
}

.catalog-tree-box .el-tree-node__label {
  font-size: 16px;
}

.catalog-tree-box .el-tree-node__content {
  height: 30px;
}

.selected-catalog-box .el-tag {
  color: #666;
  font-size: 14px;
  border-radius: 30px;
  margin: 10px;
  background-color: #f7f9fc;
}

.g-workbook-dialog .el-dialog__body {
  padding: 0px;
}

.el-message {
  min-width: 300px;
  padding: 15px 15px 15px 20px;
}

.el-message .el-message__icon {
  font-size: 18px;
  margin-top: 0;
}

.el-message .el-message__content {
  font-size: 18px;
}

.katex-display {
  display: inline-block;
  margin: 10px 0;
}

.question_body,
.optionsClass {
  img {
    vertical-align: middle;
  }
}

.question_body {
  font-size: 15px;
  padding: 0 20px;
  background: white;

  img {
    vertical-align: middle;
  }

  table {
    margin-top: 10px;
  }

  .ques-opt-box {
    align-items: center;
    display: flex;
    margin-left: 8px;

    .ques-opt {
      width: 100%;

      &.arrange_1 {
        display: flex;
        flex-direction: column;

        .ques-opt-item {
          display: flex;
          align-items: center;
          width: 100%;
        }
      }

      &.arrange_2 {
        display: flex;
        flex-wrap: wrap;

        .ques-opt-item {
          padding-right: 40px;
          flex-shrink: 0;
          display: flex;
          align-items: center;
        }
      }

      &.arrange_3 {
        display: flex;
        flex-wrap: wrap;

        .ques-opt-item {
          display: inline-block;
          padding-right: 10px;
          width: 45%;
        }
      }

      .ques-opt-item {
        padding-left: 20px;
        margin-bottom: 10px;

        >span {
          display: inline-block;
        }
      }
    }
  }
}

.optionsClass {
  display: inline-block;
  padding-left: 20px;
  margin-bottom: 10px;

  /*font-size: 16px;*/
  >span {
    display: inline-block;
  }
}

.choiceQues {
  width: 100%;

  &.arrange_1 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -ms-flex-direction: column;
    -webkit-flex-direction: column;
    flex-direction: column;

    .optionsClass {
      display: flex !important;
      align-items: center;
      width: 100%;

      >span {
        margin-left: 10px;
      }
    }
  }

  &.arrange_2 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;

    .optionsClass {
      padding-right: 40px;
      flex-shrink: 0;
      display: flex !important;
      -webkit-box-align: center;
      -ms-flex-align: center;
      -webkit-align-items: center;
      align-items: center;

      >span {
        margin-left: 10px;
      }
    }
  }

  &.arrange_3 {
    .optionsClass {
      display: inline-block !important;
      padding-right: 30px;
      width: 49%;
    }
  }
}

.nodata {
  width: 100%;
  height: auto;
  margin-bottom: 20px;

  img {
    display: block;
    margin: 0 auto;
  }
}

.leaf-q,
.intermediate-q {
  margin-bottom: 0;
  margin-left: 25px;
}

subqs ol>li {
  list-style-type: decimal;
  /* list-style-position: inside; */
}

ol {
  padding-inline-start: 0;
}

ol ol>li {
  list-style-type: upper-roman;
}

ol ol ol>li {
  list-style-type: lower-roman;
}

nn {
  font-weight: bold;
  display: inline-block;
  padding: 0 5px;
  border-bottom: 1px solid #555;
  margin: 0 2px;
  line-height: 11px;
  min-width: 50px;
}

a.math-show {
  color: unset;
}

a.math-show:hover {
  color: unset;
}

.quesList-item {
  line-height: 2em;
}

.ques-content {
  width: 100%;
  line-height: 2em;
}

.el-select .el-tag {
  margin: 2px 0 2px 3px;
  max-width: 118px;
}

.el-tag--small {
  padding: 0 6px;
}

.el-button--primary {
  background: #409eff;
}
</style>

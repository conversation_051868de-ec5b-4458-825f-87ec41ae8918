<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-07-31 11:07:21
 * @LastEditors: liuyue <EMAIL>
-->
<template>
  <div class="enter">
    <bread-crumbs :title="'补录'"></bread-crumbs>
    <div class="enter-main">
      <div class="clearfix" v-if="reportDetail.source != 6">
        <el-button class="fr pull-right" type="primary" @click="isShowAddSubjectDialog = true">添加学科</el-button>
      </div>

      <div class="enter-ul">
        <div v-for="item in subjectList" :key="item.id" class="enter-li">
          <div class="subject-name">
            {{ getPhaseName(item.phaseId) + item.name }}
            <template
              v-if="item.relateCardType == ICARD_STATE.abPaper || item.relateCardType == ICARD_STATE.abPaperTwo">
              <template v-if="item.abCardSheetType == 0">
                (A卷)
              </template>
              <template v-if="item.abCardSheetType == 1">
                (B卷)
              </template>
            </template>
          </div>
          <div v-if="item.source != 6">
            <el-button class="enter-btn" type="text" :disabled="((item.source == 3 && item.progress == 4 && item.progressState == 1) ||
              (item.source == 4 && item.progress >= 5) ||
              item.isComp) || item.testBankId != '' || item.relateCardType > 1
              " @click="exportScore(item)"
              v-if="item.importScoreState == 0 || item.source == 3 || item.source == 4 || (item.source == 3 && item.progress == 4 && item.progressState == 1) || (item.source == 4 && item.progress >= 5) || item.isComp">
              导入成绩
            </el-button>
            <el-button v-else class="enter-btn" type="text" @click="exportScore(item, false)">重新导入</el-button>
          </div>
          <div v-if="item.source == 3 || item.source == 4 || item.source == 6">
            <el-button :disabled="uploadDisabled(item)" v-if="!item.testBankId" class="enter-btn" type="text"
              @click="exportPaper(item)">上传试卷</el-button>
            <template v-else>
              <el-button v-if="item.state == 2 && item.isUse" class="enter-btn" type="text" @click="goCreateQues(item)">
                查看试卷</el-button>
              <el-button v-else :disabled="uploadDisabled(item)" class="enter-btn" type="text"
                @click="goCreateQues(item)">
                {{ item.state == 2
                  ? '去制卷'
                  : '解析中' }}
              </el-button>
            </template>

            <template v-if="item.testBankId">
              |<el-button :disabled="uploadDisabled(item)" type="text" @click="clearPaper(item)">删除试卷</el-button>
            </template>
          </div>
          <div v-if="item.state == 2 && item.isUse">
            <el-button class="enter-btn" :disabled="item.statType != 1" type="text"
              @click="matchQuesNos(item)">题号匹配</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加学科 -->
    <el-dialog title="添加学科" :visible.sync="isShowAddSubjectDialog" width="800px">
      <el-checkbox-group v-model="checkSubjectIdList">
        <el-checkbox v-for="item in addSubjectList" style="margin-top: 5px; margin-left: 5px" :label="item.id"
          :disabled="!!disabledSubjectList.find(t => t == item.id)" :key="item.id">{{ item.name }}</el-checkbox>
      </el-checkbox-group>

      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowAddSubjectDialog = false">取 消</el-button>
        <el-button type="primary" :disabled="!checkSubjectIdList.length" @click="onAddSubject">保存</el-button>
      </span>
    </el-dialog>

    <!-- 导入成绩弹框 -->
    <exportXlsx ref="exportXlsx" :examId="reportDetail.examId" :year="reportDetail.year"
      :phase="subjectInfo.phaseId - 2" :subjectId="subjectInfo.id" @closeDialog="closeDialog"
      v-if="isShowExportXlsxDialog"></exportXlsx>
    <!--上传word-->
    <upload-word v-if="isShowExportWordDialog" ref="uploadWord" :formVisible="isShowExportWordDialog"
      :formDataParam="reportDetail" :subjectInfo="editWordInfo" :editParamsData="editParamsData"
      @closeDialog="closeUploadDialog"></upload-word>
    <!--上传word-->
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import BreadCrumbs from '@/components/Breadcrumbs/index.vue';
import {
  getExamSubject,
  addExamSubject,
  getExamSubjectMulti,
  remExamPaper,
} from '@/service/pexam';
import ExportXlsx from '@/components/exportXlsx.vue';
import UploadWord from "@/components/uploadWord.vue";
import UserRole from '@/utils/UserRole';
import { ICARD_STATE, ICARD_TYPE } from '@/typings/card';
import {
  findTestBanksData,
} from '@/service/api';

interface ISubject {
  id: string;
  name: string;
  phaseId: number;
  xfId: number;
  xkId: number;
  progress: number;
  progressState: number;
  code: null;
  status: number;
  personalBookId: string;
  workId: null;
  testBankId: string;
  paperNo: string;
  fullScore: number;
  markingPapersProgress: number;
  roles: number[];
  importScoreState: number;//导入成绩状态(0:未导入 1:已导入)
  source: number;
  gradeId: string;
  categoryId: string;
  year: number;
  tbName: string;
  state: number;
  isUse: number;
  processState: number;//是否发送加工(0:未加工 1:加工中 2:加工完成，3:导入成绩，4:生成个册中，5:生成完成)
  relateCardType: number;
  abCardSheetType: number;
  bindQueState: number;
}
@Component({
  components: {
    BreadCrumbs,
    ExportXlsx,
    UploadWord
  },
})
export default class ScoreEntry extends Vue {
  // 学科列表
  subjectList: ISubject[] = [];
  // 添加学科列表
  addSubjectList = [];
  // 选择的学科id列表
  checkSubjectIdList = [];
  // 禁用的学科id列表
  disabledSubjectList = [];
  // 补录试卷信息
  editWordInfo: any = [];
  // 档期那学科详情
  subjectInfo: ISubject = null;
  // 是否显示导入对话框
  isShowExportXlsxDialog: boolean = false;
  // 是否显示添加学科对话框
  isShowAddSubjectDialog: boolean = false;
  // 是否显示导入word对话框
  isShowExportWordDialog: boolean = false;
  ICARD_STATE = ICARD_STATE;
  tbTimer = null;
  editParamsData:{}
  // 报告详情
  get reportDetail() {
    return this.$sessionSave.get('reportDetail') as IExamReportInfo;
  }

  get uploadDisabled() {
    return (item: any): boolean => {
      return !item.paperNo || item.processState != 0 || item.id.indexOf('-') > -1 || (item.originCardType == ICARD_TYPE.QUESCARD || item.originCardType == ICARD_TYPE.ONLYCARD);
    }
  }

  async mounted() {
    await this.getSubjectList();
    await this.findTestBanksData();
    this.getAddSubjectList();
    this.tbTimer = setInterval(async () => {
      this.findTestBanksData();
    }, 5000);
  }
  destroyed() {
    clearInterval(this.tbTimer);
  }

  // 获取学科列表
  async getSubjectList() {
    let res = await getExamSubjectMulti({
      examId: this.reportDetail.examId,
    });
    res.data.forEach(item => {
      item.isComp = String(item.id).split('-').length > 1;
    });

    this.subjectList = res.data;
    this.disabledSubjectList = this.subjectList.map(t => t.id.split('-')).flat();
  }

  // 获取添加学科列表
  async getAddSubjectList() {
    let ret = await UserRole.getUserInfoPersonalityTest();
    let gradeList = ret.schGrdList;
    let grd: any = gradeList.find(t => t.id == this.reportDetail.gradeCode);
    let addSubjectList = await UserRole.getSubjectList({
      year: this.reportDetail.year,
    });

    this.addSubjectList = addSubjectList.filter(
      t => t.phaseId == grd.phaseId
    );
  }

  getPhaseName(phaseId) {
    let name;
    if (phaseId == 5) {
      name = `高中`;
    } else if (phaseId == 4) {
      name = `初中`;
    } else {
      name = `小学`;
    }
    return name;
  }

  async findTestBanksData() {
    let testBankIds = this.subjectList
      .filter(t => t.testBankId)
      .map(t => t.testBankId)
      .join(',');
    findTestBanksData({
      testBankIds: testBankIds,
    })
      .then(data => {
        data.data.forEach(item => {
          this.subjectList.forEach(t => {
            if (t.testBankId == item.id) {
              this.$set(t, 'state', item.state);
              this.$set(t, 'isUse', item.isUse);
              this.$set(t, 'tbName', item.tbName);
            }
          });
        });
      });
  }

  // 导入成绩
  async exportScore(item: ISubject, confirm: boolean = true) {
    if (confirm) {
      await this.$confirm('该操作会覆盖现有的成绩，确定要导入成绩吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });
    }

    this.subjectInfo = item;
    this.isShowExportXlsxDialog = true;
  }
  clearPaper(item: ISubject) {
    this.$confirm('该操作会删除现有的试卷，确定要删除试卷吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      await remExamPaper({
        bookId: item.personalBookId,
        testBankId: item.testBankId,
      })
      this.$message.success('删除试卷成功');
      this.getSubjectList();
    });
  }
  exportPaper(item: ISubject) {
    this.editParamsData = {
      id: item.testBankId,
    }
    this.editWordInfo = this.reportDetail.paperList.find(t => t.workId == item.workId)
    this.isShowExportWordDialog = true;
  }
  matchQuesNos(item: ISubject) {
    let query = {
      personBookId: item.personalBookId,
      examName: this.reportDetail.examName,
      examId: this.reportDetail.examId,
      subjectId: item.id,
      bindQueState: item.bindQueState,
      abCardSheetType: item.abCardSheetType
    };
    if (item.relateCardType == ICARD_STATE.default || item.relateCardType == ICARD_STATE.abCard) {
      delete query.abCardSheetType;
    }
    this.$router.push({
      path: '/home/<USER>',
      query,
    });
  }
  goCreateQues(item: ISubject) {
    let loginInfo = this.$sessionSave.get('loginInfo');
    if (item.state !== 2) {
      this.$message({
        message: '请刷新页面！',
        type: 'warning',
      });
      return;
    }
    if (item.state == 2 && item.isUse) {
      this.$router.push({
        path: '/home/<USER>',
        query: {
          testBankId: item.testBankId,
          testBankName: item.tbName,
          subjectId: item.id,
        },
      });
      return;
    }

    let routeData = `${process.env.VUE_APP_CUTPAPERURL}cutTestPaper/matchQues.html?id=${item.testBankId}&userid=${loginInfo.id}&url=${process.env.VUE_APP_CUT_URL}&subject=${item.id}&phase=${this.reportDetail.phase}&token=${loginInfo.token}`;
    window.open(routeData, '_blank');
    this.$confirm('是否制卷完成，刷新数据', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        this.findTestBanksData();
      })
      .catch(err => {
        console.log(err);
      });
  }

  // 添加学科
  async onAddSubject() {
    let examId = this.reportDetail.examId;
    let arr = [];

    this.checkSubjectIdList.forEach(item => {
      let subject = this.addSubjectList.find(t => t.id == item);

      arr.push({
        examId: examId,
        subjectId: subject.id,
        subjectName: subject.name,
      });
    });

    await addExamSubject(arr);
    this.checkSubjectIdList = [];
    this.$message.success('添加学科成功');
    this.isShowAddSubjectDialog = false;
    this.getSubjectList();
  }

  closeDialog() {
    this.isShowExportXlsxDialog = false;
    this.getSubjectList();
  }
  async closeUploadDialog() {
    this.isShowExportWordDialog = false;
    await this.getSubjectList();
    this.findTestBanksData();
  }
}
</script>

<style lang="scss" scoped>
.enter {
  display: flex;
  flex-direction: column;

  .enter-main {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 100%;
    background-color: #fff;
    padding: 20px;
  }

  .enter-ul {
    padding: 0 100px;
    margin-bottom: 0;
    flex: 1;
    overflow: auto;
  }

  .enter-li {
    display: flex;
    align-items: center;
    margin-top: 10px;
    font-size: 14px;
    border-bottom: 1px solid #e5e5e5;

    .subject-name {
      min-width: 200px;
    }

    .enter-btn {
      margin-left: 100px;
    }
  }
}
</style>

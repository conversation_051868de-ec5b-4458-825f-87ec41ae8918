<!--
 * @Description: 
 * @Author: qmzhang
 * @Date: 2024-02-28 16:10:11
 * @LastEditTime: 2024-11-22 09:22:46
 * @FilePath: \personal-bigdata\src\pages\commentDetail\index.vue
-->
<!--
 * @Descripttion: 题目讲评
 * @Author: Doudou
 * @Date: 2021-11-02 09:01:24
 * @LastEditors: Please set LastEditors
-->
<template>
  <el-container class="wh100 over-hide">
    <el-container>
      <el-main>

        <splitpanes class="default-theme">
          <pane min-size="20" :max-size="showAnswer?80:100">
            题面
          </pane>
          <pane v-if="showAnswer">
            答案
          </pane>
        </splitpanes>

      </el-main>
      <el-aside width="200px" class="middle-helper layout-aside">
        <comment-outline :list="outllineList"></comment-outline>
      </el-aside>
    </el-container>

    <el-footer class="layout-footer clearfix">
      <div class="pull-right">
        <el-button>上一页</el-button>
        <el-button>插入白板</el-button>
        <el-button @click="showAnswer=!showAnswer">显示答案</el-button>
        <el-button>变试题</el-button>
        <el-button>关闭</el-button>
      </div>
    </el-footer>
  </el-container>
</template>

<script lang='ts'>
import { Component, Vue } from "vue-property-decorator";
import CommentOutline, { QuesCardList } from "@/components/CommentOutline/simple.vue";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";

@Component({ components: { CommentOutline, Splitpanes, Pane } })
export default class Index extends Vue {
  // 讲评大纲列表
  outllineList: QuesCardList = [
    {
      fullTitle: "单选题",
      index: 1,
      list: [
        {
          resList: [
            {
              id: 604,
              pointId: "3289",
              resourceName: "酸溶液的pH和浓度之间的换算",
              resourceUrl: "https://v2.leleketang.com/dat/hs/ch/k/video/28247.mp4",
              resourceType: "video",
              sourceType: 2,
              thumbnailUrl: "gece_image/2021/06/28/6d7f372a-b202-47a6-84da-267bb610d204.png",
              dateCreated: "2021-06-28T16:56:24",
              dateModified: "2022-08-19T15:17:11",
            },
          ],
          quesNumber: 1,
          quesName: "1",
          clsFullNum: 1,
          clsErrorNum: 8,
          classScoringRate: 0.1111,
          gradeScoringRate: 0.1111,
          bigId: "5ca4fa6837714cd8b5d77cac41a26f94",
          quesType: 8,
          optionCount: 4,
          quesId: "5ca4fa6837714cd8b5d77cac41a26f94",
          score: 0,
          fullScore: 2,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"0\\",\\"B\\"],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"0\\",\\"A\\"],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"0\\",\\"B\\"],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"0\\",\\"B\\"],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"0\\",\\"A\\"],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"0\\",\\"B\\"],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"2\\",\\"C\\"],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"0\\",\\"B\\"],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"0\\",\\"B\\"]]',
          links: [],
          rightAnswer: "C",
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 0,
          totalIndex: 0,
        },
        {
          resList: [],
          quesNumber: 2,
          quesName: "2",
          clsFullNum: 6,
          clsErrorNum: 3,
          classScoringRate: 0.6667,
          gradeScoringRate: 0.6667,
          bigId: "3e599f7fdf504146a9466a17f27f2ab7",
          quesType: 8,
          optionCount: 4,
          quesId: "3e599f7fdf504146a9466a17f27f2ab7",
          score: 0,
          fullScore: 2,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"2\\",\\"C\\"],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"0\\",\\"B\\"],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"2\\",\\"C\\"],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"2\\",\\"C\\"],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"0\\",\\"B\\"],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"2\\",\\"C\\"],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"2\\",\\"C\\"],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"2\\",\\"C\\"],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"0\\",\\"A\\"]]',
          links: [],
          rightAnswer: "C",
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 1,
          totalIndex: 1,
        },
        {
          resList: [],
          quesNumber: 3,
          quesName: "3",
          clsFullNum: 2,
          clsErrorNum: 7,
          classScoringRate: 0.2222,
          gradeScoringRate: 0.2222,
          bigId: "e4c139bfa15646f186b4165bbf9fc1d8",
          quesType: 8,
          optionCount: 4,
          quesId: "e4c139bfa15646f186b4165bbf9fc1d8",
          score: 0,
          fullScore: 2,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"0\\",\\"\\"],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"0\\",\\"C\\"],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"2\\",\\"D\\"],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"0\\",\\"C\\"],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"0\\",\\"A\\"],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"0\\",\\"C\\"],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"2\\",\\"D\\"],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"0\\",\\"B\\"],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"0\\",\\"C\\"]]',
          links: [],
          rightAnswer: "D",
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 0,
          totalIndex: 2,
        },
        {
          resList: [
            {
              id: 679,
              pointId: "3464",
              resourceName: "卤代烃",
              resourceUrl: "https://v2.leleketang.com/dat/hs/ch/k/video/26342.mp4",
              resourceType: "video",
              sourceType: 2,
              thumbnailUrl: "gece_image/2021/06/29/bcb9daec-b1b8-4369-8b39-9b55f4785a45.png",
              dateCreated: "2021-06-29T09:35:00",
              dateModified: "2022-08-19T15:17:20",
            },
            {
              id: 669,
              pointId: "3446",
              resourceName: "苯的取代反应",
              resourceUrl: "https://v2.leleketang.com/dat/hs/ch/k/video/26328.mp4",
              resourceType: "video",
              sourceType: 2,
              thumbnailUrl: "gece_image/2021/06/29/af26ff5b-8cc3-4cb6-8431-155d67d3ed29.png",
              dateCreated: "2021-06-29T09:26:32",
              dateModified: "2022-08-19T15:17:19",
            },
            {
              id: 735,
              pointId: "3470",
              resourceName: "乙醇的结构和物理性质",
              resourceUrl: "https://v2.leleketang.com/dat/hs/ch/k/video/26110.mp4",
              resourceType: "video",
              sourceType: 2,
              thumbnailUrl: "gece_image/2021/06/29/7da14e21-c424-4ff1-9184-b06d79e936f9.png",
              dateCreated: "2021-06-29T11:32:01",
              dateModified: "2022-08-19T15:17:27",
            },
          ],
          quesNumber: 4,
          quesName: "4",
          clsFullNum: 4,
          clsErrorNum: 5,
          classScoringRate: 0.4444,
          gradeScoringRate: 0.4444,
          bigId: "df622575e1d843f581394dbd600141d8",
          quesType: 8,
          optionCount: 4,
          quesId: "df622575e1d843f581394dbd600141d8",
          score: 0,
          fullScore: 2,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"2\\",\\"D\\"],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"0\\",\\"\\"],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"2\\",\\"D\\"],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"0\\",\\"B\\"],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"0\\",\\"C\\"],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"0\\",\\"C\\"],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"2\\",\\"D\\"],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"0\\",\\"A\\"],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"2\\",\\"D\\"]]',
          links: [],
          rightAnswer: "D",
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 0,
          totalIndex: 3,
        },
        {
          resList: [],
          quesNumber: 5,
          quesName: "5",
          clsFullNum: 2,
          clsErrorNum: 7,
          classScoringRate: 0.2222,
          gradeScoringRate: 0.2222,
          bigId: "68ffa20ffd1a475998febf734fa2cd4c",
          quesType: 8,
          optionCount: 4,
          quesId: "68ffa20ffd1a475998febf734fa2cd4c",
          score: 0,
          fullScore: 2,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"0\\",\\"A\\"],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"0\\",\\"B\\"],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"0\\",\\"D\\"],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"0\\",\\"D\\"],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"2\\",\\"C\\"],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"0\\",\\"B\\"],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"2\\",\\"C\\"],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"0\\",\\"A\\"],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"0\\",\\"B\\"]]',
          links: [],
          rightAnswer: "C",
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 0,
          totalIndex: 4,
        },
        {
          resList: [],
          quesNumber: 6,
          quesName: "6",
          clsFullNum: 3,
          clsErrorNum: 6,
          classScoringRate: 0.3333,
          gradeScoringRate: 0.3333,
          bigId: "25204c75506f47d59c83c8ae99f5b8d4",
          quesType: 8,
          optionCount: 4,
          quesId: "25204c75506f47d59c83c8ae99f5b8d4",
          score: 0,
          fullScore: 2,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"0\\",\\"C\\"],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"0\\",\\"\\"],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"0\\",\\"B\\"],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"2\\",\\"D\\"],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"0\\",\\"A\\"],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"0\\",\\"B\\"],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"2\\",\\"D\\"],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"0\\",\\"A\\"],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"2\\",\\"D\\"]]',
          links: [],
          rightAnswer: "D",
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 0,
          totalIndex: 5,
        },
        {
          resList: [],
          quesNumber: 7,
          quesName: "7",
          clsFullNum: 4,
          clsErrorNum: 5,
          classScoringRate: 0.4444,
          gradeScoringRate: 0.4444,
          bigId: "fe43395779084d45a100dd73d3468891",
          quesType: 8,
          optionCount: 4,
          quesId: "fe43395779084d45a100dd73d3468891",
          score: 0,
          fullScore: 2,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"0\\",\\"B\\"],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"0\\",\\"B\\"],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"2\\",\\"A\\"],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"0\\",\\"C\\"],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"2\\",\\"A\\"],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"0\\",\\"D\\"],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"2\\",\\"A\\"],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"2\\",\\"A\\"],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"0\\",\\"B\\"]]',
          links: [],
          rightAnswer: "A",
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 0,
          totalIndex: 6,
        },
        {
          resList: [],
          quesNumber: 8,
          quesName: "8",
          clsFullNum: 3,
          clsErrorNum: 6,
          classScoringRate: 0.3333,
          gradeScoringRate: 0.3333,
          bigId: "855ee4d2260a496fad1ad03d3e492477",
          quesType: 8,
          optionCount: 4,
          quesId: "855ee4d2260a496fad1ad03d3e492477",
          score: 0,
          fullScore: 2,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"0\\",\\"A\\"],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"2\\",\\"D\\"],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"0\\",\\"A\\"],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"0\\",\\"A\\"],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"0\\",\\"B\\"],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"2\\",\\"D\\"],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"2\\",\\"D\\"],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"0\\",\\"B\\"],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"0\\",\\"B\\"]]',
          links: [],
          rightAnswer: "D",
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 0,
          totalIndex: 7,
        },
        {
          resList: [],
          quesNumber: 9,
          quesName: "9",
          clsFullNum: 5,
          clsErrorNum: 4,
          classScoringRate: 0.5556,
          gradeScoringRate: 0.5556,
          bigId: "86cfc304e71e4370bc36dd68f83d702f",
          quesType: 8,
          optionCount: 4,
          quesId: "86cfc304e71e4370bc36dd68f83d702f",
          score: 0,
          fullScore: 2,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"2\\",\\"C\\"],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"0\\",\\"D\\"],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"2\\",\\"C\\"],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"0\\",\\"A\\"],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"0\\",\\"A\\"],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"2\\",\\"C\\"],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"2\\",\\"C\\"],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"2\\",\\"C\\"],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"0\\",\\"B\\"]]',
          links: [],
          rightAnswer: "C",
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 0,
          totalIndex: 8,
        },
        {
          resList: [],
          quesNumber: 10,
          quesName: "10",
          clsFullNum: 3,
          clsErrorNum: 6,
          classScoringRate: 0.3333,
          gradeScoringRate: 0.3333,
          bigId: "87aea05973ee4ef2960720f312eab4b8",
          quesType: 8,
          optionCount: 4,
          quesId: "87aea05973ee4ef2960720f312eab4b8",
          score: 0,
          fullScore: 2,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"0\\",\\"C\\"],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"0\\",\\"C\\"],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"0\\",\\"C\\"],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"0\\",\\"C\\"],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"0\\",\\"A\\"],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"2\\",\\"B\\"],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"2\\",\\"B\\"],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"2\\",\\"B\\"],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"0\\",\\"D\\"]]',
          links: [],
          rightAnswer: "B",
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 0,
          totalIndex: 9,
        },
      ],
    },
    {
      fullTitle: "填空题",
      index: 2,
      list: [
        {
          resList: [
            {
              id: 707,
              pointId: "3530",
              resourceName: "糖类定义和分类",
              resourceUrl: "https://v2.leleketang.com/dat/hs/ch/k/video/26395.mp4",
              resourceType: "video",
              sourceType: 2,
              thumbnailUrl: "gece_image/2021/06/29/a570720f-858e-4ec8-8dce-19a05c79144c.png",
              dateCreated: "2021-06-29T10:39:59",
              dateModified: "2022-08-19T15:17:23",
            },
          ],
          quesNumber: 11,
          quesName: "11",
          clsFullNum: 9,
          clsErrorNum: 0,
          classScoringRate: 1,
          gradeScoringRate: 1,
          bigId: "2d8ebfc1f5fb494ca1777eed64aca582",
          quesType: 3,
          optionCount: 0,
          quesId: "2d8ebfc1f5fb494ca1777eed64aca582",
          score: 0,
          fullScore: 5,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"5\\",null],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"5\\",null],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"5\\",null],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"5\\",null],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"5\\",null],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"5\\",null],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"5\\",null],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"5\\",null],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"5\\",null]]',
          links: [],
          rightAnswer:
            '<div class=\\"qml-answer\\" data-copyright=\\"xkw.com-1705743737-103441684913045300-dVhyKhCfLmEmsXFolQwJTAJicS51r_arS7OFlmdLNrluyR4CjvKYOHiNZnXyubt5\\"><span class=\\"qml-an-sq\\"><span class=\\"qml-an \\"><img src=\\"https://img.xkw.com/dksih/formula/ccac3c9553957a8825ec214df6f1a20b.svg\\" class=\\"xkw-math-img\\" /></span></span></div>',
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 3,
          totalIndex: 10,
        },
        {
          resList: [],
          quesNumber: 12,
          quesName: "12",
          clsFullNum: 3,
          clsErrorNum: 6,
          classScoringRate: 0.3333,
          gradeScoringRate: 0.3333,
          bigId: "810076cdbcf64890b020edc2b5106b9f",
          quesType: 3,
          optionCount: 0,
          quesId: "810076cdbcf64890b020edc2b5106b9f",
          score: 0,
          fullScore: 5,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"5\\",null],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"0\\",null],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"0\\",null],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"0\\",null],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"0\\",null],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"0\\",null],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"5\\",null],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"5\\",null],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"0\\",null]]',
          links: [],
          rightAnswer:
            '<div class=\\"qml-answer\\" data-copyright=\\"xkw.com-1705743737-103441684913045300-dVhyKhCfLmEmsXFolQwJTGH0_YKvs1AQIYqtvobQaqG21ZE62cN-sAi5ZmYusQZO\\"><span class=\\"qml-an-sq\\"><span class=\\"qml-an \\"><img src=\\"https://img.xkw.com/dksih/formula/bf31876698721a199c7c53c6b320aa86.svg\\" class=\\"xkw-math-img\\" /></span></span></div>',
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 0,
          totalIndex: 11,
        },
        {
          resList: [],
          quesNumber: 13,
          quesName: "13",
          clsFullNum: 5,
          clsErrorNum: 4,
          classScoringRate: 0.5556,
          gradeScoringRate: 0.5556,
          bigId: "8f3b824b9a3144a0b701da33c614d1ba",
          quesType: 3,
          optionCount: 0,
          quesId: "8f3b824b9a3144a0b701da33c614d1ba",
          score: 0,
          fullScore: 5,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"0\\",null],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"0\\",null],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"5\\",null],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"5\\",null],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"5\\",null],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"5\\",null],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"0\\",null],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"5\\",null],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"0\\",null]]',
          links: [],
          rightAnswer:
            '<div class=\\"qml-answer\\" data-copyright=\\"xkw.com-1705743737-103441684913045300-dVhyKhCfLmEmsXFolQwJTKi8LGZNnVRVubPxPAba-vgXM8vN1DbGwLgTV3WO4OCs\\"><span class=\\"qml-an-sq\\"><span class=\\"qml-an \\"><img src=\\"https://img.xkw.com/dksih/formula/8b2a698891d42c70b597f0da4f215f09.svg\\" class=\\"xkw-math-img\\" /></span></span></div>',
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 0,
          totalIndex: 12,
        },
        {
          resList: [],
          quesNumber: 14,
          quesName: "14",
          clsFullNum: 8,
          clsErrorNum: 1,
          classScoringRate: 0.8889,
          gradeScoringRate: 0.8889,
          bigId: "2d76a79077d9437182ceaca8f2a46c07",
          quesType: 3,
          optionCount: 0,
          quesId: "2d76a79077d9437182ceaca8f2a46c07",
          score: 0,
          fullScore: 5,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"5\\",null],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"5\\",null],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"5\\",null],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"5\\",null],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"5\\",null],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"5\\",null],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"5\\",null],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"5\\",null],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"0\\",null]]',
          links: [],
          rightAnswer:
            '<div class=\\"qml-answer\\" data-copyright=\\"xkw.com-1705743737-103441684913045300-dVhyKhCfLmEmsXFolQwJTGH0_YKvs1AQIYqtvobQaqEckd1E0Y8_tkbb7Lq37xZ-\\"><span class=\\"qml-an-sq\\"><span class=\\"qml-an \\"><span style=\\"font-family: \'Times New Roman\';\\">2</span><img src=\\"https://img.xkw.com/dksih/formula/cf298f00799cbf34b4db26f5f63af92f.svg\\" class=\\"xkw-math-img\\" /><span style=\\"font-family: 宋体;\\">﹣</span><span style=\\"font-family: \'Times New Roman\';\\">2</span></span></span></div>',
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 2,
          totalIndex: 13,
        },
        {
          resList: [],
          quesNumber: 15,
          quesName: "15",
          clsFullNum: 7,
          clsErrorNum: 2,
          classScoringRate: 0.7778,
          gradeScoringRate: 0.7778,
          bigId: "e33d23533a4e4f1c832d969f2af6a994",
          quesType: 3,
          optionCount: 0,
          quesId: "e33d23533a4e4f1c832d969f2af6a994",
          score: 0,
          fullScore: 5,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"0\\",null],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"5\\",null],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"5\\",null],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"5\\",null],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"5\\",null],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"5\\",null],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"5\\",null],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"5\\",null],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"0\\",null]]',
          links: [],
          rightAnswer:
            '<div class=\\"qml-answer\\" data-copyright=\\"xkw.com-1705743737-103441684913045300-dVhyKhCfLmEmsXFolQwJTKi8LGZNnVRVubPxPAba-viUzV3RCKxvUP24c21y8rD0\\"><span class=\\"qml-an-sq\\"><span class=\\"qml-an qml-exact\\"><img src=\\"https://img.xkw.com/dksih/formula/59b8136f540df61ed7c9cda3213e53a2.svg\\" class=\\"xkw-math-img\\" /></span></span></div>',
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 2,
          totalIndex: 14,
        },
      ],
    },
    {
      fullTitle: "简答题",
      index: 3,
      list: [
        {
          resList: [],
          quesNumber: 16,
          quesName: "16",
          clsFullNum: 2,
          clsErrorNum: 7,
          classScoringRate: 0.8194,
          gradeScoringRate: 0.8194,
          bigId: "8f41fd8cfde84cb4a3bf05e5399dc1f8",
          quesType: 6,
          optionCount: 0,
          quesId: "8f41fd8cfde84cb4a3bf05e5399dc1f8",
          score: 0,
          fullScore: 8,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"5\\",null],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"6\\",null],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"8\\",null],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"7\\",null],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"6\\",null],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"6\\",null],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"8\\",null],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"7\\",null],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"6\\",null]]',
          links: [],
          rightAnswer:
            '<div class=\\"qml-answer\\" data-copyright=\\"xkw.com-1705743737-103441684913045300-dVhyKhCfLmEmsXFolQwJTCLIArQS3ND_Aoa9cv0UZXguNy9_-uI4-5YIkVh4OsOh\\"><span class=\\"qml-an-sq\\"><span class=\\"qml-an \\"><img src=\\"https://img.xkw.com/dksih/formula/81fb134b2b48acc99213fff6ccfee65f.svg\\" class=\\"xkw-math-img\\" /></span></span></div>',
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 2,
          totalIndex: 15,
        },
        {
          resList: [],
          quesNumber: 17,
          quesName: "17",
          clsFullNum: 1,
          clsErrorNum: 8,
          classScoringRate: 0.7361,
          gradeScoringRate: 0.7361,
          bigId: "bd4bdb5daac745f9955363e613d82d31",
          quesType: 6,
          optionCount: 0,
          quesId: "bd4bdb5daac745f9955363e613d82d31",
          score: 0,
          fullScore: 8,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"5\\",null],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"4\\",null],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"7\\",null],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"6\\",null],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"7\\",null],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"7\\",null],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"8\\",null],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"2\\",null],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"7\\",null]]',
          links: [],
          rightAnswer:
            '<div class=\\"qml-answer\\" data-copyright=\\"xkw.com-1705743737-103441684913045300-dVhyKhCfLmEmsXFolQwJTGH0_YKvs1AQIYqtvobQaqFIsriTGOA5pUBA5JlhqyMQ\\"><span class=\\"qml-an-sq\\"><span class=\\"qml-an \\"><img src=\\"https://img.xkw.com/dksih/formula/dd5a3fb48638f5e373d0c4e74680694d.svg\\" class=\\"xkw-math-img\\" /><span style=\\"font-family: 宋体;\\">，</span><img src=\\"https://img.xkw.com/dksih/formula/f89eef3148f2d4d09379767b4af69132.svg\\" class=\\"xkw-math-img\\" /></span></span></div>',
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 2,
          totalIndex: 16,
        },
        {
          resList: [],
          quesNumber: 18,
          quesName: "18",
          clsFullNum: 3,
          clsErrorNum: 6,
          classScoringRate: 0.7654,
          gradeScoringRate: 0.7654,
          bigId: "625c0b0358f04383837476af3784cb99",
          quesType: 6,
          optionCount: 0,
          quesId: "625c0b0358f04383837476af3784cb99",
          score: 0,
          fullScore: 9,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"8\\",null],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"5\\",null],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"8\\",null],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"9\\",null],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"6\\",null],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"7\\",null],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"9\\",null],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"1\\",null],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"9\\",null]]',
          links: [],
          rightAnswer:
            '<div class=\\"qml-answer\\" data-copyright=\\"xkw.com-1705743737-103441684913045300-dVhyKhCfLmEmsXFolQwJTGH0_YKvs1AQIYqtvobQaqFnHlF1WtFgKYaZJ-Sz2gnq\\"><span class=\\"qml-an-sq\\"><span class=\\"qml-an \\"><span style=\\"font-family: 宋体;\\">（</span><span style=\\"font-family: \'Times New Roman\';\\">1</span><span style=\\"font-family: 宋体;\\">）</span><span style=\\"font-family: \'Times New Roman\';\\">200</span><span style=\\"font-family: 宋体;\\">，</span><span style=\\"font-family: \'Times New Roman\';\\">0.22</span><span style=\\"font-family: 宋体;\\">，</span><span style=\\"font-family: \'Times New Roman\';\\">40</span><span style=\\"font-family: 宋体;\\">；（</span><span style=\\"font-family: \'Times New Roman\';\\">2</span><span style=\\"font-family: 宋体;\\">）见解析；（</span><span style=\\"font-family: \'Times New Roman\';\\">3</span><span style=\\"font-family: 宋体;\\">）</span><span style=\\"font-family: \'Times New Roman\';\\">1000</span></span></span></div>',
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 2,
          totalIndex: 17,
        },
        {
          resList: [],
          quesNumber: 19,
          quesName: "19",
          clsFullNum: 1,
          clsErrorNum: 8,
          classScoringRate: 0.6,
          gradeScoringRate: 0.6,
          bigId: "b6a7084940dd40188a042fcf148c80d7",
          quesType: 6,
          optionCount: 0,
          quesId: "b6a7084940dd40188a042fcf148c80d7",
          score: 0,
          fullScore: 10,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"9\\",null],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"5\\",null],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"6\\",null],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"9\\",null],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"5\\",null],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"7\\",null],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"10\\",null],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"3\\",null],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"0\\",null]]',
          links: [],
          rightAnswer:
            '<div class=\\"qml-answer\\" data-copyright=\\"xkw.com-1705743737-103441684913045300-dVhyKhCfLmEmsXFolQwJTGH0_YKvs1AQIYqtvobQaqHCq7VECe_CkG-9NMUHWyKA\\"><span class=\\"qml-an-sq\\"><span class=\\"qml-an \\"><span style=\\"font-family: 宋体;\\">该建筑物的高度</span><span style=\\"font-family: \'Times New Roman\';font-style: italic;\\">BC</span><span style=\\"font-family: 宋体;\\">约为</span><span style=\\"font-family: \'Times New Roman\';\\">300</span><span style=\\"font-family: 宋体;\\">米．</span></span></span></div>',
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 1,
          totalIndex: 18,
        },
        {
          resList: [
            {
              id: 746,
              pointId: "3800",
              resourceName: "海水中非金属元素的提取",
              resourceUrl: "https://v2.leleketang.com/dat/hs/ch/k/video/26981.mp4",
              resourceType: "video",
              sourceType: 2,
              thumbnailUrl: "gece_image/2021/06/29/ba4c39bf-ce8a-43c2-a59d-8eddc95ff514.png",
              dateCreated: "2021-06-29T11:55:50",
              dateModified: "2022-08-19T15:17:29",
            },
          ],
          quesNumber: 20,
          quesName: "20",
          clsFullNum: 2,
          clsErrorNum: 7,
          classScoringRate: 0.5222,
          gradeScoringRate: 0.5222,
          bigId: "e4f669d5c2bb4f37a9dd365beaac3554",
          quesType: 6,
          optionCount: 0,
          quesId: "e4f669d5c2bb4f37a9dd365beaac3554",
          score: 0,
          fullScore: 10,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"10\\",null],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"5\\",null],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"8\\",null],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"0\\",null],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"5\\",null],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"9\\",null],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"10\\",null],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"0\\",null],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"0\\",null]]',
          links: [],
          rightAnswer:
            '<div class=\\"qml-answer\\" data-copyright=\\"xkw.com-1705743737-103441684913045300-dVhyKhCfLmEmsXFolQwJTIj6-mvPzsfll4hDq8LxmRUAlz6wGdhYt50gInWQwtwp\\">（1）<span class=\\"qml-an-sq\\" id-container=\\"question\\"><span class=\\"qml-an \\"><span style=\\"font-family: 宋体;\\">该公司生产</span><span style=\\"font-family: \'Times New Roman\';font-style: italic;\\">A</span><span style=\\"font-family: 宋体;\\">型无人机每月产量的平均增长率为</span><span style=\\"font-family: \'Times New Roman\';\\">150%</span><span style=\\"font-family: 宋体;\\">；</span></span></span> （2）<span class=\\"qml-an-sq\\" id-container=\\"question\\"><span class=\\"qml-an \\"><span style=\\"font-family: 宋体;\\">公司生产</span><span style=\\"font-family: \'Times New Roman\';font-style: italic;\\">A</span><span style=\\"font-family: 宋体;\\">型号无人机</span><span style=\\"font-family: \'Times New Roman\';\\">75</span><span style=\\"font-family: 宋体;\\">架，生产</span><span style=\\"font-family: \'Times New Roman\';font-style: italic;\\">B</span><span style=\\"font-family: 宋体;\\">型号无人机</span><span style=\\"font-family: \'Times New Roman\';\\">25</span><span style=\\"font-family: 宋体;\\">架成本最小．</span></span></span> </div>',
          goodAnsList: [
            {
              resId: "26355843b2fd457eb2fc7fab84d67e10",
              stuId: "d7e5b41def474e949ba8092cb5cf1179",
              evaluateState: 1,
              stuName: "钟卓婷",
              url: "gece_image/scan_detect/202401/2e39540cde0740b887b694f999307763/image/1705745077132_2.png?x-oss-process=image/crop,x_1137,y_106,w_1026,h_2114",
            },
            {
              resId: "38107237c6c84503877f81ed29ad35cd",
              stuId: "35767bbdb360453cb2ac304b297a0318",
              evaluateState: 1,
              stuName: "刘程伦",
              url: "gece_image/scan_detect/202401/2e39540cde0740b887b694f999307763/image/1705745088545_9.png?x-oss-process=image/crop,x_1138,y_106,w_1024,h_2114",
            },
          ],
          badAnsList: [],
          colorGrade: 0,
          totalIndex: 19,
        },
        {
          resList: [],
          quesNumber: 21,
          quesName: "21",
          clsFullNum: 3,
          clsErrorNum: 6,
          classScoringRate: 0.6667,
          gradeScoringRate: 0.6667,
          bigId: "dbf10f959a5a40b497130418dd4508c5",
          quesType: 6,
          optionCount: 0,
          quesId: "dbf10f959a5a40b497130418dd4508c5",
          score: 0,
          fullScore: 10,
          stus: '[[\\"d7e5b41def474e949ba8092cb5cf1179\\",\\"8\\",null],[\\"7a641a48a7ea43b49496d2783b4d455e\\",\\"5\\",null],[\\"509491b8f8be4eef88f487825d48a5dc\\",\\"10\\",null],[\\"60aee5cfadfd45abb39b28577587f0e9\\",\\"7\\",null],[\\"4460b357bdd64b0497077747f5e4c3cd\\",\\"10\\",null],[\\"d53c7666eadf4bdc841c3de81d4fca80\\",\\"8\\",null],[\\"35767bbdb360453cb2ac304b297a0318\\",\\"10\\",null],[\\"9f05aaf629b0474e9e2691de67ac46ea\\",\\"1\\",null],[\\"2a23705e8e144a8a9292fdcc56df9936\\",\\"1\\",null]]',
          links: [],
          rightAnswer:
            '<div class=\\"qml-answer\\" data-copyright=\\"xkw.com-1705743737-103441684913045300-dVhyKhCfLmEmsXFolQwJTGH0_YKvs1AQIYqtvobQaqF6p7UuQ9AC8tCzsMA4Ulwx\\"><span class=\\"qml-an-sq\\"><span class=\\"qml-an \\"><span style=\\"font-family: 宋体;\\">（</span><span style=\\"font-family: \'Times New Roman\';\\">1</span><span style=\\"font-family: 宋体;\\">）见解析；（</span><span style=\\"font-family: \'Times New Roman\';\\">2</span><span style=\\"font-family: 宋体;\\">）</span><span style=\\"font-family: \'Times New Roman\';\\">9</span><span style=\\"font-family: 宋体;\\">；（</span><span style=\\"font-family: \'Times New Roman\';\\">3</span><span style=\\"font-family: 宋体;\\">）不变，</span><img src=\\"https://img.xkw.com/dksih/formula/827ccf0c04aa941ba20d5f4c6068b46b.svg\\" class=\\"xkw-math-img\\" /></span></span></div>',
          goodAnsList: [],
          badAnsList: [],
          colorGrade: 1,
          totalIndex: 20,
        },
      ],
    },
  ];

  // 显示答案
  showAnswer: boolean = false;
}
</script>

<style scoped lang='scss'>
.layout-footer {
  border-top: 1px solid #ddd;
  line-height: 60px;
}

.layout-aside {
  border-left: 1px solid #ddd;
}

.splitpanes--dragging .splitpanes__pane {
  cursor: col-resize;
}

.splitpanes {
}
</style>
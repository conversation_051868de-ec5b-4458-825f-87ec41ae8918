<template>
  <div>
    <div v-show="!isNoData" ref="subjectAvgChart" style="width: 100%; height: 420px"></div>
    <div v-show="isNoData">
      <div style="text-align: center"><img style="width: 350px" :src="noResImg" alt="" /></div>
      <p style="text-align: center; font-size: 16px; margin-top: 10px">暂无数据!</p>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import {
  ISubjectRateData,
  IFilterData,
} from '@/pages/lookReport/reportOverview/subjectComparison.vue';
@Component
export default class SubjectAvgChart extends Vue {
  public $refs: {
    subjectAvgChart: HTMLElement;
  };

  @Prop() tableData: ISubjectRateData[];
  @Prop() schoolTableData: ISubjectRateData[];
  @Prop() filterData: IFilterData;

  // 缺少资源图片
  noResImg = require('@/assets/no-res.png');
  // 是否没有数据
  isNoData: boolean = true;
  // 图表实例
  subjectAvgChart: EChartsType = null;

  @Watch('tableData', {
    deep: true,
    immediate: true,
  })
  onTableDataChange(val: ISubjectRateData[]) {
    this.renderChart();
  }

  beforeDestroy() {
    if (this.subjectAvgChart) {
      this.subjectAvgChart.dispose();
      this.subjectAvgChart = null;
    }
  }

  // 渲染图表
  async renderChart() {
    if (this.subjectAvgChart) {
      this.subjectAvgChart.dispose();
      this.subjectAvgChart = null;
    }
    const data: ISubjectRateData[] = this.$deepClone(this.tableData) as ISubjectRateData[];
    if (!data.length) {
      this.isNoData = true;
      return;
    }

    const schoolData: ISubjectRateData[] = (
      this.$deepClone(this.schoolTableData) as ISubjectRateData[]
    ).filter(item => data.find(t => t.subjectName === item.subjectName));

    this.isNoData = false;
    await this.$nextTick();

    const subjectNames = data.map(item => item.subjectName);
    this.subjectAvgChart = this.$echarts.init(this.$refs.subjectAvgChart);

    let series: any = [
      {
        name: '平均分',
        type: 'bar',
        barMaxWidth: 50,
        label: {
          show: true,
          position: 'top',
          color: '#409EFF',
          offset: [0, -2],
        },
        data: data.map(item => item.avgScore),
      },
    ];
    if (this.filterData.classId)
      series.push({
        name: '校级平均分',
        type: 'bar',
        barMaxWidth: 50,
        label: {
          show: true,
          position: 'top',
          color: '#83AEDA',
          offset: [0, -2],
        },
        data: schoolData.map(item => item.avgScore),
      });

    let option: EChartsOption = {
      tooltip: {
        // trigger: 'axis',
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: 'none', // 默认为直线，可选为：'line' | 'shadow'
        },
        formatter: params => {
          let tableData = this.tableData.find(item => {
            return params.name == item.subjectName;
          });
          let schoolTableData = this.schoolTableData.find(item => {
            return params.name == item.subjectName;
          });

          let teaLabel = '';
          if (params.seriesName === '校级平均分') {
            teaLabel = '学科组长';
          } else {
            teaLabel = this.filterData?.classId == '' ? '学科组长' : '任课老师';
          }

          let teaName = '';
          if (params.seriesName === '校级平均分') {
            teaName = schoolTableData.teaName;
          } else {
            teaName = tableData.teaName;
          }
          let teaHtml = `${teaLabel}：${teaName || '--'}<br />`;
          return `${params.name}<br />${teaHtml}${params.marker}${params.seriesName}：<b>${params.value}</b>`;

        },
      },
      color: ['#409EFF', '#83AEDA'],
      legend: {
        icon: 'circle',
        top: 10,
        right: 70,
        textStyle: {
          color: '#757C8C',
          fontSize: '14',
        },
        data: ['平均分', '校级平均分'],
      },
      grid: {
        left: '3%',
        right: '7%',
        bottom: '11%',
        containLabel: true,
      },
      yAxis: {
        type: 'value',
        name: '平均分',
      },
      xAxis: {
        type: 'category',
        name: '学科',
        axisLine: {
          lineStyle: {
            color: '#757C8C',
          },
        },
        data: subjectNames,
        axisLabel: {
          interval: 0,
          formatter: function (value) {
            if (value.length > 15) {
              value = value.substring(0, 15) + '..';
            }
            return value;
          },
        },
      },
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 0,
          end: 100,
        },
      ],
      series: series,
    };

    this.subjectAvgChart.setOption(option);
  }
}
</script>

<style lang="scss" scoped></style>

<template>
  <div v-loading="isLoading" style="height: 100%; position: relative">
    <!--主体-->
    <div class="book-main" :class="openType == '' ? 'bank-page' : ''">
      <el-header class="book-options-div">
        <el-row class="book-options-div-left-row">
          <el-col class="book-options-div-left-row-col">
            <span class="search-name">学科:</span>
            <el-select
              v-model="curSubject"
              value-key="code"
              @change="changeSubject"
              class="col-select"
            >
              <el-option
                v-for="item in subjectList"
                :key="item.id"
                :label="item.name"
                :value="item.code"
              ></el-option>
            </el-select>
          </el-col>
          <el-col class="book-options-div-left-row-col">
            <span class="search-name">年级:</span>
            <el-select v-model="curGrade" value-key="code" @change="changeGrade" class="col-select">
              <el-option
                v-for="item in gradeList"
                :key="item.id"
                :label="item.name"
                :value="item.code"
              ></el-option>
            </el-select>
          </el-col>
          <el-col class="book-options-div-left-row-col">
            <span class="search-name">册别:</span>
            <el-select
              v-model="curVolume"
              value-key="code"
              @change="changeVolume"
              class="col-select"
            >
              <el-option
                v-for="item in volumeList"
                :key="item.id"
                :label="item.volumename"
                :value="item.volumecode"
              >
              </el-option>
            </el-select>
          </el-col>
          <el-col class="book-options-div-left-row-col">
            <span class="search-name">出版社:</span>
            <el-select
              v-model="curPublisher"
              value-key="publishercode"
              @change="changePublisher"
              class="col-select"
            >
              <el-option
                v-for="item in publisherList"
                :key="item.id"
                :label="item.publishername"
                :value="item.publishercode"
              ></el-option>
            </el-select>
          </el-col>
        </el-row>

        <el-row style="width: 20%">
          <div class="header__serarch clearfix display_flex">
            <el-input
              class="search__text"
              placeholder="请输入需要搜索的教辅"
              v-model="keyWord"
              @keyup.enter.native="searchBook(keyWord)"
              clearable
            >
            </el-input>
            <div
              class="search__icon el-icon-search display_flex align-items_center justify-content_flex-center"
              @click="searchBook(keyWord)"
            ></div>
          </div>
        </el-row>
      </el-header>
      <h4 v-if="bookList.length > 0" class="tip-msg">{{ tip.data }}</h4>
      <h4 v-else class="tip-msg">{{ tip.noData }}</h4>

      <!--书本列表-->
      <template v-if="bookList.length > 0">
      <ul
        class="book-list"
      >
        <li
          class="book-item"
          v-for="(item, index) in bookList"
          :key="index"
          @click="openBook(item)"
        >
          <el-card>
            <el-image :src="getAliBgImg(item)" fit="cover"></el-image>
            <el-button type="primary" circle class="open-book-btn">打开</el-button>
          </el-card>
        </li>
      </ul>
      <el-pagination class="pagination" background layout="prev, pager, next, jumper" :current-page="page"
        @current-change="loadPaperList" :hide-on-single-page="true" :page-size="limit" :total="totalCount">
      </el-pagination>
    </template>
      <!--无数据缺省-->
      <template v-if="bookList.length == 0 && !isLoading">
        <div class="no-data">
          <no-data text="没有相应的教辅，换其他条件试试吧~"></no-data>
        </div>
      </template>
    </div>
    <!-- <router-view
      style="
        position: absolute;
        top: 0;
        z-index: 1;
        left: 0;
        right: 0;
        background-color: #eff1fa;
        bottom: 0;
      "
    ></router-view> -->
  </div>
</template>

<script>
import NoData from "@/components/noData.vue";
import { uniqueFunc, getAliasInfo } from "@/utils/common";
import { isNullOrUndefined } from "@/utils";
import {
  getSchoolBook,
  volumeList,
  getAllPublisherList,
  getAllSubjectList,
  getAllGradeList,
} from "@/service/testbank";
import { mapGetters } from "vuex";
import { localSave, sessionSave } from "@/utils";

export default {
  data() {
    return {
      pageType: "",
      // 发送类型
      sendType: "",
      // 出题类型 HW:作业 TEST:考试
      workType: "",
      //作业打开类型
      openType: "",
      //中学题库端类型
      bankClientType: "",
      //是否加载中
      isLoading: false,
      // 学科列表
      subjectList: [],
      // 年级列表
      gradeList: [],
      // 册别列表
      volumeList: [],
      // 出版社列表
      publisherList: [],
      // 书本列表
      bookList: [],
      // 当前学科
      curSubject: "",
      // 当前年级
      curGrade: "",
      // 当前册别
      curVolume: "",
      // 当前出版社
      curPublisher: "",
      // 搜索关键词
      keyWord: "",
      // 页码
      page: 1,
      // 书本请求限定
      limit: 12,
      // 书本总数
      totalCount: 0,
      isSchool: true,
      tip: {
        data: "说明: 本页面下显示的教学资源是学校通过合法的途径取得，有合法使用权，且仅用于学校内部教学使用，不用于其他任何商业目的。",
        noData: "说明: 由学校根据自身教学需求，自行或委托第三方上传拥有合法使用权的教学资源",
      },
    };
  },
  components: {
    NoData,
  },
  computed: {
    ...mapGetters(["subjectMap", "loginInfo"]),
  },
  mounted() {},
  created() {
    this.init();
  },
  methods: {
    /**
     * @name: 初始化
     */
    init() {
      this.isLoading = true;
      let promise = Promise.all([
        this.getSubjectList(),
        this.getGradeList(),
        this.getVolumeList(),
        this.getPublisherList(),
      ]);
      promise.then(() => {
        this.getBookList();
      });
    },
    /**
     * @name: 获取学科列表
     * @return: 请求结果
     */
    getSubjectList() {
      return getAllSubjectList({}).then((res) => {
        if (res && res.data.length > 0) {
          this.subjectList = [{ id: "", code: "", name: "全部" }, ...res.data];
          // this.curSubject = UserModule.subjectInfo.subjectCode;
        }
      });
    },
    /**
     * @name: 获取年级列表
     * @return: 请求结果
     */
    getGradeList() {
      return getAllGradeList({}).then((res) => {
        if (res && res.data.length > 0) {
          this.gradeList = [{ id: "", code: "", name: "全部" }, ...res.data];
          this.curGrade = this.gradeList[0].code;
        }
      });
    },
    /**
     * @name: 获取册别列表
     * @return: 请求结果
     */
    getVolumeList() {
      return volumeList({}).then((res) => {
        if (res && res.data.length > 0) {
          this.volumeList = [{ id: "", volumecode: "", volumename: "全部" }, ...res.data];
          this.curVolume = this.volumeList[0].volumecode;
        }
      });
    },
    /**
     * @name: 获取出版社列表
     * @return: 请求结果
     */
    getPublisherList() {
      return getAllPublisherList({}).then((res) => {
        if (res && res.data.length > 0) {
          this.publisherList = [{ id: "", publishercode: "", publishername: "全部" }, ...res.data];
          this.curPublisher = this.publisherList[0].publishercode;
        }
      });
    },
    /**
     * @name: 获取书本列表
     */
    getBookList() {
      this.isLoading = true;
      getSchoolBook({
        keyWord: this.keyWord,
        gradeCode: this.curGrade,
        subjectCode: this.curSubject,
        volumeCode: this.curVolume,
        publisherCode: this.curPublisher,
        isOpenQues: 1,
        type: 1,
        page: this.page,
        limit: this.limit,
        schoolId: this.$sessionSave.get("schoolInfo").id,
      })
        .then((res) => {
          if (res) {
            this.isLoading = false;
            this.bookList = res.data.rows;
            this.totalCount = res.data.total_rows;
          }
        })
        .catch(() => {
          this.isLoading = false;
        });
    },

    /**
     * @name: 下拉加载书本
     */
    loadPaperList(page) {
      this.page = page;
      this.getBookList();
    },

    /**
     * @name: 查询书本
     * @param keyWord 关键词
     */
    searchBook(keyWord) {
      this.keyWord = keyWord;
      this.page = 1;
      this.bookList = [];
      this.getBookList();
    },
    /**
     * @name: 打开书本
     * @param item 书本信息
     */
    openBook(item) {
      let allSubjectList = localSave.get("SUBJECT_LIST");
      let isExit = allSubjectList.find((sub) => {
        return sub.id == item.subId;
      });
      //当前选择的学科与学校学科不匹配
      if (!isExit) {
        this.$message({
          message: "所选教辅与学校学段不匹配！",
          type: "error",
          duration: 1000,
        });
        return;
      }
      this.$router.push({
        path: "/home/<USER>/detail",
        query: {
          bookCode: item.bookCode,
          bookAlias: item.bookAlias || "校本练习册",
          subjectId: item.subId || "10",
        },
      });
      this.$sessionSave.set(
        "testDetail",
        `detail?bookCode=${item.bookCode}&bookAlias=${item.bookAlias}&subjectId=${item.subId}`
      );
    },
    /**
     * @name: 学科切换
     * @param code 学科code
     */
    changeSubject(code) {
      this.curSubject = code;
      this.searchBook(this.keyWord);
    },
    /**
     * @name: 年级切换
     * @param code 年级code
     */
    changeGrade(code) {
      this.curGrade = code;
      this.searchBook(this.keyWord);
    },
    /**
     * @name: 册别切换
     * @param code 册别code
     */
    changeVolume(code) {
      this.curVolume = code;
      this.searchBook(this.keyWord);
    },
    /**
     * @name: 出版社切换
     * @param code 出版社code
     */
    changePublisher(code) {
      this.curPublisher = code;
      this.searchBook(this.keyWord);
    },
    /**
     * @name: 获取阿里云水印背景图片
     * @param item 图片信息
     * @return: 水印后的图片信息
     */
    getAliBgImg(item) {
      return getAliasInfo(item);
    },
  },
};
</script>


<style scoped lang="scss">
.book-main {
  height: calc(100% - 70px);
  background-color: #fff;

  .book-options-div {
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 16px 18px;
    height: unset !important;

    .search-name {
      display: flex;
      align-items: center;
      margin-right: 20px;
    }
  }

  .tip-msg {
    padding: 0 calc(2% + 15px);
    text-align: left;
    background-color: #fffaeb;
    font: 600 16px/60px 微软雅黑, \5b8b\4f53;
    color: #404758;
  }

  .book-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    // height: calc(100% - 120px);
    padding: 0 2%;
    overflow-y: auto;
    list-style-type: none;

    .book-item {
      position: relative;
      width: 14%;
      height: 45%;
      margin: 1%;
      padding: 0;
      cursor: pointer;

      .el-image,
      image {
        width: 100%;
        height: 100%;
      }

      ::v-deep .el-card__body {
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      p {
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        min-height: 60px;
        background-color: rgba(0, 0, 0, 0.6);
        color: #fff;
      }

      &:hover {
        .open-book-btn {
          display: block;
        }
      }

      .open-book-btn {
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        padding: 20px;
        transform: translate(-50%, -50%);
      }
    }
  }

  .no-data {
    height: 100%;

    &.isSchool {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
    }

    .el-image {
      margin: 0;
    }
  }
  .pagination{
    text-align: center;
  }
}

.bank-page {
  position: relative;
  height: 100%;
}

.book-options-div-left-row {
  width: 80%;
  display: flex;
}

.book-options-div-left-row-col {
  display: flex;
  align-items: center;
}

.col-select {
  width: 65%;
}

.book-options-div-left-row-col:not(:first-child) {
  margin-left: -20px;
}
.header__serarch {
  display: flex;
  width: 240px;

  .search__icon {
    width: 38px;
    font-size: 18px;
    color: #fff;
    background: #409eff;
    border-radius: 0 3px 3px 0;
    outline: none;
    cursor: pointer;
    border: 1px solid #dcdfe6;
    border-left: 0;
  }
}
</style>

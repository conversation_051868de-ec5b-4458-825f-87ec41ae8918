<!--
 * @Descripttion: 
 * @Author: 小圆
 * @Date: 2024-01-13 09:08:51
 * @LastEditors: 小圆
-->
<!-- 图片缩放组件 -->
<template>
    <v-touch ref="imagePinch" ondragstart='return false;' tag="div" class="image-pinch" :class="imgClass"
      :swipe-options="{ threshold: '2', velocity: '2' }" :pan-options="{ threshold: 20 }" :pinch-options="{ threshold: 0 }"
      @swipeleft="swipeleft" @swiperight="swiperight" @swipeup="swipeup" @swipedown="swipedown" @panstart="elementPanStart"
      @panmove="elementPanMove" @panend="elementPanEnd" @pinchstart="elementPinchStart" @pinchmove="elementPinchMove"
      @pinchend="elementPinchEnd" @mousewheel.native="mousewheelScale">
      <div class="img-box" ref="imgBox" :class="imgData.noanimate" :style="{
        transform: `translate3d(${imgData.deltaX || 0}px,${imgData.deltaY || 0}px,0) scale3d(${imgData.scale || 1},${imgData.scale || 1},1)`,
        transformOrigin: `${centerOrigin ? 'center center' : '0px 0px'}`
      }">
        <div class="rorate-box" :style="{
          transform: `rotate3d(0,0,1,${imgData.deg}deg)`,
        }">
          <slot></slot>
        </div>
      </div>
    </v-touch>
  </template>
  
  <script>
  export default {
    name: 'image-pinch',
    props: {
      // 禁止同时移动和缩放
      banPanScaleSameTime: {
        default: false,
      },
      imgClass: {
        default: '',
      },
      mousewheel: {
        default: true,
      },
      // 移动边界控制
      frontierControl: {
        default: false,
      },
      maxSize: {
        default: 3,
      },
      minSize: {
        default: 1,
      },
    },
    data() {
      return {
        // 禁止移动
        forbidPan: false,
        // 双指缩放缓存缩放值
        scaleCache: 1,
        // 元素平移缓存值
        deltaCache: {
          x: 0,
          y: 0,
        },
        imgData: {
          maxWidth: '100%',
          maxHeight: '100%',
          deg: 0,
        },
        // 当前是否处于缩放事件
        pinchEvent: false,
        // 容器偏移量
        cOffsetX: 0,
        cOffsetY: 0,
        // 是否居中
        centerOrigin: false,
        // 按钮放大居中
        btnZoomCenter: false,
        isDoubleZoom: false,
        // 锁定操作状态,pan|pinch
        lockOperState: '',
        // 锁定缩放中心
        lockPinchCenter: null,
        // 锁定的缩放值
        lockPinchScale: 1,
        // 图片刚好适应屏幕的尺寸缩放值
        fitWindowScale: 1
      };
    },
    methods: {
      // 计算容器偏移量
      cacluCOffset() {
        let rect = this.$refs.imagePinch.$el.getBoundingClientRect();
  
        this.cOffsetX = rect.left;
        this.cOffsetY = rect.top;
      },
  
      // 当图片比例≠1，调用内部滑动
      swipeleft: function (e) {
        return;
        if (this.forbidPan) return;
  
        if (this.imgData.scale && this.imgData.scale != 1) {
          this.$emit('swipeleft');
        }
      },
  
      // 当图片比例≠1，调用内部滑动
      swiperight: function (e) {
        return;
        if (this.forbidPan) return;
  
        if (this.imgData.scale && this.imgData.scale != 1) {
          this.$emit('swiperight');
        }
      },
  
      // 当图片比例≠1，调用内部滑动
      swipeup: function (e) {
        this.$emit('swipeup');
      },
  
      // 当图片比例≠1，调用内部滑动
      swipedown: function (e) {
        this.$emit('swipedown');
      },
  
      // 按钮缩放事件
      buttonScale(type) {
        let scalc = this.scaleCache;
        // 区分按钮放大居中移动
        this.btnZoomCenter = true;
        this.cacluCOffset();
        switch (type) {
          case 'zoomin':
            if (this.scaleCache < this.maxSize) {
              this.changeScale((scalc += 0.2));
            } else {
              this.scaleCache = this.maxSize;
            }
            break;
          case 'zoomout':
            if (this.scaleCache > this.minSize) {
              this.changeScale((scalc -= 0.2));
            } else {
              this.scaleCache = this.minSize;
            }
            break;
        }
  
        // this.$set(this.imgData, 'scale', this.scaleCache);
      },
  
      // 滚轮事件缩放
      mousewheelScale(ev) {
        if (!this.mousewheel) return;
  
        this.cacluCOffset();
        this.scaleCache = this.imgData.scale || 1;
        let _scale = ev.wheelDelta > 0 ? 0.2 : -0.2;
        this.pinchResponse(
          {
            x: ev.pageX - this.cOffsetX,
            y: ev.pageY - this.cOffsetY,
          },
          this.scaleCache + _scale
        );
        this.notifyScaleChanged(this.scaleCache + _scale);
        this.getCurrentDeafaultScale();
      },
  
      // 双指缩放 - 开始
      elementPinchStart(ev) {
        if (this.banPanScaleSameTime) {
          this.lockPinchCenter = ev.center;
          this.lockPinchScale = ev.scale;
        }
  
        ev.preventDefault();
        this.cacluCOffset();
        this.$set(this.imgData, 'noanimate', 'noanimate');
      },
  
      // 双指缩放 - 运行
      elementPinchMove(ev) {
        this.pinchEvent = true;
        this.isDoubleZoom = true;
        let center = ev.center;
  
        if (this.banPanScaleSameTime) center = this.checkLockState(ev);
        this.pinchResponse(center, this.scaleCache * ev.scale, false, ev);
      },
  
      // 检查是否需要锁定状态
      checkLockState(ev) {
        let center = ev.center;
        let distance = this.getCoordsDistance(ev.center, this.lockPinchCenter);
        let deltaScale = Math.abs(1 - this.lockPinchScale);
        let panThreshold = 20;
        let scaleThreshold = 0.05;
  
        if (this.lockOperState) {
          // 按锁定状态执行
          if (this.lockOperState === 'pan') {
            ev.scale = this.lockPinchScale;
          } else {
            center = this.lockPinchCenter;
            ev.deltaX = 0;
            ev.deltaY = 0;
          }
        } else {
          // 按阈值执行
          if (distance >= panThreshold) {
            this.lockOperState = 'pan';
            ev.scale = this.lockPinchScale;
          } else if (deltaScale >= scaleThreshold) {
            this.lockOperState = 'pinch';
            center = this.lockPinchCenter;
            ev.deltaX = 0;
            ev.deltaY = 0;
          } else {
            // this.lockPinchCenter = ev.center;
            this.lockPinchScale = ev.scale;
          }
        }
  
        return center;
      },
  
      // 双指缩放 - 结束
      elementPinchEnd(ev) {
        this.lockOperState = '';
  
        if (
          this.pinchEvent &&
          (this.imgData.scale > this.maxSize || this.imgData.scale < this.minSize)
        ) {
          this.imgData.scale =
            this.imgData.scale > this.maxSize
              ? this.maxSize
              : this.imgData.scale < this.minSize
                ? this.minSize
                : this.imgData.scale;
  
          this.$set(this.imgData, 'scale', this.imgData.scale);
          let mouseX = ev.center.x - this.cOffsetX,
            mouseY = ev.center.y - this.cOffsetY,
            deltaX = 0,
            deltaY = 0;
  
          let detalScale = 1 - this.imgData.scale / this.scaleCache;
          // 换算缩放位移
          deltaX = (mouseX - this.deltaCache.x) * detalScale;
          deltaY = (mouseY - this.deltaCache.y) * detalScale;
          this.panResponse(deltaX, deltaY);
          this.pinchEvent = false;
        }
        this.scaleCache = this.imgData.scale || 1;
        this.deltaCache = {
          x: 0,
          y: 0,
        };
        this.$set(this.imgData, 'noanimate', '');
        this.notifyScaleChanged(this.imgData.scale);
        this.getCurrentDeafaultScale();
      },
  
      // 通过坐标获取两点距离
      getCoordsDistance(pointA, pointB) {
        return Math.sqrt(Math.pow(pointA.y - pointB.y, 2) + Math.pow(pointA.x - pointB.x, 2));
      },
  
      // 元素平移 - 开始
      elementPanStart(ev) {
        if (this.forbidPan) return;
        this.lockOperState = 'pan';
  
        this.$set(this.imgData, 'noanimate', 'noanimate');
        this.panResponse(ev.deltaX, ev.deltaY);
      },
  
      // 元素平移 - 运行
      elementPanMove(ev) {
        if (this.forbidPan) return;
  
        this.panResponse(ev.deltaX, ev.deltaY);
      },
  
      // 元素平移 - 结束
      elementPanEnd(ev) {
        this.lockOperState = '';
        if (this.forbidPan) return;
        this.getCurrentDeafaultScale();
        this.$set(this.imgData, 'noanimate', '');
      },
  
      // 响应元素缩放 - recover:复原
      pinchResponse(center, scale, recover, pinchEvent) {
        let pinchDeltaX = 0,
          pinchDeltaY = 0,
          pinchScale = 1;
  
        if (pinchEvent) {
          pinchScale = pinchEvent.scale;
          pinchDeltaX = pinchEvent.deltaX * pinchScale;
          pinchDeltaY = pinchEvent.deltaY * pinchScale;
        }
        scale = Math.floor(scale * 100) / 100;
  
        if (!this.pinchEvent) {
          scale = scale > this.maxSize ? this.maxSize : scale < this.minSize ? this.minSize : scale;
        }
  
        let mouseX = center.x - this.cOffsetX,
          mouseY = center.y - this.cOffsetY,
          deltaX = 0,
          deltaY = 0;
  
        if (recover) {
          deltaX = -this.deltaCache.x;
          deltaY = -this.deltaCache.y;
          // 换算缩放位移
        } else {
          let detalScale = 1 - scale / this.scaleCache;
          // 换算缩放位移
          deltaX = (mouseX - this.deltaCache.x) * detalScale;
          deltaY = (mouseY - this.deltaCache.y) * detalScale;
        }
        this.$set(this.imgData, 'scale', scale);
        this.panResponse(deltaX + pinchDeltaX, deltaY + pinchDeltaY);
      },
  
      // 响应元素平移
      panResponse(deltaX, deltaY) {
        let X = (deltaX += this.deltaCache.x);
        let Y = (deltaY += this.deltaCache.y);
        if (this.frontierControl && !this.btnZoomCenter && !this.isDoubleZoom) {
          let imgBox = this.$refs.imgBox;
          let opeDom = imgBox.childNodes[0].childNodes[0].childNodes[0];
          // 高度是否溢出
          let heightOverflow = opeDom.offsetHeight * this.scaleCache > imgBox.offsetHeight;
          // 宽度是否溢出
          let widthOverflow = opeDom.offsetWidth * this.scaleCache > imgBox.offsetWidth;
          if (deltaY > this.imgData.deltaY) {
            //  图片向下移动
            if (heightOverflow) {
              // 高度溢处判定
              // 如溢出则判定图片下拉判定
              if (deltaY + opeDom.offsetTop * this.scaleCache >= 0) {
                Y = -opeDom.offsetTop * this.scaleCache;
              }
            } else {
              // 如未溢出则执行图片下推判定
              if (
                deltaY + opeDom.offsetTop * this.scaleCache + opeDom.offsetHeight * this.scaleCache >=
                imgBox.offsetHeight
              ) {
                Y =
                  imgBox.offsetHeight -
                  opeDom.offsetHeight * this.scaleCache -
                  opeDom.offsetTop * this.scaleCache;
              }
            }
          } else {
            // 图片向上移动
            if (heightOverflow) {
              // 如溢出则执行图片上拉判定
              if (
                deltaY + opeDom.offsetTop * this.scaleCache + opeDom.offsetHeight * this.scaleCache <=
                imgBox.offsetHeight
              ) {
                Y = -(
                  imgBox.offsetHeight * this.scaleCache -
                  imgBox.offsetHeight -
                  opeDom.offsetTop * this.scaleCache
                );
              }
            } else {
              // 如未溢出则执行图片上推操作
              if (deltaY + opeDom.offsetTop * this.scaleCache <= 0) {
                Y = -opeDom.offsetTop * this.scaleCache;
              }
            }
          }
          if (deltaX > this.imgData.deltaX) {
            // 向左
            if (widthOverflow) {
              // 如溢出则执行图片左拉判定
              if (deltaX + opeDom.offsetLeft * this.scaleCache >= 0) {
                X = -opeDom.offsetLeft * this.scaleCache;
              }
            } else {
              // 如未溢出则执行执行图片左推判定
              if (
                deltaX + opeDom.offsetLeft * this.scaleCache + opeDom.offsetWidth * this.scaleCache >=
                imgBox.offsetWidth
              ) {
                X =
                  imgBox.offsetWidth -
                  opeDom.offsetWidth * this.scaleCache -
                  opeDom.offsetLeft * this.scaleCache;
              }
            }
          } else {
            // 向右
            if (widthOverflow) {
              // 如溢出则执行图片右拉判定
              if (
                deltaX + opeDom.offsetLeft * this.scaleCache + opeDom.offsetWidth * this.scaleCache <=
                imgBox.offsetWidth
              ) {
                X = -(
                  imgBox.offsetWidth * this.scaleCache -
                  imgBox.offsetWidth -
                  opeDom.offsetLeft * this.scaleCache
                );
              }
            } else {
              // 如未溢出则执行图片右推判定
              if (deltaX + opeDom.offsetLeft * this.scaleCache <= 0) {
                X = -opeDom.offsetLeft * this.scaleCache;
              }
            }
          }
        }
        if (this.btnZoomCenter) {
          this.btnZoomCenter = false;
        }
        if (this.isDoubleZoom) {
          this.isDoubleZoom = false;
        }
        // this.btnZoomCenter = false;
        this.$set(this.imgData, 'deltaX', X);
        this.$set(this.imgData, 'deltaY', Y);
      },
      getCurrentDeafaultScale() {
        this.deltaCache.x = this.imgData.deltaX || 0;
        this.deltaCache.y = this.imgData.deltaY || 0;
        this.scaleCache = this.imgData.scale || 1;
      },
      // 计算图片最大长宽
      calculMaxLength() {
        let _curimgBox = this.$refs.imgBox,
          _offset = 0;
        // 根据图片的角度控制最大长宽
        if ((this.imgData.deg / 90) % 2) {
          this.imgData.maxWidth = `${_curimgBox.offsetHeight - _offset}px`;
          this.imgData.maxHeight = `${_curimgBox.offsetWidth - _offset}px`;
        } else {
          this.imgData.maxWidth = `${_curimgBox.offsetWidth - _offset}px`;
          this.imgData.maxHeight = `${_curimgBox.offsetHeight - _offset}px`;
        }
      },
  
      // 旋转
      rotate(deg) {
        this.imgData.deg = deg || this.imgData.deg;
        this.imgData.deg += 90;
        this.calculMaxLength();
        this.$emit('rotate', this.imgData.deg);
      },
  
      // 重置元素缩放位移和旋转
      reset() {
        this.$set(this.imgData, 'scale', 1);
        this.$set(this.imgData, 'deltaX', 0);
        this.$set(this.imgData, 'deltaY', 0);
        this.$set(this.imgData, 'maxWidth', '100%');
        this.$set(this.imgData, 'maxHeight', '100%');
        this.deltaCache.x = 0;
        this.deltaCache.y = 0;
        this.scaleCache = 1;
        this.$set(this.imgData, 'deg', 0);
        this.notifyScaleChanged(this.scaleCache);
      },
  
      // 点击外部按钮缩放时，
      changeScale(scale, init = false) {
        let _rectBound = this.$refs.imgBox.getBoundingClientRect();
        let centerX = (init ? 0 : _rectBound.left) + _rectBound.width / 2;
        let centerY = (init ? 0 : _rectBound.top) + _rectBound.height / 2;
        this.pinchResponse(
          {
            x: centerX,
            y: centerY,
          },
          scale
        );
        this.notifyScaleChanged(scale);
        this.getCurrentDeafaultScale();
      },
  
      // 通知缩放变化
      notifyScaleChanged(scale) {
        // this.forbidPan = scale <= this.fitWindowScale;
        this.forbidPan = false;
        this.$emit('scale', { scale, forbidPan: this.forbidPan });
      },
  
      // 图片适应窗口
      async fitWindow(imgDom) {
        this.forbidPan = false;
        this.$set(this.imgData, 'noanimate', 'noanimate');
        this.reset();
        await this.$nextTick();

        let imgWidth = imgDom.width,
          imgHeight = imgDom.height;
        let rect = this.$refs.imagePinch.$el.getBoundingClientRect();
        
        if (imgHeight > rect.height) {
          this.fitWindowScale = rect.height / imgHeight;
          this.changeScale(this.fitWindowScale, true)
        } else if (imgWidth > rect.width) {
          this.fitWindowScale = rect.width / imgWidth;
          this.changeScale(this.fitWindowScale, true)
        }
  
        setTimeout(() => {
          this.$set(this.imgData, 'noanimate', '');
        }, 350)
      }
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .image-pinch {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    /*垂直居中*/
    justify-content: center;
    /*水平居中*/
    width: 100%;
    height: 100%;
    overflow: hidden;
    transition: transform 0.3s;
    -webkit-transition: -webkit-transform 0.3s;
  
    .img-box {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      /*垂直居中*/
      justify-content: center;
      /*水平居中*/
      transition: transform 0.3s;
      -webkit-transition: -webkit-transform 0.3s;
  
      &.noanimate {
        transition: none;
      }
    }
  
    .rorate-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      /*垂直居中*/
      justify-content: center;
      /*水平居中*/
      transition: max-width 0.25s, max-height 0.25s, transform 0.3s;
      -webkit-transition: max-width 0.25s, max-height 0.25s, -webkit-transform 0.3s;
      width: 100%;
      height: 100%;
    }
  }
  </style>
  
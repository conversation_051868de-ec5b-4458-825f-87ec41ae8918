/**
 * @Description:
 * @Author: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>@class30.com
 * @Date: 2024-05-13 15:28:22
 * @LastEditors: liu<PERSON><PERSON> y<PERSON><PERSON>@class30.com
 * @LastEditTime: 2024-05-15 16:14:28
 */
import * as httpApi from './index';

const kklUrl = process.env.VUE_APP_KKLURL;

/**
 * api转换
 * @type {{POST: API.POST, GET: API.GET,POSTSTRING: API.POSTSTRING}}
 */
const API = {
  GET: function (url, params) {
    return httpApi.GET(url, params, kklUrl);
  },
  POST: function (url, params) {
    return httpApi.POST(url, params, kklUrl);
  },
  POSTJson: function (url, params) {
    return httpApi.POSTJson(url, params, kklUrl);
  },
};

/**
 * 添加帮助中心分类
 * @param params
 * @returns {AxiosPromise}
 */
export const saveHelpCategoryAPI = params => {
  return API.POSTJson('/phelp/_/save-category', params);
};
/**
 * 修改帮助中心分类
 * @param params
 * @returns {AxiosPromise}
 */
export const editHelpCategoryAPI = params => {
  return API.POSTJson('/phelp/_/edit-category', params);
};
/**
 * 删除帮助中心分类
 * @param params
 * @returns {AxiosPromise}
 */
export const deleteHelpCategoryAPI = params => {
  return API.POSTJson('/phelp/_/remove-category', params);
};
/**
 * 获取帮助中心分类列表
 * @param params
 * @returns {AxiosPromise}
 */
export const getHelpCategoryListAPI = params => {
  return API.GET('/phelp/_/list-category', params);
};
/**
 * 添加帮助中心文章
 * @param params
 * @returns {AxiosPromise}
 */
export const saveHelpArticleAPI = params => {
  return API.POSTJson('/phelp/_/save-article', params);
};
/**
 * 修改帮助中心文章
 * @param params
 * @returns {AxiosPromise}
 */
export const editHelpArticleAPI = params => {
  return API.POSTJson('/phelp/_/edit-article', params);
};
/**
 * 删除帮助中心文章
 * @param params
 * @returns {AxiosPromise}
 */
export const deleteHelpArticleAPI = params => {
  return API.POSTJson('/phelp/_/remove-article', params);
};
/**
 * 获取帮助中心文章
 * @param params
 * @returns {AxiosPromise}
 */
export const getHelpArticleListAPI = params => {
  return API.GET('/phelp/_/list-article', params);
};
/**
 * 根据id获取帮助中心文章
 * @param params
 * @returns {AxiosPromise}
 */
export const getHelpArticleAPI = params => {
  return API.GET('/phelp/_/get-article', params);
};


/**
 * 试卷讲评匹配
 * @param {*} params : {
  examId: string | number,
  subjectId: string | number,
  cardType: 0 | 1, 0系统通过题号匹配，1系统通过回流匹配
}
 * @returns 
 */
export const matchReviewAPI = (params) => {
  return API.GET('/phelp/_/match-review', params);
};

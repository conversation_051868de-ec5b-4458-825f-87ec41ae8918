<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-05-30 15:58:23
 * @LastEditors: 小圆
-->
<template>
  <el-dialog
    title="修改密码"
    :visible.sync="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="password-tip" v-if="isWeak">
      <p><i class="el-icon-warning"></i>当前您的账号密码过于简单，为了数据安全，请修改密码。</p>
    </div>

    <el-form :model="passwordForm" ref="passwordForm" class="password-form">
      <el-form-item prop="oldPass">
        <el-input
          class="password-input"
          v-model="passwordForm.oldPass"
          type="password"
          placeholder="请输入原密码"
          autocomplete="off"
        >
          <span slot="prefix">原密码</span>
        </el-input>
      </el-form-item>

      <el-form-item prop="newPass">
        <el-input
          class="password-input"
          v-model="passwordForm.newPass"
          type="password"
          placeholder="请输入8-16位且至少包含数字、字母或特殊字符两种"
          autocomplete="off"
        >
          <span slot="prefix">新密码</span>
        </el-input>
      </el-form-item>

      <el-form-item prop="confirmPass">
        <el-input
          class="password-input"
          v-model="passwordForm.confirmPass"
          type="password"
          placeholder="请再次输入新密码"
          autocomplete="off"
        >
          <span slot="prefix">确认密码</span>
        </el-input>
      </el-form-item>
    </el-form>
    <div class="error-message" v-if="errorMessage">{{ errorMessage }}</div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { modifyPassWordAPI } from '@/service/api';
import { checkPasswordStrength } from '@/utils/index';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

@Component
export default class ChangePwdDialog extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: false }) isWeak!: boolean;

  dialogVisible = this.visible;
  errorMessage = '';
  passwordForm = {
    oldPass: '',
    newPass: '',
    confirmPass: '',
  };

  /**
   * @description: 限制输入框只能输入数字、字母、下划线
   */
  @Watch('passwordForm.newPass')
  checkNewPassInput(newVal: any, oldVal: any) {
    this.passwordForm.newPass = newVal.replace(/[^!-~]/g, '');
  }

  @Watch('passwordForm.confirmPass')
  checkConfirmPassInput(newVal, oldVal) {
    this.passwordForm.confirmPass = newVal.replace(/[^!-~]/g, '');
  }

  async handleConfirm() {
    if (!this.passwordForm.oldPass) {
      this.errorMessage = '请输入原密码';
      return;
    }

    const pwdCheck = checkPasswordStrength(this.passwordForm.newPass);
    if (!pwdCheck.valid) {
      this.errorMessage = pwdCheck.message;
      return;
    }

    if (this.passwordForm.newPass !== this.passwordForm.confirmPass) {
      this.errorMessage = '两次输入的密码不一致';
      return;
    }

    this.errorMessage = '';

    try {
      await modifyPassWordAPI({
        oldPassWord: this.passwordForm.oldPass,
        newPassWord: this.passwordForm.newPass,
        userId: this.$sessionSave.get('loginInfo').id,
      });

      this.$message.success('密码修改成功');
      this.dialogVisible = false;
      this.$emit('success');
    } catch (error) {
      this.errorMessage = error.msg || '密码修改失败';
    }
  }

  handleCancel() {
    this.dialogVisible = false;
    this.$emit('cancel');
  }

  handleClose() {
    this.handleCancel();
  }

  @Watch('visible')
  onVisibleChange(val: boolean) {
    this.dialogVisible = val;
  }

  @Watch('dialogVisible')
  onDialogVisibleChange(val: boolean) {
    this.$emit('update:visible', val);
  }
}
</script>

<style lang="scss" scoped>
.password-tip {
  background-color: #fdf6ec;
  padding: 10px;
  margin-bottom: 20px;
  border-radius: 4px;
  color: #e6a23c;
  line-height: 1.5;

  .el-icon-warning {
    margin-right: 8px;
  }
}

.password-form {
  .password-input {
    margin-bottom: 15px;

    ::v-deep .el-input__inner {
      padding-left: 80px;
    }

    ::v-deep .el-input__prefix {
      left: 10px;
      color: #606266;
    }
  }
}

.password-rules {
  font-size: 14px;
  color: #909399;
  margin: 15px 0;

  p {
    margin: 5px 0;
  }
}

.error-message {
  color: #f56c6c;
  font-size: 13px;
  margin-top: 10px;
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}
</style>

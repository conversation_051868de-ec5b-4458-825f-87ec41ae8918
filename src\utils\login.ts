import { getLoginInfoByToken, getLoginInfoByUserId, getUserSchoolRole } from '@/service/api';
import { sessionSave } from '@/utils/index.js';
import { setToken } from '@/service/auth';
import UserRole from './UserRole';
import { Message } from '@iclass/element-ui';

/**
 * 根据用户ID登录
 * @param userId 用户ID
 * @returns 返回布尔值，表示登录是否成功
 */
export async function loginByUserId(userId) {
  try {
    const res = await getLoginInfoByUserId(userId);
    if (res.code !== 1) {
      return false;
    }
    const loginInfo = res.data;
    sessionStorage.clear();
    setToken(loginInfo.token);
    const isCheck = await setAccountType(loginInfo);
    if (isCheck) {
      UserRole.dispose();
    }
    return isCheck;
  } catch (error) {
    Message.error(error.msg);
    return false;
  }
}

/**
 * 通过用户Token登录
 * @param token 用户Token
 * @returns 如果登录成功，返回true；否则返回false
 */
export async function loginByUserToken(token) {
  try {
    const res = await getLoginInfoByToken(token);
    if (res.code !== 1) {
      return false;
    }
    const loginInfo = { ...res.data, token: token }; // 根据Token接口返回用户信息缺少用户Token，需手动拼接
    sessionStorage.clear();
    setToken(loginInfo.token);
    const isCheck = await setAccountType(loginInfo);
    if (isCheck) {
      UserRole.dispose();
    }
    return isCheck;
  } catch (error) {
    Message.error(error.msg);
    return false;
  }
}

/**
 * 设置账户类型
 * @param loginInfo 登录信息对象
 * @returns 如果设置成功，返回true；否则返回false
 */
async function setAccountType(loginInfo) {
  if (loginInfo.admin_type == 2) {
    sessionSave.set('loginInfo', {
      account_type: 4,
      ...loginInfo,
    });
    return true;
  }

  if (loginInfo.user_type == 5 && loginInfo.admin_type != 2) {
    sessionSave.set('loginInfo', {
      account_type: 5,
      ...loginInfo,
    });
    return true;
  }

  if (loginInfo.user_type == 6) {
    sessionSave.set('loginInfo', {
      account_type: 5,
      ...loginInfo,
    });
    return true;
  }

  if (loginInfo.user_type == 1 && loginInfo.admin_type != 2) {
    sessionSave.set('loginInfo', loginInfo);
    return setUserSchoolRole(loginInfo);
  }

  if (loginInfo.user_type != 1 && loginInfo.user_type != 5 && loginInfo.admin_type != 2) {
    return false;
  }
  return false;
}

/**
 * 根据登录信息获取用户在学校的角色
 * @param loginInfo 登录信息
 * @returns 返回一个布尔值，表示是否获取成功
 */
async function setUserSchoolRole(loginInfo) {
  try {
    const res = await getUserSchoolRole({
      schoolId: loginInfo.schoolid,
      userId: loginInfo.id,
    });
    if (res.code == 1) {
      if (res.data.roleType == 0 || res.data.roleType == 4) {
        // 普通教师
        sessionSave.set('loginInfo', {
          account_type: 1,
          ...loginInfo,
        });
        return true;
      }
      if (res.data.roleType == 1) {
        // 年级组长
        sessionSave.set('loginInfo', {
          account_type: 3,
          ...loginInfo,
        });
        return true;
      }
      if (res.data.roleType == 2 || res.data.roleType == 3) {
        // 备课组长
        sessionSave.set('loginInfo', {
          account_type: 2,
          ...loginInfo,
        });
        return true;
      }
    }
    return false;
  } catch (error) {
    return false;
  }
}

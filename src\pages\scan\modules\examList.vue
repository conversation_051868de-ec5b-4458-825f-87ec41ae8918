<template>
  <div class="handled-info-list">
    <ul
      id="popoverUl"
      class="examReport__list list-none"
      v-if="examList.length"
      element-loading-text="加载中..."
    >
      <li v-for="(item, index) in examList" :key="index">
        <div class="examReport__item">
          <div class="exam-name-area">
            <div style="width: 91%">
              <div class="exam-name">
                <div class="exam-title">
                  {{ item.examName }}
                </div>
                <div class="exam-tag">
                  <el-tag size="small" class="tag-item" type="info">{{ item.categoryName }}</el-tag>
                  <el-tag
                    size="small"
                    effect="plain"
                    class="tag-item"
                    :type="item.source == 4 ? 'success' : item.source == 3 ? '' : 'warning'"
                    >{{ getSource(item.source) }}</el-tag
                  >
                  <!-- <el-tag size="small" v-if="item.analysisMode == 1" type="warning">新高考</el-tag> -->
                </div>
              </div>
              <div class="exam-detail display_flex align-items_center">
                <span class="exam-grade">年级：{{ item.gradeName }}</span>
                <span class="exam-grade">学科：{{ item.subjectName }}</span>
                <span class="exam-time">考试时间：{{ item.examDateTime.substring(0, 16) }}</span>
              </div>
            </div>
            <div style="display: flex" class="exam-right">
              <el-button type="primary" class="report-btn light-btn" @click="lookReport(item)" plain
                >查看报告
              </el-button>
            </div>
          </div>
        </div>
      </li>
    </ul>
    <!--分页器-->
    <el-pagination
      background
      style="margin-bottom: 30px; margin-top: 10px"
      :hide-on-single-page="!examList.length"
      class="text-center"
      layout="total, prev, pager, next"
      @current-change="handleCurrentChange"
      :current-page.sync="pagination.page"
      :page-size="pagination.limit"
      :total="pagination.total_rows"
    >
    </el-pagination>
  </div>
</template>

<script>
export default {
  props: {
    examList: {
      type: Array,
      default: () => [],
    },
    pagination: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    getPublishExamList() {
      this.$emit('getPublishExamList');
    },
    handleCurrentChange(val) {
      this.$emit('handleCurrentChange', val);
    },
    /**
     * @name:查看报告
     */
    lookReport(item) {
      this.$emit('look-report', item);
    },
    /**
     * @name:根据来源类别显示来源名称
     */
    getSource(source) {
      let sourceName = '';
      switch (source) {
        case 3:
          sourceName = '手阅';
          break;
        case 4:
          sourceName = '网阅';
          break;
        default:
          sourceName = '补录';
          break;
      }
      return sourceName;
    },
  },
};
</script>

<style lang="scss" scoped>
.handled-info-list {
  margin-top: 5px;
}
.exam-name-area {
  display: flex;
}
.examReport__list {
  overflow-y: auto;
  height: 500px;
}
.exam-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #000;
  p {
    margin: 10px;
  }
  span {
    margin: 0px 5px;
    font-size: 16px;
  }
  .exam-name {
    font-size: 18px;
    font-weight: bold;
    color: #161e26;
  }
  .exam-change {
    position: absolute;
    bottom: 100px;
  }
  .info-row {
    display: flex;
    align-items: center;
  }
  .info-item {
    display: flex;
    align-items: center;
    height: 24px;
    background: #f0f2f5;
    border-radius: 12px;
    margin-right: 8px;
    padding: 6px;
    .info-subject {
      font-weight: 400;
      color: #606266;
      line-height: 24px;
      padding: 10px;
    }
  }
  .info-nums {
    color: #303233;
    font-weight: bold;
  }
}
</style>
<style lang="scss" scoped>
.examReport__item {
  width: 100%;
  min-height: 80px;
  box-sizing: border-box;
  background: #fff;
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 14px;
  color: #222;
  font-size: 14px;
  position: relative;
  .exam-name-area {
    display: flex;
    flex-direction: row;
    align-items: center;
    .exam-name {
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
      color: #161e26;
      line-height: 21px;
      margin-right: 100px;
      width: 90%;
      justify-content: flex-start;
      .exam-title {
        margin-right: 15px;
      }
      .exam-tag {
        display: flex;
        .tag-item {
          margin-right: 10px;
        }
        .el-tag {
          background-color: #fff;
        }
      }
    }
    .exam-detail {
      margin-top: 15px;
      .line-gap {
        width: 32px;
        position: relative;
        &:before {
          content: '';
          position: absolute;
          left: 50%;
          margin-left: -1px;
          top: 50%;
          margin-top: -7px;
          display: inline-block;
          width: 2px;
          height: 14px;
          background-color: #97a0a8;
        }
      }
      .exam-grade {
        margin-right: 20px;
      }
      > span {
        display: inline-block;
        color: #3f4a54;
      }
    }
  }
  .report-btn,
  .download-btn {
    width: 80px;
    height: 32px;
    border-radius: 3px;
    color: #ffffff;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    &.is-disabled {
      background: rgba(190, 198, 209, 0.7);
      pointer-events: none;
    }
  }
  .light-btn {
    background: #409eff;
  }
}
</style>
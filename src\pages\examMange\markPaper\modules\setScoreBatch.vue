<template>
  <el-dialog custom-class="seled-score-container" :visible="modalVisible" width="80%" :before-close="handleClose"
    :modal-append-to-body="false" :close-on-click-modal="false">
    <div slot="title" class="dialog-title">
      <span style="margin-right: 10px">{{ titleName }}</span>
    </div>
    <el-radio-group v-model="activeTab">
      <el-radio label="题号">按题设置</el-radio>
      <el-radio label="序号">按序号设置</el-radio>
    </el-radio-group>
    <div v-if="activeTab == '序号'" class="warn-tip">按序号批量设置不支持多选题</div>
    <el-table v-if="activeTab == '题号'" :data="tableData" class="exam-ques-table">
      <el-table-column prop="title" label="题目名称" width="250" show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="每题分数" width="200">
        <template slot-scope="scope">
          <el-input v-model="scope.row.quesScore" style="width: 86px; height: 32px"
            @input="checkScore(scope.$index, 'quesScore')"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="判分规则">
        <template slot-scope="scope">
          <template v-if="scope.row.isMultiple">
            <el-radio-group v-model="scope.row.ruleType">
              <el-radio :label="IRULERTYPE.STANDAD">标准</el-radio>
              <el-radio :label="IRULERTYPE.EXAM">新高考</el-radio>
            </el-radio-group>
            <template v-if="scope.row.ruleType == IRULERTYPE.STANDAD">
              <div>
                全对得满分，错选的不得分，少选得
                <el-input v-model="scope.row.halfScore" style="width: 76px; height: 32px"
                  @input="checkScore(scope.$index, 'halfScore')"></el-input>
                分
              </div>
            </template>
            <template v-else-if="scope.row.ruleType == IRULERTYPE.EXAM">
              <div>
                <span
                  style="white-space: normal; color: darkgrey">若满分6分，全部选对得6分，有选错的得0分。若只有两个正确选项，每选对一个得3分；若共有3个正确选项，每选对一个得2分。</span>
              </div>
            </template>
          </template>
          <template v-else>
            <span>----</span>
          </template>
        </template>
      </el-table-column>
      <!-- <el-table-column label="漏选分数" v-if="pageType == 'objectSetting'" width="100">
        <template slot-scope="scope">
          <template v-if="scope.row.isMultiple">
            <el-input
              v-model="scope.row.halfScore"
              style="width: 76px; height: 32px"
              @input="checkScore(scope.$index, 'halfScore')"
            ></el-input>
          </template>
          <template v-else>
            <span>----</span>
          </template>
        </template>
      </el-table-column> -->
      <el-table-column label="答案（非必填）" v-if="pageType == 'objectSetting'">
        <template slot-scope="scope">
          <el-input v-model="scope.row.answers" @input="inputRule(scope.row)" style="height: 32px" :placeholder="scope.row.typeId == IQUES_TYPE.singleChoice
    ? singleChoiceText
    : scope.row.typeId == IQUES_TYPE.choice
      ? choiceText
      : judgeText
    "></el-input>
        </template>
      </el-table-column>
    </el-table>
    <el-table v-if="activeTab == '序号'" :data="choiceData" class="exam-ques-table">
      <el-table-column prop="start" label="起止序号" width="250">
        <template slot-scope="scope">
          <el-input v-model="scope.row.start" style="width: 60px; height: 32px"></el-input>
          -
          <el-input v-model="scope.row.end" style="width: 60px; height: 32px"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="每题分数" width="200">
        <template slot="header" slot-scope="scope">
          <el-button type="text" @click="addChoiceData">添加</el-button>
        </template>
        <template slot-scope="scope">
          <el-button type="text" @click="deleteChoiceData(scope.$index, scope.row)">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column label="每题分数" width="200">
        <template slot-scope="scope">
          <el-input v-model="scope.row.score" style="width: 86px; height: 32px"
            @input="checkScore(scope.$index, 'quesScore')"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="答案（非必填）" v-if="pageType == 'objectSetting'">
        <template slot-scope="scope">
          <el-input v-model="scope.row.answers" @input="inputRule(scope.row)" style="height: 32px" placeholder="请按题目顺序填写答案，如ABCD/TF"></el-input>
        </template>
      </el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeModal">取 消</el-button>
      <el-button type="primary" :loading="isComfirming" @click="sureClick">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { checkFloat } from '@/utils/number.js';
import { IQUES_TYPE, IRULERTYPE } from '@/typings/card.js';
export default {
  props: {
    modalVisible: {
      type: Boolean,
      required: true,
    },
    titleName: {
      type: String,
      default: '',
    },
    //页面类型
    pageType: {
      type: String,
      default: '',
    },
    //题目数据
    quesInfo: {
      type: Array,
      default: [],
    },
    // 客观题数据
    objectQuesData: {
      type: Array,
      default: [],
    }
  },
  data() {
    return {
      IQUES_TYPE,
      // 提交按钮是否在加载中
      isComfirming: false,
      activeTab: '题号',
      tableData: [],
      choiceData: [],
      singleChoiceText: '请按题目顺序填写答案，如ABCD',
      choiceText: '每题答案中间用,间隔，如AB,BCD',
      judgeText: '请按题目顺序填写答案，如TFFT',
      IRULERTYPE: IRULERTYPE,
      ruleType: '',
    };
  },
  watch: {
    pageType() { },
    modalVisible(newVal, oldVal) {
      this.choiceData = [{ start: "1", end: "", score: "", answers: "" }];
      this.handleByQuesType();
    },
  },
  mounted() {},
  methods: {
    addChoiceData() {
      let lastObj = this.choiceData[this.choiceData.length - 1];
      if (lastObj.end != '') {
        this.choiceData.push({ start: Number(lastObj.end) + 1, end: "", score: "", answers: "" });
      } else {
        this.$message({
          message: '请先输入上一题的结束序号',
          type: 'warning',
          duration: 1000,
        });
      }
    },
    deleteChoiceData(index, row) {
      if (this.choiceData.length == 1) {
        this.$message({
          message: '至少保留一条数据',
          type: 'warning',
          duration: 1000,
        });
        return;
      }
      this.choiceData.splice(index, 1);
    },
    inputRule(obj) {
      //增加答案输入校验
      obj.answers = obj.answers.replace(/[^A-Za-z,]/g, '').toUpperCase();
    },
    handleByQuesType() {
      this.objectQuesData = JSON.parse(JSON.stringify(this.objectQuesData))
      this.tableData = JSON.parse(JSON.stringify(this.quesInfo)).filter(item => {
        if (this.pageType == 'objectSetting') {
          this.$set(item, 'answers', '');
          let isObjective = item.data.filter(ite => {
            return this.$isObjective(ite.typeId);
          });
          let isMultiple = item.data.filter(ite => {
            return ite.typeId == IQUES_TYPE.choice;
          });
          this.$set(item, 'isMultiple', isMultiple.length > 0 ? true : false);
          return isObjective.length > 0;
          return this.$isObjective(item.typeId);
        } else if (this.pageType == 'aiSetting') {
          return item.typeId == IQUES_TYPE.fillEva;
        } else {
          return !this.$isObjective(item.typeId);
        }
      });
      this.tableData.forEach(item => {
        let nos = item.data
          .flatMap(ite => (ite.data ? ite.data.map(it => it.quesNos) : ite.quesNos))
          .join(',');
        this.$set(item, 'title', item.name + '(' + nos + ')');
      });
      console.log('批量设置', this.tableData);
    },
    /**
     * @name: 分值检测
     */
    checkScore(index, key) {
      let sourceData = this.tableData;
      let res = checkFloat(sourceData[index][key]);
      if (res > 100) {
        this.$message({
          message: '分值最大不超过100分!',
          type: 'error',
          duration: 1000,
        });
        res = 100;
      }
      sourceData[index][key] = res;
    },
    // 关闭弹窗
    handleClose(done) {
      this.$emit('close-set-score-modal');
      done();
    },
    /**
     * 取消选择的教师
     */
    closeModal() {
      this.$emit('close-set-score-modal');
    },
    sureChoiceClick() {
      // 验证输入数据的有效性
      for (const item of this.choiceData) {
        const start = Number(item.start);
        const end = Number(item.end);
        if (isNaN(start) || isNaN(end) || start > end || start <= 0 || end > this.objectQuesData.length) {
          throw new Error('序号范围无效，请检查起止序号');
        }
        // if (!item.score) {
        //   throw new Error('请设置分数');
        // }
      }

      try {
        this.choiceData.forEach(item => {
          const start = Number(item.start);
          const end = Number(item.end);
          const answers = item.answers ? item.answers.toUpperCase().split('') : [];
          let ansIndex = 0;

          this.objectQuesData.forEach((ques, index) => {
            const quesNum = index + 1;
            if (quesNum >= start && quesNum <= end) {
              // 设置分数
              ques.score = item.score;

              // 设置答案
              if (answers.length > 0) {
                if (ques.typeId == IQUES_TYPE.singleChoice) { // 单选题
                  ques.answer = answers[ansIndex++] || ques.answer;
                } else if(ques.typeId == IQUES_TYPE.judge){
                  if (answers[ansIndex] == 'T') ques.answer = 'A';
                  if (answers[ansIndex] == 'F') ques.answer = 'B';
                  ansIndex++;
                } else if (ques.typeId == IQUES_TYPE.choice) { // 多选题
                  throw new Error('多选题不支持批量设置');
                }
              }
            }
          });
        });
      } catch (error) {
        this.$message({
          message: error.message || '设置失败，请检查输入数据',
          type: 'error',
          duration: 2000
        });
        return;
      }
    },
    /**
     * @name: 确认修改
     */
    sureClick() {
      if (this.activeTab == '序号') {
        try {
          this.sureChoiceClick();
          this.$emit('confirm-set-object-score', this.objectQuesData);
        } catch (e) {
          this.$message({
            message: e.message,
            type: 'error',
            duration: 2000,
          });
        }
        return;
      }
      let errorNum = 0;
      this.tableData.forEach(item => {
        if (item.answers != '') {
          let answerList = [];
          if (item.typeId == IQUES_TYPE.choice || item.typeId == IQUES_TYPE.singleChoice) {
            let ans = item.answers.toUpperCase().trimEnd(/[,，.。]/);
            item.answers = item.answers.toUpperCase();
            if (item.typeId == IQUES_TYPE.choice) {
              answerList = ans.split(/[,，.。]/);
            } else {
              //判断是否符号分割
              if (/[,，.。]/.test(ans)) {
                answerList = ans.split(/[,，.。 ]/);
              } else {
                answerList = ans.split('');
              }
            }
            if (answerList.length > 0 && answerList.length != item.data.length) {
              // errorNum += 1;
              this.$message({
                message: '答案与题目数量不匹配，请查验后重试',
                type: 'warning',
                duration: 3000,
              });
            }
          }
        }
      });
      const unsetScore = this.tableData.some(ite => ite.quesScore == '');
      // if (unsetScore) {
      //   this.$message({
      //     message: "每题分数不能为空，请查验后重试",
      //     type: "warning",
      //     duration: 1000,
      //   });
      // }
      // && !unsetScore
      if (errorNum == 0) this.$emit('confirm-set-score', this.tableData);
    },
  },
};
</script>

<style lang="scss" scoped>
.seled-score-container {
  .warn-tip {
                display: block;
                color: #f56c6c;
                font-size: 13px;
                margin-bottom: 16px;
                padding: 8px 16px;
                background-color: #fef0f0;
                border-radius: 4px;
                border: 1px solid #fde2e2;

                &::before {
                    content: "⚠";
                    margin-right: 8px;
                }
            }
  .el-dialog__header {
    height: 45px;

    .dialog-title {
      line-height: 45px;
    }
  }
}
</style>
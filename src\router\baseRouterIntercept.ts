import { getToken, setToken } from '@/service/auth';
import { getVipInfo } from '@/service/pexam';
import store from '@/store/index';
import { clearHttpRequestingList, sessionSave } from '@/utils';
import NProgress from 'nprogress';
import { cefMsg } from '../callCplus/index';
import { isMobileMode } from '../utils';
import { router } from './baseRouter';

import { loginByUserId } from '@/utils/login';
import UserRole from '@/utils/UserRole';

// 异常处理对应手机地址列表
const exceptionMobileMap = {
  '/scan_task': '/scan_task',
  '/scan_batch': '/scan_batch',
  '/scan/errornew': '/scan/errornew',
};

const commentList = [
  '/comment',
  '/commentDetail',
  '/home/<USER>/paperCommentDetail',
  '/paperCommentDetail',
];

//白名单
const whiteList = [
  // "/comment",
  // "/commentDetail",
  '/login',
  '/quesManage',
  '/setTarget',
  '/newpapercomment',
  '/mobilecomment',
  '/mobilplayer',
  '/scan_task',
  '/scan/errornew',
  '/scan/images',
  '/scan_errornew',
  '/scan_not',
  '/scan/insider',
  '/scan_batch',
  '/home/<USER>',
  '/home/<USER>',
  ...commentList,
];

// 路由拦截
export const routerIntercept = () => {
  router.beforeEach(async (to, from, next) => {
    // 设置标题
    setMetaTitle(to.meta.title);
    // 检查是否处理异常手机端，如果是则直接跳转
    if (checkMobile(to, from, next)) {
      NProgress.done();
      return;
    }
    // 检查是否白名单，如果是则直接跳转
    if (checkWhiteList(to, from, next)) {
      next();
      NProgress.done();
      return;
    }

    clearHttpRequestingList();
    NProgress.start();
    const hasToken = getToken() || to.query.token;
    let teachParams = sessionSave.get('teachParams');
    if (teachParams) {
      store.commit('saveTeachParams', teachParams);
    }
    if (hasToken) {
      setToken(hasToken);
    }

    // ------------讲评环境-----------------
    if (commentList.indexOf(to.path) >= 0) {
      try {
        cefMsg(
          'mirco.call_cplus',
          'getConfigInfo',
          JSON.stringify(['schoolid', 'subjectid', 'schoolname', 'subjectname', 'phase']),
          'configInfo'
        ).then(data => {
          sessionSave.set('configInfo', data.data);
        });
        /*
          //测试环境：
          //http://localhost:1003/bigdata/comment?type=2&schoolId=445df23f21754abf89066b7d1245cde7&userId=84ae0c7deb4d415f844be0abb8f8745e&token=326d0925ac46bb4f9b354fdd8eb937673ce00f989f1a36beacdff9441ee1f7ff15f5385dd5eb4bc2b8d082c33b6d9b50540f5bfeef10db2f&times=cf271034ce4c47dbb2a160f63192ea90
          sessionSave.set('configInfo', { schoolid: "445df23f21754abf89066b7d1245cde7", subjectid: "11" });
          */
        next();
      } catch (err) {
        if (to.path === '/home/<USER>/paperCommentDetail') {
          next();
        } else {
          next(`/login`);
        }
      }
      NProgress.done();
      return;
    }
    // --------------------------------

    // 检查是否去往登录页面，如果是则直接跳转到登录
    if (await checkToLogin(to, from, next)) {
      next(`/login`);
      NProgress.done();
      return;
    }

    let loginInfo: any = sessionSave.get('loginInfo');
    let schoolInfo: any = sessionSave.get('schoolInfo');

    if (to.query.schoolId && to.query.schoolName) {
      schoolInfo = {
        id: to.query.schoolId,
        schoolId: to.query.schoolId,
        school_name: to.query.schoolName,
        schoolName: to.query.schoolName,
      };
    } else if (!schoolInfo) {
      schoolInfo = {
        id: loginInfo.schoolid,
        schoolId: loginInfo.schoolid,
        school_name: loginInfo.school_name,
        schoolName: loginInfo.school_name,
      };
    }

    if (schoolInfo) {
      sessionSave.set('schoolInfo', schoolInfo);
      getVipInfo({ schoolId: schoolInfo.id }).then(res => {
        const vipInfo = res.data;
        vipInfo.isVip = false;
        if (JSON.stringify(vipInfo) != '{}') {
          vipInfo.dateExpired = vipInfo.dateExpired.substring(0, 10);
          vipInfo.dateStart = vipInfo.dateStart.substring(0, 10);
          //当前时间
          let curTime = res.responsetime;
          //服务到期时间
          let dateExpired = new Date(vipInfo.dateExpired);
          if (curTime < dateExpired.getTime()) {
            vipInfo.isVip = true;
            vipInfo.showTip = (dateExpired.getTime() - curTime) / (1000 * 60 * 60 * 24) <= 15;
          }
        } else {
          vipInfo.dateExpired = '1999-01-01';
          //未设置过期时间
          vipInfo.isNotUsed = true;
        }
        store.commit('savevipInfo', vipInfo);
      });
      if (!UserRole.isInit && schoolInfo.id) {
        await UserRole.setSchoolInfo(schoolInfo);
      }
      store.commit('saveSchoolInfo', schoolInfo);
    }
    next();
    NProgress.done();
  });
};

/**
 * 判断当前页面是否需要跳转到移动端页面
 *
 * @param to 目标路由对象
 * @param from 源路由对象
 * @param next 路由守卫的下一个函数
 * @returns 如果需要跳转到移动端页面，则返回true，否则返回false
 */
function checkMobile(to, from, next) {
  if (process.env.VUE_APP_SCAN_MOBILE && isMobileMode()) {
    for (const key in exceptionMobileMap) {
      if (!to.path.includes(key)) continue;
      const searchParams = new URLSearchParams(to.query);
      const url = new URL(
        `${process.env.VUE_APP_SCAN_MOBILE}${exceptionMobileMap[key]}?${searchParams.toString()}`
      );
      window.location.replace(url.href);
      return true;
    }
  }
  return false;
}

/**
 * 检查白名单
 *
 * @param to 目标路由对象
 * @param from 起始路由对象
 * @param next 路由守卫的下一个函数
 * @returns 返回布尔值，表示是否通过白名单检查
 */
function checkWhiteList(to, from, next) {
  if (whiteList.indexOf(to.path) !== -1 || whiteList.indexOf(to.name) !== -1) {
    const hasToken = to.query.token || getToken();
    if (hasToken) {
      setToken(hasToken);
    }
    return true;
  }
  return false;
}

/**
 * 检查用户可以登录
 *
 * @param to 目标路由对象
 * @param from 当前路由对象
 * @param next 路由守卫的下一个函数
 * @returns 如果需要跳转到登录页，则返回true，否则返回false
 */
async function checkToLogin(to, from, next) {
  const loginInfo: any = sessionSave.get('loginInfo');
  const hasLoginInfo = loginInfo && String(loginInfo.account_type) !== 'undefined';
  const userId = to.query.userId || to.query.userid;
  const token = getToken() || to.query.token;
  let toLogin = true;
  let isSameUser = userId && hasLoginInfo ? userId == loginInfo.id : true;

  if (hasLoginInfo && isSameUser) {
    store.commit('saveLoginInfo', loginInfo);
    let nameCacheKey = "showAnonymousName_" + loginInfo.id;
    store.commit('setStudentAnonymous', localStorage.getItem(nameCacheKey) === "1");
    let scoreCacheKey = "showStudentScore_" + loginInfo.id;
    store.commit('setStudentShowScore', !localStorage.getItem(scoreCacheKey) || localStorage.getItem(scoreCacheKey) === "1");

    toLogin = false;
  } else if (userId) {
    toLogin = !(await loginByUserId(userId));
  }
  return toLogin;
}

/**
 * 设置网页标题
 *
 * @param title 新的网页标题
 */
function setMetaTitle(title) {
  if (title) {
    document.title = title;
  } else {
    document.title = '大数据精准教学系统';
  }
}

// 查找路径是否在路由中
function findPathInRouter(path, router) {
  function checkInRoutes(base, path, router) {
    for (const route of router) {
      if (path == base + route.path) {
        return true;
      }
      if (route.children && route.children.length) {
        let inChildren = checkInRoutes(base + route.path + '/', path, route.children);
        if (inChildren) return true;
      }
    }
    return false;
  }

  if (Array.isArray(router)) {
    return checkInRoutes('', path, router);
  } else {
    return checkInRoutes('', path, [router]);
  }
}
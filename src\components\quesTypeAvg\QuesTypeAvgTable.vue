<template>
  <div class="ques-type-box">
    <el-table
      v-if="!hideTable"
      class="ques-type-table"
      stripe
      :data="totalData"
      :header-cell-style="{
        fontSize: '14px',
        color: '#3F4A54',
        backgroundColor: '#f5f7fa',
      }"
      style="width: 100%"
      v-sticky-table="0"
    >
      <el-table-column align="center" prop="classId" label="班级">
        <template slot-scope="scope">
          <span>{{ scope.row.classId == undefined ? '年级' : scope.row.classId }}</span>
        </template>
      </el-table-column>
      <template v-for="(typeItem, index) in totalData[0].questionTypes">
        <el-table-column
          :label="`${typeItem.typeName}(${typeItem.fullScore})`"
          :key="typeItem.quesType"
          align="center"
        >
          <el-table-column align="center" label="均分">
            <template slot-scope="scope">
              <span
                :style="{
                  color:
                    quesGrdAvgScoreMap[typeItem.typeName].avgScore >
                    (scope.row.questionTypes[index]?.average === undefined
                      ? scope.row.questionTypes[index].avgScore
                      : scope.row.questionTypes[index].average)
                      ? 'red'
                      : 'inherit',
                }"
              >
                {{
                  scope.row.questionTypes[index]?.average === undefined
                    ? scope.row.questionTypes[index].avgScore
                    : scope.row.questionTypes[index].average
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="得分率">
            <template slot-scope="scope">
              <span
                v-if="scope.row.questionTypes[index].scoreRate"
                :style="{
                  color:
                    quesGrdAvgScoreMap[typeItem.typeName].scoreRate >
                    scope.row.questionTypes[index].scoreRate
                      ? 'red'
                      : 'inherit',
                }"
              >
                {{
                  scope.row.questionTypes[index].scoreRate === ''
                    ? '-'
                    : (scope.row.questionTypes[index].scoreRate * 1).toFixed(2) + '%'
                }}
              </span>
              <span
                v-else
                :style="{
                  color:
                    quesGrdAvgScoreMap[typeItem.typeName].scoreRate >
                    scope.row.questionTypes[index].scoreRate
                      ? 'red'
                      : 'inherit',
                }"
              >
                {{
                  scope.row.questionTypes[index].scoreRate === ''
                    ? '-'
                    : (scope.row.questionTypes[index].scoreRate * 1).toFixed(2) + '%'
                }}
              </span>
            </template>
          </el-table-column>
        </el-table-column>
      </template>
      <el-table-column :label="`总分(${totalData[0].fullScore})`" align="center">
        <el-table-column align="center" label="均分">
          <template slot-scope="scope">
            <span>
              {{ scope.row.totalAverageScore }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="得分率">
          <template slot-scope="scope">
            <span>
              {{
                scope.row.totalScoreRate === ''
                  ? '-'
                  : (scope.row.totalScoreRate * 1).toFixed(2) + '%'
              }}
            </span>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
    <div class="default" v-else>
      <p style="text-align: center; font-size: 16px; margin: 80px 0">暂无数据!</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'QuesTypeAvgTable',
  props: ['tableData', 'quesGrdAvgScoreMap'],
  data() {
    return {
      totalData: [
        {
          questionTypes: [],
        },
      ],
      hideTable: false,
    };
  },
  computed: {},
  watch: {
    tableData(newVal) {
      if (!newVal.length) {
        this.hideTable = true;
      } else if (!newVal[0].questionTypes) {
        this.hideTable = true;
      } else {
        this.hideTable = false;
        this.totalData = newVal;
      }
    },
  },
  methods: {},
};
</script>

<style lang="scss"></style>

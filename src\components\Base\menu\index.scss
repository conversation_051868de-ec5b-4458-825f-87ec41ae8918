.base-menu {
  // 手风琴样式
  &--accordion {
    display: flex;
    flex-direction: column;

    ::v-deep {
      & > .el-submenu {
        overflow: hidden !important;
        min-height: 56px;
      }
      & > .el-submenu.is-opened {
        flex: 1;
        min-height: 56px;

        display: flex;
        flex-direction: column;
        overflow: hidden;

        & > .el-menu {
          flex: 1;
          overflow: hidden;
          overflow-y: auto !important;
        }
      }
    }
  }
}

.base-menu-submenu {
  ::v-deep {
    .el-submenu__title {
      padding-left: 0 !important;
    }
  }
}

.base-menu-item {
  padding-left: 0 !important;
  padding-right: 20px !important;
  text-overflow: ellipsis;
  overflow: hidden;

  &.is-active {
    background-color: #e6f4fd;
    color: #409eff !important;
    position: relative;
    &:before {
      content: '';
      position: absolute;
      width: 4px;
      height: 50px;
      background: #409eff;
      border-radius: 2px;
      left: 0;
      top: 0;
    }
  }
}

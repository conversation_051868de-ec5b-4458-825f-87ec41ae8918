<template>
  <div style="height: 420px">
    <div v-show="!showDefault" id="avgChart" style="width: 100%; height: 100%"></div>
    <div v-show="showDefault">
      <div style="text-align: center"><img style="width: 350px" :src="noResImg" alt="" /></div>
      <p style="text-align: center; font-size: 16px; margin-top: 10px">暂无数据!</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AvgChart',
  props: ['type', 'tableData', 'filterData', 'queryType', 'isShowFiveAll'],
  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      showDefault: false,
      avgChart: null,
      teaNames: [],
      classNames: [],
      classAvgList: [],
      upperAvgList: [],
      lowerAvgList: [],
      gradeAvgList: [],
      data: [],
    };
  },
  mounted() {
    if (this.tableData.length) {
      this.showDefault = false;
    }

    this.$nextTick(() => {
      this.drawImg();
    });
  },
  beforeDestroy() {
    if (this.avgChart != null && this.avgChart != '' && this.avgChart != undefined) {
      this.avgChart.dispose();
      this.avgChart = null;
    }
  },
  watch: {
    tableData: {
      handler(val) {
        if (val.length) {
          this.showDefault = false;
        }
        this.$nextTick(() => {
          this.drawImg();
        });
      },
      deep: true,
    },
  },
  methods: {
    handleChartData() {
      let data = this.tableData && this.tableData.length ? this.tableData : [];
      if (data.length) {
        data = this.tableData.filter(item => item.clsName !== '年级' && item.clsName !== '全校');
      } else {
        this.showDefault = true;
      }
      this.data = data;

      this.teaNames = data.map(item => {
        return item.teaName;
      });
      this.classNames = data.map(item => {
        return item.clsName;
      });
      this.classAvgList = data.map(item => {
        let averageSubtraction = item.avgScore - item.gradeAvg;
        if (averageSubtraction <= 0) {
          return item.avgScore;
        } else {
          return (item.avgScore - Math.abs(averageSubtraction)).toFixed(2);
        }
      });
      this.upperAvgList = data.map(item => {
        let averageSubtraction = item.avgScore - item.gradeAvg;
        if (averageSubtraction <= 0) {
          return 0;
        }
        return averageSubtraction.toFixed(2);
      });
      this.lowerAvgList = data.map(item => {
        let averageSubtraction = item.avgScore - item.gradeAvg;
        if (averageSubtraction >= 0) {
          return 0;
        }
        return Math.abs(averageSubtraction).toFixed(2);
      });
      this.gradeAvgList = data.map(item => {
        return item.gradeAvg;
      });
    },
    drawImg() {
      if (this.avgChart != null && this.avgChart != '' && this.avgChart != undefined) {
        this.avgChart.dispose();
        this.avgChart = null;
      }
      this.handleChartData();
      let _this = this;
      // 基于准备好的dom，初始化echarts实例
      this.avgChart = this.$echarts.init(document.getElementById('avgChart'));

      let series = [];
      let tag = this.type == 'campus' ? '校区' : this.queryType == 1 ? '班级' : '教师';
      let xAxisData = this.queryType == 1 ? this.classNames : this.teaNames;
      const legendTag = this.type == 'campus' ? '全校' : '年级';

      series = [
        {
          name: tag,
          type: 'bar',
          stack: '总量',
          color: '#b3dbfa',
          label: {
            show: true,
            position: 'top',
            textStyle: {
              color: '#757C8C',
              fontSize: 14,
            },
          },
          z: 2,
          data: this.data.map(item => item.avgScore),
        },
        {
          name: '超均率',
          type: 'bar',
          color: '#fba110',
          data: this.data.map(item => item.passAvgRate),
        },
      ];

      if (this.isShowFiveAll && this.queryType == 1) {
        series = [
          ...series,
          {
            name: `${legendTag}均分`,
            type: 'line',
            data: this.gradeAvgList,
            color: 'red',
            symbol: 'none',
            lineStyle: {
              opacity: 0,
            },
            markLine: {
              symbol: 'none',
              data: [
                {
                  type: 'average',
                  name: '平均值',
                },
              ],
              label: {
                formatter: `${legendTag}均分:\n{c}`,
                color: 'inherit',
              },
              itemStyle: {
                opacity: 0,
              },
              lineStyle: {
                width: 2,
                type: 'solid',
              },
            },
          },
        ];
      }

      // 绘制图表
      this.avgChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: params => {
            let prop = this.queryType == 1 ? 'clsName' : 'teaName';
            let obj = this.tableData.find(item => {
              return params[0].name == item[prop];
            });
            if (!obj) {
              return '';
            }

            let tooltipContent = [
              `<div>${obj.clsName}</div>`,
              `<div>${this.type == 'campus' ? '年级主任' : this.filterData?.subjectId == '' ? '班主任' : '任课老师'}：${
                obj.teaName || '--'
              }</div>`,
            ];

            // 添加基础的参数信息
            params.forEach(item => {
              if (item.seriesName == '班级') {
                tooltipContent.push(
                  `<div>${item.marker}  ${item.seriesName}: <b style="float: right; margin-left: 20px;">${obj.avgScore}</b> </div>`
                );
              } else {
                tooltipContent.push(
                  `<div>${item.marker}  ${item.seriesName}: <b style="float: right; margin-left: 20px;">${item.value}</b> </div>`
                );
              }
            });

            if (this.queryType == 1 && this.isShowFiveAll) {
              let averageSubtraction = obj.avgScore - obj.gradeAvg;
              if (averageSubtraction > 0) {
                tooltipContent.push(
                  `<div><span style="display: inline-block; width: 10px; height: 10px; background-color: #409eff; border-radius: 50%; margin-right: 5px;"></span> 高于${
                    this.type == 'campus' ? '全校' : '年级'
                  }均分: <b style="float: right; margin-left: 20px;">${averageSubtraction.toFixed(2)}</b> </div>`
                );
              } else if (averageSubtraction < 0) {
                tooltipContent.push(
                  `<div><span style="display: inline-block; width: 10px; height: 10px; background-color: #83aeda; border-radius: 50%; margin-right: 5px;"></span> 低于${
                    this.type == 'campus' ? '全校' : '年级'
                  }均分: <b style="float: right; margin-left: 20px;">${Math.abs(averageSubtraction).toFixed(
                    2
                  )}</b> </div>`
                );
              }
            }
            return tooltipContent.join('');
          },
        },
        legend: {
          icon: 'circle',
          top: 10,
          right: 70,
          textStyle: {
            color: '#757C8C',
            fontSize: '14',
          },
          data: [tag, '超均率', `${legendTag}均分`],
        },
        grid: {
          left: '3%',
          right: '7%',
          bottom: '11%',
          containLabel: true,
        },
        yAxis: {
          type: 'value',
          name: '均分',
        },
        xAxis: {
          type: 'category',
          name: tag,
          axisLine: {
            lineStyle: {
              color: '#757C8C',
            },
          },
          data: xAxisData,
          axisLabel: {
            interval: 0,
            formatter: function (value) {
              if (value.length > 15) {
                value = value.substring(0, 15) + '..';
              }
              return value;
            },
          },
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100,
          },
        ],
        series: series,
      });
    },
  },
};
</script>

<style scoped></style>

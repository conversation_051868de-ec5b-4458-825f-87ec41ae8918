<template>
  <div>
    <div class="setting-header">
      <div class="title">权限分配</div>
      <div class="header-right">
        <el-button class="save-btn" type="primary" @click="save" size="small" :loading="saveLoading">保存</el-button>
      </div>
    </div>
    <el-table :data="roleList" style="width: 100%" border :header-cell-class-name="'table-header-style'">
      <el-table-column prop="roleName" label="角色" align="center"> </el-table-column>
      <el-table-column label="操作" width="300" align="center">
        <template #default="scope">
          <el-button type="primary" size="small" round @click="handleMenuPermissionShow(scope.row)">权限分配</el-button>
          <template v-if="scope.row.roleId == 5 || scope.row.roleId == 6">
            <el-divider direction="vertical" style="height: 2em"></el-divider>
            <el-button type="primary" size="small" round @click="handleFunctionPermissionShow(scope.row)"
              >功能分配</el-button
            >
          </template>
        </template>
      </el-table-column>
    </el-table>

    <!-- 报告设置 -->
    <ReportSetting style="margin-top: 10px" ref="reportSetting"></ReportSetting>

    <div class="tip">
      <p class="title">*注</p>
      <p>校领导：包括学校管理员、校长、副校长和教导主任角色；默认可查看本校所有分析数据；</p>
      <p>年级主任：默认可查看本年级所有学科的分析数据；</p>
      <p>学科主任：默认可查看本校所管理学科的所有分析数据；</p>
      <p>备课组长：默认可查看所管理年级、学科的分析数据；</p>
      <p>班主任：默认可查看所管理班级所有学科的分析数据；</p>
      <p>任课老师：默认可查看任教班级任教学科的分析数据。</p>
    </div>

    <!-- 权限分配对话框 -->
    <el-dialog :visible.sync="isMenuPermissionShow" title="权限分配" width="1000px">
      <div>
        <span>当前分配角色：</span>
        <span class="role-name">{{ currentRole.roleName }}</span>
      </div>
      <div>
        <p>权限设置操作说明：</p>
        <p>1、取消勾选代表禁用该权限</p>
        <p>2、当禁用权限时，禁用父级权限时，该父级权限下的所有子级权限将全部禁用；</p>
        <p>3、取消勾选的权限名称表示已禁用，禁用后，该角色下的用户则无法使用取消勾选的权限菜单，请慎重操作！</p>
      </div>
      <div class="disabled-count">*已禁用 {{ disabledMenuCount }} 项权限</div>
      <el-tree
        :key="currentRole.roleId"
        ref="tree"
        :data="getTree"
        :props="treeProps"
        show-checkbox
        default-expand-all
        node-key="id"
        @check-change="handleCheckChange"
      />

      <span slot="footer" class="dialog-footer">
        <el-button @click="isMenuPermissionShow = false">取 消</el-button>
        <el-button type="primary" @click="handleSetMenuRole">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 功能分配对话框 -->
    <el-dialog :visible.sync="isFunctionPermissionShow" title="功能分配" width="1000px">
      <div>
        <span>当前分配角色：</span>
        <span class="role-name">{{ currentRole.roleName }}</span>
      </div>
      <div>
        <p>功能分配操作说明：</p>
        <p>1、取消勾选代表禁用该权限</p>
        <p>2、取消勾选的功能名称表示已禁用，禁用后，该角色下的用户则无法使用取消勾选的功能，请慎重操作！</p>
      </div>

      <div :key="currentRole.roleId">
        <div v-for="item in functionList" :key="item.label" class="function-list-item">
          <p class="function-list-item-title">{{ item.label }}</p>
          <el-checkbox
            v-for="funcRole in item.list"
            :key="funcRole.id"
            :checked="tempFuncIds.includes(funcRole.id)"
            @change="handleFunctionChange(funcRole.id)"
            >{{ funcRole.label }}</el-checkbox
          >
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isFunctionPermissionShow = false">取 消</el-button>
        <el-button type="primary" @click="handleSetFunctionRole">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { listMenuAPI, saveRoleMenuAPI } from '@/service/pstat';
import { Tree } from '@iclass/element-ui';
import { Component, Mixins, Ref, Vue } from 'vue-property-decorator';
import SchoolSettingMixin from '../SchoolSetting.mixin';
import { FuncIdEnum, MenuIdEnum } from '@/utils/examReportUtils';
import ReportSetting from '../ReportSetting/index.vue';

export interface Role {
  roleId: string;
  roleName: string;
  menuIds: string[];
  funcIds: string[];
}

@Component({
  components: {
    ReportSetting,
  },
})
export default class PermissionManagement extends Mixins(SchoolSettingMixin) {
  public $refs!: {
    tree: Tree;
  };
  @Ref() reportSetting: ReportSetting;
  // 禁用菜单数量
  private disabledMenuCount = 0;
  // 权限分配对话框
  private isMenuPermissionShow: boolean = false;
  // 功能分配对话框
  private isFunctionPermissionShow: boolean = false;
  // 树形结构配置
  private treeProps = {
    children: 'children',
    label: 'label',
  };
  // 当前分配角色
  private currentRole: Role = {
    roleId: '5',
    roleName: '',
    menuIds: [],
    funcIds: [],
  };
  // 临时功能权限
  private tempFuncIds: string[] = [];
  // 菜单列表
  private roleList: Role[] = [];
  // 树数据
  private tree = [
    {
      id: 1,
      label: '常用报表',
      children: [
        {
          id: MenuIdEnum.cardHome,
          label: '成绩单',
        },
        {
          id: MenuIdEnum.fiveRate,
          label: '一分五率',
        },
        {
          id: MenuIdEnum.subjectComparison,
          label: '学科对比',
        },
        {
          id: MenuIdEnum.scoreSheet,
          label: '学生小分表',
        },
        {
          id: MenuIdEnum.questionAnalysis,
          label: '大小题分析',
        },
      ],
    },
    {
      id: 2,
      label: '考情分析',
      children: [
        {
          id: MenuIdEnum.overview,
          label: '学情概览',
        },
        {
          id: MenuIdEnum.gradeDistribute,
          label: '等级分布',
        },
        {
          id: MenuIdEnum.topAndDiff,
          label: '优困生分布',
        },
        {
          id: MenuIdEnum.online,
          label: '上线分析',
        },
        {
          id: MenuIdEnum.limitStu,
          label: '临界生分布',
        },
      ],
    },
    {
      id: 3,
      label: '成绩分析',
      children: [
        {
          id: MenuIdEnum.scoreSection,
          label: '成绩分段分析',
        },
        {
          id: MenuIdEnum.totalRank,
          label: '名次分析',
        },
        // {
        //   id: MenuIdEnum.rankSection,
        //   label: '各名次段分析',
        // },
        {
          id: MenuIdEnum.boxplot,
          label: '箱线图',
        },
      ],
    },
    {
      id: 4,
      label: '命题分析',
      children: [
        {
          id: MenuIdEnum.qualityReport,
          label: '命题质量',
        },
        {
          id: MenuIdEnum.bothWayReport,
          label: '双向细目表',
        },
        {
          id: MenuIdEnum.quesTypeAvg,
          label: '题型均分',
        },
        {
          id: MenuIdEnum.answerDetail,
          label: '作答详情',
        },
        {
          id: MenuIdEnum.knowledgeAnalyze,
          label: '知识点分析',
        },
      ],
    },
    {
      id: MenuIdEnum.paperComment,
      label: '试卷讲评',
      disabled: true,
    },
  ];
  get getTree() {
    if (this.currentRole.roleId == '3' || this.currentRole.roleId == '4' || this.currentRole.roleId == '6') {
      return this.removeTreeNode(this.tree, 'subjectComparison');
    }
    return this.tree;
  }
  // 功能列表
  private functionList = [
    {
      label: '成绩单',
      list: [
        {
          id: FuncIdEnum.GrdRankAscDesc,
          label: '校排&升降',
        },
        {
          id: FuncIdEnum.ClsRankAscDesc,
          label: '班排&升降',
        },
      ],
    },
    {
      label: '一分五率',
      list: [
        {
          id: FuncIdEnum.FiveAll,
          label: '全校数据',
        },
      ],
    },
    {
      label: '学生小分表',
      list: [
        {
          id: FuncIdEnum.QsClsRank,
          label: '班级排名',
        },
        {
          id: FuncIdEnum.QsGrdRank,
          label: '年级排名',
        },
      ],
    },
    {
      label: '作答详情',
      list: [
        {
          id: FuncIdEnum.AnswerAll,
          label: '全校数据',
        },
      ],
    },
  ];
  // 保存loading
  private saveLoading = false;

  created() {
    this.getRoleList();
  }

  // 获取菜单列表
  async getRoleList() {
    let res = await listMenuAPI({
      schoolId: this.$sessionSave.get('schoolInfo').id,
      schId: this.$sessionSave.get('schoolInfo').id,
    });
    this.roleList = res.data;
  }

  // 处理树勾选
  handleCheckChange(data, checked, indeterminate) {
    this.disabledMenuCount = this.getDisabledMenuCount();
  }

  // 显示权限分配对话框
  handleMenuPermissionShow(item: Role) {
    this.currentRole = item;
    this.isMenuPermissionShow = true;
    this.$nextTick(() => {
      this.$refs.tree.setCheckedKeys(item.menuIds, true);
      this.disabledMenuCount = this.getDisabledMenuCount();
    });
  }

  // 显示功能分配对话框
  handleFunctionPermissionShow(item: Role) {
    this.currentRole = item;
    this.tempFuncIds = JSON.parse(JSON.stringify(item.funcIds || []));
    this.isFunctionPermissionShow = true;
  }

  // 确定菜单权限
  handleSetMenuRole() {
    let keys = this.$refs.tree.getCheckedKeys(true);
    this.currentRole.menuIds = keys;
    this.isMenuPermissionShow = false;
    this.save();
  }

  // 处理功能权限勾选
  handleFunctionChange(id: string) {
    if (this.tempFuncIds.includes(id)) {
      this.tempFuncIds = this.tempFuncIds.filter(item => item !== id);
    } else {
      this.tempFuncIds.push(id);
    }
  }

  // 确定功能权限
  handleSetFunctionRole() {
    this.currentRole.funcIds = this.tempFuncIds;
    this.isFunctionPermissionShow = false;
    this.save();
  }

  // 保存
  async save() {
    this.saveLoading = true;
    try {
      const res = await saveRoleMenuAPI({
        schId: this.$sessionSave.get('schoolInfo').id,
        jMenu: this.roleList,
      });
      await this.reportSetting.saveConfig();
      this.$notify.success({
        title: '成功',
        message: '保存成功',
      });
    } catch (error) {
      console.error(error);
      this.$notify.error({
        title: '失败',
        message: '保存失败',
      });
    } finally {
      this.saveLoading = false;
    }
  }

  // 获取禁用菜单数量
  getDisabledMenuCount() {
    const totalNodes = this.getNodesCount(this.getTree);
    if (this.$refs.tree) {
      return totalNodes - this.$refs.tree.getCheckedKeys(true).length;
    }
    return 0;
  }

  // 获取所有节点数量
  private getNodesCount(nodes: any[]): number {
    let count = 0;
    nodes.forEach(node => {
      if (node.children && node.children.length) {
        count += this.getNodesCount(node.children);
      } else {
        count++;
      }
    });
    return count;
  }

  // 根据id删除节点并返回新数组
  private removeTreeNode(tree: any[], id: string | number): any[] {
    const removeNode = (nodes: any[]): any[] => {
      return nodes.filter(node => {
        if (node.id === id) {
          return false;
        }
        if (node.children && node.children.length) {
          node.children = removeNode(node.children);
        }
        return true;
      });
    };

    return removeNode(JSON.parse(JSON.stringify(tree)));
  }
}
</script>

<style lang="scss" scoped>
@import '../page-style.scss';

.role-name {
  color: #409eff;
}

.disabled-count {
  margin-top: 10px;
  margin-bottom: 5px;
  color: #f56c6c;
  font-size: 14px;
}

.function-list-item {
  margin-top: 10px;

  .function-list-item-title {
    margin-top: 5px;
    margin-bottom: 5px;
  }
}
</style>

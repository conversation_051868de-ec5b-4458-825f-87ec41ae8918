/*
 * @Description:
 * @Author: 小圆
 * @Date: 2025-06-18 10:05:29
 * @LastEditors: 小圆
 */
import Vue from 'vue';
import { Component } from 'vue-property-decorator';

export namespace SentenceMixinSpace {
  export interface Sentence {
    sentence: string;
    redWords: string[];
    grammar: string;
  }
}

@Component
export default class SentenceMixin extends Vue {
  // 渲染句子html
  renderSentenceHtml(sentence: SentenceMixinSpace.Sentence) {
    function getRedWord(word: string) {
      return `<span style="color: red;font-weight: bold;">${word}</span>`;
    }

    function getPlaceholder(index) {
      const placeholder = '%%%%';
      return `${placeholder}${index}${placeholder}`;
    }

    let html = sentence.sentence;
    let redWords = [...sentence.redWords];
    let htmlArr = html.split(' ');

    // 打上标识符号，最后还原替换内容，避免替换后的内容再次被替换
    const replacements = [];
    let placeIndex = 0;

    // 判断句子词组中是否存在相同项，存在相同项则添加替换词组并移除错误单词
    htmlArr.forEach((item, index) => {
      let redWordIndex = redWords.findIndex(word => word === item);
      if (redWordIndex > -1) {
        const replacement = getRedWord(item);
        htmlArr[index] = getPlaceholder(placeIndex);
        replacements.push(replacement);
        redWords.splice(redWordIndex, 1);
        placeIndex++;
      }
    });

    html = htmlArr.join(' ');

    // 剩余句子根据正则判断
    redWords.forEach((redWord, index) => {
      const replacement = getRedWord(redWord);
      html = html.replace(redWord, getPlaceholder(placeIndex));
      replacements.push(replacement);
      placeIndex++;
    });

    // 还原替换内容
    replacements.forEach((replacement, index) => {
      html = html.replace(`${getPlaceholder(index)}`, replacement);
    });

    return html;
  }
}

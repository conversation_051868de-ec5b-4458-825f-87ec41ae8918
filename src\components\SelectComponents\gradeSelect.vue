<!--
 * @Descripttion: 年级选择组件
 * @Author: 小圆
 * @Date: 2024-01-17 11:10:45
 * @LastEditors: 小圆
-->
<template>
  <div class="header__select">
    <span class="select__label">年级：</span>
    <el-select v-model="gradeValue" class="select" @change="handleChange" placeholder="请选择">
      <el-option v-for="item in gradeList" :key="item.id" :label="item.name" :value="item.id">
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { getGradeListByRole } from '@/service/api';
import UserRole from '@/utils/UserRole';
export default {
  name: 'gradeSelect',
  props: {
    value: {
      type: [String, Number],
      default: '',
    },

    noAll: {
      type: Boolean,
      default: false,
    },
    year: {
      type: String,
      default: '',
    },
    excludeRoles: {
      type: Array,
      default() {
        return [];
      },
    },
  },

  watch: {
    value(val, oldVal) {
      this.gradeValue = val;
    },
  },

  data() {
    return {
      gradeList: [],
      gradeValue: '',
    };
  },

  mounted() {},

  methods: {
    // 初始化年级学科
    async initGrade() {
      this.gradeList = await UserRole.getGradeList({
        schoolYear: this.year,
        excludeRoles: this.excludeRoles,
      });
      this.gradeValue = this.gradeList[0]?.id;
      if (this.value) {
        this.gradeValue = this.value;
      }
      this.$emit('input', this.gradeValue);
      return this.gradeList;
    },

    handleChange(val) {
      this.$emit('input', val);
      const grade = this.gradeList.find(item => item.id == val);
      this.$emit('change', val, grade);
    },
  },
};
</script>

<style lang="scss" scoped>
@import './select.scss';
</style>

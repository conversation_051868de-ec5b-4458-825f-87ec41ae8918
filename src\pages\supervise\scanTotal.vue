<!--
 * @Descripttion: 扫描总量
 * @Author: 小圆
 * @Date: 2023-11-28 16:55:40
 * @LastEditors: 小圆
-->
<template>
  <div class="scan-totle">
    <div class="scan-totle-header">
      <StatisticFilter
        v-model="queryData"
        :isCanAllGrade="false"
        @onLoad="handleFilterLoad"
      ></StatisticFilter>
    </div>

    <div class="scan-totle-main">
      <div class="statistic-tabs">
        <div
          v-for="item in tabList"
          :key="item.value"
          class="tab-item"
          :class="{ active: item.value == tab }"
          @click="changeTab(item.value)"
        >
          {{ item.label }}
        </div>
        <div class="statistic-tab--right">
          <span class="export-btn" @click="handleExport">导出 </span>
        </div>
      </div>

      <div class="statistic-detail" v-if="isInit">
        <statistic-detail-exam v-if="tab == 'exam'" :queryData="queryData"></statistic-detail-exam>
        <statistic-detail-class
          v-if="tab == 'class'"
          :queryData="queryData"
          :subjectList="subjectList"
          :gradeList="gradeList"
        ></statistic-detail-class>
        <statistic-detail-teacher
          v-if="tab == 'teacher'"
          :queryData="queryData"
        ></statistic-detail-teacher>
        <statistic-detail-student
          v-if="tab == 'student'"
          :queryData="queryData"
          :gradeList="gradeList"
        ></statistic-detail-student>
      </div>
    </div>
  </div>
</template>

<script>
import StatisticFilter from './components/statistic-filter.vue';
import { ClassList, exportScanData, getStatScanData } from '@/service/pexam';
import { getToken } from '@/service/auth';
import { classList } from '@/service/api';
import NoData from '@/components/noData.vue';
import StatisticDetailExam from './components/statistic-detail-exam.vue';
import StatisticDetailClass from './components/statistic-detail-class.vue';
import StatisticDetailStudent from './components/statistic-detail-student.vue';
import StatisticDetailTeacher from './components/statistic-detail-teacher.vue';
import moment from 'moment';

export default {
  components: {
    StatisticFilter,
    StatisticDetailExam,
    StatisticDetailClass,
    StatisticDetailStudent,
    StatisticDetailTeacher,
    NoData,
  },
  props: {},
  data() {
    return {
      // tab列表
      tabList: [
        {
          label: '测评详情',
          value: 'exam',
        },
        {
          label: '班级详情',
          value: 'class',
        },
        {
          label: '教师详情',
          value: 'teacher',
        },

        {
          label: '学生详情',
          value: 'student',
        },
      ],
      // 当前tab
      tab: 'exam',
      // 是否初始化完成
      isInit: false,

      // 查询参数
      queryData: {
        yearValue: '',
        gradeValue: '',
        subjectValue: '',
        typeValue: '',
        dateRange: [],
        statType: 0,
      },
      // 学科列表
      subjectList: [],
      // 年级列表
      gradeList: [],
    };
  },

  computed: {},
  created() {
    this.tab = this.$route.query.tab || 'exam';
  },
  methods: {
    // 处理筛选组件加载成功
    handleFilterLoad({ subjectList, gradeList }) {
      this.subjectList = subjectList;
      this.gradeList = gradeList;
      this.isInit = true;
    },

    changeTab(tab) {
      this.tab = tab;
      this.$router.replace({ query: { tab } });
    },

    // 处理导出
    handleExport() {
      this.$bus.$emit('handle-export');
    },
  },
};
</script>

<style lang="scss" scoped>
.fl {
  float: left;
}

.fr {
  float: right;
}

.scan-totle {
}
.scan-totle-header {
  padding: 0 24px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
  margin-bottom: 16px;
}

.scan-totle-main {
  background-color: #fff;
  box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
  border-radius: 6px;
  .scan-totle-title {
    position: relative;
    padding-left: 16px;
    line-height: 38px;
    overflow: hidden;
    margin-bottom: 20px;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #3f4a54;
    }

    .btn {
    }

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      margin: auto;
      width: 6px;
      height: 24px;
      background: #409eff;
      border-radius: 3px;
    }
  }
}
.scan-totle-table {
  border: 1px solid #e4e8eb;
  cursor: move;
  ::v-deep .el-table__body-wrapper {
  }
}

.statistic-tabs {
  width: 100%;
  line-height: 50px;
  background: #e9f4ff;
  display: flex;
  // border-radius: 10px 10px 0 0;

  .tab-item {
    min-width: 120px;
    height: 50px;
    font-size: 18px;
    border-radius: 6px 6px 0px 0px;
    text-align: center;
    padding: 0 15px;
    cursor: pointer;

    &.active {
      background: #ffffff;
      border: 1px solid #ffffff;
      font-weight: bold;
      font-size: 18px;
      color: #409eff;
    }
  }
}

.statistic-detail {
  padding: 15px 24px;
}

.statistic-tab--right {
  flex: 1;
  text-align: right;
}

.export-btn {
  margin-right: 20px;

  font-size: 16px;
  color: #409eff;

  cursor: pointer;

  &:hover {
    color: rgba(#409eff, 0.8);
  }
}
</style>

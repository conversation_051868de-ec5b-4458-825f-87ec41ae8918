<template>
  <div v-if="currentSubject" class="add-ques-wrapper">
    <div class="add-ques-left">
      <ques-point :point-info="pointInfo" class="ques-point-left" ref="quesPoint"></ques-point>
      <el-select
        size="mini"
        class="select-subject"
        v-model="currentSubject"
        @change="subjectChange"
      >
        <el-option v-for="item in subjectList" :key="item.id" :label="item.name" :value="item.id">
        </el-option>
      </el-select>
    </div>
    <div class="add-ques-right">
      <div class="right-wrapper">
        <div id="sticky_container" class="add-ques-content" ref="addQuesContent">
          <ques-c30-filter
            :filter-list="filterList"
            data-stuck="stuck_c30"
            :stuck="stuckInfo.stuck_c30"
            class="sticky"
            ref="c30Filter"
          ></ques-c30-filter>
          <div style="width: 100%; height: 10px; background-color: #f1f6f9"></div>
          <replace-ques-card
            @currentPage="getCurrentPage"
            @getParams="getParams"
            current-step="0"
            only-view="true"
            ref="quesCard"
          ></replace-ques-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import QuesC30Filter from '@/components/QuesC30Filter/QuesC30Filter.vue';
import QuesPoint from '@/components/QuesPoint/quesPoint.vue';
import ReplaceQuesCard from '@/components/ReplaceQuesCard/ReplaceQuesCard';
import { mapState, mapGetters } from 'vuex';
import { localSave, sessionSave } from '@/utils';
import HashUtil from '@/utils/hashUtil';

import { getUserInfoToPersonalityTest } from '../../service/api';
import UserRole from '@/utils/UserRole';

export default {
  name: 'search',
  data() {
    return {
      stuckInfo: {
        stuck_c30: false,
      },
      currentSubject: '',
      subjectList: [],
      filterList: null,
      pointInfo: null,
      currentPage: null,
    };
  },
  components: {
    QuesC30Filter,
    QuesPoint,
    ReplaceQuesCard,
  },
  computed: {
    ...mapState(['teachParams']),
    ...mapGetters(['subjectMap', 'loginInfo']),
  },
  mounted() {
    this.init();
  },
  created() {
    //恢复数据
    let filterData = localSave.get('qm-filterList-' + this.currentSubject);
    let pointData = localSave.get('qm-pointInfo-' + this.currentSubject);

    if (filterData && new Date().getTime() - filterData.time < 86400000) {
      this.filterList = filterData.data;
    }

    if (pointData && new Date().getTime() - pointData.time < 86400000) {
      this.pointInfo = pointData.data;
    }

    this.currentPage = HashUtil.get_hash('page');

    if (this.currentPage) {
      this.currentPage = parseInt(this.currentPage) || 1;
      setTimeout(() => {
        this.currentPage = null;
      }, 500);
    }

    this.onConditionChange();
  },
  beforeDestroy() {
    this.$bus.$off('changeC30Filter');
    this.$bus.$off('changePoint');
  },
  methods: {
    // initTimeout() {
    //   this.init();
    // },
    async init() {
      this.subjectList = localSave.get('SUBJECT_LIST');
      var $this = this;
      const ret = await UserRole.getUserInfoPersonalityTest();
      $this.subjectList = ret.userSubList;
      if (sessionSave.get('currentSubject')) {
        $this.currentSubject = sessionSave.get('currentSubject').id;
      } else {
        $this.currentSubject = $this.loginInfo.subjectid || '';
      }
      if (!$this.currentSubject) {
        if ($this.$route.query.fromName === 'previewPaper') {
          $this.currentSubject = $this.subjectList.filter(item => {
            return item.name == $this.$route.query.subjectName;
          })[0].id;
        } else {
          $this.currentSubject = $this.subjectList[0].id;
        }
        sessionSave.set('currentSubject', $this.subjectMap[$this.currentSubject]);
      } else {
        sessionSave.set('currentSubject', $this.subjectMap[$this.currentSubject]);
      }
      $this.$nextTick(() => {
        observeStickyHeaderChanges(document.querySelector('#sticky_container'));
        document.addEventListener('sticky-change', e => {
          let node = e.detail.target;
          $this.$set($this.stuckInfo, node.dataset['stuck'], e.detail.stuck);
        });

        $this.changeFilter();
        $this.$refs.quesCard.reload();
      });

      // $this.changeFilter();
      // $this.$refs.quesCard.reload();
    },
    getParams() {
      return {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        subjectId: this.currentSubject,
      };
    },
    getCurrentPage(page) {
      this.$refs.addQuesContent.scrollTop = 0;
      if (page) {
        HashUtil.set_hashchange({
          page,
        });
      }
    },
    subjectChange() {
      let subject = this.subjectMap[this.currentSubject];
      sessionSave.set('currentSubject', subject);
      this.changeFilter();
      this.$bus.$emit('topic', '');
      this.$refs.quesCard.reload();
    },
    // 监听条件变化
    onConditionChange() {
      this.$nextTick(function () {
        this.$bus.$on('changeC30Filter', (type, value) => {
          this.$refs.quesCard.updateFilterInfo({
            type: 'c30',
            filterList: value.filterList,
            page: this.currentPage,
          });
          if (!this.currentPage) {
            this.getCurrentPage(1);
          }
          localSave.set('qm-filterList-' + this.currentSubject, {
            time: new Date().getTime(),
            data: value.filterList,
          });
        });
        this.$bus.$on('changePoint', (type, value) => {
          if (type === 'c30Point') {
            this.$refs.quesCard.updateFilterInfo({
              type: 'c30',
              pointInfo: value,
              page: this.currentPage,
            });

            if (!this.currentPage) {
              this.getCurrentPage(1);
            }
            localSave.set('qm-pointInfo-' + this.currentSubject, {
              time: new Date().getTime(),
              data: value,
            });
          }
        });
      });
    },
    changeFilter() {
      this.$nextTick(() => {
        this.$refs.quesPoint.getC30Point();
        this.$refs.c30Filter.readC30FilterCondition();
        this.$refs.quesCard.pointInfo = {};
        this.$refs.c30Filter.filterC30Info = {};
        this.$refs.quesCard.updateFilterInfo({
          type: 'c30',
          filterList: [{}],
          pointInfo: null,
        });
      });
    },
    // 切换学校后更新数据
    changeSchool() {
      this.currentSubject = '';
      this.init();
    },
  },
};
</script>
<style lang="scss" scoped>
.add-ques-wrapper {
  display: flex;

  &.block {
    display: block;
  }

  .add-ques-left {
    overflow-y: hidden;
    position: relative;

    .select-subject {
      position: absolute;
      top: 10px;
      width: 96px;
      right: 10px;
    }

    .ques-point-left {
      height: calc(100vh - 125px);
    }

    .add-ques-kinds-box {
      // text-align: center;
      // margin: 16px 0;
      // margin-left: 16px;
      margin: 12px 0;
      margin-left: 22px;

      .el-button {
        padding: 12px 10px;
      }
    }
  }

  .add-ques-right {
    flex: 1;
    overflow-x: auto;
    .right-wrapper {
      height: calc(100vh - 115px);
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .add-ques-content {
        flex: 1;
        overflow-y: auto;
        position: relative;

        .selected-box {
          position: absolute;
          right: 75px;
          top: 10px;
        }
      }
    }
  }
}

.content-catalog {
  .catalog-treeBox {
    .catalog-tree {
      height: calc(100vh - 178px);
    }
  }
}

html::-webkit-scrollbar {
  width: 5px;
  height: 6px;
}

html::-webkit-scrollbar-track {
  background: rgb(239, 239, 239);
  border-radius: 2px;
}

html::-webkit-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 10px;
}

html::-webkit-scrollbar-thumb:hover {
  background: rgb(133, 133, 133);
}

html::-webkit-scrollbar-corner {
  background: #179a16;
}

div::-webkit-scrollbar {
  width: 5px;
  height: 6px;
}

div::-webkit-scrollbar-track {
  background: rgb(239, 239, 239);
  border-radius: 2px;
}

div::-webkit-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 10px;
}

div::-webkit-scrollbar-thumb:hover {
  background: rgb(133, 133, 133);
}

div::-webkit-scrollbar-corner {
  background: #179a16;
}
</style>

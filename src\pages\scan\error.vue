<template>
  <div class="paper-container">
    <div class="header-back el-icon-arrow-left"><span @click="backRecord">返回</span></div>

    <el-tabs v-model="activeName" @tab-click="tabClick" class="scan-student-error-tabs" type="card">
      <el-tab-pane :label="`得分异常(${studentCountInfo.score})`" name="score">
        <scan-student-list :paper-id="paperId" :paper-info="paperInfo" :status="1"
                           @student-count="studentCount"></scan-student-list>
      </el-tab-pane>
      <el-tab-pane :label="`考号重复(${studentCountInfo.repeat})`" name="repeat">
        <scan-student-repeat :paper-id="paperId" :paper-info="paperInfo"
                             @student-count="studentCount"></scan-student-repeat>
      </el-tab-pane>
      <el-tab-pane :label="`未匹配学生(${studentCountInfo.find})`" name="find">
        <scan-student-not-found :paper-id="paperId" :paper-info="paperInfo"
                                @student-count="studentCount"></scan-student-not-found>
      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script>
import {mapGetters} from 'vuex';
import {getPaperInfo} from "@/service/pexam";
import ScanStudent from "@/components/scan/ScanStudent";
import ScanStudentList from "@/components/scan/ScanStudentList";
import ScanStudentRepeat from "@/components/scan/ScanStudentRepeat";
import HashUtil from "@/utils/hashUtil";
import ScanStudentNotFound from "@/components/scan/ScanStudentNotFound";

export default {
  name: 'scan-success',
  data() {
    return {
      paperId: this.$route.query.paperId,
      activeName: 'score',
      paperInfo: null,
      studentCountInfo: {
        score: 0,
        repeat: 0,
        find: 0
      }
    };
  },
  computed: {
    ...mapGetters([
      'filterSubjectList'
    ])
  },
  components: {ScanStudentNotFound, ScanStudentRepeat, ScanStudentList, ScanStudent},
  mounted() {
    this.init()
    this.activeName = HashUtil.get_hash('tab') || 'score'
  },
  methods: {
    studentCount(info) {
      this.studentCountInfo[info.name] = info.total || 0
    },
    backRecord() {
      this.$router.replace('/scan/')
    },
    tabClick() {
      HashUtil.set_hashchange({tab: this.activeName})
    },
    init() {
      this.getPaperInfo()
    },
    async getPaperInfo() {
      let res = await getPaperInfo({
        id: this.paperId
      })

      this.paperInfo = res.data
    }
  }
};
</script>

<style lang="scss" scoped>
.paper-container {
  position: relative;

  .header-back {
    // left: 30px;
    top: 20px;
    position: absolute;
    padding: 12px 20px;
    font-size: 16px;
    color: #3a5eff;
    cursor: pointer;
  }
}
</style>
<style lang="scss">
.scan-student-error-tabs {
  height: calc(100% - 36px);

  .el-tabs__content {
    height: 98%;

    .el-tab-pane {
      height: 100%;
    }
  }

  .el-tabs__header {
    margin-left: 80px;
  }

}
</style>
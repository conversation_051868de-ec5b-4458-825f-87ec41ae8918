<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-03-16 09:09:53
 * @LastEditors: 小圆
-->
<template>
  <div class="report">
    <transition name="bounce">
      <aside class="report-aside" v-show="isAsideMenuShow">
        <base-menu
          class="report-aside__menu base-menu"
          ref="menuRef"
          accordion
          :menuList="menuList"
          :base-route="BASE_ROUTE"
          :router="true"
          @open="handleMenuOpen"
        ></base-menu>
      </aside>
    </transition>
    <main class="report-main">
      <div class="control-btn click-element" @click="isAsideMenuShow = !isAsideMenuShow">
        <i class="el-icon-caret-right" v-show="!isAsideMenuShow"></i>
        <i class="el-icon-caret-left" v-show="isAsideMenuShow"></i>
      </div>

      <report-header class="report-main__header" :filters-option="filtersOption">
        <div>
          <el-button class="header-item" type="primary" @click="onChange">查询</el-button>
        </div>
        <div class="export-btns">
          <el-button class="header-item" type="primary" @click="onExportExcel">导出</el-button>
        </div>
      </report-header>
      <transition name="slide-fade" mode="out-in">
        <router-view class="study-report" ref="studyReportRef"></router-view>
      </transition>
    </main>
  </div>
</template>

<script lang="ts">
import BaseMenu from '@/components/Base/menu/BaseMenu.vue';
import { menuList } from '@/router/routerMenu/studyReportRouterMenu';
import { Component, Vue, Watch } from 'vue-property-decorator';

import FilterModule from '@/pages/studyReport/plugins/FilterModule';
import { FilterDataKey, menuOption } from '@/pages/studyReport/plugins/types';
import ReportHeader from './components/ReportHeader.vue';

const BASE_ROUTE = '/home/<USER>';

@Component({
  components: { BaseMenu, ReportHeader },
})
export default class StudyReport extends Vue {
  // 监听筛选单例
  FilterModule: typeof FilterModule = FilterModule;
  BASE_ROUTE = BASE_ROUTE;
  // 路由菜单列表项
  menuList: typeof menuList = menuList;
  // 筛选器选项
  filtersOption = [] as FilterDataKey[];

  // 是否显示侧边菜单
  isAsideMenuShow: boolean = true;

  public $refs!: {
    menuRef: BaseMenu;
    studyReportRef: Vue;
  };

  @Watch('$route.name', { immediate: true })
  onRouteChange(name: string) {
    this.filtersOption = this.FilterModule.getMenuFilterKeys(name);
    if (this.$refs.studyReportRef) this.$refs.studyReportRef.$el.scrollTop = 0;
  }

  get menuTitle() {
    return this.$route.meta.title;
  }

  get source() {
    return this.FilterModule.filterInfo.examInfo?.source;
  }

  // 处理菜单打开事件
  handleMenuOpen(index, indexPath: string[]) {
    function openAccordion() {
      this.menuList.forEach(item => {
        const path = BASE_ROUTE + '/' + item.path;
        if (indexPath.find(item => item === path)) return;
        this.$refs.menuRef.elMenu.close(path);
      });
    }
    openAccordion.apply(this); // 打开最外层手风琴效果
    this.$router.push(index); // 打开菜单，跳转路由（通过路由重定向）
  }

  // 选择器更改事件
  async onChange() {
    FilterModule.trigger('changeFilter');
  }

  // 导出excel
  onExportExcel() {
    FilterModule.trigger('exportTable');
  }
}
</script>

<style scoped lang="scss">
@import './components/reportSelect.scss';

$aside-width: 220px;

.report {
  display: flex;
  gap: 20px;

  padding-left: 20px !important;
  padding-right: 20px !important;

  width: 100%;
  max-width: none !important;
  min-width: auto !important;
  min-height: 400px;

  font-size: 14px;
  overflow: auto;

  &-aside {
    position: relative;
    background: #fff;

    width: $aside-width;
    max-width: $aside-width;
    min-width: $aside-width;

    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;

    &__menu {
      border: 0;

      height: 100%;
      overflow: hidden;
    }
  }

  &-main {
    position: relative;
    display: flex;
    flex-direction: column;

    width: 0;
    flex: 1;
    min-width: 800px;

    border-radius: 6px;
  }
}

.control-btn {
  position: absolute;
  width: 16px;
  height: 40px;
  line-height: 40px;
  top: 0;
  bottom: 0;
  left: -16px;
  margin: auto;
  background: rgb(180, 201, 198);
  color: #fff;
  border-radius: 6px 0 0 6px;
}

.export-btns {
  flex: 1;
  text-align: right;
}

.report-main__header {
  // padding-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
  padding: 14px;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 6px;
}

// 侧边菜单动画
$time: 0.3s;
.bounce-enter-active {
  animation: bounce-in $time ease;
}
.bounce-leave-active {
  animation: bounce-in $time linear reverse;
}

@keyframes bounce-in {
  0% {
    width: 0;
    max-width: 0;
    min-width: 0;
    opacity: 0;
  }
  100% {
    width: $aside-width;
    max-width: $aside-width;
    min-width: $aside-width;
    opacity: 1;
  }
}

.base-menu {
  max-height: 80%;

  ::v-deep {
    .el-submenu[level='1'] {
      & > .el-submenu__title {
        margin-bottom: 5px;
        color: #fff;
        background-color: #2574ff;
        i {
          color: #fff;
        }
      }
    }
  }

  // 达美嘉样式
  // ::v-deep {
  //   .el-menu {
  //     background: #daf7f1;
  //   }

  //   .el-submenu[level='1'] {
  //     & > .el-submenu__title {
  //       margin-top: 10px;
  //       margin-left: 10px;
  //       height: 40px;
  //       line-height: 40px;
  //       color: #fff;
  //       background-image: linear-gradient(to right, #103f83 0%, #103f83 100%);
  //       border-top-left-radius: 8px;
  //       border-bottom-left-radius: 8px;

  //       i {
  //         color: #fff;
  //       }
  //     }
  //   }
  //   .el-submenu[level='1'].is-active {
  //     & > .el-submenu__title {
  //       color: #f70;
  //     }
  //   }

  //   .el-submenu[level='2'] {
  //     & > .el-submenu__title {
  //       margin-left: 20px;
  //       margin-right: 5px;
  //       margin-top: 5px;
  //       height: inherit;
  //       line-height: normal;
  //       background-image: linear-gradient(to right, #2b2b2b 0%, #323232 50%, #3c3f41 100%);
  //       color: #ffffff;
  //       border-radius: 8px;
  //       font-weight: bolder;

  //       span {
  //         padding-left: 10px !important;
  //       }

  //       i {
  //         color: #fff;
  //       }
  //     }
  //   }

  //   .el-menu-item {
  //     padding-left: 15px !important;
  //     margin: 8px;
  //     font-size: 13px;
  //     color: #2b2929;
  //     height: initial;
  //     line-height: normal;
  //     background: transparent;

  //     span {
  //       padding-left: 10px !important;
  //       &:hover {
  //         color: #f70;
  //       }
  //     }

  //     &::before {
  //       content: '|';
  //       left: 13px;
  //       position: initial;
  //       width: initial;
  //       height: initial;
  //       top: initial;
  //       left: initial;
  //       color: #f70;
  //       border-radius: initial;
  //       background: initial;
  //     }
  //   }

  //   .el-menu-item.is-active {
  //     color: #f70 !important;
  //     text-decoration: underline;
  //     text-underline-offset: 2px;

  //     &::before {
  //       border-radius: 0;
  //     }
  //   }
  // }
}
</style>

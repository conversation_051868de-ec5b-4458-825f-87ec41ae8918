import { getExamReportList, listExamSubject } from '@/service/pexam';
import { listRoleMenuAPI } from '@/service/pstat';
import { getExamReportSubjectList } from '@/utils/examReportUtils';
import { sessionSave, findIntersection, getToRoles } from '@/utils/index.js';
import UserRole from '@/utils/UserRole';
export interface MenuItemImpl {
  title: string;
  index: string;
  path?: string;
  children?: Array<MenuItemImpl>;
}

// 作文分析子菜单
const compositionAnalysisChildren = [
  'compositionClassSituation',
  'lexicalAnalysis',
  'grammarAnalysis',
  'referenceSample',
];

// 不受学校设置权限的菜单列表
export const noPermissionMenuList: string[] = [...compositionAnalysisChildren, 'campusComparison'];

// 常用菜单前缀
export const oftenName: string = 'often_';
// 默认菜单列表
const defaultOftenMenuIndexList: string[] = [
  'campusComparison',
  'cardHome',
  'fiveRate',
  'subjectComparison',
  'scoreSheet',
  'questionAnalysis',
];
// 默认全部菜单列表
const defaultAllMenuList: MenuItemImpl[] = [
  { title: '校区对比', index: 'campusComparison', path: '/reportDetail/campusComparison' },
  { title: '学情概览', index: 'overview', path: '/reportDetail/examOverview/overview' },
  {
    title: '学科对比',
    index: 'subjectComparison',
    path: '/reportDetail/examOverview/subjectComparison',
  },
  { title: '一分五率', index: 'fiveRate', path: '/reportDetail/examOverview/fiveRate' },
  {
    title: '学生小分表',
    index: 'scoreSheet',
    path: '/reportDetail/examOverview/scoreSheet',
  },
  {
    title: '大小题分析',
    index: 'questionAnalysis',
    path: '/reportDetail/examOverview/questionAnalysis',
  },
  {
    title: '等级分布',
    index: 'gradeDistribute',
    path: '/reportDetail/examOverview/gradeDistribute',
  },
  { title: '优困生分布', index: 'topAndDiff', path: '/reportDetail/examOverview/topAndDiff' },
  { title: '上线分析', index: 'online', path: '/reportDetail/examOverview/online' },
  { title: '临界生分布', index: 'limitStu', path: '/reportDetail/examOverview/limitStu' },
  { title: '成绩单', index: 'cardHome', path: '/reportDetail/classCompare/cardHome' },
  {
    title: '成绩分段分析',
    index: 'scoreSection',
    path: '/reportDetail/classCompare/scoreSection',
  },
  { title: '名次分析', index: 'totalRank', path: '/reportDetail/classCompare/totalRank' },
  // {
  //   title: '各名次段分析',
  //   index: 'rankSection',
  //   path: '/reportDetail/classCompare/rankSection',
  // },
  { title: '箱线图', index: 'boxplot', path: '/reportDetail/classCompare/boxplot' },
  {
    title: '命题质量',
    index: 'qualityReport',
    path: '/reportDetail/paperAnalyze/qualityReport',
  },
  {
    title: '双向细目表',
    index: 'bothWayReport',
    path: '/reportDetail/paperAnalyze/bothWayReport',
  },
  { title: '题型均分', index: 'quesTypeAvg', path: '/reportDetail/paperAnalyze/quesTypeAvg' },
  {
    title: '作答详情',
    index: 'answerDetail',
    path: '/reportDetail/paperAnalyze/answerDetail',
  },
  {
    title: '知识点分析',
    index: 'knowledgeAnalyze',
    path: '/reportDetail/paperAnalyze/knowledgeAnalyze',
  },

  {
    title: '班级学情',
    index: 'compositionClassSituation',
    path: '/reportDetail/compositionClassSituation',
  },
  {
    title: '词汇分析',
    index: 'lexicalAnalysis',
    path: '/reportDetail/lexicalAnalysis',
  },
  {
    title: '语法分析',
    index: 'grammarAnalysis',
    path: '/reportDetail/grammarAnalysis',
  },
  {
    title: '参考范文',
    index: 'referenceSample',
    path: '/reportDetail/referenceSample',
  },
  { title: '试卷讲评', index: 'paperComment', path: '/reportDetail/paperComment' },
  {
    title: '个性化练习',
    index: 'personalPracticeReport',
    path: '/reportDetail/personalPracticeReport/practiceReport',
  },
];

function getBase() {
  let base = '/home';
  if (location.href.includes('/dReport')) {
    base = '/dReport';
  }
  return base;
}

// 获取默认菜单列表
export function getDefaultOftenMenuIndexList(): string[] {
  return JSON.parse(JSON.stringify(defaultOftenMenuIndexList));
}

// 设置桌面菜单列表筛选
function filterMenuByDeskTop(allMenuList: MenuItemImpl[]) {
  return allMenuList.filter(item => item.index !== 'paperComment');
}

// 设置角色权限菜单列表筛选
async function filterMenuByRole(allMenuList: MenuItemImpl[]) {
  // 获取报告数据
  const { examId, campusCode, v, year, source, gradeName }: any = sessionSave.get('reportDetail');

  let userRoles = getToRoles(year, campusCode);
  const { roleSubjectList, noRoleSubjectList } = await getExamReportSubjectList({
    examId,
    roles: userRoles,
    v,
    year,
    gradeName,
    statType: 1,
    campusCode,
  });

  // 检查学科对比
  allMenuList = await checkSubjectComparison(noRoleSubjectList, allMenuList);
  // 检查作文智批
  allMenuList = await checkCompositionAnalysis(roleSubjectList, allMenuList);
  // 检查菜单权限
  allMenuList = await checkMenuPermission(allMenuList);
  // 检查校区对比
  allMenuList = await checkCampusComparison(allMenuList);
  return allMenuList;
}

// 获取全部菜单列表
export async function getAllMenuList() {
  let allMenuList = JSON.parse(JSON.stringify(defaultAllMenuList));

  // 设置桌面菜单列表
  let base = getBase();
  // 设置菜单路由
  allMenuList = allMenuList.map(item => {
    return {
      ...item,
      path: base + item.path,
    };
  });

  if (base == '/dReport') {
    allMenuList = filterMenuByDeskTop(allMenuList);
  }
  return filterMenuByRole(allMenuList);
}

// 获取第一个常用菜单路由
export function getFirstOftenMenuRoute() {
  let base = getBase();
  let currentMenu: MenuItemImpl;
  for (const item of defaultOftenMenuIndexList) {
    currentMenu = defaultAllMenuList.find(t => t.index === item);
    if (currentMenu) break;
  }
  return {
    path: base + currentMenu.path,
    query: {
      reportType: oftenName,
    },
  };
}

// 检查学科对比
async function checkSubjectComparison(subjectList, allMenuList) {
  const { source, campusCode, year }: any = sessionSave.get('reportDetail');

  const userRoles = getToRoles(year, campusCode);

  // 分层班屏蔽学科对比
  if (source == 101) {
    allMenuList = allMenuList.filter(item => item.index !== 'subjectComparison');
  }

  // 没有权限屏蔽学科对比
  if (!UserRole.isOperation) {
    // 全部学科发布，且具有校管、年级主任、班主任权限，则显示学科对比
    const isCanSubjectComparison = subjectList.some(item => {
      const roles = findIntersection(userRoles, item.roles);
      return [1, 2, 5].some(role => roles.includes(role));
    });

    if (!isCanSubjectComparison) {
      allMenuList = allMenuList.filter(item => item.index !== 'subjectComparison');
    }
  }
  return allMenuList;
}

// 检查作文智批
async function checkCompositionAnalysis(subjectList, allMenuList) {
  // 检查是否具有作文智批的学科
  const isCanCompositionAnalysis = subjectList.some(item => {
    return item.isSmart == 1;
  });

  // 没有作文智批则屏蔽作文分析
  if (!isCanCompositionAnalysis) {
    allMenuList = allMenuList.filter(item => !compositionAnalysisChildren.includes(item.index));
  }

  // 如果没有英语学科，则屏蔽语法分析;
  const schSubList = UserRole.userInfo.schSubList;
  const hasEnglish = subjectList.some(item => {
    return schSubList.some(t => t.id == item.id && t.center_code == 'ENGLISH');
  });
  if (!hasEnglish) {
    allMenuList = allMenuList.filter(item => item.index !== 'grammarAnalysis');
  }

  return allMenuList;
}

// 检查菜单权限
async function checkMenuPermission(allMenuList) {
  // 运营不检查菜单
  if (UserRole.isOperation) return allMenuList;

  const { year, campusCode }: any = sessionSave.get('reportDetail');
  const userRoles = getToRoles(year, campusCode);

  // 获取角色对应菜单 （学校设置）
  const res = await listRoleMenuAPI({
    schId: sessionSave.get('schoolInfo').id,
    role: userRoles.join(','),
  });
  sessionSave.set('schoolReportPermission', res.data);

  const menuIds = res.data.menuIds;
  allMenuList = allMenuList.filter(item => {
    if (noPermissionMenuList.includes(item.index)) {
      return true;
    }
    return menuIds.includes(item.index);
  });
  return allMenuList;
}

// 检查校区对比
async function checkCampusComparison(allMenuList) {
  const reportDetail: any = sessionSave.get('reportDetail');
  const reportParent: any = sessionSave.get('reportParent');

  // TODO：避免接口请求
  const res = await getExamReportList({
    schoolId: sessionSave.get('schoolInfo').id,
    parentId: reportParent.examId,
    acadYearsId: '',
    acadTermId: '',
    page: 1, // 页码
    pageSize: 100,
  });
  const examList = res.data.list || [];
  const hasCampus = examList.some(item => item.campusCode);

  // 存在校区报告时主报告、校领导、运营可查看校区对比
  if (hasCampus && reportDetail.examId == reportParent.examId && (UserRole.isOperation || UserRole.isSchoolLeader)) {
    return allMenuList;
  } else {
    return allMenuList.filter(item => item.index !== 'campusComparison');
  }
}

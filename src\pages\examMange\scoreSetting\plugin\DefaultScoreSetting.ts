/*
 * @Description: 成绩修改
 * @Author: 小圆
 * @Date: 2024-05-29 09:47:26
 * @LastEditors: 小圆
 */
import { AbstractScoreSetting } from './AbstractScoreSetting';

import {
  getExamStuList,
  getExamStuScanData,
  publishScoreAPI,
  saveExamStuScanDetails,
  setExamStuState,
} from '@/service/api';
import { updateProgress } from '@/service/pexam';

import { sessionSave } from '@/utils';

export class DefaultScoreSetting extends AbstractScoreSetting {
  constructor() {
    super();
  }

  // 初始化学生
  async initStu(params: { examId: any; subjectId: any }) {
    const res = await getExamStuList(params);
    const stuList = res.data;
    const stuTree = this.stuListToTree(stuList);

    return {
      stuList,
      stuTree,
    };
  }

  // 初始化学生题目
  async initStuQuestion(option: { examId: any; subjectId: any; studentId: any; abCardSheetType: any }) {
    const examId = option.examId;
    const subjectId = option.subjectId;
    const studentId = option.studentId;
    const abCardSheetType = option.abCardSheetType;

    const params = {
      examId,
      subjectId,
      studentId,
      abCardSheetType
    };

    const res = await getExamStuScanData(params);
    res.data.forEach(item => {
      if (item.isObj) {
        item.backAnswer = item.answer;
      } else {
        item.tempScore = undefined;
      }
    });
    const questionList = res.data;
    return questionList;
  }

  stuListToTree(stuList) {
    stuList = stuList.map(item => {
      return {
        ...item,
        label: item.stuName,
      };
    });

    // type:缺考状态 0:未扫描 1:已扫描/批改完整 2:缺考 3:批改不完整  4：0分卷 5：不参与统计
    const type_0_stu_list = stuList.filter(item => item.type == 0);
    const type_1_stu_list = stuList.filter(item => item.type == 1);
    const type_2_stu_list = stuList.filter(item => item.type == 2);
    const type_3_stu_list = stuList.filter(item => item.type == 3);
    const type_4_stu_list = stuList.filter(item => item.type == 4);
    const type_5_stu_list = stuList.filter(item => item.type == 5);

    const type_0_stu_group = this.groupByClzName(type_0_stu_list);
    const type_1_stu_group = this.groupByClzName(type_1_stu_list);
    const type_2_stu_group = this.groupByClzName(type_2_stu_list);
    const type_3_stu_group = this.groupByClzName(type_3_stu_list);
    const type_4_stu_group = this.groupByClzName(type_4_stu_list);
    const type_5_stu_group = this.groupByClzName(type_5_stu_list);

    const type_0_stu_tree = this.groupToTree(type_0_stu_group);
    const type_1_stu_tree = this.groupToTree(type_1_stu_group);
    const type_2_stu_tree = this.groupToTree(type_2_stu_group);
    const type_3_stu_tree = this.groupToTree(type_3_stu_group);
    const type_4_stu_tree = this.groupToTree(type_4_stu_group);
    const type_5_stu_tree = this.groupToTree(type_5_stu_group);

    const tree = [
      {
        label: `批改完整 (${type_1_stu_list.length})`,
        children: type_1_stu_tree,
      },
      {
        label: `批改不完整 (${type_3_stu_list.length})`,
        children: type_3_stu_tree,
      },
      {
        label: `缺考 (${type_2_stu_list.length})`,
        children: type_2_stu_tree,
      },
      {
        label: `未扫描 (${type_0_stu_list.length})`,
        children: type_0_stu_tree,
      },
      {
        label: `0分学生 (${type_4_stu_list.length})`,
        children: type_4_stu_tree,
      },
      {
        label: `不参与统计 (${type_5_stu_list.length})`,
        children: type_5_stu_tree,
      },
    ];
    return tree;
  }

  // 重新发布成绩
  async rePublishScore(option: { examId: any; personalBookId: any; schoolId: any; source: any }) {
    const examId = option.examId;
    const personalBookId = option.personalBookId;
    const schoolId = option.schoolId;
    const source = option.source;

    if (source == 4) {
      await updateProgress({
        examId: examId,
        progress: 8,
        personalBookId: personalBookId,
        progressState: 1,
        schoolId: schoolId,
      });
    } else if (source == 3) {
      await publishScoreAPI({
        schoolId: schoolId,
        workId: personalBookId,
      });
    } else {
      return Promise.reject({ msg: '当前考试不是网、手阅' });
    }
  }

  // 设置学生类型
  async setStuType(option: { examId; subjectId; studentId; state }) {
    const params = {
      examId: option.examId,
      subjectId: option.subjectId,
      studentId: option.studentId,
      state: option.state, // state 扫描状态 0:未扫描 1：已扫描/批改完整 2：缺考，3：批改不完整，4：0分学生，5：不参与统计
    };
    await setExamStuState(params);
  }

  // 保存方法
  async saveCorrect(option: { questionList; cardInfo; examId; subjectId; studentId; images; abCardSheetType }) {
    const questionList = option.questionList;
    const cardInfo = option.cardInfo;
    const examId = Number(option.examId);
    const subjectId = option.subjectId;
    const studentId = option.studentId;
    const images = option.images;
    const abCardSheetType = option.abCardSheetType;
    
    const quesList = [];
    for (const item of questionList) {
      let score = item.score;
      let isUpdate = false;
      let ques: any = {
        questionId: item.questionId,
        quesNo: item.quesNo,
        isObj: item.isObj,
        teaId: sessionSave.get('loginInfo').id,
        teaName: sessionSave.get('loginInfo').realname,
        score: score,
        isUpdate: isUpdate,
        chooseType: item.tempChooseType,
        targetId: item.tempTargetId,
      };
      if (item.type == 7) {
        ques.answer = item.answer;
        score = item.score;
        if (score == item.fullScore) {
          ques.answer = item.rightAnswer;
        } else {
          ques.answer = '';
        }
        isUpdate = ques.answer != ques.backAnswer;
      } else if (item.isObj) {
        if (item.answer) {
          score = this.getObjScore(item);
          ques.answer = item.answer;
        } else {
          score = 0;
        }

        // 判断是否修改
        const answerSet = new Set(item.answer.split(','));
        const backAnswerSet = new Set(item.backAnswer.split(','));
        isUpdate = !this.setsAreEqual(answerSet, backAnswerSet);
      } else {
        score = item.tempScore || item.tempScore === 0 ? item.tempScore : item.score;
        isUpdate = item.tempScore || item.tempScore === 0 ? true : false;
      }

      if (item.chooseType !== item.tempChooseType) isUpdate = true;
      if (item.targetId !== item.tempTargetId) isUpdate = true;

      ques.score = score;
      ques.isRight = score === item.fullScore ? 1 : 0;
      ques.isUpdate = isUpdate;
      quesList.push(ques);
    }

    const quesNos = [];
    const pages = JSON.parse(cardInfo.cardInfo).points.pages;
    for (const page of pages) {
      let nos = [];
      for (const item of page) {
        if (Reflect.has(item, 'question_type')) {
          nos.push(item.question_no);
        }
      }
      quesNos.push(nos.join(','));
    }

    let params = {
      examId: examId,
      subjectId: subjectId,
      studentId: studentId,
      images: images,
      quesNos: quesNos,
      quesList: quesList,
      abCardSheetType: abCardSheetType,
    };

    await saveExamStuScanDetails(params);
  }
}

<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-08-28 11:34:40
 * @LastEditors: 小圆
-->
<template>
  <div class="exam-manager">
    <bread-crumbs :title="query.title">
      <template slot="titleSlot"> </template>
    </bread-crumbs>

    <el-radio-group v-model="currentTab">
      <el-radio-button :label="item.value" v-for="item in tabList" :key="item.value">{{
        item.name
      }}</el-radio-button>
    </el-radio-group>

    <div class="exam-manager-main">
      <!-- 学生管理 -->
      <manage-student v-if="currentTab == 1"></manage-student>
      <!-- 管理人员设置 -->
      <manage-editor v-if="currentTab == 2"></manage-editor>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import BreadCrumbs from '@/components/Breadcrumbs/index.vue';
import ManageStudent from './manage-student.vue';
import ManageEditor from './manage-editor.vue';

@Component({
  components: {
    BreadCrumbs,
    ManageStudent,
    ManageEditor,
  },
})
export default class ExamManage extends Vue {
  // 查询参数
  query = {
    // 考试ID
    examId: this.$route.query.examId || '',
    // 标题
    title: this.$route.query.examName || '考生管理',
    // 来源
    source: this.$route.query.source || '',
  };

  // 标签切换
  tabList = [
    {
      name: '考生管理',
      value: 1,
    },
    {
      name: '管理人员设置',
      value: 2,
    },
  ];
  // 当前标签
  currentTab = 1;

  mounted() {}
}
</script>

<style scoped lang="scss">
.exam-manager {
}

.exam-manager-main {
  margin-top: 10px;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}
</style>

<template>
  <el-dialog title="导出" :visible.sync="dialogVisible" width="400px" @closed="handleClose">
    <div class="export-dialog-content">
      <div class="export-section" v-if="showQTypeOption">
        <div class="section-title">分数来源</div>
        <el-radio-group v-model="selectedQType" @change="handleQTypeChange">
          <el-radio :label="0">得分</el-radio>
          <el-radio :label="1">赋分</el-radio>
        </el-radio-group>
      </div>
      
      <div class="export-section" v-if="!showQTypeOption">
        <div class="section-title">导出说明</div>
        <p class="export-desc">将导出当前页面的上线分析数据</p>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="exporting">确定</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

@Component
export default class OnlineExportDialog extends Vue {
  @Prop({ type: Boolean, default: false }) visible!: boolean;
  @Prop({ type: Boolean, default: false }) showQTypeOption!: boolean;
  @Prop({ type: Number, default: 0 }) currentQType!: number;

  // 内部状态
  dialogVisible = false;
  selectedQType = 0;
  exporting = false;

  @Watch('visible')
  onVisibleChange(newVal: boolean) {
    this.dialogVisible = newVal;
    if (newVal) {
      this.selectedQType = this.currentQType;
    }
  }

  @Watch('currentQType')
  onCurrentQTypeChange(newVal: number) {
    this.selectedQType = newVal;
  }

  handleQTypeChange(value: number) {
    this.selectedQType = value;
  }

  handleCancel() {
    this.dialogVisible = false;
    this.$emit('update:visible', false);
    this.$emit('cancel');
  }

  handleClose() {
    this.$emit('update:visible', false);
    this.$emit('close');
  }

  async handleConfirm() {
    this.exporting = true;
    try {
      await this.$emit('confirm', {
        qType: this.selectedQType
      });
      this.dialogVisible = false;
      this.$emit('update:visible', false);
    } catch (error) {
      console.error('导出失败:', error);
    } finally {
      this.exporting = false;
    }
  }
}
</script>

<style scoped lang="scss">
.export-dialog-content {
  .export-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #3f4a54;
      margin-bottom: 12px;
      position: relative;
      padding-left: 12px;

      &:before {
        content: '';
        width: 4px;
        height: 16px;
        background: #409eff;
        border-radius: 2px;
        position: absolute;
        left: 0;
        top: 2px;
      }
    }

    .export-desc {
      color: #666;
      font-size: 14px;
      line-height: 1.5;
      margin: 0;
      padding-left: 12px;
    }

    .el-radio-group {
      padding-left: 12px;

      .el-radio {
        margin-right: 20px;
        margin-bottom: 8px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}
</style>

<template>
  <div class="c30-choice-scores-modal">
    <el-dialog :visible="isShowDialog" title="特殊规则设置" width="600" :before-close="close" :modal-append-to-body="false"
      :close-on-click-modal="false">
      <el-tabs v-model="activeName" @tab-click="handleClick" type="card">
        <el-tab-pane label="标准" :name="IRULE_TYPE.STANDARD">
          <div class="choice-score-warp tips">
            全对的满分，错选的不得分，少选得
            <el-input style="width: 100px;" v-model="item.halfScore"></el-input>分
          </div>
        </el-tab-pane>
        <el-tab-pane label="新高考" :name="IRULE_TYPE.NEW">
          <div class="choice-score-warp tips">若满分6分，全部选对得6分，有选错的得0分。若只有两个正确选项，每选对一个得3分;若共有3个正确选项每选对一个得2分。</div>
        </el-tab-pane>
        <el-tab-pane label="断句题" :name="IRULE_TYPE.BREAK">
          <div class="choice-score-warp tips">每答对一处得1分，填涂超出答案个数不得分。</div>
        </el-tab-pane>
        <el-tab-pane label="自定义规则" :name="IRULE_TYPE.CUSTOM">
          <div class="switch-ques-warp">
            <el-pagination hide-on-single-page background layout="prev, pager, next" :page-size="1" :total="quesCount"
              @change="changeQues" />
          </div>
          <div class="choice-score-warp">
            <template v-if="activeQues.rules?.length > 0">
              <el-row class="choice-score-head">
                <el-col :span="3">序号</el-col>
                <el-col :span="13">答案</el-col>
                <el-col :span="4">分值</el-col>
                <el-col :span="4">操作</el-col>
              </el-row>
              <el-row v-for="(rule, index) in activeQues.rules" :key="index" class="choice-score-item">
                <el-col :span="3">{{ index + 1 }}</el-col>
                <el-col class="opts-list" :span="13">
                  <ul>
                    <template v-for="num in Number(activeQues.optionCount)">
                      <li v-if="rule.ans.includes(aZ[num - 1])" class="active" @click="removeAnswer(rule, aZ[num - 1])">
                        {{ aZ[num - 1] }}
                      </li>
                      <li v-else @click="addAnswer(rule, aZ[num - 1])">
                        {{ aZ[num - 1] }}
                      </li>
                    </template>
                  </ul>
                </el-col>
                <el-col :span="4">
                  <el-input-number :max="Number(activeQues.score)" :min="0" v-model="rule.score" size="small" />
                </el-col>
                <el-col :span="4">
                  <span :title="'添加'" class="opt-hand add" @click="addRule(index)"><i
                      class="el-icon-circle-plus-outline"></i></span>
                  <span :title="'移除'" class="opt-hand minus" @click="removeRule(index)"><i
                      class="el-icon-delete"></i></span>
                </el-col>
              </el-row>
            </template>
            <div v-else class="no-data">
              <el-empty description="请先设置答案" />
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <template>
        <div class="dialog-footer" style="text-align: center">
          <!-- <el-button type="success" @click="setNewExam">新高考设置</el-button> -->
          <el-button v-if="activeName == IRULE_TYPE.CUSTOM" @click="buildScoreRules">重置</el-button>
          <el-button @click="close">取消</el-button>
          <el-button type="primary" @click="saveRules">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { deepClone } from '@/utils/index.js';
const ITYPE = {
  NEW: 1,
  RESERT: 2,
  AVER: 3,
};
export default {
  props: {
    //弹框是否显示
    isShowDialog: {
      type: Boolean,
      default: true,
      required: true,
    },
    item: {
      type: Object,
      default: {},
    },
  },
  watch: {
    activeIndex: {
      handler(newVal, oldVal) {
        this.activeQues = this.quesList[this.activeIndex];
      },
      deep: true,
    },
  },
  data() {
    return {
      ITYPE: {
        NEW: 1,
        RESERT: 2,
        AVER: 3,
      },
      IRULE_TYPE: {
        //标准
        STANDARD: 0,
        //新高考
        NEW: 1,
        //断句
        BREAK: 3,
        //自定义
        CUSTOM: 2
      },
      activeName:this.item.ruleType,
      activeIndex: -1,
      activeQues: {},
      quesList: [],
      quesCount: 0,
      aZ: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z']
    };
  },

  mounted() {
    this.quesList = deepClone(
      Array.isArray(this.item) ? this.item : this.item.data ? this.item.data : [this.item]
    );
    this.quesCount = this.quesList.length;
    this.buildScoreRules(ITYPE.NEW);
    this.activeIndex = 0;
  },
  methods: {
    /**
     * 数组排列组合算法
     */
    getAllCombinations(arr) {
      if (!Array.isArray(arr) || arr.length === 0) {
        return [];
      }
      const result = [];
      function combine(combination, start) {
        result.push(combination);

        for (let i = start; i < arr.length; i++) {
          combine([...combination, arr[i]], i + 1);
        }
      }
      combine([], 0);
      return result.sort((a, b) => {
        return b.length - a.length;
      });
    },
    /**
     * 构建分值打分规则
     */
    buildScoreRules(type) {
      this.quesList.forEach(item => {
        if (!item.answer) return;
        if (!item.rules?.length || type != ITYPE.NEW) {
          item.rules = [];
          let answer = this.converAnswer(item.answer);
          const ansList = this.getAllCombinations(answer);
          ansList.forEach(ans => {
            if (ans.length) {
              if (answer.length == ans.length) {
                item.rules.push({ ans, score: item.score });
              } else {
                if (type == ITYPE.AVER) {
                  let optScore = Math.round(item.score / answer.length);
                  item.rules.push({ ans, score: optScore * ans.length });
                } else {
                  item.rules.push({ ans, score: item.halfScore || item.score / 2 });
                }
              }
            }
          });
        }
      });
    },
    /**
     * 添加答案选项
     */
    addAnswer(rule, answer) {
      rule.ans.push(answer);
      rule.ans.sort();
    },

    /**
     * 移除答案选项
     */
    removeAnswer(rule, answer) {
      rule.ans = rule.ans.filter(ans => {
        return ans != answer;
      });
    },

    /**
     * 添加打分规则
     */
    addRule(index) {
      this.activeQues.rules.splice(index + 1, 0, { ans: [], score: '' });
    },

    /**
     * 移除打分规则
     */
    removeRule(index) {
      if (this.activeQues.rules.length > 1) {
        this.activeQues.rules.splice(index, 1);
      }
    },

    /**
     * 切换题目
     */
    changeQues(index) {
      this.activeIndex = index - 1;
    },

    converAnswer(answer) {
      if (typeof answer != 'string') return answer;
      if (answer.includes(',')) {
        return answer.split(',');
      } else {
        return answer.split('');
      }
    },

    /**
     * 设置新高考规则
     */
    setNewExam() {
      this.buildScoreRules(ITYPE.AVER);
    },
    saveRules() {
      this.quesList.forEach(item => {
        item.ruleType = Number(this.activeName);
        if(this.activeName != this.IRULE_TYPE.CUSTOM){
          item.rules = [];
        }
      });
      this.$emit('update-score-rules', this.quesList);
      this.close();
    },

    close() {
      this.$emit('close-dialog');
    },
  },
};
</script>

<style lang="scss">
.c30-choice-scores-modal {
  .switch-ques-warp {
    .el-pagination {
      justify-content: center;
      margin: 3px;
    }
  }

  .choice-score-warp {
    height: 320px;
    overflow: auto;
    &.tips{
      padding: 15px 0;
    }

    .choice-score-head {
      padding: 3px 0;
      margin: 3px;
      background: aliceblue;
    }

    .choice-score-item {
      line-height: 30px;
      margin-bottom: 5px;

      .opts-list {
        ul {
          width: fit-content;
          margin: auto;
          list-style: none;
          li {
            float: left;
            width: 30px;
            height: 30px;
            background-color: #fff;
            border: solid 1px #d5dae1;
            border-radius: 4px;
            text-align: center;
            line-height: 30px;
            font-size: 14px;
            margin: 0 10px 5px 0;
            cursor: pointer;

            &.active {
              background-color: rgb(64, 158, 255);
              color: #fff;
            }
          }
        }
      }

      .opt-hand {
        height: 30px;
        line-height: 30px;
        margin: 2px;
        padding: 5px;
        font-size: 18px;
        cursor: pointer;

        :hover {
          color: rgb(64, 158, 255);
        }
      }

      .el-input__inner {
        color: #000 !important;
        height: 30px !important;
        line-height: 30px !important;
      }
    }

    .el-col {
      text-align: center;
    }
  }

  .el-dialog__body {
    border-bottom: 1px solid #d8d8d8 !important;
  }
}
</style>
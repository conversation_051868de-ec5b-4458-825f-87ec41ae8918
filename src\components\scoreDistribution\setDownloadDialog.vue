<template>
  <div>
    <el-dialog custom-class="download-dialog" title="导出设置" :visible="dialogFormVisible" width="50%"
      :before-close="handleClose" :close-on-click-modal="false">
      <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
      <el-checkbox-group v-model="checkedReports" @change="handleCheckedReportsChange">
        <el-checkbox v-for="report in reports" :key="report.value" :label="report.value">{{
          report.name
        }}</el-checkbox>
      </el-checkbox-group>
      <div slot="footer" class="dialog-footer">
        <el-button @click="$emit('close-dialog')">取 消</el-button>
        <el-button type="primary" :loading="isComfirming" @click="sureDownload"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
  
<script>
export default {
  name: "setDownloadDialod",
  data() {
    return {
      isComfirming:false,
      dialogFormVisible: true,
      checkAll: false,
      isIndeterminate: false,
      checkedReports: [],
      reports: [{
        value: 1,
        name: "历次考试跟踪"
      }, {
        value: 2,
        name: "难度分析"
      }, {
        value: 3,
        name: "学科均分分析 "
      }, {
        value: 4,
        name: "上线分析 "
      }, {
        value: 5,
        name: "平均分分析"
      }, {
        value: 6,
        name: "学生成绩追踪"
      }, {
        value: 7,
        name: "学科详情"
      }]
    };
  },
  mounted() {

  },
  methods: {
    sureDownload(){
      this.checkedReports=this.checkedReports.join(",")
      this.$emit("get-download", this.checkedReports);
      this.$emit("close-dialog");
      this.dialogFormVisible = false;
    },
    //全选
    handleCheckAllChange(val) {
      this.checkedReports= val ? this.reports.map(report => report.value) : []
      this.isIndeterminate = false
    },
    handleCheckedReportsChange(value) {
      const checkedCount = value.length
      this.checkAll  = checkedCount === this.reports.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.reports.length
    },
    /**
     * @name:关闭弹窗
     */
    handleClose() {
      this.$emit("close-dialog");
      this.dialogFormVisible = false;
    },
    /**
     * @name：初始化年级和学年
     */

  },
};
</script>
  
<style lang="scss" >
.download-dialog{
  .el-checkbox{
    margin:20px 20px 0 0;
  }
  .el-checkbox:nth-child(1){
    margin-top: 0;
  }
}
</style>
  
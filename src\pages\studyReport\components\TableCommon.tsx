/*
 * @Description:
 * @Author: 小圆
 * @Date: 2024-03-26 09:06:52
 * @LastEditors: 小圆
 */
import BaseTable from '@/components/Base/table/BaseTable';
import NoData from '@/components/noData.vue';
import FilterModule from '@/pages/studyReport/plugins/FilterModule';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { exportExcel } from '@/plugins/frontend-excelify';

const defaultTableAttr = {
  headerCellStyle: {
    fontSize: '14px',
    color: '#3F4A54',
    backgroundColor: '#f5f7fa',
  },
  maxHeight: 650,
  align: 'center',
  stripe: true,
  border: true,
};

export function getDefaultTableAttr() {
  return JSON.parse(JSON.stringify(defaultTableAttr));
}

@Component({
  components: { BaseTable, NoData },
})
export default class TableCommon extends Vue {
  // 接口模块
  // @Prop({ default: 'teacher' }) apiModule: 'teacher' | 'student' | 'custom';
  // 接口名称
  @Prop({ default: '' }) apiName: string;

  // 表格数据
  tableData = [];
  // 表格列
  tableColumns = [] as IColumn[];
  // 加载状态
  tableLoading: boolean = false;
  // 表格左侧固定列
  tableLeftFixed: any[] = [];
  // 表格右侧固定列
  tableRightFixed: any[] = [];
  // 表格请求额外参数
  tableParams: any = {};

  @Watch('$route.path')
  onPathChange(path) {
    // this.getTableData();
  }

  mounted() {
    FilterModule.off('changeFilter', this.getTableData);
    FilterModule.off('exportTable', this.onExportTable);
    FilterModule.on('changeFilter', this.getTableData);
    FilterModule.on('exportTable', this.onExportTable);
    this.getTableData();
  }

  beforeDestroy() {
    FilterModule.off('changeFilter', this.getTableData);
    FilterModule.off('exportTable', this.onExportTable);
  }

  getTableAttr(): any {
    return getDefaultTableAttr();
  }

  // 获取数据
  async getTableData(): Promise<any> {
    this.tableColumns = [];
    this.tableData = [];
    this.tableLoading = true;
    await this.$nextTick();
    try {
      const res = await FilterModule.getReportTableData(
        this.apiName || this.$route.name,
        this.tableParams
      );
      if (res.code != 1) {
        return this.$message.error(res.msg);
      }
      const data = res.data;
      data.table = this.setColumnProp(data.table || []);
      this.tableColumns = (data.table as IColumn[]) || [];
      this.tableData = data.result || [];
      this.callbackGetTableData();
    } catch (error) {
      console.error(error);
      this.tableColumns = [];
      this.tableData = [];
    } finally {
      this.tableLoading = false;
    }
  }

  callbackGetTableData() {
    return;
  }

  /**
   * 设置列属性
   * @param columns 列数组
   * @returns 返回处理后的列数组
   */
  setColumnProp(columns: IColumn[]) {
    const recursion = (columns: IColumn[]) => {
      columns.forEach(item => {
        if (item.children && item.children.length > 0) {
          item.title = item.title && (item as any).title !== 0 ? item.title : '--';
          recursion(item.children);
        } else {
          item.title = item.title && (item as any).title !== 0 ? item.title : '--';
          item.minWidth = item.minWidth ? item.minWidth : item.prop === 'stuNo' ? 130 : 100;
          item.resizable = false;
          item.fixed =
            this.tableLeftFixed.includes(item.title) || this.tableLeftFixed.includes(item.prop)
              ? 'left'
              : this.tableRightFixed.includes(item.title) ||
                this.tableRightFixed.includes(item.prop)
              ? 'right'
              : false;
        }
      });
    };
    recursion(columns);
    return columns;
  }

  formatRowspanAndColspan(tableList, keyName) {
    const keyNameList = [];
    tableList.forEach(t => {
      keyNameList.push(t[keyName]);
    });

    let prev; // 上一个键名的索引
    let contin = 0; // 连续相同键名个数
    const computedList = []; // 计算后的键名列表
    for (let i = 0; i < keyNameList.length; i++) {
      if (computedList.length === 0) {
        computedList.push({ key: keyNameList[i], val: 1 });
      } else {
        if (keyNameList[prev] === keyNameList[i]) {
          contin++;
          computedList.push({ key: keyNameList[i], val: 0 });
        } else {
          if (contin > 0) {
            const index = computedList.length - 1 - contin;
            const key = computedList[index].key;
            const val = computedList[index].val;
            const obj = { key: key, val: val + contin };
            computedList.splice(index, 1, obj);
          }
          computedList.push({ key: keyNameList[i], val: 1 });
          contin = 0;
        }
      }
      prev = i;
    }
    if (contin > 0) {
      const index = computedList.length - 1 - contin;
      const key = computedList[index].key;
      const val = computedList[index].val;
      const obj = { key: key, val: val + contin };
      computedList.splice(index, 1, obj);
    }

    const finalList = [];
    computedList.forEach(t => {
      finalList.push(t.val);
    });

    return finalList;
  }

  // 导出excel表格
  onExportTable() {
    if (!this.tableColumns.length) return this.$message.warning('无法导出');
    if (!this.tableData.length) return this.$message.warning('无法导出');

    let excelName =
      this.$route.meta.title + '-' + FilterModule.filterInfo.examInfo.examName + '.xlsx';
    let excelColumns = this.tableColumns;
    let excelData = this.tableData;
    let options;

    if (Array.isArray(excelData[0])) {
      options = [];

      for (let i = 0; i < excelData.length; i++) {
        const columns = this.mapColumns(
          excelColumns[i] && (excelColumns[i] as IColumn[]).length
            ? (excelColumns[i] as IColumn[])
            : [excelColumns[i]]
        );
        const title = columns[0].title;
        const data = this.mapData(excelData[i]);
        options.push({
          name: title || `Sheel${i + 1}`,
          columns: columns,
          data: data,
        });
      }
    } else {
      const columns = this.mapColumns(excelColumns);
      const data = this.mapData(excelData);
      options = {
        name: 'Sheel1',
        columns: columns,
        data: data,
      };
    }

    exportExcel(excelName, options);
  }

  // 递归规范导出数据
  mapColumns(columns: IColumn[]) {
    return columns.map(column => {
      const { title, prop, children } = column;
      const columnItem: any = {
        title: title || '',
      };
      if (prop) columnItem.field = prop;
      if (children && children.length) {
        columnItem.children = this.mapColumns(children);
      }
      return columnItem;
    });
  }

  mapData(datas) {
    return datas.map(data => {
      const _data = this.$deepClone(data);
      for (const key in _data) {
        _data[key] =
          _data[key] || _data[key] === 0 || _data[key] === '0'
            ? isNaN(Number(_data[key]))
              ? _data[key]
              : Number(_data[key])
            : '--';

        if (key == 'stuNo') {
          _data[key] = String(_data[key]);
        }
      }
      return _data;
    });
  }

  renderTable(h) {
    const BaseTable = (
      <base-table
        data={this.tableData}
        column={this.tableColumns}
        attrs={this.getTableAttr()}
        v-drag-table
      ></base-table>
    );
    const NoData = <no-data text="暂无数据" style="height: 500px"></no-data>;
    return (
      <div>
        <div v-loading={this.tableLoading}>{this.tableData.length ? BaseTable : NoData}</div>
      </div>
    );
  }
}

/* <template>
  <div>
    <div v-loading="tableLoading">
      <base-table
      v-if="tableData.length"
      :data="tableData"
      :column="tableColumns"
      v-bind="getTableAttr()"
      v-drag-table
      ></base-table>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>
  </div>
</template> */

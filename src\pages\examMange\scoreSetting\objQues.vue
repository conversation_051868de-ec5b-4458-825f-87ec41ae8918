<!--
 * @Descripttion: 选择题（单选、多选）组件
 * @Author: 小圆
 * @Date: 2023-12-23 09:24:57
 * @LastEditors: 小圆
-->
<template>
  <div>
    <el-popover
      placement="top"
      width="180"
      trigger="hover"
      :content="getAnswerContent(item)"
      popper-class="is-dark is-text"
    >
      <div slot="reference" class="ques-container">
        <el-tooltip effect="dark" :content="item.sortTitle" placement="top-start">
          <div class="ques-no">
            {{ item.sortTitle }}
          </div>
        </el-tooltip>

        <!-- 选择、多选题 -->
        <div class="ques-btn-group" v-if="item.type == 1 || item.type == 8">
          <el-button
            v-for="idx of item.optionCount || 4"
            :type="checkBtnType(item, idx + 64)"
            :key="idx"
            size="medium"
            @click="changeAnswer(String.fromCharCode(idx + 64), item.type)"
            >{{ String.fromCharCode(idx + 64) }}</el-button
          >
        </div>

        <div class="ques-btn-group" v-else-if="item.type == 7">
          <el-button
            icon="el-icon-check"
            :type="item.fullScore == item.score ? 'primary' : ''"
            @click="changeAnswer(true, item.type)"
          ></el-button>
          <el-button
            icon="el-icon-close"
            :type="item.fullScore != item.score ? 'danger' : ''"
            @click="changeAnswer(false, item.type)"
          ></el-button>
        </div>

        <!-- 判断题 -->
        <div v-else-if="item.type == 2" class="ques-btn-group">
          <el-button
            size="medium"
            :type="checkBtnType(item, 65)"
            @click="changeAnswer(String.fromCharCode(65), item.type)"
          >
            {{ item.judgeType == 1 ? '√' : item.judgeType == 3 ? 'A' : 'T' }}
          </el-button>
          <el-button
            size="medium"
            :type="checkBtnType(item, 66)"
            @click="changeAnswer(String.fromCharCode(66), item.type)"
          >
            {{ item.judgeType == 1 ? 'x' : item.judgeType == 3 ? 'B' : 'F' }}
          </el-button>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script>
export default {
  props: ['item', 'index'],
  data() {
    return {};
  },
  mounted() {},

  methods: {
    /**
     * @name: 改变答案
     */
    changeAnswer(answer, typeId) {
      if (typeId == 7) {
        // 智能批改
        if (answer) {
          this.item.score = this.item.fullScore;
        } else {
          this.item.score = 0;
        }
      } else if (typeId == 8 || typeId == 2) {
        //单选、判断
        if (this.item.answer === answer) {
          this.item.answer = '';
        } else {
          this.item.answer = answer;
        }
      } else if (typeId == 1) {
        //多选
        let answerArr = [];
        if (this.item.answer || this.item.answer == '') {
          if (this.item.answer.length > 1 && this.item.answer.indexOf(',') == -1) {
            this.item.answer = this.item.answer.split('').join(',');
          }
          answerArr = this.item.answer.split(',');
          answerArr = answerArr.filter(item => {
            return item != '';
          });
          let inde = answerArr.findIndex(obj => obj == answer);
          //存在则移除
          if (inde > -1) {
            answerArr.splice(inde, 1);
          } else {
            //不存在则添加
            answerArr.push(answer);
          }
        }
        this.item.answer = answerArr.sort().join(',');
      }
    },

    // 返回按钮状态
    checkBtnType(item, code, typeId) {
      if (!item.answer) return 'default';
      let currentAnswer = String.fromCharCode(code);
      let rightAnswer = item.rightAnswer;
      if (item.answer.includes(currentAnswer)) {
        if (rightAnswer.includes(currentAnswer)) {
          return 'primary';
        } else {
          return 'danger';
        }
      }
      return 'default';
    },

    // 获取答案文本
    getAnswerContent(item) {
      if (item.type == 8 || item.type == 7 ||  item.type == 1) {
        return '答案：' + item.rightAnswer;
      }

      const rightAnswer = item.rightAnswer;
      let text = rightAnswer;

      if (rightAnswer == 'A') {
        text = item.judgeType == 1 ? '√' : item.judgeType == 3 ? 'A' : 'T';
      }
      if (rightAnswer == 'B') {
        text = item.judgeType == 1 ? 'x' : item.judgeType == 3 ? 'B' : 'F';
      }
      return '答案：' + text;
    },
  },
};
</script>

<style lang="scss" scoped>
.ques-container {
  display: flex;
  padding: 5px;
}
.ques-no {
  $size: 35px;

  display: inline-block;
  width: $size;
  min-width: $size;
  height: $size;
  border: 1px solid #2574ff;
  background-color: #2574ff;
  border-radius: 50%;
  color: #2574ff;
  color: #FFF;
  font-size: 15px;
  font-weight: 700;
  text-align: center;
  line-height: $size;
  margin-right: 10px;
  vertical-align: middle;
}
.ques-btn-group {
  .el-button {
    margin-top: 5px;
  }
}
</style>

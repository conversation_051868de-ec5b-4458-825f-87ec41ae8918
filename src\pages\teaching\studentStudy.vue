<template v-on="$listeners">
  <div>
    <div class="stustady-header">
      <!-- <div class="header__select">
        <span class="select__label">时间：</span>
        <el-select
          v-model="timeValue"
          style="width: 100px; margin-right: 25px"
          @change="changeTimeValue()"
        >
          <el-option
            v-for="item in timeType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-select
          v-model="yearTimeSlot"
          v-if="timeValue === 3"
          style="width: 250px; margin-right: 10px"
        >
          <el-option
            v-for="item in yearList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-date-picker
          v-if="timeValue === 2"
          popper-class="datePicker__time"
          style="margin-right: 10px; width: 250px"
          v-model="timeSlot"
          :clearable="false"
          type="monthrange"
          align="right"
          range-separator="--"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          @change="beforeFindPersonalBookListMonth"
          :picker-options="pickerOptions"
        >
        </el-date-picker>
        <el-date-picker
          v-if="timeValue === 1"
          popper-class="datePicker__time"
          style="margin-right: 10px; width: 250px"
          v-model="timeSlot"
          :clearable="false"
          type="daterange"
          align="right"
          unlink-panels
          format="yyyy-MM-dd"
          range-separator=" -- "
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptionsDay"
        >
        </el-date-picker>
      </div> -->
      <div class="header__select">
        <span class="select__label">年级：</span>
        <el-select
          v-model="gradeValue"
          style="width: 120px"
          class="source-select"
          placeholder="请选择"
          @change="changeGrade"
        >
          <el-option
            v-for="item in grdList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </div>
      <div class="header__serarch clearfix display_flex">
        <el-autocomplete
          class="search__text"
          v-model="searchStudent"
          :fetch-suggestions="querySearch"
          placeholder="请输入学生姓名搜索"
          @keyup.enter.native="getStuStatData"
          @select="handleSelect"
          :trigger-on-focus="false"
        ></el-autocomplete>
        <div
          class="search__icon el-icon-search display_flex align-items_center justify-content_flex-center"
          @click="getStuStatData"
        ></div>
      </div>
    </div>
    <div
      class="stuPage-right flex_1 clearfix"
      :style="{ minHeight: listHeight + 'px' }"
    >
      <!-- 分数分布 -->
      <div class="title">分数分布</div>
      <el-table
        :data="scoreData"
        class="score-table"
        ref="tableRef"
        v-if="scoreData.length"
        @mousedown.native="mouseDownHandler"
        @mouseup.native="mouseUpHandler"
        @mousemove.native="mouseMoveHandler"
      >
        <el-table-column prop="name" label="" fixed> </el-table-column>
        <el-table-column
          :label="item"
          v-for="(item, index) in scoreData[0].subjectList"
          :key="item"
          align="center"
          min-width="150"
        >
          <el-table-column label="分数" align="center">
            <template slot-scope="scope">
              {{ scope.row.scoreList[index] }}
            </template>
          </el-table-column>
          <el-table-column label="班/年级" align="center">
            <template slot-scope="scope">
              {{ scope.row.clzRankList[index] }}
              <span>/</span>
              {{ scope.row.grdRankList[index] }}
            </template>
          </el-table-column>
          <!-- <el-table-column label="年排" align="center">
            <template slot-scope="scope">
              {{ scope.row.grdRankList[index] }}
            </template>
          </el-table-column> -->
        </el-table-column>
      </el-table>
      <div class="nodata flex_1" v-else>
        <img :src="noResImg" alt="" />
        <p class="text-center">暂无数据</p>
      </div>
      <!--分页器-->
      <el-pagination
        background
        style="margin: 20px auto"
        :hide-on-single-page="scoreData.length <= 20"
        class="text-center"
        layout="total, prev, pager, next"
        @current-change="handleCurrentChange"
        :current-page.sync="pagination.page"
        :page-size="pagination.limit"
        :total="pagination.total_rows"
      >
      </el-pagination>
      <!--成绩趋势图-->
      <div class="title">成绩趋势图</div>
      <div class="detail-box" v-if="scoreData.length">
        <div class="indicator display_flex align-items_flex-start">
          指 标：
          <ul class="indicator-ul list-none">
            <li
              v-for="(item, index) in indicatorList"
              :key="index"
              @click="changeIndicator(index)"
              :class="index === activeIndicatorIndex ? 'active' : ''"
            >
              {{ item }}
            </li>
          </ul>
        </div>
      </div>
      <div class="nodata flex_1" v-else>
        <img :src="noResImg" alt="" />
        <p class="text-center">暂无数据</p>
      </div>
      <div
        id="gradeChart"
        :style="{ width: '100%', height: '400px' }"
        v-show="scoreData.length"
      ></div>
    </div>
  </div>
</template>

<script>
import { getStuStat, getStuList } from "@/service/pexam";
export default {
  name: "score",
  data() {
    return {
      listHeight: 200,
      scoreData: [],
      noResImg: require("../../assets/no-res.png"),
      searchStudent: "",
      gradeValue: "",
      grdList: [],
      //时间筛选类型
      timeType: [
        { value: 1, label: "按日" },
        { value: 2, label: "按月" },
        { value: 3, label: "学年" },
      ],
      timeValue: 2,
      //学年
      yearList: [],
      // 选择的具体时间数值
      defTimeSlot: [],
      defMonthSlot: [],
      timeSlot: [],
      yearTimeSlot: "",
      pickerMinDate: "", //第一次选中的时间
      pickerOptions: {
        //选择当前日期之前的时间
        onPick: (obj) => {
          this.pickerMinDate = new Date(obj.minDate).getTime();
        },
        //选择时间范围为一年
        disabledDate: (time) => {
          if (this.pickerMinDate) {
            const day1 = 364 * 24 * 3600 * 1000;
            let maxTime = this.pickerMinDate + day1;
            let minTime = this.pickerMinDate - day1;
            return (
              time.getTime() > Date.now() ||
              time.getTime() > maxTime ||
              time.getTime() < minTime
            );
          }
        },
      },
      pickerOptionsDay: {
        //选择当前日期之前的时间
        onPick: (obj) => {
          this.pickerMinDate = new Date(obj.minDate).getTime();
        },
        //可选择的时间范围为一年
        disabledDate: (time) => {
          if (this.pickerMinDate) {
            const day1 = 365 * 24 * 3600 * 1000;
            let maxTime = this.pickerMinDate + day1;
            let minTime = this.pickerMinDate - day1;
            return (
              time.getTime() > Date.now() ||
              time.getTime() > maxTime ||
              time.getTime() < minTime
            );
          }
        },
      },
      //查找学生id
      selectStuId: "",
      //查找学生name
      selectStuName: "",
      // 查找学生examId
      selectExamId: "",
      indicatorList: ["班级名次", "校级名次"],
      activeIndicatorIndex: 0,
      // charts实例
      gradeChart: null,
      // 分页
      pagination: {
        page: 1,
        limit: 20,
        total_rows: 0,
      },
    };
  },
  watch: {
    yearTimeSlot: function (v) {
      this.timeSlot = v.split("|");
    },
    searchStudent(newVal, oldVal) {
      if (!newVal) {
        this.selectStuId = "";
        this.selectStuName = "";
        this.selectExamId = "";
      }
    },
  },
  computed: {
    // 对选择后的时间格式化
    timeSlotFormat() {
      return [
        this.timeSlot[0] ? this.$formatDate(new Date(this.timeSlot[0])) : "",
        this.timeSlot[0] ? this.$formatDate(new Date(this.timeSlot[1])) : "",
      ];
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      let $this = this;
      let date = new Date(),
        y = date.getFullYear(),
        M = date.getMonth(),
        d = date.getDate();
      let yearList = [];
      for (let i = 0; i < 5; i++) {
        let y1 = y - i - 1;
        let y2 = y - i;
        if (Number(`${M + 1}${d < 10 ? `0${d}` : d}`) >= 810) {
          y1 = y - i;
          y2 = y - i + 1;
        }
        let max = `${y2}-08-09`;
        if (i == 0) {
          max = `${y}-${M < 9 ? `0${M + 1}` : M + 1}-${d < 10 ? `0${d}` : d}`;
        }
        yearList.push({
          id: i,
          value: `${y1}-08-10|${max}`,
          label: `${y1}-${y2}学年`,
        });
      }
      $this.yearList = yearList;
      $this.timeValue = 2;
      $this.defTimeSlot = [new Date(y, M), new Date(y, M, d)];
      $this.defMonthSlot = [new Date(y, M), new Date(y, M, d)];
      $this.timeSlot = $this.defTimeSlot;
      $this.grdList = $this.$sessionSave.get("teachingpage_grdList");
      $this.gradeValue = $this.grdList[0].id;
      $this.scoreData = [];
      $this.originStuList = [];
      $this.searchStudent = "";
    },
    //切换时间筛选类型，按日默认最近一周，按月默认当前月
    changeTimeValue() {
      let date = new Date(),
        y = date.getFullYear(),
        M = date.getMonth(),
        d = date.getDate();
      if (this.timeValue === 1) {
        this.timeSlot = [new Date(y, M, d - 7), new Date(y, M, d)];
      } else if (this.timeValue === 2) {
        this.timeSlot = this.defMonthSlot;
      } else {
        this.yearTimeSlot = this.yearList[0].value;
        this.timeSlot = this.yearTimeSlot.split("|");
      }
    },
    // 选择月份筛选后
    beforeFindPersonalBookListMonth(val) {
      let now = new Date(),
        y = now.getFullYear(),
        M = now.getMonth(),
        d = now.getDate();

      let start = val[0],
        y0 = start.getFullYear(),
        M0 = start.getMonth();
      let end = val[1],
        y1 = end.getFullYear(),
        M1 = end.getMonth();

      if (
        Number(`${y}${M < 10 ? `0${M}` : M}`) ==
        Number(`${y1}${M1 < 10 ? `0${M1}` : M1}`)
      ) {
        this.timeSlot = [`${y0}-${M0 + 1}-01`, `${y1}-${M + 1}-${d}`];
      } else {
        let e2 = new Date(y1, M1 + 1, 0),
          y2 = e2.getFullYear(),
          M2 = e2.getMonth(),
          d2 = e2.getDate();

        this.timeSlot = [`${y0}-${M0 + 1}-01`, `${y2}-${M2 + 1}-${d2}`];
      }
    },
    /**
     * @name：切换年级
     */
    changeGrade() {
      this.searchStudent = "";
      this.originStuList = [];
    },
    /**
     * @name:获取班级学生
     */
    // getStuList() {
    //   getStuList({
    //     studentName: this.searchStudent,
    //     schoolId: this.$sessionSave.get("schoolInfo").id,
    //     gradeId: "10",
    //   })
    //     .then((res) => {
    //       this.originStuList = res.data;
    //       this.originStuList.forEach((item) => {
    //         this.$set(item, "value", item.stuName);
    //       });
    //     })
    //     .catch((err) => {
    //       this.originStuList = [];
    //     });
    // },
    /**
     * @name:选择学生
     */
    handleSelect(item) {
      this.selectStuId = item.stuId;
      this.selectStuName = item.stuName;
      this.selectExamId = item.examId;
      this.getStuStatData();
    },
    /**
     * @name：显示学生列表
     */
    querySearch(queryString, cb) {
      getStuList({
        studentName: this.searchStudent,
        schoolId: this.$sessionSave.get("schoolInfo").id,
        gradeId: this.gradeValue,
      }).then((res) => {
        this.originStuList = res.data;
        this.originStuList.forEach((item) => {
          this.$set(
            item,
            "value",
            item.stuName + "(" + item.clzName + "-" + item.stuNo + ")"
          );
        });
        var restaurants = this.originStuList;
        var results = queryString
          ? restaurants.filter(this.createFilter(queryString))
          : restaurants;
        // 调用 callback 返回建议列表的数据
        cb(results);
      });
    },
    /**
     * @name:匹配关键字
     */
    createFilter(queryString) {
      return (restaurant) => {
        return (
          restaurant.stuName
            .toLowerCase()
            .indexOf(queryString.toLowerCase()) === 0
        );
      };
    },

    /**
     * @name:搜索学生
     */
    searchStuReport() {},
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getStuStatData();
    },

    /**
     * @name:获取学生历次考试数据
     */
    getStuStatData() {
      getStuStat({
        studentId: this.selectStuId,
        studentName: this.selectStuName,
        examId: this.selectExamId,
        schoolId: this.$sessionSave.get("schoolInfo").id,
        page: this.pagination.page,
        size: this.pagination.limit,
      })
        .then((res) => {
          this.scoreData = res.data.rows;
          this.pagination.total_rows = res.data.total_rows;
          this.drawImg();
        })
        .catch((err) => {
          this.scoreData = [];
        });
    },
    // 按下鼠标记录鼠标位置
    mouseDownHandler(e) {
      this.mouseOffset = e.clientX;
      this.mouseFlag = true;
    },
    mouseUpHandler(e) {
      this.mouseFlag = false;
    },
    mouseMoveHandler(e) {
      // 这里面需要注意，通过ref需要那个那个包含table元素的父元素
      let divData = this.$refs.tableRef.bodyWrapper;
      if (this.mouseFlag) {
        // 设置水平方向的元素的位置
        divData.scrollLeft -=
          -this.mouseOffset + (this.mouseOffset = e.clientX);
      }
    },
    // 图表预处理
    preDrawChart() {
      let _this = this;
      window.onresize = () => {
        return (() => {
          if (_this.gradeChart) {
            _this.gradeChart.resize();
          }
        })();
      };
    },
    changeIndicator(index) {
      this.activeIndicatorIndex = index;
      this.drawImg();
    },
    // 绘制图表
    async drawImg() {
      let xAxisTitle = this.scoreData.map((item) => {
        return item.name;
      });
      let seriesData = [];
      // 班排名或校排名
      let subject = this.scoreData[0].subjectList;
      for (let i = 0; i < subject.length; i++) {
        let arr = [];
        this.scoreData.forEach((item) => {
          arr.push(
            this.activeIndicatorIndex == 0
              ? item.clzRankList[i]
              : item.grdRankList[i]
          );
        });
        seriesData.push({
          data: arr,
          name: subject[i],
          type: "line",
        });
      }
      await this.$nextTick();
      if (
        this.gradeChart != null &&
        this.gradeChart != "" &&
        this.gradeChart != undefined
      ) {
        this.gradeChart.dispose();
        this.gradeChart = null;
      }
      this.gradeChart = this.$echarts.init(
        document.getElementById("gradeChart")
      );
      this.gradeChart.setOption({
        tooltip: {
          show: true,
          trigger: "item",
          confine: true,
        },
        legend: {
          top: 10,
          right: 50,
        },
        grid: {
          left: "3%",
          right: "2%",
          top: "10%",
          bottom: "12%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: xAxisTitle,
          triggerEvent: true,
          axisLabel: {
            interval: 0,
            rotate: 30,
            formatter: function (value) {
              if (value.length > 15) {
                value = value.substring(0, 15) + "..";
              }
              return value;
            },
          },
        },
        yAxis: {
          type: "value",
          inverse: true,
        },
        dataZoom: [
          {
            type: "slider",
            startValue: 0,
            endValue: 10,
          },
        ],
        series: seriesData,
      });
      this.extensionOne(this.gradeChart);
    },
    /**
     * @name:x轴悬浮展示
     */
    extensionOne(myChart) {
      var id = document.getElementById("extensionOne");
      //判断是否创建过div框,如果没有创建过，则创建。（创建时，默认隐藏）
      if (!id) {
        var div = "<div id = 'extensionOne' style='display:none'></div>";
        $("html").append(div);
      }
      var arrow_left = "20px";
      //鼠标悬浮事件
      myChart.on("mouseover", function (params) {
        if (params.componentType != "xAxis") {
          return;
        }

        //设置div框样式，并为其填充值。
        $("#extensionOne")
          .css({
            position: "absolute",
            color: "#90979c",
            // "border": "solid 0px white",
            "font-family": "Arial",
            "font-size": "14px",
            padding: "5px",
            "font-weight": "bold",
            display: "inline",
          })
          .text(params.value);
        var xx_text = params.event.offsetX - 35;
        arrow_left = xx_text;

        $("#gradeChart").mousemove(function (event) {
          // console.log("X轴坐标：" + event.pageX + " Y轴坐标：" + event.pageY);
          var xx = event.pageX - 30;
          var yy = event.pageY + 10;
          $("#extensionOne").css("top", yy).css("left", xx);
        });
      });

      myChart.on("mouseout", function (params) {
        $("#extensionOne").css("display", "none");
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.stustady-header {
  position: relative;
  padding: 16px 40px 16px 28px;
  width: 100%;
  background: #fff;
  border-radius: 6px;
  margin-bottom: 16px;
  display: flex;

  ::v-deep {
    .el-button {
      font-size: 16px;
    }

    .el-input__inner {
      font-size: 16px;
    }
  }

  .header__select {
    display: inline-block;
    margin-right: 20px;
    color: #2e4b81;
    font-size: 16px;
  }
}
.stuPage-right {
  width: 100%;
  margin-bottom: 20px;
  padding: 0px 35px 20px 35px;

  background: #fff;
  .title {
    position: relative;
    padding-left: 38px;
    font-size: 18px;
    font-weight: bold;
    color: #3f4a54;
    margin-bottom: 18px;
    margin-top: 20px;
    &:before {
      content: "";
      display: inline-block;
      width: 30px;
      height: 19px;
      background: url("../../assets/titleIcon.png") center center no-repeat;
      position: absolute;
      left: 0;
      top: 4px;
    }
  }
}
.header__serarch {
  display: flex;
  width: 240px;
  .search__icon {
    width: 48px;
    height: 40px;
    font-size: 18px;
    color: #fff;
    background: #409eff;
    border-radius: 0 3px 3px 0;
    outline: none;
    cursor: pointer;
  }
}
.detail-box {
  width: 100%;
  background: #f1f5f8;
  margin-bottom: 30px;
  padding: 8px 30px;
  font-size: 16px;
  color: #3f4a54;
  > div {
    line-height: 30px;
  }
  > div > ul {
    margin-left: 10px;
    > li {
      display: inline-block;
      margin-right: 25px;
      cursor: pointer;
      &.active {
        color: #409eff;
      }
    }
  }
  .indicator > ul > li {
    margin-right: 35px;
  }
}
.score-table {
  width: 100%;
  cursor: move;
}
</style>
<style lang="scss">
.el-autocomplete-suggestion {
  width: 240px !important;
}
</style>

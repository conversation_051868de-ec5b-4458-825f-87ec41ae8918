<template>
  <div>
    <div
      id="clzScoreChart"
      class="echart"
      ref="echart"
      style="width: 100%; height: 400px"
    ></div>
  </div>
</template>

<script>
export default {
  props: {
    //班级考试列表
    clzSore: {
      type: Array,
      default() {
        return [];
      },
    },
    // 班级id
    classId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      clzScoreChart: null,
      seriesData: [],
      xAxisTitle: [],
      targetData: [{
        name: "班级均分",
        prop: "clzAvg",
      }, {
        name: "年级均分",
        prop: "grdAvg",
      }],
    };
  },
  watch: {
    clzSore: {
      handler(val) {
        this.resetDomSize("clzScoreChart", 400);
        this.$nextTick(() => {
          this.drawImg();
        });
      },
      deep: true,
    },
  },
  beforeDestroy() {
    if (
      this.clzScoreChart != null &&
      this.clzScoreChart != "" &&
      this.clzScoreChart != undefined
    ) {
      this.clzScoreChart.dispose();
      this.clzScoreChart = null;
    }
  },
  mounted() {
    let _this = this;
    window.onresize = () => {
      return (() => {
        if (_this.clzScoreChart) {
          this.resetDomSize("clzScoreChart", 400);
          _this.clzScoreChart.resize();
        }
      })();
    };
    if (!this.clzScoreChart) {
      this.drawImg();
    }
  },
  methods: {
    resetDomSize(el, height) {
      let width = document.getElementById("clzScoreChart").clientWidth;
      Object.defineProperty(document.getElementById(el), "clientWidth", {
        get: function () {
          return width;
        },
        configurable: true,
      });
      Object.defineProperty(document.getElementById(el), "clientHeight", {
        get: function () {
          return height;
        },
        configurable: true,
      });
    },
    /**
     * @name:组装数据
     */
    handleChartData() {
      let data = this.clzSore && this.clzSore.length ? this.clzSore : [];
      if (data.length) {
        this.seriesData = [];
        let obj = {};
        //分数分布
        this.xAxisTitle = data.map((res) => {
          return res.name;
        });
        for (let [i, item] of this.targetData.entries()) {
          let arr = [];
          let prop = item.prop;
          arr = data.map((item) => item[prop]);

          // 全部班级，不显示班级均分
          if (!this.classId && prop == 'clzAvg') {
            arr = arr.map((item) => null);
          }

          obj = {
            name: item.name,
            data: arr,
            type: "line",
          };
          this.seriesData.push(obj);
        }
      }
    },
    /**
     * @name:绘制折线图
     */
    drawImg() {
      if (
        this.clzScoreChart != null &&
        this.clzScoreChart != "" &&
        this.clzScoreChart != undefined
      ) {
        this.clzScoreChart.dispose();
        this.clzScoreChart = null;
      }
      this.handleChartData();
      let _this = this;
      this.clzScoreChart = this.$echarts.init(this.$refs.echart);
      this.clzScoreChart.setOption({
        legend: {
          top: 10,
          right: 50,
        },
        tooltip: {},
        grid: {
          left: "3%",
          right: "6%",
          top: "20%",
          // bottom: '10%',
          containLabel: true,
        },
        splitLine: { show: false },
        splitArea: { show: false },
        xAxis: {
          type: "category",
          data: _this.xAxisTitle,
          triggerEvent: true,
          axisLabel: {
            interval: 0,
            rotate: 30,
            formatter: function (value) {
              if (value.length > 15) {
                value = value.substring(0, 15) + "..";
              }
              return value;
            },
          },
        },
        yAxis: {
          type: "value",
        },
        dataZoom: [
          {
            type: "slider",
            start: 0,
            end: 100,
          },
        ],
        series: _this.seriesData,
      });
      _this.extensionOne(_this.clzScoreChart);
    },
    /**
     * @name:x轴悬浮展示
     */
    extensionOne(myChart) {
      var id = document.getElementById("extensionOne");
      //判断是否创建过div框,如果没有创建过，则创建。（创建时，默认隐藏）
      if (!id) {
        var div = "<div id = 'extensionOne' style='display:none'></div>";
        $("html").append(div);
      }
      var arrow_left = "20px";
      //鼠标悬浮事件
      myChart.on("mouseover", function (params) {
        if (params.componentType != "xAxis") {
          return;
        }

        //设置div框样式，并为其填充值。
        $("#extensionOne")
          .css({
            position: "absolute",
            color: "#90979c",
            // "border": "solid 0px white",
            "font-family": "Arial",
            "font-size": "14px",
            padding: "5px",
            display: "inline",
          })
          .text(params.value);
        var xx_text = params.event.offsetX - 35;
        arrow_left = xx_text;

        $("#clzScoreChart").mousemove(function (event) {
          // console.log("X轴坐标：" + event.pageX + " Y轴坐标：" + event.pageY);
          var xx = event.pageX - 30;
          var yy = event.pageY + 10;
          $("#extensionOne").css("top", yy).css("left", xx);
        });
      });

      myChart.on("mouseout", function (params) {
        $("#extensionOne").css("display", "none");
      });
    },
  },
};
</script>

<style></style>

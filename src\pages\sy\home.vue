<template>
  <div class="workBook">
    <!-- <div class="workBook__header">
      <region-home-header class="workBook-wrapper"></region-home-header>
    </div> -->
    <div class="workBook__body">
      <div class="scrollContainer">
        <div class="workBook-wrapper clearfix" v-if="isInit">
          <keep-alive>
            <router-view
              class="router-view"
              ref="routerView"
              v-if="$route.meta.keepAlive"
              :key="$route.path"
            ></router-view>
          </keep-alive>
          <router-view
            class="router-view"
            ref="routerView"
            v-if="!$route.meta.keepAlive"
          ></router-view>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import RegionHomeHeader from './components/region-home-header.vue';
import axios from 'axios';
import UserRole from '@/utils/UserRole';

@Component({
  components: {
    RegionHomeHeader,
  },
})
export default class RegionHome extends Vue {
  isInit = false;

  async mounted() {
    await this.initSchool();
    this.isInit = true;
  }

  async initSchool() {
    let schoolList = await this.getSchoolList();
    let schoolInfo = this.$sessionSave.get('schoolInfo');
    if (!schoolInfo) {
      schoolInfo = {
        id: schoolList[0].id,
        schoolId: schoolList[0].id,
        school_name: schoolList[0].schoolName,
        schoolName: schoolList[0].schoolName,
      };
    }
    this.$sessionSave.set('schoolInfo', schoolInfo);
    await UserRole.setSchoolInfo(schoolInfo);
    this.$store.commit('saveSchoolInfo', schoolInfo);
  }

  async getSchoolList() {
    let platformId = this.$sessionSave.get('regionInfo').id;
    const path = `${
      process.env.VUE_APP_FS_URL
    }/yiqi/exam/config/${platformId}/school.json?timestamp=${new Date().getTime()}`;
    const res = await axios.get(path);
    // 获取文件成功
    if (res.status === 200) {
      return res.data.data;
    } else {
      this.$message({
        message: '获取学校数据失败',
        type: 'error',
      });
      return [];
    }
  }
}
</script>

<style scoped lang="scss">
.workBook {
  width: 100%;
  height: 100%;
  background-color: #f1f5f8;
  overflow: hidden;

  .workBook__header {
    position: relative;
    width: 100%;
    height: 60px;
    background-color: #fff;
    border-bottom: 1px solid #e4e8eb;
  }

  .workBook__body {
    width: 100%;
    height: calc(100% - 60px);
    z-index: 999;
  }
}

.scrollContainer {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  overflow-x: auto;
}

.router-view {
  margin-top: 14px;
  height: calc(100% - 14px);
}

.workBook-wrapper {
  min-width: 1300px;
  max-width: 1500px;
  width: 100%;
  height: 100%;
  margin: 0 auto;
}
</style>

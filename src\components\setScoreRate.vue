<template>
    <div>
        <el-dialog custom-class="scoreRate-dialog" :visible="dialogFormVisible" width="600px" :before-close="handleClose"
            :modal-append-to-body="false" :close-on-click-modal="false">
            <div slot="title" class="dialog-title">
                <span>得分率设置</span>
            </div>
            <div class="rate-item" >
                <span class="icon color1"></span>
                <el-input v-model="inputName[0]" disabled  size="small" style="width: 70px;margin-left: 20px;" />
                
                <span style="margin: 15px;">-</span>
                <el-input v-model="inputName[1]"  @input="inputNumber(1)" size="small" style="width: 70px;" />             
            </div>
            <div class="rate-item" >
                <span class="icon color2"></span>
                <el-input v-model="inputName[1]"  @input="inputNumber(1)" size="small" style="width: 70px;margin-left: 20px;" />
                <span style="margin: 15px;">-</span>
                <el-input v-model="inputName[2]"  @input="inputNumber(2)" size="small" style="width: 70px;" />             
            </div>
            <div class="rate-item" >
                <span class="icon color3"></span>
                <el-input v-model="inputName[2]"  @input="inputNumber(2)" size="small" style="width: 70px;margin-left: 20px;" />
                <span style="margin: 15px;">-</span>
                <el-input v-model="inputName[3]"  @input="inputNumber(3)" size="small" style="width: 70px;" />             
            </div>
            <div class="rate-item" >
                <span class="icon color4"></span>
                <el-input v-model="inputName[3]"  @input="inputNumber(3)" size="small" style="width: 70px;margin-left: 20px;" />
                <span style="margin: 15px;">-</span>
                <el-input v-model="inputName[4]"  disabled  size="small" style="width: 70px;" />             
            </div>
             <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="sureClick()"
          >确 定</el-button
        >
      </span>
        </el-dialog>
    </div>
</template>
  
<script>
export default {
    props: ['inputs'],
    data() {
        return {
            inputName: '',        
            dialogFormVisible:true,
        };
    },
    mounted() {
        this.inputName = JSON.parse(JSON.stringify(this.inputs));
    },
    methods: {
        inputNumber(index) {
            let value = this.inputName[index];
            value = value.replace(/[^0-9.]/g, ''); // 只能输入小数
            if (value < 0) value = 0; // 限制最小值为0
            if (value > 1) value = 1; // 限制最大值为1
            this.inputName[index] = value;
        },
        handleClose() {
            this.$emit("closeDialog");
            this.dialogFormVisible = false         
        },
        sureClick() {
            for(let i = 0; i < this.inputName.length; i++) {
                if(this.inputName[i] >= this.inputName[i + 1]) {
                    this.$message.warning('请正确输入得分率');
                    return;
                }
            } 
            this.$emit('submitInputs', this.inputName);
            this.$emit("closeDialog");
            this.dialogFormVisible = false
        },
    }
};
</script>
  
<style lang="scss" scoped>
.scoreRate-dialog{
    .rate-item{
        margin:20px auto;width: 100%;display: flex;align-items: center;justify-content: center;
    .icon {
        width: 30px;
        height: 30px;
        border-radius: 4px;
        display: inline-block;
        margin-right: 10px;
      }

      .color1 {
        background: #F56C6C;
        border: 1px solid #F56C6C;
      }
      .color2 {
        background: #FFBB19;
        border: 1px solid #E8A400;
      }

      .color3 {
        background: #5F9EFF;
        border: 1px solid #468FFF;
      }

      .color4 {
        background: #39CEB1;
        border: 1px solid #07C29D;
      }
}}


.dialog-footer {
    margin-right: 10px;
}

.dialog-footer button span {
    font-size: 16px;
}
</style>
<style lang="scss">
.scoreRate-dialog{
    .el-dialog__header {
        height: 45px;

        .dialog-title {
            font-weight: bold;
            line-height: 45px;
        }
    }

    .el-dialog__body {
        padding: 15px 20px  ;
    }

    .el-form-item {
        margin-bottom: 15px;
    }

    .el-dialog__footer {
        padding: 10px 20px 15px;
    }
}

.subjectCheckBox {
    line-height: 26px;

    .el-checkbox {
        margin-right: 40px;

        &.el-checkbox+.el-checkbox {
            margin-left: 0;
        }
    }
}

.dialog-title {
    line-height: 54px;
}
</style>
  
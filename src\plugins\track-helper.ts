/*
 * @Description: 埋点助手
 * @Author: qmzhang
 * @Date: 2024-11-21 20:11:02
 * @LastEditTime: 2024-11-22 16:09:46
 * @FilePath: \personal-bigdata\src\plugins\track-helper.ts
 */
import { sessionSave } from "@/utils";
import Worker from "worker-loader!./workers/track.worker";

/** 埋点声明 */
export interface ITrack {
    /** 操作的业务id(如云白板id,作业id等) */
    operationId: string
    /** 操作类型 教师 tea、学生 stu、班级 class、作业 homeWork、资源 resource、白板 whiteboard 、题目question */
    operationType: string
    /** 埋点动态数据(各应用个性数据，格式为json) */
    dyData?: Object
    /** 操作用时 */
    handleTime?: number | string
    /** 班级ID */
    classId: string
}

export class TrackHelper {
    public static Instance: TrackHelper = null;
    private trackWorker: Worker = null;

    private loginInfo: LoginInfo = null;

    constructor() {
        TrackHelper.Instance = this;
        var loginInfo: any = sessionSave.get("loginInfo");
        this.UpdateLoginInfo(loginInfo)
        this.initTrackWorker();
    }

    public UpdateLoginInfo(loginInfo: LoginInfo) {
        this.loginInfo = loginInfo;
    }

    private initTrackWorker() {
        return new Promise(resolve => {
            let _trackWorker = new Worker();
            _trackWorker.onmessage = (event: any) => {
                if (event.data.type === 'connected') {
                    this.trackWorker = _trackWorker;
                    resolve(null);
                }
            };
            _trackWorker.postMessage({
                type: "trackInfo",
                event: {
                    productVersion: '',
                    model: navigator.platform,
                    url: "https://recordlog.iclass30.com"
                }
            })
        })
    }

    /**
     * @description: 公共埋点
     * @param {string} point_code
     * @param {any} data
     * @return {*}
     */
    public PublicTrack(point_code: string, trackData?: ITrack) {
        if (!this.trackWorker) return;

        let _data: any = {
            cls: point_code,// 埋点编码
            client_time: new Date().getTime(),//客户端时间
            ...(trackData || {})
        }
        if (this.loginInfo) {
            _data.login_id = this.loginInfo.id
            _data.tea_id = this.loginInfo.id
        }
        this.trackWorker.postMessage({ type: "trackWork", event: _data });
        console.log(`trackLog: point_code：${point_code}, data：${trackData ? JSON.stringify(trackData) : ''}`);
    }
}
<template>
    <div class="reportDetail display_flex flex-direction_column" ref="reportDetail">
        <div class="reportDetail_top">
            <div v-if="$sessionSave.get('reportDetail')" class="reportDetail__back el-icon-arrow-left"
                @click="backToExamReport">
                {{ $sessionSave.get("reportDetail").examName }}
            </div>
        </div>
        <div class="track-main flex_1 display_flex align-items_flex-start">
            <div class="track-main-nav left-menu flex-shrink_0">
                <div class="title">①报告设置</div>
                <div class="title" style="top: 180px;">②指标设置
                </div>
                <el-steps direction="vertical" :active="activeStep">
                    <el-step v-for="(item, index) in stepTitles" :key="index" :title="item.title"
                        @click.native="activeStep > index ? goToPage(index) : null"></el-step>
                </el-steps>
            </div>
            <router-view class="track-main-right" style="background-color: #fff;padding:10px 0 80px 25px;"
                @updateActiveStep="updateActiveStep">
            </router-view>
        </div>
    </div>
</template>
<script>
export default {
    name: "cust-exam-report",
    data() {
        return {
            activeStep: 0,
            stepTitles: [
                { title: "报告名称设置", path: 'customReport' },
                { title: "学科范围设置", path: 'customReport' },
                { title: "学生范围设置", path: 'customReport' },
                { title: "考试指标设置", path: 'customTargetResult' },
                { title: "成绩指标设置", path: 'customtargetScore' },
                { title: "分数换算设置", path: 'customtargetFraction' }
            ],
            examId: ""
        };
    },

    methods: {
        /**
         * @name:更新当前活动步骤并导航到相应的页面
         * @param {Number} newActiveStep - 新的活动步骤
         * @param {String} customExamId - 自定义考试的ID
         */
        updateActiveStep(newActiveStep, customExamId) {
            this.activeStep = newActiveStep;
            this.examId = customExamId;
            this.$router.push({
                path: `/home/<USER>/${this.stepTitles[this.activeStep].path}`,
                query: {
                    customExamId: customExamId,
                }
            });

        },
        /**
         * @name:返回考试报告页面
         */
        backToExamReport() {
            this.$router.push({
                path: '/home/<USER>',
                query: { fromPage: 'customExamReport' },
            });
        },
        /**
         * @name:导航栏点击跳转
         * @description: 导航栏点击后，根据点击的页面索引进行页面跳转
         * @param {Number} page - 被点击的页面索引
         */
        goToPage(page) {
            this.$router.push({
                path: `/home/<USER>/${this.stepTitles[page].path}`,
                query: {
                    customExamId: this.examId,
                }
            });
            this.activeStep = page;
        },
    },
};
</script>
  
<style lang="scss" scoped>
.reportDetail {
    margin: 0 auto;
    font-family: Microsoft YaHei;

    .reportDetail_top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .reportDetail__back {
            font-size: 16px;
            font-weight: bold;
            color: #3f4a54;
            cursor: pointer;
            // margin-bottom : 20px;
        }
    }


}

.track-main {
    width: 100%;


    .track-main-nav {
        position: relative;
        padding: 0;
        list-style: none;

        .title {
            padding-top: 16px;
            padding-left: 21px;
            font-size: 16px;
            font-weight: 600;
            position: absolute;
        }
    }

    .track-main-right {
        width: 100%;
    }

    .left-menu {
        position: -webkit-sticky;
        position: sticky;
        flex-shrink: 0;
        top: 0px;
        left: 20px;
        width: 220px;
        height: 395px;
        background: #fff;
        box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
        border-radius: 6px;
        margin-right: 20px;
    }
}
</style>
<style lang="scss">
.left-menu {
    .el-steps {
        padding: 65px 0 28px;

        .el-step {
            margin-left: 48px;

            .el-step__head {
                width: 14px;
                display: flex;
                justify-content: center;

                .el-step__line {
                    left: unset;
                }

                .el-step__icon {
                    width: 14px;
                    height: 14px;

                    .el-step__icon-inner {
                        display: none;
                    }
                }
            }

            .el-step__head.is-process {
                color: #409EFF;
                border-color: #409EFF;
            }

            .el-step__title.is-finish {
                color: #3F4A54;
            }
        }

        .el-step:nth-child(3) {
            flex-basis: unset !important;
            height: 90px !important;

            .el-step__line {
                height: 0px;
            }
        }

        .el-step__main {
            .el-step__title {
                font-size: 14px;
                margin-top: -5px;
            }

            .el-step__title.is-process {
                color: #409EFF;
            }
        }
    }
}
</style>

<!--
 * @Descripttion: 主观题题目
 * @Author: 小圆
 * @Date: 2023-12-22 17:20:58
 * @LastEditors: 小圆
-->
<template>
  <div class="subject-question">
    <el-tooltip effect="dark" :content="item.sortTitle" placement="top-start">
      <div class="question-sort ellipsis clearfix" :title="item.sortTitle">
        <span class="doChoicetext fl" v-if="doChoicetext">({{ doChoicetext }})</span>
        <button
          class="question-sort-content ellipsis fl"
          :class="{
            active:
              item.tempChooseType === '' ||
              item.tempChooseType === undefined ||
              item.tempChooseType === null ||
              item.tempChooseType == 1,
          }"
          @click="setChooseType(item)"
        >
          {{ item.sortTitle }}
        </button>
        <span
          class="fr"
          v-if="
            item.tempChooseType === '' ||
            item.tempChooseType === undefined ||
            item.tempChooseType === null ||
            item.tempChooseType == 1
          "
          >得分：{{ item.score }}分</span
        >
      </div>
    </el-tooltip>

    <div
      class="question-score__container"
      v-if="
        item.tempChooseType === '' ||
        item.tempChooseType === undefined ||
        item.tempChooseType === null ||
        item.tempChooseType == 1
      "
    >
      <div class="">
        <span>修改得分：</span>
        <el-input-number
          class="question-score__input"
          v-model="item.tempScore"
          :min="0"
          :max="item.fullScore"
          :controls="false"
          :placeholder="'满分' + item.fullScore + '分'"
        ></el-input-number>
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
import { checkFloat } from '@/utils/common';

export default {
  props: ['item', 'index', 'doChoicetext'],
  data() {
    return {};
  },

  methods: {
    setChooseType() {
      this.$emit('setChooseType', this.item, this.index);
    },
  },
};
</script>

<style lang="scss" scoped>
.fl {
  float: left;
}
.fr {
  float: right;
}
.subject-question {
  padding: 10px;
  // display: flex;
  // align-items: center;
}
.question-sort {
  overflow: hidden;
  line-height: 26px;

  .question-sort-content {
    max-width: 60%;
    display: inline-block;
    padding: 0 15px;
    color: #2574ff;
    color: #606266;
    font-size: 14px;
    font-weight: 400;
    border: 1px solid #2574ff;
    border: 1px solid #dcdfe6;
    border-radius: 5px;
    background-color: #ffffff;

    cursor: pointer;

    &:hover {
      background-color: #ecf5ff;
    }

    &.active {
      background-color: #2574ff;
      font-weight: 700;
      color: #fff;
    }
  }
}

.question-score__container {
  min-width: 200px;
  padding: 5px 0;
  .question-score__input {
    width: 100px;
    height: 35px;
    text-align: left;

    ::v-deep .el-input__inner {
      height: 35px;
      line-height: 35px;
    }
  }
}

.doChoicetext {
  color: #f59a23;
  margin-right: 5px;
}
</style>

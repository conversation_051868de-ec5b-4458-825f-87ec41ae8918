<template>
  <!-- 上线设置 -->
  <div class="target-container">
    <div style="display: flex; line-height: 40px; margin-left: -20px">
      <p class="tip-txt">提示：此处指标均按总分统计，各科根据总分统计规则，按比例自动生成。</p>
      <el-button
        type="primary"
        class="btn-item"
        style="position: absolute; right: 20px"
        :loading="loading"
        @click="resetCustom"
        :disabled="isDisabled"
      >
        恢复默认</el-button
      >
    </div>
    <!-- 成绩分段设置 -->
    <div class="target-box">
      <div class="title">成绩分段设置</div>
      <div class="content-box">
        <el-form
          :inline="true"
          :model="scoreSegment"
          :hide-required-asterisk="true"
          ref="scoreSegmentRef"
        >
          <div class="content-main">
            <div class="content-form clearfix">
              <template>
                <el-form-item label="" prop="score" :rules="rules.numberMaxAndMinRules">
                  <el-input
                    v-model.number="scoreSegment.score"
                    class="w-60 m-2"
                    size="large"
                    @blur="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm, '')"
                  />分/段
                </el-form-item>
              </template>
            </div>
          </div>
        </el-form>
      </div>
    </div>
    <!-- 总名次设置 -->
    <div class="target-box">
      <div class="title">总名次设置</div>
      <div class="content-box">
        <el-form
          :inline="true"
          :model="totalRankSegment"
          :hide-required-asterisk="true"
          ref="totalRankSegmentRef"
        >
          <div class="content-main">
            <div class="content-form clearfix">
              <template v-for="(item, index) in totalRankSegment.school">
                <template>
                  <el-form-item
                    label=""
                    :prop="`school.${index}.end`"
                    :rules="rules.numberRules"
                    v-if="item.start != '-1'"
                  >
                    前
                    <el-input
                      v-model.number="item.end"
                      class="w-60 m-2"
                      size="large"
                      @blur="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm, '')"
                    />名
                  </el-form-item>
                </template>
              </template>
            </div>
            <div class="content-form clearfix" style="margin-top: 15px">
              <template v-for="(item, index) in totalRankSegment.school">
                <template>
                  <el-form-item
                    label=""
                    :prop="`school.${index}.end`"
                    :rules="rules.numberRules"
                    v-if="item.start == '-1'"
                  >
                    后
                    <el-input
                      v-model.number="item.end"
                      class="w-60 m-2"
                      size="large"
                      @blur="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm, '')"
                    />名
                  </el-form-item>
                </template>
              </template>
            </div>
          </div>
        </el-form>
      </div>
    </div>
    <!-- 名次分段设置 -->
    <div class="target-box">
      <div class="title">
        名次分段设置
        <span class="title-tips"> （注意：当参考学生低于5人时，统一展示在1个分段中。） </span>
      </div>
      <div class="content-box">
        <el-form
          :inline="true"
          :model="rankSegment"
          :hide-required-asterisk="true"
          class="clearfix"
          ref="rankRef"
        >
          <div class="content-form" :class="rankSegment.school.length > 2 ? 'score-form' : ''">
            <div class="form-body">
              <template v-for="(item, index) in rankSegment.school">
                <el-form-item :label="`第 ${item.end} 名`" class="col-rank" v-if="index == 0">
                </el-form-item>
                <el-form-item :label="`第 ${item.end} 名`" class="col-rank" v-if="index == 1">
                </el-form-item>

                <span class="form-split" v-if="index != 1">—</span>
                <el-form-item
                  v-if="index > 1"
                  label="第"
                  :prop="`school.${index}.end`"
                  :rules="rules.numberRules"
                  class="col-rank"
                >
                  <el-input
                    v-model.number="item.end"
                    @blur="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm, '')"
                    @change="changeRankVal('school', index)"
                    class="w-60 m-2"
                    size="large"
                  />名
                </el-form-item>
              </template>
            </div>
          </div>
        </el-form>
      </div>
    </div>
    <div class="target-box" style="position: relative">
      <div class="save-btn" style="left: 20px; right: unset" v-if="customExamId">
        <el-button
          type="primary"
          @click="$emit('updateActiveStep', activeStep - 1, customExamId)"
          :disabled="isDisabled"
        >
          上一步</el-button
        >
      </div>
      <div class="save-btn">
        <el-button type="primary" @click="save" :disabled="isDisabled">
          {{ customExamId ? '下一步' : '保存设置' }}</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import {
  checkFloatNum,
  checkNumberVal,
  checkIsNumber,
  checkNumberMaxandMinVal,
} from '@/utils/common.js';
import { setAllConfAPI, getConfAPI } from '@/service/pexam';
import UserRole from '@/utils/UserRole';

export default {
  props: {},
  data() {
    return {
      activeStep: 4,
      customExamId: this.$route.query.customExamId,
      // 分数线指标集合
      tempOnlineList: [],
      onlineList: [],
      // 上线表单
      onlineForm: {
        tableData: [],
      },
      onlineData: [],
      rules: {
        // 数字 可以是小数
        floatNumRules: [
          {
            required: true,
            message: '必填项',
            trigger: 'blur',
          },
          {
            validator: checkFloatNum,
            trigger: 'blur',
          },
        ],
      },
      //成绩分段
      scoreSegment: {},
      //总名次设置
      totalRankSegment: {
        school: [],
      },
      //名次分段
      rankSegment: {
        school: [],
      },
      // 表单验证规则
      rules: {
        // 数字和必填项验证规则 不超过100
        numberMaxRules: [
          {
            required: true,
            message: '必填项',
            trigger: 'blur',
          },
          {
            type: 'number',
            message: '请输入0-100',
            trigger: 'blur',
          },
          { validator: checkNumberVal, trigger: 'blur' },
        ],
        // 数字和必填项验证规则 5-1000
        numberMaxAndMinRules: [
          {
            required: true,
            message: '必填项',
            trigger: 'blur',
          },
          {
            type: 'number',
            message: '请输入5-1000',
            trigger: 'blur',
          },
          { validator: checkNumberMaxandMinVal, trigger: 'blur' },
        ],
        // 数字和必填项验证规则
        numberRules: [
          {
            required: true,
            message: '必填项',
            trigger: 'blur',
          },
          {
            type: 'number',
            message: '请输入数字',
            trigger: 'blur',
          },
          { validator: checkFloatNum, trigger: 'blur' },
        ],
      },
      //按钮加载状态
      loading: false,
    };
  },
  computed: {
    isDisabled() {
      return !(UserRole.isOperation || UserRole.isSchoolLeader || UserRole.isGradeLeader);
    },
  },
  mounted() {
    this.geConf();
  },
  methods: {
    /**
     * @name:获取默认指标数据
     */
    async geConf() {
      await getConfAPI({
        examId: this.customExamId
          ? this.customExamId
          : this.$sessionSave.get('reportDetail').examId,
        type: 101,
      }).then(res => {
        this.targetData = JSON.parse(res.data);
        //成绩分段
        this.scoreSegment = this.targetData.scoreSegment;
        //总名次设置
        this.totalRankSegment.school = this.targetData.totalRankSegment;
        //名次分段
        this.rankSegment.school = this.targetData.rankSegment;
      });
      this.$emit('updateActiveStep', this.activeStep, this.customExamId);
    },
    /**
     * @name:恢复默认
     */
    resetCustom() {
      this.loading = true;
      setAllConfAPI({
        examId: this.customExamId
          ? this.customExamId
          : this.$sessionSave.get('reportDetail').examId,
        type: 101,
        content: '{}',
      }).then(res => {
        const reportDetail = this.$sessionSave.get('reportDetail');
        this.$sessionSave.set('reportDetail', {
          ...reportDetail,
          v: reportDetail.v + 1,
        });
        setTimeout(() => {
          this.loading = false;
          this.$message({
            message: '恢复默认成功！',
            type: 'success',
            duration: 1500,
          });
          this.geConf();
        }, 1000);
      });
    },
    /**
     * @name 改变名次分段结束值时判断数值是否合适
     */
    changeRankVal(key, index) {
      const end = this.rankSegment[key][index].end;
      let start = this.rankSegment[key][index].start;
      if (index > 0) {
        start = this.rankSegment[key][index - 1].end;
      }
      if (end || end === 0) {
        if (Number(end) < Number(start)) {
          this.$message({
            message: '后者需大于等于前者',
            type: 'error',
            duration: 1500,
          });
          this.rankSegment[key][index].end = null;
          return;
        }
      }
      // 修改当前值 如果后面值小于当前值则置为空
      this.rankSegment[key].forEach((item, curIndex) => {
        if (curIndex > index) {
          if (item.end < end) {
            item.end = null;
          }
        }
      });
    },

    saveTotalRankSegmentBefore() {
      let arrLength = this.totalRankSegment.school.length;
      for (let i = 0; i < arrLength; i++) {
        if(i == 0 || i == arrLength - 1 || i == arrLength - 2 ) {
          continue;
        }
        const currentItem = this.totalRankSegment.school[i];
        const prevItem = this.totalRankSegment.school[i-1];
        currentItem.start = prevItem.end;
      }
    },

    setSaveRankBefore() {
      for (let i = 0; i < this.rankSegment.school.length; i++) {
        if(i == 0 || i == 1) {
          continue;
        }
        const currentItem = this.rankSegment.school[i];
        const prevItem = this.rankSegment.school[i-1];
        if(currentItem.end == prevItem.end) {
          currentItem.start = prevItem.end;
        } else {
          currentItem.start = prevItem.end + 1;
        }
      }
    },

    /**
     * @name:保存设置
     */
    save() {
      const score = new Promise((resolve, reject) => {
        this.$refs.scoreSegmentRef.validate((valid, message) => {
          if (valid) {
            resolve(this.scoreSegment);
          } else {
            reject(message);
          }
        });
      });

      this.saveTotalRankSegmentBefore();
      const totalRank = new Promise((resolve, reject) => {
        this.$refs.totalRankSegmentRef.validate((valid, message) => {
          if (valid) {
            resolve(this.totalRankSegment);
          } else {
            reject(message);
          }
        });
      });

      this.setSaveRankBefore();
      const rank = new Promise((resolve, reject) => {
        this.$refs.rankRef.validate((valid, message) => {
          if (valid) {
            resolve(this.rankSegment);
          } else {
            reject(message);
          }
        });
      });
      const arr = [score, totalRank, rank];
      Promise.all(arr)
        .then(async () => {
          const obj = {};
          obj.scoreSegment = this.scoreSegment;
          obj.totalRankSegment = this.totalRankSegment.school;
          obj.rankSegment = this.rankSegment.school;
          const json = JSON.stringify(obj);
          setAllConfAPI({
            examId: this.customExamId
              ? this.customExamId
              : this.$sessionSave.get('reportDetail').examId,
            type: 101,
            content: json,
          }).then(res => {
            const reportDetail = this.$sessionSave.get('reportDetail');
            this.$sessionSave.set('reportDetail', {
              ...reportDetail,
              v: reportDetail.v + 1,
            });
            this.$message({
              message: '保存成功！',
              type: 'success',
              duration: 1500,
            });
            // if (this.customExamId) {
            //   this.$router.push({
            //     path: '/home/<USER>/customtargetFraction',
            //     query: {
            //       customExamId: this.customExamId,
            //     }
            //   });
            this.$emit('updateActiveStep', this.activeStep + 1, this.customExamId);
          });
        })
        .catch(() => {
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.target-table td {
  padding: 14px 36px;
}

.content-box {
  padding: 0 20px;

  .content-main {
    position: relative;

    .content-form {
      width: 92%;
    }
  }
}

.target-table {
  width: 100%;
  border: 1px solid #e4e8eb;
  border-collapse: collapse;
  border-spacing: 0;

  thead {
    height: 64px;
    background: #f7fafc;
    border-radius: 6px 6px 0px 0px;

    th {
      text-align: center;
      font-weight: bold;
      color: #3f4a54;
      border: unset;
      border-right: 1px solid #e4e8eb;
    }
  }

  tr {
    height: 60px;

    td {
      text-align: center;
      border: 1px solid #e4e8eb;
    }
  }

  .icon-item {
    vertical-align: unset;

    &:nth-child(2) {
      margin-left: 28px;
    }
  }

  .icon-del {
    background: url('../../../assets/icon_shanchu.png');
  }

  .icon-addItem {
    width: 18px;
    height: 18px;
    background: url('../../../assets/icon_add.png');
    margin-left: unset !important;
  }
}

.form-split {
  line-height: 58px;
  margin-right: 20px;
}

.score-form {
  width: 84%;
}

.col-rank {
  margin-top: 10px;
}
</style>
<style lang="scss">
.custom-col .el-form-item {
  display: inline-block;
}

.custom-col .el-form-item__content {
  width: fit-content;
}

.target-table .el-input {
  width: unset;
}

.target-container {
  font-size: 16px !important;

  .el-form-item {
    margin-bottom: 0px !important;
  }

  .el-input__inner {
    text-align: center;
  }

  .tip-txt,
  .title-tips {
    color: #fe5d50;
    font-weight: bold;
  }

  .tip-txt {
    padding: 0 20px;
  }

  .title {
    position: relative;
    margin: 20px 0;
    padding: 0 10px;
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;

    &:before {
      overflow: hidden;
      position: absolute;
      left: 0;
      display: block;
      content: '';
      width: 4px;
      height: 24px;
      top: -3px;
      background: #409eff;
      border-radius: 3px;
    }

    .sub-title {
      color: #7e94a8;
      font-weight: 400;
      margin-left: 10px;
    }
  }

  .m-10 {
    margin-bottom: 10px;
  }

  .btn_item {
    height: 30px;
    width: 70px;
    padding: unset;
  }

  .save-btn {
    position: absolute;
    right: 20px;
    text-align: center;
    margin-top: 20px;

    .el-button {
      width: 136px;
      height: 36px;
    }
  }

  .icon-del {
    background: url('../../../assets/icon_delete.png');
    margin-top: -2px;
  }

  .icon-addItem {
    margin-left: 20px;
    background: url('../../../assets/icon_add.png');
  }

  .icon-item {
    width: 16px;
    height: 18px;
    display: inline-block;
    vertical-align: middle;
  }

  .content-box {
    padding: 0 20px;

    .content-main {
      position: relative;

      .content-form {
        width: 92%;
      }
    }
  }

  .tool-sec {
    position: absolute;
    right: 0;
    top: 10px;
    display: inline-block;
  }

  .custom-sec {
    float: right;
  }

  .custom-form-item {
    margin-right: unset !important;
  }

  .btn_del {
    border-color: #409eff !important;
    color: #409eff !important;
  }

  .col-title {
    margin-right: unset !important;
  }
}

.w-60 {
  width: 80px;
  margin-right: 10px;
}

.m-2 {
  // margin-top: 10px !important;
}

.clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: ' ';
  clear: both;
  height: 0;
}
</style>

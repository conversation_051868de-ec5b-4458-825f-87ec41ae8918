<template>
  <div class="book-view">
    <div class="book-aside">
      <div class="book-aside-header">
        <div class="book-aside-header__title">章节目录</div>
        <el-button type="text" style="padding: 0" @click="isOverviewDialogVisible = true">全书总览</el-button>
      </div>
      <el-tree
        ref="bookTree"
        class="book-tree"
        node-key="code"
        :data="bookStructure"
        :default-expand-all="false"
        :default-expanded-keys="defaultExpandedKeys"
        :props="defaultProps"
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <span :title="data.title" class="el-tree-node__label">{{ data.title }}</span>
        </template>
      </el-tree>
    </div>

    <div class="book-content" :key="currentBookStructureItem.id" v-loading="isLoading">
      <book-unpublish-view
        :stuCount="scanData.stuCount"
        :imgCount="scanData.imgCount"
        :workId="scanData.workId"
        @publish="handlePublish"
        v-if="!isPublish"
      ></book-unpublish-view>
      <book-data-view
        v-if="isPublish"
        :catalogCode="currentBookStructureItem.code"
        :examId="examId"
        @changeClassId="changeClassId"
      ></book-data-view>
    </div>

    <book-overview-dialog
      v-if="isOverviewDialogVisible"
      :classId="classId"
      @closed="isOverviewDialogVisible = false"
    ></book-overview-dialog>
  </div>
</template>

<script lang="ts">
import { Component, Ref, Vue } from 'vue-property-decorator';
import { mockBook } from './mock';
import { ElTree, TreeNode } from '@iclass/element-ui/types/tree';
import BookDataView from './bookDataView.vue';
import BookUnpublishView from './bookUnpublishView.vue';
import BookOverviewDialog from './bookOverviewDialog.vue';
import { getStructureCatalogList } from '@/service/testbank';
import { getCatalogScanDataAPI, getDirRateAPI } from '@/service/pexam';
import { ScanData } from './types';
import { localSaveUnique, sleep } from '@/utils/index';
import { Loading } from '@iclass/element-ui';

@Component({
  components: {
    BookUnpublishView,
    BookDataView,
    BookOverviewDialog,
  },
})
export default class BookView extends Vue {
  @Ref('bookTree') private bookTree: ElTree<string, any>;
  // 教辅目录扫描数据
  scanData: ScanData = {
    classCount: 0,
    imgCount: null,
    catalog_code: null,
    stuCount: null,
    workId: '',
  };
  // 报告考试id
  examId = '';
  // 班级id
  classId = '';
  // 书本结构
  bookStructure = [];
  // 当前书本信息
  currentBookStructureItem = {};
  // 是否展开
  defaultExpandedKeys = [];
  // 默认属性
  defaultProps = {
    children: 'children',
    label: 'title',
  };
  // 是否发布
  isPublish = false;
  // 是否加载中
  isLoading = false;
  // 是否显示全书总览
  isOverviewDialogVisible = false;

  mounted() {
    this.getBookStructure();
  }

  // 获取教辅结构
  async getBookStructure() {
    const res = await getStructureCatalogList({
      bookCode: this.$route.query.bookCode,
    });
    this.bookStructure = res.data;

    this.$nextTick(async () => {
      if (this.bookStructure && this.bookStructure.length > 0) {
        let node = this.getNodeByCache();
        if (!node) {
          node = this.getFirstNode(this.bookStructure);
        }
        this.defaultExpandedKeys = [node.code];
        this.bookTree.setCurrentNode(node);
        this.currentBookStructureItem = node;
        this.getData();
      }
    });
  }

  // 获取目录得分率
  async getDirRate() {
    const res = await getDirRateAPI({
      bookCode: this.$route.query.bookCode,
      catalogCode: this.currentBookStructureItem.code,
      classId: '',
      schoolId: this.$sessionSave.get('schoolInfo').id
    });

    if (res.data) {
      this.examId = res.data.examId;
      this.isPublish = true;
    } else {
      this.examId = '';
      this.isPublish = false;
    }
  }

  // 获取教辅目录扫描数据
  async getCatalogScanData() {
    const res = await getCatalogScanDataAPI({
      schoolId: this.$sessionSave.get('schoolInfo').id,
      teacherId: this.$sessionSave.get('loginInfo').id,
      bookCode: this.$route.query.bookCode,
      catalogCode: this.currentBookStructureItem.code,
    });
    if (res && res.code == 1) {
      this.scanData = res.data[0] || {
        classCount: 0,
        imgCount: null,
        catalog_code: null,
        stuCount: null,
        workId: '',
      };
    } else {
      this.scanData = {
        classCount: 0,
        imgCount: null,
        catalog_code: null,
        stuCount: null,
        workId: '',
      };
      this.$message.error(res.msg);
    }
  }

  // 获取数据
  async getData() {
    this.isLoading = true;
    this.classId = '';
    Promise.all([this.getDirRate(), this.getCatalogScanData()]).finally(() => {
      this.isLoading = false;
    });
  }

  // 处理节点点击
  async handleNodeClick(data: any, node: TreeNode<string, any>, tree: ElTree<string, any>) {
    this.handleNodeExpand(node);
    if (data.id == this.currentBookStructureItem.id) return;
    localSaveUnique.set(this.$route.query.bookCode, data.code);
    this.isPublish = false;
    this.currentBookStructureItem = data;
    this.getData();
  }

  // 处理发布
  async handlePublish() {
    const loading = Loading.service({
      lock: true,
      text: '正在生成分析报告，请稍候，预计5-20s，若长时间未生成，请刷新页面重试',
      background: 'rgba(0, 0, 0, 0.7)',
    });

    //  4次后(20s)，如果还没有生成，则提示发布失败
    let count = 0;
    while (count < 4) {
      await sleep(5000);
      await this.getDirRate();
      if (this.examId) {
        loading.close();
        this.$message.success('发布成功');
        return;
      }
      count++;
    }
    loading.close();
    this.$message.error('发布失败');
  }

  // 处理班级id变化
  changeClassId(classId: string) {
    this.classId = classId;
  }

  // 获取第一个节点
  private getFirstNode(tree: any = this.bookStructure): any {
    for (const element of tree) {
      if (element.children && element.children.length > 0) {
        return this.getFirstNode(element.children);
      } else {
        return element;
      }
    }
    return null;
  }

  // 处理非叶子节点的展开
  private handleNodeExpand(node: TreeNode<string, any>): void {
    if (!node.isLeaf && this.currentBookStructureItem.id !== node.data.id) {
      node.expanded = true;
    }
  }

  // 根据缓存获取节点
  getNodeByCache() {
    try {
      const catalogCode = localSaveUnique.get(this.$route.query.bookCode);
      return this.bookTree.getNode(catalogCode).data;
    } catch (error) {
      console.error(error);
      return null;
    }
  }
}
</script>

<style scoped lang="scss">
.book-view {
  display: flex;
  height: 100%;
  font-size: 14px;

  background-color: #fff;
  border-radius: 10px;
  padding: 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.book-aside {
  display: flex;
  flex-direction: column;
  overflow: auto;

  .book-aside-header {
    display: flex;
    justify-content: space-between;
    padding: 10px;

    .book-aside-header__title {
      font-size: 16px;
      font-weight: 500;
    }
  }
}

.book-tree {
  width: 250px;
  padding-top: 15px;
  margin-right: 20px;
  scroll-behavior: smooth;
  overflow: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
  }

  ::v-deep {
    .el-tree-node__expand-icon {
      width: 17px;
      height: 16px;
      margin    : {
        right: 10px;
        top: 2px;
      }
      background: {
        image: url('~@/assets/icons_treemenu.png');
        repeat: no-repeat;
      }
      &:before {
        display: none;
      }
      &.is-leaf {
        background: none;
      }
      &.expanded {
        transform: none;
        transition: none;
        background-position: 0px -21px;
      }
    }

    .el-tree-node__content {
      height: 32px;

      &:hover {
        background-color: #f5f7fa !important;

        .el-tree-node__label {
          transform: translateX(4px);
        }
      }
    }

    .el-tree-node:focus > .el-tree-node__content {
      background-color: transparent;
    }

    .el-tree-node.is-current {
      & > .el-tree-node__content {
        color: #409eff;
        font-weight: 500;

        .el-tree-node__expand-icon {
          background-position: -21px 0px;
          &.expanded {
            background-position: -21px -21px;
          }
        }
      }
    }

    .el-tree-node__label {
      font-size: 16px;
      text-overflow: ellipsis;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .el-tree-node__children .el-tree-node__label {
      font-size: 14px;
    }
  }
}

.book-content {
  width: 0;
  flex: 1;
  padding: 16px;
  overflow: auto;
}
</style>

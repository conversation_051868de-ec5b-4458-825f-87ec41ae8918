<template>
  <div>
    <div class="setting-header">
      <div class="title">优困生设置</div>
      <div class="header-right" v-if="!examId">
        <el-button type="primary" size="small" :loading="resetLoading" @click="reset">恢复默认</el-button>
        <el-button type="primary" size="small" :loading="saveLoading" @click="save">保存</el-button>
      </div>
    </div>

    <el-form :model="jCfg" label-position="left" inline hide-required-asterisk>
      <el-form-item label="计算规则：">
        <el-radio-group v-model="jCfg.type" @change="onTypeChange">
          <el-radio :label="1">按名次</el-radio>
          <el-radio :label="2">按得分率</el-radio>
        </el-radio-group>
      </el-form-item>

      <div v-if="jCfg.type == 1">
        <el-form-item
          label="学优生：前"
          :prop="`before`"
          :rules="[{ required: true, message: '必填项', trigger: 'blur' }]"
        >
          <el-input-number
            v-model="jCfg.before"
            class="input"
            size="small"
            :max="100 - jCfg.after"
            :min="jCfg.after"
            :controls="false"
            :step-strictly="true"
          />
          <span>%</span>
        </el-form-item>
        <el-form-item
          label="学困生：后"
          :prop="`after`"
          :rules="[{ required: true, message: '必填项', trigger: 'blur' }]"
        >
          <el-input-number
            v-model="jCfg.after"
            class="input"
            size="small"
            :max="100 - jCfg.before"
            :min="0"
            :controls="false"
            :step-strictly="true"
          />
          <span>%</span>
        </el-form-item>
      </div>

      <div v-if="jCfg.type == 2">
        <el-form-item
          label="学优生：≥"
          :prop="`before`"
          :rules="[{ required: true, message: '必填项', trigger: 'blur' }]"
        >
          <el-input-number
            v-model="jCfg.before"
            class="input"
            size="small"
            :max="100"
            :min="jCfg.after"
            :controls="false"
            :step-strictly="true"
          />
          <span>%</span>
        </el-form-item>
        <el-form-item
          label="学困生：＜"
          :prop="`after`"
          :rules="[{ required: true, message: '必填项', trigger: 'blur' }]"
        >
          <el-input-number
            v-model="jCfg.after"
            class="input"
            size="small"
            :max="jCfg.before"
            :min="0"
            :controls="false"
            :step-strictly="true"
          />
          <span>%</span>
        </el-form-item>
      </div>
    </el-form>

    <SettingSaveDialog
      v-if="isShowSaveDialog"
      :currentPhase="currentPhase"
      :currentGradeId="currentGradeId"
      :currentGradeItem="currentGradeItem"
      :currentGradeList="currentGradeList"
      :examId="examId"
      @save="handleSave"
      @closed="isShowSaveDialog = false"
    ></SettingSaveDialog>
  </div>
</template>

<script lang="ts">
import { Component, Mixins, Vue } from 'vue-property-decorator';
import SchoolSettingMixin from '../SchoolSetting.mixin';
import { getSchCfgAPI, restoreSchCfgAPI, setSchCfgAPI } from '@/service/pexam';
import { SchoolSettingType, SettingChangeParams } from '../../types';
import SettingSaveDialog from '../SettingSaveDialog.vue';

interface JCfg {
  type: number;
  before: number;
  after: number;
}

@Component({
  components: {
    SettingSaveDialog,
  },
})
export default class StudentTypeSetting extends Mixins(SchoolSettingMixin) {
  // 配置信息
  private jCfg: JCfg = {
    type: 1,
    before: 15,
    after: 5,
  };
  // 恢复loading
  private resetLoading = false;
  // 保存loading
  private saveLoading = false;
  // 保存对话框
  private isShowSaveDialog = false;

  mounted() {
    this.getConfig();
  }

  // 获取配置
  async getConfig() {
    const res = await getSchCfgAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      type: SchoolSettingType.StudentTypeSetting,
      phase: this.currentPhase,
      gradeId: this.currentGradeId,
      examId: this.examId,
    });
    if (res.data && res.data.jCfg) {
      this.jCfg = res.data.jCfg;
    } else {
      this.jCfg = {
        type: 1,
        before: 15,
        after: 5,
      };
    }
  }

  // 恢复默认
  async reset() {
    this.resetLoading = true;
    try {
      await this.restoreSchCfg();
      this.$notify.success({
        title: '成功',
        message: '恢复成功',
      });
      this.getConfig();
    } catch (error) {
      this.$notify.error({
        title: '失败',
        message: '恢复失败',
      });
    } finally {
      this.resetLoading = false;
    }
  }

  // 保存
  async save() {
    await this.checkConfig();
    if (this.currentGradeId == '0') {
      this.isShowSaveDialog = true;
    } else {
      this.handleSave([this.currentGradeId]);
    }
  }

  // 执行保存
  async handleSave(gradeIds: string[]) {
    this.saveLoading = true;
    this.isShowSaveDialog = false;
    try {
      await this.setSchCfg(gradeIds);
      this.$notify.success({
        title: '成功',
        message: '保存成功',
      });
    } catch (error) {
      this.$notify.error({
        title: '失败',
        message: '保存失败',
      });
    } finally {
      this.saveLoading = false;
    }
  }

  // 设置学校配置
  public setSchCfg(gradeIds: string[]) {
    const promises = [];
    for (const gradeId of gradeIds) {
      promises.push(
        setSchCfgAPI({
          schId: this.$sessionSave.get('schoolInfo').id,
          schName: this.$sessionSave.get('schoolInfo').schoolName,
          type: SchoolSettingType.StudentTypeSetting,
          phase: this.currentPhase,
          gradeId,
          jCfg: this.jCfg,
          examId: this.examId,
        })
      );
    }
    return Promise.all(promises);
  }

  // 获取配置信息
  public getCfg() {
    return {
      type: SchoolSettingType.StudentTypeSetting,
      jCfg: this.jCfg,
    };
  }

  // 恢复学校配置
  public restoreSchCfg() {
    return restoreSchCfgAPI({
      phase: this.currentPhase,
      schId: this.$sessionSave.get('schoolInfo').id,
      type: SchoolSettingType.StudentTypeSetting,
      gradeId: this.currentGradeId,
      examId: this.examId,
    });
  }

  // 检查配置
  public checkConfig() {
    if (this.jCfg.after === null || this.jCfg.after === undefined) {
      this.$notify.error({
        title: '【优困生设置】',
        message: '请设置学困生',
      });
      return Promise.reject(false);
    }
    if (this.jCfg.before === null || this.jCfg.before === undefined) {
      this.$notify.error({
        title: '【优困生设置】',
        message: '请设置学优生',
      });
      return Promise.reject(false);
    }
    return Promise.resolve(true);
  }

  // 设置更改
  onSettingChange(data: SettingChangeParams) {
    this.getConfig();
  }

  // 类型更改
  onTypeChange(val: number) {
    if (val == 1) {
      this.jCfg.before = 15;
      this.jCfg.after = 5;
    } else {
      this.jCfg.before = 90;
      this.jCfg.after = 30;
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../page-style.scss';

.setting-item {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 16px;
}

.setting-label {
}

.setting-content {
}

.input {
  width: 80px;
  margin: 0 10px;
}
</style>

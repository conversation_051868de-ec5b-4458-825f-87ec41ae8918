<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-11-05 11:13:58
 * @LastEditors: 小圆
-->
<template>
  <div class="statistic-scan clearfix" v-loading="isLoading">
    <el-radio-group class="radio-group" v-model="type" @change="getStatScanData">
      <el-radio :label="1">
        <span>应用次数</span>
        <el-tooltip
          class="item"
          effect="dark"
          content="所选时间内，扫描数量≥5的测评总数，小于5的不计入"
          placement="top"
        >
          <i class="header-icon el-icon-question"></i>
        </el-tooltip>
      </el-radio>
      <el-radio :label="2">
        <span>扫描份数</span>
        <el-tooltip
          class="item"
          effect="dark"
          content="所选时间内，实际扫描数量，其中填涂缺考的计入已扫"
          placement="top"
        >
          <i class="header-icon el-icon-question"></i>
        </el-tooltip>
      </el-radio>
    </el-radio-group>

    <div v-if="dataList && dataList.length && classColumn && classColumn.length">
      <el-table
        class="scan-totle-table"
        stripe
        :data="dataList"
        :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
        style="margin-top: 10px; width: 100%; border: 1px solid #ebeef5"
        v-drag-table
        v-sticky-table="0"
      >
        <el-table-column align="center" fixed label="年级" width="150">
          <template #default="scope">
            <span>{{ (currentGrade && currentGrade.name) || '' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" fixed label="学科" prop="subjectName" width="150">
        </el-table-column>
        <el-table-column
          align="center"
          v-for="item in classColumn"
          :key="item.id"
          :label="item.class_name"
          min-width="120"
          prop=""
        >
          <template #default="scope">
            <span>{{ scope.row.class[item.id] || '--' }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <no-data v-else></no-data>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { QueryData } from './types';
import moment from 'moment';
import { classList } from '@/service/api';
import { getStatScanData } from '@/service/pexam';
import NoData from '@/components/noData.vue';

@Component({
  components: {
    NoData,
  },
})
export default class StatisticDetailClass extends Vue {
  @Prop({ default: {} }) queryData: QueryData;
  @Prop({ default: [] }) subjectList;
  @Prop({ default: [] }) gradeList;

  // 1 应用次数 2 扫描次数
  type = 1;
  // 数据列表
  dataList = [];
  // 班级列数据
  classColumn = [];

  isLoading = false;

  // 筛选可用的学科列表
  get subjectListFilter() {
    if (!this.queryData.gradeValue) return this.subjectList;
    const grade = this.gradeList.find(item => item.id == this.queryData.gradeValue);
    const subjectList = this.subjectList.filter(
      item => item.id == '' || item.phaseId == grade.phaseId
    );
    return subjectList;
  }

  // 当前年级
  get currentGrade() {
    return this.gradeList.find(item => item.id == this.queryData.gradeValue);
  }

  // 当前学科
  get currentSubject() {
    return this.subjectList.find(item => item.id == this.queryData.subjectValue);
  }

  mounted() {
    this.getStatScanData();
    this.$bus.$on('statistic-change', this.getStatScanData);
    this.$bus.$on('handle-export', this.handleExport);
  }

  beforeDestroy() {
    this.$bus.$off('statistic-change', this.getStatScanData);
    this.$bus.$off('handle-export');
  }

  // 获取统计数据
  async getStatScanData() {
    this.isLoading = true;
    const params = this.getParams();
    const { data } = await getStatScanData(params);
    const dataList = [];
    // 全部
    if (!this.queryData.subjectValue) {
      this.subjectListFilter.forEach(item => {
        if (item.id)
          dataList.push({
            subjectId: item.id,
            subjectName: item.name,
            class: {},
          });
      });
    } else {
      dataList.push({
        subjectId: this.currentSubject.id,
        subjectName: this.currentSubject.name,
        class: {},
      });
    }
    data.forEach(item => {
      const dataItem = dataList.find(e => e.subjectId == item.subjectId);
      if (dataItem) dataItem.class[item.classId] = item.num;
    });

    this.classColumn = [];
    this.classColumn = await this.getClassList();
    this.dataList = dataList;
    this.isLoading = false;
  }

  // 获取班级列表列
  async getClassList() {
    const grade = this.gradeList.find(item => item.id == this.queryData.gradeValue);
    const { data } = await classList(grade.year, grade.phaseId - 2);
    const list = data.rows;
    list.sort((a, b) => {
      const numA = Number((a.class_name.match(/\d+/) && a.class_name.match(/\d+/)[0]) || 0);
      const numB = Number((b.class_name.match(/\d+/) && b.class_name.match(/\d+/)[0]) || 0);
      return numA - numB;
    });
    return list;
  }

  // 导出扫描数据
  async handleExport() {
    if (!this.dataList.length) {
      this.$message.warning('没有数据可以导出');
      return;
    }

    const params = this.getParams();
    const query = new URLSearchParams(params);
    const url = `${process.env.VUE_APP_KKLURL}/pscan/_/export-scan-data?${query.toString()}`;
    window.open(url, '_blank');
  }

  // 获取查询参数
  getParams() {
    const params = {
      schId: this.$sessionSave.get('schoolInfo').id,
      grdId: this.queryData.gradeValue,
      subjectId: this.queryData.subjectValue,
      categoryId: this.queryData.categoryValue,
      begin: moment(this.queryData.dateRange[0]).format('YYYY-MM-DD'),
      end: moment(this.queryData.dateRange[1]).format('YYYY-MM-DD'),
      leNum: this.type == 1 ? 5 : '',
      statType: this.queryData.statType,
    };
    return params;
  }
}
</script>

<style scoped lang="scss">
.radio-group {
  margin: 10px 0;
}

.scan-totle-title {
  position: relative;
  padding-left: 16px;
  line-height: 38px;
  overflow: hidden;
  margin-bottom: 20px;

  .title {
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;
  }

  .btn {
  }

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
  }
}

.scan-totle-table {
  border: 1px solid #e4e8eb;
}

.header-icon {
  margin-bottom: 0;
}
</style>

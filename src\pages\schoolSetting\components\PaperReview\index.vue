<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-02-11 10:43:05
 * @LastEditors: 小圆
-->
<template>
  <div>
    <div class="setting-header">
      <div class="title">试卷讲评</div>
      <div class="header-right">
        <el-button type="primary" size="small" :loading="saveLoading" @click="saveConfig">保存</el-button>
      </div>
    </div>

    <div v-for="item in jCfg" :key="item.code" class="type-item">
      <div class="type-item-name">{{ item.name }}：</div>
      <el-radio-group v-model="item.value" size="small" class="type-item-radio" @change="saveConfig">
        <el-radio :label="0" border>发布成绩前</el-radio>
        <el-radio :label="1" border>发布成绩后</el-radio>
      </el-radio-group>
    </div>

    <div class="tip">
      <div class="title">
        <p>
          *注：对于练习和导学案类型的测评，只需在设置中选择“成绩发布前”进行讲评，便能即刻在关联答卷成功后，开启讲评环节。
        </p>
        <p>而其他测评类型，则需遵循规则，待成绩扫描发布后方可进行讲评。</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Mixins, Vue } from 'vue-property-decorator';
import SchoolSettingMixin from '../SchoolSetting.mixin';
import { getSchCfgAPI, setSchCfgAPI } from '@/service/pexam';
import { SchoolSettingType } from '../../types';

interface JCfg {
  code: string;
  name: string;
  /** 0：发布成绩前，1：发布成绩后 */
  value: number;
}

@Component
export default class PaperReview extends Mixins(SchoolSettingMixin) {
  // 配置列表
  private jCfg: JCfg[] = [];
  // 保存loading
  private saveLoading = false;

  async created() {
    this.getConfig();
  }

  // 获取配置
  async getConfig() {
    const res = await getSchCfgAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      type: SchoolSettingType.PaperReview,
    });
    const data = res.data;
    if (data && data.jCfg) {
      this.jCfg = data.jCfg;
    }
  }

  // 保存配置
  async saveConfig() {
    this.saveLoading = true;
    try {
      await setSchCfgAPI({
        schId: this.$sessionSave.get('schoolInfo').id,
        schName: this.$sessionSave.get('schoolInfo').schoolName,
        type: SchoolSettingType.PaperReview,
        jCfg: this.jCfg,
      });
      this.$notify.success({
        title: '成功',
        message: '保存成功',
      });
    } catch (error) {
      this.$notify.error({
        title: '失败',
        message: '保存失败',
      });
    } finally {
      this.saveLoading = false;
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../page-style.scss';

.type-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .type-item-name {
    padding-left: 20px;
    min-width: 7em;
  }
}
</style>

<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-05-27 15:23:58
 * @LastEditors: 小圆
-->
<template>
  <div>
    <div v-show="!isNoData" ref="subjectRateChart" style="width: 100%; height: 400px"></div>
    <div v-show="isNoData">
      <div style="text-align: center">
        <img style="width: 350px" :src="noResImg" alt="" />
      </div>
      <p style="text-align: center; font-size: 16px; margin-top: 10px">暂无数据!</p>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import {
  ISubjectRateData,
  IFilterData,
} from '@/pages/lookReport/reportOverview/subjectComparison.vue';

@Component
export default class SubjectRateChart extends Vue {
  public $refs: {
    subjectRateChart: HTMLElement;
  };

  @Prop() tableData: ISubjectRateData[];
  @Prop() filterData: IFilterData;
  // 缺少资源图片
  noResImg = require('@/assets/no-res.png');
  // 是否没有数据
  isNoData: boolean = true;
  // 图表
  subjectRateChart: EChartsType = null;

  @Watch('tableData', {
    deep: true,
    immediate: true,
  })
  onTableDataChange(val: ISubjectRateData[]) {
    this.renderChart();
  }

  beforeDestroy() {
    if (this.subjectRateChart) {
      this.subjectRateChart.dispose();
      this.subjectRateChart = null;
    }
  }

  // 渲染图表
  async renderChart() {
    if (this.subjectRateChart) {
      this.subjectRateChart.dispose();
      this.subjectRateChart = null;
    }
    let data: ISubjectRateData[] = this.$deepClone(this.tableData) as ISubjectRateData[];
    if (data.length) {
      data = data.filter(item => item.subjectName !== '总分');
      this.isNoData = false;
    } else {
      this.isNoData = true;
      return;
    }
    await this.$nextTick();
    const subjectNames = data.map(item => item.subjectName);
    this.subjectRateChart = this.$echarts.init(this.$refs.subjectRateChart);
    let option: EChartsOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
        },
        formatter: (params, ticket) => {
          params.reverse();
          let html = '';
          let classHtml = `<div style="font-size:14px;color:#666;font-weight:400;line-height:1.25;">${params[0].name}</div>`;
          let obj = this.tableData.find(item => {
            return params[0].name == item.subjectName;
          });
          let teaHtml = `<div style="font-size:14px;color:#666;font-weight:400;line-height:1.25;margin-top: 5px">${
            this.filterData?.classId == '' ? '学科组长' : '任课老师'
          }：${obj?.teaName || '--'}</div>`;
          html = classHtml + teaHtml;

          params.forEach(item => {
            html += `<div class="clearfix" style="margin-top: 5px">${item.marker}<span>${item.seriesName}</span><span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${item.value}</span></div>`;
          });
          return html;
        },
      },

      color: ['#C6C9CC', '#FF6A68', '#FFB400', '#3E73F6', '#07C29D'],
      legend: {
        icon: 'circle',
        top: 10,
        right: 40,
        textStyle: {
          color: '#757C8C',
          fontSize: '14',
        },
        data: ['优秀率', '优良率', '及格率', '不及格率', '低分率'],
      },
      grid: {
        left: '3%',
        right: '6%',
        bottom: '9%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        name: '学科',
        axisLabel: {
          interval: 0,
          rotate: 30,
          margin: 20,
        },
        axisLine: {
          lineStyle: {
            color: '#757C8C',
          },
        },
        data: subjectNames,
      },
      yAxis: {
        type: 'value',
        name: '百分比',
      },
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 0,
          end: 100,
        },
      ],
      series: [
        {
          name: '优秀率',
          type: 'bar',
          stack: '百分比',
          label: {
            show: true,
            position: 'left',
          },
          barMaxWidth: 60,
          barMinWidth: 40,
          barGap: 20,
          data: data.map(item => item.fineRate),
        },
        {
          name: '优良率',
          type: 'bar',
          stack: '百分比',
          label: {
            show: true,
            position: 'left',
          },
          data: data.map(item => item.goodRate),
        },
        {
          name: '及格率',
          type: 'bar',
          stack: '百分比',
          label: {
            show: true,
            position: 'left',
          },
          data: data.map(item => item.passRate),
        },
        {
          name: '不及格率',
          type: 'bar',
          stack: '百分比',
          label: {
            show: true,
            position: 'left',
          },
          data: data.map(item => item.failRate),
        },
        {
          name: '低分率',
          type: 'bar',
          stack: '百分比',
          label: {
            show: true,
            position: 'left',
            formatter: params => {
              // console.log(params)
              if (!params.value) {
                return '';
              }
              return params.value;
            },
          },
          data: data.map(item => item.lowRate),
        },
      ].reverse() as any,
    };

    this.subjectRateChart.setOption(option);
  }
}
</script>

<style scoped></style>

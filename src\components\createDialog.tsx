import Vue from 'vue'
import { v1 as uuid } from 'uuid';

// 对话框参数
interface CreateDialogOptions {
  component: any
  store?: any
  router?: any
  title?: string
  header?: string
  dialogId?: string
  // 插入dom的id
  appendToId?: string
  // 显示遮罩 - true
  showModal?: boolean
  // 设定尺寸
  size?: 'large' | 'middle' | 'small'
  // 是否居中
  center?: boolean
  // 为el-dialog-body添加class
  bodyClass?: string
  // 自定义class
  customClass?: string
  // 是否插入到body:true
  appendToBody?: boolean
  // 是否显示关闭：true
  showColse?: boolean
  // 是否全屏
  fullscreen?: boolean
  closeOnClickModal?: boolean
  closeOnPressEscape?: boolean
  width?: string
  height?: string
  data?: any
  // 关闭弹窗
  close?: Function
  // 确认弹窗
  confirm?: Function
  // 关闭弹窗前
  beforeClose?: Function
  // 自定义事件回调
  change?: Function
}

export default function createDialog(option: CreateDialogOptions) {
  var dom = document.createElement('div');
  let isModal = true;
  if (option.appendToBody) {
    document.getElementsByTagName('body')[0].appendChild(dom);
  } else {
    document.getElementById(option.appendToId || 'rootBody').appendChild(dom);
  }
  if (typeof (option.showModal) !== "undefined") {
    isModal = option.showModal
  }

  let cuuid = uuid();
  let vue = new Vue({
    el: dom,
    data: function () {
      return {
        title: option.title,
        header: option.header,
        dialogId: option.dialogId,
        size: option.size || 'small',
        center: option.center,
        bodyClass: option.bodyClass,
        customClass: option.customClass,
        appendToBody: option.appendToBody,
        showColse: option.showColse,
        fullscreen: option.fullscreen,
        closeOnClickModal: option.closeOnClickModal,
        closeOnPressEscape: option.closeOnPressEscape,
        width: option.width,
        dialogData: option.data,
        show: false,
        isClosed: false,
        change: option.change || function () { },
      };
    },
    store: option.store,
    router: option.router,
    render(h) {
      const self = this;

      if (!self.show) {
        return
      }
      let dialogContent = h('dialogContent', {
        ref: "elementDialog",
        attrs: {
          vModel: self.dialogData,
        },
        on: {
          close: self.closeDialog,
          confirm: self.confirmDialog,
          change: self.change,
        }
      })

      if (!self.show) {
        dialogContent = null;
      }
      let slotHeader = h('span', {
        ref: "elementDialog",
        slot: 'title',
        attrs: {
          vModel: self.dialogData,
          domPropsInnerHTML: self.header
        },
        on: {
          close: self.closeDialog,
          confirm: self.confirmDialog,
        }
      })
      if (!self.header) slotHeader = null;

      return h('el-dialog', {
        class: self.bodyClass,
        attrs: {
          'id': cuuid,
          'width': self.width,
          'visible': self.show,
          'title': self.title,
          'size': self.size,
          'modal': isModal,
          'center': self.center,
          'fullscreen': self.fullscreen,
          'close-on-click-modal': self.closeOnClickModal,
          'close-on-press-escape': self.closeOnPressEscape,
          'custom-class': `${self.customClass} ${self.sizeCLass}`,
          'append-to-body': self.appendToBody,
          'modal-append-to-body': self.appendToBody,
          'show-close': self.showColse,
          'destroy-on-close': true,
        },
        on: {
          close: self.close,
          closed: self.closed,
        },
      }, [slotHeader, dialogContent])
    },
    computed: {
      sizeCLass() {
        return `el-dialog--width-${option.size || 'auto'}`;
      }
    },
    mounted() {
      this.show = true;
    },
    methods: {
      close() {
        if (option.beforeClose) {
          option.beforeClose(false, this.closeDialog);
          return
        }

        option.close && option.close();
        this.closeDialog()
      },
      closed() {
        let lastDrawer = document.getElementById(cuuid);
        if (lastDrawer) lastDrawer.parentNode.removeChild(lastDrawer);
      },
      closeDialog() {
        this.isClosed = true;
        this.show = false;
      },
      confirmDialog(result) {
        if (option.beforeClose) {
          option.beforeClose(true, this.closeDialog);
          return
        }

        option.confirm && option.confirm(result)
        this.closeDialog()
      }
    },
    components: {
      dialogContent: option.component,
    },
  });

  return vue;
}

<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-03-25 10:27:44
 * @LastEditors: 小圆
-->
<template>
  <div class="header-item">
    <span class="header-item__label">班级：</span>
    <el-select
      class="header-item__select short-select"
      value-key="id"
      :value="filterInfo.classInfo"
      @change="onChange"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <el-option
        v-for="item in selectOption.classList"
        :key="item.id"
        :title="item.class_name"
        :label="item.class_name"
        :value="item"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import ReportComponent from './ReportComponent';
import { IClassInfo } from '@/pages/studyReport/plugins/types';

@Component
export default class ReportClassInfoSelect extends ReportComponent {
  async onChange(value: IClassInfo) {
    await this.FilterModule.setClassInfo(value);
    this.FilterModule.trigger('changeFilter');
  }
}
</script>

<style scoped lang="scss">
@import './reportSelect.scss';
</style>

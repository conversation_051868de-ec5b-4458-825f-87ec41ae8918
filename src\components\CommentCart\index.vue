<!--
 * @Description: 讲评购车
 * @Author: qmzhang
 * @Date: 2024-02-21 09:22:03
 * @LastEditTime: 2024-02-21 15:32:18
 * @FilePath: \personal-bigdata\src\components\CommentCart\index.vue
-->
<template>
    <div class="addComment animated fadeInUpBig" ref="dragBlock"
        :style="{ top: dragPos.top + 'px', left: dragPos.left + 'px' }">
        <v-touch class="comment-number" :pan-options="{ directions: 'all', threshold: 0 }" @panstart="elementPanStart"
            @panmove="elementPanMove" @panend="elementPanEnd">{{ totalLength }}</v-touch>
        <ul class="comment-edit list-none noselect">
            <li class="click-element" @click="$emit('change', 'addAll')">全部选入</li>
            <li class="click-element" :class="{ 'disable': !totalLength }" @click="$emit('change', 'startComment')">讲评</li>
            <li class="click-element" :class="{ 'disable': !totalLength }" @click="$emit('change', 'clearAll')">清空</li>
        </ul>
    </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component
export default class CommentCart extends Vue {
    @Prop({ default: 0 }) initTop: number;
    @Prop({ default: 0 }) initLeft: number;
    @Prop({ default: 0 }) total: number;

    // 拖动中的标签
    dragPos = {
        top: 0,
        left: 0,
    }
    // 元素平移缓存值
    deltaCache = {
        x: 0,
        y: 0,
    }
    totalLength: number = 0
    // 判断是否拖出边界时候需要取值的屏幕宽高和拖动块的宽高数据对象
    computeObj: any = {}

    created() {
        this.dragPos.top = this.initTop;
        this.dragPos.left = this.initLeft;
        this.deltaCache.y = this.initTop;
        this.deltaCache.x = this.initLeft;
        this.totalLength = this.total;
    }

    updateTotal(total: number) {
        this.totalLength = total;
    }

    // 拖动开始
    elementPanStart(ev: any) {
        this.panResponse(ev.deltaX, ev.deltaY);
    }

    // 拖动中
    elementPanMove(ev: any) {
        this.panResponse(ev.deltaX, ev.deltaY);
    }

    // 拖动结束
    elementPanEnd(ev: any) {
        this.deltaCache.x = ev.deltaX + this.deltaCache.x;
        this.deltaCache.y = ev.deltaY + this.deltaCache.y;
    }

    // 响应元素平移
    panResponse(deltaX: number, deltaY: number) {
        let posX = deltaX + this.deltaCache.x,
            posY = deltaY + this.deltaCache.y;
        this.limitPos(posX, posY);
    }

    // 判断拖动块是否超出边界
    limitPos(posX: number, posY: number) {
        let winWidth = this.computeObj.winWidth,
            winHeight = this.computeObj.winHeight,
            dragWidth = this.computeObj.dragWidth,
            dragHeight = this.computeObj.dragHeight;

        posX = posX < 0 ? 0 : posX;
        posX = posX > winWidth - dragWidth ? winWidth - dragWidth : posX;
        posY = posY < 0 ? 0 : posY;
        posY = posY > winHeight - dragHeight ? winHeight - dragHeight : posY;
        this.dragPos.left = posX;
        this.dragPos.top = posY;
    }

}
</script>

<style lang="scss" scoped>
.addComment {
    display: none;
    position: fixed;
    right: 255px;
    bottom: 50px;
    z-index: 1;
    background: url("../../assets/add_comment.png") center center no-repeat;
    width: 100px;
    height: 270px;

    .comment-number {
        width: 100px;
        height: 150px;
        padding-top: 50px;
        font-size: 48px;
        color: #994e2cff;
        text-align: center;
        line-height: 90px;
    }

    .comment-edit {
        >li {
            display: block;
            position: relative;
            height: 38px;
            line-height: 38px;
            color: #fff;
            font-size: 18px;
            text-align: center;
            font-weight: bold;
            cursor: pointer;

            &:after {
                content: "";
                position: absolute;
                width: 50px;
                height: 1px;
                background: #ffdbabff;
                left: 25px;
                bottom: 0;
            }

            &:last-child:after {
                content: none;
            }

            &.disable{
                pointer-events: none;
                opacity: 0.6;
            }
        }
    }
}
</style>
<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-06-25 11:48:20
 * @LastEditors: 小圆
-->
<template>
  <el-dialog :visible.sync="dialogVisible" :title="'进度'" v-bind="$attrs" v-on="$listeners">
    <div v-if="list.length">
      <el-row v-for="(item, index) in list" :key="index" class="download-row" :gutter="20">
        <el-col class="download-col name-col" :span="15"
          ><span :title="item.title">{{ item.index }}、{{ item.title }}</span></el-col
        >
        <el-col class="download-col date-col" :span="5"
          ><span>{{ $moment(item.modifyTime).format('YYYY.MM.DD HH:mm') }}</span></el-col
        >
        <el-col class="download-col state-col" :span="4"
          ><a
            v-if="item.state"
            target="_blank"
            :class="{ 'is-click': !!item.isClick }"
            :href="item.downloadUrl"
            :download="item.title"
            @click="download(item)"
            >下载</a
          >
          <span v-else> 生成中 </span>
        </el-col>
      </el-row>
      <!--分页器-->
      <el-pagination
        background
        v-show="list.length"
        style="margin: 15px auto"
        class="text-center"
        layout="total, prev, pager, next"
        @current-change="onPageChange"
        :current-page.sync="pagination.page"
        :page-size="pagination.limit"
        :total="pagination.total"
      >
      </el-pagination>
    </div>
    <no-data v-else></no-data>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { getDownloadPaperProgressList } from '@/service/eReport';
import moment from 'moment';
import NoData from '@/components/noData.vue';

interface IDownloadPaperProgress {
  rowNum: number;
  id: number;
  schoolId: string;
  userId: string;
  title: string;
  sourceId: number;
  state: number;
  downloadUrl: string;
  taskId: string;
  isDelete: number;
  createTime: number;
  modifyTime: number;
  index?: number;
  isClick?: boolean;
}

@Component({
  components: {
    NoData,
  },
})
export default class DownloadDialog extends Vue {
  $moment: typeof moment = moment;
  // 弹窗显示
  dialogVisible: boolean = true;
  // 数据列表
  list: IDownloadPaperProgress[] = [];
  // 分页器
  pagination = {
    page: 1,
    limit: 10,
    total: 0,
  };
  // 定时器
  timer: number = null;

  mounted() {
    this.getDownloadPaperProgressList();
  }

  beforeDestroy() {
    clearInterval(this.timer);
  }

  onPageChange(val) {
    this.pagination.page = val;
    this.getDownloadPaperProgressList();
  }

  async getDownloadPaperProgressList(isPoll = false) {
    const params = {
      schoolId: this.$sessionSave.get('schoolInfo').id,
      userId: this.$sessionSave.get('loginInfo').id,
      page: this.pagination.page,
      limit: this.pagination.limit,
    };
    const res = await getDownloadPaperProgressList(params);
    if (isPoll) {
      this.list.length = res.data.rows.length;
      for (let i = 0; i < res.data.rows.length; i++) {
        let item = res.data.rows[i];
        if (!this.list[i]) (this.list[i] as any) = {};
        for (const key in item) {
          if (Object.prototype.hasOwnProperty.call(item, key)) {
            const element = item[key];
            this.$set(this.list[i], key, element);
          }
        }
      }
    } else {
      this.list = res.data.rows;
      this.pagination.total = res.data.total_rows;
      clearInterval(this.timer);
      this.timer = setInterval(() => {
        this.getDownloadPaperProgressList(true);
      }, 6000);
    }

    this.list.forEach((item, i) => {
      item.index = (this.pagination.page - 1) * this.pagination.limit + (i + 1);
    });
  }

  download(item: IDownloadPaperProgress) {
    this.$set(item, 'isClick', true);
  }
}
</script>

<style scoped lang="scss">
.is-click {
  opacity: 0.8;
}

.download-row {
  font-size: 16px;
  color: #606266;
  line-height: 28px;
  margin-top: 10px;

  .download-col {
  }

  .name-col {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .date-col {
  }
  .state-col {
  }
}
</style>

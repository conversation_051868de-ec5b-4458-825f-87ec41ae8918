import { FilterDataKey } from '@/pages/studyReport/plugins/types';
import { menuOption } from '../types';

export const Custom_Option_Map: Map<string, menuOption> = new Map([
  [
    'stuHistoryExamResults',
    {
      title: 'G1-学生历次考试成绩',
      path: 'stuHistoryExamResults',
      name: 'stuHistoryExamResults',
      filters: ['schoolId', 'examId', 'gradeId', 'classInfo', 'stuId'],
      component: () => import('@/pages/studyReport/custom/stuHistoryExamResults.vue'),
    },
  ],
  [
    'singleSubjectQuesSummary',
    {
      title: 'G2-单科小题成绩汇总',
      path: 'singleSubjectQuesSummary',
      name: 'singleSubjectQuesSummary',
      filters: ['schoolId', 'examId', 'gradeId', 'subjectId', 'classList'],
      component: () => import('@/pages/studyReport/custom/singleSubjectQuesSummary.vue'),
    },
  ],
  [
    'classAvgScore',
    {
      title: 'G3-班级平均分',
      path: 'classAvgScore',
      name: 'classAvgScore',
      filters: ['schoolId', 'examId', 'gradeId'],
      component: () => import('@/pages/studyReport/custom/classAvgScore.vue'),
    },
  ],
  [
    'classTypeStats',
    {
      title: 'G4-班级分类统计',
      path: 'classTypeStats',
      name: 'classTypeStats',
      filters: ['schoolId', 'examId', 'gradeId'],
      component: () => import('@/pages/studyReport/custom/classTypeStats.vue'),
    },
  ],
  [
    'gradeMaxScore',
    {
      title: 'G5-年级最高分列表',
      path: 'gradeMaxScore',
      name: 'gradeMaxScore',
      filters: ['schoolId', 'examId', 'gradeId'],
      component: () => import('@/pages/studyReport/custom/gradeMaxScore.vue'),
    },
  ],
  [
    'topNumStats',
    {
      title: 'G6-尖端生人数统计',
      path: 'topNumStats',
      name: 'topNumStats',
      filters: ['schoolId', 'examId', 'contrastExamId', 'gradeId', 'limit'],
      component: () => import('@/pages/studyReport/custom/topNumStats.vue'),
    },
  ],
  [
    'allScoreRankCompare',
    {
      title: 'G7-总分分段统计',
      path: 'allScoreRankCompare',
      name: 'allScoreRankCompare',
      filters: ['schoolId', 'examId', 'contrastExamId', 'gradeId'],
      component: () => import('@/pages/studyReport/custom/allScoreRankCompare.vue'),
    },
  ],
  // [
  //   'scoreAnalysis',
  //   {
  //     title: 'G11-成绩统计分析表',
  //     path: 'scoreAnalysis',
  //     name: 'scoreAnalysis',
  //     component: () => import('@/pages/studyReport/custom/index.vue'),
  //   },
  // ],
  // [
  //   'scoreAnalysisAll',
  //   {
  //     title: 'G12-成绩统计分析表-全区',
  //     path: 'scoreAnalysisAll',
  //     name: 'scoreAnalysisAll',
  //     component: () => import('@/pages/studyReport/custom/index.vue'),
  //   },
  // ],
  // [
  //   'classIndex',
  //   {
  //     title: 'G14-各班的综合指标',
  //     path: 'classIndex',
  //     name: 'classIndex',
  //     component: () => import('@/pages/studyReport/custom/index.vue'),
  //   },
  // ],
  [
    'subsectionAvgScore',
    {
      title: 'G15-分段均分',
      path: 'subsectionAvgScore',
      name: 'subsectionAvgScore',
      filters: ['schoolId', 'examId', 'gradeId', 'classList', 'startRank', 'endRank', 'step'],
      component: () => import('@/pages/studyReport/custom/subsectionAvgScore.vue'),
    },
  ],
  // [
  //   'subjectQuery',
  //   {
  //     title: 'G16-学科综合查询',
  //     path: 'subjectQuery',
  //     name: 'subjectQuery',
  //     component: () => import('@/pages/studyReport/custom/index.vue'),
  //   },
  // ],
  // [
  //   'levelCompare',
  //   {
  //     title: 'G17-等级对照',
  //     path: 'levelCompare',
  //     name: 'levelCompare',
  //     component: () => import('@/pages/studyReport/custom/index.vue'),
  //   },
  // ],
  // [
  //   'classCompare',
  //   {
  //     title: 'G19-班级对照',
  //     path: 'classCompare',
  //     name: 'classCompare',
  //     component: () => import('@/pages/studyReport/custom/index.vue'),
  //   },
  // ],
  [
    'scoreAnalysis',
    {
      title: 'G21-成绩统计分析',
      path: 'scoreAnalysis',
      name: 'scoreAnalysis',
      filters: ['schoolId', 'gradeId', 'examId'],
      component: () => import('@/pages/studyReport/custom/scoreAnalysis.vue'),
    },
  ],
  [
    'classOnePointTwoRates',
    {
      title: 'G24-班级一分两率',
      path: 'classOnePointTwoRates',
      name: 'classOnePointTwoRates',
      filters: ['schoolId', 'gradeId', 'subjectId', 'examList'],
      component: () => import('@/pages/studyReport/custom/classOnePointTwoRates.vue'),
    },
  ],
  // [
  //   'levelGroup',
  //   {
  //     title: 'G25-等级组合统计',
  //     path: 'levelGroup',
  //     name: 'levelGroup',
  //     component: () => import('@/pages/studyReport/custom/index.vue'),
  //   },
  // ],
  // [
  //   'historyTest',
  //   {
  //     title: 'G26-历次考试检测',
  //     path: 'historyTest',
  //     name: 'historyTest',
  //     component: () => import('@/pages/studyReport/custom/index.vue'),
  //   },
  // ],
  // [
  //   'teacherScore',
  //   {
  //     title: 'G28-教师积分',
  //     path: 'teacherScore',
  //     name: 'teacherScore',
  //     component: () => import('@/pages/studyReport/custom/index.vue'),
  //   },
  // ],
  // [
  //   'schoolScore',
  //   {
  //     title: 'G29-学校综合得分',
  //     path: 'schoolScore',
  //     name: 'schoolScore',
  //     component: () => import('@/pages/studyReport/custom/index.vue'),
  //   },
  // ],
  // [
  //   'rankDynamic',
  //   {
  //     title: 'G30-名次分布动态',
  //     path: 'rankDynamic',
  //     name: 'rankDynamic',
  //     component: () => import('@/pages/studyReport/custom/index.vue'),
  //   },
  // ],
  // [
  //   'questionTypeDiagnosis',
  //   {
  //     title: 'G31-题型诊断表',
  //     path: 'questionTypeDiagnosis',
  //     name: 'questionTypeDiagnosis',
  //     component: () => import('@/pages/studyReport/custom/index.vue'),
  //   },
  // ],
] as [string, menuOption][]);

const Custom_Option_Value = Array.from(Custom_Option_Map.values());
Custom_Option_Value.forEach(item => {
  item.meta ? (item.meta.title = item.title) : (item.meta = { title: item.title });
});

export const Custom_Menu = Array.from(Custom_Option_Map.values());

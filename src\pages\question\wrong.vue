<template>
  <div>
    <div class="teachingPage-header">
      <!-- <div class="header__select">
        <span class="select__label">来源：</span>
        <el-select
          v-model="sourceVal"
          multiple
          @change="changeSource"
          collapse-tags
          style="width: 140px"
          class="source-select"
          placeholder="请选择"
        >
          <el-option v-for="item in sourceList" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </div> -->
      <!--时间-->
      <div class="header__select">
        <span class="select__label">时间：</span>
        <el-select
          v-model="timeValue"
          style="width: 100px; margin-right: 5px"
          @change="changeTimeValue()"
        >
          <el-option
            v-for="item in timeType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <!-- 学年 -->
        <el-select
          v-model="yearTimeSlot"
          v-if="timeValue === 3"
          style="width: 230px"
          @change="changeYearValue()"
        >
          <el-option
            v-for="item in yearList"
            :key="item.id"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <!-- 按月 -->
        <el-date-picker
          v-if="timeValue === 2"
          popper-class="datePicker__time"
          style="width: 230px"
          v-model="timeSlot"
          :clearable="false"
          type="monthrange"
          align="right"
          range-separator="--"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          @change="beforeFindPersonalBookListMonth"
          :picker-options="pickerOptions"
        >
        </el-date-picker>
        <!-- 按日 -->
        <el-date-picker
          v-if="timeValue === 1"
          popper-class="datePicker__time"
          style="width: 230px"
          v-model="timeSlot"
          :clearable="false"
          type="daterange"
          align="right"
          unlink-panels
          format="yyyy-MM-dd"
          range-separator=" -- "
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="getClassList"
          :picker-options="pickerOptionsDay"
        >
        </el-date-picker>
      </div>
      <!-- 年级 -->
      <div class="header__select">
        <span class="select__label">年级：</span>
        <el-select
          v-model="gradeValue"
          style="width: 120px"
          class="source-select"
          @change="changeGrade"
          placeholder="请选择"
        >
          <el-option v-for="item in grdList" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </div>
       <!--学科-->
       <div class="header__select">
        <span class="select__label">学科：</span>
        <el-select
          v-model="subjectVal"
          style="width: 140px"
          class="source-select"
          @change="changeSubject"
          placeholder="请选择"
        >
          <el-option v-for="item in subjectList" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </div>
      <!-- 班级 -->
      <div class="header__select">
        <span class="select__label">班级：</span>
        <el-select
          v-model="classValue"
          style="width: 120px"
          class="source-select"
          @change="changeClass"
          placeholder="请选择"
        >
          <el-option
            v-for="item in classList"
            :key="item.clsId"
            :label="item.clsName"
            :value="item.clsId"
          >
          </el-option>
        </el-select>
      </div>
     
      <!-- 得分率 -->
      <div class="header__select wrong-score-range">
        <span class="select__label">得分率：</span>
        <NumericInput
          class="wrong-scoreInput flex_1"
          v-model="rateParam[0]"
          :type="'digit'"
          :isPositive="true"
        ></NumericInput>
        <span class="gapline">-</span>
        <NumericInput
          class="wrong-scoreInput flex_1"
          v-model="rateParam[1]"
          :type="'digit'"
          :isPositive="true"
        ></NumericInput>
      </div>
      <!-- 查询按钮 -->
      <div class="header__select">
        <el-button type="primary" @click="findData">查询</el-button>
      </div>
    </div>
    <div class="block class-mistack clearfix">
      题型：
      <el-checkbox style="display: inline-block;margin-right: 30px;" v-model="isCheckAll" @change="changeAll">全选</el-checkbox>
      <el-checkbox-group style="display: inline-block;" v-model="choiceTypeList" @change="changeChoiceType">
        <el-checkbox label="8">单选题</el-checkbox>
        <el-checkbox label="1">多选题</el-checkbox>
        <el-checkbox label="2">判断题</el-checkbox>
        <el-checkbox label="3">填空题</el-checkbox>
        <el-checkbox label="6">简答题</el-checkbox>
      </el-checkbox-group>
      <clsWrong @getParams="getParams" ref="clsWrong" :type="'wrongques'" :classId="classValue"></clsWrong>
    </div>
  </div>
</template>

<script>
import { ClassList } from '@/service/pexam';
import { getToken } from '@/service/auth';
import UserRole from '@/utils/UserRole';
import { mapGetters } from 'vuex';
import clsWrong from '@/components/paper/clsWrong.vue';
import NumericInput from '@/components/NumericInput';

// 当前日期
let today = new Date();
// 当前月
// 当前年
let nowYear = today.getYear();
nowYear += nowYear < 2000 ? 1900 : 0;
export default {
  components: { clsWrong, NumericInput },
  name: 'index',
  data() {
    return {
      //时间筛选类型
      timeType: [
        { value: 1, label: '按日' },
        { value: 2, label: '按月' },
        { value: 3, label: '学年' },
      ],
      timeValue: 3,
      //学年
      yearList: [],
      sourceVal: [1,2,3],
      sourceList: [
        { id: 1, name: '考试' },
        { id: 2, name: '个册' },
        { id: 3, name: '作业' },
      ],
      // 选择的具体时间数值
      defTimeSlot: [],
      defMonthSlot: [],
      timeSlot: [],
      yearTimeSlot: '',
      pickerMinDate: '', //第一次选中的时间
      pickerOptions: {
        //选择当前日期之前的时间
        onPick: obj => {
          this.pickerMinDate = new Date(obj.minDate).getTime();
        },
        //选择时间范围为一年
        disabledDate: time => {
          if (this.pickerMinDate) {
            const day1 = 364 * 24 * 3600 * 1000;
            let maxTime = this.pickerMinDate + day1;
            let minTime = this.pickerMinDate - day1;
            return (
              time.getTime() > Date.now() || time.getTime() > maxTime || time.getTime() < minTime
            );
          }
        },
      },
      pickerOptionsDay: {
        //选择当前日期之前的时间
        onPick: obj => {
          this.pickerMinDate = new Date(obj.minDate).getTime();
        },
        //可选择的时间范围为一年
        disabledDate: time => {
          if (this.pickerMinDate) {
            const day1 = 365 * 24 * 3600 * 1000;
            let maxTime = this.pickerMinDate + day1;
            let minTime = this.pickerMinDate - day1;
            return (
              time.getTime() > Date.now() || time.getTime() > maxTime || time.getTime() < minTime
            );
          }
        },
      },
      grdList: [],
      allGradeList: [],
      subjectVal: '',
      gradeValue: '',
      classList: [],
      classValue: '',
      pms: {},
      //班级名
      className: '',
      rateParam: [0, 60],
      isCheckAll:true,
      allTypeList:['1','2','3','6','8'],
      choiceTypeList:[]
    };
  },
  watch: {
    yearTimeSlot: function (v) {
      this.timeSlot = v.split('|');
    },
  },
  computed: {
    // ...mapGetters(['filterSubjectList', 'subjectMap', 'gradeList', 'loginInfo']),
    ...mapGetters(['filterSubjectList', 'loginInfo']),
    // 对选择后的时间格式化
    timeSlotFormat() {
      return [
        this.timeSlot[0] ? this.$formatDate(new Date(this.timeSlot[0])) : '',
        this.timeSlot[0] ? this.$formatDate(new Date(this.timeSlot[1])) : '',
      ];
    },
    phaseId() {
      let phaseId = '';
      if (this.gradeValue) {
        phaseId = this.grdList.filter(item => {
          return item.id == this.gradeValue;
        })[0].phaseId;
      }
      if (!this.gradeValue && this.subjectId) {
        let list = this.filterSubjectList.filter(item => {
          return item.id === this.subjectId;
        });
        if (list.length) {
          phaseId = list[0].phaseId;
        }
      }
      return phaseId;
    },
    subjectLists() {
      let allSubjectList = this.$localSave.get('SUBJECT_LIST');
      let tempList = [];
      if (this.loginInfo.multiple_phase && this.loginInfo.multiple_phase != '') {
        tempList = allSubjectList.filter(item => {
          return this.loginInfo.multiple_phase.indexOf(item.phase - 2) >= 0;
        });
      } else if (this.loginInfo.phase && this.loginInfo.phase != '') {
        tempList = allSubjectList.filter(item => {
          return this.loginInfo.phase.indexOf(item.phase - 2) >= 0;
        });
      } else {
        tempList = allSubjectList;
      }
      // tempList.unshift({
      //   id: '',
      //   name: '全部',
      //   subjectId: '',
      //   phaseId: '',
      // });
      return tempList;
    },
    subjectList() {
      let subList = [];
      if (this.phaseId && this.gradeValue) {
        subList = this.subjectLists.filter(item => {
          return item.phaseId === this.phaseId || item.name === '全部';
        });

        this.subjectId = subList[0].id;
      } else {
        subList = this.subjectLists;
      }
      return subList;
    },
  },
  async mounted() {
    await this.initSchool();
    this.findData();
  },
  methods: {
    /**
     * @name:初始化数据
     */
    async initSchool(cb) {
      let $this = this;
      // this.matchingRoutes();
      // if ($this.subjectList.length > 0) {
      //   if (cb) {
      //     cb.call($this);
      //   }
      //   return;
      // }
      const ret = await UserRole.getUserInfoPersonalityTest();
      $this.allGradeList = ret.schGrdList;
      $this.grdList = ret.schGrdList;
      $this.gradeValue = $this.grdList[0].id;
      this.choiceTypeList = this.allTypeList;
      $this.init();
      await $this.getClassList();
    },
    init() {
      let $this = this;
      let sub = this.$localSave.get('SUBJECT_LIST').find(it => it.id == $this.loginInfo.subjectid);
      let date = new Date(),
        y = date.getFullYear(),
        M = date.getMonth(),
        d = date.getDate();
      let yearList = [];
      for (let i = 0; i < 5; i++) {
        let y1 = y - i - 1;
        let y2 = y - i;
        if (Number(`${M + 1}${d < 10 ? `0${d}` : d}`) >= 810) {
          y1 = y - i;
          y2 = y - i + 1;
        }
        let max = `${y2}-08-09`;
        if (i == 0) {
          max = `${y}-${M < 9 ? `0${M + 1}` : M + 1}-${d < 10 ? `0${d}` : d}`;
        }
        yearList.push({
          id: i,
          value: `${y1}-08-10|${max}`,
          label: `${y1}-${y2}学年`,
        });
      }
      $this.yearList = yearList;
      $this.timeValue = 3;
      $this.yearTimeSlot = $this.yearList[0].value;
      $this.timeSlot = $this.yearTimeSlot.split('|');
      if ($this.subjectList.length > 0) {
        if (sub && sub.id) {
          let subjectItem = $this.subjectList.find(item => item.id == sub.id);
          if (subjectItem) {
            $this.subjectVal = subjectItem.id;
          } 
        } 
        if(!$this.subjectVal) $this.subjectVal = $this.subjectList[0].id;
      }
      $this.defMonthSlot = [new Date(y, M), new Date(y, M, d)];
    },
    changeAll(){
      if(this.isCheckAll){
        this.choiceTypeList = this.allTypeList;
      }else{
        this.choiceTypeList = [];
      }
      this.findData();
    },
    changeChoiceType(){
      if(this.choiceTypeList.length == this.allTypeList.length){
        this.isCheckAll = true;
      }else{
        this.isCheckAll = false;
      }
      this.findData();
    },
    changeYearValue() {
      setTimeout(() => {
        this.getClassList();
      }, 100);
    },
    // 切换来源
    changeSource() {},
    //切换时间筛选类型，按日默认最近一周，按月默认当前月
    changeTimeValue() {
      this.pickerMinDate = '';
      let date = new Date(),
        y = date.getFullYear(),
        M = date.getMonth(),
        d = date.getDate();
      if (this.timeValue === 1) {
        this.timeSlot = [new Date(y, M, d - 7), new Date(y, M, d)];
      } else if (this.timeValue === 2) {
        this.timeSlot = this.defMonthSlot;
      } else {
        this.yearTimeSlot = this.yearList[0].value;
        this.timeSlot = this.yearTimeSlot.split('|');
      }
      this.getClassList();
    },
    /**
     * @name：切换年级
     */
    changeGrade() {
      if (this.subjectList.length > 0) {
        this.subjectVal = this.subjectList[0].id;
      }
      this.getClassList();
    },
    /**
     * @name:切换班级
     */
    changeClass() {
      this.className = this.classList.filter(item => {
        return item.clsId == this.classValue;
      })[0].clsName;
      // this.findData();
    },
    // 切换学科
    changeSubject(val) {
      // let sjt = this.subjectList.find(q => q.id == this.subjectVal);
      // this.grdList = this.allGradeList.filter(q => q.phaseId == sjt.phaseId);
      // this.gradeValue = this.grdList[0].id;
      this.getClassList();
    },

    /**
     * @name:获取班级
     * @param {*} subjectId  学科id
     * @param {*} gradeId 年级Id
     * @param {*} startTime  开始时间
     * @param {*} endTime 结束时间
     */
    async getClassList() {
      this.classValue = '';
      this.className = '';
      let token = getToken();
      const data = await ClassList({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        subjectId: this.subjectVal || '',
        gradeId: this.gradeValue || '',
        token: token,
        startTime: this.timeSlotFormat[0],
        endTime: this.timeSlotFormat[1],
      });
      const classList = data.data;
      // 校管、运营、年级主任获取全部学科
      classList.unshift({
        clsId: '',
        clsName: '全部',
      })
      this.classList = classList;
      if (this.classList.length > 1) {
        this.classValue = this.classList[1].clsId;
        this.className = this.classList[1].clsName;
      } else {
        this.classValue = this.classList[0].clsId;
        this.className = this.classList[0].clsName;
      }
      // this.findData();
    },
    // 选择月份筛选后
    beforeFindPersonalBookListMonth(val) {
      let now = new Date(),
        y = now.getFullYear(),
        M = now.getMonth(),
        d = now.getDate();
      let start = val[0],
        y0 = start.getFullYear(),
        M0 = start.getMonth();
      let end = val[1],
        y1 = end.getFullYear(),
        M1 = end.getMonth();
      if (Number(`${y}${M < 10 ? `0${M}` : M}`) == Number(`${y1}${M1 < 10 ? `0${M1}` : M1}`)) {
        this.timeSlot = [`${y0}-${M0 + 1}-01`, `${y1}-${M + 1}-${d}`];
      } else {
        let e2 = new Date(y1, M1 + 1, 0),
          y2 = e2.getFullYear(),
          M2 = e2.getMonth(),
          d2 = e2.getDate();

        this.timeSlot = [`${y0}-${M0 + 1}-01`, `${y2}-${M2 + 1}-${d2}`];
      }
      this.getClassList();
    },
    findData() {
      // if (!this.classValue) {
      //   this.$message.warning('请选择班级');
      //   return;
      // }
      if (Number(this.rateParam[0]) >= 100) {
        this.$message.error('得分率最小值不能大于等于100');
        return;
      }
      if (Number(this.rateParam[0]) > Number(this.rateParam[1])) {
        this.$message.error('得分率最小值不能大于最大值');
        return;
      }
      if (Number(this.rateParam[0]) > 100 || Number(this.rateParam[1]) > 100) {
        this.$message.error('得分率不能大于100%');
        return;
      }
      this.pms = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        gradeId: this.gradeValue || '',
        classId: this.classValue || '',
        className: this.className || '',
        timeValue: this.timeValue,
        subjectId: this.subjectVal || '',
        startTime: this.timeSlotFormat[0],
        endTime: this.timeSlotFormat[1],
        sourceType: this.sourceVal.length
          ? this.sourceVal.length === 3
            ? ''
            : this.sourceVal.join(',')
          : '',
        quesType: this.choiceTypeList.join(','),
        clsMinRate: this.rateParam[0] / 100,
        clsMaxRate: this.rateParam[1] / 100,
      };
      this.getParams();
      // this.$listeners.savePams(this.pms);
      this.$refs.clsWrong.listErrQues();
    },
    getParams() {
      return this.pms;
    },
  },
};
</script>

<style lang="scss" scoped>
.disFlex {
  display: flex;
  flex-direction: column;
}
.radioGroup {
  margin-bottom: 16px;
}
.teachingPage-header {
  position: relative;
  padding: 0 16px;
  width: 100%;
  background: #fff;
  // box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
  border-radius: 6px;
  // margin-bottom: 16px;
  display: flex;
  // justify-content: space-between;
  .header__select {
    display: inline-block;
    margin-right: 5px;
  }
}
.block {
  width: 100%;
  position: relative;
  padding: 16px;
  background: #fff;
  // box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
  border-radius: 6px;
  .title {
    padding-left: 16px;
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;
    line-height: 38px;
  }
}
</style>
<style lang="scss">
.wrong-score-range {
  padding: 0 10px;
  line-height: 40px;
  .gapline {
    margin: 0 10px;
  }
}
.wrong-scoreInput {
  width: 70px;
  position: relative;
  // margin-right: 26px;
  .numeric-input-inner {
    border-radius: 4px 0 0 4px !important;
    height: 40px !important;
    text-align: unset !important;
    &:focus {
      border-color: #dcdfe6 !important;
    }
  }
  &:after {
    content: '%';
    position: absolute;
    right: 0px;
    top: 0;
    border: 1px solid #dcdfe6;
    border-radius: 0 4px 4px 0;
    width: 26px;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    text-align: center;
    line-height: 40px;
    z-index: 11;
    border-left: none;
  }
}
</style>
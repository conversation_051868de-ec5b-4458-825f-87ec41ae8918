<template>
  <div>
    <div class="small-ques-avg-box">
      <div class="table-box" v-loading="isLoading">
        <SmallQuesAvgTable :tableData="tableData" :clzList="clzList"></SmallQuesAvgTable>
      </div>
      <div
        class="subjects-box ques-list-box"
        v-if="questionGroups.length || tableData.length"
        style="margin-top: 15px"
      >
        <span class="subject-label ft-14 mgr-10">选择题目:</span>
        <el-select
          v-model="currentTQuesNo"
          placeholder="请选择题目"
          v-if="questionGroups.length"
          @change="setCurrentTQuesNo"
        >
          <el-option-group v-for="(group, gi) in questionGroups" :key="gi" :label="group.name">
            <el-option
              v-for="(item, i) in group.data"
              :key="i"
              :label="`${item.quesNos}`"
              :value="item.tQuesNo"
            >
            </el-option>
          </el-option-group>
        </el-select>

        <el-select v-model="currentTQuesNo" placeholder="请选择题目" v-else-if="tableData.length">
          <el-option
            v-for="item in tableData"
            :key="item.tQuesNo"
            :label="`第${item.quesNoDesc}题`"
            :value="item.tQuesNo"
          >
          </el-option>
        </el-select>
      </div>
      <div class="chart-box" v-loading="isLoading" id="pane-chart">
        <SmallQuesAvgChart
          ref="smallQuesAvgChart"
          :tableData="tableData"
          :clzList="clzList"
          :currentQuesNumber="currentQuesNumber"
          :currentTQuesNo="currentTQuesNo"
        ></SmallQuesAvgChart>
      </div>
    </div>
  </div>
</template>

<script>
import SmallQuesAvgChart from '@/components/smallQuesAvg/SmallQuesAvgChart.vue';
import SmallQuesAvgTable from '@/components/smallQuesAvg/SmallQuesAvgTable.vue';
import { getSmallClsQuesAvg, getSmallGrdQuesAvg, listQuesAvg } from '@/service/pexam';
import { getExamTeamQues } from '@/service/testbank';

export default {
  name: 'smallQuesAvg',
  props: ['filterData'],
  components: {
    SmallQuesAvgChart,
    SmallQuesAvgTable,
  },
  data() {
    return {
      isLoading: false,
      // 表格数据
      tableData: [],
      // 当前班级列表
      clzList: [],
      // 题目组
      questionGroups: [],
      // 当前题目编号
      currentQuesNumber: '',
      // 当前题目tQuesNo
      currentTQuesNo: '',
    };
  },
  watch: {
    filterData: {
      immediate: true,
      deep: true,
      async handler(val) {
        this.listQuesAvg();
      },
    },
  },
  async mounted() {},
  beforeDestroy() {
    this.$bus.$off('updateFilter');
  },
  methods: {
    // 获取小题均分
    async listQuesAvg() {
      this.tableData = [];
      const innerClassList = this.$sessionSave.get('innerClassList');
      try {
        this.isLoading = true;
        const res = await listQuesAvg({
          examId: this.$sessionSave.get('reportDetail').examId,
          subjectId: this.filterData.subjectId,
          v: this.$sessionSave.get('reportDetail').v,
        });
        let clzList = res.data.clzList.filter(item => {
          return item.id === '' || innerClassList.find(t => item.id === t.id);
        });
        if (this.filterData.classId) {
          clzList = clzList.filter(item => {
            return item.id === '' || item.id == this.filterData.classId;
          });
        }

        this.tableData = res.data.data;
        this.clzList = clzList;
        this.currentTQuesNo = this.tableData[0].tQuesNo;
        this.getExamTeamQues();
      } catch (error) {
        console.log(error);
        this.tableData = [];
      }
      this.isLoading = false;
    },

    // 获取题目接口
    async getExamTeamQues() {
      try {
        const { data } = await getExamTeamQues({
          schoolId: this.$sessionSave.get('schoolInfo').id,
          examId: this.$sessionSave.get('reportDetail').examId,
          subjectId: this.filterData.subjectId,
        });
        if (data.length) {
          this.questionGroups = data;
          if (data[0].data[0].tQuesNo) this.setCurrentTQuesNo(data[0].data[0].tQuesNo);
        }
      } catch (error) {
        this.questionGroups = [];
        console.log(error);
      }
    },

    // 设置当前题目QuesNo
    setCurrentQuesNo(quesNo) {
      let quesNos = quesNo.split(',');
      this.currentQuesNo = quesNo;
      this.currentQuesNumber = this.tableData.find(item => {
        return quesNos.includes(String(item.quesNumber));
      })?.quesNumber;
    },
    // 设置当前题目TQuesNo
    setCurrentTQuesNo(tQuesNo) {
      this.currentTQuesNo = tQuesNo;
    },

    updateFilter(data) {},
    updateData({ isLoading }) {
      // console.log('updatting', isLoading)
      this.isLoading = isLoading;
    },
    handleClick(tab, event) {
      if (tab.name == 'chart') {
        this.$nextTick(() => {
          this.$refs.smallQuesAvgChart.smallQuesAvgChart.resize();
        });
      } else {
      }
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.getSmallGrdQuesAvg(val);
    },
  },
};
</script>

<style lang="scss" scoped>
.small-ques-avg-box {
  border-radius: 6px;
  padding-bottom: 30px;
  background-color: #fff;
  .pagination-box {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
  }
  .chart-box {
    height: 425px;
    background: #ffffff;
    border: 1px solid #e4e8eb;
    border-radius: 3px;
    margin-top: 20px;
  }

  .table-box {
    border: 1px solid #e4e8eb;
    border-radius: 3px;
    margin-top: 20px;
  }
}
</style>
<style lang="scss">
.small-ques-avg-box {
  .el-tabs {
    .el-tabs__header {
      margin: 0 0 1px 4px;
    }
    .el-tabs__nav-wrap::after {
      width: 160px;
    }
    .el-tabs__item {
      width: 80px;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      &.is-top:last-child {
        padding-left: 0;
      }
      &.is-top:nth-child(2) {
        padding-right: 0;
      }
    }
  }
}
</style>

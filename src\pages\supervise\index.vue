<!--
 * @Descripttion: 教学监管父路由
 * @Author: 小圆
 * @Date: 2023-11-28 16:31:57
 * @LastEditors: 小圆
-->
<template>
  <div class="supervise" v-loading="!isFilterLoad">
    <router-view v-if="isFilterLoad" :queryData="queryData"></router-view>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      // 查询数据
      queryData: null,
      // 是否完成加载
      isFilterLoad: true,
    };
  },

  mounted() {
    this.$bus.on("changeSchool", () => {
      this.isFilterLoad = false;
      this.$nextTick(() => {
        this.isFilterLoad = true;
      });
    });
  },
  destroyed() {
    this.$bus.off("changeSchool");
  },

  methods: {
    handleFilterLoad(val) {
      this.isFilterLoad = val;
    },
  },
};
</script>

<style lang="scss" scoped>
.supervise {
}
</style>

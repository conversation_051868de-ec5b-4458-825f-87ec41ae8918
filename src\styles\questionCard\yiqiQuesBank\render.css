/* Created by <PERSON> in 2013 */
.MathJax_SVG{pointer-events: none}
q,stem,subqs,subq{
    display:block;
}
q{
    /*position: relative;*/
    font-size: 14px;
    font-family: 'Helvetica Neue', Helvetica, Arial,'simsun','Microsoft Yahei','Lantinghei SC';
    color: #333;
}
q:before, q:after, blockquote:before, blockquote:after {
    content: "";
    display: none;
}
q p{
    margin: 0;
}
opt,nn,nng,wn,lwn,rwn,un,sn{
    display:inline-block;
}

stem {
    line-height: 26px;
    display: block;
    word-spacing:3px;
    padding:0 0 5px;
    margin:0;
    word-wrap: break-word;
}

.article-container stem, q>stem{
    margin-bottom: 20px;
}

subqs {
    display:block;
    margin:0;
}
subq {
    display: block;
    margin:0;
    margin-bottom: 5px;
}
subq>input[type=text]{
    margin-bottom: 0;
}
q .leaf-q{
    margin-bottom: 20px;
}
opt {
    color:#585858;
    font-weight: normal;
    line-height: 24px;
    display: inline-block;
    margin:0;
    padding:0;
}
opt label:hover{
    background-color:#ddd;
}
opt input{
    margin:0 10px 0 0 !important;
    vertical-align:middle;
}
opt label{
    color:#585858;
    font-weight:normal;
    padding:0;
    margin:0;
    cursor:pointer;
}
opt p{
    display: inline-block;
    margin: 0;
}
nn{
    font-weight:bold;
    display: inline-block;
    padding:0 5px;
    border-bottom:1px solid #555;
    margin:0 2px;
    line-height:11px;
    min-width: 50px;
}
nng {
    font-weight:bold;
    display: inline-block;
    padding:0 5px;
    border-bottom:1px solid #555;

}
un {
    font-weight:bold;
    border-bottom:1px solid #555;
}
wn,rwn,lwn {
    padding: 0;
    text-align:center;
    display: inline-block;
    border-bottom:1px solid #555;
    line-height:11px;
}
wn {
    padding: 0 25px;
    *line-height:15px;
}
rwn,lwn{
    line-height:25px;
}
rwn{
    float:right;
    height:27px;
    clear:both;
    border-bottom:0px;
    text-align:right;
}
lwn{
    height:27px;
    border-bottom:0px;
    text-align:left;
}
rwn>nn{
    border-bottom:1px solid #555;
}
lwn>nn{
    border-bottom:1px solid #555;
}
sn{
    height:27px;
}
q p.even{
    background-color:#ddd;
}
/*q li{*/
    /*position:relative;*/
/*}*/


q ol {
    margin:0 0 5px 28px;
}
q>ol.display_number{
    margin-left: 9px;
}
q>ol.display_number>li{
    list-style-type:none;
}
q ol ol {
    margin:0 0 5px 20px;
}
q subqs ol>li{
    list-style-type:decimal;
    list-style-position: inside;
}
q ol ol>li{
    list-style-type:upper-roman;
}
q ol ol>li.leaf-q.display_number{
    list-style-type:decimal;
}
q ol ol ol{
    margin:0 0 5px 20px;
}
q ol ol ol>li{
    list-style-type:lower-roman;
}
q ul{
    margin:0 0 5px 0px;
}
.col-xs-6{
    width: 50%
}
.col-xs-3{
    width: 25%
}
.col-xs-4{
    width: 33.3%
}
.col-xs-12{
    width: 100%
}

.markq,.markq stem>p,.markq label {
    color:#fe5656
}
.markq input[type=text]{
    border-color: #fe5656
}
subqs>ol>li>.answer>i{
    margin-top: 5px;
}
.subjective{
    position: static
}
q .audio{
    text-align: center;
}
q .audio>a{
    color: #006A92
}
q .q_stem_overflow{
    position: relative;
    max-height: 320px;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 10px;
}

q .context{
    margin:10px 0 10px -35px;
    font-weight: 600;
}

q .q_subqs_overflow{
    position: relative;
    max-height: 320px;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 10px;
}
.close_q_overflow{
    overflow: visible !important;
    max-height: 100% !important;
}
.item_class_data{
position:relative;
max-height: 450px;
overflow: hidden;
}
.q_overflow{
    height:40px;
    text-align: center;
    display:  none;
}
.template_i .q_overflow{
    display: block;
}
.template_i .line{
    height: 2px;
    position: relative
}
.q_overflow a{
    margin-top: 14px;
}

.template_i .line:before{
    content: '';
    position: absolute;
    bottom:0px;
    left: 0;
    width: 100%;
    height: 10px;
    z-index: 100;
    pointer-events: none;
    background: -webkit-linear-gradient(bottom, #FFF 0%, rgba(255, 255, 255, 0) 100%);
    background: -moz-linear-gradient(bottom, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
    background: -o-linear-gradient(bottom, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
    background: linear-gradient(to top, #FFF 0%, rgba(255, 255, 255, 0) 100%);
}
.auxiliary{
    /*margin-left: -16px;*/
    background-color: #F8F8F8;
}
.auxiliary .diffculty,
.auxiliary .result,
.auxiliary .answer,
.auxiliary .exp,
.auxiliary .q_tags{
    padding: 5px 18px;
}
.auxiliary .result{
    padding: 10px 18px;
    font-family: 'Microsoft Yahei','Helvetica Neue', Helvetica, Arial,'simsun','Lantinghei SC';
    margin-top: 15px;
}
.auxiliary .diffculty ul,
.auxiliary .q_tags ul{
    margin-bottom: 0;
}
.auxiliary .sub_tag{
    margin-left: 60px;
}
.auxiliary .dt{
    float: left;
    display: inline-block;
    /*width: 42px;*/
    text-align: left;
    font-weight: normal;
    font-size: 14px;
}
.user_answer .dd{
    white-space: pre;
    word-break: break-all;
    word-wrap: break-word;
}

.diffculty .dt.search{
    width: 84px;
    display: inline-block;
}

.diffculty .dd.search{
    margin-left: 0px;
    display: inline-block;
}

.diffculty .origin{
    display: inline-block;
    margin-left: 10px;
}

.q_tags .dt.long{
    width: 58px;
    display: inline-block;
}

.q_tags .dt.short{
    width: 44px;
    display: inline-block;
}

.q_tags .dd li{
    display: inline-block;
    font-size: 14px;
}

.q_tags .dd li:first-child{
    margin-left: 0px;
}

.collect_btn,.cancel_collect_btn{
  visibility: hidden;
  position: absolute;
  border: 1px solid #599d41;
  background-color: #FFF;
  color: #599d41;
  padding: 0 13px;
  top: -33px;
  right: -1px;
  font-size: 14px;
  text-align: center;
  height: 33px;
  line-height: 30px;
  border-radius: 0;
  cursor: pointer;
  display: inline-block;
  width: 84px;
  z-index: 200;
  border-top-right-radius: 4px;
}

.collect_btn:hover,.cancel_collect_btn:hover{
  opacity: 0.8;
  text-decoration: none;
}

.collect_btn:focus,.cancel_collect_btn:focus{
  outline: none;
}

/*english*/
.description{
    font-size: 14px;
    color: #999;
    text-indent: 30px;
    margin-top: 20px;
}
.item .article-container .listening{
    display: none;
}
.item q>.article-container{
    padding: 0 20px;
}
.item q>subqs{
   padding: 0 40px;
}
.item q .cancel_collect_btn,
.item q .collect_btn{
    top:-33px;
}
.item.listening q .cancel_collect_btn,
.item.listening q .collect_btn{
    top:-43px;
}
.audio_player,
q .audio_player{
    height: 40px;
    line-height: 40px;
    border-radius: 4px;
    /*width:595px;*/
    margin: 10px 0 10px 40px;
    font-size: 14px;
    color:#999;
}
.audio_player i,
.audio_player .progress_wrap,
q .audio_player i,
q .audio_player .progress_wrap{
    display: inline-block;
    vertical-align: middle;
}
.audio_player i,
q .audio_player i{
    cursor: pointer;
    width: 22px;
    height: 22px;
    margin: 0 10px;
    background: url(../../../assets/questionCard/icon_paly.png) no-repeat center/22px;
}
.audio_player i.pause,
q .audio_player i.pause{
    background: url(../../../assets/questionCard/icon_suspend.png) no-repeat center/22px;
}
.audio_player .progress_wrap,
q .audio_player .progress_wrap{
    width:430px;
    height:16px;
    background:rgba(239,241,250,1);
    border:1px solid rgba(226,229,239,1);
    border-radius:8px;
}
.audio_player .current_time,
q .audio_player .current_time{
    margin-left: 10px;
    margin-right:5px;
}
.audio_player .total_time,
q .audio_player .total_time{
    margin-left: 5px;
}
.audio_player .progress_wrap .progress,
q .audio_player .progress_wrap .progress{
    height:100%;
    background-color: #3e73f6;
    width:0;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
}
.audio_player .progress_wrap .progress.done,
q .audio_player .progress_wrap .progress.done{
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
}
.item .listen_text{
    margin-left: 20px;
}
.item .listen_text .dt{
    width:80px;
}
.item .listen_text .dd{
    margin-left: 80px;
}
.item .listen_text .dd p{
    line-height: 22px;
}
.paper_header .audio_player{
    display: none;
}
.paper_header .audio_player .progress_wrap{
    width:390px;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}
.paper_header .audio_player .progress_wrap .marquee_text{
    position: absolute;
    top:-17px;
    white-space: nowrap;
    display: none;
}
.paper_header .audio_player a.download.done{
    background-position: -47px -2px;
    cursor: pointer;
}
.paper_header .audio_player .current_time{
    margin-left: 0px;
}


<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>IconFont</title>
    <link rel="stylesheet" href="demo.css">
    <script src="iconfont.js"></script>

    <style type="text/css">
        .icon {
          /* 通过设置 font-size 来改变图标大小 */
          width: 1em; height: 1em;
          /* 图标和文字相邻时，垂直对齐 */
          vertical-align: -0.15em;
          /* 通过设置 color 来改变 SVG 的颜色/fill */
          fill: currentColor;
          /* path 和 stroke 溢出 viewBox 部分在 IE 下会显示
             normalize.css 中也包含这行 */
          overflow: hidden;
        }

    </style>
</head>
<body>
    <div class="main markdown">
        <h1>IconFont 图标</h1>
        <ul class="icon_lists clear">
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-duoxuan"></use>
                    </svg>
                    <div class="name">多选</div>
                    <div class="fontclass">#iconfont-duoxuan</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-dianping"></use>
                    </svg>
                    <div class="name">点评</div>
                    <div class="fontclass">#iconfont-dianping</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-fanhui"></use>
                    </svg>
                    <div class="name">返回</div>
                    <div class="fontclass">#iconfont-fanhui</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-gaijin"></use>
                    </svg>
                    <div class="name">改进</div>
                    <div class="fontclass">#iconfont-gaijin</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-guanbi"></use>
                    </svg>
                    <div class="name">关闭</div>
                    <div class="fontclass">#iconfont-guanbi</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-guanlizuyuan"></use>
                    </svg>
                    <div class="name">管理组员</div>
                    <div class="fontclass">#iconfont-guanlizuyuan</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-paixu"></use>
                    </svg>
                    <div class="name">排序</div>
                    <div class="fontclass">#iconfont-paixu</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-quanxuan"></use>
                    </svg>
                    <div class="name">全选</div>
                    <div class="fontclass">#iconfont-quanxuan</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-qiehuan"></use>
                    </svg>
                    <div class="name">切换</div>
                    <div class="fontclass">#iconfont-qiehuan</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-shanchu"></use>
                    </svg>
                    <div class="name">删除</div>
                    <div class="fontclass">#iconfont-shanchu</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-sousuo"></use>
                    </svg>
                    <div class="name">搜索</div>
                    <div class="fontclass">#iconfont-sousuo</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-suijifenzu"></use>
                    </svg>
                    <div class="name">随机分组</div>
                    <div class="fontclass">#iconfont-suijifenzu</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-xingmingpaixu"></use>
                    </svg>
                    <div class="name">按姓名首字母排序</div>
                    <div class="fontclass">#iconfont-xingmingpaixu</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-baocun"></use>
                    </svg>
                    <div class="name">保存</div>
                    <div class="fontclass">#iconfont-baocun</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-bianji"></use>
                    </svg>
                    <div class="name">编辑</div>
                    <div class="fontclass">#iconfont-bianji</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-zhongxinfenzu"></use>
                    </svg>
                    <div class="name">重新分组</div>
                    <div class="fontclass">#iconfont-zhongxinfenzu</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-tongji"></use>
                    </svg>
                    <div class="name">统计</div>
                    <div class="fontclass">#iconfont-tongji</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-biaoyang"></use>
                    </svg>
                    <div class="name">表扬</div>
                    <div class="fontclass">#iconfont-biaoyang</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-class-evaluate"></use>
                    </svg>
                    <div class="name">banji</div>
                    <div class="fontclass">#iconfont-class-evaluate</div>
                </li>
            
                <li>
                    <svg class="icon" aria-hidden="true">
                        <use xlink:href="#iconfont-evaluate"></use>
                    </svg>
                    <div class="name">pingjiaguanli</div>
                    <div class="fontclass">#iconfont-evaluate</div>
                </li>
            
        </ul>


        <h2 id="symbol-">symbol引用</h2>
        <hr>

        <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
        这种用法其实是做了一个svg的集合，与另外两种相比具有如下特点：</p>
        <ul>
          <li>支持多色图标了，不再受单色限制。</li>
          <li>通过一些技巧，支持像字体那样，通过<code>font-size</code>,<code>color</code>来调整样式。</li>
          <li>兼容性较差，支持 ie9+,及现代浏览器。</li>
          <li>浏览器渲染svg的性能一般，还不如png。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-symbol-">第一步：引入项目下面生成的symbol代码：</h3>
        <pre><code class="lang-js hljs javascript"><span class="hljs-comment">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;</span></code></pre>
        <h3 id="-css-">第二步：加入通用css代码（引入一次就行）：</h3>
        <pre><code class="lang-js hljs javascript">&lt;style type=<span class="hljs-string">"text/css"</span>&gt;
.icon {
   width: <span class="hljs-number">1</span>em; height: <span class="hljs-number">1</span>em;
   vertical-align: <span class="hljs-number">-0.15</span>em;
   fill: currentColor;
   overflow: hidden;
}
&lt;<span class="hljs-regexp">/style&gt;</span></code></pre>
        <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
        <pre><code class="lang-js hljs javascript">&lt;svg <span class="hljs-class"><span class="hljs-keyword">class</span></span>=<span class="hljs-string">"icon"</span> aria-hidden=<span class="hljs-string">"true"</span>&gt;<span class="xml"><span class="hljs-tag">
  &lt;<span class="hljs-name">use</span> <span class="hljs-attr">xlink:href</span>=<span class="hljs-string">"#iconfont-xxx"</span>&gt;</span><span class="hljs-tag">&lt;/<span class="hljs-name">use</span>&gt;</span>
</span>&lt;<span class="hljs-regexp">/svg&gt;
        </span></code></pre>
    </div>
</body>
</html>

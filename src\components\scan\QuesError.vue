<template>
  <div class="error-main-container">

    <!-- <p>
      <el-popover
        placement="right-start"
        trigger="click"
        width="400"
        popper-class="error-setting-popover"
      >
        <div class="error-setting">
          <h4 class="setting-title">设置
          </h4>
          
          <div class="setting-item" v-if="fillSmartSettingVisible">
            <span class="setting-label">显示空</span>
            <div class="setting-input">
              <el-switch v-model="setting.fillTipsVisible" />        
            </div>
          </div>
          <div class="setting-item" v-if="fillSmartSettingVisible">
            <span class="setting-label">符号大小</span>
            <div class="setting-input">
              <el-slider v-model="setting.fillSymbolSize" show-input :min="1"  />
            </div>
          </div>
          <div class="setting-item">
            <span class="setting-label">缩放比例</span>
            <div class="setting-input">
              <el-slider v-model="setting.zoom" show-input :min="0.5" :max="2" :step="0.1" />
            </div>
          </div>
        </div>
        <el-button slot="reference" type="primary" size="small" icon="el-icon-setting">设置 <i class="el-icon-arrow-right"></i></el-button>
      </el-popover>
      <el-button style="margin-left: 10px;" type="" size="small" icon="el-icon-refresh-left" @click="resetSetting">还原设置</el-button>
    </p> -->


    <div class="error-list" :style="{ '--display-flex': errorData[0]?.type == 16 ? 'block' : 'flex'}" v-if="errorData.length > 0" ref="errorList">
      <div class="error-item" v-for="(item, quesIndex) in errorData" :class="{ isgroup: currentError=='choice' && quesIndex%chooseDoCount!=0 && cardType != 1 }">
        <div class="error-item-top">
          <!-- 考号、查看原卷 -->
          <div class="error-item-stuno" v-if="currentError!='choice' || cardType == 1 || (currentError=='choice' && quesIndex%chooseDoCount==0)">
            <span>{{ item.stu_no || 'xxxxxxxx' }}</span>
            <div class="hover-button">
              <el-popover placement="top-start" trigger="hover">
                <div class="paper-info">
                  <span>
                    <p>{{ item.task_name }}</p>
                    <span>
                      {{ item.stu_name }}
                    </span>
                    <span>
                      {{ item.cls_name }}
                    </span>
                    <span>
                      {{ item.stu_no }}
                    </span>
                    <el-button
                      type="primary"
                      @click="setPointError(item)"
                      class="set_point_error"
                      size="mini"
                      >设置为定位点异常</el-button
                    >
                  </span>
                  <div class="img-container">
                    <img
                      ref="image"
                      :src="getOriginImg(img)"
                      alt="ques"
                      class="item-img"
                      v-for="(img, imgIndex) in item.originImgs"
                      :key="imgIndex"
                    />
                  </div>
                </div>
                <span slot="reference">查看原卷</span>
              </el-popover>
            </div>
          </div>
          <div v-else></div>
          <!-- 错误原因 -->
          <div v-if="item.type != 15" class="err-tips" :class="{ error: item.code != 0 && item.status == 0 }">
            {{ item.status == 0 ? quesErrorType[item.code] : '' }}
          </div>
        </div>
        <div class="error-item-content">
          <!-- 手写打分显示识别分值 -->
          <template v-if="item.type==13">
            <div class="pos-score" :style="{top:(item.score_list[0].rows+1)*6+'mm'}">{{ item.score }}</div>
          </template>
          <!-- 异常图片 -->
          <img :style="{zoom: setting.zoom}" v-if="item.newImg" ref="image" :src="item.newImg" alt="ques" class="item-img" />
          <!-- 坐标 -->
          <template v-if="item.is_obj">
            <points-box
              class="points-box"
              :class="{ active: box.fill }"
              :style="{zoom: setting.zoom}"
              v-for="(box, optionIndex) in item.list"
              :key="optionIndex"
              :ques="item"
              :points="box"
              :quesIndex="quesIndex"
              :optionIndex="optionIndex"
              @choice-correct="choiceCorrect"
            ></points-box>
          </template>
          <div v-else>
            <!-- 线上批改填空位置标识 -->
            <template v-if="item.type == 15">
              <div class="online-fill-symbol" v-for="(scoreItem, scoreItemIndex) in item.list" :style="{...getBoxStyle(item,scoreItem.pos), zoom: setting.zoom}" @click="tabScore(scoreItem, scoreItemIndex ,quesIndex)" >
                <div class="online-fill-tips"  style="position: absolute;" :style="{
                  left: 0,
                  top: 0,
                }" v-show="setting.fillTipsVisible">空{{scoreItemIndex+1}}</div>
                <template v-if="scoreItem.total_score == scoreItem.score">
                  <SvgCheck class="online-fill-svg" :style="{
                    width: setting.fillSymbolSize + 'px',
                    height: setting.fillSymbolSize + 'px',
                  }" />
                </template>
                <template v-else-if="scoreItem.score == 0">
                  <SvgClose class="online-fill-svg" :style="{
                    width: setting.fillSymbolSize + 'px',
                    height: setting.fillSymbolSize * 0.75 + 'px',
                  }" />
                </template>
                <template v-else>
                  <SvgHalfCheck class="online-fill-svg" :style="{
                    width: setting.fillSymbolSize + 'px',
                    height: setting.fillSymbolSize + 'px',
                  }" />
                </template>
              </div>
            </template>
            <template>
              <template v-for="(scores, scoresIndex) in item.list">
                <div class="online-fill" v-if="item.type == 15">空{{ scoresIndex+1 }}</div>
                <template v-for="(box, optionIndex) in scores.newArray">
                  <points-box
                    :style="{zoom: item.type == 15 ? 1 : setting.zoom}"
                    class="points-box"
                    :class="{ active: box.fill,online:item.type == 15 }"
                    :ques="item"
                    :points="box"
                    :quesIndex="quesIndex"
                    :optionIndex="scoresIndex"
                    :rowIndex="optionIndex"
                    @choice-correct="choiceCorrect"
                  ></points-box>
                </template>
                <div class="line-tips">{{scores.code != 0 ? quesErrorType[scores.code] : ''}}</div>
              </template>
            </template>
          </div>
        </div>

        <!-- <el-dialog
          :visible="item.isShowOrigin"
          width="800px"
          :before-close="handleClose"
          :modal-append-to-body="false"
          :close-on-click-modal="true"
          :title="item.stu_no"
        >
          <img ref="image" :src="item.originImg" alt="ques" class="item-img" />
        </el-dialog> -->
      </div>
    </div>
    <no-data :type="'zan'" v-else text="异常已全部处理，辛苦啦~"></no-data>
  </div>
</template>

<script>
import PointsBox from './PointsBox.vue';
import NoData from '@/components/noData';
import { savePaperScanImg,saveInnerPaperScanImg, deleteAllQues, setImagePointError,setInnerImagePointError } from '@/service/pexam';
import { QUES_ERRTYPE_WORD } from '@/typings/scan';
import { setCroppedImageUrl } from '@/utils/index';

import SvgCheck from '../Svg/SvgCheck.vue';
import SvgClose from '../Svg/SvgClose.vue';
import SvgHalfCheck from '../Svg/SvgHalfCheck.vue';

function getSetting(){
  return {
    fillTipsVisible: false,
    fillSymbolSize: 40,
    zoom: 1,
  }
}

export default {
  components: { PointsBox, NoData, SvgCheck, SvgClose, SvgHalfCheck },
  props: {
    //当前错误类型
    errorData: {
      type: Array,
      default: () => [],
    },
    //坐标点
    pointsData: {
      type: Object,
      default: () => {},
    },
    //考试信息
    examInfo: {
      type: Object,
      default: () => {},
    },
    //当前错误类型
    currentError: {
      type: String,
      default: () => '',
    },
    //多选题得分规则
    choiceScoreMap: {
      type: Object,
      default: () => {},
    },
    chooseDoCount:{
      type: Number,
      default: () => 1,
    },
    //批次
    taskIds: {
      type: String,
      default: () => '',
    },
    //是否内部处理页面
    isInner:{
      type: Boolean,
      default: () => false
    },
    cardType:{
      type: Number,
      default: () => 0,
    },
  },
  watch: {
    errorData(newval, oldval) {
      console.log(newval);
    },
  },
  data() {
    return {
      //题目异常类型
      quesErrorType: QUES_ERRTYPE_WORD,
      quesIndex: 0,
      // 设置对象
      setting: getSetting(),
    };
  },
  computed: {
    // 设置显示状态
    fillSmartSettingVisible(){
      if (!this.errorData || this.errorData.length == 0) {
        return false;
      }
      return this.errorData.some(item => item.type == 15);
    }
  },
  mounted() {
    this.onConditionChange();
  },
  beforeDestroy() {
    this.$bus.$off('submitData');
    this.$bus.$off('dealAllQues');
  },

  methods: {

    // 获取原卷图片
    getOriginImg(img){
      return setCroppedImageUrl(img, 1.5)
    },

      /**
     * 获取 box的相对位置
     */
     getBoxStyle(ques,box) {
      let scale = 4.173;
      let queLeft = ques.pos[0];
      let queTop = ques.pos[1];
      return {
        left: box[0] * scale - queLeft + 'px',
        top: box[1] * scale - queTop + 10 + 'px',
        width: box[2] * scale + 'px',
        height: box[3] * scale + 'px',
      };
    },
    onConditionChange() {
      this.$bus.$on('submitData', () => {
        this.submit();
      });
      this.$bus.$on('dealAllQues', () => {
        this.dealAllQues();
      });
    },
    /**
     * @name:选择正确选项
     * @param {*} index 题目下标
     * @param {*} inde 选项下标
     */
    choiceCorrect(current) {
      let customNos = Object.keys(this.choiceScoreMap);
      let currentQues = this.errorData[current.quesIndex];
      //客观题获取当前选项
      if (this.errorData[current.quesIndex].is_obj) {
        //当前选项
        let isCurrent = this.errorData[current.quesIndex].list.filter((item, index) => {
          return index == current.optionIndex;
        })[0];
        //不是当前选项
        let notCurrent = this.errorData[current.quesIndex].list.filter((item, index) => {
          return index != current.optionIndex;
        });
        isCurrent.fill = !isCurrent.fill;
        //题卡合一选做题 可选目标不超过选做数
        if(this.errorData[current.quesIndex].type == 18 && this.cardType == 1){
          let doCount = this.errorData[current.quesIndex].do_count;
          //选做一题，其他填涂区移除
          if(doCount == 1 && isCurrent.fill){
            notCurrent.forEach(ite => {
              ite.fill = false;
            });
          }else{
            //选做多题，判断是否超过选做数
            let count = this.errorData[current.quesIndex].list.filter((item, index) => {
              return item.fill;
            }).length;
            if(count > doCount){
              isCurrent.fill = false;
              this.$message.error('超过可选数，请取消后重新选择');
            }
          }
        }
        if (
          this.errorData[current.quesIndex].question_type &&
          (this.errorData[current.quesIndex].question_type == 8 ||
            this.errorData[current.quesIndex].question_type == 2) ||
            (this.errorData[current.quesIndex].type == 18 && this.cardType != 1)
        ) {
          if (isCurrent.fill) {
            notCurrent.forEach(ite => {
              ite.fill = false;
            });
          }
        }
        //当前题目的坐标点
        let page = this.errorData[current.quesIndex].page;
        let quesNo = this.errorData[current.quesIndex].question_no;
        // let currentPoints = this.pointsData.pages[page - 1].filter(item => {
        //   return item.question_no == quesNo;
        // })[0];
        //已选择的选项2的index次方
        let optionInt = 0;
        this.errorData[current.quesIndex].list.forEach((item, index) => {
          if (item.fill) {
            optionInt += Math.pow(2, index);
          }
        });
        // 判断是否是多选题自定义规则
        if (customNos.includes(String(currentQues.question_no))) {
          this.errorData[current.quesIndex].tempScore =
            this.choiceScoreMap[currentQues.question_no][optionInt] || 0;
        } else {
          if (optionInt == this.errorData[current.quesIndex].answer_int) {
            //全对
            this.errorData[current.quesIndex].tempScore = currentQues.total_score;
          } else if (
            //半对
            (optionInt & this.errorData[current.quesIndex].answer_int) != 0 &&
            (optionInt & this.errorData[current.quesIndex].answer_int) == optionInt
          ) {
            this.errorData[current.quesIndex].tempScore = currentQues.miss_score;
          } else {
            //全错
            this.errorData[current.quesIndex].tempScore = 0;
          }
        }
      } else {
        //主观题获取分数
        this.errorData[current.quesIndex].list[current.optionIndex].score = current.score;
        let totalScore = 0;
        this.errorData[current.quesIndex].list.forEach(item => {
          totalScore += item.score;
        });
        if(this.errorData[current.quesIndex].total_score < totalScore){
          this.$message.error('分数超出题目总分！');
        }
        this.errorData[current.quesIndex].tempScore = Math.min(this.errorData[current.quesIndex].total_score,totalScore);
      }
    },
    /**
     * @name:标记为未涂
     */
    markNotFilling(item, index) {
      this.errorData[index].notFilling = !this.errorData[index].notFilling;
    },
    /**
     * @name:查看原卷
     * @param {*} index
     */
    checkOriginQues(item, index) {
      setTimeout(() => {
        item.isShowOrigin = true;
        this.quesIndex = index;
      }, 300);
    },
    closeCheck(item) {
      item.isShowOrigin = false;
    },
    handleClose() {
      this.errorData[this.quesIndex].isShowOrigin = false;
    },
    /**
     * @name:提交异常处理
     */
    submit() {
      let data = this.errorData.map(item => {
        let socre =  Math.min(item.tempScore,item.total_score);
        if(item.scoringMode == 1){
          //减分制
          socre = Math.max(item.total_score - item.tempScore,0);
        }
        return {
          id: item.id,
          page: item.page,
          question_no: item.question_no,
          sort: item.sort,
          score: socre,
          is_obj: item.is_obj,
          list: item.is_obj ? item.list : [],
          score_list: item.is_obj ? [] : item.list,
          choose_id:item.choose_id,
          exam_id: this.examInfo.examId,
        };
      });
      let scanFunc = this.isInner ? saveInnerPaperScanImg : savePaperScanImg;
      scanFunc(data)
        .then(res => {
          this.$message({
            message: '提交成功！',
            type: 'success',
            duration: 1000,
          });
          this.$emit('update-error');
        })
        .catch(err => {
        });
    },
    setPointError(info) {
      let params = {
        id: info.id,
        weight: 2,
        examId: this.examInfo.examId,
      }
      let pointFunc = this.isInner ? setInnerImagePointError : setImagePointError;
      pointFunc(params)
        .then(res => {
          this.$message({
            message: '设置成功！',
            type: 'success',
            duration: 1000,
          });
          if(!this.isInner){
            //内部复核页面定位点异常处理后不进行页面更新
            this.$emit('update-error');
          }
        })
        .catch(err => {
        });
    },
    /**
     * @name:处理全部题目异常
     */
    dealAllQues() {
      deleteAllQues({
        examId: this.examInfo.examId,
        isObj: this.currentError == 'objective',
        taskId: this.taskIds,
      })
        .then(res => {
          this.$message({
            message: '批量处理成功！',
            type: 'success',
            duration: 1500,
          });
          this.$emit('update-error');
        })
        .catch(err => {
        });
    },
    scrollTotop() {
      this.errorData.length > 0 &&
        this.$refs.errorList.scrollTo({
          top: 0,
          behavior: 'smooth',
        });
    },

    // 切换分数
    tabScore(item, scoreItemIndex, quesIndex) {
      let index = item.new_score_list.findIndex(item => item.isChoice);
      if (index < 0) {
        index = 0;
      } else {
        item.new_score_list[index].isChoice = false;
      }
      let nextIndex = item.new_score_list[index + 1] ? index + 1 : 0;
      item.new_score_list[nextIndex].isChoice = true;
      let score = item.new_score_list[nextIndex].value

      this.choiceCorrect({
        quesIndex: quesIndex, // 题目索引
        optionIndex: scoreItemIndex, // 小空索引
        score: score, // 小空分数
        scoreColIndex: 0
      })
    },

    // 还原设置
    resetSetting() {
      this.setting = getSetting();
    },
  },
};
</script>

<style lang="scss" scoped>
.error-main-container {
  position: absolute;
  top: 100px;
  height: calc(100% - 110px);
  width: 100%;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.error-setting {
  font-size: 14px;
  padding: 5px;
  
  .setting-title {
    margin: 0 0 15px 0;
    color: #303133;
    font-weight: 500;
    text-align: center;
    border-bottom: 1px solid #EBEEF5;
    padding-bottom: 10px;
  }
  
  .setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    
    .setting-label {
      color: #606266;
      font-weight: 400;
    }

    .setting-input{
      flex: 1;
      margin-left: 15px;
    }
  }
}

.error-list {
  overflow-y: auto;
  height: 95%;
  width: 100%;
  display: var(--display-flex);
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  align-content: flex-start;
}
.error-item {
  position: relative;
  // width: 801px;
  // max-height: 410px;
  max-width: 801px;
  min-width: 300px;
  margin: 25px 25px 10px 0px;
  background-color: #fff;
  &.isgroup{
    margin-top: 0px;
  }
  .pos-score{
    display: inline-block;
    position: absolute;
    right: -40px;
    color: red;
    font-size: 24px;
    background: #fff;
    padding: 5px;
  }
}
.error-item-top {
  position: absolute;
  top: -20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  min-width: 190px;
  .err-tips {
    display: flex;
    align-items: center;
    color: #1a1919;
    &.error {
      color: red;
    }
    .not-filling {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 28px;
      margin-left: 25px;
      margin-top: -5px;
      border: 1px solid #e4e8eb;
      background-color: #fff;
      padding: 0 10px;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
      &.active {
        background-color: #409eff;
        color: #fff;
      }
    }
  }
  span {
    margin-top: -5px;
  }
}
.item-img {
  // width: 100%;
}
.error-pagination {
  text-align: center;
}
.online-fill-tips{
  position: absolute;
  color: #409eff;
  font-size: small;
  font-weight: 600;
}

.online-fill-symbol {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  &:hover {

    .online-fill-tips{
      display: block !important;
    }

    .online-fill-svg {
      opacity: 1;
    }
  }

  .online-fill-svg {
    position: absolute;
    bottom: -2px;
    right: -4px;
    // width: 40px;
    // min-width: 25px;
    // height: 40px;
    // max-height: 100%;
    // min-height: 30px;
    fill: #FF0000;
    opacity: 0.7;
  }
}

.online-fill{
  display: inline-block;
  height: 30px;
  width: 60px;
  line-height: 30px;
  text-align: right;
  vertical-align: top;
}
.line-tips{
  display: inline-block;
  height: 30px;
  width: 100px;
  line-height: 30px;
  color: red;
  vertical-align: top;
}
.points-box {
  position: absolute;
  // border: solid 2px red;
  cursor: pointer;
  &.online{
    display: inline-block;
    position: relative;
    vertical-align: top;
  }
  &.active {
    border: solid 4px #01cc7d;
  }
}
.error-item-stuno {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-weight: bold;
}
.hover-button {
  margin-left: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  color: #409eff;
  &:hover {
    color: #409eff;
  }
}
.paper-info {
  display: flex;
  flex-direction: column;
  span {
    font-size: 16px;
    color: #409eff;
  }
  .set_point_error {
    float: right;
  }
}
.img-container {
  display: flex;
  flex-direction: column;
  max-height: 550px;
  overflow-y: auto;
  margin-top: 10px;
}
</style>

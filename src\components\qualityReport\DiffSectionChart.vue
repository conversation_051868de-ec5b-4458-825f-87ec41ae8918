<template>
  <div>
    <div v-show="!showDefault" id="diffSectionChart"
         style="width: 100%; height: 320px"></div>
    <div v-show="showDefault">
      <p style="text-align: center;font-size: 16px;margin-top: 80px;">暂无数据!</p>
    </div>
  </div>
</template>

<script>
export default {
  name: "DiffSectionChart",
  props: ['qualityInfo'],
  data() {
    return {
      showDefault: false,
      diffSectionChart: null,
    }
  },
  mounted(){
    let _this = this;
    window.onresize = ()  =>{
      return (() => {
        if(_this.diffSectionChart) {
          _this.diffSectionChart.resize();
        }
      })()
    };
    // this.drawImg()
  },
  beforeDestroy() {
    if(this.diffSectionChart != null && this.diffSectionChart != "" && this.diffSectionChart != undefined) {
      this.diffSectionChart.dispose();
      this.diffSectionChart = null;
    }
  },
  watch: {
    qualityInfo: {
      handler() {
        this.drawImg();
      },
      deep: true
    }
  },
  methods: {
    handleChartData() {
      let diff = this.qualityInfo.diff!=undefined ? this.qualityInfo.diff : {
        easyNumRate: '0',
        middleNumRate: '0',
        diffNumRate: '0',
        easyScoreRate: '0',
        middleScoreRate: '0',
        diffScoreRate: '0'
      };
      return diff
    },
    drawImg() {
      if(this.diffSectionChart != null && this.diffSectionChart != "" && this.diffSectionChart != undefined) {
        this.diffSectionChart.dispose();
        this.diffSectionChart = null;
      }
      let diff = this.handleChartData();
      let _this = this;
      // 基于准备好的dom，初始化echarts实例
      this.diffSectionChart = this.$echarts.init(
          document.getElementById("diffSectionChart")
      );
      // 绘制图表
      this.diffSectionChart.setOption({
        title: [
          {
            text: '难度-题量占比',
            bottom: 20,
            left: '25%',
            textAlign: 'center',
            textStyle: {
              fontSize: 16,
              color: '#3F4A54'
            }
          },
          {
            text: '难度-分值占比',
            bottom: 20,
            left: '75%',
            textAlign: 'center',
            textStyle: {
              fontSize: 16,
              color: '#3F4A54'
            }
          }
        ],
        color: ['#07C29D', '#3E73F6', '#FF6A68'],
        legend: {
          icon: 'circle',
          x : 'center',
          top: '20',
          data:['简单题','中等题','困难题']
        },
        series : [
          {
            name:'题量占比',
            type:'pie',
            center: ['25%', '50%'],
            radius: ['45%', '60%'],
            data:[
              {value: diff.easyNumRate, name:'简单题'},
              {value: diff.middleNumRate, name:'中等题'},
              {value: diff.diffNumRate, name:'困难题'},
            ],
            emphasis: {
              label: {
                show: true,
                // fontSize: '50',
                fontWeight: 'bold',
                // textStyle: {
                //   fontSize: 24, // 改变标示文字的大小
                //   color: "#3F4A54",
                // },
                fontSize: 24, // 改变标示文字的大小
                color: "#3F4A54",
                formatter: "{c}%", //提示文本内容 d代表百分比，b代表name文本
                // position: "center", //控制文字显示的位置，center居中显示
              }
            },
            label: {
              show: false,
              position: 'center'
            },
          },
          {
            name:'分值占比',
            type:'pie',
            center: ['75%', '50%'],
            radius: ['45%', '60%'],
            data:[
              {value: diff.easyScoreRate, name:'简单题'},
              {value: diff.middleScoreRate, name:'中等题'},
              {value: diff.diffScoreRate, name:'困难题'},
            ],
            emphasis: {
              label: {
                show: true,
                // fontSize: '50',
                fontWeight: 'bold',
                // textStyle: {
                //   fontSize: 24, // 改变标示文字的大小
                //   color: "#3F4A54",
                // },
                fontSize: 24, // 改变标示文字的大小
                color: "#3F4A54",
                formatter: "{c}%", //提示文本内容 d代表百分比，b代表name文本
                // position: "center",//控制文字显示的位置，center居中显示
              }
            },
            label: {
              show: false,
              position: 'center'
            },
          }
        ]
      });
    },
  }
}
</script>

<style scoped>

</style>

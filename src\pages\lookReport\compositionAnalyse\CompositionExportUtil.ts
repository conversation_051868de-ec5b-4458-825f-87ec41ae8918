/*
 * @Description:
 * @Author: 小圆
 * @Date: 2025-06-09 14:16:07
 * @LastEditors: 小圆
 */
// 导出类型
export enum CompositionExportType {
  /** 学生报告type */
  STUDENT = 'stu_report',
  /** 教师报告type */
  TEACHER = 'teacher_report',
}

// 学生报告导出参数
export interface StuCompositionParams {
  type: CompositionExportType.STUDENT;
  stuNo: string;
  stuName: string;
  gradeName: string;
  className: string;
  quesNo: string;
  tQuesNo: string;
  schoolId: string;
  userId: string;
  workId: string;
  stuId: string;
  classId: string;
  token: string;
  test: string;
}

// 教师报告导出参数
export interface TeacherCompositionParams {
  type: CompositionExportType.TEACHER;
  examName: string;
  examDateTime: string;
  gradeName: string;
  className: string;
  classId: string;
  subjectId: string;
  abPaper: string;
  quesNo: string;
  tQuesNo: string;
  examId: string;
  workId: string;
  v: string;
  userId: string;
  schoolId: string;
  token: string;
  test: string;
}

export type CompositionExportParams = StuCompositionParams | TeacherCompositionParams;

// 导出作文报告
export const exportCompositionAPI = (params: CompositionExportParams): Promise<string> => {
  const url =
    process.env.VUE_APP_BASE_API == 'https://test.iclass30.com'
      ? 'http://webtest.iclass30.com:9810/export/exportComposition'
      : 'https://exportnode.iclass30.com/export/exportComposition';

  return new Promise((resolve, reject) => {
    fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    })
      .then(res => {
        return res.json();
      })
      .then(res => {
        resolve(res);
      })
      .catch(err => {
        reject(err);
      });
  });
};

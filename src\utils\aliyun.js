/**
 * auth: sshu
 * time: 2020.03.14
 * description: 阿里云上传公共方法
 */

import {getToken, getUserId} from "../service/auth"

let accessid = ''// id
let host = ''// host: 指的是用户要往哪个域名发往上传请求   host+文件名就是下载路径了.
let policyBase64 = ''// policy：指的是用户表单上传的策略policy，是经过base64编码过的字符串
let signature = ''// signature：是对上述第三个变量policy签名后的字符串
// let callbackbody = '';// 返回的body
let key = ''
// let expire = 0;// 过期时间
let g_object_name = ''// 文件类型. local_name
let g_object_name_type = 'random_name'// 文件路径方式

// 这个是获取accessid policy signature dir host expire.
function send_request (fileFrom, type, schoolId) {
    let xmlhttp = null
    if (window.XMLHttpRequest) {
        xmlhttp = new XMLHttpRequest()
    } else if (window.ActiveXObject) {
        xmlhttp = new ActiveXObject("Microsoft.XMLHTTP")
    }

    if (xmlhttp !== null) {
        let path = ''
        if (type !== 1) {
            path = fileFrom
        } else {
            path = "aliba/resources/" + fileFrom + getDateForPath(schoolId)
        }
        let serverUrl = process.env.VUE_APP_BASE_API + '/common/oss/signature?path=' + path + '&token=' + getToken()
        xmlhttp.open("GET", serverUrl, false)
        xmlhttp.send(null)
        return xmlhttp.responseText
    } else {
        console.log('您的浏览器不支持XMLHTTP,请换浏览器登录再使用')
    }
}

function get_signature (fileFrom, type, schoolId) {
    // 2020/07/24 hss 屏蔽 当一个表单有两个上次文件时 上传的路径不同这个不会去走send_request方法
    // 可以判断当前expire是否超过了当前时间,如果超过了当前时间,就重新取一下.3s 做为缓冲.
    // now = timestamp = Date.parse(new Date()) / 1000;
    // if (expire < now + 3) {
    let body = send_request(fileFrom, type, schoolId)
    let obj = eval('(' + body + ')').data
    host = obj['host']
    policyBase64 = obj['policy']
    accessid = obj['accessid']
    signature = obj['signature']
    // expire = parseInt(obj['expire']);
    // callbackbody = obj['callback'];
    key = obj['dir']

    return true
}

// 截取文件的后缀.
function get_suffix (filename) {
    let pos = filename.lastIndexOf('.')
    let suffix = ''
    if (pos !== -1) {
        suffix = filename.substring(pos)
    }
    return suffix
}

// 上传的时候文件保存路径和名称:我这里是 文件夹+uuid+文件后缀.
function calculate_object_name (filename) {
    if (g_object_name_type === 'local_name') {
        g_object_name += `${filename}`
    } else if (g_object_name_type === 'random_name') {
        let uuid = generateUUID()
        let suffix = get_suffix(filename)
        g_object_name = key + uuid + '/f' + suffix
    }
    return ''
}

// 初始化一些参数.  新增type参数 默认1 会给fileFrom拼接年月日路径  2：fileFrom为文件全路径  3.fileFrom为文件全路径并且不拼接guid和重命名文件名为f
function getPolicy (filename, fileFrom, ret, type = 1, schoolId) {
    if (!ret) {
        ret = get_signature(fileFrom, type, schoolId)
    }
    g_object_name = key
    if (filename !== '' && type !== 3) {
        // let suffix = get_suffix(filename);
        calculate_object_name(filename)
    }

    let new_multipart_params = {
        'key': g_object_name,
        'policy': policyBase64,
        'OSSAccessKeyId': accessid,
        'success_action_status': '200', // 让服务端返回200,不然，默认会返回204.
        // 'callback': callbackbody,
        'signature': signature,
        'host': host
    }

    return new_multipart_params
}

// 获取key
function getUploadKey (fileFrom, ret) {
    if (!ret) {
        ret = get_signature(fileFrom)
    }
    g_object_name = key
    calculate_object_name()

    let new_multipart_params = {
        'key': g_object_name
    }

    return new_multipart_params
}

/**
 * 生产随机id用于拼接上传文件路径
 */
function generateUUID () {
    let s = []
    let hexDigits = '0123456789abcdefghijklmnopqrstuvwxyz'
    for (let i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
    }
    s[14] = '4' // bits 12-15 of the time_hi_and_version field to 0010
    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1) // bits 6-7 of the clock_seq_hi_and_reserved to 01
    s[8] = s[13] = s[18] = s[23]
    return s.join("")
}

/**
 * 根据当前时间当前用户的学校id和用户id拼接文件夹路径
 * @returns {string}
 */
function getDateForPath (schoolid) {
    let date = new Date()
    let y = date.getFullYear()
    let m = date.getMonth() + 1
    m = m < 10 ? ('0' + m) : m
    let d = date.getDate()
    d = d < 10 ? ('0' + d) : d

    if (schoolid) {
        // 当用户学校id不为空时
        return '/' + y + '/' + m + '/' + d + '/' + schoolid + '/' + getUserId() + '/'
    } else {
        // 当用户学校id为空时
        return '/' + y + '/' + m + '/' + d + '/' + getUserId() + '/'
    }
}

export {
    getPolicy,
    getUploadKey,
    generateUUID,
    get_suffix
}

<!--
 * @Descripttion: 教学监管概览
 * @Author: 小圆
 * @Date: 2023-11-28 16:55:30
 * @LastEditors: 小圆
-->
<template>
  <div class="statistic-overview">
    <StatisticFilter
      v-model="queryData"
      :isShowTypeSelect="false"
      @onLoad="handleFilterLoad"
      @change="getStatScanExam"
    ></StatisticFilter>
    <div v-loading="!isInit">
      <div class="statistic-wrapper clearfix">
        <el-row :gutter="20">
          <el-col :span="8">
            <StatisticItem
              :value="examData.exam"
              title="测评总量"
              unit-text="次"
              :img-src="require('@/assets/supervise/banner_1.png')"
              @goto="gotoExamReport"
            ></StatisticItem>
          </el-col>
          <el-col :span="8">
            <StatisticItem
              :value="examData.scan"
              title="扫描总量"
              unit-text="份"
              :img-src="require('@/assets/supervise/banner_2.png')"
              @goto="gotoDetails"
            ></StatisticItem>
          </el-col>
          <el-col :span="8">
            <StatisticItem
              :value="examData.question"
              title="校本试题总量"
              unit-text="道"
              :img-src="require('@/assets/supervise/banner_3.png')"
              @goto="gotoSchoolQuestion"
            ></StatisticItem>
          </el-col>
        </el-row>
      </div>

      <div class="statistic-main">
        <div class="statistic-tabs">
          <div
            v-for="item in tabList"
            :key="item.value"
            class="tab-item"
            :class="{ active: item.value == tab }"
            @click="() => (tab = item.value)"
          >
            {{ item.label }}
          </div>

          <div class="statistic-tab--right">
            <span class="link" @click="gotoDetails"
              >查看详情 <i class="el-icon-arrow-right"></i
            ></span>
          </div>
        </div>

        <template v-if="isInit">
          <statistic-analysis-type
            v-if="tab == 'type'"
            :queryData="queryData"
            :examData="examData"
          ></statistic-analysis-type>
          <statistic-analysis-subject
            v-if="tab == 'subject'"
            :queryData="queryData"
          ></statistic-analysis-subject>
          <statistic-analysis-scan
            v-if="tab == 'scan'"
            :queryData="queryData"
          ></statistic-analysis-scan>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { ClassList, getStatScanExam } from '@/service/pexam';
import StatisticFilter from './components/statistic-filter.vue';
import StatisticItem from './components/statistic-item.vue';
import UserRole from '@/utils/UserRole';
import NoData from '@/components/noData.vue';
import StatisticAnalysisType from './components/statistic-analysis-type.vue';
import StatisticAnalysisSubject from './components/statistic-analysis-subject.vue';
import StatisticAnalysisScan from './components/statistic-analysis-scan.vue';
import { deepClone } from '@/utils/index';

function getDefaultExamData() {
  return deepClone({
    exam: 0,
    scan: 0,
    question: 0,
    hand: {
      num: 0,
      typeMap: {},
    },
    online: {
      num: 0,
      typeMap: {},
    },
    additional: {
      num: 0,
      typeMap: {},
    },
    school: {
      num: 0,
      typeMap: {},
    },
  });
}

export default {
  components: {
    StatisticFilter,
    StatisticItem,
    NoData,
    StatisticAnalysisType,
    StatisticAnalysisSubject,
    StatisticAnalysisScan,
  },
  data() {
    return {
      // 是否加载成功
      isInit: false,
      // 查询数据
      queryData: {
        yearValue: '',
        gradeValue: '',
        subjectValue: '',
        dateRange: [],
        statType: 0,
      },
      // 考试数据
      examData: getDefaultExamData(),

      // tab列表
      tabList: [
        {
          label: '类型分析',
          value: 'type',
        },
        {
          label: '学科分析',
          value: 'subject',
        },
        {
          label: '扫描分析',
          value: 'scan',
        },
      ],
      // 当前tab
      tab: 'type',
    };
  },

  computed: {},

  methods: {
    // 筛选加载成功
    handleFilterLoad() {
      this.isInit = true;
      this.getStatScanExam();
    },

    // 获取统计数据
    async getStatScanExam(val) {
      try {
        let params = {
          schId: this.$sessionSave.get('schoolInfo').id,
          grdId: this.queryData.gradeValue,
          subjectId: this.queryData.subjectValue,
          begin: this.$moment(this.queryData.dateRange[0]).format('YYYY-MM-DD'),
          end: this.$moment(this.queryData.dateRange[1]).format('YYYY-MM-DD'),
          statType: this.queryData.statType,
        };
        const { data } = await getStatScanExam(params);
        this.examData = data;
      } catch (err) {
        this.examData = getDefaultExamData();
      } finally {
      }
    },

    // 跳转测评中心
    gotoExamReport() {
      this.$sessionSave.set('pageType', 'testMge'); // 跳转评测中心时，跳转测评管理
      this.$router.push({
        path: '/home/<USER>',
      });
    },

    // 跳转详情
    gotoDetails() {
      this.$router.push({
        path: '/home/<USER>/scanTotal',
      });
    },

    // 跳转校本题库
    gotoSchoolQuestion() {
      this.$localSave.set('paper-tab', 'school');
      this.$router.push({
        path: '/home/<USER>/school',
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.statistic-overview {
  padding: 0 24px;
  padding-bottom: 20px;
  background-color: #fff;
}

.statistic-wrapper {
}

.statistic-main {
  width: 100%;
  min-height: 400px;
  margin-top: 23px;
  background-color: #fff;
  box-shadow: 0px 0px 54px 0px rgba(224, 224, 224, 0.76);
  border-radius: 6px;
}

.statistic-tabs {
  width: 100%;
  line-height: 50px;
  background: #e9f4ff;
  display: flex;
  border-radius: 6px 6px 0 0;

  .tab-item {
    min-width: 120px;
    height: 50px;
    font-size: 18px;
    border-radius: 6px 6px 0px 0px;
    text-align: center;
    padding: 0 15px;
    cursor: pointer;

    &.active {
      background: #ffffff;
      border: 1px solid #ffffff;
      font-weight: bold;
      font-size: 18px;
      color: #409eff;
    }
  }
}

.statistic-tab--right {
  flex: 1;
  text-align: right;
}

.link {
  margin-right: 20px;

  font-size: 16px;
  color: #409eff;

  cursor: pointer;

  &:hover {
    color: rgba(#409eff, 0.8);
  }
}
</style>

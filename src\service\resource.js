/*
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2024-08-26 17:39:33
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-05-28 17:41:41
 */
import * as httpApi from './index';

const kklUrl = process.env.VUE_APP_BASE_API;

/**
 * api转换
 * @type {{POST: API.POST, GET: API.GET,POSTSTRING: API.POSTSTRING}}
 */
const API = {
  GET: function (url, params) {
    return httpApi.GET(url, params, kklUrl);
  },
  POST: function (url, params) {
    return httpApi.POST(url, params, kklUrl);
  },
  POSTJson: function (url, params) {
    return httpApi.POSTJson(url, params, kklUrl);
  },
};

/**
 * 获取单词教材列表
 * @param params
 * @returns {AxiosPromise}
 */
export const getBookListAPI = params => {
  return API.GET('/resource/pointReader/getBookListNew', params);
};

/**
 * 获取单词教材章节
 * @param params
 * @returns {AxiosPromise}
 */
export const getBookCatalogAPI = params => {
  return API.GET('/resource/pointReader/getBookCatalog', params);
};

/**
 * 获取单词列表
 * @param params
 * @returns {AxiosPromise}
 */
export const getWordListAPI = params => {
  return API.GET('/resource/pointReader/getWordList', params);
};
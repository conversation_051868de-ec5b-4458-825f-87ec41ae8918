<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-11-05 11:13:58
 * @LastEditors: 小圆
-->
<template>
  <div class="statistic-type-main clearfix">
    <div class="statistic-type-ul--left fl">
      <statistic-li
        v-for="item in statisticList"
        :active="currentStatisticId == item.id"
        :key="item.id"
        :title="item.title"
        :unit-text="item.unitText"
        :value="item.num"
        :icon="item.icon"
        @click.native="tabStatistList(item)"
      ></statistic-li>
    </div>
    <div class="statistic-type-ul--right fr">
      <div ref="chart" v-show="isShowChart" style="width: 100%; height: 560px"></div>
      <no-data style="margin-top: 100px" v-show="!isShowChart"></no-data>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import NoData from '@/components/noData.vue';
import UserRole from '@/utils/UserRole';
import StatisticLi from './statistic-li.vue';
import { QueryData } from './types';

export interface ExamData {
  exam: number;
  scan: number;
  question: number;
  hand: Additional;
  online: Additional;
  additional: Additional;
  school: Additional;
}

export interface Additional {
  num: number;
  typeMap: TypeMap;
}

export interface TypeMap {
  [key: string]: number;
}

@Component({
  components: {
    StatisticLi,
    NoData,
  },
})
export default class StatisticAnalysisType extends Vue {
  public $refs!: {
    chart: HTMLDivElement;
  };
  @Prop({ default: {} }) queryData: QueryData;
  @Prop({ default: {} }) examData: ExamData;
  @Watch('examData', { deep: true, immediate: true })
  async onExamDataChange(val: ExamData) {
    if (!this.typeList.length) this.typeList = await UserRole.getAllType();
    this.statisticList = this.statisticList.map(item => {
      const num = val[item.id].num;
      const typeMap = val[item.id].typeMap;
      return {
        ...item,
        num: num,
        typeMap: typeMap,
      };
    });
    this.updateCharts();
  }

  // 统计列表
  statisticList = [
    {
      id: 'hand',
      title: '手阅',
      unitText: '次',
      num: 0,
      typeMap: {},
      icon: 'icon-qianming',
    },
    {
      id: 'online',
      title: '网阅',
      unitText: '次',
      num: 0,
      typeMap: {},
      icon: 'icon-shangwutubiao-',
    },
    {
      id: 'additional',
      title: '补录',
      unitText: '次',
      num: 0,
      typeMap: {},
      icon: 'icon-waixieluru',
    },
    {
      id: 'school',
      title: '校本试卷',
      unitText: '套',
      num: 0,
      typeMap: {},
      icon: 'icon-shijuanguanli',
    },
  ];
  // 当前统计id
  currentStatisticId = '';
  // 图表
  myChart = null;
  // 类别列表
  typeList = []; // {id,name}
  // 是否正在加载
  isLoading = false;

  // 是否显示类别图表
  get isShowChart() {
    return this.chartDataList.some(item => item.value > 0);
  }

  // 图表数据
  get chartDataList() {
    if (!this.currentStatisticValue || !this.typeList || !this.typeList.length) return [];
    return this.typeList.map(item => {
      const value = this.currentStatisticValue.typeMap[item.name] || 0;
      return {
        name: item.name,
        value: value,
        label: {
          show: !!value,
        },
        labelLine: {
          show: !!value,
        },
      };
    });
  }

  // 当前列表值
  get currentStatisticValue() {
    return this.statisticList.find(item => item.id == this.currentStatisticId);
  }

  mounted() {
    this.currentStatisticId = this.statisticList[0].id;
  }

  // 获取全部类型
  async selectAllType() {
    this.typeList = await UserRole.getAllType();
  }

  // 初始化图表
  async initCharts() {
    await this.$nextTick();
    if (!this.isShowChart) return;
    if (this.myChart) {
      this.myChart.dispose();
      this.myChart = null;
    }
    if (!this.$refs.chart) return;
    this.myChart = this.$echarts.init(this.$refs.chart);
    this.myChart.setOption({
      tooltip: {
        trigger: 'item',
      },
      color: [
        '#008DEA',
        '#FE9A5C',
        '#3D56FF',
        '#91CD76',
        '#FAC958',
        '#FB7374',
        '#58DCCF',
        '#73C0DF',
        '#3BA272',
      ],

      legend: {
        type: 'scroll',
        orient: 'vertical',
        right: 50,
        top: 'middle',
        itemGap: 20,
        itemWidth: 15,
        itemHeight: 15,
        textStyle: {
          color: '#7B7D8B',
          fontSize: 14,
        },
      },
      series: [
        {
          type: 'pie',
          radius: ['44%', '70%'],
          center: ['40%', '50%'],
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 5,
          },
          label: {
            show: true,
            color: 'inherit',
            fontSize: 14,
            position: 'outer',
            formatter: params => {
              return params.name + '：' + params.percent + '%';
            },
          },
          labelLine: {
            length: 20,
            length2: 60,
          },
          data: this.chartDataList,
        },
      ],
    });
  }
  // 更新图表
  updateCharts() {
    if (!this.myChart) return this.initCharts();
    this.myChart.setOption({
      series: [
        {
          data: this.chartDataList,
        },
      ],
    });
  }

  // 切换统计列表
  tabStatistList(item) {
    if (item.id === this.currentStatisticId) return;
    this.currentStatisticId = item.id;
    this.updateCharts();
  }
}
</script>

<style scoped lang="scss">
.fl {
  float: left;
}

.fr {
  float: right;
}
.statistic-type-main {
  width: 100%;

  .statistic-type-ul--left {
    width: 325px;
  }
  .statistic-type-ul--right {
    width: calc(100% - 325px);
  }
}
</style>

const socket_url = process.env.VUE_APP_SCAN_SOCKET;
const socket = {
  // 初始化
  init() {
    this.listener = {};
    this.rooms = {};
    this.initSocket();
    setInterval(() => {
      this.sendMessage({ type: 'heartBeat' });
    }, 20000);
  },
  initSocket() {
    this.webSocket = new WebSocket(socket_url);
    this.webSocket.onopen = e => {
      console.log('socket开启');
      this.roomsInit();
    };
    this.webSocket.onclose = e => {
      console.log('socket关闭');
      this.webSocket = null;
      setTimeout(() => {
        this.initSocket();
      }, 10000);
    };
    this.webSocket.onmessage = e => {
      console.log('msg:' + e.data);
      try {
        let data = JSON.parse(e.data);
        let func = this.listener[data.type];
        if (func) {
          func(data);
        }
      } catch (e) {
        console.log(e);
      }
    };
  },
  roomsInit() {
    console.log('rooms>>>', this.rooms);
    for (let room in this.rooms) {
      if(!this.joinRoom(room)){
        return;
      }
    }
  },
  joinRoom(room) {
    let msg = {
      type: 'joinRoom',
      room: room,
    };
    this.rooms[room] = 1;
    return this.sendMessage(msg);
  },
  leaveRoom(room) {
    let msg = {
      type: 'leaveRoom',
      room: room,
    };
    delete this.rooms[room];
    this.sendMessage(msg);
  },
  addListener(event, func) {
    console.log('addListener:' + event);
    this.listener[event] = func;
  },
  removeListener(event) {
    console.log('removeListener:' + event);
    delete this.listener[event];
  },
  sendMessage(msg) {
    console.log('sendMessage:' + JSON.stringify(msg));
    if (!this.webSocket || this.webSocket.readyState == 0) {
      setTimeout(() => {
        this.roomsInit();
      }, 1000);
      return false;
    }
    this.webSocket.send(JSON.stringify(msg));
    return true;
  },
  closeSocket() {
    if (this.webSocket) {
      this.webSocket.close();
    }
  },
};

socket.init();

export default socket;

<template>
  <div class="reportCard">
    <!-- <headerFilter @updateFilter="updateFilter" ref="headerFilter" @init="initFilter"></headerFilter> -->
    <router-view
      ref="cardHomeRef"
      @hideTotalClass="hideTotalClass"
      @showTotalClass="showTotalClass"
    ></router-view>
  </div>
</template>

<script>
import headerFilter from '@/components/headerFilter.vue';

export default {
  name: 'report-card',
  data() {
    return {
      // 是否初始化
      isInit: false,
      // 当前筛选数据
      filterData: null,
    };
  },
  components: {
    headerFilter,
  },
  mounted() {},
  methods: {
    initFilter({ filterData }) {
      this.isInit = true;
      this.filterData = filterData;
      this.updateFilter(filterData);
    },
    // 班级学科初始化后更新数据
    async updateFilter(data, type) {
      if (!this.isInit) return;
      await this.$nextTick();
      this.$refs.cardHomeRef && this.$refs.cardHomeRef.updateFilter(data, type);
    },
    hideTotalClass(className) {
      this.$refs.headerFilter.hideTotalClass(className);
    },
    showTotalClass() {
      this.$refs.headerFilter.showTotalClass();
    },
  },
};
</script>

<style lang="scss" scoped></style>

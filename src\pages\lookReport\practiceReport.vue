<template>
  <div class="practice">
    <div class="practice-top">
      <!-- 班级 -->
      <div class="header__filter display_flex clearfix">
        <span>班级：</span>
        <ul class="class__ul pull-left flex_1">
          <li class="class__li" :class="{active: filterData.classId === item.id || (!filterData.classId && item.name==='全部')}" v-for="(item, index) in clzs" :key="index" @click="changeFilter('class', item.id)">
            {{item.name}}
          </li>
        </ul>
      </div>
      <!-- 学科 -->
      <div class="header__filter display_flex clearfix bottomLine">
        <span class="leftText pull-left">学科：</span>
        <ul class="class__ul pull-left flex_1">
          <li class="class__li" :class="{active: filterData.subjectId === item.id}" v-for="item in subjectList" :key="item.id" @click="changeFilter('subject', item)">
            {{item.name}}
          </li>
        </ul>
      </div>
    </div>
    <div v-if=" !this.tableQuesData.length" class="nodata">
      <img :src="noResImg" alt="">
      <p class="text-center">{{errorMsg}}</p>
    </div>
    <div v-else>
      <div class="practice-class">
        <div class="titleLine" style="width:90px">班级概况</div>
        <div class="practice-class-one">
          <span class="practice-class-word">正确率</span>
          <span class="practice-class-value">{{classData.rightRate}}%</span>
        </div>
        <div class="practice-class-two">
          <span class="practice-class-word">错题解决率</span>
          <span class="practice-class-value">{{classData.errRate}}%</span>
        </div>
        <div class="practice-class-three">
          <span class="practice-class-word">知识点提升个数</span>
          <span class="practice-class-value">{{classData.pointUpNum}}</span>
        </div>
        <div class="practice-class-four">
          <span class="practice-class-word">提交人数</span>
          <span class="practice-class-value">{{classData.commitNum}}/{{classData.totalNum}}</span>
        </div>

      </div>
      <div class="practice-point">
        <div class="titleLine">知识点分析</div>
        <el-table v-loading="tableLoading" :data="tableKnowledgeData.slice((currentPage-1)*PageSize,currentPage*PageSize)" ref="pointRef" style="width: 100%" stripe class="reportCard-table" :default-sort="{prop:'persRate',order:'descending'}" @sort-change="sortKnowledgeChange">
          <el-table-column prop="knowledgeName" label="知识点" width="400"></el-table-column>
          <el-table-column sortable prop="scoreRate" label="原卷得分率" width="400">
            <template slot-scope="scope">
              <span v-if="Number(scope.row.scoreRate) === -100">--</span>
              <span v-else-if="Number(scope.row.scoreRate)<60" style="color:red">{{scope.row.scoreRate}}%</span>
              <span v-else>{{scope.row.scoreRate}}%</span>
            </template>
          </el-table-column>
          <el-table-column sortable='custom' prop="persRate" label="个性化得分率">
            <template slot-scope="scope">
              <span v-if="Number(scope.row.persRate) === -100">--</span>
              <span v-else-if="Number(scope.row.persRate)<60" style="color:red">{{scope.row.persRate}}%</span>
              <span v-else>{{scope.row.persRate}}%</span>
            </template>
          </el-table-column>
        </el-table>

        <!--分页-->
        <el-pagination background style="margin:30px auto;" :hide-on-single-page="!tableKnowledgeData.length" class="text-center" layout="total, prev, pager, next" @current-change="handleKnowledgeChange" @size-change="handleSizeChange" :current-page.sync="currentPage" :page-size="PageSize" :total="pagination.Knowledgetotal">
        </el-pagination>
      </div>
      <div class="practice-small">
        <div class="titleLine">小题分析</div>
        <el-table v-loading="tableLoading" :data="tableQuesData" :default-sort="{prop:'quesNo',order:'ascending'}" ref="quesRef" style="width: 100%" stripe class="reportCard-table" @sort-change="sortQuesChange">
          <el-table-column prop="quesNo" label="题号" width="190" fixed>
            <template slot-scope="scope">
              <span>原卷第{{scope.row.quesNo}}题</span>
            </template>
          </el-table-column>
          <el-table-column prop="commitNum" label="提交人数" width="180" fixed>
          </el-table-column>
          <el-table-column sortable prop="rate" label="原卷得分率" width="180">
            <template slot-scope="scope">
              <span v-if="Number(scope.row.rate) === -100">--</span>
              <span v-else-if="scope.row.rate<60" style="color:red">{{scope.row.rate}}%</span>
              <span v-else>{{ scope.row.rate}}%</span>
            </template>
          </el-table-column>
          <el-table-column sortable prop="persRate" label="个性化原题得分率">
            <template slot-scope="scope">
              <span v-if="Number(scope.row.persRate) === -100">--</span>
              <span v-else-if="Number(scope.row.persRate)<60" style="color:red">{{scope.row.persRate}}%</span>
              <span v-else>{{scope.row.persRate}}%</span>
            </template>
          </el-table-column>
          <el-table-column sortable prop="normalRate" label="巩固题得分率">
            <template slot-scope="scope">
              <span v-if="Number(scope.row.normalRate) === -100">--</span>
              <span v-else-if="Number(scope.row.normalRate)<60" style="color:red">{{ scope.row.normalRate}}%</span>
              <span v-else>{{scope.row.normalRate}}%</span>
            </template>
          </el-table-column>
          <el-table-column sortable prop="hardRate" label="拔高题得分率">
            <template slot-scope="scope">
              <span v-if="Number(scope.row.hardRate) === -100">--</span>
              <span v-else-if="Number(scope.row.hardRate)<60" style="color:red">{{scope.row.hardRate}}%</span>
              <span v-else>{{scope.row.hardRate}}%</span>
            </template>
          </el-table-column>
        </el-table>

        <!--分页-->
        <el-pagination background style="margin:30px auto;" :hide-on-single-page="!tableQuesData.length" class="text-center" layout="total, prev, pager, next" @current-change="handleQuesChange" @size-change="handleSizeChange" :current-page.sync="pagination.Quespage" :page-size.sync="pagination.pageSize" :total="pagination.Questotal">
        </el-pagination>
      </div>
      <div class="practice-answer">
        <div class="titleLine">学生作答详情</div>
        <el-table v-loading="tableLoading" :data="tableStuData" ref="answerRef" style="width: 100%" stripe class="reportCard-table" :header-cell-style="{fontSize: '16px', color: '#3F4A54',}" @sort-change="sortStuChange">
          <el-table-column prop="stuName" label="姓名" width="200" fixed></el-table-column>
          <el-table-column prop="scoreRate" label="试卷得分率" width="200">
            <template slot-scope="scope">{{scope.row.scoreRate}}%</template>
          </el-table-column>
          <el-table-column prop="persRate" label="个性化正确率">
            <template slot-scope="scope"> {{scope.row.persRate}}% </template>
          </el-table-column>
          <el-table-column prop="noDealNum,totalErrorNum" label="未解决/总错题">
            <template slot-scope="scope">
              {{scope.row.noDealNum}}/{{scope.row.totalErrorNum}}
            </template>
          </el-table-column>
          <el-table-column label="更多">
            <template slot-scope="scope">
              <el-button @click="handleClick(scope.row)" type="text" size="small">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!--分页-->
        <el-pagination background style="margin:30px auto;" :hide-on-single-page="!tableStuData.length" class="text-center" layout="total, prev, pager, next" @size-change="handleStuSizeChange" @current-change="handleStuChange" :current-page.sync="pagination.Stupage" :page-size.sync="pagination.pageSize" :total="pagination.Stutotal">
        </el-pagination>
      </div>
    </div>

  </div>
</template>

<script>
import {
  getClassData,
  getKnowledgeData,
  getQuestionData,
  getStudentData,
  getExamRelation,
  getExamSubject,
  getExamClass,
  listExamSubject,
} from "@/service/pexam";
export default {
  name: "report-card",
  data() {
    return {
      currentPage: 1,
      PageSize: 15,
      classData: "",
      classDataList: [],
      mouseFlag: false,
      mouseOffset: 0,
      searchValue: "",
      pagination: {
        page: 1,
        pageSize: 15,
        total: 0,
        Quespage: 1,
        Stupage: 1,
      },
      scoreList: [],
      tableLoading: false,
      tableData: [],
      tableQuesData: [],
      tableStuData: [],
      tableKnowledgeData: [],
      contrastReportDialogVisible: false,
      filterData: {},
      sortType: "",
      contrastObj: {
        examName: "",
        examId: "",
      },
      queOrderType: "",
      queSortType: "",
      classList:[],
      clsList: [],
      subjectList: [],
      noResImg: require("@/assets/no-res.png"),
      errorMsg: "暂无数据",
      examSubjectList: [],
      subjectId: [],
      subId: [],
      clzs: []
    };
  },
  components: {},
  mounted() {
    this.getExamRelation();
    this.getExamClass();
  },
  methods: {
    // 切换班级/学科筛选项
    changeFilter(type, item) {
      switch (type) {
        case "class":
          this.filterData.classId = item === "全部" ? "" : item;
          break;
        default:
          this.filterData.subjectId = item.id;
          this.filterData.phaseId = item.phaseId;
          break;
      }
     
      if (this.tableQuesData.length) {
        for(let k in this.$refs){
           this.$refs[k].clearSort();
           if(k == "pointRef"){
             this.$refs[k].sort("persRate","descending");
           }
           
        }
      } 

      this.currentPage = 1;
      this.pagination.Quespage = 1;
      this.pagination.Stupage = 1;
      this.getClassData();
      this.getQuestionData();
      this.getKnowledgeData();
      this.getStudentData()
    },
    // 知识点分析分页查询
    // 每页显示的条数
    handleSizeChange(val) {
      // 改变每页显示的条数
      this.PageSize = val;
      this.pagination.pageSize = val;
      this.pagination.Quespage = 1;
      // 注意：在改变每页显示的条数时，要将页码显示到第一页
      this.currentPage = 1;
    },
    handleKnowledgeChange(val) {
      this.currentPage = val;
      // this.getKnowledgeData()
    },
    handleQuesChange(val) {
      this.pagination.Quespage = val;
      this.getQuestionData();
    },
    //学生作答详情
    handleStuSizeChange(val) {
      // 改变每页显示的条数
      this.pagination.pageSize = val;
      // 注意：在改变每页显示的条数时，要将页码显示到第一页
      this.pagination.Stupage = 1;
    },
    handleStuChange(val) {
      this.pagination.Stupage = val;
      this.getStudentData();
    },

    //获取班级学科
    getExamRelation() {
      let innerSubjectList = this.$sessionSave.get("innerSubjectList");
      let innerClasstList = this.$sessionSave.get("innerClassList");

      this.clsList = innerClasstList;
      getExamRelation({
        examId: this.$sessionSave.get("reportDetail").examId || "",
      }).then((data) => {
          this.getExamSubject();
          this.errorMsg = "暂无数据";
          this.classList = data.data.classIdArr;
          this.classList.unshift("全部");
          this.subjectId = data.data.subjectIdArr;
        }).catch((err) => {
          console.log(err);
        });
    },
    getExamSubject() {
      listExamSubject({
        examId: this.$sessionSave.get("reportDetail").examId || "",
        v: this.$sessionSave.get("reportDetail").v || "",
        statType: 1
      }).then((data) => {
          data.data.forEach((item) => {
            this.subjectId.forEach((it) => {
              if (item.id === Number(it)) {
                this.subjectList.push(item);
                this.filterData.subjectId = this.subjectList[0].id;
              }
            });
          });
          this.getClassData();
          this.getQuestionData();
          this.getKnowledgeData();
          this.getStudentData();
        }).catch((err) => {
          console.log(err);
        });
    },
    // 获取班级
    getExamClass() {
      getExamClass({
        examId: this.$sessionSave.get("reportDetail").examId,
      })
        .then((data) => {
          let $this = this;
          let clzs = [];
          data.data.forEach(item => {
            let cls = $this.clsList.find(q=>q.id == item);
            if(cls){
              clzs.push({id:cls.id,name:cls.class_name})
            }else{
              clzs.push({id:item,name:item});
            }
          });

          this.clzs = clzs;
          

          this.clzs.unshift({id:"全部",name:"全部"});
        })
        .catch((error) => {
          console.log(error);
        });
    },
    //班级概况
    getClassData(type) {
      getClassData({
        examId: this.$sessionSave.get("reportDetail").examId || "",
        classId: this.filterData.classId || "",
        subjectId: this.filterData.subjectId || "",
      }).then((data) => {
          this.tableLoading = false;
          if (data.data.length) {
            this.classDataList = data.data;
          } else {
            this.classDataList = [];
          }
          this.classDataList.forEach((item) => {
            this.classData = item;
          });
        }).catch((err) => {
          console.log(err);
        });
    },

    //小题分析
    getQuestionData() {
      getQuestionData({
        examId: this.$sessionSave.get("reportDetail").examId,
        classId: this.filterData.classId,
        subjectId: this.filterData.subjectId,
        sortType: this.queSortType,
        sortOrder: this.queOrderType,
        page: this.pagination.Quespage,
        pageSize: 15,
      }).then((data) => {
          this.tableLoading = false;
          if (data.data.list.length) {
            //根据题号升序
            data.data.list.sort(function (a, b) {
              return a.quesNo - b.quesNo;
            });
            data.data.list.forEach((item) => {
              item.persRate = Number(item.persRate);
              item.hardRate = Number(item.hardRate);
              item.normalRate = Number(item.normalRate);
              item.rate = Number(item.rate);
            });
            this.tableQuesData = data.data.list;
            this.pagination.Questotal = data.data.total;
          } else {
            this.tableQuesData = [];
          }
        }).catch((err) => {
          console.log(err);
        });
    },
    //知识点分析
    getKnowledgeData() {
      getKnowledgeData({
        examId: this.$sessionSave.get("reportDetail").examId,
        classId: this.filterData.classId,
        subjectId: this.filterData.subjectId,
      }).then((data) => {
          this.tableLoading = false;
          data.data.forEach((item) => {
            item.persRate = Number(item.persRate);
            item.scoreRate = Number(item.scoreRate);
          });
          //根据个性化得分率降序
          data.data.sort(function (a, b) {
            return b.persRate - a.persRate;
          });
          this.pagination.Knowledgetotal = data.data.length;
          this.tableKnowledgeData = data.data;
        }).catch((err) => {
          console.log(err);
        });
    },
    //学生作答详情
    getStudentData() {
      getStudentData({
        examId: this.$sessionSave.get("reportDetail").examId,
        classId: this.filterData.classId,
        subjectId: this.filterData.subjectId,
        page: this.pagination.Stupage,
        // pageSize  : 15
      }).then((data) => {
          this.tableLoading = false;
          if (data.data.list.length) {
            this.tableStuData = data.data.list;
            this.pagination.Stutotal = data.data.total;
          } else {
            this.tableStuData = [];
          }
        }).catch((err) => {
          console.log(err);
        });
    },
    // 点击查看详情
    handleClick(item) {
      this.$sessionSave.set("reportCardSearchVal", this.searchValue);
      this.$router.push({
        path: "/home/<USER>/personalPracticeReport/reportDetail",
        query: {
          quesIds: item.quesIds,
          stuId: item.stuId,
          clsId:item.classId,
          subId: item.subjectId,
          fromName: "practiceReport",
        },
      });
    },
    sortKnowledgeChange({ prop, order }) {
      if(order == "ascending"){
        this.tableKnowledgeData.sort((q1,q2)=>Number(q1[prop])-Number(q2[prop]));
      }else{
        this.tableKnowledgeData.sort((q1,q2)=>Number(q2[prop])-Number(q1[prop]));
      }
    },
    sortQuesChange({ prop, order }) {
        switch(prop){
            case "rate":
                 this.queSortType = 1;
                 break;
            case "persRate":
                 this.queSortType = 2;
                 break;
            case "normalRate":
                 this.queSortType = 3;
                 break
            case "hardRate":
                 this.queSortType = 4;
                 break;
            default:
                 this.queSortType = 0;
                 break;
            
        }

      if (order == "descending") {
        this.queOrderType = "desc";
      } else if (order == "ascending") {
        this.queOrderType = "asc";
      } else {
        this.queOrderType = "";
      }
      this.getQuestionData();
    },
    sortStuChange({ prop, order }) {
      this.pagination.Stupage = 1;
      if (prop === "total") {
        this.sortType = order === "ascending" ? prop : "-" + prop;
      } else {
        this.sortType =
          order === "ascending" ? "subject_" + prop : "-subject_" + prop;
      }
    },
  },

  beforeDestroy() {
    this.$sessionSave.set("backContrastObj", false);
  },
};
</script>

<style lang="scss" scoped>
.practice-class {
  height: 220px;
  display: flex;
  flex-direction: row;
  .practice-class-one,
  .practice-class-four {
    position: relative;
    width: 290px;
    height: 140px;
    background-color: rgba(80, 173, 233, 1);
    margin-top: 60px;
    margin-left: 10px;
    border-radius: 10px;
    .practice-class-word {
      font-size: 20px;
      color: aliceblue;
      position: absolute;
      margin-top: 30px;
      margin-left: 20px;
    }
    .practice-class-value {
      position: absolute;
      margin-top: 60px;
      margin-left: 30px;
      font-size: 28px;
      color: aliceblue;
    }
  }
  .practice-class-one {
    background-image: url("../../assets/right_rate.png");
  }
  .practice-class-two {
    background-image: url("../../assets/wrong_solve.png");
  }
  .practice-class-three {
    background-image: url("../../assets/improve_know.png");
  }
  .practice-class-four {
    background-image: url("../../assets/commit_num.png");
  }
  .practice-class-two,
  .practice-class-three {
    position: relative;
    width: 290px;
    height: 140px;
    border-radius: 10px;
    margin-top: 60px;
    margin-left: 10px;
    .practice-class-word {
      font-size: 20px;
      color: aliceblue;
      position: absolute;
      margin-top: 30px;
      margin-left: 20px;
    }
    .practice-class-value {
      position: absolute;
      margin-top: 60px;
      margin-left: 30px;
      font-size: 28px;
      color: aliceblue;
    }
  }
  .practice-class-one {
    margin-left: -85px;
  }
  .titleLine {
    display: inline-block;
    position: relative;
    height: 54px;
    line-height: 54px;
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;
    padding-left: 16px;
    &:before {
      content: "";
      width: 6px;
      height: 24px;
      background: #409eff;
      border-radius: 3px;
      position: absolute;
      left: 0;
      top: 15px;
    }
  }
}
.practice-answer,
.practice-small,
.practice-point {
  margin-top: 25px;
  .titleLine {
    display: inline-block;
    position: relative;
    height: 54px;
    line-height: 54px;
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;
    padding-left: 16px;
    &:before {
      content: "";
      width: 6px;
      height: 24px;
      background: #409eff;
      border-radius: 3px;
      position: absolute;
      left: 0;
      top: 15px;
    }
  }
}
.reportCard-title {
  margin: 15px 0 18px;
  .selectClass {
    color: #409eff;
    font-size: 16px;
  }
  .addCompare {
    width: 136px;
    height: 36px;
    padding: 0;
    text-align: center;
    line-height: 35px;
    background: #409eff;
    border-radius: 4px;
    margin-left: 20px;
    color: #fff;
  }
  .header__select {
    display: inline-block;
    margin-right: 29px;
  }
  .header__serarch {
    display: flex;
    width: 240px;
    .search__icon {
      width: 38px;
      font-size: 18px;
      color: #fff;
      background: #409eff;
      border-radius: 0 3px 3px 0;
      outline: none;
      cursor: pointer;
    }
  }
}

.reportCard-table {
  cursor: move;
  border: 1px solid #e4e8eb;
}
</style>
<style lang="scss">
.search__text {
  .el-input__inner {
    border-radius: 4px 0 0 4px;
  }
}

.reportCard-table {
  &.el-table th,
  .el-table td,
  &.el-table .cell {
    text-align: center;
  }
  &.el-table thead {
    font-size: 16px;
    color: rgb(63, 74, 84);
  }
  &.el-table th.is-leaf {
    border-bottom: 0.5px solid #ebeef5;
  }
  .el-table td,
  .el-table th.is-leaf {
    border: 0.5px solid #ebeef5;
  }
}

.paperTable {
  border: 1px solid #e4e8eb;
  &.el-table th,
  &.el-table td {
    text-align: center;
  }
  &.el-table thead {
    font-size: 16px;
    color: rgb(63, 74, 84);
  }

  .el-table td,
  .el-table th.is-leaf {
    border: 0.5px solid #ebeef5;
  }
}
.practice-top {
  .header__filter {
    margin: 10px 0;
    .class__li {
      display: inline-block;
      margin-right: 25px;
      margin-bottom: 5px;
      padding: 0 8px;
      border-radius: 3px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      &.active,
      &:hover {
        color: #409eff;
      }
      &.moreClass {
        height: 30px;
        line-height: 30px;
        border-radius: 15px;
        padding: 0 4px 0 8px;
        .el-icon-close {
          display: none;
        }
      }
      &.moreActive {
        background: #f5faff;
        .el-icon-close {
          display: inline-block;
        }
      }
      &.allActive {
        color: #409eff;
        background: #fff;
        .el-icon-close {
          display: none;
        }
      }
    }
    &.bottomLine {
      border-bottom: 1px solid #e4e8eb;
      padding-bottom: 8px;
    }
  }
}
</style>

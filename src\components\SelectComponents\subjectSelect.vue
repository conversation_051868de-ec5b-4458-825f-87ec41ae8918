<!--
 * @Descripttion: 年级选择组件
 * @Author: 小圆
 * @Date: 2024-01-17 11:10:45
 * @LastEditors: 小圆
-->
<template>
  <div class="header__select">
    <span class="select__label">学科：</span>
    <el-select v-model="subjectValue" class="select" @change="handleChange" placeholder="请选择">
      <el-option
        v-for="item in subjectListFilter"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { getSubjectListByRole } from '@/service/api';
import UserRole from '@/utils/UserRole';
export default {
  props: {
    value: {
      type: [String, Number],
      default: '',
    },
    phaseId: {
      type: [String, Number],
      default: '',
    },

    // 不显示全部
    noAll: {
      type: Boolean,
      default: false,
    },

    // 不限制角色
    noRole: {
      type: Boolean,
      default: false,
    },

    // 入学年份
    enterYear: {
      type: Number | String,
      default: '',
    },

    excludeRoles: {
      type: Array,
      default() {
        return [];
      },
    },

    systemCode: {
      type: String,
      default: '',
    }
  },
  watch: {
    value(val, oldVal) {
      this.subjectValue = val;
    },

    phaseId(val) {
      if (this.isInitialize) {
        this.subjectValue = this.subjectListFilter[0]?.id;
        this.$emit('input', this.subjectValue);
      }
    },

    async enterYear(val) {
      if (this.isInitialize) {
        this.subjectValue = this.subjectListFilter[0]?.id;
        this.$emit('input', this.subjectValue);
      }
    },
  },

  data() {
    return {
      subjectList: [],
      subjectValue: '',
      isInitialize: false,
    };
  },

  computed: {
    subjectListFilter() {
      if (!this.phaseId) return this.subjectList;
      const subjectList = this.subjectList.filter(
        item => item.id == '' || item.phaseId == this.phaseId
      );
      return subjectList;
    },
  },

  mounted() {
    // this.initSubject();
  },

  methods: {
    // 初始化年级学科
    async initSubject() {
      this.subjectList = await UserRole.getSubjectList({
        systemCode: this.systemCode,
        year: this.enterYear,
        excludeRoles: this.excludeRoles,
      });
      if (UserRole.isOperation || UserRole.isSchoolLeader) {
        this.subjectList.unshift({
          id: '',
          name: '全部学科',
        });
      }

      this.subjectValue = this.subjectList[0]?.id;
      if (this.value) {
        this.subjectValue = this.value;
      }
      this.$emit('input', this.subjectValue);
      this.isInitialize = true;
      return this.subjectListFilter;
    },

    handleChange(val) {
      this.$emit('input', val);
      const subject = this.subjectList.find(item => val == item.id);
      this.$emit('change', val, subject);
    },
  },
};
</script>

<style lang="scss" scoped>
@import './select.scss';
</style>

<template>
  <div>
    <el-dialog
      custom-class="set-correct-methods-container"
      :visible="modalVisible"
      width="890px"
      :before-close="closeModal"
      :close-on-click-modal="false"
    >
      <div slot="title" class="dialog-title">
        <span style="margin-right: 10px">评阅方式</span>
      </div>
      <div class="sele-tea-area">
        <el-form :model="correctInfo">
          <el-form-item label="评阅方式">
            <el-radio-group
              v-model="correctInfo.correctMode"
              @change="changeCorrectMode"
            >
              <el-radio :label="1" :disabled="disabledSet">单评</el-radio>
              <el-radio :label="2" :disabled="isSetByClass">双评</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="开启仲裁" v-if="correctInfo.correctMode == 2">
            <el-switch  v-model="correctInfo.isArbitration"></el-switch>
            <template v-if="correctInfo.isArbitration">
              <span style="margin-left: 40px">
                分差大于<el-input-number
                  :min="0"
                  :max="Number(correctInfo.score)"
                  v-model="correctInfo.beyondScore"
                  size="small"
                />分，进行仲裁
              </span>
              <div style="margin-left: 80px">
                <el-checkbox v-model="correctInfo.isCheckScore"
                  >仲裁时可查看两位阅卷老师的评分</el-checkbox
                >
              </div>
            </template>
          </el-form-item>
          <el-form-item label="最终得分" v-if="correctInfo.correctMode == 2">
            <el-select
              style="width: 250px"
              v-model="correctInfo.finalScore"
              placeholder="请选择活动区域"
            >
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="(item, index) in finalScoreList"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <!-- </div> -->
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeModal">取 消</el-button>
        <el-button type="primary" :loading="isComfirming" @click="sureClick">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    modalVisible: {
      type: Boolean,
      required: true,
    },
    currentQues: {
      type: Object,
      required: true,
    },
    progress: {
      type: Number,
      required: true,
    },
    quesList: {
      type: Array,
      required: false,
    },
  },
  data() {
    return {
      isComfirming: false,
      correctInfo: {
        correctMode: 1, //1单评，2双评 默认1
        isArbitration: true, //false:关闭仲裁,true:开启仲裁
        beyondScore: null, //分差大于多少分，开启仲裁
        isCheckScore: false, //仲裁时是否可查看两位阅卷老师的评分
        finalScore: 1, //1:四舍五入保留一位小数（取0.5）,2:四舍五入取整
        arbitrationTeacherList: [], //仲裁老师列表
      },
      finalScoreList: [
        {
          value: 1,
          label: '四舍五入保留一位小数取0.5',
        },
        {
          value: 2,
          label: '四舍五入取整',
        },
      ],
    };
  },
  computed: {
    disabledSet() {
      return this.progress >= 4;
    },
    //是否设置了按任教班级
    isSetByClass() {
      return this.quesList.some(item => {
        return item.assignType == 4;
      });
    },
  },
  mounted() {
    this.correctInfo = JSON.parse(JSON.stringify(this.currentQues));
  },
  methods: {
    changeCorrectMode() {
      if (this.correctInfo.correctMode == 2) {
        this.correctInfo.isArbitration = true;
      }
    },
    closeModal() {
      this.$emit('close-set-correct-modal');
    },
    sureClick() {
      if (
        this.correctInfo.correctMode == 2 &&
        this.correctInfo.isArbitration &&
        this.correctInfo.beyondScore == 0
      ) {
        this.$message({
          message: '请设置分差大于多少分进行仲裁!',
          type: 'warning',
          duration: 1000,
        });
        return;
      }
      this.$emit('confirm-set-correct', this.correctInfo);
      this.$emit('close-set-correct-modal');
    },
  },
};
</script>

<style lang="scss">
.set-correct-methods-container {
  .ivu-table-body {
    max-height: 255px;
    overflow-x: hidden;
    overflow-y: auto;
  }
  .el-dialog__header {
    height: 45px;

    .dialog-title {
      line-height: 45px;
    }
  }
}
</style>
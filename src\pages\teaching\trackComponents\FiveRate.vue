<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-05-14 08:49:18
 * @LastEditors: 小圆
-->
<template>
  <div>
    <div class="title">
      五率分布
      <span class="tips"
        >注：优秀率、优良率、及格率低于年级平均标红显示，不及格率、低分率高于年级平均标红显示。</span
      >
    </div>
    <el-radio-group style="padding: 20px" v-model="chartType" @input="renderChart(fiveRateResData)">
      <el-radio-button :label="0">按考试维度</el-radio-button>
      <el-radio-button :label="1">按班级维度</el-radio-button>
    </el-radio-group>
    <div ref="fiveRateChart" style="width: 100%"></div>
    <div style="width: 100%; padding: 20px">
      <div v-if="tableData.length">
        <!-- 表格 -->
        <el-table
          style="width: 100%"
          :data="tableData"
          :headerCellStyle="{
            fontSize: '14px',
            color: '#3F4A54',
            backgroundColor: '#f5f7fa',
          }"
          border
          v-drag-table
        >
          <el-table-column prop="clzName" label="班级" fixed="left" align="center" :min-width="160">
          </el-table-column>
          <template v-for="item in fiveRateHeader">
            <el-table-column align="center" :label="item.name" :min-width="120">
              <template v-for="(exam, i) in item.list">
                <el-table-column
                  :label="exam.name"
                  align="center"
                  :class="colorList[i]"
                  :min-width="110"
                >
                  <template #default="scope">
                    <span
                      v-if="['fail', 'lower'].includes(item.value)"
                      :style="{
                        color:
                          scope.row[exam.id + item.value] > fiveRateData[0][exam.id + item.value]
                            ? 'red'
                            : 'inherit',
                      }"
                      >{{ scope.row[exam.id + item.value] == '--' ? '--' : scope.row[exam.id + item.value] + '%' }}</span
                    >
                    <span
                      v-else
                      :style="{
                        color:
                          scope.row[exam.id + item.value] < fiveRateData[0][exam.id + item.value]
                            ? 'red'
                            : 'inherit',
                      }"
                      >{{ scope.row[exam.id + item.value] == '--' ? '--' : scope.row[exam.id + item.value] + '%' }}</span
                    >
                  </template>
                </el-table-column>
              </template>
            </el-table-column>
          </template>
        </el-table>
        <div
          class="score-table-more"
          @click="isShowMore = !isShowMore"
          v-if="fiveRateData.length > defaultSliceNum"
        >
          <span class="more-button"
            >{{ !isShowMore ? `展 开 ` : '收 起 ' }}
            <i :class="[isShowMore ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"></i>
          </span>
        </div>
      </div>
      <no-data v-else style="width: 350px; height: 350px; margin: 0 auto"></no-data>
    </div>
  </div>
</template>

<script lang="ts">
import NoData from '@/components/noData.vue';
import { getHisFiveRateAPI } from '@/service/pexam';
import { Component, Vue } from 'vue-property-decorator';

interface FiveRateAPI {
  code: number;
  data: Datum[];
  msg: string;
}

interface Datum {
  clzId: string;
  clzName: string;
  examList: ExamList[];
  fiveRateList: FiveRateList[];
}

interface ExamList {
  id: number;
  name: string;
}

interface FiveRateList {
  examId: number;
  avgScore: string | number;
  passAvgRate: string | number;
  fine: string | number;
  good: string | number;
  pass: string | number;
  fail: string | number;
  lower: string | number;
}

const colorList = [
  '#fdf5e6',
  '#f0f9eb',
  '#ebf9f0',
  '#ebf8f9',
  '#ebf1f9',
  '#edebf9',
  '#f2ebf9',
  '#f6ebf9',
  '#f4f9eb',
  '#f9eeeb',
];

const chartColor = ['#C6C9CC', '#FF6A68', '#FFB400', '#3E73F6', '#07C29D'];

const legend = {
  icon: 'circle',
  top: 10,
  right: 40,
  textStyle: {
    color: '#757C8C',
    fontSize: '14',
  },
  data: ['优秀率', '优良率', '及格率', '不及格率', '低分率'],
};
// 数组对象去重
function removeDuplicates(data) {
  const set = new Set();
  data.forEach(item => {
    set.add(JSON.stringify(item));
  });
  const array = Array.from(set).map((item: string) => JSON.parse(item));
  return array;
}

const defaultSliceNum = 8;

@Component({
  components: {
    NoData,
  },
})
export default class FiveRate extends Vue {
  public $parent!: any;
  public $refs: {
    fiveRateChart: any;
  };

  // 一分五率原始数据
  fiveRateResData: Datum[] = null;
  // 一份五率表格数据
  fiveRateData: Datum[] = [];
  // 一分五率头部
  fiveRateHeader: { name: string; value: string; list: ExamList[] }[] = [];
  // 一分五率图表
  fiveRateChart: EChartsType = null;
  // 考试列表
  examList: ExamList[] = [];
  // 颜色列表
  colorList: typeof colorList = colorList;
  // 是否显示全部
  isShowMore: boolean = false;
  // 图表类型 0：按考试；1：按班级
  chartType: 0 | 1 = 0;

  get defaultSliceNum() {
    return defaultSliceNum;
  }

  get tableData() {
    if (this.isShowMore) {
      return this.fiveRateData;
    }
    return this.fiveRateData.slice(0, defaultSliceNum);
  }

  created() {
    this.$bus.$on('onGetData', this.handleLinstening);
  }

  beforeDestroy() {
    this.$bus.$off('onGetData', this.handleLinstening);
  }

  mounted() {
    this.$parent.getHisFiveRate();
  }

  handleLinstening(params) {
    let ids = params.ids;
    let filterInfo = params.filterInfo;
    this.getHisFiveRate(ids, filterInfo.subjectIdTrack || '');
  }

  async getHisFiveRate(ids, subjectId) {
    try {
      if(ids && ids.length == 0) {
        return;
      }

      const res = (await getHisFiveRateAPI({
        examIds: ids.join(','),
        subjectId: subjectId,
      })) as FiveRateAPI;
      if (res.code == 1) {
        let fiveRateData = [];
        res.data.forEach(item => {
          let obj: any = {};
          obj.clzName = item.clzId == 'all' ? '年级' : item.clzName;
          let avgList = [];
          for (let i = 0; i < item.examList.length; i++) {
            let exam = item.examList[i];
            let rate = item.fiveRateList[i];
            if (!exam || !rate) continue;
            if (item.clzId == 'all') {
              avgList[exam.id] = rate.avgScore;
            } else {
              obj['tip'] = '1';
            }
            obj[exam.id + 'avgScore'] = rate.avgScore == '-1' ? '--' : rate.avgScore;
            obj[exam.id + 'passAvgRate'] = rate.passAvgRate == '-1' ? '--' : rate.passAvgRate;
            obj[exam.id + 'fail'] = rate.fail == '-1' ? '--' : rate.fail;
            obj[exam.id + 'fine'] = rate.fine == '-1' ? '--' : rate.fine;
            obj[exam.id + 'good'] = rate.good == '-1' ? '--' : rate.good;
            obj[exam.id + 'lower'] = rate.lower == '-1' ? '--' : rate.lower;
            obj[exam.id + 'pass'] = rate.pass == '-1' ? '--' : rate.pass;
          }
          fiveRateData.push(obj);
        });
        const examList = removeDuplicates(res.data.map(item => item.examList).flat());
        this.fiveRateHeader = [
          { name: '优秀率', value: 'fine', list: examList },
          { name: '优良率', value: 'good', list: examList },
          { name: '及格率', value: 'pass', list: examList },
          { name: '不及格率', value: 'fail', list: examList },
          { name: '低分率', value: 'lower', list: examList },
        ];
        this.fiveRateResData = res.data;
        this.fiveRateData = fiveRateData;
        this.examList = examList;
      }
    } catch (error) {
      console.error(error);
      this.fiveRateHeader = [];
      this.fiveRateResData = [];
      this.fiveRateData = [];
    }
    this.renderChart(this.fiveRateResData);
  }

  renderChart(data: Datum[]) {
    if (this.fiveRateChart) {
      this.fiveRateChart.dispose();
      this.fiveRateChart = null;
    }

    if (!data.length) return;
    this.fiveRateChart = this.$echarts.init(this.$refs.fiveRateChart, null, {
      height: 450,
    });

    if (this.chartType == 0) {
      this.renderChartByExam(data);
    } else {
      this.renderChartByClass(data);
    }
  }

  // 学科维度渲染图表
  renderChartByExam(data: Datum[]) {
    let series = [];
    const label = {
      show: true,
      color: '#fff',
      position: 'inside',
      textBorderWidth: 2,
      textBorderType: 'solid',
    };
    data.forEach(item => {
      series.push(
        {
          name: '低分率',
          type: 'bar',
          stack: item.clzName,
          label: {
            ...label,
            textBorderColor: '#C6C9CC',
            formatter: '{c}%',
          },
          data: item.fiveRateList.map(item => (item.lower == '-1' ? 0 : item.lower)),
        },
        {
          name: '不及格率',
          type: 'bar',
          stack: item.clzName,
          label: {
            ...label,
            textBorderColor: '#FF6A68',
            formatter: '{c}%',
          },
          data: item.fiveRateList.map(item => (item.fail == '-1' ? 0 : item.fail)),
        },
        {
          name: '及格率',
          type: 'bar',
          stack: item.clzName,
          label: {
            ...label,
            textBorderColor: '#FFB400',
            formatter: '{c}%',
          },
          data: item.fiveRateList.map(item => (item.pass == '-1' ? 0 : item.pass)),
        },
        {
          name: '优良率',
          type: 'bar',
          stack: item.clzName,
          label: {
            ...label,
            textBorderColor: '#3E73F6',
            formatter: '{c}%',
          },
          data: item.fiveRateList.map(item => (item.good == '-1' ? 0 : item.good)),
        },
        {
          name: '优秀率',
          type: 'bar',
          stack: item.clzName,
          label: {
            ...label,
            textBorderColor: '#07C29D',
            formatter: '{c}%',
          },
          data: item.fiveRateList.map(item => (item.fine == '-1' ? 0 : item.fine)),
        }
      );
    });

    let option: EChartsOption = {
      tooltip: {
        show: true,
        axisPointer: {
          type: 'shadow',
        },
        formatter: params => {
          const seriesIndex = params.seriesIndex;
          let index = Math.floor(seriesIndex / 5) % data.length;
          let className = data[index].clzName;
          let examHtml = `<div style="font-size:14px;color:#666;font-weight:400;line-height:1.25;">${params.name}（${className}）</div>`;
          let valueHtml = `<div class="clearfix" style="margin-top: 5px">${params.marker}<span>${params.seriesName}</span><span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${params.value}%</span></div>`;
          return examHtml + valueHtml;
        },
      },
      color: chartColor,
      legend: legend,
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 0,
          end: (6 / data.length) * 100,
        },
      ],
      xAxis: {
        type: 'category',
        name: '考试',
        data: this.examList.map(item => item.name),
      },
      yAxis: {
        type: 'value',
        name: '五率',
      },
      series: series,
    };

    this.fiveRateChart.setOption(option);
  }

  // 班级维度渲染图表
  renderChartByClass(data: Datum[]) {
    let series = [];

    let examArr: { name: string; id: string | number; fiveRateList: FiveRateList[] }[] = [];
    this.examList.forEach((item, index) => {
      let examObj: { name: string; id: string | number; fiveRateList: FiveRateList[] } = {
        name: item.name,
        id: item.id,
        fiveRateList: [],
      };

      data.forEach(datum => {
        const fiveRate = datum.fiveRateList[index];
        examObj.fiveRateList.push(fiveRate);
      });
      examArr.push(examObj);
    });

    const label = {
      show: true,
      color: '#fff',
      position: 'inside',
      textBorderWidth: 2,
      textBorderType: 'solid',
    };

    examArr.forEach(item => {
      series.push(
        {
          name: '低分率',
          type: 'bar',
          stack: item.name,
          label: {
            ...label,
            textBorderColor: '#C6C9CC',
          },
          data: item.fiveRateList.map(item => (item.lower == '-1' ? 0 : item.lower)),
        },
        {
          name: '不及格率',
          type: 'bar',
          stack: item.name,
          label: {
            ...label,
            textBorderColor: '#FF6A68',
          },
          data: item.fiveRateList.map(item => (item.fail == '-1' ? 0 : item.fail)),
        },
        {
          name: '及格率',
          type: 'bar',
          stack: item.name,
          label: {
            ...label,
            textBorderColor: '#FFB400',
          },
          data: item.fiveRateList.map(item => (item.pass == '-1' ? 0 : item.pass)),
        },
        {
          name: '优良率',
          type: 'bar',
          stack: item.name,
          label: {
            ...label,
            textBorderColor: '#3E73F6',
          },
          data: item.fiveRateList.map(item => (item.good == '-1' ? 0 : item.good)),
        },
        {
          name: '优秀率',
          type: 'bar',
          stack: item.name,
          label: {
            ...label,
            textBorderColor: '#07C29D',
          },
          data: item.fiveRateList.map(item => (item.fine == '-1' ? 0 : item.fine)),
        }
      );
    });

    let option: EChartsOption = {
      tooltip: {
        show: true,
        axisPointer: {
          type: 'shadow',
        },
        formatter: params => {
          const seriesIndex = params.seriesIndex;
          let index = Math.floor(seriesIndex / 5) % examArr.length;
          let examName = examArr[index].name;
          let examHtml = `<div style="font-size:14px;color:#666;font-weight:400;line-height:1.25;">${params.name}（${examName}）</div>`;
          let valueHtml = `<div class="clearfix" style="margin-top: 5px">${params.marker}<span>${params.seriesName}</span><span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${params.value}</span></div>`;
          return examHtml + valueHtml;
        },
      },
      color: chartColor,
      legend: legend,
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 0,
          end: (6 / data.length) * 100,
        },
      ],
      xAxis: {
        type: 'category',
        data: data.map(item => item.clzName),
        name: '班级',
      },
      yAxis: {
        type: 'value',
        name: '五率',
      },
      series: series,
    };

    this.fiveRateChart.setOption(option);
  }

  checkMore() {}
}
</script>

<style scoped lang="scss">
.title {
  position: relative;

  display: flex;
  align-items: center;
  
  width: 100%;
  line-height: 38px;
  padding: 0 20px;
  padding-top: 10px;

  color: #3f4a54;
  font-size: 16px;
  font-weight: bold;

  background: #fff;

  &:before {
    content: '';
    display: block;
    width: 5px;
    height: 17px;
    background: #409eff;
    border-radius: 3px;
    margin-right: 8px;
  }

  .tips {
    margin-left: 5px;
    font-size: 14px;
    color: red;
    font-weight: normal;
  }
}

.score-table-more {
  display: flex;
  justify-content: center;
  background-color: #f7fbff;
  color: #409eff;
  cursor: pointer;

  .more-button {
    line-height: 40px;
    font-size: 16px;
  }
}
</style>

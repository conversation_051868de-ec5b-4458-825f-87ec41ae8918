import * as httpApi from './index';

const kklUrl = process.env.VUE_APP_KKLURL;


/**
 * api转换
 * @type {{POST: API.POST, GET: API.GET,POSTSTRING: API.POSTSTRING}}
 */
const API = {
    GET: function (url, params) {
        return httpApi.GET(url, params, kklUrl);
    },
    POST: function (url, params) {
        return httpApi.POST(url, params, kklUrl);
    },
    POSTSTRING: function (url, params) {
        return httpApi.POSTSTRING(url, params, kklUrl);
    }
};

/**
 * 识别题目答案信息
 * @param data
 * @returns {*}
 */
export function detectQuesAnswer(data) {
    return API.GET('/pocr/answer_box', data);
}

/**
 * 识别答案内容
 * @param url 答案框地址
 * @returns {*}
 */
export function detectAnswerContent(url) {
    return API.GET('/pocr/answer_content', {path: url});
}
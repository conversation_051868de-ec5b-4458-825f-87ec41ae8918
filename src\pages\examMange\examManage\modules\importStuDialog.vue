<template>
  <el-dialog title="批量导入" :visible.sync="dialogVisible" width="600px" :close-on-click-modal="false"
    :before-close="handleClose" custom-class="import-temp-dialog">
    <div class="dialog-content">
      <span class="warn-tip">批量导入的学科会以表格名单覆盖当前参考学生名单</span>
      <div class="form-item">
        <span class="label required">所属校区</span>
        <el-select v-model="selectedCampus" placeholder="校区" class="class-select">
          <el-option v-for="item in campusList" :key="item.code" :label="item.name" :value="item.code">
          </el-option>
        </el-select>
      </div>
      <div class="form-item">
        <span class="label">下载模板</span>
        <el-button type="primary" size="small" @click="downloadTemplate" class="template-btn">
          <i class="el-icon-download"></i>
          下载模板
        </el-button>
      </div>
      <div class="form-item upload-item">
        <span class="label required">导入文件</span>
        <el-upload class="upload-demo" action="" :auto-upload="false" :on-change="handleFileChange"
          :on-remove="handleFileRemove" :file-list="fileList" :limit="2" accept=".xlsx,.xls">
          <el-button type="primary" size="small">
            <i class="el-icon-upload2"></i>
            选择文件
          </el-button>
          <div slot="tip" class="el-upload__tip">
            <i class="el-icon-info"></i>
            只能上传xlsx/xls文件
          </div>
        </el-upload>
      </div>
      <div class="form-item">
        <span class="label"></span>
        <el-tooltip class="item" effect="dark" content="若已导入临时考号，本次测评将优先以临时考号作为学生考号。" placement="top">
          <el-checkbox v-model="isTempExamNo">优先使用临时考号<i class="el-icon-info"></i></el-checkbox>
        </el-tooltip>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getCampusCodeList } from '@/service/api.js'
import { importExamStuAPI } from '@/service/pexam.js'
import { guid,downloadFileByIframe } from '@/utils/index';
import ossUploadFile from '@/utils/ossUploadFile'
export default {
  name: 'ImportStuDialog',
  data() {
    return {
      dialogVisible: true,
      campusList: [],
      selectedCampus: '',
      fileList: [],
      errFile: '',
      loading: false,
      isTempExamNo: false,
      filePath: 'aliba/exam/import'
    }
  },
  created() {
    this.getCampusList()
    ossUploadFile.getSTSToken(this.filePath)
  },
  methods: {
    /**
     * 根据当前时间拼接文件夹路径
     * @returns {string}
     */
    getDateForPath(examId) {
      let date = new Date()
      let y = date.getFullYear()
      let m = date.getMonth() + 1
      m = m < 10 ? ('0' + m) : m
      let d = date.getDate()
      d = d < 10 ? ('0' + d) : d
      return this.filePath + '/' + y + '/' + m + '/' + d + '/' + examId + '/';
    },
    async getCampusList() {
      let params = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        page: 1,
        limit: 1000
      }
      const res = await getCampusCodeList(params);
      this.campusList = res.data.rows;
      this.selectedCampus = this.campusList[0].code;
    },
    handleClose(isSuccess=false) {
      this.dialogVisible = false
      this.$emit('close', isSuccess == true)
    },
    downloadTemplate() {
      this.$message.success('模板下载中...')
      let url = process.env.VUE_APP_KKLURL + "/pexam/scanExam/downloadExamStuImportTpl?examId=" + this.$route.query.examId;
      downloadFileByIframe(url);
    },
    handleFileChange(file) {
      this.errFile = '';
      const isExcel = file.raw.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.raw.type === 'application/vnd.ms-excel'
      if (!isExcel) {
        this.$message.error('只能上传Excel文件!')
        this.fileList = []
        return false
      }
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('文件大小不能超过 5MB!')
        this.fileList = []
        return false
      }
      this.fileList = [file]
    },
    handleFileRemove() {
      this.fileList = []
    },
    async importStudent(filePath,fileName) {
      let params = {
        tplUrl: filePath,
        originalFileName: fileName,
        examId: this.$route.query.examId,
        campusCode: this.selectedCampus,
        isUseTempExamNo: this.isTempExamNo ? 1 : 0
      }
      let res = await importExamStuAPI(params);
      if (res.code === 1) {
        this.$message.success("处理中，请在操作记录中查看导入结果");
        this.handleClose(true)
      } else {
        this.$message.error(res.res);
      }
    },
    async handleConfirm() {
      if (!this.selectedCampus) {
        this.$message.warning('请选择所属校区')
        return
      }
      if (this.fileList.length === 0) {
        this.$message.warning('请选择要上传的文件')
        return
      }
      this.loading = true
      try {
        let fileName = this.fileList[0].name;
        let filePath = this.getDateForPath(this.$route.query.examId) + guid() + '/' + fileName;
        ossUploadFile.uploadFile(this.fileList[0].raw, filePath, async (res) => {
          if (res.code === 1) {
            await this.importStudent(filePath,fileName);
          } else {
            this.$message.error("上传失败，请重新上传");
          }
        })
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.import-temp-dialog {
  ::v-deep .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e4e7ed;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 500;
    }
  }

  .dialog-content {

    .warn-tip {
      display: block;
      color: #f56c6c;
      font-size: 13px;
      margin-bottom: 16px;
      padding: 8px 16px;
      background-color: #fef0f0;
      border-radius: 4px;
      border: 1px solid #fde2e2;

      &::before {
        content: "⚠";
        margin-right: 8px;
      }
    }

    .form-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 80px;
        line-height: 32px;
        color: #606266;

        &.required::before {
          content: '*';
          color: #f56c6c;
          margin-right: 4px;
        }
      }

      .select-input {
        width: 300px;
      }

      .template-btn {
        .el-icon-download {
          margin-right: 5px;
        }
      }

      &.upload-item {
        .upload-demo {
          margin-left: 0;

          ::v-deep .el-upload-list {
            margin-top: 10px;
            width: 400px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .el-icon-upload2 {
            margin-right: 5px;
          }

          .el-upload__tip {
            display: inline-block;
            color: #909399;
            font-size: 12px;
            margin: 8px;

            .el-icon-info {
              margin-right: 4px;
            }
          }
        }
      }
    }
  }

  ::v-deep .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #e4e7ed;
  }
}
</style>
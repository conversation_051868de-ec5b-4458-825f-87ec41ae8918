<template>
    <el-dialog
        class="stu-growth-dialog"
        :visible.sync="visible"
        title="添加测评"
        width="1300px"
        @closed="$emit('closed')"
    >
        <StuFilterHeader
            class="filter-header"
            v-model="filterData"
            :category-multiple="true"
            :hide-class="true"
            @onMounted="initMounted"
            @onSearch="handlePageChange(1)"
            @change="handlePageChange(1)"
            @change-class="handleClearSelectedExams"
        />
        <div>
            <div class="exam-list">
                <div class="header-row">
                    <el-checkbox v-model="isCheckAll" @change="handleCheckAllChange">全选</el-checkbox>
                    <span class="inline-tip">（为保证数据分析更具有可对比性，请选择总分、学科一致的学科）</span>
                </div>

                <div class="exam-items">
                    <el-checkbox-group v-model="selectedExamIds" @change="handleCheckedExamsChange">
                        <div v-for="item in examList" :key="item.id" class="exam-item">
                            <div class="exam-item-content">
                                <el-checkbox :label="item.id" :disabled="isExamDisabled(item)">
                                    <div class="exam-info-wrapper">
                                        <el-tag v-if="!item.isRole" type="warning" size="mini" style="margin-right: 8px">未发布</el-tag>
                                        <span class="exam-name" :title="item.name">
                                            {{ item.name }}
                                        </span>
                                        <span class="exam-info">
                                            （类型：{{ item.categoryName }}，学科数：<span class="number">{{
                                                item.subjectNum
                                            }}</span
                                            >，总分：<span class="number">{{ item.fullScore }}</span
                                            >，年级：{{ item.gradeName }}，测评时间：{{ item.examTime }}）
                                        </span>
                                    </div>
                                </el-checkbox>
                                <el-radio
                                    v-if="selectedExamIds.includes(item.id)"
                                    v-model="startPointExamId"
                                    :label="item.id"
                                    class="start-point-radio"
                                >
                                    设为起点
                                </el-radio>
                            </div>
                        </div>
                    </el-checkbox-group>
                    <no-data v-if="examList.length == 0" />
                </div>

                <!-- 添加分页 -->
                <div class="pagination-wrapper">
                    <el-pagination
                        background
                        :current-page="pagination.page"
                        :page-size="pagination.pageSize"
                        :total="pagination.total"
                        layout="total, prev, pager, next"
                        @current-change="handlePageChange"
                    />
                </div>
            </div>
        </div>

        <div class="footer-note">注：最多支持选择10场测评进行对比</div>

        <!-- 已选择测评标签 -->
        <div class="selected-exams-wrapper">
            <div class="selected-title">
                <span>
                    已选择测评：<span class="count">{{ selectedExams.length }} </span>场
                </span>

                <el-tag
                    v-for="exam in selectedExams"
                    :key="exam.id"
                    closable
                    size="medium"
                    class="exam-tag"
                    :disable-transitions="true"
                    @close="handleRemoveSelectedExam(exam.id)"
                >
                    {{ exam.name }}
                </el-tag>
            </div>
            <div class="selected-tags"></div>
        </div>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="$emit('closed')">取 消</el-button>
                <el-button type="primary" @click="handleConfirm" :loading="btnLoading">确 定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import StuFilterHeader, { FilterData } from './StuFilterHeader.vue';
import { listHisExamAPI, setUserDataAPI } from '@/service/pexam';
import moment from 'moment';
import UserRole from '@/utils/UserRole';
import NoData from '@/components/noData.vue';

// 最大选择测评数量
const MAX_EXAMS = 10;

interface Exam {
    id: number;
    name: string;
    start?: number;
    // ... 其他测评相关字段
}

@Component({
    components: {
        StuFilterHeader,
        NoData,
    },
})
export default class ExamGrowthDialog extends Vue {
    @Prop({ default: () => [] }) defaultSelectExamList: Exam[];

    // 是否显示对话框
    visible = true;
    // 筛选条件
    filterData: FilterData = {
        source: [1],
        categoryId: '',
        subjectId: '',
        gradeId: '',
        classId: '',
        realname: '',
        stuId: '',
        timeType: 3,
        startYear: '',
        endYear: '',
        timeSlot: [],
        year: '',
    };

    // 测评列表
    examList = [];
    // 是否全选
    isCheckAll = false;
    // 已选测评ID
    selectedExamIds: number[] = [];
    // 已选择的测评完整信息
    selectedExams: Exam[] = [];
    // 添加起点测评ID
    startPointExamId: number | null = null;

    // 分页相关
    pagination = {
        page: 1,
        pageSize: 10,
        total: 0,
    };

    // 加载状态
    isLoading: boolean = false;
    // 按钮加载状态
    btnLoading: boolean = false;


    initMounted() {
        this.selectedExams = this.defaultSelectExamList.map(item => {
            if (item.start == 1) {
                this.startPointExamId = item.id;
            }
            return {
                id: item.id,
                name: item.name,
            };
        });
        this.selectedExamIds = this.selectedExams.map(item => item.id);
        this.getExamList();
    }

    // 获取测评列表
    async getExamList() {
        // 模拟异步请求
        this.isLoading = true;
        // 获取角色
        let role = '';
        if (!UserRole.isOperation) {
            const map = await UserRole.utils.getRoleSubjectClassMap(this.filterData.year);
            role = JSON.stringify(map);
        }

        try {
            const res = await listHisExamAPI({
                schId: this.$sessionSave.get('schoolInfo').id,
                categoryId: (this.filterData.categoryId as string[]).join(',') || '-1',
                grdId: this.filterData.gradeId,
                subjectId: this.filterData.subjectId,
                role: role,
                begin:
                    this.filterData.timeType == 3
                        ? `${this.filterData.startYear}`
                        : moment(this.filterData.timeSlot[0]).format('YYYY-MM-DD'),
                end:
                    this.filterData.timeType == 3
                        ? `${moment(this.filterData.endYear).subtract(1, 'days').format('YYYY-MM-DD')}`
                        : moment(this.filterData.timeSlot[1]).format('YYYY-MM-DD'),
                page: this.pagination.page,
                size: this.pagination.pageSize,
            });
            this.examList = res.data.rows || [];
            this.pagination.total = res.data.total_rows;
            this.isCheckAll = this.checkAll();
        } finally {
            this.isLoading = false;
        }
    }

    // 处理页码变化
    async handlePageChange(page: number) {
        this.pagination.page = page;
        await this.getExamList();
    }

    // 判断测评是否应该被禁用
    isExamDisabled(exam): boolean {
        return (this.selectedExamIds.length >= MAX_EXAMS && !this.selectedExamIds.includes(exam.id)) || !exam.isRole;
    }

    // 检查是否全选
    checkAll() {
        return this.examList.every(exam => this.selectedExamIds.includes(exam.id)) && this.examList.length > 0;
    }

    // 处理全选
    handleCheckAllChange(val: boolean) {
        if (val) {
            this.examList.forEach(item => {
                if (!this.selectedExamIds.includes(item.id) && !this.isExamDisabled(item)) {
                    this.selectedExamIds.push(item.id);
                    this.selectedExams.push(item);
                }
            });
        } else {
            this.examList.forEach(item => {
                if (this.selectedExamIds.includes(item.id)) {
                    this.selectedExamIds = this.selectedExamIds.filter(id => id !== item.id);
                    this.selectedExams = this.selectedExams.filter(exam => exam.id !== item.id);
                }
            });
        }
        this.isCheckAll = this.checkAll();
    }

    // 处理选择变化
    handleCheckedExamsChange(value: number[]) {
        // 更新新选中的测评信息
        const newSelectedExams = this.examList
            .filter(exam => value.includes(exam.id))
            .filter(exam => !this.selectedExams.some(selected => selected.id === exam.id));

        this.selectedExams = [...this.selectedExams.filter(exam => value.includes(exam.id)), ...newSelectedExams];

        // 如果取消选中的测评是当前的起点，则清除起点设置
        if (this.startPointExamId && !value.includes(this.startPointExamId)) {
            this.startPointExamId = null;
        }
        this.isCheckAll = this.checkAll();
    }

    // 当选择某个测评时
    handleSelectExam(exam: Exam) {
        if (!this.selectedExamIds.includes(exam.id)) {
            this.selectedExamIds.push(exam.id);
            this.selectedExams.push(exam);
        }
    }

    // 清空已选择的测评
    handleClearSelectedExams() {
        this.selectedExamIds = [];
        this.selectedExams = [];
        this.startPointExamId = null;
        this.isCheckAll = false;
    }

    // 移除已选择的测评
    handleRemoveSelectedExam(examId: number) {
        this.selectedExamIds = this.selectedExamIds.filter(id => id !== examId);
        this.selectedExams = this.selectedExams.filter(exam => exam.id !== examId);

        // 如果移除的是起点测评，清除起点设置
        if (this.startPointExamId === examId) {
            this.startPointExamId = null;
        }

        this.isCheckAll = this.checkAll();
    }

    // 处理确认按钮点击
    async handleConfirm() {
        if (this.selectedExamIds.length == 0) {
            this.$message.warning('请选择测评');
            return;
        }

        if (this.selectedExamIds.length == 1) {
            this.$message.warning('最少选择两场对比测评');
            return;
        }

        if (!this.startPointExamId) {
            this.$message.warning('请设置起点测评');
            return;
        }

        try {
            this.btnLoading = true;
            await setUserDataAPI({
                userId: this.$sessionSave.get('loginInfo').id,
                type: 1,
                grdId: this.filterData.gradeId,
                year: this.filterData.year,
                subjectId: this.filterData.subjectId,
                jCfg: {
                    data: this.selectedExams.map(item => {
                        return {
                            id: item.id,
                            name: item.name,
                            start: this.startPointExamId == item.id ? 1 : 0,
                        };
                    }),
                },
            });
            this.$message.success('操作成功');
            this.$emit('confirm');
        } catch (error) {
            console.log(error);
        } finally {
            this.btnLoading = false;
        }
    }
}
</script>

<style scoped lang="scss">
.stu-growth-dialog {
    font-size: 14px;

    .filter-header {
        ::v-deep {
            .header__select {
                font-size: 14px;
                margin-right: 15px;
            }

            .el-input__inner {
                font-size: 14px;
            }
        }
    }

    .exam-list {
        background: #fff;
        border-radius: 4px;
        padding: 15px;
        margin-top: 10px;
        // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        // border: 1px solid #e4e7ed;

        .exam-items {
            margin-top: 16px;
            padding: 0;
            background: #fff;
            border-radius: 6px;
            border: 1px solid #e4e7ed;
            max-height: 600px;
            overflow-y: auto;
        }

        .exam-item {
            margin-bottom: 0;
            padding: 16px 20px;
            border-bottom: 1px solid #e4e7ed;
            transition: all 0.3s ease;

            &:last-child {
                border-bottom: none;
            }

            &:hover {
                background: #f5f7fa;
            }

            .exam-item-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .exam-info-wrapper {
                display: flex;

                .exam-name {
                    display: -webkit-box;
                    overflow: hidden;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                    text-overflow: ellipsis;
                    white-space: normal;
                }

                .exam-info {
                    color: #909399;
                    font-size: 13px;
                    margin-left: 8px;
                    line-height: 1.4;

                    .number {
                        color: #d9001b;
                        font-weight: 500;
                        padding: 0 2px;
                    }
                }
            }
        }

        .start-point-radio {
            margin-left: 30px;
            flex-shrink: 0;
            padding-left: 20px;
            border-left: 1px solid #e4e7ed;
        }

        .header-row {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .inline-tip {
            color: #d9001b;
            font-size: 13px;
        }
    }

    .footer-note {
        margin-top: 16px;
        color: #d9001b;
        font-size: 13px;
        line-height: 1.4;
    }

    .selected-exams-wrapper {
        margin: 10px 0;

        .selected-title {
            font-size: 14px;
            color: #606266;
            margin-bottom: 12px;
        }

        .exam-tag {
            margin-top: 8px;
            margin-left: 8px;
        }
    }

    .count {
        color: #d9001b;
        font-size: 16px;
        font-weight: 500;
    }

    .pagination-wrapper {
        margin-top: 20px;
        display: flex;
        justify-content: center;
    }
}
</style>

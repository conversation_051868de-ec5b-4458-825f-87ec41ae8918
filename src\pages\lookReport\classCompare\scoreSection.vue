<template>
  <div class="score-section" v-loading="loading">
    <div class="score-section-box">
      <div class="titleLine">{{ type == 'campus' ? '校区' : '' }}成绩分段分析</div>
      <el-popover placement="bottom" width="370" trigger="click" popper-class="popover" v-model="isPopoverVisible">
        <div class="tip" v-if="tempCheckTypeList.length == 1">至少保留一项指标</div>

        <el-checkbox-group v-model="tempCheckTypeList">
          <el-checkbox
            :disabled="tempCheckTypeList.length == 1 && tempCheckTypeList.includes(item.value)"
            class="checkbox"
            v-for="item in typeList"
            :key="item.value"
            :label="item.value"
            >{{ item.label }}</el-checkbox
          >
        </el-checkbox-group>
        <div class="popover-footer">
          <el-button size="small" @click="isPopoverVisible = false">取消</el-button>
          <el-button type="primary" size="small" @click="handleCheckType">确定</el-button>
        </div>
        <el-button class="filtrate" slot="reference" type="text">指标筛选 <i class="el-icon-arrow-down"></i></el-button>
      </el-popover>

      <div class="tool-box">
        <div>
          <el-button :disabled="tableData.length == 0" class="export-btn" type="primary" @click="exportData"
            >导出</el-button
          >
        </div>
      </div>

      <div class="table-box" v-if="tableData.length">
        <el-table
          style="width: 100%"
          :data="tableData"
          :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
          stripe
          border
          align="center"
          v-drag-table
          v-sticky-table="0"
        >
          <el-table-column
            :label="type == 'campus' ? '校区' : '班级'"
            prop="className"
            min-width="120"
            align="center"
            :resizable="false"
          ></el-table-column>
          <el-table-column
            label="实考人数"
            prop="num"
            min-width="120"
            align="center"
            :resizable="false"
          ></el-table-column>
          <el-table-column
            v-for="(item, index) in tableHeader"
            :key="item.open"
            :label="item.name"
            align="center"
            :resizable="false"
          >
            <template slot="header" slot-scope="scope">
              <span>{{ index == 0 ? `[${item.close}, ${item.close}]` : `[${item.open}-${item.close})` }}</span>
            </template>

            <el-table-column
              v-if="checkTypeList.includes('num')"
              label="本段人数"
              :prop="`items[${index}].num`"
              align="center"
              min-width="100"
              :resizable="false"
            >
            </el-table-column>
            <el-table-column
              v-if="checkTypeList.includes('numRate')"
              label="本段比例"
              :prop="`items[${index}].numRate`"
              align="center"
              min-width="100"
              :resizable="false"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.items[index].numRate }}%</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="checkTypeList.includes('avg')"
              label="本段均分"
              :prop="`items[${index}].avg`"
              align="center"
              min-width="100"
              :resizable="false"
            >
            </el-table-column>
            <el-table-column
              v-if="checkTypeList.includes('addNum')"
              label="累计人数"
              :prop="`items[${index}].addNum`"
              align="center"
              min-width="100"
              :resizable="false"
            >
            </el-table-column>
            <el-table-column
              v-if="checkTypeList.includes('addNumRate')"
              label="累计比例"
              :prop="`items[${index}].addNumRate`"
              align="center"
              min-width="100"
              :resizable="false"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.items[index].addNumRate }}%</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="checkTypeList.includes('addAvg')"
              label="累计均分"
              :prop="`items[${index}].addAvg`"
              align="center"
              min-width="100"
              :resizable="false"
            >
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>
      <no-data v-else></no-data>
    </div>

    <div class="score-section-box">
      <div class="titleLine">{{ type == 'campus' ? '校区成绩' : '' }}分段统计</div>
      <div class="radio-box">
        <el-radio-group v-model="numType" @change="initNumChart">
          <el-radio-button :label="1">按{{ type == 'campus' ? '校区' : '班级' }}查看</el-radio-button>
          <el-radio-button :label="2">按分数段查看</el-radio-button>
        </el-radio-group>
      </div>
      <div ref="numChart" style="width: 100%; height: 475px" v-show="tableData.length"></div>
      <no-data v-show="!tableData.length"></no-data>
    </div>

    <div class="score-section-box">
      <div class="titleLine">{{ type == 'campus' ? '校区成绩' : '' }}累计统计</div>
      <div class="radio-box">
        <el-radio-group v-model="addType" @change="initAddChart">
          <el-radio-button :label="1">按{{ type == 'campus' ? '校区' : '班级' }}查看</el-radio-button>
          <el-radio-button :label="2">按分数段查看</el-radio-button>
        </el-radio-group>
      </div>
      <div ref="addChart" style="width: 100%; height: 475px" v-show="tableData.length"></div>
      <no-data v-show="!tableData.length"></no-data>
    </div>

    <CheckSubClassDialog
      v-if="isDialogVisible"
      :canQType="true"
      @closed="isDialogVisible = false"
      @confirm="exportData"
    />
  </div>
</template>

<script>
import ScoreSectionChart from '@/components/scoreSection/ScoreSectionChart.vue';
import ScoreSectionTable from '@/components/scoreSection/ScoreSectionTable.vue';
import { getCampusStatSegmentAPI, getClsScoreSection, getGrdScoreSection, statSegmentAPI } from '@/service/pexam';
import UserRole from '@/utils/UserRole';
import * as echarts from 'echarts';
import CheckSubClassDialog from '../reportOverview/components/CheckSubClassDialog.vue';
import NoData from '@/components/noData.vue';
import { indicatorManager } from '@/utils/examReportUtils';

const typeList = indicatorManager.scoreSectionIndicatorList;

export default {
  name: 'smallQuesAvg',
  props: ['filterData', 'type'],
  components: {
    ScoreSectionChart,
    ScoreSectionTable,
    CheckSubClassDialog,
    NoData,
  },
  data() {
    return {
      activeName: 'chart',
      // 表格数据
      tableData: [],
      // 表格头
      tableHeader: [],
      // 班级列表
      clsList: [],
      // 是否加载
      isInit: false,
      // 是否显示累计
      isShowAdd: true,
      // 分段统计类型
      numType: 1,
      // 累计统计类型
      addType: 1,
      // 导出弹窗
      isDialogVisible: false,
      // 是否显示指标筛选
      isPopoverVisible: false,
      // 指标列表
      typeList: typeList,
      // 指标筛选
      checkTypeList: typeList.map(item => item.value),
      // 指标筛选
      tempCheckTypeList: typeList.map(item => item.value),
      // 是否加载
      loading: false,
    };
  },
  watch: {
    filterData: {
      deep: true,
      handler(newValue) {
        if (!newValue) return;
        this.clsList = this.$sessionSave.get('innerClassList');
        this.updateFilter();
      },
    },
    isPopoverVisible: {
      handler(newValue) {
        if (!newValue) return;
        this.tempCheckTypeList = JSON.parse(JSON.stringify(this.checkTypeList));
      },
    },
  },

  async mounted() {
    this.clsList = this.$sessionSave.get('innerClassList');
    this.checkTypeList = indicatorManager.getIndicator('scoreSection');
    this.updateFilter();
    this.isInit = true;
  },

  methods: {
    // 更新数据
    updateFilter() {
      if (this.type == 'campus') {
        this.getCampusStatSegment();
      } else {
        this.getStatSegment();
      }
    },
    // 成绩分段分析
    async getStatSegment() {
      this.loading = true;
      this.tableData = [];

      const res = await statSegmentAPI({
        subjectId: this.filterData.subjectId,
        examId: this.$sessionSave.get('reportDetail').examId,
        qType: this.filterData.qType,
        v: this.$sessionSave.get('reportDetail').v,
      });

      let list = res.data;
      this.tableData = list.filter(item => this.clsList.find(x => x.id == item.classId));
      this.tableHeader = list[0]?.items || [];

      this.$nextTick(() => {
        this.initNumChart();
        this.initAddChart();
      });
      this.loading = false;
    },

    // 获取校区成绩分段分析
    async getCampusStatSegment() {
      this.loading = true;
      this.tableData = [];

      const res = await getCampusStatSegmentAPI({
        subjectId: this.filterData.subjectId,
        examId: this.$sessionSave.get('reportDetail').examId,
        qType: this.filterData.qType,
        v: this.$sessionSave.get('reportDetail').v,
      });

      let list = res.data;
      this.tableData = list;
      this.tableHeader = list[0]?.items || [];

      this.$nextTick(() => {
        this.initNumChart();
        this.initAddChart();
      });
      this.loading = false;
    },

    // 初始化分段统计图表
    initNumChart() {
      if (!this.$refs.numChart) {
        return;
      }

      const charts = this.$echarts.init(this.$refs.numChart);

      let xAxisData = [];
      let series = [];

      if (this.numType == 1) {
        xAxisData = this.tableData.map(item => item.className);
        series = this.tableHeader.map((item, index) => ({
          name: index == 0 ? item.close : `(${item.close}-${item.open}]`,
          type: 'bar',
          data: this.tableData.map(x => x.items[index].num),
          barMaxWidth: 20,
        }));
      } else {
        xAxisData = this.tableHeader.map((item, index) => (index == 0 ? item.close : `(${item.close}-${item.open}]`));
        series = this.tableData.map(item => ({
          name: item.className,
          type: 'bar',
          data: item.items.map(x => x.num),
          barMaxWidth: 20,
        }));
      }

      charts.setOption(
        {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          legend: {
            type: 'scroll',
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '12%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: xAxisData,
          },
          yAxis: {
            type: 'value',
          },
          dataZoom: [
            {
              type: 'slider',
              show: true,
              xAxisIndex: [0],
              start: 0,
              end: 100,
            },
          ],
          series: series,
        },
        true
      );
    },

    // 初始化累计统计图表
    initAddChart() {
      if (!this.$refs.addChart) {
        return;
      }
      const charts = this.$echarts.init(this.$refs.addChart);

      let xAxisData = [];
      let series = [];

      if (this.addType == 1) {
        xAxisData = this.tableData.map(item => item.className);
        series = this.tableHeader.map((item, index) => ({
          name: `≥${item.open}`,
          type: 'bar',
          data: this.tableData.map(x => x.items[index].addNum),
          barMaxWidth: 20,
        }));
      } else {
        xAxisData = this.tableHeader.map(item => `≥${item.open}`);
        series = this.tableData.map(item => ({
          name: item.className,
          type: 'bar',
          data: item.items.map(x => x.addNum),
          barMaxWidth: 20,
        }));
      }

      charts.setOption(
        {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          legend: {
            type: 'scroll',
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '12%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: xAxisData,
          },
          yAxis: {
            type: 'value',
          },
          dataZoom: [
            {
              type: 'slider',
              show: true,
              xAxisIndex: [0],
              start: 0,
              end: 100,
            },
          ],
          series: series,
        },
        true
      );
    },

    // 显示导出弹窗
    showExportDialog() {
      this.isDialogVisible = true;
    },

    // 指标筛选
    handleCheckType() {
      this.checkTypeList = JSON.parse(JSON.stringify(this.tempCheckTypeList));
      this.isPopoverVisible = false;
      indicatorManager.setIndicator('scoreSection', this.checkTypeList);
    },

    // 导出
    async exportData({ subjectCheckList, classCheckList, qType }) {
      let role = '';
      if (!UserRole.isOperation) {
        const { year, campusCode } = this.$sessionSave.get('reportDetail');
        const map = await UserRole.utils.getRoleSubjectClassMap(year, campusCode, this.$sessionSave.get('reportType') == 'school' ? true : false);
        role = JSON.stringify(map);
      }
      const params = {
        examId: this.$sessionSave.get('reportDetail').examId,
        qType: this.filterData.qType,
        role: role,
        filter: this.checkTypeList.join(','),
        v: this.$sessionSave.get('reportDetail').v,
      };
      const urlSearch = new URLSearchParams(params);
      const path = this.type == 'campus' ? '/pexam/_/exp-campus-stat-segment' : '/pexam/_/exp-stat-segment';
      let url = process.env.VUE_APP_KKLURL + path + `?${urlSearch.toString()}`;
      window.open(url);
    },
  },
};
</script>

<style lang="scss" scoped>
.score-section {
  border-radius: 6px;
  padding-bottom: 10px;
  background-color: #fff;
  font-size: 14px;

  .pagination-box {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
  }
  .chart-box {
    height: 475px;
    background: #ffffff;
    border: 1px solid #e4e8eb;
    border-radius: 3px;
    margin: 20px 0;
  }

  .table-box {
    // border: 1px solid #e4e8eb;
    // border-radius: 3px;
  }

  .score-section-box {
    margin-bottom: 20px;
  }
}
.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;
  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 15px;
  }
}

.tool-box {
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
  margin-bottom: 10px;

  .export-btn {
    margin-left: 10px;
  }
}

.radio-box {
  margin-bottom: 20px;
}

.popover-footer {
  margin-top: 10px;
  text-align: right;
}

.tip {
  margin-bottom: 10px;
  color: red;
  font-size: 13px;
}

.filtrate {
  margin-left: 10px;
}
</style>
<style lang="scss">
.score-section-box {
  .el-tabs {
    .el-tabs__header {
      margin: 0 0 1px 4px;
    }
    .el-tabs__nav-wrap::after {
      width: 160px;
    }
    .el-tabs__item {
      width: 80px;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      &.is-top:last-child {
        padding-left: 0;
      }
      &.is-top:nth-child(2) {
        padding-right: 0;
      }
    }
  }
}
</style>

<!--
 * @Description: 批改抽查
 * @Author: liuyue <EMAIL>
 * @Date: 2024-07-23 16:18:00
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2024-08-08 10:46:19
-->
<template>
    <div class="spotcheck-container">
        <el-page-header @back="back" content="抽查"> </el-page-header>
        <div v-if="curStuQues" class="spotcheck-content">
            <div class="stu-answer">
                <div class="img-warp">
                    <el-image class="img-item" v-for="img in curStuQues.stuNewResList"
                        :src="getImgUrl(img.source)"></el-image>
                </div>
            </div>
            <div class="correct-info">
                <div class="correct-info-nos">
                    <div class="info-item">题块：{{ quesName }}</div>
                    <div class="info-item">阅卷老师：{{ correctName }}</div>
                </div>
                <div class="correct-info-score">
                    <div class="info-item">给分：{{ curStuQues.stuScores }}分</div>
                    <div class="info-item">满分：{{ curStuQues.totalScore }}分</div>
                </div>
                <div class="correct-info-full-score">
                    <template v-if="curStuQues.quesList.length">
                        <div class="info-item score-label">得分：</div>
                        <div class="info-item">
                            <div v-for="ques in curStuQues.quesList">
                                <span class="title">{{ ques.title }}</span><el-input-number v-model="ques.checkScore" :key="ques.quesId" :min="0" :max="ques.totalScore"
                                    size="small"></el-input-number>
                                <span class="fullscore">满分：{{ ques.totalScore }}</span>
                            </div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="info-item">得分：</div>
                        <div class="info-item">
                            <el-input-number v-model="curStuQues.checkScore" :min="0" :max="curStuQues.totalScore"
                                size="small"></el-input-number>
                        </div>
                    </template>
                </div>
                <div class="correct-info-btn">
                    <el-button @click="submit(ICHECKTYPE.CHECK)">改分</el-button>
                    <el-button @click="submit(ICHECKTYPE.PASS)">通过</el-button>
                </div>
            </div>
        </div>
        <el-empty v-else description="暂无可抽查学生"></el-empty>
    </div>
</template>

<script>
import {
    getExamCheckStuList,
    saveExamCheckData
} from '@/service/api';
import { getImgUrl } from '@/utils/index';
import { replaceALiUrl } from '@/utils/common';
export default {
    props: {
    },
    data() {
        return {
            workId: this.$route.query.workId,
            checkId: this.$sessionSave.get('loginInfo').id,
            correctId: this.$route.query.correctId,
            correctName: this.$route.query.correctName,
            quesId: this.$route.query.quesId,
            quesName: this.$route.query.quesName,
            teaType: this.$route.query.teaType,
            curStuQues: null,
            ICHECKTYPE:{
                CHECK:0,
                PASS:1
            }
        }
    },
    mounted() {
        this.getExamCheckStuList();
    },
    methods: {
        getImgUrl(src) {
            let path = getImgUrl(src)
            return replaceALiUrl(path)
        },
        async getExamCheckStuList() {
            let params = {
                schoolId: this.$sessionSave.get('schoolInfo').id,
                workId: this.workId,
                checkId: this.$sessionSave.get('loginInfo').id,
                correctId: this.correctId,
                quesId: this.quesId,
                teaType: this.teaType,
                limit: 1
            };
            let res = await getExamCheckStuList(params).catch(e => {
            });;
            let data = res.data[0];
            if (data) {
                if (data.quesList.length) {
                    data.quesList.forEach(ques => {
                        ques.checkScore = ques.stuScores;
                    })
                } else {
                    data.checkScore = data.stuScores;
                }
                this.curStuQues = data;
            } else {
                this.curStuQues = null;
            }
        },
        async submit(type) {
            if (type == this.ICHECKTYPE.CHECK) {
                if (this.curStuQues.quesList.length) {
                    let err = this.curStuQues.quesList.find(ques => ques.checkScore == undefined)
                    if (err){
                        this.$message.error('分值填写有误')
                        return;
                    }
                } else {
                    if (this.curStuQues.checkScore == undefined){
                        this.$message.error('分值填写有误')
                        return;
                    }
                }
            }
            await this.saveExamCheckData(type)
            this.getExamCheckStuList();
        },
        buildData(state,curQues){
            let data = {
                "schoolId": this.$sessionSave.get('schoolInfo').id,
                "workId": this.workId,
                "shwId": curQues.shwId,
                "correctType": curQues.correctType,
                "quesId": curQues.quesId,
                "correctId": this.correctId,
                "correctName": this.correctName,
                "checkId": this.$sessionSave.get('loginInfo').id,
                "checkName": this.$sessionSave.get('loginInfo').realname,
                "checkScore": curQues.checkScore,
                "state": state,
                "isRight": curQues.checkScore == curQues.totalScore ? 1 : 0,
                "stuRes": this.curStuQues.stuNewResList
            };
            return data;
        },
        async saveExamCheckData(state) {
            let params = [];
            if(this.curStuQues.quesList.length){
                this.curStuQues.quesList.forEach(ques => {
                    params.push(this.buildData(state,ques))
                })
            }else{
                params.push(this.buildData(state,this.curStuQues))
            }
            let res = await saveExamCheckData(params).catch(e => {
            });
            if (res.code == -1) {
                this.$message.error(res.msg)
            }
        },
        back() {
            this.$router.back();
        }
    }
}
</script>

<style lang="scss" scoped>
.spotcheck-container {
    .spotcheck-content {
        margin-top: 20px;

        .stu-answer {
            display: inline-block;
            width: calc(75% - 10px);
            min-height: 280px;
            margin-right: 10px;
            text-align: center;
            background: #fff;
            border-radius: 10px;

            .img-warp {

                // margin: auto;
                .img-item {
                    border: 1px solid #DCDFE6;
                }
            }
        }

        .correct-info {
            display: inline-block;
            width: 25%;
            min-height: 280px;
            vertical-align: top;
            position: sticky;
            top: 0;
            background: #fff;
            border-radius: 10px;

            >div {
                margin: 30px 20px;
                text-align: center;

                &.correct-info-btn {
                    // text-align: center;
                }

                .info-item {
                    display: inline-block;
                    clear: both;
                    margin-right: 20px;
                }
            }
            .correct-info-full-score{
                .score-label{
                    vertical-align: top;
                    margin-right: 10px;
                    line-height: 30px;
                }
                .title,.fullscore{
                    font-size: 14px;
                    color:gray;
                    margin-right: 10px;
                }
            }
        }
    }
}
</style>
<style lang="scss">
.spotcheck-container {
    .correct-info-full-score {
        .el-input-number {
            margin-bottom: 5px;
            margin-right: 10px;
        }
    }
}
</style>
/*
 * @Descripttion: 
 * @Author: 老李
 * @Date: 2021-02-06 18:03:11
 * @LastEditors: Doudou
 */
import Vue from 'vue'
import {v1 as uuid} from 'uuid';

export default function createDrawer(option: any) {
  let dom = document.createElement('div');
  let parentDom;
  if (option.appendToBody) {
    parentDom = document.getElementsByTagName('body')[0]
    parentDom.appendChild(dom);
  } else {
    parentDom = document.getElementById(option.appendToId || 'rootBody')
    parentDom = parentDom || document.getElementById('page_main_body');
    parentDom.appendChild(dom);
  }

  let cuuid = uuid();
  var vue = new Vue({
    el: dom,
    data: function () {
      return {
        title: option.title || '',
        size: option.size || 'small',
        direction: option.direction || 'rtl',
        customClass: option.customClass,
        appendToBody: option.appendToBody,
        modalAppendToBody: option.modalAppendToBody,
        show: false,
        isClosed: false,
        bodyClass: option.bodyClass,
        showClose: option.showClose || false,
        wrapperClosable: option.wrapperClosable,
        change: option.change || function () { },
        dialogData: option.data,
        modal: option.showModal,
        withHeader: option.withHeader
      };
    },
    store: option.store,
    render(h) {
      let dialogContent = h('dialogContent', {
        ref: "elementDrawer",
        attrs: {
          dialogData: this.dialogData,
        },
        on: {
          close: this.closeDialog,
          confirm: this.confirmDialog,
          change: this.change,
        }
      })
      if (!this.show) dialogContent = null;

      return h('el-drawer', {
        class: this.bodyClass,
        style: 'position: absolute;overflow: hidden;',
        attrs: {
          id: cuuid,
          title: this.title,
          visible: this.show,
          wrapperClosable: true,
          direction: this.direction,
          modal: this.modal,
          size: this.size,
          withHeader: this.withHeader,
          'custom-class': this.customClass + ' ' + this.sizeCLass,
          'append-to-body': this.appendToBody,
          'modal-append-to-body': this.modalAppendToBody,
          'show-close': this.showClose,
        },
        on: {
          close: this.close,
          closed: this.closed,
          opened: this.opened,
          open: this.open,
        },
      }, [dialogContent])
    },
    computed: {
      sizeCLass() {
        return `el-drawer--width-${option.size || 'auto'}`;
      }
    },
    mounted() {
      this.show = true;
    },
    methods: {
      close() {
        if (option.close) {
          option.close();
        }
        this.closeDialog()
      },
      closed() {
        let lastDrawer = document.getElementById(cuuid);
        if (lastDrawer) lastDrawer.parentNode.removeChild(lastDrawer);
      },
      open() {
      },
      opened() {
        option.opened && option.opened()
      },
      closeDialog() {
        this.isClosed = true;
        this.show = false
      },
      confirmDialog(result) {
        this.show = false
        option.confirm && option.confirm(result)
      },
    },
    components: {
      dialogContent: option.component,
    },
  });
  return vue;
}
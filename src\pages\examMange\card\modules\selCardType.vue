<template>
  <el-dialog custom-class="sel-card-container" :visible="modalVisible" width="890px" :before-close="handleClose"
    :modal-append-to-body="false" :close-on-click-modal="false">
    <div slot="title" class="dialog-title">
      <span style="margin-right: 10px">答题卡设置</span>
    </div>
    <el-form class="sel-card-form" ref="form" :model="form" label-width="80px">
      <el-form-item label="批阅类型">
        <el-radio-group v-model="form.correctType">
          <el-radio :label="ICORRECT_TYPES.WEB">网阅</el-radio>
          <el-radio :label="ICORRECT_TYPES.HAND">手阅</el-radio>
          <el-radio :label="ICORRECT_TYPES.PHOTO">拍改
            <el-tooltip class="item" effect="dark" content="点击查看拍改教程：手机拍扫，自动批改，支持单选、多选、判断" placement="right">
              <i class="el-icon-video-play click-element" style="display: inline-block;height: 0;top:1px;color: #333;font-size: 18px;position: relative;"
              @click="defaultPlayerResource.show = true"></i>
            </el-tooltip>
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="版  式" class="sel-pageLayout-container" style="display: flex; align-items: center"
        v-if="form.correctType != ICORRECT_TYPES.PHOTO">
        <el-radio-group v-model="form.pageLayout" class="sel-radio-group">
          <el-radio :label="IPAGELAYOUT.A4">
            <p>一栏</p>
            <p>A4/16K/B5</p>
          </el-radio>
          <el-radio :label="IPAGELAYOUT.A3">
            <p>两栏</p>
            <p>A3/8K/B4</p>
          </el-radio>
          <el-radio :label="IPAGELAYOUT.A33">
            <p>三栏</p>
            <p>A3/8K/B4</p>
          </el-radio>
          <el-radio :label="IPAGELAYOUT.A32">
            <p>正3反2</p>
            <p>A3/8K/B4</p>
          </el-radio>
          <el-radio :label="IPAGELAYOUT.A23">
            <p>正2反3</p>
            <p>A3/8K/B4</p>
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="所属学科" v-if="form.correctType != ICORRECT_TYPES.PHOTO">
        <el-radio-group v-model="form.subjectType">
          <el-radio :label="0">单学科</el-radio>
          <el-radio :label="1">综合学科
            <el-tooltip class="item" effect="dark" content="设置综合学科可实现多个学科在一张答题卡" placement="right">
              <i class="el-icon-question" style="height: 0;"></i> </el-tooltip></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="form.correctType != ICORRECT_TYPES.PHOTO ? '' : '所属学科'" prop="subjectId">
        <template v-if="form.subjectType == 0">
          <el-select v-model="form.subjectId" placeholder="请选择学科">
            <el-option v-for="item in subjectList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
          <el-checkbox v-model="form.isABCard" style="margin-left: 20px"
            v-if="form.correctType != ICORRECT_TYPES.PHOTO">AB卡<el-tooltip class="item" effect="dark"
              content="选择AB卡后可同时制两份题目结构、分值相同，但排版不同的答题卡" placement="right">
              <i class="el-icon-question" style="height: 0;"></i> </el-tooltip></el-checkbox>
        </template>
        <el-checkbox-group v-else class="subjectCheckBox" v-model="form.checkSubjects"
          @change="handleCheckedSubjectChange">
          <template v-for="sub in subjectList">
            <el-checkbox :label="sub.id" :key="sub.id" style="position: relative">
              {{ sub.name }}
            </el-checkbox>
          </template>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="sureClick">确 定</el-button>
    </div>

    <!-- 查看资源弹窗 -->
    <video-player v-if="defaultPlayerResource.show" :src="defaultPlayerResource.url" :title="defaultPlayerResource.title"
      @close-dialog="defaultPlayerResource.show = false" :isVideo="true" />
  </el-dialog>
</template>

<script>
import { ICORRECT_TYPES, IPAGELAYOUT } from '@/typings/card';
import VideoPlayer from '@/components/VideoPlayer/VideoPlayer.vue';

export default {
  name: 'sele-more-tea',
  props: {
    modalVisible: {
      type: Boolean,
      required: true,
    },
  },
  components: {
    VideoPlayer
  },
  watch: {
    modalVisible(newVal) {
      if (newVal) {
        this.form.checkSubjects = [];
        this.form.subjectType = 0;
        let loginInfo = this.$sessionSave.get('loginInfo');
        this.form.subjectId = loginInfo.subjectid || this.subjectId[0].id;
        this.form.isABCard = false;
      }
    },
  },
  data() {
    return {
      form: {
        //页面布局
        pageLayout: 1,
        //批阅类型
        correctType: 2,
        //学科
        subjectId: '',
        //学科名
        subjectName: '',
        //学科类型
        subjectType: 0, //0 单学科 ，1综合
        //综合学科
        checkSubjects: [],
        //是否AB卡
        isABCard: false,
        abCardType: 0,
      },
      //批阅方式
      ICORRECT_TYPES,
      //页面布局
      IPAGELAYOUT,
      subjectList: [],
      defaultPlayerResource: {
        show: false,
        url: 'https://fs.iclass30.com/aliba/resources/2025/06/16/4e61e5bf095c4255b9f478edeb70c195/8ae1d7d6c26644ab97e39d21e8464ad6/a9132120-4a8e-11f0-86e5-fd8689399e56/thumbnail/video.mp4',
        title: '拍改教程',
      },
    };
  },
  mounted() {
    this.subjectList = this.$localSave.get('SUBJECT_LIST');
  },
  methods: {
    /**
     * @name：关闭弹窗
     */
    handleClose(done) {
      this.$emit('confirm-cancel');
    },
    /**
     * @name：取消选择的教师
     */
    closeModal() {
      this.$emit('confirm-cancel');
    },
    handleCheckedSubjectChange() { },
    sureClick() {
      //单学科
      if (this.form.subjectType == 0) {
        let subjectName = this.subjectList.filter(item => {
          return item.id == this.form.subjectId;
        })[0].name;
        this.form.subjectName = subjectName;
        this.form.abCardType = this.form.isABCard ? 1 : 0;
      } else {
        this.form.abCardType = 0;
        if (this.form.checkSubjects.length == 0) {
          this.$message.error('学科不能为空！');
          return;
        } else {
          const subjectName = [];
          this.form.checkSubjects.forEach(id => {
            const matchingSubject = this.subjectList.find(subject => subject.id === id);
            if (matchingSubject) {
              subjectName.push(matchingSubject.name);
            }
          });
          this.form.subjectId = this.form.checkSubjects.join(',');
          this.form.subjectName = subjectName.join(',');
        }
      }
      this.$emit('confirm-sure', this.form);
    },
  },
};
</script>

<style lang="scss" scoped>
.sel-card-form{
  ::v-deep {
    .el-form-item__label,.el-radio__label,.el-checkbox__label {
      font-size: 16px;
    }
  }
}
</style>

<style lang="scss">
.sel-radio-group {
  display: flex;
  align-items: center;

  .el-radio {
    display: flex;
    align-items: center;
  }
}

.sel-pageLayout-container {
  .el-form-item__content {
    margin-left: 0px !important;
    // display: flex;
    // align-items: center;
  }
}
</style>
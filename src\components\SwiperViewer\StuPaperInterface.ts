/*
 * @Description:
 * @Author: 小圆
 * @Date: 2024-04-24 17:07:51
 * @LastEditors: 小圆
 */
export interface IPaper {
  id: string;
  code: number;
  detect_code: number;
  version: number;
  page: number;
  idx: number;
  idx_num: number;
  stu_no: string;
  card_id: string;
  task_id: string;
  exam_id: string;
  multi: null;
  stu_no_detect: StuNoDetect | null;
  questions: Question[];
  origin: string;
  image: string;
  detect_image: string;
  status: number;
  width: number;
  height: number;
  is_deleted: boolean;
  warning: string;
  date_created: Date;
  date_updated: Date;
  isEdit: boolean;
}

export interface Question {
  special_type: number;
  ai_mode: number;
  is_obj: boolean;
  status: number;
  question_no: number;
  question_nos: string;
  question_id: string;
  answer_int: number;
  detect_answer_int: number;
  code: number;
  score: number;
  miss_score: number;
  total_score: number;
  score_list: ScoreList[] | null;
  list: List[] | null;
  type: number;
  pos: number[];
  question_type: number;
  tempScore?: number;
  showScore?: boolean;
  scoringMode: number;
}

export interface List {
  pos: number[];
  fill: boolean;
}

export interface ScoreList {
  scores: number[];
  pos: number[];
  rows: number;
  cols: number;
  score: number;
  detect_score: number;
  new_score_list: NewScoreList[];
  newArray?: any[];
  total_score: number;
}

export interface NewScoreList {
  value: number;
  isChoice: boolean;
}

export interface StuNoDetect {
  stu_no: string;
  code: number;
  score: number;
  list: null;
}

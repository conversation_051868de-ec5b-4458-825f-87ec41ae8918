<template>
  <div class="perview-container">
    <div class="rc-main">
      <!--<div class="rc-main" v-loading="loading"-->
      <!--element-loading-text="加载中...">-->
      <div class="rc-main-nav">
        <div class="exam-nav">
          <div
            v-on:click.stop="gotoBack"
            style="cursor: pointer; display: inline-block"
          >
            <img
              src="../assets/questionCard/icon_back.png"
              class="perview-container-back-icon"
            />
            <span class="perview-container-back-txt">返回</span>
          </div>
          <div class="exam-title">
            <p>{{ testBankName }}</p>
          </div>
          <a class="exam-ques-count" href="javascript:void(0)"
            >共<span>{{ questionData.length }}</span
            >题</a
          >
        </div>
      </div>
      <div class="rc-main-left ques-all-wrap">
        <div
          class="ques-ul"
          v-for="(item, bigindex) in topicQuesData"
          :key="bigindex"
        >
          <template>
            <div class="big-title">{{ item.title }}</div>
            <ul>
              <li class="item" v-for="(sItem, index) in item.ques" :key="index">
                <div class="ques-content">
                  <div
                    class="article-container"
                    v-html="
                      sItem.data.levelcode == ''
                        ? sItem.data.q_html
                        : sItem.data.desc_html
                    "
                  ></div>
                  <!-- 三级结构 -->
                  <div
                    class="ques-detail-small-ques"
                    v-if="sItem.data.levelcode != ''"
                  >
                    <div></div>
                    <!-- <div class="ques-detail-button">
                      <el-button
                        :class="[
                          'ques-detail-small-button',
                          { active: smallIndex == sItem.smallQuesIndex },
                        ]"
                        circle
                        v-for="(small, smallIndex) in sItem.data.qs"
                        :key="smallIndex"
                        @click.stop="viewBigSmallQues(bigindex,index, smallIndex)"
                      >
                        {{ smallIndex + 1 }}
                      </el-button>
                    </div> -->
                    <div
                      v-for="(small, smallIndex) in sItem.data.qs"
                      :key="smallIndex"
                      class="article-container"
                    >
                      <span>{{ smallIndex + 1 }}、</span>
                      <div v-html="small.q_html"></div>
                    </div>
                  </div>
                  <div class="auxiliary leaf-q">
                    <div class="answer">
                      <div class="dt">答案：</div>
                      <br />
                      <div
                        v-for="(qsItem, qsIndex) in sItem.data.qs"
                        :key="qsIndex"
                        style="display: flex"
                      >
                        <template>
                          <span>{{
                            sItem.data.levelcode == "" ? "" : `${qsIndex + 1}、`
                          }}</span>
                          <template v-if="qsItem.ans.length != 0">
                            <div
                              v-for="(ansItem, index) in qsItem.ans"
                              :key="index"
                            >
                              <template>
                                <!--智批题-->
                                <div
                                  class="dd"
                                  v-if="sItem.data.type == 7"
                                  style="margin-left: 0px"
                                >
                                  \({{ ansItem }}\)
                                </div>
                                <div
                                  class="dd"
                                  style="margin-left: 0px"
                                  v-else
                                  v-html="ansItem"
                                ></div>
                              </template>
                            </div>
                          </template>
                          <div class="dd" style="margin-left: 0px" v-else>
                            略
                          </div>
                        </template>
                      </div>
                    </div>
                    <div class="exp">
                      <div class="dt">解析：</div>
                      <br />
                      <div
                        v-for="(qsItem, qsIndex) in sItem.data.qs"
                        :key="qsIndex"
                        style="display: flex"
                      >
                        <template>
                          <span>{{
                            sItem.data.levelcode == "" ? "" : `${qsIndex + 1}、`
                          }}</span>
                          <div
                            class="dd"
                            style="margin-left: 0px"
                            v-if="qsItem.exp.length != 0"
                            v-html="qsItem.exp"
                          ></div>
                          <div class="dd" style="margin-left: 0px" v-else>
                            略
                          </div>
                        </template>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="ques-tip">
                  <div class="ques-tip-left">
                    <span class="ques-tip-left-item">
                      题型:
                      <span class="qs-type" v-if="sItem.data.type == 1"
                        >多选题</span
                      >
                      <span class="qs-type" v-if="sItem.data.type == 2"
                        >判断题</span
                      >
                      <span class="qs-type" v-if="sItem.data.type == 3"
                        >填空题</span
                      >
                      <span class="qs-type" v-if="sItem.data.type == 4"
                        >语音题</span
                      >
                      <span class="qs-type" v-if="sItem.data.type == 5"
                        >英语自动评测题</span
                      >
                      <span class="qs-type" v-if="sItem.data.type == 6"
                        >解答题</span
                      >
                      <span class="qs-type" v-if="sItem.data.type == 7"
                        >填空题智批</span
                      >
                      <span class="qs-type" v-if="sItem.data.type == 8"
                        >单选题</span
                      >
                    </span>
                  </div>
                </div>
              </li>
            </ul>
          </template>
        </div>
      </div>
    </div>
    <el-backtop target=".scrollContainer"></el-backtop>
  </div>
</template>
<script>
import {
  loadTopicInfo,
  findTestBankQuestionInfo,
  findQueTypeList,
} from "../service/api";
import { loadTestStructureAPI } from "../service/testbank";
import { convertHtml } from "@/service/pbook";

export default {
  name: "preview-test-ques",
  data() {
    return {
      testBankId: this.$route.query.testBankId,
      testBankName: this.$route.query.testBankName,
      subjectId: this.$route.query.subjectId,
      topicData: {},
      questionData: [],
      topicQuesData: [],
      quesTypeMap: [],
      loading: true,
      bigList: [],
    };
  },
  methods: {
    async initData() {
      this.testBankId = this.$route.query.testBankId;
      this.testBankName = this.$route.query.testBankName;
      this.subjectId = this.$route.query.subjectId;
      await this.findQuesType();
      await this.getTestTopicInfo();
    },
    converQues(data) {
      data.desc_html = convertHtml(data.desc_html);
      data.q_html = convertHtml(data.q_html);
      data.qs.forEach((it) => {
        it.desc_html = convertHtml(it.desc_html);
        it.q_html = convertHtml(it.q_html);
        if (it.opts_htmls) {
          let list = [];
          for (let j = 0; j < it.opts_htmls.length; j++) {
            list.push(convertHtml(it.opts_htmls[j]));
          }
          it.optionText = list;
        }
      });
      return data;
    },
    /**
     * 获取试卷下题型和题目关系
     */
    async getTestTopicInfo() {
      let params = {
        tid: this.testBankId,
      };
      const res = await loadTestStructureAPI(params);
      if (res && parseInt(res.code) === 1) {
        this.topicData = res.data.bigList;
      }
      let qinfo = new Map();
      res.data.bigList.forEach((item) => {
        if (item.isSubQues == 1) {
          qinfo.set(item.Id, item.Id);
        } else {
          item.smallList.forEach((ite) => {
            qinfo.set(ite.Id, ite.BigQuestionId);
          });
        }
      });

      this.getTestBankQuestionInfo(qinfo);
    },
    /**
     * 获取整个试卷题目信息
     */
    async getTestBankQuestionInfo(qinfo) {
      let params = {
        testBankId: this.testBankId,
      };

      const res = await findTestBankQuestionInfo(params);

      if (res && parseInt(res.code) === 1) {
        this.questionData = res.data;
      }
      this.topicQuesData = this.renderQuesTypeAndQues(res.data, qinfo);
      this.topicQuesData.forEach((item) => {
        item.ques.forEach((ite) => {
          ite.data = this.converQues(ite.data);
          this.$set(ite, "activeIndex", 0);
          this.$set(ite, "smallQuesIndex", 0);
        });
      });
      this.loading = false;
      this.$nextTick(() => {
        // let content = document.getElementsByClassName('ques-ul');
        // //重新渲染
        // MathJax.Hub.Queue(["Typeset", MathJax.Hub, content]);
        this.mathJaxUpdate();
      });
    },
    /**
     * 遍历题型和所有题目将对应题型的题目挂到相应的题型上
     */
    renderQuesTypeAndQues(quesDatas, qinfo) {
      var _quesDatas = [];
      var newQuesDatas = [];
      for (var i = 0; i < quesDatas.length; i++) {
        var title = quesDatas[i].bigTitle;
        var bid = qinfo.get(quesDatas[i].qId);
        var index = _quesDatas.indexOf(bid);
        var score = 0;
        quesDatas[i].data.qs.forEach(function (q) {
          score += q.score;
        });
        quesDatas[i].data.score = score;
        quesDatas[i].bid = bid;
        if (index == -1) {
          _quesDatas.push(bid);
          newQuesDatas.push({
            title: this.intToChinese(_quesDatas.length) + "、" + title,
            ques: [quesDatas[i]],
          });
        } else newQuesDatas[index].ques.push(quesDatas[i]);
      }
      return newQuesDatas;
    },
    /**
     * 获取校本题型
     */
    async findQuesType() {
      let _this = this;
      let params = {
        subjectId: this.subjectId,
        typeCode: 104,
      };
      await findQueTypeList(params)
        .then(function (result) {
          let dataAttr = result.data;
          dataAttr.forEach(function (item) {
            _this.quesTypeMap.push({
              name: item.jyeooTypeName,
              score: item.score,
              jyeooTypeId: item.jyeooTypeId,
              objOrSub: item.objOrSub,
            });
          });
        })
        .catch(function (error) {
        });
    },
    getQuesType(typeid) {
      let quesType = this.quesTypeMap.filter((item) => {
        return typeid === item.jyeooTypeId;
      })[0];

      return quesType;
    },
    /**
     * 查看题目详情
     */
    lookQuesDetail(id) {
      let currentRef = "quesDetail_" + id;
      let curBtnRef = "lookLink_" + id;
      this.$refs[currentRef][0].style.display =
        this.$refs[currentRef][0].style.display == "none" ? "block" : "none";
      this.$refs[curBtnRef][0].className =
        this.$refs[currentRef][0].style.display == "none"
          ? "detail-item"
          : "detail-item active";
      this.$refs[curBtnRef][0].innerHTML =
        this.$refs[currentRef][0].style.display == "none" ? "详情" : "收起";
    },
    /**
     * @name:查看大题题面上的小题
     */
    viewBigSmallQues(bigIndex, index, smallIndex) {
      this.topicQuesData[bigIndex].ques[index].smallQuesIndex = smallIndex;
    },
    /**
     * 返回
     */
    gotoBack() {
      this.$emit("updateTab", "1");
      this.$router.back(-1);
    },
    intToChinese(str) {
      str = str + "";
      var len = str.length - 1;
      var idxs = [
        "",
        "十",
        "百",
        "千",
        "万",
        "十",
        "百",
        "千",
        "亿",
        "十",
        "百",
        "千",
        "万",
        "十",
        "百",
        "千",
        "亿",
      ];
      var num = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
      return str.replace(/([1-9]|0+)/g, function ($, $1, idx, full) {
        var pos = 0;
        if ($1[0] != "0") {
          pos = len - idx;
          if (idx == 0 && $1[0] == 1 && idxs[len - idx] == "十") {
            return idxs[len - idx];
          }
          return num[$1[0]] + idxs[len - idx];
        } else {
          var left = len - idx;
          var right = len - idx + $1.length;
          if (Math.floor(right / 4) - Math.floor(left / 4) > 0) {
            pos = left - (left % 4);
          }
          if (pos) {
            return idxs[pos] + num[$1[0]];
          } else if (idx + $1.length >= len) {
            return "";
          } else {
            return num[$1[0]];
          }
        }
      });
    },

    katexUpdate() {
      let texList = document.getElementsByClassName("math-tex");

      let reg = /\\\((.*)\\\)/;

      for (let i = texList.length - 1; i >= 0; i--) {
        let it = texList[i];
        let match = it.innerText.match(reg);

        if (match) {
          try {
            it.outerHTML = katex.renderToString(match[1], {
              displayMode: true,
              leqno: false,
              fleqn: false,
              throwOnError: true,
              errorColor: "#cc0000",
              strict: "warn",
              output: "htmlAndMathml",
              trust: false,
              macros: {
                "\\f": "f(#1)",
              },
            });
          } catch (e) {
            console.log(match[1]);
          }
        } else {
          console.debug("no match:" + it.innerText);
        }
      }
      if (document.getElementsByClassName("math-tex").length) {
        this.mathJaxUpdate();
      }
    },
    mathJaxUpdate() {
      let success = false;
      if (MathJax && MathJax.startup) {
        try {
          MathJax.startup.getComponents();
          MathJax.typeset();
          success = true;
        } catch (e) {}
      }

      if (!success) {
        setTimeout(() => {
          this.mathJaxUpdate();
        }, 100);
      }
    },
  },
  async mounted() {
    this.initData();
  },
  async activated() {
    this.initData();
  },
};
</script>

<style lang="scss" scoped>
@import "../styles/questionCard/perviewTestQues";
.rc-main-left {
  ul {
    list-style: none;
  }
}
.ques-detail-small-ques {
  margin-left: 20px;
}
.ques-detail-button {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}
.ques-detail-small-button {
  width: 40px;
  height: 40px;
  &.active {
    background-color: #409eff;
    color: #fff;
  }
}
</style>

<style>
a.math-show {
  color: unset;
  cursor: unset;
}
</style>

<style lang="css" scoped>
@import "../styles/questionCard/yiqiQuesBank/render.css";
</style>

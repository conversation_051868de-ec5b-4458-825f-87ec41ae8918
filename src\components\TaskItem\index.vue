<template>
  <div class="exam-item">
    <div class="exam-name-area">
      <div class="exam-name">
        <div style="margin-right: 20px">
          {{ item.title }}
        </div>
        <div class="exam-category" v-if="item.categoryName">
          <div>{{ item.categoryName }}</div>
        </div>
        <!-- <div class="exam-category">
          <div>网阅</div>
        </div> -->
      </div>
      <div class="exam-detail display_flex align-items_center">
        <span class="exam-grade">年级：{{ item.gradeName }}</span>
        <span class="exam-time">考试时间：{{ item.examTime.substring(0, 16) }}</span>
      </div>
      <!-- 该场考试学科列表 -->
      <!-- v-for="subjectItem in item.paperList" :key="subjectItem.subectId" -->
      <el-row v-for="work in item.workList" style="margin-top: 10px;height: 30px;" type="flex" justify="space-between" align="middle">
        <div class="exam-item-left">
        <i class="subject-icon"></i>
        <span class="subject-name">{{ work.comprehensiveName || work.subjectName }}
          <template v-if="work.relateCardType == ICARD_STATE.abPaperTwo || work.relateCardType == ICARD_STATE.abPaper">
            （{{work.sheetType == IAB_CARD_SHEEFT_TYPE.aCard ? 'A' : 'B' }}卷）
          </template>
        </span>
        <div class="speed-info" v-if="work.allStuQuesCount > 0 && !work.isOver">
          待批改：
          <span class="speed-val">
            <span style="color: #00caaf; font-size: 20px">{{ work.remainStuQuesCount }}</span
            >/{{ work.allStuQuesCount }}</span
          >
        </div>
        <el-button v-if="work.showSetUp" type="text" @click="goCorrectSet(work)">阅卷设置</el-button>
        <el-button v-if="work.showMonitor" type="text" @click="goCorrectSpeed(work)">阅卷监控</el-button>
        <el-button v-if="work.assignType == 4" type="text" :disabled="!!work.isOver" @click="handlePublishScore(work)">发布本班成绩</el-button>
      </div>
        <div class="exam-item-right">
      <div class="exam-state" v-if="work.arbitrationPaperCount">
        <!-- <span class="num">{{ work.pendingArbitrationCount }}</span>
        <p class="problem-title">仲裁卷</p> -->
        <el-button
          v-if="work.arbitrationPaperCount > 0"
          :disabled="!!work.isOver || !!work.breakStatus"
          size="mini"
          class="exam-btn"
          @click="gotoArbitrate(work)"
        >
          处理仲裁卷（<span style="font-weight: bold;color:red;">{{ work.pendingArbitrationCount}}</span>）
        </el-button>
      </div>
      <div class="exam-state" v-if="work.showProblem && work.problemPaperCount">
        <!-- <span class="num">{{ work.pendingCompleteCount }}</span>
        <p class="problem-title">问题卷</p> -->
        <el-button
          v-if="work.problemPaperCount > 0"
          :disabled="!!work.isOver || !!work.breakStatus"
          class="exam-btn"
          @click="gotoProblem(work)"
          >处理问题卷（<span style="font-weight: bold;color:red;">{{ work.pendingCompleteCount}}</span>）
        </el-button>
      </div>
      <div class="exam-state" v-if="work.isSmartCorrect == 1">
        <!-- <p class="problem-title">智批改</p> -->
        <el-button
          size="mini"
          class="exam-btn"
          @click="gotoSmartCorrect(work)"
          :disabled="!!work.isOver || !!work.breakStatus"
          >复核智批改</el-button
        >
      </div>

      <el-button type="info" disabled class="exam-btn" v-if="work.isOver">已结束</el-button>
      <el-button type="info" disabled class="exam-btn" v-else-if="work.breakStatus">已暂停</el-button>
      <div class="exam-state" v-else-if="work.allStuQuesCount > 0">
        <template>
          <el-button
            type="primary"
            class="exam-btn"
            @click="gotoCorrect(work, 2, 0)"
            v-if="work.remainStuQuesCount == 0 && work.effApprovalCount == 0"
            >复查</el-button
          >
          <el-button type="primary" class="exam-btn" @click="gotoCorrect(work, 1, 0)" v-else>{{
            work.completeStuQuesCount == 0 ? '开始阅卷' : '继续阅卷'
          }}</el-button>
        </template>
      </div>
    </div>
      </el-row>
    </div>

  </div>
</template>

<script>
import UserRole from '@/utils/UserRole';
import { mapGetters, mapState } from 'vuex';
import { ICARD_TYPE, ICARD_STATE, IAB_CARD_SHEEFT_TYPE } from '@/typings/card';
import { publishTeaExamAPI } from '@/service/api';
export default {
  name: 'task-item',
  props: ['item', 'index'],
  data() {
    return {
      ICARD_STATE,
      IAB_CARD_SHEEFT_TYPE,
      correctUrl: process.env.VUE_APP_CORRECT_URL,
      isBkzz: false, // 是否是学科组长
      roleList: [],
    };
  },
  components: {},
  computed: {
    ...mapGetters([]),

    // 是否学科组长
    isXkzz() {
      return this.roleList.includes('4');
    },
  },

  created() {},

  async mounted() {
    this.roleList = UserRole.examRolesTypes.split(',')
  },
  methods: {
    /**
     * @name：开始/继续阅卷
     */
    gotoCorrect(item, isReview, isProblem, isArbitrate) {
      let datas = {
        workId: item.workId,
        workTitle: item.title,
        isReview: isReview, // 是否复查
        isProblem: isProblem, // 是否问题卷
        isShowOriginBtn: isProblem ? 1 : 0, // 是否显示原卷按钮
        isArbitrate: isArbitrate ? 1 : 0, // 是否仲裁
      };
      let loginInfo = this.$sessionSave.get('loginInfo');
      let data = JSON.stringify(datas);
      window.open(
        `${this.correctUrl}?userid=${loginInfo.id}&clientType=web&data=${encodeURIComponent(data)}`,
        '_blank'
      );
    },
    /**
     * @name:问题卷
     */
    gotoProblem(item) {
      let isReview = 2;
      //如果有待完成  则为批改状态
      if (item.pendingCompleteCount > 0) {
        isReview = 1;
      }
      this.gotoCorrect(item, isReview, 1);
    },

    // 仲裁卷
    gotoArbitrate(item) {
      let isReview = 2;
      //如果有待完成  则为批改状态
      if (item.pendingArbitrationCount > 0) {
        isReview = 1;
      }
      this.gotoCorrect(item, isReview, 0, 1);
    },

    /**
     * @name:跳转到智批改题目
     */
    gotoSmartCorrect(item) {
      this.$router.push({
        path: '/correct',
        query: {
          workId: item.workId,
          subjectId: item.subjectId,
        },
      });
    },
    goCorrectSet(item){
      this.$router.push({
          name: 'markPaperSetting',
          query: {
            examId: item.examId,
            examName: this.item.title,
            paperNo: item.paperNo,
            subjectId: item.subjectId,
            subjectName: item.subjectName,
            subjectRealName: item.subjectName,
            personBookId: item.workId,
            source: item.source,
            workId: item.workId,
            progress: item.progress,
            progressState: item.progressState,
            schoolId: this.$sessionSave.get('schoolInfo').id,
          },
        });
    },
    goCorrectSpeed(item){
      this.$router.push({
          name: 'markPaperSpeed',
          query: {
            examId: this.item.examId,
            examName: this.item.title,
            subjectId: item.subjectId,
            subjectName: item.subjectName,
            subjectRealName: item.subjectName,
            personBookId: item.workId,
            workId: item.workId,
            schoolId: this.$sessionSave.get('schoolInfo').id,
          },
        });
    },

  // 发布成绩
  async handlePublishScore(item) {
      const loading = this.$loading({
        lock: true,
        text: '正在生成分析报告，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      try {
        await publishTeaExamAPI({
          schoolId: this.$sessionSave.get('schoolInfo').id,
          userId: this.$sessionSave.get('loginInfo').id,
          workId: item.workId,
        });
        this.$message.success("发布成绩成功");
      } catch (error) {
        this.$message.error(error.msg || "发布成绩失败");
      } finally {
        loading.close();
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.exam-item {
  width: 100%;
  min-height: 120px;
  box-sizing: border-box;
  background: #fff;
  padding: 20px;
  border: 1px solid #e4e7eb;
  box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.15);
  border-radius: 6px;
  margin-bottom: 14px;
  color: #838993;
  font-size: 14px;
  position: relative;

  .exam-name-area {
    .exam-name {
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
      color: #161e26;
      line-height: 21px;

      .exam-category {
        margin-right: 15px;
        height: 24px;
        line-height: 24px;
        background: #f0f2f5;
        border-radius: 12px;
        color: #606266;
        text-align: center;
        font-weight: 400;
        font-size: 14px;
        padding: 0 11px;
      }
    }

    .exam-detail {
      margin-top: 15px;

      .line-gap {
        width: 32px;
        position: relative;

        &:before {
          content: '';
          position: absolute;
          left: 50%;
          margin-left: -1px;
          top: 50%;
          margin-top: -7px;
          display: inline-block;
          width: 2px;
          height: 14px;
          background-color: #97a0a8;
        }
      }

      .exam-grade {
        margin-right: 20px;
      }

      > span {
        display: inline-block;
        color: #838993;
      }
    }

    .subject-icon {
      display: block;
      width: 12px;
      height: 15px;
      background: url(../../assets/exam/subject-icon.png);
      margin-right: 5px;
    }

    .subject-name {
      color: #141414;
      width: 130px;
      display: inline-block;
      font-weight: bold;
    }

    .speed-info {
      color: #303233;

      .speed-val {
        font-size: 18px;
        font-weight: bold;
        margin-right: 50px;
      }
    }
  }
  .exam-item-left{
    display: flex;
    align-items: center;
  }

  .exam-item-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    text-align: center;

    .exam-state {
      margin-right: 40px;

      &:last-child {
        margin-right: unset;
      }

      .num {
        font-weight: bold;
        font-size: 18px;
        color: #303233;
      }

      p {
        min-width: 65px;
        margin: 0 0 10px 0;
        color: #838993;
      }
    }

    .exam-btn {
      width: 120px;
      height: 32px;
      padding: unset !important;
      text-align: center;
    }
  }
}
</style>
<style lang="scss" scoped>
.exam-item-right {
  .el-button--mini {
    font-size: 14px;
  }
}
</style>

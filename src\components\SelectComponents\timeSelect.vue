<!--
 * @Descripttion: 
 * @Author: 小圆
 * @Date: 2024-01-18 11:49:22
 * @LastEditors: 小圆
-->
<template>
  <div class="header__select">
    <span class="select__label">时间：</span>
    <el-select
      v-model="timeType"
      style="width: 100px; margin-right: 25px"
      @change="handleChangeTimeType"
    >
      <el-option
        v-for="item in timeTypes"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      >
      </el-option>
    </el-select>
    <!-- 学年 -->
    <el-select
      v-model="yearTimeSlot"
      v-if="timeType === 3"
      style="width: 250px; margin-right: 10px"
      @change="changeYearValue"
    >
      <el-option v-for="item in yearList" :key="item.id" :label="item.label" :value="item.value">
      </el-option>
    </el-select>
    <!-- 按月 -->
    <el-date-picker
      v-if="timeType === 2"
      popper-class="datePicker__time"
      style="margin-right: 10px; width: 250px"
      v-model="dateRange"
      :clearable="false"
      type="monthrange"
      align="right"
      range-separator="-"
      start-placeholder="开始月份"
      end-placeholder="结束月份"
      @change="beforeFindPersonalBookListMonth"
      :picker-options="pickerOptions"
    >
    </el-date-picker>
    <!-- 按日 -->
    <el-date-picker
      v-if="timeType === 1"
      popper-class="datePicker__time"
      style="margin-right: 10px; width: 250px"
      v-model="dateRange"
      :clearable="false"
      type="daterange"
      align="right"
      unlink-panels
      format="yyyy-MM-dd"
      range-separator="-"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      @change="changeDate"
      :picker-options="pickerOptionsDay"
    >
    </el-date-picker>
  </div>
</template>

<script>
import { getLastDayOfYear } from "@/utils/dateUtil";
import { getDefaultDateRangeByType, getLastMonthRange } from "./SelectDefault";
import { getSchoolYearListAPI } from "@/service/api";
import moment from "moment";
export default {
  props: {
    value: {
      type: [Array],
      default: () => [],
    },

    // 默认类型
    defaultType: {
      type: Number,
      default: 3,
    },
  },

  data() {
    return {
      dateRange: [],
      yearList: [],
      //时间筛选类型
      timeTypes: [
        { value: 1, label: "按日" },
        { value: 2, label: "按月" },
        { value: 3, label: "学年" },
      ],
      timeType: 3,
      timeSlot: [],
      yearTimeSlot: "",
      pickerMinDate: "", //第一次选中的时间
      pickerOptions: {
        //选择当前日期之前的时间
        onPick: (obj) => {
          this.pickerMinDate = new Date(obj.minDate).getTime();
        },
        //选择时间范围为一年
        disabledDate: (time) => {
          if (this.pickerMinDate) {
            const day1 = 365 * 24 * 3600 * 1000;
            let maxTime = this.pickerMinDate + day1;
            let minTime = this.pickerMinDate - day1;
            return (
              time.getTime() > maxTime || time.getTime() < minTime
            );
          } 
        },
      },
      pickerOptionsDay: {
        //选择当前日期之前的时间
        onPick: (obj) => {
          this.pickerMinDate = new Date(obj.minDate).getTime();
        },
        //可选择的时间范围为一年
        disabledDate: (time) => {
          if (this.pickerMinDate) {
            const day1 = 365 * 24 * 3600 * 1000;
            let maxTime = this.pickerMinDate + day1;
            let minTime = this.pickerMinDate - day1;
            return (
             time.getTime() > maxTime || time.getTime() < minTime
            );
          } 
        },
      },
    };
  },

  computed: {},

  mounted() {
    this.initYearsList();
  },

  methods: {
    // 初始化学年列表
    async initYearsList() {
      const res = await getSchoolYearListAPI({
        schoolId: this.$sessionSave.get('schoolInfo').id,
      });
      let yearList = res.data || [];
      yearList = yearList.map(item => {
        return {
          id: item.schoolYearId,
          value: `${moment(item.startTime).format('YYYY-MM-DD')}|${moment(item.endTime).format('YYYY-MM-DD')}`,
          label: item.schoolYear,
        }
      })
      this.yearList = yearList;
      this.yearTimeSlot = this.yearList[0].value;

      if (this.defaultType) {
        this.timeType = this.defaultType;
      }
      let timeSlot = this.yearList[0].value.split('|');
      let timeSlotFormat = [
        timeSlot[0] ? this.$formatDate(new Date(timeSlot[0])) : "",
        timeSlot[1] ? this.$formatDate(new Date(timeSlot[1])) : "",
      ];
      this.dateRange = timeSlotFormat;
      this.$emit("input", timeSlotFormat);
      this.$emit("init");
    },

    //切换时间筛选类型，按日默认最近一周，按月默认当前月
    handleChangeTimeType(val) {
      this.timeType = val;
      this.pickerMinDate = "";
      this.yearTimeSlot = this.yearList[0].value;
      let timeSlot;
      if(val == 3) {
        timeSlot = this.yearTimeSlot.split("|");
      } else {
        timeSlot = getDefaultDateRangeByType(this.timeType);
      }
      this.changeDate(timeSlot);
    },

    // 更新学年
    changeYearValue(val) {
      this.yearTimeSlot = val;
      let timeSlot = this.yearTimeSlot.split("|");
      this.changeDate(timeSlot);
    },

    beforeFindPersonalBookListMonth(val) {
      this.pickerMinDate = "";
      let timeSlot = [
        this.$moment(val[0]).startOf("month").format("YYYY-MM-DD"),
        this.$moment(val[1]).endOf("month").format("YYYY-MM-DD"),
      ];
      this.changeDate(timeSlot);
    },

    changeDate(timeSlot = []) {
      this.pickerMinDate = "";
      let timeSlotFormat = [
        timeSlot[0] ? this.$formatDate(new Date(timeSlot[0])) : "",
        timeSlot[1] ? this.$formatDate(new Date(timeSlot[1])) : "",
      ];
      this.dateRange = timeSlotFormat;
      this.$emit("input", timeSlotFormat);
      this.$emit("change", timeSlotFormat);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "./select.scss";
</style>

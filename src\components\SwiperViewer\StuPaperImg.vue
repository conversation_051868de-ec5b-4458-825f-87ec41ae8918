<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-07-03 17:57:57
 * @LastEditors: 小圆
-->
<template>
  <div class="img-box" v-if="currentRow">
    <el-image class="img" :src="imgSrc" @load="loadImage" v-if="imgSrc"></el-image>
    <div v-else v-loading="true"></div>

    <!-- 总分 -->
    <div class="total-score-title" v-if="isDrawImg">
      <span v-show="index == 0">
        <strong> <span>总分：</span>{{ defaultScore }}</strong>
      </span>
    </div>

    <!-- 第一页显示学号 -->
    <div
      class="stu-no-box"
      :style="{ top: 45 + 'px', left: 10 + 'px' }"
      v-if="currentRow.page % 2 != 0 && isDrawImg && currentRow.image != ''"
    >
      <span>{{ currentRow.stu_no }} </span>
    </div>

    <div v-if="edit && isDrawImg" :key="key">
      <template v-for="item in currentRow.questions">
        <ObjQuesEditor v-if="item.is_obj" :quesItem="item" :scale="scale" :scoreMap="scoreMap"></ObjQuesEditor>
        <SubjectiveQuesEditor v-else :quesItem="item" :scale="scale" :scoreMap="scoreMap"></SubjectiveQuesEditor>
      </template>
    </div>

    <div v-else>
      <template v-if="isDrawImg">
        <template v-for="(ques, quesIndex) in currentRow.questions">
          <div v-if="ques.is_obj">
            <div class="pointer-none" :style="{ textAlign: 'right', position: 'absolute', ...getBoxStyle(ques.pos) }">
              <span class="score-text" v-if="ques.tempScoreList && ques.showScore">
                <div v-for="item in ques.tempScoreList">
                  <span>{{ item.title }}：</span>
                  <span>{{ item.score }}分 </span>
                </div>
              </span>
            </div>
            <points-box
              class="points-box"
              :class="{
                active: box.fill,
                error: box.fill && (Math.pow(2, optionIndex) & ques.answer_int) == 0,
                right: box.fill && (Math.pow(2, optionIndex) & ques.answer_int) != 0,
              }"
              v-for="(box, optionIndex) in ques.list"
              v-if="ques.type != 16 && ques.type != 13"
              :ques="ques"
              :points="box"
              :quesIndex="quesIndex"
              :optionIndex="optionIndex"
              :key="optionIndex"
              :checkType="noPoint ? 'errpage' : 'detail'"
              :scale="scale"
            ></points-box>
          </div>
          <div v-else>
            <div class="pointer-none" :style="{ textAlign: 'right', position: 'absolute', ...getBoxStyle(ques.pos) }">
              <span class="score-text" v-if="ques.tempScoreList && ques.showScore">
                <div v-for="item in ques.tempScoreList">
                  <span>{{ item.title }}：</span>
                  <span>{{ item.score }}分 </span>
                </div>
              </span>
            </div>

            <!-- 题目小空对错 -->
            <template v-for="(scores, scoresIndex) in ques.score_list">
              <div
                v-if="
                  (scores.code === 0 && ques.question_type == 3 && source == ISOURCE_TYPES.HAND) || ques.ai_mode == 1
                "
                :style="{
                  position: 'absolute',
                  ...getBoxStyle(scores.pos),
                  color: 'red',
                  fontSize: '20px',
                  fontWeight: 'bold',
                  textAlign: 'right',
                }"
              >
                <SvgCheck class="score-svg check-svg" v-if="scores.score == scores.total_score"></SvgCheck>
                <SvgClose class="score-svg close-svg" v-else-if="scores.score == 0"></SvgClose>
                <SvgHalfCheck class="score-svg half-svg" v-else-if="scores.score"></SvgHalfCheck>
              </div>
            </template>
          </div>
        </template>
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

import { replaceALiUrl } from '@/utils/common';
import { IPaper } from './StuPaperInterface';
import PointsBox from '@/components/scan/PointsBox.vue';
import { getImgUrl } from '@/utils/index';
import ObjQuesEditor from './Edit/objQuesEditor.vue';
import SubjectiveQuesEditor from './Edit/subjectiveQuesEditor.vue';
import SvgHalfCheck from '../Svg/SvgHalfCheck.vue';
import SvgCheck from '../Svg/SvgCheck.vue';
import SvgClose from '../Svg/SvgClose.vue';
import { ISOURCE_TYPES } from '@/typings/card';

@Component({
  components: {
    PointsBox,
    ObjQuesEditor,
    SubjectiveQuesEditor,
    SvgHalfCheck,
    SvgCheck,
    SvgClose,
  },
})
export default class StuPaperImg extends Vue {
  // 当前Paper对象
  @Prop() currentRow: IPaper;
  // 来源
  @Prop() source: number;
  // 当前Paper对象索引
  @Prop() index: number;
  // 默认得分
  @Prop() defaultScore: any;
  // 是否显示得分
  @Prop() showQuesScore: boolean;
  // 是否显示笔迹
  @Prop() mark: boolean;
  // 是否可编辑
  @Prop({ default: false }) edit: boolean;
  // 分数映射
  @Prop({
    default: () => {
      return {};
    },
  })
  scoreMap: any;

  // 图片与坐标缩放值
  scale: number = 1;
  //答案坐标需要减去的宽度 mm
  subWidth: number = 20;
  // 图片是否加载
  isDrawImg: boolean = false;
  // 是否可以点击
  noPoint: boolean = true;
  // 扫描图片地址
  imgSrc: string = '';
  // 强制刷新
  key = 0;

  ISOURCE_TYPES = ISOURCE_TYPES;

  @Watch('mark')
  async onCurrentRowChange() {
    this.isDrawImg = false;
    this.imgSrc = await this.getImage(this.currentRow);
  }

  async created() {
    this.imgSrc = await this.getImage(this.currentRow);
  }

  mounted() {}

  getBoxStyle(box) {
    return {
      left: box[0] * this.scale + 'px',
      top: box[1] * this.scale + 'px',
      width: box[2] * this.scale + 'px',
      height: box[3] * this.scale + 'px',
    };
  }

  // 获取图片
  async getImage(item) {
    let image = '';
    if (this.mark) {
      image = item.markImage;
    } else {
      image = replaceALiUrl(item.image);
    }
    image = await getImgUrl(image);
    return image;
  }

  loadImage(val: Event) {
    let imgWidth = val.currentTarget.width;
    let imgHeight = val.currentTarget.height;
    let heightScale = 1080 / imgHeight;
    imgWidth = imgWidth * heightScale;
    imgHeight = 1080;
    val.currentTarget.style.height = imgHeight + 'px';
    val.currentTarget.style.width = imgWidth + 'px';

    this.scale = imgHeight / 297;
    this.key++;
    this.$emit('loadImage', val.currentTarget, this.index);
    this.isDrawImg = true;
  }
}
</script>

<style scoped lang="scss">
.stu-img-list {
  height: 100%;
  width: calc(100% - 305px);
  display: inline-block;
  overflow-y: scroll;
  .img {
    position: relative;
    width: 100%;
  }
}
.img-box {
  position: relative;
}
.score-box {
  position: absolute;
  width: 60px;
  height: 35px;
  opacity: 0.8;
  right: 0;
  font-size: 24px;
  text-align: center;
  color: red;
  font-weight: bold;
  &.objBox {
    display: none;
  }
}

.total-score-box,
.total-score-title,
.stu-no-box {
  position: absolute;
  opacity: 0.8;
  text-align: center;
  color: red;
  z-index: 999;
}
.total-score-title {
  top: 1px;
  left: 10px;
  font-size: 22px;
}
.total-score-box {
  font-size: 24px;
  top: 0px;
  left: 130px;
  font-weight: bold;
}
.stu-no-box {
  font-size: 28px;
  font-weight: bold;
}
.stu-no {
  color: #333;
  font-size: 16px;
}
.stu-task-name {
  color: #909399;
}
// .points-box {
//   position: absolute;
//   cursor: pointer;
//   z-index: 2;
//   &.active {
//     border: solid 4px #01cc7d;
//   }
//   &.error {
//     border: solid 4px red !important;
//   }
//   &.right {
//     border: solid 4px #01cc7d !important;
//   }
// }
.page-box-main {
  position: absolute;
  border: solid 2px #409eff;
}

.pointer-none {
  pointer-events: none;
}

.score-text {
  font-weight: 700;
  color: red;
  font-size: 20px;
  opacity: 0.6;
  pointer-events: auto;
  &:hover {
    opacity: 1;
  }
}
.img-dropdown-menu {
  position: absolute;
  font-size: 20px;
  right: 20px;
  top: 25px;
}
.img-switch {
  position: absolute;
  font-size: 12px !important;
  right: 15px;
  top: 1px;
}
.pointer-none {
  pointer-events: none;
}

.score-svg {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 25px;
  height: 25px;
  fill: #f56c6c;
}

.close-svg {
  width: 25px;
  height: 17.5px;
  fill: #f56c6c;
}

.half-svg {
  fill: #e6a23c;
}

.check-svg {
  fill: #67c23a;
}
</style>

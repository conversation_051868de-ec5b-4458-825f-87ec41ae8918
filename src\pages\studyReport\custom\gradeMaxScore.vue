<template>
  <div>
    <div v-loading="tableLoading">
      <base-table
        v-if="tableData.length"
        :data="tableData"
        :column="tableColumns"
        :span-method="handleSpanMethod"
        v-bind="getTableAttr()"
        v-drag-table
      ></base-table>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';

@Component({
  components: {},
})
export default class Index extends Mixins(TableCommon) {
  /**
   * 合并单元格句柄方法
   */
  handleSpanMethod({
    row, // 行
    column, // 列
    rowIndex, // 行索引
    columnIndex, // 列索引
  }) {
    if (column.property === 'subjectName') {
      const subjectSpanArr = this.formatRowspanAndColspan(this.tableData, 'subjectName');
      return {
        rowspan: subjectSpanArr[rowIndex],
        colspan: 1,
      };
    }
  }
}
</script>

<style scoped lang="scss"></style>

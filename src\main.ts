/*
 * @Descripttion:
 * @Author: 小圆
 * @Date: 2023-12-04 20:29:37
 * @LastEditors: 小圆
 */
import Vue from 'vue';
import ElementUI from '@iclass/element-ui';
import '@iclass/element-ui/lib/theme-chalk/index.css';
import { router } from './router/baseRouter';
import { router as syRouter } from './router/syRouter';

import App from './App.vue';
import store from '@/store/index';
import Utils from './utils'; // 引入全局工具类api
import 'nprogress/nprogress.css'; // progress bar style
import '@/plugins/iview/ui-settings.js'; // 按需导入iview-ui
import './styles/common.scss';
import ossUploadFile from '@/utils/ossUploadFile';
import * as echarts from 'echarts';
import Bus from '@/utils/bus'; // 事件中心
import VueDragResize from 'vue-drag-resize';
import VueTouch from 'vue-touch';
import moment from 'moment'; //导入文件
import '@/utils/array-extensions';
import VueAwesomeSwiper from 'vue-awesome-swiper';
import 'swiper/dist/css/swiper.css';
import { initIDBStore } from './plugins/idb-store';
import { TrackHelper } from './plugins/track-helper';
// TIP VueRouter类型注解，不要删除
import VueRouter from 'vue-router';
import { isC30, isSj, isSy, isYc } from './utils/channel';
import { setupDirective } from '@/utils/directives';


new TrackHelper();
// 初始化indexDB数据库
initIDBStore();
Vue.use(VueAwesomeSwiper);
VueTouch.registerCustomEvent('doubletap', {
  type: 'tap',
  taps: 2,
});
Vue.use(VueTouch, { name: 'v-touch' });
Vue.use(Utils);
// 挂载
Vue.use(ElementUI);
Vue.use(Bus);
ossUploadFile.getSTSToken();
Vue.prototype.$moment = moment; //赋值使用
Vue.prototype.$echarts = echarts;

Vue.component('vue-drag-resize', VueDragResize);

setupDirective();

store.dispatch('initData');

if (window !== window.parent && isC30()) {
  // 被iframe内嵌，使用window.parent.open （大屏作业-作业报告使用）
  window.open = function (url, target, features) {
    return window.parent.open(url, target, features);
  };
}

function mainApp() {
  let useRouter;
  if (isC30() || isYc() || isSj()) {
    useRouter = router;
  } else if (isSy()) {
    useRouter = syRouter;
  }

  new Vue({
    store,
    router: useRouter,
    render: h => h(App),
  }).$mount('#app');
}

mainApp();

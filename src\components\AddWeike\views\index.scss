.video-container {
    position: relative;
    overflow: hidden;
    border-radius: 3px;
    border: 1px solid #ccc;

    &:hover {
        .play-btn-area {
            display: block;
        }
    }
}

.video-time {
    position: absolute;
    right: 5px;
    bottom: 5px;
    padding: 5px 10px;
    background-color: rgba(0, 0, 0, .5);
    color: #fff;
    font-size: 12px;
    z-index: 2;
    border-radius: 3px;
}

.play-btn-area {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 3;
    background-color: rgba(0, 0, 0, .2);
    color: #fff;
    text-align: center;

    .el-icon-video-play {
        font-size: 58px;
    }
}


.dialog-footer {
    padding-top: 10px;
    border-top: 1px solid #f0f0f0;
}
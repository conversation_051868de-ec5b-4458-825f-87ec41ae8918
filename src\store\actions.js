/*
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2024-05-16 14:40:00
 * @LastEditors: 小圆
 * @LastEditTime: 2025-07-24 16:55:28
 */
import { localSave, sessionSave } from '@/utils';
import { getRegion, getSubjectsAPI,getSchoolYearListAPI, getCampusCodeList } from '@/service/api';
import { selectAllGrade } from '@/service/pbook';
const actions = {
  /**
   * 初始化数据
   */
  async initData({ commit, state }) {
    let provinceList = localSave.get('PROVINCE_LIST');
    let gradeList = localSave.get('GRADE_LIST');
    let yearList = sessionSave.get('YEAR_LIST');
    if (provinceList) {
      commit('SET_REGION_PROVINCE', provinceList);
    }
    if (gradeList) {
      commit('saveGradeList', gradeList);
    }
    if (!provinceList || provinceList.length == 0) {
      //获取地区
      getRegion({
        type: 1,
        id: '',
      }).then(data => {
        let list = data.data;
        localSave.set('PROVINCE_LIST', list);
        commit('SET_REGION_PROVINCE', list);
      });
    }
    let schoolInfo = sessionSave.get('schoolInfo');
    if (schoolInfo) {
      //获取基础数据学校下全部学科
      const res = await getSubjectsAPI({ schoolId: schoolInfo.id });
      let subjectList = [];
      if (res.code == 1) {
        subjectList = res.data.map(item => {
          item.phase = item.phase + 2;
          item.phaseId = item.phase;
          item.name = item.subject_name;
          return item;
        });
      }
      localSave.set('SUBJECT_LIST', subjectList);
      commit('SET_SUBJECT', subjectList);
    }
    if (!gradeList || gradeList.length == 0) {
      //获取所有年级
      selectAllGrade().then(res => {
        localSave.set('GRADE_LIST', res.data);
        commit('saveGradeList', res.data);
      });
    }
    if (schoolInfo) {
      //获取所有年份
      getSchoolYearListAPI({
        schoolId: schoolInfo.id,
      }).then(res => {
        let yearList = res.data || [];
        yearList = yearList.map(item => {
          return {
            id: item.schoolYearId,
            name: item.schoolYear,
            startTime: item.startTime,
            endTime: item.endTime,
          }
        });
        sessionSave.set('YEAR_LIST', yearList);
        commit('saveYearList', yearList);
      });
    }
  },
};
export default actions;

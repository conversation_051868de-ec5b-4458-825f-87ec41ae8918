<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-03-25 10:38:56
 * @LastEditors: 小圆
-->
<template>
  <div class="header-item">
    <span class="header-item__label">由 </span>
    <el-input-number
      class="header-item__inputnum"
      v-model="filterData.startRank"
      :min="1"
      :max="filterData.endRank"
      :controls="false"
      :step="1"
      step-strictly
    ></el-input-number>
    <span class="header-item__label"> 名~至 </span>
    <el-input-number
      class="header-item__inputnum"
      v-model="filterData.endRank"
      :min="filterData.startRank"
      :controls="false"
      :step="1"
      step-strictly
    ></el-input-number>
    <span class="header-item__label"> 名</span>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import ReportComponent from './ReportComponent';

@Component
export default class ReportRankRangeInput extends ReportComponent {
  onChange(value: number) {}
}
</script>

<style scoped lang="scss">
@import './reportSelect.scss';
</style>

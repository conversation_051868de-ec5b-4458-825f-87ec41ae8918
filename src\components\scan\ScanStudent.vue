<template>
  <div v-if="studentInfo" @click="answerEditDlg.show = false" class="student-scan-paper">
    <div class="student-images-box">
      <div class="image-header">
        <span class="text">识别区</span>
        <span class="title">{{ paperInfo.title }}</span>
        <el-button class="edit-btn" type="primary" size="mini" @click="editImage=!editImage">
          {{ editImage ? '取消' : '编辑' }}
        </el-button>
      </div>
      <div ref="studentImage" class="student-images">
        <scan-image v-for="(quesImage,i) in quesImageList" :image="quesImage" :index="i"
                    :can-edit="editImage" @add-ques-answer-box="addAnswerBox"
                    :ref="'ques_img_'+i" :key="'image_'+i" @add-ques-box="addQuesBox"
                    @delete-ques-box="deleteQuesBox"></scan-image>
      </div>
    </div>
    <div class="student-score-list">
      <div class="right_title">
        <span class="ques_score_tip">题目得分</span>
        <div v-if="studentInfo.studentNo" class="student_no">考号：
          <span class="stu_no_input" @dblclick="changeStudentNo"
          >{{ studentInfo.studentNo }}</span>
        </div>
      </div>
      <div class="ques_score_item">
        <div class="column ques_sort">题号</div>
        <div class="column ques_score">得分</div>
        <div class="column my_answer">我的答案</div>
        <div class="column ques_answer">参考答案</div>
      </div>
      <div class="score-list">
        <div v-for="(item,i) in quesList" class="ques_score_item" :key="'ques_'+i">
          <div class="column ques_sort">
            <span class="ques_sort_btn"
                  :class="{'ques-error':item.ques.error>0,'ques-change':item.ques.changeStatus>0}"
                  @click="scrollToQues(i)">{{ i + 1 }}</span>
          </div>
          <div class="column ques_score">
            <InputNumber @on-change="scoreChange(item)"
                         @on-blur="scoreChange(item)"
                         :min="0"
                         :max="item.fullScore"
                         :step="1"
                         size="small"
                         :disabled="item.isChoose||readonly||!item.ques.quesId"
                         class="score_input" v-model="item.ques.score"></InputNumber>
          </div>
          <div v-if="item.isChoose" @click.stop="toEditAnswer(item,$event)" class="column my_answer">
            {{ item.ques.answer || (item.ques.error > 0 ? '--' : '未作答') }}
          </div>
          <div v-else class="column my_answer"></div>

          <div v-if="item.isChoose" class="column ques_answer">{{ item.ans.join('') }}</div>
          <div v-else class="column ques_answer">
            <span class="view_answer" @click="viewAnswer(item)">查看
              <i class="header-back el-icon-arrow-right"></i></span>
          </div>
        </div>
        <el-button v-if="!(readonly||quesCountNoMatch)" class="save-paper-btn" type="primary" @click="saveStudentPaper">
          保存
        </el-button>
        <el-popover
            placement="bottom"
            title=""
            ref="answerEdigDlg"
            class="answer-edit-dlg student-scan-paper-answer-edit-popover"
            trigger="manual"
            :style="answerEditDlg.style"
            v-model="answerEditDlg.show">
          <span class="answer-option" v-for="item in answerEditDlg.options" :key="'option_'+item.name"
                @click.stop="selectOption(item)" :class="{active:item.checked}">{{ item.name }}</span>

          <span class="answer-option no-answer-option" :class="{active:answerEditDlg.noAnswer}"
                @click.stop="selectNoAnswer">未作答</span>
        </el-popover>

      </div>
    </div>
    <el-dialog
        class="answer-dialog"
        v-if="answerQues"
        :title="`第${answerQues.ques.quesSort}题答案`"
        :visible.sync="answerDialog">
      <div class="answer-body">
        <span class="answer-label">【答案】：</span>
        <latex-html class="answer-html" v-if="answerQues" :html="answerQues.ans.join('')"></latex-html>
      </div>
      <div class="answer-body">
        <span class="answer-label">【解析】：</span>
        <latex-html class="answer-html" v-if="answerQues" :html="answerQues.exp"></latex-html>
      </div>
    </el-dialog>

    <el-button
        class="btn-full-screen-loading"
        type="primary"
        v-loading.fullscreen.lock="fullscreenLoading">
      loading
    </el-button>

    <el-dialog
        title="新的题目框"
        class="add-box-dialog"
        width="30%"
        @close="addQuesBoxClose"
        :visible.sync="addBoxInfo.show">
      <el-form ref="form" label-width="80px">
        <el-form-item label="序号：">
          <el-input-number :min="addBoxInfo.preSort||1" :max="addBoxInfo.nextSort||(addBoxInfo.preSort+1)"
                           v-model="addBoxInfo.quesSort">
            <template slot="prepend">第</template>
            <template slot="append">题</template>
          </el-input-number>
        </el-form-item>

      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="danger"
                   :disabled="!addBoxInfo.detectFinish"
                   v-show="!((addBoxInfo.quesSort===addBoxInfo.preSort+1)&&!addBoxInfo.nextSort)"
                   @click="saveQuesBox(1)">替 换</el-button>
        <el-button type="primary"
                   :disabled="!addBoxInfo.detectFinish"
                   v-show="addBoxInfo.quesSort > addBoxInfo.preSort"
                   @click="saveQuesBox(2)">插 入</el-button>
        <el-button type="primary"
                   :disabled="!addBoxInfo.detectFinish"
                   v-show="addBox3Show"
                   @click="saveQuesBox(3)">补 充</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import {getStudentBook, getStudentPaperInfo} from "@/service/pexam";
import {getQueListWithWork} from "@/service/api";
import {FS_URL, inArr} from "@/utils/common";
import {chooseQuesSearch} from "@/service/pbook";
import {getViewPaper} from "@/service/testbank";
import {updateStudentPaper} from "@/service_json/pexam_json";
import ScanImage from "@/components/scan/ScanImage";
import {detectAnswerContent, detectQuesAnswer} from "@/service/pocr";
import LatexHtml from "@/components/LatexHtml";

const img_width = 1000;
export default {
  name: 'scan-student',
  components: {LatexHtml, ScanImage},
  props: ['studentPaperId', 'studentId', 'paperInfo', 'readonly'],
  data() {
    return {
      A_Z: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
      studentInfo: null,
      studentNo: '',
      quesImageList: [],
      quesList: [],
      inArr: inArr,
      answerDialog: false,
      answerQues: null,
      answerEditDlg: {
        show: false,
        style: {
          top: '0',
          left: '0'
        },
        options: [
          {
            name: 'A',
            checked: true
          },
          {
            name: 'B',
            checked: false
          },
          {
            name: 'C',
            checked: false
          },
          {
            name: 'D',
            checked: false
          }
        ],
        single: false,
        noAnswer: false,
        info: null
      },
      changeBoxes: [],
      fullscreenLoading: false,
      quesCountNoMatch: false,
      addBoxInfo: {
        show: false,
        quesSort: 0,
        preSort: 0,
        nextSort: 0,
        box: null,
        url: null,
        index: 0,
        detect: null,
        detectFinish: false
      },
      editImage: false,
      workIds:[]
    };
  },
  watch: {
    'studentPaperId': function (newValue) {
      this.getStudentInfo()
    },
  },
  computed: {
    addBox3Show() {
      let info = this.addBoxInfo
      return (info.quesSort === info.preSort) || (info.quesSort === info.nextSort)
    }
  },
  mounted() {
    this.getStudentInfo()
  },
  methods: {
    changeStudentNo() {
      this.$emit('change-student-no', this.studentPaperId)
    },
    /**
     * 删除题目box
     */
    deleteQuesBox(box) {
      let quesInfo = this.studentInfo.quesList[box.quesSort - 1]
      quesInfo.boxes.splice(box.index, quesInfo.boxes.length)
      if (quesInfo.boxes.length === 0) {
        this.studentInfo.quesList.splice(box.quesSort - 1, 1)
      }

      this.initQuesImages(this.quesList)
    },
    /**
     * 保存题目框
     * type：1 替换，2 插入，3 补充
     */
    saveQuesBox(type) {
      let quesList = this.studentInfo.quesList;
      let boxInfo = this.addBoxInfo
      boxInfo.url = boxInfo.detect.url
      let box = boxInfo.box.join(' ');
      let boxItem = {
        box: box,
        page: boxInfo.page,
        path: boxInfo.url
      }
      let ques = {
        answer: '',
        score: 0,
        error: 0,
        url: '',
        boxes: [boxItem]
      }

      let questionInfo = this.quesList[boxInfo.quesSort - 1]

      switch (type) {
        case 1:
          quesList.splice(boxInfo.quesSort - 1, 1, ques);
          break;
        case 2:
          quesList.splice(boxInfo.quesSort - 1, 0, ques);
          break;
        case 3:
          ques = quesList[boxInfo.quesSort - 1]

          ques.boxes.push(boxItem);
          ques.boxes = quesList[boxInfo.quesSort - 1]
              .boxes.sort((a, b) => {
                if (a.page !== b.page) {
                  return a.page - b.page
                }
                let a1 = parseFloat(a.box.split(' ')[1]);
                let b1 = parseFloat(b.box.split(' ')[1])
                return a1 - b1
              });
          break;
      }

      if (boxInfo.detect.data.length) {
        let detect = boxInfo.detect.data[0]
        this.updateQuesAnswer(questionInfo, ques, detect)

        boxItem.answerBox = detect.box
      }
      ques.url = ''
      this.studentInfo.quesList = quesList
      this.initQuesImages(this.quesList)
      this.addBoxInfo.show = false
    },
    /**
     * 更新识别的信息
     */
    updateQuesAnswer(questionInfo, ques, detect) {
      let answer = detect.answer
      if (this.inArr(questionInfo.type, [1, 8])) {
        if (answer) {
          let answers = answer.split('')
          let answerSet = new Set(answers)
          if (answerSet.size !== answers.length) {
            answer = ''
            ques.error = 1
          }
          answerSet.forEach(a => {
            let code = a.charCodeAt() - 64
            if (code > questionInfo.opts_htmls.length) {
              ques.error = 1
            }
          })
        } else {
          ques.error = 1
        }
      } else if (ques.score > questionInfo.fullScore) {
        ques.error = 1
      }

      ques.answer = answer;
      ques.scanAnswer = detect.answer
      ques.answerRate = detect.answerRate

      ques.score = parseFloat(detect.score) || 0
      ques.scanScore = detect.score
      ques.scoreRate = detect.scoreRate
    },
    /**
     * 下一题的序号
     */
    getNextQuesSort(index) {
      let currentImage = this.quesImageList[index]
      let sort = 0
      if (currentImage.boxes && currentImage.boxes.length) {
        sort = currentImage.boxes[0].quesSort
      }
      return sort
    },
    /**
     * 上一题的序号
     */
    getPreQuesSort(index) {
      let currentImage = this.quesImageList[index]
      let sort = 0
      if (currentImage.boxes && currentImage.boxes.length) {
        sort = currentImage.boxes[currentImage.boxes.length - 1].quesSort
      }
      return sort
    },
    /**
     * 关闭添加题目框
     */
    addQuesBoxClose() {
      this.$refs['ques_img_' + this.addBoxInfo.page][0].endAddBox()
    },
    getBoxPosition(width, height, box) {
      let imgWidth = parseInt(width * box[2])
      let imgHeight = parseInt(height * box[3])
      let startX = parseInt(width * box[0] - imgWidth / 2)
      let startY = parseInt(height * box[1] - imgHeight / 2)
      return {
        imgWidth,
        imgHeight,
        startX,
        startY
      }
    },
    getOssStyle(info) {
      return `x-oss-process=image/crop,x_${info.startX},y_${info.startY},w_${info.imgWidth},h_${info.imgHeight}`
    },
    /**
     * 添加答案框
     */
    addAnswerBox(boxInfo, box, imgInfo) {
      let questionInfo = this.quesList[boxInfo.quesSort - 1]
      let ques = this.studentInfo.quesList[boxInfo.quesSort - 1]

      ques.boxes[boxInfo.index].answerBox = box.join(' ')
      this.initQuesImages(this.quesList)

      let url = `${FS_URL}/${imgInfo.path}?x-oss-process=image/resize,w_${img_width}`;

      let img_height = parseInt(img_width * imgInfo.height / imgInfo.width)

      let quesBox = this.getBoxPosition(img_width, img_height, boxInfo.box)

      url += ',' + this.getOssStyle(quesBox)

      let cutBox = [
        box[0],
        box[1] - box[3] * 0.1,
        box[2] * 0.9,
        box[3] * 0.7
      ]

      let answerBox = this.getBoxPosition(quesBox.imgWidth, quesBox.imgHeight, cutBox)

      url += ',' + this.getOssStyle(answerBox)

      console.log(url)

      detectAnswerContent(url)
          .then(res => {
            if (res.code === 1) {
              this.updateQuesAnswer(questionInfo, ques, res.data)
              this.initQuesImages(this.quesList)
            }
          })
    },
    /**
     * 添加题目框
     */
    async addQuesBox(info) {
      let page = info.page
      let box = info.box
      let url = info.url
      this.addBoxInfo.detect = null
      this.addBoxInfo.detectFinish = false

      detectQuesAnswer({
        path: `${FS_URL}/${url}`,
        paperId: this.paperInfo.id
      }).then(r => {
        if (r.code === 1) {
          this.addBoxInfo.detect = r
          this.addBoxInfo.detectFinish = true
        }
      })

      let currentImage = this.quesImageList[page]
      //上一个题号
      let preSort = 0
      //下一个题号
      let nextSort = 0

      if (currentImage.boxes && currentImage.boxes.length) {
        for (let i = 0; i < currentImage.boxes.length; i++) {
          let cBox = currentImage.boxes[i]
          let cSort = cBox.quesSort
          if (box[1] < cBox.box[1]) {
            nextSort = cSort;
            break
          } else {
            preSort = cSort
          }
        }
      }

      if (!preSort) {
        for (let i = page - 1; i >= 0; i--) {
          preSort = this.getPreQuesSort(i)
          if (preSort) {
            break
          }
        }
      }

      if (!nextSort) {
        for (let i = page + 1; i < this.quesImageList.length; i++) {
          nextSort = this.getNextQuesSort(i)
          if (nextSort) {
            break
          }
        }
      }

      this.addBoxInfo.quesSort = preSort + 1
      this.addBoxInfo.preSort = preSort
      this.addBoxInfo.nextSort = nextSort
      this.addBoxInfo.page = page
      this.addBoxInfo.box = box
      this.addBoxInfo.url = url
      this.addBoxInfo.show = true
    },
    /**
     * 分数改变事件
     */
    scoreChange(item) {
      item.ques.changeStatus = item.ques.score !== item.ques.scoreValue ? 2 : 0
    },
    /**
     * 保存试卷
     */
    saveStudentPaper() {
      let questionList = JSON.stringify(this.studentInfo.quesList)
      questionList = JSON.parse(questionList)

      let errorList = questionList.filter(it => it.changeStatus === 0 && it.error > 0)
      if (errorList.length > 0) {
        this.$message.error("请修改错误识别的题目")
        return
      }

      let noScore = questionList.find(it => typeof it.score !== 'number')

      if (noScore) {
        this.$message.error(`第${noScore.quesSort}题请填写分数`)
        return;
      }

      questionList.forEach(ques => {
        ques.isRight = ques.fullScore === ques.score ? 1 : 0
      });

      let changeList = questionList.filter(it => it.changeStatus > 0)
      /*
            if (changeList.length === 0) {
              this.$message.error("没有任何修改")
              return
            }*/

      let boxList = []
      //保存修改后的数据，用于以后加入训练
      for (let i = 0; i < changeList.length; i++) {
        let ques = changeList[i]

        if (!ques.changeStatus === 4) {
          continue
        }
        for (let j = 0; j < ques.boxes.length; j++) {
          let box = ques.boxes[j];
          if (box.answerBox) {
            let img = this.$refs['ques_img_' + box.page][0].getImage()
            if (!img.width) {
              this.$message.error('请等待图片加载完成')
              return
            }
            boxList.push({
              quesId: ques.quesId,
              image: box.path,
              width: img.width,
              height: img.height,
              answerBox: box.answerBox,
              box: box.box,
              type: ques.changeStatus,
              result: ques.changeStatus === 1 ? ques.answer : ques.score
            })
            break
          }
        }
      }

      /**
       * 更新数据
       */
      updateStudentPaper({
        id: this.studentInfo.id,
        quesList: questionList,
        boxList: boxList
      }).then(res => {
        this.$emit('student-update')
        this.$message.success('更新成功')
      }).catch(err => {
      })

    },
    /**
     * 编辑答案信息
     */
    toEditAnswer(item, event) {
      if (this.readonly || !item.ques.quesId) {
        return;
      }
      let dom = event.currentTarget;
      if (this.answerEditDlg.info === item) {
        this.answerEditDlg.info = null
        this.answerEditDlg.show = false
        return
      }

      let options = []

      //添加选项数量
      for (let i = 0; i < item.opts_htmls.length; i++) {
        let option = this.A_Z[i]
        let info = {
          checked: false,
          name: option
        }
        if ((item.ques.error === 0
            || item.ques.changeStatus > 0) &&
            item.ques.answer.indexOf(option) >= 0) {
          info.checked = true
        }
        options.push(info);
      }

      this.answerEditDlg.options = options;
      this.answerEditDlg.style = {
        top: (dom.offsetTop + dom.clientHeight + 10) + 'px',
        left: (230 - options.length * 30) / 2 + 'px'
      }
      this.answerEditDlg.single = item.type === 8

      this.answerEditDlg.noAnswer = !item.ques.answer && item.ques.error === 0
      this.answerEditDlg.info = item
      this.answerEditDlg.show = true
    },
    /**
     * 选择选项
     */
    selectOption(item) {
      let options = this.answerEditDlg.options
      let answers = []
      if (this.answerEditDlg.single) {
        if (item.checked) {
          return
        }
        options.forEach(it => {
          it.checked = false
        })
        item.checked = true
        this.answerEditDlg.noAnswer = false
        answers.push(item.name)
      } else {
        item.checked = !item.checked
        options.forEach(item => {
          if (item.checked) {
            this.answerEditDlg.noAnswer = false
            answers.push(item.name)
          }
        })
      }
      let ques = this.answerEditDlg.info.ques
      ques.answer = answers.join('')
      this.judgeQuesRight(this.answerEditDlg.info)
      //答案是不是改变
      ques.changeStatus = ques.answer !== ques.answerValue ? 1 : 0;
      if (ques.error > 0) {
        ques.error = -1;
      }
    },
    judgeQuesRight(info) {
      let ques = info.ques
      if (ques.typeId !== 1 && ques.typeId !== 8) {
        return
      }
      let rightAnswer = info.answer.split(',').join('');

      //判断正确错误并更改分数
      let isRight = ques.answer === rightAnswer
      if (isRight) {
        ques.score = info.fullScore
      } else if (ques.answer && rightAnswer.indexOf(ques.answer) >= 0) {
        ques.score = info.fullScore / 2
      } else {
        ques.score = 0
      }
    },
    /**
     * 选择未作答
     */
    selectNoAnswer() {
      this.answerEditDlg.options.forEach(it => {
        it.checked = false
      })
      let ques = this.answerEditDlg.info.ques
      ques.answer = ''
      ques.score = 0
      if (ques.error > 0) {
        ques.changeStatus = 4
        ques.error = -1;
      } else if (ques.error === 0) {
        ques.changeStatus = ques.answerValue !== ques.answer ? 1 : 0;
      }

      this.answerEditDlg.noAnswer = true
    },
    /**
     * 查看答案
     **/
    viewAnswer(ques) {
      this.answerQues = ques
      this.answerDialog = true
    },
    scrollToQues(i) {
      let quesList = this.$refs.studentImage.getElementsByClassName('ques_no_scroll')
      if (i >= quesList.length) {
        i = quesList.length - 1
      }
      quesList[i].scrollIntoView({
        block: 'start',
        behavior: 'smooth'
      });
    },
    /**
     * 获取学生信息
     */
    getStudentInfo() {
      if (!this.studentPaperId) {
        return
      }

      getStudentPaperInfo({
        id: this.studentPaperId
      }).then(res => {
        this.studentInfo = res.data

        this.getQuestions()
      })

    },
    /**
     * 获取 box的相对位置
     */
    getBoxStyle(box) {
      return {
        left: (box[0] - box[2] / 2) * 100 + "%",
        top: (box[1] - box[3] / 2) * 100 + "%",
        width: box[2] * 100 + "%",
        height: box[3] * 100 + "%",
      }
    },
    /**
     * 获取题目详情
     */
    async getQuestions() {
      let workIds = []
      this.fullscreenLoading = true
      if (this.paperInfo.type === 3) {
        let s = await getQueListWithWork({
          schoolId: this.paperInfo.schoolId,
          workId: this.paperInfo.sourceId
        })
        // let ids = [];
        s.data.forEach(item => {
          item.smallQueList.forEach(it => {
            workIds.push(it.questionId)
          })

        })
      } else {
        workIds = this.studentInfo.quesIds
      }

      let r = await chooseQuesSearch({
        qIds: workIds.join(',')
      })

      let quesMap = {}

      r.data.forEach(ques => {
        quesMap[ques.qId] = ques
      })

      let list = []

      workIds.forEach(id => {
        let quesInfo = quesMap[id]
        if (quesInfo) {
          let qsList = []
          let fullScore = 0
          quesInfo.data.qs.forEach(qs=>{

            qs.fullScore = parseInt(qs.score) || 0
            fullScore += qs.fullScore
            qs.ques = {
              score: 0,
              error: 1
            }
            if(qs.type<=0){
              qs.type = quesInfo.data.type
            }
            qs.answer = qs.ans.join(',')
            if (!qs.qId) {
              qs.qId = quesInfo.qId;
            }
            qs.bigQuesId = id
            qs.isChoose = this.inArr(qs.type, [1, 8])
            qsList.push(qs)
          })

          if(quesInfo.data.levelcode !== '302'){
            list.push(...qsList)
          } else {
            let qs = qsList[0]
            qs.qId = quesInfo.qId
            qs.fullScore = fullScore
            list.push(qs)
          }
        }
      })

      await this.getQuesScore(list)
      this.initQuesImages(list)
      this.quesList = list
      this.answerEditDlg.show = false

      if (this.$refs.studentImage) {
        this.$refs.studentImage.scroll(0, 0)
      }
      this.fullscreenLoading = false
    },
    /**
     * 获取题目分数并更新
     */
    async getQuesScore(list) {
      let fullScoreMap = {}
      //获取个册或试卷分数
        if (this.paperInfo.type === 3) {
          // let r = await getQueListWithWork({
          //   schoolId :  this.paperInfo.schoolId,
          //   workId   :  this.paperInfo.sourceId
          // })
          // r.data.forEach(item=>{
          //   item.smallQueList.forEach(ite=>{
          //       fullScoreMap[ite.questionId] = ite.scores
          //   })
          // })
          // list.forEach(it => {
          //   it.fullScore = it.fullScore
          // })
          return
        }

      if (this.paperInfo.type === 1) {
        let r = await getStudentBook({
          bookId: this.paperInfo.sourceId,
          studentId: this.studentId,
        })

        r.data.questionList.forEach(it => {
          fullScoreMap[it.quesId] = it.fullScore
          fullScoreMap[it.hardQuesId] = it.fullScore
          fullScoreMap[it.normalQuesId] = it.fullScore
        })

        r.data.generalQuesList.forEach(it => {
          fullScoreMap[it.quesId] = it.fullScore
          fullScoreMap[it.hardQuesId] = it.fullScore
          fullScoreMap[it.normalQuesId] = it.fullScore
        })

        list.forEach(it => {
          it.fullScore = parseFloat(fullScoreMap[it.qId]) || it.fullScore;
        })
      } else {
        let r = await getViewPaper({id: this.paperInfo.sourceId});

        let ques_list = JSON.parse(r.data.quesInfo);
        ques_list.forEach(it => {
          it.data.forEach(ques => {
            fullScoreMap[ques.id] = ques.score
          })
        })

        list.forEach(it => {
          it.fullScore = it.fullScore || parseFloat(fullScoreMap[it.qId]);
        })
      }
    },
    /**
     * 初始化学生题目
     */
    initQuesImages(questionList) {
      let imageList = []

      let images = this.studentInfo.images
      images.forEach(image => {
        imageList.push({
          url: `${FS_URL}/${image}?x-oss-process=image/resize,w_${img_width}`,
          path: image,
          boxes: []
        })
      })
      let quesSort = 1;
      let score = 0
      //获取题目框信息在题目上显示
      this.studentInfo.quesList.forEach((ques, quesIndex) => {
        let boxes = []
        let isFirst = true

        ques.quesSort = quesSort
        //设置答案和分数用于修改保存比较
        if (ques.answer) {
          ques.answer = ques.answer.split('').sort().join('');
        }
        if (ques.score) {
          ques.score = parseFloat(ques.score);
        } else {
          ques.score = 0
        }
        if (ques.error > 0) {
          ques.score = null
        }


        ques.boxes.forEach((it, index) => {
          let box = it.box.split(' ');
          box = box.map(pos => parseFloat(pos))

          let boxInfo = {
            box: box,
            style: this.getBoxStyle(box),
            ques: ques,
            quesSort: quesSort,
            index: index,
            isFirst: isFirst,
            answerBox: null,
            answerStyle: null
          }

          let answerBox = it.answerBox
          if (answerBox) {
            answerBox = answerBox.split(' ')
            answerBox = answerBox.map(pos => parseFloat(pos))
            boxInfo.answerBox = answerBox
            boxInfo.answerStyle = this.getBoxStyle(answerBox)
          }

          isFirst = false

          boxes.push(boxInfo)
          imageList[it.page].boxes.push(boxInfo)
        })

        //一个题目的框跨页去除部分border
        let len = boxes.length
        if (len > 1) {
          boxes[0].style.borderBottom = 'none'
          boxes[len - 1].style.borderTop = 'none'
          for (let i = 1; i < len - 1; i++) {
            boxes[i].style.borderBottom = 'none'
            boxes[i].style.borderTop = 'none'
          }
        }

        //1：答案改变，2：分数改变，3：分数框改变，4：答案确认为空
        ques.changeStatus = 0;
        if (quesSort <= questionList.length) {
          let quesInfo = questionList[quesSort - 1]
          ques.quesId = quesInfo.qId
          ques.bigQuesId = quesInfo.bigQuesId
          ques.typeId = quesInfo.type
          ques.fullScore = quesInfo.fullScore
          if (ques.score > ques.fullScore) {
            ques.score = 0
          }
          quesInfo.ques = ques;
          this.judgeQuesRight(quesInfo)
        }

        ques.scoreValue = ques.score;
        ques.answerValue = ques.answer
        quesSort++;
        score += ques.score
      })

      this.quesImageList = imageList
      this.quesCountNoMatch = questionList.length !== this.studentInfo.quesList.length
    }
  }
};
</script>

<style lang="scss" scoped>

.student-scan-paper {
  height: 100%;
  width: 100%;

  .student-score-list {
    display: inline-block;
    position: relative;
    width: 320px;
    background: #fff;
    border: solid 1px #EAEAEA;
    height: 100%;
    float: right;

    .right_title {
      border-bottom: solid 1px #EAEAEA;
      line-height: 30px;
      padding: 5px 10px;

      .ques_score_tip {
        border-left: solid 3px #3E9FFF;
        padding-left: 5px;
      }

      .student_no {
        float: right;

        .stu_no_input {
          width: 150px;
        }
      }
    }

    .score-list {
      overflow-y: auto;
      position: relative;
      height: calc(100% - 90px);

      .ques_sort_btn {
        width: 28px;
        height: 28px;
        display: inline-block;
        border-radius: 5px;
        background: #5F9EFF;
        color: white;
        border: solid 1px rgb(220 223 230);

        &.ques-error {
          background: #F78989;
        }

        &.ques-change {
          background: #FFBB19;
        }
      }

      .save-paper-btn {
        width: 90%;
        margin: 20px 5%;
      }
    }

    .ques_score_item {
      display: flex;
      padding: 10px 0;
      border-bottom: solid 1px #efefef;

      .column {
        flex: 2;
        line-height: 28px;
        text-align: center;
      }

      .ques_answer {
        .view_answer {
          font-size: 12px;
        }
      }

      .ques_sort {
        flex: 1;
      }

      .score_input {
        width: 60px;
      }
    }
  }

  .student-images-box {
    display: inline-block;
    width: calc(100% - 320px);
    height: 100%;

    .image-header {
      background: #eaeaea;
      padding: 13px 20px 10px 20px;
      text-align: center;
      font-size: 18px;
      line-height: 28px;

      .text {
        float: left;
        font-size: 16px;
      }

      .title {
        color: #333;
      }

      .edit-btn {
        float: right;
      }
    }
  }

  .student-images {
    width: 100%;
    height: calc(100% - 51px);
    overflow-y: auto;
    border: solid 1px #EAEAEA;

    .ques-image {
      position: relative;

      .ques_box {
        position: absolute;
        border: solid 1px red;

        .ques_no_scroll {
          position: absolute;
          top: -10px;
        }

        .ques_no {
          border: 3px solid red;
          border-radius: 50%;
          position: absolute;
          text-align: center;
          font-weight: 700;
          color: red;
          width: 30px;
          height: 30px;
          font-size: 15px;
          line-height: 25px;
          left: -15px;
          background: white;
          top: -1px;
        }

        .ques_answer_box {
          position: absolute;
          border: solid 1px red;

          .rec_result {
            position: absolute;
            color: red;
            font-size: 20px;
            left: 0;
            top: -23px;
            background: #ffffffaa;
            line-height: 1em;
          }

          .rec_error {
            position: absolute;
            color: red;
            font-size: 20px;
            left: 0;
            bottom: -23px;
            background: #ffffffaa;
            line-height: 1em;

            :before {
              content: '错误：';
            }
          }
        }
      }


      img {
        width: 100%;
        padding: 0;
      }
    }
  }

}

.answer-edit-dlg {
  position: absolute;
  top: 10px;
  left: 10px;

  .answer-option {
    display: inline-block;
    width: 25px;
    height: 25px;
    text-align: center;
    border: solid 1px #eaeaea;
    margin: 0 5px;
    border-radius: 4px;
    line-height: 25px;

    &.active {
      background: #3E9FFF;
      color: white;
    }
  }

  .no-answer-option {
    width: 65px;
  }
}

.btn-full-screen-loading {
  position: absolute;
  top: -100px;
}
</style>

<style lang="scss">
.answer-dialog {


  .answer-body {
    position: relative;

    .answer-html {
      margin-left: 100px;

      img {
        vertical-align: middle;
      }
    }

    .answer-label {
      font-size: 18px;
      position: absolute;
      left: 0;
      top: 0;
      color: #000;
    }
  }

  .answer-body + .answer-body {
    margin-top: 20px;
  }
}

.student-scan-paper-answer-edit-popover {
  .el-popover {
    padding: 5px;
    width: max-content;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 50%);
  }
}
</style>

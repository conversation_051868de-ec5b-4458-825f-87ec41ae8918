// 消息提示框
@import "../config";

// 操作类弹窗
.el-message-box.el-message-box--operate {
  width: vw_pc(500) ;
  height: vw_pc(250);
  background-color: #FFFFFF;
  border-radius: vw_pc(5);

  .el-message-box__header {
    position: relative;
    height: vw_pc(46);
    background-color: #F0F3F5;
    padding: 0;
    padding-top: 0 !important;
    padding-left: vw_pc(20);

    .el-message-box__title {
      display: block;
      font-size: vw_pc(18);
      line-height: vw_pc(46);
      font-family: Microsoft YaHei;
      font-weight: 400;
      text-align: left;
      color: #4E5668;
    }

    .el-message-box__headerbtn {
      position: absolute;
      top: 50%;
      right: vw_pc(20);
      transform: translateY(-50%);
      font-size: vw_pc(25);
      color: #7B7B7B;
      background: transparent;
      border: none;
      cursor: pointer;

      .el-message-box__close {
        color: #7B7B7B;
      }
    }
  }

  .el-message-box__content {
    border-top: vw_pc(1) solid #E2E9ED;
    position: relative;
    height: vw_pc(114);
    padding-top: 0;

    .el-message-box__input {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: vw_pc(400);

      .el-input {
        .el-input__inner {
          font-size: vw_pc(25);
          height: vw_pc(50);
          line-height: vw_pc(50);
        }
      }
    }
  }

  .el-message-box__message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    font-size: vw_pc(20);
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #434959;
    white-space: nowrap;
  }

  .el-message-box__btns {
    margin-top: vw_pc(5);
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row-reverse;
    
    .el-button {
      width: vw_pc(120);
      height: vw_pc(44);
      border: vw_pc(1) solid #CAD0DE;
      border-radius: vw_pc(22);
      font-size: vw_pc(18);
      margin-left: vw_pc(33);
      color: #4E5668;
    }
    .el-button--primary {
      border: 0;
      background: $mainColor;
      margin-left: vw_pc(0);
      color: #FFFFFF;
    }
  }
}

// 反馈类弹窗
.el-message-box.el-message-box--feedback {
  width: vw_pc(440);
  height: vw_pc(220);
  background-color: #FFFFFF;
  border-radius: vw_pc(8);

  .el-message-box__header {
    position: relative;
    height: vw_pc(62);
    padding: 0;
    padding-top: 0 !important;
    padding-left: vw_pc(20);

    .el-message-box__title {
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
      font-size: vw_pc(22);
      font-family: Microsoft YaHei;
      font-weight: 700;
      text-align: center;
      color: #111417;
    }

    .el-message-box__headerbtn {
      position: absolute;
      top: vw_pc(20);
      right: vw_pc(20);
      transform: translateY(-50%);
      font-size: vw_pc(30);
      color: #9FA9BF;
      background: transparent;
      border: none;
      cursor: pointer;

      .el-message-box__close {
        color: #9FA9BF;
      }
    }
  }

  .el-message-box__content {
    position: relative;
    height: vw_pc(60);
    padding-top: 0;

    .el-message-box__input {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: vw_pc(400);

      .el-input {
        .el-input__inner {
          font-size: vw_pc(25);
          height: vw_pc(50);
          line-height: vw_pc(50);
        }
      }
    }
  }

  .el-message-box__message {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    padding-left: vw(35);
    padding-right: vw(35);
    transform: translate(-50%, -50%);
    text-align: center;
    font-size: vw_pc(16);
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #454851;
    white-space: normal;
    
  }

  .el-message-box__btns {
    margin-top: vw_pc(5);
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row-reverse;

    .el-button {
      width: vw_pc(120);
      height: vw_pc(44);
      border: vw_pc(1) solid #CAD0DE;
      border-radius: vw_pc(22);
      font-size: vw_pc(18);
      margin-left: vw_pc(33);
      color: #4E5668;
    }

    .el-button--primary {
      border: 0;
      background: $mainColor;
      margin-left: vw_pc(0);
      color: #FFFFFF;
    }
  }
}

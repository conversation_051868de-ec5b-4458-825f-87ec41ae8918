import * as httpApi from './index';

const XUEBAN_API = process.env.VUE_APP_XUEBAN_API;

const API = {
  GET: function (url, params) {
    return httpApi.GET(url, params, XUEBAN_API);
  },
  POST: function (url, params) {
    return httpApi.POST(url, params, XUEBAN_API);
  },
};
// 重新识别
export const renewScanAPI = params => {
  return API.GET('/scan_image/detect_by_id', params);
};
// 重新识别答卷
export const renewPageAPI = params => {
  return API.GET('/scan_image/detect_page_image', params);
};
/**
 * @name: 获取定位点图片
 * @param paper_no 试卷号
 * @param show_answer 是否显示答案标记
 */
export const getPaperImgMark = params => {
  return API.GET('/scan_image/get_paper_mark', params);
};
/**
 * @name: 刷新试卷信息
 * @param paper_no 试卷号
 * @param refresh_pdf 是否刷新pdf
 * @param refresh_points 是否刷新定位点
 */
export const refreshPaperInfo = params => {
  return API.GET('/scan_image/refresh_paper_info', params);
};
/**
 * @name: 识别区域（考号，客观题）
 * @param image_url 图片地址
 * @param pos 定位信息
 * @param type 识别类型
 */
export const partDetect = params => {
  return API.POST('/detect_card/part_detect', params);
};

/**
 * @name: 检查页码定位是否合理
 * @param image_url 图片地址
 * @param pos 定位信息
 * @param page 页码
 */
export const page_info_check = params => {
  return API.POST('/detect_card/page_info_check', params);
};

/**
 * @name: 获取分数映射
 * @param paper_no 试卷号
 */
export const GetScoreMap = params => {
  return API.GET('/scan_image/get_score_map', params);
};
/**
 * @name: 获取生成的pdf图片
 * @param paper_no 试卷号
 */
export const getPaperImages = params => {
  return API.GET('/scan_image/get_paper_images', params);
};

/**
 * @name: 获取学生笔迹留痕原卷
 * @param examId 考试workid
 * @param studentNo 学生学号
 */
export const getStudentPaperImage = params => {
  return API.GET('/scan_image/get_student_paper_image', params);
}
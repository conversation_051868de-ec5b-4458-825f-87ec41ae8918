<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-03-21 15:52:03
 * @LastEditors: 小圆
-->
<template>
  <div class="book-module">
    <div class="book-module-header">
      <span class="book-module-title">教辅作业</span>
      <el-button type="text" size="small" @click="showAddBook">+添加</el-button>
    </div>

    <div class="book-module-content">
      <div v-if="books.length === 0" class="empty-book">
        <div class="empty-book-title">暂未添加教辅</div>
        <div class="empty-book-action">
          <el-button type="primary" @click="showAddBook">立即添加</el-button>
        </div>
      </div>

      <div v-for="item in books" class="book-item" @click="goReport(item)">
        <div class="book-cover">
          <el-image :src="item.cover" fit="cover" />
        </div>
        <div class="book-info">
          <div class="book-name" :title="item.bookName">{{ item.bookName }}</div>
          <div class="book-meta">
            <span class="meta-item" v-if="item.subjectName">{{ item.subjectName }}</span>
            <span class="meta-item" v-if="item.gradeName">{{ item.gradeName }}</span>
            <span class="meta-item" v-if="item.volumeName">{{ item.volumeName }}</span>
            <span class="meta-item" v-if="item.year">{{ item.year }}</span>
          </div>
          <div class="book-question-count">题目数：{{ item.quesCount }}</div>
          <div class="book-action">
            <el-button type="primary" size="small" @click="goReport(item)">查看报告</el-button>
          </div>
        </div>

        <i title="删除" class="book-delete delect-icon el-icon-error" @click.stop="deleteBook(item)"></i>
      </div>
      <p v-if="isListLoading" class="loading-text">加载中...</p>
    </div>

    <BookAddDialog
      ref="bookAddDialog"
      v-if="addBookDialogVisible"
      @add="refreshBookList"
      @closed="addBookDialogVisible = false"
    ></BookAddDialog>
  </div>
</template>

<script lang="ts">
import BookAddDialog from '@/components/BookAddDialog.vue';
import { addToShelfAPI, getShelfBooksAPI, removeFromShelfAPI } from '@/service/pexam';
import { Component, Ref, Vue } from 'vue-property-decorator';

interface Book {
  editionCode: string;
  bookCode: string;
  year: string;
  isonshelf: boolean;
  quesCount: number;
  bookIsbn: string;
  volumeCode: string;
  subjectId: number;
  cover: string;
  schoolId: string;
  rowNum: number;
  publishername: string;
  id: string;
  subjectName: string;
  phase: string;
  gradeName: string;
  bookAlias: string;
  volumename: string;
  editionName: string;
  ebookId: string;
  bookName: string;
  bookId: number;
  subId: string;
  createTime: Date;
  dateUpdate: Date;
  volumeName: string;
  gradeCode: string;
  volumecode: string;
  subjectCode: string;
}

interface List {
  bookCode: string;
  gradeId: string;
  userId: string;
  subjectId: string;
  bookId: string;
  dateUpdated: Date;
  deleted: number;
  dateCreated: Date;
  schoolId: string;
  id: string;
}

@Component({
  components: {
    BookAddDialog,
  },
})
export default class BookModule extends Vue {
  @Ref('bookAddDialog') bookAddDialog: BookAddDialog;
  // 教辅列表
  books: Book[] = [];
  // 教辅信息列表
  list: List[] = [];
  // 分页器
  pagination = {
    page: 1,
    size: 21,
  };
  // 添加教辅弹窗
  addBookDialogVisible = false;
  // 是否禁用无限滚动
  disabledInfiniteScroll = true;
  // 是否加载中
  isListLoading = false;

  mounted() {
    this.getShelfBooks();
  }

  // 获取我的教辅
  async getShelfBooks() {
    this.isListLoading = true;
    const res = await getShelfBooksAPI({
      userId: this.$sessionSave.get('loginInfo').id,
      // page: this.pagination.page,
      // size: this.pagination.size,
    });
    const books = res.data.books || [];
    const list = res.data.list || [];

    // if (books.length == 0) {
    // this.disabledInfiniteScroll = true;
    // } else {
    // this.disabledInfiniteScroll = false;
    // }

    // if (this.books.length > 0) {
    // this.books = [...this.books, ...books];
    // this.list = [...this.list, ...list];
    // } else {
    this.books = books;
    this.list = list;
    // }
    this.pagination.page++;
    this.isListLoading = false;
  }

  // 刷新列表
  refreshBookList() {
    this.books = [];
    this.list = [];
    this.pagination.page = 1;
    this.getShelfBooks();
  }

  // 删除教辅
  async deleteBook(item: Book) {
    const id = this.list.find(t => t.bookCode === item.bookCode).id;
    const res = await removeFromShelfAPI({
      id: id,
      userId: this.$sessionSave.get('loginInfo').id,
    });
    if (res.code == 1) {
      this.$notify({
        title: '提示',
        message: '删除成功',
        type: 'success',
      });
      this.books = this.books.filter(t => t.bookCode !== item.bookCode);
      this.list = this.list.filter(t => t.bookCode !== item.bookCode);
    }
  }

  // 添加教辅
  showAddBook() {
    this.addBookDialogVisible = true;
    this.$nextTick(() => {
      this.bookAddDialog.addIds = this.books.map(t => t.bookId);
    });
  }

  // 查看报告
  goReport(item: Book) {
    this.$router.push({
      path: '/home/<USER>',
      query: { bookCode: item.bookCode },
    });
  }
}
</script>

<style scoped lang="scss">
.book-module {
  position: relative;
  margin-bottom: 16px;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
}

.book-module-header {
  display: flex;
  justify-content: space-between;
  gap: 16px;
  margin-bottom: 10px;

  .book-module-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;

    &::before {
      content: '';
      width: 6px;
      height: 24px;
      margin-right: 8px;
      background: #409eff;
      border-radius: 3px;
    }
  }
}

.book-module-content {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  height: 200px;
  overflow: auto;
}

.book-item {
  position: relative;
  display: flex;
  width: calc(33.33% - 14px);
  height: 100%;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  border: 2px solid #f5f7fa;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;

  &:hover {
    border: 2px solid lighten(#409eff, 20%);

    .book-delete {
      display: block;
    }
  }

  &.selected {
    border: 2px solid #409eff;
  }
}

.book-cover {
  width: 120px;
  height: 100%;
  overflow: hidden;
  border-radius: 4px;

  :deep(.el-image) {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.book-info {
  flex: 1;
  margin-left: 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.book-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.book-meta {
  .meta-item {
    display: inline-block;
    padding: 2px 8px;
    margin-right: 8px;
    margin-bottom: 8px;
    background-color: #f5f7fa;
    color: #606266;
    border-radius: 4px;
    font-size: 12px;
  }
}

.book-question-count {
  font-size: 14px;
  color: #606266;
}

.book-delete {
  display: none;
  position: absolute;
  right: 0;
  top: 0;
}

.delect-icon {
  font-size: 24px;
  color: #f56c6c;
  cursor: pointer;
}

.book-action {
}

.empty-book {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .empty-book-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .empty-book-action {
    margin-top: 12px;
  }
}

.loading-text {
  position: absolute;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #5e6d82;
  font-size: 14px;
  line-height: 1.5;
}
</style>

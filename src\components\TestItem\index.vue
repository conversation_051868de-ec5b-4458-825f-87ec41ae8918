<template>
  <div class="examReport__item">
    <div class="square" v-if="item.persRecoveryType == 1">
      <span class="square-word">个性化</span>
    </div>
    <div class="exam-name-area">
      <div>
        <div class="exam-name">
          <div class="exam-title">
            {{ item.examName }}
          </div>
          <div class="exam-tag">
            <el-tag size="small" class="tag-item" type="info">{{ item.categoryName }}</el-tag>
            <!-- <el-tag
              size="small"
              effect="plain"
              class="tag-item"
              :type="item.source == 4 ? 'success' : item.source == 3 ? '' : 'warning'"
              >{{ getSource(item.source) }}</el-tag
            > -->
            <el-tag size="small" v-if="item.analysisMode == 1" type="warning">新高考</el-tag>
          </div>
        </div>
        <div class="exam-detail display_flex align-items_center">
          <span class="exam-grade">年级：{{ item.gradeName }}</span>
          <span class="exam-time">考试时间：{{ item.examDateTime.substring(0, 16) }}</span>
        </div>
      </div>
      <div style="display: flex" class="exam-right">
        <el-button v-if="isExamLeader" :disabled="item.isHistory" type="text" @click="handlerCommand('manageStudent_' + index)">师生管理</el-button>
        <el-button type="text" v-if="isExamLeader && (item.source == 3 || item.source == 4 || item.source == 6)"
          :disabled="item.progress < 3" @click="handlerCommand('goMissExamList_' + index)">查看缺考名单</el-button>
        <el-dropdown @command="handlerCommand" class="list-dropdown-menu" style="margin-left: 15px" v-if="isExamLeader">
          <span class="el-dropdown-link">
            <!-- <i slot="reference" class="el-icon-more"></i> -->
            <span>更多</span>
          </span>
          <el-dropdown-menu slot="dropdown" class="dropdown-menu-ul">
            <p v-if="isWebCorrect" class="dropdown-menu-li">
              <span class="dropdown-menu-title">阅卷管理</span>
              <span class="dropdown-menu">
                <el-dropdown-item class="dropdown-menu-item" :command="'setStuAnsArea_' + index">答题位置变更
                </el-dropdown-item>
              </span>
            </p>
            <p class="dropdown-menu-li">
              <span class="dropdown-menu-title">学情管理</span>

              <span class="dropdown-menu" v-if="isPhotoCorrect">
                <el-dropdown-item class="dropdown-menu-item" :command="'goPublishSetting_' + index">成绩发布
                </el-dropdown-item>
                <el-dropdown-item class="dropdown-menu-item" :command="'goScoreEntry_' + index">补&nbsp;&nbsp;录
                </el-dropdown-item>
                <el-dropdown-item class="dropdown-menu-item" :command="'downLoadReport_' + index"
                  :disabled="item.statState != 1">下载报告
                </el-dropdown-item>
              </span>

              <span class="dropdown-menu" v-else>
                <el-dropdown-item class="dropdown-menu-item" :command="'goScoreSetting_' + index"
                  :disabled="!item.dataState >= 1">
                  成绩修改
                </el-dropdown-item>
                <el-dropdown-item class="dropdown-menu-item" :command="'goPublishSetting_' + index">成绩发布
                </el-dropdown-item>
                <el-dropdown-item class="dropdown-menu-item" :command="'addReport_' + index"
                  :disabled="!item.dataState >= 1">新增报告
                </el-dropdown-item>
                <el-dropdown-item class="dropdown-menu-item" :command="'downLoadReport_' + index"
                  :disabled="item.statState != 1">下载报告
                </el-dropdown-item>
                <el-dropdown-item v-if="item.source == 3 || item.source == 4" class="dropdown-menu-item"
                  :command="'goScoreEntry_' + index">补&nbsp;&nbsp;录
                </el-dropdown-item>
                <el-dropdown-item v-if="item.source == 3 || item.source == 4" class="dropdown-menu-item"
                  :command="'goPaperExport_' + index" :disabled="!item.dataState >= 1">导出原卷
                </el-dropdown-item>

                <el-dropdown-item class="dropdown-menu-item" :command="'scoreConfirmDetail_' + index"
                  v-show="item.scoreConfirm == 1">成绩确认详情
                </el-dropdown-item>
                <el-dropdown-item class="dropdown-menu-item"
                  v-show="$sessionSave.get('loginInfo') && item.source != 3 && item.source != 4">
                  <span>
                    <span :underline="false" @click.stop="exportGrade(item)" v-if="item.source != 3">导入全科成绩
                    </span>
                    <span :underline="false" @click="syncData(item)" v-else>同步数据 </span>
                  </span>
                </el-dropdown-item>
              </span>
            </p>
            <p class="dropdown-menu-li">
              <span class="dropdown-menu-title">测评管理</span>
              <span class="dropdown-menu">
                <el-dropdown-item :disabled="item.isHistory" class="dropdown-menu-item" :command="'createReport_' + index">
                  编辑考试
                </el-dropdown-item>
                <template v-if="isExamLeader">
                  <!-- 校管,运营支持删除全部,年级组长支持删除自己创建的 -->
                  <el-dropdown-item class="dropdown-menu-item" :command="'deleteItem_' + index" style="color: #ff4646">
                    <span style="color: red">删除考试</span>
                  </el-dropdown-item>
                </template>
              </span>
            </p>
          </el-dropdown-menu>
        </el-dropdown>

        <template v-else>
          <el-button type="text" :disabled="!(item.dataState >= 1 && getFilterPaperListHasPublish())"
            @click="handlerCommand('goScoreSetting_' + index)">成绩修改</el-button>
          <el-button v-if="item.source == 3 || item.source == 4" type="text" :disabled="!(
            item.dataState >= 1 &&
            getFilterPaperListHasPublish() &&
            getFilterPaperListHasHandOrWeb()
          )
            " @click="handlerCommand('goPaperExport_' + index)">导出原卷</el-button>
        </template>
      </div>
    </div>
    <div class="exam-subject-box">
      <!-- 该场考试学科列表 -->
      <!-- 手动展开详情,没发布成绩和未结束考试时默认展开 -->
      <div class="exam-subject-content">
        <el-row v-for="(subjectItem, sindex) in filterPaperList" :key="subjectItem.subectId" type="flex" justify="start"
          align="middle" class="exam-subject-list">
          <i :class="['subject-icon', subjectItem.processState >= 2 ? 'gc-icon' : 'nor-icon']"></i>
          <span class="subject-name-wrapper">
            <span class="subject-name" :title="subjectItem.subectName">{{
              subjectItem.subectName
            }}</span>
          </span>
          <div>
            <template v-for="(sjitem, index) in subjectItem.list">
              <template v-if="sjitem.source == 3 || sjitem.source == 4 || sjitem.source == 6">
                <!-- 网阅 -->
                <div v-if="sjitem.source == 4 || sjitem.source == 6" style="width: 100%; display: flex">
                  <span class="subject-name-wrapper">
                    <el-tag size="small" effect="plain" class="tag-item" style="margin-left: 5px"
                      :type="sjitem.source == 4 ? 'success' : sjitem.source == 3 ? '' : sjitem.source == 6 ? 'info' : 'warning'">
                      {{ getSource(sjitem) }}
                    </el-tag>
                  </span>
                  <div class="exam-step">
                    <template v-for="subItem in sjitem.progressList">
                      <!-- 未进行答卷设置 -->
                      <template v-if="sjitem.progressState == 0">
                        <div class="step-item" :class="{ issuccess: subItem.progressState }" v-if="subItem.progress == 1"
                          @click="changeStep(subItem.progress, sjitem, item)">
                          <div class="step-icon" :class="{ notstart: subItem.progressState == 0 }">
                            <i class="step-icon-inner right-icon" v-if="subItem.progressState"></i>
                          </div>
                          <div class="step-txt" :class="{
                            canclick:
                              subItem.progress == 1 ||
                              subItem.progress == 3 ||
                              isExamLeader ||
                              isSubjectLeader(sjitem),
                          }">
                            {{ subItem.progressName }}
                          </div>
                        </div>
                      </template>
                      <!-- 已进行答卷设置 -->
                      <template v-else :key="">
                        <template v-if="sjitem.progress <= 6">
                          <div class="step-item" :class="{
                            'issuccess': subItem.progressState || (subItem.progress >= 6 && subItem.progress != 8),
                            'pointer-none': isPhotoCorrect && subItem.progress > 2
                          }" v-if="setShowStep(sjitem, subItem)" @click="changeStep(subItem.progress, sjitem, item)">
                            <div class="step-icon" :class="{
                              notstart:
                                subItem.progressState == 0 &&
                                (subItem.progress < 6 || subItem.progress == 8),
                            }">
                              <i class="step-icon-inner right-icon" v-if="(subItem.progress == 8 && subItem.progressState) ||
                                subItem.progressState ||
                                (subItem.progress >= 6 && subItem.progress != 8)
                                "></i>
                              <i class="step-icon-inner left-icon"
                                v-if="subItem.progressState == 1 && subItem.progress < 6"></i>
                            </div>
                            <div class="step-txt" :class="{
                              canclick:
                                subItem.progress == 1 ||
                                subItem.progress == 3 ||
                                isExamLeader ||
                                isSubjectLeader(sjitem) ||
                                subItem.progress >= 5,
                            }">
                              {{ subItem.progressName }}
                            </div>
                            <div class="step-line"></div>
                          </div>
                        </template>
                      </template>
                    </template>
                  </div>
                  <!-- 开始阅卷后 -->
                  <!-- 结束阅卷后 -->
                  <div class="result-info" v-if="(sjitem.progress == 3 || sjitem.progress == 4) &&
                    sjitem.progressState == 1
                    ">
                    <span v-if="sjitem.source == 6">
                      {{ sjitem.realScanNum }}人已扫描,
                      {{ sjitem.errorNum }}个异常
                    </span>
                    <span @click="changeHandStep(3, sjitem, sindex, item)" v-else>
                      <span class="num-txt">{{ sjitem.realScanNum }}</span>人已扫描,
                      <span class="num-txt" :class="{ hasError: sjitem.errorNum != 0 }">{{
                        sjitem.errorNum
                      }}</span>个异常
                    </span>
                  </div>
                  <div v-if="sjitem.progress == 4 && sjitem.progressState == 1 && sjitem.source != 6">
                    <span class="canlink"
                      @click="changeStep(9, sjitem, item)">查看阅卷进度（{{ sjitem.markingPapersProgress }}）</span>
                  </div>
                  <!-- 结束考试 -->
                  <div v-if="sjitem.progress >= 5">
                    <span v-if="sjitem.progress > 6" class="link-item-one"
                      style="color: #909299; cursor: auto">阅卷已结束</span>
                    <span class="link-item" :class="{
                      canlink: isExamLeader || isSubjectLeader(sjitem),
                    }" v-if="sjitem.progress > 6 && (isExamLeader || isSubjectLeader(sjitem))">
                      <span @click="changeStep(2, sjitem, item)">查看阅卷设置</span>
                    </span>
                    <span class="canlink">
                      <span @click="changeStep(9, sjitem, item)">查看阅卷进度（{{ sjitem.markingPapersProgress }}）</span>
                    </span>
                  </div>
                </div>
                <!-- 手阅 -->
                <div v-if="sjitem.source == 3" style="width: 100%; display: flex">
                  <span class="subject-name-wrapper">
                    <el-tag size="small" effect="plain" class="tag-item" style="margin-left: 5px"
                      :type="sjitem.source == 4 ? 'success' : sjitem.source == 3 ? '' : sjitem.source == 6 ? 'info' : 'warning'">{{ getSource(sjitem) }}</el-tag>
                  </span>
                  <div class="exam-step">
                    <template v-for="subItem in sjitem.progressList">
                      <!-- 未进行答卷设置 -->
                      <template v-if="sjitem.progressState == 0">
                        <div class="step-item" :class="{ issuccess: subItem.progressState }" v-if="subItem.progress == 1"
                          @click="changeHandStep(subItem.progress, sjitem, sindex, item)">
                          <div class="step-icon" :class="{ notstart: subItem.progressState == 0 }">
                            <i class="step-icon-inner right-icon" v-if="subItem.progressState"></i>
                          </div>
                          <div class="step-txt" :class="{
                            canclick:
                              subItem.progress == 1 ||
                              subItem.progress == 3 ||
                              isExamLeader ||
                              isSubjectLeader(sjitem),
                          }">
                            {{ subItem.progressName }}
                          </div>
                        </div>
                      </template>
                      <!-- 已进行答卷设置 -->
                      <template v-else>
                        <div class="step-item" :class="{ issuccess: subItem.progressState }" :key=""
                          v-if="setShowHandleStep(sjitem, subItem)"
                          @click="changeHandStep(subItem.progress, sjitem, sindex, item)">
                          <div class="step-icon" :class="{ notstart: subItem.progressState == 0 }">
                            <i class="step-icon-inner right-icon" v-if="subItem.progressState"></i>
                          </div>
                          <div class="step-txt" :class="{
                            canclick:
                              subItem.progress == 1 ||
                              subItem.progress == 3 ||
                              isExamLeader ||
                              isSubjectLeader(sjitem),
                          }">
                            {{ subItem.progressName }}
                          </div>
                          <div class="step-line"></div>
                        </div>
                      </template>
                    </template>
                  </div>
                  <span class="result-info" v-if="sjitem.progress >= 3">
                    <span @click="changeHandStep(3, sjitem, sindex, item)">
                      <span class="num-txt">{{ sjitem.realScanNum }}</span>人已扫描,
                      <span class="num-txt" :class="{ hasError: sjitem.errorNum != 0 }">{{
                        sjitem.errorNum
                      }}</span>个异常
                    </span>
                  </span>
                  <span v-if="sjitem.aiCorrect > 0 && sjitem.aiCorrectProgress > 0" class="canlink">
                      <span>AI批改进度（{{ sjitem.aiCorrectProgress || '0' }}%）</span>
                    </span>
                </div>
              </template>
              <div v-else>
                <el-tag size="small" effect="plain" class="tag-item" style="margin-left: 5px;margin-right: 10px;"
                  :type="sjitem.source == 4 ? 'success' : sjitem.source == 3 ? '' : sjitem.source == 6 ? 'info' : 'warning'">{{ getSource(sjitem) }}</el-tag>
                <span v-if="sjitem.isUploadWord == 0">
                  <span class="uploadingState click-item" @click="toUploadWord(sjitem, item)">
                    上传试卷
                  </span>
                </span>
                <template v-else>
                  <span class="uploadingState click-item" @click="createPaper(sjitem, item)">
                    {{
                      sjitem.state == 2 && sjitem.isUse
                      ? '查看试卷'
                      : sjitem.state == 2
                        ? '去制卷'
                        : '解析中'
                    }}
                  </span>
                  <el-divider direction="vertical"></el-divider>
                  <span class="uploadAgain click-item" :class="{ banClick: sjitem.processState != 0 }"
                    @click="toUploadWord(sjitem, item, 'again')">重新上传</span>
                  <div class="split-line"></div>
                  <span class="sendProcess click-item" @click="processHandle(sjitem, item)" :class="{
                    allowClick:
                      (sjitem.isExamUse == 1 &&
                        sjitem.processState == 0 &&
                        sjitem.state == 2 &&
                        sjitem.isUse) ||
                      (sjitem.isExamUse == 0 &&
                        sjitem.processState == 0 &&
                        sjitem.isUse &&
                        sjitem.isTeachingSchedule &&
                        (sjitem.checkTeacherId != '' || !sjitem.needAudit)),
                  }">
                    <span v-if="(sjitem.isExamUse == 1 && sjitem.workingState == 2) ||
                      sjitem.processState > 1
                      ">加工完成</span>
                    <span v-else-if="sjitem.processState == 0" class="send">发送加工</span>
                    <span v-else-if="sjitem.processState == 1">加工中</span>
                    <span v-else></span>
                  </span>
                </template>
                <div class="split-line"></div>
                <span class="uploadingState click-item" @click="toExport(sjitem, item)">{{
                  sjitem.importScoreState == 0 ? '导入成绩' : '重新导入'
                }}</span>
                <div class="split-line"></div>
                <!-- 重新匹配逻辑调整为：上传试卷且已完成制题，且导入成绩 -->
                <span class="sendProcess click-item" :class="{
                    allowClick:
                      (sjitem.isUploadWord != 0 && sjitem.isUse && sjitem.state == 2)
                      && sjitem.importScoreState == 1,
                  }" @click="toMatchQuesNum(sjitem, item)"><span class="send">
                    {{ sjitem.bindQueState == 1 ? '重新匹配' : '题号匹配' }}
                  </span></span>
              </div>
            </template>
          </div>
        </el-row>
        <!-- 结束考试,开启考试 -->
        <div class="exam-state-btn" v-if="isAdmin && item.source == 4">
          <span v-if="item.progressState == 1 && item.progress == 7">
            <span class="red">考试已结束</span>
          </span>
          <!-- <span @click="finshExam(7, 1, item)" v-else style="cursor: pointer">
            <i class="close-icon"></i>
            <span class="red">结束考试</span>
          </span> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import { updateProgress, getMergeStatus } from '@/service/pexam';
import { startCorrect, saveBankJSONAPI, getSchoolRoleList } from '@/service/api';
import { getBankInfoByPaperNo } from '@/service/testbank';
import UserRole from '@/utils/UserRole';
import { Loading } from '@iclass/element-ui';
import { IAB_CARD_SHEEFT_TYPE, ICARD_STATE } from '@/typings/card';

export default {
  name: 'test-item',
  components: {},
  props: ['item', 'index'],
  data() {
    return {
      //是否展开详情
      isOpenDesc: false,
      processState: 6,
      examState: 1,
      mergeTimes: 0,
      mergeLoading: null,
    };
  },
  computed: {
    ...mapGetters([]),
    //是否为校管、运营、年级组长、备课组长
    isAdminorOperate() {
      return (
        this.$sessionSave.get('loginInfo') &&
        (this.$sessionSave.get('loginInfo').user_type == 5 ||
          this.$sessionSave.get('loginInfo').admin_type == 2 ||
          this.$sessionSave.get('loginInfo').account_type == 3 ||
          this.$sessionSave.get('loginInfo').account_type == 2)
      );
    },
    //是否为校管、运营
    isAdmin() {
      return (
        this.$sessionSave.get('loginInfo') &&
        (this.$sessionSave.get('loginInfo').user_type == 5 ||
          this.$sessionSave.get('loginInfo').admin_type == 2)
      );
    },
    isWebCorrect() {
      return this.item.paperList.find(paper => {
        return paper.source == 4;
      });
    },
    // 是否拍改
    isPhotoCorrect() {
      return this.item.paperList.some(paper => {
        return paper.source == 6;
      });
    },


    // 是否考试管理员
    isExamLeader() {
      let isOperation = UserRole.isOperation; // 运营
      let isSchoolAdmin = UserRole.isSchoolLeader; // 校管
      let isExamLeader = this.item.leaderIds?.includes(this.$sessionSave.get('loginInfo').id); // 考试管理员
      let isSameCreateUser = this.isCreate(this.item.createUserId); // 创建者
      return isOperation || isSchoolAdmin || isExamLeader || isSameCreateUser;
    },

    // 筛选后的试卷列表
    filterPaperList() {
      let paperList = this.item.paperList;
      // 使用 reduce 进行分组，并生成符合目标结构的数据
      const grouped = paperList.reduce((acc, item) => {
        const key = item.subectId;

        // 如果该name的分组还没有创建，先初始化
        if (!acc[key]) {
          acc[key] = {
            ...item,
            list: []
          };
        }

        // 把当前项加入到该分组的 list 中
        acc[key].list.push(item);

        return acc;
      }, {});
      // 转换成数组并输出结果
      paperList = Object.values(grouped);
      if (this.isExamLeader) return paperList;
      return paperList.filter(t => {
        return this.isSubjectLeader(t);
      });
    },
  },
  watch: {
    item: {
      handler(newVal, oldVal) {
        //TODO 考试状态
        if (newVal.dataState < 1 && this.examState == 1) {
          this.isOpenDesc = true;
        } else {
          this.isOpenDesc = false;
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() { },
  mounted() { },
  methods: {
    // 学科管理员的学科试卷列表是否有发布成绩
    getFilterPaperListHasPublish() {
      return this.filterPaperList.some(item => item.statType == 1);
    },

    // 学科管理员的学科试卷列表是否有手阅或网阅试卷
    getFilterPaperListHasHandOrWeb() {
      return this.filterPaperList.some(item => item.source == 3 || item.source == 4);
    },

    // 是否学科管理员
    isSubjectLeader(subjectItem) {
      let leaderIds = subjectItem.leaderIds || '';
      return leaderIds.includes(this.$sessionSave.get('loginInfo').id);
    },

    /**
     * @name:发布成绩
     */
    async openPublishDialog(sindex, subjectItem) {
      await this.mergeData(subjectItem.mainWorkId || subjectItem.workId);
      this.$emit('open-publish', this.item, sindex, subjectItem);
    },
    /**
     * @name:查看报告
     */
    lookReport(item) {
      this.$emit('look-report', item);
    },
    /**
     * @name:下载报告
     */
    downLoadReport(item) {
      this.$emit('download-report', item);
    },
    /**
     * @name:发布成绩
     */
    handlerCommand(command) {
      if (command == 'finshExam') return;
      this.$emit('handler-command', command);
    },
    /**
     * @name:是否为当前登录用户创建的考试
     */
    isCreate(createUserId) {
      return createUserId == this.$sessionSave.get('loginInfo').id;
    },
    /**
     * @name:设置当前需要展示的进度
     * @param {*} subjectItem  学科
     * @param {*} subItem 步骤
     */
    setShowStep(subjectItem, subItem) {
      if (subjectItem.source == 6) return true;

      //开始阅卷之前的步骤
      if (subItem.progress <= subjectItem.progress && subItem.progress < 4) {
        return true;
      }
      // 阅卷设置和扫描答卷
      if (subjectItem.progressState >= 1 && (subItem.progress == 2 || subItem.progress == 3)) {
        return true;
      }
      // 开始阅卷 已完成阅卷设置且已扫描试卷和处理完异常再显示开始阅卷
      if (
        subjectItem.progress == 3 &&
        subjectItem.progressState == 1 &&
        subjectItem.progressList[1].progressState == 1 &&
        // subjectItem.errorNum == 0 &&
        subItem.progress == 4
      ) {
        return true;
      }
      //发布成绩按钮在结束阅卷后出现
      if (subItem.progress == 8 && subjectItem.progress == 5) {
        return true;
      }
      // 开始阅卷之后的步骤
      if (subjectItem.progress >= 4 && subjectItem.progressState == 1) {
        return subItem.progress == subjectItem.progress + 1;
      }
    },
    /**
     * @name:设置当前需要展示的手阅进度
     * @param {*} subjectItem  学科
     * @param {*} subItem 步骤
     */
    setShowHandleStep(subjectItem, subItem) {
      //答卷设置后展示阅卷设置，扫描答卷
      if (subjectItem.progress >= 1 && subItem.progress < 4) {
        return true;
      }
      // 扫描答卷后展示发布成绩
      if (subjectItem.progress >= 3 && subItem.progress == 4) {
        return true;
      }
    },
    /**
     * @name：页面跳转
     */
    async changeStep(step, subjectItem, item) {
      //不是答卷设置、扫描答卷、阅卷进度或者创建考试者，运营、校管 则不能操作
      if (
        step != 1 &&
        step != 3 &&
        step != 9 &&
        !this.isExamLeader &&
        !this.isSubjectLeader(subjectItem)
      ) {
        return;
      }
      if (step == 7) return;
      let name = '',
        stepVal = step,
        state = 0,
        examId = this.item.examId,
        paperNo = this.item.paperNo || subjectItem.paperNo,
        workId = subjectItem.personBookId;
      switch (step) {
        case 1:
          name = 'answerSetting';
          paperNo = subjectItem.mainPaperNo || subjectItem.paperNo;
          workId = subjectItem.mainWorkId || subjectItem.personBookId;
          break;
        case 2:
          if (subjectItem.markSetType == 0) {
            name = 'markPaperSettingold';
          } else {
            name = 'markPaperSetting';
          }
          break;
        case 3:
          name = 'scanTask';
          examId = subjectItem.mainWorkId || subjectItem.personBookId || this.item.examId;
          break;
        case 4:
          name = '';
          stepVal = 5;
          state = 0;
          break;
        //结束阅卷
        case 5:
          name = '';
          stepVal = 5;
          state = 1;
          break;
        //重新阅卷
        case 6:
          name = '';
          stepVal = 6;
          state = 0;
          break;
        //发布成绩
        case 8:
          name = '';
          stepVal = 8;
          state = 1;
          break;
        case 9:
          name = 'markPaperSpeed';
          break;
        default:
          name = 'answerSetting';
          break;
      }
      if (name) {
        this.$router.push({
          name: name,
          query: {
            examId: examId,
            examName: this.item.examName,
            paperNo: paperNo,
            subjectId: subjectItem.subectId,
            subjectName: subjectItem.subectName,
            subjectRealName: subjectItem.subjectRealName,
            gradeCode: this.item.gradeCode,
            personBookId: subjectItem.personBookId,
            testBankId: subjectItem.testBankId,
            source: subjectItem.source,
            workId: workId,
            isEdit: 2,
            progress: subjectItem.progress,
            progressState: subjectItem.progressState,
            createUserId: this.item.createUserId,
            cardType: subjectItem.cardType,
            schoolId: this.$sessionSave.get('schoolInfo').id,
            examDateTime: this.item.examDateTime,
            year: this.item.year,
            originCardType: subjectItem.originCardType,
            aiCorrect: subjectItem.aiCorrect,
          },
        });
      } else {
        //网阅开始、结束、重新阅卷
        if (subjectItem.source == 4 && (step == 4 || step == 5 || step == 6 || step == 8)) {
          //发布成绩
          if (step == 8) {
            await this.mergeData(subjectItem.personBookId);
          }
          let tips =
            step == 4 ? '开始阅卷' : step == 5 ? '结束阅卷' : step == 6 ? '继续阅卷' : '发布成绩';
          this.$confirm(`确定${tips}？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(async () => {
              //获取备课组长userid
              // const params = {
              //   schoolId: this.$sessionSave.get('schoolInfo').id,
              //   phase: '',
              //   year: this.item.year,
              //   systemCode: '',
              //   subjectId: subjectItem.subectId,
              //   roleType: 2,
              // };
              // const result = await getSchoolRoleList(params);
              // let userId = '';
              // if (result.code == 1) {
              //   if (result.data.length > 0) {
              //     userId = result.data[0].userId;
              //   } else {
              //     this.$message({
              //       message: '请先设置备课组长！',
              //       type: 'error',
              //       duration: 1500,
              //     });
              //     return;
              //   }
              // } else {
              //   this.$message({
              //     message: '请求失败！',
              //     type: 'error',
              //     duration: 1000,
              //   });
              //   return;
              // }
              await this.changeProcess(
                stepVal,
                state,
                subjectItem.personBookId,
                subjectItem.personBookId
              );
              //开始阅卷
              if (step == 4) {
                await this.clickStartCorrect(subjectItem, userId);
              }
            })
            .catch(err => {
              console.log('取消结束阅卷');
            });
        } else {
          this.changeProcess(stepVal, state, subjectItem.personBookId, subjectItem.personBookId);
        }
      }
    },
    /**
     * @name:根据来源类别显示来源名称
     */
    getSource(item) {
      let sourceName = '';
      switch (item.source) {
        case 3:
          sourceName = '手阅';
          break;
        case 4:
          sourceName = '网阅';
          break;
        case 6:
          sourceName = '拍改';
          break;
        default:
          sourceName = '补录';
          break;
      }
      let abTag = '';
      if (item.relateCardType == ICARD_STATE.abPaper || item.relateCardType == ICARD_STATE.abPaperTwo) {
        if (item.abCardSheetType == IAB_CARD_SHEEFT_TYPE.aCard) {
          abTag = '(A卷)'
        } else if (item.abCardSheetType == IAB_CARD_SHEEFT_TYPE.bCard) {
          abTag = '(B卷)'
        }
      }
      return sourceName + abTag;
    },
    /**
     * @name:获取合并状态
     */
    async mergeData(pbookId) {
      this.mergeLoading = Loading.service({
        lock: true,
        text: '正在合并数据，请稍候',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      this.mergeTimes = 0;
      return this.checkingMergeStatus(pbookId);
    },
    checkingMergeStatus(pbookId) {
      return new Promise((resolve, reject) => {
        let params = {
          examId: pbookId,
          times: this.mergeTimes,
        };
        getMergeStatus(params)
          .then(res => {
            if (res.data.status === 0) {
              this.mergeLoading.close();
              resolve();
            } else {
              setTimeout(() => {
                this.mergeTimes++;
                this.checkingMergeStatus(pbookId).then(resolve).catch(reject);
              }, 1000);
            }
          })
          .catch(err => {
            this.mergeLoading.close();
            reject();
          });
      });
    },
    /**
     * @name:上传试卷
     */
    toUploadWord(ite, item, isUploadAgain) {
      this.$emit('to-upload-word', ite, item, isUploadAgain);
    },
    /**
     * @name:去制卷
     */
    createPaper(ite, item) {
      this.$emit('create-paper', ite, item);
    },
    /**
     * @name:点击发送加工或者显示加工进度
     */
    processHandle(ite, item) {
      this.$emit('process-handle', ite, item);
    },
    toExport(ite, item) {
      this.$emit('to-export', ite, item);
    },
    //跳转到题号匹配
    toMatchQuesNum(ite, item) {
      this.$emit('to-match-ques-num', ite, item);
    },
    // 导入成绩弹框
    exportGrade(item) {
      this.$emit('export-grade', item);
    },
    //同步数据
    syncData(item) {
      window.open(`/bigdata/importExcel?examId=${item.examId}`, '_blank');
    },
    /**
     * @name:结束考试
     */
    finshExam(progress, progressState, item) {
      //是否开始阅卷并且阅卷进度不为100%
      const hasMatchingItem = item.paperList.some(ite => {
        return ite.progress == 4 && ite.markingPapersProgress !== '100.0%';
      });
      let tips = hasMatchingItem ? '仍有考试未完成阅卷,' : '';
      this.$confirm(`${tips}结束考试后将无法继续阅卷，确定结束吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.changeProcess(progress, progressState);
        })
        .catch(err => {
          console.log(err);
        });
    },
    /**
     * @name：改变当前的进度
     */
    async changeProcess(progress, progressState, personalBookId) {
      let params = {
        examId: this.item.examId,
        progress: progress,
        personalBookId: personalBookId,
        progressState: progressState,
        schoolId: this.$sessionSave.get('schoolInfo').id,
      };
      let result = await updateProgress(params);
      if (result && result.code) {
        this.$message({
          message: '设置成功！',
          type: 'success',
          duration: 500,
        });
        //更新列表数据
        this.$emit('reload-data');
      }
    },
    changeHandStep(progress, subjectItem, sindex, item) {
      //答卷设置
      if (progress <= 2) {
        this.changeStep(progress, subjectItem, item);
      }
      //扫描答卷
      if (progress == 3) {
        this.scanExam(sindex, subjectItem);
      }
      //发布成绩
      if (progress == 4) {
        this.openPublishDialog(sindex, subjectItem);
      }
    },
    /**
     * @name：扫描答卷
     */
    scanExam(sIndex, subjectItem) {
      this.$emit('scan-exam', this.item, sIndex, subjectItem);
    },
    /**
     * 开启阅卷
     */
    async clickStartCorrect(subjectItem, userId) {
      startCorrect({
        examId: this.item.examId,
        schoolId: this.$sessionSave.get('schoolInfo').id,
        workId: subjectItem.personBookId,
      });
      // await this.saveWorkJSON(subjectItem, userId);
    },
    /**
     * @name:结束阅卷
     */
    async stopCorrect(subjectItem, userId) {
      await this.saveWorkJSON(subjectItem, userId);
    },
    /**
     * @name: 保存作业
     * */
    async saveWorkJSON(subjectItem, userId) {
      let paperNum = 0;
      let res = await getBankInfoByPaperNo({ paperNo: subjectItem.paperNo });
      if (res && res.code == 1) {
        paperNum = res.data.paperNum;
      }
      let workJson = this.buildSendOptoions(subjectItem);
      await saveBankJSONAPI({
        darftId: '',
        state: 0, //0 定时 1:已发送 2：草稿箱[默认] 来源
        userId: userId, //备课组长userId
        creatType: 2, // creat_type表示的是创建什么样的作业,是普通作业还是备课组长作业,云考试全为1
        sendSource: 2, // PC
        correctType: 0, //批改类型  0:老师自批改，1：全班互批，2：学生自批
        permissionType: 0, //学生作业互看状态（0:允许互看优秀作答，1:允许互看 2:不允许互看）
        autoSubmit: 0,
        firstType: 3,
        subjectId: subjectItem.subectId,
        workJson: workJson,
        sourceId: subjectItem.personBookId,
        paperNo: subjectItem.paperNo,
        paperNum: paperNum,
        relationId: this.item.examId,
      })
        .then(res => {
          // startCorrect({
          //   examId: this.item.examId,
          //   schoolId: this.$sessionSave.get('schoolInfo').id,
          //   workId: subjectItem.personBookId,
          // });
        })
        .catch(err => {
        });
    },
    /**
     * @name: 构建workJson数据
     * @return 发送数据
     */
    buildSendOptoions(subjectItem) {
      let examTime = this.item.examDateTime;
      let date = new Date(examTime);
      date.setDate(date.getDate() + 1);
      let dateStr = this.$formatDate(date);
      let quesData = {
        darftid: '',
        cardid: '',
        title: this.item.examName,
        classes: [],
        objectList: [],
        classnames: [],
        groups: [],
        stuList: [],
        uid: this.$sessionSave.get('loginInfo').id,
        queSource: 0,
        sendType: 1,
        answerType: 0,
        startTime: `${examTime} 08:00`,
        endtime: `${dateStr} 08:00`,
        answerTime: '',
        hwTypeCode: '104',
        xkwPaperId: '',
        openId: '',
        remarks: '',
        docids: '',
        anwserdocids: '',
        year: '',
        subjectId: subjectItem.subectId,
        markingType: this.item.source == '3' ? 1 : 2,
        quesdatas: [],
      };
      return JSON.stringify(quesData);
    },
    /**
     * @name:更新考生信息
     */
    refreshStuInfo(item) {
      this.$emit('update-exam-info', item);
    },
    /**
     * @name:展开/收起详情
     */
    openDesc() {
      this.isOpenDesc = !this.isOpenDesc;
    },
  },
};
</script>

<style lang="scss" scoped>
.examReport__item {
  width: 100%;
  min-height: 120px;
  box-sizing: border-box;
  background: #fff;
  padding: 20px;
  border-radius: 6px;
  margin-bottom: 14px;
  color: #222;
  font-size: 14px;
  position: relative;
  border: 1px solid #e4e7eb;
  box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.15);
  border-radius: 6px;

  .square {
    width: 70px;
    border-top: 24px solid #409eff;
    z-index: 100;
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 6px 0 0;

    .square-word {
      position: absolute;
      left: 8px;
      top: -23px;
      color: #ffffff;
      font-size: 15px;
    }
  }

  .exam-name-area {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .exam-name {
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
      color: #161e26;
      line-height: 21px;
      margin-right: 100px;
      width: 90%;
      justify-content: flex-start;

      .exam-title {
        margin-right: 15px;
        // width: calc(100% - 125px);
      }

      .exam-tag {
        display: flex;

        .tag-item {
          margin-right: 10px;
        }

        .el-tag {
          background-color: #fff;
        }
      }

      .exam-category {
        margin-right: 15px;
        height: 24px;
        line-height: 24px;
        background: #f0f2f5;
        border-radius: 12px;
        color: #606266;
        text-align: center;
        font-weight: 400;
        font-size: 14px;
        padding: 0 11px;
        width: 120px;
      }
    }

    .exam-detail {
      margin-top: 15px;

      .line-gap {
        width: 32px;
        position: relative;

        &:before {
          content: '';
          position: absolute;
          left: 50%;
          margin-left: -1px;
          top: 50%;
          margin-top: -7px;
          display: inline-block;
          width: 2px;
          height: 14px;
          background-color: #97a0a8;
        }
      }

      .exam-grade {
        margin-right: 20px;
      }

      >span {
        display: inline-block;
        // font-size: 16px;
        color: #3f4a54;
      }
    }

    .exam-right {
      .el-dropdown-link {
        // color: #9ba1a8 !important;
      }

      .el-dropdown {
        // margin-top: 6px;
      }

      .el-button+.el-button {
        margin-left: 16px;
      }

      .refresh-stu-info {
        margin-right: 10px;
      }
    }
  }

  .scannet {
    width: 80%;
  }

  .scanhand {
    width: 70%;
  }

  .upload-btn {
    display: inline-block;
    width: 130px;
    height: 42px;
    font-size: 16px;
    margin-left: 25px;
    cursor: pointer;

    &.is-disabled {
      border-color: #80c6f5;
    }
  }

  .line {
    display: inline-block;
    height: 42px;
    font-size: 16px;
    color: #000000;
    pointer-events: none;
    cursor: pointer;
  }

  .score-btn {
    display: inline-block;
    width: 80px;
    height: 42px;
    font-size: 16px;
    margin-left: 25px;
    cursor: pointer;

    &.is-disabled {
      border-color: #b8b8b8;
      pointer-events: none;
      cursor: pointer;
    }
  }

  .report-btn,
  .download-btn {
    width: 80px;
    height: 32px;
    border-radius: 3px;
    color: #ffffff;
    cursor: pointer;

    &.is-disabled {
      background: rgba(190, 198, 209, 0.7);
      pointer-events: none;
      // color: #b8b8b8;
      // pointer-events: none;
      // cursor: pointer;
    }
  }

  .light-btn {
    background: #409eff;
  }

  .exam-step {
    display: flex;
    white-space: nowrap;

    .step-item {
      position: relative;
      flex-shrink: 1;
      display: inline-block;
      width: 153px;
      color: #909299;
      cursor: pointer;

      &.pointer-none{
        opacity:.5;
      }

      .step-icon {
        position: relative;
        z-index: 1;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 17px;
        height: 17px;
        font-size: 14px;
        box-sizing: border-box;
        background: #fff;
        transition: 0.15s ease-out;
        border-radius: 50%;
        border: 1px solid #e5e5e5;
        border-color: inherit;
        margin-right: 7px;

        .step-icon-inner {
          -webkit-user-select: none;
          -moz-user-select: none;
          user-select: none;
          text-align: center;
          font-weight: 700;
          line-height: 1;
          color: inherit;
        }

        .right-icon {
          width: 10px;
          height: 7px;
          display: block;
          background: url(../../assets/exam/right-icon.png);
        }
      }

      .step-line {
        position: absolute;
        top: 19px;
        left: 90px;
        right: 9px;
        border: 1px dashed #c0c4cc;
      }

      .notstart {
        vertical-align: sub;
      }

      .step-txt {
        position: relative;
        z-index: 1;
        display: inline-flex;
      }

      &:last-child {
        .step-line {
          display: none;
        }
      }
    }

    .issuccess {
      // color: #303233;
      border-color: #67c23a;
    }

    .canclick {
      color: #303233;
    }
  }

  .result-info {
    margin-right: 20px;
    color: #909299;
    cursor: pointer;
  }

  .num-txt {
    color: #409eff;
    margin-right: 4px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;

    &.hasError {
      color: red;
    }
  }

  .link-item {
    display: inline-block;
    color: #909299;
    cursor: pointer;
    font-weight: 400;

    >span {
      margin-right: 20px;
    }
  }

  .link-item-one {
    display: inline-block;
    color: #909299;
    cursor: pointer;
    font-weight: 400;
    margin-right: 20px;
  }

  .canlink {
    color: #409eff;
    cursor: pointer;
  }

  .click-item {
    cursor: pointer;
  }

  .exam-subject-box {
    background: #fafbfc;
    // border: 1px solid #e5e5e5;
    // opacity: 0.5;
    border-radius: 4px;
    margin-top: 14px;

    .exam-subject-header {
      height: 43px;
      line-height: 43px;
      padding: 0 23px;
      color: #606266;
    }

    .exam-subject-content {
      position: relative;
      padding: 10px 0;
      // border-top: 1px solid #e5e5e5;
    }

    .exam-subject-list {
      padding: 0 23px;
      min-height: 40px;
      line-height: 40px;
      margin-bottom: 10px;

      .subject-icon {
        display: block;
        width: 12px;
        height: 15px;
        margin-right: 10px;
      }

      .re-icon {
        width: 20px;
        height: 20px;
        line-height: 20px;

        color: #fff;
        text-align: center;
        font-size: 12px;

        border-radius: 10px;
        background: #d9001b;
      }

      .nor-icon {
        background: url(../../assets/exam/subject-icon.png);
      }

      .gc-icon {
        background: url(../../assets/exam/gc-icon.png);
      }

      .subject-name-wrapper {
        display: flex;
        align-items: center;
        margin-right: 10px;
      }

      .subject-name {
        color: #141414;
        max-width: 90px;
        display: inline-block;
        font-weight: bold;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .subject-tag {
        margin-left: 3px;
        color: #e6a23c;
      }
    }

    .split-line {
      width: 54px;
      display: inline-block;
      vertical-align: middle;
      margin: 0 9px;
      border: 1px dashed #c0c4cc;
    }

    .uploadingState {
      display: inline-block;
      // width: 70px;
      // color: #409eff;
      cursor: pointer;
      font-weight: 400;
    }

    .sendProcess {
      // margin-left: 30px;
      color: #b8b8b8;
      pointer-events: none;
      cursor: pointer;
      font-weight: 400;

      &.allowClick {
        // margin-left: 30px;
        // color: #409eff;
        pointer-events: auto;
        cursor: pointer;

        .send {
          color: rgb(34, 34, 34);
        }
      }
    }

    .uploadAgain {
      color: #409eff;
      pointer-events: auto;
      cursor: pointer;
      font-weight: 400;
    }

    .uploadAgain.banClick {
      color: #b8b8b8;
      pointer-events: none;
      cursor: pointer;
    }

    .no-allow-click {
      color: #b8b8b8;
      pointer-events: none;
      cursor: pointer;
    }
  }

  .sub-item {
    display: inline-block;
    // width: 60px;
  }

  .exam-state-btn {
    position: absolute;
    right: 24px;
    bottom: 24px;
    // cursor: pointer;

    i {
      vertical-align: middle;
      margin-right: 5px;
      display: inline-block;
    }

    .close-icon {
      width: 14px;
      height: 14px;

      background: url(../../assets/exam/close-icon.png) 100% 100%;
    }

    .restart-icon {
      width: 16px;
      height: 14px;
      display: inline-block;
      background: url(../../assets/exam/restart-icon.png) 100% 100%;
    }

    .red {
      color: #f14d4d;
    }
  }
}

.dropdown-menu-ul {
  width: 260px;
  padding: 10px;

  .dropdown-menu-li {
    margin-bottom: 10px;
  }

  .dropdown-menu-title {
    font-weight: 800;
  }
}

.dropdown-menu {
  display: flex;
  flex-wrap: wrap;
}

.dropdown-menu-item {
  min-width: 100px;
  text-align: center;
  border-radius: 4px;
  border: 1px solid #bbb;
  margin-top: 10px;
  margin-left: 10px;
  line-height: 26px;
}
</style>
<style lang="scss">
.exam-name-area {
  .el-button {
    padding: unset !important;
  }
}

.el-divider--vertical {
  position: relative !important;
  height: 12px !important;
  top: 0 !important;
  margin: 0 10px !important;
}
</style>

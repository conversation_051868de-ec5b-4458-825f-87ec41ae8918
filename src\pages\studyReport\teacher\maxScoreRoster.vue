<!--
 * @Description: 最高分名册
 * @Author: 小圆
 * @Date: 2024-04-02 09:02:53
 * @LastEditors: 小圆
-->

<template>
  <div>
    <div v-loading="tableLoading">
      <div v-if="tableColumns.length > 0" class="score-table-wrapper" v-drag>
        <base-table
          v-for="(item, index) in tableColumns"
          class="score-table"
          :key="index"
          :data="tableData[index]"
          :column="[tableColumns[index]]"
          :span-method="
            ({ row, column, rowIndex, columnIndex }) =>
              handleSpanMethod({ row, column, rowIndex, columnIndex }, index)
          "
          v-bind="getTableAttr()"
        ></base-table>
      </div>

      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins, Prop } from 'vue-property-decorator';
import TableCommon, { getDefaultTableAttr } from '../components/TableCommon';
import FilterModule from '../plugins/FilterModule';

@Component({
  components: {},
})
export default class MaxScoreRoster extends Mixins(TableCommon) {
  @Prop({ default: 'teacher' }) apiModule;
  //   @Prop({ default: 'singleSubject' }) apiName;

  getTableAttr() {
    const attr = getDefaultTableAttr();
    delete attr.maxHeight;
    return attr;
  }

  // 获取数据
  async getTableData() {
    this.tableColumns = [];
    this.tableData = [];
    this.tableLoading = true;
    await this.$nextTick();
    try {
      const res = await FilterModule.getReportTableData(this.apiName || this.$route.name);
      if (res.code != 1) {
        return this.$message.error(res.msg);
      }
      const data = res.data;
      data.table = this.setColumnProp(data.table);
      this.tableColumns = data.table as IColumn[];

      const tableData = [];

      this.tableColumns.forEach(item => {
        let arr = [];
        data.result.forEach(data => {
          if (item.title === '三科总分') {
            if (data.subjectName === '三科总分') arr.push(data);
          } else if (item.title == '总分') {
            if (data.subjectName === '总分') arr.push(data);
          } else if (data.subjectName && item.title == data.subjectName) {
            arr.push(data);
          }
        });
        tableData.push(arr);
      });
      this.tableData = tableData;
    } catch (error) {
      console.error(error);
      this.tableColumns = [];
      this.tableData = [];
    } finally {
      this.tableLoading = false;
    }
  }

  /**
   * 合并单元格句柄方法
   */
  handleSpanMethod(
    {
      row, // 行
      column, // 列
      rowIndex, // 行索引
      columnIndex, // 列索引
    },
    tableIndex // 表格索引
  ) {
    const property = column.property;
    if (property.includes('className')) {
      const spanArr = this.formatRowspanAndColspan(this.tableData[tableIndex], property);
      return {
        rowspan: spanArr[rowIndex],
        colspan: 1,
      };
    }
  }

  render(h) {
    const table = this.renderTable(h);
    return table;
  }
}
</script>

<style scoped lang="scss">
.score-table-wrapper {
  display: flex;
  overflow: auto;

  .score-table {
    width: 100%;
    min-width: 450px;
  }
}
</style>

<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-05-21 15:55:51
 * @LastEditors: 小圆
-->
<template>
  <el-dialog
    title="修改记录"
    :visible.sync="visible"
    width="50%"
    @close="$emit('close')"
    @closed="$emit('closed')"
  >
    <div class="ques">
      <span class="ques-title">当前题目： {{ ques.sortTitle }}</span>
      <span class="ques-score">得分：{{ ques.score }}</span
      >分
    </div>
    <div class="headline">阅卷记录</div>
    <el-table
      :data="ques.markingRecords"
      style="width: 100%; border: 1px solid #e4e8eb"
      :header-cell-style="{ color: '#3F4A54', backgroundColor: '#f5f7fa' }"
    >
      <el-table-column prop="teaName" label="教师姓名" align="center"> </el-table-column>
      <el-table-column prop="score" label="给分" align="center"> </el-table-column>
      <el-table-column prop="typeName" label="类型" align="center"> </el-table-column>
    </el-table>

    <div class="headline">修改记录</div>
    <el-table
      :data="ques.editRecords"
      style="width: 100%; border: 1px solid #e4e8eb"
      :header-cell-style="{ color: '#3F4A54', backgroundColor: '#f5f7fa' }"
    >
      <el-table-column prop="teaName" label="教师姓名" align="center"> </el-table-column>
      <el-table-column prop="oldScore" label="修改前分值" align="center"> </el-table-column>
      <el-table-column prop="score" label="修改后分值" align="center"> </el-table-column>
      <el-table-column prop="modifyTime" label="修改时间" align="center"> </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';

export interface QuesImpl {
  specialTypeId: number;
  quesNo: string;
  rightAnswer: string;
  questionId: string;
  sort: string;
  markingRecords: MarkingRecord[];
  type: number;
  optionCount: number;
  isObj: boolean;
  sortTitle: string;
  score: number;
  answer: string;
  fullScore: number;
  editRecords: EditRecord[];
}

export interface EditRecord {
  score: number;
  teaName: string;
  oldScore: number;
  modifyTime: Date;
  teaId: string;
}

export interface MarkingRecord {
  score: number;
  teaName: string;
  typeName: string;
  teaId: string;
  sort: number;
  type: number;
}

@Component
export default class ScoreRecordsDialog extends Vue {
  @Prop() ques: QuesImpl;
  visible: boolean = true;
}
</script>

<style lang="scss" scoped>
.ques {
  font-size: 16px;
  &-score {
    margin-left: 25px;
  }
}
.headline {
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  line-height: 38px;
  width: 100%;
  position: relative;
  background: #fff;
}
</style>

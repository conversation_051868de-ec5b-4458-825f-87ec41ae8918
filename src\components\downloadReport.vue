<template>
  <div>
    <el-dialog title="下载设置" :visible.sync="dialogVisible" width="50%" @closed="handleClose">
      <div class="dialog-content">
        <div v-if="isDownloadCompre && canDownBySource">
          <div class="dialog-content-top">
            <div class="titleLine">
              综合报告指标选择<span class="download-tips">
                (说明：下载后，选中的指标展示在同一份excel文件里面)</span
              >
            </div>
            <div class="dialog-content-main">
              <el-checkbox
                :indeterminate="isIndetCompre"
                v-model="checkCompretAll"
                @change="handleCheckCompreAllChange"
                >全选</el-checkbox
              >
              <el-checkbox-group v-model="compreCheckList" @change="checkedCompreChange">
                <el-checkbox
                  v-for="(item, index) in compreList"
                  :label="item.title"
                  :key="index"
                ></el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <el-button
            type="primary"
            @click="sureClick('compre')"
            class="download-button"
            style="margin-bottom: 20px"
            >下载综合报告</el-button
          >
        </div>
        <div class="dialog-content-top">
          <div class="titleLine">
            拆分报告指标选择<span class="download-tips">
              (说明：下载后，选中的指标单独一份excel文件展示)</span
            >
          </div>
          <div class="dialog-content-main">
            <!-- 考情分析 -->
             <template v-if="studyAnalysisList.length">
              <p style="font-weight: bold; margin-bottom: 10px">考情分析：</p>
              <el-checkbox
                :indeterminate="isIndeterTest"
                v-model="checkTestAll"
                @change="handleCheckTestAllChange"
                >全选</el-checkbox
              >
              <el-checkbox-group
                v-model="splitTest"
                class="dialog-content-group"
                @change="checkedTestChange"
              >
                <el-checkbox
                  v-for="(item, index) in studyAnalysisList"
                  :label="item.title"
                  :key="index"
                >
              {{ item.title }}</el-checkbox>
              </el-checkbox-group>
             </template>

            <!-- 试卷分析 -->
            <template v-if="paperAnalysisList.length">
              <p style="font-weight: bold; margin-bottom: 10px">试卷分析：</p>
              <el-checkbox
                :indeterminate="isIndeterminate"
                v-model="checkSplitAll"
                @change="handleCheckSplitAllChange"
                >全选</el-checkbox
              >
              <el-checkbox-group
                v-model="splitExam"
                class="dialog-content-group"
                @change="checkedSplitChange"
              >
                <el-checkbox
                  v-for="(item, index) in paperAnalysisList"
                  :label="item.title"
                  :key="index"
                >
                  {{ item.title }}
                </el-checkbox>
              </el-checkbox-group>
            </template>
          </div>
        </div>
        <el-button type="primary" @click="sureClick('split')" class="download-button"
          >下载拆分报告</el-button
        >
      </div>
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="sureClick()"
          >确 定</el-button
        >
      </span> -->
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/service/auth';
import { getMenuEnable, MenuIdEnum } from '@/utils/examReportUtils';
import UserRole from '@/utils/UserRole';
import { getToRoles } from '@/utils/index';

export default {
  data() {
    return {
      dialogVisible: true,
      //综合报告选项
      compreList: [{
        menuId: MenuIdEnum.scoreSection,
        title: '总分分布',
        permission: false
      }, {
        menuId: MenuIdEnum.fiveRate,
        title: '班级各科均分',
        permission: false
      }, {
        menuId: MenuIdEnum.scoreSection,
        title: '班级各科分数段',
        permission: false
      }, {
        menuId: MenuIdEnum.rankSection,
        title: '班级各科名次段',
        permission: false
      }, {
        menuId: MenuIdEnum.cardHome,
        title: '成绩单',
        permission: false
      }],
      // 考情分析List
      studyAnalysisList: [
        {
          menuId: MenuIdEnum.cardHome,
          title: '成绩单',
          permission: false
        },{
          menuId: MenuIdEnum.scoreSection,
          title: '分数段分布',
          permission: false
        },
        {
          menuId: MenuIdEnum.online,
          title: '上线分析',
          permission: false
        },
        {
          menuId: MenuIdEnum.rankSection,
          title: '名次段分布',
          permission: false
        },
        {
          menuId: MenuIdEnum.gradeDistribute,
          title: '学生等级分布',
          permission: false
        },
        {
          menuId: MenuIdEnum.limitStu,
          title: '临界生',
          permission: false
        },
        {
          menuId: MenuIdEnum.topAndDiff,
          title: '学优生学困生',
          permission: false
        }
      ],
      // 试卷分析List
      paperAnalysisList: [{
        menuId: MenuIdEnum.answerDetail,
        title: '小题均分',
        permission: false
      }, {
        menuId: MenuIdEnum.answerDetail,
        title: '答题统计',
        permission: false
      }, {
        menuId: MenuIdEnum.bothWayReport,
        title: '双向细目表',
        permission: false
      }, {
        menuId: MenuIdEnum.quesTypeAvg,
        title: '题型均分',
        permission: false
      }, {
        menuId: MenuIdEnum.knowledgeAnalyze,
        title: '知识点分析',
        permission: false
      }, {
        menuId: MenuIdEnum.paperComment,
        title: '试卷分析',
        permission: false
      }],
      //综合报告已选下载项
      compreCheckList: [],
      //拆分报告已选下载项
      splitCheckList: [],
      //考情分析下载项
      splitTest: [],
      //试卷分析下载项
      splitExam: [],
      fsUrl: process.env.VUE_APP_KKLURL,
      //考试详情
      reportDetail: {},
      //是否全选试卷分析
      checkSplitAll: false,
      //试卷分析全选框状态
      isIndeterminate: false,
      //是否全选综合报告
      checkCompretAll: false,
      //综合报告全选框状态
      isIndetCompre: false,
      //考情分析全选框状态
      isIndeterTest: false,
      //是否全选考情分析
      checkTestAll: false,
      roles: '',
      accountType: '',
    };
  },
  computed: {
    isDownloadCompre() {
      return (
        this.roles.indexOf('1') != -1 ||
        this.roles.indexOf('2') != -1 ||
        // this.roles.indexOf('5') != -1 ||
        this.accountType === 3 ||
        this.accountType === 4 ||
        this.accountType === 5
      );
    },
    // 根据来源是否可以下载
    canDownBySource() {
      const source_101 = this.$sessionSave.get('reportDetail').source == 101; // 是否分层班
      const source_102 = this.$sessionSave.get('reportDetail').source == 102; // 3+1+2物理方向
      const source_103 = this.$sessionSave.get('reportDetail').source == 103; // 3+1+2历史方向
      return !source_101;
    },
  },
  mounted() {
    this.reportDetail = this.$sessionSave.get('reportDetail');
    if (this.reportDetail.dataState != 2) {
      this.paperAnalysisList = this.paperAnalysisList.filter(item => item.title == '小题均分' || item.title == '答题统计');
    }
    // 0总管理员 1校管 2年级组长 3学科组长 4备课组长 5班主任 6普通老师
    this.roles = getToRoles().join(',');
    let loginInfo = this.$sessionSave.get('loginInfo');
    this.accountType = loginInfo.account_type;
    // 初始化菜单权限
    this.initMenuPermission();
  },
  methods: {
    // 初始化菜单权限
    initMenuPermission() {
      let roles = this.roles.split(',').map(Number);
      this.compreList = this.compreList.filter(item => {
        item.permission = getMenuEnable({ menuId: item.menuId, roles: roles });
        return item.permission;
      });
      this.studyAnalysisList = this.studyAnalysisList.filter(item => {
        item.permission = getMenuEnable({ menuId: item.menuId, roles: roles });
        return item.permission;
      });
      this.paperAnalysisList = this.paperAnalysisList.filter(item => {
        item.permission = getMenuEnable({ menuId: item.menuId, roles: roles });
        return item.permission;
      });
    },

    async handleClose() {
      this.$emit('cloase-dialog');
    },
    /**
     * @name:全选综合报告
     */
    handleCheckCompreAllChange(val) {
      this.compreCheckList = val ? this.compreList.map(item => item.title) : [];
      this.isIndetCompre = false;
    },
    /**
     * @name:单选综合报告指标
     */
    checkedCompreChange(value) {
      let checkedCount = value.length;
      this.checkCompretAll = checkedCount === this.compreList.length;
      this.isIndetCompre = checkedCount > 0 && checkedCount < this.compreList.length;
    },
    /**
     * @name:全选考情分析
     */
    handleCheckTestAllChange(val) {
      this.splitTest = val ? this.studyAnalysisList.map(item => item.title) : [];
      this.isIndeterTest = false;
    },
    /**
     * @name: 单选考情分析
     */
    checkedTestChange(value) {
      let checkedCount = value.length;
      this.checkTestAll = checkedCount === this.studyAnalysisList.length;
      this.isIndeterTest = checkedCount > 0 && checkedCount < this.studyAnalysisList.length;
    },
    /**
     * @name:全选试卷分析
     */
    handleCheckSplitAllChange(val) {
      this.splitExam = val ? this.paperAnalysisList.map(item => item.title) : [];
      this.isIndeterminate = false;
    },

    /**
     * @name: 单选试卷分析
     */
    checkedSplitChange(value) {
      let checkedCount = value.length;
      this.checkSplitAll = checkedCount === this.paperAnalysisList.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.paperAnalysisList.length;
    },
    /**
     * @name:确定下载
     */
    async sureClick(type) {
      let token = getToken();
      let classList = this.$sessionSave.get('leaderClass');
      let mainSubjectList = this.$sessionSave.get('mainSubjectList') || [];

      //学科组长学科
      let subjectNames = mainSubjectList.map(item => {
        return item.subjectName;
      });
      //班主任班级
      let classIds = classList.map(item => {
        return item.class_name;
      });
      //年级组长不传班级名称
      if (this.roles.indexOf('2') != -1) {
        classIds = [];
      }
      //学科老师所带班级学科
      let subjectClz = this.$sessionSave.get('subjectClz');
      this.splitCheckList = this.splitTest.concat(this.splitExam);
      if (
        (type == 'compre' && this.compreCheckList.length == 0) ||
        (type == 'split' && this.splitCheckList.length == 0)
      ) {
        this.$message({
          message: '请先勾选指标！',
          type: 'warning',
        });
        return;
      }
      let downloadUrl = '';
      if (type == 'compre') {
        downloadUrl = `${this.fsUrl}/pexam/_/down/multiple?examId=${
          this.reportDetail.examId
        }&data=${this.compreCheckList.join(',')}`;
      } else {
        let role = {};
        if (this.$sessionSave.get('loginInfo').account_type == 5) {
          role = '';
        } else {
          const { year, campusCode } = this.$sessionSave.get('reportDetail');
          role = await UserRole.utils.getRoleSubjectClassMap(year, campusCode, this.$sessionSave.get('reportType') == 'school' ? true : false);
        }

        const params = {
          examId: this.reportDetail.examId,
          data: this.splitCheckList.join(','),
          loginType: this.accountType,
          role: role ? JSON.stringify(role) : '',
          subject: subjectNames.join(','),
          clz: classIds.join(','),
          // subjectClz: JSON.stringify(subjectClz),
          token,
        };

        const searchParams = new URLSearchParams(params);
        downloadUrl = `${this.fsUrl}/pexam/_/down/basic?${searchParams.toString()}`;
      }
      console.log('downloadUrl', downloadUrl);
      window.open(`${downloadUrl}`);
      this.dialogVisible = false;
      this.$emit('cloase-dialog');
    },

    // 获取备课组长学科 {学科: 班级}
    async getMainSubject() {
      const map = UserRole.utils.getRoleSubjectListMapByYear();
      const subjectList = map['4'];
      const classlist = await UserRole.utils.getRoleClassByRoleType('4');
      let subjectObj = '';
      if (subjectList) {
        const classNames = classlist.map(item => item.class_name);
        subjectObj = {};
        subjectList.forEach(item => {
          subjectObj[item.subjectName] = classNames.join(',');
        });
      }
      return subjectObj;
    },
  },
};
</script>

<style lang="scss" scoped>
.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;
  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 15px;
  }
}
.download-tips {
  font-size: 14px;
  color: #aaa;
  font-weight: 400;
}
.dialog-content-top {
  // margin-bottom:20px;
}
.download-button {
  margin-top: 15px;
}
</style>
<style lang="scss">
.dialog-content-main {
  .el-checkbox {
    margin-left: unset !important;
    margin-bottom: 10px;
  }
}
</style>

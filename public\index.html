<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <meta name=renderer content=webkit> 
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <title><%= htmlWebpackPlugin.options.title %></title>
    <link rel="stylesheet" href="<%= BASE_URL %>css/animate.min.css" />
    <link rel="stylesheet" href="<%= BASE_URL %>css/render.css" />
    <link rel="stylesheet" href="<%= BASE_URL %>css/shougong.css" />
    <link rel="stylesheet" href="<%= BASE_URL %>css/jy/jye-root.css" />
    <link rel="stylesheet" href="<%= BASE_URL %>css/jy/style.css" />
    <link rel="stylesheet" href="<%= BASE_URL %>css/jy/style_math.css" />
    <link rel="stylesheet" href="https://fs.iclass30.com/package/katex@0.15.2/dist/katex.min.css" />
    <script src="https://fs.iclass30.com/aliba/plug/jquery/jquery-3.2.1.min.js"></script>
    <style>
      html,
      body,
      #app,
      .page-wrapper {
        width: 100%;
        height: 100%;
        /* margin: 0; */
        overflow-x: hidden;
        line-height: 1.5;
      }

      /* body {
        filter: grayscale(100%);
      } */
      ::-webkit-scrollbar {
        height: 10px;
        overflow: auto;
        width: 3px;
      }

      ::-webkit-scrollbar-thumb {
        background-color: #c6c6c6;
        min-height: 25px;
        min-width: 25px;
        border: 1px solid rgba(0, 0, 0, 0.08);
        border-radius: 0px;
      }

      .over-hide {
        overflow: hidden;
      }

      [v-cloak] {
        display: none;
      }
    </style>
    <script src="<%= BASE_URL %>js/sticky-event.js"></script>
    <script src="<%= BASE_URL %>js/MathjaxHelper.js"></script>
  </head>

  <body>
    <noscript>
      <strong
        >We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without
        JavaScript enabled. Please enable it to continue.</strong
      >
    </noscript>
    <script type="text/javascript" src="<%= VUE_APP_SSO_PATH %>"></script>
    <div id="app"></div>
    <!-- built files will be auto injected -->

    <script>
      MathJax = {
        showMathMenu: false,
        tex: {
          inlineMath: [
            ['$$', '$$'],
            ['$', '$'],
            ['\\(', '\\)'],
            ['\\[', '\\]'],
          ],
          displayMath: [],
          // packages: { '[+]': ['base','ams', 'boldsymbol','mhchem', "physics","extpfeil"] },
        },
        options: {
          processHtmlClass: 'math-tex,math-show',
          // ignoreHtmlClass: '.*'
        },
        loader: {
          // load: ['[tex]/boldsymbol','[tex]/mhchem','[tex]/extpfeil', '[tex]/physics','input/tex', 'output/svg'],
          source: {
            // "[tex]/mhchem":
            //   "https://fs.iclass30.com/package/mathjax@3.2.0/es5/tex-mhchem.js",
            // "[tex]/physics":
            //   "https://fs.iclass30.com/package/mathjax@3.2.0/es5/tex-physics.js",
          },
          require: null,
        },
      };
    </script>
    <script src="https://res.wx.qq.com/open/js/jweixin-1.0.0.js"></script>
    <script>
      var _hmt = _hmt || [];
      (function () {
        var hm = document.createElement('script');
        hm.src = 'https://hm.baidu.com/hm.js?2b2cdbacbc0a6d54aa4e46bc94953606';
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(hm, s);
        function isMobileDevice() {
          // 检查 userAgent
          const isMobileUA = /Mobi|Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
          // 检查屏幕宽度
          const isSmallScreen = window.matchMedia('(max-width: 768px)').matches;
          return isMobileUA || isSmallScreen;
      }

      if (isMobileDevice()) {
        document.body.style.zoom = Math.min(document.body.clientWidth / 1500,1);
      }
      })();
    </script>
  </body>
</html>

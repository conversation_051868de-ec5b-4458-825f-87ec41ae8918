/*
 * @Description: 补录成绩设置，使用老接口
 * @Author: 小圆
 * @Date: 2024-05-29 09:45:05
 * @LastEditors: 小圆
 */

import { AbstractScoreSetting } from './AbstractScoreSetting';

import { getStuScore, listStuScore, publishScore, setStuScore, setStuType } from '@/service/pexam';

export class SupplScoreSetting extends AbstractScoreSetting {
  // 初始化学生
  async initStu(params: { examId: any; subjectId: any }) {
    const res = await listStuScore(params);
    const stuList = res.data;
    const stuTree = this.stuListToTree(stuList);

    return {
      stuList,
      stuTree,
    };
  }

  // 学生列表转换为树型结构
  stuListToTree(stuList) {
    stuList = stuList.map(item => {
      return {
        ...item,
        label: item.stuName,
      };
    });

    // type:缺考状态 0:已批改(已扫描) 1:未扫描 2:缺考 3:批改不完整  4：0分卷
    const type_0_stu_list = stuList.filter(item => item.type == 0);
    const type_1_stu_list = stuList.filter(item => item.type == 1);
    const type_2_stu_list = stuList.filter(item => item.type == 2);
    const type_3_stu_list = stuList.filter(item => item.type == 3);
    const type_4_stu_list = stuList.filter(item => item.type == 4);

    const type_0_stu_group = this.groupByClzName(type_0_stu_list);
    const type_1_stu_group = this.groupByClzName(type_1_stu_list);
    const type_2_stu_group = this.groupByClzName(type_2_stu_list);
    const type_3_stu_group = this.groupByClzName(type_3_stu_list);
    const type_4_stu_group = this.groupByClzName(type_4_stu_list);

    const type_0_stu_tree = this.groupToTree(type_0_stu_group);
    const type_1_stu_tree = this.groupToTree(type_1_stu_group);
    const type_2_stu_tree = this.groupToTree(type_2_stu_group);
    const type_3_stu_tree = this.groupToTree(type_3_stu_group);
    const type_4_stu_tree = this.groupToTree(type_4_stu_group);

    const tree = [
      {
        label: `批改完整 (${type_0_stu_list.length})`,
        children: [],
      },
      {
        label: `批改不完整 (${type_3_stu_list.length})`,
        children: [],
      },
      {
        label: `缺考 (${type_2_stu_list.length})`,
        children: [],
      },
      {
        label: `未扫描 (${type_1_stu_list.length})`,
        children: [],
      },
      {
        label: `0分学生 (${type_4_stu_list.length})`,
        children: [],
      },
    ];

    tree[0].children = type_0_stu_tree;
    tree[1].children = type_3_stu_tree;
    tree[2].children = type_2_stu_tree;
    tree[3].children = type_1_stu_tree;
    tree[4].children = type_4_stu_tree;
    return tree;
  }

  async initStuQuestion(option: { examId: any; subjectId: any; studentId: any }) {
    const params = {
      examId: option.examId,
      subjectId: option.subjectId,
      stuId: option.studentId,
    };

    const res = await getStuScore(params);
    const questionList = res.data.questions;
    return questionList;
  }

  async rePublishScore(option: { examId: any; }) {
    const params = {
      examId: option.examId,
    };
    await publishScore(params);
  }

  async setStuType(option: { examId: any; subjectId: any; studentId: any; state: any }) {
    let params = {
      examId: option.examId,
      id: option.studentId,
      type: option.state, // type:缺考状态 0:已批改(已扫描) 1:未扫描 2:缺考 3:批改不完整  4：0分卷
    };
    await setStuType(params);
  }

  async saveCorrect(option: {
    questionList: any;
    cardInfo: any;
    examId: any;
    subjectId: any;
    studentId: any;
    images: any;
  }) {
    const questionList = option.questionList;
    const examId = option.examId;
    const subjectId = option.subjectId;
    const studentId = option.studentId;
    const quesList = [];
    for (const item of questionList) {
      let score;
      let ques: any = {
        questionId: item.questionId,
        sort: item.sort,
        isObj: item.isObj,
      };
      if (item.type == 7) {
        ques.answer = item.answer;
        score = item.score;
      } else if (item.isObj) {
        if (item.answer) {
          score = this.getObjScore(item);
          ques.answer = item.answer;
        } else {
          score = 0;
        }
      } else {
        score = item.tempScore || item.tempScore === 0 ? item.tempScore : item.score;
      }
      ques.score = score;
      quesList.push(ques);
    }

    let params = {
      examId: examId,
      subjectId: subjectId,
      studentId: studentId,
      questions: quesList,
    };
    await setStuScore(params);
  }
}

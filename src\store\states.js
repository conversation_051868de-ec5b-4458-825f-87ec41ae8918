/*
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-08-24 09:27:25
 * @LastEditors: 小圆
 */
export const state = {
  // 个人登录信息
  loginInfo: {},
  // 选择学校信息
  schoolInfo: {},
  // 校区列表
  campusList: [],
  // 年级列表
  gradeList: [],
  // 学年列表
  yearList: [],
  // 学科列表
  subjectList: [],
  filterSubjectList: [],
  // 类别列表
  categoryList: [],
  // 学年列表
  acadYearsList: [],
  // 学期列表
  acadTermList: [],
  // 省份map
  provinceMap: {},
  // 省份列表
  provinceList: [],
  // 是否年级组长
  isTeaLeader: false,
  // 用户角色列表
  roleList: [],
  //教师所教学科
  teacherSubjects: [],
  teachParams: {
    sourceUrl: '',
    schoolId: '',
    gradeId: '',
    classId: '',
    subjectId: '',
    startTime: '',
    endTime: '',
    sourceType: '',
  },
  //单张图片的矩阵变换数据
  image_matrix: {
    matrix: '',
    height: 0,
    rotate: 0,
  },
  //会员信息
  vipInfo: {},
  // 加入对比讲评的信息
  joinCommentParam: {
    stuList: [],
    quesNo: -1,
    tQuesNo: '',
  },
  // 显示学生匿名
  showAnonymousName: false,
  // 显示学生分数
  showStudentScore: true,
};

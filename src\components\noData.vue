<template>
  <div class="nodata">
    <div>
      <img :src="imgSrc" alt="" />
      <slot v-if="$slots.default"></slot>
      <p v-else class="text-center">{{ text }}</p>
  </div>
  </div>
</template>

<script>
export default {
  name: 'no-data',
  props: {
    text: {
      type: String,
      default: '暂无数据',
    },
    type: {
      type: String,
      default: 'noRes',
    },
  },
  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      emptyImg: require('@/assets/empty.png'),
      zanImg: require('@/assets/zan.png'),
    };
  },
  computed: {
    imgSrc() {
      if (this.type == 'noRes') {
        return this.noResImg;
      } else if (this.type == 'empty') {
        return this.emptyImg;
      } else if (this.type == 'zan') {
        return this.zanImg;
      }
      return this.noResImg;
    },
  },
};
</script>

<style lang="scss" scoped>
.nodata {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0;

  /*background : url("../assets/no-res.png") center center no-repeat;*/
  img {
    display: block;
    margin: 0 auto;
    max-width: 100%;
  }
}
</style>

<template>
  <div class="exam-number-box">
    <div
      v-for="(item, index) in quesList"
      :class="[
        'ques_box',
        item.item_type == currentType ? 'active' : '',
        item.isEdit ? 'edit' : '',
      ]"
      :did="item.id"
      :style="[
        {
          left: item.pos[0] + 'px',
          top: item.pos[1] + 'px',
          width: item.pos[2] + 'px',
          height: item.pos[3] + 'px',
        },
      ]"
      :key="index + Math.random()"
    >
      <div class="exam-no">
        <div
          class="exam-no-item"
          v-for="(col, index) in Number(item.cols)"
          :key="index"
          :style="[{ marginRight: 8 + 'px' }]"
        >
          {{ index + 1 }}
        </div>
      </div>
      <edit-container
        class="edit-container"
        :item="item"
        @save-point="savePoint"
        @edit-point="editPoint"
        @delete-point="deletePoint"
      ></edit-container>
    </div>
  </div>
</template>

<script>
import EditContainer from './editContainer.vue';

export default {
  components: {
    EditContainer,
  },
  props: {
    ques: {
      type: Array,
      default() {
        return [];
      },
    },
    currPage: {
      type: Number,
      default() {
        return 0;
      },
    },
    currentType: {
      type: Number,
      default() {
        return 0;
      },
    },
  },
  watch: {
    ques: {
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          this.quesList = newVal;
        });
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      quesList: [],
    };
  },
  mounted() {
    this.$nextTick(() => {});
  },
  methods: {
    update(item) {
      this.quesList.forEach(ques => {
        if (ques.id == item.id) {
          this.$set(ques, 'pos', item.pos);
        }
      });
      this.$forceUpdate();
    },
    savePoint(item) {
      this.$emit('save-point', item);
      this.$forceUpdate();
    },
    editPoint(item) {
      this.$emit('edit-point', item);
      this.$forceUpdate();
    },
    deletePoint(item) {
      this.$emit('delete-point', item);
      this.$forceUpdate();
    },
  },
};
</script>

<style lang="scss" scoped>
.ques_box {
  position: absolute;
  border: 0.6mm solid #03c617;
  font-size: 22px;
  color: #03c617;
  &.draw {
    border: 0.6mm solid #ff6600;
    color: #ff6600;
    background-color: rgba(245, 201, 172, 0.4);
  }
  &.active {
    border: 0.6mm solid #409eff;
    color: #409eff;
    z-index: 9;
    &:hover {
      .edit-container,
      .ques-edit-wrapper {
        display: flex;
      }
    }
    .option_box {
      background-color: rgba(167, 204, 241, 0.5);
    }
  }
  &.edit {
    z-index: 9;
  }
  .option_box {
    position: fixed;
    font-size: 22px;
    background-color: rgba(91, 242, 116, 0.4);
  }
  .notop {
    position: absolute;
    top: -17px;
    left: 0px;
    min-width: 55px;
    font-size: 14px;
    color: #f5222d;
    font-weight: bold;
    &.point {
      top: -20px;
      left: 0px;
    }
  }
  .nobottom {
    position: absolute;
    bottom: 2px;
    right: 2px;
  }
  .resize-dot {
    position: absolute;
    width: 8px;
    height: 8px;
    border: 1px solid;
    background: #fff;
    box-sizing: border-box;

    &.top {
      cursor: n-resize;
      top: -4px;
      left: 50%;
      margin-left: -4px;
    }

    &.bottom {
      cursor: s-resize;
      bottom: -4px;
      left: 50%;
      margin-left: -4px;
    }

    &.left {
      top: 50%;
      margin-top: -4px;
      cursor: w-resize;
      left: -4px;
    }

    &.right {
      cursor: e-resize;
      right: -4px;
      top: 50%;
      margin-top: -4px;
    }

    &.top-left {
      margin-top: -4px;
      cursor: nw-resize;
      margin-left: -4px;
    }

    &.top-right {
      cursor: ne-resize;
      right: 0;
      margin-right: -4px;
      margin-top: -4px;
    }

    &.bottom-left {
      cursor: sw-resize;
      bottom: 0;
      margin-left: -4px;
      margin-bottom: -4px;
    }

    &.bottom-right {
      cursor: se-resize;
      right: -4px;
      bottom: 0;
      margin-bottom: -4px;
    }
  }
  .edit-container {
    display: none;
  }
  .ques-edit-wrapper {
    display: none;
    background: rgba(0, 0, 0, 0.5);
    font-size: 12px;
    z-index: 9;
    position: absolute;
    right: 0px;
    bottom: -19px;
    justify-content: space-around;
    color: #fff;
  }
  .ques-cancel,
  .ques-save,
  .ques-edit {
    text-align: center;
    // width: 30px;
    padding: 0 2px;
    z-index: 9;
    &:hover {
      background: rgba(0, 0, 0, 0.5);
      cursor: pointer;
    }
  }
}
.exam-no {
  display: flex;
  position: absolute;
  top: -26px;
  font-size: 18px;
  .exam-no-item {
    color: #f5222d;
  }
}
</style>
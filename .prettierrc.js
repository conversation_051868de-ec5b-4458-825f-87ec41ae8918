/*
 * @Descripttion: Prettierrc代码格式化设置
 * @Author: 小圆
 * @Date: 2023-11-23 10:06:59
 * @LastEditors: 小圆
 */
module.exports = {
    "printWidth": 120, // 指定每行代码的最大宽度。默认为 80。
    "tabWidth": 2,  // 指定一个制表符等于多少个空格。默认为 2。
    "useTabs": false,   // 指定是否使用制表符代替空格缩进。默认为 false。
    "semi": true,       // 指定是否在语句末尾添加分号。默认为 true。
    "singleQuote": true,        // 指定是否使用单引号而不是双引号。默认为 false。
    "quoteProps": "as-needed",  // 指定对象属性名称是否使用引号。可以是 “as-needed”、true 或 false。默认为 “as-needed”。
    "jsxSingleQuote": false,    // 指定 JSX 属性是否使用单引号而不是双引号。默认为 false。
    "trailingComma": "es5",     // 指定是否在数组和对象字面量的末尾添加逗号。可能的值是 “none”、“es5”（在 ES5 中有效）和 “all”。默认为 “es5”。
    "bracketSpacing": true,     // 指定是否在对象字面量中的括号之间添加空格。默认为 true。
    "jsxBracketSameLine": false,    // 指定是否将多行 JSX 元素的末尾括号放在同一行上。默认为 false。
    "arrowParens": "avoid",    // 指定箭头函数参数是否永远使用圆括号。可以是 “always”、“avoid”、或 “as-needed”。默认为 “always”。
    "requirePragma": false,     // 指定是否需要在文件顶部添加 // @format 注释才会格式化。默认为 false。
    "insertPragma": false,      // 指定是否在文件顶部插入 // @format 注释。默认为 false。
    "vueIndentScriptAndStyle": false,       // 指定是否单独缩进 Vue 组件中的
    "proseWrap": "preserve"
}
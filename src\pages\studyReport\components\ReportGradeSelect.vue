<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-03-25 10:15:59
 * @LastEditors: 小圆
-->
<template>
  <div class="header-item">
    <span class="header-item__label">年级：</span>
    <el-select
      class="header-item__select short-select"
      value-key="gradeCode"
      :value="filterInfo.gradeInfo"
      @change="onChange"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <el-option
        v-for="item in selectOption.gradeList"
        :key="item.gradeCode"
        :title="item.gradeName"
        :label="item.gradeName"
        :value="item"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { IGradeInfo } from '@/pages/studyReport/plugins/types';
import ReportComponent from './ReportComponent';

@Component
export default class ReportGradeSelect extends ReportComponent {
  onChange(value: IGradeInfo) {
    this.FilterModule.setGradeInfo(value);
    this.FilterModule.trigger('changeFilter');
  }
}
</script>

<style scoped lang="scss">
@import './reportSelect.scss';
</style>

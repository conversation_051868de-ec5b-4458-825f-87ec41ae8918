<template>
  <div class="small-ques-box">
    <div class="titleLine">小题分析</div>
    <div class="table-box" v-loading="isLoading">
      <SmallQuesTable :tableData="tableData"></SmallQuesTable>
    </div>
    <div class="chart-box" v-loading="isLoading" id="pane-chart">
      <SmallQuesChart ref="smallQuesChart" :tableData="tableData"></SmallQuesChart>
    </div>
  </div>
</template>

<script>
import SmallQuesChart from '@/components/smallQuesReport/SmallQuesChart';
import SmallQuesTable from '@/components/smallQuesReport/SmallQuesTable';
import { getReportSmallAnalysis } from '@/service/pexam';

export default {
  name: 'smallQuesReport',
  props: ['filterData'],
  components: {
    SmallQuesChart,
    SmallQuesTable,
  },
  data() {
    return {
      activeName: 'chart',
      isLoading: false,
      tableData: [],
    };
  },
  watch: {
    filterData: {
      immediate: true,
      deep: true,
      handler(newValue, oldValue) {
        if (newValue) this.getReportSmallAnalysis();
      },
    },
  },
  activated() {},
  methods: {
    // 获取试卷小题分析
    async getReportSmallAnalysis() {
      try {
        const data = await getReportSmallAnalysis({
          subjectId: this.filterData.subjectId,
          examId: this.$sessionSave.get('reportDetail').examId,
        });
        if (!data.data.length) {
          this.tableData = [];
          return;
        }
        this.tableData = data.data;
      } catch (error) {
        console.log(error);
        this.tableData = [];
      }
    },
    updateFilter(data) {},
    updateData({ isLoading }) {
      this.isLoading = isLoading;
    },
    handleClick(tab, event) {
      if (tab.name == 'chart') {
        this.$nextTick(() => {
          if (this.$refs.smallQuesChart.smallQuesChart) {
            this.$refs.smallQuesChart.smallQuesChart.resize();
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.small-ques-box {
  margin-top: 10px;
  border-radius: 6px;
  padding-bottom: 30px;

  .chart-box {
    height: 410px;
    background: #ffffff;
    border: 1px solid #e4e8eb;
    border-radius: 3px;
    margin-top: 20px;
  }

  .table-box {
    border: 1px solid #e4e8eb;
    border-radius: 3px;
  }
}
.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;
  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 15px;
  }
}
</style>
<style lang="scss">
.small-ques-box {
  .el-tabs {
    .el-tabs__header {
      margin: 0 0 1px 4px;
    }
    .el-tabs__nav-wrap::after {
      width: 160px;
    }
    .el-tabs__item {
      width: 80px;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      &.is-top:last-child {
        padding-left: 0;
      }
      &.is-top:nth-child(2) {
        padding-right: 0;
      }
    }
  }
}
</style>

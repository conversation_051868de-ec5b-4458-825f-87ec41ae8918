<template>
  <div class="mobile-chat" v-drag-dom="{ handle: '.chat-header' }">
    <!-- 顶部导航栏 -->
    <div class="chat-header">
      <div class="title">AI客服</div>
      <div class="header-right">
        <el-button type="text" class="minimize-btn" title="最小化" @click="$emit('toggleAI', false)">
          <i class="el-icon-minus"></i>
        </el-button>
      </div>
    </div>

    <!-- 聊天内容区域 -->
    <div class="chat-container" ref="chatContainer">
      <div v-for="(message, index) in chatHistory" :key="index" class="message-item">
        <div :class="['message', message.role === 'user' ? 'user-message' : 'ai-message']">
          <div class="avatar">
            {{ message.role === 'user' ? '👤' : '🤖' }}
          </div>
          <div class="content" @click="handleClick" v-html="message.content"></div>
        </div>
      </div>
      <div v-if="isLoading" class="message-item">
        <div class="message ai-message">
          <div class="avatar">🤖</div>
          <div class="content">
            <span class="typing-dots">思考中</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部输入区域 -->
    <div class="chat-footer">
      <div class="input-container">
        <el-input v-model="userInput" type="textarea" style="height: 25px" :rows="1" autosize placeholder="请输入您的问题..."
          @keyup.enter.native.exact="handleSend" />
        <el-button type="primary" circle @click="handleSend" :loading="isLoading">
          <i class="el-icon-s-promotion"></i>
        </el-button>
      </div>
    </div>
    <!-- 模态框 -->
    <div v-if="showModal" class="modal" @click.self="closeModal">
      <div class="modal-content">
        <!-- 图片 -->
        <img v-if="mediaType === 'img'" :src="currentMedia" />
        <!-- 视频 -->
        <video v-if="mediaType === 'video'" ref="modalVideo" :src="currentMedia" controls autoplay></video>
        <!-- 关闭按钮 -->
        <button class="close-btn" @click="closeModal">×</button>
      </div>
    </div>
  </div>
</template>

<script>
import { guid } from '@/utils/index';
import { getCozeTokenAPI } from '@/service/pexam';
import { CozeAPI, COZE_COM_BASE_URL, ChatStatus, RoleType, ChatEventType } from '@coze/api';
export default {
  name: 'AiHelp',
  data() {
    return {
      chatHistory: [
        {
          role: 'assistant',
          content: '有什么问题可以帮助您吗？'
        }
      ],
      userInput: '',
      isLoading: false,
      client: null,
      COZE_BASE_URL: 'https://api.coze.cn',
      BOT_ID: '7478531927604674569',
      showModal: false,
      currentMedia: '',
      mediaType: '', // 'img' 或 'video'
      userId: '', // 添加用户ID存储
      maxChatHistoryLength: 6, // 添加最大聊天记录长度限制
    };
  },
  created() {
    this.createCoze();
    // 获取并存储用户ID
    this.userId = this.$sessionSave.get('loginInfo')?.id || guid();
  },
  mounted() {
    // 加载历史聊天记录
    this.loadChatHistory();
  },
  methods: {
    createCoze() {
      this.client = new CozeAPI({
        baseURL: this.COZE_BASE_URL,
        token: async () => {
          return this.getCozeToken();
        },
      });
    },
    async getCozeToken() {
      try {
        let res = await getCozeTokenAPI();
        return res.data.access_token;
      } catch (error) {
        return '';
      }
    },
    async handleSend() {
      if (!this.userInput.trim() || this.isLoading) return;

      // 添加用户消息
      this.chatHistory.push({
        role: 'user',
        content: this.userInput,
      });

      const userMessage = this.userInput;
      this.userInput = '';
      this.isLoading = true;
      this.scrollToBottom();

      try {
        const stream = await this.client.chat.stream({
          bot_id: this.BOT_ID,
          user_id: this.userId,
          additional_messages: [
            {
              role: RoleType.User,
              content: userMessage,
              content_type: 'text',
              type: 'question',
            },
          ],
        });

        let aiResponse = '';
        for await (const part of stream) {
          if (part.event === ChatEventType.CONVERSATION_MESSAGE_DELTA) {
            if (this.isLoading) {
              this.isLoading = false;
              // 创建AI消息占位
              this.chatHistory.push({
                role: 'assistant',
                content: '',
              });
            }
            aiResponse = this.formatVideoContent(aiResponse + part.data.content);
            // 更新最后一条消息并格式化媒体内容
            this.chatHistory[this.chatHistory.length - 1].content = aiResponse;
          }else if(part.event === ChatEventType.DONE){
            this.chatHistory[this.chatHistory.length - 1].content = this.formatMediaContent(aiResponse);
          }
          // 滚动到底部
          this.scrollToBottom();
        }

        // 保存聊天历史
        this.saveChatHistory();
      } catch (error) {
        console.error('Chat error:', error);
        this.$message.error('发送消息失败，请重试');
        // 移除失败的消息占位符
        if (this.chatHistory[this.chatHistory.length - 1].role === 'assistant' &&
          !this.chatHistory[this.chatHistory.length - 1].content) {
          this.chatHistory.pop();
        }
      } finally {
        this.isLoading = false;
      }
    },
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.chatContainer;
        container.scrollTop = container.scrollHeight;
      });
    },
    handleClick(event) {
      // 获取点击的图片或视频元素
      const mediaElement = event.target.closest('img, video');
      if (!mediaElement) return;
      // 阻止默认行为（如链接跳转）
      event.preventDefault();

      // 获取媒体资源路径
      this.currentMedia = mediaElement.src || mediaElement.currentSrc;
      // 设置媒体类型
      this.mediaType = mediaElement.tagName.toLowerCase();
      this.showModal = true;
    },
    closeModal() {
      this.showModal = false;
      this.currentMedia = '';
      this.mediaType = '';
    },
    // 新增格式化媒体内容的方法
    formatMediaContent(content) {
      return content
        // .replace(/<img[^>]*src=['"]([^'"]+)['"]/gi, '<img style="width:160px;cursor:pointer" src="$1" /><br />')
        // .replace(/<link[^>]*src=['"]([^'"]+)['"]/gi, '<a href="$1" target="_blank">点击下载</a><br />')
        .replace(/!\[(.*?)]\((.*?)\)/gi, '<img src="$2" alt="$1" style="width: 160px;cursor:pointer" />')
        .replace(/\[(.*?)]\((.*?)\)/gi, '<a href="$2" target="_blank">点击下载</a>');
    },
    formatVideoContent(content){
      let path = require("@/assets/correct/icon_web_movieclass.png");
      return content
        .replace(/[\n]/g,'<br />')
        .replace(/<video\b[^>]*src\s*=\s*["']([^"']*)["'][^>]*\/>/gi, `<video poster="${path}" preload="none" style="width:60px;cursor:pointer" src="$1"></video>`);
    },
    // 保存聊天历史到本地存储
    saveChatHistory() {
      try {
        // 限制聊天记录长度
        if (this.chatHistory.length > this.maxChatHistoryLength) {
          // 保留最新的消息，删除旧消息
          this.chatHistory = this.chatHistory.slice(-this.maxChatHistoryLength);
        }
        localStorage.setItem(`aiChat_${this.userId}`, JSON.stringify(this.chatHistory));
      } catch (error) {
        console.error('保存聊天历史失败:', error);
      }
    },

    // 从本地存储加载聊天历史
    loadChatHistory() {
      try {
        const savedHistory = localStorage.getItem(`aiChat_${this.userId}`);
        if (savedHistory) {
          this.chatHistory = JSON.parse(savedHistory);
          this.scrollToBottom();
        }
      } catch (error) {
        console.error('加载聊天历史失败:', error);
        // 如果加载失败，保持默认的欢迎消息
      }
    },

    // 清空聊天历史
    clearChatHistory() {
      this.chatHistory = [
        {
          role: 'assistant',
          content: '有什么问题可以帮助您吗？'
        }
      ];
      this.saveChatHistory();
    },
  }
};
</script>

<style scoped>
.mobile-chat {
  position: fixed;
  height: 600px;
  width: 300px;
  right: 100px;
  bottom: 60px;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  box-shadow: 0px 0px 5px 1px #bbbbbb;
  z-index: 9999;
}

.chat-header {
  height: 50px;
  background: #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  position: sticky;
  top: 0;
  z-index: 10;
  cursor: move;
}

.header-right {
  position: absolute;
  right: 0;
}

.minimize-btn {
  padding: 8px;
  font-size: 18px;
  color: #666;
}

.minimize-btn:hover {
  color: #409eff;
}

.title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.chat-container {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
  padding-bottom: 70px;
}

.message-item {
  margin-bottom: 16px;
}

.message {
  display: flex;
  gap: 8px;
  max-width: 90%;
}

.user-message {
  flex-direction: row-reverse;
  margin-left: auto;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  background: #fff;
  flex-shrink: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.content {
  padding: 10px 12px;
  border-radius: 16px;
  font-size: 14px;
  line-height: 1.5;
  word-break: break-word;
}

.content :deep(img),
.content :deep(video) {
  cursor: pointer;
  transition: transform 0.2s;
}

.content :deep(img:hover),
.content :deep(video:hover) {
  transform: scale(1.05);
}

.user-message .content {
  background: #07c160;
  color: #fff;
  margin-right: 4px;
  border-radius: 16px 4px 16px 16px;
}

.ai-message .content {
  background: #fff;
  color: #333;
  margin-left: 4px;
  border-radius: 4px 16px 16px 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.chat-footer {
  background: #f5f7fa;
  padding: 10px;
  border-top: 1px solid #eee;
}

.input-container {
  display: flex;
  gap: 8px;
  align-items: flex-end;
  background: #fff;
  border-radius: 20px;
  padding: 8px;
}

.input-container .el-textarea {
  flex: 1;
}

.input-container .el-textarea :deep(.el-textarea__inner) {
  border: none;
  resize: none;
  background: transparent;
  padding: 0;
  line-height: 20px;
  min-height: 20px !important;
}

.input-container .el-button {
  padding: 8px;
}

.typing-dots::after {
  content: '...';
  animation: typing 1.5s infinite;
}

@keyframes typing {
  0% {
    content: '.';
  }

  33% {
    content: '..';
  }

  66% {
    content: '...';
  }
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  position: relative;
  max-width: 100%;
  max-height: 100%;
  background: transparent;
}

.modal-content img {
  max-width: 90vw;
  max-height: 90vh;
  object-fit: contain;
}

.modal-content video {
  max-width: 90vw;
  max-height: 90vh;
  object-fit: contain;
}

.close-btn {
  position: absolute;
  background: transparent;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 5px 10px;
}

.close-btn:hover {
  color: #ccc;
}
</style>

<template>
  <div>
    <!-- 服务到期提示 -->
    <el-dialog
      custom-class="dateExpiredDialog"
      :visible="dialogVisible"
      width="40%"
      :modal="false"
      :show-close="false"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
    >
      <!-- 弹窗内容 -->
      <div style="text-align: center">{{ tips }}</div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      required: true,
    },
    tips: {
      type: String,
      default() {
        return '非常抱歉，贵校服务已到期，请与商务联系进行延期。';
      },
    },
  },
};
</script>

<style lang="scss">
.dateExpiredDialog {
  background-color: rgba(0, 0, 0, 0.5);
  .el-dialog__header {
    height: 0;
  }
  .el-dialog__body {
    font-size: 18px;
    color: #fff;
    height: 60px;
    padding: 15px 25px 30px;
  }
}
</style>
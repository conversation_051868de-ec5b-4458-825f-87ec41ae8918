<template>
    <div class="record-list-container">
        <el-breadcrumb separator-class="el-icon-arrow-right">
            <el-breadcrumb-item @click.native="goBack" style="cursor: pointer;">
                <i class="el-icon-back"></i>
                返回
            </el-breadcrumb-item>
            <el-breadcrumb-item>{{ title }}</el-breadcrumb-item>
            <el-breadcrumb-item>操作记录</el-breadcrumb-item>
            <el-button type="primary" style="position: absolute;right: 20px;" @click="getRecordList">刷新</el-button>
        </el-breadcrumb>


        <el-table :data="recordList" style="width: 100%" border v-loading="loading" element-loading-text="加载中..."
            element-loading-spinner="el-icon-loading">
            <el-table-column type="index" label="序号" width="80" align="center">
            </el-table-column>
            <el-table-column prop="createDate" label="操作时间" align="center">
            </el-table-column>
            <el-table-column prop="realname" label="操作人" align="center">
            </el-table-column>
            <el-table-column prop="cmd" label="操作内容" align="center">
                <template slot-scope="scope">
                    <div class="operation-content">
                        <span>{{ scope.row.cmd }}</span>
                        <el-button v-if="scope.row.description.fileUrl" type="text" class="download-btn"
                            @click="handleDownload(scope.row)">
                            下载
                        </el-button>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="description" label="操作结果" align="center">
                <template slot-scope="scope">
                    <div :class="getResultClass(scope.row.description.state)">
                        <div v-if="scope.row.description.state == 2" class="error-message">
                            {{ scope.row.description.msg }}
                        </div>
                        <div v-else-if="scope.row.description.state == 1">
                            <el-popover
                                placement="top-start"
                                width="300"
                                trigger="hover"
                                :content="scope.row.description.msg">
                                <el-button slot="reference" type="text">操作成功</el-button>
                            </el-popover>
                        </div>
                        <div v-else>处理中</div>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <div class="pagination-container">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="pagination.currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.pageSize"
                layout="total, sizes, prev, pager, next, jumper" :total="pagination.total">
            </el-pagination>
        </div>
    </div>
</template>

<script>
import { getQueryString } from '@/utils';
import BreadCrumbs from '@/components/Breadcrumbs/index.vue';
import { getLogListAPI } from '@/service/pexam';
import { downloadFileByIframe } from '@/utils/index';
export default {
    name: 'RecordList',
    components: {
        BreadCrumbs
    },
    data() {
        return {
            loading: false,
            examId: getQueryString('examId') || '',
            title: getQueryString('examName') || '操作记录',
            recordList: [],
            pagination: {
                currentPage: 1,
                pageSize: 10,
                total: 0
            },
        };
    },
    created() {
        this.getRecordList();
    },
    methods: {
        // 获取记录列表
        async getRecordList() {
            if (!this.examId) {
                this.$message.warning('考试ID不能为空');
                return;
            }

            this.loading = true;
            try {
                // 构建查询参数
                const params = {
                    examId: this.examId,
                    page: this.pagination.currentPage,
                    limit: this.pagination.pageSize,
                    schoolId: this.$sessionSave.get('schoolInfo').id,
                };

                // 实际项目中使用API获取数据
                const res = await getLogListAPI(params);
                this.recordList = res.data.rows.map(item => {
                    return {
                        ...item,
                        description: JSON.parse(item.description)
                    }
                });
                this.pagination.total = res.data.total_rows;
                this.loading = false;
            } catch (error) {
                console.error('获取记录列表失败:', error);
                this.$message.error('获取记录列表失败');
                this.loading = false;
            }
        },
        // 处理页码变化
        handleCurrentChange(currentPage) {
            this.pagination.currentPage = currentPage;
            this.getRecordList();
        },

        // 处理每页条数变化
        handleSizeChange(pageSize) {
            this.pagination.pageSize = pageSize;
            this.pagination.currentPage = 1;
            this.getRecordList();
        },

        // 处理下载
        async handleDownload(row) {
            try {
                downloadFileByIframe(row.description.fileUrl);
            } catch (error) {
                console.error('下载文件失败:', error);
                this.$message.error('下载文件失败');
            }
        },

        // 获取操作结果的样式类
        getResultClass(result) {
            if (result == '1') {
                return 'result-success';
            } else if (result == '2') {
                return 'result-fail';
            } else if (result == '0') {
                return 'result-processing';
            }
            return '';
        },
        goBack() {
            this.$router.back();
        }
    }
};
</script>

<style lang="scss" scoped>
.record-list-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .el-breadcrumb {
        line-height: 40px;
        font-size: 16px;
    }

    .record-list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        flex-wrap: wrap;
        gap: 16px;

        .title {
            font-size: 20px;
            font-weight: 600;
            color: #303133;
            margin: 0;
            position: relative;
            padding-left: 12px;

            &::before {
                content: "";
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 4px;
                height: 18px;
                background-color: #409EFF;
                border-radius: 2px;
            }
        }

        .filter-section {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;

            @media screen and (max-width: 768px) {
                width: 100%;
            }

            .el-date-picker,
            .el-select {
                width: 220px;
            }
        }
    }

    .el-table {
        margin-bottom: 20px;
        border-radius: 4px;
        overflow: hidden;

        ::v-deep .el-table__header-wrapper th {
            background-color: #f5f7fa;
            color: #606266;
            font-weight: 600;
        }

        ::v-deep .el-table__row {
            transition: background-color 0.3s;

            &:hover {
                background-color: #f0f9ff;
            }
        }
    }

    .operation-content {
        display: flex;
        align-items: center;
        justify-content: center;

        .content-import {
            color: #409EFF;
            font-weight: 500;
        }

        .download-btn {
            margin-left: 10px;
            color: #409EFF;
            padding: 2px 8px;
            border-radius: 4px;
            transition: all 0.3s;

            &:hover {
                background-color: #ecf5ff;
            }
        }
    }

    .result-success {
        color: #67C23A;
        font-weight: 500;
        display: flex;
        flex-direction: column;
        align-items: center;
        .el-button--text{
            color: #67C23A;
        }
    }

    .result-fail {
        color: #F56C6C;
        font-weight: 500;
        display: flex;
        flex-direction: column;
        align-items: center;

        .error-message {
            font-size: 12px;
            margin-top: 5px;
            color: #F56C6C;
            opacity: 0.8;
            font-weight: normal;
            max-width: 180px;
            white-space: normal;
            word-break: break-all;
            line-height: 1.4;
        }
    }

    .result-processing {
        color: #E6A23C;
        font-weight: 500;
        display: flex;
        flex-direction: column;
        align-items: center;

        &::after {
            content: "";
            display: inline-block;
            width: 10px;
            height: 10px;
            margin-top: 5px;
            border-radius: 50%;
            background-color: #E6A23C;
            animation: pulse 1.5s infinite;
        }
    }

    @keyframes pulse {
        0% {
            transform: scale(0.8);
            opacity: 0.8;
        }

        50% {
            transform: scale(1.2);
            opacity: 1;
        }

        100% {
            transform: scale(0.8);
            opacity: 0.8;
        }
    }

    .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;

        ::v-deep .el-pagination {
            padding: 0;
            font-weight: normal;

            .el-pagination__total,
            .el-pagination__sizes,
            .el-pagination__jump {
                margin-right: 16px;
            }

            .btn-prev,
            .btn-next,
            .el-pager li {
                min-width: 32px;
                height: 32px;
                line-height: 32px;
                border-radius: 4px;
                border: 1px solid #e4e7ed;
                background-color: #fff;

                &:hover {
                    color: #409EFF;
                }

                &.active {
                    background-color: #409EFF;
                    color: #fff;
                    border-color: #409EFF;
                }
            }
        }
    }
}
</style>

/*
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2024-12-12 20:53:48
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-07-23 11:53:43
 */
/**
 * @name: 页面布局
 */
export const IPAGELAYOUT = {
    A4: 1,
    A3: 2,
    A33: 3,
    A32: 4, //正三反二
    A23: 5, //正二反三
};
/**
* @name: 半对给分规则
*/
export const IRULERTYPE = {
    /**标准*/
    STANDAD: 0,
    /**新高考*/
    EXAM: 1,
    /**自定义*/
    CUSTOM: 2,
};
/**
* @name: 答题卡类型
*/
export const ICARD_TYPE = {
    /**纯答题卡*/
    BLANKCARD: 0,
    /**题卡合一*/
    QUESCARD: 1,
    /**仅答题卡*/
    ONLYCARD: 2,
    /**三方卡*/
    THIRD: 3,
    /**英语专项*/
    ENGLISH: 4
};
//卡类型
export const IAB_CARD_TYPE = {
    /**普通卡*/
    default: 0,
    /**AB卡*/
    abCard: 1,
    /**AB卷*/
    abPaper: 2,
}
//关联答题卡类型
export const ICARD_STATE = {
    /**普通卡*/
    default: 0,
    /**AB卡*/
    abCard: 1,
    /**AB卷*/
    abPaper: 2,
    /**AB卷(2张卡)*/
    abPaperTwo: 3,
}
// AB卡或者AB卷时,此字段生效 0:A卡(卷) 1:B卡(卷)
export const IAB_CARD_SHEEFT_TYPE = {
    /**A卡*/
    aCard: 0,
    /**B卡*/
    bCard: 1,
}
//批阅类型
export const ICORRECT_TYPES = {
    /**手阅*/
    HAND: 1,
    /**网阅*/
    WEB: 2,
    /** 拍改（移动端拍扫改卷） */
    PHOTO: 3,
}
//考试来源类型
export const ISOURCE_TYPES = {
    /**手阅*/
    HAND: 3,
    /**网阅*/
    WEB: 4,
    /**拍改 */
    PHOTO: 6,
}

export const IQUES_SCAN_MODE = {
    /** 普通  */
    NORMAL: 0,
    /** 智能批改-填空 */
    AI_FILL: 1,
    /** 智能批改-简答 */
    AI_SUBJECT: 2,
    /** 智能批改-作文 */
    AI_ESSAY: 3,
}

/**
 * @name: 题目类型
 */
export const IQUES_TYPE = {
    /** 单选 */
    singleChoice : 8,
    /** 多选题 */
    choice : 1,
    /** 判断题 */
    judge : 2,
    /** 填空题 */
    fill : 3,
    /** 语音题 */
    voice : 4,
    /** 英语自动评测题 */
    evaluating : 5,
    /** 简答题 */
    subject : 6,
    /** 填空题自动批改 */
    fillEva : 7,
    /* 混合题 */
    mixin : 9,
    /* 非作答区 */
    noAnswerArea : 10,
  }

  /**
   * @name: 阅卷状态
   */
  export const IPROGRESS_TYPE = {
    /** 未开始 */
    unStart : 0,
    /** 答卷设置 */
    cardSetting : 1,
    /** 阅卷设置 */
    correctSetting : 2,
    /** 扫描试卷 */
    scanPaper : 3,
    /** 发布成绩（手）/开始阅卷（网） */
    startCorrect : 4,
    /** 结束阅卷（网） */
    endCorrect : 5,
    /** 重新阅卷（网） */
    reCorrect : 6,
    /** 结束考试（网） */
    endExam : 7,
    /** 发布成绩（网） */
    publishScore : 8,
  }
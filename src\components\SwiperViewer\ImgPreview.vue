<!--
 * @Descripttion: 学生题目图片预览
 * @Author: 小圆
 * @Date: 2024-01-13 09:26:00
 * @LastEditors: 小圆
-->
<template>
  <previewSlider
    ref="previewSlider"
    :data="imgList"
    @slideChange="slideChange"
    @tabPrev="tabPrev"
    @tabNext="tabNext"
    @close="close"
  >
    <!-- 主体 -->
    <template slot-scope="{ item, index }">
      <img
        :src="item"
        :data-src="item"
        :data-index="index"
        @load="loadImage(index)"
        class="swiper-lazy"
      />
    </template>
    <slot name="toolLeft" slot="toolLeft"></slot>
    <slot name="toolRight" slot="toolRight"></slot>
  </previewSlider>
</template>

<script>
import previewSlider from '@/components/SwiperViewer/previewSlider.vue';
export default {
  components: {
    previewSlider,
  },

  props: {
    // 图片
    imgList: {
      type: Array,
      default: () => [],
    },
    // 索引
    initialIndex: {
      type: Number,
      default: 0,
    },
  },
  computed: {},
  data() {
    return {
      // 当前图片列表
      imgs: [],
      // 当前学生索引
      stuIndex: this.initialStuIndex,
    };
  },

  async mounted() {
    if (this.imgList.length) {
      this.imgs = this.imgList;
    }
  },

  methods: {
    // 图片加载
    loadImage(index) {
      this.$refs.previewSlider.previewTo('fitWindow', index);
    },

    // 索引更新
    slideChange(index) {
      console.log('当前索引', index);
    },

    // 按钮切换上一个
    async tabPrev(activeIndex) {},

    // 按钮切换下一个
    async tabNext(activeIndex) {},

    close() {
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .gallery-top {
  height: 100% !important;
}

::v-deep .swiper-imageTool-box {
  bottom: 5% !important;
}
</style>

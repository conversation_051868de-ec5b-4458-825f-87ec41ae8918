<template>
  <div class="numeric-input-box">
    <input
      class="numeric-input-inner"
      type="tel"
      inputmode="numeric"
      :placeholder="placeholder"
      v-model="inputVal"
      @input="onInput"
      @blur="onBlur"
    />
  </div>
</template>

<script>
import { isDef, formatNumber, trimExtraZero } from '../utils/number.js';

export default {
  name: 'NumericInput',
  props: {
    value: [String, Number],
    placeholder: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: 'digit',
      validator: function (value) {
        return ['digit', 'number'].indexOf(value) !== -1;
      },
    },
    isPositive: {
      type: Boolean,
      default: false,
    },
    toFixed: {
      type: Number,
      default: 8,
    },
  },
  data() {
    return {
      inputVal: this.value,
    };
  },
  mounted() {},
  methods: {
    onInput(event) {
      if (event.target.composing) {
        return;
      }

      this.updateValue(event.target.value);
    },
    onBlur() {
      this.inputVal = trimExtraZero(this.inputVal);
      this.$emit('input', this.inputVal);
    },

    updateValue(value) {
      value = isDef(value) ? String(value) : '';

      // native maxlength not work when type is number
      const { maxlength } = this;
      if (isDef(maxlength) && value.length > maxlength) {
        value = value.slice(0, maxlength);
      }
      if (this.type === 'number' || this.type === 'digit') {
        const allowDot = this.type === 'number';

        value = formatNumber(value, allowDot, this.isPositive, this.toFixed);
      }

      const { input } = this.$refs;

      if (input && value !== input.value) {
        input.value = value;
      }

      // if (value !== this.value) {
      //   this.inputVal = value;
      //   this.$emit("input", value);
      // }
      this.inputVal = value;
      this.$emit('input', value);
    },
  },
};
</script>

<style lang="scss" scoped>
.numeric-input-box {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  .numeric-input-inner {
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    box-sizing: border-box;
    color: #606266;
    display: inline-block;
    font-size: inherit;
    height: 30px;
    line-height: 30px;
    text-align: center;
    vertical-align: middle;
    outline: none;
    padding: 0 5px;
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    width: 100%;
    &:focus {
      outline: none;
      border-color: #008dea;
      position: relative;
      z-index: 10;
    }
  }
}
</style>


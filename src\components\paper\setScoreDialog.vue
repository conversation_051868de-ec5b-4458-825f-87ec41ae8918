<!--
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2024-11-22 15:45:29
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2024-11-23 09:58:01
-->
<template>
    <el-dialog class="set-score-dialog" :title="'设置分数'" :visible.sync="showDialog" width="30%"
        :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false" :before-close="closeDialog">
        <div class="set-score-dialog">
            <div class="set-score-dialog-title">
                <span class="title-text">共{{ totalScore }}分</span>
            </div>
            <div class="set-score-dialog-header">
                <span class="title text">题目</span>
                <span class="score text">分值</span>
            </div>
            <div class="set-score-dialog-list" style="height: 50vh;overflow: auto;">
                <template v-for="(item, index) in quesInfos[0].data">
                    <div class="set-score-item-big">
                        <span class="title">{{ item.name }}（{{ item.defaultScore }}分）</span>
                        <span class="score">
                            <el-input v-model="item.averageScore" @change="changeAverageScore(item)"
                                size="mini"></el-input>分/题
                        </span>
                    </div>
                    <template v-if="item.data[0].data">
                        <div class="set-score-item-sques" v-for="(sqitem, sqindex) in item.data[0].data">
                            <span class="sques title">{{ sqitem.name }}</span>
                            <span class="sques score"><el-input v-model="sqitem.score"
                                    @change="changeQScore(item)" size="mini"></el-input>分</span>
                        </div>
                    </template>
                    <template v-else>
                        <div class="set-score-item-ques" v-for="(qitem, qindex) in item.data">
                            <span class="ques title">{{ qitem.name }}</span>
                            <span class="ques score"><el-input v-model="qitem.defaultScore"
                                    @change="changeQScore(item)" size="mini"></el-input>分</span>
                        </div>
                    </template>
                </template>

            </div>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button @click="closeDialog">取消</el-button>
            <el-button @click="submit">确定</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    props: ["showDialog", "quesList"],
    data() {
        return {
            quesInfos: [],
        }
    },
    computed: {
        totalScore() {
            let score = 0;
            this.quesInfos.forEach(item => {
                item.data.forEach(bitem => {
                    bitem.data.forEach((qitem) => {
                        if (qitem.data) {
                            qitem.data.forEach((sqitem) => {
                                score += Number(sqitem.score);
                            })
                        } else {
                            score += Number(qitem.defaultScore);
                        }
                    })
                })
            })
            return score;
        }
    },
    created() {
        let quesinfo = JSON.parse(JSON.stringify(this.quesList));
        quesinfo.forEach(item => {
            item.data.forEach(bitem => {
                this.caclQuesAvarageScore(bitem)
            })
        })
        this.quesInfos = quesinfo;
    },
    methods: {
        caclQuesAvarageScore(ques) {
            let firstScore = 0;
            let isAverage = true;
            let totalScore = 0;
            ques.data.forEach((qitem, qindex) => {
                if (qitem.data) {
                    let qScore = 0;
                    qitem.data.forEach((sqitem, sindex) => {
                        totalScore += Number(sqitem.score);
                        qScore += Number(sqitem.score);
                        if (sindex == 0) {
                            firstScore = sqitem.score;
                        }
                        if (sqitem.score != firstScore) isAverage = false;
                    })
                    qitem.defaultScore = qScore;
                } else {
                    totalScore += Number(qitem.defaultScore);
                    if (qindex == 0) {
                        firstScore = qitem.defaultScore;
                    }
                    if (qitem.defaultScore != firstScore) isAverage = false;
                }
            })
            ques.defaultScore = totalScore;
            ques.averageScore = isAverage ? firstScore : '';
            ques.isAverage = isAverage;
        },
        changeAverageScore(ques) {
            let qScore = 0;
            ques.data.forEach((qitem) => {
                if (qitem.data) {
                    qitem.data.forEach((sqitem) => {
                        sqitem.score = ques.averageScore;
                        qScore += Number(sqitem.score);
                    })
                    qitem.defaultScore = qScore;
                } else {
                    qitem.defaultScore = ques.averageScore;
                    qScore += Number(qitem.defaultScore);
                }
            })
            ques.defaultScore = qScore;
            ques.isAverage = true;
        },
        changeQScore(bques) {
            this.caclQuesAvarageScore(bques)
        },
        closeDialog() {
            this.$emit('close')
        },
        submit() {
            this.$emit('saveScore',this.quesInfos)
        }
    }
}
</script>

<style lang="scss" scoped>
.set-score-dialog {
    .set-score-dialog-header {
        display: flex;
        padding: 0 10px;
        background: #fafbff;
        height: 28px;
        line-height: 28px;

        .text {
            width: 50%;
        }
    }

    .set-score-dialog-list {
        padding: 0 10px;

        .set-score-item-big,
        .set-score-item-ques,
        .set-score-item-sques {
            display: flex;
            line-height: 28px;
            height: 32px;
        }

        .title,
        .score {
            width: 50%;
        }
    }
}
</style>
<style lang="scss">
.set-score-dialog {
    .el-dialog__body {
        padding: 20px;
    }

    .score {
        .el-input {
            width: 50px;
        }
    }
}
</style>
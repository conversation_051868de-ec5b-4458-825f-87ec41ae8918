<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-04-03 11:02:09
 * @LastEditors: 小圆
-->
<template>
  <el-dialog :visible.sync="visible" title="作答原题" width="900px" v-bind="$attrs" v-on="$listeners">
    <div v-if="quesPointSourceList.length" v-loading="loading">
      <div v-for="item in quesPointSourceList" :key="item">
        <el-image :src="item" />
      </div>

      <div v-if="examId" class="score-box">{{ score }}分</div>
      <div v-if="examId" class="evaluate-box" @click="setExamStuEvaluate">
        <div class="evaluate-icon" :class="{ active: state == 1 }">优</div>
        <el-button class="evaluate-btn" type="text">{{ state == 1 ? '取消优秀' : '设置优秀' }}</el-button>
      </div>
    </div>
    <no-data v-else v-loading="loading"></no-data>
  </el-dialog>
</template>

<script lang="ts">
import { getNewQuesPointSourceList } from '@/service/api';
import { setExamStuEvaluate } from '@/service/pexam';
import { Component, Prop, Ref, Vue } from 'vue-property-decorator';
import NoData from '@/components/noData.vue';
@Component({
  components: {
    NoData,
  },
})
export default class QuesPointDialog extends Vue {
  /** 考试ID */
  @Prop({ default: () => null }) examId: string;
  /** 作业ID */
  @Prop({ default: () => null }) workId: string;
  /** 学科ID */
  @Prop({ default: () => null }) subjectId: string;
  /** 原题题号 */
  @Prop({ default: () => null }) quesNo: string;
  /** 唯一题号 */
  @Prop({ default: () => null }) tQuesNo: string;
  /** 学生ID */
  @Prop({ default: () => null }) stuId: string;
  /** 学生学号 */
  @Prop({ default: () => null }) stuNo: string;
  /** 学生得分 */
  @Prop({ default: () => null }) score: number;

  /** 是否显示原卷 */
  visible = false;
  /** 作答原题 */
  quesPointSourceList: string[] = [];
  /** 状态 */
  state = 0; // 0取消 1优秀作答
  /** 是否加载中 */
  loading = false;

  mounted() {
    this.visible = true;
    this.getQuesPointSourceList();
  }

  // 获取作答原题
  async getQuesPointSourceList() {
    this.loading = true;
    const res = await getNewQuesPointSourceList({
      schoolId: this.$sessionSave.get('schoolInfo').id,
      workId: this.workId,
      quesNo: this.quesNo,
      tQuesNo: this.tQuesNo,
      stuId: this.stuId,
      stuNo: this.stuNo,
    });

    this.quesPointSourceList = res.data?.images || [];
    this.state = res.data?.evaluateState || 0;
    this.loading = false;
  }

  // 设置优秀作答
  async setExamStuEvaluate() {
    let state = this.state == 0 ? 1 : 0;
    const res = await setExamStuEvaluate({
      examId: this.examId,
      subjectId: this.subjectId,
      stuId: this.stuId,
      quesNo: this.quesNo,
      tQuesNo: this.tQuesNo,
      evaluateState: state,
    });
    this.$notify({
      title: '设置评价',
      message: state == 0 ? '取消优秀作答成功！' : '设置优秀作答成功！',
      type: 'success',
      duration: 3000,
    });
    this.state = state;
  }
}
</script>

<style lang="scss" scoped>
.evaluate-box {
  position: absolute;
  bottom: 80px;
  left: 50px;

  .evaluate-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
    font-weight: 700;
    line-height: 50px;
    text-align: center;

    border-radius: 50%;
    background: #fff;
    border: 1px solid #dcdfe6;
    color: #4688ff;
    cursor: pointer;
    z-index: 1;

    &.active {
      background: #1fcdaa;
      color: #fff;
    }

    &:hover {
      background: #1fcdaa;
      color: #fff;
    }
  }

  .evaluate-btn {
    font-size: 16px;
  }
}

.score-box {
  position: absolute;
  top: 10px;
  right: 50px;
  width: 70px;
  height: 70px;
  line-height: 70px;

  text-align: center;
  font-size: 18px;
  font-weight: 700;
  color: red;

  background: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 50%;
  z-index: 1;
}
</style>

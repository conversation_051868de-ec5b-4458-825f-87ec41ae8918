<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-04-29 16:44:45
 * @LastEditors: 小圆
-->
<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';

@Component({
  components: {},
})
export default class Index extends Mixins(TableCommon) {
  tableLeftFixed: any[] = ['className'];

  render(h) {
    const table = this.renderTable(h);
    return table;
  }
}
</script>

<style scoped lang="scss"></style>

<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-04-02 17:30:29
 * @LastEditors: 小圆
-->
<template>
  <div class="composition-analyse">
    <div class="composition-analyse-back" @click="goBack">
      <span>
        <i class="el-icon-arrow-left"></i>
        {{ $sessionSave.get('reportDetail').examName }}
      </span>
    </div>

    <div class="composition-analyse-header">
      <el-select v-model="query.abPaper" placeholder="请选择AB卷" @change="changeAbPaper" v-if="abPaperList.length">
        <el-option v-for="item in abPaperList" :key="item.value" :label="item.text" :value="item.value"></el-option>
      </el-select>

      <el-select v-model="query.classId" placeholder="请选择班级" @change="changeClass">
        <el-option v-for="item in classListGetter" :key="item.id" :label="item.class_name" :value="item.id"></el-option>
      </el-select>

      <el-select v-model="curQues" placeholder="请选择题目" value-key="tQuesNo" @change="changeQues">
        <el-option v-for="item in quesList" :key="item.tQuesNo" :label="`${item.quesNoDesc}`" :value="item">
        </el-option>
      </el-select>

      <div class="header__serarch" v-if="subjectCenterCode == 'ENGLISH'">
        <el-input
          class="search__text"
          placeholder="输入学号或姓名搜索"
          v-model="searchValue"
          @keyup.enter.native="handleFilterStu"
          clearable
        >
        </el-input>
        <div class="search__icon el-icon-search" @click="handleFilterStu"></div>
      </div>
    </div>

    <div class="composition-analyse-content" :class="{ 'p-0': subjectCenterCode != 'ENGLISH' }">
      <div class="composition-analyse-aside stu-ul" v-if="subjectCenterCode == 'ENGLISH'">
        <div
          v-for="item in stuList"
          :key="item.stuId"
          class="stu-li"
          :class="{ active: item.stuId == curStu.stuId }"
          @click="changeStu(item)"
        >
          {{ item.stuName }}
        </div>
      </div>

      <div
        class="composition-analyse-main"
        v-if="curStu"
        :class="{ 'overflow-visible': subjectCenterCode != 'ENGLISH' }"
      >
        <div class="stu-info" v-if="subjectCenterCode == 'ENGLISH'">
          <div>
            <span class="stu-name">{{ curStu?.stuName }}</span>

            <span class="stu-score">
              得分/总分 <span class="score">{{ curStu?.score }}</span
              >/{{ compositionQues?.fullScore }}
            </span>
          </div>

          <div>
            <el-button
              type="text"
              :loading="exportLoading"
              @click="downloadStuReport"
              v-if="subjectCenterCode == 'ENGLISH'"
            >
              <i class="el-icon-download"></i>
              下载学生报告
            </el-button>
          </div>
        </div>

        <CompositionAnalyseDetail
          v-if="subjectCenterCode == 'ENGLISH'"
          ref="compositionAnalyseDetail"
          class="composition-analyse-detail"
          :examId="$sessionSave.get('reportDetail').examId"
          :workId="workId"
          :subjectId="query.subjectId"
          :stuId="curStu?.stuId"
          :stuNo="curStu?.stuNo"
          :classId="query.classId"
          :quesNo="curQues.quesNo"
          :tQuesNo="curQues.tQuesNo"
          :score="curStu?.score"
        ></CompositionAnalyseDetail>

        <iframe
          v-if="subjectCenterCode == 'CHINESE'"
          class="chinese-composition-analyse-detail"
          width="100%"
          height="100%"
          :src="zuowenSrc"
          frameborder="0"
        ></iframe>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Ref, Vue } from 'vue-property-decorator';

import CompositionAnalyseDetail from '@/pages/lookReport/compositionAnalyse/compositionAnalyseDetail.vue';
import { getCompositionQsAPI, getCompositionQsnosAPI } from '@/service/pexam';
import { getSubjectCenterCode } from '@/utils/UserRoleUtils';
import {
  CompositionExportType,
  exportCompositionAPI,
  StuCompositionParams,
} from './lookReport/compositionAnalyse/CompositionExportUtil';
import { getExamStuCorrectDetailAPI } from '@/service/api';
import { getToken } from '@/service/auth';

@Component({
  components: {
    CompositionAnalyseDetail,
  },
})
export default class CompositionAnalyse extends Vue {
  @Ref('compositionAnalyseDetail') private compositionAnalyseDetail!: CompositionAnalyseDetail;

  // 查询参数
  query = {
    subjectId: this.$route.query.subjectId,
    abPaper: this.$route.query.abPaper,
    tQuesNo: this.$route.query.tQuesNo,
    stuId: this.$route.query.stuId,
    classId: this.$route.query.classId,
  };

  // ab卷列表
  abPaperList = [];
  // 班级列表
  classList = [];
  get classListGetter() {
    if (this.query.abPaper !== '') {
      return this.classList.filter(
        item =>
          item.abPaper !== null && item.abPaper !== undefined && item.abPaper.split(',').includes(this.query.abPaper)
      );
    }
    return this.classList;
  }
  // 题目列表
  quesList = [];
  // 学生列表
  stuList = [];
  // 当前作文题目信息
  compositionQues = null;
  // 当前题目
  curQues = null;
  // 当前学生信息
  curStu = null;
  // 搜索值
  searchValue = '';
  // 导出loading
  exportLoading = false;
  // 学科中心编码
  subjectCenterCode = '';
  // 作文报告地址
  zuowenSrc = '';

  // 当前workId
  get workId() {
    let subjectId = this.query.subjectId;
    let subjectList = this.$sessionSave.get('innerNoRoleSubjectList');
    let subject = subjectList.find(item => item.id == subjectId);
    if (subject) {
      return subject.workIds[Number(this.query.abPaper)];
    }
    return '';
  }

  async mounted() {
    this.subjectCenterCode = getSubjectCenterCode(this.query.subjectId as string) || 'ENGLISH';
    this.getClassList();
    await this.getQuesList();
    await this.getCompositionQues();
    this.getZuoWenSrc();

    this.$nextTick(() => {
      document.querySelector('.stu-li.active')?.scrollIntoView({ behavior: 'smooth' });
    });
  }

  // 获取作文地址
  async getZuoWenSrc() {
    if (this.subjectCenterCode != 'CHINESE') return;

    const res = await getExamStuCorrectDetailAPI({
      schoolId: this.$sessionSave.get('schoolInfo').id,
      workId: this.workId,
      stuId: this.curStu.stuId,
      quesNo: this.curQues.quesNo,
      tQuesNo: this.curQues.tQuesNo,
      classId: this.query.classId,
    });
    const detail = res.data;
    const importId = detail.stuScore.importId;

    const className = this.classList.find(item => item.id == this.query.classId)?.class_name;
    const url = new URL(process.env.VUE_APP_ZUOWEN + '/correct');
    url.searchParams.set('id', importId);
    url.searchParams.set('title', this.$sessionSave.get('reportDetail').examName);
    url.searchParams.set('classId', this.query.classId as string);
    url.searchParams.set('className', className);
    url.searchParams.set('stuId', this.curStu?.stuId);
    url.searchParams.set('isCheck', '-1');
    url.searchParams.set('isHideMenu', '1');
    url.searchParams.set('userId', this.$sessionSave.get('loginInfo').id);
    url.searchParams.set('token', getToken() || this.$sessionSave.get('loginInfo').token);
    this.zuowenSrc = url.toString();
  }

  // 获取ab卷列表
  async getAbPaperList() {
    let list = [];
    let isShowAPaper = this.classList.some(
      item => item.abPaper !== null && item.abPaper !== undefined && item.abPaper.split(',').includes('0')
    );
    let isShowBpaper = this.classList.some(
      item => item.abPaper !== null && item.abPaper !== undefined && item.abPaper.split(',').includes('1')
    );
    if (isShowAPaper) {
      list.push({
        text: 'A卷',
        value: '0',
      });
    }
    if (isShowBpaper) {
      list.push({
        text: 'B卷',
        value: '1',
      });
    }
    this.abPaperList = list;
  }

  // 获取班级列表
  async getClassList() {
    this.classList = this.$sessionSave.get('innerClassList');
    this.getAbPaperList();
  }

  // 获取题目列表
  async getQuesList() {
    const res = await getCompositionQsnosAPI({
      examId: this.$sessionSave.get('reportDetail').examId,
      subjectId: this.query.subjectId,
      v: this.$sessionSave.get('reportDetail').v,
      abPaper: this.query.abPaper,
    });
    this.quesList = res.data || [];
    this.curQues = this.quesList.find(item => item.tQuesNo == this.$route.query.tQuesNo) || this.quesList[0];
  }

  // 获取作文题目信息
  async getCompositionQues() {
    const res = await getCompositionQsAPI({
      examId: this.$sessionSave.get('reportDetail').examId,
      subjectId: this.query.subjectId,
      classId: this.query.classId,
      abPaper: this.query.abPaper,
      tQuesNo: this.curQues?.tQuesNo,
      v: this.$sessionSave.get('reportDetail').v,
    });
    this.compositionQues = res.data || null;
    this.stuList = this.compositionQues.stuList;
    this.curStu = this.stuList.find(item => item.stuId == this.query.stuId) || this.stuList[0];
  }

  // 更改ab卷
  async changeAbPaper(value) {
    this.query.abPaper = value;
    this.query.classId = this.classListGetter[0].id;
    await this.getQuesList();
    await this.getCompositionQues();
    this.$nextTick(() => {
      this.compositionAnalyseDetail?.getExamStuCorrectDetail();
      this.getZuoWenSrc();
      this.scrollToTop();
    });
  }

  // 更改学生
  changeStu(item) {
    this.curStu = item;
    this.$nextTick(() => {
      this.compositionAnalyseDetail?.getExamStuCorrectDetail();
      this.getZuoWenSrc();
      this.scrollToTop();
    });
  }

  // 更改班级
  async changeClass(item) {
    this.query.classId = item;
    await this.getCompositionQues();
    this.$nextTick(() => {
      this.compositionAnalyseDetail?.getExamStuCorrectDetail();
      this.getZuoWenSrc();
      this.scrollToTop();
    });
  }

  // 更改题目
  async changeQues(item) {
    this.curQues = item;
    await this.getCompositionQues();
    this.$nextTick(() => {
      this.compositionAnalyseDetail?.getExamStuCorrectDetail();
      this.getZuoWenSrc();
      this.scrollToTop();
    });
  }

  // 搜索学生
  handleFilterStu() {
    if (this.searchValue) {
      this.stuList =
        this.compositionQues?.stuList.filter(
          item => item.stuNo.includes(this.searchValue) || item.stuName.includes(this.searchValue)
        ) || [];
    } else {
      this.stuList = this.compositionQues?.stuList || [];
    }
  }

  // 滚动到顶部
  scrollToTop() {
    document.querySelector('.composition-analyse-main').scrollTop = 0;
  }

  // 下载学生报告
  async downloadStuReport() {
    const params: StuCompositionParams = {
      type: CompositionExportType.STUDENT,
      stuNo: this.curStu?.stuNo,
      stuName: this.curStu?.stuName,
      gradeName: this.$sessionSave.get('reportDetail').gradeName,
      className: this.classList.find(item => item.id == this.query.classId)?.class_name,
      quesNo: this.curQues?.quesNo,
      tQuesNo: this.curQues?.tQuesNo,
      schoolId: this.$sessionSave.get('schoolInfo').id,
      userId: this.$sessionSave.get('loginInfo').id,
      workId: this.workId,
      stuId: this.curStu?.stuId,
      classId: this.query.classId as string,
      token: this.$sessionSave.get('loginInfo').token,
      test: process.env.VUE_APP_BASE_API == 'https://test.iclass30.com' ? '1' : '0',
    };

    this.exportLoading = true;
    try {
      const url = await exportCompositionAPI(params);
      window.open(url, '_blank');
    } catch (error) {
      console.log(error);
      this.$message.error('导出失败');
    } finally {
      this.exportLoading = false;
    }
  }

  // 返回
  goBack() {
    this.$router.push('/home/<USER>/compositionClassSituation');
  }
}
</script>

<style scoped lang="scss">
.composition-analyse {
  display: flex;
  flex-direction: column;
}

.composition-analyse-back {
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  cursor: pointer;
}

.composition-analyse-header {
  display: flex;
  gap: 10px;
}

.composition-analyse-content {
  flex: 1;
  display: flex;
  margin-top: 10px;
  padding: 16px;
  background-color: #f5f7fa;
  overflow: hidden;
}

.composition-analyse-aside {
  width: 220px;
  height: 100%;
  padding: 20px;
  overflow: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.composition-analyse-main {
  flex: 1;
  width: 0;
  overflow: auto;
}

.stu-ul {
  overflow: auto;
}

.stu-li {
  padding: 0 28px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 16px;
  color: #3f4a54;
  background-color: #fff;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
  border-radius: 8px;

  // 添加过渡效果和激活状态
  transition: all 0.3s ease;

  &:hover {
    background-color: #f5f7fa;
  }

  &.active {
    color: #2574ff;
    background-color: #f0f5f9;
    font-weight: 500;
  }
}

.stu-info {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #3f4a54;
  line-height: 36px;
  margin: 0 20px;
  margin-bottom: 10px;
  padding: 10px 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

  .stu-name {
    font-size: 15px;
    font-weight: bold;
  }

  .stu-score {
    margin-left: 20px;
  }

  .score {
    font-size: 24px;
    font-weight: bold;
    color: #f56c6c;
  }
}

.header__serarch {
  display: flex;
  width: 220px;
  .search__icon {
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 18px;
    color: #fff;
    background: #409eff;
    border-radius: 0 3px 3px 0;
    outline: none;
    cursor: pointer;
    text-align: center;
  }
}

.composition-analyse-detail {
  padding: 0;
  margin: 0 20px;
}

.chinese-composition-analyse-detail {
  padding: 0px;
  width: 100%;
  height: 100%;
}

.overflow-visible {
  overflow: visible;
}

.p-0 {
  padding: 0 !important;
}
</style>

/*
 * @Description:
 * @Author: 小圆
 * @Date: 2024-03-21 17:14:55
 * @LastEditors: 小圆
 */

import { RouteConfigSingleView } from 'vue-router/types/router';
import FilterModule from './FilterModule';

// 发布订阅事件类型
export type FilterEvent = 'changeFilter' | 'exportTable';

// 筛选字段列表
export type FilterDataKey = keyof typeof FilterModule.filterData;

export interface menuOption extends RouteConfigSingleView {
  title: string;
  filters?: FilterDataKey[];
}

// 学校对象类型
export interface ISubjectInfo {
  personalBookId?: string;
  subjectId: number;
  subjectName: string;
  classType?: number;
  classIds?: string;
  classNames?: string;
  progress?: number;
  progressState?: number;
  num?: number;
}

export interface IScoreSubjectInfo {
  id: number;
  name: string;
  phaseId: number;
  xfId: number;
  xkId: number;
  progress: number;
  progressState: number;
  code: string | number;
  status: number;
  personalBookId: string | number;
  testBankId: string | number;
}

// 班级对象类型
export interface IClassInfo {
  grdId: string;
  grdName: string;
  id: string;
  class_name: string;
  sort: number;
}

// 年级对象类型
export interface IGradeInfo {
  gradeCode: string;
  gradeName: string;
}

// 考试报告对象类型
export interface IExamReportInfo {
  createUserId: string;
  progressState: number;
  year: number;
  yqExamId: string;
  classNames: string;
  paperList: Paper[];
  source: number;
  categoryName: string;
  subjectId: string;
  createRealName: string;
  scoreRuleId: string;
  analysisMode: number;
  comprehensiveSubjectIds: string;
  fileUrl: string;
  scoreConfirm: number;
  subjectName: string;
  gradeName: string;
  statState: string;
  schoolTermId: string;
  classIds: string;
  examName: string;
  dataState: number;
  persRecoveryType: number;
  dateModified: string;
  categoryCode: string;
  examDateTime: string;
  schoolYearId: string;
  isPublish: number;
  comprehensiveSubjectNames: string;
  paperNo: string;
  examId: number;
  clzType: string;
  progress: number;
  gradeCode: string;
  classType: string;
  v: number;
}

// 对比考试类型
export interface IContrastExamInfo {
  examId: number;
  examName: string;
  examTime: string;
  examType: string;
  subjectIds: string;
  subjectNames: string[];
}

// 学生类型
export interface IStudentInfo {
  id: string;
  name: string;
  uName: string;
}

export interface ISchoolInfo {
  id: string;
  schoolId: string;
  schoolName: string;
}

// 数据表格数据类型
export interface dataType {
  [x: string]: string;
}

interface Paper {
  importScoreState: number;
  testBankId: string;
  progressState: number;
  subectName: string;
  processState: number;
  subectId: string;
  submitNum: number;
  isExamUse: number;
  isMarkingPapers: number;
  realScanNum: number;
  problemPaperCount: number;
  isTeachingSchedule: number;
  isUploadWord: number;
  bindQueState: number;
  needAudit: boolean;
  errorNum: number;
  progressList: Progress[];
  checkTeacherId: string;
  workId: string;
  handleCompleteCount: number;
  completeNum: number;
  paperNo: string;
  progress: number;
  personBookId: string;
  workingState: number;
}

interface Progress {
  id: string;
  examId: number;
  relationId: string;
  schoolId: string;
  progress: number;
  progressName: string;
  progressState: number;
}

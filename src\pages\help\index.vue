<!--
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>@class30.com
 * @Date: 2024-05-10 17:41:24
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-02-13 16:56:25
-->
<template>
    <div class="help-container">
        <div class="help-catalogue-left">
            <el-tree :data="data" :props="defaultProps" @node-click="handleNodeClick" node-key="id"
             :highlight-current="true" :default-expand-all="true">
                <div class="custom-tree-node" slot-scope="{ node, data }">
                    <span class="tree-label">{{ node.label }}</span>
                    <span v-if="hasAdminPower" class="tree-btn">
                        <el-button type="text" size="mini" @click.stop="() => appendNode(data)">
                            添加
                        </el-button>
                        <el-button type="text" size="mini" @click.stop="() => editNode(data)">
                            编辑
                        </el-button>
                        <el-button type="text" size="mini" @click.stop="() => removeNode(node, data)">
                            移除
                        </el-button>
                    </span>
                </div>
            </el-tree>
            <div v-if="hasAdminPower" class="add-catalogue" @click.stop="() => appendNode()">+ 添加分类</div>
        </div>
        <div class="help-content-right">
            <router-view />
        </div>
        <edit-help-node v-if="isShowEdit" :data="currentNode" :isEdit="isEdit" @submit="submitNode"
            @close="isShowEdit = false"></edit-help-node>
    </div>
</template>

<script>
import editHelpNode from './components/editHelpNode.vue';
import { getHelpCategoryListAPI, saveHelpCategoryAPI, editHelpCategoryAPI, deleteHelpCategoryAPI } from '@/service/phelp';
import { getUserRoleIdListAPI } from '@/service/api';
export default {
    name: 'Help',
    components: {
        editHelpNode
    },
    data() {
        return {
            data: [],
            defaultProps: {
                children: 'data',
                label: 'title'
            },
            hasAdminPower:false,
            isShowEdit: false,
            currentNode: null,
            isEdit: false,
        };
    },
    async created() {
        this.hasAdminPower = await this.checkUserRole(this.$sessionSave.get('loginInfo').id, '266'); // 266为扫描监管权限
        this.$sessionSave.set('hasAdminPower',this.hasAdminPower);
        this.getHelpCategoryList();
    },
    methods: {
        /**
         * @description: 检查用户权限
         * @param {*} userId 用户ID
         * @param {*} role 权限类型
         * @return {Boolean} 是否有权限
         */
         async checkUserRole (userId, role) {
            try {
                const res = await getUserRoleIdListAPI({ userId });
                if (res.code == 1) {
                const list = res.data;
                const roleId = list.find(item => item == role);
                return !!roleId;
                }
                return false;
            } catch (error) {
                return false;
            }
        },
        async getHelpCategoryList() {
            let res = await getHelpCategoryListAPI({});
            if (res.code == 1) {
                this.data = res.data;
                const getIndexData = (data) => {
                    if(data.data.length){
                        return getIndexData(data.data[0])
                    }else{
                        return data
                    }
                }
                if(!this.$route.query.categoryId){
                    this.handleNodeClick(getIndexData(categoryId))
                }
            }
        },
        async editHelpCategory() {
            let params = {
                "id": this.currentNode.id,
                "sort": this.currentNode.sort,
                "parentId": this.currentNode.parentId,
                "title": this.currentNode.title,
                "content": this.currentNode.content || "",
                "data": this.currentNode.data
            }
            let res = await editHelpCategoryAPI(params).catch(err => {
                this.getHelpCategoryList();
            });
        },
        async addHelpCategory(newChild) {
            let params = {
                "sort": newChild.sort,
                "parentId": newChild.parentId,
                "title": newChild.title,
                "content": newChild.content || "",
            }
            let res = await saveHelpCategoryAPI(params).catch(err => {
            });
            this.getHelpCategoryList();
        },
        appendNode(data) {
            this.currentNode = data;
            this.isEdit = false;
            this.isShowEdit = true;
        },
        editNode(data) {
            this.currentNode = data;
            this.isEdit = true;
            this.isShowEdit = true;
        },

        removeNode(node, data) {
            this.$confirm("确定删除 <strong>" + data.title + "</strong> 吗？", "提示", {
                dangerouslyUseHTMLString: true
            }).then(async () => {
                let res = await deleteHelpCategoryAPI({ id: data.id }).catch(err => {
                });
                if (res.code == 1) {
                    const parent = node.parent;
                    const children = parent.data.data || parent.data;
                    const index = children.findIndex(d => d.id === data.id);
                    children.splice(index, 1);
                }
            });
        },
        submitNode(info) {
            if (this.isEdit) {
                this.currentNode.title = info.title;
                this.currentNode.sort = info.sort;
                this.currentNode.content = info.desc;
                this.editHelpCategory();
            } else {
                const newChild = { parentId: this.currentNode?.id || "", title: info.title,sort:info.sort, content: info.desc };
                if (this.currentNode) {
                    if (!this.currentNode.data) {
                        this.$set(this.currentNode, 'data', []);
                    }
                    this.currentNode.data.push(newChild)
                } else {
                    this.data.push(newChild)
                }
                this.addHelpCategory(newChild);
            }
        },
        handleNodeClick(data) {
            this.$router.replace({
                path: '/help/list',
                query: {
                    categoryId: data.id
                }
            })
        }
    }
}
</script>

<style scoped lang="scss">
.help-container {
    width: 100%;
    height: 100%;

    .help-catalogue-left {
        display: inline-block;
        width: 300px;
        height: 100%;
        background: #fff;
        vertical-align: top;

        .custom-tree-node {
            .tree-label {
                font-size: 16px;
            }

            .tree-btn {
                margin-left: 35px;
            }
        }

        .add-catalogue {
            text-align: center;
            cursor: pointer;
            margin: 0 10px;
            height: 36px;
            line-height: 36px;
            background-color: #F5F7FA;

            &:hover {
                background-color: #e9f0fb;
            }
        }
    }

    .help-content-right {
        width: calc(100% - 310px);
        height: 100%;
        margin-left: 10px;
        display: inline-block;
        background: #fff;
        vertical-align: top;
    }
}
</style>
<style lang="scss">
.help-container {
    .el-tree {
        margin: 10px;
        height: calc(100% - 20px);
        overflow: auto;

        .el-tree-node__content {
            height: 36px;

            .el-tree-node__label {
                font-size: 16px;
            }
        }
    }
}
</style>
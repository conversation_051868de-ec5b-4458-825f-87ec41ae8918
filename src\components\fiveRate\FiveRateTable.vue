<template>
  <el-table
    class="five-rate-table el-table--group"
    :data="totalData"
    stripe
    ref="fiveRateTable"
    :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
    v-drag-table
    v-sticky-table="0"
    style="width: 100%"
    :key="key"
  >
    <el-table-column
      v-if="queryType == 2"
      align="center"
      fixed
      :label="filterData && filterData.subjectId == '' ? '班主任' : '教师'"
      :show-overflow-tooltip="true"
      width="90"
    >
      <template #default="scope">
        <span>{{ scope.row.teaName ? scope.row.teaName : '--' }}</span>
      </template>
    </el-table-column>

    <el-table-column
      align="center"
      prop="clsName"
      fixed
      :label="type == 'campus' ? '校区' : '班级'"
      :show-overflow-tooltip="true"
      width="100"
    >
    </el-table-column>
    <el-table-column
      v-if="queryType == 1"
      align="center"
      fixed
      :label="type == 'campus' ? '年级主任' : filterData && filterData.subjectId == '' ? '班主任' : '任课老师'"
      :show-overflow-tooltip="true"
      width="90"
    >
      <template #default="scope">
        <span>{{ scope.row.teaName ? scope.row.teaName : '--' }}</span>
      </template>
    </el-table-column>
    <el-table-column align="center" prop="totalNum" label="实考人数" width="90"> </el-table-column>
    <el-table-column align="center" prop="maxScore" label="最高分"></el-table-column>
    <el-table-column align="center" prop="minScore" label="最低分"></el-table-column>
    <el-table-column align="center" label="平均分" class-name="avg-cell">
      <el-table-column align="center" prop="avgScore" sortable label="分数">
        <template #default="scope">
          <span :class="{ red: gradeData && gradeData.avgScore > scope.row.avgScore }">{{ scope.row.avgScore }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="rank" sortable label="排名">
        <template #default="scope">
          <span>{{ formatRank(scope.row.rank) }}</span>
        </template>
      </el-table-column>
    </el-table-column>
    <el-table-column align="center" label="超均率" v-if="checkTypeList.includes('passAvgRate')">
      <el-table-column align="center" label="比率" prop="passAvgRate" sortable>
        <template #default="scope">
          <span v-if="!scope.row.classId">{{ formatRate(formatRank(scope.row.passAvgRate)) }}</span>
          <span v-else :class="{ red: scope.row.passAvgRate < 0 }">{{ formatRate(scope.row.passAvgRate) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="排名" prop="passAvgRank" sortable>
        <template #default="scope">
          <span>{{ formatRank(scope.row.passAvgRank) }}</span>
        </template>
      </el-table-column>
    </el-table-column>
    <el-table-column v-if="contrastId && checkTypeList.includes('growthRate')" align="center" label="增长率">
      <el-table-column align="center" label="比率" prop="growthRate" sortable>
        <template #default="scope">
          <span v-if="!scope.row.classId">{{ formatRate(formatRank(scope.row.growthRate)) }}</span>
          <span v-else :class="{ red: scope.row.growthRate < 0 }">{{ formatRate(scope.row.growthRate) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="排名" prop="growthRank" sortable>
        <template #default="scope">
          <span>{{ formatRank(scope.row.growthRank) }}</span>
        </template>
      </el-table-column>
    </el-table-column>
    <el-table-column align="center" label="优秀率" v-if="checkTypeList.includes('fineRate')">
      <el-table-column align="center" label="人数" prop="fineNum" sortable> </el-table-column>
      <el-table-column align="center" label="比率" prop="fineRate" sortable>
        <template #default="scope">
          <span>{{ formatRate(scope.row.fineRate) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="排名" prop="fineRank" sortable>
        <template #default="scope">
          <span>{{ formatRank(scope.row.fineRank) }}</span>
        </template>
      </el-table-column>
    </el-table-column>
    <el-table-column align="center" label="优良率" v-if="checkTypeList.includes('goodRate')">
      <el-table-column align="center" label="人数" prop="goodNum" sortable> </el-table-column>
      <el-table-column align="center" label="比率" prop="goodRate" sortable>
        <template #default="scope">
          <span>{{ formatRate(scope.row.goodRate) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="排名" prop="goodRank" sortable>
        <template #default="scope">
          <span>{{ formatRank(scope.row.goodRank) }}</span>
        </template>
      </el-table-column>
    </el-table-column>
    <el-table-column align="center" label="及格率" v-if="checkTypeList.includes('passRate')">
      <el-table-column align="center" label="人数" prop="passNum" sortable> </el-table-column>
      <el-table-column align="center" label="比率" prop="passRate" sortable>
        <template #default="scope">
          <span>{{ formatRate(scope.row.passRate) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="排名" prop="passRank" sortable>
        <template #default="scope">
          <span>{{ formatRank(scope.row.passRank) }}</span>
        </template>
      </el-table-column>
    </el-table-column>
    <el-table-column align="center" label="不及格率" v-if="checkTypeList.includes('failRate')">
      <el-table-column align="center" label="人数" prop="failNum" sortable> </el-table-column>
      <el-table-column align="center" label="比率" prop="failRate" sortable>
        <template #default="scope">
          <span>{{ formatRate(scope.row.failRate) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="排名" prop="failRank" sortable>
        <template #default="scope">
          <span>{{ formatRank(scope.row.failRank) }}</span>
        </template>
      </el-table-column>
    </el-table-column>
    <el-table-column align="center" label="低分率" v-if="checkTypeList.includes('lowRate')">
      <el-table-column align="center" label="人数" prop="lowNum" sortable> </el-table-column>
      <el-table-column align="center" label="比率" prop="lowRate" sortable>
        <template #default="scope">
          <span>{{ formatRate(scope.row.lowRate) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="排名" prop="lowRank" sortable>
        <template #default="scope">
          <span>{{ formatRank(scope.row.lowRank) }}</span>
        </template>
      </el-table-column>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'FiveRateTable',
  props: ['type', 'tableData', 'filterData', 'contrastId', 'queryType', 'checkTypeList'],
  data() {
    return {
      totalData: [],
      key: 0,
    };
  },
  watch: {
    tableData: {
      handler(val) {
        this.totalData = this.$deepClone(val);
        this.key++;
      },
      deep: true,
    },
  },
  computed: {
    // 年级数据
    gradeData() {
      return this.tableData.find(item => item.classId === '');
    },
  },
  mounted() {
    this.totalData = this.$deepClone(this.tableData);
  },
  methods: {
    // 格式化比率
    formatRate(val) {
      if (val == '--') return val;
      if (typeof val === 'undefined') return '--';
      return val + '%';
    },

    // 格式化排名
    formatRank(val) {
      if (!val) return '--';
      return val;
    },

    formatterCellVal(row, column, cellValue, index) {
      // console.log(row, cellValue, Boolean(cellValue), typeof cellValue);
      if (cellValue === undefined) {
        return '-';
      }
      return cellValue;
    },

    // // 排序
    // sortChange({ prop, order }) {
    //   this.pagination.page = 1;
    //   if (order === 'ascending') {
    //     this.sortType = prop;
    //   } else if (order === 'descending') {
    //     this.sortType = '-' + prop;
    //   } else {
    //     this.sortType = '';
    //   }
    //   this.getReportCard();
    // },

    // 按照平均分排序
    sortTableByAvg({ prop, order }) {
      let tableData = this.totalData,
        headData = {};

      if (order == 'ascending') {
        // 降序
        tableData = tableData.sort((a, b) => a.average - b.average);
      } else if (order == 'descending') {
        // 升序
        tableData = tableData.sort((a, b) => b.average - a.average);
      } else {
        this.totalData = this.$deepClone(this.tableData);
      }
      tableData.map((item, index) => {
        if (item.classId == '年级') {
          headData = item;
          tableData.splice(index, 1);
        }
        return item;
      });
      tableData.unshift(headData);
    },
  },
};
</script>

<style lang="scss" scoped>
.red {
  color: red;
}
</style>

<style lang="scss">
.five-rate-table {
  .avg-cell.is-sortable {
    .cell {
      height: 28px;
    }
  }

  .el-table__fixed {
    height: 100% !important;
  }
}
</style>

<template>
  <div class="errexam-container">
    <el-page-header @back="backReport" content="待处理异常"> </el-page-header>
    <div class="scan-task-list-header">
      <span style="color: red">温馨提示：手阅考试处理完异常需要发布成绩。</span>
      <el-button type="" class="search-button" @click="getList('refresh')">刷新 </el-button>
    </div>
    <div class="scan-error-list-box" id="scan-error-list-box" v-if="dataList.length > 0">
      <!--扫描批次数据-->
      <el-table class="custome-table" :data="dataList" v-loading="listLoading" style="width: 100%" stripe
        :height="height">
        <el-table-column prop="title" label="考试名">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.title" placement="top">
              <span class="showOverTooltip">{{ scope.row.title }} </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="source" label="类型" width="100">
          <template slot-scope="scope">
            <span>{{ sourceTypeName(scope.row.source) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="examDate" label="考试时间" width="180"></el-table-column>
        <el-table-column prop="gradeName" label="年级"></el-table-column>
        <el-table-column prop="subjectName" label="学科"></el-table-column>
        <el-table-column prop="scanNum" label="已扫" width="120"></el-table-column>
        <el-table-column prop="errorCount" label="异常数" width="120">
          <template slot-scope="scope">
            <span class="task_error_num" :class="{ error_no_zero: scope.row.errorCount > 0 }">{{
              scope.row.errorCount
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <div v-if="scope.row.id">
              <el-button :disabled="scope.row.source == 6" size="mini" type="text"
                @click="handleError(scope.$index, scope.row)">处理异常
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination class="list-pagination" background layout="total, prev, pager, next, jumper"
        :current-page="pageInfo.current" @current-change="onPageChange" :page-size="pageInfo.pageSize"
        :total="pageInfo.total">
      </el-pagination>
    </div>
    <div class="nodata flex_1" v-else>
      <img :src="noResImg" alt="" />
      <p class="text-center">暂无数据</p>
    </div>
    <el-backtop target=".scrollContainer"></el-backtop>
  </div>
</template>

<script>
import { getErrorExamNums } from '@/service/pexam';
import { dateFormat } from '@/utils/dateUtil';
import { gradeMap } from '@/utils/common';
import UserRole from '@/utils/UserRole';

export default {
  data() {
    return {
      dataList: [],
      listLoading: true,
      params: {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        type: 1, //0：全部，1：有异常数据，2：无异常数据,3:无考试信息
        className: null,
        schoolName: null,
        title: null,
        page: 1,
        pageSize: 50,
      },
      pageInfo: {
        current: 1,
        total: 0,
        pageSize: 50,
      },
      subjectList: [],
      subjectMap: {},
      grdMap: gradeMap(),
      height: 'calc(100% - 50px)',
      isFirst: false,
      errExamTimer: null,
      noResImg: require('@/assets/no-res.png'),
    };
  },
  computed: {
    sourceTypeName() {
      return (source) => {
        return source == 4 ? '网阅' : source == 3 ? '手阅' : source == 6 ? '拍改' : ''
      }
    }
  },
  created() { },
  async mounted() {
    this.subjectList = this.$localSave.get('SUBJECT_LIST');
    this.subjectMap = {};
    this.subjectList.forEach(it => {
      this.subjectMap[it.id] = it.name;
    });
    this.params.schoolId = this.$sessionSave.get('schoolInfo').id;
    //非校领导和运营人员只能查看自己任教班级的异常数据
    if (!UserRole.isSchoolLeader && !UserRole.isOperation) {
      let classList = await UserRole.getClassList({ classType: -1 });
      this.params.classIdList = classList.map(it => it.classId).join(',');
      let subjectIds = [
        ...new Set(
          UserRole.substituteClassList
            .map(item => {
              return String(item.subjectId);
            })
        ),
      ];
      this.params.subjectIdList = subjectIds.join(',');
    }
    this.params.page = 1;
    clearTimeout(this.errExamTimer);
    this.getList();
    this.isFirst = true;
  },
  async activated() {
    this.params.schoolId = this.$sessionSave.get('schoolInfo').id;
    if (!this.isFirst) {
      return;
    }
    clearTimeout(this.errExamTimer);
    await this.getList();
  },
  beforeDestroy() {
    clearTimeout(this.errExamTimer);
  },
  deactivated() {
    clearTimeout(this.errExamTimer);
  },
  methods: {
    /***
     * @name:返回上一页
     */
    backReport() {
      this.$router.back();
    },
    /**
     * @name:获取列表list
     */
    getList(type) {
      clearTimeout(this.errExamTimer);
      this.listLoading = true;
      if (type == 'refresh') this.params.page = 1;
      getErrorExamNums(this.params).then(res => {
        this.listLoading = false;
        if (res.code !== 1) {
          this.$message.error(res.msg || '发生错误');
        } else {
          res.data.list.forEach(it => {
            it.classCount = 0;
            if (it.scanList) {
              it.classCount = it.scanList.length;
              it.classNames = it.scanList.map(it => it.className).join(',');
            }
            it.date_str = dateFormat(it.dateCreated).substring(0, 10);
            let dateExam = it.dayExam.toString();
            let year = dateExam.substring(0, 4),
              month = dateExam.substring(4, 6),
              day = dateExam.substring(6, 8);
            // 返回格式化后的日期字符串
            it.examDate = `${year}-${month}-${day}`;
          });
          this.dataList = res.data.list;
          this.dataList.forEach(item => {
            if (this.subjectMap[item.subjectId]) {
              item.subjectName = this.subjectMap[item.subjectId];
            }
            item.gradeName = this.grdMap[item.gradeId];
          });
          // console.log(this.dataList);
          // this.getErrorNums();
          this.pageInfo.total = res.data.total;
          this.pageInfo.current = res.data.current;
          if (!this.checkState(this.dataList)) {
            clearTimeout(this.errExamTimer);
            this.errExamTimer = setTimeout(() => {
              this.getErrorNums();
            }, 15000);
          } else {
            this.clearTimer();
          }
        }
      });
    },
    /**
     * @name:获取试卷转换状态
     */
    async getErrorNums() {
      const res = await getErrorExamNums(this.params);
      if (res.code == 1 && res.data.list.length) {
        res.data.list.forEach(item => {
          this.dataList.forEach(ite => {
            if (item.id == ite.id) {
              this.$set(ite, 'scanNum', item.scanNum);
              this.$set(ite, 'errorCount', item.errorCount);
            }
          });
        });
        if (!this.checkState(this.dataList)) {
          clearTimeout(this.errExamTimer);
          this.errExamTimer = setTimeout(() => {
            this.getErrorNums();
          }, 15000);
        } else {
          this.clearTimer();
        }
      }
    },
    /**
     * @name:获取异常数
     */
    checkState(data) {
      let dataLength = data.length;
      let name = this.$route.name;
      for (let i = 0; i < dataLength; i++) {
        let item = data[i];
        if (item.errorCount != 0 && name == 'errExamList') {
          return false;
        }
      }
      return true;
    },
    /**
     * @name：清除定时器
     */
    clearTimer() {
      window.clearTimeout(this.errExamTimer);
      clearTimeout(this.errExamTimer);
      this.errExamTimer = null;
    },
    onPageChange(page) {
      this.params.page = page;
      this.getList();
    },
    handleError(index, item) {
      clearTimeout(this.errExamTimer);
      this.$router.push({
        path: '/scan/errornew',
        query: {
          examId: item.id,
          examName: item.title,
          subjectName: item.subjectName,
          paperNo: item.paperNo,
          schoolId: item.schoolId,
          personBookId: item.id,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.scan-task-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.scan-error-list-box {
  margin-top: 20px;
  border: solid #f2f2f2;
  height: calc(100% - 70px);

  .error_no_zero {
    color: red;
  }
}

.list-pagination {
  padding: 20px;
  margin: auto;
  width: 600px;
}

.showOverTooltip {
  display: -webkit-box;
  position: relative;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
<style lang="scss">
.scan-error-list-box {
  .custome-table {
    .editCell {
      >span {
        color: #409eff;
        display: inline-block;
        margin-right: 10px;
        cursor: pointer;

        &:last-child {
          margin-right: 0;
        }

        &.disabled {
          opacity: 0.6;
          cursor: no-drop;
        }
      }

      .el-icon-more {
        width: 40px;
        text-align: center;
        transform: rotate(90deg);
      }
    }
  }

  .custome-table {
    border: 1px solid #e4e8eb;
  }

  .el-tabs__active-bar {
    display: none;
  }

  .el-tabs__nav-wrap::after {
    display: none;
  }

  .el-tabs__item.is-active {
    font-weight: bold;
  }

  .el-table thead {
    color: #606266;
    font-size: 16px;
  }

  .el-table th,
  .el-table td {
    text-align: center;
  }

  .el-table th.el-table__cell {
    background-color: #f5f7fa !important;
  }

  .el-table th.el-table__cell>.cell {
    padding-left: 20px !important;
    padding-right: 20px !important;
  }

  .el-table .el-table__row .cell {
    padding-left: 20px !important;
    padding-right: 20px !important;
  }

  .el-table {
    .el-button {
      width: 32px;
      height: 32px;
      padding: unset !important;
    }
  }
}
</style>
var MathjaxHelper;
(function () {
  MathjaxHelper = {
    toMathJax: function (latex) {
      const mapping = {
        // MathLive 格式  →  MathJax 格式
        "\\text{\\textbackslash}": "\\text{\\}",
        "\\text{\\textbraceleft}": "\\{",
        "\\text{\\textbraceright}": "\\}",
        "\\text{\\textasciicircum}": "\\text{^}",
        "\\text{\\textasciitilde}": "\\text{~}",
        "\\text{\\textsterling}": "\\text{£}",
        "\\exponentialE": "\\mathrm{e}",
        "\\imaginaryI": "\\mathrm{i}",
        "\\imaginaryJ": "\\mathrm{j}",
        "\\differentialD": "\\mathrm{d}",
        "\\partialD": "\\partial",
        "\\implies": "\\Rightarrow",
        "\\impliedby": "\\Leftarrow",
        "\\iff": "\\Leftrightarrow",
      };
      let result = latex;
      for (const [ml, mj] of Object.entries(mapping)) {
        const reg = new RegExp(ml.replace(/\\/g, "\\\\"), "g");
        result = result.replace(reg, mj);
      }
      return result;
    }
  };
})();
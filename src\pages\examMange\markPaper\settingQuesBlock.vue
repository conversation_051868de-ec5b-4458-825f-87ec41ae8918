<template>
  <div class="exam-mk-container">
    <bread-crumbs :title="'题块框选'"></bread-crumbs>
    <div class="ques-setting-container">
      <div class="ques-setting-left">
        <div class="ques-setting-left-top">
          <div class="top-tip">操作提示：按住鼠标左键，选择题块对应区域</div>
          <div v-if="abCardType == IAB_CARD_TYPE.abCard" class="top-ab">
            <el-radio-group v-model="paperNo" @change="changePaperNo" size="small">
              <el-radio-button :label="abPaperNo[0]">A卡</el-radio-button>
              <el-radio-button :label="abPaperNo[1]">B卡</el-radio-button>
            </el-radio-group>
          </div>
          <div v-if="pageCount > 1" class="top-page">
            <div
              :class="['page', currPage == page - 1 ? 'active' : '']"
              v-for="page in pageCount"
              @click="currPage = page - 1"
            >
              第{{ page }}页
            </div>
          </div>
        </div>
        <div class="exam-pdf-container">
          <div
            @mousedown="mousedown"
            @mousemove="mousemove"
            @mouseup="mouseup"
            @mouseleave="mouseup"
            :class="[pageLayout == IPAGELAYOUT.A4 ? 'a4' : (pageLayout == IPAGELAYOUT.A3 ? 'a3' : 'a33')]"
          >
            <img class="img" :src="pageUrl" />
            <div class="ques_box draw" :style="drawBoxStyle"></div>
            <template v-for="(item, index) in drawBox[currPage]">
              <div
                :class="['ques_box', item.active ? 'active' : '', item.isEdit ? 'edit' : '']"
                :did="item.id"
                :style="[
                  {
                    left: item.x + 'px',
                    top: item.y + 'px',
                    width: item.w + 'px',
                    height: item.h + 'px',
                  },
                ]"
              >
                <div v-if="item.active" class="ques-edit" @click.stop="editPoint(item)">编辑</div>
                <div class="ques-cancel" @click.stop="deletePoint(item)">删除</div>
                <div class="notop ellipsis">
                  {{ item.title }}
                </div>
                <div class="nobottom">
                  <el-dropdown
                    :ref="'dropRef' + item.id"
                    @command="
                      ques => {
                        handleCommand(ques, item);
                      }
                    "
                  >
                    <div class="el-dropdown-link">
                      <div class="ellipsis">{{ item.title }}</div>
                      <i class="el-icon-arrow-down el-icon--right"></i>
                    </div>
                    <el-dropdown-menu slot="dropdown" class="custom-drop-menu">
                      <template v-for="ques in quesInfos">
                        <template v-if="ques.isMerge || (ques.isSplit && ques.scoreMode == 3)">
                          <el-dropdown-item v-if="isSubjectiv(ques.typeId)" :command="ques">{{
                            ques.quesNos
                          }}</el-dropdown-item>
                        </template>
                        <template v-else>
                          <template v-if="ques.data">
                            <template v-for="qs in ques.data">
                              <template v-if="qs.isMerge || (qs.isSplit && qs.scoreMode == 3)">
                                <el-dropdown-item v-if="isSubjectiv(qs.typeId)" :command="qs">{{
                                  qs.quesNos
                                }}</el-dropdown-item>
                              </template>
                              <template v-else>
                                <template v-if="qs.data">
                                  <template v-for="sq in qs.data">
                                    <el-dropdown-item v-if="isSubjectiv(sq.typeId) && !sq.isHide" :command="sq">{{
                                      sq.quesNos
                                    }}</el-dropdown-item>
                                  </template>
                                </template>
                                <template v-else>
                                  <el-dropdown-item v-if="isSubjectiv(qs.typeId) && !qs.isHide && isChooseDo(qs)" :command="qs">
                                    <template v-if="qs.isChooseDo">
                                      {{ qs.chooseName.join(',') }}({{qs.chooseIds.length}}选{{qs.doCount}})
                                    </template>
                                    <template v-else>
                                      {{ qs.quesNos }}
                                    </template>
                                  </el-dropdown-item>
                                </template>
                              </template>
                            </template>
                          </template>
                          <template v-else>
                            <el-dropdown-item v-if="isSubjectiv(ques.typeId) && !ques.isHide" :command="ques">{{
                              ques.quesNos
                            }}</el-dropdown-item>
                          </template>
                        </template>
                      </template>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
                <template v-if="item.isEdit">
                  <div class="ques-save" @click="savePoint(item)">保存</div>
                  <div class="resize-dot top"></div>
                  <div class="resize-dot bottom"></div>
                  <div class="resize-dot left"></div>
                  <div class="resize-dot right"></div>
                  <div class="resize-dot top-left"></div>
                  <div class="resize-dot top-right"></div>
                  <div class="resize-dot bottom-left"></div>
                  <div class="resize-dot bottom-right"></div>
                </template>
              </div>
            </template>
          </div>
        </div>
      </div>
      <div class="ques-setting-right">
        <div class="ques-setting-right-top">
          <div class="top-title">题块框选</div>
          <div class="top-tip">
            <div class="tip-block"></div>
            <span>已框选 </span>
            <div class="tip-block-blue"></div>
            <span>未框选</span>
          </div>
        </div>
        <div class="right-ques-list">
          <template v-for="ques in quesInfos">
            <template v-if="ques.isMerge || (ques.isSplit && ques.scoreMode == 3)">
              <div
                v-if="isSubjectiv(ques.typeId)"
                :class="[
                  'ques-title',
                  (matchQues.includes(ques.firstSmallId)||matchQues.includes(ques.id)) ? '' : 'unselect',
                  currQues?.firstSmallId == ques.firstSmallId ? 'active' : '',
                ]"
                @click="selectQues(ques)"
              >
                {{ ques.quesNos }}
              </div>
            </template>
            <template v-else>
              <template v-if="ques.data">
                <template v-for="qs in ques.data">
                  <template v-if="qs.isMerge || (qs.isSplit && qs.scoreMode == 3)">
                    <div
                      v-if="isSubjectiv(qs.typeId)"
                      :class="[
                        'ques-title',
                        (matchQues.includes(qs.firstSmallId)||matchQues.includes(qs.id)) ? '' : 'unselect',
                        currQues?.firstSmallId == qs.firstSmallId ? 'active' : '',
                      ]"
                      @click="selectQues(qs)"
                    >
                      {{ qs.quesNos }}
                    </div>
                  </template>
                  <template v-else>
                    <template v-if="qs.data">
                      <template v-for="sq in qs.data">
                        <div
                          v-if="isSubjectiv(sq.typeId) && !sq.isHide"
                          :class="[
                            'ques-title',
                            matchQues.includes(sq.id) ? '' : 'unselect',
                            currQues?.id == sq.id ? 'active' : '',
                          ]"
                          @click="selectQues(sq)"
                        >
                          {{ sq.quesNos }}
                        </div>
                      </template>
                    </template>
                    <template v-else>
                      <div
                        v-if="isSubjectiv(qs.typeId) && !qs.isHide && isChooseDo(qs)"
                        :class="[
                          'ques-title',
                          matchQues.includes(qs.id) ? '' : 'unselect',
                          currQues?.id == qs.id ? 'active' : '',
                        ]"
                        @click="selectQues(qs)"
                      >
                      <template v-if="qs.isChooseDo">
                        {{ qs.chooseName.join(',') }}({{qs.chooseIds.length}}选{{qs.doCount}})
                      </template>
                      <template v-else>
                        {{ qs.quesNos }}
                      </template>
                      </div>
                    </template>
                  </template>
                </template>
              </template>
              <template v-else>
                <div
                v-if="isSubjectiv(ques.typeId) && !ques.isHide"
                  :class="[
                    'ques-title',
                    matchQues.includes(ques.id) ? '' : 'unselect',
                    currQues?.id == ques.id ? 'active' : '',
                  ]"
                  @click="selectQues(ques)"
                >
                  {{ ques.quesNos }}
                </div>
              </template>
            </template>
          </template>
        </div>
        <div class="right-ques-hand">
          <span style="color: red;font-size: 14px;margin-right: 20px;">完成框选后请点击保存</span>
          <el-button @click="saveData" type="primary">保存</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BreadCrumbs from '@/components/Breadcrumbs/index.vue';
import { getViewPaper, updateViewPaper } from '@/service/testbank.js';
import { mmConversionPx, pxConversionMm } from '@/utils/common';
import { getQueryString } from '@/utils';
import { generateUUID } from '@/utils/index.js';
import { getPaperImages } from '@/service/xueban';
import { IAB_CARD_TYPE, ICARD_TYPE, IPAGELAYOUT } from '@/typings/card';

const HAND_TYPE = {
  DRAW: 0,
  MOVE: 1,
  LEFT: 2,
  TOP: 3,
  RIGHT: 4,
  BOTTOM: 5,
  LEFT_TOP: 6,
  RIGHT_TOP: 7,
  LEFT_BOTTOM: 8,
  RIGHT_BOTTOM: 9,
};
export default {
  name: 'setting-ques-block',
  data() {
    return {
      IPAGELAYOUT,
      IAB_CARD_TYPE,
      paperNo: getQueryString('paperNo') || '101678',
      abCardType: IAB_CARD_TYPE.default,
      abPaperNo:[],
      mainPaperNo: "",
      pagePoints: [],
      drawBox: [[]],
      quesInfos: [],
      //区域匹配题目
      matchQues: [],
      currPage: 0,
      pageCount: 0,
      cardType: ICARD_TYPE.BLANKCARD,
      pageLayout: 1,
      currQues: null,
      groupMap: new Map(),
      handType: HAND_TYPE.DRAW,
      drawBoxStyle: {
        display: 'none',
      },
      start: {
        offsetLeft: 0,
        offsetTop: 0,
        x: 0,
        y: 0,
      },
      end: {
        x: 0,
        y: 0,
      },
      isDrag: false,
      dragBox: null,
      pageUrl: '',
      imgList: [],
    };
  },
  watch: {
    currPage: {
      handler(newval, oldval) {
        // this.pageUrl = `https://fs.iclass30.com/scan/pdf/${
        //   this.paperNo
        // }_${this.getPageLayoutTxt()}/${newval}.png`;
        this.pageUrl = this.imgList[newval];
        document.getElementsByClassName('exam-pdf-container')[0].scrollTop = 0;
      },
    },
  },
  components: {
    BreadCrumbs,
  },
  async created() {
    this.initData();
  },
  mounted() {},
  methods: {
    async initData(){
      this.drawBox = [];  
      this.matchQues = [];
      this.currPage = 0;
      this.currQues = null;
      this.groupMap = new Map();
      this.dragBox = null;
      this.pageUrl = '';
      this.imgList = [];
      await this.getPaperData();
      await this.getImgUrls();
    },
    isChooseDo(ques){
      return !ques.isChooseDo || this.cardType == ICARD_TYPE.QUESCARD || (ques.isChooseDo  && ques.targetIds.includes(ques.id))
    },
    changePaperNo(val){
      this.paperNo = val;
      this.initData();
    },
    /**
     * @name:获取生成的pdf图片
     */
    async getImgUrls() {
      const params = {
        paper_no: this.abCardType == IAB_CARD_TYPE.abPaper ? this.mainPaperNo : this.paperNo,
        show_answer: false,
      };
      const res = await getPaperImages(params);
      if (res.code == 1) {
        this.imgList = res.data;
        this.pageUrl = this.imgList[0];
      } else {
        this.imgList = [];
      }
    },
    mmConvertPx(point) {
      if (this.pageLayout == IPAGELAYOUT.A4) {
        return mmConversionPx(point);
      } else {
        return (mmConversionPx(point) / 4) * 3;
      }
    },
    pxConvertMm(point) {
      if (this.pageLayout == IPAGELAYOUT.A4) {
        return pxConversionMm(point);
      } else {
        return (pxConversionMm(point) / 3) * 4;
      }
    },
    isSubjectiv(typeid) {
      return ![1, 2, 8, '1', '2', '8'].includes(typeid);
    },
    getPageLayoutTxt() {
      if (this.pageLayout == IPAGELAYOUT.A4) {
        return 'A4';
      } else {
        return 'A3';
      }
    },
    getQuesTitle(ques){
      if(ques.isChooseDo){
        return ques.chooseName.join(',') + '(' + ques.chooseIds.length + '选' + ques.doCount + ')';
      }else{
        return ques.quesNos;
      }
    },
    converPoints2Draw(ques, qid) {
      if ([1, 2, 8, '1', '2', '8'].includes(ques.typeId) || (ques.isHide && !ques.isMerge)) return;
      ques.points?.forEach(point => {
        if (!this.drawBox[point.page]) {
          this.drawBox[point.page] = [];
        }
        this.matchQues.push(qid);
        this.drawBox[point.page].push({
          id: generateUUID(),
          qid: qid,
          title: this.getQuesTitle(ques),
          active: false,
          x: this.mmConvertPx(point.pos[0]),
          y: this.mmConvertPx(point.pos[1]),
          w: this.mmConvertPx(point.pos[2]),
          h: this.mmConvertPx(point.pos[3]),
        });
      });
    },
    converQuesInfos() {
      this.quesInfos.forEach(bq => {
        if (bq.isMerge) {
          this.groupMap.set(bq.quesNo, bq);
          this.converPoints2Draw(bq, bq.firstSmallId);
        }else if(bq.isSplit && bq.scoreMode == 3){
          this.groupMap.set(bq.quesNo, bq);
          this.converPoints2Draw(bq, bq.id);
        } else {
          if (bq.data) {
            bq.data.forEach(sq => {
              if (sq.isMerge) {
                this.groupMap.set(sq.quesNo, sq);
                this.converPoints2Draw(sq, sq.firstSmallId);
              } else if(sq.isSplit && sq.scoreMode == 3){
                this.groupMap.set(sq.quesNo, sq);
                this.converPoints2Draw(sq, sq.id);
              }else {
                if (sq.data) {
                  sq.data.forEach(qs => {
                    if (qs.isMerge) {
                      this.groupMap.set(qs.quesNo, qs);
                      this.converPoints2Draw(qs, qs.firstSmallId);
                    }else {
                      this.groupMap.set(qs.quesNo, qs);
                      this.converPoints2Draw(qs, qs.id);
                    }
                  });
                } else if (!sq.isHide){
                  this.groupMap.set(sq.quesNo, sq);
                  this.converPoints2Draw(sq, sq.id);
                }
              }
            });
          } else if (!bq.isHide){
            this.groupMap.set(bq.quesNo, bq);
            this.converPoints2Draw(bq, bq.id);
          }
        }
      });
    },
    async getPaperData() {
      let params = { paperNo: this.paperNo,type:1 };
      let res = await getViewPaper(params);
      if (res.code == 1) {
        const cardInfo = JSON.parse(res.data.cardInfo);
        this.pageLayout = cardInfo.pageLayout;
        this.pagePoints = cardInfo.points.pages;
        this.pageCount = res.data.paperNum;
        this.cardType = cardInfo.cardType;
        this.quesInfos = JSON.parse(res.data.teamInfo);
        this.abCardType = res.data.abCardType;
        this.abPaperNo = res.data.abPaperNo.split(',');
        this.mainPaperNo = res.data.mainPaperNo;
        this.converQuesInfos();
      }
    },
    selectQues(info) {
      this.currQues = info;
      let id = info.isMerge ? info.firstSmallId : info.id;
      this.drawBox.forEach(page => {
        page.forEach(item => {
          if (item.qid == id) {
            item.active = true;
          } else {
            item.active = false;
          }
        });
      });
      this.$forceUpdate();
    },
    setDrawBoxStyle() {
      this.drawBoxStyle = {
        left: this.start.x + 'px',
        top: this.start.y + 'px',
        width: this.end.x + 'px',
        height: this.end.y + 'px',
      };
    },
    clearDrawBoxStyle() {
      this.start = {
        x: 0,
        y: 0,
      };
      this.end = {
        x: 0,
        y: 0,
      };
      this.drawBoxStyle = {
        display: 'none',
      };
    },
    /**
     * @name 获取矩阵重叠面积
     */
    getOverlapArea(rect, rectQ) {
      let left = Math.max(rect.x, rectQ.x);
      let right = Math.min(rect.w, rectQ.w);
      let top = Math.max(rect.y, rectQ.y);
      let bottom = Math.min(rect.h, rectQ.h);
      if (right - left < 0 || bottom - top < 0) return 0;
      return (right - left) * (bottom - top);
    },
    getQuesByPoint(rect) {
      let ques = new Map();
      this.pagePoints[this.currPage].forEach(point => {
        let x = this.mmConvertPx(point.pos[0]);
        let y = this.mmConvertPx(point.pos[1]);
        let w = this.mmConvertPx(point.pos[0] + point.pos[2]);
        let h = this.mmConvertPx(point.pos[1] + point.pos[3]);
        let rectQ = { x: x, y: y, w: w, h: h };
        let overlap =
          this.getOverlapArea(rect, rectQ) /
          (this.mmConvertPx(point.pos[2]) * this.mmConvertPx(point.pos[3]));
        if (overlap) {
          let group = this.groupMap.get(point.question_no);
          let id = group.isMerge ? group.firstSmallId : group.id;
          if (ques.get(id)) {
            overlap += ques.get(id)?.overlap;
          }
          ques.set(id, { overlap, title: this.getQuesTitle(group), id: id });
        }
      });
      let quesList = Array.from(ques);
      quesList = quesList.sort((a, b) => {
        return b[1].overlap - a[1].overlap;
      });
      return quesList[0];
    },
    deletePoint(item) {
      this.drawBox[this.currPage] = this.drawBox[this.currPage].filter(info => {
        return info.id != item.id;
      });
      if (item.qid) {
        this.matchQues.splice(this.matchQues.indexOf(item.qid), 1);
      }
      this.$forceUpdate();
    },
    editPoint(item) {
      item.isEdit = true;
      this.dragBox = this.drawBox[this.currPage].filter(info => {
        return info.id == item.id;
      })[0];
      this.$forceUpdate();
    },
    savePoint(item) {
      item.isEdit = false;
      this.$forceUpdate();
    },
    handleCommand(ques, item) {
      if (item.qid) {
        this.matchQues.splice(this.matchQues.indexOf(item.qid), 1);
      }

      let id = ques.isMerge ? ques.firstSmallId : ques.id;
      item.qid = id;
      item.title = this.getQuesTitle(ques);
      this.matchQues.push(id);
      this.$forceUpdate();
    },
    mousedown(e) {
      e.preventDefault();
      let boxRect = e.currentTarget.getBoundingClientRect();
      let targ = e.srcElement;
      if (targ.classList.contains('top')) {
        this.handType = HAND_TYPE.TOP;
      } else if (targ.classList.contains('bottom')) {
        this.handType = HAND_TYPE.BOTTOM;
      } else if (targ.classList.contains('left')) {
        this.handType = HAND_TYPE.LEFT;
      } else if (targ.classList.contains('right')) {
        this.handType = HAND_TYPE.RIGHT;
      } else if (targ.classList.contains('top-left')) {
        this.handType = HAND_TYPE.LEFT_TOP;
      } else if (targ.classList.contains('top-right')) {
        this.handType = HAND_TYPE.RIGHT_TOP;
      } else if (targ.classList.contains('bottom-left')) {
        this.handType = HAND_TYPE.LEFT_BOTTOM;
      } else if (targ.classList.contains('bottom-right')) {
        this.handType = HAND_TYPE.RIGHT_BOTTOM;
      } else if (targ.classList.contains('edit')) {
        this.handType = HAND_TYPE.MOVE;
        this.start.offsetTop = this.dragBox.y - (e.clientY - boxRect.y);
        this.start.offsetLeft = this.dragBox.x - (e.clientX - boxRect.x);
      } else {
        this.handType = HAND_TYPE.DRAW;
      }
      this.start.x = e.clientX - boxRect.x;
      this.start.y = e.clientY - boxRect.y;

      this.isDrag = true;
    },
    mousemove(e) {
      e.preventDefault();
      if (!this.isDrag) return;
      let boxRect = e.currentTarget.getBoundingClientRect();
      const nowX = e.clientX - boxRect.x,
        nowY = e.clientY - boxRect.y;
      const disX = nowX - this.start.x,
        disY = nowY - this.start.y;
      let moveY = 0;
      let moveX = 0;
      switch (this.handType) {
        case HAND_TYPE.TOP:
          moveY = this.dragBox.y - nowY;
          this.dragBox.y = nowY;
          this.dragBox.h = this.dragBox.h + moveY;
          break;
        case HAND_TYPE.BOTTOM:
          moveY = this.dragBox.y + this.dragBox.h - nowY;
          this.dragBox.h = this.dragBox.h - moveY;
          break;
        case HAND_TYPE.LEFT:
          moveX = this.dragBox.x - nowX;
          this.dragBox.x = nowX;
          this.dragBox.w = this.dragBox.w + moveX;
          break;
        case HAND_TYPE.RIGHT:
          moveX = this.dragBox.x + this.dragBox.w - nowX;
          this.dragBox.w = this.dragBox.w - moveX;
          break;
        case HAND_TYPE.LEFT_TOP:
          moveX = this.dragBox.x - nowX;
          moveY = this.dragBox.y - nowY;
          this.dragBox.y = nowY;
          this.dragBox.h = this.dragBox.h + moveY;
          this.dragBox.x = nowX;
          this.dragBox.w = this.dragBox.w + moveX;
          break;
        case HAND_TYPE.LEFT_BOTTOM:
          moveX = this.dragBox.x - nowX;
          moveY = this.dragBox.y + this.dragBox.h - nowY;
          this.dragBox.x = nowX;
          this.dragBox.w = this.dragBox.w + moveX;
          this.dragBox.h = this.dragBox.h - moveY;
          break;
        case HAND_TYPE.RIGHT_TOP:
          moveX = this.dragBox.x + this.dragBox.w - nowX;
          this.dragBox.w = this.dragBox.w - moveX;
          moveY = this.dragBox.y - nowY;
          this.dragBox.y = nowY;
          this.dragBox.h = this.dragBox.h + moveY;
          break;
        case HAND_TYPE.RIGHT_BOTTOM:
          moveY = this.dragBox.y + this.dragBox.h - nowY;
          this.dragBox.h = this.dragBox.h - moveY;
          moveX = this.dragBox.x + this.dragBox.w - nowX;
          this.dragBox.w = this.dragBox.w - moveX;
          break;
        case HAND_TYPE.MOVE:
          moveY = nowY + this.start.offsetTop;
          moveX = nowX + this.start.offsetLeft;
          this.dragBox.y = moveY;
          this.dragBox.x = moveX;
          break;
        case HAND_TYPE.DRAW:
          this.end.x = disX;
          this.end.y = disY;
          this.setDrawBoxStyle();
          break;

        default:
          break;
      }
    },
    async mouseup(e) {
      e.preventDefault();
      this.isDrag = false;
      if (this.handType != HAND_TYPE.DRAW) return;
      if (this.end.x < 50 && this.end.y < 20) {
        this.clearDrawBoxStyle();
        return;
      }
      let uid = generateUUID();
      // try {
        if (!this.drawBox[this.currPage]) {
          this.drawBox[this.currPage] = [];
        }
      //   let ques = this.getQuesByPoint({
      //     x: this.start.x,
      //     y: this.start.y,
      //     w: this.start.x + this.end.x,
      //     h: this.start.y + this.end.y,
      //   })[1];
      //   this.matchQues.push(ques.id);
      //   this.drawBox[this.currPage].push({
      //     id: uid,
      //     qid: ques.id,
      //     title: ques.title,
      //     active: false,
      //     x: this.start.x,
      //     y: this.start.y,
      //     w: this.end.x,
      //     h: this.end.y,
      //   });
      // } catch (e) {
        let id = "";
        let title = "";
        if(this.currQues){
          id = this.currQues.isMerge ? this.currQues.firstSmallId : this.currQues.id;
          title = this.getQuesTitle(this.currQues);
          this.matchQues.push(id);
        }
        
        this.drawBox[this.currPage].push({
          id: uid,
          qid: id,
          title: title,
          active: false,
          x: this.start.x,
          y: this.start.y,
          w: this.end.x,
          h: this.end.y,
        });
      // }
      this.clearDrawBoxStyle();
      setTimeout(() => {
        this.$refs['dropRef' + uid][0].show();
      }, 100);
    },
    checkPointsComplete(ques) {
      if(this.isSubjectiv(ques.typeId)){
        if(!ques.points || !ques.points.length){
          return false;
        }else{
          return true
        }
      }
      return true;
    },
    buildTeamInfo() {
      let isComplete = true;
      let quesMap = new Map();
      this.drawBox.forEach((page, index) => {
        page.forEach(point => {
          if (!quesMap.get(point.qid)) {
            quesMap.set(point.qid, []);
          }
          quesMap.get(point.qid).push({
            page: index,
            pos: [
              this.pxConvertMm(point.x),
              this.pxConvertMm(point.y),
              this.pxConvertMm(point.w),
              this.pxConvertMm(point.h),
            ],
          });
        });
      });
      this.quesInfos.forEach(bq => {
        if (bq.isMerge) {
          bq.points = quesMap.get(bq.firstSmallId);
          isComplete && (isComplete = this.checkPointsComplete(bq))
        } else if (bq.isSplit && bq.scoreMode == 3) {
          bq.points = quesMap.get(bq.id);
          isComplete && (isComplete = this.checkPointsComplete(bq))
        } else {
          if (bq.data) {
            bq.data.forEach(sq => {
              if (sq.isMerge) {
                sq.points = quesMap.get(sq.firstSmallId);
                isComplete && (isComplete = this.checkPointsComplete(sq))
              } else if (sq.isSplit && sq.scoreMode == 3) {
                sq.points = quesMap.get(sq.id);
                isComplete && (isComplete = this.checkPointsComplete(sq))
              } else {
                if (sq.data) {
                  sq.data.forEach(qs => {
                    if (qs.isMerge) {
                      qs.points = quesMap.get(qs.firstSmallId);
                      isComplete && (isComplete = this.checkPointsComplete(qs))
                    } else if (qs.isSplit && qs.scoreMode == 3) {
                      qs.points = quesMap.get(qs.id);
                      isComplete && (isComplete = this.checkPointsComplete(qs))
                    } else if (!qs.isHide) {
                      qs.points = quesMap.get(qs.id);
                      isComplete && (isComplete = this.checkPointsComplete(qs))
                    }
                  });
                } else if (!sq.isHide && this.isChooseDo(sq)) {
                  sq.points = quesMap.get(sq.id);
                  isComplete && (isComplete = this.checkPointsComplete(sq))
                }
              }
            });
          } else if (!bq.isHide) {
            bq.points = quesMap.get(bq.id);
            isComplete && (isComplete = this.checkPointsComplete(bq))
          }
        }
      });
      return isComplete;
    },
    /**
     * @name 保存框选数据
     */
    async saveData() {
      let isComplete = this.buildTeamInfo();
      let cardInfo = {
        paperNo: this.paperNo,
        teamInfo: JSON.stringify(this.quesInfos),
        isCheckBox: isComplete ? 1 : 0,
        notCopyTeamInfo: 1
      };
      let result = await updateViewPaper({ json: JSON.stringify(cardInfo) });
      if (result && result.code == 1) {
        let type = "success";
        let message = "修改成功";
        let offset = 20;
        if(!isComplete){
          type = "warning";
          message = "保存成功，仍有题目框选未完成";
          offset = 400;
        }
        this.$message({
          message: message,
          type: type,
          duration: 3000,
          offset:offset
        });
      } else {
        this.$message({
          message: result.msg,
          type: 'error',
          duration: 1000,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.exam-mk-container {
  padding: 0 !important;
}

.ques-setting-container {
  height: calc(100% - 40px);

  .ques-setting-left {
    position: relative;
    display: inline-block;
    vertical-align: top;
    width: 80%;
    height: 100%;

    .ques-setting-left-top {
      height: 28px;

      .top-tip {
        display: inline-block;
      }
      .top-ab{
        text-align: center;
        margin-top: -30px;
      }

      .top-page {
        position: absolute;
        right: 0;
        display: inline-block;
        z-index: 9;
        div {
          display: inline-block;
        }

        .page {
          width: 60px;
          height: 28px;
          margin-left: 2px;
          line-height: 24px;
          border: 1px solid;
          border-radius: 5px;
          text-align: center;
          background: #fff;
          cursor: pointer;
          &.active {
            color: #5f9eff;
            border: 1px solid #5f9eff;
          }
        }
      }
    }

    .exam-pdf-container {
      width: 100%;
      height: calc(100% - 28px);
      overflow: auto;
      background: #dcdcdc;
      user-select: none;

      .a3,.a33 {
        margin: 0 auto;
        width: 315mm;
        height: 223.4mm;
        position: relative;
        
      }
      .a33{
        .ellipsis{
          max-width: 300px;
        }
      }
      .a3{
        .ellipsis{
          max-width: 500px;
        }
      }

      .a4 {
        margin: 0 auto;
        width: 210mm;
        height: 297mm;
        position: relative;
      }
      .ellipsis{
          max-width: 600px;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          display: inline-block;
        }

      .img {
        // width: 100%;
        height: 100%;
      }

      .ques_box {
        position: absolute;
        border: 1mm solid #03c617;
        font-size: 22px;
        color: #03c617;

        &.draw {
          border: 1mm solid #ff6600;
          color: #ff6600;
        }

        &.active {
          border: 1mm solid #ff6600;
          color: #ff6600;
        }

        .notop {
          position: absolute;
          top: 2px;
          left: 2px;
        }

        .nobottom {
          position: absolute;
          bottom: 2px;
          right: 2px;
          .ellipsis{
            text-align: right;
            line-height: 1;
          }
        }

        .resize-dot {
          position: absolute;
          width: 8px;
          height: 8px;
          border: 1px solid;
          background: #fff;
          box-sizing: border-box;

          &.top {
            cursor: n-resize;
            top: -4px;
            left: 50%;
            margin-left: -4px;
          }

          &.bottom {
            cursor: s-resize;
            bottom: -4px;
            left: 50%;
            margin-left: -4px;
          }

          &.left {
            top: 50%;
            margin-top: -4px;
            cursor: w-resize;
            left: -4px;
          }

          &.right {
            cursor: e-resize;
            right: -4px;
            top: 50%;
            margin-top: -4px;
          }

          &.top-left {
            margin-top: -4px;
            cursor: nw-resize;
            margin-left: -4px;
          }

          &.top-right {
            cursor: ne-resize;
            right: 0;
            margin-right: -4px;
            margin-top: -4px;
          }

          &.bottom-left {
            cursor: sw-resize;
            bottom: 0;
            margin-left: -4px;
            margin-bottom: -4px;
          }

          &.bottom-right {
            cursor: se-resize;
            right: -4px;
            bottom: 0;
            margin-bottom: -4px;
          }
        }

        .ques-save,
        .ques-edit {
          text-align: center;
          width: 40px;
          font-size: 14px;
          background: #fff;
          z-index: 9;
          position: absolute;
          right: 44px;
          top: 4px;
          color: #5f9eff;
          border: 1px solid #5f9eff;
        }

        .ques-cancel {
          text-align: center;
          width: 40px;
          font-size: 14px;
          background: #fff;
          z-index: 9;
          position: absolute;
          right: 4px;
          top: 4px;
          color: #000;
          border: 1px solid rgb(136, 135, 135);
        }
      }
    }
  }

  .ques-setting-right {
    display: inline-block;
    position: relative;
    height: calc(100% - 80px);
    width: 19%;
    margin-left: 1%;
    background: #fff;
    padding: 5px;
    font-weight: bold;

    .ques-setting-right-top {
      font-size: 16px;
      height: 30px;
      line-height: 30px;
      border-bottom: 1px solid #d9d9d9;

      .top-title {
        display: inline-block;
      }

      .top-tip {
        display: inline-block;
        height: 15px;
        position: absolute;
        right: 0;

        .tip-block {
          display: inline-block;
          width: 25px;
          height: 15px;
          background: #029d02;
        }
        .tip-block-blue{
          display: inline-block;
          width: 25px;
          height: 15px;
          background: #409eff;
        }
      }
    }

    .right-ques-list {
      height: calc(100% - 20px);
      font-size: 18px;
      overflow: auto;

      .ques-title {
        color: #029d02;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border-bottom: solid 1px #d9d9d9;
        white-space: nowrap;       /* 文本不换行 */
        overflow: hidden;          /* 隐藏超出部分 */
        text-overflow: ellipsis;   /* 显示省略号 */
        width: 100%;            

        &.unselect {
          color: #5f9eff;
        }

        &.active {
          color: #ff6600;
        }
      }
    }

    .right-ques-hand {
      height: 40px !important;
      text-align: center;
      line-height: 40px !important;
    }
  }
}
</style>
<style lang=scss>
.ques-setting-container .ques-setting-left .el-dropdown-link {
  font-size: 22px;
  color: #03c617;
}

.ques-setting-container .ques-setting-left .active .el-dropdown-link {
  font-size: 22px;
  color: #ff6600;
}

.custom-drop-menu {
  max-height: 400px;
  overflow-y: auto;
  border: 2px solid #ff6600;
  .el-dropdown-menu__item {
    border: 1px solid #ab9b9b;
  }
}
</style>

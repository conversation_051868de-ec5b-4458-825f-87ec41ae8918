/*
 * @Description:
 * @Author: 小圆
 * @Date: 2024-05-29 09:38:10
 * @LastEditors: 小圆
 */

export abstract class AbstractScoreSetting {
  scoreMap = {};

  constructor() {}

  // 初始化学生
  abstract initStu(params: { examId: any; subjectId: any });

  // 初始化学生题目
  abstract initStuQuestion(option: { examId: any; subjectId: any; studentId: any });

  // 学生列表转树
  abstract stuListToTree(stuList);

  // 重新发布成绩
  abstract rePublishScore(option: { examId: any; personalBookId: any; schoolId: any });

  // 设置学生类型
  abstract setStuType(option: { examId; subjectId; studentId; state });

  // 保存方法
  abstract saveCorrect(option: { questionList; cardInfo; examId; subjectId; studentId; images });

  // 设置分数映射
  public setScoreMap(scoreMap) {
    this.scoreMap = scoreMap;
  }

  // 通过班级分组
  protected groupByClzName(arr) {
    return arr.reduce((acc, current) => {
      const key = current.clzName;
      const curGroup = acc[key] ?? [];
      return { ...acc, [key]: [...curGroup, current] };
    }, {});
  }

  // 通过分组转换为Tree
  protected groupToTree(group) {
    let arr = [];
    for (const key in group) {
      if (Object.hasOwnProperty.call(group, key)) {
        arr.push({ label: `${key} (${group[key].length})`, children: group[key] });
      }
    }
    return arr;
  }

  // 获取选择题分数
  protected getObjScore(ques) {
    if (ques.type == 7) {
      return ques.score || 0;
    }
    let score = 0;
    const answerSet = new Set(ques.answer.split(','));
    const rightAnswerSet = new Set(ques.rightAnswer.split('').filter(t=> t != ',')); // 兼容多选题不含有逗号分割
    const isCorrect = this.setsAreEqual(answerSet, rightAnswerSet);
    score = isCorrect ? ques.fullScore : 0;
    const quesNo = String(ques.sort);

    // 如果题目有映射，则重新设置分数
    if (Object.hasOwnProperty.call(this.scoreMap, quesNo)) {
      let hasError = false;
      answerSet.forEach(item => {
        if (!rightAnswerSet.has(item)) {
          answerSet.delete(item);
          hasError = true;
        }
      });
      if (hasError) return 0;
      const map = this.scoreMap[quesNo];
      let optionSum = 0;
      answerSet.forEach(option => {
        optionSum = optionSum + this.getOptionIndex(option);
      });
      score = map[optionSum] || 0;
    }
    return score;
  }

  // 判断Set是否相同
  protected setsAreEqual(set1, set2) {
    if (set1.size !== set2.size) {
      return false;
    }

    for (const item of set1) {
      if (!set2.has(item)) {
        return false;
      }
    }

    return true;
  }

  // 根据题目选项获取题目索引
  protected getOptionIndex(option) {
    const index = option.charCodeAt() - 65;
    return Math.pow(2, index);
  }
}

<template>
  <div class="header-filter__wrapper">
    <!-- 组合榜筛选 -->
    <div class="header__filter display_flex clearfix" v-if="isShowTypeOption">
      <span class="leftText pull-left ft-16">榜单：</span>
      <ul class="class__ul pull-left flex_1">
        <li
          v-for="item in sourceList"
          class="class__li ft-16"
          :class="{ active: filterData.source === item.value }"
          :key="item.value"
          @click="changeSource(item.value)"
        >
          {{ item.label }}
        </li>
      </ul>
    </div>

    <!-- 学科筛选 -->
    <div class="header__filter display_flex clearfix" v-if="isShowSubjectOption">
      <span class="leftText pull-left ft-16">学科：</span>
      <ul class="class__ul pull-left flex_1">
        <template v-if="subjectList.length > 0">
          <li
            class="class__li ft-16"
            :class="{ active: filterData.subjectId === item.id }"
            v-for="item in subjectList"
            :key="item.id"
            @click="changeFilter('subject', item, false)"
          >
            {{ item.name }}
          </li>
        </template>
        <template v-else>
          <li class="class__li ft-16">无</li>
        </template>
      </ul>
    </div>

    <!-- AB卷筛选 -->
    <div class="header__filter display_flex clearfix" v-if="isShowAbPaperOption && abPaperList.length > 0">
      <span class="leftText pull-left ft-16">类型：</span>
      <ul class="class__ul pull-left flex_1">
        <li
          class="class__li ft-16"
          v-for="item in abPaperList"
          :key="item.value"
          :class="{ active: filterData.abPaper === item.value }"
          @click="changeAbPaper(item.value)"
        >
          {{ item.text }}
        </li>
      </ul>
    </div>

    <!-- 班级筛选 -->
    <div class="header__filter display_flex clearfix" v-if="isShowClassOption">
      <span class="leftText pull-left ft-16" :class="{ moreClass: isMultiClass }">班级：</span>
      <ul class="class__ul pull-left flex_1" v-if="!isMultiClass">
        <template v-if="classList.length > 0">
          <li
            class="class__li ft-16"
            :class="{
              active: filterData.classId === item.id || (!filterData.classId && item.id === ''),
            }"
            v-for="item in classList"
            :key="item.id"
            @click="changeFilter('class', item, false)"
          >
            {{ item.class_name }}
          </li>
        </template>
        <template v-else>
          <li class="class__li ft-16">无</li>
        </template>
      </ul>
      <ul class="class__ul pull-left flex_1" v-else>
        <li
          class="class__li moreClass ft-16"
          :class="{
            moreActive: filterData.classIds.indexOf(item.id) !== -1,
            allActive: !filterData.classIds.join(',') && item.id === '',
          }"
          v-for="item in classList"
          :key="item.id"
          @click="addClass(item)"
        >
          {{ item.class_name }}
          <i class="el-icon-close" @click.stop="cancelClass(item)"></i>
        </li>
      </ul>
    </div>

    <!-- 组合学科筛选 -->
    <div class="header__filter display_flex clearfix" v-if="filterData.source == 1">
      <span class="leftText pull-left ft-16">组合：</span>
      <ul class="class__ul pull-left flex_1">
        <template v-if="groupSubjectList.length > 0">
          <li
            v-for="item in groupSubjectList"
            class="class__li ft-16"
            :class="{ active: filterData.aliasName === item.aliasName }"
            :key="item.id"
            @click="changeAliasName(item)"
          >
            {{ item.aliasName }}
          </li>
        </template>
        <template v-else>
          <li class="class__li ft-16">无</li>
        </template>
      </ul>
    </div>

    <!-- 分数来源筛选 -->
    <div class="header__filter display_flex clearfix" v-if="isShowQTypeOption">
      <span class="leftText pull-left ft-16">分数来源：</span>
      <ul class="class__ul pull-left flex_1">
        <li
          class="class__li ft-16"
          v-for="item in qTypeList"
          :key="item.value"
          :class="{ active: filterData.qType === item.value }"
          @click="changeQType(item.value)"
        >
          {{ item.label }}
        </li>
      </ul>
    </div>

    <!-- 英语作文智批题号筛选 -->
    <div class="header__filter display_flex clearfix" v-if="isShowCompositionQuesOption">
      <span class="leftText pull-left ft-16">题号：</span>
      <ul class="class__ul pull-left flex_1">
        <li
          v-for="item in compositionQuesList"
          class="class__li ft-16"
          :key="item.tQuesNo"
          :class="{ active: filterData.quesInfo?.tQuesNo == item.tQuesNo }"
          @click="changeCompositionQues(item)"
        >
          {{ item.quesNoDesc }}
        </li>
      </ul>
    </div>

    <!-- 底部线 -->
    <div class="bottomLine"></div>
  </div>
</template>

<script>
import {
  clzListAPI,
  getCompositionQsAPI,
  getCompositionQsnosAPI,
  getGroupSubject,
  listExamSubject,
} from '@/service/pexam';
import UserRole from '@/utils/UserRole';
import { FuncIdEnum, getExamReportClassList, getExamReportSubjectList, getFuncEnable } from '@/utils/examReportUtils';
import { findIntersection, getToRoles } from '@/utils/index';
import { noPermissionMenuList } from './ExamReport/constant';

export default {
  name: 'header-filter',
  data() {
    return {
      // 考试报告班级列表
      examClassList: [],
      // 考试报告学科列表
      examSubjectList: [],
      // 没有权限的考试学科列表
      noRoleExamSubjectList: [],
      // 没有权限限制的考试报告班级列表
      noRoleExamClassList: [],
      // 当前选中的筛选项数据
      filterData: {
        classId: '',
        classIds: [],
        subjectId: '',
        phaseId: '',
        xfId: '',
        classList: [],
        aliasName: '', // 组合学科
        qType: 0, // 0:得分 1：赋分
        source: 0, // 0:成绩榜 1：组合成绩榜
        abPaper: '', // -1:普通 0:A卷 1:B卷
        quesInfo: null, // 作文题 {classId: '', quesId: '', quesNo: '', tQuesNo: '', quesNoDesc: ''}
      },
      subClassList: [],
      // 榜单列表
      sourceList: [
        {
          label: '成绩榜',
          value: 0,
        },
        {
          label: '组合成绩榜',
          value: 1,
        },
      ],
      // 来源列表
      qTypeList: [
        {
          label: '得分',
          value: 0,
        },
        {
          label: '赋分',
          value: 1,
        },
      ],
      // ab卷类型列表
      abPaperList: [],
      // 组合学科列表
      groupSubjectList: [],
      // 是否显示AB卷
      isShowAbPaper: false,

      // 作文题号列表
      compositionQuesList: [],
    };
  },
  computed: {
    // 学科列表
    subjectList() {
      let arr = JSON.parse(JSON.stringify(this.examSubjectList));
      if (this.isShowCompositionQuesOption) {
        arr = arr.filter(item => item.isSmart == 1);
      }

      if (this.$route.path.includes('grammarAnalysis')) {
        let schSubList = UserRole.userInfo.schSubList;
        arr = arr.filter(item => {
          return schSubList.some(t => t.id == item.id && t.center_code == 'ENGLISH');
        });
      }

      if (this.canAddAllSubItem) arr.unshift(this.allSubjectItem);
      return arr;
    },

    // 班级列表
    classList() {
      if (!this.subjectList.length) return [];
      let noRoleExamClassList = JSON.parse(JSON.stringify(this.noRoleExamClassList));
      let examClassList = JSON.parse(JSON.stringify(this.examClassList));
      let arr = this.isNoRoleClass ? noRoleExamClassList : examClassList;
      if (this.filterData.abPaper !== '') {
        arr = arr.filter(
          item =>
            item.abPaper !== null &&
            item.abPaper !== undefined &&
            item.abPaper.split(',').includes(this.filterData.abPaper)
        );
      }
      if (this.canAllClass) {
        let subjectItem = this.subjectList.find(item => item.id == this.filterData.subjectId);
        arr.unshift({
          id: '',
          class_name: '全部',
          intersectionRoles: findIntersection(subjectItem?.intersectionRoles || [], [1, 2, 3, 4]),
        });
      }
      return arr;
    },

    // 全部学科对象
    allSubjectItem() {
      let roles = getToRoles();
      let subRoles = findIntersection(roles, [1, 2, 5]).map(t => Number(t));
      let moduleRoles = this.getModuleRoles();
      let intersectionRoles = findIntersection(subRoles, moduleRoles).map(t => Number(t));
      let isRule = this.examSubjectList.some(item => item.isRule);
      return {
        id: '',
        name: '全部',
        phaseId: '',
        xfId: '',
        roles: subRoles,
        intersectionRoles: intersectionRoles,
        isRule: isRule,
      };
    },

    // 是否可以显示全部学科
    canAddAllSubItem() {
      const path = this.$route.path;
      let isAllSubjectPublished = this.getAllSubjectByRolePublished([1, 2, 5]);
      let isCanAllSubBySource = this.$sessionSave.get('reportDetail').source != 101;
      if (
        // 校管，年级主任，班主任，运营
        (path.includes('campusComparison') ||
          path.includes('overview') ||
          path.includes('fiveRate') ||
          path.includes('subjectComparison') ||
          path.includes('gradeDistribute') ||
          path.includes('topAndDiff') ||
          path.includes('online') ||
          path.includes('limitStu') ||
          path.includes('cardHome') ||
          path.includes('cardDetail') ||
          path.includes('scoreSection') ||
          path.includes('totalRank') ||
          path.includes('boxplot')) &&
        this.examSubjectList.length > 1 &&
        isCanAllSubBySource &&
        isAllSubjectPublished
      ) {
        return true;
      }
      return false;
    },

    // 是否可以显示全部班级
    canAllClass() {
      const path = this.$route.path;
      let canAllRole = false;
      // 判断当前学科所拥有角色
      let subjectItem = this.subjectList.find(item => item.id == this.filterData.subjectId);
      let intersectionRoles = subjectItem?.intersectionRoles || [];
      canAllRole =
        UserRole.isOperation ||
        intersectionRoles.includes(1) ||
        intersectionRoles.includes(2) ||
        intersectionRoles.includes(3) ||
        intersectionRoles.includes(4);
      if (
        !path.includes('paperComment') &&
        !path.includes('cardDetail') &&
        !path.includes('compositionClassSituation') &&
        !path.includes('grammarAnalysis') &&
        canAllRole
      ) {
        return true;
      }
      return false;
    },

    // 是否桌面
    isDesktop() {
      return this.$route.path.includes('/dReport');
    },

    // 是否查看组合榜
    isCanGroupSubject() {
      return this.getAllSubjectByRolePublished([1, 2]);
    },

    // 是否放开权限限制
    isNoRoleClass() {
      let path = this.$route.path;
      if (path.includes('/answerDetail')) {
        return getFuncEnable({ funcId: FuncIdEnum.AnswerAll, subjectId: this.filterData.subjectId });
      }
      return false;
    },

    // 是否显示AB卷
    isShowAbPaperOption() {
      let path = this.$route.path;
      return (
        path.includes('cardHome') ||
        path.includes('cardDetail') ||
        path.includes('scoreSheet') ||
        path.includes('questionAnalysis') ||
        path.includes('paperComment') ||
        path.includes('qualityReport') ||
        path.includes('bothWayReport') ||
        path.includes('quesTypeAvg') ||
        path.includes('answerDetail') ||
        path.includes('knowledgeAnalyze') ||
        path.includes('compositionClassSituation') ||
        path.includes('referenceSample') ||
        path.includes('lexicalAnalysis') ||
        path.includes('grammarAnalysis')
      );
    },
    // 是否显示全部AB卷
    isShowAbPaperAllOption() {
      let path = this.$route.path;
      return path.includes('cardHome');
    },

    // 是否显示榜单类型
    isShowTypeOption() {
      const path = this.$route.path;
      return path.includes('cardHome') && this.groupSubjectList.length;
    },

    // 是否显示学科选项
    isShowSubjectOption() {
      const path = this.$route.path;
      return !path.includes('subjectComparison') && !path.includes('lexicalAnalysis') && this.filterData.source != 1;
    },

    // 是否显示班级选项
    isShowClassOption() {
      const path = this.$route.path;
      return !(
        path.includes('campusComparison') ||
        path.includes('gradeDistribute') ||
        path.includes('topAndDiff') ||
        path.includes('limitStu') ||
        path.includes('online') ||
        path.includes('fiveRate') ||
        path.includes('scoreSection') ||
        path.includes('totalRank') ||
        path.includes('rankSection') ||
        path.includes('boxplot') ||
        path.includes('qualityReport') ||
        path.includes('bothWayReport') ||
        path.includes('quesTypeAvg') ||
        path.includes('smallQuesReport') ||
        path.includes('referenceSample')
      );
    },

    // 是否显示分数来源选项
    isShowQTypeOption() {
      let subjectItem = this.subjectList.find(item => item.id == this.filterData.subjectId);
      let isRule = subjectItem?.isRule;
      let pathList = [
        'campusComparison',
        'cardHome',
        'fiveRate',
        'topAndDiff',
        'scoreSection',
        'totalRank',
        'boxplot',
        'online',
      ];
      let isCardHome = this.$route.path.includes('cardHome');
      let onlyLv = this.$sessionSave.get('onlyLv');
      return (
        (isRule && pathList.some(item => this.$route.path.includes(item)) && (isCardHome ? !onlyLv : true)) ||
        this.filterData.source == 1
      );
    },

    // 是否显示作文题号筛选
    isShowCompositionQuesOption() {
      const path = this.$route.path;
      return (
        path.includes('compositionClassSituation') ||
        path.includes('referenceSample') ||
        path.includes('lexicalAnalysis') ||
        path.includes('grammarAnalysis')
      );
    },

    // 是否多选班级
    isMultiClass() {
      const path = this.$route.path;
      return path.includes('knowledgeAnalyze') || path.includes('questionAnalysis');
    },
  },
  watch: {
    // '$route.path'(val) {
    //   let subjectId = this.$sessionSave.get('subjectId') ? this.$sessionSave.get('subjectId') : this.subjectList[0]?.id;
    //   this.filterData.subjectId = subjectId;
    //   if (!val.includes('cardHome') && !val.includes('cardDetail')) {
    //     this.filterData.classId = this.classList.length ? this.classList[0].id : '-1';
    //     this.filterData.classIds = this.classList.length && this.classList[0].id ? [this.classList[0].id] : [];
    //   }
    //   if (this.isDesktop && this.$sessionSave.get('DReport_ClassId')) {
    //     const classId = this.$sessionSave.get('DReport_ClassId');
    //     this.filterData.classId = classId;
    //     this.filterData.classIds = [classId];
    //   }
    //   this.supplyFilterClassInfo();
    //   this.$emit('updateFilter', this.filterData);
    // },
    // 监听赋分规则模式
    // // 监听隐藏学科时
    // isShowSubjectOption(newVal, oldVal) {
    //   if (!newVal) {
    //     this.filterData.subjectId = ''; // 设为全部学科
    //   }
    //   if (newVal !== oldVal) this.getExamClassList(); // 重新获取班级
    // },
    // classList(val) {
    //   if (this.isDesktop) return;
    //   if (!val || val.length == 0) {
    //     this.filterData.classId = '-1';
    //     this.filterData.classIds = [-1];
    //   } else {
    //     this.filterData.classId = this.classList[0].id;
    //     this.filterData.classIds = [this.classList[0].id];
    //   }
    // },
  },

  created() {},

  async mounted() {
    await this.getExamSubjectList();

    if (this.examSubjectList.length) await this.getExamClassList();
    this.initQuery();
    this.initGroupSubjectList();
    this.initBusEvent();
    this.supplyFilterClassInfo();
    await this.initCompositionQues();

    this.$emit('updateFilter', this.filterData);
    this.$emit('init', {
      filterData: this.filterData,
      examClassList: this.examClassList,
      examSubjectList: this.examSubjectList,
    });
    if (this.examSubjectList.length == 0 && this.isShowSubjectOption) {
      this.$message.warning('请确认您的任教学科信息是否有误');
      return;
    }
    if (this.examClassList.length == 0 && this.isShowClassOption) {
      this.$message.warning('请确认您的任教班级信息是否有误');
      return;
    }
    this.$sessionSave.set('subjectId', this.filterData.subjectId);
  },

  beforeDestroy() {
    this.$bus.$off('desktop-change-filter');
  },

  methods: {
    // 初始化查询参数
    initQuery() {
      if (this.$route.query.abPaper) {
        if (this.abPaperList.length) this.filterData.abPaper = this.$route.query.abPaper;
      }
      if (this.$route.query.classId) this.filterData.classId = this.$route.query.classId;
    },

    // 初始化组合成绩榜
    initGroupSubjectList() {
      let detailExamId = this.$sessionSave.get('reportDetail').examId;
      let parentExamId = this.$sessionSave.get('reportParent').examId;
      let isCardHome = this.$route.path.includes('cardHome');
      let onlyLv = this.$sessionSave.get('onlyLv');
      if (this.isCanGroupSubject && detailExamId == parentExamId && isCardHome && !onlyLv) {
        this.getGroupSubjectList();
      }
    },

    // 初始化作文题号
    async initCompositionQues() {
      if (!this.isShowCompositionQuesOption) return;
      const { examId, v } = this.$sessionSave.get('reportDetail');

      const res = await getCompositionQsnosAPI({
        examId,
        subjectId: this.filterData.subjectId,
        v: v,
        abPaper: this.filterData.abPaper,
      });
      this.compositionQuesList = res.data || [];
      if (this.compositionQuesList.length) {
        this.filterData.quesInfo = this.compositionQuesList[0] || null;
      }
      this.$sessionSave.set('compositionQuesList', this.compositionQuesList);
    },

    // 初始化监听
    initBusEvent() {
      this.$bus.$on('desktop-change-filter', async ({ type, item, refresh }) => {
        if (refresh && type === 'subject') {
          // 刷训学科列表
          await this.refreshSubjectList();
          item = this.subjectList.find(it => it.id == this.filterData.subjectId);
        }
        this.changeFilter(type, item, true);
      });
    },

    // 获取模块角色
    getModuleRoles() {
      let schoolReportPermission = this.$sessionSave.get('schoolReportPermission');
      if (schoolReportPermission) {
        let menuRoles = schoolReportPermission.menuRoles;
        let path = this.$route.path;
        if (path.includes('cardDetail')) {
          path = path.replace('cardDetail', 'cardHome');
        }
        if (noPermissionMenuList.some(item => path.includes(item))) {
          return [1, 2, 3, 4, 5, 6];
        } else {
          let roles = menuRoles.find(item => path.includes(item.menuId));
          return roles.roleIds.map(t => Number(t));
        }
      }
      return [];
    },

    async refreshSubjectList() {
      const { examId, campusCode, year, gradeName, v } = this.$sessionSave.get('reportDetail');
      // 获取当前入学年份角色权限
      let roles = getToRoles(year, campusCode);
      if (!UserRole.isOperation) {
        // 获取当前模块权限
        let moduleRoles = this.getModuleRoles();
        roles = findIntersection(roles, moduleRoles).map(t => Number(t));
      }

      // 获取模块角色
      const { roleSubjectList, noRoleSubjectList } = await getExamReportSubjectList({
        examId,
        campusCode,
        roles,
        v,
        year,
        gradeName,
        statType: 1,
      });

      // 设置未经过权限筛选的学科
      this.noRoleExamSubjectList = noRoleSubjectList;
      this.$sessionSave.set('innerNoRoleSubjectList', this.noRoleExamSubjectList);
      // 设置经过权限筛选的学科
      this.examSubjectList = roleSubjectList;
      this.$sessionSave.set('innerSubjectList', this.subjectList);
    },

    /**
     * @name:获取班级
     */
    async getExamClassList() {
      const routerPath = this.$route.path;
      const { examId, campusCode, year } = this.$sessionSave.get('reportDetail');
      this.examClassList = [];
      // 获取班级
      let subject = this.subjectList.find(item => item.id == this.filterData.subjectId);
      // 学科对比，全部学科
      if (this.$route.path.includes('subjectComparison')) {
        subject = this.allSubjectItem;
        this.filterData.subjectId = subject.id;
      }
      // 组合成绩榜，全部学科
      if (this.filterData.source == 1) {
        subject = this.allSubjectItem;
        this.filterData.subjectId = subject.id;
      }

      let roles = getToRoles(year, campusCode);
      if (!UserRole.isOperation) roles = (subject.intersectionRoles || []).map(t => Number(t));

      let subjectId = subject.id;
      let subRoles = subject.roles;
      const { roleClassList, noRoleClassList } = await getExamReportClassList({
        examId,
        roles,
        subjectId,
        year,
        campusCode,
      });

      this.noRoleExamClassList = JSON.parse(JSON.stringify(noRoleClassList));
      this.$sessionSave.set('innerNoRoleClassList', noRoleClassList);
      this.subClassList = this.$sessionSave.get('substituteClassList');
      this.examClassList = roleClassList;
      this.$sessionSave.set('innerClassList', this.classList);

      await this.getAbPaperList();
      this.filterData.classList = this.classList;

      let classItem = this.classList.find(item => item.id == this.$sessionSave.get('classId'));
      if (classItem) {
        this.filterData.classId = classItem.id;
        this.filterData.classIds = [classItem.id];
      } else {
        this.filterData.classId = this.classList.length ? this.classList[0].id : '-1';
        this.filterData.classIds = this.classList.length && this.classList[0].id ? [this.classList[0].id] : [];
      }
    },

    /**
     * @name:获取学科
     */
    async getExamSubjectList() {
      await this.refreshSubjectList();
      // 若账号为主任，校长或者运营，且不是知识点和讲评页，允许查看全部年级学科
      if (this.subjectList.length) {
        let subject;
        if (this.$sessionSave.get('subjectId')) {
          subject = this.subjectList.find(item => item.id == this.$sessionSave.get('subjectId'));
        }
        if (subject) {
          this.filterData.subjectId = subject.id || '';
          this.filterData.subjectName = subject.name;
          this.filterData.phaseId = subject.phaseId;
          this.filterData.xfId = subject.xfId;
        } else {
          this.filterData.subjectId = this.subjectList[0].name === '全部' ? '' : this.subjectList[0].id;
          this.filterData.subjectName = this.subjectList[0].name;
          this.filterData.phaseId = this.subjectList[0].phaseId;
          this.filterData.xfId = this.subjectList[0].xfId;
        }
      }
      this.$emit('getSubjectList', this.subjectList);
    },

    // 成绩单点击查看详情后，隐藏全部班级，选中当前学生所在班级
    hideTotalClass(className) {
      this.filterData.classId = className;
      this.supplyFilterClassInfo();
      this.$emit('updateFilter', this.filterData);
    },

    // 成绩从详情点击返回，显示全部班级，选中全部
    showTotalClass() {
      this.supplyFilterClassInfo();
      this.$emit('updateFilter', this.filterData);
    },

    // // 考试总览页面切换头部tab初始化班级和学科
    // initData() {
    //   //todo 数据概览，学生等级分布，学优生学困生，如果是教师和备长学科不显示全部，否则显示全部，知识点分析学科不管什么角色都没有全部学科
    //   const routerPath = this.$route.path;
    //   if (!this.subjectList.length) return;

    //   this.filterData.classId = this.classList.length ? this.classList[0].id : '-1';
    //   this.filterData.classIds = this.classList.length && this.classList[0].id ? [this.classList[0].id] : [];

    //   if (routerPath.indexOf('knowledgeAnalyze') >= 0 && !this.filterData.subjectId) {
    //     this.filterData.subjectId = this.subjectList[0].id;
    //     this.filterData.subjectName = this.subjectList[0].name;
    //     this.filterData.phaseId = this.subjectList[0].phaseId;
    //     this.filterData.xfId = this.subjectList[0].xfId;
    //     this.filterData.isSubstitute = this.subjectList[0].isSubstitute;
    //   }
    //   this.supplyFilterClassInfo();
    //   this.$emit('getSubjectList', this.subjectList);
    //   this.$emit('updateFilter', this.filterData);
    // },

    // 补充筛选班级信息
    supplyFilterClassInfo() {
      if (this.filterData.classId && this.filterData.classId == -1) return;

      this.filterData.isSubstitute = this.subClassList.some(
        item => item.subjectId === this.filterData.subjectId && item.classId == this.filterData.classId
      );
    },

    // 切换班级/学科筛选项
    async changeFilter(type, item, bus = false) {
      switch (type) {
        case 'class':
          this.filterData.classId = item === '全部' ? '' : item.id;
          this.filterData.isSubstitute = item.isSubstitute;
          this.$sessionSave.set('classId', this.filterData.classId);
          break;
        default:
          this.filterData.subjectId = item.id;
          this.filterData.phaseId = item.phaseId;
          this.filterData.xfId = item.xfId;
          this.filterData.subjectName = item.name;
          this.filterData.qType = 0;
          this.$sessionSave.set('subjectId', this.filterData.subjectId);
          await this.getExamClassList('subject');
          await this.initCompositionQues();
          break;
      }
      this.supplyFilterClassInfo();
      this.$emit('updateFilter', this.filterData, type);
      if (!bus) {
        this.$bus.$emit('header-change-filter', { type, item });
      }
    },
    // 班级多选，选中班级
    addClass(item) {
      let classIds = this.filterData.classIds.filter(t => t);
      if (item.class_name === '全部') {
        this.filterData.classIds = [];
      } else {
        if (classIds.indexOf(item.id) === -1) {
          classIds.push(item.id);
        } else {
          classIds.splice(classIds.indexOf(item.id), 1);
        }
        this.filterData.classIds = classIds;
      }
      this.supplyFilterClassInfo();
      this.$emit('updateFilter', this.filterData);
    },
    // 班级多选，取消选中班级
    cancelClass(item) {
      let classIds = this.filterData.classIds;
      if (classIds.indexOf(item.id) !== -1) {
        classIds.splice(classIds.indexOf(item.id), 1);
      }
      this.filterData.classIds = classIds;
      this.supplyFilterClassInfo();
      this.$emit('updateFilter', this.filterData);
    },

    async changeSource(value) {
      this.filterData.source = value;
      if (value == 0) {
        await this.getExamSubjectList();
      }
      await this.getExamClassList();
      this.filterData.aliasName = this.groupSubjectList[0].aliasName;
      this.$emit('updateFilter', this.filterData);
    },

    // 更改分数类型
    changeQType(value) {
      this.filterData.qType = value;
      this.$emit('updateFilter', this.filterData);
    },

    // 更改组合榜学科
    changeAliasName(item) {
      this.filterData.aliasName = item.aliasName;
      this.$emit('updateFilter', this.filterData);
    },

    // 更改AB卷
    async changeAbPaper(value) {
      this.filterData.abPaper = value;
      this.filterData.classId = this.classList.length ? this.classList[0].id : '-1';
      this.filterData.classIds = this.classList.length && this.classList[0].id ? [this.classList[0].id] : [];
      await this.initCompositionQues();
      this.$emit('updateFilter', this.filterData);
    },

    // 更改作文题
    changeCompositionQues(item) {
      this.filterData.quesInfo = item;
      this.$emit('updateFilter', this.filterData);
    },

    // 获取组合学科列表
    async getGroupSubjectList() {
      const examId = this.$sessionSave.get('reportDetail').examId;
      try {
        const res = await getGroupSubject({
          examId: examId,
        });
        this.groupSubjectList = res.data || [];
      } catch (error) {
        console.error(error);
        this.groupSubjectList = [];
      }
    },

    // 获取ab卷列表
    async getAbPaperList() {
      let list = [];
      let isShowAPaper = this.examClassList.some(
        item => item.abPaper !== null && item.abPaper !== undefined && item.abPaper.split(',').includes('0')
      );
      let isShowBpaper = this.examClassList.some(
        item => item.abPaper !== null && item.abPaper !== undefined && item.abPaper.split(',').includes('1')
      );
      if (this.isShowAbPaperOption) {
        if ((isShowAPaper || isShowBpaper) && this.isShowAbPaperAllOption) {
          list.push({
            text: '全部',
            value: '',
          });
        }
        if (isShowAPaper) {
          list.push({
            text: 'A卷',
            value: '0',
          });
        }
        if (isShowBpaper) {
          list.push({
            text: 'B卷',
            value: '1',
          });
        }
      }

      this.abPaperList = list;
      this.filterData.abPaper = list[0]?.value || '';
    },

    getAllSubjectByRolePublished(roles) {
      if (UserRole.isOperation) {
        return true;
      } else {
        let { year, campusCode } = this.$sessionSave.get('reportDetail');
        let userRoles = getToRoles(year, campusCode);
        let intersectionRoles = findIntersection(userRoles, roles).map(t => Number(t));
        return this.noRoleExamSubjectList.every(item => {
          return intersectionRoles.some(role => item.roles.includes(role));
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.header-filter__wrapper {
  // position: sticky;
  // top: 0;
  background: #fff;
  // padding-top: 5px;
  padding-bottom: 10px;
  z-index: 999;
}

.header__filter {
  margin: 10px 0 5px;

  .class__li {
    display: inline-block;
    margin-right: 5px;
    margin-bottom: 5px;
    padding: 0 8px;
    border-radius: 3px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;

    &.active,
    &:hover {
      color: #409eff;
    }

    &.moreClass {
      height: 30px;
      line-height: 30px;
      border-radius: 15px;
      padding: 0 4px 0 8px;

      .el-icon-close {
        display: none;
      }
    }

    &.moreActive {
      background: #f5faff;

      .el-icon-close {
        display: inline-block;
      }
    }

    &.allActive {
      color: #409eff;
      background: #fff;

      .el-icon-close {
        display: none;
      }
    }
  }

  .moreClass {
    line-height: 30px;
  }

  .leftText {
    width: auto;
    /*line-height: inherit;*/
  }

  .rightText {
    width: auto;
  }
}

.bottomLine {
  height: 1px;
  margin-top: 5px;
  border-bottom: 1px solid #e4e8eb;
}
</style>

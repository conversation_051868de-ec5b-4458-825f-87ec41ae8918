<template>
  <div>
    <div class="setting-header">
      <div class="title">优秀作答</div>
      <div class="header-right">
        <el-button type="primary" size="small" :loading="saveLoading" @click="saveConfig">保存</el-button>
      </div>
    </div>

    <div class="excellent-answer-setting">
      <div class="excellent-answer-setting-item">
        <span>年级优秀作答自动推送：</span>
        <el-switch v-model="jCfg.push" :active-value="1" :inactive-value="0" />
      </div>

      <template v-if="jCfg.push == 1">
        <div class="excellent-answer-setting-item">
          <span>得分率： ≥</span>
          <el-input-number v-model="jCfg.rate" class="rate-input" size="small" :min="60" :max="100" :controls="false" />
        </div>

        <div class="excellent-answer-setting-item">
          <span>年级前： </span>
          <el-input-number v-model="jCfg.grd" class="rank-input" size="small" :min="1" :max="20" :controls="false" />
        </div>

        <div class="tip">
          <p class="title">*注：同时满足得分率≥X%且排名在年级前X人两个条件，自动推送主观题年级优秀作答</p>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Mixins, Vue } from 'vue-property-decorator';
import { getSchCfgAPI, setSchCfgAPI } from '@/service/pexam';
import { SchoolSettingType } from '../../types';
import SchoolSettingMixin from '../SchoolSetting.mixin';


interface JCfg {
  /** 是否自动推送 */
  push: number;
  /** 得分率 */
  rate: number;
  /** 年级前几人 */
  grd: number;
}

@Component({
  components: {},
})
export default class ExcellentAnswer extends Mixins(SchoolSettingMixin) {
  // 配置项
  private jCfg: JCfg = {
    push: 0,
    rate: 85,
    grd: 20,
  };
  // 保存loading
  private saveLoading = false;

  mounted() {
    this.getConfig();
  }

  // 获取配置项
  async getConfig() {
    const res = await getSchCfgAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      type: SchoolSettingType.ExcellentAnswer,
    });
    const data = res.data;
    if (data && data.jCfg) {
      this.jCfg = data.jCfg;
    }
  }

  // 保存配置
  async saveConfig() {
    this.saveLoading = true;
    try {
      await setSchCfgAPI({
        schId: this.$sessionSave.get('schoolInfo').id,
        schName: this.$sessionSave.get('schoolInfo').schoolName,
        type: SchoolSettingType.ExcellentAnswer,
        jCfg: this.jCfg,
      });
      this.$notify.success({
        title: '成功',
        message: '保存成功',
      });
    } catch (error) {
      this.$notify.error({
        title: '失败',
        message: '保存失败',
      });
    } finally {
      this.saveLoading = false;
    }
  }
}
</script>
<style lang="scss" scoped>
@import '../page-style.scss';

.excellent-answer-setting {
}

.excellent-answer-setting-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .rate-input {
    width: 80px;
    margin: 0 5px;

    &::after {
      display: inline-block;
      content: '%';
      position: absolute;
      top: 2px;
      bottom: 0;
      right: 5px;
      color: #c0c4cc;
    }
  }

  .rank-input {
    width: 80px;
    margin: 0 5px;

    &::after {
      display: inline-block;
      content: '人';
      position: absolute;
      top: 2px;
      bottom: 0;
      right: 5px;
      color: #c0c4cc;
    }
  }
}
</style>

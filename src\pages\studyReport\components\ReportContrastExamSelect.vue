<template>
  <div class="header-item">
    <span class="header-item__label">对比考试：</span>
    <el-select
      class="header-item__select"
      value-key="examId"
      popper-class="select-popper"
      :popper-append-to-body="false"
      v-model="filterInfo.contrastExamInfo"
      :loading="!selectOption.contrastExamList.length && loading"
      :filterable="true"
      :filter-method="onFilterMethod"
      @visible-change="onVisibleChange"
      @change="onExamChange"
      v-lazyloading="getExamList"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <el-option
        v-for="item in selectOption.contrastExamList"
        :key="item.examId"
        :label="item.examName"
        :value="item"
        :title="item.examName"
      >
      </el-option>
      <div v-if="!finished && loading" style="text-align: center">
        <el-button type="text" :loading="loading">加载中</el-button>
      </div>
    </el-select>
  </div>
</template>

<script lang="ts">
import { IContrastExamInfo } from '@/pages/studyReport/plugins/types';
import { getContrastTestList } from '@/service/pexam';
import { Component } from 'vue-property-decorator';
import ReportComponent from './ReportComponent';

// 定义防抖函数
const debounce = (func, delay) => {
  let timerId;
  return function () {
    const context = this;
    const args = arguments;
    clearTimeout(timerId);
    timerId = setTimeout(function () {
      func.apply(context, args);
    }, delay);
  };
};

@Component({
  directives: {
    // 懒加载
    lazyloading: {
      bind(el, binding) {
        let SELECT_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
        SELECT_DOM.addEventListener('scroll', function () {
          let condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      },
    },
  },
})
export default class ReportContrastExamSelect extends ReportComponent {
  // 考试列表查询参数
  queryParams = {
    keyWord: '',
  };
  // 考试列表分页器
  pagination = {
    page: 1,
    pageSize: 50,
    total: 0,
  };
  // 是否查询结束
  finished = false;
  // 是否正在加载
  loading = false;

  mounted() {
    this.onFilterMethod = debounce(this.onFilterMethod, 300);
  }

  // 获取考试列表
  async getExamList() {
    if (this.finished) return;
    if (this.loading) return;
    this.loading = true;

    try {
      const res = await getContrastTestList({
        examId: this.filterData.examId,
        schoolId: this.$sessionSave.get('schoolInfo').id,
        gradeId: this.filterData.gradeId,
        name: this.queryParams.keyWord,
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
      });
      if (this.pagination.page === 1) {
        this.selectOption.contrastExamList = res.data.list;
      } else {
        this.selectOption.contrastExamList = this.selectOption.contrastExamList.concat(
          res.data.list
        );
      }
      this.pagination.page++;
      this.pagination.total = res.data.total;
      if (this.selectOption.contrastExamList.length >= this.pagination.total) {
        this.finished = true;
      }
      this.loading = false;
    } catch (error) {
      console.error(error);
      this.selectOption.contrastExamList = [];
      this.loading = false;
      this.finished = true;
    }
  }

  // 搜索考试
  onFilterMethod(val) {
    this.queryParams.keyWord = val;
    this.pagination.page = 1;
    this.pagination.total = 0;
    this.selectOption.contrastExamList = [];
    this.finished = false;
    this.getExamList();
  }

  // 显示隐藏
  onVisibleChange(show) {
    this.queryParams.keyWord = '';
    this.pagination.page = 1;
    this.pagination.total = 0;
    this.selectOption.contrastExamList = [];
    this.finished = false;
    this.getExamList();
  }

  // 考试更换事件
  async onExamChange(examInfo: IContrastExamInfo) {
    this.FilterModule.setContrastExamInfo(examInfo);
    this.FilterModule.trigger('changeFilter');
  }
}
</script>

<style scoped lang="scss">
@import './reportSelect.scss';
</style>

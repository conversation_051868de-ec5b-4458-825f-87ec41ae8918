<template>
  <div>
    <div class="overcard-container">
      <el-page-header @back="back">
        <template #content>
          <el-radio-group v-model="listType">
            <el-radio-button label="0">完成制题列表</el-radio-button>
            <el-radio-button label="1">完成制卡列表</el-radio-button>
          </el-radio-group>
        </template>
      </el-page-header>
      <!-- --头部筛选项-- -->
      <div class="card-header">
        <div v-if="isOperate" class="header__select">
          <span class="select__label">学校：</span>
          <el-select class="select_school" ref="schoolSelect" v-model="schoolId" filterable @change="initData"
            placeholder="学校" size="mini">
            <el-option v-for="item in schoolList" :key="item.schoolId" :label="item.schoolName" :value="item.schoolId">
            </el-option>
          </el-select>
        </div>
        <div class="header__select">
          <span class="select__label">时间：</span>
          <el-date-picker v-model="times" class="date-picker" type="daterange" range-separator="-"
            start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" format="yyyy-MM-dd"
            :clearable="false" @change="searchList">
          </el-date-picker>
        </div>
        <div class="header__select">
          <el-input placeholder="请输入关键字搜索" v-model="keyWord" class="input-with-select">
            <el-button slot="append" icon="el-icon-search" @click="getOverCardList"></el-button>
          </el-input>
        </div>
        <div class="header__select">
          <el-button type="primary" @click="getOverCardList()">刷新</el-button>
        </div>
        <div v-if="listType == 0" class="header__select">
          <el-checkbox v-model="isInComplete">未完成</el-checkbox>
        </div>
      </div>
      <!-- 表格数据 -->
      <div class="card-table">
        <el-table v-if="listType == 0" :data="tableData" stripe border align="center" style="width: 100%">
          <el-table-column prop="schoolName" align="center" label="学校" show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="tbName" align="center" label="试卷名称" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.tbName }}<i v-if="scope.row.needCheckPassword" style="color:#ff0000;"
                class="el-icon-lock"></i>
            </template>
          </el-table-column>
          <el-table-column prop="subjectName" align="center" label="学科"> </el-table-column>
          <el-table-column prop="gradeName" align="center" label="年级" width="100">
          </el-table-column>
          <el-table-column prop="typeName" align="center" label="类型" width="100"> </el-table-column>
          <el-table-column prop="creatorName" align="center" label="操作人"> </el-table-column>
          <el-table-column prop="modifyTime" align="center" label="完成时间"> </el-table-column>
          <el-table-column prop="action" align="center" label="操作" width="150">
            <template slot-scope="scope">
              <el-button v-if="!isInComplete" type="text" size="mini" @click="goCreateCard(scope.row)">去制卡</el-button>
              <el-button v-else type="text" size="mini" @click="goCreateQues(scope.row)">去制题</el-button>
              <el-button v-if="scope.row.needCheckPassword" type="text" size="mini"
                @click="editPwd(scope.row)">修改密码</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-table v-else :data="tableData" stripe border align="center" style="width: 100%">
          <el-table-column prop="schoolName" align="center" label="学校" show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="paperNo" align="center" label="试卷号" width="100"> </el-table-column>
          <el-table-column prop="cardTypeName" align="center" label="类型" width="100"> </el-table-column>
          <el-table-column prop="tbName" align="center" label="答题卡名称" show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="subjectName" align="center" label="学科" width="100"> </el-table-column>
          <el-table-column prop="gradeName" align="center" label="年级" width="100">
          </el-table-column>
          <el-table-column prop="creatorName" align="center" label="操作人" width="100"> </el-table-column>
          <el-table-column prop="modifyTime" align="center" label="完成时间"> </el-table-column>
          <el-table-column align="center" label="当前状态" width="100">
            <template slot-scope="scope">
              <template v-if="scope.row.isDelete == 1">
                <span style="color: red;">
                  被删除
                </span>
              </template>
              <template v-else>
                <template v-if="scope.row.isRelatedWork == 1">
                  <el-tooltip effect="dark" :content="scope.row.examTitle" placement="top-start">
                    <span style="color:green;">已关联</span>
                  </el-tooltip>
                </template>
                <span v-else-if="scope.row.isRelatedWork == 0">
                  未关联
                </span>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="action" align="center" label="操作">
            <template slot-scope="scope">
              <template v-if="scope.row.isDelete == 1">
                <el-button type="text" size="mini" @click="reset(scope.row)">恢复</el-button>
              </template>
              <template v-else>
                <el-button style="margin-right: 10px" type="text" size="mini"
                  @click="goScan(scope.row)">查看识别</el-button>
                <el-dropdown @command="handleCommand">
                  <span class="el-dropdown-link">
                    修改<i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="goCreateCard(scope.row)">编辑题卡</el-dropdown-item>
                    <el-dropdown-item @click.native="editPage(scope.row)">修改页数</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!--分页器-->
      <el-pagination background style="margin: 10px auto 0" :hide-on-single-page="!tableData.length" class="text-center"
        layout="total, prev, pager, next" @current-change="handleCurrentChange" :current-page.sync="pagination.page"
        :page-size="pagination.limit" :total="pagination.total_rows">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { getTestBankOrCardList, modifyNotDelete, modifyTbPassword, updatePaperNum } from '@/service/testbank';
import { getUserInfoToToken } from '@/service/api'
import { dateFormat } from '@/utils/dateUtil';
import { mapState } from 'vuex';
import { getOperSchoolList } from '@/service/api';
import { getToken } from '@/service/auth';
import { sessionSave } from '@/utils';
import { IAB_CARD_TYPE } from '@/typings/card';

export default {
  data() {
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            },
          },
        ],
      },
      listType: 0,
      keyWord: "",
      //筛选时间
      times: [],
      tableData: [],
      // 分页
      pagination: {
        page: 1,
        pageSize: 10,
        total_rows: 0,
      },
      //在线查看定位点地址
      checkPoints: process.env.VUE_APP_POINTS,
      schoolId: '',
      schoolList: [],
      cardUrl: process.env.VUE_APP_CARDURL,
      abCardUrl: process.env.VUE_APP_ABCARDURL,
      schoolInfo: {},
      isInComplete: false,//未完成
    };
  },
  computed: {
    isSchoolAdmin() {
      return (
        this.$sessionSave.get('loginInfo') && this.$sessionSave.get('loginInfo').admin_type == 2
      );
    },
    isOperate() {
      return (
        this.$sessionSave.get('loginInfo') && this.$sessionSave.get('loginInfo').user_type == 5
      );
    }
  },
  watch: {
    listType(newVal, oldVal) {
      this.searchList();
    },
    isInComplete(newVal) {
      this.searchList();
    }
  },
  async mounted() {
    this.schoolInfo = sessionSave.get('schoolInfo');
    if (this.isOperate) {
      await this.getSchoolList();
    } else {
      this.schoolId = this.schoolInfo.id;
      this.schoolList = [this.schoolInfo];
    }
    this.initData();
  },
  methods: {
    handleCommand(command) {
    },
    // 获取代理商学校列表
    async getSchoolList() {
      await getOperSchoolList({
        userId: this.$sessionSave.get('loginInfo').id,
        userType: 5,
        page: 1,
        limit: 100,
      }).then(data => {
        this.schoolList = data.data.list[0].schools;
        this.schoolId = this.schoolInfo.id || this.schoolList[0].schoolId;
        // this.schoolList.unshift({
        //   schoolId: '',
        //   schoolName: '全部学校',
        // });
      });
    },
    /**
     * @name:初始化数据
     */
    initData() {
      this.times = this.getLastWeekRange();
      this.searchList();
    },
    searchList() {
      this.pagination.page = 1;
      this.getOverCardList();
    },
    /**
     * @name：获取列表数据
     */
    getOverCardList() {
      getTestBankOrCardList({
        schoolId: this.schoolId,
        startTime: this.$moment(this.times[0]).format('YYYY-MM-DD'),
        endTime: this.$moment(this.times[1]).format('YYYY-MM-DD'),
        page: this.pagination.page,
        limit: this.pagination.pageSize,
        keyWord: this.keyWord,
        type: this.listType,
        isOverCard: this.isInComplete ? '0' : '1'
      })
        .then(res => {
          this.tableData = res.data.rows;
          this.tableData.forEach(item => {
            item.modifyTime = dateFormat(item.modifyTime);
            item.cardTypeName = this.getCardTypeName(item.cardType);
          });
          this.pagination.total_rows = res.data.total_rows;
        })
        .catch(err => { });
    },
    getCardTypeName(type) {
      let name = '';
      switch (type) {
        case 0:
          name = '纯答题卡';
          break;
        case 1:
          name = '题卡合一';
          break;
        case 2:
          name = '题卡分离';
          break;
        case 3:
          name = '三方卡';
          break;
        case 4:
          name = '英语专项';
          break;
      }
      return name
    },
    /**
     * @name:获取最近一周时间范围
     */
    getLastWeekRange() {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
    /***
     * @name:返回上一页
     */
    back() {
      this.$router.back();
    },
    /**
     * @name:分页查询
     */
    handleCurrentChange(val) {
      this.getOverCardList();
    },
    async getUserInfoById(userid, schoolid) {
      let params = {
        userId: userid,
        schoolid: schoolid
      }
      let res = await getUserInfoToToken(params)
      return res.data.token
    },
    /**
     * @name:查看识别
     */
    goScan(item) {
      window.open(`/scan-view/view_paper?paper_no=${item.paperNo}&refresh_pdf=false`);
      return;
    },
    async editPwd(item) {
      let { value } = await this.$prompt('请输入新密码', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      });
      let params = {
        tbId: item.id,
        password: value,
        optUserId: this.$sessionSave.get('loginInfo').id,
      };
      let res = await modifyTbPassword(params).catch(err => {
      })
      if (res.code == 1) {
        this.$message({
          message: '修改成功！',
          type: 'success',
          duration: 2000,
        });
      } else {
        this.$message({
          message: res.msg || '修改失败，请重试！',
          type: 'error',
          duration: 2000,
        });
      }
    },
    async editPage(item) {
      const h = this.$createElement;
      this.$msgbox({
        title: '修改答题卡张数',
        message: h('div', { class: 'custom-prompt-container' }, [
          h('p', { class: 'warn-tip' }, '请在方框中输入实际印刷的答题卡页数，此操作无法恢复，请谨慎操作！'),
          h('div', { class: 'input-row' }, [
            h('span', { class: 'input-label' }, '实际使用'),
            h('input', {
              attrs: {
                type: 'number',
                value: item.paperNum || '',
                class: 'el-input__inner custom-input'
              },
              ref: 'pageInput',
              style: {
                width: '100px',
                textAlign: 'center'
              },
              on: {
                input: (event) => {
                  const value = event.target.value;
                  // 移除非数字字符
                  const numericValue = value.replace(/\D/g, '');

                  // 限制最大值为999
                  let finalValue = numericValue;
                  if (parseInt(numericValue) > item.originPaperNum) {
                    finalValue = item.originPaperNum;
                  }
                  if (parseInt(numericValue) < 1) {
                    finalValue = 1;
                  }

                  // 如果值被修改，更新输入框
                  if (finalValue !== value) {
                    event.target.value = finalValue;
                  }
                }
              }
            }),
            h('span', { class: 'input-unit' }, '页')
          ])
        ]),
        showCancelButton: true,
        closeOnClickModal: false,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        customClass: 'page-count-dialog',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            const inputValue = instance.$el.querySelector('input').value;
            // 验证输入是否为数字
            if (!/^\d+$/.test(inputValue)) {
              this.$message.error('请输入有效的页数');
              return;
            }

            // 执行更新操作
            this.updatePageCount(item.id, inputValue).then(() => {
              this.$message({
                type: 'success',
                message: '答题卡页数已更新为: ' + inputValue + '页'
              });
              done();
            }).catch(err => {
              this.$message.error('更新失败: ' + err.message);
            });
          } else {
            done();
          }
        }
      });
    },

    // 添加更新页数的方法
    async updatePageCount(id, pageCount) {
      try {
        let params = {
          tbId: id,
          paperNum: pageCount,
        }
        await updatePaperNum(params);
        return Promise.resolve();
      } catch (error) {
        return Promise.reject(error);
      }
    },
    //去制卡
    async goCreateCard(item) {
      let routeData = '';
      //三方卡
      if (item.originCardType == 3) {
        routeData = `/bigdata/home/<USER>
      } else {
        let token = await this.getUserInfoById(item.creatorId, item.schoolId);
        routeData = `&examName=${item.tbName}&cardType=${item.cardType}&correctType=${item.correctType}&pageLayout=${item.pageLayout}&subjectId=${item.subjectId}&token=${token}`;
        if (item.abCardType == IAB_CARD_TYPE.abCard) {
          routeData = `${this.abCardUrl}?id=${item.abCardId}${routeData}`;
        } else {
          routeData = `${this.cardUrl}?id=${item.id}${routeData}`;
        }
      }
      window.open(routeData, '_blank');
    },
    async goCreateQues(item) {
      let routeData = `${process.env.VUE_APP_CUTPAPERURL}cutTestPaper/matchQues.html?id=${item.id}&userid=${item.creatorId}&url=${process.env.VUE_APP_CUT_URL}&subject=${item.subectId}&token=${this.$sessionSave.get('loginInfo').token}`;
      window.open(routeData, '_blank');
      this.$confirm('是否制卷完成，刷新数据', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.searchList();
        })
        .catch(err => {
          this.searchList();
        });
    },
    //恢复答题卡
    async reset(item) {
      let params = {
        tbId: item.id,
        optUserId: this.$sessionSave.get('loginInfo').id,
      };
      let res = await modifyNotDelete(params);
      if (res.code == 1) {
        this.$message({
          message: '恢复成功！',
          type: 'success',
          duration: 1000,
        });
        this.getOverCardList();
      } else {
        this.$message({
          message: res.msg || '恢复失败，请重试！',
          type: 'error',
          duration: 1000,
        });
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.overcard-container {
  background-color: #fff;
  padding: 20px;
  // height: 100%;
  height: calc(100%);
}

.card-header {
  position: relative;
  padding: 24px;
  // padding-top: 24px;
  width: 100%;
  line-height: 38px;

  .header__select {
    display: inline-block;
    margin-right: 20px;
    float: left;
  }

  .select {
    width: 104px;
  }

  .date-picker {
    width: 238px;
  }

  .time {
    font-size: 14px;
    font-weight: 400;
    color: #606266;
  }
}

.card-table {
  margin-top: 30px;
}
</style>
<style lang="scss">
.overcard-container {
  .el-page-header__title {
    align-self: center;
  }
}

.custom-prompt-container {
  padding: 10px 0;

  .warn-tip {
    display: block;
    color: #f56c6c;
    font-size: 13px;
    margin-bottom: 16px;
    padding: 8px 16px;
    background-color: #fef0f0;
    border-radius: 4px;
    border: 1px solid #fde2e2;

    &::before {
      content: "⚠";
      margin-right: 8px;
    }
  }

  .input-row {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 0;
  }

  .input-label {
    margin-right: 10px;
    font-size: 14px;
  }

  .input-unit {
    margin-left: 10px;
    font-size: 14px;
  }

  .custom-input {
    height: 40px;
    font-size: 16px;
  }
}

</style>
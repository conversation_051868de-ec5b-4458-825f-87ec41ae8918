<template>
  <div class="exam-manage">
    <div class="exam-manager__area">
      <div class="exam-manager__flex">
        <div class="exam-manager__label">考试管理员：</div>
        <div class="exam-manager__tags">
          <el-tag
            closable
            v-for="item in examLeaderList"
            :key="item.id"
            @close="removeTeacher('', item.id)"
            >{{ item.name }}</el-tag
          >
          <el-button type="text" @click="addTeacher('exam')">添加</el-button>
        </div>
      </div>
      <div class="exam-manager__tip">此管理员只具有管理本次考试权限</div>
    </div>
    <el-divider class="divider"></el-divider>
    <div class="exam-manager__area">
      <div class="exam-manager__flex">
        <div class="exam-manager__label">学科管理员：</div>
      </div>
      <div class="exam-manager__tip">学科管理员只具有管理本次考试对应学科权限</div>
    </div>

    <el-table
      :data="subjectList"
      style="width: 100%; border: 1px solid #e9eaec; border-bottom: 0"
      :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
    >
      <el-table-column prop="name" label="学科" width="240"> </el-table-column>
      <el-table-column label="学科负责人">
        <template #default="scope">
          <div class="exam-manager__tags">
            <el-tag
              closable
              v-for="item in scope.row.leaderList"
              :key="item.id"
              @close="removeTeacher(scope.row.personalBookId, item.id)"
              >{{ item.name }}</el-tag
            >
            <el-button type="text" @click="addTeacher('subject', scope.row)">添加</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <sele-more-tea
      ref="addSchoolTeaRef"
      v-if="selTeaDialog.isShow"
      :modalVisible="selTeaDialog.isShow"
      :titleName="'添加老师'"
      :subjectid="selTeaDialog.subjectId"
      :workId="selTeaDialog.workId"
      :teaList="selTeaDialog.teaList"
      @confirm-sele-tea="confirmSeleTea"
      @close-sele-tea-modal="closeSeleTeaModal"
    >
    </sele-more-tea>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { getExamSubjectMulti, addOrDelExamLeaderIds, getExamLeader } from '@/service/pexam';
import SeleMoreTea from '@/components/seleMoreTea.vue';

export interface ISubject {
  id: string;
  name: string;
  phaseId: number;
  xfId: number;
  xkId: number;
  progress: number;
  progressState: number;
  importScoreState: number;
  source: number;
  code: string;
  status: number;
  personalBookId: string;
  workId: string;
  testBankId: string;
  paperNo: string;
  fullScore: number;
  markingPapersProgress: number;
  roles: any[];
}

type EDITOR_TYPE = 'exam' | 'subject';

@Component({
  components: {
    SeleMoreTea,
  },
})
export default class ManageEditor extends Vue {
  public $refs!: {
    addSchoolTeaRef: any;
  };
  // 查询参数
  query = {
    // 考试ID
    examId: this.$route.query.examId || '',
    // 标题
    title: this.$route.query.examName || '考生管理',
    // 来源
    source: this.$route.query.source || '',
  };
  // 学科列表
  subjectList: ISubject[] = [];
  // 当前学科
  currentSubject = null;
  // 添加老师组件值对象
  selTeaDialog = {
    type: '',
    subjectId: '',
    workId: '',
    teaList: [],
    isShow: false,
  };

  // 考试管理员列表
  examLeaderList = [];
  // 学科管理员字典
  subjectLeaderMap = {};

  async mounted() {
    await this.getExamLeader();
  }

  // 获取学科列表
  async getExamSubjectList() {
    let res = await getExamSubjectMulti({
      examId: this.query.examId,
    })
    let list = res.data;
    // 根据id去重
    list = list.filter((item, index, self) =>
      index === self.findIndex((t) => t.id === item.id)
    );
    list.forEach(item => {
      item.leaderList = [];
      if (this.subjectLeaderMap[item.id]) {
        item.leaderList = this.subjectLeaderMap[item.id].leaderList || [];
        item.personalBookId = this.subjectLeaderMap[item.id].personalBookId;
      }
    });
    this.subjectList = list || [];
  }

  // 获取考试管理员
  async getExamLeader() {
    let res = await getExamLeader({
      examId: this.query.examId,
    });
    this.examLeaderList = res.data.examLeaderList || [];
    this.subjectLeaderMap = res.data;
    this.getExamSubjectList();
  }

  // 添加老师
  addTeacher(type: EDITOR_TYPE, subject?) {
    if (type == 'exam') {
      this.currentSubject = null;
      this.selTeaDialog.type = 'exam';
      this.selTeaDialog.subjectId = '';
      this.selTeaDialog.workId = 'all';
      this.selTeaDialog.teaList = [];
      this.selTeaDialog.teaList = this.examLeaderList.map(item => {
        return {
          teaId: item.id,
          teaName: item.name,
          queCount: 0,
        };
      });
    }

    if (type == 'subject') {
      this.currentSubject = subject;
      this.selTeaDialog.type = 'subject';
      this.selTeaDialog.subjectId = subject.id.split('-')[0];
      this.selTeaDialog.workId = subject.workId;
      this.selTeaDialog.teaList = [];
      this.selTeaDialog.teaList = subject.leaderList.map(item => {
        return {
          teaId: item.id,
          teaName: item.name,
          queCount: 0,
        };
      });
    }

    this.selTeaDialog.isShow = true;
    this.$nextTick(() => {
      this.$refs.addSchoolTeaRef.getSubjectList();
    });
  }

  // 添加考试管理员
  async addExamLeaderIds(personalBookId, userIds) {
    return this.addOrDelExamLeaderIds(personalBookId, userIds, 0);
  }

  // 删除考试管理员
  async delExamLeaderIds(personalBookId, userIds) {
    return this.addOrDelExamLeaderIds(personalBookId, userIds, 1);
  }

  // 添加或删除考试管理员
  async addOrDelExamLeaderIds(personalBookId, userIds, type) {
    return addOrDelExamLeaderIds({
      examId: this.query.examId,
      personalBookId: personalBookId,
      userIds: userIds,
      type: type, // 0：新增、1：删除
    });
  }

  // 移除老师
  async removeTeacher(personalBookId, teaId) {
    await this.delExamLeaderIds(personalBookId, teaId);
    this.getExamLeader();
  }

  async confirmSeleTea(teaList) {
    let addTeaIds = teaList
      .filter(item => !item.isSelected)
      .map(item => item.key)
      .join(',');

    if (!addTeaIds) {
      this.closeSeleTeaModal();
      return;
    }

    try {
      let personalBookId = this.currentSubject?.personalBookId || '';
      await this.addExamLeaderIds(personalBookId, addTeaIds);
      this.getExamLeader();
      this.$message.success('添加成功');
    } catch (err) {
      console.error(err);
    } finally {
      this.closeSeleTeaModal();
    }
  }

  closeSeleTeaModal() {
    this.selTeaDialog.isShow = false;
  }
}
</script>

<style scoped lang="scss">
.exam-manage {
  background-color: #fff;
  font-size: 14px;
  padding: 20px;
}

.exam-manager__area {
}

.exam-manager__flex {
  display: flex;
  line-height: 40px;
}

.exam-manager__label {
}

.exam-manager__tags {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 5px;
}

.exam-manager__tip {
  color: #aaaaaa;
  line-height: 28px;
}

.divider {
  margin: 5px 0;
}
</style>

<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-03-17 14:32:22
 * @LastEditors: 小圆
-->
<template>
  <div class="composition-class-situation report-wrapper">
    <headerFilter @updateFilter="updateFilter" ref="headerFilter" class="header-filter"></headerFilter>
    <div>
      <div class="clearfix">
        <div class="titleLine">班级学情</div>

        <el-button
          class="pull-right"
          type="text"
          :loading="exportLoading"
          @click="downloadTeacherReport"
          v-if="subjectCenterCode == 'ENGLISH'"
        >
          <i class="el-icon-download"></i>
          老师报告
        </el-button>
      </div>
      <div class="class-situation">
        <div class="class-situation-content">
          <div class="class-situation-item">
            <div class="value">
              {{ `${compositionQues?.clsMaxScore || 0}/` }}{{ compositionQues?.grdMaxScore || 0 }}
            </div>
            <div class="title">班级/年级最高分</div>
          </div>
          <div class="class-situation-item">
            <div class="value">
              {{ `${compositionQues?.clsMinScore || 0}/` }}{{ compositionQues?.grdMinScore || 0 }}
            </div>
            <div class="title">班级/年级最低分</div>
          </div>
          <div class="class-situation-item">
            <div class="value">
              {{ `${compositionQues?.clsAvgScore || 0}/` }}{{ compositionQues?.grdAvgScore || 0 }}
            </div>
            <div class="title">班级/年级平均分</div>
          </div>
        </div>
        <div class="class-situation-content">
          <div class="class-situation-item">
            <div class="value">
              {{ `${compositionQues?.fullRate || 0}%` }}
            </div>
            <div class="title">满分率</div>
          </div>
          <div class="class-situation-item">
            <div class="value">
              {{ `${compositionQues?.fineRate || 0}%` }}
            </div>
            <div class="title">优秀率</div>
          </div>
          <div class="class-situation-item">
            <div class="value">
              {{ `${compositionQues?.passRate || 0}%` }}
            </div>
            <div class="title">及格率</div>
          </div>
          <div class="class-situation-item">
            <div class="value">
              {{ `${compositionQues?.lowRate || 0}%` }}
            </div>
            <div class="title">低分率</div>
          </div>
        </div>
      </div>
    </div>

    <div>
      <div class="titleLine">学生分档</div>
      <div class="student-level">
        <div class="student-level-left">
          <div class="student-level-chart" style="width: 100%; height: 100%" ref="studentLevelChart"></div>
          <div class="student-level-desc">
            <span> <span class="bracket">[</span>：意为包括。例如[5,10)表示分数在5~10之间，包含5。 </span>
          </div>
        </div>
        <div class="student-level-data">
          <div class="student-level-aside">
            <div
              class="student-level-aside-item"
              v-for="(item, index) in stuLvList"
              :key="item.lvName"
              :class="{ active: stuLvListIndex == index }"
              @click="changeStuLvListIndex(index)"
            >
              <span class="lv-name">{{ item.lvName }}</span>
              <span class="lv-count">{{ item.num }} 人</span>
            </div>
          </div>
          <div class="student-level-content">
            <div
              class="student-level-content-item"
              v-if="stuLvList[stuLvListIndex] && stuLvList[stuLvListIndex].data.length > 0"
            >
              <div class="stu-info" v-for="item in stuLvList[stuLvListIndex].data" :key="item.stuName">
                <span class="stu-name">{{ item.stuName }}</span>
                <span class="stu-score">{{ item.score }}分</span>
              </div>
            </div>
            <no-data v-else></no-data>
          </div>
        </div>
      </div>
    </div>

    <div>
      <div class="module-header">
        <div class="titleLine">学生详情</div>
        <div class="header__serarch">
          <el-input
            class="search__text"
            placeholder="输入学号或姓名搜索"
            v-model="searchValue"
            @keyup.enter.native="handleFilterStu"
            clearable
          >
          </el-input>
          <div class="search__icon el-icon-search" @click="handleFilterStu"></div>
        </div>
      </div>
      <el-table
        :data="stuList"
        style="width: 100%"
        stripe
        :header-cell-style="{
          fontSize: '14px',
          color: '#3F4A54',
          backgroundColor: '#f5f7fa',
        }"
        v-sticky-table="0"
      >
        <el-table-column prop="stuNo" label="学号" align="center" :resizable="false" min-width="180" />
        <el-table-column prop="className" label="班级" align="center" :resizable="false" min-width="180">
          <template slot-scope="scope">
            <span>{{ compositionQues?.className }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="stuName" label="姓名" align="center" :resizable="false" min-width="180" />
        <el-table-column prop="score" label="得分" align="center" :resizable="false" width="160">
          <template #header>
            <span>得分</span>
            <i class="score-btn el-icon-view" :class="{ active: isShowScore }" @click="isShowScore = !isShowScore"></i>
          </template>
          <template slot-scope="scope">
            <span v-if="isShowScore">{{ scope.row.score }}</span>
            <span v-else><i class="el-icon-lock"></i></span>
          </template>
        </el-table-column>
        <el-table-column prop="grdRank" label="校排" align="center" :resizable="false" sortable width="160" />
        <el-table-column prop="clsRank" label="班排" align="center" :resizable="false" sortable width="160" />
        <el-table-column label="操作" align="center" :resizable="false" min-width="210">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="showOriginalPaper(scope.row)"> 查看原卷 </el-button>
            <el-button type="text" size="small" @click="goDetail(scope.row)"> 查看报告 </el-button>
            <el-button
              v-if="subjectCenterCode == 'CHINESE'"
              type="text"
              size="small"
              @click="downloadChinesePDF(scope.row)"
              :loading="scope.row.loading"
            >
              下载报告
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 查看原卷 -->
    <QuesPointDialog
      v-if="isShowOriginalPaper"
      append-to-body
      :examId="$sessionSave.get('reportDetail').examId"
      :workId="workId"
      :subjectId="filterData.subjectId"
      :quesNo="compositionQues.quesNo"
      :tQuesNo="compositionQues.tQuesNo"
      :stuId="stuId"
      :stuNo="stuNo"
      :score="score"
      @closed="isShowOriginalPaper = false"
    ></QuesPointDialog>
  </div>
</template>

<script lang="ts">
import HeaderFilter from '@/components/headerFilter.vue';
import NoData from '@/components/noData.vue';
import QuesPointDialog from '@/components/QuesPointDialog.vue';
import { getCompositionQsAPI } from '@/service/pexam';
import { getSubjectCenterCode } from '@/utils/UserRoleUtils';
import { ECharts } from 'echarts';
import { Component, Vue } from 'vue-property-decorator';
import { CompositionExportType, exportCompositionAPI, TeacherCompositionParams } from './CompositionExportUtil';
import { getExamStuPdfUrlAPI } from '@/service/api';

interface CompositionQues {
  classId: string;
  className: string;
  quesNo: number;
  tQuesNo: string;
  quesNoDesc: string;
  clsTotal: number;
  grdTotal: number;
  fullScore: number;
  clsMaxScore: number;
  grdMaxScore: number;
  clsMinScore: number;
  grdMinScore: number;
  clsAvgScore: number;
  grdAvgScore: number;
  fullRate: number;
  fineRate: number;
  passRate: number;
  lowRate: number;
  stuList: StuList[];
  stuLvList: StuLvList[];
}

interface StuList {
  stuId: string;
  stuNo: string;
  stuName: string;
  score: number;
  lv: number;
  lvName: string;
  clsRank: number;
  grdRank: number;
}

interface StuLvList {
  start: number;
  end: number;
  open: string;
  close: string;
  lvName: string;
  num: number;
  data: Datum[];
}

interface Datum {
  stuName: string;
  score: number;
}

@Component({
  components: {
    HeaderFilter,
    NoData,
    QuesPointDialog,
  },
})
export default class CompositionClassSituation extends Vue {
  // 当前选中的筛选项数据
  filterData = {
    classId: '',
    classIds: [],
    subjectId: '',
    phaseId: '',
    xfId: '',
    classList: [],
    aliasName: '', // 组合学科
    qType: 0, // 0:得分 1：赋分
    source: 0, // 0:成绩榜 1：组合成绩榜
    abPaper: '', // -1:普通 0:A卷 1:B卷
    quesInfo: null, // 作文题
  };
  // 当前作文题目
  compositionQues: CompositionQues | null = null;
  // 当前选中的等级索引
  stuLvListIndex: number = 0;
  // 学生等级列表
  stuLvList: StuLvList[] = [];
  // 是否显示原卷
  isShowOriginalPaper = false;

  // 当前学生列表
  stuList: StuList[] = [];
  // 当前学生id
  stuId = '';
  // 当前学生学号
  stuNo = '';
  // 当前学生得分
  score = 0;
  // 学生等级图表
  studentLevelChart: ECharts | null = null;
  // 是否显示得分
  isShowScore = true;
  // 筛选
  searchValue = '';
  // 学科中心编码
  subjectCenterCode = '';
  // 导出loading
  exportLoading = false;

  // 当前workId
  get workId() {
    let subjectId = this.filterData.subjectId;
    let subjectList = this.$sessionSave.get('innerNoRoleSubjectList');
    let subject = subjectList.find(item => item.id == subjectId);
    if (subject) {
      return subject.workIds[Number(this.filterData.abPaper)];
    }
    return '';
  }

  mounted() {}

  beforeDestroy() {
    this.studentLevelChart = null;
  }

  // 更新筛选条件
  updateFilter(filterData) {
    this.filterData = this.$deepClone(filterData);
    this.subjectCenterCode = getSubjectCenterCode(this.filterData.subjectId) || 'ENGLISH';
    this.getCompositionQues();
  }

  // 获取作文题目
  async getCompositionQues() {
    const res = await getCompositionQsAPI({
      examId: this.$sessionSave.get('reportDetail').examId,
      subjectId: this.filterData.subjectId,
      classId: this.filterData.classId,
      tQuesNo: this.filterData.quesInfo?.tQuesNo,
      v: this.$sessionSave.get('reportDetail').v,
      abPaper: this.filterData.abPaper,
    });
    this.compositionQues = res.data || null;
    this.stuList = this.compositionQues?.stuList || [];
    this.stuLvList = this.compositionQues?.stuLvList || [];
    this.stuLvListIndex = this.stuLvList.findIndex(item => item.num > 0);

    this.$nextTick(() => {
      this.initStudentLevelChart();
    });
  }

  // 获取当前等级学生人数
  getLvCount(lv) {
    const stuList = this.compositionQues?.stuList || [];
    return stuList.filter(item => item.lv === lv).length;
  }

  // 切换等级
  changeStuLvListIndex(index) {
    this.stuLvListIndex = index;
  }

  // 初始化学生等级图表
  private initStudentLevelChart(): void {
    if (!this.studentLevelChart) {
      this.studentLevelChart = this.$echarts.init(this.$refs.studentLevelChart as HTMLDivElement);
    }
    this.studentLevelChart.setOption({
      tooltip: {
        show: true,
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '3%',
        right: '8%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: this.stuLvList.map(item => item.lvName),
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        name: '分数',
        nameTextStyle: {
          color: '#409EFF',
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          show: true,
        },
        name: '人数',
        nameTextStyle: {
          color: '#409EFF',
        },
      },
      series: [
        {
          data: this.stuLvList.map(item => {
            return item.num;
          }),
          itemStyle: {
            color: params => {
              if (params.dataIndex == 0) {
                return '#FF6A68';
              } else if (params.dataIndex == this.stuLvList.length - 1) {
                return '#f9cd58';
              } else {
                return '#2fbde8';
              }
            },

            borderRadius: [5, 5, 0, 0],
          },
          barWidth: 40,
          label: {
            show: true,
            position: 'top',
            formatter: '{c}人',
            color: 'inherit',
          },
          type: 'bar',
        },
      ],
    });
  }

  // 显示原卷
  showOriginalPaper(item: StuList) {
    this.stuId = item.stuId;
    this.stuNo = item.stuNo;
    this.score = item.score;
    this.isShowOriginalPaper = true;
  }

  // 跳转详情
  goDetail(item: StuList) {
    this.$router.push({
      path: '/home/<USER>',
      query: {
        subjectId: this.filterData.subjectId,
        classId: this.filterData.classId,
        abPaper: this.filterData.abPaper,
        tQuesNo: this.filterData.quesInfo?.tQuesNo,
        stuId: item.stuId,
      },
    });
  }

  // 筛选学生
  handleFilterStu() {
    if (this.searchValue) {
      this.stuList =
        this.compositionQues?.stuList.filter(
          item => item.stuNo.includes(this.searchValue) || item.stuName.includes(this.searchValue)
        ) || [];
    } else {
      this.stuList = this.compositionQues?.stuList || [];
    }
  }

  // 下载老师报告
  async downloadTeacherReport() {
    const compositionQuesList = this.$sessionSave.get('compositionQuesList');

    const params: TeacherCompositionParams = {
      type: CompositionExportType.TEACHER,
      examName: this.$sessionSave.get('reportDetail').examName,
      examDateTime: this.$sessionSave.get('reportDetail').examDateTime,
      gradeName: this.$sessionSave.get('reportDetail').gradeName,
      className: this.compositionQues?.className,
      classId: this.filterData.classId,
      subjectId: this.filterData.subjectId,
      abPaper: this.filterData.abPaper,
      quesNo: compositionQuesList.map(item => item.quesNo).join(','),
      tQuesNo: compositionQuesList.map(item => item.tQuesNo).join(','),
      examId: this.$sessionSave.get('reportDetail').examId.toString(),
      workId: this.workId,
      v: this.$sessionSave.get('reportDetail').v.toString(),
      userId: this.$sessionSave.get('loginInfo').id,
      schoolId: this.$sessionSave.get('schoolInfo').id,
      token: this.$sessionSave.get('loginInfo').token,
      test: process.env.VUE_APP_BASE_API == 'https://test.iclass30.com' ? '1' : '0',
    };

    this.exportLoading = true;
    try {
      const url = await exportCompositionAPI(params);
      window.open(url, '_blank');
    } catch (error) {
      console.log(error);
      this.$message.error('导出失败');
    } finally {
      this.exportLoading = false;
    }
  }

  // 下载语文报告
  async downloadChinesePDF(item: StuList) {
    try {
      this.$set(item, 'loading', true);
      const res = await getExamStuPdfUrlAPI({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        workId: this.workId,
        stuId: item.stuId,
        quesNo: this.filterData.quesInfo?.quesNo,
        tQuesNo: this.filterData.quesInfo?.tQuesNo,
      });
      if (res.code == 1 && res.data) {
        const url = res.data;
        window.open(url, '_blank');
      } else {
        this.$message.error('未查询到学生作文报告');
      }
    } catch (error) {
    } finally {
      this.$set(item, 'loading', false);
    }
  }
}
</script>

<style scoped lang="scss">
@import './index.scss';
.composition-class-situation {
}

.class-situation {
  display: flex;
  justify-content: space-between;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

  .class-situation-content {
    flex: 1;
    display: flex;
    justify-content: space-around;

    &:first-child {
      margin-right: 24px;
      padding-right: 24px;
      border-right: 1px solid #eee;
    }

    .class-situation-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      text-align: center;
      padding: 12px;
      overflow: hidden;

      .value {
        font-size: 26px;
        font-weight: 700;
        color: #333;
        margin-bottom: 8px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .title {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }
    }
  }
}

.student-level {
  display: flex;
  background: #fff;
  height: 350px;

  .student-level-left {
    flex: 1;
    height: 300px;
  }

  .student-level-desc {
    padding: 5px 10px;
    margin-top: 15px;
    margin-left: 20px;
    margin-right: 20px;
    color: #606266;
    font-size: 13px;
    text-align: center;
    background-color: #f8f8f8;
    border-radius: 2px;

    .bracket {
      color: #5bc7b1;
    }
  }

  .student-level-data {
    flex: 1;
    display: flex;
    border: 1px solid #eee;
    border-radius: 2px;
  }

  .student-level-aside {
    display: flex;
    flex-direction: column;
    width: 200px;
    overflow: auto;

    .student-level-aside-item {
      flex: 1;
      display: flex;
      align-items: center;
      padding: 6px 16px;
      font-weight: 500;
      border-right: 1px solid #eee;
      border-bottom: 1px solid #eee;
      background-color: #fafafb;
      cursor: pointer;

      &:last-child {
        border-bottom: 0;
      }

      &.active {
        border-right: 0;
        font-weight: 700;
        background-color: #fff;
      }

      .lv-icon {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        border: 2px solid #07c29d;
        margin-right: 8px;
      }

      .lv-count {
        flex: 1;
        text-align: right;
      }
    }
  }

  .student-level-content {
    flex: 1;
    padding: 15px;
    overflow: auto;

    .student-level-content-title {
      color: #a19f9d;
      margin-bottom: 15px;
    }

    .student-level-content-item {
      flex: 1;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;

      .stu-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border: 1px solid #eee;
        border-radius: 2px;
        padding: 6px;
        padding-left: 15px;
        padding-right: 15px;

        .stu-name {
        }

        .stu-score {
          color: #a19f9d;
        }
      }
    }
  }
}

.score-btn {
  position: relative;
  margin-left: 10px;
  color: #a19f9d;
  cursor: pointer;

  &::after {
    position: absolute;
    content: '|';
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    transform: rotate(45deg);
  }

  &.active {
    color: #1890ff;
    &::after {
      content: '';
    }
  }

  &:hover {
    color: #1890ff;
  }
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header__serarch {
  display: flex;
  width: 220px;
  .search__icon {
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 18px;
    color: #fff;
    background: #409eff;
    border-radius: 0 3px 3px 0;
    outline: none;
    cursor: pointer;
    text-align: center;
  }
}
</style>

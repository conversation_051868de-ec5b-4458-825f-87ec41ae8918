<template>
  <div class="scan-task-container" v-loading="headloding">
    <template>
      <div class="task-info">
        <!-- <el-page-header class="batch-header" @back="backReport" content=""> </el-page-header> -->
        <div class="exam-info">
          <div class="exam-name-area">
            <p class="exam-name">待处理</p>
            <el-select
              size="small"
              v-model="handleTeacher"
              class="year-select"
              placeholder="请选择"
              @change="changeHandleTeacher"
              filterable
              style="width: 150px"
            >
              <el-option
                v-for="item in teacherList"
                :key="item.id"
                :label="item.realName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <div class="error-handle-btn">
            <Button class="btn" type="default" @click="getTeacherExams('refresh')">刷新</Button>
          </div>
        </div>
        <div class="scan-task-list-box" v-if="handleingExamList.length > 0">
          <!--扫描批次数据-->
          <el-table
            :data="handleingExamList"
            v-loading="listLoading"
            :row-class-name="tableRowClassName"
            style="width: 100%"
            class="bacth-table"
            :header-cell-style="headerStyle"
            height="300"
            v-load-more.expand="{
              func: loadListMore,
              target: '.el-table__body-wrapper',
              distance: 100,
              delay: 100,
            }"
          >
            <el-table-column prop="examTitle" show-overflow-tooltip label="试卷名称" width="260">
            </el-table-column>
            <el-table-column prop="classNames" show-overflow-tooltip label="班级" width="260">
            </el-table-column>
            <el-table-column prop="scanCount" label="已扫描"> </el-table-column>
            <el-table-column prop="errorNum" label="异常">
              <template slot-scope="scope">
                <span class="task_error_num" :class="{ error_no_zero: scope.row.errorNum > 0 }">{{
                  scope.row.errorNum
                }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="missCount" label="缺考">
              <template slot="header">
                缺考
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="缺考人数 = 未扫描 + 填涂缺考 + 考号异常未处理"
                  placement="top"
                >
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                <span>{{ scope.row.missCount }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <template v-if="scope.row.errorNum == 0 && scope.row.source == 3">
                  <el-button type="text" @click="openPublishDialog(scope.$index, scope.row)">
                    发布成绩
                  </el-button>
                </template>
                <el-button v-else type="text" @click="onGogoHandleError(scope.$index, scope.row)"
                  >处理异常
                </el-button>
                <el-button
                  style="color: #333"
                  type="text"
                  @click="toNoScanNumPage(scope.$index, scope.row)"
                  >缺考名单
                </el-button>
                <el-button
                  style="color: #333"
                  type="text"
                  @click="handleViewImage(scope.$index, scope.row)"
                  >查看详情
                </el-button>
                <!-- <el-dropdown class="deal-dropdown-menu">
                  <span class="el-dropdown-link">
                    <i slot="reference" class="el-icon-more"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="toNoScanNumPage(scope.$index, scope.row)"
                      >查看缺考
                    </el-dropdown-item>
                    <el-dropdown-item @click.native="handleViewImage(scope.$index, scope.row)"
                      >查看详情
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown> -->
              </template>
            </el-table-column>
          </el-table>
          <!--分页器-->
          <!-- <el-pagination
            background
            style="margin-top: 10px"
            :hide-on-single-page="!handleingExamList.length"
            class="text-center"
            layout="total, prev, pager, next"
            @current-change="changeHandleingPage"
            :current-page.sync="handleingPagination.page"
            :page-size="handleingPagination.limit"
            :total="handleingPagination.total_rows"
          >
          </el-pagination> -->
        </div>
        <div class="nodata flex_1" v-else>
          <img :src="noResImg" alt="" />
          <p class="text-center">暂无数据</p>
        </div>
      </div>
    </template>
    <!-- 已处理 -->
    <div class="handled-info">
      <div class="exam-info">
        <div class="exam-name-area">
          <p class="exam-name">已处理</p>
          <el-select
            size="small"
            v-model="yearValue"
            class="year-select"
            @change="changeYear"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option v-for="item in years" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
          <el-select
            size="small"
            v-model="processTeacher"
            class="teacher-select"
            placeholder="请选择"
            filterable
            style="width: 150px"
            @change="changeProcessTeacher"
          >
            <el-option
              v-for="item in teacherList"
              :key="item.id"
              :label="item.realName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <div class="error-handle-btn">
          <Button class="btn" type="default" @click="getPublishExamList('refresh')">刷新</Button>
        </div>
      </div>
      <!-- 已处理考试列表 -->
      <exam-list
        v-if="examList.length > 0"
        :examList="examList"
        :pagination="pagination"
        @handleCurrentChange="handleCurrentChange"
        @look-report="lookReport"
        v-loading="tableLoading"
      ></exam-list>
      <div class="nodata flex_1" v-else>
        <img :src="noResImg" alt="" />
        <p class="text-center">暂无数据</p>
      </div>
    </div>

    <!-- 发布成绩弹窗 -->
    <publish-score-dialog
      v-if="isPublishScoreDialog"
      :examInfo="publishScoreInfo"
      @close-publish="closePublish"
      @publish-score="publishScore"
    ></publish-score-dialog>
    <!--自定义报告弹框-->
    <look-report
      :reportInfo="reportInfo"
      @closeDialog="closeDialog"
      @lookReport1="lookReport1"
      v-if="lookReportDialogVisible"
    ></look-report>
  </div>
</template>

<script>
import {
  getSchoolGrade,
  getSubjectsAPI,
  getUserInfoAPI,
  publishScoreAPI,
  searchScanPaperWorkList,
  getSchoolTeacher,
} from '@/service/api';
import lookReport from '@/components/lookReport.vue';
import PublishScoreDialog from '@/components/PublishScoreDialog.vue';
import {
  getExamInfoAPI,
  getExamReportList,
  getPersonalBookInfo,
  getPublishExamList,
  getTeacherExams,
  updateTaskExam,
} from '@/service/pexam';
import { getQueryString } from '@/utils';
import { reviseListMap } from '@/utils/common';
import UserRole from '@/utils/UserRole';
import { Loading } from '@iclass/element-ui';
import ExamList from './modules/examList.vue';
import { loginByUserId, loginByUserToken } from '@/utils/login';
import store from '@/store/index';

export default {
  name: 'scan-task',
  data() {
    return {
      // 表格头部样式
      headerStyle: {
        background: '#F5F7FA ',
        color: '#606266',
        fontWeight: '400',
        fontSize: '14px',
        width: '100%',
        height: '40px',
      },
      noResImg: require('@/assets/no-res.png'),
      schoolId: getQueryString('schoolId'),
      userId: getQueryString('userId') || getQueryString('userid'),
      listLoading: false,
      showEditDlg: false,
      isEdit: 1,
      examData: [],
      isPublishScoreDialog: false,
      publishScoreInfo: {},
      //加载中状态
      scoreLoading: null,
      askId: this.$route.query.id || this.$route.query.Id,
      examReportId: '',
      headloding: false,
      reDetectDlg: {
        show: false,
        taskId: '',
        preId: '',
      },
      taskInfo: {},
      subjectList: [],
      examList: [],
      gradeList: [],
      dlgExamInfo: {
        gradeId: '',
        selectPhase: 0,
        subjectId: '',
        examId: '',
        loading: false,
      },
      taskId: '',
      classIds: [],
      subjectId: '',
      // 分页
      pagination: {
        page: 1,
        limit: 10,
        total_rows: 0,
      },
      examList: [],
      // 学年列表
      years: [],
      yearValue: '',
      reportInfo: {},
      lookReportDialogVisible: false,
      //待处理考试列表
      handleingExamList: [],
      //待处理考试列表分页
      handleingPagination: {
        page: 1,
        limit: 20,
        total_rows: 0,
        pageCount: 1,
      },
      teacherList: [],
      handleTeacher:
        this.$sessionSave.get('handleTeacher') ||
        getQueryString('userId') ||
        getQueryString('userid'),
      processTeacher:
        this.$sessionSave.get('processTeacher') ||
        getQueryString('userId') ||
        getQueryString('userid'),
      tableLoading: false,
    };
  },
  components: {
    PublishScoreDialog,
    ExamList,
    lookReport,
  },
  computed: {
    gradeSubjectList() {
      if (!this.dlgExamInfo.selectPhase) {
        return this.subjectList;
      }
      return this.subjectList.filter(it => it.phase === this.dlgExamInfo.selectPhase);
    },
  },
  created() {},
  mounted() {
    this.$sessionSave.set('lookReportPage', '');
    // this.$sessionSave.set('handleTeacher', this.handleTeacher);
    // this.$sessionSave.set('processTeacher', this.processTeacher);
    this.getMainSubject();
    this.getTeacherExams();
    this.getYearList();
    this.getUserInfo();
    this.getTeacherList();
  },
  methods: {
    /***
     * @name:返回上一页
     */
    backReport() {
      this.$router.back();
    },
    async getTeacherList() {
      let _this = this;
      let params = {
        schoolId: _this.schoolId,
        page: 1,
        limit: 1000,
      };
      await getSchoolTeacher(params)
        .then(function (result) {
          if (result.code == 1) {
            _this.teacherList = result.data.rows;
          }
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    /**
     * @name:切换待处理老师
     */
    changeHandleTeacher(val) {
      this.listLoading = true;
      this.handleingPagination.page = 1;
      this.$sessionSave.set('handleTeacher', this.handleTeacher);
      this.getTeacherExams();
    },
    /**
     * @name:切换已处理老师
     */
    async changeProcessTeacher(val) {
      this.tableLoading = true;
      await loginByUserId(val);
      this.$sessionSave.set('lookReportPage', '');
      let loginInfo = this.$sessionSave.get('loginInfo');
      let schoolInfo = this.$sessionSave.get('schoolInfo');
      if (getQueryString('schoolId') && getQueryString('schoolName')) {
        schoolInfo = {
          id: getQueryString('schoolId'),
          school_name: getQueryString('schoolName'),
        };
      } else if (!schoolInfo) {
        schoolInfo = {
          id: loginInfo.schoolid,
          school_name: loginInfo.school_name,
        };
      }
      if (schoolInfo) {
        store.commit('saveSchoolInfo', schoolInfo);
      }
      UserRole.init();
      this.$sessionSave.set('processTeacher', val);
      this.getMainSubject();
      this.getUserInfo();
    },
    // 获取学科教师学科 {学科: 班级}
    async getMainSubject() {
      const info = await UserRole.getUserInfoPersonalityTest();
      this.subjectId = info.subjectId;
      const classlist = await UserRole.utils.getRoleClassByRoleType('6');
      this.classIds = classlist.map(item => item.id);
      this.getPublishExamList('refresh');
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getPublishExamList();
    },
    changeYear(val) {
      this.getPublishExamList('refresh');
    },
    /**
     * @name:获取发布考试列表
     */
    getPublishExamList(type) {
      if (type == 'refresh') this.pagination.page = 1;
      let params = {
        schoolId: this.schoolId,
        acadYearsId: this.yearValue,
        subjectId: this.subjectId,
        keyWord: '',
        clzId: this.classIds.join(','),
        orderType: 1, //排序规则 0:按照考试时间 1:按照创建时间
        desc: 1, //desc 排序是否按照降序 0:否 1:是
        page: this.pagination.page, // 页码
        pageSize: this.pagination.limit,
      };
      getPublishExamList(params)
        .then(res => {
          this.examList = res.data.list;
          this.pagination.total_rows = res.data.total;
          this.tableLoading = false;
        })
        .catch(err => {
          this.examList = [];
          console.log(err);
        });
    },
    getYearList() {
      if (this.years.length > 0) return;
      let date = new Date(),
        y = date.getFullYear(),
        M = date.getMonth(),
        d = date.getDate();
      for (let i = 0; i < 5; i++) {
        let y1 = y - i - 1;
        let y2 = y - i;
        if (Number(`${M + 1}${d < 10 ? `0${d}` : d}`) >= 810) {
          y1 = y - i;
          y2 = y - i + 1;
        }
        this.years.push({
          id: `${Number(y1.toString().substring(1))}${Number(y2.toString().substring(1))}`,
          name: `${y1}-${y2}学年`,
        });
      }
      // this.years.unshift({ id: '', name: '全部学年' });
      this.yearValue = this.years[0].id;
    },

    // 查看报告
    async lookReport(item) {
      // await this.getExamClassList();
      let params = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        parentId: item.examId,
        acadYearsId: '',
        acadTermId: '',
        gradeId: '',
        subjectId: '',
        categoryId: '',
        keyWord: '',
        page: 1, // 页码
        pageSize: 10,
      };
      const res = await getExamReportList(params);
      if (res.code == 1) {
        if (res.data.list.length == 0) {
          //无新增的报告
          const res = await this.getExamInfoByExamId(item);
          this.lookReport1(res);
        } else {
          this.lookReportDialogVisible = true;
          this.reportInfo = item;
        }
      } else {
        this.$message({
          message: '查看报告失败！',
          type: 'error',
          duration: 1000,
        });
      }
    },

    getUserInfo() {
      return getUserInfoAPI({
        userId: this.$sessionSave.get('loginInfo').id,
      })
        .then(res => {
          let subClass = res.data.substituteClassList;
          let subjectClz = reviseListMap(subClass, 'subjectName', 'className');
          this.$sessionSave.set('subjectClz', subjectClz);
          let mapSub = reviseListMap(subClass, 'subjectId', 'className');
          this.$sessionSave.set('subjectIdClz', mapSub);
        })
        .catch(err => {});
    },

    lookReport1(item) {
      this.lookReportDialogVisible = false;
      this.$sessionSave.set('currentRoles', '6');
      this.$sessionSave.set('innerClassList', null);
      this.$sessionSave.set('innerSubjectList', null);
      this.$sessionSave.set('loadComment', true);
      this.$sessionSave.set('reportDetail', item);
      this.$sessionSave.remove('subjectId');
      let dt = new Date(item.dateModified).getTime();
      this.$sessionSave.set('downLoadState', item.statState);
      this.$sessionSave.set('lookReportPage', 'deal');
      this.$router.push({ path: '/home/<USER>', query: { fromPage: 'deal' } });
    },

    async getExamInfoByExamId(item) {
      const params = {
        examId: item.examId,
      };
      const res = await getExamInfoAPI(params);
      if (res.code == 1) {
        res.data.examId = item.examId;
        res.data.examName = item.examName;
        res.data.classNames = item.clzNames;
        res.data.paperList = res.data.pBookList;
        res.data.paperList.map(i => {
          i.subectId = i.subjectRealId;
          i.workId = i.id;
        });
        return res.data;
      }
    },

    closeDialog() {
      this.lookReportDialogVisible = false;
    },
    loadListMore() {
      console.log('滚动到底部了', this.handleingPagination.page);
      if (this.handleingPagination.page >= this.handleingPagination.pageCount) return;
      this.handleingPagination.page++;
      this.getTeacherExams();
    },
    //获取未处理考试列表
    getTeacherExams(type) {
      if (type == 'refresh') this.handleingPagination.page = 1;
      let params = {
        schoolId: this.schoolId || this.$sessionSave.get('schoolInfo').id,
        teacherId: this.handleTeacher,
        page: this.handleingPagination.page,
        pageSize: this.handleingPagination.limit,
      };
      getTeacherExams(params)
        .then(res => {
          if (res.code == 1) {
            this.handleingPagination.total_rows = res.data.total;
            this.handleingPagination.pageCount = Math.ceil(
              res.data.total / this.handleingPagination.limit
            );
            if (this.handleingPagination.page == 1) {
              this.handleingExamList = res.data.list;
            } else {
              this.handleingExamList = this.handleingExamList.concat(res.data.list);
            }
            this.handleingExamList.forEach(item => {
              item.classNames = item.scanClass
                .map(it => it.className + '(' + it.stuCount + ')')
                .join(',');
              let missCount = 0;
              let scanCount = 0;
              if (item.scanClass.length > 0) {
                item.scanClass.forEach(it => {
                  missCount += it.stuCount - it.scanCount;
                  scanCount += it.scanCount;
                });
              }
              item.missCount = missCount;
              item.scanCount = scanCount;
            });
            this.listLoading = false;
          }
        })
        .catch(err => {
          this.listLoading = false;
          this.handleingExamList = [];
          console.log(err);
        });
    },
    changeHandleingPage(val) {
      this.handleingPagination.page = val;
      this.getTeacherExams();
    },
    /**
     * @name:关联考试跳转到登录页
     */
    relationExam(data, row) {
      this.taskId = row.id;
      this.showEditDlg = true;
    },
    tableRowClassName({ row, rowIndex }) {
      return '';
    },
    handleCommand(index) {},
    /**
     * @name:跳转到查看全部批次
     */
    handleViewImage(index, item) {
      this.$router.push({
        path: '/scan_task',
        query: {
          examId: item.examId,
          paperNo: item.paperNo,
          personBookId: item.examId,
          schoolId: this.schoolId,
          isEdit: 2,
          teacherId: this.handleTeacher,
          cardType: item.cardType,
        },
      });
    },
    /**
     * @name:处理异常
     */
    onGogoHandleError(index, item) {
      this.$router.push({
        path: '/scan/errornew',
        query: {
          examId: item.examId,
          paperNo: item.paperNo,
          examName: item.examTitle,
          schoolId: this.schoolId,
          personBookId: item.examId,
          fromPage: 'deal',
          teacherId: this.handleTeacher,
        },
      });
    },
    /**
     * @name:跳转到未扫名单列表
     */
    toNoScanNumPage(index, item) {
      this.$router.push({
        path: '/scan_not',
        query: {
          workId: item.examId,
          schoolId: this.schoolId,
          userId: this.handleTeacher,
        },
      });
    },
    /**
     * @name:打开发布成绩弹窗
     */
    openPublishDialog(index, item) {
      let paperInfo = {
        personBookId: item.examId,
        subectName: item.subjectName,
      };
      this.publishScoreInfo.examId = item.examId;
      this.publishScoreInfo.examName = item.examTitle;
      this.publishScoreInfo.paperInfo = paperInfo;
      this.publishScoreInfo.schoolId = this.schoolId;
      this.isPublishScoreDialog = true;
      this.getPersonalBookInfo();
    },
    /**
     * @name:获取考试id
     */
    async getPersonalBookInfo() {
      let res = await getPersonalBookInfo({
        id: this.publishScoreInfo.examId,
      });
      if (res.code == 1) {
        this.examReportId = res.data.examId;
      } else {
        this.examReportId = '';
      }
    },
    /**
     * @name:发布成绩
     */
    async publishScore() {
      this.isPublishScoreDialog = false;
      await publishScoreAPI({
        schoolId: this.publishScoreInfo.schoolId,
        workId: this.publishScoreInfo.examId,
      })
        .then(res => {
          this.scoreLoading = Loading.service({
            lock: true,
            text: '正在生成分析报告，请稍候，预计5-20s，若长时间未生成，请刷新页面重试',
            background: 'rgba(0, 0, 0, 0.7)',
          });
          this.checkingScoreStatus();
        })
        .catch(err => {
        });
    },
    /**
     * @name:检查发布成绩状态
     */
    checkingScoreStatus() {
      setTimeout(async () => {
        let params = {
          examId: this.examReportId,
        };
        getExamInfoAPI(params)
          .then(res => {
            if (res.data.dataState >= 1) {
              this.scoreLoading.close();
              this.$message({
                message: '发布成功！',
                type: 'success',
                duration: 1500,
              });
              this.getTeacherExams();
            } else {
              this.checkingScoreStatus();
            }
          })
          .catch(err => {
            this.scoreLoading.close();
          });
      }, 5000);
    },
    /**
     * @name:关闭发布成绩弹窗
     */
    closePublish() {
      this.isPublishScoreDialog = false;
    },
    onSelectExamChange(item) {
      console.log(this.dlgExamInfo.examId);
    },
    /**
     * 获取年级学科列表
     * @param schoolId
     */
    getGradeSubjectInfo(schoolId) {
      if (!schoolId) return;
      // 获取年级列表
      if (this.gradeList.length === 0) {
        getSchoolGrade({
          schoolId: schoolId,
        }).then(res => {
          this.gradeList = res.data;
        });
      }

      if (this.subjectList.length === 0) {
        getSubjectsAPI({
          schoolId: schoolId,
        }).then(res => {
          this.subjectList = res.data;
        });
      }
    },
    onSelectGradeChange(gradeId) {
      let phase = 0;
      if (gradeId >= 1 && gradeId <= 6) {
        phase = 1;
      } else if (gradeId >= 7 && gradeId <= 9) {
        phase = 2;
      } else if (gradeId >= 10 && gradeId <= 12) {
        phase = 3;
      }
      this.dlgExamInfo.selectPhase = phase;
      this.getSchoolExamList();
    },
    /**
     * 获取考试列表
     */
    getSchoolExamList() {
      this.queryExamList(this.dlgExamInfo.query);
    },
    queryExamList(query) {
      this.dlgExamInfo.query = query;
      if (this.dlgExamInfo.loading) return;
      if (!this.dlgExamInfo.gradeId || !this.dlgExamInfo.subjectId) {
        // return
      }
      this.dlgExamInfo.loading = true;
      searchScanPaperWorkList({
        schoolId: this.schoolId,
        keyWord: query,
        gradeId: this.dlgExamInfo.gradeId,
        subjectId: this.dlgExamInfo.subjectId,
      })
        .then(res => {
          this.dlgExamInfo.loading = false;
          this.examList = res.data.rows;
          if (this.dlgExamInfo.examId) {
            let examInfo = this.examList.find(it => it.workId === this.dlgExamInfo.examId);
            if (!examInfo) {
              this.dlgExamInfo.examId = '';
            }
          }

          // 查询条件变化，再次查询
          if (query !== this.dlgExamInfo.query) {
            this.queryExamList(this.dlgExamInfo.query);
          }
        })
        .catch(data => {
          this.dlgExamInfo.loading = false;
        });
    },
    // 确定关联考试
    onSaveExamInfo() {
      if (!this.dlgExamInfo.examId) {
        this.$message.error('没有选择考试');
        return;
      }
      updateTaskExam({
        taskId: this.taskId,
        examId: this.dlgExamInfo.examId,
      }).then(res => {
        this.showEditDlg = false;
        this.$message.success('更新成功');
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.scan-task-container {
  .exam-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #000;
    p {
      margin: 10px;
    }
    span {
      margin: 0px 5px;
      font-size: 16px;
    }
    .exam-name {
      font-size: 18px;
      font-weight: bold;
      color: #161e26;
    }
    .exam-change {
      position: absolute;
      bottom: 100px;
    }
    .info-row {
      display: flex;
      align-items: center;
    }
    .info-item {
      display: flex;
      align-items: center;
      height: 24px;
      background: #f0f2f5;
      border-radius: 12px;
      margin-right: 8px;
      padding: 6px;
      .info-subject {
        font-weight: 400;
        color: #606266;
        line-height: 24px;
        padding: 10px;
      }
    }
    .info-nums {
      color: #303233;
      font-weight: bold;
    }
  }

  .task-info {
    flex: 1;
    .error-handle-btn {
      float: right;
      .btn {
        margin-left: 10px;
      }
    }
    .scan-task-list-box {
      // margin-top: 20px;
      // border: solid #f2f2f2;
      .task_progress_label {
        color: #3a5eff;
      }
      .task_progress {
        width: 200px;
        display: inline-block;
        ::v-deep .el-progress-bar {
          padding-right: 65px;
          margin-right: -70px;
        }
      }
    }
  }
  .nodata {
    width: 100%;
    height: auto;
    /*background : url("../assets/no-res.png") center center no-repeat;*/
    img {
      display: block;
      margin: 0 auto;
    }
  }

  .alter-tips {
    color: #ff4646;
    margin-left: 40px;
    padding: 0 20px 20px;
  }

  .error_no_zero {
    color: #ff4646;
    font-weight: bold;
  }

  ::v-deep .el-table__row.sum_table_info {
    background: #f5f7fa;
  }
  .el-dropdown-link {
    margin-left: 10px;
    color: unset;
  }
}
.handled-info {
  margin-top: 10px;
}
.batch-header {
  padding: 0px 0px 5px 0px;
}
.operate-row {
  display: flex;
  align-items: center;
}
.publish-button {
  background: #ff7c1f;
}
.error-button {
  background: #008dea;
}
.success-button,
.publish-button,
.error-button {
  width: 72px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.no-totaldata {
  width: 100%;
  height: 100%;
  .no-data-button {
    display: block;
    margin: 0 auto;
    margin-top: 10%;
  }
}
.exam-name-area {
  display: flex;
  align-items: center;
}
.teacher-select {
  margin-left: 15px;
}
</style>

<style lang="scss">
.deal-dropdown-menu {
  .el-icon-more {
    width: 40px;
    text-align: center;
    transform: rotate(90deg);
  }
}
</style>

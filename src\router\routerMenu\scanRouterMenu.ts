export default {
  path: '/scan',
  name: 'scan',
  component: resolve => require(['../../pages/home'], resolve),
  children: [
    {
      path: '',
      component: resolve => require(['../../pages/scan/record'], resolve),
    },
    {
      path: 'success',
      component: resolve => require(['../../pages/scan/success'], resolve),
    },
    {
      path: 'error',
      component: resolve => require(['../../pages/scan/error'], resolve),
    },
    {
      path: 'errornew',
      component: resolve => require(['../../pages/scan/errornew'], resolve),
    },
    {
      path: 'insider',
      component: resolve => require(['../../pages/scan/insider'], resolve),
    },
    {
      path: 'batch',
      component: resolve => require(['../../pages/scan/batch'], resolve),
    },
    {
      path: 'deal',
      component: resolve => require(['../../pages/scan/deal'], resolve),
    },
  ],
};

<template v-on="$listeners">
  <div class="stuPage  display_flex align-items_flex-start" v-loading="stuListLoading">
    <div class="stuPage-left display_flex flex-direction_column" v-if="stuList.length"  
      :style="{ minHeight: listHeight + 'px' }">

      <div style="position: sticky;top: 0px;background-color: #FFF;">
        <el-checkbox v-model="attentionChecked" class="attention-checkbox"
        style="position: sticky; top: 0; margin-right: 10px;">关注标记</el-checkbox>
        <el-popover placement="top-start" title="" width="500" trigger="hover">
          <div style="line-height: 25px; font-size: 13px">
            知识点列表：按照班级知识点得分从低到高排序 <br />
            学生列表：按照关注学生、下降、波动、平稳、上升排序
          </div>
          <i class="el-icon-question" slot="reference"></i>
        </el-popover>
      </div>


      <div class="header" style="position: sticky; top: 70px ">
        <div class="header__serarch clearfix display_flex">
          <el-input class="search__text" placeholder="请输入学生姓名或学号" v-model="searchValue" @keyup.enter.native="searchReport"
            clearable>
          </el-input>
          <div class="search__icon el-icon-search display_flex align-items_center justify-content_flex-center"
            @click="searchReport"></div>
        </div>
      </div>
      <ul class="stuList flex_1 list-none" v-loading="stuListLoading">
        <li v-for="(item, index) in stuList" :key="index" :class="{ 'odd-item': index % 2 !== 0 }"
          :title="`${item.stuName}${item.stuNo}`">
          <div class="li-inner display_flex justify-content_space-between">
            <div class="left flex_1 display_flex align-items_center">
              <i class="star el-icon-star-on" :style="{ visibility: attentionChecked ? '' : 'hidden' }"
                :class="item.isFollow ? 'active' : ''" @click.stop="starStudent(item)"></i>
              <span style="height: 70px" class="flex_1 text-ellipsis">{{
                item.stuName
              }}</span>
            </div>
            <div class="right display_flex align-items_center">
              <i class="type-icon" :class="!item.status
                    ? ''
                    : item.status === 1
                      ? 'goDown'
                      : item.status === 2
                        ? 'wave'
                        : item.status === 3
                          ? 'smooth'
                          : 'goUp'
                  ">
              </i>
              {{ !item.status ? "--" : item.status === 1
                ? ' 下降'
                : item.status === 2
                  ? ' 波动'
                  : item.status === 3
                    ? ' 平稳'
                    : ' 上升' }}
            </div>

          </div>
        </li>
      </ul>
    </div>
    <div class="stuPage-right" v-if="stuList.length">
      <div class="header" v-if="pointList.length" style="position: sticky;top: 0; z-index: 10;">
        <span @click="openScoreRateDialog" class="getscore"><span>得分率设置</span> <i class="el-icon-setting"></i></span>
        <div v-for="(input, index) in inputs.slice(0, -1)" :key="index"
          style="margin:0 10px;display: flex;align-items: center;">
          <span :class="`icon color${index + 1}`"> </span><span>[{{ input }}~{{ index === 3 ? inputs[index + 1] + ']' :
            inputs[index + 1] + ')' }}</span>
        </div>
      </div>
      <div class="content">
        <div style="position: sticky; top: 70px;z-index: 200; ">
          <i class="el-icon-arrow-left icon" @click="decrementPage" v-if="pointList.length"
            :style="{ cursor: pagination.page == 1 ? 'not-allowed' : 'pointer' }"></i>
          <i class="el-icon-arrow-right icon"
            :style="{ ...arrowRightStyle, cursor: pagination.page == Math.ceil(pagination.total_rows / 8) ? 'not-allowed' : 'pointer' }"
            @click="incrementPage" v-if="pointList.length">
          </i>
        </div>


        <!-- 表格 -->
        <el-table :header-cell-style="{ background: '#EEF0F9' }" :data="stuList" stripe
          v-if="pointList && pointList.length && stuList[0].point1 && stuList"  cell-class-name="cust-table">
          <div v-for="(point, index) in pointList" :key="index">
            <el-table-column :prop="`point${index + 1}`" :label="point.pointName" align="center">
              <template slot="header" slot-scope="scope">
                <div class="table-header-text" :title="point.pointName">
                  {{ point.pointName }}
                </div>
              </template>
              <template slot="default" slot-scope="scope">
                <div :class="getColorClass(scope, index)">
                  {{ scope.row[`point${index + 1}`] !== '--' ? (scope.row[`point${index + 1}`] *
                    100).toFixed(2).toString()
                    + '%' : '--' }}</div>

              </template>
            </el-table-column>
          </div>
          <el-table-column label="操作" width="135">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row)">查看详情 <i
                  class="el-icon-arrow-right"></i></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>


    </div>
    <div class="nodata flex_1" v-if="!stuList.length">
      <img :src="noResImg" alt="" />
      <p class="text-center">暂无数据</p>
    </div>
    <set-score-rate v-if="isShowDialog" :inputs="inputs" @closeDialog="closeDialog"
      @submitInputs="handleInputs"></set-score-rate>
  </div>
</template>

<script>
import setScoreRate from "@/components/setScoreRate.vue";
import {
  listClsStu,
  followStu,
  listClsWeakPoint
} from "@/service/pexam";
export default {
  name: "student",
  components: { setScoreRate },
  data() {
    return {
      inputs: ['0', '0.6', '0.7', '0.9', '1'],
      isShowDialog: false,
      listLoading: false,
      listHeight: 200,
      selectStuIndex: 0,
      selectStuName: "",
      attentionChecked: true,
      lookWeakPoint: true,
      stuListLoading: false,
      searchValue: "",
      stuList: [],
      copystuList: [],
      originStuList: [],
      scoreVal: 0,
      pointList: [],
      noResImg: require("../../assets/no-res.png"),
      // 分页
      pagination: {
        page: 1,
        pageSize: 8,
        total_rows: 0,
      },
    };
  },
  mounted() {
    // 初始化班级学生列表
    this.listClsStu();
  },
  computed: {
    //右箭头位置
    arrowRightStyle() {
      return {
        left: 'calc(100% - 140px)'
      };
    },
    //设置得分率颜色
    getColorClass() {
      return function (scope, index) {
        const pointValue = Number(scope.row[`point${index + 1}`]);
        for (let i = 3; i >= 0; i--) {
          if (pointValue >= this.inputs[i]) {
            return `color${i + 1}`;
          }
        }
      }
    }

  },
  methods: {
    /**
     * @name:获取用户填写的得分率数据
     */
    handleInputs(inputs) {
      this.inputs = inputs;
    },
    /**
     * @name:关闭弹窗
     */
    closeDialog() {
      this.isShowDialog = false;
    },
    openScoreRateDialog() {
      this.isShowDialog = true
    },
    /**
     * @name:查看学生详情
     */
    handleClick(item) {
      this.$sessionSave.remove("studentDetail");
      this.$sessionSave.set("studentDetail", item);
      this.$router.push({
        path: "/home/<USER>/studentDetail",
      });
    },
    /**
     * @name:请求上一页知识点数据
     */
    decrementPage() {
      if (this.pagination.page > 1) {
        this.pagination.page -= 1;
        this.listClsWeakPoint()
      }
    },
    /**
     * @name:请求下一页知识点数据
     */
    incrementPage() {
      if (this.pagination.page < Math.ceil(this.pagination.total_rows / 8)) {
        this.pagination.page += 1;
        this.listClsWeakPoint()
      };
    },
    /**
     * @name:关注学生
     */
    starStudent(item) {
      this.selectStuIndex = item.stuId;
      this.selectStuName = item.stuName;
      let pms = this.$listeners.getParams();
      followStu({
        teaId: this.$sessionSave.get("loginInfo").id,
        classId: pms.classId,
        className: pms.className,
        subjectId: pms.subjectId,
        studentId: this.selectStuIndex,
        studentName: this.selectStuName,
        isFollow: !item.isFollow,
      }).then(( ) => {
        this.listClsStu();
      });
    },
    /**
     * @name:获取班级学生
     */
    listClsStu() {
      this.stuListLoading = true;
      listClsStu({
        teaId: this.$sessionSave.get("loginInfo").id,
        ...this.$listeners.getParams(),
      })
        .then((res) => {
          this.stuListLoading = false;
          this.originStuList = res.data;
          this.stuList = res.data;
          this.pagination.page = 1;
          this.listClsWeakPoint();
        })
        .catch((err) => {
          this.stuListLoading = false;
        });
    },
    /**
     * @name:获取知识点列表
     */
    listClsWeakPoint() {
      this.listLoading = true;
      listClsWeakPoint({
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
        sort: "",
        q: "",
        ...this.$listeners.getParams()
      }).then(res => {
        this.listLoading = false;
        this.pointList = res.data.list || [];
        if (!this.pointList.length && this.stuList.length) {
          this.$sessionSave.remove("studentDetail");
          this.$sessionSave.set("studentDetail", this.stuList[0]);
          this.$router.push('/home/<USER>/studentDetail');
        }
        this.pointList.forEach((item, index) => {
          let rateMap = item.rateMap;
          this.stuList.forEach(stu => {
            if (rateMap.hasOwnProperty(stu.stuId)) {
              stu[`point${index + 1}`] = rateMap[stu.stuId];
            } else {
              stu[`point${index + 1}`] = '--';
            }
          });
        });
        this.$forceUpdate();
        this.pagination.total_rows = res.data.total || 0;
      })
        .catch((err) => {
          this.listLoading = false;
        });
    },
    /**
     * @name:搜索
     */
    searchReport() {
      let stuList = [];
      this.originStuList.forEach((item) => {
        if (
          item.stuName.indexOf(this.searchValue) >= 0 ||
          item.stuNo.indexOf(this.searchValue) >= 0
        ) {
          stuList.push(item);
        }
      });
      this.stuList = stuList;
    },
  },
};
</script>

<style lang="scss" scoped>
.stuPage {
  width: 100%;
  background: #fff;

  >div {
    background: #fff;
    border-radius: 6px;
  }

  .color1 {
    color: #FF5F5A;
  }

  .color2 {
    color: #FFBB19;
  }

  .color3 {
    color: #5F9EFF;
  }

  .color4 {
    color: #39CEB1;
  }

  .stuPage-left {
    width: 17%;
    margin-right: 16px;
    padding: 0 0 24px 21px;

    .attention-checkbox {
      padding: 24px 0 22px 6px;
      font-size: 16px;
      height: 70px;
      background: #fff
    }

    .header {
      background-color: #EEF0F9;
      border-radius: 4px 4px 0px 0px;

      .header__serarch {
        display: flex;
        width: 200px;
        margin: 16px 17px 14px;

        .search__icon {
          width: 48px;
          height: 40px;
          font-size: 18px;
          color: #fff;
          background: #409eff;
          border-radius: 0 3px 3px 0;
          outline: none;
          cursor: pointer;
        }
      }
    }

    .stuList {
      width: 100%;

      overflow-y: auto;
      overflow-x: hidden;

      >li {
        padding: 0 28px;
        cursor: pointer;
        height: 55px;
        line-height: 70px;
        font-size: 16px;
        color: #3f4a54;

        &:last-child {
          border: none;
        }

        .li-inner {
          width: 100%;
          height: 100%;
          overflow: hidden;

          // border-bottom: 1px solid #e7eaed;
          .left {
            font-size: 16px;
            color: #3f4a54;

            .star {
              margin-right: 3px;
              font-size: 24px;
              color: #d5dbdf;
              flex-shrink: 0;

              &.active {
                color: #ffb400;
              }
            }
          }

          .right {
            font-size: 14px;
            color: #717980;

            .type-icon {
              margin-right: -5px;
              display: inline-block;
              width: 22px;
              height: 20px;
            }

            .goUp {
              background: url("../../assets/goUp.png") center center no-repeat;
            }

            .goDown {
              background: url("../../assets/goDown.png") center center no-repeat;
            }

            .smooth {
              background: url("../../assets/smooth.png") center center no-repeat;
            }

            .wave {
              background: url("../../assets/wave.png") center center no-repeat;
            }
          }
        }
      }

      .odd-item {
        background: #FAFAFA;
      }
    }
  }

  .stuPage-right {
    width: 82%;
    padding: 0 40px 34px 5px;
    margin-bottom: 20px;

    .header {
      padding: 15px 0 15px;
      display: flex;
      height: 70px;
      align-items: center;
      justify-content: flex-end;
      background-color: #fff;

      .getscore {
        cursor: pointer;
        color: #5F9EFF;
        display: flex;
        align-items: center;
      }

      .icon {
        width: 24px;
        height: 24px;
        border-radius: 4px;
        display: inline-block;
        margin-right: 10px;
      }

      .color1 {
        background: #F56C6C;
        border: 1px solid #F56C6C;
      }

      .color2 {
        background: #FFBB19;
        border: 1px solid #E8A400;
      }

      .color3 {
        background: #5F9EFF;
        border: 1px solid #468FFF;
      }

      .color4 {
        background: #39CEB1;
        border: 1px solid #07C29D;
      }
    }

    .content {
      width: 100%;
      position: relative;

      .icon {
        color: black;
        position: absolute;
        z-index: 200;
        top: 32px;
        font-weight: bold;
        cursor: pointer;
      }

    }
  }
}

.table-header-text {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
<style lang="scss">

.stuPage {
  .header__serarch {
    .el-input {
      width: 76%;
    }

    .search__text {
      &.el-input--suffix .el-input__inner {
        padding-right: 0px;
        padding-left: 7px;
      }

      .el-input__inner {
        border-right: none;
        border-radius: 3px 0 0 3px;
      }
    }
  }

  .point-checkbox {
    .el-checkbox__inner {
      width: 20px;
      height: 20px;

      &:after {
        height: 10px;
        left: 6px;
        position: absolute;
        top: 1px;
        width: 5px;
      }
    }

    .el-checkbox__label {
      font-size: 16px;
    }
  }

  .content {
    .el-table {
      overflow: visible;
    }

    .el-table__header-wrapper {
      position: sticky;
      position: -webkit-sticky;
      top: 70px;
      z-index: 10;
    }

    .el-table__header {
      height: 70px !important;
      /* 你想要的高度 */
      line-height: 70px;
      /* 保持与高度一致以垂直居中文本 */
      background-color: #EEF0F9 !important;
    }

    .el-table td.el-table__cell,
    .el-table th.el-table__cell.is-leaf {
      border-bottom: 0;
    }

    .el-table th.el-table__cell>.cell {
      padding-left: 20px;
    }

    .el-table .el-table__cell {
      height: 55px;
      padding: 0;
    }
  }
}
</style>

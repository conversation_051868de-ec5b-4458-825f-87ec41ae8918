<template>
  <div class="previewPaper display_flex flex-direction_column">
    <div class="detail-header">
      <div class="back-btn">
        <i class="el-icon-arrow-left" style="margin-right: 6px" @click="closePaper"></i>
        <div class="previewName" :title="previewObj.name || '预览试卷'" @click="closePaper">
          {{ previewObj.name || '预览试卷' }}
        </div>
        <!-- <span>{{ previewObj.name || "预览试卷" }}</span> -->
      </div>
      <div class="top-btn">
        <span class="edit-btn pull-right saveQues" @click="createPaper"><i class="el-icon-mobile"></i> 保存</span>
        <span v-if="this.outQuestore" class="edit-btn pull-right outQues" @click="externalQues"><i
            class="el-icon-tickets"></i> 外部题库</span>
        <span class="edit-btn pull-right addQues" v-if="!paramsData.isXueKe" @click="continueAdd"><i
            class="el-icon-plus"></i> 继续加题</span>
        <span class="edit-btn pull-right cleanQues" @click="cleanPaper"><i class="el-icon-delete"></i> 清空试卷</span>
      </div>
    </div>
    <div class="detail-body flex_1 display_flex align-items_flex-start" :style="{ height: bodyHeight + 'px' }"
      v-loading="listLoading">
      <!--左侧知识点列表-->
      <div class="left-list flex_1 display_flex flex-direction_column">
        <!--标题-->
        <el-input class="paper-title" v-model="previewObj.name" @focus="focusTitle(previewObj.name)"
          @change="changeTitle" type="text" maxlength="200"></el-input>
        <div v-loading="quesLoading" class="quesList" style="margin-top: 10px">
          <div v-for="item in previewObj.quesInfo" :key="item.onlyId">
            <div class="third-box" v-for="(subItem, subIdx) in item.data" :key="subItem.onlyId">
              <!--题目类型;style="height: 80px" padding-top: 36px-->
              <div :id="subItem.onlyId">
                <h3 class="third-title" :class="{ active: detailId == subItem.onlyId }" :id="subItem.onlyId">
                  {{ subItem.name }}
                  <span class="defaultScore">（共{{ subItem.data.length }}题，共{{ subItem.defaultScore }}分）</span>
                  <div class="hover-btn">
                    <span class="delete" @click="deleteQues(subItem, item.data, subIdx)">删除</span>
                  </div>
                </h3>
              </div>

              <!--题面-->
              <div v-for="(third, thirdIdx) in subItem.data" :key="third.onlyId" :id="third.onlyId">
                <!-- style="padding-top: 36px; margin-top: -36px" -->

                <div class="quesContent" style="position: relative" :class="{ active: detailId == third.onlyId }">
                  <div class="hover-btn" v-if="third.content">
                    <span class="delete" @click="deleteQues(third, subItem.data, thirdIdx)">删除</span>
                    <span class="delete" :class="{ open: third.content.showDetail }" @click="
                        $set(third.content, 'showDetail', !third.content.showDetail),
                          getOutQuesDetails(third)
                      ">
                      {{ third.content.showDetail ? '收起' : '展开' }}解析</span>
                  </div>
                  <div class="ques-list">
                    <div class="ques-content display_flex flex-direction_column" v-if="third.content">
                      <span v-if="!third.data?.length" style="flex-shrink: 0">
                        {{ third.name }}
                        <span class="defaultScore">（{{ third.defaultScore }}分）</span>
                      </span>
                      <div class="question_content flex_1" style="width: 100%">
                        <LatexHtml class="question_body" :html="
                            third.content.topic
                          " :subject="third.content.subject" v-show="third.normalDetail">
                        </LatexHtml>
                        <LatexHtml v-show="third.content.showDetail" class="question_body" :html="third.content.details"
                          :subject="third.content.subject">
                        </LatexHtml>
                        <div class="question_body" v-if="third.content.data.qs.length > 1" v-show="third.normalDetail">
                          <p v-for="(item, index) in third.content.optionText" :key="index" style="display: flex">
                            <span v-if="third.data?.length">&nbsp;&nbsp;&nbsp;({{ third.data[index].name }})</span>
                            <span v-html="item"></span>
                          </p>
                        </div>
                      </div>
                    </div>
                    <div class="ques-detail" :class="{ active: !third.normalDetail }"
                      v-if="third.content && third.content.showDetail">
                      <!--答案-->
                      <div class="answer_box display_flex align-items_flex-start">
                        <strong class="flex_shrink_0">【答案】</strong>
                        <div class="flex_1">
                          <div class="answer_content" v-if="third.content.type == 2 || third.content.quesType == 2">
                            {{ third.content.answer.split(',')[0] == 'A' ? '正确' : '错误' }}
                          </div>
                          <div v-if="third.content.data.levelcode != ''" class="answer_content">
                            <p v-for="(item, index) in third.content.answer" :key="index" style="display: flex">
                              <span>&nbsp;&nbsp;({{ index + 1 }})&nbsp;&nbsp;&nbsp;</span>
                              <span v-html="item"></span>
                            </p>
                          </div>
                          <LatexHtml class="answer_content" v-else :html="third.content.answer"
                            :subject="third.content.subject"></LatexHtml>
                        </div>
                      </div>
                      <!--考点-->
                      <div v-if="third.content.knowledgeName">
                        <strong>【考点】</strong>{{ third.content.knowledgeName.split(',').join('，') }}
                      </div>
                      <!--解析-->
                      <div v-if="third.content.analysis" class="answer_box display_flex align-items_flex-start">
                        <span class="flex_shrink_0"><strong>【解析】</strong></span>
                        <LatexHtml class="answer_content flex_1" :html="third.content.analysis"
                          :subject="third.content.subject"></LatexHtml>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--右侧-->
      <div class="right-content display_flex flex-direction_column">
        <div class="right-content__header flex-shrink_0">
          <!--从试卷袋进入不显示基本信息栏，点击保存的时候弹出基本信息确认框-->
          <div class="pull-left leftDiv" v-if="!$route.query.fromName">
            总览（共{{ getTotalLen().length }}题，共{{ paperTotalScore }}分）
          </div>
          <!--从试卷列表页详情进入显示基本信息栏，点击保存的时候不再弹出基本信息确认框-->
          <ul class="list-none left pull-left" v-else>
            <li v-for="(item, index) in titleList" :key="index" :class="{ active: activeTitle == index }"
              @click="changeTab(index)">
              {{ item }}
            </li>
          </ul>
          <el-button size="small" type="text" class="reset-btn pull-right" @click="showSetScoreDialog">
            设置分数
          </el-button>
        </div>
        <div class="right-content__body flex_1">
          <div style="padding: 0 23px;line-height: 30px;">
            <div v-if="previewObj.sortType == 1" style="display: inline-block;font-size: 14px;">顺序：
              <span>
                <el-popover placement="bottom-start" v-model="isShowPreviewQuesFrame" trigger="manual">
                  <previewQuesFrame v-if="isShowPreviewQuesFrame" 
                    :quesList="previewObj.quesInfo" @close="isShowPreviewQuesFrame = false" @saveFrame="saveQuesFrame"></previewQuesFrame>
                  <span slot="reference" @click="isShowPreviewQuesFrame = true">按选题顺序<i class="el-icon-sort"></i></span>
                </el-popover>
              </span>
            </div>
            <el-button style="float: right;margin-right: 16px;" size="small" type="text" @click="resetQueNum">
              重置题号
            </el-button>
          </div>
          <div class="quesCard" v-if="!activeTitle">
            <!-- <ul class="list-none quesCard-ul display_flex">
              <li>题目</li>
              <li>题型</li>
              <li>分值</li>
              <li>操作</li>
            </ul> -->
            <div class="treeDrag">
              <draggable :list="previewObj.quesInfo" :group="{ name: 'first', pull: true }" ghost-class="ghostClass"
                :no-transition-on-drag="true" fallbackTolerance="20" @end="dragEnd" :options="{ forceFallback: true }">
                <div class="tree-paper" v-for="item in previewObj.quesInfo" :key="item.onlyId">
                  <ul class="list-none firstNode__child node__child" v-show="!item.isFold">
                    <draggable :list="item.data" :group="{ name: 'second', pull: true }" @end="dragEnd"
                      ghost-class="ghostClass" fallbackTolerance="20" :options="{ forceFallback: true }">
                      <li class="second-tree" v-for="(subItem, subIndex) in item.data" :key="subItem.onlyId">
                        <div class="nodeBox secondBox" @click="getActiveClass(subItem)" :class="{
                            error: errorList.indexOf(subItem.onlyId) >= 0,
                          }">
                          <div class="secondNode nodeEle mover display_flex align-items_center">
                            <i class="foldIcon" :class="subItem.isFold ? 'el-icon-circle-plus' : 'el-icon-remove'"
                              @click="$set(subItem, 'isFold', !subItem.isFold)"></i>
                            <el-input class="secondNode__input node__input" v-model="subItem.name"
                              @focus="focusName(subItem)" @change="changeName($event, subItem)">{{ subItem.name }}
                            </el-input>
                            <!-- {{ subItem.name }} -->
                            <!--题型-->
                            <span class="quesType" style="visibility: hidden"></span>
                            <!--总分-->
                            <span class="totalScore" :draggable="false">
                              <span>总分 </span>
                              <!-- <el-input
                                class="single-total"
                                size="mini"
                                v-model="subItem.defaultScore"
                                @focus="focusScore(subItem.defaultScore)"
                                @change="changeScore(subItem)"
                                :draggable="false"
                              ></el-input> -->
                              {{ subItem.defaultScore }}
                              分
                            </span>
                            <!-- <el-checkbox
                              @change="changeCheckbox($event, subItem)"
                              class="checkBox"
                              v-model="subItem.isAverage"
                            >
                              平均分配
                            </el-checkbox> -->
                            <span class="checkboxSpan"></span>
                            <div class="editbtn display_flex justify-content_flex-around">
                              <span class="deleteSpan" @click="deleteQues(subItem, item.data, subIndex)">删除</span>
                            </div>
                          </div>
                        </div>
                        <ul class="list-none second__child node__child" v-show="!subItem.isFold">
                          <draggable :list="subItem.data" ghost-class="ghostClass" fallbackTolerance="20"
                            @end="dragEnd($event, subItem)" :data-id="subItem.onlyId"
                            :options="{ forceFallback: true }">
                            <li v-for="(thirdItem, thirdIndex) in subItem.data" :key="thirdItem.onlyId"
                              class="third-tree item-drag" :data-id="thirdItem.onlyId">
                              <div class="nodeBox thirdBox" @click="getActiveClass(thirdItem)">
                                <div class="thirdNode nodeEle mover display_flex align-items_center">
                                  <!-- <el-input
                                    class="thirdNode__input node__input"
                                    v-model="thirdItem.name"
                                    @focus="focusName(thirdItem)"
                                    @change="changeName($event, thirdItem)"
                                  >
                                    {{ thirdItem.name }}
                                  </el-input> -->
                                  <span style="width: 150px;">
                                    <template v-if="thirdItem.data?.length > 0">
                                      {{ getThreeQuesName(thirdItem.data) }}
                                    </template>
                                    <template v-else>
                                      {{ thirdItem.name }}
                                    </template>
                                  </span>
                                  <!--题型-->
                                  <span class="quesType text-ellipsis" style="visibility: hidden;"
                                    :title="thirdItem.type">
                                    {{ thirdItem.type }}
                                  </span>
                                  <!--总分-->
                                  <span class="totalScore">
                                    <span style="visibility: hidden">总分</span>
                                    <!-- <el-input
                                      class="single-total"
                                      size="mini"
                                      v-model="thirdItem.defaultScore"
                                      @focus="focusScore(thirdItem.defaultScore)"
                                      @change="changeScore(thirdItem)"
                                      :draggable="false"
                                    ></el-input> -->
                                    {{ thirdItem.defaultScore }}
                                    分
                                  </span>
                                  <span class="checkboxSpan"></span>
                                  <div class="editbtn display_flex justify-content_flex-around">
                                    <span class="deleteSpan"
                                      @click="deleteQues(thirdItem, subItem.data, thirdIndex)">删除</span>
                                  </div>
                                </div>
                              </div>
                            </li>
                          </draggable>
                        </ul>
                      </li>
                    </draggable>
                  </ul>
                </div>
              </draggable>
            </div>
          </div>
          <div class="baseInfo" v-else>
            <div>试卷名称：{{ previewObj.name }}</div>
            <div><span class="twoWords">学</span>科：{{ previewObj.subjectName }}</div>
            <div><span class="twoWords">年</span>级：{{ previewObj.gradeName }}</div>
            <div>试卷类型：{{ previewObj.categoryName }}</div>
            <div><span class="twoWords">年</span>份：{{ previewObj.year }}</div>
            <!-- <div><span class="twoWords">备</span>注：{{previewObj.remark}}</div> -->
            <el-button @click="modifyBaseInfo">修改基本信息</el-button>
          </div>
        </div>
      </div>
    </div>
    <formDialog v-if="showBaseDialog" :paramsData="previewObj" :type="baseType" :defaultName="defaultName"
      @close="closeDialog" @saveMsg="saveBaseMessage"></formDialog>
    <setScoreDialog v-if="isShowSetScoreDialog" :showDialog="isShowSetScoreDialog" :quesList="previewObj.quesInfo"
      @close="isShowSetScoreDialog=false" @saveScore="saveQuesScore">
    </setScoreDialog>
  </div>
</template>

<script>
import { getQueTypeListBySubId } from '@/service/pexam';
import { getPersonalTestBank, savePersonalTestBank } from '@/service/testbank';
import { chooseQuesSearch } from '@/service/pbook';
import LatexHtml from '@/components/LatexHtml';
import draggable from 'vuedraggable';
import { mapGetters, mapState } from 'vuex';
import formDialog from '@/components/paper/formDialog';
import setScoreDialog from '@/components/paper/setScoreDialog';
import previewQuesFrame from '@/components/paper/previewQuesFrame'
import { getQuesDetails } from '@/service/yiqi';

export default {
  name: 'preview-paper',
  components: {
    LatexHtml,
    draggable,
    formDialog,
    setScoreDialog,
    previewQuesFrame
  },
  props: ['tokenInfo'],

  data() {
    return {
      detailId: '',
      titleList: ['试题', '基本信息'],
      activeTitle: 0,
      previewObj: {},
      quesLoading: false,
      bodyHeight: 300,
      optionProps: {
        value: 'name',
        label: 'name',
        children: 'data',
      },
      // 从url上获取的参数列表
      paramsData: {},
      // 默认分值
      scoreList: [],
      // 题类总分与小题不一致的题类id数组
      errorList: [],
      // 重复题号的数组
      repeatQuesList: [],
      // 最后保存提交的题目数组
      quesInfo: [],
      showBaseDialog: false,
      listLoading: false,
      baseType: '',
      // 题名输入框聚焦时候的取值，即未更改前的值，如果题名设置有重复则恢复聚焦时候的数据
      focusVal: '',
      // 分值输入框聚焦时候的取值，即未更改前的值，如果更改为0则恢复聚焦时候的数据
      focusScoreVal: '',
      focusTitleVal: '',
      defaultName: '',
      outQuestore: false,
      ques: '',
      orderType:1,//1:按选题顺序 2：按题目类型
      isShowSetScoreDialog:false,
      isShowPreviewQuesFrame:false,
    };
  },
  mounted() {
    /*
     * paramsData中paperName有值表示从试卷列表中点击试卷详情中进入
     * */
    this.paramsData = this.$route.query;
    this.outQuestore = this.$sessionSave.get('outQuestore');

    this.getQueTypeListBySubId();
  },
  computed: {
    ...mapState(['teachParams']),
    ...mapGetters(['subjectMap', 'gradeList']),
    paperTotalScore() {
      let quesInfo = this.previewObj.quesInfo || [],
        paperTotalScore = 0;
      quesInfo.length &&
        quesInfo.forEach((item, index) => {
          item.data.forEach((subItem, subIndex) => {
            // 获取当前题型总分
            let typeScore = 0;
            subItem.data.forEach((thirdItem, thirdIndex) => {
              typeScore += Number(thirdItem.defaultScore);
            });
            paperTotalScore += typeScore;
          });
        });
      return paperTotalScore;
    },
  },
  methods: {
    /**
     * @name: 获取一起题库题目详情
     * @param item 题目信息
     */
    getOutQuesDetails(item) {
      let loginData = this.$sessionSave.get('loginInfo');
      // if(loginData.user_type == 5 && loginData.admin_type!=2){
      //   //运营账号
      //   return
      // }
      if (!item.content.showDetail) {
        item.content.showDetail = item.content.showDetail;
        item.normalDetail = true;
        return;
      }
      let tokenInfo = loginData.token;
      getQuesDetails({
        headers: {
          token: tokenInfo,
        },
        data: {
          userId: loginData.id,
          questionId: item.id,
          subjectId: item.content.subject,
        },
      })
        .then(result => {
          let res = result;
          let data = res.data && res.data.data;
          item.content.details = data.html;
          item.content.data.qs = data.qs;
          this.$nextTick(() => {
            item.content.showDetail = true;
            item.normalDetail = false;
          });
        })
        .catch(err => {});
    },
    showSetScoreDialog(){
      this.isShowSetScoreDialog = true;
    },
    resetQueNum() {
      let quesInfo = this.previewObj.quesInfo,
        quesIndex = 0;
      quesInfo.forEach(item => {
        item.data.length &&
          item.data.forEach(sub => {
            sub.data.length &&
              sub.data.forEach(third => {
                this.$set(third, 'isChange', false);
                
                if (third.data) {
                  third.data.forEach(fourth => {
                    if (!fourth.isChange) {
                      quesIndex += 1;
                      fourth.name = quesIndex;
                      fourth.quesNo = Number(quesIndex);
                      fourth.quesNos = quesIndex;
                    }
                  });
                }else{
                  quesIndex += 1;
                  third.name = quesIndex;
                  third.quesNo = Number(quesIndex);
                  third.quesNos = quesIndex;
                }
              });
          });
      });
      this.savePersonalTestBank('reset');
    },
    // 生成默认标题，格式为年月日+手动组卷
    getCurTitle() {
      let nowDate = new Date(),
        year = nowDate.getFullYear(),
        month =
          nowDate.getMonth() + 1 < 10 ? '0' + (nowDate.getMonth() + 1) : nowDate.getMonth() + 1,
        day = nowDate.getDate() < 10 ? '0' + nowDate.getDate() : nowDate.getDate(),
        dateStr = year.toString() + month.toString() + day.toString() + '手动组卷';
      this.defaultName = dateStr;
      this.previewObj.name = dateStr;
    },
    // 切换显示试题还是基本信息
    changeTab(index) {
      this.activeTitle = index;
    },
    // 点击修改基本信息
    modifyBaseInfo() {
      this.baseType = 'modify';
      this.getQuesInfo('checkData');
      this.showBaseDialog = true;
    },
    // 获取题型的默认分值
    getQueTypeListBySubId() {
      this.listLoading = true;
      getQueTypeListBySubId({
        subId: this.paramsData.subId || '',
        schoolId: this.$sessionSave.get('schoolInfo').id,
        tbId: this.paramsData.tbId || '',
        userId: this.$sessionSave.get('loginInfo').id,
        serviceVersion: 4.0,
        hwTypeCode: 104,
      })
        .then(data => {
          this.scoreList = data.data;
          if (!data.data.length) {
            this.listLoading = false;
          }
          this.getPersonalTestBank('update');
        })
        .catch(err => {
          this.listLoading = false;
        });
    },
    // 继续加题
    continueAdd() {
      let sourceUrl = this.teachParams.sourceUrl;
      if (sourceUrl) {
        this.$router.push({
          path: sourceUrl,
          query: {
            fromName: 'previewPaper',
            subjectName: this.previewObj.subjectName || '',
          },
        });
      } else {
        this.$router.push({
          path: '/home/<USER>/paper',
          query: {
            fromName: 'previewPaper',
            subjectName: this.previewObj.subjectName || '',
          },
        });
      }
    },
    // 外部题库
    externalQues() {
      this.$router.push({
        path: 'home/externalQues',
        query: {
          fromName: 'previewPaper',
          subjectName: this.previewObj.subjectName || '',
        },
      });
    },
    // 获取基本信息弹窗中的学科，年级和题类等
    getBaseInfo() {
      // 获取学科
      this.previewObj.subjectName = this.subjectMap[this.previewObj.subjectId].name;

      // 获取年级
      if (this.previewObj.gradeId) {
        this.previewObj.gradeName =
          this.gradeList &&
          this.gradeList.filter(item => {
            return item.id == this.previewObj.gradeId;
          })[0].name;
      }

      if (!this.previewObj.year) {
        if (this.$route.query.fromName == 'previewDetail' && this.$route.query.year) {
          this.previewObj.year = this.$route.query.year;
        } else {
          this.previewObj.year = new Date().getFullYear();
        }
      }
      let { year, gradeId = '', categoryId = '', fromName = '' } = this.$route.query;
      if (fromName == 'previewDetail') {
        let grd = '';
        if (gradeId) {
          grd = this.gradeList.find(item => item.id == gradeId);
          this.previewObj.gradeName = grd.name || '';
        } else {
          this.previewObj.gradeName = '';
        }
        if (grd && categoryId) {
          // 获取题类
          let phaseId = '';
          if (this.$sessionSave.get('loginInfo').user_type != 5) {
            phaseId = this.$sessionSave.get('loginInfo').phase;
          } else {
            phaseId = grd.phaseId - 2;
          }
          let c30 = this.$localSave.get('c30_tb_' + phaseId).sources;
          this.previewObj.categoryName =
            c30.filter(obj => {
              return obj.id == categoryId;
            })[0].name || '';
        }
      }
    },
    // 获取预览列表数据
    getPersonalTestBank(scoreList) {
      getPersonalTestBank({
        id: this.paramsData.tbId || '',
        schoolId: this.paramsData.schoolId || '',
        userId: this.$sessionSave.get('loginInfo').id,
        subjectId: this.paramsData.subId || '',
      })
        .then(res => {
          this.ques = res.data.quesInfo;
          let dataRes = JSON.parse(res.data && res.data.quesInfo);
          let newData = [
            {
              name: '第一卷',
              data: [],
              id: 0,
              show: 1,
            },
            // {
            //   name: "第二卷",
            //   data: [],
            //   id: 1,
            //   show: 1,
            // },
          ]; // 长度为2，第一卷和第二卷
          //获取题型默认分数
          if (this.$route.query.fromName != 'previewDetail') {
            dataRes.forEach(item => {
              item.data.forEach(it => {
                // it.score = this.getDefaultScore(it.type);
              });
            });
          }
          dataRes.forEach(item => {
            newData[0].data.push({
              isChangeSort: true,
              isAverage: true,
              ...item,
            });
            // if (
            //   item.name.includes("单") ||
            //   item.name.includes("选择") ||
            //   item.name.includes("多选") ||
            //   item.typeId == "1" ||
            //   item.typeId == "8"
            // ) {
            //   newData[0].data.push({
            //     isChangeSort: true,
            //     isAverage: true,
            //     ...item,
            //   });
            // } else {
            //   newData[1].data.push({
            //     isChangeSort: true,
            //     isAverage: true,
            //     ...item,
            //   });
            // }
          });

          res.data.quesInfo = JSON.stringify(newData);
          res.data.quesInfo = res.data.quesInfo ? JSON.parse(res.data.quesInfo) : [];
          this.previewObj = res.data;
          this.previewObj.shareType = this.previewObj.shapeType || 0;
          // 从试卷袋进入, 且试卷名称不存在（防止是从详情进去同步过试卷列表后，从试卷袋进入，这个时候不是新建是编辑之前的试卷）
          // if (this.paramsData.fromPaper == "paperBag" && !this.previewObj.name) {
          //   this.getCurTitle();
          // }
          if (this.paramsData.fromPaper == 'paperBag') {
            this.getCurTitle();
          }

          let quesInfo = this.previewObj.quesInfo || [],
            quesIds = [];
          quesInfo.length &&
            quesInfo.forEach((item, index) => {
              this.$set(item, 'onlyId', index);
              item.data.forEach((subItem, subIndex) => {
                subItem.data.forEach((thirdItem, thirdIndex) => {
                  this.$set(thirdItem, 'onlyId', index + '-' + subIndex + '-' + thirdIndex);
                  quesIds.push(thirdItem.id);
                });
              });
            });
          if (!quesIds.length) {
            this.listLoading = false;
          } else {
            this.chooseQuesSearch(quesIds.join(','));
          }
          this.getBaseInfo();
        })
        .catch(err => {
          this.listLoading = false;
        });
    },
    // 获取总题数量
    getTotalLen() {
      let totalList = [],
        ids = [];
      this.previewObj.quesInfo &&
        this.previewObj.quesInfo.length &&
        this.previewObj.quesInfo.forEach(item => {
          totalList = totalList.concat(item.data);
        });
      totalList.length &&
        totalList.forEach(item => {
          if (item.data.length) {
            ids = ids.concat(
              item.data.map(sub => {
                return sub.id;
              })
            );
          }
        });

      return ids;
    },
    // 拖动结束
    dragEnd(evt, item) {
      this.updataScore();
      // if (item) {
      //   let selfItem = evt.item.dataset.id.split('-');
      //   let targetItem = evt.to.dataset.id.split('-');
      //   if (!(selfItem[0] == targetItem[0] && selfItem[1] == targetItem[1])) {
      //     item.isAverage = false;
      //     let targetLevel = evt.to.dataset.id.split('-');
      //     this.previewObj.quesInfo[targetLevel[0]].data[targetLevel[1]].isAverage = false;
      //   }
      // }
      this.savePersonalTestBank();
    },
    //关闭试卷
    async closePaper() {
      let sourceUrl = this.teachParams.sourceUrl;
      if (this.$route.query.fromPaper == 'paperBag') {
        if (sourceUrl) {
          this.$router.push({
            path: sourceUrl,
            query: {
              fromName: 'previewPaper',
              subjectName: this.previewObj.subjectName || '',
            },
          });
        } else {
          this.$router.back();
        }
        return;
      }
      let newData = await getPersonalTestBank({
        id: this.paramsData.tbId || '',
        schoolId: this.paramsData.schoolId || '',
        userId: this.$sessionSave.get('loginInfo').id,
        subjectId: this.paramsData.subId || '',
      });
      let newQues = newData.data.quesInfo;
      if (newQues == this.ques) {
        this.$confirm(`试卷未做任何修改是否保存后退出?`, '提示', {
          confirmButtonText: '保存并退出',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.savePersonalTestBank('savePersonalTestBank');
          })
          .catch(() => {});
      } else {
        this.savePersonalTestBank('edit');
      }
    },
    // 清空试卷
    cleanPaper() {
      this.$confirm(`确定要清空试卷吗?`, '提示', {
        confirmButtonText: '清空后重新加题',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.previewObj.quesInfo = [];
          this.savePersonalTestBank('clean');
        })
        .catch(() => {});
    },
    // 保存试卷
    createPaper() {
      if (!this.previewObj.quesInfo.length) {
        this.$message.error('试卷至少保留一题');
        return;
      }
      // if (this.checkShowNumError()) {
      //   this.$message.error("存在题目有相同题号，请检查");
      //   return;
      // }
      // 从试卷袋进入，需要先校验分数，再弹出基本信息弹窗
      // if (this.paramsData.fromPaper == 'paperBag') {
      if (!this.previewObj.categoryId) {
        this.getQuesInfo('checkScore', true);
      } else {
        if (!this.getQuesInfo('checkScore')) return;
        this.savePersonalTestBank('savePersonalTestBank');
      }
      if (this.teachParams) {
        // let tempData = JSON.parse(JSON.stringify(this.teachParams));
        this.teachParams.sourceUrl = '';
        this.teachParams.gradeId = '';
        this.teachParams.classId = '';
        this.teachParams.startTime = '';
        this.teachParams.endTime = '';
        this.teachParams.sourceType = '';
        this.$store.commit('saveTeachParams', this.teachParams);
      }
    },
    // 检查是否有小题分累加与题类总分不一致的情况
    getQuesInfo(type, showBaseDialog) {
      /**
       *type取值checkData意思是组织调用savePaper中quesInfo中需要的数据格式，去除多余的数据
       *type取值checkScore是检查题型总分和题型下小题累加总分是否一致，不一致需要弹出错误提示
       *showBaseDialog是检查没问题之后是否需要立刻弹出基本信息弹窗
       */
      let quesInfo = this.previewObj.quesInfo;
      this.errorList = [];
      this.quesInfo = [];
      // 检查题类总分和题类下小题分数总和是否对的上
      let dataLength = quesInfo.length;
      for (let i = 0; i < dataLength; i++) {
        let item = quesInfo[i];
        item.name = String(item.name).trim();
        this.quesInfo.push({
          name: item.name,
          data: [],
          show: item.show,
          id: item.id,
        });
        let subLen = item.data.length;
        for (let j = 0; j < subLen; j++) {
          let sub = item.data[j];
          sub.name = String(sub.name).trim();
          if (sub.data && !sub.data.length && type == 'checkScore') {
            this.$message.error(`${sub.name}下没有试题数据`);
            return false;
          }
          let score = 0;
          this.quesInfo[i].data.push({
            name: sub.name,
            type: sub.type,
            typeId: sub.typeId,
            isChange: sub.isChange,
            isChangeSort: true,
            isAverage: sub.isAverage,
            data: [],
          });

          let thirdLen = sub.data.length;
          for (let k = 0; k < thirdLen; k++) {
            let third = sub.data[k];
            third.name = String(third.name).trim();
            score += Number(third.defaultScore);
            if (third.data && third.data.length != 0) {
              //三级题型小问的分值
              let subScore = (third.defaultScore / third.data.length).toFixed(0);
              third.data.forEach(fourth => {
                fourth.score = fourth.score ?? subScore;
              });
            }
            this.quesInfo[i].data[j].data.push({
              id: third.id,
              name: third.name,
              quesNo: Number(third.name),
              quesNos: third.name,
              type: third.type,
              typeId: third.typeId,
              isChange: third.isChange,
              score: Number(third.defaultScore),
              data: third.data,
              answer: this.$isObjective(third.typeId) ? third.content?.answer : ''
            });
          }
          if (type == 'checkScore') {
            if (score != sub.defaultScore) {
              this.errorList.push(sub.onlyId);
            }
          }
        }
      }
      if (type == 'checkScore') {
        if (this.errorList.length) {
          this.$message.error('存在题型总分与下面小题分累加不相等的情况，请检查');
          return false;
        } else {
          if (showBaseDialog) {
            this.baseType = 'saveBase';
          }
          this.showBaseDialog = showBaseDialog;
        }
      }
      return true;
    },
    // 基本信息弹窗点击确认
    saveBaseMessage(data) {
      this.previewObj.name = data.name;
      this.previewObj.subjectId = data.subjectId;
      this.previewObj.subjectName = data.subjectName;
      this.previewObj.gradeId = data.gradeId;
      this.previewObj.gradeName = data.gradeName;
      this.previewObj.categoryId = data.categoryId;
      this.previewObj.categoryName = data.categoryName;
      this.previewObj.remark = data.remark;
      this.previewObj.year = data.year;
      this.previewObj.shareType = data.shareType || 0;
      this.closeDialog();
      this.savePersonalTestBank(
        this.baseType == 'modify' ? 'saveBaseInfo' : 'savePersonalTestBank'
      );
    },
    // 关闭基本信息弹窗
    closeDialog() {
      this.showBaseDialog = false;
    },
    //保存分值设置
    saveQuesScore(data){
      this.previewObj.quesInfo = data;
      this.isShowSetScoreDialog = false;
    },
    saveQuesFrame(data){
      this.previewObj.quesInfo = data;
      this.previewObj.sortType = 2;//按题目类型
      this.isShowPreviewQuesFrame = false;
      this.savePersonalTestBank()
    },
    // 接口保存当前试卷袋数据
    savePersonalTestBank(type) {
      let loginInfo = this.$sessionSave.get('loginInfo');
      this.previewObj.name = this.previewObj.name.trim();
      if (!this.previewObj.name) {
        this.$message.error('试卷名称不能为空');
        return;
      }
      if (type !== 'reset' && type !== 'changeName') {
        this.updateOrder();
      }
      if (!this.getQuesInfo('checkData')) return;
      let quesInfoRes = [];
      if (this.quesInfo.length && this.quesInfo.length == 1) {
        quesInfoRes = this.quesInfo[0].data;
      } else if (this.quesInfo.length && this.quesInfo.length == 2) {
        quesInfoRes = [].concat(this.quesInfo[0].data, this.quesInfo[1].data);
      }
      let qCount = 0;
      quesInfoRes.map(it => {
        if (it.data && it.data.length) {
          qCount += it.data.length;
        }
      });
      savePersonalTestBank({
        userId: loginInfo.id,
        userName: loginInfo.realname,
        schoolId: this.$sessionSave.get('schoolInfo').id,
        name: this.previewObj.name || '',
        categoryId: this.previewObj.categoryId || '',
        gradeId: this.previewObj.gradeId || '',
        subjectId: this.previewObj.subjectId || '',
        quesInfo: JSON.stringify(quesInfoRes),
        isSave: type == 'edit' ? 1 : type == 'savePersonalTestBank' ? 1 : 0, // 除了预览里头的保存试卷传1，其他都传0
        remark: this.previewObj.remark || '',
        year: this.previewObj.year || '',
        shareType: this.previewObj.shareType,
        queCount: qCount,
        typeCode: this.previewObj.categoryId || '', //试卷类型编码
        typeName: this.previewObj.categoryName || '', //试卷类型名称
        tbId: this.previewObj.tbId || '', //试卷id
        sName: this.previewObj.subjectName || '', //学科名称
        gCode: this.previewObj.gradeId, //年级编码
        gName: this.previewObj.gradeName || '', //年级名称
        sCode: this.previewObj.subjectId || '', //学科编码
        //phase   : 3,//学段
        source: 5, // 校本制作来源 1校本上传 2课课练同步 3个性化练习册 4个册考试报考 5个册试题组卷
        tbName: this.previewObj.name || '', //试卷名称
        cardType: 1, //0纯答题卡 1题卡合一 2题卡分离
        sortType: this.previewObj.sortType || 1, //1 按选题顺序  2 按题目类型合并
      }).then(res => {
        // 保存修改并返回到试卷详情页面
        if (type == 'edit') {
          this.$router.go(-1);
        }
        // 保存成功后跳到试卷列表页
        if (type == 'savePersonalTestBank') {
          this.$sessionSave.set('testDetail', '');
          this.$localSave.set('paper-tab', 'paper');
          this.$router.push({ path: '/home/<USER>/paper' });
        }
        // 保存基本信息
        if (type == 'saveBaseInfo') {
          // this.getViewPaper();
        }
        // 清空
        if (type == 'clean') {
          let sourceUrl = this.teachParams.sourceUrl;
          if (sourceUrl) {
            this.$router.push({
              path: sourceUrl,
              query: {
                fromName: 'previewPaper',
                subjectName: this.previewObj.subjectName || '',
              },
            });
          } else {
            this.$router.push({
              path: '/home/<USER>/paper',
              query: {
                fromName: 'previewPaper',
                subjectName: this.previewObj.subjectName || '',
              },
            });
          }
        }
      });
    },
    changeTitleState(item, type) {
      item.show = type == 'hide' ? false : !item.show;
      this.savePersonalTestBank();
    },
    changeTitleState(item, type) {
      item.show = type == 'hide' ? false : !item.show;
      this.savePersonalTestBank();
    },
    // 删除
    deleteQues(data, parentList, index) {
      this.$confirm(`确定要删除吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        // 删除卷一，卷二
        parentList.splice(index, 1);
        this.deleteNoData();
        this.updataScore();
        this.savePersonalTestBank();
      });
    },
    // 检查是否有空的子项，有则删除
    deleteNoData() {
      let quesInfo = this.previewObj.quesInfo,
        dataLen = quesInfo.length;
      for (let i = dataLen - 1; i >= 0; i--) {
        let item = quesInfo[i].data;
        if (item.length) {
          for (let j = item.length - 1; j >= 0; j--) {
            let subItem = item[j].data;
            if (!subItem.length) {
              item.splice(j, 1);
              if (!item.length) {
                this.deleteNoData();
              }
            }
          }
        } else {
          quesInfo.splice(i, 1);
        }
      }
    },
    // 获取当前传入题型的默认分值
    getDefaultScore(type) {
      let score = 0;
      this.scoreList.forEach(item => {
        if (item.dtTypeName == type) {
          score = item.score;
        }
      });
      return score;
    },
    // 更新顺序
    updateOrder() {
      let quesInfo = this.previewObj.quesInfo,
        quesIndex = 0,
        typeIndex = 0,
        bigIndex = 0;
      quesInfo.forEach(item => {
        item.data.length &&
          item.data.forEach(sub => {
            typeIndex += 1;
            if (!sub.isChange) {
              let name = sub.name.indexOf('、') != -1 ? sub.name.split('、')[1] : sub.name;
              sub.name = this.$sectionToChinese(typeIndex) + '、' + name;
            }
            sub.data.length &&
              sub.data.forEach(third => {
                // if (!third.isChange) {
                  bigIndex += 1;
                  third.name = bigIndex;
                  third.quesNo = Number(bigIndex);
                  third.quesNos = bigIndex;
                // }
                if (third.data) {
                  third.data.forEach(fourth => {
                    // if (!fourth.isChange) {
                      quesIndex += 1;
                      fourth.name = quesIndex;
                      fourth.quesNo = Number(quesIndex);
                      fourth.quesNos = quesIndex;
                    // }
                  });
                }else{
                  quesIndex += 1;
                  third.name = quesIndex;
                  third.quesNo = Number(quesIndex);
                  third.quesNos = quesIndex;
                }
                if (third.data && (third.data == '' || third.data.length == 0)) {
                  delete third.data;
                }
              });
          });
      });
    },
    // 标题输入框获取焦点事件
    focusTitle(val) {
      this.focusTitleVal = val;
    },
    // 更改标题框内容
    changeTitle() {
      if (!this.previewObj.name.trim()) {
        this.previewObj.name = this.focusTitleVal;
        return;
      }
      this.savePersonalTestBank();
    },
    // 题型框聚焦事件
    focusName(item) {
      this.focusVal = item.name;
      this.getActiveClass(item);
    },
    //修改名字
    changeName(val, data) {
      this.$set(data, 'isChange', true);
      this.savePersonalTestBank('changeName');
    },
    getThreeQuesName(data) {
      if(data.length == 1){
        return data[0].name;
      }
      return `${data[0].name}-${data[data.length - 1].name}`;
    },
    // 锚点
    getActiveClass(item) {
      this.detailId = item.onlyId;
      document.getElementById(item.onlyId).scrollIntoView({ block: 'start', behavior: 'smooth' });
    },
    // 修改子项得分之后更新总分
    updataScore() {
      let quesInfo = this.previewObj.quesInfo || [];
      quesInfo.length &&
        quesInfo.forEach(item => {
          item.data.forEach(subItem => {
            let totalScore = 0,
              firstScore = subItem.data.length ? subItem.data[0].defaultScore : 0,
              isAverage = true;
            subItem.data.length &&
              subItem.data.forEach((thirdItem, thirdIndex) => {
                totalScore += Number(thirdItem.defaultScore);
                if (thirdIndex == subItem.data.length - 1) {
                  if (
                    !(
                      thirdItem.defaultScore == firstScore ||
                      thirdItem.defaultScore == firstScore + (totalScore % subItem.data.length)
                    )
                  ) {
                    isAverage = false;
                  }
                } else {
                  if (firstScore != thirdItem.defaultScore) {
                    isAverage = false;
                  } else {
                    firstScore = thirdItem.defaultScore;
                  }
                }
              });
            subItem.defaultScore = totalScore;
            subItem.isAverage = !isAverage ? false : subItem.isAverage;
          });
        });
    },
    // 勾选/取消选择 平均分配按钮
    changeCheckbox(val, data) {
      if (val) {
        let dataLen = data.data.length;
        // 子项平均分配，若不能整除，则最后一个取值为平均值+余数
        let remainder = data.defaultScore % dataLen,
          averScore = Math.floor(data.defaultScore / dataLen);
        data.data.forEach((item, index) => {
          item.defaultScore = index == dataLen - 1 ? averScore + remainder : averScore;
        });
        this.checkShowError();
        this.savePersonalTestBank();
      }
    },
    // 分值框聚焦事件
    focusScore(val) {
      this.focusScoreVal = val;
    },
    // 更改分数
    changeScore(data) {
      if (data.defaultScore == 0 || !String(data.defaultScore).trim()) {
        data.defaultScore = this.focusScoreVal;
        return;
      }
      // 父级更改总分，判断是否有勾选平均分配
      if (data.level == 2) {
        // 勾选了平均分配，则失焦后直接平均分配小题分数
        if (data.isAverage) {
          let dataLen = data.data.length;
          // 判断总分不能小于子项个数
          if (data.defaultScore < dataLen) {
            this.$message.error('总分最低不能小于题类下题目个数');
            let scoreArr =
              dataLen &&
              data.data.map(item => {
                return item.defaultScore;
              })[0];
            data.defaultScore = scoreArr.reduce(function (prev, cur) {
              return prev + cur;
            }, 0);
            return;
          }
          // 子项平均分配，若不能整除，则最后一个取值为平均值+余数
          let remainder = data.defaultScore % dataLen,
            averScore = Math.floor(data.defaultScore / dataLen);
          data.data.forEach((item, index) => {
            item.defaultScore = index == dataLen - 1 ? averScore + remainder : averScore;
          });
          this.savePersonalTestBank();
        }
        this.checkShowError();
      } else {
        // 更改小题分数，需要同步更改总分
        this.updataScore();
        this.savePersonalTestBank();
      }
    },
    // 检查错误边框是否还需要显示
    checkShowError(data) {
      if (!this.errorList.length || this.errorList.indexOf(data.onlyId) < 0) return;
      let score = 0;
      data.data.forEach(item => {
        score += Number(item.defaultScore);
      });
      if (score == data.defaultScore) {
        this.errorList.splice(this.errorList.indexOf(data.onlyId), 1);
      }
    },
    // 检查是否有相同题号，返回boolean, true代表有相同题号，false么有
    checkShowNumError() {
      let quesInfo = this.previewObj.quesInfo,
        quesNumList = [],
        repeatQuesList = [];
      // 检查题类总分和题类下小题分数总和是否对的上
      quesInfo.forEach((item, index) => {
        this.quesInfo.push({ name: item.name, data: [], show: item.show });
        item.name = String(item.name).trim();
        if (quesNumList.indexOf(item.name) < 0) {
          quesNumList.push(item.name);
        } else {
          repeatQuesList.push(item.name);
        }
        item.data.forEach((sub, subIdx) => {
          sub.name = String(sub.name).trim();
          if (quesNumList.indexOf(sub.name) < 0) {
            quesNumList.push(sub.name);
          } else {
            repeatQuesList.push(sub.name);
          }
          sub.data.forEach(third => {
            third.name = String(third.name).trim();
            if (quesNumList.indexOf(third.name) < 0) {
              quesNumList.push(third.name);
            } else {
              repeatQuesList.push(third.name);
            }
          });
        });
      });
      return repeatQuesList.length > 0;
    },
    // 获取题目的题面
    chooseQuesSearch(ids) {
      this.listLoading = true;
      chooseQuesSearch({
        qIds: ids,
      })
        .then(data => {
          let res = data.data;
          if (!this.previewObj.quesInfo.length) {
            this.quesLoading = false;
            return;
          }
          let qsMap = new Map();
          // 获取小问
          res.forEach(item => {
            const qs = item.data.qs;
            qs.forEach(q => {
              qsMap.set(q.qId, q);
            });
          });
          console.log(this.previewObj.quesInfo);
          this.previewObj.quesInfo.forEach(obj => {
            this.$set(obj, 'level', 1);
            obj.data.length &&
              obj.data.forEach(subObj => {
                this.$set(subObj, 'level', 2);
                this.$set(
                  subObj,
                  'isAverage',
                  this.previewObj.categoryId ? subObj.isAverage : true
                );
                subObj.data.length &&
                  subObj.data.forEach(third => {
                    this.$set(third, 'level', 3);
                    res.forEach(item => {
                      if (third.id == item.qId) {
                        this.$set(third, 'content', item);
                        this.$set(third, 'normalDetail', true);
                      }
                    });
                    if (third.content) return;
                    const qs = qsMap.get(third.id);
                    if (!qs) return;
                    this.$set(third, 'content', qs);
                    this.$set(third.content, 'data', qs);
                    this.$set(third, 'normalDetail', true);
                    third.content.data.levelcode = '';
                  });
              });
          });
          this.previewObj.quesInfo.forEach(item => {
            item.data.forEach(ite => {
              ite.data.forEach(it => {
                //若为三级题型则获取每小题分值之和
                if (it.content.data.levelcode == '301') {
                  it.score = 0;
                  it.content.data.qs.forEach(qscore => {
                    it.score += qscore.score;
                  });
                }
              });
            });
          });
          let quesInfo = this.previewObj.quesInfo || [];
          quesInfo.length &&
            quesInfo.forEach((item, index) => {
              item.data.forEach((subItem, subIndex) => {
                // 获取当前题型总分
                let typeScore = 0;
                subItem.data.forEach((thirdItem, thirdIndex) => {
                  // 若当前小题没有分值
                  if (!Number(thirdItem.score)) {
                    let score = this.getDefaultScore(subItem.type);
                    this.$set(thirdItem, 'defaultScore', Number(score));
                  } else {
                    this.$set(thirdItem, 'defaultScore', Number(thirdItem.score));
                  }
                  typeScore += thirdItem.defaultScore;
                });
                this.$set(subItem, 'defaultScore', typeScore);
              });
            });
          //题型序号处理
          console.log('this.previweObj.quesInfo======>', this.previewObj.quesInfo);
          if (this.paramsData.source !== '5' && this.paramsData.fromName == 'previewDetail') {
            let indexId = 0;
            this.previewObj.quesInfo.forEach(item => {
              item.data.forEach(it => {
                indexId++;
              });
            });
          }
          this.$nextTick(() => {
            this.listLoading = false;
            this.$katexUpdate();
            this.quesLoading = false;
            this.updataScore();
          });
        })
        .catch(err => {
          this.listLoading = false;
          this.quesLoading = false;
          console.log(err);
        });
    },
    setIndexId(index) {
      let number = '';
      switch (index) {
        case 1:
          number = '一、';
          break;
        case 2:
          number = '二、';
          break;
        case 3:
          number = '三、';
          break;
        case 4:
          number = '四、';
          break;
      }
      return number;
    },
  },
};
</script>
<style lang="scss" scoped>
.router-view {
  display: flex;
  flex-direction: column;
}
.preview-name {
  text-overflow: ellipsis;
  overflow: hidden;
  width: 100px;
  height: 20px;
}
.previewPaper {
  width: 100%;
  height: 100%;
  padding: 20px;
  background-color: #f1f5f8;
  font-family: Microsoft YaHei;
  .detail-header {
    // width: 62%;
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;
    margin: 4px 0 20px;
    > span {
      cursor: pointer;
    }
    .back-btn {
      display: flex;
      align-items: center;
      display: -webkit-box;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      // cursor: pointer;
      font-size: 18px;
      color: #333;
      font-weight: 400;
      // max-width: 500px;
      .el-icon-arrow-left {
        margin-right: 8px;
      }
    }
    .top-btn {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .edit-btn {
      position: relative;
      font-size: 16px;
      font-weight: 400;
      color: #3f4a54;
      padding-left: 26px;
      display: inline-block;
      height: 20px;
      line-height: 20px;
      cursor: pointer;
      &:not(:nth-of-type(4)) {
        margin-left: 50px;
      }
      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: -3px;
        // background : url("../../assets/preview_icon.png");
        width: 24px;
        height: 24px;
      }
      &:nth-of-type(2):before {
        background-position: -58px -58px;
      }
      &:nth-of-type(3):before {
        background-position: -58px 0px;
      }
      &:nth-of-type(4):before {
        background-position: -29px 0px;
      }
      // &:nth-of-type(5):before {
      //     background-position : -30px 0px;
      // }
      &.saveQues {
        margin-right: 30px;
      }
      // &.cleanQues {
      //     margin-right : 30px;
      // }
      // &.outQues {
      //     margin-right : 30px;
      // }
      &.addQues {
        // margin-right : 30px;
        margin-left: 20px;
      }
    }
  }
  .detail-body {
    width: 100%;
    min-height: 300px;
    height: auto;
    > div {
      height: 100%;
      background: #fff;
      box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
      border-radius: 6px;
    }
    .left-list {
      min-height: 300px;
      flex-shrink: 0;
      margin-right: 15px;
      padding: 25px 24px 0 24px;
      overflow-x: hidden;
      overflow-y: auto;
      .paper-title {
        font-size: 18px;
        font-weight: bold;
        color: #3f4a54;
        // line-height: 52px;
        // height: 52px;
        text-align: center;
      }
      .second-title {
        position: relative;
        font-size: 18px;
        font-weight: 400;
        color: #3f4a54;
        line-height: 44px;
        height: 44px;
        text-align: center;
        border-radius: 4px 0 4px 4px;
        border: 1px solid #fff;
        /*margin        : 36px 0 0;*/
        .hover-btn {
          display: none;
          width: 170px;
          height: 36px;
          line-height: 36px;
          position: absolute;
          right: -1px;
          // top: -36px;
          border: 1px solid #409eff;
          border-radius: 4px 4px 0 0;
          // border-bottom: 1px solid #fff;
          border-top: 1px solid #fff;
          border-left: 1px solid #fff;
          bottom: -1px;
          background: #fff;
          > span {
            cursor: pointer;
            position: relative;
            padding-left: 26px;
            display: inline-block;
            width: 50%;
            &:before {
              content: '';
              position: absolute;
              left: 10px;
              top: 7px;
              width: 24px;
              height: 24px;
              background: url('../../assets/preview_icon.png');
              background-position: -29px -58px;
            }
            &:nth-child(1) {
              &:before {
                background-position: -29px -58px;
              }
              &:after {
                content: '';
                position: absolute;
                right: 0;
                width: 1px;
                height: 12px;
                background: #409eff;
                opacity: 0.5;
                top: 12px;
              }
            }
            &:nth-child(2):before {
              background-position: 0px 0px;
            }
          }
        }
        &:hover,
        &.active {
          border: 1px solid #409eff;
          .hover-btn {
            display: inline-block;
          }
        }
      }
      .third-box {
        width: 100%;
        box-sizing: border-box;
        margin-bottom: 10px;
        .third-title {
          position: relative;
          line-height: 44px;
          // height: 44px;
          /*margin        : 35px 0;*/
          font-size: 16px;
          font-weight: 400;
          color: #3f4a54;
          text-align: left;
          // padding-left: 20px;
          border-radius: 4px;
          border: 1px solid #fff;
          .title-score {
            color: #717980;
          }
          .hover-btn {
            display: none;
            width: 85px;
            height: 36px;
            line-height: 36px;
            position: absolute;
            right: -1px;
            // top: -36px;
            border: 1px solid #409eff;
            border-radius: 4px 4px 0 0;
            // border-bottom: 1px solid #fff;
            border-top: 1px solid #fff;
            border-left: 1px solid #fff;
            bottom: -1px;
            background: #fff;
            > span {
              cursor: pointer;
              position: relative;
              padding-left: 36px;
              display: inline-block;
              &:before {
                content: '';
                position: absolute;
                left: 10px;
                top: 7px;
                width: 24px;
                height: 24px;
                background: url('../../assets/preview_icon.png');
                background-position: 0px 0px;
              }
            }
          }
          &:hover,
          &.active {
            border: 1px solid #409eff;
            border-radius: 4px 0 4px 4px;
            .hover-btn {
              display: inline-block;
            }
          }
        }
        .quesContent {
          width: 100%;
          box-sizing: border-box;
          position: relative;
          padding: 0px 5px 7px 30px;
          border-radius: 4px 0 4px 4px;
          border: 1px solid #fff;
          /*margin-bottom : 36px;*/
          .hover-btn {
            display: none;
            width: 170px;
            height: 36px;
            position: absolute;
            right: -1px;
            // top: -36px;
            border: 1px solid #409eff;
            border-radius: 4px 4px 0 0;
            border-top: 1px solid #fff;
            border-left: 1px solid #fff;
            bottom: -1px;
            background: #fff;
            > span {
              cursor: pointer;
              position: relative;
              height: 36px;
              line-height: 36px;
              padding-left: 36px;
              display: inline-block;
              cursor: pointer;
              &:before {
                content: '';
                position: absolute;
                left: 13px;
                top: 7px;
                width: 24px;
                height: 24px;
                background: url('../../assets/preview_icon.png');
                background-position: -29px -58px;
              }
              &:nth-child(1) {
                &:before {
                  background-position: 0px 0px;
                }
                &:after {
                  content: '';
                  position: absolute;
                  right: -8px;
                  width: 1px;
                  height: 12px;
                  background: #409eff;
                  opacity: 0.5;
                  top: 12px;
                }
              }
              &:nth-child(2) {
                &:before {
                  background-position: 0px -58px;
                }
                &.open:before {
                  left: 12px;
                  top: 8px;
                  background-position: -58px -29px;
                }
              }
            }
          }
          &:hover,
          &.active {
            border: 1px solid #409eff;
            .hover-btn {
              display: inline-block;
            }
          }
          .ques-detail {
            margin-top: 20px;
            &.active {
              display: none !important;
            }
          }
        }
      }
    }
    .right-content {
      width: 520px;
      flex-shrink: 0;
      min-height: 300px;
      .right-content__header {
        height: 47px;
        padding: 0 23px;
        outline: 1px solid #e4e8eb;
        .leftDiv {
          line-height: 47px;
        }
        .left {
          li {
            cursor: pointer;
            line-height: 45px;
            display: inline-block;
            width: auto;
            &.active {
              color: #409eff;
              border-bottom: 2px solid #409eff;
            }
            &:first-child {
              margin-right: 45px;
            }
            &:last-child {
              width: auto;
            }
          }
        }
        .right {
          line-height: 47px;
        }
        .reset-btn {
          margin-right: 20px;
          margin-top: 6px;
        }
      }
      .right-content__body {
        width: 100%;
        overflow-x: hidden;
        overflow-y: auto;
        .baseInfo {
          width: 100%;
          padding-left: 30px;
          font-size: 16px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #4e5668;
          line-height: 36px;
          overflow-x: hidden;
          overflow-y: auto;
          padding-top: 10px;
          > div {
            text-align: left;
          }
          > button {
            margin-top: 20px;
            margin-bottom: 20px;
          }
          .twoWords {
            letter-spacing: 32px;
          }
        }
        .quesCard {
          .quesCard-ul {
            height: 60px;
            line-height: 60px;
            > li {
              display: inline-block;
              height: 100%;
              &:nth-child(1) {
                width: 147px;
                padding-left: 28px;
              }
              &:nth-child(2) {
                margin-left: 20px;
              }
              &:nth-child(3) {
                margin-left: 60px;
              }
              &:nth-child(4) {
                margin-left: 35px;
              }
            }
          }
        }
        li{
          user-select: none;
          -webkit-user-select: none;
        }
      }
    }
  }
}

.defaultScore {
  font-size: 16px;
  color: #717980;
}
.previewName {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 500px;
  cursor: pointer;
}

.question_body {
  // overflow: auto;
  padding: 10px 20px !important;
}

.question_body ol ::marker {
  color: white;
}
.question_body ol li ::marker {
  color: #545454;
}
</style>
<style lang="scss">
.paper-title {
  height: 52px;
  /*margin : 18px auto 0;*/
  .el-input__inner {
    height: 52px;
    line-height: 52px;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: #3f4a54;
    border: none;
  }
  &:hover {
    .el-input__inner {
      border: 1px solid #409eff;
    }
  }
}

.quesCard {
  .el-tree-node__content {
    height: 40px;
    line-height: 40px;
    margin-bottom: 14px;
    margin-top: 1px;
    .el-tree-node__expand-icon {
      font-size: 18px;
      transform: rotate(0deg);
      color: #acb9c3;
      &:before {
        content: '\e7a0';
        /*content : "\e7a2";*/
      }
      &.expanded:before {
        content: '\e7a2';
      }
      &.is-leaf:before {
        content: none;
      }
    }
    .el-input {
      width: auto;
    }
    .el-input__inner {
      width: 120px;
    }
    &:hover {
      border: 1px solid #409eff;
      -webkit-box-shadow: 0 0 20px #eaeee9;
      box-shadow: 0 0 20px #eaeee9;
      background: #fff;
    }
  }
  .el-tree-node__children {
    .el-tree-node__content {
      .el-input {
        width: auto;
      }
      .el-input__inner {
        width: 101px;
        padding: 0 7px;
      }
    }
  }
}

.foldIcon {
  font-size: 18px;
  color: #acb9c3;
  margin-right: 8px;
  cursor: pointer;
}

.treeDrag {
  font-family: Microsoft YaHei;
  font-size: 14px;
  font-weight: 400;
  color: #545454;
  .nodeBox {
    padding: 10px;
    border: 1px solid transparent;
    border-radius: 2px;
    &:hover {
      border: 1px solid #409eff;
      -webkit-box-shadow: 0 0 20px #eaeee9;
      box-shadow: 0 0 20px #eaeee9;
    }
    &.error {
      border: 1px solid #ee4334;
    }
    .el-input__inner {
      height: 100%;
      padding: 0 10px;
      font-size: 14px;
      font-weight: 400;
      color: #545454;
    }
    > div {
      width: 100%;
      height: 34px;
      .foldIcon {
        font-size: 18px;
        color: #acb9c3;
        margin-right: 8px;
      }
    }
    .nodeEle {
      height: 34px;
      .node__input {
        width: 150px;
        height: 100%;
        /*margin-right : 16px;*/
        .el-input__inner {
          width: 150px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        &.firstNode__input {
          width: 120px;
          .el-input__inner {
            width: 120px;
          }
        }
      }
      .quesType {
        display: inline-block;
        width: 56px;
        margin-left: 14px;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .totalScore {
        display: inline-block;
        width: 96px;
      }
      .checkboxSpan {
        display: inline-block;
        width: 30px;
        margin-left: 10px;
      }
      .single-total {
        width: 50px;
        .el-input__inner {
          width: 50px;
          height: 34px;
          padding: 0 10px;
        }
      }
      > .el-checkbox {
        margin-left: 10px;
        .el-checkbox__label {
          padding-left: 6px;
        }
      }
      .editbtn {
        width: 76px;
        > span {
          cursor: pointer;
        }
      }
      .hiddenSpan {
        font-size: 14px;
        color: #409eff;
        margin-left: 10px;
      }
      .deleteSpan {
        font-size: 14px;
        color: #ee4334;
        margin-left: 10px;
      }
    }
  }
  .node__child {
    padding-left: 26px;
  }
}

.ghostClass {
  border-radius: 2px;
  border: 1px solid #409eff;
  /*-webkit-box-shadow : 0 0 20px #eaeee9;*/
  /*box-shadow         : 0 0 20px #eaeee9;*/
}

.chosenClass {
  opacity: 0.5;
}
</style>

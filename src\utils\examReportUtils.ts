/**
 * 考试报告工具
 */
import { clzListAPI, getExamAnalyzeState, getReportCfgAPI, listExamSubject, setReportCfgAPI } from '@/service/pexam';
import UserRole from './UserRole';
import { sessionSave } from '@/utils';
import { findIntersection } from './index';
import { Loading } from '@iclass/element-ui';
import { ElLoadingComponent } from '@iclass/element-ui/types/loading';
import { getToken } from '@/service/auth';

/** 菜单ID */
export enum MenuIdEnum {
  /** 成绩单 */
  cardHome = 'cardHome',
  /** 一分五率 */
  fiveRate = 'fiveRate',
  /** 学科对比 */
  subjectComparison = 'subjectComparison',
  /** 学生小分表 */
  scoreSheet = 'scoreSheet',
  /** 大小题分析 */
  questionAnalysis = 'questionAnalysis',
  /** 学情概览 */
  overview = 'overview',
  /** 等级分布 */
  gradeDistribute = 'gradeDistribute',
  /** 优困生分布 */
  topAndDiff = 'topAndDiff',
  /** 上线分析 */
  online = 'online',
  /** 临界生分布 */
  limitStu = 'limitStu',
  /** 成绩分段分析 */
  scoreSection = 'scoreSection',
  /** 总名次分析 */
  totalRank = 'totalRank',
  /** 各名次段分析 */
  rankSection = 'rankSection',
  /** 箱线图 */
  boxplot = 'boxplot',
  /** 命题质量 */
  qualityReport = 'qualityReport',
  /** 双向细目表 */
  bothWayReport = 'bothWayReport',
  /** 题型均分 */
  quesTypeAvg = 'quesTypeAvg',
  /** 作答详情 */
  answerDetail = 'answerDetail',
  /** 知识点分析 */
  knowledgeAnalyze = 'knowledgeAnalyze',
  /** 试卷讲评 */
  paperComment = 'paperComment',
}

// 功能ID
export enum FuncIdEnum {
  /** 成绩单、查询学生历次成绩、学生增值评价 - 年级排名 */
  GrdRankAscDesc = 'grd-rank-asc-desc',
  /** 成绩单、查询学生历次成绩、学生增值评价 - 班级排名 */
  ClsRankAscDesc = 'cls-rank-asc-desc',
  /** 一分五率 - 是否显示全部班级 */
  FiveAll = 'five-all',
  /** 学生小分表-年级排名 */
  QsGrdRank = 'qs-grd-rank',
  /** 学生小分表-班级排名 */
  QsClsRank = 'qs-cls-rank',
  /** 作答详情 - 全校数据 */
  AnswerAll = 'answer-all',
}

export interface IExamSubject {
  id: string;
  name: string;
  phaseId: number;
  xfId: number;
  xkId: number;
  progress: number;
  progressState: number;
  processState: number;
  importScoreState: number;
  bindQueState: number;
  gradeId: null;
  year: number;
  categoryId: null;
  statType: number;
  relateCardType: number;
  abCardSheetType: number;
  source: number;
  code: null;
  status: number;
  personalBookId: string;
  workId: string;
  workIds: string[];
  testBankId: string;
  paperNo: string;
  fullScore: number;
  markingPapersProgress: number;
  roles: number[];
  isRule: boolean;
  v: number;
  isSmart: number;
  intersectionRoles?: number[];
}

export interface IExamClass {
  grdId: string;
  grdName: string;
  id: string;
  class_name: string;
  totalNum: number;
  abPaper: string;
  isShow?: boolean;
  intersectionRoles?: number[];
}

/**
 * 根据角色筛选学科
 * @param subjectList 学科列表
 * @param params 参数
 * @param params.year 入学年份
 * @param params.campusCode 校区编码
 * @param params.roles 角色列表
 */
function toRoleSubjectList(
  subjectList: IExamSubject[],
  params: {
    year: number;
    roles: number[];
    campusCode: string;
    gradeName?: string;
  }
) {
  // 只有教师角色
  if (params.roles.join(',') == '6') {
    let subjectIds = [
      ...new Set(
        UserRole.utils
          .getSubstituteClassList(params.campusCode)
          .filter(item => item.gradeName == params.gradeName || item.year == params.year)
          .map(item => {
            return String(item.subjectId);
          })
      ),
    ];
    subjectList = subjectList.filter(item => subjectIds.includes(item.id));
  }

  // 非运营，比较学科权限（成绩发布控制）
  if (!UserRole.isOperation) {
    // 获取当前报告入学年份的角色对应的学科
    const roleSubjectMap = UserRole.utils.getRoleSubjectListMapByYear(params.year, params.campusCode);
    if (params.roles.includes(1)) {
      roleSubjectMap[1] = UserRole.utils.getSchoolLeaderSubjectList(params.year, params.campusCode);
    }
    // 遍历学科列表，判断成绩发布中的角色是否包含角色
    let subjectIds = [];
    subjectList.forEach(item => {
      let intersectionRoles = [];
      if (item.roles && item.roles.length) {
        item.roles.forEach(role => {
          if (params.roles.includes(role)) {
            let roleSubjectList = roleSubjectMap[String(role)] || [];
            let ids = roleSubjectList.map(t => String(t.subjectId));
            // 查找学科角色,挂载到学科对象上
            if (ids.find(t => t == item.id)) {
              intersectionRoles.push(role);
            }
            subjectIds = [...subjectIds, ...ids];
          }
        });
      }
      item.intersectionRoles = intersectionRoles;
    });
    subjectIds = [...new Set(subjectIds)];
    subjectList = subjectList.filter(item => subjectIds.includes(item.id));
  }
  return subjectList;
}

/**
 * 根据角色筛选班级
 * @param classList 班级列表
 * @param params 参数
 * @param params.roles 角色列表
 * @param params.subjectId 学科ID
 * @param params.year 入学年份
 * @param params.campusCode 校区编码
 * @returns 班级列表
 */
function toRoleClassList(
  classList: IExamClass[],
  params: {
    roles: number[];
    subjectId: string;
    year: number;
    campusCode: string;
  }
) {
  if (UserRole.isOperation) {
    return classList;
  } else {
    // 学科对应角色和用户角色交集
    let intersectionRoles = params.roles;
    // 根据所交集的角色获取班级
    let classIds = [];

    // 根据学科的角色intersectionRoles，判断班级是否显示
    classList.forEach(item => {
      let roles = [];
      intersectionRoles.forEach(role => {
        if (role == 1 || role == 2 || role == 3 || role == 4) {
          classIds.push(item.id);
          roles.push(role);
        }
        if (role == 5) {
          if (UserRole.utils.getLeaderClassList(params.campusCode).some(leaClass => leaClass.id == item.id)) {
            classIds.push(item.id);
            roles.push(role);
          }
        }
        if (role == 6) {
          if (
            UserRole.utils
              .getSubstituteClassList(params.campusCode)
              .some(subClass => subClass.classId == item.id && subClass.subjectId == params.subjectId)
          ) {
            classIds.push(item.id);
            roles.push(role);
          }
        }
      });
      item.intersectionRoles = roles.map(t => Number(t));
    });

    classList = classList.filter(item => classIds.includes(item.id));
    return classList;
  }
}

/**
 * 获取考试报告学科列表
 * @param params 参数
 * @param params.examId 考试ID
 * @param params.campusCode 校区编码
 * @param params.statType 统计类型
 * @param params.roles 角色列表
 * @param params.year 入学年份
 * @param params.v 版本号
 * @param params.gradeName 年级名称
 * @returns 学科列表
 */
export async function getExamReportSubjectList(params: {
  examId: string;
  campusCode?: string;
  statType?: number;
  roles: any[];
  year: number;
  v: number | string;
  gradeName?: string;
}) {
  // 获取学科列表
  const res = await listExamSubject({
    examId: params.examId,
    statType: params.statType,
    role: params.roles.join(','),
    v: params.v,
  });
  const subjectList: IExamSubject[] = res.data;
  const noRoleSubjectList: IExamSubject[] = JSON.parse(JSON.stringify(subjectList));
  // 根据角色筛选学科
  const roleSubjectList = toRoleSubjectList(subjectList, {
    year: params.year,
    campusCode: params.campusCode,
    roles: params.roles,
    gradeName: params.gradeName,
  });

  return {
    roleSubjectList,
    noRoleSubjectList,
  };
}

/**
 * 获取考试报告班级列表
 * @param params 参数
 * @param params.examId 考试ID
 * @param params.subjectId 学科ID
 * @param params.roles 角色列表
 * @param params.subRoles 学科角色列表
 * @param params.year 入学年份
 * @param params.campusCode 校区编码
 * @returns 班级列表
 */
export async function getExamReportClassList(params: {
  examId: string | number;
  subjectId: string;
  roles: number[];
  year: number;
  campusCode?: string;
}) {
  // 获取班级
  const res = await clzListAPI({
    examId: params.examId,
    subjectId: params.subjectId,
  });

  const classList: IExamClass[] = res.data;
  const noRoleClassList: IExamClass[] = JSON.parse(JSON.stringify(classList));
  // 根据角色筛选班级
  const roleClassList = toRoleClassList(classList, {
    subjectId: params.subjectId,
    roles: params.roles,
    year: params.year,
    campusCode: params.campusCode,
  });

  return {
    roleClassList,
    noRoleClassList,
  };
}

/**
 * 检查是否具有管理员权限（校管、年级主任、学科主任、备课组长）
 */
function hasAdminRole(roles: number[]): boolean {
  return [1, 2, 3, 4].some(role => roles.includes(role));
}

/**
 * 检查功能权限
 */
function checkFuncPermission(funcId: string, intersectionRoles: number[], funcRoles: any[]): boolean {
  const func = funcRoles.find(item => item.funcId === funcId);
  if (!func) return false;

  const roleIds = func.roleIds.map(item => Number(item));
  return findIntersection(roleIds, intersectionRoles).length > 0;
}

/**
 * 获取功能权限
 * @param params 参数
 * @param params.funcId 功能ID
 * @param params.classId 班级ID
 * @param params.subjectId 学科ID
 * @returns 是否显示
 */
export function getFuncEnable(params: { funcId: FuncIdEnum; classId?: string; subjectId?: string }) {
  if (UserRole.isOperation) return true;

  const schoolReportPermission = sessionSave.get('schoolReportPermission');

  // 检查班级权限
  const innerClassList = sessionSave.get('innerClassList');
  const classItem = innerClassList.find(item => item.id === params.classId);
  if (classItem) {
    return (
      hasAdminRole(classItem.intersectionRoles) ||
      checkFuncPermission(params.funcId, classItem.intersectionRoles, schoolReportPermission.funcRoles)
    );
  }
  // 检查学科权限
  const innerSubjectList = sessionSave.get('innerSubjectList');
  const subjectItem = innerSubjectList.find(item => item.id === params.subjectId);
  if (subjectItem) {
    return (
      hasAdminRole(subjectItem.intersectionRoles) ||
      checkFuncPermission(params.funcId, subjectItem.intersectionRoles, schoolReportPermission.funcRoles)
    );
  }
  return schoolReportPermission.funcIds.includes(params.funcId);
}

/**
 * 获取菜单权限
 * @param params 参数
 * @param params.menuId 菜单ID
 * @param params.roles 角色列表
 * @returns 是否可用
 */
export function getMenuEnable(params: { menuId: MenuIdEnum; roles: number[] }) {
  if (UserRole.isOperation) return true;
  const schoolReportPermission = sessionSave.get('schoolReportPermission');

  const menuRoles = schoolReportPermission.menuRoles;
  const menuItem = menuRoles.find(item => item.menuId == params.menuId);
  if (!menuItem) return false;
  const menuRoleIds = menuItem.roleIds.map(role => Number(role));
  return findIntersection(menuRoleIds, params.roles).length > 0;
}

/**
 * 检查成绩状态
 * @param examId 考试ID
 * @returns 成绩状态检查结果
 */
async function checkingScoreStatus(examId: string): Promise<boolean | null> {
  try {
    const res = await getExamAnalyzeState({ examId });

    if (res === null || res?.data === null) {
      return false;
    }

    return res.code === 1 && res.data.dataState >= 1;
  } catch (error) {
    console.error('检查成绩状态失败:', error);
    return null;
  }
}

export class ScoreStatusManager {
  // 加载状态
  checkLoading: ElLoadingComponent = null;
  // 检查定时器
  checkTimer: ReturnType<typeof setInterval> | null = null;

  /**
   * 检查成绩状态
   * @param examId 考试ID
   * @param options 配置选项
   * @returns Promise<'error' | 'timeout' | 'success'>
   */
  public async startCheck(
    examId: any,
    options: {
      maxRetries?: number;
      retryInterval?: number;
      loadingText?: string;
    } = {}
  ): Promise<'error' | 'timeout' | 'success'> {
    // 默认配置
    const {
      maxRetries = 12,
      retryInterval = 3000,
      loadingText = '正在生成分析报告，请稍候，预计5-20s，若长时间未生成，请刷新页面重试',
    } = options;

    // 初始检查
    try {
      const initialCheck = await checkingScoreStatus(examId);

      // 检查失败
      if (initialCheck === null) {
        return 'error';
      }

      // 检查成功
      if (initialCheck === true) {
        return 'success';
      }

      // 需要轮询检查
      let checkCount = 0;
      this.checkLoading = Loading.service({
        lock: true,
        text: loadingText,
        background: 'rgba(0, 0, 0, 0.7)',
      });

      return new Promise<'error' | 'timeout' | 'success'>(resolve => {
        this.checkTimer = setInterval(async () => {
          try {
            const check = await checkingScoreStatus(examId);

            // 检查失败
            if (check === null) {
              this.stopCheck();
              resolve('error');
              return;
            }

            // 超时
            if (checkCount >= maxRetries) {
              this.stopCheck();
              resolve('timeout');
              return;
            }

            // 检查成功
            if (check === true) {
              this.stopCheck();
              resolve('success');
              return;
            }

            // 继续检查
            checkCount++;
          } catch (error) {
            console.error('轮询检查成绩状态失败:', error);
            this.stopCheck();
            resolve('error');
          }
        }, retryInterval);
      });
    } catch (error) {
      console.error('初始检查成绩状态失败:', error);
      return 'error';
    }
  }

  // 停止检查
  public stopCheck() {
    if (this.checkLoading) {
      this.checkLoading.close();
      this.checkLoading = null;
    }
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
      this.checkTimer = null;
    }
  }
}

class IndicatorManager {
  private static instance: IndicatorManager;

  // 成绩单指标列表
  scoreCardIndicatorList = [
    {
      label: '得分',
      value: 'score',
    },
    {
      label: '得分校排',
      value: 'grdRank',
    },
    {
      label: '得分班排',
      value: 'clsRank',
    },
    {
      label: '赋分',
      value: 'ruleScore',
    },
    {
      label: '赋分校排',
      value: 'ruleGrdRank',
    },
    {
      label: '赋分班排',
      value: 'ruleClsRank',
    },
    {
      label: '选科组合',
      value: 'orientAlias',
    },
    {
      label: '选考得分',
      value: 'orientScore',
    },
    {
      label: '选考赋分',
      value: 'orientRuleScore',
    },
    {
      label: '等级',
      value: 'lv',
    },
    {
      label: '赋分等级',
      value: 'ruleName',
    },
  ];

  // 一分五率指标列表
  fiveRateIndicatorList = [
    {
      label: '超均率',
      value: 'passAvgRate',
    },
    {
      label: '增长率',
      value: 'growthRate',
    },
    {
      label: '优秀率',
      value: 'fineRate',
    },
    {
      label: '优良率',
      value: 'goodRate',
    },
    {
      label: '及格率',
      value: 'passRate',
    },
    {
      label: '不及格率',
      value: 'failRate',
    },
    {
      label: '低分率',
      value: 'lowRate',
    },
  ];

  // 学生小分表指标列表
  scoreSheetIndicatorList = [
    {
      label: '等级',
      value: 1,
    },
    {
      label: '主客观得分',
      value: 2,
    },
    {
      label: '主观小题分',
      value: 3,
    },
    {
      label: '得分',
      value: 4,
    },
    {
      label: '题型得分',
      value: 5,
    },
    {
      label: '客观小题分',
      value: 6,
    },
    {
      label: '班级排名',
      value: 7,
    },
    {
      label: '大题得分',
      value: 8,
    },
    {
      label: '客观题作答',
      value: 9,
    },
    {
      label: '年级排名',
      value: 10,
    },
  ];

  // 大小题分析指标列表
  questionAnalysisIndicatorList = [
    {
      label: '大题分析',
      value: '大题分析',
    },
    {
      label: '满分率',
      value: '满分率',
    },
    {
      label: '标准差',
      value: '标准差',
    },
    {
      label: '小题分析',
      value: '小题分析',
    },
    {
      label: '零分率',
      value: '零分率',
    },
    {
      label: '均分',
      value: '均分',
    },
    {
      label: '题型',
      value: '题型',
    },
    {
      label: '难度',
      value: '难度',
    },
    {
      label: '得分率',
      value: '得分率',
    },
    {
      label: '分值',
      value: '分值',
    },
    {
      label: '区分度',
      value: '区分度',
    },
  ];

  // 成绩成段分析指标列表
  scoreSectionIndicatorList = [
    { label: '本段人数', value: 'num' },
    { label: '本段比例', value: 'numRate' },
    { label: '本段均分', value: 'avg' },
    { label: '累计人数', value: 'addNum' },
    { label: '累计比例', value: 'addNumRate' },
    { label: '累计均分', value: 'addAvg' },
  ];

  // 总名次分析指标列表
  totalRankIndicatorList = [
    { label: '本段人数', value: 'num' },
    { label: '本段比例', value: 'numRate' },
    { label: '本段均分', value: 'avg' },
    { label: '累计人数', value: 'addNum' },
    { label: '累计比例', value: 'addNumRate' },
    { label: '累计均分', value: 'addAvg' },
  ];

  map: Map<string, any> = new Map();

  constructor() {}

  // 获取默认数据
  getDefault() {
    return {
      // 成绩单
      cardHome: {
        indicator: this.fiveRateIndicatorList.map(item => item.value),
      },
      // 一分五率
      fiveRate: {
        indicator: this.fiveRateIndicatorList.map(item => item.value),
      },
      // 学生小分表
      scoreSheet: {
        indicator: this.scoreSheetIndicatorList.map(item => item.value),
      },
      // 大小题分析
      questionAnalysis: {
        indicator: ['大题分析', '小题分析', '题型', '分值', '标准差', '均分', '得分率'],
      },
      // 成绩分段分析
      scoreSection: {
        indicator: this.scoreSectionIndicatorList.map(item => item.value),
      },
      // 总名次分析
      totalRank: {
        indicator: this.totalRankIndicatorList.map(item => item.value),
      },
    };
  }

  // 初始化
  async init() {
    try {
      const res = await getReportCfgAPI({ id: sessionSave.get('loginInfo').id });
      const data = res.data;
      if (data && !Array.isArray(data)) {
        this.map = new Map(Object.entries(data));
      }
    } catch (error) {
      console.error('初始化指标配置失败:', error);
    }
  }

  // 获取配置
  get(key: string) {
    const data = this.map.get(key);
    if (!data) {
      return this.getDefault()[key];
    }
    return data;
  }

  // 获取指标
  getIndicator(key: string) {
    const data = this.get(key);
    return data?.indicator || [];
  }

  // 设置配置
  async set(key: string, data) {
    this.map.set(key, data);
    this.save();
  }

  // 设置指标
  async setIndicator(key: string, indicator: any[]) {
    const data = { ...this.get(key) };
    data.indicator = indicator;
    this.set(key, data);
  }

  // 保存配置
  async save() {
    await setReportCfgAPI({ token: getToken(), data: Object.fromEntries(this.map) });
  }
}

export const indicatorManager = new IndicatorManager();

// /**
//  * 检查成绩状态
//  * @param examId 考试ID
//  * @param options 配置选项
//  * @returns Promise<'error' | 'timeout' | 'success'>
//  */
// export async function startCheckingScoreStatus(
//   examId: any,
//   options: {
//     maxRetries?: number;
//     retryInterval?: number;
//     loadingText?: string;
//   } = {}
// ): Promise<'error' | 'timeout' | 'success'> {
//   // 默认配置
//   const {
//     maxRetries = 12,
//     retryInterval = 3000,
//     loadingText = '正在生成分析报告，请稍候，预计5-20s，若长时间未生成，请刷新页面重试',
//   } = options;

//   // 初始检查
//   try {
//     const initialCheck = await checkingScoreStatus(examId);

//     // 检查失败
//     if (initialCheck === null) {
//       return 'error';
//     }

//     // 检查成功
//     if (initialCheck === true) {
//       return 'success';
//     }

//     // 需要轮询检查
//     let checkCount = 0;
//     let checkTimer: ReturnType<typeof setInterval> | null = null;
//     const checkLoading = Loading.service({
//       lock: true,
//       text: loadingText,
//       background: 'rgba(0, 0, 0, 0.7)',
//     });

//     // 确保在任何情况下都能清理资源
//     const cleanup = () => {
//       if (checkLoading) {
//         checkLoading.close();
//       }
//       if (checkTimer) {
//         clearInterval(checkTimer);
//       }
//     };

//     return new Promise<'error' | 'timeout' | 'success'>(resolve => {
//       checkTimer = setInterval(async () => {
//         try {
//           const check = await checkingScoreStatus(examId);

//           // 检查失败
//           if (check === null) {
//             cleanup();
//             resolve('error');
//             return;
//           }

//           // 超时
//           if (checkCount >= maxRetries) {
//             cleanup();
//             resolve('timeout');
//             return;
//           }

//           // 检查成功
//           if (check === true) {
//             cleanup();
//             resolve('success');
//             return;
//           }

//           // 继续检查
//           checkCount++;
//         } catch (error) {
//           console.error('轮询检查成绩状态失败:', error);
//           cleanup();
//           resolve('error');
//         }
//       }, retryInterval);
//     });
//   } catch (error) {
//     console.error('初始检查成绩状态失败:', error);
//     return 'error';
//   }
// }

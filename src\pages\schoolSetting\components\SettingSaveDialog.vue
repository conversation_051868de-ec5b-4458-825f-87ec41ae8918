<template>
  <el-dialog :visible.sync="visible" title="保存设置" width="500px" v-bind="$attrs" v-on="$listeners">
    <p>当前设置的参数是通用年级的参数，是否需要同步到以下年级：</p>
    <el-checkbox-group v-model="checkedGradeList">
      <el-checkbox v-for="item in gradeList" :key="item.id" :label="item.id">{{ item.gradeName }}</el-checkbox>
    </el-checkbox-group>

    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :disabled="checkedGradeList.length === 0" @click="handleSave">确定</el-button>
    </span>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Mixins, Vue } from 'vue-property-decorator';
import SchoolSettingMixin from './SchoolSetting.mixin';
import { GradeInfo } from '../types';

@Component
export default class SettingSaveDialog extends Mixins(SchoolSettingMixin) {
  // 是否显示
  private visible = false;
  // 年级列表
  private gradeList: GradeInfo[] = [];
  // 选中的年级
  private checkedGradeList: string[] = [];

  mounted() {
    this.gradeList = JSON.parse(JSON.stringify(this.currentGradeList.filter(item => item.id !== 0)));
    this.visible = true;
  }

  beforeDestroy() {
    this.visible = false;
  }

  handleSave() {
    this.$emit('save', this.checkedGradeList);
  }
}
</script>

<style scoped lang="scss"></style>

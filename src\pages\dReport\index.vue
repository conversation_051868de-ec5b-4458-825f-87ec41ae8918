<!-- 讲评报告 -->
<template>
  <div class="tab-container">
    <div class="tab-main" v-loading="isLoading">
      <router-view
        ref="commentRef"
        class="tab-view"
        :key="key"
        :filterData="filterData"
        @changeAbPaper="onChangeAbPaper"
        @initAbPaper="onInitAbPaper"
        v-if="isInit"
      />
      <no-data v-else-if="!isLoading" class="no-data" />
    </div>
    <div class="tab-footer comment-footer">
      <!--报告列表-->
      <div class="report-select pull-left" v-if="reportList.length > 1">
        <el-popover
          popper-class="report-select-popover"
          placement="top-start"
          trigger="click"
          v-model="showReportPopover"
        >
          <ul class="select-report-list list-none">
            <li
              class="click-element"
              v-for="item in reportList"
              :key="item.name"
              :class="{ 'active pointer-none': item.value.examId == useExamId }"
              @click="selectReport(item)"
            >
              {{ item.name }}
            </li>
          </ul>
          <el-button class="footer-btn ellipsis" slot="reference">{{ useExamName || reportList[0].name }}</el-button>
        </el-popover>
      </div>

      <!--学科-->
      <div class="subject-select pull-left" v-if="isCommentPage && examSubjectList.length > 1">
        <el-select v-model="filterData.subjectId" class="term-select" @change="changeFilter" placeholder="请选择">
          <el-option v-for="item in examSubjectList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
        </el-select>
      </div>

      <!--班级-->
      <div v-if="isCommentPage && classList.length" class="class-select pull-left">
        <el-select
          v-model="filterData.classId"
          class="term-select"
          @change="changeFilter($event, 'class')"
          placeholder="请选择"
        >
          <el-option v-for="item in classList" :key="item.id" :label="item.class_name" :value="item.id"> </el-option>
        </el-select>
      </div>

      <el-button class="footer-btn close-btn pull-right" @click="closeWnd">关闭</el-button>
      <el-button class="footer-btn prev-btn pull-right" @click="$router.push({ path: '/comment' })">上一页 </el-button>
      <el-button
        class="footer-btn footer-btn--comment pull-right"
        type="success"
        :disabled="joinedCommentCount === 1"
        @click="startCompareComment"
        v-if="joinedCommentCount"
      >
        对比讲评 ({{ joinedCommentCount }})
      </el-button>

      <el-select
        v-if="examClassList.length && examSubjectList.length"
        v-model="currentPath"
        class="tab-select pull-right"
        placeholder="请选择"
        @change="changeTab"
      >
        <el-option v-for="item in selectList" :key="item.path" :label="item.label" :value="item.path"> </el-option>
      </el-select>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { getExamReportList, clzListAPI, listExamSubject, getChildReportList } from '@/service/pexam';
import { deepClone, findIntersection } from '@/utils/index';
import { State, Getter, Mutation } from 'vuex-class';
import { localSave } from '@/utils';
import { getExamReportClassList, getExamReportSubjectList } from '@/utils/examReportUtils';
import NoData from '@/components/noData.vue';
import UserRole from '@/utils/UserRole';

function getExamNamePrefix(source: number) {
  if (source == 101) {
    return '【系统报告】【分层班】';
  }
  if (source == 102) {
    return '【系统报告】【行政班物理方向】';
  }
  if (source == 103) {
    return '【系统报告】【行政班历史方向】';
  }
  if (source == 105) {
    return '【系统报告】';
  }
  return '【自定义报告】';
}

@Component({
  components: {
    NoData,
  },
})
export default class CommentReport extends Vue {
  @State joinCommentParam: any;
  @Getter joinedCommentCount: Number;
  @Mutation CLEAR_COMPARE_COMMENT: () => void;

  public $refs!: {
    commentRef: HTMLFormElement;
  };

  // AB卷
  abPaper = '';
  // 考试学科列表
  examSubjectList = [];
  // 考试班级列表
  examClassList = [];
  // 报告列表
  reportList: { name: string; value: IExamReportInfo }[] = [];
  // 当前考试Id
  useExamId: any = this.$route.query.examId || this.$sessionSave.get('reportDetail').examId;
  // 当前考试名称
  useExamName = '';
  // 显示报告气泡框
  showReportPopover: boolean = false;
  // 当前选中的筛选项数据
  filterData = {
    classId: '',
    subjectId: '',
    phaseId: '',
    xfId: '',
    subjectName: '',
  };
  // 路由key
  key: number = 1;
  loginInfo: LoginInfo = null;
  // 选择列表
  selectList = [
    {
      label: '题目讲评',
      path: '/dReport/commentDetail',
    },
    {
      label: '学情分析',
      path: '/dReport/reportDetail',
    },
  ];
  // 当前选择的路径
  currentPath: string = '/dReport/commentDetail';
  // 是否已经初始化
  isInit: boolean = false;
  // 是否正在加载
  isLoading: boolean = false;

  get isCommentPage() {
    return this.currentPath == '/dReport/commentDetail';
  }

  // 班级列表
  get classList() {
    if (!this.examSubjectList.length) return [];

    let arr = this.examClassList;
    if (this.abPaper !== '') {
      arr = this.examClassList.filter(
        item => item.abPaper !== null && item.abPaper !== undefined && item.abPaper.split(',').includes(this.abPaper)
      );
    }

    return arr;
  }

  created() {
    this.$sessionSave.set('currentRoles', '6');
  }

  async mounted() {
    this.loginInfo = this.$sessionSave.get('loginInfo');
    this.$bus.$on('header-change-filter', ({ type, item }) => {
      if (item) this.changeFilter(item.id, type, true);
    });

    this.currentPath = this.selectList.find(t => this.$route.path.includes(t.path)).path;
    await this.beforeRouteEnter();
    await this.updateViewModel();
  }

  beforeDestroy() {
    this.$bus.$off('header-change-filter');
    this.CLEAR_COMPARE_COMMENT();
  }

  async updateViewModel() {
    this.isLoading = true;
    this.isInit = false;
    await this.getSubjectList();
    if (this.examSubjectList.length == 0) {
      this.$message.warning('请确认您的任教学科信息是否有误');
      this.isLoading = false;
      return;
    }
    await this.changeFilter(this.filterData.subjectId, 'subject');
  }

  /**
   * @description: 页面路由进入之前
   * @return {*}
   */
  async beforeRouteEnter() {
    const { examList, hasCampus } = await getChildReportList({
      examId: this.$sessionSave.get('reportParent').examId,
      examName: this.$sessionSave.get('reportParent').examName,
    });
    let actions = [];
    let list = examList;
    list.forEach(item => {
      actions.push({
        value: item,
        name: `${getExamNamePrefix(item.source)}${item.examName}`,
      });
    });
    let item = this.$sessionSave.get('reportParent');
    this.reportList = actions;
    if (hasCampus) {
      this.useExamId = actions[0].value.examId;
      this.useExamName = actions[0].name;
      this.$sessionSave.set('reportDetail', actions[0].value);
    } else {
      this.reportList.unshift({ value: item, name: '【系统报告】' + item.examName });
    }
  }

  // 获取班级
  async getExamClass() {
    this.isLoading = true;
    const { examId, year, campusCode } = this.$sessionSave.get('reportDetail');
    this.examClassList = [];
    let subject = this.examSubjectList.find(item => item.id == this.filterData.subjectId);
    let subjectId = subject.id;
    let subRoles = subject.roles;
    const { roleClassList, noRoleClassList } = await getExamReportClassList({
      examId,
      roles: [6],
      subjectId,
      subRoles,
      year,
      campusCode,
    });
    this.examClassList = roleClassList;
    this.$sessionSave.set('innerNoRoleClassList', noRoleClassList);
    this.$sessionSave.set('innerClassList', this.examClassList);

    const cacheKey = `comment_classId_${this.loginInfo.id}`;
    let cacheClassId = localSave.get(cacheKey);
    if (this.examClassList.length) {
      if (this.examClassList.some(it => it.id === cacheClassId)) {
        this.filterData.classId = cacheClassId;
      } else {
        this.filterData.classId = this.examClassList[0].id;
        localSave.set(cacheKey, this.filterData.classId);
      }
      this.isInit = true;
    } else {
      this.filterData.classId = '';
      // 发布前可看
      if (this.$route.query.statType === '0') {
        this.isInit = true;
      } else {
        // this.$message.warning('请确认您的任教班级信息是否有误');
        this.isInit = false;
      }
    }
    this.isLoading = false;
  }

  /**
   * @name:获取学科
   */
  async getSubjectList() {
    const { year, campusCode, gradeName, v } = this.$sessionSave.get('reportDetail');
    const { roleSubjectList } = await getExamReportSubjectList({
      examId: this.useExamId,
      roles: [6],
      v: v,
      year,
      gradeName,
      statType: this.$route.query.statType === '0' ? 0 : 1,
      campusCode,
    });
    this.examSubjectList = roleSubjectList;
    if (!this.examSubjectList.length) return;
    this.$sessionSave.set('innerSubjectList', this.examSubjectList);
    let subject = this.examSubjectList[0];
    if (this.$sessionSave.get('subjectId')) {
      let tempSubject = this.examSubjectList.find(item => item.id == this.$sessionSave.get('subjectId'));
      if (tempSubject) subject = tempSubject;
    }

    this.filterData.subjectId = subject.id;
    this.filterData.subjectName = subject.name;
    this.filterData.phaseId = subject.phaseId;
    this.filterData.xfId = subject.xfId;
  }

  // 选择报告
  async selectReport({ value, name }) {
    this.useExamId = value.examId;
    this.useExamName = name;
    this.$sessionSave.set('reportDetail', value);
    await this.updateViewModel();
    this.showReportPopover = false;
    this.key++;
  }

  async changeFilter(id, type: string, bus = false) {
    let item;
    if (type == 'class') {
      item = this.examClassList.find(t => t.id == id);
      this.filterData.classId = item.id;
    } else {
      item = this.examSubjectList.find(t => t.id == id);
      this.filterData.subjectId = item.id;
      this.filterData.phaseId = item.phaseId;
      this.filterData.xfId = item.xfId;
      this.filterData.subjectName = item.name;
      this.$sessionSave.set('subjectId', item.id);
      await this.getExamClass();
    }
    if (!bus) this.$bus.$emit('desktop-change-filter', { type, item });

    const cacheKey = `comment_classId_${this.loginInfo.id}`;
    localSave.set(cacheKey, this.filterData.classId);
  }

  /**
   * @description: 开始对比讲评
   */
  async startCompareComment() {
    const loginInfo = this.loginInfo;
    let joinCommentParam = this.joinCommentParam;

    this.$bus.$emit('closePreview');
    this.CLEAR_COMPARE_COMMENT();

    await this.$refs.commentRef.storeCommentInfo(joinCommentParam.quesNo);
    // 获取对比学生信息
    const studentList = joinCommentParam.stuList;
    // 缓存对比讲评信息
    await window.idbStore.put('compare', {
      userid: loginInfo.id,
      list: studentList,
    });
    this.$cefMsg(
      'mirco.call_cplus',
      'open_explain',
      `${location.origin}/quescom/compare/${loginInfo.id}_${this.useExamId}_${this.filterData.subjectId}/${joinCommentParam.tQuesNo}`
    );
  }

  // 关闭窗口
  closeWnd() {
    cef.message.sendMessage('mirco.call_cplus', ['', 'closewnd']);
  }

  changeTab(val) {
    this.CLEAR_COMPARE_COMMENT();
    this.$sessionSave.set('DReport_ClassId', this.filterData.classId);
    this.$router.push(val);
  }

  onChangeAbPaper(val) {
    this.abPaper = val;
    if (this.classList.length === 1) {
      this.changeFilter(this.classList[0].id, 'class');
    } else {
      this.changeFilter(this.filterData.classId, 'class');
    }
  }

  onInitAbPaper(val) {
    this.abPaper = val;
    // 根据过滤后的AB班级判断当前选择的班级是否支持
    if (this.filterData.classId && !this.classList.some(it => it.id === this.filterData.classId)) {
      this.changeFilter(this.classList[0].id, 'class');
    }
  }
}
</script>

<style scoped lang="scss">
.tab-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tab-main {
  width: 100%;
  height: 100%;
  flex: 1;
  overflow: hidden;

  .no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
}

.tab-view {
  height: 100%;
}

.tab-footer {
}

.comment-footer {
  position: relative;
  box-shadow: 0 0 3px 0 rgba(49, 57, 215, 0.23);
  height: 90px;
  background-color: #fff;
  padding: 0 40px;

  .class-select,
  .subject-select,
  .tab-select {
    width: 150px;
    margin-top: 20px;

    ::v-deep {
      .el-input__inner {
        border-width: 2px;
      }
    }
  }

  .tab-select {
    margin-right: 10px;

    ::v-deep {
      .el-input__inner {
        height: 52px;
        font-size: 24px;
        border-radius: 8px;
      }
    }
  }

  .report-select {
    margin-right: 20px;

    .el-button {
      width: 150px;
      padding: 0 10px;
      line-height: 38px;
      height: 40px;
      font-size: 14px;
      border-radius: 4px;
      text-align: left;
    }
  }

  .subject-select {
    margin-right: 20px;
  }

  .footer-btn {
    margin-top: 20px;
    text-align: center;
    font-size: 24px;
    border-width: 2px;
    border-radius: 8px;

    &.close-btn,
    &.prev-btn {
      width: 150px;
      background-color: #fff;
      border-color: #dddee1;
    }

    &.close-btn {
      margin-left: 20px;
    }
  }
}
</style>

<style lang="scss">
.report-select-popover {
  padding: 10px 0;
  border: 1px solid #e4e7ed;

  .select-report-list {
    line-height: 34px;
    font-size: 14px;

    > li {
      padding: 0 10px;

      &.active {
        color: #409eff;
      }

      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}
</style>

<template>
  <div class="class-compare clearfix">
    <headerFilter
      @updateFilter="updateFilter"
      @init="initFilter"
      ref="headerFilter"
      class="header-filter"
    ></headerFilter>

    <router-view
      v-if="isInit && examSubjectList.length"
      class="router-view"
      :class="{ desktop: isDesktop }"
      ref="routerView"
      :currentSubId="currentSubId"
      :filterData="filterData"
      @hideTotalClass="hideTotalClass"
      @showTotalClass="showTotalClass"
    ></router-view>
    <el-skeleton style="padding: 20px" :rows="7" animated v-else />
  </div>
</template>

<script>
import { getConfAPI } from '@/service/pexam';

import headerFilter from '@/components/headerFilter.vue';
import Boxplot from '@/pages/lookReport/classCompare/boxplot.vue';
import RankSection from '@/pages/lookReport/classCompare/rankSection.vue';
import ScoreSection from '@/pages/lookReport/classCompare/scoreSection.vue';
import TotalRank from '@/pages/lookReport/classCompare/totalRank.vue';
import NoData from '@/components/noData.vue';

export default {
  name: 'ClassCompare',
  components: {
    ScoreSection,
    TotalRank,
    RankSection,
    Boxplot,
    headerFilter,
    NoData,
  },
  data() {
    return {
      tabList: [
        { name: '成绩单', link: 'cardHome' },
        { name: '成绩分段分析', link: 'scoreSection' },
        { name: '总名次分析', link: 'totalRank' },
        { name: '各名次段分析', link: 'rankSection' },
        { name: '箱线图', link: 'boxplot' },
      ],
      currentSubId: '', // 当前选择的学科id
      // 筛选条件
      filterData: null,
      // 是否加载学科
      isSubjectInit: false,
      // 是否加载帅选
      isInit: false,
      // 考试列表
      examSubjectList: [],
    };
  },
  watch: {
    async $route(to, from) {
    },
  },
  computed: {
    isDesktop() {
      return this.$route.path.includes('/dReport');
    },
  },
  methods: {
    async initFilter({ filterData, examSubjectList }) {
      this.examSubjectList = examSubjectList;
      this.isInit = true;
    },

    async updateFilter(filterData, type) {
      this.currentSubId = filterData.subjectId;
      this.filterData = this.$deepClone(filterData);
      await this.$nextTick();
      this.$refs.routerView &&
        this.$refs.routerView.updateFilter &&
        this.$refs.routerView.updateFilter(filterData, type);
    },
    hideTotalClass(className) {
      this.$refs.headerFilter.hideTotalClass(className);
    },
    showTotalClass() {
      this.$refs.headerFilter.showTotalClass();
    },
  },
};
</script>

<style lang="scss" scoped>
@import '../../styles/base.css';

.header-filter {
  padding-top: 20px;
  padding-left: 24px;
  padding-right: 24px;
}

.router-view {
  padding: 24px;
  padding-top: 0;
  height: 100%;
  // overflow: hidden;
  // overflow-y: auto;

  &.desktop {
    overflow: hidden;
    overflow-y: auto;
  }
}

.report-router {
  background-color: #f7fafc !important;
  padding: 0 !important;
}

.class-compare {
  .group-btn {
    background: #fff;
    //box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
    border-radius: 6px 6px 0 0;
    // padding: 20px 24px;
  }

  .score-section {
    margin-bottom: 10px;
  }

  .line {
    width: 100%;
    border-top: 1px solid #e4e8eb;
  }

  .subjects-box {
    margin: 10px 0;

    .subject-label {
      color: var(--normal-color);
    }

    .subject-list {
      display: inline-flex;

      .subject-item {
        color: var(--normal-color);
        cursor: pointer;

        &.active {
          color: var(--primary-color);
        }
      }
    }
  }

  .ques-list-box {
    display: flex;

    & > span {
      white-space: nowrap;
    }

    .ques-list {
      display: flex;
      flex-wrap: wrap;

      & > span {
        margin-bottom: 6px;
        white-space: nowrap;
      }
    }
  }

  .five-rate-opt-box {
    padding-top: 10px;
    overflow: hidden;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;

    .opt-label {
      color: var(--normal-color);
      white-space: nowrap;
      margin-top: 3px;
      margin-bottom: 6px;
    }

    .text-field {
      min-width: 6em;
      max-width: 7em;
      text-align: center;
      display: inline-block;
    }

    .opt-input-wrapper {
      display: inline-block;
      flex-shrink: 0;
      margin-right: 20px;

      .opt-bottom {
        margin-top: 20px;
        float: right;
      }
    }

    .opt-btn {
      flex: 1;
    }

    .confirm-btn {
      font-size: 16px;
      margin-top: 20px;
    }
  }
}

.radioGroup {
  margin: 0 auto 10px;
}
</style>
<style lang="scss">
.score-section {
  .numeric-input-box {
    width: 60px;
  }
}

.radioGroup {
  .el-radio-button__inner {
    font-size: 16px;
  }
}
</style>

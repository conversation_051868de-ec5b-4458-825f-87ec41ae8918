<!-- 该组件暂无优化 -->
<template>
  <div :style="{ top: stickyTop }" class="ques-filter-wrapper">
    <div ref="baseCondition" class="ques-filter-condition">
      <div
        class="filter-type display_flex align-items_flex-start"
        v-for="(item, i) in filterList"
        :key="'parent_' + i"
      >
        <span class="typeName">{{ item.title }}：</span>
        <div class="typeList flex_1 over-hidden">
          <span
            class="typeOpts"
            @click="clickFliterItem(typeItem, item)"
            v-for="typeItem in item.typeList"
            :key="typeItem.name"
            :class="{ active: item.activeName === typeItem.name }"
            >{{ typeItem.name }}</span
          >
        </div>
      </div>
    </div>
    <div class="ques-filter-condition stuck-filter-box">
      <div class="filter-type">
        <span class="typeName">搜索：</span>
        <el-input
          v-model="content"
          @change="search"
          class="filter-type-item"
          clearable
          placeholder="题干搜索"
          style="width: 300px"
          size="mini"
        ></el-input>

        <div
          v-show="stuck"
          class="filter-type-item-box more-condition-box animated fadeIn"
        >
          <div
            class="filter-type-item"
            v-for="(item, index) in filterList"
            :key="index"
          >
            <el-popover
              placement="bottom"
              :width="item.width"
              :open-delay="80"
              :close-delay="80"
              trigger="hover"
            >
              <div class="typeList">
                <span
                  class="typeOpts"
                  @click="clickFliterItem(typeItem, item)"
                  v-for="typeItem in item.typeList"
                  :key="typeItem.name"
                  :class="{ active: item.activeName === typeItem.name }"
                  >{{ typeItem.name }}</span
                >
              </div>
              <span class="dropdown-link" slot="reference">
                <span class="text-box" v-if="!item.activeTitle">
                  <span class="text">{{ item.title }}</span>
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </span>
                <span class="text-box" v-else>
                  <span class="text">{{ item.activeTitle }}</span>
                  <i
                    class="el-icon-circle-close"
                    v-show="item.title != '排序'"
                    @click="clearCurrentSelect(item)"
                  ></i>
                  <i
                    class="el-icon-arrow-down el-icon--right"
                    v-show="item.title == '排序'"
                  ></i>
                </span>
              </span>
            </el-popover>
          </div>
        </div>
        <span class="ques-nums">题目数：{{ quesNums }}</span>
      </div>
    </div>

    <div style="width: 100%; height: 10px; background-color: #f1f6f9"></div>
  </div>
</template>

<!-- 该组件暂无优化 -->
<script>
import { getFilterCondition } from "@/service/ptask.js";
import { getSearchInfo } from "@/service/yiqi";

export default {
  name: "QuesNetFilter",
  components: {},
  props: {
    stuck: {
      type: Boolean,
      default: false,
    },
  },
  updated() {
    if (this.$refs.baseCondition) {
      // console.log(this.$refs.baseCondition.clientHeight)
      this.stickyTop =
        -(
          this.topObj[this.currentQuesBank] ||
          this.$refs.baseCondition.clientHeight
        ) + "px";
      this.topObj[this.currentQuesBank] =
        this.topObj[this.currentQuesBank] ||
        this.$refs.baseCondition.clientHeight;
    }
  },

  data() {
    return {
      // 路由参数上的信息
      workbookInfo: {},
      // 网络题库的筛选项
      filterList: [],
      stickyTop: "",
      content: "",
      // xf题库筛选项
      xfCondition: [],
      // 一起题库筛选项
      yiQCondition: [],
      // 菁优题库筛选项
      jyeooCondition: [],
      // 当前题库类型—— 菁优、一起、xf
      currentQuesBank: "yiqi",
      // 一起题型数据
      item_types_with_subtype: [],
      // 菁优题型数据
      item_types: [],
      // 菁优题目难度数据
      difficulties: [],
      topObj: {
        yiqi: 0,
        jyeoo: 0,
        xf: 0,
      },
      quesNums: "",
    };
  },
  beforeDestroy() {
    this.$bus.$off("changeQuesBank");
  },
  mounted() {
    this.workbookInfo = this.$route.query;
    // this.getXfFilterCondition()
    this.onQuesBankChange();
    this.$bus.$on("content", (value) => {
      this.content = "";
    });
  },
  methods: {
    // 监听题库类型变化
    onQuesBankChange() {
      // 接收 quesNetPoint 组件的广播事件
      this.$bus.$on("changeQuesBank", (type) => {
        if (type === "yiqi") {
          this.filterList = this.yiQCondition;
          this.currentQuesBank = "yiqi";
        } else if (type === "jyeoo") {
          this.filterList = this.jyeooCondition;
          this.currentQuesBank = "jyeoo";
        } else {
          this.filterList = this.xfCondition;
          this.currentQuesBank = "xf";
        }
      });
      this.$bus.$on("getQuesNums", (data) => {
        this.quesNums = data;
      });
    },
    // 获取一起题库筛选条件
    getFilterCondition(tokenInfo, subInfo) {
      getSearchInfo({
        // schoolId: this.workbookInfo.school_id,
        headers: {
          token: tokenInfo.token,
        },
        data: {
          userId: tokenInfo.id || "",
          subId: subInfo.id || "",
          type: 0,
        },
      })
        .then((data) => {
          if (data.yiqiInfo) {
            let res =
              (JSON.parse(data.yiqiInfo) && JSON.parse(data.yiqiInfo).data) ||
              "";
            if (res) {
              this.handleYiQCondition(res);
            }
          } else {
            this.filterList = [];
          }
          // if(data.jyeoo) {
          //   let res = JSON.parse(data.jyeoo) || '' ;
          //   if(res) {
          //     this.handleJyeooCondition(res)
          //   }
          // } else {
          //   this.filterList = []
          // }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 处理一起题库条件
    handleYiQCondition(data) {
      let yiQCondition = [];
      // 题型
      if (data.item_types && data.item_types.length) {
        this.item_types_with_subtype = data.item_types_with_subtype;
        let quesType = data.item_types;
        quesType.forEach((it) => {
          it.xfId = it.value;
        });
        quesType.unshift({ id: "", name: "全部", type: "", xfId: "" });
        yiQCondition.push({
          title: "题型",
          activeName: "全部",
          width: 420,
          activeTitle: "",
          key: "xfSectionCode",
          typeList: quesType,
        });
      }
      // 难度
      if (data.difficulties && data.difficulties.length) {
        let diffs = data.difficulties;
        diffs.forEach((it) => {
          it.xfId = it.value;
          it.name = it.name == "中等" ? "一般" : it.name;
        });
        diffs.unshift({ id: "", name: "全部", type: "", xfId: "" });
        yiQCondition.push({
          title: "难度",
          activeName: "全部",
          width: 370,
          activeTitle: "",
          key: "difficultyCode",
          typeList: diffs,
        });
      }
      if (data.item_tags && data.item_tags.length) {
        let tags = data.item_tags;
        tags.forEach((it) => {
          it.xfId = it.value;
        });
        tags.unshift({ id: "", name: "全部", type: "", xfId: "" });
        yiQCondition.push({
          title: "题类",
          activeName: "全部",
          width: 370,
          activeTitle: "",
          key: "examCode",
          typeList: tags,
        });
      }
      if (data.provinces && data.provinces.length) {
        let provinces = data.provinces;
        provinces.forEach((it) => {
          it.xfId = it._id;
        });
        provinces.unshift({ id: "", name: "全部", type: "", xfId: "" });
        yiQCondition.push({
          title: "地区",
          activeName: "全部",
          width: 370,
          activeTitle: "",
          key: "provinceCode",
          typeList: provinces,
        });
      }
      if (data.sources && data.sources.length) {
        let sources = data.sources;
        sources.forEach((it) => {
          it.xfId = it.value;
        });
        sources.unshift({ id: "", name: "全部", type: "", xfId: "" });
        yiQCondition.push({
          title: "来源",
          activeName: "全部",
          width: 370,
          activeTitle: "",
          key: "sourceCode",
          typeList: sources,
        });
      }
      if (data.years && data.years.length) {
        let years = [];
        years = data.years.map((it) => {
          return {
            id: it,
            xfId: it,
            name: it,
          };
        });
        years.unshift({ id: "", name: "全部", type: "", xfId: "" });
        yiQCondition.push({
          title: "年份",
          activeName: "全部",
          width: 370,
          activeTitle: "",
          key: "yearCode",
          typeList: years,
        });
      }
      this.yiQCondition = yiQCondition;
      this.filterList = this.yiQCondition;
    },
    // 处理菁优题库条件
    handleJyeooCondition(data) {
      let jyeooCondition = [];
      // 题型
      if (data.item_types.length) {
        this.item_types = data.item_types;
        let quesTypes = data.item_types;
        quesTypes.forEach((it) => {
          it.xfId = it.value;
        });
        quesTypes.unshift({ id: "", name: "全部", type: "", xfId: "" });
        jyeooCondition.push({
          title: "题型",
          activeName: "全部",
          width: 420,
          activeTitle: "",
          key: "xfSectionCode",
          typeList: quesTypes,
        });
      }
      // 难度
      if (data.difficulties.length) {
        this.difficulties = data.difficulties;
        let diffs = data.difficulties;
        diffs.forEach((it) => {
          it.xfId = it.value;
          it.name = it.name == "中等" ? "一般" : it.name;
          it.name = it.name == "易" ? "容易" : it.name;
          it.name = it.name == "难" ? "困难" : it.name;
        });
        diffs.unshift({ id: "", name: "全部", type: "", xfId: "" });
        jyeooCondition.push({
          title: "难度",
          activeName: "全部",
          width: 370,
          activeTitle: "",
          key: "difficultyCode",
          typeList: diffs,
        });
      }
      // 题类
      if (data.item_tags.length) {
        let tags = data.item_tags;
        tags.forEach((it) => {
          it.xfId = it.value;
        });
        tags.unshift({ id: "", name: "全部", type: "", xfId: "" });
        jyeooCondition.push({
          title: "题类",
          activeName: "全部",
          width: 370,
          activeTitle: "",
          key: "examCode",
          typeList: tags,
        });
      }
      // 地区
      if (data.provinces.length) {
        let provinces = data.provinces;
        provinces.forEach((it) => {
          it.xfId = it.value;
        });
        provinces.unshift({ id: "", name: "全部", type: "", xfId: "" });
        jyeooCondition.push({
          title: "地区",
          activeName: "全部",
          width: 370,
          activeTitle: "",
          key: "provinceCode",
          typeList: provinces,
        });
      }
      // 来源
      if (data.sources.length) {
        let sources = data.sources;
        sources.forEach((it) => {
          it.xfId = it.value;
        });
        sources.unshift({ id: "", name: "全部", type: "", xfId: "" });
        jyeooCondition.push({
          title: "来源",
          activeName: "全部",
          width: 370,
          activeTitle: "",
          key: "sourceCode",
          typeList: sources,
        });
      }
      // 年份
      if (data.years.length) {
        let years = [];
        years = data.years.map((it) => {
          return {
            id: it,
            xfId: it,
            name: it,
          };
        });
        years.unshift({ id: "", name: "全部", type: "", xfId: "" });
        jyeooCondition.push({
          title: "年份",
          activeName: "全部",
          width: 370,
          activeTitle: "",
          key: "yearCode",
          typeList: years,
        });
      }
      // 排序
      if (data.sorts.length) {
        let sorts = data.sorts;
        sorts.forEach((it) => {
          it.xfId = it.value;
        });
        // sorts.unshift({ id: '', name: "全部", type: '', xfId: '' })
        jyeooCondition.push({
          title: "排序",
          activeName: sorts[0].name || "",
          width: 390,
          activeTitle: sorts[0].name || "",
          key: "sortCode",
          typeList: sorts,
        });
      }
      this.jyeooCondition = jyeooCondition;
      this.filterList = this.jyeooCondition;
    },
    // 还原选项
    resetNetFilter() {
      this.filterList.forEach((it) => {
        if (it.key == "sortCode") {
          it.activeName = it.typeList[0].name || "全部";
          it.active = it.typeList[0] || null;
          it.activeTitle = it.typeList[0].name || "";
        } else {
          it.activeName = "全部";
          it.active = null;
          it.activeTitle = "";
        }
      });
      this.content = "";
    },
    // 获取讯飞题库过滤条件
    getXfFilterCondition() {
      getFilterCondition({
        subjectId: this.workbookInfo.subject_id,
        phaseId: this.workbookInfo.phase_id,
      })
        .then((data) => {
          // 处理xf题库条件
          this.handleXfCondition(data);
        })
        .catch((err) => {});
    },
    // 处理xf题库条件
    handleXfCondition(data) {
      this.filterList = [];
      let xfCondition = [];
      // 难度
      if (data.difficulty.legnth) {
        let difficultyList = data.difficulty.sort(function (a, b) {
          if (a.id > b.id) {
            return -1;
          }
        });
        difficultyList.unshift({
          id: "",
          name: "全部",
          type: 2,
          xfId: "",
        });
        xfCondition.push({
          title: "难度",
          activeName: "全部",
          width: 370,
          activeTitle: "",
          key: "difficultyCode",
          typeList: difficultyList,
        });
      }
      // 题型
      if (data.xfSections.length) {
        let xfSections = data.xfSections;
        xfSections.forEach((it) => {
          it.xfId = it.categoryCode;
        });
        xfSections.unshift({
          id: "",
          name: "全部",
          type: "",
          xfId: "",
        });
        xfCondition.push({
          title: "题型",
          activeName: "全部",
          width: 420,
          activeTitle: "",
          key: "xfSectionCode",
          typeList: xfSections,
        });
      }
      // 题类
      if (data.type.length) {
        let typeList = data.type;
        typeList.unshift({
          id: "",
          name: "全部",
          type: 1,
          xfId: "",
        });
        xfCondition.push({
          title: "题类",
          activeName: "全部",
          width: 500,
          activeTitle: "",
          key: "examCode",
          typeList: typeList,
        });
      }
      // 年份
      if (data.year.length) {
        let yearList = data.year;
        yearList.unshift({
          id: "",
          name: "全部",
          type: 3,
          xfId: "",
        });
        xfCondition.push({
          title: "年份",
          activeName: "全部",
          width: 340,
          activeTitle: "",
          key: "yearCode",
          typeList: yearList,
        });
      }
      this.xfCondition = xfCondition;
    },
    clearCurrentSelect(item) {
      item.activeName = "全部";
      item.activeTitle = "";
      item.active = null;
      this.search();
    },
    // 点击筛选项
    clickFliterItem(item, parent) {
      if (parent.active === item) return;
      parent.activeName = item.name;
      parent.active = item;
      if (item.xfId) {
        parent.activeTitle = item.name;
      } else if (!item.xfId && parent.title != "排序") {
        parent.activeTitle = "";
      } else if (parent.title == "排序") {
        parent.activeTitle = item.name;
      }
      this.search();
    },
    search() {
      let params = {};
      this.filterList.forEach((it) => {
        if (it.active) {
          params[it.key] = it.active.xfId;
        }
      });
      params.content = this.content;
      if (this.currentQuesBank == "xf") {
        this.$bus.$emit("changeNetFilter", params);
      } else if (this.currentQuesBank == "yiqi") {
        this.$bus.$emit("changeNetYiqFilter", params);
      } else if (this.currentQuesBank == "jyeoo") {
        this.$bus.$emit("changeNetJyeooFilter", params);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.ques-filter-wrapper {
  background: white;
}
.stuck-filter-box {
  margin-top: 10px;
  padding-bottom: 8px;
}
.ques-filter-condition {
  margin-left: 11px;
  //margin-top: 15px;
  padding-right: 43px;
  //padding-bottom: 8px;
  position: relative;

  .filter-type {
    display: flex;
    .typeName,
    .typeOpts {
      display: inline-block;
      text-align: center;
    }

    .typeName {
      font-size: 16px;
      color: #646464;
      font-weight: bold;
      margin-top: 4px;
    }

    .typeOpts {
      font-size: 16px;
      color: #5a6474;
      cursor: pointer;
      padding: 4px 12px;
      margin-bottom: 5px;
      border-radius: 3px;

      &.active {
        background-color: #3f86f9;
        color: #fff;
      }
    }
  }

  .filter-type {
    .filter-type-item-box {
      display: flex;
      flex-wrap: wrap;

      .filter-type-item {
        margin: 0 10px;
        // &:hover {
        //   .dropdown-link .el-icon-arrow-down {
        //     transition: all .3s;
        //     transform: rotateZ(180deg);
        //   }
        // }
        .dropdown-link .text-box {
          // width: 6em;
          display: flex;
          align-items: center;
        }

        .dropdown-link .text {
          min-width: 2em;
          max-width: 4em;
          display: inline-block;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .dropdown-link .el-icon-circle-close {
          margin-left: 6px;
        }
      }
    }
  }

  .shrink-text {
    font-size: 15px;
    position: absolute;
    bottom: 10px;
    right: 30px;
    cursor: pointer;

    .shrink-block {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .text {
      color: #008dea;
    }

    .icon-down {
      width: 14px;
      height: 14px;
      margin-left: 6px;
      background: url("../../assets/icon/icon-down.png") no-repeat;
    }

    .icon-up {
      width: 14px;
      height: 14px;
      margin-left: 6px;
      background: url("../../assets/icon/icon-up.png") no-repeat;
    }
  }
}
.ques-nums {
  position: absolute;
  right: 20px;
  font-size: 17px;
}
</style>

<style lang="scss">
.dropdown-link {
  font-size: 16px;
  cursor: pointer;
  color: #5a6474;
}

.el-icon-arrow-down {
  font-size: 12px;
}

.el-popover.el-popper {
  .typeOpts {
    display: inline-block;
    text-align: center;
    white-space: nowrap;
    font-size: 16px;
    color: #5a6474;
    cursor: pointer;
    padding: 4px 12px;
    border-radius: 3px;

    &.active {
      background-color: #3f86f9;
      color: #fff;
    }
  }

  .area-box .area-row {
    display: flex;

    .area-label {
      width: 40px;
      text-align: center;
      line-height: 32px;
      white-space: nowrap;
    }
  }
}
</style>

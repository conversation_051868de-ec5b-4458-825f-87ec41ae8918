<template>
  <el-card shadow="never" :class="['ques-item']">
    <el-container>
      <ques-surface
        ref="quesSurfaceRef"
        :id="id"
        :stem="stem"
        :triggerDetail="isShowDetail || !isShowDetailBtn"
        @render-finish="stemChange"
      >
      </ques-surface>
      <!--底部按钮-->
      <el-footer height="40px" :class="['item-footer']">
        <el-row class="footer-left ellipsis" type="flex" :title="quesInfoTitle()">
          <!--同步,复习,校本-->
          <template>
            <div style="margin-right: 20px">
              题型：&nbsp;&nbsp;{{ getQuesType(surface.type, surface.subtype) }}
            </div>
            <div>难度：</div>
            <el-rate :value="getDiffStart(surface.difficulty * 1)" disabled></el-rate>
            <span v-if="surface.useCount">已用：&nbsp;&nbsp; {{ surface.useCount }}次</span>
          </template>
        </el-row>
        <el-row class="footer-right" type="flex">
          <!--纠错-->
          <el-button
            v-if="isShowCheckError && isShowBtn"
            type="text"
            class="foot-btn err-back"
            @click="emitEvent('checkError')"
          >
            {{ 1 }}
          </el-button>
          <!--详情-->
          <template v-if="isShowDetailBtn">
            <template>
              <el-button
                type="text"
                class="foot-btn detail-btn detail-btn-close"
                @click="closeQuesDetail"
                v-if="isShowDetail"
              >
                收起
              </el-button>
              <el-button
                type="text"
                class="foot-btn detail-btn detail-btn-open"
                @click="showQuesDetail"
                v-else
              >
                详情
              </el-button>
              <div class="edit-btn">
                <span
                  class="add text-center"
                  @click="selectQues"
                  :class="[
                    selectIds.length && selectIds.indexOf(id) >= 0
                      ? 'hasAdd el-icon-minus'
                      : 'el-icon-plus',
                  ]"
                >
                  {{ selectIds.length && selectIds.indexOf(id) >= 0 ? " 移除" : " 选入" }}
                </span>
              </div>
            </template>
          </template>
        </el-row>
      </el-footer>
    </el-container>
  </el-card>
</template>

<script>
import { getDiffStartStar } from "@/utils/common";
import QuesSurface from "@/components/ClassTest/surface.vue";
import { isNullOrUndefined } from "@/utils";
import { getPlatFromInfoBySchoolIdAPI, recordUseQue } from "@/service/testbank";
export default {
  components: {
    QuesSurface,
  },
  props: {
    stem: {
      type: String,
      defalut: "",
    },
    id: {
      type: String,
      defalut: "",
    },
    isShowBtn: {
      type: Boolean,
      defalut: false,
    },
    isShowComment: {
      type: Boolean,
      defalut: false,
    },
    isShowFooter: {
      type: Boolean,
      defalut: true,
    },
    //题型列表
    quesTypeList: {
      type: Array,
      defalut: [],
    },
    selectIds: {
      type: Array,
      defalut: [],
    },
  },
  data() {
    return {
      // 是否展示17,校本,课课练题库题目详情
      isShowDetail: false,
      // 分数修改框参数
      scoreSetOptions: {
        isShow: false,
        score: 0,
      },
      // 题目数据
      surface: {},
      isShowDetailBtn: true,
      isShowCheckError: false,
      topicName: false,
    };
  },
  watch: {
    stem(newVal, oldVal) {
      this.$nextTick(() => {
        this.surface = newVal == "" ? "" : JSON.parse(newVal);
      });
    },
    id(newVal, oldVal) {
      this.isShowDetail = false;
    },
  },
  created() {},
  mounted() {
    this.surface = this.stem == "" ? "" : JSON.parse(this.stem);
  },
  methods: {
    stemChange(newValue) {
      this.surface = newValue == "" ? "" : JSON.parse(newValue);
    },
    /**
     * @name: 获取题目信息,用于底部显示title文本
     * @description: 避免小屏环境下样式污染,使用title显示题目信息
     * @return: title文本
     */
    quesInfoTitle() {
      // 同步,复习,校本
      let title = `题型：${this.getQuesType(this.surface.type, this.surface.subtype)} `;
      title += ` 难度：${this.getDiffStart(this.surface.difficulty)} `;
      if (this.surface.useCount) {
        title += `已用: ${this.surface.useCount ? this.surface.useCount : "-"}`;
      }
      return title;
    },
    /**
     * @name: 获取题型名称
     * @param type 题型code
     * @param subtype 选择题类型code
     * @return: 题型
     */
    getQuesType(type, subtype) {
      let quesObj = this.getQuesTypeUtils(type, subtype);
      return isNullOrUndefined(quesObj) ? "略" : quesObj.queTypeName;
    },
    getQuesTypeUtils(typeid, subtype) {
      let quesType = this.quesTypeList.getByKey("queTypeId", typeid);
      let subQuesType = null;
      let quesTypeObj = null;
      if (quesType && quesType.child) {
        subQuesType = quesType.child.getByKey("value", subtype);
        if (subQuesType) {
          quesTypeObj = {
            queTypeId: subQuesType.id,
            queTypeName: subQuesType.name,
          };
        }
      }
      return quesTypeObj || quesType;
    },
    /**
     * @name: 判断是否选择题
     * @param typeid 题型code
     * @return: 是否选择题
     */
    isChooseType(typeid) {
      if (this.quesTypeList.length == 0) {
        return false;
      }
      let info =
        this.quesTypeList.getByKey("queTypeId", typeid) ||
        this.quesTypeList.getByKey("queTypeId", "-1");
      if (isNullOrUndefined(info)) {
        return false;
      }
      return info.dtTypeId == 1 || (info && info.dtTypeId == 8);
    },
    /**
     * @name: 获取难度星星等级
     * @param difficuity 难度
     * @param isJY 是否优选题库,默认false
     * @return: 难度等级
     */
    getDiffStart(difficuity, isJY = false) {
      return getDiffStartStar(difficuity, isJY);
    },
    /**
     * @name: 关闭一起,校本题库题目详情
     * */
    closeQuesDetail() {
      this.isShowDetail = false;
      this.surface = this.stem === "" ? "" : JSON.parse(this.stem);
    },
    /**
     * @name: 获取17,校本题目详情
     * */
    showQuesDetail() {
      this.isShowDetail = true;
      this.addUseLog();
    },
    /**
     * @name: 记录题目浏览量日志
     */
    async addUseLog() {
      let areaId = "";
      let loginInfo = this.$sessionSave.get("loginInfo");
      let schoolInfo = this.$sessionSave.get("schoolInfo");
      if (areaId == "") {
        areaId = await this.getRegionInfo();
      }
      const res = await recordUseQue({
        schName: schoolInfo.school_name,
        userId: loginInfo.id,
        userName: loginInfo.realname,
        areaId: areaId,
        schId: schoolInfo.id,
        queId: this.id,
        subId: loginInfo.subjectid,
        behavior: 1,
        schoolId: schoolInfo.id || loginInfo.schoolid,
      });
    },
    /**
     * @name 获取区域id
     * @param param0
     */
    async getRegionInfo() {
      let areaId = "";
      let res = await getPlatFromInfoBySchoolIdAPI({
        schoolId: this.$sessionSave.get("schoolInfo").id,
        platformState: 1,
      });
      if (res.data.length > 0) {
        areaId = res.data[0].platformId;
      }
      return areaId;
    },
    /**
     * @name:题目加入、移除试卷袋
     * @param {*} item
     */
    selectQues() {
      this.$emit("select-ques", JSON.parse(this.stem));
    },
  },
};
</script>


<style lang="scss" scoped>
.ques-item {
  margin-bottom: 15px;
  padding: 0;
  border-color: rgba(204, 208, 220, 1);

  &:hover {
    .isPreview {
      background-color: #f0f5f9;
      .foot-btn {
        display: block;
      }
    }
  }

  &.choosed {
    background-color: #fafcff;
  }

  &.bank {
    width: 83%;
    margin: 0 auto;
    margin-bottom: 10px;
  }

  .sq-score-text {
    color: #f56c6c;
    text-decoration: underline;
  }
}
.edit-btn {
  float: right;
  display: flex;
  align-items: center;
  height: 100%;
  span {
    width: 80px;
    height: 32px;
    border-radius: 4px;
    padding: 0;
    line-height: 32px;
    color: #fff;
    margin-top: 0px !important;
    margin-left: 20px;
    cursor: pointer;
    &.add {
      background: #409effff;
    }
    &.hasAdd {
      background-color: #fff;
      border: 1px solid #468fffff;
      color: #468fffff;
    }
  }
}
::v-deep .el-card__body {
  padding-bottom: 0;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f6f8fc;
  margin: 0 -20px;
  padding: 0 20px;

  &.isPreview {
    height: 45px !important;
    justify-content: flex-end;
    display: flex;
  }

  .foot-btn {
    cursor: pointer;
    margin: 0 15px;
    color: #323232;
    font-size: 16px;

    &:hover {
      color: #3e73f6;
    }

    &.detail-btn {
      padding-left: 20px;
      background: url("../../assets/bank/icon_detail.png") no-repeat left center/18px 16px;

      &:hover {
        background: url("../../assets/bank/icon_detail_hover.png") no-repeat left center/18px 16px;
      }

      &.detail-btn-close {
        background: url("../../assets/bank/ques_detail_close_hover.png") no-repeat left center/12px
          11px;
      }

      &.detail-btn-open {
        background: url("../../assets/bank/ques_detail_close.png") no-repeat left center/12px 11px;

        &:hover {
          background: url("../../assets/bank/ques_detail_open.png") no-repeat left center/12px 11px;
        }
      }
    }

    &.err-back {
      padding-left: 20px;
      background: url("../../assets/bank/icon_rubber.png") no-repeat left center/15px 16px;
    }

    &.similar-btn {
      padding-left: 20px;
      background: url("../../assets/bank/search_btn.png") no-repeat left center/15px 15px;
    }

    &.change-btn {
      padding-left: 20px;
      background: url("../../assets/bank/pre_icon_change_normal.png") no-repeat left center/15px
        18px;

      &:hover {
        background: url("../../assets/bank/pre_icon_change_hover.png") no-repeat left center/15px
          18px;
      }
    }

    &.delete-btn {
      padding-left: 20px;
      background: url("../../assets/bank/delete__icon.png") no-repeat left center/16px;

      &:hover {
        background: url("../../assets/bank/icon_delete_hover.png") no-repeat left center/16px;
      }
    }

    &.add-btn {
      color: #fff;
    }

    &.add-txt-btn,
    &.remove-btn {
      border-color: #f6f8fc;
      color: #f6f8fc;
    }

    &.topic-name {
      height: 44px;
      padding-left: 5px;
      padding-right: 5px;
      margin: 0;
      margin-right: 20px;
      background-color: #5f9eff;
      color: #fff;
      cursor: default;

      &.left {
        position: absolute;
        left: 0;
        bottom: 0;
      }

      &:hover {
        color: #3e73f6;
      }
    }
  }

  .footer-left {
    font-size: 16px;
    align-items: center;

    > span {
      &:not(:first-child) {
        margin: 0 10px;
      }
    }
  }

  .footer-right {
    align-items: center;
  }
}
</style>

<!--
 * @Description: 上线设置（考试用，无校级）
 * @Author: 小圆
 * @Date: 2025-04-16 14:47:08
 * @LastEditors: 小圆
-->

<template>
  <div>
    <div class="setting-header">
      <div class="title">上线设置</div>
      <div class="header-right" v-if="!examId">
        <el-button type="primary" size="small" :loading="resetLoading" @click="reset">恢复默认</el-button>
        <el-button type="primary" size="small" :loading="saveLoading" @click="save">保存</el-button>
      </div>
    </div>

    <el-form
      class="setting-form"
      inline
      label-position="left"
      label-suffix="："
      :model="currentConfig"
      hide-required-asterisk
    >
      <div>
        <el-form-item label="挡位数">
          <el-select v-model="currentConfig.size" size="small" @change="onSizeChange">
            <el-option v-for="item in 4" :label="`${item}挡`" :value="item"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="统计方式" style="margin-left: 20px">
          <el-radio-group v-model="checkedType" @change="onCheckedTypeChange">
            <el-radio :label="1">按成绩统计</el-radio>
            <el-radio :label="2">按名次统计</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>

      <div>
        <el-form-item>
          <el-radio-group v-model="settingProp" @change="onSettingPropChange">
            <el-radio-button :label="'data'">上线设置</el-radio-button>
            <el-radio-button :label="'goal'">目标设置</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </div>

      <div>
        <el-table
          class="setting-table"
          :data="currentConfig[settingProp]"
          style="width: 100%"
          max-height="600"
          border
          v-drag-table
          :span-method="
            ({ row, column, rowIndex, columnIndex }) =>
              column.property === 'operation' ? [currentConfig[settingProp].length, 1] : [1, 1]
          "
        >
          <el-table-column
            prop="name"
            :label="`${settingProp == 'data' ? '学科' : '班级'}`"
            width="120"
            align="center"
            fixed="left"
            :resizable="false"
          >
            <template #default="scope">
              <span>
                {{ scope.row.name }}
              </span>
              <span style="display: inline-block">
                {{ `(${scope.row.total || 0}${settingProp == 'goal' || checkedType == 2 ? '人' : '分'})` }}
              </span>
              <p>
                {{ scope.row.isRule ? '(赋分)' : '(得分)' }}
              </p>
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in currentConfig.lines"
            :key="index"
            align="center"
            min-width="225"
            :resizable="false"
          >
            <template #header="scope">
              <el-form-item
                label=""
                :prop="`lines[${index}].name`"
                :rules="[{ required: true, message: '必填项', trigger: 'blur' }]"
              >
                <el-input v-model.trim="currentConfig.lines[index].name" size="mini" style="width: 100px"> </el-input>
              </el-form-item>
            </template>
            <template #default="scope">
              <el-form-item
                label=""
                :prop="`${settingProp}[${scope.$index}].data[${index}]`"
                :rules="[{ required: true, message: '必填项', trigger: 'blur' }]"
              >
                <el-input-number
                  v-model="scope.row.data[index]"
                  size="small"
                  :min="0"
                  :max="scope.row.total"
                  :controls="false"
                  :disabled="!getAllPropEnable(scope.row.id)"
                  @change="limitRate(scope.$index, index)"
                ></el-input-number>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column label="操作" prop="operation" width="120" align="center" fixed="right" :resizable="false">
            <template #default="scope">
              <el-button type="text" size="mini" @click="onEdit">
                <i class="el-icon-edit-outline" style="font-size: 20px"> </i>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="setting-header" style="margin-top: 20px">
        <div class="title">临界生设置</div>
      </div>

      <div class="setting-content">
        <el-form-item
          label="上浮值"
          :prop="`critical.up`"
          :rules="[{ required: true, message: '必填项', trigger: 'blur' }]"
        >
          <el-input-number
            class="input"
            v-model="currentConfig.critical.up"
            size="small"
            :step-strictly="checkedType == 2 ? true : false"
            :min="0"
            :controls="false"
          ></el-input-number>
          <span>{{ checkedType == 1 ? '分' : '名' }}</span>
        </el-form-item>
        <el-form-item
          label="下浮值"
          :prop="`critical.down`"
          :rules="[{ required: true, message: '必填项', trigger: 'blur' }]"
        >
          <el-input-number
            class="input"
            v-model="currentConfig.critical.down"
            size="small"
            :step-strictly="checkedType == 2 ? true : false"
            :min="0"
            :controls="false"
          ></el-input-number>
          <span>{{ checkedType == 1 ? '分' : '名' }}</span>
        </el-form-item>
      </div>
    </el-form>

    <SettingSaveDialog
      v-if="isShowSaveDialog"
      :currentPhase="currentPhase"
      :currentGradeId="currentGradeId"
      :currentGradeItem="currentGradeItem"
      :currentGradeList="currentGradeList"
      :examId="examId"
      @save="handleSave"
      @closed="isShowSaveDialog = false"
    ></SettingSaveDialog>

    <el-dialog title="上线分析" :visible.sync="isShowSettingDialog" width="600px">
      <el-form ref="settingDialogFormRef" :model="settingDialogForm" inline label-position="left" label-suffix="：">
        <el-table class="setting-table" :data="settingDialogForm.lines" style="width: 100%">
          <el-table-column label="" width="50">
            <template #default="scope">
              <div class="line-color" :class="`line-color-${scope.$index}`"></div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="挡位名称">
            <template #default="scope">
              <el-form-item
                :prop="`lines[${scope.$index}].name`"
                :rules="[{ required: true, message: '必填项', trigger: 'blur' }]"
              >
                <el-input v-model="scope.row.name" size="small" style="width: 150px"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="rate" label="上线比例">
            <template #default="scope">
              <el-form-item
                :prop="`lines[${scope.$index}].rate`"
                :rules="[{ required: true, message: '必填项', trigger: 'blur' }]"
              >
                <el-input-number
                  v-model="scope.row.rate"
                  size="small"
                  style="width: 150px"
                  :controls="false"
                  :min="0"
                  :max="100"
                ></el-input-number>
                <span> % </span>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowSettingDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmSetOnline">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { Component, Mixins, Ref, Vue } from 'vue-property-decorator';
import SettingSubjectHeader from '../SettingSubjectHeader.vue';
import SchoolSettingMixin from '../SchoolSetting.mixin';
import { getExamSubject, getSchCfgAPI, restoreSchCfgAPI, setSchCfgAPI } from '@/service/pexam';
import { SchoolSettingType } from '../../types';
import SettingSaveDialog from '../SettingSaveDialog.vue';
import { ElForm } from '@iclass/element-ui/types/form';

function limitDecimalPlaces(num, decimalPlaces) {
  const factor = Math.pow(10, decimalPlaces);
  return Math.round(num * factor) / factor;
}

interface JCfg {
  size: number;
  type: number;
  checked: number;
  lines: Line[];
  data: Datum[];
  critical: Critical;
  goal: Datum[];
}

interface Critical {
  up: number;
  down: number;
}

interface Datum {
  id: string;
  name: string;
  data: number[];
  /** 分数或人数 */
  total: number;
  checked?: number;
}

interface Line {
  name: string;
  rate: number;
}

@Component({
  components: {
    SettingSubjectHeader,
    SettingSaveDialog,
  },
})
export default class ScoreLineSetting extends Mixins(SchoolSettingMixin) {
  @Ref() settingSubjectHeader!: SettingSubjectHeader;
  @Ref() settingDialogFormRef!: ElForm;

  // 当前设置属性
  settingProp: 'data' | 'goal' = 'data';
  // 1：按成绩统计、2：按名次统计
  checkedType: 1 | 2 = 1;

  // 配置项
  private jCfg: JCfg[] = [];
  // 备份配置项
  private backupJCfg: JCfg[] = [];

  // 当前配置项
  private currentConfig: JCfg = {
    critical: { down: 10, up: 10 },
    checked: 1,
    size: 4,
    type: 1,
    lines: [
      {
        name: '分数线1',
        rate: 90,
      },
      {
        name: '分数线1',
        rate: 80,
      },
      {
        name: '分数线1',
        rate: 70,
      },
      {
        name: '分数线1',
        rate: 60,
      },
    ],
    data: [],
    goal: [],
  };

  // 恢复loading
  private resetLoading = false;
  // 保存loading
  private saveLoading = false;
  // 保存对话框
  private isShowSaveDialog = false;
  // 上线分析设置对话框
  private isShowSettingDialog = false;
  // 上线分析设置
  private settingDialogForm = {
    lines: [] as Line[],
  };
  // 成绩全部发布
  private allScorePublished = false;

  /**
   * 5、按成绩统计时全部学科需等所有学科成绩发布后支持手动修改，否则取默认值（灰显不许修改）——解决有学科未发布时提前进行了指标设置，再发布成绩时总分未更新问题
   */
  getAllPropEnable(prop) {
    if (this.settingProp == 'goal') {
      return true;
    }
    if (this.checkedType == 2) {
      return true;
    }
    if (prop) return true;
    return this.allScorePublished;
  }

  mounted() {
    if (this.examId) {
      this.getConfig();
    }
    this.getExamSubjectList();
  }

  // 获取考试学科列表
  async getExamSubjectList() {
    const res = await getExamSubject({
      examId: this.examId,
    });
    this.allScorePublished = res.data.every(item => item.importScoreState == 1);
  }

  // 上线设置类型变化
  onCheckedTypeChange(type) {
    this.checkedType = type;
    this.jCfg.forEach(item => {
      if (item.type == type) {
        item.checked = 1;
        this.currentConfig = item;
      } else {
        item.checked = 0;
      }
    });
  }

  // 设置类型变化
  onSettingPropChange(prop) {
    this.settingProp = prop;
  }

  // 挡位数变化
  onSizeChange(size: number) {
    this.currentConfig.size = size;
    let lines = this.currentConfig.lines;
    if (size < lines.length) {
      lines = lines.slice(0, size);
    } else {
      lines = lines.concat(
        Array.from({ length: size - lines.length }, (_, i) => ({
          name: (this.checkedType == 1 ? '分数线' : '名次') + (i + lines.length + 1),
          rate: [90, 80, 70, 60][i + lines.length],
        }))
      );
    }
    this.currentConfig.lines = lines;
  }

  // 编辑
  onEdit() {
    this.settingDialogForm.lines = this.$deepClone(this.currentConfig.lines);
    this.isShowSettingDialog = true;
  }

  // 确认上线设置
  confirmSetOnline() {
    this.settingDialogFormRef.validate(valid => {
      if (valid) {
        this.currentConfig.lines = this.settingDialogForm.lines;
        this.currentConfig.lines.forEach((item, index) => {
          this.currentConfig.data.forEach(dataItem => {
            if (this.checkedType == 1) {
              this.$set(dataItem.data, index, Number((((dataItem.total || 0) * item.rate) / 100).toFixed(2)));
            } else {
              this.$set(dataItem.data, index, Math.ceil(((dataItem.total || 0) * item.rate) / 100));
            }
          });
          this.currentConfig.goal.forEach(dataItem => {
            this.$set(dataItem.data, index, Math.ceil(((dataItem.total || 0) * item.rate) / 100));
          });
        });
        this.isShowSettingDialog = false;
      }
    });
  }

  // 获取配置
  async getConfig() {
    const res = await getSchCfgAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      type: SchoolSettingType.ScoreLineSetting,
      phase: this.currentPhase,
      gradeId: this.currentGradeId,
      examId: this.examId,
    });

    if (res.data && res.data.jCfg) {
      this.jCfg = res.data.jCfg;
      this.currentConfig = this.jCfg.find(item => item.checked);
      this.checkedType = this.currentConfig.type as 1 | 2;
      this.backupJCfg = JSON.parse(JSON.stringify(this.jCfg));
    }
  }

  // 恢复默认
  async reset() {
    this.resetLoading = true;
    try {
      await this.restoreSchCfg();
      this.$notify.success({
        title: '成功',
        message: '恢复成功',
      });
      this.getConfig();
    } catch (error) {
      this.$notify.error({
        title: '失败',
        message: '恢复失败',
      });
    } finally {
      this.resetLoading = false;
    }
  }

  // 保存
  async save() {
    await this.checkSizeColumn();
    await this.checkConfig();

    if (this.currentGradeId == '0') {
      this.isShowSaveDialog = true;
    } else {
      this.handleSave([this.currentGradeId]);
    }
  }

  // 保存
  async handleSave(gradeIds: string[]) {
    this.saveLoading = true;
    this.isShowSaveDialog = false;
    try {
      await this.setSchCfg(gradeIds);
      this.$notify.success({
        title: '成功',
        message: '保存成功',
      });
    } catch (error) {
      this.$notify.error({
        title: '失败',
        message: '保存失败',
      });
    } finally {
      this.saveLoading = false;
    }
  }

  // 恢复学校配置
  public restoreSchCfg() {
    return restoreSchCfgAPI({
      phase: this.currentPhase,
      schId: this.$sessionSave.get('schoolInfo').id,
      type: SchoolSettingType.ScoreLineSetting,
      gradeId: this.currentGradeId,
      examId: this.examId,
    });
  }

  // 设置学校配置
  public async setSchCfg(gradeIds: string[]) {
    const config = this.getCfg().jCfg;

    const promises = [];
    for (const gradeId of gradeIds) {
      promises.push(
        setSchCfgAPI({
          schId: this.$sessionSave.get('schoolInfo').id,
          schName: this.$sessionSave.get('schoolInfo').schoolName,
          type: SchoolSettingType.ScoreLineSetting,
          phase: this.currentPhase,
          jCfg: config,
          gradeId: gradeId,
          examId: this.examId,
        })
      );
    }
    return Promise.all(promises);
  }

  // 获取配置信息
  public getCfg() {
    const config = this.backupJCfg;
    config.forEach(item => {
      item.checked = 0;
    });
    config[config.findIndex(item => item.type == this.checkedType)] = this.$deepClone(this.currentConfig);
    return {
      type: SchoolSettingType.ScoreLineSetting,
      jCfg: config,
    };
  }

  // 检查分数线列
  checkSizeColumn() {
    const uniqueLines = [...new Set(this.currentConfig.lines.map(item => item.name))];
    if (uniqueLines.length !== this.currentConfig.lines.length) {
      this.$notify.error({
        title: '【上线设置】',
        message: '上线设置挡位名称不能相同',
      });
      return Promise.reject(false);
    }

    if (this.currentConfig.lines.some(item => item.name === '' || item.name === null || item.name === undefined)) {
      this.$notify.error({
        title: '【上线设置】',
        message: '上线设置挡位名称不能为空',
      });
      return Promise.reject(false);
    }

    return Promise.resolve(true);
  }

  // 检查配置
  checkConfig() {
    for (const item of this.currentConfig.data) {
      for (let index = 0; index < this.currentConfig.lines.length; index++) {
        if (item.data[index] === null || item.data[index] === undefined) {
          this.$notify.error({
            title: '失败',
            message: '上线设置值不能为空',
          });
          return Promise.reject(false);
        }
      }
    }

    for (const item of this.currentConfig.goal) {
      for (let index = 0; index < this.currentConfig.lines.length; index++) {
        if (item.data[index] === null || item.data[index] === undefined) {
          this.$notify.error({
            title: '【上线设置】',
            message: '目标设置值不能为空',
          });
          return Promise.reject(false);
        }
      }
    }

    if (this.currentConfig.critical.up === null || this.currentConfig.critical.up === undefined) {
      this.$notify.error({
        title: '【上线设置】',
        message: '上浮值不能为空',
      });
      return Promise.reject(false);
    }

    if (this.currentConfig.critical.down === null || this.currentConfig.critical.down === undefined) {
      this.$notify.error({
        title: '【上线设置】',
        message: '下浮值不能为空',
      });
      return Promise.reject(false);
    }

    return Promise.resolve(true);
  }

  // 检查上线比例
  limitRate(index: number, lineIndex: number) {
    const rateData = limitDecimalPlaces(this.currentConfig[this.settingProp][index].data[lineIndex], 1);
    this.$nextTick(() => {
      this.$set(this.currentConfig[this.settingProp][index].data, lineIndex, rateData);
    });
  }
}
</script>

<style lang="scss" scoped>
@import '../page-style.scss';

.setting-content {
  display: flex;
  gap: 10px;

  .input {
    width: 80px;
    margin-right: 10px;
  }
}

.line-color {
  display: inline-block;
  width: 25px;
  height: 25px;
  border-radius: 6px;
  background-color: red;
}

.line-color-0 {
  background-color: #ff0000;
}

.line-color-1 {
  background-color: #ffc000;
}

.line-color-2 {
  background-color: #92d050;
}

.line-color-3 {
  background-color: #26b3f4;
}
</style>

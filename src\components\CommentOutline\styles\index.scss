.comment-outline {
    .com-header {
        position: sticky;
        top: 0;
        line-height: 40px;
        padding: 0 15px;
        font-size: 16px;
        background-color: #fff;

        >.title {
            position: relative;
            color: #3f4a54;
            padding-left: 10px;

            &::before {
                content: "";
                position: absolute;
                display: inline-block;
                left: 0;
                width: 4px;
                height: 20px;
                background: #409eff;
                border-radius: 2px;
                top: 10px;
            }
        }
    }

    .com-content {
        padding: 15px;
    }

    .color-block {
        min-width: var(--block-size);
        height: var(--block-size);
        line-height: var(--block-size);
        padding: 0 4px;
        border-radius: 4px;
        text-align: center;
        font-size: var(--block-fontsize);
        color: rgb(255, 255, 255);

        &.level-0 {
            background-color: rgb(247, 137, 137);
            border: 1px solid rgb(245, 108, 108);
        }

        &.level-1 {
            background-color: rgb(255, 187, 25);
            border: 1px solid rgb(232, 164, 0);
        }

        &.level-2 {
            background-color: rgb(95, 158, 255);
            border: 1px solid rgb(70, 143, 255);
        }

        &.level-3 {
            background-color: rgb(57, 206, 177);
            border: 1px solid rgb(7, 194, 157);
        }
    }
}

.com-abpaper-list {
    display: inline-flex;
    align-items: center;

    margin-bottom: 10px;
    padding: 2px;
    
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    .abpaper-item {
        cursor: pointer;
        border-radius: 6px;
        padding: 2px 16px;

        &.active {
            color: #fff;
            background: #f5a033;
            font-weight: 700;
        }
    }
}

.com-score-list {
    .score-item {
        width: 50%;
        margin-bottom: 20px;
        white-space: nowrap;
        font-size: var(--block-fontsize);
        color: rgb(63, 74, 84);
    }

    .color-block {
        margin-right: 5px;
    }

    .score-string {
        font-size: 15px;
    }
}

.ques-title {
    font-size: 16px;
    color: rgb(78, 86, 104);
    margin: 10px 0px;
}

.com-ques-list {
    border-top: 1px dashed #e4e7ed;
}

.com-number-list {
    >li {
        float: left;

        &.color-block {
            margin-right: var(--block-margin);
            margin-bottom: var(--block-margin);
        }
    }
}
<!--
 * @Description: 
 * @Author: qmzhang
 * @Date: 2024-04-09 08:44:13
 * @LastEditTime: 2025-07-25 11:10:22
 * @FilePath: \personal-bigdata\src\pages\comment\index.vue
-->
<template>
  <div class="commentContainer">
    <div class="breadcrumb-header">
      <div class="pull-left left-text">讲评</div>
      <div class="right-text">
        <!--搜索-->
        <div class="header__select">
          <div class="header__serarch">
            <el-input class="search__text" placeholder="输入考试名称搜索" v-model="searchValue" @clear="searchReport"
              @keyup.enter.native="searchReport" clearable>
            </el-input>
            <div class="search__icon el-icon-search display_flex align-items_center justify-content_flex-center"
              @click="searchReport"></div>
          </div>
        </div>

        <el-button class="refresh-btn header__select el-icon-refresh" @click="getCommentExamList('')" round>刷新</el-button>
      </div>
    </div>

    <div class="comment-body">
      <div class="comment-main flex_1 display_flex flex-direction_column">
        <div id="list-container" class="examReport__content"
          :class="examReportList.length ? '' : 'display_flex align-items_center'">
          <!--没有个册数据缺省图-->
          <div v-if="!listLoading && !examReportList.length"
            class="nodata display_flex align-items_center justify-content_flex-center">
            <div>
              <img :src="noResImg" alt="" />
              <p class="text-center">{{ errorMsg }}</p>
            </div>
          </div>

          <div class="loadingMask" v-if="pageInited && listLoading"></div>

          <!-- 骨架屏 -->
          <ul class="examReport__list examReport__list--skeleton list-none" v-if="!pageInited">
            <li class="examReport__item examReport__item--skeleton" v-for="(item, index) in liLength" :key="index">
              <div class="score-container middle-helper">
                <skeleton width="90px" height="90px" radius="30px" />
              </div>
              <div class="exam-name-area">
                <skeleton width="260px" height="32px" radius="2px" />
                <skeleton width="400px" height="18px" radius="2px" />
                <skeleton width="360px" height="18px" radius="2px" />
                <skeleton width="600px" height="18px" radius="2px" />
              </div>
              <div class="btns middle-helper">
                <skeleton width="120px" height="40px" radius="5px" />
              </div>
            </li>
          </ul>

          <!--个册列表数据-->
          <ul id="popoverUl" class="examReport__list list-none">
            <li class="examReport__item" v-for="item in examReportList" :key="item.index" @click="lookReport(item)">
              <!-- 平均分 -->
              <div class="score-container middle-helper">
                <img :src="getQuesIcon(item)" alt="" />
              </div>

              <!-- 考试信息 -->
              <div class="exam-name-area">
                <div class="exam-name middle-helper" v-if="!item.workId">
                  <span class="title">{{ item.examName }}</span>
                  <el-tag size="small" effect="plain" class="tag-item"
                    :type="item.source == 4 ? 'success' : item.source == 3 ? '' : 'warning'">{{ getSource(item)
                    }}</el-tag>
                  <el-tag size="small" v-if="item.analysisMode == 1" type="warning">新高考</el-tag>
                  <el-tag size="small" type="info">{{ item.categoryName }}</el-tag>
                </div>
                <div class="exam-name middle-helper" v-else>
                  <span class="title">{{ item.examName }}</span>
                  <el-tag size="small" effect="plain" class="tag-item" type="success">在线作业</el-tag>
                </div>

                <div class="down">
                  <div class="exam-detail clearfix">
                    <div class="exam-time pull-left" v-if="!item.workId">
                      时间：{{ item.examDateTime }}
                    </div>
                    <div class="exam-time pull-left" v-else>
                      时间：{{ item.workInfo.hwSendTime }} ~ {{ item.workInfo.endTime }}
                    </div>
                    <br />
                    <div class="exam-time">学科：{{ item.subjectName }}</div>
                    <div class="exam-time">班级：{{ item.classNames }}</div>
                  </div>
                </div>
              </div>

              <div class="btns">
                <div class="last-report-state" v-if="lastOpenExamId === item.examId">上次讲评</div>
                <el-button type="primary" class="report-btn ver-mid" @click="lookReport(item)">
                  开始讲评
                </el-button>
              </div>
            </li>
          </ul>
        </div>

        <!--分页器-->
        <el-pagination background :hide-on-single-page="!examReportList.length" class="text-right page"
          layout="total, prev, pager, next" @current-change="handleCurrentChange" :current-page.sync="pagination.page"
          :page-size="liLength" :total="pagination.total_rows">
        </el-pagination>
      </div>
    </div>

    <div class="comment-footer">
      <div class="pull-left">
        <!--学年-->
        <!-- <div class="header__select pull-left">
          <span class="select__label">学年：</span>
          <el-select v-model="yearValue" class="year-select" @change="changeYear" placeholder="请选择学年">
            <el-option v-for="item in academicYears" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </div> -->

        <!-- 月份 -->
        <div class="header__select pull-left">
          <span class="select__label">按月：</span>
          <el-date-picker class="year-month-select" v-model="timeSlot" align="right" type="monthrange" range-separator="-"
            :picker-options="pickerOptions" start-placeholder="开始月份" end-placeholder="结束月份" @change="changeMonth"
            :clearable="false" value-format="yyyy-MM-dd">
          </el-date-picker>
        </div>

        <!--学科-->
        <div class="header__select pull-left">
          <span class="select__label">学科：</span>
          <el-select v-model="subjectValue" class="term-select" @change="changeSubject" placeholder="请选择学科">
            <el-option v-for="item in subjectList" :key="item.id" :label="item.name" :value="item.id"
              v-show="item.show || item.show == undefined">
            </el-option>
          </el-select>
        </div>
      </div>

      <div class="pull-right">
        <el-button @click="closeWnd">关闭</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { selectAllType } from '@/service/pbook';
import { getCommentExamList, statGrdRate, getDefaultData } from '@/service/pexam';
import {
  getUserRoles,
  getGradeListByRole,
  getSubjectListByRole,
  getClassListByRole,
  getTeacherClassList,
  getUserRoleAPI,
  classList,
  getUserInfoAPI,
  getPublicConfigBySchoolInfo,
} from '@/service/api';
import { uniqueFunc, reviseListMap } from '@/utils/common';
import UserRole from '@/utils/UserRole';
import skeleton from '@/components/skeleton.vue';
import { loginByUserId, loginByUserToken } from '@/utils/login';
import { WeikeRecord } from '@/plugins/WeikeRecord/WeikeRecord.class';
import moment from "moment";
import { TrackHelper } from "@/plugins/track-helper";

var now = '';
export default {
  name: 'comment',
  components: {
    skeleton,
  },
  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      // 学年列表
      academicYears: [],
      yearValue: '',
      // 学期列表
      academicTerm: [
        { id: '', name: '全部学期' },
        { id: 110, name: '第一学期' },
        { id: 111, name: '第二学期' },
      ],
      termValue: '',
      // 类别列表
      categoryList: [],
      categoryValue: '',
      searchValue: '',
      // 考试报告列表
      examReportList: [],
      // 分页
      pagination: {
        page: 1,
        total_rows: 0,
      },
      // 每页显示的数量
      liLength: 5,
      errorMsg: '暂无数据',
      // 是否完成初始化
      pageInited: false,
      // 加载中
      listLoading: true,
      //角色列表
      roleList: [],
      roles: '6',
      roleTypes: '',
      //年级信息
      gradeInfo: {},
      //角色下的班级
      roleClassList: [],
      //角色下的学科
      roleSubjectList: [],
      subjectList: [],
      // 年级列表
      grdList: [],
      subjectValue: '',
      accountType: '',
      adminType: '',
      userType: '',
      userId: '',
      schoolId: '',
      lastOpenExamId: '',

      timeSlot: [],
      pickerMinDate: "", //第一次选中的时间
      pickerOptions: {
        onPick: (obj) => {
          this.pickerMinDate = new Date(obj.minDate).getTime();
        },
      },
    };
  },
  computed: {
    // 获得角色班级分类
    getRoleClassCate() {
      let clzId = [],
        clzName = [],
        layeredClassIds = [];
      this.roleClassList.forEach((item, index) => {
        if (item.classType == 1) {
          clzId.push(item.classId)
          clzName.push(item.className)
        } else if (item.classType == 3) {
          layeredClassIds.push(item.classId)
        }
      });
      return {
        clzId: clzId.join(','),
        clzName: clzName.join(','),
        layeredClassIds: layeredClassIds.join(',')
      };
    },
    // 对选择后的时间格式化
    timeSlotFormat() {
      return [
        this.timeSlot[0],
        moment(this.timeSlot[1]).endOf('month').format('YYYY-MM-DD'),
      ];
    },
  },

  async mounted() {
    let $listContainer = document.getElementById('list-container');
    let contHeight = $listContainer.offsetHeight;
    this.liLength = Math.floor(contHeight / 150);

    let querySchoolId = this.$route.query.schoolId || this.$route.query.schoolid;
    let queryUserId = this.$route.query.userId;
    let loginInfo;

    if (querySchoolId && this.$route.query.userId) {
      this.schoolId = querySchoolId;
      this.userId = queryUserId;
      loginInfo = await this.getUserInfo();
    } else {
      loginInfo = this.$sessionSave.get('configInfo') || this.$sessionSave.get('loginInfo');
      this.schoolId = loginInfo.schoolid;
      this.userId = loginInfo.id;
    }
    TrackHelper.Instance.UpdateLoginInfo(loginInfo);

    this.accountType = 1;
    // this.accountType = loginInfo.account_type || 4;
    this.$store.commit('saveSchoolInfo', { id: this.schoolId });
    this.lastOpenExamId = Number(this.$localSave.get('lastOpenExamId_' + this.userId));
    this.timeSlot = this.getLastMonthRange();

    this.initUserAuth();
    this.initToolbar();
    new WeikeRecord(loginInfo);
  },
  methods: {
    /**
     * @description: 初始化用户权限
     * @return {*}
     */
    async initUserAuth() {
      let res = await getPublicConfigBySchoolInfo({
        schoolId:
          this.$sessionSave.get('schoolInfo').id ||
          this.$sessionSave.get('loginInfo').schoolid,
        dictCode: '125',
        userId: this.$sessionSave.get('loginInfo').id,
      })

      this.handlePermissions(res.data);
    },

    handlePermissions(data) {
      // 初始化jy权限
      let jyeooQues = data.filter(item => {
        return item.dictCode == '125';
      })[0].state;
      this.$sessionSave.set('jyeooQues', jyeooQues);
    },

    // 获取最近前一个月+本月+后一个月时间范围
    getLastMonthRange() {
      const start = moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD');
      const end = moment().add(1, 'months').endOf('month').format('YYYY-MM-DD');
      return [start, end];
    },
    /**
     * @description: 初始化工具栏
     * @return {*}
     */
    async initToolbar() {
      this.getAcadData();
      await this.selectAllType();
      await this.getRoleSubject();
      this.roleClassList = await this.getRoleClassList();
      await this.initSchool();
      await this.getDefaultData();
      await this.getCommentExamList();
      this.pageInited = true;
    },

    /**
     * @description: 获取教师角色班级列表
     * @return {*}
     */
    async getRoleClassList() {
      let subject = this.roleSubjectList.find(it => this.subjectValue == it.subjectId)
      const res = await getClassListByRole({
        schoolId: this.schoolId,
        userId: this.userId,
        userType: 6,
        classType: -1,
      });
      let classList = Array.isArray(res.data) ? res.data : [];

      return classList;
    },

    /**
     * @description: 获取作业题目图标
     * @param {*} workId 不为空是在线作业
     * @param {*} scanExamSource 0:自定义 1:校本 2:区本 3:学科网
     * @param {*} workInfo hwTypeCode,firstType,hwType,markingType
     * @return {*}
     */
    getQuesIcon({ scanExamSource, workId, workInfo }) {
      let iconName = '';

      if (workId) {
        // 在线作业
        switch (Number(workInfo.hwTypeCode)) {
          case 101:
            iconName = 'icon_sync';
            break;
          case 102:
            iconName = 'icon_review';
            break;
          case 103:
            iconName = 'icon_tiku';
            break;
          case 104:
            iconName = 'icon_xiaoben';
            break;
          case 108:
            iconName = 'icon_xueke';
            break;
          case 110:
            iconName = 'icon_practice';
            break;
          case 112:
            iconName = 'icon_qb';
            break;
          case 114:
            iconName = 'icon_word';
            break;
          default:
            iconName = 'icon_defined';
            break;
        }
      } else {
        // 扫描作业
        switch (Number(scanExamSource)) {
          case 0:
            iconName = 'icon_defined';
            break;
          case 1:
            iconName = 'icon_xiaoben';
            break;
          case 2:
            iconName = 'icon_qb';
            break;
          case 3:
            iconName = 'icon_xueke';
            break;
        }
      }

      return require(`../../assets/ques-icons/${iconName}.png`);
    },

    /**
     * @name:根据来源类别显示来源名称
     */
    getSource({ source }) {
      let sourceName = '';

      switch (source) {
        case 3:
          sourceName = '手阅';
          break;
        case 4:
          sourceName = '网阅';
          break;
        default:
          sourceName = '补录';
          break;
      }

      return sourceName;
    },

    // 初始化学校后
    async initSchool() {
      if (now) {
        if (new Date().getTime() - now.getTime() < 1500) {
          return;
        }
      } else {
        now = new Date();
      }

      return new Promise(async resolve => {
        let $this = this;
        if ($this.subjectList.length > 0) {
          resolve();
          return;
        }

        const ret = await UserRole.getUserInfoPersonalityTest();
        const roleMap = (this.roleMap = UserRole.utils.getRoleSubjectListMapByYear());
        this.subjectList = [];

        let roleSubjectList = this.roleSubjectList;
        if (roleMap[this.roles] && roleMap[this.roles].length) {
          roleSubjectList = roleMap[this.roles];
        }
        ret.userSubList.forEach((v, i) => {
          let show = !!roleSubjectList.find(item => {
            return item.subjectId == v.id;
          });
          if (show) {
            v.show = true;
            this.subjectList.push(v);
          }
        });
        this.setSubjectListByRole();

        let firstShowSubject = this.subjectList.find(item => item.show === true);
        let subjectValue = this.$localSave.get('comment_subjectid_' + this.userId);
        this.subjectValue = subjectValue || (firstShowSubject ? firstShowSubject.id : '');

        resolve();
      });
    },

    /**
     * @name:切换学科
     */
    changeSubject() {
      clearTimeout(this.listTimer);
      this.pagination.page = 1;
      this.getCommentExamList();
    },

    // 切换年级，联动显示学科
    changeGrade(data) {
      if (data == '') {
        this.subjectList.forEach(v => {
          v.show = true;
        });
      } else {
        this.setSubjectListByRole();
        // let grd = this.grdList.find(q => q.id == data);
        // this.subjectList.forEach(v => {
        //   if (v.id && !v.show) return;
        //   v.show = !!(v.id == '' || v.phaseId == grd.phaseId || v.phaseId == grd.phase + 2);
        // });
        let firstShowSubject = this.subjectList.find(item => item.show === true);
        this.subjectValue = firstShowSubject.id || '';
      }
      clearTimeout(this.listTimer);
      this.pagination.page = 1;
      this.getCommentExamList();
    },

    // 设置角色学科列表
    setSubjectListByRole() {
      if (this.roles == 2 || this.roles == 5 || this.accountType == 4 || this.accountType == 5) {
        this.subjectList.forEach(item => {
          item.show = true;
        });
        const allSubject = this.subjectList.filter(item => {
          return item.id == '';
        });
        if (allSubject.length == 0) {
          this.subjectList.unshift({
            id: '',
            name: '全部',
          });
        }
        this.subjectValue = '';
      } else if (this.roleMap[this.roles] && this.roleMap[this.roles].length) {
        let roleList = this.roleMap[this.roles];
        this.subjectList.forEach(v => {
          v.show = !!roleList.find(item => {
            return item.subjectId == v.id;
          });
        });
        this.subjectValue = this.subjectList.find(item => item.show).id;
      }
    },

    // 获取默认值
    async getDefaultData() {
      let data = await getDefaultData();
      this.$sessionSave.set('defaultList', data.data);
    },

    /**
     * @name:获取状态
     */
    getState(item) {
      return item.source == 3 || item.source == 4;
    },
    updateExamList() {
      this.getCommentExamList(true);
    },
    /**
     * @name:获取用户信息
     */
    async getUserInfo() {
      await loginByUserId(this.userId);
      let res = await getUserInfoAPI({
        userId: this.userId,
      });
      res.data.token = this.$route.query.token;
      this.adminType = res.data.admin_type;
      this.userType = res.data.user_type;
      let subClass = res.data.substituteClassList;
      let map = reviseListMap(subClass, 'subjectName', 'className');
      let mapSub = reviseListMap(subClass, 'subjectId', 'className');
      this.$sessionSave.set('subjectClz', map);
      this.$sessionSave.set('subjectIdClz', mapSub);
      this.$sessionSave.set('adminType', this.adminType);
      this.$sessionSave.set('userType', this.userType);
      return res.data;
    },
    /**
     * @name:获取班级
     */
    async getExamClassList() {
      this.roleClassList = uniqueFunc(this.roleClassList, 'classId');
      this.$sessionSave.set('roleClassList', this.roleClassList);
    },
    /**
     * @name:获取备课组长信息
     */
    async getPreparationInfo() {
      let res = await getUserRoleAPI({
        userId: this.userId,
        roleType: 2,
      });

      this.gradeInfo = res.data[0];
      this.getGradeClassList(this.gradeInfo.year, this.gradeInfo.phase);
    },
    /**
     * @name:获取年级下的班级
     */
    async getGradeClassList(year, phase) {
      let res = await classList(year, phase);
      data.data.rows.forEach(item => {
        this.roleClassList.push(item);
      });
    },
    /**
     *
     * @name:根据用户角色获取学科
     */
    async getRoleSubject() {
      let res = await getSubjectListByRole({
        schoolId: this.schoolId,
        userId: this.userId,
        userType: 6,
      });

      let list = [];
      res.data.forEach(ite => {
        list.push(ite);
      });

      this.roleSubjectList = uniqueFunc(list, 'subjectId');
      this.subjectValue = this.roleSubjectList[0].subjectId;
      this.$sessionSave.set('roleSubjectList', this.roleSubjectList);
    },
    /**
     * @name:获取角色对应年级
     */
    async getGradeList() {
      try {
        let res = await getGradeListByRole({
          schoolId: this.schoolId,
          userId: this.userId,
          userType: this.roles,
        });
        res.data.forEach(item => {
          this.$set(item, 'id', item.gradeId);
          this.$set(item, 'name', item.gradeName);
        });
        this.grdList = res.data;
        if (this.roles == 1) {
          this.grdList.unshift({
            id: '',
            name: '全部',
          });
        }
      } catch (error) {
        this.grdList = [];
      }
    },
    //
    // 关闭窗口
    closeWnd() {
      cef.message.sendMessage('mirco.call_cplus', ['', 'closewnd']);
    },
    // 获取学年，学期
    getAcadData() {
      if (this.academicYears.length > 0) return;
      let date = new Date(),
        y = date.getFullYear(),
        M = date.getMonth(),
        d = date.getDate();
      for (let i = 0; i < 5; i++) {
        let y1 = y - i - 1;
        let y2 = y - i;
        if (Number(`${M + 1}${d < 10 ? `0${d}` : d}`) >= 810) {
          y1 = y - i;
          y2 = y - i + 1;
        }
        this.academicYears.push({
          id: `${Number(y1.toString().substring(1))}${Number(y2.toString().substring(1))}`,
          name: `${y1}-${y2}学年`,
        });
      }
      this.academicYears.unshift({ id: '', name: '全部学年' });
      this.$store.commit('saveAcadYearsList', this.academicYears);
      this.$store.commit('saveAcadTermList', this.academicTerm);
      this.initAcadData();
    },

    // 初始化学年学期，默认显示机器当前所在学年学期
    initAcadData() {
      let lastYear = this.$localSave.get('comment_year_' + this.userId),
        lastTerm = this.$sessionSave.get('lastTerm');
      // 例如2020到2021年上学期[2020-8-10，2021-2-15], 下学期[2021-2-16，2021-8-9]
      let newDate = new Date(),
        curYear = newDate.getFullYear(),
        curMonth = newDate.getMonth() + 1,
        curDate = newDate.getDate(),
        curDateTime = new Date(curYear + '-' + curMonth + '-' + curDate).getTime(),
        selectYear = '',
        selectTerm = '';
      if (this.academicYears.length) {
        if (lastYear || lastYear === '') {
          this.yearValue = lastYear;
        } else {
          // 默认显示当前机器的年份,
          if (curDateTime <= new Date(curYear + '-2-15')) {
            selectYear = Number(curYear) - 1;
            selectTerm = '第一学期';
          } else if (
            curDateTime >= new Date(curYear + '-2-16') &&
            curDateTime <= new Date(curYear + '-8-9')
          ) {
            selectYear = Number(curYear) - 1;
            selectTerm = '第二学期';
          } else {
            selectYear = curYear;
            selectTerm = '第一学期';
          }
          this.academicYears.forEach(item => {
            if (item.name.indexOf(selectYear + '-') !== -1) {
              this.yearValue = item.id;
            }
          });
        }
      }
      if (this.academicTerm.length) {
        if (lastTerm || lastTerm === '') {
          this.termValue = lastTerm;
        } else {
          //默认显示当前机器时间所在的学期
          this.academicTerm.forEach(item => {
            if (item.name.indexOf(selectTerm) !== -1) {
              this.termValue = item.id;
            }
          });
        }
      }
      this.$localSave.set('comment_year_' + this.userId, this.yearValue);
      this.$sessionSave.set('lastTerm', this.termValue);
    },

    // 获取类别列表
    async selectAllType() {
      let res = await selectAllType();

      this.$store.commit('saveCategoryList', res.data);
      this.categoryList = this.$deepClone(res.data);
      this.categoryList.unshift({
        id: '',
        name: '全部',
      });
    },

    // 获取考试报告列表
    async getCommentExamList(notChanePage) {
      try {
        this.pagination.page = notChanePage ? this.pagination.page : 1;
        this.listLoading = true;
        this.$localSave.set('comment_subjectid_' + this.userId, this.subjectValue);
        this.$localSave.set('comment_year_' + this.userId, this.yearValue);

        let res = await getCommentExamList({
          schoolId: this.schoolId,
          acadYearsId: '',
          acadTermId: '',
          gradeId: '',
          subjectId: this.subjectValue,
          categoryId: this.categoryValue || '',
          keyWord: this.searchValue && this.searchValue.trim(),
          page: this.pagination.page,
          pageSize: this.liLength,
          importScoreState: 1,
          orderType: 1,
          desc: 1,
          startTime: this.timeSlotFormat[0],
          endTime: this.timeSlotFormat[1],
          ...this.getRoleClassCate,
        });
        this.pagination.total_rows = res.data.total;

        //数据为空
        if (!res.data.list.length) {
          this.examReportList = [];
          this.listLoading = false;

          return;
        }

        let ids = [];
        res.data.list.forEach(v => {
          v.grd = { left: -135, right: -135 };
          ids.push(`${v.examId}`);
        });
        statGrdRate({ examIds: JSON.stringify(ids) }).then(grds => {
          grds.data.forEach(grd => {
            let item = res.data.list.find(q => q.examId == grd.sourceId);
            grd.left = -135;
            grd.right = -135;

            let rate = parseFloat(grd.scoreRate) * 360;
            if (rate > 180) {
              grd.right = 45;
              grd.left = rate - 180 - 135;
            } else {
              grd.right = rate - 135;
            }

            if (!item.dataState || item.dataState == 0) {
              grd.left = -135;
              grd.right = -135;
            }
            item.grd = grd;
          });

          this.examReportList = res.data.list;
          this.listLoading = false;
        });
      } catch (error) {
        this.listLoading = false;
      }
    },
    // 切换学年
    changeYear(val) {
      this.getCommentExamList();
    },
    // 切换月份区间
    changeMonth() {
      this.pagination.page = 1;
      this.getCommentExamList();
    },
    // 切换学期
    changeTerm(val) {
      this.$sessionSave.set('lastTerm', val);
      this.getCommentExamList();
    },
    // 切换类别
    changeCategory(val) {
      this.$sessionSave.set('lastCategory', val);
      this.getCommentExamList();
    },
    // 搜索
    searchReport() {
      this.pagination.page = 1;
      this.$sessionSave.set('lastSearchVal', this.searchValue && this.searchValue.trim());
      this.getCommentExamList();
    },
    // 分页查询
    handleCurrentChange(val) {
      this.pagination.page = val;
      console.log('分页查询');
      this.getCommentExamList('changePage');
    },

    /**
     * @description: 获取在线作业讲评的路由名称
     * @param {*} hwType
     * @param {*} hwTypeCode
     * @return {*}
     */
    getHomeworkRouterName({ hwType, hwTypeCode }) {
      let routerName = '';
      //答题卡作业
      if (hwType == 0) {
        routerName = 'jpReport';
      } else if (hwType == 1) {
        //优选题库
        if (hwTypeCode == '103') {
          routerName = 'jpJingyouReport';
        } else if (hwTypeCode == '104' || hwTypeCode == '112' || hwTypeCode == '110') {
          routerName = 'jpXBReport';
        } else if (hwTypeCode == '108') {
          routerName = 'jpXKWReport';
        } else if (hwTypeCode == '114') {
          routerName = 'jpWordReport';
        } else {
          //17题库作业
          routerName = 'jpQuestionBankReport';
        }
      }

      return routerName;
    },

    // 查看报告
    lookReport(item) {
      if (item.workId) {
        const WORKINFO = item.workInfo;
        const HOST = process.env.VUE_APP_HOMEWORK;
        const ROUTER_NAME = this.getHomeworkRouterName(WORKINFO);
        const url = `${HOST}/${ROUTER_NAME}/${this.userId}/${item.workId}/${item.examName}/${WORKINFO.hwType
          }/screen?hwtypecode=${WORKINFO.hwTypeCode}&subjectId=${this.subjectValue}&cardId=${WORKINFO.cardId
          }&isHomeMade=0&returnUrl=${encodeURIComponent(location.href)}`;

        // 在线作业
        window.open(url, '_blank');
      } else {
        // 扫描作业
        this.getExamClassList();
        let reportDetail = this.$sessionSave.get('reportDetail');
        let isUpdate = !reportDetail || (reportDetail && reportDetail.examId !== item.examId);
        this.$sessionSave.set('reportDetail', item);
        this.$sessionSave.set('reportParent', item);
        reportDetail = item;
        this.$sessionSave.remove('subjectId');
        this.$sessionSave.set('subjectId', this.subjectValue);
        this.$router.push({
          path: '/commentDetail',
          query: {
            isUpdate: isUpdate,
            examId: item.examId,
            subjectRealId: '',
            schoolId: this.$route.query.schoolId,
            statType: item.statType === 0 ? '0' : '1'
          },
        });

        this.lastOpenExamId = reportDetail.examId;
        this.$localSave.set('lastOpenExamId_' + this.userId, reportDetail.examId);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.commentContainer {
  width: 100%;
  height: 100%;
  background-color: #eff1fa;
  padding-top: 15px;
  overflow: hidden;

  .breadcrumb-header {
    position: relative;
    height: 60px;
    padding: 10px 30px;
    background-color: #fff;
    font-size: 20px;
    color: #495060;

    .left-text {
      line-height: 38px;
    }

    .right-text {
      float: right;
    }

    .icon {
      display: inline-block;
      margin-right: 10px;
    }

    .header__serarch {
      display: flex;
      width: 400px;
      border-radius: 50px;
      overflow: hidden;

      ::v-deep .el-input__inner {
        border-top-left-radius: 50px;
        border-bottom-left-radius: 50px;
      }

      .search__icon {
        width: 60px;
        height: 40px;
        padding: 0 20px;
        font-size: 20px;
        color: #fff;
        background: #409eff;
        border-radius: 0 3px 3px 0;
        outline: none;
        cursor: pointer;
      }
    }
  }

  .header__select {
    display: inline-block;
    margin-right: 15px;

    .year-month-select {
      position: relative;
      top: -2px;
      width: 200px;
    }

    .year-select,
    .term-select,
    .grade-select {
      width: 180px;
    }

    &.refresh-btn {
      height: 40px;
    }
  }

  .comment-body {
    width: 100%;
    height: 100%;
    padding: 50px 0 90px;
    margin: -50px 0 -90px;
  }

  .comment-main {
    width: 100%;
    height: 100%;
    padding: 20px 10px;
    font-size: 16px;
    background-color: #edf2f6;

    .page {
      line-height: 90px;
      height: 90px;
      vertical-align: middle;
      border-radius: 0 0 15px 15px;
      padding-top: 10px;

      ::v-deep {
        .el-pager li {
          background-color: #fff;

          &.active {
            background-color: #409eff;
          }
        }
      }
    }

    .examReport__content {
      position: relative;
      width: 100%;
      border-radius: 15px 15px 0 0;

      .loadingMask {
        background-color: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #c8cee1;
        border-radius: 5px;
      }

      .examReport__list {
        position: relative;
        z-index: 100;
        width: 100%;
        height: calc(100vh - 240px);
        padding: 0 15px;
        overflow-y: auto;

        &.examReport__list--skeleton {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          right: 0;
          z-index: 10;
        }

        .examReport__item {
          width: 100%;
          display: flex;
          box-sizing: border-box;
          background: #fff;
          color: #222;
          font-size: 14px;
          border-radius: 5px;
          line-height: 130px;
          margin: 12px 0;
          overflow: hidden;

          &:first-child {
            margin-top: 0;
          }

          &.examReport__item--skeleton {
            padding-top: 10px;

            .skeleton {
              margin-bottom: 10px;
            }
          }

          &:hover {
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
            border-color: #eee;
          }

          .score-container {
            position: relative;
            padding: 0 50px;

            .el-progress {
              display: inline-block;

              &::before,
              &:after {
                position: absolute;
                left: 0;
                z-index: 1;
                width: 100%;
                text-align: center;
              }

              &::before {
                top: 34px;
                content: attr(aria-valuetext);
                font-size: 24px;
              }

              &:after {
                bottom: 25px;
                content: '平均分';
                font-size: 12px;
                color: #9297a6;
              }

              &.no-score {
                &::before {
                  display: none;
                }

                &::after {
                  bottom: 0;
                  top: 0;
                  content: '暂无平均分';
                  line-height: 100px;
                }
              }
            }
          }

          .exam-name-area {
            flex: auto;
            padding: 10px 0;

            .exam-name {
              font-size: 24px;
              color: #3f4a54;
              line-height: 45px;

              >.title {
                margin-right: 15px;
              }

              >.el-tag {
                margin-right: 10px;
                background-color: #fff;
                font-weight: 600;
                font-family: 微软雅黑, Microsoft YaHei, Verdana, Arial, Helvetica, sans-serif;
              }
            }

            .exam-detail {
              line-height: 28px;
              width: 100%;

              >div {
                margin-right: 30px;
                font-size: 16px;
                color: #8a8a8a;
              }

              .exam-subject {
                max-width: 350px;
              }
            }
          }

          .moreEdit {
            width: 38px;
            display: inline-block;
            height: 38px;
            cursor: pointer;
            color: #008dea;
            font-size: 18px;

            background: url('../../assets/moreEdit.png') center center no-repeat;
          }

          .btns {
            text-align: center;
            padding-right: 50px;
            white-space: nowrap;

            .last-report-state {
              display: inline-block;
              color: #ff9c1c;
              font-size: 20px;
              font-weight: 700;
              margin-right: 15px;
              vertical-align: middle;
            }

            .report-btn {
              border-radius: 5px;
              display: inline-block;
              vertical-align: middle;
              width: 120px;
              height: 40px;
              font-size: 16px;
              cursor: pointer;
              // background-color: #409eff;
            }
          }
        }
      }
    }
  }

  .comment-footer {
    position: relative;
    box-shadow: 0 0 3px 0 rgba(49, 57, 215, 0.23);
    line-height: 88px;
    height: 90px;
    background-color: #fff;
    padding: 0 40px;

    .header__select {
      margin-right: 30px;
      line-height: 82px;

      ::v-deep .el-input__inner {
        border-width: 2px;
        height: 45px;
      }
    }

    .el-button {
      width: 150px;
      height: 52px;
      font-size: 24px;
      border-width: 2px;
      border-radius: 8px;
    }
  }
}

.nodata {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  img {
    display: block;
    margin: 0 auto;
  }
}
</style>
<style lang="scss">
.search__text {
  .el-input__inner {
    border-radius: 4px 0 0 4px;
  }
}
</style>

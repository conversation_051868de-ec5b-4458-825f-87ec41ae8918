<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-02-02 14:01:14
 * @LastEditors: 小圆
-->
<template>
  <div class="img-box" v-if="currentRow">
    <!-- 有扫描数据 -->
    <template v-if="currentRow.image && currentRow.image != ''">
      <el-image class="img" :src="getImage(currentRow)" @load="loadImage" />
    </template>
    <!-- 坐标 -->
    <template v-for="(ques, quesIndex) in currentRow.questions">
      <template v-if="isDrawImg && currentRow.image != ''">
        <!-- 客观题 -->
        <div v-if="ques.is_obj">
          <div
            v-if="ques.pos && ques.pos.length"
            class="pointer-none"
            :id="'id_' + ques.question_id"
            :data-sort="'no_' + ques.question_no"
            :style="{ textAlign: 'right', position: 'absolute', ...getBoxStyle(ques.pos, ques) }"
          ></div>
          <!-- <points-box
            class="points-box"
            :class="{
              active: box.fill,
              error: box.fill && (Math.pow(2, optionIndex) & ques.answer_int) == 0,
              right: box.fill && (Math.pow(2, optionIndex) & ques.answer_int) != 0,
            }"
            v-for="(box, optionIndex) in ques.list"
            :ques="ques"
            :points="box"
            :quesIndex="quesIndex"
            :optionIndex="optionIndex"
            :checkType="'detail'"
            :scale="scale"
            @choice-correct="choiceCorrect"
          ></points-box> -->
        </div>
        <!-- 主观题 -->
        <div v-else>
          <div
            v-if="ques.pos && ques.pos.length"
            class="pointer-none"
            :id="'id_' + ques.question_id"
            :data-sort="'no_' + ques.question_no"
            :style="{ textAlign: 'right', position: 'absolute', ...getBoxStyle(ques.pos, ques) }"
          ></div>
        </div>
      </template>
    </template>
  </div>
</template>

<script>
import { saveScanPageAPI, setImagePointError, setAbsentAPI } from '@/service/pexam';
import PointsBox from '@/components/scan/PointsBox.vue';
import { getScanPaperPoints } from '@/service/testbank';
import { replaceALiUrl } from '@/utils/common';

export default {
  components: { PointsBox },
  props: {
    currentRow: {
      type: Object,
      default: () => {},
    },
    // 当前索引
    index: {
      type: Number,
      default: 0,
    },
    currentSort: {},
    currentSortTitle: {},
    currentQuesNo: {},
    currentQuesId: {},
    currentQues: {},
    cardType: {},
  },
  watch: {
    async currentQues(ques) {
      await this.$nextTick();
      if (!ques) return;

      if (ques.tempChooseType === 1) {
        let id = this.cardType == 1 ? ques.questionId : ques.tempTargetId;
        const dom = document.getElementById('id_' + id);
        if (dom) {
          dom.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
        return;
      }

      const dom = document.querySelector(`[data-sort=no_${ques.sort}]`);
      if (dom) {
        dom.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    },

    async currentSort(val) {},
    async currentQuesNo(val) {
      if (!val) {
        this.currentQuesNoArr = [];
        return;
      }
      this.currentQuesNoArr = this.currentQuesNo.split(',');
    },
  },
  data() {
    return {
      scale: 1,
      isDrawImg: false,
      currentQuesNoArr: [],
    };
  },
  created() {},
  mounted() {},
  methods: {
    getBoxStyle(box, ques) {
      let display = '';
      if (
        this.cardType != 1 &&
        this.currentQues &&
        (this.currentQues.tempChooseType === 1 || this.currentQues.tempChooseType === 0)
      ) {
        display =
          this.currentQues.tempTargetId === ques.question_id && ques.question_id ? 'block' : 'none';
      } else {
        display = this.currentQuesNoArr.includes(String(ques.question_no)) ? `block` : `none`;
      }
      return {
        left: box[0] * this.scale + 'px',
        top: box[1] * this.scale + 'px',
        width: box[2] * this.scale + 'px',
        height: box[3] * this.scale + 'px',
        border: `1px solid red`,
        // display: this.currentSort == ques.question_no ? `block` : `none`,
        display,
      };
    },

    loadImage(val) {
      this.scale = val.currentTarget.height / 297;
      this.isDrawImg = true;
      this.$emit('loadImage', val.currentTarget, this.index);
    },

    getImage(item) {
      return replaceALiUrl(item.image) + '?x-oss-process=image/resize,h_1080';
    },

    /**
     * @name:获取处理后的题目数据
     * @param {*} currentInfo  当前题目
     */
    choiceCorrect(currentInfo) {
      let currentItem = this.currentRow.questions[currentInfo.quesIndex];
      //客观题
      if (currentItem.is_obj) {
        currentItem.status = 1;
        //当前选项
        let isCurrent = currentItem.list.filter((item, index) => {
          return index == currentInfo.optionIndex;
        })[0];
        //不是当前选项
        let notCurrent = currentItem.list.filter((item, index) => {
          return index != currentInfo.optionIndex;
        });
        isCurrent.fill = !isCurrent.fill;
        if (
          currentItem.question_type &&
          (currentItem.question_type == 8 || currentItem.question_type == 2)
        ) {
          if (isCurrent.fill) {
            notCurrent.forEach(ite => {
              ite.fill = false;
            });
          }
        }
        let optionInt = 0;
        currentItem.list.forEach((item, index) => {
          if (item.fill) {
            optionInt += Math.pow(2, index);
          }
        });
        // 判断是否选择了正确答案
        if (optionInt == currentItem.answer_int) {
          //全对
          currentItem.tempScore = currentItem.total_score;
        } else if (
          (optionInt & currentItem.answer_int) != 0 &&
          (optionInt & currentItem.answer_int) == optionInt
        ) {
          //半对
          currentItem.tempScore = currentItem.miss_score;
        } else {
          //全错
          currentItem.tempScore = 0;
        }
      } else {
        //主观题获取分数
        currentItem.score_list[currentInfo.optionIndex].score = currentInfo.score;
        let totalScore = 0;
        currentItem.score_list.forEach(item => {
          totalScore += item.score;
        });
        currentItem.tempScore = totalScore;
      }
      this.$emit('get-change-row', this.currentRow);
    },
  },
};
</script>

<style lang="scss" scoped>
.stu-img-list {
  height: 100%;
  width: calc(100% - 305px);
  display: inline-block;
  overflow-y: scroll;
  .img {
    position: relative;
    width: 100%;
  }
}
.img-box {
  position: relative;
}
.score-box {
  position: absolute;
  width: 60px;
  height: 35px;
  // background-color: rgba(255, 61, 90, 0.218);
  opacity: 0.8;
  right: 0;
  font-size: 24px;
  text-align: center;
  // line-height: 42px;
  color: red;
  font-weight: bold;
  &.objBox {
    display: none;
  }
}
.total-score-box,
.total-score-title,
.stu-no-box {
  position: absolute;
  // background-color: rgb(255, 61, 90, 0.6);
  opacity: 0.8;
  text-align: center;
  color: red;
}
.total-score-title {
  top: 1px;
  left: 10px;
  font-size: 22px;
}
.total-score-box {
  font-size: 24px;
  top: 0px;
  left: 130px;
  font-weight: bold;
}
.stu-no-box {
  font-size: 28px;
  font-weight: bold;
}
.stu-no {
  color: #333;
  font-size: 16px;
}
.stu-task-name {
  color: #909399;
}
// .points-box {
//   position: absolute;
//   cursor: pointer;
//   z-index: 2;
//   &.active {
//     border: solid 4px #01cc7d;
//   }
//   &.error {
//     border: solid 4px red !important;
//   }
//   &.right {
//     border: solid 4px #01cc7d !important;
//   }
// }
.page-box-main {
  position: absolute;
  border: solid 2px #409eff;
}

.pointer-none {
  pointer-events: none;
}

.score-text {
  font-weight: 700;
  color: red;
  font-size: 28px;
}
.img-dropdown-menu {
  position: absolute;
  font-size: 20px;
  right: 20px;
  top: 1px;
}
</style>

<template>
  <div class="essay-setting-container">
    <el-form ref="form" :model="form" label-width="100px" :rules="rules">
      <el-form-item label="选择题号" prop="quesNo">
        <template v-for="item in aiEssayQuesData">
          <el-button v-if="!item.hasTip" class="btn-item ellipsis-btn" :key="item.id" @click="changeQuesNo(item)"
            :type="form.ques.quesNo == item.quesNo ? 'primary' : 'default'">{{ item.quesNos
            }}</el-button>
          <el-badge v-else is-dot class="btn-item" :key="'dot_' + item.id"><el-button class="ellipsis-btn"
              :key="item.id" @click="changeQuesNo(item)"
              :type="form.ques.quesNo == item.quesNo ? 'primary' : 'default'">{{ item.quesNos
              }}</el-button></el-badge>
        </template>
      </el-form-item>

      <el-form-item v-if="hasStyle" prop="style">
        <template slot="label">
          <span>作文体裁
            <el-popover placement="right-start" trigger="hover">
              <p>如果类型选择错误，会影响AI判分的效果，请务必慎重选择。开启作文智批阅卷后，将无法再修改作文体裁。</p>
              <i class="el-icon-warning" style="color: #F56C6C" slot="reference"></i>
            </el-popover></span>
        </template>
        <el-radio-group :disabled="isReadonly" v-model="form.way" class="essay-type-radio">
          <div class="radio-item">
            <el-radio :label="1">应用文<div class="radio-desc">通用的作文题型，要求学生按照一个文章主题写一篇短文，常见的类型有书信、邮件、记叙文、看图写作等。</div>
              </el-radio>

          </div>
          <div class="radio-item">
            <el-radio :label="2">读后续写<div class="radio-desc">常见于新高考/新中考地区的考试中，要求学生阅读已有的文章后，续写一段或者两段文字，形成一篇完整的文章。</div>
              </el-radio>

          </div>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="作文满分">
        <div class="score-display">{{ form.score }}分</div>
      </el-form-item>

      <el-form-item label="作文题目" prop="question">
        <el-input :disabled="isReadonly" type="textarea" v-model="form.question" rows="5" placeholder="请输入作文题目的详细要求..."
          resize="none"></el-input>
        <div class="hint-text">请输入作文题目的具体描述，包括主题、字数要求等</div>
      </el-form-item>

      <el-form-item label="批改要求" prop="corrRule">
        <el-input :disabled="isReadonly" type="textarea" v-model="form.corrRule" rows="5" placeholder="请输入批改的具体标准..."
          resize="none"></el-input>
        <div class="hint-text">请详细说明批改标准，如语法、内容、结构等方面的评分要点</div>
      </el-form-item>

      <el-form-item v-if="correctType == ICORRECT_TYPES.WEB" label="得分说明" prop="type">
        <el-radio-group v-model="form.type">
          <div class="radio-item">
            <el-radio :disabled="isReadonly" :label="1"> AI 批改：AI批改，老师复核，可直接使用AI评分</el-radio>
          </div>
          <div class="radio-item">
            <el-radio :disabled="isReadonly" :label="2">人机双评：AI初评，老师二评，以老师打分为准，可设置误差分，达到误差分则由仲裁老师进行评阅打分</el-radio>
            <div style="margin-left: 92px;font-size:12px;" v-if="form.type == 2">
              <span>开启仲裁：</span><el-switch v-model="form.isArbitration" :disabled="isReadonly" />
              <template v-if="form.isArbitration">
                <span style="margin-left: 20px;">分差大于<el-input v-model="form.beyondScore" :disabled="isReadonly"
                    style="width: 48px; height: 32px"></el-input>分进入仲裁</span>
                <span style="margin-left: 20px;"><el-checkbox v-model="form.isCheckScore" size="medium" :disabled="isReadonly"
                    border>仲裁时可查看AI和阅卷老师的评分</el-checkbox></span>
              </template>
            </div>
          </div>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button :disabled="isReadonly" style="float:right;" type="primary" @click="submitForm">保存修改</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  getExamAiCorrectQueAPI,
  saveExamAiCorrectQueAPI
} from '@/service/api';
import { ICORRECT_TYPES } from '@/typings/card';
export default {
  name: "essay-set",
  props: {
    aiEssayQuesData: {
      type: Array,
      default: () => []
    },
    isReadonly: {
      type: Boolean,
      default: false
    },
    hasStyle: {
      type: Boolean,
      default: false
    },
    correctType:{
      type: Number,
      default: ICORRECT_TYPES.WEB
    }
  },
  data() {
    return {
      ICORRECT_TYPES,
      form: {
        ques: null,
        quesId: "",
        title: "",
        way: "",
        score: "",
        question: "",
        corrRule: "",
        type: "",
        isArbitration:true,
        beyondScore:2,
        isCheckScore:false
      },
      rules: {
        quesId: [{ required: true, message: '请选择题目', trigger: 'change' }],
        way: [{ required: true, message: '请选择作文体裁', trigger: 'change' }],
        question: [{ required: true, message: '请输入作文题目', trigger: 'blur' }],
        corrRule: [{ required: true, message: '请输入批改要求', trigger: 'blur' }],
        type: [{ required: true, message: '请选择得分说明', trigger: 'change' }]
      }
    };
  },
  computed: {
    // quesDataWithTips() {
    //   return this.aiEssayQuesData.map(item => {
    //     debugger
    //     if(this.hasStyle) {
    //       return { ...item, hasTip: !item.question || !item.corrRule || !item.way }
    //     } else {
    //       return { ...item, hasTip: !item.question || !item.corrRule }
    //     }
    //   })
    // }
  },
  created() {
    if (this.aiEssayQuesData.length == 0) return;
    this.form.ques = this.aiEssayQuesData[0];
    this.changeQuesNo(this.form.ques);
  },
  methods: {
    async getExamAiCorrectQue() {
      let params = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        workId: this.$route.query.workId,
        quesId: this.form.quesId,
        userId: this.$sessionSave.get("loginInfo").id,
        userName: this.$sessionSave.get("loginInfo").user_name,
      }
      let res = await getExamAiCorrectQueAPI(params);
      if (res.code == 1) {
        this.form.question = res.data[0].question;
        this.form.corrRule = res.data[0].corrRule;
        this.form.way = res.data[0].way;
        this.form.type = res.data[0].type || "";
        this.form.isArbitration = res.data[0].isArbitration == "" ? true : (res.data[0].isArbitration ?? true);
        this.form.beyondScore = res.data[0].beyondScore || 2;
        this.form.isCheckScore = res.data[0].isCheckScore || false;
      }
    },
    async saveExamAiCorrectQue() {
      let params = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        workId: this.$route.query.workId,
        userId: this.$sessionSave.get("loginInfo").id,
        userName: this.$sessionSave.get("loginInfo").user_name,
        json: JSON.stringify({
          title: this.form.title,
          quesId: this.form.quesId,
          way: this.form.way,
          question: this.form.question,
          corrRule: this.form.corrRule,
          type: this.form.type,
          score: this.form.score,
          isArbitration:this.form.isArbitration,
          beyondScore: this.form.beyondScore,
          isCheckScore: this.form.isCheckScore,
          finalScore: 1,//双评分数计算 0:默认不处理 1:四舍五入保留一位小数(取0.5) 2:四舍五入取整
        })
      }
      await saveExamAiCorrectQueAPI(params);
    },
    changeQuesNo(ques) {
      this.form.ques = ques;
      this.form.score = ques.score;
      this.form.quesId = ques.quesNo;
      this.form.title = ques.quesNos;
      this.form.way = ques.way;
      this.getExamAiCorrectQue();
    },
    async submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          await this.$confirm(`开启作文题智批阅卷后，将无法再修改作文题分值，请确认作文题分值设置正确。当前设置的本题分值：${this.form.score}分`, '提示', {
            confirmButtonText: '已确认无误',
            cancelButtonText: '再确认下',
            type: 'warning'
          });
          this.saveExamAiCorrectQue();
          this.$emit('saveAICorrectQue');
        } else {
          this.$message.error('请完成必填项');
          return false;
        }
      });
    },
  }
};
</script>

<style lang="scss" scoped>
.essay-setting-container {
  width: 100%;
  padding: 20px;
  background-color: #fff;

  .el-form-item {
    margin-bottom: 25px;

    .btn-item {
      margin-right: 8px;
      margin-bottom: 8px;
    }
    .ellipsis-btn{
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
  }

  .score-display {
    font-size: 16px;
    color: #67c23a;
    font-weight: bold;
  }

  .hint-text {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
  }

  .el-textarea {
    ::v-deep .el-textarea__inner {
      border-color: #dcdfe6;
      transition: all 0.3s;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
    }
  }

  .essay-type-radio {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .radio-item {
    display: flex;
    flex-direction: column;
    padding: 12px 16px;
    transition: all 0.3s;

    &:hover {
      border-color: #C0C4CC;
      background-color: #F5F7FA;
    }

    .radio-desc {
      margin-top: 8px;
      margin-left: 24px;
      font-size: 13px;
      color: #606266;
      line-height: 1.5;
    }
  }

  /* 当单选框被选中时，高亮显示对应项 */
  .radio-item:has(.el-radio.is-checked) {
    border-color: #409EFF;
    background-color: #ecf5ff;
  }

  .el-button[type="primary"] {
    padding: 12px 25px;
    font-size: 14px;
  }
}
</style>
<!--
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2024-12-03 09:11:35
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-04-23 16:03:52
-->
<template>
  <div>
    <bread-crumbs :title="'答卷设置（' + subjectRealName + '）'"></bread-crumbs>
    <div class="exam-page-containter">
      <!-- 头部切换 -->
      <el-row class="card-header">
        <span class="exam-title">{{ examName }}</span>
        <el-button style="margin-right: 15px;" v-if="source == ISOURCE_TYPES.HAND" type="text"
          @click="showScoringModeDialog = true">统分模式设置</el-button>
        <el-select v-if="!isCompositeSubject" v-model="relateCardType" @change="changeCardType" value-key="value"
          placeholder="请选择卡类型" :disabled="cardInfoList.length > 0" style="width: 160px">
          <el-option v-for="item in relateCardTLits" :key="item.value" :label="item.name" :value="item.value"></el-option>
        </el-select>
        <span v-if="relateCardType == ICARD_STATE.abPaperTwo" style="color: red; font-size: 14px; margin-left: 10px;">请确保
          A、B 卷总分值一致，否则无法进行学情分析！</span>
        <span v-if="relateCardType == ICARD_STATE.abCard"
          style="color: red; font-size: 14px; margin-left: 10px;">AB卡适用于同一份试卷，2张排版不同的答题卡！</span>
      </el-row>
      <!-- 关联答题卡 -->
      <!-- <el-row>
        <el-button type="primary" @click="chooseCard">关联答题卡</el-button>
      </el-row> -->
      <div class="card-content">
        <div class="nodata flex_1"
          v-if="!loading && (cardInfoList.length == 0 || ((relateCardType == ICARD_STATE.abPaperTwo || relateCardType == ICARD_STATE.abCard) && cardInfoList.length < 2))">
          <el-row class="relate-btn">
            <template v-if="relateCardType == ICARD_STATE.abPaperTwo">
              <div style="margin-bottom: 5px;" v-for="(text, index) in ['A', 'B']" :key="index">
                <template v-if="!cardInfoList.find(card => card?.abCardSheetType == index)">
                  <el-button type="primary" @click="chooseCard(text)" style="width: 135px">关联答题卡（{{ text }}）</el-button>
                  <el-button type="warning" @click="createCard(text)" style="width: 135px">新建答题卡（{{ text }}）</el-button>
                  <template v-if="thirdCardState == 1">
                    <el-button type="success" @click="createThirdCard(text)" style="width: 132px">创建{{ text
                    }}卷三方卡</el-button>
                  </template>
                </template>
              </div>
            </template>
            <template v-else-if="relateCardType == ICARD_STATE.abCard">
              <el-button type="primary" @click="chooseCard(null)" style="width: 120px">关联答题卡</el-button>
              <el-button type="warning" v-if="!cardInfoList.length" @click="createCard(null)"
                style="width: 120px">新建答题卡</el-button>
              <template v-if="thirdCardState == 1">
                <template v-for="(text, index) in ['A', 'B']">
                  <template v-if="!cardInfoList.find(card => card?.abCardSheetType == index)">
                    <el-button type="success" @click="createThirdCard(text)"
                      style="width: 135px">创建三方卡（{{ text }}）</el-button>
                  </template>
                </template>
              </template>
            </template>
            <template v-else>
              <el-button type="primary" @click="chooseCard('A')" style="width: 120px">关联答题卡</el-button>
              <el-button type="warning" @click="createCard('A')" style="width: 120px">新建答题卡</el-button>
              <template v-if="thirdCardState == 1">
                <el-button type="success" @click="createThirdCard" style="width: 120px">创建三方卡</el-button>
              </template>
            </template>
          </el-row>
          <template v-if="cardInfoList.length == 0">
            <img :src="noResImg" alt="" />
            <p class="text-center">暂未关联答题卡，请先关联答题卡</p>
          </template>
        </div>
        <template v-if="!loading && cardInfoList.length > 0">
          <div v-for="cardInfo in cardInfoList" :key="cardInfo.id">
            <el-row class="info-row">
              <span class="info-title">
                <template v-if="relateCardType == ICARD_STATE.abPaperTwo || relateCardType == ICARD_STATE.abPaper">
                  {{ cardInfo?.abCardSheetType == IAB_CARD_SHEEFT_TYPE.aCard ? 'A卷模板：' : 'B卷模板：' }}
                </template>
                <template v-else>答卷模板：</template>
              </span><span class="info-content">{{ cardInfo.name
              }}</span>

              <!-- 更多操作关联题卡：教辅不需要操作 -->
              <el-dropdown style="margin-left: 15px" v-if="cardInfo.cardType != 6">
                <span class="el-dropdown-link"> 更多操作 </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :disabled="disabledButton" @click.native="cancelRelate(cardInfo)">
                    取消关联</el-dropdown-item>
                  <template v-if="cardInfo.originCardType != 3">
                    <el-dropdown-item @click.native="downloadPaper(cardInfo)">下载试卷</el-dropdown-item>
                    <el-dropdown-item @click.native="refreshPaper('refresh_points', cardInfo)">刷新定位点</el-dropdown-item>
                    <el-dropdown-item @click.native="refreshPaper('refresh_pdf', cardInfo)">刷新pdf</el-dropdown-item>
                  </template>
                </el-dropdown-menu>
              </el-dropdown>

            </el-row>
            <el-row class="info-row"><span class="info-title">答卷预览：</span><span class="info-content">{{
              cardInfo.pageLayout == IPAGELAYOUT.A4
              ? 'A4'
              : cardInfo.pageLayout == IPAGELAYOUT.A3
                ? 'A3两栏'
                : cardInfo.pageLayout == IPAGELAYOUT.A33
                  ? 'A3三栏'
                  : cardInfo.pageLayout == IPAGELAYOUT.A32
                    ? '正3反2'
                    : cardInfo.pageLayout == IPAGELAYOUT.A23
                      ? '正2反3'
                      : '暂无布局'
            }}，{{ cardInfo.correctType == ICORRECT_TYPES.HAND ? '手阅' : '网阅' }}，总题数{{ cardInfo.qCount }}题</span>
            </el-row>
            <!-- <el-button type="text" @click="downloadPaper">下载试卷</el-button> -->
            <el-row class="info-row" v-if="cardInfo.paperNum && cardInfo.paperNum > 0" v-loading="loadingImg">
              <div class="img-container">
                <div class="img-list" v-for="(img, index) in cardInfo.imgList" :key="index">
                  <el-image class="img" :style="drawImgStyles(cardInfo)" :src="img" :preview-src-list="cardInfo.imgList">
                  </el-image>
                  <p>第 {{ index + 1 }} 页</p>
                </div>
              </div>
            </el-row>
            <no-data v-else text="暂无可预览图片"></no-data>
          </div>
        </template>
      </div>
      <!--关联答题卡弹框-->
      <select-card ref="selectCardRef" v-if="modalVisible" :modalVisible="modalVisible" :examId="examId"
        :examName="examName" :personBookId="personBookId" :subjectId="subjectId" :gradeId="gradeCode" :source="source"
        :examInfo="examInfo" :abCardType="abCardType" :relateCardType="relateCardType" :abCardSheetType="abCardSheetType"
        @confirm-sure="confirmSure" @confirm-cancel="confirmCancel"></select-card>
      <!--下载试卷弹窗-->
      <downloadDialog :downItem="downItem" v-if="showDialog" @close="closeDialog"> </downloadDialog>
      <!-- 创建三方卡弹框 -->
      <create-third-card-dialog v-if="showThirdCardDialog" :dialogVisible="showThirdCardDialog"
        @close-dialog="closeThirdCardDialog" @confirm-create="confirmCreateThirdCard">
      </create-third-card-dialog>
      <scoring-mode-dialog v-if="showScoringModeDialog" :dialogVisible="showScoringModeDialog" :workId="workId"
        @close-dialog="showScoringModeDialog = false">
      </scoring-mode-dialog>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import BreadCrumbs from '@/components/Breadcrumbs/index.vue';
import {
  getExamSubject,
  getExamInfoAPI,
  cancelRelateAnswerCard,
  getExamSubjectMulti,
} from '@/service/pexam';
import { getViewPaper, saveTestBanks } from '@/service/testbank';
import selectCard from './modules/selectCard';
import downloadDialog from '@/components/paper/downloadDialog';
import { getQueryString } from '@/utils';
import { getToken } from '@/service/auth';
import { saveBankJSONAPI } from '@/service/api';
import NoData from '@/components/noData.vue';
import { drawImgStyle, getImgList, getImgUrl } from '@/utils/common';
import { getPaperImgMark, refreshPaperInfo } from '@/service/xueban';
import CreateThirdCardDialog from './modules/createThirdCardDialog.vue';
import { ICARD_TYPE, ICARD_STATE, IAB_CARD_TYPE, ISOURCE_TYPES, ICORRECT_TYPES, IPAGELAYOUT, IAB_CARD_SHEEFT_TYPE } from '@/typings/card';
import UserRole from '@/utils/UserRole';
import ScoringModeDialog from './modules/scoringModeDialog.vue';

export default {
  name: 'answer-setting-index',
  data() {
    return {
      ICARD_STATE,
      IAB_CARD_TYPE,
      IAB_CARD_SHEEFT_TYPE,
      ISOURCE_TYPES,
      ICORRECT_TYPES,
      IPAGELAYOUT,
      //考试id
      examId: 0,
      //考试名称
      examName: '',
      //试卷编号
      paperNo: '',
      //学科Id
      subjectId: '',
      //学科名称
      subjectName: '',
      subjectRealName: '',
      //年级编码
      gradeCode: '',
      //个册id
      personBookId: '',
      //试卷类别
      source: ISOURCE_TYPES.HAND,
      //已关联题卡id
      testBankId: '',
      workId: '',
      //加载中状态
      loading: false,
      cardInfoList: [],
      //暂无数据缺省图
      noResImg: require('../../../assets/exam/unrelate-card.png'),
      //是否显示关联答题卡
      modalVisible: false,
      //考试详情
      examInfo: {},
      //学科考试信息
      subjectExamInfo: {},
      showDialog: false,
      downItem: {},
      fsUrl: process.env.VUE_APP_FS_URL,
      //制卡地址
      cardUrl: process.env.VUE_APP_CARDURL,
      abCardUrl: process.env.VUE_APP_ABCARDURL,
      //当前进度
      progress: getQueryString('progress') || '',
      progressState: getQueryString('progressState') || '',
      //考试创建者id
      createUserId: getQueryString('createUserId') || '',
      //图片列表
      imgList: [],
      loadingImg: false,
      showThirdCardDialog: false,
      //三方卡权限
      thirdCardState: 0,
      //关联答题卡类型
      relateCardType: ICARD_STATE.default,
      //卡类型列表
      relateCardTLits: [
        {
          name: '标准卡（1卷1卡）',
          value: ICARD_STATE.default
        },
        {
          name: 'AB卡（1卷2卡）',
          value: ICARD_STATE.abCard
        },
        {
          name: 'AB卷（2卷1卡）',
          value: ICARD_STATE.abPaper
        },
        {
          name: 'AB卷（2卷2卡）',
          value: ICARD_STATE.abPaperTwo
        }
      ],
      //AB卡类型 '0':普通卡 '1':AB卡 '2':AB卷
      abCardType: IAB_CARD_TYPE.default,
      abCardTypeText: '', // A , B
      abCardSheetType: '',
      showScoringModeDialog: false
    };
  },
  components: {
    BreadCrumbs,
    selectCard,
    downloadDialog,
    NoData,
    CreateThirdCardDialog,
    ScoringModeDialog
  },
  computed: {
    ...mapGetters([]),
    disabledButton() {
      //校管、运营、考试创建者并且扫描答卷之前可以删除
      return !(
        (UserRole.isSchoolLeader ||
          UserRole.isOperation ||
          this.$sessionSave.get('loginInfo').id == this.createUserId) &&
        ((this.source == ISOURCE_TYPES.WEB && this.progress < 4) ||
          (this.source == ISOURCE_TYPES.HAND && this.progress < 3)));
    },
    //是否综合学科
    isCompositeSubject() {
      return this.subjectId.indexOf('-') > -1 || this.examInfo?.comprehensiveSubjectIds?.split(/,|-/).includes(this.subjectId);
    }
  },
  created() { },
  async mounted() {
    this.init();
  },
  methods: {
    /**
     * @name：初始化页面参数
     */
    async init() {
      //初始化页面参数
      this.initData();
      //根据考试id获取考试详情
      await this.getExamInfo();
      this.subjectExamInfo = this.examInfo.pBookList.find(item => item.workId == this.workId);
      if (!this.subjectExamInfo) {
        return;
      }
      this.relateCardType = this.subjectExamInfo.relateCardType;
      this.abCardType = this.subjectExamInfo.abCardType;
      let tbIds = [];
      if (this.relateCardType == ICARD_STATE.abPaperTwo || this.relateCardType == ICARD_STATE.abCard) {
        if (this.subjectExamInfo.aTestBankId) {
          tbIds.push(this.subjectExamInfo.aTestBankId);
        }
        if (this.subjectExamInfo.bTestBankId) {
          tbIds.push(this.subjectExamInfo.bTestBankId);
        }
      } else {
        tbIds.push(this.subjectExamInfo.testBankId);
      }

      for (const tbid of tbIds) {
        //获取已关联答题卡信息
        await this.getCardInfo(tbid);
      }
    },
    initData() {
      let data = this.$route.query;
      this.examId = Number(data.examId) || 0;
      this.examName = data.examName || '';
      this.subjectId = data.subjectId || '';
      this.subjectName = data.subjectName || '';
      this.subjectRealName = data.subjectRealName || '';
      this.gradeCode = data.gradeCode || '';
      this.personBookId = data.personBookId || '';
      this.source = Number(data.source) || ISOURCE_TYPES.HAND;
      this.testBankId = data.testBankId || '';
      this.workId = data.workId || '';
      this.thirdCardState = this.$sessionSave.get('thirdCardState');
      this.cardInfoList = [];
    },
    /**
     * @name: 获取题卡详情
     */
    async getCardInfo(tbid) {
      if (tbid) {
        let result = await getViewPaper({ id: tbid });
        if (
          result &&
          result.code == 1 &&
          (result.data.isOverCard == 1 || result.data.workNum > 0)
        ) {
          await this.getImgUrls(result.data);
          if (this.relateCardType == ICARD_STATE.abPaperTwo) {
            if (result.data.aTestBankId == tbid) {
              result.data.abCardSheetType = IAB_CARD_SHEEFT_TYPE.aCard;
            }
            if (result.data.bTestBankId == tbid) {
              result.data.abCardSheetType = IAB_CARD_SHEEFT_TYPE.bCard;
            }
          }
          this.cardInfoList.push(result.data);
        }
      }
    },
    /**
     * @name: 根据试卷id获取试卷信息
     */
    async getExamInfo() {
      let result = await getExamInfoAPI({ examId: this.examId });
      if (result && result.code == 1) {
        this.examInfo = result.data;
      }
    },
    changeCardType(type) {
      if (type == ICARD_STATE.abCard) {
        this.abCardType = IAB_CARD_TYPE.abCard;
      } else if (type == ICARD_STATE.abPaper) {
        this.abCardType = IAB_CARD_TYPE.abPaper;
      } else {
        this.abCardType = IAB_CARD_TYPE.default;
      }
    },
    /**
     * @name: 打开关联答题卡弹框
     */
    chooseCard(abCardTypeText) {
      if (!abCardTypeText) {
        abCardTypeText = this.subjectExamInfo?.paperNo ? 'B' : 'A'
      }
      this.abCardTypeText = abCardTypeText;
      this.abCardSheetType = abCardTypeText === 'B' ? IAB_CARD_SHEEFT_TYPE.bCard : IAB_CARD_SHEEFT_TYPE.aCard;
      this.modalVisible = true;
    },
    // 创建卡片参数构建
    buildCardParams(abCardTypeText) {
      const loginInfo = this.$sessionSave.get('loginInfo')
      return {
        tbName: `${this.examName}${this.subjectRealName}答题卡`,
        schoolId: this.$sessionSave.get('schoolInfo').id,
        userId: loginInfo.id,
        userName: loginInfo.realname,
        sCode: this.subjectId.indexOf('-') != -1 ? this.subjectId.replaceAll('-', ',') : this.subjectId,
        sName: this.subjectRealName.replaceAll('-', ','),
        correctType: this.source === ISOURCE_TYPES.HAND ? ICORRECT_TYPES.HAND : ICORRECT_TYPES.WEB,
        ...this.getAbCardParams(abCardTypeText)
      }
    },

    // AB卡相关参数
    getAbCardParams(abCardTypeText) {
      const params = {}
      if (this.relateCardType === ICARD_STATE.abCard) {
        params.abCardType = IAB_CARD_TYPE.abCard;
      } else if (ICARD_STATE.abPaper === this.relateCardType) {
        params.abCardType = IAB_CARD_TYPE.abPaper
      } else {
        params.abCardType = IAB_CARD_TYPE.default;
      }
      params.abCardSheetType = abCardTypeText === 'B' ? IAB_CARD_SHEEFT_TYPE.bCard : IAB_CARD_SHEEFT_TYPE.aCard;
      return params
    },
    async createCard(abCardTypeText) {
      if (!abCardTypeText) {
        abCardTypeText = this.subjectExamInfo?.paperNo ? 'B' : 'A'
      }
      let _this = this;
      let params = this.buildCardParams(abCardTypeText);
      saveTestBanks(params)
        .then(function (result) {
          if (result && parseInt(result.code) == 1) {
            if (result.data && result.data.tbInfo && result.data.tbInfo.tbId) {
              _this.testBankId = result.data.tbInfo.tbId;
              let obj = {
                id: _this.testBankId,
                tbName: _this.examName,
                abCardType: params.abCardType,
                cardType: 0,
                isCreate: true,
                subjectId: _this.subjectId,
                correctType: params.correctType, //网阅、手阅
                abCardSheetType: params.abCardSheetType
              }
              if (obj.abCardType == IAB_CARD_TYPE.abCard) {
                obj.abCardId = [_this.testBankId, result.data.tbInfo1.tbId].join(',');
              }
              _this.goCreateCard(obj);
            }
          } else {
            _this.$message.error(result.msg);
          }
        })
        .catch(function (error) {
        });
    },
    /**
     * @name: 去制卡且关联答题卡
     */
    goCreateCard(item) {
      if (item.isUse == 0 && item.cardType == 1) {
        return;
      }
      let token = getToken();
      let routeData =
        `&examName=${item.tbName}&cardType=${item.cardType}&correctType=${item.correctType}&relateCard=true&relateCardType=${this.relateCardType}&abCardSheetType=${item.abCardSheetType}&examId=${this.examId}&personBookId=${this.personBookId}&subjectId=${item.subjectId}&token=${token}`;
      if (item.abCardType == IAB_CARD_TYPE.abCard) {
        routeData =
          this.abCardUrl +
          `?id=${item.abCardId}` + routeData;
      } else {
        routeData =
          this.cardUrl +
          `?id=${item.id}` + routeData;
      }
      window.open(routeData, '_blank');
      if (item.isCreate) {
        this.$confirm('是否制卡完成，刷新数据', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.init()
          })
          .catch(() => { });
      }
    },
    /**
     * 关闭关联答题卡弹框
     */
    confirmCancel() {
      this.modalVisible = false;
    },
    /**
     * 确认选择
     */
    async confirmSure(ids) {
      this.confirmCancel();
      this.init()
      // setTimeout(() => {
      //   this.saveWorkJSON();
      // }, 300);
    },
    closeDialog() {
      this.showDialog = false;
    },
    // 点击下载试卷显示下载弹窗
    downloadPaper(cardInfo) {
      //   if (item.dlStatus !== 1) return;
      this.downItem = {
        id: cardInfo.tbId,
        fileUrl: cardInfo.tbFileUrl || '',
        tbName: cardInfo.name,
        schoolId: cardInfo.schoolId,
        isUse: cardInfo.isUse,
        dlStatus: cardInfo.scanStatus,
        paperNo: cardInfo.paperNo,
      };
      this.showDialog = true;
    },
    /**
     * @name:刷新图片信息
     * @param {*} type
     */
    async refreshPaper(type, cardInfo) {
      const params = {
        paper_no: cardInfo.paperNo,
        refresh_points: type == 'refresh_points',
        refresh_pdf: type == 'refresh_pdf',
      };
      const res = await refreshPaperInfo(params);
      if (res.code == 1) {
        this.$message({
          message: '刷新成功！',
          type: 'success',
          duration: 800,
        });
        setTimeout(() => {
          this.getImgUrls(cardInfo);
        }, 300);
      }
    },
    /**
     * @name:获取生成的pdf图片
     */
    async getImgUrls(cardInfo) {
      this.loadingImg = true;
      const params = {
        paper_no: cardInfo.paperNo,
        show_answer: false,
      };
      const res = await getPaperImgMark(params);
      if (res.code == 1) {
        cardInfo.imgList = res.data;
      } else {
        cardInfo.imgList = [];
      }
      this.loadingImg = false;
    },
    drawImgStyles(cardInfo) {
      return drawImgStyle(cardInfo);
    },
    /**
     * @name:取消关联答题卡
     */
    async cancelRelate(cardInfo) {
      this.$confirm('取消关联将同步删除该学科所有数据（阅卷设置、扫描等），确定取消关联吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        this.sureCancelRelate(cardInfo);
      });
    },
    /**
     * @name:确定删除关联
     */
    async sureCancelRelate(cardInfo) {
      let params = {
        personalBookId: this.personBookId || "",
        schoolId: cardInfo.schoolId,
        abCardSheetType: cardInfo.abCardSheetType,
        markingType: this.source == ISOURCE_TYPES.HAND ? 1 : 2,
      };
      const res = await cancelRelateAnswerCard(params);
      if (res && res.code == 1) {
        this.init();
      }
    },
    /**
     * @name: 打开创建三方卡弹窗
     * @param {*}
     */
    createThirdCard(abCardTypeText) {
      this.abCardTypeText = abCardTypeText;
      this.showThirdCardDialog = true;
    },
    /**
     * @name: 关闭创建三方卡弹窗
     * @param {*}
     */
    closeThirdCardDialog() {
      this.showThirdCardDialog = false;
    },
    /**
     * @name: 确定创建三方卡
     * @param {*}
     */
    confirmCreateThirdCard(data) {
      this.showThirdCardDialog = false;
      console.log(data);
      this.saveThirdCard(data);
    },
    /**
     * @name:保存三方卡
     */
    async saveThirdCard(data) {
      let params = this.buildCardParams(this.abCardTypeText);
      if (params.abCardType == IAB_CARD_TYPE.abCard) {
        params.tbName = `${this.examName}${this.subjectRealName}${this.abCardTypeText}答题卡`;
      }
      params.pageType = data.thirdCardInfo.pageType; //页面类型 0:双面 1:单面
      params.pageLayout = data.thirdCardInfo.size == 'A4' ? IPAGELAYOUT.A4 : data.thirdCardInfo.pageLayout;
      params.cardType = ICARD_TYPE.THIRD;
      params.correctType = ICORRECT_TYPES.WEB;
      this.subjectExamInfo?.aTestBankId && (params.aTestBankId = this.subjectExamInfo.aTestBankId);
      this.subjectExamInfo?.bTestBankId && (params.bTestBankId = this.subjectExamInfo.bTestBankId);
      params.thirdPartyCardImg = JSON.stringify(data.imgUrl);
      const result = await saveTestBanks(params);
      if (result.code == 1) {
        this.$router.push({
          path: '/home/<USER>',
          query: {
            tbId: result.data.tbInfo.tbId,
            paperNo: result.data.tbInfo.paperNo,
            examId: this.examId,
            personalBookId: this.personBookId,
            schoolId: params.schoolId,
            abCardType: params.abCardType,
            abCardSheetType: params.abCardSheetType,
            relateCardType: this.relateCardType
          },
        });
        console.log(result.data);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.exam-page-containter {
  background: #fff;
  padding: 20px;
  min-height: calc(100% - 40px);
  border-radius: 2px;

  .exam-title {
    font-size: 18px;
    font-weight: bold;
    margin-right: 20px;
    color: #161e26;
  }

  .card-header {
    margin-bottom: 20px;
  }

  .card-content {}

  .nodata {
    margin-top: 80px;
  }

  .text-center {
    color: #648093;
    font-size: 18px;
  }

  .relate-btn {
    margin-top: -60px;
  }

  .info-title {
    color: #303233;
    font-size: 16px;
  }

  .info-content {
    font-size: 16px;
    color: #606266;
  }

  .info-row {
    line-height: 40px;
    margin-bottom: 10px;
  }

  .img-container {
    display: flex;
    flex-wrap: wrap;

    .img-list {
      margin: 10px 10px;

      .img {
        border: 1px solid #ebeef1;
      }

      p {
        text-align: center;
        margin-top: -15px;
      }
    }
  }
}
</style>
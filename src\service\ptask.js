/*
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2024-05-16 14:40:00
 * @LastEditors: 小圆
 * @LastEditTime: 2025-02-11 11:34:24
 */
import * as httpApi from "./index";
import { covertQuesList } from "./pbook";

const kklUrl = process.env.VUE_APP_KKLURL;

/**
 * api转换
 * @type {{POST: API.POST, GET: API.GET,POSTSTRING: API.POSTSTRING}}
 */
const API = {
  GET: function (url, params) {
    return httpApi.GET(url, params, kklUrl);
  },
  POST: function (url, params) {
    return httpApi.POST(url, params, kklUrl);
  },
  POSTJson: function (url, params) {
    return httpApi.POSTJson(url, params, kklUrl);
  },
  POSTSTRING: function (url, params) {
    return httpApi.POSTSTRING(url, params, kklUrl);
  },
};

// 获取题面详情
export const GetSubjectList = (params) => {
  return API.POST("/ptask/extra/subjectList", params);
};

//获取错因列表
export function getAllCauseError(data) {
  return API.POST("/pbook/cause/getAllCauseError", data);
}

//纠错吐槽接口
export function setDebunk(data) {
  return API.POST("/pbook/pushQuestionTask/setDebunk", data);
}

// 题目标注
export function quesMark(data) {
  return API.POST("/ptask/baseQuestion/quesMark", data);
}

// 查询题目难度等标注信息
export function findDictionaryInfo(data) {
  return API.POST("/ptask/baseDictionary/findDictionaryInfo", data);
}

// 获取过滤条件
export function getFilterCondition(data) {
  return API.POSTSTRING("/ptask/pushQuestionTask/getFilterCondition", data);
}

//根据章节选题知识点选题查询题库
export function quesSearch(data) {
  return new Promise((resolve) => {
    resolve({});
  });
}
// 获取题目列表
export function getQuestions(data) {
  return API.GET("/ptask/_/questions", data);
}
// 获取题目列表
export function getPersonBookQues(data) {
  return API.GET("pbook/_/getPersonBookQues", data);
}
// 绑定大精题目
export function bindStatQuesNo(data) {
  return API.POST("/ptask/baseQuestion/bindStatQuesNo", data);
}
// 获取已经绑定的大精题目
export function getQueBindNo(data) {
  return API.GET("/ptask/baseQuestion/getQueBindNo", data);
}
// 获取知识点关联资源
export function listResAPI(data) {
  return API.GET("/ptask/pointResourceRelation/listRes", data);
}
// 查询个册es中相似题
export function findLikeQues(data) {
  return new Promise((resolve,reject) => {
    API.POST('/ptask/search/findLikeQuesNew', data).then(async res => {
      await covertQuesList(res.data.rows);
      resolve(res);
    }).catch(reject);
  });
}

// 获取教材章节目录
export function getTeachingCatalogAPI(data) {
  return API.GET("/ptask/teachingProgress/getTeachingCatalog", data);
}
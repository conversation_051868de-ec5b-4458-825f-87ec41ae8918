<template>
  <div class="limit-stu-box">
    <div class="header-box">
      <div class="titleLine">临界生分布</div>
      <el-button type="primary" @click="exportTable">导出</el-button>
    </div>
    <div class="table-box" v-loading="isLoading">
      <el-table
        v-if="tableData.length > 0"
        :data="tableData"
        stripe
        :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
        style="width: 100%"
        v-drag-table
        v-sticky-table="0"
      >
        <el-table-column align="center" prop="" label="" fixed min-width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.clsName }}</span>
          </template>
        </el-table-column>
        <el-table-column v-for="(item, index) in lines" prop="" :label="item" align="center" :key="index">
          <el-table-column label="上临界" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.data[index].up }}</span>
            </template>
          </el-table-column>
          <el-table-column label="占比" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.data[index].upRate }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="下临界" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.data[index].down }}</span>
            </template>
          </el-table-column>
          <el-table-column label="占比" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.data[index].downRate }}%</span>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
      <div v-if="!tableData.length && !isLoading" class="nodata">
        <img :src="noResImg" alt="" />
        <p class="text-center">暂无数据</p>
      </div>
    </div>
  </div>
</template>

<script>
import NumericInput from '@/components/NumericInput';
import { SchoolSettingType } from '@/pages/schoolSetting/types';
import { getCriticalConfAPI, getSchCfgAPI } from '@/service/pexam';
import UserRole from '@/utils/UserRole';

export default {
  name: 'limitStu',
  components: {
    NumericInput,
  },
  props: ['filterData', 'targetData'],
  data() {
    return {
      isLoading: false,
      tableData: [],
      lines: [],
      noResImg: require('@/assets/no-res.png'),
    };
  },
  watch: {
    filterData: {
      deep: true,
      handler(newValue, oldValue) {
        if (!newValue) return;
        this.updateFilter();
      },
    },
  },
  async mounted() {
    this.updateFilter();
  },
  methods: {
    // 获取到班级和学科后更新
    async updateFilter(data) {
      this.getCriticalData();
    },

    /**
     * @name:获取临界生数据
     */
    async getCriticalData() {
      this.tableData = [];
      try {
        const res = await getCriticalConfAPI({
          examId: this.$sessionSave.get('reportDetail').examId,
          subjectId: this.filterData.subjectId,
          qType: this.filterData.qType,
        });
        this.lines = res.data.lines;
        let classList = this.$sessionSave.get('innerClassList');
        let data = [];
        classList.forEach(cls => {
          let item = res.data.data.find(item => item.clsId == cls.id);
          if (item) {
            data.push(item);
          }
        });
        this.tableData = data;
      } catch (error) {
        console.error(error);
        this.tableData = [];
      }
    },

    // 导出
    async exportTable() {
      const { examId, v } = this.$sessionSave.get('reportDetail');
      let role = '';
      if (!UserRole.isOperation) {
        const { year, campusCode } = this.$sessionSave.get('reportDetail');
        const map = await UserRole.utils.getRoleSubjectClassMap(year, campusCode, this.$sessionSave.get('reportType') == 'school' ? true : false);
        role = JSON.stringify(map);
      }
      const params = {
        examId,
        qType: this.filterData.qType,
        role,
        v,
      };
      const urlSearch = new URLSearchParams(params);
      const path = '/pexam/_/exp-crisis';
      let url = process.env.VUE_APP_KKLURL + path + `?${urlSearch.toString()}`;
      window.open(url);
    },
  },
};
</script>

<style lang="scss" scoped>
@import '../../../styles/base.css';

.header-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.limit-stu-box {
  width: 100%;
  // height: 400px;
  background: #ffffff;
  // box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
  border-radius: 6px;
  border-top: 1px solid transparent;

  .subjects-box {
    margin: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e4e8eb;
    .subject-label {
      color: var(--normal-color);
    }
    .subject-list {
      display: inline-flex;

      .subject-item {
        color: var(--normal-color);
        cursor: pointer;
        &.active {
          color: var(--primary-color);
        }
      }
    }
  }
  .opt-box {
    .limit-score {
      margin-right: 40px;
    }
    .float-score {
      margin-right: 20px;
    }
  }
  .limit-score,
  .float-score {
    display: inline-block;
  }
  .numeric-input-inner {
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    box-sizing: border-box;
    color: #606266;
    display: inline-block;
    font-size: inherit;
    height: 30px;
    line-height: 30px;
    text-align: center;
    vertical-align: middle;
    outline: none;
    padding: 0 5px;
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    width: 100%;
    &:focus {
      outline: none;
      border-color: #008dea;
      position: relative;
      z-index: 10;
    }
  }
  .table-box {
    // border: 1px solid #e4e8eb;
    // border-bottom: none;
    // margin-top: 20px;
  }
}
.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;
  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 15px;
  }
}
</style>
<style lang="scss">
.limit-stu-box {
  .opt-box {
    .numeric-input-box {
      width: 60px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
    }
  }
  .el-table {
    .click-item {
      color: #409eff;
      display: inline-block;
      min-width: 60px;
      cursor: pointer;
      &.not-click {
        pointer-events: none;
      }
    }
  }
  .limit-stu-dialog {
    .el-dialog__header {
      background-color: #fff;
    }
    .stu-list-box {
      padding: 0 30px;
    }
    .el-pagination {
      text-align: center;
      margin-top: 20px;
    }
  }
}
</style>

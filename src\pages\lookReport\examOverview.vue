<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-04-23 17:01:19
 * @LastEditors: 小圆
-->
<template>
    <div class="examOverview clearfix">
      <headerFilter
        @updateFilter="updateFilter"
        @init="initFilter"
        ref="headerFilter"
        class="header-filter"
      ></headerFilter>
      <router-view
        v-if="isInitFilter"
        class="overview-router"
        :class="{ desktop: isDesktop }"
        ref="overviewRouter"
        :filterData="filterData"
      ></router-view>
      <el-skeleton style="padding: 20px" :rows="7" animated v-else />
    </div>
</template>

<script>
import headerFilter from '@/components/headerFilter.vue';
import { getConfAPI } from '@/service/pexam';
import { deepClone } from '@/utils';

export default {
  name: 'exam-overview',
  components: {
    headerFilter,
  },
  data() {
    return {
      // 是否初始化头部筛选
      isInitFilter: false,
      filterData: null,
      examSubjectList: [],
    };
  },
  computed: {
    isDesktop() {
      return this.$route.path.includes('/dReport');
    },
  },
  async mounted() {
  },
  methods: {
    async initFilter({ filterData }) {
      this.filterData = deepClone(filterData);
      this.isInitFilter = true;
    },

    updateFilter(data) {
      this.filterData = deepClone(data);
    },
  },
};
</script>

<style lang="scss" scoped>
.radioGroup {
  margin: 0 auto 10px;
}

.header-filter {
  padding-top: 20px;
  padding-left: 24px;
  padding-right: 24px;
}

.overview-router {
  padding: 24px;
  padding-top: 0;
  height: 100%;

  &.desktop {
    overflow: hidden;
    overflow-y: auto;
  }
}
</style>

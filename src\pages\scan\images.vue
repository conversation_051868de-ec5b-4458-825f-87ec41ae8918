<template>
  <div class="container">
    <el-page-header @back="goBack" content="查看详情"> </el-page-header>
    <div style="display: flex; height: 100%">
      <div class="stu-list">
        <el-tabs
          v-model="activeName"
          :stretch="true"
          @tab-click="handleClick"
          class="image-tabpane"
        >
          <el-tab-pane label="按学生" name="stu">
            <!-- 筛选项 -->
            <div class="task-info">
              <el-select
                v-show="!showInput"
                v-model="classId"
                @change="classChange"
                placeholder="请选择班级"
              >
                <el-option
                  v-for="item in classList"
                  :key="item.id"
                  :label="item.class_name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
              <el-input
                v-model="searchStuInfo"
                v-show="showInput"
                @blur="hideInput"
                ref="searchInput"
                suffix-icon="el-icon-search"
                clearable
                @change="debouncedSearchStu"
                @keyup.enter.native="debouncedSearchStu"
              ></el-input>
              <div
                v-show="!showInput"
                class="search__icon el-icon-search"
                @click="showInputBox"
              ></div>
            </div>
            <!-- 学生列表 -->
            <div class="task-list">
              <el-table
                :data="stuList"
                ref="stuTable"
                highlight-current-row
                @current-change="handleCurrentStuChange"
                v-load-more.expand="{
                  func: loadStuMore,
                  target: '.el-table__body-wrapper',
                  distance: 20,
                  delay: 100,
                }"
                :load-more-disabled="false"
                style="width: 100%"
                :height="tableHeight"
              >
                <el-table-column label="姓名" width="100">
                  <template slot-scope="scope">
                    <span>
                      <p class="stu-no">
                        {{ scope.row.realName }}
                      </p>
                      <span class="stu-task-name">{{ scope.row.className }}</span>
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="page" label="考号" width="200">
                  <template slot-scope="scope">
                    {{ scope.row.stuNumber }}
                  </template>
                  <template slot-scope="scope">
                    <template v-if="scope.row.isEdit">
                      <el-autocomplete
                        popper-class="edit-stu-no-autocomplete"
                        v-model="tempStuNo"
                        :fetch-suggestions="querySearchStu"
                        @select="handleSelect"
                        placeholder="请输入学生姓名或考号"
                      >
                      <template slot-scope="{ item }">
                        <div class="name">{{ item.value }}</div>
                        <span class="class-name">{{ item.className }}</span>
                      </template>
                    </el-autocomplete>
                      <el-button
                        type="text"
                        class="stu-no-edit"
                        size="small"
                        @click="submitEditStuNo(scope.row)"
                      >
                        提交
                      </el-button>
                    </template>
                    <span v-else>
                      <p class="stu-no">
                        {{ scope.row.stu_no == '' ? 'xxxxxxxx' : scope.row.stuNumber }}
                        <span class="stu-no-edit" @click.stop="editCurrentNo(scope.row)"
                          ><i class="el-icon-edit"></i
                        ></span>
                      </p>
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
          <el-tab-pane label="按图片" name="img">
            <div class="task-info">
              <!-- <span class="task-info-count">共{{ stuCount }}人</span> -->
              <el-select
                v-show="!showInput"
                v-model="taskValue"
                @change="taskChange"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in taskList"
                  :key="item.id"
                  :label="item.batchId"
                  :value="item.id"
                >
                </el-option>
              </el-select>
              <el-input
                v-model="searchStuno"
                v-show="showInput"
                @blur="hideInput"
                ref="searchInput"
                suffix-icon="el-icon-search"
                clearable
                @change="searchImage"
                @keyup.enter.native="searchImage"
              ></el-input>
              <div
                v-show="!showInput"
                class="search__icon el-icon-search"
                @click="showInputBox"
              ></div>
            </div>
            <div class="task-list">
              <el-table
                :data="tableData"
                ref="taskTable"
                highlight-current-row
                @current-change="handleCurrentChange"
                v-load-more.expand="{
                  func: loadMore,
                  target: '.el-table__body-wrapper',
                  distance: 20,
                  delay: 100,
                }"
                :load-more-disabled="false"
                style="width: 100%"
                :height="tableHeight"
              >
                <el-table-column type="index" :index="indexMethod"> </el-table-column>
                <el-table-column label="考号" width="200">
                  <template slot-scope="scope">
                    <template v-if="scope.row.isEdit">
                      <el-input v-model="tempStuNo" maxlength="20" size="small"></el-input>
                      <el-button
                        type="text"
                        class="stu-no-edit"
                        size="small"
                        @click="submitEditQues(currentRow, true)"
                      >
                        提交
                      </el-button>
                    </template>
                    <span v-else>
                      <p class="stu-no">
                        {{ scope.row.stu_no == '' ? 'xxxxxxxx' : scope.row.stu_no }}
                        <span class="stu-no-edit" @click.stop="editCurrentNo(scope.row)"
                          ><i class="el-icon-edit"></i
                        ></span>
                      </p>
                      <span class="stu-task-name"
                        >{{ getBatchName(scope.row) }}&nbsp;第{{ scope.row.idx }}张</span
                      >
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="page" label="页码" width="50">
                  <template slot-scope="scope">
                    {{ scope.row.idx_num }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <!-- 图片列表 -->
      <div class="stu-img-list" ref="stuImg">
        <template v-if="activeName == 'img'">
          <img-list
            ref="imgList"
            :currentRow="currentRow"
            :pointsData="pointsData"
            :stuTableData="[]"
            :choiceScoreMap="choiceScoreMap"
            v-if="currentRow != null"
          ></img-list>
        </template>
        <template v-else>
          <template v-for="(stuimg, index) in stuTableData">
            <div class="stu-total-score">
              <span>
                <span>总分：</span
                ><span style="font-weight: bold; font-size: 28px">{{ stuTotalScore }}</span>
              </span>
            </div>
            <img-list
              ref="imgList"
              :currentRow="stuimg"
              :pointsData="pointsData"
              :stuTableData="stuTableData"
              :key="index"
              :index="index"
              :choiceScoreMap="choiceScoreMap"
              @get-change-row="onGetChangeRow"
            ></img-list>
          </template>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
var dom;
var observer;
import {
  getScanPaperDataAPI,
  getBatchListAPI,
  saveScanPageAPI,
  changeStuNoAPI,
  getStuScanDataAPI,
  getScanTaskList,
  getTaskImages,
} from '@/service/pexam';
import NoData from '@/components/noData';
import PointsBox from '@/components/scan/PointsBox.vue';
import ImgList from '@/components/scan/ImgList.vue';
import { getClassListByWorkIdAPI, getScanStuInfosAPI,searchStuInfoByWork } from '@/service/api';
import { getScanPaperPoints } from '@/service/testbank';
import { GetScoreMap } from '@/service/xueban';
import { _debounce } from '@/utils/index';
import { getQueryString } from '@/utils';

export default {
  name: 'scan-images',
  data() {
    return {
      examId: this.$route.query.examId,
      activeName: 'img',
      stuCount: 0,
      taskList: [],
      taskValue: '',
      tableData: [],
      currentRow: null,
      scale: 1,
      page: 1,
      size: 50,
      pageCount: 1,
      //答案坐标需要减去的宽度 mm
      subWidth: 18,
      tableHeight: 'calc(100% - 20px)',
      isDrawImg: false,
      rotate: 90,
      isEditExamNo: false,
      tempQuesInfo: {},
      //临时学号
      tempStuNo: '',
      //临时学生
      tempStu: {},
      // 批次名
      taskNames: [],
      //班级列表
      classList: [],
      classId: '',
      stuList: [],
      stu: {
        page: 1,
        pageCount: 1,
        pageSize: 50,
      },
      stuTableData: [],
      currentStuRow: [],
      //试卷号
      paperNo: '',
      //坐标点
      pointsData: {},
      //未改之前的学号
      oldStuNo: '',
      showInput: false,
      searchText: '',
      searchStuno: '',
      searchStuInfo: '',
      stuTotalScore: 0,
      //多选题得分规则
      choiceScoreMap: {},
      debouncedSearchStu: _.debounce(this.searchStu, 300),
      teacherId: getQueryString('teacherId') || '',
    };
  },
  components: { NoData, PointsBox, ImgList },
  async mounted() {
    this.taskValue = this.$route.query.taskId || '';
    await this.getBatchList();
    if (this.activeName == 'img') {
      this.loadImgInfo();
    } else {
      this.loadStuInfo();
    }
    await this.getScoreMap();
  },
  methods: {
    /**
     * @name:获取多选题得分规则
     */
    async getScoreMap() {
      GetScoreMap({ paper_no: this.paperNo })
        .then(res => {
          this.choiceScoreMap = res.data;
          console.log(this.choiceScoreMap);
        })
        .catch(err => {
          console.log(err);
        });
    },
    indexMethod(index) {
      return index + 1;
    },
    async getTaskImages() {
      if (!this.taskValue) return;
      let params = {
        taskId: this.taskValue,
      };
      const res = await getTaskImages(params);
      this.tableData = res.data;
      this.$refs.taskTable.setCurrentRow(this.tableData[0]);
    },
    async getScanPaperData() {
      if (!this.examId || this.examId === 'no_exam_id') {
        await this.getTaskImages();
        return;
      }
      let params = {
        examId: this.examId,
        taskId: this.taskValue,
        stuNo: this.searchStuno,
        page: this.page,
        size: this.size,
      };
      const res = await getScanPaperDataAPI(params);
      if (res.code == 1) {
        // this.searchStuno = "";
        if (this.page == 1) {
          this.tableData = res.data.rows;
          this.$refs.taskTable.setCurrentRow(this.tableData[0]);
        } else {
          this.tableData = this.tableData.concat(res.data.rows);
        }
        this.tableData.forEach(ques => {
          this.$set(ques, 'isEdit', false);
          ques.questions.forEach(item => {
            if(item.scoringMode == 1){
              this.$set(item, 'tempScore', item.total_score - item.score);
            }else{
              this.$set(item, 'tempScore', item.score);
            }
            if (!item.is_obj) {
              item.score_list &&
                item.score_list.forEach(ite => {
                  let scores = [];
                  ite.scores?.forEach(it => {
                    let isChoice = ite.score == it && item.miss_score != item.score ? true : false;
                    scores.push({ value: it, isChoice: isChoice });
                  });
                  //每一行的高度
                  let rowHeight = ite.pos[3] / (ite.rows||1);
                  const newArray = [];
                  for (let i = 0; i < scores.length; i += ite.cols) {
                    const subArray = scores.slice(i, i + ite.cols);
                    newArray.push(subArray);
                  }
                  let array1 = [];
                  newArray.forEach((everyRow, index) => {
                    let tempObj = {};
                    tempObj.pos = [
                      ite.pos[0],
                      ite.pos[1] + index * rowHeight,
                      ite.pos[2],
                      rowHeight,
                    ];
                    tempObj.new_score_list = [...everyRow];
                    tempObj.cols = ite.cols;

                    array1.push(tempObj);
                  });
                  this.$set(ite, 'new_score_list', scores);
                  this.$set(ite, 'newArray', array1);
                });
            }
          });
        });
        this.pageCount = res.data.page_count;
      }
    },
    async getBatchList() {
      let params = {
        examId: this.examId,
        taskId: this.$route.query.taskId,
        teacherId: this.teacherId,
      };
      let queryFunction = getScanTaskList;
      if (this.examId && this.examId !== 'no_exam_id') {
        queryFunction = getBatchListAPI;
      }
      const res = await queryFunction(params);
      if (res.code == 1) {
        this.paperNo = res.data[0].cardId;
        this.taskNames = res.data.map(item => ({ [item.id]: item.batchId }));
        this.taskList = [{ batchId: '全部批次', id: '' }, ...res.data];
      }
    },
    /**
     * @name:获取题目坐标
     */
    getScanPaperPoints() {
      getScanPaperPoints({
        paperNo: this.paperNo,
      })
        .then(res => {
          this.pointsData = JSON.parse(res.data);
          console.log('points', this.pointsData);
        })
        .catch(err => {});
    },
    /**
     * @name:获取批次名
     */
    getBatchName(item) {
      const matchingTask = this.taskNames.find(task => task[item.task_id]);
      return matchingTask ? matchingTask[item.task_id] : undefined;
    },
    /**
     * @name:加载更多图片数据
     */
    loadMore() {
      console.log('滚动到底部了', this.page);
      if (this.page >= this.pageCount) return;
      this.page++;
      this.getScanPaperData();
    },
    getBoxTop(top) {
      return `${top * this.scale}mm`;
    },
    goBack() {
      this.$router.back();
    },
    async loadImgInfo() {
      // await this.getBatchList();
      await this.getScanPaperData();
    },
    loadImage(val) {
      this.scale = val.currentTarget.height / 297;
      this.isDrawImg = true;
    },
    /**
     * @name:加载更多学生
     */
    async loadStuInfo() {
      await this.getClassList();
    },
    /**
     * @name:获取参加考试的班级
     */
    async getClassList() {
      let params = {
        schoolId: '',
        workId: this.examId,
        teaId: this.teacherId,
      };
      const res = await getClassListByWorkIdAPI(params);
      if (res.code == 1) {
        this.classList = res.data;
        this.classList.unshift({
          class_name: '全部班级',
          id: '',
        });
        this.classId = this.classList[0].id;
        this.getStuList();
      } else {
        this.classList = [];
        this.classId = '';
      }
    },
    loadStuMore() {
      console.log('滚动到底部了', this.stu.page);
      if (this.stu.page >= this.stu.pageCount) return;
      this.stu.page++;
      this.getStuList();
    },
    /**
     * @name:获取学生名单
     */
    async getStuList() {
      let params = {
        workId: this.examId,
        schoolId: this.taskList[1].schoolId,
        classId: this.classId,
        state: "1,2,3,4,5",
        page: this.stu.page,
        limit: 50,
        keyWord: this.searchStuInfo,
      };
      const res = await getScanStuInfosAPI(params);
      if ((res.code = 1)) {
        if (this.stu.page == 1) {
          this.stuTotalScore = 0;
          this.stuList = res.data.rows;
          this.$refs.stuTable.setCurrentRow(this.stuList[0]);
        } else {
          this.stuList = this.stuList.concat(res.data.rows);
        }
        this.stu.pageCount = res.data.page_count;
        // this.searchStuInfo = "";
      }
    },
    /**
     * @name:班级切换
     */
    classChange() {
      this.stu.page = 1;
      this.searchStuInfo = '';
      this.getStuList();
    },
    //table内容选中
    async handleCurrentStuChange(val) {
      this.$refs.stuImg.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
      this.stuTotalScore = 0;
      this.currentStuRow = val;
      this.stuList.forEach(item => {
        this.$set(item, 'isEdit', false);
      });
      await this.getScanStuData();
    },
    /**
     * @name:获取学生作答数据
     */
    async getScanStuData() {
      this.stuTableData = [];
      this.stuTotalScore = 0;
      let params = {
        examId: this.examId,
        stuNo: this.currentStuRow.stuNumber || '',
        stuId: this.currentStuRow.userId || ''
      };
      const res = await getStuScanDataAPI(params);
      if (res.code == 1) {
        this.stuTableData = res.data;
        this.stuTableData.forEach(ques => {
          this.$set(ques, 'isEdit', false);
          ques.questions.forEach(item => {
            this.stuTotalScore += item.score;
            if(item.scoringMode == 1){
              this.$set(item, 'tempScore', item.total_score - item.score);
            }else{
              this.$set(item, 'tempScore', item.score);
            }
            if (!item.is_obj && item.total_score > 0 && item.type != 7) {
              item.score_list && item.score_list.forEach(ite => {
                let scores = [];
                ite.scores?.forEach(it => {
                  let isChoice = ite.score == it && item.miss_score != item.score ? true : false;
                  scores.push({ value: it, isChoice: isChoice });
                });
                //每一行的高度
                let rowHeight = ite.pos[3] / ite.rows;
                const newArray = [];
                for (let i = 0; i < scores.length; i += ite.cols) {
                  const subArray = scores.slice(i, i + ite.cols);
                  newArray.push(subArray);
                }
                let array1 = [];
                newArray.forEach((everyRow, index) => {
                  let tempObj = {};
                  tempObj.pos = [ite.pos[0], ite.pos[1] + index * rowHeight, ite.pos[2], rowHeight];
                  tempObj.new_score_list = [...everyRow];
                  tempObj.cols = ite.cols;
                  array1.push(tempObj);
                });
                this.$set(ite, 'new_score_list', scores);
                this.$set(ite, 'newArray', array1);
              });
            }
          });
        });
      }
    },
    /**
     * @name:获取学生总分
     */
    onGetChangeRow(row) {
      this.stuTotalScore = 0;
      let currentScore = 0;
      row.questions.forEach(item => {
        if(item.scoringMode == 1){
          currentScore += Math.max((item.total_score - item.tempScore),0);
            }else{
              currentScore += item.tempScore;
            }
      });
      let otherRow = this.stuTableData.filter(item => {
        return item.id != row.id;
      });
      let otherScore = 0;
      otherRow.length != 0 &&
        otherRow.forEach(item => {
          item.questions.forEach(ite => {
            if(item.scoringMode == 1){
              otherScore += Math.max((ite.total_score - ite.tempScore),0);
            }else{
              otherScore += ite.tempScore;
            }
          });
        });
      this.stuTotalScore = currentScore + otherScore;
    },
    //选项卡切换
    handleClick(tab, event) {
      if (tab.name == 'img') {
        this.loadImgInfo();
      } else {
        this.loadStuInfo();
      }
    },
    //批次切换
    taskChange() {
      this.page = 1;
      this.searchStuno = '';
      this.getScanPaperData();
    },
    //table内容选中
    handleCurrentChange(val) {
      this.$refs.stuImg.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
      this.tableData.forEach(ques => {
        this.$set(ques, 'isEdit', false);
      });
      this.currentRow = val;
      this.tempQuesInfo = JSON.parse(JSON.stringify(val));
      this.oldStuNo = JSON.parse(JSON.stringify(this.currentRow.stu_no));
      this.tempStuNo = this.currentRow.stu_no;
    },
    /**
     * @name:编辑学号
     */
    editCurrentNo(item) {
      this.$set(item, 'isEdit', !item.isEdit);
      if (item.isEdit) {
        this.tempStuNo = item.stu_no || item.stuNumber;
      }
    },
    // 按学生修改考号 搜索学生
    async querySearchStu(keyWord, cb) {
      let params = {
        keyWord: keyWord,
        page: 1,
        limit: 50,
        workId: this.examId,
        schoolId: this.taskList[1].schoolId
      };
      let res = await searchStuInfoByWork(params)
      if (res.code == 1) {
        let stuList = res.data.rows.map(item => {
          return {
            value: item.realName,
            className:item.className,
            stuNo: item.stuNumber,
            stuId: item.userId,
            schoolId: item.schoolId,
          }
        })
        cb(stuList)
      }
    },
    handleSelect(item) {
      this.tempStuNo = item.stuNo;
      this.tempStu = item;
    },

    /**
     * @name:提交修改后的学号
    */
    async submitEditStuNo(item){
      let params = {
        examId:this.examId,
        oldStuNo:item.stuNumber,
        stuNo:this.tempStuNo,
        studentId: this.tempStu.stuId,
        schoolId:this.tempStu.schoolId
      };
      let res = await changeStuNoAPI(params);
      this.$set(item, 'isEdit', !item.isEdit);
      if(res.code == 1){
        this.getStuList();
        this.$message({
          message: '保存成功！',
          type: 'success',
          duration: 1000,
        });
      }else if(res.code == 2){
        this.$message({
          message: res.msg,
          type: 'error',
          duration: 1000,
        });
      }else{
        this.$message({
          message: '保存失败！',
          type: 'error',
          duration: 1000,
        });
      }
    },
    /**
     * @name:保存修改后的数据
     * @param {*} ques  题目数据
     * @param {*} type  是否为修改考号，否：为空，是：editExamNo
     */
    submitEditQues(ques, isEditExamNo) {
      if (isEditExamNo) {
        //修改考号
        ques.code = ques.code &= ~8;
        ques.code = ques.code &= ~16;
        ques.code = ques.code &= ~32;
        ques.code = ques.code &= ~128;
        ques.stu_no = this.tempStuNo;
        this.tableData.forEach(item => {
          if (item.stu_no == this.oldStuNo) {
            this.$set(item, 'stu_no', this.tempStuNo);
          }
        });
      } else {
        //修改题目
        // 是否有还未处理异常的题目
        const isNotHandleQues = ques.questions.some(item => {
          return item.status == 0;
        });
        ques.code = isNotHandleQues ? ques.code : (ques.code &= ~64);
      }
      this.savePageData(ques);
    },
    /**
     * @name:提交数据
     * @param {*} ques 题目数据
     */
    async savePageData(ques) {
      const res = await saveScanPageAPI(ques);
      if (res.code == 1) {
        this.$message({
          message: '保存成功！',
          type: 'success',
          duration: 1000,
        });
      } else {
        this.$message({
          message: '保存失败！',
          type: 'error',
          duration: 1000,
        });
      }
    },
    searchImage() {
      this.page = 1;
      this.getScanPaperData();
    },
    async searchStu() {
      this.stuTotalScore = 0;
      this.stu.page = 1;
      await this.getStuList();
    },
    showInputBox() {
      this.showInput = true;
      this.$nextTick(() => {
        this.$refs.searchInput.focus();
      });
    },
    hideInput() {
      this.showInput = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-width: 1300px;
  max-width: 1500px;
  margin: 0 auto;
  padding: 20px 0;
  background-color: #f7fafc;
  height: 100%;
  overflow-y: hidden;
  .stu-list {
    height: 100%;
    width: 300px;
    margin: 2px;
    display: inline-block;
    .task-info {
      margin: 10px 0px;
      display: flex;
      align-items: center;
      .task-info-count {
        margin: 0 5px;
      }
    }
    .task-list {
      height: 90%;
    }
  }
  .stu-img-list {
    position: relative;
    height: 100%;
    width: calc(100% - 305px);
    display: inline-block;
    overflow-y: scroll;
    .img {
      position: relative;
      width: 100%;
    }
  }
  .img-box {
    position: relative;
  }
  .score-box {
    position: absolute;
    width: 60px;
    height: 42px;
    background-color: rgba(255, 61, 90, 0.218);
    opacity: 0.6;
    right: 0;
    font-size: 28px;
    text-align: center;
    line-height: 42px;
    color: red;
  }
  .stu-no-box {
    position: absolute;
    width: 150px;
    height: 42px;
    // background-color: rgb(255, 61, 90, 0.6);
    opacity: 0.6;
    // right: 0;
    top: 70px;
    left: 20px;
    font-size: 28px;
    text-align: center;
    line-height: 42px;
    color: red;
  }
  .stu-no {
    color: #333;
    font-size: 16px;
  }
  .stu-task-name {
    color: #909399;
  }
  // .points-box {
  //   position: absolute;
  //   cursor: pointer;
  //   z-index: 2;
  //   &.active {
  //     border: solid 4px #01cc7d;
  //   }
  //   &.error {
  //     border: solid 4px red !important;
  //   }
  //   &.right {
  //     border: solid 4px #01cc7d !important;
  //   }
  // }
}
.stu-no-edit {
  display: none;
}
.search__icon {
  width: 38px;
  font-size: 25px;
  color: #409eff;
  border-radius: 0 3px 3px 0;
  outline: none;
  cursor: pointer;
  margin-left: 15px;
}
.stu-total-score {
  position: absolute;
  top: 0px;
  left: 10px;
  font-size: 22px;
  opacity: 0.8;
  text-align: center;
  color: red;
  z-index: 2;
}
</style>
<style lang="scss">
.task-list {
  .el-table--enable-row-hover .el-table__body tr:hover > td {
    .stu-no-edit {
      // display: inline-block;
      // margin-left: 10px;
    }
  }
  .el-table .current-row {
    .stu-no-edit {
      display: inline-block;
      margin-left: 10px;
      cursor: pointer;
    }
  }
  .el-table .el-table__row {
    .el-input {
      width: 60%;
    }
  }
}
.image-tabpane {
  height: 100%;
  .el-tabs__content {
    height: calc(100% - 22px);
  }
  .el-tab-pane {
    height: calc(100% - 20px);
  }
}
.edit-stu-no-autocomplete{
  width: 100%;
  li{
    line-height: normal;
    padding: 7px;
    .name {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .class-name {
      font-size: 12px;
      color: #b4b4b4;
    }
  }

}
</style>

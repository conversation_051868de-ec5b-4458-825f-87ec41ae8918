<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-03-13 16:23:57
 * @LastEditors: 小圆
-->
<template>
  <div class="book-unpublish">
    <div class="status-info">
      <div class="placeholder-image">
        <i class="el-icon-document"></i>
        <div class="status-text">
          <span v-if="getStuCount == 0">尚未采集到学生作答数据，请提醒学生按时完成作业并提交</span>
          <span v-else>
            已采集 <span class="count">{{ getStuCount }}</span> 位学生作答数据
          </span>
        </div>
        <div class="action-bar">
          <el-button
            type="primary"
            :disabled="workId && getStuCount == 0"
            :loading="isPublishLoading"
            @click="handlePublish"
            >发布成绩</el-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { publishScoreAPI } from '@/service/api';
import { getMergeStatus } from '@/service/pexam';
import { sleep } from '@/utils/index';
import { Loading } from '@iclass/element-ui';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component
export default class BookUnpublishView extends Vue {
  /** 学生作答数据 */
  @Prop({
    type: Number,
    default: 0,
  })
  private stuCount: number;

  /** 作业id */
  @Prop({
    type: String,
    default: '',
  })
  private workId: string;

  /** 获取学生作答数据 */
  get getStuCount() {
    return this.stuCount || 0;
  }

  /** 发布成绩loading */
  private isPublishLoading = false;

  /** 合并数据 */
  async mergeData() {
    const mergeLoading = Loading.service({
      lock: true,
      text: '正在合并数据，请稍候',
      background: 'rgba(0, 0, 0, 0.7)',
    });

    try {
      let mergeTimes = 0;
      while (mergeTimes < 20) {
        const res = await getMergeStatus({
          examId: this.workId,
          times: mergeTimes,
        });

        if (res.data.status === 0) {
          mergeLoading.close();
          return;
        }

        await sleep(1000);
        mergeTimes++;
      }
    } catch (error) {
      mergeLoading.close();
      this.$message.error('合并数据失败');
      this.isPublishLoading = false;
      throw error;
    }
  }

  /** 发布成绩 */
  async handlePublish() {
    this.isPublishLoading = true;
    await this.mergeData();
    publishScoreAPI({
      schoolId: this.$sessionSave.get('schoolInfo').id,
      workId: this.workId,
    })
      .then(res => {
        if (res.code == 1) {
          this.$emit('publish');
          this.isPublishLoading = false;
        } else {
          this.$message.error(res.msg || '发布成绩失败');
          this.isPublishLoading = false;
        }
      })
      .catch(err => {
        console.error(err);
        this.$message.error(err?.msg || '发布成绩失败');
        this.isPublishLoading = false;
      });
  }
}
</script>

<style scoped lang="scss">
.book-unpublish {
  padding: 24px;
  height: 100%;
  background-color: #fff;

  .status-info {
    text-align: center;
    margin-bottom: 32px;

    .placeholder-image {
      padding: 80px;
      margin-bottom: 24px;
      width: 100%;
      height: 100%;
      background: #f5f7fa;
      border-radius: 8px;

      i {
        font-size: 80px;
        color: #c0c4cc;

        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .status-text {
      margin-top: 25px;
      font-size: 14px;
      color: #606266;
      line-height: 1.5;

      .count {
        color: #409eff;
        font-weight: bold;
        font-size: 16px;
        margin: 0 4px;
      }
    }
  }

  .action-bar {
    margin-top: 20px;
    text-align: center;
  }
}
</style>

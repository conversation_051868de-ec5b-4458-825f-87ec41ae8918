@if $use-fadeInDownBig == true {

	@-webkit-keyframes fadeInDownBig {
		0% {
			opacity: 0;
			-webkit-transform: translateY(-$base-distance-big * 2);
		}

		100% {
			opacity: 1;
			-webkit-transform: translateY(0);
		}
	}

	@keyframes fadeInDownBig {
		0% {
			opacity: 0;
			transform: translateY(-$base-distance-big * 2);
		}

		100% {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.fadeInDownBig {
		@include animate-prefixer(animation-name, fadeInDownBig);
	}
}

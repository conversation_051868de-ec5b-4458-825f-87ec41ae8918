<template>
  <div>
    <div v-show="!isNoData" ref="subjectZScoreRadarChart" style="width: 100%; height: 400px"></div>
    <div v-show="isNoData">
      <div style="text-align: center">
        <img style="width: 350px" :src="noResImg" alt="" />
      </div>
      <p style="text-align: center; font-size: 16px; margin-top: 10px">暂无数据!</p>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { ISubjectRateData, IFilterData } from '@/pages/lookReport/reportOverview/subjectComparison.vue';

@Component
export default class SubjectZScoreRadarChart extends Vue {
  public $refs: {
    subjectZScoreRadarChart: HTMLElement;
  };

  @Prop() tableData: ISubjectRateData[];
  @Prop() schoolTableData: ISubjectRateData[];
  @Prop() filterData: IFilterData;
  // 缺少资源图片
  noResImg = require('@/assets/no-res.png');
  // 是否没有数据
  isNoData: boolean = true;
  // 图表
  subjectZScoreRadarChart: EChartsType = null;

  @Watch('tableData', {
    deep: true,
    immediate: true,
  })
  onTableDataChange(val: ISubjectRateData[]) {
    this.renderChart();
  }

  beforeDestroy() {
    if (this.subjectZScoreRadarChart) {
      this.subjectZScoreRadarChart.dispose();
      this.subjectZScoreRadarChart = null;
    }
  }

  // 获取图表数据
  getChatData() {
    const classItem = this.$sessionSave.get('innerClassList').find(item => item.id === this.filterData.classId);

    const subjectNames = [];
    this.tableData.forEach(item => {
      subjectNames.push(item.subjectName);
    });

    const classList = [
      {
        classId: '',
        className: '全部班级',
        tableData: this.schoolTableData.map(item => {
          return {
            ...item,
            zScore: 0,
          };
        }),
        data: [] as number[],
      },
      {
        classId: this.filterData.classId,
        className: classItem.class_name,
        tableData: this.tableData,
        data: [] as number[],
      },
    ];
    classList.forEach(classItem => {
      const data = [];
      subjectNames.forEach(subjectName => {
        data.push(classItem.tableData.find(item => item.subjectName === subjectName).zScore);
      });
      classItem.data = data;
    });

    return {
      legendData: subjectNames,
      seriesData: classList,
    };
  }

  // 渲染图表
  async renderChart() {
    if (this.subjectZScoreRadarChart) {
      this.subjectZScoreRadarChart.dispose();
      this.subjectZScoreRadarChart = null;
    }
    const { legendData, seriesData } = this.getChatData();
    if (seriesData.length === 0) {
      this.isNoData = true;
      return;
    } else {
      this.isNoData = false;
    }
    await this.$nextTick();
    this.subjectZScoreRadarChart = this.$echarts.init(this.$refs.subjectZScoreRadarChart);
    const option: EChartsOption = {
      tooltip: {},
      legend: {
        show: true,
        data: seriesData.map(item => item.className),
      },
      radar: {
        indicator: legendData.map((name, index) => {
          return {
            name: name,
            max: Math.ceil(Math.max(...seriesData.map(item => item.data).flat())),
            min: Math.floor(Math.min(...seriesData.map(item => item.data).flat())),
            axisLabel: {
              show: index === 0 ? true : false,
            },
          };
        }),
        radius: '65%',
      },
      series: {
        type: 'radar',
        data: seriesData.map(item => {
          return {
            name: item.className,
            value: item.data,
          };
        }),
      },
    };
    this.subjectZScoreRadarChart.setOption(option);
  }
}
</script>

<style scoped></style>

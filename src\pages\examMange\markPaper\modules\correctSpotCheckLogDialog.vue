<template>
    <el-dialog title="抽查结果" :visible.sync="dialogVisible" width="50%" :before-close="handleClose">
        <div class="result-handle">
            <div class="handle">
                <span>题块：</span>
                <el-select v-model="curQues" value-key="quesId" size="small">
                    <el-option v-for="ques in quesList" :label="ques.title" :disabled="!curQues.operate"  :value="ques"></el-option>
                </el-select>
            </div>
            <div class="handle">
                <span>阅卷老师：</span>
                <el-select v-model="curTea" value-key="userId" size="small">
                    <el-option v-for="tea in curQues.teas" :label="tea.userName"  :value="tea"></el-option>
                </el-select>
            </div>
        </div>
        <div v-if="tableData.length" class="result-table">
            <el-row class="table-header">
                <el-col :span="2" class="col-li"> 序号</el-col>
                <el-col :span="4" class="col-li"> 阅卷老师 </el-col>
                <el-col :span="2" class="col-li"> 阅卷得分 </el-col>
                <el-col :span="2" class="col-li"> 改分</el-col>
                <el-col :span="3" class="col-li"> 抽查结果 </el-col>
                <el-col :span="3" class="col-li"> 抽查人员 </el-col>
                <el-col :span="4" class="col-li"> 抽查时间 </el-col>
                <el-col :span="4" class="col-li"> 操作 </el-col>
            </el-row>
            <el-row class="table-content" v-for="(item,index) in tableData" :key="item.stu_id">
                <el-col :span="2" class="col-li"> {{ (page.index-1) * page.pageSize + index+1 }} </el-col>
                <el-col :span="4" class="col-li"> {{ item.correct_name }} <span v-if="item.teaType == 2">（仲裁）</span> </el-col>
                <el-col :span="2" class="col-li"> {{ item.stu_score }} </el-col>
                <el-col :span="2" class="col-li"> {{ item.state == "0" ? item.check_score : '-' }} </el-col>
                <el-col :span="3" class="col-li"> {{ item.state == "0" ? "未通过" : "通过" }} </el-col>
                <el-col :span="3" class="col-li"> {{ item.spot_check_name }} </el-col>
                <el-col :span="4" class="col-li"> {{ item.create_time }} </el-col>
                <el-col :span="4" class="col-li"> <el-button type="text" @click="previewImgs(item)">查看作答</el-button> </el-col>
            </el-row>
        </div>
        <el-empty v-else description="暂无抽查记录"></el-empty>
        <el-pagination
        style="text-align: center; margin-top: 20px;"
        :hide-on-single-page="true"
        background
        layout="prev, pager, next"
        @current-change="(page) => {
            this.page.index = page
            this.getExamResult()
        }"
        :page-size="page.pageSize"
        :total="page.total">
        </el-pagination>
        <img-preview
        v-if="isShowPreviewImg"
        :imgList="previewImgList"
        @close="isShowPreviewImg = false"
        ></img-preview>
    </el-dialog>
</template>

<script>
import {
    getExamCheckResult
} from '@/service/api';
import { getImgUrl } from '@/utils/index';
import { replaceALiUrl } from '@/utils/common';
import ImgPreview from '@/components/SwiperViewer/ImgPreview.vue';
export default {
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
        quesList:{
            type: Array,
            default: []
        },
        workId:{
            type: String,
            default: ''
        },
        teaId:{
            type: String,
            default: ''
        },
        currentQues: {
            type: Object,
            default: null
        }
    },
    watch: {
        curQues(newVal, oldVal){
            if (newVal && this.curTea) {
                this.curTea = newVal.teas[0]
            }
        },
        curTea(newVal, oldVal){
            if(newVal){
                this.getExamResult()
            }
        }
    },
    components: {
        ImgPreview
    },
    data() {
        return {
            curQues: null,
            curTea: null,
            tableData: [],
            page:{
                index:1,
                pageSize:10,
                total:0
            },
            isShowPreviewImg:false,
            previewImgList:[]
        }
    },
    async created() {
        if (this.currentQues) {
            this.curQues = this.quesList.find(item => {
                return item.quesId == this.currentQues.quesId
            })
        } else {
            this.curQues = this.quesList[0]
        }
        await this.$nextTick();
        this.curTea = this.curQues.teas.find(item=>{
            return item.userId == this.teaId
        })
    },
    methods: {
        async getExamResult() {
            let params = {
                schoolId: this.$sessionSave.get('schoolInfo').id,
                workId:this.workId,
                correctId:this.curTea.userId,
                quesId:this.curQues.quesId,
                page:this.page.index,
                pageSize:this.page.pageSize
            };
            let res = await getExamCheckResult(params).catch(e => {
            });
            this.tableData = res.data.rows;
            this.page.total = res.data.total_rows;
        },
        getImgUrl(src) {
            let path = getImgUrl(src)
            return replaceALiUrl(path)
        },
        previewImgs(item){
            this.previewImgList = item.urls.map(item => this.getImgUrl(item));
            this.isShowPreviewImg = true;
        },
        handleClose() {
            this.$emit("closeDialog")
        }
    }
}
</script>

<style lang="scss" scoped>
.result-handle {
    .handle {
        display: inline-block;
        margin-right: 20px;
    }
}

.result-table {
    margin-top: 10px;

    .table-header {
        height: 50px;
        line-height: 50px;
        background: #f8f8f9;
        border-radius: 5px 5px 0 0;
    }

    .table-content {
        height: 45px;
    }

    .col-li {
        text-align: center;
        border-right: 1px solid #f3f3f3;
        border-bottom: 1px solid #f3f3f3;
        height: 100%;
        vertical-align: middle;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
<template>
    <div class="right-wrapper">
        <div id="sticky_container" class="add-ques-content">
            <ques-c30-filter :filter-list="filterList" data-stuck="stuck_c30" :stuck="stuckInfo.stuck_c30"
                             class="sticky"
                             ref="c30Filter"></ques-c30-filter>
            <wrong-ques-card v-on="$listeners" :pointId="pointId" @currentPage="getCurrentPage" current-step="0" only-view="true"
                             ref="quesCard"></wrong-ques-card>
        </div>
    </div>
</template>

<script>
    import {localSave, sessionSave} from '@/utils/index.js';
    import {mapGetters} from 'vuex';
    import HashUtil from '@/utils/hashUtil';

    import QuesC30Filter from '@/components/QuesC30Filter/QuesC30Filter.vue';
    import wrongQuesCard from '@/components/ReplaceQuesCard/wrongQuesCard.vue';
    export default {
        name: 'wrong-intensify',
        props: ['pointId'],
        data () {
            return {
                stuckInfo     : {
                    stuck_c30: false
                },
                currentSubject: '',
                subjectList   : [
                    {
                        id   : 11,
                        title: '高中数学'
                    }, {
                        id   : 2,
                        title: '初中数学'
                    }
                ],
                filterList    : null,
                pointInfo     : null,
                currentPage   : null
            };
        },
        components: {
            QuesC30Filter,
            wrongQuesCard
        },
        computed  : {
            ...mapGetters(['subjectMap'])
        },
        created () {
            //恢复数据
            let filterData = localSave.get('qm-filterList-' + this.currentSubject);
            let pointData = localSave.get('qm-pointInfo-' + this.currentSubject);

            if (filterData && new Date().getTime() - filterData.time < 86400000) {
                this.filterList = filterData.data;
            }

            if (pointData && new Date().getTime() - pointData.time < 86400000) {
                this.pointInfo = pointData.data;
            }

            this.currentPage = HashUtil.get_hash('page');

            if (this.currentPage) {
                this.currentPage = parseInt(this.currentPage) || 1;
                setTimeout(() => {
                    this.currentPage = null;
                }, 500);
            }

            this.onConditionChange();
        },
        mounted () {
            // this.initTimeout();
        },
        methods   : {
            initTimeout () {
                if (this.subjectMap['11']) {
                    this.init();
                } else {
                    setTimeout(() => {
                        this.initTimeout();
                    }, 200);
                }
            },
            init () {
                let loginInfo = this.$sessionSave.get('loginInfo');
                this.currentSubject = this.$route.query.subId || loginInfo.subjectid || 11;

                let subject = this.subjectMap[this.currentSubject];
                sessionSave.set('currentSubject', subject);

                this.$nextTick(() => {
                    observeStickyHeaderChanges(document.querySelector('#sticky_container'));
                    document.addEventListener('sticky-change', e => {
                        let node = e.detail.target;
                        this.$set(this.stuckInfo, node.dataset['stuck'], e.detail.stuck);
                    });

                    this.changeFilter();
                });
            },
            getCurrentPage (page) {
                if (page) {
                    HashUtil.set_hashchange({
                        page
                    });
                }
            },
            subjectChange () {
                let subject = this.subjectMap[this.currentSubject];
                sessionSave.set('currentSubject', subject);
                this.changeFilter();
            },
            // 监听条件变化
            onConditionChange () {
                this.$bus.$on('changeC30Filter', (type, value) => {
                    this.$refs.quesCard.updateFilterInfo({
                        type      : 'c30',
                        filterList: value.filterList,
                        page      : this.currentPage
                    });
                    if (!this.currentPage) {
                        this.getCurrentPage(1);
                    }
                    localSave.set('qm-filterList-' + this.currentSubject, {
                        time: new Date().getTime(),
                        data: value.filterList
                    });
                });

                this.$bus.$on('changePoint', (type, value) => {
                    if (type === 'c30Point') {
                        this.$refs.quesCard.updateFilterInfo({
                            type     : 'c30',
                            pointInfo: value,
                            page     : this.currentPage
                        });

                        if (!this.currentPage) {
                            this.getCurrentPage(1);
                        }
                        localSave.set('qm-pointInfo-' + this.currentSubject, {
                            time: new Date().getTime(),
                            data: value
                        });
                    }
                });
            },
            changeFilter () {
                this.$refs.c30Filter.readC30FilterCondition();
            }
        },
        beforeDestroy () {
            this.$bus.$off('changeC30Filter');
            this.$bus.$off('changePoint');
        },
    };
</script>

<style lang="scss" scoped>
    .right-wrapper {
        height         : calc(100vh - 115px);
        overflow       : hidden;
        display        : flex;
        flex-direction : column;

        .add-ques-content {
            flex       : 1;
            overflow-y : auto;
            position   : relative;

            .selected-box {
                position : absolute;
                right    : 75px;
                top      : 10px;
            }
        }
    }
</style>

<template>
  <div class="exam-mk-container">
    <bread-crumbs :title="title"></bread-crumbs>
    <div class="ques-setting-header">
      <!-- <div class="ques-setting-title">{{title}}</div> -->
      <div class="ques-setting-select">
        <el-select v-model="subject" value-key="id" class="select-item" @change="handleSelectSubj" placeholder="请选择学科">
          <el-option v-for="item in subjectList" :key="item.id" :label="item.name" :value="item">
          </el-option>
        </el-select>
        <el-autocomplete class="select-item" style="width: 200px;" v-model="stuInfo.value" value-key="title"
          :fetch-suggestions="getSearchStu" placeholder="请输入学生姓名或考号" clearable
          @select="handleSelectStu"></el-autocomplete>
        <el-button class="select-item" type="primary" @click="searchStu">查询</el-button>
        <el-select v-model="currQues" value-key="tid" class="select-item" placeholder="请选择题目"
          @change="handleSelectQues">
          <el-option v-for="item in quesInfos" :key="item.tid" :label="getQuesTitle(item)" :value="item">
          </el-option>
        </el-select>
        <el-button style="float: right;" type="text" class="select-item" @click="gotoRecord">查看处理记录</el-button>
      </div>
    </div>
    <div class="ques-setting-container">
      <div v-if="currQues" class="ques-setting-left">
        <div class="ques-setting-left-top">
          <div class="top-tip">操作提示：按住鼠标左键，选择题块对应区域</div>
          <div v-if="pageCount > 1" class="top-page">
            <div :class="['page', currPage == page - 1 ? 'active' : '']" v-for="page in pageCount"
              @click="currPage = page - 1">
              第{{ page }}页
            </div>
          </div>
        </div>
        <div class="exam-pdf-container">
          <div @mousedown="mousedown" @mousemove="mousemove" @mouseup="mouseup" @mouseleave="mouseup"
            :class="[pageLayout == IPAGELAYOUT.A4 ? 'a4' : 'a3']">
            <img class="img" :src="pageUrl" />
            <div class="ques_box draw" :style="drawBoxStyle"></div>
            <template v-for="(item, index) in currQues.points[currPage]">
              <div :class="['ques_box', 'active', item.isEdit ? 'edit' : '']" :did="item.id" :style="[
      {
        left: item.x + 'px',
        top: item.y + 'px',
        width: item.w + 'px',
        height: item.h + 'px',
      },
    ]">
                <div class="ques-edit" @click.stop="editPoint(item)">编辑</div>
                <div class="ques-cancel" @click.stop="deletePoint(item)">删除</div>
                <div class="notop">
                  {{ item.title }}
                </div>
                <div class="nobottom">
                  {{ item.title }}
                </div>
                <template v-if="item.isEdit">
                  <div class="ques-save" @click="savePoint(item)">保存</div>
                  <div class="resize-dot top"></div>
                  <div class="resize-dot bottom"></div>
                  <div class="resize-dot left"></div>
                  <div class="resize-dot right"></div>
                  <div class="resize-dot top-left"></div>
                  <div class="resize-dot top-right"></div>
                  <div class="resize-dot bottom-left"></div>
                  <div class="resize-dot bottom-right"></div>
                </template>
              </div>
            </template>
          </div>
          <div style="position: absolute;right: 0;top: 30px;"><el-button @click="saveData">保存</el-button></div>
        </div>
      </div>
      <el-empty v-else description="请选择学科、输入学生姓名或考号进行查询"></el-empty>
    </div>
  </div>
</template>

<script>
import BreadCrumbs from '@/components/Breadcrumbs/index.vue';
import { getViewPaper } from '@/service/testbank.js';
import { getWorkStuList, getStuAnswerAreaReplaceData, saveStuAnswerAreaReplace, getStuAnswerImages } from '@/service/api.js';
import { mmConversionPx, pxConversionMm,replaceALiUrl } from '@/utils/common';
import { getQueryString } from '@/utils';
import { generateUUID } from '@/utils/index.js';
import { getExamSubjectMulti } from '@/service/pexam';
import { IPAGELAYOUT } from '@/typings/card';

const HAND_TYPE = {
  DRAW: 0,
  MOVE: 1,
  LEFT: 2,
  TOP: 3,
  RIGHT: 4,
  BOTTOM: 5,
  LEFT_TOP: 6,
  RIGHT_TOP: 7,
  LEFT_BOTTOM: 8,
  RIGHT_BOTTOM: 9,
};
export default {
  name: 'setting-ques-block',
  data() {
    return {
      IPAGELAYOUT,
      examId: getQueryString('examId') || '',
      title: getQueryString('title') || '',
      quesInfos: [],
      currPage: 0,
      pageCount: 0,
      pageLayout: 1,
      cardType: 0,
      currQues: null,
      handType: HAND_TYPE.DRAW,
      drawBoxStyle: {
        display: 'none',
      },
      start: {
        offsetLeft: 0,
        offsetTop: 0,
        x: 0,
        y: 0,
      },
      end: {
        x: 0,
        y: 0,
      },
      isDrag: false,
      dragBox: null,
      pageUrl: '',
      imgList: [],
      subject: null,
      subjectList: [],
      stuInfo: { id: "", value: "" },
      stuList: [],
      isInitialRender: false
    };
  },
  watch: {
    currPage: {
      handler(newval, oldval) {
        // this.pageUrl = `https://fs.iclass30.com/scan/pdf/${
        //   this.paperNo
        // }_${this.getPageLayoutTxt()}/${newval}.png`;
        this.pageUrl = replaceALiUrl(this.imgList[newval]);
        document.getElementsByClassName('exam-pdf-container')[0].scrollTop = 0;
      },
    },
  },
  components: {
    BreadCrumbs,
  },
  async mounted() {
    if (!this.isInitialRender) {
      await this.getExamSubject();
    }
  },
  beforeRouteEnter(to, from, next) {
    if (from.name === 'examReport') {
      next(vm => {
        vm.isInitialRender = true;
        vm.init()
      });
    } else {
      next();
    }
  },
  methods: {
    init() {
      this.examId = getQueryString('examId') || '';
      this.title = getQueryString('title') || '';
      this.dragBox = null;
      this.pageUrl = '';
      this.imgList = [];
      this.subject = null;
      this.subjectList = [];
      this.stuInfo = { id: "", value: "" };
      this.currQues = null;
      this.quesInfos = [];

      this.getExamSubject();
    },
    async getExamSubject() {
      const res = await getExamSubjectMulti({ examId: this.examId }).catch(err => {
      });
      if (res.code == 1) {
        this.subjectList = res.data.filter(item=>{
          return item.source == 4
        }).removeDuplicatesByKey('id');
        this.subject = res.data[0]
      } else {
        this.$message.error("获取考试学科信息失败，请刷新重试")
      }
    },
    async getSearchStu(queryString, cb) {
      const params = {
        workId: this.subject.personalBookId,
        schoolId: this.$sessionSave.get('schoolInfo').id,
        text: queryString,
        scanState: 1,
        page: 1,
        pageSize: 50
      };
      let res = await getWorkStuList(params).catch(err => {
      });
      if (res.code == 1) {
        let list = [];
        res.data.rows.forEach((stu) => {
          list.push({ id: stu.stuId, title: `${stu.stuName} (${stu.stuNo})`, value: stu.stuName,paperNo:stu.paperNo })
        })
        this.stuList = list;
        cb(this.stuList)
      } else {
        this.$message.error("获取学生信息失败，请刷新重试")
      }
    },
    async getStuPaperInfo() {
      let params = {
        workId: this.subject.personalBookId,
        stuId: this.stuInfo.id,
        quesNo: this.currQues.isTemp ? this.currQues.id :this.currQues.quesNo,
        schoolId: this.$sessionSave.get('schoolInfo').id,
      };
      let res = await getStuAnswerAreaReplaceData(params).catch(err => {
      });
      let tid = this.currQues.tid;
      this.currQues = res.data.ansAreas[0];
      this.currQues.tid = tid;
      this.converPoints2Draw(this.currQues, this.currQues.firstSmallId || this.currQues.id)
    },
    handleSelectSubj() {
      this.stuInfo.value = "";
      this.stuInfo.id = "";
      this.currQues = null;
      this.quesInfos = [];
    },
    handleSelectStu(item) {
      this.stuInfo = item
    },
    handleSelectQues(item) {
      let page = 0;
      if (item.points.length > 0) {
        //查找当前题目坐标所在页
        while (!item.points[page]) {
          page++
        }
      }
      this.currPage = page;
      this.currQues = item;
      this.currQues.points[this.currPage].forEach((point) => {
        point.isEdit = false;
      })
      this.getStuPaperInfo();
    },
    /**
     * @name:获取生成的pdf图片
     */
    async getImgUrls() {
      const params = {
        workId: this.subject.personalBookId,
        stuId: this.stuInfo.id,
      };
      const res = await getStuAnswerImages(params).catch(err => {
      });;
      if (res.code == 1) {
        this.imgList = res.data;
        this.pageCount = this.imgList.length;
        this.pageUrl = replaceALiUrl(this.imgList[0]);
      } else {
        this.imgList = [];
      }
    },
    mmConvertPx(point) {
      if (this.pageLayout == IPAGELAYOUT.A4) {
        return mmConversionPx(point);
      } else {
        return (mmConversionPx(point) / 4) * 3;
      }
    },
    pxConvertMm(point) {
      if (this.pageLayout == IPAGELAYOUT.A4) {
        return pxConversionMm(point);
      } else {
        return (pxConversionMm(point) / 3) * 4;
      }
    },
    isSubjectiv(typeid) {
      return ![1, 2, 8, '1', '2', '8'].includes(typeid);
    },
    getPageLayoutTxt() {
      if (this.pageLayout == IPAGELAYOUT.A4) {
        return 'A4';
      } else {
        return 'A3';
      }
    },
    getQuesTitle(ques){
      if(ques.isChooseDo){
        return ques.chooseName.join(',') + '(' + ques.chooseIds.length + '选' + ques.doCount + ')';
      }else{
        return ques.quesNos;
      }
    },
    converPoints2Draw(ques, qid) {
      if (!this.isSubjectiv(ques.typeId)) return;
      ques._id = qid;
      let points = []
      ques.points?.forEach(point => {
        if (!points[point.page]) {
          points[point.page] = [];
        }
        points[point.page].push({
          id: generateUUID(),
          qid: qid,
          title: this.getQuesTitle(ques),
          x: this.mmConvertPx(point.pos[0]),
          y: this.mmConvertPx(point.pos[1]),
          w: this.mmConvertPx(point.pos[2]),
          h: this.mmConvertPx(point.pos[3]),
        });
      });
      ques.points = points;
    },
    converQuesInfos(quesInfos) {
      let list = [];
      quesInfos.forEach(bq => {
        if (bq.isMerge || (bq.isSplit && bq.scoreMode == 3)) {
          this.converPoints2Draw(bq, bq.firstSmallId);
          bq.quesNo = bq.data[0].quesNo;
          bq.tid = bq.isMerge ? bq.firstSmallId : bq.id;
          list.push(bq)
        } else {
          if (bq.data) {
            bq.data.forEach(sq => {
              if (sq.isMerge || (sq.isSplit && sq.scoreMode == 3)) {
                this.converPoints2Draw(sq, sq.firstSmallId);
                sq.quesNo = sq.data[0].quesNo;
                sq.tid = sq.isMerge ? sq.firstSmallId : sq.id;
                list.push(sq)
              } else {
                if (sq.data) {
                  sq.data.forEach(qs => {
                    if(!qs.isHide){
                      this.converPoints2Draw(qs, qs.id);
                      qs.tid = qs.isMerge ? qs.firstSmallId : qs.id;
                      list.push(qs)
                    }
                  });
                } else {
                  if(!sq.isHide && (!sq.isChooseDo || this.cardType == 1 || (sq.isChooseDo && sq.targetIds.includes(sq.id)))){
                    this.converPoints2Draw(sq, sq.id);
                    sq.tid = sq.isMerge ? sq.firstSmallId : sq.id;
                    list.push(sq)
                  }
                }
              }
            });
          } else {
            if(!bq.isHide){
              this.converPoints2Draw(bq, bq.id);
              bq.tid = bq.isMerge ? bq.firstSmallId : bq.id;
              list.push(bq)
            }
          }
        }
      });
      this.quesInfos = list.filter((item) => {
        return this.isSubjectiv(item.typeId)
      });
    },
    async getPaperData() {
      let params = { paperNo: this.stuInfo.paperNo,type:1 };
      let res = await getViewPaper(params).catch(err => {
      });
      if (res.code == 1) {
        const cardInfo = JSON.parse(res.data.cardInfo);
        this.pageLayout = cardInfo.pageLayout;
        this.cardType = cardInfo.cardType;
        this.quesInfos = [];
        this.converQuesInfos(JSON.parse(res.data.teamInfo));
        this.handleSelectQues(this.quesInfos[0])
      }
    },
    setDrawBoxStyle() {
      this.drawBoxStyle = {
        left: this.start.x + 'px',
        top: this.start.y + 'px',
        width: this.end.x + 'px',
        height: this.end.y + 'px',
      };
    },
    clearDrawBoxStyle() {
      this.start = {
        x: 0,
        y: 0,
      };
      this.end = {
        x: 0,
        y: 0,
      };
      this.drawBoxStyle = {
        display: 'none',
      };
    },
    deletePoint(item) {
      this.currQues.points[this.currPage] = this.currQues.points[this.currPage].filter(info => {
        return info.id != item.id;
      });
      this.$forceUpdate();
    },
    editPoint(item) {
      this.currQues.points[this.currPage].forEach((point) => {
        point.isEdit = false;
      })
      item.isEdit = true;
      this.dragBox = item;
      this.$forceUpdate();
    },
    savePoint(item) {
      item.isEdit = false;
      this.$forceUpdate();
    },
    mousedown(e) {
      e.preventDefault();
      let boxRect = e.currentTarget.getBoundingClientRect();
      let targ = e.srcElement;
      if (targ.classList.contains('top')) {
        this.handType = HAND_TYPE.TOP;
      } else if (targ.classList.contains('bottom')) {
        this.handType = HAND_TYPE.BOTTOM;
      } else if (targ.classList.contains('left')) {
        this.handType = HAND_TYPE.LEFT;
      } else if (targ.classList.contains('right')) {
        this.handType = HAND_TYPE.RIGHT;
      } else if (targ.classList.contains('top-left')) {
        this.handType = HAND_TYPE.LEFT_TOP;
      } else if (targ.classList.contains('top-right')) {
        this.handType = HAND_TYPE.RIGHT_TOP;
      } else if (targ.classList.contains('bottom-left')) {
        this.handType = HAND_TYPE.LEFT_BOTTOM;
      } else if (targ.classList.contains('bottom-right')) {
        this.handType = HAND_TYPE.RIGHT_BOTTOM;
      } else if (targ.classList.contains('edit')) {
        this.handType = HAND_TYPE.MOVE;
        this.start.offsetTop = this.dragBox.y - (e.clientY - boxRect.y);
        this.start.offsetLeft = this.dragBox.x - (e.clientX - boxRect.x);
      } else {
        this.handType = HAND_TYPE.DRAW;
      }
      this.start.x = e.clientX - boxRect.x;
      this.start.y = e.clientY - boxRect.y;

      this.isDrag = true;
    },
    mousemove(e) {
      e.preventDefault();
      if (!this.isDrag) return;
      let boxRect = e.currentTarget.getBoundingClientRect();
      const nowX = e.clientX - boxRect.x,
        nowY = e.clientY - boxRect.y;
      const disX = nowX - this.start.x,
        disY = nowY - this.start.y;
      let moveY = 0;
      let moveX = 0;
      switch (this.handType) {
        case HAND_TYPE.TOP:
          moveY = this.dragBox.y - nowY;
          this.dragBox.y = nowY;
          this.dragBox.h = this.dragBox.h + moveY;
          break;
        case HAND_TYPE.BOTTOM:
          moveY = this.dragBox.y + this.dragBox.h - nowY;
          this.dragBox.h = this.dragBox.h - moveY;
          break;
        case HAND_TYPE.LEFT:
          moveX = this.dragBox.x - nowX;
          this.dragBox.x = nowX;
          this.dragBox.w = this.dragBox.w + moveX;
          break;
        case HAND_TYPE.RIGHT:
          moveX = this.dragBox.x + this.dragBox.w - nowX;
          this.dragBox.w = this.dragBox.w - moveX;
          break;
        case HAND_TYPE.LEFT_TOP:
          moveX = this.dragBox.x - nowX;
          moveY = this.dragBox.y - nowY;
          this.dragBox.y = nowY;
          this.dragBox.h = this.dragBox.h + moveY;
          this.dragBox.x = nowX;
          this.dragBox.w = this.dragBox.w + moveX;
          break;
        case HAND_TYPE.LEFT_BOTTOM:
          moveX = this.dragBox.x - nowX;
          moveY = this.dragBox.y + this.dragBox.h - nowY;
          this.dragBox.x = nowX;
          this.dragBox.w = this.dragBox.w + moveX;
          this.dragBox.h = this.dragBox.h - moveY;
          break;
        case HAND_TYPE.RIGHT_TOP:
          moveX = this.dragBox.x + this.dragBox.w - nowX;
          this.dragBox.w = this.dragBox.w - moveX;
          moveY = this.dragBox.y - nowY;
          this.dragBox.y = nowY;
          this.dragBox.h = this.dragBox.h + moveY;
          break;
        case HAND_TYPE.RIGHT_BOTTOM:
          moveY = this.dragBox.y + this.dragBox.h - nowY;
          this.dragBox.h = this.dragBox.h - moveY;
          moveX = this.dragBox.x + this.dragBox.w - nowX;
          this.dragBox.w = this.dragBox.w - moveX;
          break;
        case HAND_TYPE.MOVE:
          moveY = nowY + this.start.offsetTop;
          moveX = nowX + this.start.offsetLeft;
          this.dragBox.y = moveY;
          this.dragBox.x = moveX;
          break;
        case HAND_TYPE.DRAW:
          this.end.x = disX;
          this.end.y = disY;
          this.setDrawBoxStyle();
          break;

        default:
          break;
      }
    },
    async mouseup(e) {
      e.preventDefault();
      this.isDrag = false;
      if (this.handType != HAND_TYPE.DRAW) return;
      if (this.end.x < 50 && this.end.y < 20) {
        this.clearDrawBoxStyle();
        return;
      }
      let uid = generateUUID();
      try {
        if (!this.currQues.points[this.currPage]) {
          this.currQues.points[this.currPage] = [];
        }
        this.currQues.points[this.currPage].push({
          id: uid,
          qid: this.currQues._id,
          title: this.getQuesTitle(this.currQues),
          x: this.start.x,
          y: this.start.y,
          w: this.end.x,
          h: this.end.y,
        });
      } catch (e) {
        this.currQues.points[this.currPage].push({
          id: uid,
          qid: '',
          title: '',
          active: false,
          x: this.start.x,
          y: this.start.y,
          w: this.end.x,
          h: this.end.y,
        });
      }
      this.clearDrawBoxStyle();
    },
    buildTeamInfo() {
      let quesInfo = {
        id: this.currQues.id,
        firstSmallId: this.currQues._id,
        quesNo: this.currQues.quesNo,
        quesNos: this.currQues.quesNos,
        points: []
      };
      this.currQues.points.forEach((page, index) => {
        page.forEach(point => {
          quesInfo.points.push({
            page: index,
            pos: [
              this.pxConvertMm(point.x),
              this.pxConvertMm(point.y),
              this.pxConvertMm(point.w),
              this.pxConvertMm(point.h),
            ],
          });
        });
      });
      return JSON.stringify(quesInfo)
    },
    async searchStu() {
      await this.getImgUrls();
      await this.getPaperData();
    },
    gotoRecord() {
      this.$router.push({
        name: 'stuAnswerRecordList',
        query: {
          workId: this.subject.personalBookId,
          examId: this.examId
        }
      })
    },
    /**
     * @name 校验是否框选坐标
     */
    checkPointsData(){
      let hasPoint = false;
      if(!this.currQues.points.length) return hasPoint;
      this.currQues.points.forEach((page)=>{
        if(!page.length && !hasPoint){
          hasPoint = false;
        } else {
          hasPoint = true;
        }
      })
      return hasPoint;
    },
    /**
     * @name 保存框选数据
     */
    async saveData() {
      if (!this.checkPointsData()) {
        this.$message({
          message: "请先框选题目",
          type: 'error',
          duration: 1000,
        });
        return;
      }
      let params = {
        workId: this.subject.personalBookId,
        stuId: this.stuInfo.id,
        json: this.buildTeamInfo(),
        schoolId: this.$sessionSave.get('schoolInfo').id,
      };
      let result = await saveStuAnswerAreaReplace(params).catch(err => {
      });;
      if (result && result.code == 1) {
        this.$message({
          message: '修改成功!',
          type: 'success',
          duration: 1000,
        });
      } else {
        this.$message({
          message: result.msg,
          type: 'error',
          duration: 1000,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.exam-mk-container {
  padding: 0 !important;
}

.ques-setting-header {
  .ques-setting-title {
    font-size: 16px;
    font-weight: bold;
  }

  .ques-setting-select {
    .select-item {
      margin-right: 10px;
    }
  }
}

.ques-setting-container {
  height: calc(100% - 40px);

  .ques-setting-left {
    position: relative;
    display: inline-block;
    vertical-align: top;
    width: 100%;
    height: 100%;

    .ques-setting-left-top {
      height: 28px;

      .top-tip {
        display: inline-block;
      }

      .top-page {
        position: absolute;
        right: 0;
        display: inline-block;

        div {
          display: inline-block;
        }

        .page {
          width: 60px;
          height: 28px;
          margin-left: 2px;
          line-height: 24px;
          border: 1px solid;
          border-radius: 5px;
          text-align: center;

          &.active {
            color: #5f9eff;
            border: 1px solid #5f9eff;
          }
        }
      }
    }

    .exam-pdf-container {
      width: 100%;
      // height: calc(100% - 28px);
      // overflow: auto;
      background: #dcdcdc;
      user-select: none;

      .a3 {
        margin: 0 auto;
        width: 315mm;
        height: 223.4mm;
        position: relative;
      }

      .a4 {
        margin: 0 auto;
        width: 210mm;
        height: 297mm;
        position: relative;
      }

      .img {
        // width: 100%;
        height: 100%;
      }

      .ques_box {
        position: absolute;
        border: 1mm solid #03c617;
        font-size: 22px;
        color: #03c617;

        &.draw {
          border: 1mm solid #ff6600;
          color: #ff6600;
        }

        &.active {
          border: 1mm solid #ff6600;
          color: #ff6600;
        }

        .notop {
          position: absolute;
          top: 2px;
          left: 2px;
        }

        .nobottom {
          position: absolute;
          bottom: 2px;
          right: 2px;
        }

        .resize-dot {
          position: absolute;
          width: 8px;
          height: 8px;
          border: 1px solid;
          background: #fff;
          box-sizing: border-box;

          &.top {
            cursor: n-resize;
            top: -4px;
            left: 50%;
            margin-left: -4px;
          }

          &.bottom {
            cursor: s-resize;
            bottom: -4px;
            left: 50%;
            margin-left: -4px;
          }

          &.left {
            top: 50%;
            margin-top: -4px;
            cursor: w-resize;
            left: -4px;
          }

          &.right {
            cursor: e-resize;
            right: -4px;
            top: 50%;
            margin-top: -4px;
          }

          &.top-left {
            margin-top: -4px;
            cursor: nw-resize;
            margin-left: -4px;
          }

          &.top-right {
            cursor: ne-resize;
            right: 0;
            margin-right: -4px;
            margin-top: -4px;
          }

          &.bottom-left {
            cursor: sw-resize;
            bottom: 0;
            margin-left: -4px;
            margin-bottom: -4px;
          }

          &.bottom-right {
            cursor: se-resize;
            right: -4px;
            bottom: 0;
            margin-bottom: -4px;
          }
        }

        .ques-save,
        .ques-edit {
          text-align: center;
          width: 40px;
          font-size: 14px;
          background: #fff;
          z-index: 9;
          position: absolute;
          right: 44px;
          top: 4px;
          color: #5f9eff;
          border: 1px solid #5f9eff;
        }

        .ques-cancel {
          text-align: center;
          width: 40px;
          font-size: 14px;
          background: #fff;
          z-index: 9;
          position: absolute;
          right: 4px;
          top: 4px;
          color: #000;
          border: 1px solid rgb(136, 135, 135);
        }
      }
    }
  }

}
</style>
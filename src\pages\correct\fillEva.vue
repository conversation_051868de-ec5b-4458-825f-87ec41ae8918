<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-03-17 08:45:05
 * @LastEditors: 小圆
-->
<template>
  <el-main class="main-container">
    <el-container class="main-container-mid">
      <el-main class="correct-main">
        <div class="correct-reply">
          <div class="line-btns">
            <template v-if="curQues.lineList.length > 1">
              <el-button
                v-for="(item, index) in curQues.lineList"
                class="line-btn"
                :key="item.index"
                :type="currentQuesLineId == item.quesId ? 'primary' : ''"
                @click="changeQuesLine(item.quesId)"
                >{{ '空' + item.index }}</el-button
              >
              <el-button class="line-btn" :type="currentQuesLineId == '' ? 'primary' : ''" @click="changeQuesLine('')"
                >全部</el-button
              >
            </template>
          </div>
          <div class="high-frequency-btns">
            <el-button class="high-frequency-btn" type="warning" @click="openHighFrequencyDialog"
              >学生高频作答</el-button
            >
          </div>
        </div>
        <div class="correct-answer" :class="{ 'answer--expanded': isAnswerExpanded }" ref="answerContainer">
          正确答案：<span class="answer"><span v-html="quesAnswer"></span> </span>
          <el-button v-if="showToggleButton" type="text" class="answer-toggle" @click="toggleAnswer">
            {{ isAnswerExpanded ? '收起' : '展开' }}
          </el-button>
        </div>

        <div class="correct-body" v-loading="isLoading">
          <div class="correct-ul" v-if="stuList && stuList.length > 0">
            <div
              class="correct-item"
              v-for="(stuItem, index) in stuList"
              :key="index"
              :style="{
                flex: `0 0 calc(${(1 / layout.col) * 100}% - ${layout.gap * 2}px )`,
                marginLeft: layout.gap + 'px',
                marginRight: layout.gap + 'px',
              }"
            >
              <div class="correct-item-body">
                <template
                  v-if="curQues.lineList && curQues.lineList.length && stuItem.quesList && stuItem.quesList.length"
                >
                  <template v-if="isTestEnv">
                    <div class="similar-text" style="position: inherit; display: flex; gap: 10px; flex-wrap: wrap">
                      <div
                        v-for="(item, index) in stuItem.quesList"
                        v-show="currentQuesLineId === '' || currentQuesLineId == item.quesId"
                      >
                        <p>空{{ index + 1 }}</p>
                        <p>相似度：{{ item.similar }}</p>
                        <p>识别结果：<span v-html="getAnswer(item.stuAnswer)"></span></p>
                      </div>
                    </div>
                  </template>

                  <div class="correct-img-container" v-if="isQuesCardLayout">
                    <!-- 题卡合一 -->
                    <div style="position: relative" v-for="(stuRes, stuResIndex) in stuItem.stuNewResList">
                      <img :src="stuRes.source" class="click-element" @click="previewImg(stuRes.source)" />
                      <div
                        v-for="(line, lineIndex) in getResLines(stuItem, stuRes.page ?? stuResIndex)"
                        class="correct-ques-box"
                        :style="{
                          ...getBoxStyle(line.quesId),
                        }"
                      >
                        <img
                          v-if="line.stuScores === getQuesLine(line.quesId)?.score"
                          :src="iconCheckImg"
                          alt=""
                          class="symbol-svg"
                          @click="switchLineScore(stuItem, line.quesId)"
                        />
                        <img
                          v-if="line.stuScores === 0"
                          :src="iconErrorImg"
                          alt=""
                          class="symbol-svg"
                          @click="switchLineScore(stuItem, line.quesId)"
                        />
                        <img
                          v-if="line.stuScores !== getQuesLine(line.quesId)?.score && line.stuScores"
                          :src="iconHalfcheckImg"
                          alt=""
                          class="symbol-svg"
                          @click="switchLineScore(stuItem, line.quesId)"
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    :class="{ 'correct-lines--inline': isQuesCardLayout, 'correct-img-container': !isQuesCardLayout }"
                  >
                    <div
                      v-for="(line, lineIndex) in stuItem.quesList"
                      class="correct-item-main"
                      v-show="currentQuesLineId === '' || currentQuesLineId == line.quesId"
                    >
                      <div class="correct-item--left">
                        <span class="correct-item-label">
                          {{ '空' + (lineIndex + 1) }}
                        </span>
                        <el-image
                          v-if="!isQuesCardLayout"
                          class="correct-img"
                          :key="line.correctUrl"
                          :src="getImgSrc(line.correctUrl)"
                          @click="previewImg(line.correctUrl)"
                        ></el-image>
                      </div>

                      <div class="correct-item--right">
                        <el-button
                          class="correct-btn check"
                          :class="{
                            active: line.stuScores === getQuesLine(line.quesId)?.score,
                          }"
                          @click="setLineScore(stuItem, line.quesId, getQuesLine(line.quesId)?.score)"
                        >
                          <i class="el-icon-check"></i>
                        </el-button>
                        <el-button
                          v-if="halfCheckEnabled"
                          class="correct-btn warning"
                          :class="{
                            active: line.stuScores !== getQuesLine(line.quesId)?.score && line.stuScores,
                          }"
                          @click="setLineScore(stuItem, line.quesId, getQuesLine(line.quesId)?.score / 2)"
                        >
                          <i class="el-icon-check"></i>
                        </el-button>

                        <el-button
                          class="correct-btn error"
                          :class="{
                            active: line.stuScores === 0,
                          }"
                          @click="setLineScore(stuItem, line.quesId, 0)"
                        >
                          <i class="el-icon-close"></i>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </template>

                <template v-else>
                  <div v-if="isTestEnv" class="similar-text">
                    <p>相似度：{{ stuItem.similar }}</p>
                    <p>识别结果：<span v-html="getAnswer(stuItem.stuAnswer)"></span></p>
                  </div>

                  <div v-for="stuRes in stuItem.stuNewResList" class="correct-img-container">
                    <div class="correct-item-main">
                      <div class="correct-item--left">
                        <el-image
                          class="correct-img"
                          :key="stuRes.source"
                          :src="getImgSrc(stuRes.source)"
                          @click="previewImg(stuRes.source)"
                        ></el-image>
                      </div>

                      <div class="correct-item--right">
                        <el-button
                          class="correct-btn check"
                          :class="{ active: stuItem.stuScores === curQues.quesScore }"
                          @click="setScore(stuItem, curQues.quesScore)"
                        >
                          <i class="el-icon-check"></i>
                        </el-button>
                        <el-button
                          v-if="halfCheckEnabled"
                          class="correct-btn warning"
                          :class="{ active: stuItem.stuScores != curQues.quesScore && stuItem.stuScores != 0 }"
                          @click="setScore(stuItem, curQues.quesScore / 2)"
                        >
                          <i class="el-icon-check"></i>
                        </el-button>
                        <el-button
                          class="correct-btn error"
                          :class="{ active: stuItem.stuScores === 0 }"
                          @click="setScore(stuItem, 0)"
                        >
                          <i class="el-icon-close"></i>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
          <no-data v-else></no-data>
        </div>
      </el-main>

      <!-- 底部按钮 -->
      <el-footer class="corrent-foot-bar">
        <el-pagination
          v-if="pagination.total_rows > 0"
          background
          :pager-count="5"
          class="corrent-foot-pagination text-center"
          layout="total, prev, pager, next"
          @current-change="handleCurrentChange"
          :current-page.sync="pagination.page"
          :page-size="pagination.limit"
          :total="pagination.total_rows"
        >
        </el-pagination>
        <el-button type="primary" @click="submitData" :loading="submitLoading" v-if="stuList.length"
          >提交批改</el-button
        >
      </el-footer>
    </el-container>

    <ImgPreview v-if="isShowPreviewImg" :imgList="previewImgList" @close="isShowPreviewImg = false"> </ImgPreview>
    <HighFrequencyDialog
      v-if="highFrequencyDialogVisible"
      :curQues="curQues"
      :type="type"
      :smartType="smartType"
      :halfCheckEnabled="halfCheckEnabled"
      :quesAnswer="quesAnswer"
      @submit-success="handleHightFrequencySubmitSuccess"
      @closed="highFrequencyDialogVisible = false"
    >
    </HighFrequencyDialog>
  </el-main>
</template>

<script lang="ts">
import NoData from '@/components/noData.vue';
import { getSmartCorrectStuList, saveBatchCorrectResult } from '@/service/api';
import { replaceALiUrl } from '@/utils/common';
import { Component, Mixins, Prop, Vue, Watch } from 'vue-property-decorator';
import { PREVIEW_TYPE, SMART_TYPE, SmartCorrectStu, TeaCorrectQues } from './types';
import { setCroppedImageUrl } from '@/utils/index';
import CorrectMixin from './correct.mixin.vue';
import { ICARD_TYPE } from '@/typings/card';
import { isSupportMathSubject } from '@/utils';
import HighFrequencyDialog from './components/highFrequencyDialog.vue';

@Component({
  components: {
    HighFrequencyDialog,
    NoData,
  },
})
export default class FillEva extends Mixins(CorrectMixin) {
  iconCheckImg = require('@/assets/icon_check.png');
  iconErrorImg = require('@/assets/icon_error.png');
  iconHalfcheckImg = require('@/assets/icon_halfcheck.png');
  /** 当前题目 */
  @Prop({ default: () => null }) curQues: TeaCorrectQues | null;
  /** 作答模式 1:作答错误 2:作答正确 */
  @Prop({ default: PREVIEW_TYPE.ONLY_ERROR }) type;
  /** 批改状态 0:待复核 1:已复核 */
  @Prop({ default: SMART_TYPE.WAIT_REVIEW }) smartType;
  /** 相似度 */
  @Prop() similar;
  /** 学科中心编码 */
  @Prop({ default: 'ENGLISH' }) subjectCenterCode: string;
  /** 布局设置 */
  @Prop({ default: () => ({ col: 2, scale: 1, gap: 10 }) }) layout: { col: number; scale: number; gap: number };
  // 学生列表
  stuList: SmartCorrectStu[] = [];
  // 分页器
  pagination = {
    page: 1, // 页码
    limit: 50, // 每页数据大小
    total_rows: 0, // 数据总数
  };

  // 是否正在加载
  isLoading = false;
  // 等待提交
  submitLoading = false;
  // 是否展开答案
  isAnswerExpanded = false;
  // 是否需要展开收起按钮
  needToggleButton = false;
  // 学生高频作答对话框是否可见
  highFrequencyDialogVisible = false;
  // 当前空id
  currentQuesLineId = '';

  isMathSubject() {
    return isSupportMathSubject(Number(this.$route.query.subjectId));
  }

  // 是否是测试环境
  get isTestEnv() {
    return (
      process.env.NODE_ENV == 'development' ||
      process.env.VUE_APP_BASE_API == 'https://test.iclass30.com' ||
      this.$route.query.test == '1'
    );
  }

  getAnswer(answer) {
    if (answer == '$$' || answer == '\\$\\$') {
      return '<span style="color: red">（该题由老师批改）</span>';
    }
    if (!answer && answer !== 0) return '';
    if (this.isMathSubject()) {
      answer = answer.replace(/\\\(|\\\)/g, ''); // 去掉括号
      try {
        const res = MathJax.tex2svg(answer, { display: false });
        return `<span data-latex="${answer}" class="math-tex-svg">${res.outerHTML}</span>`;
      } catch (error) {
        return `<span style="color: red">${answer}</span>`;
      }
    }
    return answer;
  }

  // 获取题目答案
  get quesAnswer() {
    if (this.curQues.lineList && this.curQues.lineList.length) {
      let lineList = this.curQues.lineList;
      if (this.currentQuesLineId) {
        lineList = [this.getQuesLine(this.currentQuesLineId)];
      }
      let str = '';
      lineList.forEach((lineItem, lineIndex) => {
        const answers = lineItem.answer;
        str += '<span>';
        str += `<span style="color: #333"> 空${lineItem.index}: </span>`;
        const answer = answers.map((item, index) => `<span> ${this.getAnswer(item)}</span>`).join('/');
        str += `${answer}`;
        if (lineIndex < lineList.length - 1) {
          str += `<span style="color: #333;"> ； </span>`;
        }
        str += '</span>';
      });
      return str;
    }
    const answers = this.curQues.answer;
    if (!answers) return '';
    if (Array.isArray(answers)) {
      return answers.map(t => this.getAnswer(t)).join('；');
    } else {
      return this.getAnswer(answers);
    }
  }

  // 是否启用半批
  get halfCheckEnabled() {
    return this.subjectCenterCode != 'ENGLISH';
  }

  // 是否是题卡合一
  get isQuesCardLayout() {
    return this.curQues.cardType == ICARD_TYPE.QUESCARD && this.currentQuesLineId == '';
  }

  // 是否显示展开/收起按钮
  get showToggleButton() {
    return this.needToggleButton;
  }

  @Watch('curQues', { deep: true, immediate: true })
  onCurQuesChange(newVal: TeaCorrectQues) {
    if (this.curQues.lineList && this.curQues.lineList.length) {
      this.currentQuesLineId = this.curQues.lineList[0].quesId;
    } else {
      this.currentQuesLineId = '';
    }
    this.checkToggleButton();
  }

  mounted() {
    this.checkToggleButton();
  }

  // 替换阿里云图片地址
  replaceALiUrl(url) {
    return replaceALiUrl(url);
  }

  // 展开/收起答案
  toggleAnswer() {
    this.isAnswerExpanded = !this.isAnswerExpanded;
  }

  // 检查是否需要显示展开/收起按钮
  checkToggleButton() {
    this.$nextTick(() => {
      const container = this.$refs.answerContainer as HTMLElement;
      if (container) {
        // 临时移除最大高度限制来获取真实高度
        const originalMaxHeight = container.style.maxHeight;
        const originalOverflow = container.style.overflow;

        container.style.maxHeight = 'none';
        container.style.overflow = 'visible';

        const realHeight = container.scrollHeight;
        const maxHeight = 88;

        // 恢复原始样式
        container.style.maxHeight = originalMaxHeight;
        container.style.overflow = originalOverflow;

        this.needToggleButton = realHeight > maxHeight;
      }
    });
  }

  // 获取题目行样式
  getBoxStyle(quesId: string): any {
    const bigQuesPoints = this.curQues.points;
    const stuQuesPoints = this.curQues.lineList.find(item => item.quesId == quesId)?.points;
    if (!stuQuesPoints) return;
    const style = {
      position: 'absolute',
      left: '0',
      top: '0',
      width: '0',
      height: '0',
    };
    stuQuesPoints.forEach(item => {
      const bigQuesPoint = bigQuesPoints.find(bigItem => bigItem.page == item.page);
      const scale = 6;
      if (bigQuesPoint) {
        style.left = (Number(item.pos[0]) - Number(bigQuesPoint.pos[0])) * scale + 'px';
        style.top = (Number(item.pos[1]) - Number(bigQuesPoint.pos[1])) * scale + 'px';
        style.width = Number(item.pos[2]) * scale + 'px';
        style.height = Number(item.pos[3]) * scale + 'px';
      }
    });

    return style;
  }

  // 获取题目行
  getResLines(stuItem: SmartCorrectStu, page: number) {
    const resLines = stuItem.quesList;
    const lineList = this.curQues.lineList;
    return resLines.filter(resLine => {
      const line = lineList.find(line => line.quesId == resLine.quesId);
      return line && line.points && line.points.some(point => point.page == page);
    });
  }

  // 获取图片源
  getImgSrc(url) {
    let wScale = this.layout.scale;
    let hScale = 0.5 + 0.5 * this.layout.scale;
    return setCroppedImageUrl(replaceALiUrl(url), wScale, hScale);
  }

  // 获取学生列表
  async getStuList(option = { page: this.pagination.page }) {
    this.isLoading = true;
    this.stuList = [];
    let params: any = {
      type: this.type,
      workId: this.$route.query.workId,
      userId: this.$sessionSave.get('loginInfo').id,
      quesId: this.curQues.quesId,
      quesLevel: this.curQues.quesLevel,
      schoolId: this.$sessionSave.get('schoolInfo').id,
      page: option.page,
      sort: 0,
      limit: this.pagination.limit,
      smartType: this.smartType,
      similar: this.smartType == SMART_TYPE.WAIT_REVIEW && this.type == PREVIEW_TYPE.ONLY_ERROR ? this.similar : 0,
      correctVersion: 4,
      childQueId: this.currentQuesLineId,
    };
    if (this.$route.query.classId) {
      params.classId = this.$route.query.classId;
      params.source = 1;
    }
    try {
      const res = await getSmartCorrectStuList(params);
      if (this.checkPause(res.code, res.msg)) return;
      this.stuList = res.data.rows;
      this.pagination.page = res.data.page;
      this.pagination.total_rows = res.data.total_rows;
    } catch (e) {
      this.stuList = [];
      this.pagination.page = 1;
      this.pagination.total_rows = 0;
    } finally {
      this.isLoading = false;
    }
  }

  // 提交批改
  async submitData() {
    this.submitLoading = true;
    try {
      let correctJson: any = [];
      this.stuList.forEach(item => {
        if (item.quesList && item.quesList.length) {
          item.quesList.forEach(line => {
            if (this.currentQuesLineId && this.currentQuesLineId != line.quesId) return;
            correctJson.push({
              shwId: item.shwId,
              isRight: line.stuScores == this.getQuesLine(line.quesId)?.score ? 1 : 0,
              childQueId: line.quesId,
              score: line.stuScores,
            });
          });
        } else {
          correctJson.push({
            shwId: item.shwId,
            isRight: item.stuScores == this.curQues.quesScore ? 1 : 0,
          });
        }
      });
      let params: any = {
        workId: this.$route.query.workId,
        userId: this.$sessionSave.get('loginInfo').id,
        realName: this.$sessionSave.get('loginInfo').realname,
        schoolId: this.$sessionSave.get('schoolInfo').id,
        quesId: this.curQues.quesId,
        score: this.curQues.quesScore,
        correctJson: JSON.stringify(correctJson),
        correctVersion: 4,
      };
      if (this.$route.query.classId) {
        params.source = 1;
      }
      const res = await saveBatchCorrectResult(params);
      this.submitLoading = false;
      if (this.checkPause(res.code, res.msg)) return;
      if (res.code != 1) {
        return;
      }
      this.$message.success('批改成功');
      if (this.pagination.total_rows <= this.pagination.limit && this.type == PREVIEW_TYPE.ONLY_ERROR) {
        if (this.currentQuesLineId && this.getNextQuesLine()) {
          this.changeQuesLine(this.getNextQuesLine().quesId);
        } else {
          this.$emit('submit-next');
        }
      } else {
        this.$emit('submit-current');
      }
    } catch (error) {
      this.submitLoading = false;
    }
  }

  // 分页
  handleCurrentChange(val) {
    this.pagination.page = val;
    this.getStuList();
  }

  // 切换当前小空
  changeQuesLine(quesLineId) {
    this.currentQuesLineId = quesLineId;
    this.handleCurrentChange(1);
  }

  // 设置分数
  setScore(stu, score) {
    this.$set(stu, 'stuScores', score);
  }

  // 获取题目行
  getQuesLine(quesId) {
    return this.curQues.lineList.find(item => item.quesId == quesId);
  }

  // 获取下一个题目行
  getNextQuesLine() {
    const index = this.curQues.lineList.findIndex(item => item.quesId == this.currentQuesLineId);
    return this.curQues.lineList[index + 1];
  }

  // 设置行分数
  setLineScore(stu, quesId, score) {
    const index = stu.quesList.findIndex(item => item.quesId == quesId);
    this.$set(stu.quesList[index], 'stuScores', score);
  }

  // 切换行分数
  switchLineScore(stu, quesId) {
    const quesLine = this.getQuesLine(quesId);
    let scoreList = [quesLine.score, 0];
    if (this.halfCheckEnabled) {
      scoreList = [quesLine.score, quesLine.score / 2, 0];
    }
    const stuLine = stu.quesList.find(item => item.quesId == quesId);
    let nextIndex = scoreList.findIndex(it => it == stuLine?.stuScores) + 1;
    if (nextIndex >= scoreList.length) {
      nextIndex = 0;
    }
    this.setLineScore(stu, quesId, scoreList[nextIndex]);
  }

  // 打开学生高频作答对话框
  openHighFrequencyDialog() {
    this.highFrequencyDialogVisible = true;
  }

  // 处理高频作答提交成功
  handleHightFrequencySubmitSuccess() {
    this.handleCurrentChange(1);
    this.highFrequencyDialogVisible = false;
  }
}
</script>

<style scoped lang="scss">
// 主色
$mainColor: #3e73f6;
// 主要字号
$fontSize: 18px;
// 大标题字号
$bFontSize: 20px;
// 正文字号
$sFontSize: 16px;
// 头部,底部背景色
$borderBg: #f6f8fc;
// 提示信息颜色
$tipTextColor: rgba(165, 172, 189, 1);

.tip-text {
  color: $tipTextColor;
  font-size: 12px;
}

.main-container {
  position: relative;
  padding: 0;
}

.main-container-mid {
  width: 100%;
  height: 100%;
  position: relative;

  ::v-deep .small-font {
    font-size: 14px;
    width: 100%;
  }

  .correct-main {
    display: flex;
    flex-direction: column;
    position: relative;
    width: 100%;
    overflow: auto;
    padding: 0;
    background-color: #fff;
  }

  .correct-body {
    // min-height: calc(100vh - 300px);
    flex: 1;
    overflow: auto;
  }

  .correct-ul {
    display: flex;
    flex-wrap: wrap;
  }

  .correct-item {
    position: relative;
    margin-top: 10px;
    margin-bottom: 10px;

    width: 100%;
    min-height: 75px;

    .correct-statu {
      display: block;
      height: 24px;
      line-height: 24px;

      font-size: 14px;
      font-weight: 400;
      color: #eb4140;
      text-align: left;
    }

    .correct-item-body {
      padding: 8px;
      background: #fafafa; // 添加浅色背景区分容器
      border: 1px solid #e8e8e8; // 添加边框
      border-radius: 6px; // 添加圆角
      transition: all 0.2s ease; // 添加过渡效果

      &:hover {
        background: #f5f5f5; // 悬停时背景变化
        border-color: #d4d4d4; // 悬停时边框颜色变化
      }

      &:last-child {
        margin-bottom: 0; // 最后一个容器不需要底部间距
      }
    }

    .correct-item-main {
      display: flex;
      align-items: center;
      height: 100%;
    }

    .correct-lines--inline {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }

    .correct-img-container {
      position: relative;
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 8px;
      width: 100%;
      height: 100%;
    }

    .correct-ques-box {
      pointer-events: none;
    }

    .symbol-svg {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 30px;
      height: 30px;
      z-index: 10;
      cursor: pointer;
      opacity: 0.9;
      pointer-events: all;

      &:hover {
        opacity: 1;
      }
    }

    .similar-text {
      position: absolute;
      top: 0;
      left: 0;
      font-size: 13px;
      color: #c8161d;
      z-index: 1;
    }

    .correct-img {
      border: 1px solid #ccc;
      border-radius: 4px;
      max-width: 100%;
      max-height: 100%;
      cursor: pointer;
      user-select: none;
      -webkit-user-drag: none;
      -khtml-user-drag: none;
      -moz-user-drag: none;
      -o-user-drag: none;
    }

    .correct-item--left {
      display: flex;
      align-items: center;
      flex: 1;
      width: 100%;
      height: 100%;
      overflow: auto;
    }

    .correct-item--right {
      flex: none;
      width: 125px;
      min-width: 125px;
      margin-left: 8px;
    }

    .correct-item-label {
      flex: none;
      margin-right: 8px;
    }

    .correct-btn {
      display: inline-block;
      padding: 0;
      margin-left: 8px;
      width: 32px;
      height: 32px;
      line-height: 32px;
      font-size: 18px;
      background: #f8f9ff;
      border: 1px solid #e5e5e5;
      border-radius: 16px;

      color: #b3b3b3;
      cursor: pointer;

      &.active {
        color: #fff;
        border: 0;
      }

      &.check {
        &.active {
          background: #67c23a;
        }
      }

      &.error {
        &.active {
          background: #f94e4e;
        }
      }

      &.warning {
        .el-icon-check {
          position: relative;
          &::after {
            content: '|';
            position: absolute;
            left: 9px;
            top: 2px;
            transform: rotate(-45deg);
            font-size: 12px;
          }
        }

        &.active {
          background: #e6a23c;
        }
      }

      &:first-child {
        margin-left: 0;
      }
    }

    .correct-icon {
      vertical-align: middle;
      width: 15px;
      height: 15px;
    }
  }

  .correct-answer {
    position: relative;
    margin-left: 20px;
    margin-right: 20px;
    margin-bottom: 10px;
    padding-left: 16px;
    padding-right: 20px; // 为按钮预留空间
    background: rgba(211, 239, 210, 0.5);
    border-radius: 4px;
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #303233;
    text-align: left;
    line-height: 44px;

    overflow-x: auto;

    max-height: 88px;
    overflow-y: hidden;
    transition: max-height 0.3s ease; // 添加过渡动画

    &.answer--expanded {
      max-height: none;
      overflow-y: auto;
    }

    .answer {
      color: #27991f;
      border: 0;
    }

    .answer-toggle {
      position: absolute;
      right: 4px;
      bottom: -2px;
      transform: translateY(-50%);
      padding: 0;
      color: #409eff;
      font-size: 14px;

      &:hover {
        color: #66b3ff;
      }
    }
  }

  .noData {
    position: absolute;
    height: calc(100% - 70px);
    overflow: hidden;
    top: 70px;
    width: 100%;
    background-color: $borderBg;

    &:after {
      display: flex;
      content: '该学生未作答';
      align-items: center;
      justify-content: center;
      height: 100%;
      font-size: 26px;
      color: $mainColor;
    }
  }

  .noStu {
    position: absolute;
    height: calc(100% - 70px);
    overflow: hidden;
    top: 60px;
    width: 100%;

    &:after {
      display: flex;
      content: '当前题目无批改学生!';
      align-items: center;
      justify-content: center;
      height: 100%;
      margin-top: 40px;
      font-size: $fontSize;
      color: $tipTextColor;
    }
  }
}

.correct-reply {
  display: flex;
  justify-content: space-between;
  gap: 10px;

  margin-bottom: 10px;
  font-size: 18px;
  font-family: Microsoft YaHei;
  text-align: left;
  color: #161e26;
  padding-left: 20px;
  padding-right: 20px;

  .line-btns {
    display: flex;
    overflow-x: auto;
  }

  .line-btn {
    padding-left: 8px;
    padding-right: 8px;
  }

  .high-frequency-btns {
    flex: none;
  }
}

// 底部
.corrent-foot-bar {
  background-color: #fff;
  height: 80px !important;
  // line-height: 40px;
  text-align: center;

  .corrent-foot-pagination {
    // position: absolute;
    right: 20px;
    text-align: right;
  }
}
</style>

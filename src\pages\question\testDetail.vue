<template>
  <div style="height: 100%">
    <!-- 头部标题栏 -->
    <el-header class="detail-title">
      <el-page-header @back="goBack"></el-page-header>
      <span>组卷学科：</span>
      <!-- <el-select
        size="small"
        class="select-subject"
        v-model="currentSubject"
        @change="subjectChange"
      >
        <el-option v-for="item in subjectList" :key="item.id" :label="item.name" :value="item.id">
        </el-option>
      </el-select> -->
      <span>{{ subjectName }}</span>
    </el-header>
    <!--主体-->
    <div
      :style="{
        height: 'calc(100% - 70px)',
        display: 'flex',
      }"
    >
      <el-aside class="detail-left" width="27%">
        <div>
          <el-header class="detail-left-title left-head" height="50px">
            <el-row
              class="blueDot ellipsis head-name"
              style="margin-left: 10px; max-width: calc(100% - 115px)"
              type="flex"
              justify="center"
              align="middle"
              >{{ bookAlias }}</el-row
            >
            <el-button type="text" style="margin-right: 10px" @click="goBack">
              <span class="switch-book">切换教辅</span>
            </el-button>
          </el-header>
          <el-tree
            :data="treeData"
            :props="{ children: 'children', label: 'title' }"
            node-key="code"
            ref="tree"
            :indent="22"
            :highlight-current="true"
            :default-expanded-keys="[curBookData.id]"
            :default-checked-keys="[curBookData.id]"
            @node-click="handleNodeClick"
          >
            <span slot-scope="{ node, data }" class="el-tree-node__label" :title="node.label">
              {{ node.label }}
            </span>
          </el-tree>
        </div>
      </el-aside>
      <el-main class="detail-main">
        <div
          v-if="quesList.length > 0"
          v-infinite-scroll="loadQuesList"
          :infinite-scroll-disabled="loading"
          infinite-scroll-immediate="false"
          class="ques-list"
          ref="quesListRef"
        >
          <el-row type="flex" align="middle" justify="space-between" class="ques-info">
            <span>共{{ quesIdList.length }}题</span>
            <div>
              <el-button v-if="testBankId" @click="gotoCard" style="margin-right: 10px;">去制卡</el-button>
              <el-checkbox v-model="isSelectAll" @change="switchChooseAll"
              >整套试卷全部选入</el-checkbox
            ></div>
          </el-row>
          <template v-for="(item, index) in quesList">
            <p
              v-if="item.bigTitle != ''"
              :key="'key_' + index"
              class="qs-title"
              v-html="item.bigTitle"
            ></p>
            <ques-item
              v-if="quesList.length > 0"
              :key="index"
              :stem="JSON.stringify(item.data)"
              :id="item.qId"
              :isShowBtn="true"
              :isShowComment="false"
              :quesTypeList="quesTypeList"
              :selectIds="selectIds"
              @select-ques="onSelectQues"
            ></ques-item>
          </template>
        </div>
        <div class="no-data" v-else>
          <no-data text="没有相应的题目，换其他条件试试吧~"></no-data>
        </div>
        <!-- <el-backtop target=".ques-list"></el-backtop> -->
        <!-- <ques-bone @ques-bone-event="boneEventHandler" :hwType="hwType"
          v-if="isSchool && openType != WORK_OPEN_TYPE.BANK"></ques-bone> -->
      </el-main>
    </div>
    <!--纠错弹窗-->
    <!-- <error-back v-if="isShowCheckError" :hwType="hwType" :id="curQuesId" @close-dialog="isShowCheckError = false">
    </error-back> -->
    <!--试卷袋-->
    <fixedPaper
      v-on="$listeners"
      ref="fixedPaper"
      class="fixed-box"
      :quesIds="selectIds"
      @update="updateData"
      @getParams="getParams"
    ></fixedPaper>
  </div>
</template>

<script>
import { isNullOrUndefined, getQueryString } from "@/utils";
import {
  getStructureCatalogList,
  getQuesByCatalog,
  getQueTypeListBySubId,
  getSchoolBookTbIdAPI,
} from "@/service/testbank";
import QuesItem from "@/components/ClassTest/index.vue";
import NoData from "@/components/noData.vue";
import fixedPaper from "@/components/fixedPaper";
import UserRole from "@/utils/UserRole";
import { localSave, sessionSave } from "@/utils";
import { mapState, mapGetters } from "vuex";
import { convertHtml } from "@/service/pbook";
import { getToken } from "@/service/auth";

export default {
  data() {
    return {
      // 书本id
      bookId: getQueryString("bookCode"),
      // 是否校本教辅
      isSchool: false,
      // 当前题目id
      curQuesId: "",
      // 是否展示纠错弹窗
      isShowCheckError: false,
      // 目录数据
      treeData: [],
      // 题目列表
      quesList: [],
      // 当前页题目id列表
      quesIdList: [],
      // 书本阿里名称
      bookAlias: getQueryString("bookAlias"),
      // 当前书本信息
      curBookData: { title: "", id: "" },
      // 页码
      page: 1,
      // 请求条目限制
      limit: 10,
      // 是否正在加载
      loading: false,
      //题型列表
      quesTypeList: [],
      //已选入试卷袋的题目
      quesInfo: [],
      //学科列表
      subjectList: [],
      //当前选择的学科
      currentSubject: "",
      isChooseAll: false,
      testBankId:""
    };
  },
  computed: {
    ...mapState(["teachParams"]),
    ...mapGetters(["subjectMap", "loginInfo"]),
    selectIds() {
      let totalList = [],
        ids = [];
      this.quesInfo.length &&
        this.quesInfo.forEach((item) => {
          totalList = totalList.concat(item.data);
        });
      totalList.length &&
        totalList.forEach((item) => {
          if (item.data.length) {
            ids = ids.concat(
              item.data.map((sub) => {
                return sub.id;
              })
            );
          }
        });
      return ids;
    },
    isSelectAll: {
      get() {
        // 获取值
        let isSelect = true;
        if (!this.quesList.length) return;
        this.quesList.forEach((item) => {
          if (this.selectIds.indexOf(item.qId) < 0) {
            isSelect = false;
          }
        });
        return isSelect;
      },
      set(val) {
        // 设置新值
      },
    },
  },
  async created() {
    await this.initSubject();
    await this.getQueTypeListBySubId();
    this.getTreeDate(true);
    this.reload();
  },
  components: {
    QuesItem,
    NoData,
    fixedPaper,
  },
  mounted() {
    this.currentSubject = this.$route.query.subjectId;
  },
  methods: {
    reload() {
      this.$nextTick(() => {
        this.$refs.fixedPaper && this.$refs.fixedPaper.getPersonalTestBank();
      });
    },
    async initSubject() {
      let allSubjectList = localSave.get("SUBJECT_LIST");
      this.subjectName =
        allSubjectList.filter((item) => {
          return item.id == this.$route.query.subjectId;
        })[0].name || "";
    },
    /**
     * @name: 返回
     */
    goBack() {
      // this.$router.back();
      this.$router.push({ path: `/home/<USER>/test` });
    },
    // 更新加入试卷的数据
    updateData(quesInfo) {
      this.quesInfo = quesInfo || this.quesInfo;
    },
    getParams() {
      return {
        schoolId: this.$sessionSave.get("schoolInfo").id,
        subjectId: this.currentSubject,
      };
    },
    /**
     * @name:切换学科
     */
    subjectChange() {
      let subject = this.subjectMap[this.currentSubject];
      sessionSave.set("currentSubject", subject);
      this.reload();
    },
    /**
     * @name:获取题型列表
     */
    getQueTypeListBySubId() {
      getQueTypeListBySubId({
        subId: this.currentSubject,
        hwTypeCode: 104,
        schoolId:
          this.$sessionSave.get("schoolInfo").id || this.$sessionSave.get("loginInfo").schoolid,
      })
        .then((res) => {
          this.quesTypeList = res.data;
        })
        .catch((err) => {});
    },
    /**
     * @name: 获取目录树
     * @param isAuto 是否自动切换
     * */
    async getTreeDate(isAuto = false) {
      await getStructureCatalogList({ bookCode: this.bookId }).then((res) => {
        if (res.data) {
          this.treeData = res.data;
          // let treeId = window.CACHE.getCache(this.cacheId);
          let item;
          // if (!isNullOrUndefined(treeId)) {
          //   item = res.getByKey("code", treeId);
          // }
          this.handleNodeClick(res.data[0], isAuto);
        }
      });
    },
    /**
     * @name: 目录树点击事件
     * @param data 当前目录树数据
     * @param isAuto 是否自动触发
     */
    handleNodeClick(data, isAuto = false) {
      if (data.code != this.curBookData.id) {
        this.curBookData = {
          id: data.code,
          title: data.title,
        };
        this.page = 1;
      }
      this.$nextTick(() => {
        let dom = this.$refs.tree;
        if (!isNullOrUndefined(dom)) {
          dom.setCurrentKey(data.code);
        }
        if (this.$refs.quesListRef) this.$refs.quesListRef.scrollTop = 0;
      });
      this.getQuesList(isAuto);
    },
    /**
     * @name: 获取题目列表
     * @param isAuto 是否自动触发
     */
    getQuesList(isAuto = false) {
      this.loading = true;
      getQuesByCatalog({
        code: this.curBookData.id,
        page: this.page,
        limit: this.limit,
      })
        .then((res) => {
          if (res.data && res.data.length > 0) {
            let list = [];
            let idList = [];
            this.testBankId = res.data[0].testBankId;
            res.data.forEach((item) => {
              item.data.forEach((sq) => {
                let info = {
                  data: sq,
                  bid: item.bigQId,
                  bigTitle: item.bigTitle,
                  qId: sq.qId,
                };
                info.data = this.converQues(info.data);
                list.push(info);
                idList.push(sq.qId);
              });
            });
            this.quesList = list;
            this.quesIdList = idList;
          } else {
            this.quesList = [];
            this.quesIdList = [];
            // 自动切换到有题目的树
            if (isAuto) {
              let curTree = this.treeData.getByKey("code", this.curBookData.id);
              let item = "";
              if (!isNullOrUndefined(curTree)) {
                if (curTree.children.length > 0) {
                  item = curTree.children[0];
                } else {
                  item = this.treeData.getNextByKey("code", this.curBookData.id);
                }
              }
              if (!isNullOrUndefined(item)) {
                this.handleNodeClick(item);
              }
            }
          }
          // this.mathJaxUpdate();

          // console.log("this.quesList", this.quesList);
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.quesList = [];
          this.quesIdList = [];
          this.isChooseAll = false;
        });
    },
    mathJaxUpdate() {
      let success = false;
      if (MathJax && MathJax.startup) {
        try {
          MathJax.startup.getComponents();
          MathJax.typeset();
          success = true;
        } catch (e) {}
      }
    },
    converQues(data) {
      data.desc_html = convertHtml(data.desc_html);
      data.q_html = convertHtml(data.q_html);
      data.qs.forEach((it) => {
        it.desc_html = convertHtml(it.desc_html);
        it.q_html = convertHtml(it.q_html);
        if (it.opts_htmls) {
          let list = [];
          for (let j = 0; j < it.opts_htmls.length; j++) {
            list.push(convertHtml(it.opts_htmls[j]));
          }
          it.optionText = list;
        }
      });
      return data;
    },
    /**
     * @name: 下拉加载题目
     * */
    loadQuesList() {
      this.page++;
      this.getQuesList();
    },
    /**
     * @name:加入、移除试卷袋
     */
    onSelectQues(item) {
      this.$refs.fixedPaper.selectQues(item, true);
    },
    /**
     * @name:全部选入试卷袋
     */
    switchChooseAll() {
      this.quesList.forEach((item) => {
        let content = {};
        content = JSON.parse(JSON.stringify(item.data));
        content.data = JSON.parse(JSON.stringify(item.data));
        this.$set(item, "content", content);
      });
      this.$refs.fixedPaper.selectAll(this.quesList, this.isSelectAll);
    },
    async getCardId() {
      const res = await getSchoolBookTbIdAPI({
        id:this.testBankId
      })
      if(res.code == 1){
        return res.data.id;
      }
      return this.testBankId;
    },
    async gotoCard(){
      let id = await this.getCardId();
      if(!id)return;
      let token = getToken();
      let routeData =
      process.env.VUE_APP_CARDURL +
        `?id=${id}&token=${token}`;
      window.open(routeData, "_blank");
    },
    // 切换学校后更新数据
    changeSchool() {
      this.currentSubject = "";
      this.initSubject();
    },
  },
};
</script>

<style scoped lang="scss">
.detail-title {
  display: flex;
  // justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  height: 50px !important;
  background-color: #fff;
}
.el-tree-node__label {
  font-size: 16px;
}
/*蓝色小点*/
.blueDot:before {
  display: inline-block;
  content: "";
  width: 5px;
  height: 100%;
  min-height: 18px;
  margin-right: 12px;
  border-radius: 3px;
  vertical-align: middle;
  background-color: #3e73f6;
}
.blueDot.blueDot-circle:before {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  min-height: 8px;
  vertical-align: middle;
}
.bank-page {
  position: relative;
  height: 100%;

  ::v-deep .el-main {
    flex: unset;
  }
}
.bank-aside {
  position: absolute;
  right: 10px;
  height: 100%;
  ::v-deep .aside-box {
    position: relative;
    height: 100%;
    ::v-deep .el-header {
      position: absolute;
      bottom: 0;
      width: 100%;
      z-index: 20;
    }
    ::v-deep .el-tree {
      height: calc(100% - 50px);
      overflow: auto;
      padding-top: 10px;
      font-size: 16px;
    }
  }
}
.detail-left {
  background-color: #fff;
  overflow-y: auto;
  overflow-x: hidden;
  .detail-left-title {
    height: 60px;
    .switch-book {
      display: inline-block;
      padding-left: 20px;
      background: url("../../assets/bank/change_book.png") no-repeat left center/18px 16px;
    }
  }
}
.detail-main {
  position: relative;
  display: flex;
  justify-content: flex-end;
  padding: 0;
  background-color: #fff;
  margin: 0 0 0 10px;
  &.bank {
    width: calc(78% - 30px);
  }
  .ques-list {
    overflow-y: auto;
    padding: 0 10px;
    width: 100%;

    &.isSchool {
      border-right: 10px solid #eff1fa;
    }

    .qs-title {
      text-align: left;
      padding: 10px 0;
      font-size: 18px;
    }

    .ques-info {
      height: 45px;
      position: sticky;
      top: 0;
      z-index: 1;
      background-color: #fff;
      font-size: 18px;
    }
  }
}
.no-data {
  position: absolute;
  left: 10px;
  top: 0;
  width: calc(100% - 20px);
  height: 100%;
}
.left-head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f6f8fc;
  padding: unset;
  .head-name {
    align-items: center;
    margin-left: 10px;
    display: inline-flex;
    font: 600 18px/50px 微软雅黑, \5b8b\4f53;
    color: rgba(117, 124, 140, 1);
  }
}
/*清除浮动*/
.clearfix:after {
  content: "";
  display: block;
  clear: both;
}

/*溢出文字省略号显示*/
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
<style lang="scss">
.el-tree-node__expand-icon:not(.is-leaf) {
  background-image: url("../../assets/bank/close_nor.png") !important;

  &:hover {
    background-image: url("../../assets/bank/close_hover.png") !important;
  }

  &.expanded {
    background-image: url("../../assets/bank/open_nor.png");

    &:hover {
      background-image: url("../../assets/bank/open_hover.png");
    }
  }
}

<template>
  <div>
    <div v-loading="tableLoading">
      <div v-if="tableData.length">
        <base-table
          v-for="(item, i) in tableData"
          class="table"
          :key="i"
          :data="tableData[i]"
          :column="tableColumns[i]"
          :span-method="
            ({ row, column, rowIndex, columnIndex }) =>
              handleSpanMethod({ row, column, rowIndex, columnIndex }, i)
          "
          v-bind="getTableAttr()"
          v-drag-table
        ></base-table>
      </div>

      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';
import FilterModule from '../plugins/FilterModule';

@Component({
  components: {},
})
export default class Index extends Mixins(TableCommon) {
  // 获取数据
  async getTableData() {
    this.tableColumns = [];
    this.tableData = [];
    this.tableLoading = true;
    await this.$nextTick();

    const promiseArr = [this.getTable(0), this.getTable(1)];

    const funcs = await Promise.all(promiseArr);

    funcs.forEach(res => {
      const data = res.data;
      data.table = this.setColumnProp(data.table);
      this.tableColumns.push(data.table);
      this.tableData.push(data.result);
    });

    // const settledArr = await Promise.allSettled(promiseArr);
    // const resArr = settledArr.filter(item => item.status === 'fulfilled').map(item => item.value);
    // resArr.forEach(res => {
    //   const data = res.data;
    //   data.table = this.setColumnProp(data.table);
    //   this.tableColumns.push(data.table);
    //   this.tableData.push(data.result);
    // });

    this.tableLoading = false;
  }

  /**
   * @description: 获取表格数据
   * @param {*} type 0:按班级维度1:按学科维度
   */
  async getTable(type: 0 | 1) {
    const apiName = this.apiName || this.$route.name;
    const queryParams = FilterModule.getQueryParams(this.apiName || this.$route.name);
    return FilterModule.getCustomReport(apiName, { ...queryParams, type });
  }

  /**
   * 合并单元格句柄方法
   */
  handleSpanMethod(
    {
      row, // 行
      column, // 列
      rowIndex, // 行索引
      columnIndex, // 列索引
    },
    tableIndex // 表格索引
  ) {
    if (column.property === 'className') {
      const subjectSpanArr = this.formatRowspanAndColspan(this.tableData[tableIndex], 'className');
      return {
        rowspan: subjectSpanArr[rowIndex],
        colspan: 1,
      };
    }
  }
}
</script>

<style scoped lang="scss">
.table {
  margin-top: 20px;
}
</style>

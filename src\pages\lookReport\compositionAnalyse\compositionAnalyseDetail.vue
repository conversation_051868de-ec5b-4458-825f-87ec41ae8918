<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-03-19 14:03:52
 * @LastEditors: 小圆
-->
<template>
  <div class="composition-analyse-detail" v-loading="loading">
    <template v-if="stuCorrectDetail">
      <div class="module comment-module">
        <p class="tip">以下内容由AI大模型技术生成</p>
        <div class="titleLine">智能点评</div>
        <div class="content-box">
          <div class="chart-section" ref="chartSection"></div>
          <div class="comment-section">
            <template v-if="subjectCenterCode == 'ENGLISH'">
              <template
                v-if="
                  stuCorrectDetail?.stuScore?.conComment ||
                  stuCorrectDetail?.stuScore?.lcomment ||
                  stuCorrectDetail?.stuScore?.ocommnet ||
                  stuCorrectDetail?.stuScore?.wcomment
                "
              >
                <div class="comment-item">
                  <div class="comment-title">内容要点</div>
                  <div class="comment-text">
                    {{ stuCorrectDetail?.stuScore.conComment }}
                  </div>
                </div>
                <div class="comment-item">
                  <div class="comment-title">词汇语法</div>
                  <div class="comment-text">
                    {{ stuCorrectDetail?.stuScore.lcomment }}
                  </div>
                </div>
                <div class="comment-item">
                  <div class="comment-title">篇章结构</div>
                  <div class="comment-text">
                    {{ stuCorrectDetail?.stuScore.ocommnet }}
                  </div>
                </div>
              </template>
              <no-data v-else></no-data>
            </template>
            <template v-else>
              <div class="comment-item" v-for="(item, index) in stuCorrectDetail?.stuScore.ans" :key="index">
                <div class="comment-title">{{ item.angle }}</div>
                <div class="comment-text">
                  {{ item.content }}
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>

      <div class="module composition-module" :class="{ 'sticky-module': subjectCenterCode == 'ENGLISH' }">
        <div class="clearfix">
          <div class="titleLine pull-left">
            <span>作文内容</span>
          </div>
          <div class="pull-right" v-if="subjectCenterCode == 'ENGLISH'">
            <span class="error-tip">句子错误</span>
            <el-button type="text" @click="showOriginalPaper"> <i class="el-icon-tickets"></i> 查看原卷</el-button>
          </div>
        </div>

        <template v-if="subjectCenterCode == 'ENGLISH'">
          <div v-if="composition" v-html="composition" class="composition-content"></div>
          <no-data v-else></no-data>
        </template>
        <template v-else>
          <div v-if="quesPointSourceList.length">
            <div v-for="item in quesPointSourceList" :key="item">
              <el-image :src="item" />
            </div>
          </div>
          <no-data v-else></no-data>
        </template>
      </div>

      <div class="module">
        <div class="titleLine">
          <span>逐句批改</span>
        </div>
        <template v-if="subjectCenterCode == 'ENGLISH'">
          <div v-if="stuCorrectDetail?.sentence?.length">
            <div v-for="(item, index) in stuCorrectDetail?.sentence" :key="item.sentence">
              <table class="sentence-table">
                <tr>
                  <td>原句 {{ item.sort }}</td>
                  <td><span v-html="renderSentenceHtml(item)"></span></td>
                </tr>
                <tr class="cor-row">
                  <td>修改后</td>
                  <td class="cor-sentence">{{ item.corSentence }}</td>
                </tr>
                <tr class="reason-row">
                  <td>错误原因</td>
                  <td>
                    <div v-for="reason in item.reason" :key="reason">
                      {{ reason }}
                    </div>
                  </td>
                </tr>
                <tr>
                  <td>更多</td>
                  <td>
                    <el-button type="text" @click="showRemoveStuSentence(item)">删除</el-button>
                    <el-button type="text" @click="showEditStuSentence(item)">修改</el-button>
                  </td>
                </tr>
              </table>
            </div>
          </div>
          <no-data
            v-else-if="composition && stuCorrectDetail?.stuScore?.corrTscore"
            type="zan"
            :text="'没有发现错误哦，你真是太棒了。相信你能够在英语写作的道路上越走越远，取得更多的成功。'"
          ></no-data>
          <no-data v-else> </no-data>
        </template>

        <template v-else>
          <div v-if="stuCorrectDetail?.optSentence?.length">
            <div v-for="(item, index) in stuCorrectDetail?.optSentence" :key="index">
              <table class="sentence-table">
                <tr>
                  <td>原句</td>
                  <td><span v-html="item.text"></span></td>
                </tr>
                <tr class="">
                  <td>优化建议</td>
                  <td>
                    {{ item.optSug }}
                  </td>
                </tr>
                <tr>
                  <td>优化原因</td>
                  <td>
                    {{ item.reason }}
                  </td>
                </tr>
                <tr class="">
                  <td>优化后</td>
                  <td class="">{{ item.optText }}</td>
                </tr>
              </table>
            </div>
          </div>
          <no-data
            v-else-if="stuCorrectDetail?.stuScore?.tscore"
            type="zan"
            :text="'没有发现错误哦，你真是太棒了。相信你能够在语文写作的道路上越走越远，取得更多的成功。'"
          ></no-data>
          <no-data v-else> </no-data>
        </template>
      </div>

      <div class="module">
        <div class="titleLine">润色建议</div>
        <div
          v-if="stuCorrectDetail?.polishComposition"
          class="composition-content"
          v-html="stuCorrectDetail?.polishComposition"
        ></div>
        <no-data v-else></no-data>
      </div>

      <el-dialog :visible.sync="isShowEditDialog" title="修改句子" width="900px" append-to-body>
        <div v-if="editForm">
          <div class="dialog-sentence">原句：{{ editForm.sentence }}</div>
          <el-form :model="editForm" label-width="120px" ref="editFormRef" :rules="rules">
            <el-form-item label="修改后：" prop="corSentence" required>
              <el-input v-model="editForm.corSentence" type="textarea" :rows="4"></el-input>
            </el-form-item>
            <template v-for="(item, index) in editForm.grammars">
              <el-form-item
                :label="`错误原因${index + 1}：`"
                :prop="`grammars[${index}].grammar`"
                required
                :rules="[{ required: true, message: '请选择错误原因', trigger: 'blur' }]"
              >
                <el-select v-model="editForm.grammars[index].grammar" placeholder="请选择错误原因">
                  <el-option v-for="error in errors" :key="error" :label="error" :value="error"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="精细化评语：">
                <el-input
                  v-model="editForm.grammars[index].gContent"
                  type="textarea"
                  :rows="4"
                  maxlength="1500"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </template>
          </el-form>

          <el-button @click="addGrammar">+新增错误类型</el-button>
          <el-button class="pull-right" type="primary" @click="saveEdit">保存</el-button>
        </div>
      </el-dialog>

      <QuesPointDialog
        v-if="isShowOriginalPaper"
        append-to-body
        :examId="examId"
        :subjectId="subjectId"
        :workId="workId"
        :quesNo="quesNo"
        :tQuesNo="tQuesNo"
        :stuId="stuId"
        :stuNo="stuNo"
        :score="score"
        @closed="isShowOriginalPaper = false"
      ></QuesPointDialog>
    </template>

    <no-data v-else v-loading="loading"></no-data>
  </div>
</template>

<script lang="ts">
import QuesPointDialog from '@/components/QuesPointDialog.vue';
import NoData from '@/components/noData.vue';
import {
  getExamStuCorrectDetailAPI,
  getNewQuesPointSourceList,
  removeStuSentenceAPI,
  saveStuSentenceErrorAPI,
} from '@/service/api';
import { getSubjectCenterCode } from '@/utils/UserRoleUtils';
import { ElForm } from '@iclass/element-ui/types/form';
import { Component, Mixins, Prop, Ref } from 'vue-property-decorator';
import SentenceMixin, { SentenceMixinSpace } from './SentenceMixin';

namespace ChineseComposition {
  export interface StuCorrectDetail {
    id: number;
    stuName: string;
    stuScore: StuScore;
    polishComposition: string;
    optSentence: OptSentence[];
    classScore: ClassScore;
    clsAvgScore: number;
    clsMaxScore: number;
    subjectId: number;
    clsAns: Ans[];
  }

  /** 班级得分 */
  export interface ClassScore {
    cScore: number;
    oScore: number;
    lScore: number;
    wScore: number;
  }

  export interface OptSentence {
    text: string;
    optText: string;
    optSug: string;
    reason: string;
  }

  export interface StuScore {
    tscore: number;
    ans: Ans[];
    totalScore: number;
  }

  export interface Ans {
    score: number;
    angle: string;
    content: string;
    fullScore: number;
  }
}

namespace EnglishComposition {
  /** 考试学生批改详情 */
  export interface StuCorrectDetail {
    classScore: ClassScore;
    stuScore: StuScore;
    composition: string;
    polishComposition: string;
    sentence: Sentence[];
    id: string;
    subjectId: string;
  }

  /** 班级得分 */
  export interface ClassScore {
    cScore: number;
    oScore: number;
    lScore: number;
    wScore: number;
  }

  /** 逐句批改 */
  export interface Sentence extends SentenceMixinSpace.Sentence {
    sort: number;
    sentence: string;
    corSentence: string;
    reason: string[];
    redWords: string[];
    gErrors: { [key: string]: string }[];
  }

  /** 学生得分 */
  export interface StuScore {
    conComment: string;
    tscore: number;
    cscore: number;
    wscore: number;
    wcomment: string;
    lscore: number;
    lcomment: string;
    oscore: number;
    ocommnet: string;
    ctotal: number;
    wtotal: number;
    ltotal: number;
    ototal: number;
    corrTscore: number;
  }

  /** 修改句子 */
  export interface EditForm {
    id: string;
    sentence: string;
    corSentence: string;
    grammars: Grammar[];
  }

  /** 语法 */
  export interface Grammar {
    grammar: string;
    gContent: string;
  }
}

type StuCorrectDetail = ChineseComposition.StuCorrectDetail | EnglishComposition.StuCorrectDetail;

type EditForm = EnglishComposition.EditForm;

type Sentence = EnglishComposition.Sentence;

type StuScore = EnglishComposition.StuScore;

type ClassScore = EnglishComposition.ClassScore;

@Component({
  components: {
    QuesPointDialog,
    NoData,
  },
})
export default class CompositionAnalyseDetail extends Mixins(SentenceMixin) {
  @Ref('editFormRef') editFormRef: ElForm;
  @Ref('chartSection') chartSection: HTMLDivElement;
  /** 考试id */
  @Prop({ default: '' }) examId: string;
  /** 作业id */
  @Prop({ default: '' }) workId: string;
  /** 学科id */
  @Prop({ default: '' }) subjectId: string;
  /** 学生id */
  @Prop({ default: '' }) stuId: string;
  /** 学生学号 */
  @Prop({ default: '' }) stuNo: string;
  /** 题号 */
  @Prop({ default: '' }) quesNo: string;
  /** 唯一题号 */
  @Prop({ default: '' }) tQuesNo: string;
  /** 班级id */
  @Prop({ default: '' }) classId: string;
  /** 学生得分 */
  @Prop({ default: 0 }) score: number;
  /** 考试学生批改详情 */
  stuCorrectDetail: StuCorrectDetail | null = null;
  /** 修改句子 */
  editForm: EditForm | null = null;
  /** 错误类型 */
  errors = [
    '时态错误',
    '语态错误',
    '主谓一致错误',
    '句子结构错误',
    '冠词错误',
    '名词错误',
    '介词错误',
    '代词错误',
    '数词错误',
    '形容词和副词错误',
    '动词错误',
    '非谓语动词错误',
    '虚拟语气错误',
    '固定搭配错误',
    '拼写错误',
    '标点符号错误',
  ];
  /** 修改句子规则 */
  rules = {
    corSentence: [{ required: true, message: '请填写修改后内容' }],
    grammars: [{ required: true, message: '请填写错误原因' }],
  };

  /** 是否显示修改句子 */
  isShowEditDialog = false;
  /** 是否显示原卷 */
  isShowOriginalPaper = false;
  /** 是否显示loading */
  loading = false;
  // 学科类型
  subjectCenterCode = 'CHINESE';
  // 图片列表
  quesPointSourceList = [];

  /** 作文内容 */
  get composition() {
    const stuCorrectDetail = this.stuCorrectDetail as EnglishComposition.StuCorrectDetail;

    if (stuCorrectDetail?.composition) {
      let html = stuCorrectDetail.composition;
      stuCorrectDetail.sentence.forEach(item => {
        html = html.replace(item.sentence, `<span style="color: red;">${item.sentence}</span>`);
      });
      return html;
    }
    return '';
  }

  mounted() {
    this.getExamStuCorrectDetail();
  }

  // 获取学生作文批改详情
  public async getExamStuCorrectDetail() {
    this.loading = true;
    const res = await getExamStuCorrectDetailAPI({
      schoolId: this.$sessionSave.get('schoolInfo').id,
      workId: this.workId,
      stuId: this.stuId,
      quesNo: this.quesNo,
      tQuesNo: this.tQuesNo,
      classId: this.classId,
    });
    this.stuCorrectDetail = res.data;
    this.loading = false;
    this.subjectCenterCode = getSubjectCenterCode(this.stuCorrectDetail.subjectId || this.subjectId) || 'ENGLISH';
    this.getQuesPointSourceList();
    this.$nextTick(() => {
      this.initChart();
    });
  }

  // 获取作答原题
  async getQuesPointSourceList() {
    this.quesPointSourceList = [];
    const res = await getNewQuesPointSourceList({
      schoolId: this.$sessionSave.get('schoolInfo').id,
      workId: this.workId,
      quesNo: this.quesNo,
      tQuesNo: this.tQuesNo,
      stuId: this.stuId,
      stuNo: this.stuNo,
    });

    this.quesPointSourceList = res.data?.images || [];
  }

  // 查看原卷
  showOriginalPaper() {
    this.isShowOriginalPaper = true;
  }

  // 显示删除学生句子
  showRemoveStuSentence(item: Sentence) {
    this.$confirm('确认删除本句内容？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    }).then(() => {
      this.removeStuSentence(item);
    });
  }

  // 删除学生句子
  async removeStuSentence(item: Sentence) {
    const res = await removeStuSentenceAPI({
      id: this.stuCorrectDetail?.id,
      sentence: item.sentence,
    });
    if (res.code == 1) {
      this.$message.success('删除成功');
      this.getExamStuCorrectDetail();
    }
  }

  // 修改学生句子
  showEditStuSentence(item: Sentence) {
    this.editForm = {
      id: this.stuCorrectDetail?.id,
      sentence: item.sentence,
      corSentence: item.corSentence,
      grammars: item.gErrors.map(item => {
        const key = Object.keys(item)[0];
        const value = item[key];
        return {
          grammar: key,
          gContent: value,
        };
      }),
    };
    this.isShowEditDialog = true;
  }

  // 保存修改
  async saveStuSentenceError() {
    const res = await saveStuSentenceErrorAPI({
      id: this.stuCorrectDetail?.id,
      sentence: this.editForm.sentence,
      corSentence: this.editForm.corSentence,
      grammars: this.editForm.grammars,
    });
    if (res.code == 1) {
      this.$message.success('修改成功');
      this.isShowEditDialog = false;
      this.getExamStuCorrectDetail();
    }
  }

  // 新增错误类型
  addGrammar() {
    this.editForm.grammars.push({
      grammar: '',
      gContent: '',
    });
  }

  // 保存修改
  saveEdit() {
    this.editFormRef.validate(valid => {
      if (valid) {
        this.saveStuSentenceError();
      } else {
        return false;
      }
    });
  }

  // 获取图表数据
  getChartData() {
    let indicatorArr = [];
    let stuValues = [];
    let classValues = [];

    if (this.subjectCenterCode == 'ENGLISH') {
      const stuCorrectDetail = this.stuCorrectDetail as EnglishComposition.StuCorrectDetail;
      indicatorArr = [
        {
          name: '词汇语法',
          value: stuCorrectDetail?.stuScore?.lscore,
          max: stuCorrectDetail?.stuScore?.ltotal,
        },
        {
          name: '内容要点',
          value: stuCorrectDetail?.stuScore?.cscore,
          max: stuCorrectDetail?.stuScore?.ctotal,
        },
        {
          name: '篇章结构',
          value: stuCorrectDetail?.stuScore?.oscore,
          max: stuCorrectDetail?.stuScore?.ototal,
        },
      ];
      stuValues = [
        stuCorrectDetail?.stuScore?.lscore,
        stuCorrectDetail?.stuScore?.cscore,
        stuCorrectDetail?.stuScore?.oscore,
        stuCorrectDetail?.stuScore?.wscore,
      ];
      classValues = [
        stuCorrectDetail?.classScore?.lScore,
        stuCorrectDetail?.classScore?.cScore,
        stuCorrectDetail?.classScore?.oScore,
        stuCorrectDetail?.classScore?.wScore,
      ];
    } else {
      const stuCorrectDetail = this.stuCorrectDetail as ChineseComposition.StuCorrectDetail;
      indicatorArr = stuCorrectDetail.stuScore.ans.map(item => {
        return {
          name: item.angle,
          value: item.score,
          max: item.fullScore,
        };
      });
      stuValues = stuCorrectDetail.stuScore.ans.map(item => {
        return item.score;
      });
      classValues = stuCorrectDetail?.clsAns?.map(item => {
        return item.score;
      });
    }
    return {
      indicatorArr,
      stuValues,
      classValues,
    };
  }

  // 初始化图表
  initChart() {
    if (!this.chartSection) {
      return;
    }

    const chart = this.$echarts.init(this.chartSection);

    // 生成星星
    const getStars = (value, max) => {
      const rate = (value * 100) / max; // 转换为百分比
      const starCount = rate <= 20 ? 1 : rate <= 40 ? 2 : rate <= 60 ? 3 : rate <= 80 ? 4 : 5;
      return '★'.repeat(starCount);
    };

    const chartData = this.getChartData();

    let indicatorArr = chartData.indicatorArr;
    let stuValues = chartData.stuValues;
    let classValues = chartData.classValues;

    chart.setOption({
      legend: {
        data: ['学生', '班级平均'],
        bottom: '0',
      },
      tooltip: {
        trigger: 'item',
        formatter: params => {
          return [
            `<div>${params.data.name}</div>`,
            ...indicatorArr.map((item, index) => `<div>${params.marker} ${item.name}: ${params.value[index]}</div>`),
          ].join('');
        },
      },
      grid: {
        left: '5%',
        right: '5%',
        bottom: '3%',
        containLabel: true,
      },
      radar: {
        axisName: {
          formatter: (name, indicator) => {
            return [`${name}`, `{star|${getStars(indicator.value, indicator.max)}}`].join('\n');
          },
          rich: {
            star: {
              color: '#f4a527',
              fontSize: 18,
            },
          },
        },
        shape: 'circle',
        indicator: indicatorArr.map(item => {
          return {
            name: item.name,
            max: item.max,
            value: item.value,
          };
        }),
        startAngle: 40,
      },
      series: [
        {
          type: 'radar',
          data: [
            {
              value: stuValues,
              name: '学生',
            },
            this.classId
              ? {
                  value: classValues,
                  name: '班级平均',
                }
              : null,
          ].filter(item => item),
        },
      ],
    });
  }
}
</script>

<style scoped lang="scss">
@import './index.scss';
.composition-analyse-detail {
  padding: 20px;
  background-color: #f5f7fa;
  font-size: 14px;
  min-height: 100vh;

  .module {
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    padding-top: 5px;
    margin-bottom: 10px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

    .tip {
      font-size: 14px;
      margin-top: 10px;
      margin-bottom: 10px;
      color: #909399;
    }
  }

  .tip-module {
    padding-bottom: 5px;
    line-height: 2;
  }

  .comment-module {
    .content-box {
      display: flex;

      .chart-section {
        width: 50%;
        height: 380px;
      }

      .comment-section {
        width: 50%;
        height: 400px;
        overflow: auto;
      }
    }
  }

  .composition-module {
  }

  .sticky-module {
    position: sticky;
    top: -20px;
    z-index: 1;
  }

  .content-box {
    .comment-section {
      .comment-item {
        margin-bottom: 16px;
        padding: 12px;
        background: #f8f9fb;
        border-radius: 6px;

        &:last-child {
          margin-bottom: 0;
        }

        .comment-title {
          font-size: 16px;
          font-weight: 700;
          color: #303133;
          margin-bottom: 8px;
        }

        .comment-text {
          color: #606266;
          line-height: 1.6;
        }
      }
    }
  }
}

.error-tip {
  font-size: 12px;
  margin-right: 10px;

  &::before {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    background-color: #f56c6c;
    border-radius: 50%;
    margin-right: 5px;
  }
}

.sentence-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;

  tr {
    border: 1px solid #e8e8e8;
  }

  td {
    color: #333;
    padding: 10px;
    border: 1px solid #e8e8e8;

    &:first-child {
      width: 100px;
      text-align: center;
      color: #303133;
    }
  }

  .cor-row {
    background-color: #f0f9ff;

    .cor-sentence {
      // color: #3a9ada;
    }
  }

  .reason-row {
    background-color: #fff1f2;
  }
}

.composition-content {
  white-space: pre-wrap;
}

.dialog-sentence {
  padding: 0 10px;
  margin-bottom: 20px;
  line-height: 1.5;
}
</style>

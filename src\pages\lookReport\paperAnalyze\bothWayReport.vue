<template>
  <div v-loading="isLoading">
    <div class="titleLine">
      <span> 双向细目表 </span>
    </div>

    <el-tooltip placement="bottom-start" v-if="doQuesList.length">
      <div slot="content">
        <div v-html="getDoQuesChoiceTipHtml()"></div>
      </div>
      <span class="mixin-dochoice-tip">{{
        getDoQuesChoiceTipText()
      }}</span>
    </el-tooltip>
    <!-- <div v-if="doQuesList.length" class="mixin-dochoice-tip" v-html="getDoQuesChoiceTipHtml()"></div> -->
    
    <el-table
      class="both-way-table"
      :data="tableData"
      stripe
      :header-cell-style="{ fontSize: '14px', color: '#3F4A54', fontWeight: 'bold' }"
      style="width: 100%"
      v-sticky-table="0"
    >
      <el-table-column align="center" label="题型" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.questionTypeName == '' ? '-' : scope.row.questionTypeName }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="quesNoDesc" label="题号" width="90">
        <template #default="scope">
          <span>{{ scope.row.quesNoDesc }}</span>
          <span v-if="getDoChoiceQuesCountTextByQuesNo(scope.row.quesNumber)">
              (<span class="mixin-dochoice-text">{{
                getDoChoiceQuesCountTextByQuesNo(scope.row.quesNumber)
              }}</span
              >)
            </span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="score" label="分值(分)" width="90"> </el-table-column>
      <el-table-column align="center" label="知识点">
        <template slot-scope="scope">
          <span v-if="scope.row.knowledgePoint">
            <span v-if="scope.row.knowledgePoint.length">
              <span v-for="(point, index) in scope.row.knowledgePoint">
                {{ point }}{{ index == scope.row.knowledgePoint.length - 1 ? '' : '、' }}
              </span>
            </span>
            <span v-else>-</span>
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="难度" align="center">
        <el-table-column align="center" class-name="custom-cell" label="简单" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.diffLevel == '简单'">
              <svg
                t="1615956997256"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="4605"
                width="20"
                height="20"
              >
                <path
                  d="M380.342857 801.645714a53.394286 53.394286 0 0
                1-36.571428-16.091428l-218.331429-217.234286a55.588571 55.588571 0 0 1
                0-77.165714 54.125714 54.125714 0 0 1 76.8 0l178.102857 179.2L835.291429
                272.091429a53.394286 53.394286 0 0 1 76.434285 0 54.125714 54.125714 0 0
                1 0 76.8L418.742857 785.554286a54.491429 54.491429 0 0 1-38.4 16.091428z"
                  p-id="4606"
                  fill="#07C29D"
                ></path>
              </svg>
            </span>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="custom-cell" label="中等" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.diffLevel == '中等'">
              <svg
                t="1615956997256"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="4605"
                width="20"
                height="20"
              >
                <path
                  d="M380.342857 801.645714a53.394286 53.394286 0 0
                1-36.571428-16.091428l-218.331429-217.234286a55.588571 55.588571 0 0 1
                0-77.165714 54.125714 54.125714 0 0 1 76.8 0l178.102857 179.2L835.291429
                272.091429a53.394286 53.394286 0 0 1 76.434285 0 54.125714 54.125714 0 0 1 0
                76.8L418.742857 785.554286a54.491429 54.491429 0 0 1-38.4 16.091428z"
                  p-id="4606"
                  fill="#3E73F6"
                ></path>
              </svg>
            </span>
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column align="center" class-name="custom-cell" label="困难" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.diffLevel == '困难'">
              <svg
                t="1615956997256"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="4605"
                width="20"
                height="20"
              >
                <path
                  d="M380.342857 801.645714a53.394286 53.394286 0 0
                1-36.571428-16.091428l-218.331429-217.234286a55.588571 55.588571 0 0 1
                0-77.165714 54.125714 54.125714 0 0 1 76.8 0l178.102857 179.2L835.291429
                272.091429a53.394286 53.394286 0 0 1 76.434285 0 54.125714 54.125714 0 0 1 0
                76.8L418.742857 785.554286a54.491429 54.491429 0 0 1-38.4 16.091428z"
                  p-id="4606"
                  fill="#FF6A68"
                ></path>
              </svg>
            </span>
            <span v-else></span>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getBothWayList } from '@/service/pexam';
import DochoiceMixin from '../mixin/DochoiceMixin.vue';

export default {
  name: 'bothWayReport',
  props: ['filterData'],
  mixins: [DochoiceMixin],
  data() {
    return {
      isLoading: false,
      tableData: [],
    };
  },
  watch: {
    filterData: {
      immediate: true,
      handler(newValue, oldValue) {
        if (newValue) this.getBothWayList();
        this.getPaperChoice(
          this.$sessionSave.get('reportDetail').examId,
          this.filterData.subjectId
        );
      },
    },
  },
  methods: {
    // 双向细目表
    async getBothWayList() {
      try {
        const data = await getBothWayList({
          subjectId: this.filterData.subjectId,
          examId: this.$sessionSave.get('reportDetail').examId,
          abPaper: this.filterData.abPaper
        });
        if (!data.data.length) {
          this.tableData = [];
          return;
        }
        this.tableData = data.data;
      } catch (error) {
        console.log(error);
        this.tableData = [];
      }
    },

    updateData({ isLoading }) {
      // console.log('updatting', isLoading)
      this.isLoading = isLoading;
    },
    updateFilter(data) {},
  },
};
</script>

<style lang="scss">
.both-way-table {
  .custom-cell .cell {
    font-weight: 400;
  }
}
</style>
<style lang="scss" scoped>
.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;
  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 15px;
  }
}
</style>

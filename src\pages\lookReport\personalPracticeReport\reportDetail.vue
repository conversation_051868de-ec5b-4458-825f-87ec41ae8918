<template>
  <div>
    <div class="reportDetail-title clearfix">
      <div class="back el-icon-arrow-left back-arrow" @click="backPage">
        返回
      </div>
    </div>
    <div class="practice-top">
      <!-- 班级 -->
      <div class="header__filter display_flex clearfix">
        <span>班级：</span>
        <ul class="class__ul pull-left flex_1">
          <li class="class__li" :class="{active: filterData.classId === item.id || 
              (!filterData.classId && item.class_name==='全部')}" 
              v-for="(item, index) in classId" :key="index" @click="changeFilter('class', item)">
            {{item.class_name}}
          </li>
        </ul>
      </div>
      <!-- 学科 -->
      <div class="header__filter display_flex clearfix bottomLine">
        <span class="leftText pull-left">学科：</span>
        <ul class="class__ul pull-left flex_1">
          <li class="class__li" :class="{active: filterData.subjectId === item.id}" v-for="item in subjectList" :key="item.id" @click="changeFilter('subject', item)">
            {{item.name}}
          </li>
        </ul>
      </div>
    </div>
    <div v-if=" !this.stuList.length" class="nodata">
      <img :src="noResImg" alt="">
      <p class="text-center">{{errorMsg}}</p>
    </div>
    <div v-else>
      <div class="reportDetail-top">
        <span class="titleLine">学生名单</span>
        <el-row class="list-none stu-ul flex_1" :gutter="20">
          <el-col :span="2" class="stu-li text-ellipsis" :class="{active: item.studentId === activeStu}" @click.native="changeStu(item)" v-for="(item, index) in stuList" :key="index" :title="item.stuName">
            {{item.stuName}}
          </el-col>
        </el-row>
        <div class="reportDetail-middle">
          <span style="margin-left: 4px;">学生：{{this.activedStu}}&nbsp;</span>
          <span style="margin-left: 20px">&nbsp;&nbsp; 得分：{{this.activedScore}}/{{this.fullScore}}</span>
        </div>
      </div>
      <div class="reportDetail-content">
        <div v-for="(item,index) in persList" :key="index">
          <div v-if="examList[item.k].length" style="font-size:16px; width:100%;height:25px; font-weight:bold; color: #3f4a54;">
            {{item.v}}（共{{item.len}}题，总分：{{item.fullScore}}分） 
          </div>
          <div v-for="(it,index) in examList[item.k]" :key="index">
            <div v-if="it.quesType == 2" class="originQues">
              <span style="margin-left: 4px;">原卷第{{it.sort+1}}题</span>
            </div>

            <div class="ques-list" @click="selectQuesItem(it.quesId)" :class="{active: it.quesId === detailId}">
              <div class="quesType">
                <span class="ques-type" type="primary" size="medium">{{it.quesType===2?'原题':it.quesType===1?'拔高题':'巩固题'}}</span>
              </div>
              <div class="ques-content">
                <div class="question_content">
                  <div class="question_body" v-if="it.ques" v-html="it.ques.topic"></div>
                  <div class="ques-stuAnswer" v-if="!(it.type==1 || it.type == 8)">
                       <div class="que-topic">【作答】</div>
                       <div class="que-img">
                       <el-image class='answer-imgList'
                                         :src="`https://fs.iclass30.com/${it.url}`"
                                         :preview-src-list="`https://fs.iclass30.com/${it.url}`.split()"
                                         alt="">
                       </el-image>
                        </div>
                  </div>
                </div>
              </div>
              <div class="edit-block clearfix">
                <el-link v-if="it.type==1 || it.type == 8" class="score" :underline="false">作答：{{it.answer}}</el-link>
                <span class="pull-right ques-btn" :class="it.showDetail?'active':''" @click.stop="showQuesDetails(it)"><i :class="it.showDetail?'el-icon-d-arrow-left active':'el-icon-d-arrow-right'"></i>题目详情</span>
              </div>
              <div class="ques-detail" v-if="it.showDetail && it.ques.content">
                <!--答案-->
                <div class="answer_box display_flex align-items_flex-start">
                  <strong class="flex_shrink_0">【答案】</strong>
                  <div class="flex_1">
                    <div class='answer_content' v-html="it.ques.answer">
                      <!-- {{it.answer}} -->
                    </div>
                    <!-- <div class='answer_content' v-else v-html="item.content.answer"></div> -->
                  </div>
                </div>
                <div v-if="it.ques.points && it.ques.points.length"><strong>【考点】</strong>
                  <span v-for="(it,idx) in it.ques.points" :key="it.name+Math.random()">{{it.name}}{{idx!=item.points.length-1?',':''}}</span>
                </div>
                <!--解析-->
                <div v-if="it.ques.analysis" class="answer_box display_flex align-items_flex-start">
                  <span class="flex_shrink_0"><strong>【解析】</strong></span>
                  <div class='answer_content flex_1' v-html="it.ques.analysis"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { chooseQuesSearch } from "@/service/pbook";
import {
  getClassStudentData,
  getStuExamData,
  getExamRelation,
  getExamSubject,
  getExamClass
} from "@/service/pexam";
export default {
  data() {
    return {
      stuList: [],
      activeStu: "",
      quesList: {},
      // 当前选中的题目id
      detailId: "",
      activedStu: "",
      activedScore: "",
      fullScore: "",
      activatedScanId: "",
      examList: { commMapList: [],commFullscore:0,persFullscore:0, persMapList: [] },
      persList: [],
      errorMsg: "暂无数据",
      noResImg: require("@/assets/no-res.png"),
      commonQues: [],
      personalQues: [],
      classList: [],
      subjectList: [],
      filterData: {},
      queryData: {},
      subjectId: [],
      quesTypeName: "",
      hardQuesId: [],
      normalQuesId: [],
      quesId: [],
      examStuId: "",
      phaseId: "",
      initList: [],
      quesRes: {},
      examQueList: [],
      quesAnswer: [],
      quesType: [],
      classId:[]
    };
  },
  mounted() {
    this.queryData = this.$route.query;
    this.activeStu = this.queryData.stuId;
    this.filterData.classId = this.queryData.clsId;
    this.getExamRelation();
    this.getExamClass();
  },
  computed: {},
  methods: {
    // 切换学生
    changeStu(item) {
      this.activeStu = item.studentId;
      this.activedStu = item.stuName;
      this.activedScore = item.score;
      this.fullScore = item.fullScore;
      this.examStuId = item.scanStudentId;
      this.quesType = [];
      this.getStuExamData();
    },
    // 展开、收起题目详情
    showQuesDetails(it) {
      this.$set(it, "showDetail", !it.showDetail);
      this.$nextTick(() => {
        this.$katexUpdate();
      });
    },
    //切换班级、学科
    changeFilter(type, item) {
      this.queryData.fromName = "";
      this.activeStu = "";
      this.activedStu = "";
      this.activedScore = "";
      this.activatedScanId = "";
      this.examStuId = "";
      this.stuList = "";
      switch (type) {
        case "class":
          this.filterData.classId = item.class_name === "全部" ? "" : item.id;
          break;
        default:
          this.filterData.subjectId = item.id;
          break;
      }

      this.getClassStudentData();
    },
    //获取班级学科
    getExamRelation() {
      getExamRelation({
        examId: this.$sessionSave.get("reportDetail").examId,
      }).then((data) => {
          this.getExamSubject();
          this.errorMsg = "暂无数据";
          this.classList = data.data.classIdArr;
          this.subjectId = data.data.subjectIdArr;
        }).catch((err) => {
          console.log(err);
        });
    },
     // 获取班级
    getExamClass() {
      let innerClasstList = this.$sessionSave.get("innerClassList");
      this.classId = innerClasstList;
    },
    getExamSubject() {
      getExamSubject({
        examId: this.$sessionSave.get("reportDetail").examId,
      }).then((data) => {
          data.data.forEach((item) => {
            this.subjectId.forEach((it) => {
              if (item.id === Number(it)) {
                this.subjectList.push(item);
                this.filterData.subjectId = this.subjectList[0].id;
              }
            });
          });
          this.getClassStudentData();
        }).catch((err) => {
          console.log(err);
        });
    },
    //获取学生列表
    getClassStudentData() {
      getClassStudentData({
        examId: this.$sessionSave.get("reportDetail").examId,
        classId: this.filterData.classId,
        subjectId: this.filterData.subjectId,
      }).then((data) => {
          this.stuList = data.data;
          this.stuList.forEach((item) => {
            if (item.studentId === this.activeStu) {
              this.activedStu = item.stuName;
              this.activedScore = item.score;
              this.fullScore = item.fullScore;
              this.activatedScanId = item.scanStudentId;
            }
          });
          if (this.queryData.fromName === "practiceReport") {
            this.getStuExamData();
          } else {
            this.changeStu(this.stuList[0]);
          }
        }).catch((err) => {
          console.log(err);
        });
    },
    //获取作答题目
    getStuExamData() {
      getStuExamData({
        examId: this.$sessionSave.get("reportDetail").examId,
        classId: this.filterData.classId,
        subjectId: this.filterData.subjectId,
        scanStuId: this.examStuId || this.activatedScanId,
      }).then((data) => {
          this.examList = data.data;
          this.initExamData();
        }).catch((err) => {
          console.log(err);
        });
    },
    //处理数据
    initExamData() {
      this.persList = [
        { k: "commMapList", v: "共性题",len:0,fullScore:0 },
        { k: "persMapList", v: "个性题",len:0,fullScore:0 },
         ];
      for(let i = 0;i<this.persList.length;i++){
        let item = this.persList[i];
        let key = item.k;
        let ids = [];
        this.examList[key].forEach((it) => {
          item.fullScore += Number(it.fullScore);
          item.len++;
          if (it.quesId) {
            ids.push(it.quesId);
          }
        });
        this.chooseQuesSearch(ids.join(","), key);
      }
    },
    //获取题目结构
    chooseQuesSearch(ids, key) {
      let innerSubjectList = this.$sessionSave.get("innerSubjectList");
      innerSubjectList.forEach((item) => {
        if (item.id === this.filterData.subjectId) {
          this.phaseId = item.phaseId;
        }
      });
      chooseQuesSearch({
        qIds: ids,
        subjectId: this.filterData.subjectId,
        phaseId: this.phaseId,
      }).then((data) => {
          this.examList[key].forEach((q) => {
            this.$set(q, "ques",data.data.find((q1) => q1.qId == q.quesId)
            );
          });
          this.examList[key].forEach(item=>{
            data.data.forEach(it=>{
                        if(item.quesId===it.qId){
                        this.$set(item,"type",it.data.type)
                        }
            })
          })
        }).catch((err) => {
          console.log(err);
        });
    },
    // 点击题目亮显
    selectQuesItem(it) {
      this.detailId = it.id === this.detailId ? "" : it.id;
      this.$nextTick(() => {
        if (this.detailId) {
          this.$katexUpdate();
        }
      });
    },
    // 返回个性化练习主页
    backPage() {
      this.$sessionSave.set("backContrastObj", true);
      this.$router.push({
        path: "/home/<USER>/personalPracticeReport",
        query: {
          from: "reportDetail",
        },
      });
    },
  },
};
</script>

<style lang="scss" soped>
.reportDetail-title {
  margin: -26px 0 -3px -20px;
  .back-arrow {
    line-height: 40px;
    font-size: 14px;
    font-weight: bold;
    color: #409eff;
    cursor: pointer;
  }
}
.reportDetail-top {
  widows: 100%;
  .titleLine {
    display: inline-block;
    position: relative;
    height: 54px;
    line-height: 54px;
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;
    padding-left: 16px;
    &:before {
      content: "";
      width: 6px;
      height: 24px;
      background: #409eff;
      border-radius: 3px;
      position: absolute;
      left: 0;
      top: 15px;
    }
  }
  .stu-ul {
    width: 100%;
    .stu-li {
      font-size: 16px;
      color: #3f4a54;
      cursor: pointer;
      margin-top: 5px;
      &.active {
        color: #409eff;
      }
    }
  }
}
.reportDetail-middle {
  background-color: rgba(93, 157, 255, 0.0862745098039216);
  width: 100%;
  height: 45px;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 20px;
}
.originQues {
  background-color: rgba(93, 157, 255, 0.0862745098039216);
  width: 100%;
  height: 45px;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 5px;
  margin-bottom: 5px;
}
.reportDetail-content {
  margin-top: 20px;
  .ques-list {
    position: relative;
    border: 1px solid #e4e8eb;
    border-radius: 3px;
    margin-bottom: 20px;
    .quesType {
      position: relative;
      margin-left: 15px;
      margin-top: 10px;
      .ques-type {
        padding: 4px 8px;
        background-color: #c6c8c4;
        color: #fff;
        margin-right: 20px;
        border-radius: 4px;
        font-size: 15px;
      }
    }
    .edit-block {
      position: relative;
      width: 100%;
      height: 48px;
      line-height: 48px;
      padding: 0 21px 0 11px;
      background: #f5f8fa;
      border-radius: 3px;
      font-size: 16px;
      color: #4e5668ff;
      .error {
        position: absolute;
        margin-right: 0px;
        right: 130px;
        font-size: 16px;
      }
      .score {
        position: absolute;
        left: 20px;
        font-size: 16px;
      }
    }
    &:before {
      content: attr(data-index);
      position: absolute;
      left: -1px;
      top: -1px;
      width: 32px;
      height: 38px;
      // background  : url("../../../assets/flag_gray.png") center center no-repeat;
      font-size: 16px;
      color: #3f4a54ff;
      text-align: center;
      line-height: 30px;
    }
    &.active,
    &:hover {
      border: 1px solid #409eff;
      &:before {
        // background : url("../../../assets/flag_active.png") center center no-repeat;
        color: #fff;
      }
    }
    .ques-detail {
      padding: 20px;
      > div {
        margin-bottom: 10px;
      }
      .resourceList {
        margin-top: 10px;
        margin-bottom: 0;
      }
    }
    .ques-content {
      min-height: 100px;
      padding: 40px 0 10px;
      margin-top: -30px;
      line-height: 2em;
    }
  }
}
</style>
<style lang="scss" scoped>
.ques-stuAnswer{
    width: 100%;
    margin-top: 20px;
    height: 140px;
    background: #f5f8fa;
    color: #4e5668ff;
    position: relative;
}
.que-topic{
    position: absolute;
    margin-left: 10px;
    margin-top: 20px;
}
.que-img{
    position: absolute;
    margin-top: 40px;
    height:50px;
    width:60px;
}
.answer-imgList {
    margin-left: 76px;
    width         : 114px;
    height        : 90px;
    cursor        : pointer;
    margin-bottom : 10px;
    border        : 1px solid #cecece
}
.el-icon-d-arrow-left {
  transform: rotate(90deg);
}
.el-icon-d-arrow-right {
  transform: rotate(90deg);
}
.practice-top {
  .header__filter {
    margin: 10px 0;
    .class__li {
      display: inline-block;
      margin-right: 20px;
      margin-bottom: 5px;
      padding: 0 8px;
      border-radius: 3px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      &.active,
      &:hover {
        color: #409eff;
      }
      &.moreClass {
        height: 30px;
        line-height: 30px;
        border-radius: 15px;
        padding: 0 4px 0 8px;
        .el-icon-close {
          display: none;
        }
      }
      &.moreActive {
        background: #f5faff;
        .el-icon-close {
          display: inline-block;
        }
      }
      &.allActive {
        color: #409eff;
        background: #fff;
        .el-icon-close {
          display: none;
        }
      }
    }
    &.bottomLine {
      border-bottom: 1px solid #e4e8eb;
      padding-bottom: 8px;
    }
  }
}
</style>
<template>
  <div>
    <!-- 编辑上线分析占比弹窗 -->
    <el-dialog title="上线分析" :visible.sync="isShowEditDialog" :close-on-click-modal="false" width="50%"
      :before-close="closeDialog">
      <el-form>
        <el-form-item v-for="item, index in onlieList" :key="item.id" label="" prop="rate" class="w-100 custom-form-item">
          <div class="set-online" v-if="selectedOption == 1">
            <div class="label" :style="{ background: getBackgroundColor(index) }"></div>
            <el-input class="set-online-input" maxlength="20" v-model="item.name" />
            <el-input-number class="set-online-input" v-model.number="item.rate" :max="100" :min="0" :controls="false" />           
            <div class="icon">%</div>
            <!-- <percent-input :percentValue="item.rate" v-model="item.rate">
            </percent-input> -->
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="confirmSetOnline">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { checkFloatNum, checkNumberVal } from "@/utils/common.js";
import { isDef, formatNumber, trimExtraZero } from "@/utils/number.js";

import PercentInput from "@/components/PercentInput.vue";

export default {
  components: { PercentInput },

  props: {
    //上线分析数据
    tempOnlineList: {
      type: Array,
      default() {
        return [];
      },
    },
    //上线分析种类
    selectedOption: {
      type: String,
      default: ''
    }
  },
  watch: {
    tempOnlineList: {
      handler(val) { },
      deep: true,
    },
  },
  data() {
    return {
      isShowEditDialog: true,
      onlieList: [],
      // 表单验证规则
      rules: {
        // 数字和必填项验证规则 不超过100
        numberMaxRules: [
          {
            required: true,
            message: "必填项",
            trigger: "blur",
          },
          {
            type: "number",
            message: "请输入0-100",
            trigger: "blur",
          },
          { validator: checkNumberVal, trigger: "blur" },
        ],
      },
    };
  },
  mounted() {
    this.onlieList = JSON.parse(JSON.stringify(this.tempOnlineList))
  },
  methods: {
    onInput(event) {
      if (event.target.composing) {
        return;
      }
      this.updateValue(event.target.value);
    },
    onBlur() {
      this.inputVal = trimExtraZero(this.inputVal);
    },
    updateValue(value) {
      value = isDef(value) ? String(value) : "";

      // native maxlength not work when type is number
      const { maxlength } = this;
      if (isDef(maxlength) && value.length > maxlength) {
        value = value.slice(0, maxlength);
      }
      if (this.type === "number" || this.type === "digit") {
        const allowDot = this.type === "number";

        value = formatNumber(value, allowDot, this.isPositive, this.toFixed);
      }

      const { input } = this.$refs;

      if (input && value !== input.value) {
        input.value = value;
      }
      this.inputVal = value;
    },
    /**
     * @name:设置分数线标签背景色
     */
    getBackgroundColor(index) {
      let colors = ['#FF0000', '#FFC000', '#92D050', '#26B3F4'];
      return colors[index];
    },
    /**
     * @name:确定设置分数线
     */
    confirmSetOnline() {
      let unsetObject = this.onlieList.find((item) => {
        return item.name == "" || item.rate == "";
      });
      if (unsetObject) {
        this.$message(
          {
            message: this.selectedOption == 1 ? "分数线不能为空！" : "名次不能为空！",
            type: "error",
            duration: 1500,
          }
        );
        return;
      }
      this.isShowEditDialog = false;
      this.$emit("confirmSetOnline", this.onlieList);
    },
    /**
     * @name:关闭弹窗
     */
    closeDialog() {
      this.isShowEditDialog = false;
      this.$emit("closeDialog");
    },
  },
};
</script>

<style lang="scss" scoped>
.set-online {
  display: flex;
  margin-bottom: 30px;
  align-items: center;

  .label {
    width: 25px;
    height: 25px;
    border-radius: 6px;
  }
}

.set-online-input {
  width: 20%;
  margin-left: 20px;
  margin-right: 10px;
}
</style>

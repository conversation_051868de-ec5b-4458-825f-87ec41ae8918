<!--
 * @Description: 已添加的微课列表
 * @Author: qmzhang
 * @Date: 2024-07-29 10:12:44
 * @LastEditTime: 2025-01-10 08:44:12
 * @FilePath: \personal-bigdata\src\components\AddWeike\views\AddedList.vue
-->
<template>
    <el-container>
        <el-main class="list-container" v-if="addedList.length">
            <div class="list-item clearfix" v-for="item in addedList" :key="item.id">
                <div class="video-container pull-left">
                    <el-image class="video-image" :src="item.imgUrl" fit="cover">
                        <div slot="error" class="image-slot">
                            <i class="el-icon-picture-outline"></i>
                        </div>
                    </el-image>
                    <div class="video-time">{{ item.time }}</div>
                    <!-- 播放按钮 -->
                    <div class="play-btn-area click-element wh100 middle-helper" title="点击预览视频" @click="previewVideo(item)">
                        <i class="el-icon-video-play"></i>
                    </div>
                </div>
                <!-- 标题区域 -->
                <div class="list-title pull-left middle-helper">
                    <div>
                        <div class="video-title text-ellipsis-two" :title="item.title">{{ item.title || 'XXX XXX XX' }}
                        </div>
                        <div class="video-desc clearfix">
                            {{ item.targetTypeLabel }}可见 &nbsp;&nbsp;&nbsp; {{ item.createTime || '--:--'
                            }}
                        </div>
                    </div>
                </div>
                <!-- 操作区域 -->
                <div class="list-action pull-right middle-helper">
                    <el-button type="text" @click="renameWeike(item)">重命名</el-button>
                    <el-button type="text" @click="deleteWeikeFromQuestion(item)">删除</el-button>
                </div>
            </div>
        </el-main>

        <el-main v-else>
            <el-empty description="暂未添加微课资源"></el-empty>
        </el-main>

        <!-- <el-footer class="clearfix dialog-footer ">
            <div class="pull-left" style="padding-top: 14px;">已添加：{{ addedList.length }}/9</div>
            <div class="pull-right">
                <el-button type="primary" icon="el-icon-plus" @click="openWeikeDialog">网盘微课</el-button>
                <el-button type="primary" style="margin-left: 15px;" icon="el-icon-video-camera" @click="recordWeike"
                    v-if="isDesktop">录制微课</el-button>
            </div>
        </el-footer> -->
    </el-container>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { AddWeikeListJson, AddWeikeVM } from '../AddWeikeVM.class'
import createDialog from '@/components/createDialog';
import WeikeListComponent from '@/components/AddWeike/views/WeikeList.vue'
import { DeleteQuesWk, EditQuesWk } from '@/service/api';
import { getQueryString } from '@/utils';

@Component
export default class AddWeike extends Vue {
    @Prop() vModel;

    vm: AddWeikeVM;
    addedList: AddWeikeListJson[] = [];
    isDesktop = (!!window.cef  && this.$route.name != 'paperComment') || getQueryString('isDesktop') == 1;

    async created() {
        this.vm = new AddWeikeVM(this.vModel);
        this.addedList = this.vModel.courseList.map(item => {
            return {
                ...item,
                time: AddWeikeVM.FormatSeconds(item.duration),
                targetTypeLabel: AddWeikeVM.ReciverList.find(rItem => item.targetType == rItem.value).label,
                createTime: item.createTime.replace('T', ' ').substring(0, 19)
            }
        });
    }

    // 添加微课
    openWeikeDialog() {
        createDialog({
            title: '网盘微课',
            size: 'middle',
            appendToBody: true,
            closeOnClickModal: false,
            customClass: 'el-dialog--globalVW',
            store: this.$store,
            router: this.$router,
            component: WeikeListComponent,
            width: '1000px',
            data: {
                ...this.vModel,
                courseList: this.$deepClone(this.addedList)
            },
            close: () => {
                //关闭后触发
                console.log('dialog is closed');
            },
            change: (data) => {
                if (data.type === 'deleteWK') {
                    this.addedList = this.addedList.filter(item => item.id != data.data);
                    this.$emit('change', { type: 'deleteWK', data: this.addedList })
                } else {
                    this.$emit("change", data);
                }
            },
            confirm: (data) => {
                this.addedList = data.concat(this.addedList);
                this.$emit("change", { type: 'addWK', data: this.addedList });
            }
        });
    }

    // 启动录制微课
    recordWeike() {
        if (this.addedList.length >= 9) {
            this.$notify({
                title: "无法录制",
                message: "每道题最多支持添加9个微课！",
                type: 'error',
                offset: 100,
            })
            return;
        }

        this.$emit('change', { type: 'recordWeike' });
        this.$emit('close');
    }

    /**
     * @description: 预览微课
     * TODO
     * @param {*} wkItem
     * @return {*}
     */
    previewVideo(wkItem: AddWeikeListJson) {
        this.$emit("change", {
            type: 'previewVideo',
            data: {
                url: wkItem.microCourseUrl,
                title: wkItem.title,
                show: true
            }
        })
    }

    /**
     * @description: 微课重命名
     * @param {*} wkItem
     * @return {*}
     */
    async renameWeike(wkItem: AddWeikeListJson) {
        let { value } = await this.$prompt('请输入微课名称', '重命名', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            closeOnClickModal: false,
            inputValue: wkItem.title,
            inputPattern: /\S/,
            inputErrorMessage: '文件名不能为空',
            beforeClose: async (action, instance, done) => {
                if (action === 'cancel') {
                    done();
                    return;
                }

                if (action === 'confirm') {
                    instance.confirmButtonLoading = true;

                    if (instance.inputValue.length <= 100) {
                        if (/[$\*\|\\:"<>\/]+/g.test(instance.inputValue)) {
                            this.$message({
                                message: '文件名不能包含特殊字符 $ * | \\ : " < > ?/',
                                type: 'warning',
                                duration: 1500,
                            });
                            instance.confirmButtonLoading = false;
                            return;
                        }
                    } else {
                        this.$message({
                            message: '请将名称限制的100字符以内！',
                            type: 'warning',
                            duration: 1500,
                        });
                        instance.confirmButtonLoading = false;
                        return;
                    }
                    instance.confirmButtonLoading = false;
                    done();
                }
            },
        })

        try {
            await EditQuesWk({
                id: wkItem.id,
                schoolId: this.vModel.loginInfo.schoolid,
                title: value,
                beforeTargetType: wkItem.targetType,
                afterTargetType: wkItem.targetType
            })

            wkItem.title = value;
            this.$notify({
                title: "提示",
                message: '微课重命名成功！',
                type: 'success',
                offset: 100,
            });
            this.$emit('change', { type: 'renameWK', data: this.addedList })
        } catch (error) {
            this.$notify({
                title: "提示",
                offset: 100,
                type: 'error',
                message: error.msg ? error.msg : '微课重命名失败，请检查稍后重试！'
            });
        }

    }

    /**
     * @description: 从当前题目中删除此微课
     * @param {*} wkItem
     * @return {*}
     */
    async deleteWeikeFromQuestion(wkItem: AddWeikeListJson) {
        await this.$confirm('微课删除后将无法恢复，且学生无法查看，确定删除吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            closeOnClickModal: false,
            type: 'warning'
        })

        try {
            await DeleteQuesWk({
                schoolId: this.vModel.loginInfo.schoolid,
                id: wkItem.id,
                targetType: wkItem.targetType
            })

            this.$notify({
                title: "提示",
                message: '微课删除成功！',
                type: 'success',
                offset: 100,
            });

            this.addedList = this.addedList.filter(item => item.id != wkItem.id);
            this.$emit('change', { type: 'deleteWK', data: this.addedList })
            this.$emit('res-remove','video')
        } catch (error) {
            this.$notify({
                title: "提示",
                offset: 100,
                type: 'error',
                message: error.msg ? error.msg : '微课删除失败，请稍后重试！'
            });
        }

    }
}
</script>

<style lang="scss" scoped>
@import "./index.scss";

.el-container,
.el-header,
.el-main {
    padding: 0;
}
.list-container {
    min-height: 450px;
    max-height: 500px;
    overflow-y: auto;

    &.list-container--none {
        text-align: center;
        color: #999;

        img {
            margin: 30px auto 10px;
        }
    }
}

.list-item {
    height: 95px;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 15px;
    padding-bottom: 15px;

    &:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }
}

.video-image {
    position: relative;
    width: 100%;
    padding-top: 56.25%;
    z-index: 1;

    ::v-deep img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    .el-icon-picture-outline {
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 10;
        margin: -25px;
        color: #ddd;
        font-size: 50px;
    }
}

.video-container {
    width: 136px;
}

.list-title {
    height: 80px;
    padding-left: 30px;
}

.list-action {
    height: 100%;

    .el-button {
        margin-right: 30px;
    }
}

.video-title {
    width: 500px;
    margin-bottom: 10px;
    font-size: 18px;
}

.video-desc {
    font-size: 16px;
    color: #999;
}
</style>
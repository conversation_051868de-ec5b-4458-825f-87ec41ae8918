<template>
  <el-dialog
    title="上线设置(按分数计算)"
    :visible.sync="dialogVisible"
    width="60%"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <!-- 上线设置 -->
    <div class="custom-container">
      <div class="target-box">
        <div class="content-box">
          <el-form
            :model="tableForm"
            :hide-required-asterisk="true"
            ref="onlineRef"
          >
            <table class="target-table">
              <thead>
                <th width="180px">学科</th>
                <th v-for="item in tempOnlineList" :key="item.key">
                  {{ item.name }}
                </th>
                <th width="130px">操作</th>
              </thead>
              <tr
                v-for="(item, index) in subjectList"
                :key="item.id"
              >
                <td>{{ item.name }}({{ item.full }})</td>
                <td
                  v-for="(subItem, subIndex) in tempOnlineList"
                  :key="subItem.key"
                  class="custom-col"
                >
                  <el-form-item
                    label=""
                    :prop="'tableData.' + index + '.score.' + subIndex"
                    :rules="rules.floatNumRules"
                    class="w-100 custom-form-item"
                  >
                    <el-input
                      v-model="item.score[subIndex]"
                      class="w-100"
                      size="large"
                      @blur="
                        $event.target.value = $event.target.value.replace(
                          /^\s+|\s+$/gm,
                          ''
                        )
                      "
                    />
                  </el-form-item>
                </td>
                <td
                  :rowspan="tableForm.tableData.length"
                  v-if="index === 0"
                  style="padding: 0"
                >
                  <i
                    class="icon-item icon-addItem click-element"
                    @click="addOnlineTarget"
                    v-if="tempOnlineList.length < 4"
                  ></i>
                  <i
                    class="icon-item icon-del click-element"
                    @click="delOnlineTarget"
                    v-if="tempOnlineList.length > 1"
                  ></i>
                </td>
              </tr>
            </table>
          </el-form>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="sureClick">保 存</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { checkFloatNum } from "@/utils/common.js";
import { getLineConfAPI, setConfAPI } from "@/service/pexam";

export default {
  props: ["onLinesubjectList","onlineData"],
  data() {
    return {
      dialogVisible: true,
      // 分数线指标集合
      tempOnlineList: [],
      onlineList:[],
      // 上线表单
      onlineForm: {
        tableData: [],
      },
      rules: {
        // 数字 可以是小数
        floatNumRules: [
          {
            required: true,
            message: "必填项",
            trigger: "blur",
          },
          {
            validator: checkFloatNum,
            trigger: "blur",
          },
        ],
      },
      tableForm:{
        tableData:[]
      },
      subjectList:[]
    };
  },
  mounted(){
    this.onlineForm.tableData = JSON.parse(JSON.stringify(this.onlineData))
    this.subjectList = JSON.parse(JSON.stringify(this.onLinesubjectList))
    this.initOnline()
    this.mateSubName()
  },
  methods: {
    /**
     * @name: 初始化分数线配置
     */
    initOnline() {
      let onlineLen = 0;
      if (this.onlineForm.tableData.length > 0) {
        onlineLen = this.onlineForm.tableData[0].score.length;
      }
      this.onlineList = [];
      for (let i = 0; i < onlineLen; i++) {
        const indexName = i + 1;
        this.onlineList.push({
          id: i,
          name: "分数线" + indexName,
          key: "online" + indexName,
        });
      }
      this.tempOnlineList = this.onlineList.concat();
    },
    /**
     * @name:根据学科顺序匹配数据
     */
    mateSubName(){
      this.subjectList.forEach((item,index) => {
        this.onlineForm.tableData.forEach((ite,inde) => {
          if(item.id == ite.subjectId){
            this.$set(item,'score',ite.score)
            this.$set(item,'full',ite.full)
          }
        })
      })
      this.replaceData()
    },
    /**
     * @name:学科顺序与分数线保持一致
     */
    replaceData(){
      this.subjectList.forEach((item,index) => {
        this.tableForm.tableData.push({
          full:item.full,
          score:item.score,
          subjectId:item.id
        })
      })
    },
    /**
     * @name:关闭弹窗
     */
    handleClose() {
      this.dialogVisible = false;
      this.$emit("close-dialog");
    },
    /**
     * @name: 添加分数线指标
     */
    addOnlineTarget() {
      const id = this.tempOnlineList[this.tempOnlineList.length - 1].id + 1;
      const indexName = id + 1;
      this.tempOnlineList.push({
        id: id,
        name: "分数线" + indexName,
        key: "online" + indexName,
      });
    },
    /**
     * @name: 删除分数线指标
     */
    delOnlineTarget() {
      // 删除分数线对应数据
      this.onlineForm.tableData.forEach((item) => {
        if (
          item.score.length > 1 &&
          item.score.length === this.tempOnlineList.length
        )
          item.score.pop();
      });
      this.tempOnlineList.pop();
    },
    /**
     * @name:关闭弹窗
     */
    cancel() {
      this.dialogVisible = false;
      this.$emit("close-dialog");
    },
    /**
     * @name:保存设置
     */
    sureClick() {
      this.$refs["onlineRef"].validate((valid) => {
        if (valid) {
          this.setLineConf()
        } else {
          return false;
        }
      });
    },
    /**
     * @name:设置分数线
     */
    setLineConf(){
      setConfAPI({
        examId:this.$sessionSave.get('reportDetail').examId,
        type:1,
        content:JSON.stringify(this.onlineForm.tableData)
      }).then( res => {
        this.dialogVisible = false;
        this.$emit("close-dialog");
        this.$emit("update-line");
        this.$Message.success("保存成功！")
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.target-table td {
  padding: 14px 36px;
}
.content-box {
  padding: 0 20px;

  .content-main {
    position: relative;

    .content-form {
      width: 92%;
    }
  }
}
.target-table {
  width: 100%;
  border: 1px solid #e4e8eb;
  border-collapse: collapse;
  border-spacing: 0;
  thead {
    height: 64px;
    background: #f7fafc;
    border-radius: 6px 6px 0px 0px;

    th {
      text-align: center;
      font-weight: bold;
      color: #3f4a54;
      border: unset;
      border-right: 1px solid #e4e8eb;
    }
  }

  tr {
    height: 60px;

    td {
      text-align: center;
      border: 1px solid #e4e8eb;
    }
  }

  .icon-item {
    vertical-align: unset;

    &:nth-child(2) {
      margin-left: 28px;
    }
  }

  .icon-del {
    background: url("../assets/icon_shanchu.png");
  }

  .icon-addItem {
    width: 18px;
    height: 18px;
    background: url("../assets/icon_add.png");
    margin-left: unset !important;
  }
}
</style>
<style lang="scss">
.custom-col .el-form-item {
  display: inline-block;
}

.custom-col .el-form-item__content {
  width: fit-content;
}

.target-table .el-input {
  width: unset;
}

.custom-container {
  font-size: 16px !important;
  .el-form-item {
    margin-bottom: 0px !important;
  }
  .el-input__inner{
    text-align: center;
  }
  .tip-txt {
    color: #fe5d50;
    padding: 0 20px;
  }

  .title {
    position: relative;
    margin: 20px 0;
    padding: 0 20px;
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;

    &:before {
      overflow: hidden;
      position: absolute;
      left: 0;
      display: block;
      content: "";
      width: 4px;
      height: 18px;
      background: #3e73f6;
      border-radius: 3px;
    }

    .sub-title {
      color: #7e94a8;
      font-weight: 400;
      margin-left: 10px;
    }
  }

  .m-10 {
    margin-bottom: 10px;
  }

  .btn_item {
    height: 30px;
    width: 70px;
    padding: unset;
  }

  .save-btn {
    position: absolute;
    right: 20px;
    text-align: center;
    margin-top: 20px;

    .el-button {
      width: 136px;
      height: 36px;
    }
  }

  .icon-del {
    background: url("../assets/icon_delete.png");
    margin-top: -2px;
  }

  .icon-addItem {
    margin-left: 20px;
    background: url("../assets/icon_add.png");
  }

  .icon-item {
    width: 16px;
    height: 18px;
    display: inline-block;
    vertical-align: middle;
  }

  .content-box {
    padding: 0 20px;

    .content-main {
      position: relative;

      .content-form {
        width: 92%;
      }
    }
  }

  .tool-sec {
    position: absolute;
    right: 0;
    top: 10px;
    display: inline-block;
  }

  .custom-sec {
    float: right;
  }

  .custom-form-item {
    margin-right: unset !important;
  }

  .btn_del {
    border-color: #3e73f6 !important;
    color: #3e73f6 !important;
  }

  .col-title {
    margin-right: unset !important;
  }
}
</style>
<template>
  <div>
    <el-table
      class="small-ques-table"
      :data="tableData"
      stripe
      :header-cell-style="{ fontSize: '16px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
      style="width: 100%"
      v-sticky-table="0"
    >
      <el-table-column align="center" prop="quesNoDesc" label="题号">
        <template slot-scope="scope">
          <div class="small-no-cell">
            {{ scope.row.quesNoDesc }}
            <div class="small-square" v-if="scope.row.specialTypeId != 0">
              <span class="small-square-word">
                {{
                  scope.row.specialTypeId == 1 ? "送" : scope.row.specialTypeId == 2 ? "零" : "附"
                }}</span
              >
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="quesType" label="题型">
        <template slot-scope="scope">
          <span>{{ scope.row.typeName === "" ? "-" : scope.row.typeName }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="fullScore" label="分值"> </el-table-column>
      <el-table-column align="center" prop="grdAvgScore" label="均分"> </el-table-column>
      <el-table-column align="center" prop="grdFullRate" label="满分率">
        <template slot-scope="scope">
          <span v-show="!isNaN(Number(scope.row.grdFullRate))">
            {{
              scope.row.grdFullRate === "" ? "-" : Number(scope.row.grdFullRate).toFixed(2) + "%"
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="grdZeroRate" label="零分率">
        <template slot-scope="scope">
          <span v-show="!isNaN(Number(scope.row.grdZeroRate))">
            {{
              scope.row.grdZeroRate === "" ? "-" : Number(scope.row.grdZeroRate).toFixed(2) + "%"
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="difficult" label="难度">
        <template slot-scope="scope">
          <span v-show="!isNaN(Number(scope.row.difficult))">
            {{ scope.row.difficult === "" ? "-" : Number(scope.row.difficult).toFixed(2) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="grdDistinguish" label="区分度">
        <template slot-scope="scope">
          <span>{{
            scope.row.grdDistinguish === "" ? "-" : Number(scope.row.grdDistinguish).toFixed(2)
          }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: "SmallQuesTable",
  props: ["tableData"],
  data() {
    return {
      tableData1: [
        {
          quesNumber: 1,
          questionType: "1", //题型
          questionTypeName: "选择题", //题型名
          score: "1.00", //分值
          avgScore: "0.95", //均分
          fullScoreRate: "95.12", //满分率
          zeroScoreRate: "4.76", //零分率
          diffCoefficient: "0.95", //难度系数
          discrimination: "-0.01", //区分度
        },
        {
          quesNumber: 2,
          questionType: "2", //题型
          questionTypeName: "填空题", //题型名
          score: "2.00", //分值
          avgScore: "0.45", //均分
          fullScoreRate: "45.12", //满分率
          zeroScoreRate: "4.76", //零分率
          diffCoefficient: "0.95", //难度系数
          discrimination: "1.34", //区分度
        },
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
.small-no-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}
.small-ques-no {
  position: relative;
  display: flex;
  justify-content: flex-start;
}
.small-ques-mark {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  border: 1px solid;
  width: 20px;
  height: 20px;
  color: #e6a23c;
  background: #fdf6ec;
  border-color: #f5dab1;
  border-radius: 5px;
}
.small-square {
  width: 0;
  height: 0;
  border: 16px solid transparent;
  border-top: 16px solid #fdf6ec;
  border-left: 16px solid #fdf6ec;
  z-index: 100;
  border-radius: 5px 0 0 0;
  position: absolute;
  left: 0;
  top: 0;
  .small-square-word {
    position: absolute;
    left: -12px;
    top: -16px;
    color: #e6a23c;

    font-size: 13px;
  }
}
</style>

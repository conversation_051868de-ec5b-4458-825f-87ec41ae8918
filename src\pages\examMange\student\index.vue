<template>
  <div class="student-container">
    <!-- <bread-crumbs :title="title">
      <template slot="titleSlot"> </template>
    </bread-crumbs> -->
    <div class="student-main">
      <div class="student-title">
        考生管理
        <span class="refresh-student" @click="refreshStuInfo"
          >更新考生信息
          <el-popover placement="right" width="380" trigger="hover">
            <p>参考班级无变化，班级内学生有变化时点此更新</p>
            <i class="el-icon-question" slot="reference"></i>
          </el-popover>
        </span>
      </div>
      <!-- 列表 -->
      <div class="student-table">
        <el-table :data="subjectData" class="exam-ques-table">
          <el-table-column label="学科" prop="subjectName" width="130" show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="classNames" label="参考班级">
            <template slot-scope="scope">
              <el-tooltip :content="scope.row.classNames" placement="top">
                <span class="showOverTooltip">{{ scope.row.classNames }} </span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="参考人数" prop="num" width="180"> </el-table-column>
          <el-table-column label="操作" width="180">
            <template slot-scope="scope">
              <el-button
                type="text"
                :disabled="source == 4 && scope.row.progress >= 4"
                @click="openDialog(scope.row)"
                >设置参考学生</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <student-dialog
      :modalVisible="isShowStudent"
      :subjectInfo="subjectInfo"
      @close-set-student="onCloseSetStudent"
      @confirm-set-student="onConfirmSetStudent"
    >
    </student-dialog>
  </div>
</template>

<script>
import { getExamSubjectList, saveExamSubjectInfo } from '@/service/pexam';
import BreadCrumbs from '@/components/Breadcrumbs/index.vue';
import { getQueryString } from '@/utils';
import { updateExamHomeWorkInfo } from '@/service/api';
import StudentDialog from './studentDialog.vue';

export default {
  data() {
    return {
      examId: getQueryString('examId') || '',
      title: getQueryString('examName') || '考生管理',
      source: getQueryString('source') || '',
      //参考学科、班级数据
      subjectData: [],
      elTableHeight: 'calc(100%)',
      isShowStudent: false,
      subjectInfo: {},
    };
  },
  components: {
    BreadCrumbs,
    StudentDialog,
  },
  mounted() {
    this.getExamSubjectList();
  },
  methods: {
    /**
     * @name:获取考试学科
     */
    getExamSubjectList() {
      getExamSubjectList({
        examId: this.examId,
      })
        .then(res => {
          // console.log(res.data);
          this.subjectData = res.data;
        })
        .catch(err => {
          this.subjectData = [];
        });
    },
    /**
     * @name:打开设置学生弹窗
     */
    openDialog(item) {
      this.subjectInfo = item;
      this.isShowStudent = true;
    },
    /**
     * @name:关闭设置考生弹窗
     */
    onCloseSetStudent() {
      this.isShowStudent = false;
    },
    /**
     * @name:确定选择考生
     */
    onConfirmSetStudent() {
      this.isShowStudent = false;
      this.getExamSubjectList();
    },
    /**
     * @name:更新考试作业师生关系信息
     */
    async refreshStuInfo() {
      this.$confirm(
        `更新后，当前考试参考班级的考生信息会被后台最新的考生信息所覆盖，是否继续？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        let res = await updateExamHomeWorkInfo({ examId: this.examId }).catch(err => {
        });
        if (res.code == 1) {
          this.$message({
            message: '更新成功！',
            type: 'success',
            duration: 1000,
          });
        } else {
          this.$message({
            message: '更新失败',
            type: 'error',
            duration: 1000,
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.showOverTooltip {
  display: -webkit-box;
  position: relative;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.student-main {
  background-color: #fff;
  padding: 20px;
}
.student-title {
  height: 18px;
  font-size: 18px;
  font-weight: bold;
  color: #161e26;
  .refresh-student {
    font-size: 14px;
    color: #008dea;
    margin-left: 20px;
    cursor: pointer;
  }
}
</style>
<style lang="scss">
.student-table {
  padding-top: 18px;
  .el-table thead {
    color: #606266;
  }

  .el-table th.el-table__cell {
    background-color: #f5f7fa !important;
  }

  .el-table th.el-table__cell > .cell {
    padding-left: 20px !important;
    padding-right: 20px !important;
  }

  .el-table .el-table__cell {
    padding: 8px 0 !important;
  }

  .el-table .el-table__row .cell {
    padding-left: 20px !important;
    padding-right: 20px !important;
    line-height: 35px !important;
  }

  .el-table {
    .el-button {
      width: 32px;
      height: 32px;
      padding: unset !important;
    }
  }
  .el-table__header-wrapper .el-checkbox {
    display: none;
  }
}
</style>
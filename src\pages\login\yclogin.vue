<template>
  <div class="loginPage ycloginPage">
    <img class="loginLogo" :src="loginLogo" />
    <img class="loginBg" :src="loginBg" />
    <div class="loginBox">
      <p class="loginBox__title">欢迎登录博雅教育</p>
      <div class="loginBox__account display_flex align-items_center">
        <span class="avator"></span>
        <el-input class="rightInput" v-model="accountValue" :autofocus="true" placeholder="请输入用户名或手机号登录"></el-input>
      </div>
      <div class="loginBox__password display_flex align-items_center">
        <span class="avator"></span>
        <el-input class="rightInput" v-model="passwordValue" placeholder="请输入密码"
          :autocomplete="dialogVisible ? 'new-password' : 'on'" show-password></el-input>
      </div>
      <!--<el-checkbox class="rememberPassword" v-model="rememberPassword">记住密码-->
      <!--</el-checkbox>-->
      <el-button class="loginBtn" @click="loginWithNoType" :loading="isLoading">登录</el-button>
    </div>
    <div class="download">
      <div class="pc">
        <img title="PC下载" src="../../assets/yc/windows.png" />
        <div class="text">PC下载</div>
        <div class="detail">
          <div class="url-link">
            <el-link href="https://fs.iclass30.com/software/common/boyaedu/%E5%8D%9A%E9%9B%85262-%E6%95%99%E5%B8%88%E7%AB%AF.exe" target="_blank">教师端下载</el-link>
            <el-link href="https://fs.iclass30.com/software/common/boyaedu/%E5%8D%9A%E9%9B%85262.exe" target="_blank">教室端下载</el-link>
          </div>
        </div>
      </div>
      <div class="app">
        <img title="APP下载" src="../../assets/yc/android.png" />
        <div class="text">APP下载</div>
        <div class="detail">
          <img title="APP下载" src="../../assets/yc/app.png" />
        </div>
      </div>
      <div class="wxp">
        <img title="微信扫码" src="../../assets/yc/qrcode.png" />
        <div class="text">微信扫码</div>
        <div class="detail">
          <img title="微信扫码" src="../../assets/yc/wx.png" />
        </div>
      </div>
    </div>
    <div class="beian"><a target="_blank" href="https://beian.miit.gov.cn">滇ICP备2024038504号-3</a><span
        style="margin-left: 20px;">易成教育集团</span></div>

    <!--<el-button type="text" @click="dialogVisible = true">点击打开 Dialog</el-button>-->
    <!--学科网账号退出-->
    <iframe v-if="loadXKWLogout" style="display: none" src="https://zjse.xkw.com/logout/"></iframe>

    <el-dialog title="" :visible.sync="dialogVisible" :show-close="true" width="500px">
      <div class="dialog-body">
        <p class="tip">请选择您的登录身份</p>
        <ul class="selection-list">
          <li v-for="item in userTypes" :key="item.name" :class="{ active: item.type == currentSelType }"
            @click="selectType(item)">
            {{ item.name }}
          </li>
        </ul>
        <div style="text-align: center">
          <el-button class="submit-btn" type="primary" :disabled="!canSubmit" @click="confirmSubmit">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import loginMixin from '@/mixins/loginMixin';

export default {
  name: 'login',
  mixins: [loginMixin],
  data() {
    return {
      loginBg: require('@/assets/yc_loginBg.png'),
      loginLogo: require('@/assets/yc_loginLogo.png'),
    };
  }
};
</script>

<style lang="scss" scoped>
.loginPage {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .loginBg {
    width: 100%;
    height: auto;
  }

  .loginLogo {
    position: absolute;
    left: 100px;
    top: 50px;
  }

  .loginBox {
    position: absolute;
    right: 200px;
    top: 50%;
    margin-top: -295px;
    width: 480px;
    height: 590px;
    background: #fff;
    box-shadow: 0px 6px 33px 0px rgba(0, 129, 213, 0.2);
    border-radius: 20px;
    padding: 115px 70px 0 75px;

    .loginBox__title {
      font-size: 26px;
      letter-spacing: 3px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #333;
    }

    .title_desc {
      color: #b5b5b5;
      line-height: 28px;
      margin-right: 20px;
    }

    .loginBox__account,
    .loginBox__password {
      width: 340px;
      height: 60px;
      border: 1px solid #e1e3e6;
      border-radius: 4px;

      .avator {
        position: relative;
        width: 80px;
        height: 100%;

        &:before {
          position: absolute;
          left: 50%;
          top: 50%;
          margin-left: -12px;
          margin-top: -14px;
          content: '';
          background: url('../../assets/personWorkIcon.png');
          background-position: 0px -46px;
          width: 26px;
          height: 30px;
        }

        &:after {
          position: absolute;
          content: '';
          right: 0;
          top: 13px;
          width: 2px;
          height: 34px;
          background: #e9edee;
          box-shadow: 1px 0px 0px 0px rgba(255, 255, 255, 0.2);
        }
      }

      .rightInput {
        margin-left: 20px;
      }
    }

    .loginBox__account {
      margin: 40px 0;

      .avator {
        &:before {
          background-position: -31px -46px;
          width: 26px;
          height: 30px;
          margin-left: -13px;
          margin-top: -15px;
        }
      }
    }

    .rememberPassword {
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #008eeb;
      margin-top: 25px;
    }

    .loginBtn {
      width: 100%;
      height: 54px;
      background: #eba100;
      border-radius: 4px;
      margin-top: 70px;
      font-size: 18px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #fff;
    }
  }

  .dialog-body {
    .tip {
      text-align: center;
      font-size: 18px;
      color: #555;
    }

    .selection-list {
      list-style: none;
      text-align: center;
      margin: 30px auto;

      li {
        width: 200px;
        margin: 15px auto;
        font-size: 16px;
        border: 1px solid #b3b3b3;
        padding: 5px 0;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
          color: #fff;
          border: 1px solid #eba100;
          background-color: #eba100;
        }

        &.active {
          color: #fff;
          border: 1px solid #eba100;
          background-color: #eba100;
        }
      }
    }

    .submit-btn {
      width: 200px;
      font-size: 16px;
      margin-bottom: 10px;
    }
  }

  .beian {
    position: sticky;
    bottom: 10px;
    text-align: center;

    a {
      color: #000;
      font-size: 14px;
    }
  }

  .download {
    position: absolute;
    right: 30px;
    bottom: 300px;
    box-shadow: 1px 1px 2px 2px rgb(197 195 195 / 30%);
    background: #fff;
    border-radius: 5px;
    text-align: center;

    .pc,
    .app,
    .wxp {
      padding: 5px;

      &:hover {
        color: #eba100;
        background: #eba10030;

        .detail {
          display: block;
        }
      }
    }

    .detail {
      position: absolute;
      right: 60px;
      margin-top: -50px;
      display: none;
      box-shadow: 1px 1px 2px 2px rgb(197 195 195 / 30%);

      img {
        width: 128px;
        height: 128px;
      }
      .url-link {
        width: 128px;
        height: 80px;
        line-height: 36px;
        font-size: 18px;
      }
    }

    .text {
      font-size: 12px;
    }
  }
}
</style>
<style lang="scss">
.loginPage {
  .el-dialog__header {
    height: 0;
    background-color: #ffffff;
  }
}

.ycloginPage {
  .el-button {
    background: #eba100;
    border-color: #eba100;
  }
  .el-link.el-link--default:hover{
    color:#eba100;
  }
  .el-link.is-underline:hover:after{
    border-bottom: 1px solid #eba100;
  }
}

.rightInput {
  .el-input__inner {
    border: none;
  }
}
</style>

<!--
 * @Description: 单科尖子生
 * @Author: 小圆
 * @Date: 2024-04-02 09:03:13
 * @LastEditors: 小圆
-->

<script lang="ts">
import { Component, Vue, Mixins, Prop } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';

@Component({
  components: {},
})
export default class SingleSubjectMaxScoreStu extends Mixins(TableCommon) {
  @Prop({ default: 'teacher' }) apiModule;
  //   @Prop({ default: 'singleSubject' }) apiName;

  render(h) {
    const table = this.renderTable(h);
    return table;
  }
}
</script>

<style scoped lang="scss"></style>

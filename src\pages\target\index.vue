<template>
  <div
    class="reportDetail display_flex flex-direction_column"
    ref="reportDetail"
  >
    <div class="reportDetail_top">
      <div
        v-if="$sessionSave.get('reportDetail')"
        class="reportDetail__back el-icon-arrow-left"
        @click="
          $router.push({
            path: '/home/<USER>',
            query: { fromPage: 'target' },
          })
        "
      >
        返回
      </div>
    </div>
    <div class="reportDetail__main flex_1 display_flex align-items_flex-start">
      <ul class="list-none left-menu flex-shrink_0" ref="menuRef">
        <li
          v-for="item in menuList"
          :key="item.id"
          @click="changeMenu(item)"
          :class="{ active: item.id === activeMenuId }"
        >
          {{ item.name }}
        </li>
      </ul>
      <router-view
        class="right-content flex_1"
        :style="{ minHeight: minHeight + 'px' }"
        @updateTab="matchingRoutes"
        ref="routerView"
      >
      </router-view>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 选中的菜单索引
      activeMenuId: 0,
      // 菜单列表项
      menuList: [
        {
          name: '考情指标设置',
          path: 'targetResult',
          id: 0,
        },
        {
          name: '上线指标设置',
          path: 'targetOnline',
          id: 1,
        },
        {
          name: '成绩指标设置',
          path: 'targetScore',
          id: 2,
        },
        {
          name: '分数换算设置',
          path: 'targetFraction',
          id: 3,
        },
        {
          name: '赋分设置',
          path: 'targetNewExam',
          id: 4,
        },
      ],
      minHeight: 0,
      posLeft: 0,
    };
  },
  mounted() {
    this.checkMenuList();
    this.matchingRoutes();
    this.$nextTick(() => {
      // 定位右边内容区的位置
      let menuRect = this.$refs.menuRef.getBoundingClientRect(),
        windowW = document.body.clientWidth;
      this.contentPos = {
        left: menuRect.left + menuRect.width + 20,
        width: windowW - menuRect.left * 2 - menuRect.width - 20,
      };
    });
  },
  methods: {
    checkMenuList() {
      // const isFcClass = this.$sessionSave.get('reportDetail').source == 101;
      // if (isFcClass) {
      // this.menuList = this.menuList.filter(item => item.path !== 'targetNewExam');
      // }
      const parentId = this.$sessionSave.get('reportParent').examId;
      const examId = this.$sessionSave.get('reportDetail').examId;
      if (parentId !== examId) {
        this.menuList = this.menuList.filter(item => item.path !== 'targetNewExam');
      }
    },

    matchingRoutes(data) {
      let localPath = this.$route.path;
      for (let item of this.menuList) {
        if (localPath.includes(item.path)) {
          this.activeMenuId = item.id;
          return;
        }
      }
    },
    changeMenu(item) {
      if (this.activeMenuId == item.id) {
        return;
      }
      this.activeMenuId = item.id;
      this.$router.push({ path: `/home/<USER>/${item.path}` });
    },
  },
};
</script>

<style lang="scss" scoped>
.reportDetail_top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.downloadeBtn {
  margin-right: 15px;
}
.reportDetail {
  position: relative;
  font-family: Microsoft YaHei;
  .reportDetail__back {
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;
    cursor: pointer;
  }
  .reportDetail__main {
    width: 100%;
    .left-menu {
      position: -webkit-sticky;
      position: sticky;
      flex-shrink: 0;
      top: 0px;
      width: 166px;
      // height: 100px;
      background: #fff;
      box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
      border-radius: 6px;
      margin-right: 20px;
      li {
        cursor: pointer;
        width: 100%;
        height: 48px;
        line-height: 48px;
        font-size: 16px;
        color: #3f4a54;
        text-align: center;
        &.active {
          background-color: #f7fbff;
          color: #409eff;
          position: relative;
          &:before {
            content: '';
            position: absolute;
            width: 4px;
            height: 48px;
            background: #409eff;
            border-radius: 2px;
            left: 0;
            top: 0;
          }
        }
      }
    }
    .right-content {
      height: 100%;
      background: #fff;
      box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
      border-radius: 6px;
      padding: 20px 24px;
      min-height: 290px;
      overflow: auto;
    }
  }
}
</style>
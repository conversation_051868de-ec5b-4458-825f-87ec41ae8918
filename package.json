{"name": "personal-bigdata", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "dev:yc": "vue-cli-service serve --mode prodyc", "dev:sj": "vue-cli-service serve --mode prodsj", "dev:sy": "vue-cli-service serve --mode prodsy", "dev:prod": "vue-cli-service serve --mode production", "build": "vue-cli-service build", "build:yc": "vue-cli-service build --mode prodyc", "build:sy": "vue-cli-service build --mode prodsy", "build:sj": "vue-cli-service build --mode prodsj", "build:test": "vue-cli-service build --mode test", "build:analyz": "set use_analyzer=true&&vue-cli-service build --mode test", "deploy:test": "npm run build:test && deploy test", "postinstall": "patch-package"}, "dependencies": {"@coze/api": "^1.1.0", "@iclass/element-ui": "^2.15.23", "@iclass/observe": "^1.3.12", "@tinymce/tinymce-vue": "^5.1.1", "ali-oss": "^6.20.0", "animate.css": "^4.1.1", "axios": "^1.7.2", "clipboard": "^2.0.6", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "dom-serialize": "^2.2.1", "echarts": "^5.0.2", "exceljs": "^4.4.0", "gsap": "^3.5.1", "html2canvas": "^1.4.1", "idb": "^8.0.0", "js-cookie": "^2.2.1", "js-md5": "^0.7.3", "jszip": "^3.10.1", "lodash": "^4.17.20", "mathlive": "^0.106.0", "matrix2d.js": "^0.6.3", "moment": "^2.29.1", "nprogress": "^0.2.0", "qrcodejs2": "^0.0.2", "qs": "^6.9.4", "spark-md5": "^3.0.1", "splitpanes": "^2.4.1", "tinymce": "5.7.1", "uuid": "^3.1.0", "view-design": "^4.4.0", "vue": "^2.6.11", "vue-awesome-swiper": "^3.1.3", "vue-drag-resize": "^1.5.4", "vue-img-viewer": "^1.5.0", "vue-jcrop": "^1.0.3", "vue-markdown": "^2.2.4", "vue-overflow-ellipsis": "^1.0.7", "vue-property-decorator": "8.4.2", "vue-router": "^3.2.0", "vue-touch": "^2.0.0-beta.4", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "vuex-class": "^0.3.2", "watermark-dom": "^2.3.0", "xe-utils": "^3.5.26"}, "devDependencies": {"@babel/plugin-syntax-dynamic-import": "^7.7.4", "@iclass/deploy": "^1.0.0", "@plugin-web-update-notification/webpack": "^2.0.1", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "^4.2.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "compression-webpack-plugin": "^5.0.1", "node-deploy": "^0.2.0", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "sass": "^1.26.5", "sass-loader": "^8.0.2", "typescript": "~3.7.5", "uglifyjs-webpack-plugin": "^2.2.0", "vue-template-compiler": "^2.6.11", "webpack-bundle-analyzer": "^4.5.0", "worker-loader": "^3.0.8"}}
<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-03-17 16:42:39
 * @LastEditors: 小圆
-->
<template>
  <div class="lexical-analysis report-wrapper">
    <headerFilter @updateFilter="updateFilter" ref="headerFilter" class="header-filter"></headerFilter>
    <div>
      <div class="titleLine">
        易错词汇
        <el-popover
          placement="right-start"
          width="300"
          trigger="hover"
          content="词汇诊断内容源于AI识别，归类呈现了班级拼写易错词、亮点高分词汇。老师可在备课、课中等环节按需使用。（识别效果会受学生卷面影响，与实际作答可能存在偏差）"
        >
          <i class="el-icon-question" slot="reference"></i>
        </el-popover>
      </div>

      <div class="word-wrapper" v-if="lexicalAnalysisDetail?.errorList.length">
        <div class="word-card" v-for="item in lexicalAnalysisDetail?.errorList" :key="item.words">
          <div>{{ item.words }}</div>
          <div>
            错误案例 <span v-for="err in item.errors" :key="err.stuId">{{ err.stuName }}（{{ err.errWords }}）</span>
          </div>
        </div>
      </div>
      <no-data v-else></no-data>
    </div>

    <div>
      <div class="titleLine">词汇亮点表达</div>
      <el-table
        :data="lexicalAnalysisDetail?.brightList"
        style="width: 100%"
        stripe
        :header-cell-style="{
          fontSize: '14px',
          color: '#3F4A54',
          backgroundColor: '#f5f7fa',
        }"
        v-sticky-table="0"
      >
        <el-table-column label="序号" prop="no" width="100" align="center">
          <template #default="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="词汇" prop="words"></el-table-column>
        <el-table-column label="学生例句" prop="sentence">
          <template #default="scope">
            <span>{{ `${scope.row.sentence} (${scope.row.stuName})` }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="操作" width="160" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="mini">修改</el-button>
            <el-button type="text" size="mini">删除</el-button>
          </template>
        </el-table-column> -->
      </el-table>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import HeaderFilter from '@/components/headerFilter.vue';
import { getExamQueClassDetailsAPI } from '@/service/api';
import NoData from '@/components/noData.vue';

interface LexicalAnalysisDetail {
  errorList: ErrorList[];
  brightList: BrightList[];
}

interface BrightList {
  words: string;
  stuName: string;
  sentence: string;
}

interface ErrorList {
  words: string;
  errCount: number;
  errNUm: number;
  errors: Error[];
}

interface Error {
  stuId: string;
  stuName: string;
  errWords: string;
}

@Component({
  components: {
    HeaderFilter,
    NoData,
  },
})
export default class LexicalAnalysis extends Vue {
  // 当前选中的筛选项数据
  filterData = {
    classId: '',
    classIds: [],
    subjectId: '',
    phaseId: '',
    xfId: '',
    classList: [],
    aliasName: '', // 组合学科
    qType: 0, // 0:得分 1：赋分
    source: 0, // 0:成绩榜 1：组合成绩榜
    abPaper: '', // -1:普通 0:A卷 1:B卷
    quesInfo: null, // 作文题
  };

  lexicalAnalysisDetail: LexicalAnalysisDetail | null = null;

  // 当前workId
  get workId() {
    let subjectId = this.filterData.subjectId;
    let subjectList = this.$sessionSave.get('innerNoRoleSubjectList');
    let subject = subjectList.find(item => item.id == subjectId);
    if (subject) {
      return subject.workIds[Number(this.filterData.abPaper)];
    }
    return '';
  }

  // 更新筛选条件
  updateFilter(filterData) {
    this.filterData = this.$deepClone(filterData);
    this.getExamQueClassDetails();
  }

  // 获取词汇分析详情
  async getExamQueClassDetails() {
    const res = await getExamQueClassDetailsAPI({
      schoolId: this.$sessionSave.get('schoolInfo').id,
      workId: this.workId,
      quesNo: this.filterData.quesInfo?.quesNo,
      tQuesNo: this.filterData.quesInfo?.tQuesNo,
      classId: this.filterData.classId,
      type: 1, // 1:词汇分析 2:语法分析
    });
    this.lexicalAnalysisDetail = res.data;
  }
}
</script>

<style scoped lang="scss">
@import './index.scss';
.lexical-analysis {
}

.word-wrapper {
  display: grid;
  grid-template-rows: repeat(3, 1fr);
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}
.word-card {
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
}
</style>

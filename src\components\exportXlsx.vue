<template>
    <div>
        <el-dialog custom-class="exportxls-dialog" :title="dialogTitle" :visible="dialogFormVisible" width="700px"
            :before-close="handleClose" :close-on-click-modal="false">
            <el-button type="primary" plain style="margin:20px 0px" @click="downloadMould">
              导入学生成绩模板
              <i class="el-icon-download el-icon--right"></i>
            </el-button>
            <Upload ref="uploadFile" :action="uploadUrl" :max-size="102400" :accept="'.xlsx'" :format="['xlsx']"
                :show-upload-list="false" :on-success="uploadSuccess" :before-upload="beforeUpload"
                :on-format-error="handleFormat" :on-exceeded-size="handleMaxSize" :data="uploadData">
                <div class="upload-main">
                    <div class="import-tips remarkStar">导入学生成绩</div>
                    <label class="file-name">
                        <p class="limit-info" v-if="formItem.tbName == ''">
                            请选择100M以内的文档上传（xlsx）
                        </p>
                        <p class="limit-info" v-else>{{ formItem.tbName }}</p>
                    </label>
                    <span>选择文件</span>
                </div>
            </Upload>
            <div slot="footer" class="dialog-footer">
                <el-button @click="$emit('closeDialog',false)">取 消</el-button>
                <el-button type="primary" @click="addReport" :loading="loadingStatus">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getToken } from "@/service/auth"
import { done } from "nprogress";

var url = process.env.VUE_APP_KKLURL;
//var url = "http://localhost:5028";
export default {
    name: 'createReports',
    props: ['examId', 'year', 'phase','subjectId','subjectIds','type'],
    data() {
        return {
            loadingStatus:false,
            dialogFormVisible: true,
            reportDetail: {},
            reportList: [],
            pagination: {
                page: 1,
                pageSize: 5,
                total: 0
            },
            uploadUrl: url + "/pexam/_/import/export",
            examVal: '',
            formItem: {
                tbName: '', //word名称
            },
            uploadData: {
                token: getToken(),
                examId: this.examId,
                year: this.year,
                phase: this.phase,
                subjectIds: this.subjectId ? [this.subjectId] : this.subjectIds
            },
            dialogTitle:this.type == 'single' ? '导入全科成绩' : '导入单科成绩'
        };
    },
    mounted() {

    },
    methods: {
        // 关闭弹窗
        handleClose(done) {
            this.$emit('closeDialog');
            this.dialogFormVisible = false;
            done();
        },
        // excel上传前
        beforeUpload(file) {
            console.log('file-->', file)
            this.file = file
            this.formItem.tbName = file.name
            let login = JSON.parse(sessionStorage.loginInfo);
            let sch = JSON.parse(sessionStorage.schoolInfo);
            let schId = login.schoolid;
            if (login.user_type == 5) {
                schId = sch.schoolId || sch.id;
            }
            this.uploadData.schoolId = schId;
            return false
        },
        dataURItoBlob(dataURI) {
            dataURI = Buffer.from(dataURI, 'base64').toString('binary');
            const ia = new Uint8Array(dataURI.length);
            for (let i = 0; i < dataURI.length; i++) {
                ia[i] = dataURI.charCodeAt(i);
            }

            return new Blob([ia], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        },
        // 上传成功回调
        uploadSuccess(response, file, fileList) {
            console.log(response);
            if(response.code == 3) {
                this.$message.warning(response.msg);
            } else if (response.code == 2) {
                var blob = this.dataURItoBlob(response.data);
                var a = document.createElement('a');
                a.download = "异常.xlsx";
                a.href = URL.createObjectURL(blob);
                a.click();
                this.$message.warning('导入数据异常，请查看异常文档中标红位置');
            } else if (response.code < 1) {
                // this.$Message.error(`${response.msg}`)
                var msg = response.msg;
                if(response.code == -2){
                    msg = "";
                    var data = response.data;
                    data.forEach(v => {
                        msg += `"${v}"学科不存在，请修改后再上传！`;
                    });
                }
                this.$message({
                    type: 'error',
                    message: msg
                })
                this.formItem.tbName = ''
            } else if (response.code == 1) {
                this.$message({
                    type: 'success',
                    message: '上传成功!'
                })
                this.$emit('closeDialog');
            }
            this.loadingStatus=false
        },
        // excel文件太大的提示
        handleMaxSize(file) {
            // this.$Message.error('文件' + file.name + '太大，不允许超过100M')
            this.$message({
                type: 'error',
                message: '文件' + file.name + '太大，不允许超过100M'
            })
            this.$refs.uploadFile.clearFiles();
            this.formItem.tbName = '';
        },
        // 上传的文件格式验证失败的提示
        handleFormat(file) {
            // this.$Message.error('文件' + file.name + '格式不支持，请选择xls、xlsx格式的文档')
            this.$message({
                type: 'error',
                message: '文件' + file.name + '格式不支持，请选择xlsx格式的文档'
            })
            this.$refs.uploadFile.clearFiles();
            this.formItem.tbName = '';
        },
        addReport() {
            this.loadingStatus=true;
            this.$refs.uploadFile.post(this.file)
        },
        //下载导入模板
        downloadMould(){
          let params = new URLSearchParams();
          params.append('examId', this.examId);
          if(this.subjectId) params.append('subjectId', this.subjectId);
          window.open(process.env.VUE_APP_KKLURL + `/pexam/_/download/sheet?${params.toString()}`,'_self');
        },
    }
};
</script>
<style lang="scss">
.exportxls-dialog{
  .el-dialog__body{
    padding:15px 20px 30px 20px;
  }
}
</style>
<style lang="scss" scoped>
.dialog-footer {
    margin-right: 10px;
}

.dialog-footer button span {
    font-size: 16px;
}

.upload-main {
    display: flex;
    font-size: 14px;
    color: #a5acbd;
    cursor: pointer;
    align-items: center;
    margin-left: 18px;
    .file-name {
        width: 300px;
        height: 40px;
        box-sizing: border-box;
        line-height: 40px;
        white-space: nowrap;
        border: 1px solid #008DEA;
        border-radius: 4px 0 0 4px;
        text-indent: 1em;

        .limit-info {
            cursor: pointer;
        }
    }

    >span {
        width: 120px;
        height: 40px;
        line-height: 40px;
        color: #fff;
        background-color: #008DEA;
        text-align: center;
        border-radius: 0 4px 4px 0;
    }
}
.import-tips{
  color: #333;
  margin-right: 15px;
  &.remarkStar {
    position: relative;
    &:before {
      content: "*";
      position: absolute;
      left: -12px;
      font-size: 18px;
      font-weight: bold;
      color: #f56353;
      margin-right: 3px;
    }
  }
}
</style>
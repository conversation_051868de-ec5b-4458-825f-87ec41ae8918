/*
 * @Description:
 * @Author: 小圆
 * @Date: 2024-04-16 17:29:38
 * @LastEditors: 小圆
 */
import Observe from '@iclass/observe';
import { sessionSave } from '@/utils';
import { GetCustomReport, GetEReportByModule, GetTeacherReport } from '@/service/eReport';
import {
  clzListAPI,
  getContrastTestList,
  listExamStu,
  listExamSubject,
} from '@/service/pexam';

import { Custom_Option_Map } from './menu/custom';
import { Student_Option_Map } from './menu/student';
import { Teacher_Option_Map } from './menu/teacher';

import {
  FilterDataKey,
  FilterEvent,
  IClassInfo,
  IContrastExamInfo,
  IExamReportInfo,
  IGradeInfo,
  ISchoolInfo,
  IStudentInfo,
  ISubjectInfo,
  IScoreSubjectInfo,
} from './types';

const filterFieldMap = {
  examId: 'examId', // 考试ID
  gradeId: 'gradeId', // 年级ID
  subjectId: 'subjectId', // 学科ID
  classInfo: 'classList', // 单选班级信息
  classList: 'classList', // 多选班级列表
  keyWord: 'keyWord', // 关键字
  schoolId: 'schoolId', // 学校ID
  limit: 'limit', // limit 前几名
  scoreType: 'scoreType', // 得分类型
};
const mergeOptionMap = new Map([
  ...Teacher_Option_Map,
  ...Student_Option_Map,
  ...Custom_Option_Map,
]);

/*
 * @Description:
 * @Author: 小圆
 * @Date: 2024-03-21 17:26:11
 * @LastEditors: 小圆
 */
export class FilterModule extends Observe {
  private static instance: FilterModule;
  public static getInstance(): FilterModule {
    if (!FilterModule.instance) {
      FilterModule.instance = new FilterModule();
    }
    return FilterModule.instance;
  }

  // 筛选条件
  filterData = {
    examId: '' as string | number,
    examList: '' as string, // 考试列表，JSON对象 { id: string | number; name: string }[]
    gradeId: '' as string,
    subjectId: '' as string | number,
    classInfo: '' as string, // 单选 对应接口classList
    classList: '' as string, // 多选，JSON对象 { id: string | number; name: string }[]
    keyWord: '' as string,
    schoolId: '' as string,
    limit: 2000 as number, // 前几名 对应接口limit
    stuId: '' as string, // 学生Id
    contrastExamId: '' as string | number,
    scoreType: 0 as number, // 0:得分，1:标准分
    rank: 10 as number, // 单个名次步长
    startRank: 1 as number, // 开始名次
    endRank: 50 as number, // 结束名次
    step: 10 as number, // 步长
  };

  // 元数据对象
  filterInfo = {
    // 考试报告对象
    examInfo: null as IExamReportInfo,
    // 当前学科信息
    subjectInfo: null as IScoreSubjectInfo,
    // 当前班级信息
    classInfo: null as IClassInfo,
    // 当前班级信息
    classInfos: [] as IClassInfo[],
    // 当前年级信息
    gradeInfo: null as IGradeInfo,
    // 对比考试信息
    contrastExamInfo: null as IContrastExamInfo,
    // 学生信息
    stuInfo: null as IStudentInfo,
    // 学校信息
    schoolInfo: null as ISchoolInfo,
    // 考试报告对象列表
    examInfos: [] as IExamReportInfo[],
  };

  // 选择器选项
  selectOption = {
    examList: [] as IExamReportInfo[], // 考试列表
    subjectList: [] as IScoreSubjectInfo[], // 学科列表
    gradeList: [] as IGradeInfo[], // 年级列表
    classList: [] as IClassInfo[], // 班级列表
    contrastExamList: [] as IContrastExamInfo[], // 对比考试列表
    stuList: [] as IStudentInfo[],
    rankList: [5, 10, 50, 100],
  };

  examsFilterData = {
    examList: '' as string, // 考试列表，JSON对象 { id: string | number; name: string }[]
    gradeId: '' as string,
    subjectId: '' as string | number,
  };

  examsFilterInfo = {
    // 考试报告对象列表
    examInfos: [] as IExamReportInfo[],
    gradeInfo: null as IGradeInfo,
    subjectInfo: null as IScoreSubjectInfo,
  };

  examsSelectOption = {
    gradeList: [] as IGradeInfo[], // 年级列表
    gradeMap: {} as { string: IScoreSubjectInfo }, // 年级列表
  };

  examSubjectMap = {} as { string: ISubjectInfo[] };

  isInit: boolean = false; // 是否初始化

  public constructor() {
    super();
  }

  /**
   * @description: 初始化考试信息
   * @param {IExamReportInfo} examInfo 考试信息
   */
  public async initExam(examInfo: IExamReportInfo) {
    const examId = examInfo.examId;
    const v = examInfo.v

    const gradeInfo = { gradeCode: examInfo.gradeCode, gradeName: examInfo.gradeName };
    const gradeList = [gradeInfo];

    // const subjectRes = await getExamSubjectList({ examId });
    const subjectRes = await listExamSubject({ examId, statType: 1, v });
    const subjectList = subjectRes.data;
    const subjectInfo = subjectRes.data[0];
    const subjectId = subjectRes.data[0]?.id;
    this.examSubjectMap[examId] = subjectList;

    // const clzRes = await clzListAPI({ examId, subjectId });
    // const classList = clzRes.data;
    // const classInfo = clzRes.data[0];
    // const classId = clzRes.data[0]?.id;

    const contrastExamRes = await getContrastTestList({
      examId,
      schoolId: this.filterData.schoolId || sessionSave.get('schoolInfo').id,
      gradeId: gradeInfo.gradeCode,
      page: 1,
      pageSize: 1,
    });
    const contrastExamList = contrastExamRes.data.list;
    const contrastExamInfo = contrastExamRes.data.list[0];
    const contrastExamId = contrastExamRes.data.list[0]?.examId;

    // const stuRes = await listExamStu({
    //   examId,
    //   subjectId: '',
    //   classId,
    // });
    // const stuList = stuRes?.data;
    // const stuInfo = stuRes?.data[0];
    // const stuId = stuRes?.data[0]?.id;

    this.selectOption.gradeList = gradeList;
    this.selectOption.subjectList = subjectList;
    // this.selectOption.classList = classList;
    this.selectOption.contrastExamList = contrastExamList;
    // this.selectOption.stuList = stuList;

    this.setExamInfo(examInfo);
    this.setExamInfos([examInfo]);
    this.setGradeInfo(gradeInfo);
    await this.setSubjectInfo(subjectInfo);
    // this.setClassInfo(classInfo);
    // this.setClassInfos([classInfo]);
    this.setContrastExamInfo(contrastExamInfo);
    // this.setStuInfo(stuInfo);
    this.isInit = true;
  }

  /**
   * @description: 设置考试报告
   * @param {IExamReportInfo} examInfo
   */
  public setExamInfo(examInfo: IExamReportInfo) {
    this.filterInfo.examInfo = examInfo;
    this.filterData.examId = examInfo.examId;
  }

  /**
   * @description: 设置考试信息
   * @param {examInfos} 考试信息数组
   */
  public setExamInfos(examInfos: IExamReportInfo[]) {
    this.filterInfo.examInfos = examInfos;
    this.filterData.examList = JSON.stringify(
      examInfos.map((item: IExamReportInfo) => {
        return { id: item.examId, name: item.examName };
      })
    );
    this.setExamInfosGradeAndSubject(examInfos);
  }

  private async setExamInfosGradeAndSubject(examInfos: IExamReportInfo[]) {
    let gradeList = [];
    let gradeMap = {} as { string: IScoreSubjectInfo };

    for (const item of examInfos) {
      const gradeCode = item.gradeCode;
      const gradeName = item.gradeName;
      gradeList.push({
        gradeCode,
        gradeName,
      });

      let subjectList = [];

      if (this.examSubjectMap[item.examId]) {
        subjectList = this.examSubjectMap[item.examId];
      } else {
        const subjectRes = await listExamSubject({ examId: item.examId, statType: 1 ,v: item.v });
        subjectList = subjectRes.data;
        this.examSubjectMap[item.examId] = subjectList;
      }

      if (gradeMap[gradeCode]) {
        gradeMap[gradeCode] = gradeMap[gradeCode].concat(subjectList);
        gradeMap[gradeCode] = gradeMap[gradeCode].filter((item, index) => {
          return gradeMap[gradeCode].findIndex(v => v.id === item.id) === index;
        });
      } else {
        gradeMap[gradeCode] = subjectList;
      }
    }
    gradeList = gradeList.filter((item, index) => {
      return gradeList.findIndex(v => v.gradeCode === item.gradeCode) === index;
    });
    this.examsSelectOption.gradeMap = gradeMap;
    this.examsSelectOption.gradeList = gradeList;
    this.setExamsGradeInfo(gradeList[0]);
  }

  /**
   * @description: 设置年级信息
   * @param {IGradeInfo} gradeInfo
   */
  public setExamsGradeInfo(gradeInfo: IGradeInfo) {
    this.examsFilterInfo.gradeInfo = gradeInfo;
    this.examsFilterData.gradeId = gradeInfo?.gradeCode;
    const subjectList = this.examsSelectOption.gradeMap[gradeInfo?.gradeCode] || [];
    this.setExamsSubjectInfo(subjectList[0]);
  }

  public setExamsSubjectInfo(subjectInfo: IScoreSubjectInfo) {
    this.examsFilterInfo.subjectInfo = subjectInfo;
    this.examsFilterData.subjectId = subjectInfo?.id;
  }

  /**
   * @description: 设置班级信息
   * @param {IClassInfo} classInfo
   */
  public async setClassInfo(classInfo: IClassInfo) {
    this.filterInfo.classInfo = classInfo;
    this.filterData.classInfo = JSON.stringify([{ id: classInfo.id, name: classInfo.class_name }]);
    await this.initStu(classInfo.id);
  }

  public async initStu(classId) {
    try {
      const stuRes = await listExamStu({
        examId: this.filterData.examId,
        subjectId: '',
        classId,
      });
      const stuList = stuRes?.data;
      const stuInfo = stuRes?.data[0];
      const stuId = stuRes?.data[0]?.id;
      this.selectOption.stuList = stuList;
      this.setStuInfo(stuInfo);
    } catch (error) {
      console.error(error);
      this.selectOption.stuList = [];
      this.setStuInfo(null);
    }
  }

  /**
   * @description: 设置班级信息数组
   * @param {IClassInfo} classInfos
   */
  public setClassInfos(classInfos: IClassInfo[]) {
    this.filterInfo.classInfos = classInfos;
    this.filterData.classList = JSON.stringify(
      classInfos.map((item: IClassInfo) => {
        return {
          id: item.id,
          name: item.class_name,
        };
      })
    );
  }

  /**
   * @description: 获取班级信息数组
   * @return {IClassInfo[]} 班级信息数组
   */
  public getClassInfos(): IClassInfo[] {
    return JSON.parse(JSON.stringify(this.filterInfo.classInfos));
  }

  /**
   * @description: 设置学科信息
   * @param {IScoreSubjectInfo} subjectInfo
   */
  public async setSubjectInfo(subjectInfo: IScoreSubjectInfo) {
    this.filterInfo.subjectInfo = subjectInfo;
    this.filterData.subjectId = subjectInfo.id;
    await this.initClass(this.filterData.subjectId);
  }

  public async initClass(subjectId) {
    const clzRes = await clzListAPI({
      examId: this.filterData.examId,
      subjectId: this.filterData.subjectId,
    });
    const classList = clzRes.data;
    const classInfo = clzRes.data[0];
    const classId = clzRes.data[0]?.id;
    this.selectOption.classList = classList;
    await this.setClassInfo(classInfo);
    this.setClassInfos([classInfo]);
  }

  /**
   * @description: 设置年级信息
   * @param {IGradeInfo} gradeInfo
   */
  public setGradeInfo(gradeInfo: IGradeInfo) {
    this.filterInfo.gradeInfo = gradeInfo;
    this.filterData.gradeId = gradeInfo?.gradeCode;
  }

  /**
   * @description: 设置对比考试信息
   * @param {IContrastExamInfo} contrastExamInfo
   */
  public setContrastExamInfo(contrastExamInfo: IContrastExamInfo) {
    this.filterInfo.contrastExamInfo = contrastExamInfo;
    this.filterData.contrastExamId = contrastExamInfo?.examId;
  }

  /**
   * @description: 设置学生信息
   * @param {IStudentInfo} stuInfo 学生对象
   */
  public setStuInfo(stuInfo: IStudentInfo) {
    this.filterInfo.stuInfo = stuInfo;
    this.filterData.stuId = stuInfo?.id;
  }

  /**
   * @description: 设置学校信息
   * @param {schoolInfo} 学校信息对象
   */
  public setSchoolInfo(schoolInfo: ISchoolInfo) {
    if (JSON.stringify(this.filterInfo.schoolInfo) == JSON.stringify(schoolInfo)) {
      return;
    }
    this.filterInfo.schoolInfo = schoolInfo;
    this.filterData.schoolId = schoolInfo.id || '';
    this.isInit = false;
  }

  /**
   * 获取菜单过滤键
   *
   * @param routeName 路由名称
   * @returns 返回筛选数据键数组
   */

  public getMenuFilterKeys(routeName): FilterDataKey[] {
    const option = mergeOptionMap.get(routeName);
    const FilterDataKeys = Object.keys(this.filterData) as FilterDataKey[]; // 全部筛选类型
    if (!option) return FilterDataKeys;
    return option.filters || FilterDataKeys;
  }

  /**
   * 获取查询参数
   *
   * @param routeName 路由名称
   * @returns 查询参数对象
   */
  public getQueryParams(routeName: string): any {
    const filterData = JSON.parse(JSON.stringify(this.filterData));
    const examsFilterData = JSON.parse(JSON.stringify(this.examsFilterData));
    const keys = this.getMenuFilterKeys(routeName);
    const keySet = new Set(keys);
    const queryParams = {} as any;
    keys.forEach(key => {
      if (keySet.has('examList') && (key === 'gradeId' || key === 'subjectId')) {
        queryParams[key] = examsFilterData[key];
        return;
      }

      if (filterFieldMap[key]) {
        queryParams[filterFieldMap[key]] = filterData[key];
      } else {
        queryParams[key] = filterData[key];
      }
    });
    queryParams.schoolId = this.filterData.schoolId || sessionSave.get('schoolInfo').id;
    return queryParams;
  }

  /**
   * @description: 获取报表数据
   * @param {*} apiName 接口名称
   */
  public async getReportTableData(apiName, params = {}) {
    const queryParams = this.getQueryParams(apiName);
    let apiModule;
    if (Teacher_Option_Map.has(apiName)) {
      apiModule = 'teacher';
    } else if (Student_Option_Map.has(apiName)) {
      apiModule = 'student';
    } else if (Custom_Option_Map.has(apiName)) {
      apiModule = 'custom';
    }
    return GetEReportByModule(apiModule, apiName, { ...queryParams, ...params });
  }

  /**
   * @description: 获取教师报表数据
   * @param {*} apiName 接口名称
   * @param {*} query 查询参数
   */
  public async getTeacherReport(apiName, query?) {
    const queryParams = query ? query : this.getQueryParams(apiName);
    return GetTeacherReport(apiName, { ...queryParams });
  }

  /**
   * @description: 获取自定义报表数据
   * @param {*} apiName 接口名称
   * @param {*} query 查询参数
   */
  public async getCustomReport(apiName, query?) {
    const queryParams = query ? query : this.getQueryParams(apiName);
    return GetCustomReport(apiName, { ...queryParams });
  }

  on(key: FilterEvent, handler: Function, thisObject?: any): this {
    return super.on(key, handler, thisObject);
  }

  once(key: FilterEvent, handler: Function, thisObject?: any) {
    super.once(key, handler, thisObject);
  }

  off(eventName: FilterEvent, handler?: Function): undefined | this {
    return super.off(eventName, handler);
  }

  async trigger(eventName: FilterEvent, options?: any, isAsync: boolean = true): Promise<this> {
    return super.trigger(eventName, options, isAsync);
  }
}

export default new FilterModule();

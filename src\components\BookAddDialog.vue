<template>
  <el-dialog :visible.sync="dialogVisible" title="添加" width="950px" v-bind="$attrs" v-on="$listeners">
    <div class="select-header">
      <div class="select-header-item">
        <span class="label">学科</span>
        <el-select class="select" v-model="subjectCode" @change="getBookList">
          <el-option v-for="item in subjectList" :key="item.id" :label="item.name" :value="item.code" />
        </el-select>
      </div>
      <div class="select-header-item">
        <span class="label">年级</span>
        <el-select class="select" v-model="gradeCode" @change="getBookList">
          <el-option v-for="item in gradeList" :key="item.id" :label="item.name" :value="item.code" />
        </el-select>
      </div>
      <div class="select-header-item">
        <span class="label">年份</span>
        <el-select class="select" v-model="selected.year" placeholder="请选择年份" @change="getBookList">
          <el-option v-for="item in yearList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
    </div>
    <div class="book-list" v-if="bookList.length > 0">
      <div class="book-item" v-for="item in bookList" :key="item.id">
        <div class="book-item-cover">
          <el-image :src="item.cover" fit="cover" />
        </div>
        <div class="book-item-info">
          <div class="book-item-meta">
            <span class="meta-item" v-if="item.subjectName">{{ item.subjectName }}</span>
            <span class="meta-item" v-if="item.gradeName">{{ item.gradeName }}</span>
            <span class="meta-item" v-if="item.year">{{ item.year }}</span>
          </div>

          <div class="book-item-title" :title="item.bookName">
            {{ item.bookName }}
          </div>
          <el-button
            class="book-item-add"
            type="primary"
            :loading="isAddLoading && addId === item.bookId"
            :disabled="addIds.includes(item.bookId)"
            @click="handleAddBook(item)"
          >
            {{ addIds.includes(item.bookId) ? '已添加' : '添加' }}
          </el-button>
        </div>
      </div>
    </div>
    <no-data v-else></no-data>

    <el-pagination
      style="text-align: center"
      background
      layout="total, prev, pager, next"
      :total="pagination.total_rows"
      :page-size="pagination.limit"
      :current-page="pagination.page"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </el-dialog>
</template>

<script lang="ts">
import NoData from '@/components/noData.vue';
import { addToShelfAPI } from '@/service/pexam';
import { getAllGradeList, getAllSubjectList, getSchoolBook } from '@/service/testbank';
import { Component, Vue } from 'vue-property-decorator';

@Component({
  components: {
    NoData,
  },
})
export default class BookAddDialog extends Vue {
  // 选择器
  selected = {
    subjectId: '',
    gradeId: '',
    year: '',
  };
  // 学科列表
  subjectList = [];
  // 年级列表
  gradeList = [];
  // 年份列表
  yearList = [];
  // 年级code
  gradeCode = '';
  // 学科code
  subjectCode = '';
  // 弹窗是否显示
  dialogVisible = false;
  // 分页器
  pagination = {
    page: 1,
    limit: 8,
    total_rows: 0,
  };
  // 书本列表
  bookList = [];
  // 添加书本loading
  isAddLoading = false;
  // 当前添加的书本id
  addId = '';
  // 已添加的书本id
  addIds = [];

  async mounted() {
    this.dialogVisible = true;
    await this.getSubjectList();
    await this.getGradeList();
    await this.getYearList();
    await this.getBookList();
  }

  beforeDestroy() {
    this.dialogVisible = false;
  }

  async getSubjectList() {
    const res = await getAllSubjectList({});
    if (res && res.data.length > 0) {
      this.subjectList = res.data;
      let subjectItem = this.subjectList.find(item => item.id == this.$sessionSave.get('loginInfo').subjectid);
      if (subjectItem) {
        this.subjectCode = subjectItem.code;
      } else {
        this.subjectCode = this.subjectList[0].code;
      }
    }
  }

  /**
   * @name: 获取年级列表
   * @return: 请求结果
   */
  async getGradeList() {
    const res = await getAllGradeList({});
    if (res && res.data.length > 0) {
      this.gradeList = [{ id: '', code: '', name: '全部' }, ...res.data];
    }
  }

  // 获取年份列表
  async getYearList() {
    const year = new Date().getFullYear();
    for (let i = 0; i < 6; i++) {
      this.yearList.push({
        value: year - i,
        label: year - i,
      });
    }
    this.selected.year = this.yearList[0].value;
  }

  // 获取学科列表
  async getBookList() {
    const res = await getSchoolBook({
      gradeCode: this.gradeCode,
      subjectCode: this.subjectCode,
      year: this.selected.year,
      isOpenQues: 1,
      type: 1,
      page: this.pagination.page,
      limit: this.pagination.limit,
      schoolId: this.$sessionSave.get('schoolInfo').id,
    });
    this.bookList = res.data.rows;
    this.pagination.total_rows = res.data.total_rows;
  }

  // 分页
  handleCurrentChange(page) {
    this.pagination.page = page;
    this.getBookList();
  }

  // 添加书本
  async handleAddBook(item) {
    this.isAddLoading = true;
    this.addId = item.bookId;
    const res = await addToShelfAPI({
      userId: this.$sessionSave.get('loginInfo').id,
      schoolId: this.$sessionSave.get('schoolInfo').id,
      schoolName: this.$sessionSave.get('schoolInfo').schoolName,
      bookJson: JSON.stringify(item),
    });

    if (res.code == 1) {
      this.$message.success('添加成功');
      this.$emit('add');
      this.addIds.push(item.bookId);
    }
    this.isAddLoading = false;
  }
}
</script>

<style scoped lang="scss">
.select-header {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;

  .select-header-item {
    .label {
      font-size: 14px;
      margin-right: 10px;
    }
    .select {
      width: 140px;
    }
  }
}

.book-list {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  padding: 16px 0;
}

.book-item {
  display: flex;
  flex-direction: column;
  width: calc(100% / 4 - 20px);
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 2px solid #e5e5e5;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    border: 2px solid lighten(#409eff, 20%);
  }

  &.selected {
    border: 2px solid #409eff;
  }
}

.book-item-cover {
  position: relative;
  width: 100%;
  height: 220px;
  margin-bottom: 12px;

  .el-image {
    width: 100%;
    height: 100%;
    border-radius: 4px;
  }
}

.book-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.book-item-meta {
  margin-bottom: 5px;

  .meta-item {
    display: inline-block;
    padding: 2px 8px;
    margin-right: 8px;
    margin-bottom: 8px;
    background-color: #f5f7fa;
    color: #606266;
    border-radius: 4px;
    font-size: 12px;
  }
}

.book-item-title {
  font-size: 14px;
  font-weight: 700;
  color: #333;
  line-height: 1.4;
  margin: 0;
  margin-bottom: 5px;

  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.book-selected-icon {
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  right: 0;
  bottom: 0;
}

.selected-count {
  font-size: 15px;

  .selected-count-number {
    font-size: 16px;
    font-weight: 700;
    color: #409eff;
  }
}
</style>

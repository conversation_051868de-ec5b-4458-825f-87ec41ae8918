<!--
 * @Descripttion: 
 * @Author: 小圆
 * @Date: 2023-11-14 09:19:49
 * @LastEditors: Please set LastEditors
-->
<template>
  <div class="ques-detail">
    <!--题目资源-->
    <div class="answer_box resourceList display_flex align-items_center" v-if="quesResList">
      <br />
      <strong class="flex_shrink_0 title" style="cursor: pointer">【题目资源】</strong>
      <div class="flex_1 display_flex align-items_flex-start flex-wrap_wrap">
        <img class="answer-imgList" style="width: 50px; border: none" v-for="(imgItem, idx) in quesResList" :key="idx"
          @click="showImg(quesResList, idx)" :src="getCoverByType(imgItem.resourceType, imgItem.resourceExt)" alt="" />
      </div>
    </div>

    <!--相关资源-->
    <!-- <div class="answer_box resourceList display_flex align-items_center" v-if="resList">
      <strong class="flex_shrink_0">【相关资源】</strong>
      <div class="flex_1 display_flex align-items_flex-start flex-wrap_wrap">
        <img class="answer-imgList" v-for="(imgItem, idx) in resList" :key="idx"
          @click="showImg(resList, idx)" :src="`https://fs.iclass30.com/${imgItem.thumbnailUrl}`"
          alt="" />
      </div>
    </div> -->
    <!--考点-->
    <!--<div v-if="item.content.knowledgeName"><strong>【考点】</strong>{{item.content.knowledgeName.split(',').join('，')}}-->
    <!--</div>-->
    <div v-if="points">
      <strong>【知识点】</strong>
      <span v-for="(it, idx) in points"
        :key="it.name + Math.random()">{{ it.name }}{{ idx != points.length - 1 ? ',' : '' }}</span>
    </div>
    
    <!--答案-->
    <div class="answer_box display_flex align-items_flex-start">
      <strong class="flex_shrink_0">【答案】</strong>
      <div class="flex_1">
        <div class="answer_content" v-if="((item && item.type === 2) || content.quesType === 2) && !Array.isArray(content?.answer)
        ">
          {{ content.answerLabel }}
          <!-- {{ content.answerLabel || content.answer.split(',')[0] === 'A' ? '正确' : '错误' }} -->
        </div>

        <div class="answer_content" v-else-if="Array.isArray(content.answer)" v-html="getContentAnswerArr(content)">
        </div>

        <div class="answer_content" v-else v-html="content?.answer || item?.answer || getRightAnswer()"></div>
      </div>
    </div>

    <!--解析-->
    <div v-if="content.analysis" class="answer_box display_flex align-items_flex-start">
      <span class="flex_shrink_0"><strong>【解析】</strong></span>
      <div v-if="Array.isArray(content.analysis)" class="answer_content flex_1"
        v-html="'' + content.analysis.join('\n')">
      </div>
      <div v-else class="answer_content flex_1" v-html="'' + content.analysis"></div>
    </div>
    <!--<div class=""><strong>【点评】</strong></div>
                            <div class="display_flex align-items_flex-start" style="margin-top:5px;">
                                <strong>【统计】</strong>
                                <div>
                                    共有{{item.clsFullNum}}人答对，{{item.clsErrorNum}}人答错
                                </div>
                            </div>
                            -->
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
    },

    content: {
      type: Object,
    },
  },

  computed: {
    // 相关资源
    resList() {
      if (this.content) {
        if (this.content.resList && this.content.resList.length) return this.content.resList;
      }
      if (this.item) {
        if (this.item.resList && this.item.resList.length) return this.item.resList;
      }
      return null;
    },

    // 题目资源
    quesResList() {
      if (this.content) {
        if (this.content.data && this.content.data.url.length) return this.content.data.url;
      }
      if (this.item) {
        if (this.item.quesResList && this.item.quesResList.length) return this.item.quesResList;
      }
      return null;
    },

    // 题目考点
    points() {
      if (this.content) {
        if (this.content.points && this.content.points.length) return this.content.points;
        if (this.content.tag_ids && this.content.tag_ids.length) return this.content.tag_ids;
      }
      if (this.item) {
        if (this.item.points && this.item.points.length) return this.item.points;
        if (this.item.tag_ids && this.item.tag_ids.length) return this.item.tag_ids;
      }
      return null;
    },
  },

  methods: {
    getContentAnswerArr(content) {
      let arr = [];
      content.data.qs.forEach(item => {
        if (item.type == 2) {
          arr.push(item.answer === 'A' ? '正确' : '错误');
        } else {
          arr.push(item.answer);
        }
      });
      return arr.join('\n');
    },
    showQuesRes(list, index) {
      this.$emit('showQuesRes', list, index);
    },

    showImg(list, index) {
      this.$emit('showImg', list, index);
    },
    /**
     * @name: 根据文件类型查找封面
     * @param url 地址
     */
    getCoverByType(resourceType, resourceExt) {
      let coverUrl = '';
      switch (resourceType) {
        case '1':
          if (resourceExt === 'doc' || resourceExt === 'docx') {
            coverUrl = require('../../../assets/res-icons/document_icon_word.png');
          } else if (resourceExt === 'pdf') {
            coverUrl = require('../../../assets/res-icons/document_icon_pdf.png');
          } else if (resourceExt === 'txt') {
            coverUrl = require('../../../assets/res-icons/document_icon_txt.png');
          } else {
            coverUrl = require('../../../assets/res-icons/document_icon_word.png');
          }
          break;
        case '2':
          coverUrl = require('../../../assets/res-icons/document_icon_video.png');
          break;
        case '3':
          coverUrl = require('../../../assets/res-icons/document_icon_music.png');
          break;
        case '4':
          coverUrl = require('../../../assets/res-icons/document_icon_img.png');
          break;
        case '5':
          coverUrl = require('../../../assets/res-icons/document_icon_huohua.png');
          break;
        case '6':
          coverUrl = require('../../../assets/res-icons/document_icon_ggb.png');
          break;
        case '7':
          coverUrl = require('../../../assets/res-icons/document_icon_url.png');
          break;
        case '0':
          coverUrl = require('../../../assets/res-icons/video-cover.png');
          break;
        default:
          coverUrl = require('../../../assets/res-icons/video-cover.png');
          break;
      }
      return coverUrl;
    },

    // 获取正确答案
    getRightAnswer() {
      try {
        const data = JSON.parse(this.item?.rightAnswer);
        if (Array.isArray(data)) {
          return data.join('\n');
        } else {
          return data;
        }
      } catch (error) {
        return this.item?.rightAnswer;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.ques-detail {
  padding: 20px;

  >div {
    margin-bottom: 10px;
  }

  .resourceList {
    margin-top: 10px;
    margin-bottom: 0;
  }

  .answer-imgList {
    display: inline-block;
    width: 85px;
    max-height: 50px;
    margin-right: 10px;
    cursor: pointer;
    margin-bottom: 10px;
    border: 1px solid #cecece;
  }

  .sub {
    span {
      line-height: 28px;
    }

    .ans-btn {
      width: 155px;
      min-width: 155px;
      height: 28px;
    }

    .last {
      width: calc(100% - 110px);
    }
  }

  .obj {
    span {
      line-height: 28px;
    }

    .ans-btn {
      width: 100px;
      min-width: 100px;
      height: 28px;
    }

    .last {
      width: calc(100% - 110px);
    }
  }

  .ans {
    display: flex;
    padding: 4px 0;

    .ans-btn {
      display: inline-block;
      border: 1px solid #ccc;
      line-height: 26px;
      text-align: center;
    }

    .blue {
      color: #409effff;
      padding-left: 10px;
      display: inline-block;
    }

    .item {
      padding-left: 10px;
      display: inline-block;
    }
  }
}
</style>

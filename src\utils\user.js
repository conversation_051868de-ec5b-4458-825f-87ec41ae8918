import {
  getUserRoles,
  getGradeListByRole,
  getSubjectListByRole,
  getTeacherClass<PERSON>ist,
  getUserRoleAP<PERSON>,
  classList,
  getUserInfoAPI,
} from "@/service/api";
import { localSave, sessionSave } from "@/utils/index.js";
import { uniqueFunc } from "../common";

const roleList = [];
const roleTypes = "";
const roles = "";
const accountType = sessionSave.get("loginInfo").account_type;
const roleClassList = [];
const roleSubjectList = [];
/**
 * @name: 根据用户权限
 */
async function getUserRolesList() {
  getUserRoles({
    schoolId: sessionSave.get("schoolInfo").id,
    userId: sessionSave.get("loginInfo").id,
  }).then((res) => {
    // 0总管理员 1校管 2年级组长 3学科组长 4备课组长 5班主任 6普通老师
    roleTypes = res.data.types;
    sessionSave.set("roleTypes", res.data.types);
    let rolesStatus = res.data.status.split(",");
    let role = res.data.types.split(",");
    rolesStatus.forEach((item, index) => {
      roleList.push({
        types: role[index],
        status: item,
      });
    });
    roles = roleList[0].types;
    getRoleClass();
    getRoleSubject();
    // 1：普通教师 2：备课组长 3：年级组长 4：校长 5：运营
    if (accountType != 4 && accountType != 5) {
      getGradeList();
    }
  });
}
//获取班主任班级
function getRoleClass() {
  getTeacherClassList({
    userId: sessionSave.get("loginInfo").id,
    containLeaderClass: 1,
  })
    .then((res) => {
      let classList = [];
      res.data.fcList.length &&
        res.data.fcList.forEach((item) => {
          roleClassList.push(item);
          classList.push(item);
        });
      res.data.xzList.length &&
        res.data.xzList.forEach((item) => {
          roleClassList.push(item);
          classList.push(item);
        });
      let leaderClass = classList.filter((ite) => {
        return ite.leader_class;
      });
      sessionSave.set("leaderClass", leaderClass);
      if (roleTypes.indexOf("4") != -1) {
        //备课组长
        getPreparationInfo();
      }
      getExamClassList();
    })
    .catch((err) => {});
}
/**
 *
 * @name:根据用户角色获取学科
 */
function getRoleSubject() {
  let list = [];
  roleList.forEach((item) => {
    getSubjectListByRole({
      schoolId: sessionSave.get("schoolInfo").id,
      userId: sessionSave.get("loginInfo").id,
      userType: item.types,
    })
      .then((res) => {
        res.data.forEach((ite) => {
          list.push(ite);
        });
        if (item.types == "4") {
          //获取备课组长学科
          sessionSave.set("mainSubjectList", res.data);
        }
      })
      .catch((err) => {});
  });
  setTimeout(() => {
    roleSubjectList = uniqueFunc(list, "subjectId");
    sessionSave.set("roleSubjectList", roleSubjectList);
  }, 500);
}
/**
 * @name:获取角色对应年级
 */
function getGradeList() {
  getGradeListByRole({
    schoolId: sessionSave.get("schoolInfo").id,
    userId: sessionSave.get("loginInfo").id,
    userType: roles,
  })
    .then((res) => {
      res.data.forEach((item) => {
        this.$set(item, "id", item.gradeId);
        this.$set(item, "name", item.gradeName);
      });
      this.grdList = res.data;
      if (this.roles == 1) {
        this.grdList.unshift({
          id: "",
          name: "全部",
        });
      }

      this.gradeValue = this.grdList[0].id;
    })
    .catch((err) => {
      this.grdList = [];
    });
}
function getPreparationInfo() {}
function getExamClassList() {}
export {
  getUserRolesList,
  getRoleClass,
  getRoleSubject,
  getGradeList,
  getPreparationInfo,
  getExamClassList,
};

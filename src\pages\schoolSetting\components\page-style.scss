.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 2;
  margin-bottom: 24px;

  .header-right {
    display: flex;
    align-items: center;
  }

  .title {
    font-size: 16px;
    font-weight: 700;
    color: #303133;
  }
}

.tip {
  font-size: 14px;
  color: #515151;
  margin-top: 24px;
  margin-bottom: 24px;
  line-height: 32px;

  .title {
    // font-weight: bold;
    font-size: 14px;
    color: #f56c6c;
  }
}

::v-deep(.table-header-style) {
  background: #f5f7fa !important;
  color: #333 !important;
  font-weight: 700 !important;
}

.setting-table {
  ::v-deep .el-form-item {
    margin-bottom: 0;
    margin-right: 0;
  }
}

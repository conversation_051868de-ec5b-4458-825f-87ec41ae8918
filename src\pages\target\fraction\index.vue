<template>
  <div class="target-fraction-container">
    <div class="target-box">
      <div class="title">分数换算</div>
      <div class="content-box">
        <el-form
          :inline="true"
          :model="targetData"
          :hide-required-asterisk="true"
          class="target-form clearfix"
          ref="fractionRef"
        >
          <div class="target-form-container">
            <div v-for="(item, index) in targetData.radio" :key="item.id">
              <div class="main-container">
                <span>{{ item.name }}</span>
                <div class="radio-container input-number-container">
                  <el-radio v-model="item.checked" :label="true" @input="checkType(item, index)"
                    >按比例换算</el-radio
                  >
                  <el-form-item label="" :prop="'radio.' + index + '.radio'" class="col-rank">
                    <el-tooltip class="item" effect="dark" placement="top">
                      <div slot="content">
                        <p><i class="el-icon-info"></i>目标换算分</p>
                      </div>
                      <el-input-number
                        v-model="item.radio"
                        :disabled="!item.checked"
                        :controls="false"
                        :min="0"
                        :max="1000"
                        :precision="1"
                      ></el-input-number>
                    </el-tooltip>

                    <el-tooltip class="item" effect="dark" content="" placement="top">
                      <div slot="content">
                        <p><i class="el-icon-info"></i>题目满分</p>
                      </div>
                      <el-input-number
                        :value="item.full"
                        :controls="false"
                        disabled
                      ></el-input-number>
                    </el-tooltip>
                  </el-form-item>

                  <!-- 
                  <el-form-item
                    label=""
                    :prop="'radio.' + index + '.radio'"
                    :rules="rules.numberMaxRules"
                    class="col-rank"
                  >
                    <el-input
                      type="number"
                      v-model.number="item.radio"
                      :disabled="!item.checked"
                      :max="1000"
                      @blur="$event.target.value = $event.target.value.replace(/^\s+|\s+$/gm, '')"
                    >
                      <template slot="prepend">满分*</template>
                      <template slot="append">%</template></el-input
                    >
                  </el-form-item> -->
                </div>
              </div>
            </div>
          </div>

          <div class="target-form-container">
            <div v-for="(item, index) in targetData.bonus" :key="item.id">
              <div class="main-container">
                <div class="radio-container input-number-container">
                  <el-radio v-model="item.checked" :label="true" @input="checkType(item, index)"
                    >附加题换算</el-radio
                  >
                  <el-form-item label="" :prop="'bonus.' + index + '.bonus'" class="col-rank">
                    <el-tooltip class="item" effect="dark" content="" placement="top">
                      <div slot="content">
                        <p><i class="el-icon-info"></i>目标换算分</p>
                      </div>
                      <el-input-number
                        v-model="item.radio"
                        :disabled="!item.checked"
                        :controls="false"
                        :min="0"
                        :max="item.full"
                      ></el-input-number>
                    </el-tooltip>

                    <el-tooltip class="item" effect="dark" content="" placement="top">
                      <div slot="content">
                        <p><i class="el-icon-info"></i>题目满分</p>
                      </div>
                      <el-input-number
                        :value="item.full"
                        :controls="false"
                        disabled
                      ></el-input-number>
                    </el-tooltip>
                  </el-form-item>
                </div>
              </div>
            </div>
          </div>
        </el-form>
      </div>
    </div>
    <div class="target-box" style="position: relative">
      <div class="save-btn" style="left: 20px; right: unset" v-if="customExamId">
        <el-button
          type="primary"
          @click="$emit('updateActiveStep', activeStep - 1, customExamId)"
          :disabled="isDisabled"
        >
          上一步</el-button
        >
      </div>
      <div class="save-btn">
        <el-button type="primary" @click="save" :disabled="isDisabled"> 保存设置</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { setAllConfAPI, getConfAPI } from '@/service/pexam';
import { checkNumberVal } from '@/utils/common.js';
import UserRole from '@/utils/UserRole';
export default {
  props: {},
  data() {
    return {
      activeStep: 5,
      customExamId: this.$route.query.customExamId,
      targetData: {},
      //最高分
      tempScore: '',
      rules: {
        // 数字和必填项验证规则
        numberMaxRules: [
          {
            required: true,
            message: '必填项',
            trigger: 'blur',
          },
          {
            type: 'number',
            message: '请输入0-1000',
            trigger: 'blur',
          },
          {
            validator: (rule, value, callback) => {
              if (String(value).split('.')[1]?.length > 1) {
                callback(new Error('小数点只能保留一位'));
              } else if (Number(value) > 1000 || Number(value) < 0) {
                callback(new Error('请输入0-1000'));
              } else {
                callback();
              }
            },
            trigger: 'blur',
          },
        ],
      },
    };
  },
  computed: {
    isDisabled() {
      return !(UserRole.isOperation || UserRole.isSchoolLeader || UserRole.isGradeLeader);
    },
  },
  mounted() {
    this.geConf();
  },
  methods: {
    /**
     * @name:获取默认指标数据
     */
    async geConf() {
      const res = await getConfAPI({
        examId: this.customExamId
          ? this.customExamId
          : this.$sessionSave.get('reportDetail').examId,
        type: 102,
      });
      this.targetData = JSON.parse(res.data);
      this.$emit('updateActiveStep', this.activeStep, this.customExamId);
    },

    // 设置类型
    checkType(item, index) {
      for (const key in this.targetData) {
        if (Object.hasOwnProperty.call(this.targetData, key)) {
          this.targetData[key][index].checked = false;
        }
      }
      item.checked = true;
    },

    /**
     * @name:保存设置
     */
    async save() {
      const fraction = new Promise((resolve, reject) => {
        this.$refs.fractionRef.validate((valid, message) => {
          if (valid) {
            resolve(this.targetData);
          } else {
            reject(message);
          }
        });
      });
      let arr = [fraction];

      try {
        await Promise.all(arr);
        const json = JSON.stringify(this.targetData);
        setAllConfAPI({
          examId: this.customExamId
            ? this.customExamId
            : this.$sessionSave.get('reportDetail').examId,
          type: 102,
          content: json,
        }).then(res => {
          const reportDetail = this.$sessionSave.get('reportDetail');
          this.$sessionSave.set('reportDetail', {
            ...reportDetail,
            v: reportDetail.v + 1,
          });
          this.$message({
            message: '保存成功！',
            type: 'success',
            duration: 1500,
          });
          if (this.customExamId) {
            this.$sessionSave.remove('customReportInfo');
            setTimeout(() => {
              this.$router.push({
                path: '/home/<USER>',
                query: { fromPage: 'customExamReport' },
              });
            }, 300);
          }
        });
      } catch (error) {
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.main-container {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.target-form {
  display: flex;

  .target-form-container {
    margin-left: 20px;

    &:first-of-type {
      margin-left: 0;
    }
  }
}

.input-number-container {
  ::v-deep .el-input-number {
    width: auto;
  }
}
</style>
<style lang="scss">
.target-fraction-container {
  font-size: 16px !important;

  .el-form-item {
    margin-bottom: 0px !important;
  }

  .el-input__inner {
    width: 110px;
  }

  .radio-group-container {
    padding: 10px 20px 20px 20px;
  }

  .radio-container {
    display: flex;
    align-items: center;
    margin-left: 50px;
  }

  .tip-txt,
  .title-tips {
    color: #fe5d50;
    font-weight: bold;
  }

  .tip-txt {
    padding: 0 20px;
  }

  .title {
    position: relative;
    margin: 20px 0;
    padding: 0 10px;
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;

    &:before {
      overflow: hidden;
      position: absolute;
      left: 0;
      display: block;
      content: '';
      width: 4px;
      height: 24px;
      top: -3px;
      background: #409eff;
      border-radius: 3px;
    }

    .sub-title {
      color: #7e94a8;
      font-weight: 400;
      margin-left: 10px;
    }
  }

  .m-10 {
    margin-bottom: 10px;
  }

  .btn_item {
    height: 30px;
    width: 70px;
    padding: unset;
  }

  .save-btn {
    position: absolute;
    right: 20px;
    text-align: center;
    margin-top: 20px;

    .el-button {
      width: 136px;
      height: 36px;
    }
  }

  .icon-del {
    background: url('../../../assets/icon_delete.png');
    margin-top: -2px;
  }

  .icon-addItem {
    margin-left: 20px;
    background: url('../../../assets/icon-add.png');
  }

  .icon-edit {
    width: 19px !important;
    background: url('../../../assets/icon_edit.png');
  }

  .icon-item {
    width: 16px;
    height: 18px;
    display: inline-block;
    vertical-align: middle;
  }

  .content-box {
    padding: 0 20px;

    .content-main {
      position: relative;

      .content-form {
        width: 92%;
      }
    }
  }

  .tool-sec {
    position: absolute;
    right: 0;
    top: 10px;
    display: inline-block;
  }

  .custom-sec {
    float: right;
    margin-right: 25px;
    margin-top: 20px;
  }

  .custom-form-item {
    margin-right: unset !important;
  }

  .btn_del {
    border-color: #409eff !important;
    color: #409eff !important;
  }

  .col-title {
    margin-right: unset !important;
  }
}
</style>

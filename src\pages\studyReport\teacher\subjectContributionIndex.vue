<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-04-02 09:03:13
 * @LastEditors: 小圆
-->
<template>
  <div>
    <div v-loading="tableLoading">
      <base-table
        v-if="tableData.length"
        :data="tableData"
        :column="tableColumns"
        v-bind="getTableAttr()"
        v-drag-table
      ></base-table>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>

    <!-- 学科贡献指数（平均分）对比图 -->
    <div style="margin-top: 20px; width: 100%" id="TDevoteChart"></div>
    <!-- 学科平均分-贡献指数二维图 -->
    <div style="margin-top: 20px; width: 100%" id="ScatterChart"></div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins, Prop } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';
import {
  getDefaultGrid,
  getDefaultLegend,
  getDefaultTitle,
  getDefaultToolBox,
  getDefaultTooltipFormatter,
} from '../plugins/DefaultEchartsOption';

@Component({
  components: {},
})
export default class SubjectContributionIndex extends Mixins(TableCommon) {
  // 贡献指数对比图
  tDevoteChart: EChartsType = null;
  // 学科平均分-贡献指数二维图
  scatterChart: EChartsType = null;

  callbackGetTableData() {
    this.renderTDevoteChart();
    this.renderScatterChart();
  }

  // 渲染学科贡献指数（平均分）对比图
  renderTDevoteChart() {
    if (this.tDevoteChart) {
      this.tDevoteChart.dispose();
      this.tDevoteChart = null;
    }
    const dom = document.getElementById('TDevoteChart');
    this.tDevoteChart = this.$echarts.init(dom, null, {
      height: 450,
    });

    let option: EChartsOption = {
      toolbox: getDefaultToolBox(),
      title: {
        ...getDefaultTitle(),
        text: `学科贡献指数（平均分）对比图`,
      },
      tooltip: {
        trigger: 'item',
        // formatter: params => {
        //   return getDefaultTooltipFormatter(
        //     params.name,
        //     params.seriesName,
        //     params.value,
        //     params.color
        //   );
        // },
      },
      legend: getDefaultLegend(),
      grid: getDefaultGrid(),
      xAxis: {
        type: 'value',
        boundaryGap: [0, 0.01],
        minInterval: 1,
      },
      yAxis: {
        type: 'category',
        data: this.tableData.map(item => item.className),
      },
      series: [
        {
          name: '学科贡献指数',
          type: 'bar',
          data: this.tableData.map(item => item.tDevote),
          barMaxWidth: 50,
          label: {
            show: true,
          },
          markLine: {
            lineStyle: {
              type: 'dashed',
              color: '#F00',
            },
            data: [{ type: 'average', name: '平均指数' }],
          },
        },
      ],
    };

    this.tDevoteChart.setOption(option);
  }

  // 渲染学科平均分-贡献指数二维图
  renderScatterChart() {
    if (this.scatterChart) {
      this.scatterChart.dispose();
      this.scatterChart = null;
    }

    const dom = document.getElementById('ScatterChart');
    this.scatterChart = this.$echarts.init(dom, null, {
      height: 600,
    });

    // const seriesData = [];
    let subjectScoreSum = 0;
    let tDevoteSum = 0;
    this.tableData.forEach(item => {
      const subjectScore = parseFloat(item.subjectScore); // 原始分
      const tDevote = parseFloat(item.tDevote); // 贡献指数
      subjectScoreSum += subjectScore;
      tDevoteSum += tDevote;
      // seriesData.push();
    });

    const subjectScoreAvg = subjectScoreSum / this.tableData.length;
    const tDevoteAvg = tDevoteSum / this.tableData.length;
    const series = [];
    this.tableData.forEach((item, index) => {
      let subjectScore = parseFloat(item.subjectScore);
      let tDevote = parseFloat(item.tDevote);

      let color = '';
      if (subjectScore >= subjectScoreAvg && tDevote >= tDevoteAvg) {
        color = '#50c48f';
      } else if (subjectScore < subjectScoreAvg && tDevote >= tDevoteAvg) {
        color = '#3cb9fc';
      } else if (subjectScore < subjectScoreAvg && tDevote < tDevoteAvg) {
        color = '#FF9132';
      } else if (subjectScore >= subjectScoreAvg && tDevote < tDevoteAvg) {
        color = '#fa4343';
      }

      series.push({
        name: item.className,
        type: 'scatter',
        symbolSize: 30,
        data: [[subjectScore, tDevote]],
        emphasis: {
          focus: 'series',
        },
        itemStyle: {
          normal: {
            label: {
              show: true,
              position: 'inside',
              formatter: '{a}',
            },
            value: item.className,
            opacity: 0.8,
            color: color,
          },
        },
      });
    });

    let option: EChartsOption = {
      title: { ...getDefaultTitle(), text: `学科平均分-贡献指数二维图` },
      grid: getDefaultGrid(),
      toolbox: getDefaultToolBox(),
      graphic: this.getGraphicArr(),
      tooltip: {
        show: true,
        trigger: 'item',
        formatter: params => {
          if (!params.seriesName) return '';
          var html =
            '<div style="font-size:14px;color:#666;font-weight:400;line-height:1;">' +
            params.seriesName +
            '</div>';
          for (var i = 0; i < params.value.length; i++) {
            html +=
              '<div style="margin: 10px 0 0;line-height:1;">' +
              '<span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">' +
              `${i == 0 ? '平均分' : '贡献指数'}` +
              '</span>' +
              '<span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">' +
              params.value[i] +
              '</span>' +
              '<div style="clear:both"></div>';
            html += '</div>';
          }
          return html;
        },
      },
      xAxis: {
        name: '平均分',
        type: 'value',
        nameGap: 20,
        nameLocation: 'middle',
        axisTick: {
          inside: true,
        },
        splitLine: {
          show: false,
        },
        scale: true,
      },
      yAxis: {
        name: '贡献指数',
        type: 'value',
        nameGap: 30,
        nameLocation: 'middle',
        axisTick: {
          inside: true,
        },
        splitLine: {
          show: false,
        },
        scale: true,
      },
      series: [
        {
          type: 'scatter',
          tooltip: {
            show: false,
          },
          markLine: {
            lineStyle: {
              type: 'solid',
              color: '#F00',
              width: 2,
            },
            data: [
              {
                type: 'average',
                yAxis: tDevoteAvg,
              },
              {
                type: 'average',
                xAxis: subjectScoreAvg,
              },
            ],
          },
        },
        ...series,
      ],
    };

    this.scatterChart.setOption(option);
  }

  getGraphicArr() {
    let graphicArr = [];
    let graphic_left_top = {
      type: 'text',
      left: '9%',
      top: '20%',
      style: { text: ['水平较低\n贡献到位'], fill: '#3cb9fc' },
    };
    let graphic_right_top = {
      type: 'text',
      right: '5%',
      top: '20%',
      style: { text: ['水平较高\n贡献到位'], fill: '#50c48f' },
    };
    let graphic_left_bottom = {
      type: 'text',
      left: '9%',
      bottom: '15%',
      style: { text: ['水平较低\n贡献不到位'], fill: '#FF9132' },
    };
    let graphic_right_bottom = {
      type: 'text',
      right: '5%',
      bottom: '15%',
      style: { text: ['水平较高\n贡献不到位'], fill: '#fa4343' },
    };
    graphicArr.push(graphic_left_top);
    graphicArr.push(graphic_right_top);
    graphicArr.push(graphic_left_bottom);
    graphicArr.push(graphic_right_bottom);
    return graphicArr;
  }
}
</script>

<style scoped lang="scss"></style>

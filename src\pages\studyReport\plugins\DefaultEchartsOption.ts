/*
 * @Description:
 * @Author: 小圆
 * @Date: 2024-05-06 17:37:32
 * @LastEditors: 小圆
 */

export function getDefaultGrid(config?) {
  return Object.assign(
    {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: 100,
      containLabel: true, //显示全坐标文本，里面图形宽高自适应
    },
    config
  );
}

export function getDefaultLegend(config?) {
  return Object.assign(
    {
      show: true,
      x: 'center',
      y: 'top',
      padding: [40, 5, 20, 5],
    },
    config
  );
}

export function getDefaultTitle(config?) {
  return Object.assign(
    {
      left: 'center',
    },
    config
  );
}

export function getDefaultToolBox(config?) {
  return Object.assign(
    {
      show: true,
      right: 28,
      feature: {
        mark: { show: true },
        saveAsImage: { show: true, title: '保存为图片' },
      },
    },
    config
  );
}

export function getDefaultPercentAxis(config?) {
  return Object.assign(
    {
      type: 'value',
      min: 0,
      max: 100,
      interval: 20,
      axisLabel: {
        formatter: '{value}%',
      },
    },
    config
  );
}

export function getDefaultTooltipFormatter(name, title, value, color?) {
  return `<div style="font-size:14px;color:#666;font-weight:400;line-height:1;text-align:left;">
  <span>${name}</span>
</div>
<div style="margin: 10px 0 0;line-height:1;">
  <span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">
    <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${
      color || '#5c7bd9'
    };">
    </span>
    ${title}
  </span>
  <span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${value}</span>
</div>
`;
}

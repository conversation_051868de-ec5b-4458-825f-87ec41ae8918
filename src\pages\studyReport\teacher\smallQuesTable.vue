<template>
  <div>
    <div v-loading="tableLoading">
      <div v-if="tableData.length">
        <base-table
          class="table"
          :data="tableData"
          :column="tableColumns"
          :span-method="handleSpanMethod"
          v-bind="getTableAttr()"
          v-drag-table
        ></base-table>
      </div>

      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';
import FilterModule from '../plugins/FilterModule';

@Component({
  components: {},
})
export default class Index extends Mixins(TableCommon) {
  // 表格左侧固定列
  tableLeftFixed: any[] = ['stuNo', 'stuName'];

  /**
   * 合并单元格句柄方法
   */
  handleSpanMethod({
    row, // 行
    column, // 列
    rowIndex, // 行索引
    columnIndex, // 列索引
  }) {
    if (rowIndex == this.tableData.length - 1 || rowIndex == this.tableData.length - 2) {
      if (column.property === 'stuName') {
        return [1, 2];
      } else if (column.property === 'stuNo') {
        return [1, 0];
      } else if (column.property === 'clsRank') {
        return [1, 3];
      } else if (column.property === 'grdRank') {
        return [1, 0];
      } else if (column.property === 'remark') {
        return [1, 0];
      }
    }
  }
}
</script>

<style scoped lang="scss">
.table {
  margin-top: 20px;
}
</style>

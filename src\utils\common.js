import Base64 from 'js-base64';

/**
 * 文件地址
 */
export const FS_URL = process.env.VUE_APP_FS_URL;

export function inArr(a, arr) {
  return arr.indexOf(a) >= 0;
}

const mathContentReg1 = /<span[^>]*>\\\(([\s\S]*?)\\\)<\/span>/g;
const mathContentReg2 = /\\\(([\s\S]*?)\\\)/g;
const mathContentReg3 = /\$([\s\S]*?)\$/g;

/**
 * 获取html里的公共转换成图片显示
 * @param html
 * @returns {Promise<string|*>}
 */
export function convertHtml(html, subject) {
  //是否为语文或英语学科
  const subjectIds = ['1', '3', '10', '12', '24', '25', 1, 3, 10, 12, 24, 25];
  let isChineseOrEnglish = subjectIds.includes(subject);
  if (!html) {
    return '';
  }
  //语文英语不转换公式
  if (isChineseOrEnglish) {
    return html;
  }
  html = html.replace(/<a[^>]*>(.*?)<\/a>/g, '$1');
  html = html.replace(/<span[^>]*class="MathJax_Preview"[^>]*><\/span>/g, '');

  html = html.replace(mathContentReg2, convertLatex2image);
  html = html.replace(mathContentReg3, convertLatex2image);
  html = html.replace(mathContentReg1, convertLatex2image);
  return html;
}

function convertLatex2image(all, input) {
  input = input.replace(/&amp;/g, '&');
  input = input.replace(/&nbsp;/g, ' ');
  input = input.replace(/<br.?>/g, '');
  input = input.replace(/&lt;/g, '<');
  input = input.replace(/&gt;/g, '>');
  try {
    const res = MathJax.tex2svg(input, { display: false });

    return `<span data-latex="${input}" class="math-tex-svg">${res.outerHTML}</span>`;
  } catch (e) {
    try {
      let html = katex.renderToString(input, {
        displayMode: true,
        leqno: false,
        fleqn: false,
        throwOnError: true,
        errorColor: '#cc0000',
        strict: 'warn',
        output: 'htmlAndMathml',
        trust: false,
        macros: { '\\f': 'f(#1)' },
      });
      return `<span alt="${input}" class="math-tex-svg">${html}</span>`;
    } catch (e) { }
  }

  console.log(input);
  return all;
  // return `<img alt="${input}" class="math-tex-img" src="${MATH_CDN}/mathsvg/${encodeURIComponent(encodeURIComponent(input))}.svg"/>`
}

export function fixedNoZero(num, fixed) {
  if (fixed < 0) {
    fixed = 0;
  }
  num = Number(num);
  if (num) {
    return Number(num.toFixed(fixed));
  }
  return '';
}
export function checkFloatNum(rule, value, callback) {
  const regPos = /^\d+(\.\d+)?$/;
  if (regPos.test(value)) {
    callback();
  } else {
    callback(new Error('请输入非负数'));
  }
}
/**
 * @name 判断数字是否大于0小于100
 */
export function checkNumberVal(rule, value, callback) {
  if (Number(value) > 100 || Number(value) < 0) {
    // value = null;
    callback(new Error('请输入0-100'));
  } else {
    callback();
  }
}
/**
 * @name 判断数字是否大于0小于100
 */
export function checkNumberMaxandMinVal(rule, value, callback) {
  if (Number(value) > 1000 || Number(value) < 5) {
    // value = null;
    callback(new Error('请输入5-1000'));
  } else {
    callback();
  }
}
/**
 * @name 判断是否是数字
 */
export function checkIsNumber(rule, value, callback) {
  const regPos = /^[0-9]*$/;
  if (regPos.test(value)) {
    callback();
  } else {
    callback(new Error('请输入非负数'));
  }
}
/**去重对象数组
 * @param arr 要去重的数组
 * @param id  根据什么字段去重
 */
export function uniqueFunc(arr, id) {
  const res = new Map();
  return arr.filter(item => !res.has(item[id]) && res.set(item[id], 1));
}
/**根据属性分组
 * @param arr 需要分组的数组
 * @param group_key  根据什么字段
 * @param name
 */
export function reviseListMap(arr, group_key, name) {
  let map = {};
  for (let i = 0; i < arr.length; i++) {
    let ai = arr[i];
    if (!map[ai[group_key]]) {
      map[ai[group_key]] = [`${ai[name]}`];
    } else {
      map[ai[group_key]].push(`${ai[name]}`);
    }
  }
  Object.keys(map).forEach(key => {
    map[key] = map[key].join(',');
  });
  return map;
}
/**
 * 获取DPI
 * @returns {Array}
 */
export const conversion_getDPI = () => {
  const arrDPI = new Array();
  if (window.screen.deviceXDPI) {
    arrDPI[0] = window.screen.deviceXDPI;
    arrDPI[1] = window.screen.deviceYDPI;
  } else {
    const tmpNode = document.createElement('DIV');
    tmpNode.style.cssText =
      'width:1in;height:1in;position:absolute;left:0px;top:0px;z-index:99;visibility:hidden';
    document.body.appendChild(tmpNode);
    arrDPI[0] = parseInt(tmpNode.offsetWidth);
    arrDPI[1] = parseInt(tmpNode.offsetHeight);
    tmpNode.parentNode.removeChild(tmpNode);
  }
  return arrDPI;
};
/**
 * px转换为mm
 * @param value
 * @returns {number}
 */
export const pxConversionMm = value => {
  const inch = value / conversion_getDPI()[0];
  const c_value = inch * 25.4;
  //      console.log(c_value);
  return c_value;
};

/**
 * mm转换为px
 * @param value
 * @returns {number}
 */
export const mmConversionPx = value => {
  const inch = value / 25.4;
  const c_value = inch * conversion_getDPI()[0];
  //      console.log(c_value);
  return c_value;
};
/**
 * @name: 获取别名信息
 * @param info 书本信息对象
 * @return: 增加了别名信息的资源路径
 */
export function getAliasInfo(info) {
  let title = info.bookAlias || '校本练习册';
  let coverUrl = 'https://fs.iclass30.com/aliba/plug/kkl/icon_book.png';
  coverUrl += `?x-oss-process=image/resize,m_fill,w_320,h_440`;
  //学科
  coverUrl += getImageMark(info.subjectName, 44, '000000', 'north', 0, 85, 80);
  //名称
  coverUrl += getImageMark(title, 28, '009cff', 'north', 0, 200);
  //小横线
  coverUrl += getImageMark('—', 20, '009cff', 'north', 0, 245);
  //版本
  coverUrl += getImageMark(info.editionName || '其他', 28, '009cff', 'north', 0, 275);
  //年级
  let gradeName = info.gradeName;
  // if (info.volumeName) {
  //   gradeName += info.volumeName || info.volumename;
  // }
  // if (info.volumename) {
  //   gradeName += info.volumename;
  // }
  gradeName += info.volumeName || info.volumename;
  coverUrl += getImageMark(gradeName, 28, '009cff', 'north', 0, 310);

  // 年份
  if (info.year) {
    coverUrl += getImageMark(info.year, 28, '009cff', 'north', 0, 350);
  }
  return coverUrl;
}
/**
 * @name: 增加水印
 * @param txt 文字内容
 * @param size 字号
 * @param color 字体颜色
 * @param g 水印在图片中的位置
 * @param x 水平位移
 * @param y 垂直位移
 * @param shadow 水印的阴影透明度
 * @return: 增加水印后的资源路径
 */
export function getImageMark(txt, size, color, g, x, y, shadow) {
  if (!txt) {
    return '';
  }
  if (txt.length > 12) {
    txt = txt.substr(0, 12) + '...';
  }
  let text = Base64.Base64.encode(txt);
  text = text.replace(/\+/g, '-');
  text = text.replace(/\//g, '_');
  text = text.replace(/\//g, '_');
  let result = `/watermark,type_ZHJvaWRzYW5zZmFsbGJhY2s,size_${size},text_${text},color_${color},t_100,g_${g},x_${x},y_${y}`;
  if (shadow) {
    result += ',shadow_' + shadow;
  }
  return result;
}
export function getDiffStartStar(difficuity, isJY = false) {
  let diff = difficuity;
  if (isJY) {
    let diffList = QuesModule.jyDifficulties;
    for (let i = 0; i < diffList.length; i++) {
      let obj = diffList[i];
      if (diff >= obj.minValue && diff < obj.maxValue) {
        diff = obj.yqValue;
        break;
      }
    }
  }
  let res = 1;
  if (diff <= 1) {
    res = 1;
  } else if (diff > 1 && diff <= 2) {
    res = 2;
  } else if (diff > 2 && diff <= 3) {
    res = 3;
  } else if (diff > 3 && diff <= 4) {
    res = 4;
  } else if (diff > 4 && diff <= 5) {
    res = 5;
  }
  return res;
}
/**
 * @name: 根据题目typeid获取题目类型对象
 * @param typeid 题型code
 * @param subtype 选择题二级题型code
 * @return: 对应题目类型对象
 */
export function getQuesType(typeid, subtype) {
  if (this.isChooseType(typeid) && !isNullOrUndefined(subtype)) {
    let info = this.quesTypeList.getByKeys(
      { key: 'queTypeId', value: typeid },
      { key: 'subType', value: subtype }
    );
    return info || this.quesTypeList.getByKey('queTypeId', '-1');
  }
  return (
    this.quesTypeList.getByKey('queTypeId', typeid) || this.quesTypeList.getByKey('queTypeId', '-1')
  );
}
/**
 * @name: 智批题答案特殊标记替换
 */
export function replaceFillevaAnsSign(html) {
  html = html.replace(/；或；/g, '（或）');
  return html;
}
/**
 * @name: 判断是否公式
 * @return: 是否公式
 */
export function replaceMathHtml(html) {
  html += '';
  let isMQStu = /\\+|\^+|\_+/.test(html) && !/\\\(/.test(html);
  html = isMQStu ? '\\(' + html + '\\)' : html;
  return html;
}
export function getImgUrl(nums, cardInfo) {
  let pageLayout = cardInfo.pageLayout == 1 ? 'A4' : 'A3';
  let url = FS_URL + '/scan/pdf/' + cardInfo.paperNo + '_' + pageLayout + '/' + nums + '.png';

  return url;
}
export function getImgList(cardInfo) {
  let pageLayout = cardInfo.pageLayout == 1 ? 'A4' : 'A3';
  let imgList = [];
  for (let i = 0; i < cardInfo.paperNum; i++) {
    let url = FS_URL + '/scan/pdf/' + cardInfo.paperNo + '_' + pageLayout + '/' + i + '.png';
    imgList.push(url);
  }
  return imgList;
}
export function drawImgStyle(cardInfo) {
  let style = {
    width: 428 + 'px',
    height: 600 + 'px',
    display: 'inline-block',
  };
  //A4
  if (cardInfo.pageLayout != '1') {
    style.width = 560 + 'px';
    style.height = 400 + 'px';
  }
  return style;
}

export function checkFloat(value) {
  value = value
    .toString()
    .replace(/[^\d.]/g, '') // 清除“数字”和“.”以外的字符
    .replace(/\.{2,}/g, '.') // 只保留第一个. 清除多余的
    .replace('.', '$#$')
    .replace(/\./g, '')
    .replace('$#$', '.')
    .replace(/^(\-)*(\d+)\.(\d).*$/, '$1$2.$3'); // 只能输入两个小数
  if (!/\./g.test(value) && value != '') {
    // 以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
    return parseFloat(value);
  }
  // 类似于.123这样的以点开始,其余都为数字,且小数点后面多余2位
  if (/^\.(\d+)$/g.test(value) && value.length > 2) {
    return value.substr(0, 2);
  }
  return value;
}

/*
 * @name 地址替换
 * */
export function replaceALiUrl(url) {
  if (!url) return '';
  if (url.startsWith("data:image")) return url;

  const fs_url = process.env.VUE_APP_FS_URL;
  if (!url.startsWith("http")) {
    if (url.startsWith("/")) {
      url = fs_url + url;
    } else {
      url = fs_url + '/' + url;
    }
  }
  return url;
}

export function gradeMap() {
  return {
    1: '一年级',
    2: '二年级',
    3: '三年级',
    4: '四年级',
    5: '五年级',
    6: '六年级',
    7: '七年级',
    8: '八年级',
    9: '九年级',
    10: '高一',
    11: '高二',
    12: '高三',
  };
}

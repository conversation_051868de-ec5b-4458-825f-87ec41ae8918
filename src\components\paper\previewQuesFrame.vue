<!--
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2024-11-25 14:11:59
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2024-11-25 16:44:56
-->
<template>
    <div class="preview-ques-frame">
        <el-radio v-model="orderType" @change="changeOrderType" label="2">同题型合并<span class="red-tips">注：多个小题共用题干的不合并</span></el-radio>
        <div class="ques-list" style="max-height: 50vh;overflow: auto;">
                <template v-for="(item, index) in quesInfos[0].data">
                    <div class="big-ques ques-item">
                        <span class="title">{{ item.name }}（{{ item.defaultScore }}分）</span>
                    </div>
                    <div class="small-ques ques-item" v-for="(qitem, qindex) in item.data">
                        <template v-if="qitem.data?.length > 0">
                            {{ getThreeQuesName(qitem.data) }}
                        </template>
                        <template v-else>
                            {{ qitem.name }}
                        </template>
                    </div>
                </template>

            </div>
            <div class="btn-box">
                <span class="red-tips">确认更改后不可撤回</span>
                <el-button @click="close">取消</el-button>
                <el-button type="primary" @click="submit">确认修改</el-button>
            </div>
    </div>
</template>

<script>
import { getQuesType } from "@/utils";
export default {
    props: ["quesList"],
    data() {
        return {
            orderType: '2',
            quesInfos: [],
        }
    },
    computed: {
        totalScore() {
            let score = 0;
            this.quesInfos.forEach(item => {
                item.data.forEach(bitem => {
                    bitem.data.forEach((qitem) => {
                        if (qitem.data) {
                            qitem.data.forEach((sqitem) => {
                                score += Number(sqitem.score);
                            })
                        } else {
                            score += Number(qitem.defaultScore);
                        }
                    })
                })
            })
            return score;
        }
    },
    created() {
        let quesinfo = JSON.parse(JSON.stringify(this.quesList));
        this.quesInfos = this.orderQuesType(quesinfo);
    },
    methods: {
        orderQuesType(quesinfo){
            let quesInfos = [];
            quesinfo.forEach(item => {
                item.data.forEach(bitem => {
                    //三级题目直接插入新数组
                    if(bitem.levelcode){
                        quesInfos.push(bitem)
                    }else{
                        bitem.data.forEach((qitem) => {
                            let isTypeExit = false;
                            let totalTypes = [];
                            totalTypes = quesInfos.map(item => {
                                if(item.levelcode == ''){
                                    return item.typeId;
                                }
                            });
                            isTypeExit = totalTypes.includes(qitem.typeId) ;
                            if (!isTypeExit) {
                                quesInfos.push({
                                    data:  [qitem],
                                    defaultScore: qitem.defaultScore,
                                    isAverage: false,
                                    isChange: false,
                                    isChangeSort: false,
                                    level: 2,
                                    levelcode: "",
                                    name: `${this.$sectionToChinese(quesInfos.length+1)}、${getQuesType(qitem.typeId)}`,
                                    type: getQuesType(qitem.typeId),
                                    typeId: qitem.typeId
                                });
                            }else{
                                quesInfos.forEach(sub => {
                                  if (sub.typeId == qitem.typeId && sub.levelcode == '') {
                                    sub.data.push(qitem);
                                  }
                                });
                            }
                        })
                    }
                })
            });
            let quesNo = 1;
            quesInfos.forEach(bitem => {
                if(bitem.levelcode){
                    bitem.data[0].data.forEach(sqitem=>{
                        sqitem.quesNo = quesNo;
                        sqitem.quesNos = quesNo;
                        sqitem.name = quesNo;
                        quesNo++;
                    })
                }else{
                    let score = 0;
                    bitem.data.forEach(qitem => {
                        qitem.quesNo = quesNo;
                        qitem.quesNos = quesNo;
                        qitem.name = quesNo;
                        quesNo++;
                        score += Number(qitem.defaultScore);
                    })
                    bitem.defaultScore = score;
                }
            });
            quesinfo[0].data = quesInfos;
            return quesinfo;
        },
        getThreeQuesName(data) {
            if(data.length == 1){
                return data[0].name;
            }
            return `${data[0].name}-${data[data.length - 1].name}`;
        },
        close() {
            this.$emit('close')
        },
        submit() {
            this.$emit('saveFrame',this.quesInfos)
        }
    }
}
</script>

<style lang="scss" scoped>
.preview-ques-frame{
    .red-tips{
        color: red;
        margin: 0 5px;
    }
    .ques-list{
        margin: 10px;
        .ques-item{
            line-height: 24px;
        }
    }
    .btn-box{
        text-align: right;
    }
}
</style>
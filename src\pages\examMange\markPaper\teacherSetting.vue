<template>
  <div class="allocate-container">
    <div class="allocate-content">
      <!--左侧题目列表-->
      <div class="left-ques-list-area">
        <!--头部-->
        <div class="left-ques-list-area-header">
          <span class="paper-title">{{ paperTitle }}</span>
          <div class="exam-category">
            {{ subjectName }}
          </div>
          <span class="ques-num-summary">（共{{ quesList.length }}题须批阅）</span>
          <template v-if="isStartCorrect && source == ISOURCE_TYPES.WEB">
            <el-button v-if="markingStatus == IMARKING_STATUS.CORRECTING" class="pause-button" type="warning"
              @click="pauseCorrect" icon="el-icon-video-pause">暂停阅卷</el-button>
            <el-button v-else class="continue-button" type="primary" @click="continueCorrect"
              icon="el-icon-video-play">继续阅卷</el-button>
          </template>
          <el-button class="batch-button" type="text" @click="openSetAssignDialog('', '', 'batch')">批量设置</el-button>
        </div>
        <!--列表-->
        <div class="left-ques-list-area-body">
          <el-row class="table-header">
            <el-col :span="3">题目</el-col>
            <el-col :span="3">题号</el-col>
            <el-col :span="3">评阅方式</el-col>
            <el-col :span="3">阅卷分配</el-col>
            <el-col :span="3">题组长
              <el-popover placement="right-start" trigger="hover">
                <p>负责该题块的问题卷处理并支持抽查</p>
                <i class="el-icon-question" slot="reference"></i>
              </el-popover>
            </el-col>
            <el-col :span="3">阅卷教师 </el-col>
            <el-col :span="3">仲裁教师 </el-col>
            <el-col :span="3">操作 </el-col>
          </el-row>
          <!--内容-->
          <div class="content-height" :class="assignState != 1 ? '' : 'content-height-done'" v-if="quesList.length > 0">
            <template v-for="(item, index) in quesList">
              <el-row class="table-content">
                <el-col :span="3" class="text-area" :title="item.title">
                  <div>
                    <span class="showOverTooltip">{{ item.title }} </span>
                  </div>
                  <div class="square" v-if="item.scanMode != IQUES_SCAN_MODE.NORMAL">
                    <span class="square-word">AI</span>
                  </div>
                </el-col>
                <el-col :span="3" class="text-area" :title="item.titleNos">
                  <div>
                    <span class="text-container">{{ item.titleNos }} </span>
                  </div>
                </el-col>
                <!-- 评阅方式 -->
                <el-col :span="3" class="text-area" style="display: flex; flex-direction: column">
                  <span class="assign-button" :class="{ assignDisabled: item.scanMode != IQUES_SCAN_MODE.NORMAL }"
                    @click="openSetMethodsDialog(item, index)"><i class="el-icon-setting"></i>{{ isAIDoubleEval(item) ? '人机双评' : (item.correctMode == 1 ?
                    '单评' : '双评') }}</span>
                  <span style="margin-top: -15px" v-if="item.correctMode == 2 && item.isArbitration">分差{{
                    item.beyondScore }}分</span>
                </el-col>
                <!-- 阅卷分配 -->
                <el-col :span="3" class="text-area" :title="item.title">
                  <span class="assign-button"
                    :class="{ assignDisabled: (!isAIDoubleEval(item) && item.scanMode != IQUES_SCAN_MODE.NORMAL) || item.typeId == 7 }"
                    @click="openSetAssignDialog(item, index, 'single')"><i class="el-icon-setting"
                      v-if="currentAssignType != 4"></i>{{ getAssignName(item.assignType) }}</span>
                </el-col>
                <!-- 设置题组长 -->
                <el-col :span="3" class="tea-col">
                  <template v-if="item.scanMode == IQUES_SCAN_MODE.NORMAL || isAIDoubleEval(item)">
                    <div class="add-tea-area" v-if="item.leaderList.length == 0" @drop="drop($event, index, 'leader')"
                      @dragover="allowDrop($event, 'leader')">
                      <div class="add-tea" v-if="currentAssignType != 4">
                        <div class="add-tea-text">拖动教师至此处</div>
                      </div>
                    </div>
                    <template v-else>
                      <div class="sele-allocate-tea-list" @drop="drop($event, index, 'leader')"
                        @dragover="allowDrop($event, 'leader')">
                        <template v-for="(subItem, subIndex) in item.leaderList">
                          <div class="sele-allocate-tea-item" :title="subItem.teaName + '(' + subItem.mobile + ')'">
                            <div class="add-tea-text add-tea-text-alsw">
                              {{ subItem.teaName }}
                            </div>
                            <Icon type="md-close-circle" class="delete-tea" v-on:click="
                              cancelCurrSeleTea(index, subIndex, subItem.teaId, 'leader')
                              " v-if="assignState != 1 && currentAssignType != 4"></Icon>
                          </div>
                        </template>
                        <div class="sele-allocate-tea-item add-tea-alsw"
                          v-if="assignState != 1 && currentAssignType != 4">
                          <div class="add-tea-text">拖动教师至此处</div>
                        </div>
                      </div>
                    </template>
                  </template>
                  <template v-else> ---- </template>
                </el-col>
                <!-- 设置阅卷教师 -->
                <el-col :span="3" class="tea-col">
                  <div class="add-tea-area" v-if="item.teacherList.length == 0" @drop="drop($event, index, 'correct')"
                    @dragover="allowDrop($event, 'correct')">
                    <div class="add-tea" v-if="currentAssignType != 4">
                      <div class="add-tea-text">拖动教师至此处</div>
                    </div>
                  </div>
                  <template v-else>
                    <div class="sele-allocate-tea-list" @drop="drop($event, index, 'correct')"
                      @dragover="allowDrop($event, 'correct')">
                      <template v-for="(subItem, subIndex) in item.teacherList">
                        <div class="sele-allocate-tea-item" :title="subItem.teaName + '(' + subItem.mobile + ')'">
                          <div class="add-tea-text add-tea-text-alsw">
                            {{ subItem.teaName }}
                          </div>
                          <Icon type="md-close-circle" class="delete-tea" v-on:click="
                            cancelCurrSeleTea(index, subIndex, subItem.teaId, 'correct')
                            " v-if="assignState != 1 && currentAssignType != 4"></Icon>
                        </div>
                      </template>
                      <div class="sele-allocate-tea-item add-tea-alsw"
                        v-if="assignState != 1 && currentAssignType != 4">
                        <div class="add-tea-text">拖动教师至此处</div>
                      </div>
                    </div>
                  </template>
                </el-col>
                <!-- 设置仲裁教师 -->
                <el-col :span="3" class="tea-col">
                  <template v-if="item.correctMode == 2 && item.isArbitration">
                    <div class="add-tea-area" v-if="item.arbitrationTeacherList?.length == 0"
                      @drop="drop($event, index, 'arbitration')" @dragover="allowDrop($event, 'arbitration')">
                      <div class="add-tea" v-if="currentAssignType != 4">
                        <div class="add-tea-text">拖动教师至此处</div>
                      </div>
                    </div>
                    <template v-else>
                      <div class="sele-allocate-tea-list" @drop="drop($event, index, 'arbitration')"
                        @dragover="allowDrop($event, 'arbitration')">
                        <template v-for="(subItem, subIndex) in item.arbitrationTeacherList">
                          <div class="sele-allocate-tea-item" :title="subItem.teaName + '(' + subItem.mobile + ')'">
                            <div class="add-tea-text add-tea-text-alsw">
                              {{ subItem.teaName }}
                            </div>
                            <Icon type="md-close-circle" class="delete-tea" v-on:click="
                              cancelCurrSeleTea(index, subIndex, subItem.teaId, 'arbitration')
                              " v-if="assignState != 1 && currentAssignType != 4"></Icon>
                          </div>
                        </template>
                        <div class="sele-allocate-tea-item add-tea-alsw"
                          v-if="assignState != 1 && currentAssignType != 4">
                          <div class="add-tea-text">拖动教师至此处</div>
                        </div>
                      </div>
                    </template>
                  </template>
                  <template v-else> ---- </template>
                </el-col>
                <!-- 操作 -->
                <el-col :span="3" class="text-area">
                  <el-button type="text" :disabled="assignState == 1"
                    @click="openCopyTeacherDialog(item, index)">复制阅卷任务</el-button>
                </el-col>
              </el-row>
            </template>
          </div>
          <div class="content-height" :class="assignState != 1 ? '' : 'content-height-done'" v-else>
            <div class="no-data">暂无数据</div>
          </div>
        </div>
        <!--底部-->
        <div class="left-ques-list-area-footer" v-if="assignState != 1">
          <div class="footer-btn-area">
            <!-- <el-button class="cancel-btn" @click="saveExamQuesMark(0)">保存，暂不提交</el-button> -->
            <el-button type="primary" class="save-btn"
              :disabled="isStartCorrect && markingStatus == IMARKING_STATUS.CORRECTING"
              :title="isStartCorrect && markingStatus == IMARKING_STATUS.CORRECTING ? '请先暂停阅卷再进行分配' : ''"
              :loading="loading" @click="getExamQuesMark()">
              {{ loading ? 'Loading...' : '完成分配' }}
            </el-button>
          </div>
        </div>
      </div>
      <!--右侧老师列表-->
      <div class="right-tea-list-area">
        <!--头部标题-->
        <div class="right-tea-list-header">
          <div class="marking-tea">选择阅卷老师</div>
          <div class="split-line"></div>
          <div class="use-explain">选择教师拖动至左侧</div>
        </div>
        <div class="right-tea-list-body" :class="assignState != 1 ? '' : 'right-tea-list-body-done'"
          v-if="currSubTeaList.length > 0">
          <template v-for="(item, index) in currSubTeaList">
            <div class="tea-item">
              <div :draggable="currentAssignType != 4" class="tea-name" @dragstart="handleDragStart($event, item)"
                :title="item.teaName + '(' + item.mobile + ')'">
                <div class="add-tea-text add-tea-text-alsw">
                  {{ item.teaName }}
                </div>
              </div>
              <div v-if="item.queCount != 0" class="tea-allocate-num">
                已分配<span class="num">{{ item.queCount }}</span>题
              </div>
            </div>
          </template>
        </div>
        <div class="right-tea-list-body" :class="assignState != 1 ? '' : 'right-tea-list-body-done'" v-else>
          暂无任课教师
        </div>
        <div class="right-tea-list-footer" v-if="assignState != 1 && currentAssignType != 4">
          <p class="add-another-tea" v-on:click="addSchoolTea">+ 添加学校其他老师</p>
        </div>
      </div>
    </div>
    <!-- 开始阅卷后修改分配选择分配方式 -->
    <el-dialog title="分配方式" :visible.sync="afterAssignDialog" width="30%">
      <el-radio v-model="afterAssignType" label="1">按总量分配</el-radio>
      <el-radio v-model="afterAssignType" label="2">按余量分配</el-radio>
      <div class="set-assign-container">
        <span v-if="afterAssignType == 1"> 包括已阅任务，保持总量分配规则。 </span>
        <span v-else> 不包括已阅任务，对剩余量进行分配。 </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="afterAssignDialog = false">取 消</el-button>
        <el-button type="primary" @click="sureSetAfterAssign">确 定</el-button>
      </span>
    </el-dialog>
    <!--选择本校的其他教师-->
    <sele-more-tea ref="addSchoolTeaRef" :modalVisible="teaFormVisible" :titleName="'添加老师'" :subjectid="subjectId"
      :workId="workId.toString()" :teaList="currSubTeaList" @confirm-sele-tea="confirmSeleTea"
      @close-sele-tea-modal="closeSeleTeaModal"></sele-more-tea>
    <!-- 分配方式 -->
    <set-assign-methods ref="setScoreBatch" :modalVisible="isShowAssign" :currentQues="currentQues" :quesList="quesList"
      :setType="setType" @confirm-set-assign="confirmSetAssign" @close-set-assign="closeSetAssign"></set-assign-methods>
    <!-- 评阅方式 -->
    <set-correct-methods-dialog ref="setCorrectMethods" v-if="isShowCorrect" :modalVisible="isShowCorrect"
      :currentQues="currentQues" :quesList="quesList" :progress="Number(progress)"
      @close-set-correct-modal="closeSetCorrect" @confirm-set-correct="confirmSetCorrect"></set-correct-methods-dialog>
    <!-- 复制阅卷任务 -->
    <copy-teacher-dialog ref="copyTeacher" v-if="isShowCopyDialog" :modalVisible="isShowCopyDialog"
      :quesList="needCopyQues" :progress="Number(progress)" @close-copy-teacher="closeCopyTeacher"
      @confirm-copy-teacher="confirmCopyTeacher"></copy-teacher-dialog>
  </div>
</template>

<script>
import seleMoreTea from '@/components/seleMoreTea.vue';
import {
  findQuestionAssignmentList,
  getSchoolTeacher,
  generateAssignment,
  findTeacherAssignmentList,
  changeWorkClass,
  changeMarkingJson,
  setMarkingStatusAPI,
  getMarkingStatusAPI
} from '@/service/api';
import { updateProgress, getExamInfoAPI } from '@/service/pexam';
import SetAssignMethods from './modules/setAssignMethods.vue';
import { getQueryString } from '@/utils';
import SetCorrectMethodsDialog from './modules/setCorrectMethodsDialog.vue';
import CopyTeacherDialog from './modules/copyTeacherDialog.vue';
import { ISPECIAL_QUES_TYPE } from '@/typings/scan';
import { IQUES_SCAN_MODE, IPROGRESS_TYPE, ISOURCE_TYPES } from '@/typings/card';

const IMARKING_STATUS = {
  /**阅卷中*/
  CORRECTING: 0,
  /**阅卷暂停*/
  PAUSE: 1,
}
export default {
  name: 'teacher-setting',
  components: {
    seleMoreTea,
    SetAssignMethods,
    SetCorrectMethodsDialog,
    CopyTeacherDialog,
  },
  props: {
    examName: {
      type: String,
      default: () => '',
    },
    //客观题
    objectQuesData: {
      type: Array,
      default: () => [],
    },
    //智批题
    aiQuesData: {
      type: Array,
      default: () => [],
    },
    //是否完成题目框选设置
    isCompleteSetArea: {
      type: Boolean,
      default: () => true,
    },
    isAIHasAnswer: {
      type: Boolean,
      default: () => true,
    }
  },
  created() {
    //阻止浏览器默认drop行为（火狐)
    document.body.ondrop = function (event) {
      event.preventDefault();
      event.stopPropagation();
    };
  },
  activated() { },
  data() {
    return {
      ISPECIAL_QUES_TYPE,
      IQUES_SCAN_MODE,
      ISOURCE_TYPES,
      IMARKING_STATUS,
      //阅卷分配的高度
      examContentStyle: {
        height: '536px',
      },
      //加载中状态
      loading: false,
      //考试id
      examId: this.$route.query.examId,
      //个册id
      personalBookId: this.$route.query.personBookId,
      //试卷id
      workId: this.$route.query.workId,
      //学科id
      subjectId: this.$route.query.subjectId,
      //学科名称
      subjectName: this.$route.query.subjectName,
      //试卷名称
      paperTitle: this.$route.query.examName,
      //是否已分配完改试卷
      assignState: 0,
      //该试卷的题目数量
      quesCount: 5,
      //题目集合
      quesList: [],
      //当前学科的教师集合
      currSubTeaList: [],
      //选择教师弹框
      teaFormVisible: false,
      //试卷详情
      examInfo: {},
      //已分配题目教师
      assignedTeaList: [],
      //批量设置分值弹窗
      isShowAssign: false,
      //当前题目下标
      currentQuesIndex: 0,
      //设置题组弹窗
      isShowQuesGroup: false,
      paperNo: getQueryString('paperNo') || '101678',
      //当前题目
      currentQues: {},
      //分配方式 batch:批量  single:单题设置
      setType: '',
      selectedQuesList: '',
      progress: getQueryString('progress') || IPROGRESS_TYPE.cardSetting,
      progressState: getQueryString('progressState') || '1',
      source: getQueryString('source') || ISOURCE_TYPES.HAND,
      afterAssignDialog: false,
      afterAssignType: '1', //0:默认值  1:按总量分配  2:按剩余分配
      //是否显示评阅方式弹窗
      isShowCorrect: false,
      //是否显示复制阅卷任务弹窗
      isShowCopyDialog: false,
      //需要复制阅卷任务的题目
      needCopyQues: [],
      //被复制阅卷任务的题目
      copiedQues: {},
      markingStatus: IMARKING_STATUS.CORRECTING
    };
  },
  computed: {
    //当前批量设置的分配类型
    currentAssignType() {
      return this.quesList.length > 0 && this.quesList[0].assignType;
    },
    //是否人机双评
    isAIDoubleEval(){
      return (item)=>{
        return item.scanMode > IQUES_SCAN_MODE.AI_FILL && item.correctMode == 2
      }
    },
    //判断客观题是否都设置答案
    isAllHasAnswer() {
      if (this.objectQuesData.length > 0 || this.aiQuesData.length > 0) {
        return ![...this.objectQuesData, ...this.aiQuesData].some(item => {
          return item.answer == '';
        });
      } else {
        return true;
      }
    },
    isStartCorrect() {
      return this.progress >= IPROGRESS_TYPE.startCorrect;
    },
  },
  async mounted() {
    await this.getExamInfo();
    if (this.isStartCorrect) {
      await this.getMarkingStatus();
    }
    //获取试卷的分配详情
    await this.getQuestionAssignmentList();
    //获取已分配老师
    await this.getTeacherAssignmentList();
    this.initElement();
  },
  methods: {
    getAssignName(type) {
      let name = '';
      switch (type) {
        case 1:
          name = '平均分配';
          break;
        case 2:
          name = '动态平均';
          break;
        case 3:
          name = '定量分配';
          break;
        case 4:
          name = '按任教班级';
          break;
        case 5:
          name = '效率优先';
          break;
      }
      return name
    },
    /**
     * @name: 根据试卷id获取试卷信息
     */
    async getExamInfo() {
      let result = await getExamInfoAPI({ examId: this.examId });
      if (result && result.code === 1) {
        this.examInfo = result.data;
      }
    },
    continueCorrect() {
      this.markingStatus = IMARKING_STATUS.CORRECTING;
      this.setMarkingStatus(this.markingStatus);
    },
    pauseCorrect() {
      this.markingStatus = IMARKING_STATUS.PAUSE;
      this.setMarkingStatus(this.markingStatus);
    },
    async getMarkingStatus() {
      const res = await getMarkingStatusAPI({
        workId: this.workId
      });
      if (res.code == 1) {
        this.markingStatus = res.data;
      }
    },
    async setMarkingStatus(status) {
      const res = await setMarkingStatusAPI({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        examId: this.examId,
        paperNo: this.paperNo,
        status: status,
      });
      if (res.code == 1) {
        this.$message({
          message: '操作成功',
          type: 'success',
          duration: 1500,
        });
      }
    },
    /**
     * 关闭教师选择弹框
     */
    closeSeleTeaModal() {
      this.teaFormVisible = false;
    },
    /**
     * 确认选择添加的教师
     */
    confirmSeleTea(teaList) {
      for (let i = 0; i < teaList.length; i++) {
        let curObj = this.currSubTeaList.filter(sitem => {
          return teaList[i].key === sitem.teaId;
        })[0];
        if (!curObj) {
          this.currSubTeaList.push({
            teaId: teaList[i].key,
            teaName: teaList[i].label,
            mobile: teaList[i].mobile,
            queCount: 0,
          });
        }
      }
      this.closeSeleTeaModal();
    },
    allowDrop(e, type) {
      e.preventDefault();
    },
    drop(e, index, type) {
      if (this.assignState != 1) {
        let list = [];
        switch (type) {
          case 'correct':
            list = this.quesList[index].teacherList;
            break;
          case 'arbitration':
            list = this.quesList[index].arbitrationTeacherList;
            break;
          case 'leader':
            list = this.quesList[index].leaderList;
            break;
          default:
            break;
        }
        e.preventDefault();
        let data = e.dataTransfer.getData('Text');
        let dataItem = JSON.parse(data);
        let leaderTeaId = this.quesList[index].leaderList?.map(item => {
          return item.teaId;
        });
        if (type == 'leader') {
          //题组长处理
          if (!leaderTeaId.includes(dataItem.teaId)) {
            list.push(dataItem);
            this.quesList = [...this.quesList];
          }
          return;
        }
        //避免向集合中塞相同的值
        let curObj = list?.filter(sitem => {
          return dataItem.teaId === sitem.teaId;
        })[0];
        let teaId = this.quesList[index].teacherList?.map(item => {
          return item.teaId;
        });
        let arbitrationTeaId = this.quesList[index].arbitrationTeacherList?.map(item => {
          return item.teaId;
        });
        let allTeaId = [...teaId, ...arbitrationTeaId];
        // 判断是否已存在于阅卷教师和仲裁教师之中
        let isExit = teaId.includes(dataItem.teaId) && type == 'correct';
        let isLeaderExit = arbitrationTeaId.includes(dataItem.teaId) && type == 'arbitration';
        // let isExit = false;
        if (!isExit && !isLeaderExit) {
          if (type == "correct") {
            for (let i = 0; i < this.currSubTeaList.length; i++) {
              if (this.currSubTeaList[i].teaId == dataItem.teaId) {
                this.currSubTeaList[i].queCount = this.currSubTeaList[i].queCount + 1;
                dataItem.queCount = dataItem.queCount + 1;
              }
            }
          }
          list.push(dataItem);
          this.quesList = [...this.quesList];
        }
        // else {
        //   if (
        //     (type == 'arbitration' && teaId.includes(dataItem.teaId)) ||
        //     (type == 'correct' && arbitrationTeaId.includes(dataItem.teaId))
        //   ) {
        //     this.$Message.error('仲裁教师不可与阅卷教师重复！');
        //   }
        // }
      } else {
        this.$Message.error('已完成分配，暂不支持修改！');
      }
    },
    handleDragStart(e, item) {
      e.dataTransfer.setData('Text', JSON.stringify(item));
    },
    /**
     *取消当前选择的教师
     */
    cancelCurrSeleTea(index, subIndex, teaId, type) {
      let list = [];
      switch (type) {
        case 'correct':
          list = this.quesList[index].teacherList;
          break;
        case 'arbitration':
          list = this.quesList[index].arbitrationTeacherList;
          break;
        case 'leader':
          list = this.quesList[index].leaderList;
          break;
        default:
          break;
      }
      list.splice(subIndex, 1);
      if (type != 'leader' && type != 'arbitration') {
        for (let i = 0; i < this.currSubTeaList.length; i++) {
          if (this.currSubTeaList[i].teaId == teaId) {
            this.currSubTeaList[i].queCount = this.currSubTeaList[i].queCount - 1;
          }
        }
      }
      this.quesList = [...this.quesList];
    },
    /**
     * 获取试卷题目分配详情
     */
    getQuestionAssignmentList() {
      let _this = this;

      let params = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        // workId: _this.workId,
        userId: this.$sessionSave.get('loginInfo').id,
        paperNo: this.paperNo,
        examId: this.examId,
      };
      findQuestionAssignmentList(params)
        .then(function (result) {
          if (result.code == 1) {
            //获取试卷题型信息
            // _this.loading = false;
            _this.quesList = result.data.examRelationList;
            _this.selectedQuesList = JSON.stringify(_this.quesList);
            // _this.paperTitle =  result.data.workTitle;
            _this.assignState = result.data.isOver;
            _this.$nextTick(() => {
              _this.initElement();
            });
          }
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    /**
     * 获取代课老师列表
     */
    async getTeacherAssignmentList() {
      let _this = this;
      let params = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        userId: this.$sessionSave.get('loginInfo').id,
        workId: _this.workId,
        subjectId: _this.subjectId,
        other: 0, //是否是选择其他老师 0：否 1：是 默认1
        paperNo: _this.paperNo,
        examId: _this.examId,
      };
      // getTeacherAssignmentList
      await findTeacherAssignmentList(params)
        .then(function (result) {
          if (result.code == 1) {
            _this.assignedTeaList = result.data;
            //获取学校下该学年该学科老师列表
            _this.getSchoolTeaList();
            // _this.$nextTick(() => {
            //   _this.initElement();
            // });
          }
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    /**
     * 获取代课老师列表
     */
    async getSchoolTeaList() {
      let _this = this;
      let params = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        year: this.examInfo.year,
        subjectId: this.subjectId,
        paperNo: this.paperNo,
        page: 1,
        limit: 10000,
      };
      // getTeacherListSupportSubjectAndGrade
      await getSchoolTeacher(params)
        .then(function (result) {
          if (result.code == 1) {
            result.data.rows.forEach(item => {
              item.teaName = item.realName;
              item.teaId = item.id;
              item.isAllocated = 0;
              item.queCount = 0;
              _this.assignedTeaList.forEach(sitem => {
                if (sitem.teaId == item.teaId) {
                  item.isAllocated = sitem.isAllocated;
                  item.queCount = sitem.queCount;
                }
              });
            });
            // console.log(_this.assignedTeaList);
            _this.currSubTeaList = _this.assignedTeaList;
          }
        })
        .catch(function (error) {
          console.log(error);
        });
    },
    /**
     * 添加学校的其他教师
     */
    addSchoolTea() {
      this.teaFormVisible = true;
      this.$refs.addSchoolTeaRef.getSubjectList();
    },
    /**
     * 完成匹配当前分配的数据
     */
    async getExamQuesMark() {
      let _this = this;
      let doubleList = this.quesList.filter(item => {
        return item.correctMode == 2 && item.teacherList.length < 2 && !this.isAIDoubleEval(item);
      });
      if (doubleList.length > 0) {
        let titleNos = doubleList.map(item => {
          return item.titleNos;
        });
        this.$message({
          message: `${titleNos.join(',')}题设置了双评，阅卷教师至少设置2位老师`,
          type: 'warning',
          duration: 1500,
        });
        return;
      }
      let arbitrationList = this.quesList.filter(item => {
        return (
          item.correctMode == 2 && item.isArbitration && item.arbitrationTeacherList.length == 0
        );
      });
      if (arbitrationList.length > 0) {
        let titleNos = arbitrationList.map(item => {
          return item.titleNos;
        });
        this.$message({
          message: `${titleNos.join(',')}题设置了仲裁但未设置仲裁老师，请设置仲裁老师！`,
          type: 'warning',
          duration: 1500,
        });
        return;
      }
      let leaderList = this.quesList.filter(item => {
        return (
          item.leaderList.length == 0 && item.scanMode == IQUES_SCAN_MODE.NORMAL
        );
      });
      if (leaderList.length > 0) {
        let titleNos = leaderList.map(item => {
          return item.titleNos;
        });
        this.$message({
          message: `${titleNos.join(',')}题未设置题组长，请先完成设置！`,
          type: 'warning',
          duration: 1500,
        });
        return;
      }
      let taskList = this.quesList.filter(item => {
        let teacherTaskCount = item.teacherList.reduce((acc, cur) => {
          return acc + Number(cur.taskCount || 0);
        }, 0);
        if (item.assignRule == 2) {
          return (
            item.assignType == 3 && item.actualTotalCount > 0 && item.actualTotalCount != teacherTaskCount
          );
        } else {
          return (
            item.assignType == 3 && item.taskTotalCount > 0 && item.taskTotalCount != teacherTaskCount
          );
        }
      });
      if (taskList.length > 0) {
        let titleNos = taskList.map(item => {
          return item.titleNos;
        });
        this.$message({
          message: `${titleNos.join(',')}题阅卷分配未完成，请先完成设置！`,
          type: 'warning',
          duration: 1500,
        });
        return;
      }
      // 屏蔽仲裁教师与阅卷教师重复的校验
      // let duplicatesList = this.quesList.filter(item => {
      //   const set1 = new Set(item.teacherList.map(item => item.teaId));
      //   const set2 = new Set(item.arbitrationTeacherList.map(item => item.teaId));
      //   const duplicates = [...set1].filter(item => set2.has(item));
      //   return duplicates.length > 0;
      // });
      // if(duplicatesList.length > 0){
      //   let titleNos = duplicatesList.map(item => {
      //     return item.titleNos;
      //   });
      //   this.$message({
      //     message: `${titleNos.join(',')}题仲裁教师与阅卷教师重复！`,
      //     type: 'warning',
      //     duration: 3000,
      //   });
      //   return;
      // }

      //网阅开始阅卷后
      if (_this.source == ISOURCE_TYPES.WEB && _this.progress >= IPROGRESS_TYPE.startCorrect) {
        _this.checkEdit();
      } else {
        _this.saveAssign();
      }
      // _this.saveAssign();
    },
    /**
     * @name:校验是否修改
     */
    async checkEdit() {
      let dataObj = {};
      dataObj.examTeachers = this.quesList;
      let params = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        paperNo: this.paperNo,
        json: JSON.stringify(dataObj),
      };
      let res = await changeMarkingJson(params);
      if (res.code == 1) {
        if (res.data === 0) {
          //未做修改给以提示保存成功
          this.$Message.success('保存成功');
        } else {
          this.afterAssignType = 1;
          this.saveExamQuesMark('after');
          // this.afterAssignDialog = true;
        }
      } else {
        this.$Message.error('请求失败');
      }
    },
    /**
     * @name:确定分配
     */
    sureSetAfterAssign() {
      this.afterAssignDialog = false;
      this.saveExamQuesMark('after');
    },
    /**
     * @name：未开始阅卷时分配老师
     */
    async saveAssign() {
      let _this = this;
      let curObj = this.quesList.filter(item => {
        return item.teacherList.length === 0;
      })[0];
      let params = {
        subjectId: this.subjectId,
        paperNo: this.paperNo,
        examId: this.examId,
      };
      await changeWorkClass(params).then(res => {
        if (res.data.length != 0) {
          let classNames = res.data.join(',');
          _this.$Message.error(`${classNames}未设置任课教师，请先设置任课教师！`);
          return;
        }
        if (curObj) {
          _this.instance('warn');
        } else {
          _this.$Modal.confirm({
            title: '提示',
            content: "<p style='font-size:16px;margin-top: -6px;'>确定完成阅卷分配吗？</p>",
            onOk: () => {
              _this.loading = true;
              _this.saveExamQuesMark();
            },
            onCancel: () => { },
          });
        }
      }).catch(err => {
      });
    },
    /**
     * 展示当前未匹配的题目
     */
    instance(type) {
      if (type == 'warn') {
        let titleList = [];
        for (let i = 0; i < this.quesList.length; i++) {
          if (this.quesList[i].teacherList.length == 0) {
            titleList.push(`${this.quesList[i].title}（${this.quesList[i].titleNos}）`);
          }
        }
        titleList = titleList.join('、');
        this.$Modal.warning({
          content: "<p style='font-size:16px;margin-top: -6px;'>" + titleList + '尚未分配阅卷!</p>',
          okText: '我知道了',
        });
      } else if (type == 'info') {
        this.$Modal.info({
          title: '提示',
          content:
            "<p style='font-size:16px;margin-top: -6px;'>当前阅卷分配为平均分配，若添加1位老师，则系统将批改任务全部分配给该老师，若添加多位老师，则系统将批改任务平均分配多位老师。</p>",
        });
      }
    },
    /**
     * 保存当前分配的数据
     */
    saveExamQuesMark(type = '') {
      let _this = this;
      let dataObj = {};
      dataObj.examTeachers = _this.quesList;
      let params = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        userId: this.$sessionSave.get('loginInfo').id,
        // workId: _this.workId,
        paperNo: this.paperNo,
        json: JSON.stringify(dataObj),
        op: 1, //0:保存  1:完成匹配
        afterAssignType: type == 'after' ? this.afterAssignType : 0,
        examId: this.examId,
      };
      generateAssignment(params)
        .then(
          function (result) {
            if (result && parseInt(result.code) === 1) {
              _this.$Message.success('分配成功');
              //客观题设置了答案
              if (_this.isAllHasAnswer && _this.isCompleteSetArea && _this.isAIHasAnswer){
                _this.changeProcess(1);
              }else{
                _this.changeProcess(0);
              }
              _this.$emit('complete-assign', true);
            }
          },
          function (err) {
            _this.$Message.error(err.toString());
          }
        )
        .catch(function (error) {
          console.log(error);
        });
      this.loading = false;
    },
    /**
     * @name：改变当前的进度
     */
    async changeProcess(state) {
      let params = {
        examId: this.examId,
        progress: IPROGRESS_TYPE.correctSetting,
        personalBookId: this.personalBookId,
        progressState: state,
        schoolId: this.$sessionSave.get('schoolInfo').id,
      };
      await updateProgress(params);
      // if (result && result.code) {
      //     this.$message({
      //         message: "设置成功！",
      //         type: "success",
      //         duration: 500,
      //     });
      //     //更新列表数据
      //     this.$emit('reload-data');
      // }
    },
    /**
     * @name:打开设置分配方式弹窗
     */
    openSetAssignDialog(item, index, type) {
      if(this.assignState == 1)return;
      //按任教班级不支持单独操作
      if (item !="" && ((!this.isAIDoubleEval(item) && item.scanMode != IQUES_SCAN_MODE.NORMAL) || item.assignType == 4 || item.typeId == 7)) return;
      this.setType = type;
      this.currentQues = item == '' ? this.quesList[0] : item;
      this.currentQuesIndex = index;
      this.isShowAssign = true;
    },

    /**
     * @name:打开设置评阅方式弹窗（单，双评）
     */
    openSetMethodsDialog(item, index) {
      if (item.scanMode != IQUES_SCAN_MODE.NORMAL) return;
      this.currentQues = item;
      this.currentQuesIndex = index;
      this.isShowCorrect = true;
    },
    /**
     * @name:关闭设置评阅方式弹窗
     */
    closeSetCorrect() {
      this.isShowCorrect = false;
    },
    /**
     * @name:确认设置评阅方式
     */
    confirmSetCorrect(data) {
      const currentQuestion = this.quesList[this.currentQuesIndex];
      if (currentQuestion.correctMode != data.correctMode) {
        if (data.correctMode == 2) {
          data.taskTotalCount = data.stuTotalCount * 2;
          data.actualTotalCount = data.actualTotalCount * 2;
        } else {
          data.taskTotalCount = data.stuTotalCount;
          data.actualTotalCount = data.actualTotalCount;
          data.arbitrationTeacherList = [];
        }
      }
      Object.assign(currentQuestion, {
        correctMode: data.correctMode,
        beyondScore: data.beyondScore,
        finalScore: data.finalScore,
        arbitrationTeacherList: data.arbitrationTeacherList,
        isCheckScore: data.isCheckScore,
        isArbitration: data.isArbitration,
        taskTotalCount: data.taskTotalCount,
        actualTotalCount: data.actualTotalCount
      });
    },
    /**
     * @name:打开复制阅卷任务弹窗
     */
    openCopyTeacherDialog(ques, index) {
      this.copiedQues = ques;
      this.needCopyQues = this.quesList.filter(item => {
        return item.quesNos !== ques.quesNos;
      });
      this.isShowCopyDialog = true;
    },
    /**
     * @name:关闭复制阅卷任务弹窗
     */
    closeCopyTeacher() {
      this.isShowCopyDialog = false;
    },
    /**
     * @name:确认复制阅卷任务
     */
    confirmCopyTeacher(data) {
      let copiedQues = JSON.parse(JSON.stringify(this.copiedQues));
      this.isShowCopyDialog = false;
      this.quesList.forEach(item => {
        if (data.checkedQues.includes(item.quesNos)) {
          for (let i = 0; i < data.checkedModes.length; i++) {
            let key = data.checkedModes[i];
            if (key == 'arbitration') {
              if(copiedQues.correctMode == 1)return;
              item.arbitrationTeacherList = copiedQues['arbitrationTeacherList'].map(tea => ({
                ...tea,
              }));
              if(item.scanMode != IQUES_SCAN_MODE.NORMAL) return;
              //仲裁信息
              item.isArbitration = copiedQues['isArbitration'];
              item.beyondScore = Number(item.score) > Number(copiedQues['beyondScore']) ? copiedQues['beyondScore'] : item.score;
              item.isCheckScore = copiedQues['isCheckScore'];
              item.finalScore = copiedQues['finalScore'];
            } else if (key == 'teacherList') {
              item.teacherList = this.copiedQues['teacherList'].map(tea => ({ ...tea }));
              item.teacherList.forEach(tea => {
                this.$set(tea, 'queCount', data.checkedQues.length + 1);
                this.currSubTeaList.forEach(curr => {
                  if (curr.teaId == tea.teaId) curr.queCount = data.checkedQues.length + 1;
                });
              });
            } else if (key == 'leaderList') {
              item.leaderList = this.copiedQues['leaderList'].map(tea => ({ ...tea }));
              // item.leaderList.forEach(tea => {
              //   this.$set(tea, 'queCount', data.checkedQues.length + 1);
              //   this.currSubTeaList.forEach(curr => {
              //     if (curr.teaId == tea.teaId) curr.queCount = data.checkedQues.length + 1;
              //   });
              // });
            } else if (key == 'assignType') {
              if (item.scanMode == IQUES_SCAN_MODE.NORMAL) {
                //非智批题支持复制阅卷分配模式
                item.assignType = copiedQues['assignType'];
                item.assignRule = copiedQues['assignRule'];
              }
            } else if (key == "correctMode") {
              if (item.scanMode != IQUES_SCAN_MODE.NORMAL || copiedQues.scanMode != IQUES_SCAN_MODE.NORMAL) continue;
              item.correctMode = copiedQues['correctMode'];
            } else {
              item[key] = copiedQues[key];
            }
            item.taskTotalCount = copiedQues['taskTotalCount'];
            item.stuTotalCount = copiedQues['stuTotalCount'];
            item.actualTotalCount = copiedQues['actualTotalCount'];
          }
        }
      });
    },
    /**
     * @name:确认设置分配方式
     * @param {*} data
     */
    confirmSetAssign(data, list) {
      let { assignType, assignRule } = data;
      // 批量设置
      if (this.setType == 'batch') {
        this.handleBatchSetting(assignType);
      } else {
        this.quesList[this.currentQuesIndex].assignType = assignType;
        this.quesList[this.currentQuesIndex].assignRule = assignRule;
        if (assignType == 3) {
          this.quesList[this.currentQuesIndex].teacherList = list;
        }
      }
      this.isShowAssign = false;
    },
    /**
     * @name:批量设置分配方式
     */
    async handleBatchSetting(assignType) {
      //还原数据
      this.quesList.forEach(item => {
        //智批题不支持设置分配方式
        if (item.scanMode == IQUES_SCAN_MODE.NORMAL || this.isAIDoubleEval(item)) {
          this.$set(item, 'assignType', assignType);
        }
      }); //按任教班级分配
      if (assignType == 4) {
        await this.setBatchTeacherList();
      }
    },
    /**
     * @name:批量设置分配教师
     */
    async setBatchTeacherList() {
      this.currSubTeaList = [];
      let params = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        userId: this.$sessionSave.get('loginInfo').id,
        workId: this.workId,
        subjectId: this.subjectId,
        other: 2, //是否是选择其他老师 0：否 1：是 默认1 ,2：只获取班级授课教师
        paperNo: this.paperNo,
        examId: this.examId,
      };
      let result = await findTeacherAssignmentList(params);
      if (result.code == 1) {
        this.assignedTeaList = result.data; //获取学校下该学年该学科老师列表
        await this.getSchoolTeaList();
      }
      this.quesList.forEach(item => {
        if (item.scanMode == IQUES_SCAN_MODE.NORMAL || this.isAIDoubleEval(item)) item.teacherList = [];
      });
      this.currSubTeaList.forEach(item => {
        item.queCount = 0;
      });
      for (let i = 0; i < this.currSubTeaList.length; i++) {
        let currentTeacher = this.currSubTeaList[i];
        let dataItem = {
          isAllocated: 1,
          queCount: 0,
          teaId: currentTeacher.teaId,
          teaName: currentTeacher.teaName,
        };
        for (let j = 0; j < this.quesList.length; j++) {
          if (this.quesList[j].scanMode == IQUES_SCAN_MODE.NORMAL || this.isAIDoubleEval(this.quesList[j])) {
            this.currSubTeaList[i].queCount = this.currSubTeaList[i].queCount + 1;
            dataItem.queCount = dataItem.queCount + 1;
            this.quesList[j].teacherList.push(dataItem);
          }
        }
      }
      this.quesList = [...this.quesList];
    },

    /**
     * @name:关闭分配方式弹窗
     */
    closeSetAssign() {
      this.isShowAssign = false;
    },
    /**
     * @name:渲染文本超出
     */
    initElement() {
      function ellipsisMiddleWithEnds(element, maxWidth, ellipsis) {
        var text = element.innerText || element.textContent;
        var span = document.createElement('span');
        span.style.visibility = 'hidden';
        span.style.whiteSpace = 'nowrap';
        span.style.font = element.style.font;
        span.textContent = text;
        element.parentNode.appendChild(span);

        var textWidth = span.offsetWidth;
        var ellipsisWidth = ellipsis.length * parseInt(getComputedStyle(span).fontSize, 10);

        // 检查是否需要省略
        if (textWidth > maxWidth) {
          // 计算可以展示的首尾字符数
          var charWidth = textWidth / text.length;
          var charsToShow = Math.floor((maxWidth - ellipsisWidth) / charWidth);
          var leftChars = Math.ceil(charsToShow / 2);
          var rightChars = charsToShow - leftChars;

          // 截取首尾文本，并添加省略号
          var leftText = text.substring(0, leftChars);
          var rightText = text.substring(text.length - rightChars);
          element.textContent = leftText + ellipsis + rightText;
        } else {
          // 如果不需要省略，则直接显示完整文本
          element.textContent = text;
        }

        // 清理创建的 span 元素
        element.parentNode.removeChild(span);
      }
      var element = document.getElementsByClassName('text-container');
      var maxWidth = 160;
      var ellipsis = '......';
      Array.from(element)?.forEach(item => {
        ellipsisMiddleWithEnds(item, maxWidth, ellipsis);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.allocate-container {
  height: 100%;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background: #f0eff2;

  .allocate-content {
    display: flex;
    // margin-top: 10px;
    height: 100%;

    .left-ques-list-area {
      width: 80%;
      margin-right: 10px;
      background: #fff;
      border-radius: 5px;
      padding: 20px 0;
      height: 750px;
      position: relative;

      .left-ques-list-area-header {
        width: 100%;
        height: 50px;
        padding-left: 25px;
        border-bottom: 1px solid #f6f6f6;
        text-align: left;
        display: flex;
        align-items: center;

        .paper-title {
          font-size: 18px;
          font-weight: bold;
          margin-right: 20px;
          color: #161e26;
          max-width: 65%;
        }

        .exam-category {
          margin-right: 15px;
          height: 24px;
          line-height: 24px;
          background: #f0f2f5;
          border-radius: 12px;
          color: #606266;
          text-align: center;
          font-weight: 400;
          font-size: 14px;
          padding: 0 11px;
        }

        .ques-num-summary {
          margin-left: 5px;
          font-size: 14px;
          color: #666;
        }
      }

      .left-ques-list-area-body {
        margin: 29px 50px 0 50px;
        // display: grid;
        height: calc(70%);

        // overflow-y: auto;
        .table-header {
          height: 40px;
          line-height: 40px;
          background: #f8f8f9;
          border: 1px solid #f3f3f3;
          border-radius: 5px 5px 0 0;
        }

        .content-height {
          // height: 643px;
          // margin-top: 1px;
          overflow-y: auto;
          height: calc(100% - 40px);
          width: 100%;

          .text-container {
            white-space: normal;
            word-wrap: break-word;
            word-break: break-all;
          }
        }

        .content-height-done {
          // height: 697px;
        }

        .table-content {
          min-height: 40px;
          line-height: 40px;
          border: 1px solid rgba(243, 243, 243, 1);
          border-top: 0;
          display: flex;
          align-items: center;

          .tea-col {
            position: relative;
            min-height: 40px;
            line-height: 40px;

            .sele-allocate-tea-list {
              width: 100%;
              min-height: 80px;
              display: flex;
              justify-content: center;
              align-items: center;
              flex-wrap: wrap;

              .sele-allocate-tea-item {
                position: relative;
                display: inline-block;
                width: 45px;
                height: 45px;
                // width: 60px;
                // height: 33px;
                line-height: 70px;
                vertical-align: middle;
                background: rgba(3, 138, 228, 0.1);
                color: #038ae4;
                text-align: center;
                border-radius: 50%;
                // border-radius: 5px;
                font-size: 14px;
                border: 1px dashed #fff;
                cursor: pointer;
                margin-top: 5px;

                .add-tea-text {
                  position: absolute;
                  left: 50%;
                  top: 50%;
                  transform: translate(-50%, -50%);
                  width: 49px;
                  // width: 100%;
                  font-size: 14px;
                  color: #ccc;
                  line-height: 18px;
                  text-align: center;
                }

                .add-tea-text-alsw {
                  color: #038ae4 !important;
                }
              }

              .add-tea-alsw {
                background: #fff;
                border: 2px dashed #f3f3f3;
                cursor: default;

                .add-tea-text {
                  width: 49px !important;
                  // width: 60px !important;
                }
              }

              .sele-tea-name {
                width: 60px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
              }

              .delete-tea {
                position: absolute;
                right: 0;
                top: -5px;
                // top: -8px;
                font-size: 20px;
                color: #bdbdbd;
              }

              .sele-allocate-tea-item:hover .delete-tea {
                color: #038ae4;
              }
            }

            .add-tea-area {
              width: 100%;
              height: 80px;

              .add-tea {
                display: flex;
                justify-content: center;
                align-items: center;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                width: 50px;
                height: 50px;
                // width: 60px;
                // height: 33px;
                border-radius: 50%;
                // border-radius: 5px;
                border: 2px dashed #f3f3f3;

                .add-tea-text {
                  // width: 49px;
                  font-size: 14px;
                  color: #ccc;
                  line-height: 18px;
                  text-align: center;
                }
              }
            }
          }

          .assign-button {
            cursor: pointer;
          }

          .assignDisabled {
            color: #ccc;
            cursor: auto;
          }
        }
      }

      .left-ques-list-area-footer {
        width: 100%;
        height: 54px;
        line-height: 54px;
        border-top: 1px solid #f3f3f3;

        .footer-btn-area {
          float: right;
          display: flex;
          align-items: center;
          height: 100%;

          .cancel-btn {
            width: 150px;
            margin-right: 40px;
            border-color: #008dea;
            color: #008dea;
            background: #fff;
          }

          .save-btn {
            width: 116px;
            margin-right: 40px;
          }
        }
      }
    }

    .right-tea-list-area {
      width: 20%;
      background: #fff;
      border-radius: 5px;
      padding: 20px 0;
      height: 750px;

      .right-tea-list-header {
        display: flex;
        height: 50px;
        align-items: center;
        //line-height: vw(50);
        padding-left: 20px;
        border-bottom: 1px solid #f6f6f6;

        .marking-tea {
          font-size: 14px;
          font-weight: bold;
          color: #333;
        }

        .split-line {
          width: 1px;
          height: 14px;
          margin: 0 13px;
          background: #f3f3f3;
        }

        .use-explain {
          font-size: 14px;
          color: #666;
        }
      }

      .right-tea-list-body-done {
        height: 747px;
      }

      .right-tea-list-body {
        // height: 589px;
        height: calc(71%);
        margin: 20px auto 0 40px;
        overflow-y: auto;

        .tea-item {
          display: flex;
          align-items: center;
          height: 60px;
          margin-bottom: 16px;
          padding-top: 8px;
          cursor: pointer;

          .tea-name {
            position: relative;
            width: 60px;
            height: 60px;
            // width: 70px;
            // height: 35px;
            line-height: 70px;
            background: rgba(3, 138, 228, 0.1);
            color: #038ae4;
            text-align: center;
            border-radius: 50%;
            font-size: 14px;
            border: 1px dashed #fff;
          }

          .add-tea-text {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 49px;
            font-size: 14px;
            color: #ccc;
            line-height: 18px;
            text-align: center;
          }

          .add-tea-text-alsw {
            color: #038ae4 !important;
          }

          .tea-allocate-num {
            margin-left: 28px;
            font-size: 14px;
            color: #666;
          }

          .num {
            color: #038ae4;
            font-weight: bold;
          }
        }
      }

      .right-tea-list-footer {
        width: 100%;
        height: 54px;
        line-height: 54px;
        border-top: 1px solid #f3f3f3;
        text-align: left;

        .add-another-tea {
          margin-left: 20px;
          font-size: 14px;
          color: #038ae4;
          cursor: pointer;
        }
      }
    }

    .marking-progress-area {
      width: 100%;
      background: #fff;
      position: relative;

      .marking-progress-area-header {
        display: flex;
        width: 100%;
        height: 50px;
        padding-left: 25px;
        line-height: 50px;
        border-bottom: 1px solid #f6f6f6;

        .paper-title {
          font-size: 14px;
          font-weight: bold;
          color: #333;
        }

        .data-area {
          display: flex;
          align-items: center;
          margin-left: 45px;

          .data-item {
            padding: 0 38px;
            margin-right: 13px;
            height: 34px;
            line-height: 34px;
            border-radius: 5px;
          }

          .sub-num {
            background: #eefffd;
          }

          .send-num {
            background: #ffefef;
          }

          .total-num {
            background: #f8f8f9;
          }

          .total-number {
            font-size: 18px;
          }

          .sub-number {
            font-size: 18px;
            color: #00caaf;
          }

          .send-number {
            font-size: 18px;
            color: #ff4e4e;
          }

          .data-progress {
            width: 200px;
            margin-left: 20px;
          }
        }
      }

      .marking-progress-area-body {
        margin: 22px 50px 22px 50px;

        .col-li {
          text-align: center;
          border-right: 1px solid #f3f3f3;
          border-bottom: 1px solid #f3f3f3;
          height: 100%;
          vertical-align: middle;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .summary-col:first-child {
          border-left: 1px solid #f3f3f3;
        }

        .table-header {
          height: 40px;
          line-height: 40px;
          background: #f8f8f9;
          border-radius: 5px 5px 0 0;
        }

        .content-height {
          height: 612px;
          margin-top: 1px;
          overflow-y: auto;
        }
      }
    }
  }

  .no-data {
    height: 80px;
    line-height: 80px;
    text-align: center;
    border: 1px solid #f3f3f3;
    border-top: 0;
  }

  .end-marking-button {
    position: absolute;
    right: 100px;
    top: 13px;
    padding: 6px 10px !important;
  }
}

.set-assign-container {
  margin: 20px 0px;
  line-height: 24px;
  background: #f0f2f5;
  border-radius: 10px;
  color: #606266;
  // text-align: center;
  font-weight: 400;
  font-size: 14px;
  padding: 10px 11px;
}

.help {
  background: url(../../../assets/exam/help.png) no-repeat;
  width: 20px;
  height: 20px;
  position: absolute;
  cursor: pointer;
}

.text-area {
  position: relative;
}

.ai-word {
  color: #409eff;
  margin-left: 10px;
  font-weight: bold;
  position: absolute;
  // right: 15px;
  bottom: 5px;
}

.showOverTooltip {
  display: -webkit-box;
  position: relative;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.square {
  width: 0;
  height: 0;
  border: 12px solid transparent;
  border-top: 13px solid #409eff;
  border-left: 13px solid #409eff;
  z-index: 100;
  border-radius: 5px 0 0 0;
  position: absolute;
  left: 0;
  top: 0;

  .square-word {
    position: absolute;
    left: -13px;
    top: -24px;
    color: #ffffff;
    font-size: 13px;
  }
}

.batch-button {
  position: absolute;
  right: 60px;
}

.pause-button,
.continue-button {
  position: absolute;
  right: 130px;
}
</style>
<style lang="scss">
.table-header {
  .el-col-3 {
    text-align: center;
  }

  .el-col-4 {
    text-align: center;
  }
}

.table-content {
  .el-col-6 {
    text-align: center;
  }

  .el-col-3 {
    text-align: center;
  }

  .el-col-4 {
    text-align: center;
  }
}
</style>

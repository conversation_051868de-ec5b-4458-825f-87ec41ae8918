<template>
  <div>
    <div v-loading="tableLoading">
      <base-table
        v-if="tableData.length"
        :data="tableData"
        :column="tableColumns"
        v-bind="getTableAttr()"
        v-drag-table
      ></base-table>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>

    <!-- 客观题得分率对比图 -->
    <div style="margin-top: 20px; width: 100%" id="ObjectiveChart"></div>
    <!-- 主观题得分率对比图 -->
    <div style="margin-top: 20px; width: 100%" id="SubjectiveChart"></div>
  </div>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';
import {
  getDefaultGrid,
  getDefaultLegend,
  getDefaultPercentAxis,
  getDefaultTitle,
  getDefaultToolBox,
} from '../plugins/DefaultEchartsOption';

@Component({
  components: {},
})
export default class Index extends Mixins(TableCommon) {
  tableLeftFixed: any[] = ['className'];

  // 客观题得分率对比图表
  objectiveChart: EChartsType = null;
  // 主观题得分率对比图表
  subjectiveChart: EChartsType = null;

  mounted() {}

  callbackGetTableData() {
    this.renderQuesChart('ObjectiveChart');
    this.renderQuesChart('SubjectiveChart');
  }

  renderQuesChart(chartId: 'ObjectiveChart' | 'SubjectiveChart' = 'ObjectiveChart') {
    let chartProp = chartId == 'ObjectiveChart' ? 'objectiveChart' : 'subjectiveChart';
    if (this[chartProp]) {
      this[chartProp].dispose();
      this[chartProp] = null;
    }

    let quesColumns =
      chartId == 'ObjectiveChart'
        ? this.tableColumns.find(item => item.title == '客观题').children
        : this.tableColumns.find(item => item.title == '主观题').children;
    let text = chartId == 'ObjectiveChart' ? '客观题得分率对比图' : '主观题得分率对比图';
    if (!quesColumns || quesColumns.length == 0) return;

    let series = [];
    this.tableData.forEach(item => {
      let data = [];
      quesColumns.forEach(col => {
        const prop = col.prop;
        const no = prop.split('tQuesNo')[1];
        data.push(parseFloat(item['clsScoreRate' + no]) * 100);
      });

      series.push({
        name: item.className,
        data: data,
        type: 'line',
      });
    });

    const dom = document.getElementById(chartId);
    this[chartProp] = this.$echarts.init(dom, null, {
      height: 450,
    });

    let option: EChartsOption = {
      toolbox: getDefaultToolBox(),
      title: {
        ...getDefaultTitle(),
        text: text,
      },
      tooltip: {
        trigger: 'axis',
        valueFormatter: value => {
          return value + '%';
        },
      },
      legend: getDefaultLegend(),
      grid: getDefaultGrid(),
      xAxis: {
        type: 'category',
        data: quesColumns.map(item => item.title),
      },
      yAxis: {
        ...getDefaultPercentAxis(),
      },
      series: series,
    };
    this[chartProp].setOption(option);
  }
}
</script>

<style scoped lang="scss"></style>

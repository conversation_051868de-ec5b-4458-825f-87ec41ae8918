<template>
  <div class="noselect">
    <br>
    <div class="set-container">
      <div class="set-header">
        得分率设置
      </div>
      <div class="set-body level-setting">
        <!-- <div class="setting-label"><b>得分率区间：</b></div> -->
        <ul class="set-ul list-none">
          <li v-for="(item, index) in levelValueList" :key="index" class="dis-table ver-mid">
            <div class="dis-tab-row">
              <div class="dis-tab-cell" style="width:110px;padding:10px 0;">
                <span class="text text-right">{{ item[0] }}</span>
                <span class="color-block ver-mid" :class="`color-block_${index}`"></span>
              </div>
              <div class="dis-tab-cell">
                <el-slider :class="`color-${index}`" :ref="`slider_${index}`" :key="index"
                  @dragging="handleSliderDragging($event, index)" v-model="item[1]" :step="1" range :show-tooltip="false">
                </el-slider>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <br>
    <br>
    <div class="set-container">
      <el-checkbox v-model="isAnonymousName">不显示姓名</el-checkbox>
      <el-checkbox v-model="isHideStudentScore">不显示得分</el-checkbox>
      <!--       
      <div class="set-header" style="margin-bottom: 15px;">
        姓名显示
      </div>
      <div class="set-body">
        <span class="set-label">匿名</span>
        <el-switch v-model="isAnonymousName" active-color="#13ce66" inactive-color="#ff4949"
          :active-text="isAnonymousName ? '开' : '关' ">
        </el-switch>
      </div> -->
    </div>
    <br>

    <div slot="footer" class="el-dialog__footer clearfix">
      <el-button class="pull-left" icon="el-icon-refresh" style="padding:12px;" @click="resetSetting">重置</el-button>
      <el-button @click="$emit('close')">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
  
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { State, Getter, Mutation } from 'vuex-class';

const LEVELLIST_DEFAULT: number[] = [0, 60, 70, 90, 100];

@Component
export default class LevelSettingDialog extends Vue {
  @Prop() vModel;
  @State showAnonymousName: boolean;
  @State showStudentScore: boolean;
  @State loginInfo: LoginInfo;

  public $refs!: {
    slider_1: HTMLFormElement;
    slider_2: HTMLFormElement;
    slider_3: HTMLFormElement;
    slider_4: HTMLFormElement;
  };
  levelStrList: string[] = ['不及格', '及格', '中等', '优秀'];
  levelValueList: any[] = [
    ['不及格', [0, 60]],
    ['及格', [60, 70]],
    ['中等', [70, 90]],
    ['优秀', [90, 1]],
  ];
  // 排序规则:quesNo - 题号，scoreRate - 得分率，avgDiffer - 与年级平均分差值
  quesSortRatio: 'quesNo' | 'scoreRate' | 'scoreRateDiffer' = 'quesNo';
  // 显示匿名
  isAnonymousName: boolean = false;
  // 不显示得分
  isHideStudentScore: boolean = false;

  async created() {
    this.isAnonymousName = this.showAnonymousName;
    this.isHideStudentScore = !this.showStudentScore;
    this.quesSortRatio = this.vModel.quesSort;
    this.levelValueList = [];

    let levelList = this.vModel.levelList;
    this.levelStrList.forEach((item: string, index: number) => {
      this.levelValueList.push([item, [levelList[index], levelList[index + 1]]]);
    });
  }

  /**
   * @description: 重置设置
   * @return {*}
   */
  resetSetting() {
    this.levelValueList = [];
    this.levelStrList.forEach((item: string, index: number) => {
      this.levelValueList.push([item, [LEVELLIST_DEFAULT[index], LEVELLIST_DEFAULT[index + 1]]]);
    });

    this.quesSortRatio = 'quesNo';
    this.isAnonymousName = false;
    this.isHideStudentScore = false;
  }

  confirm() {
    let levelList = [0];
    this.levelValueList.forEach(item => {
      levelList.push(item[1][1]);
    });
    this.$store.commit('setStudentAnonymous', this.isAnonymousName);
    this.$store.commit('setStudentShowScore', !this.isHideStudentScore);
    localStorage.setItem("showAnonymousName_" + this.loginInfo.id, this.isAnonymousName ? "1" : "0")
    localStorage.setItem("showStudentScore_" + this.loginInfo.id, !this.isHideStudentScore ? "1" : "0");
    this.$emit('confirm', { levelList, quesSort: this.quesSortRatio });
  }

  handleSliderDragging(range: [number, number], index: number) {
    if (range[0] > range[1]) range = [range[1], range[0]];

    let preIndex = index - 1;
    let lastPrevRange = range;
    while (preIndex >= 0) {
      let prevRange = this.levelValueList[preIndex][1];
      prevRange[1] = lastPrevRange[0];
      // 交换大小
      if (prevRange[0] > prevRange[1]) prevRange[0] = prevRange[1];

      this.$refs['slider_' + preIndex][0].setValues();
      preIndex--;
      lastPrevRange = prevRange;
    }

    let nextIndex = index + 1;
    let lastNextRange = range;
    while (nextIndex <= this.levelValueList.length - 1) {
      let nextRange = this.levelValueList[nextIndex][1];
      nextRange[0] = lastNextRange[1];
      // 交换大小
      if (nextRange[1] <= range[1]) nextRange[1] = range[1];

      this.$refs['slider_' + nextIndex][0].setValues();
      nextIndex++;
      lastNextRange = nextRange;
    }
  }
}
</script>
  
<style lang="scss" scoped>
$color0: #f78989;
$color1: #ffbb19ff;
$color2: #5f9effff;
$color3: #39ceb1ff;

.set-ul {
  width: 500px;
  margin: 0;

  li {
    width: 100%;

    .li-box {
      margin: 15px auto;
    }

    .color-block {
      display: inline-block;
      width: 30px;
      height: 30px;
      border-radius: 4px;
      margin-right: 9px;
    }

    .color-block_0 {
      background: $color0;
      border: 1px solid #f56c6c;
    }

    .color-block_1 {
      background: $color1;
      border: 1px solid #e8a400ff;
    }

    .color-block_2 {
      background: $color2;
      border: 1px solid #468fffff;
    }

    .color-block_3 {
      background: $color3;
      border: 1px solid #07c29dff;
    }

    .text {
      display: inline-block;
      width: 60px;
      height: 30px;
      line-height: 30px;
      padding-right: 10px;
    }

    .el-input {
      width: 60px;
      height: 30px;
      text-align: center;
    }

    .line {
      display: inline-block;
      margin: 0 10px;
      color: #3f4a54ff;
    }

    .el-input__inner {
      width: 60px;
      height: 30px;
      line-height: 30px;
    }
  }
}

.setting-label {
  font-size: 16px;
  margin: 10px 0;
}

.set-header {
  margin-bottom: 10px;
  font-weight: bold;
}

.set-label {
  display: inline-block;
  width: 60px;
  height: 30px;
  line-height: 30px;
  padding-right: 10px;
  text-align: right;
}

.level-setting {
  ::v-deep .el-slider {
    &.color-0 {
      .el-slider__bar {
        background: $color0;
      }

      .el-slider__button {
        border-color: $color0;
      }
    }

    &.color-1 {
      .el-slider__bar {
        background: $color1;
      }

      .el-slider__button {
        border-color: $color1;
      }
    }

    &.color-2 {
      .el-slider__bar {
        background: $color2;
      }

      .el-slider__button {
        border-color: $color2;
      }
    }

    &.color-3 {
      .el-slider__bar {
        background: $color3;
      }

      .el-slider__button {
        border-color: $color3;
      }
    }

    .el-slider__button {
      position: relative;

      &:after {
        content: attr(attr-value)'%';
        position: absolute;
        top: 100%;
        text-align: center;
        width: 40px;
        left: 50%;
        margin-left: -20px;
        margin-top: 3px;
        font-size: 13px;
        color: #909399;
      }
    }
  }
}

.el-dialog__footer {
  padding: 20px 0;
}
</style>
  
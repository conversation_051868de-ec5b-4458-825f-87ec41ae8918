import Vue from 'vue';
import Router from 'vue-router';
import { sessionSave } from '@/utils';

import { getFirstOftenMenuRoute } from '@/components/ExamReport/constant';
import { isSj, isYc } from '@/utils/channel';

import examReportRouterMenu from './routerMenu/examReportRouterMenu'; // 考试报告路由
import scanRouterMenu from './routerMenu/scanRouterMenu'; // 扫描答卷路由
import studyReportRouterMenu from './routerMenu/studyReportRouterMenu'; // 达美嘉分析路由
import targetRouterMenu from './routerMenu/targetRouterMenu';


import { routerIntercept } from './baseRouterIntercept';

Vue.use(Router);
const originalPush = Router.prototype.push;
//修改原型对象中的push方法
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err);
};

export const router = new Router({
  mode: 'history',
  base: process.env.VUE_APP_BASE_URL,
  routes: [
    {
      path: '/dev',
      component: resolve => require(['../pages/commentDetail/index.vue'], resolve),
      meta: {
        keepAlive: true,
      },
    },
    {
      path: '/userRole',
      component: resolve => require(['../components/Role.vue'], resolve),
      meta: {
        keepAlive: true,
      },
    },
    {
      path: '/',
      redirect: to => {
        const loginInfo = sessionSave.get('loginInfo');
        if (!loginInfo) return '/home';
        if (!loginInfo.account_type) return '/home';
        if ([2, 3, 4, 5].includes(loginInfo.account_type)) return '/home/<USER>';
        return '/home';
      },
    },
    {
      path: '/newpapercomment',
      component: resolve => require(['../pages/paperComment.vue'], resolve),
      meta: {
        keepAlive: false,
      },
    },
    {
      path: '/mobilecomment',
      name: 'mobilecomment',
      component: resolve => require(['../pages/mobileComment.vue'], resolve),
      meta: {
        keepAlive: true,
      },
    },
    {
      path: '/mobilplayer',
      name: 'mobilplayer',
      component: resolve => require(['../pages/VideoMobilePlayer.vue'], resolve),
      meta: {
        keepAlive: true,
      },
    },
    scanRouterMenu,
    {
      path: '/importExcel',
      component: resolve => require(['../pages/importExcel.vue'], resolve),
      meta: {
        keepAlive: false,
      },
    },
    {
      path: '/home',
      name: 'home',
      component: resolve => require(['../pages/home'], resolve),
      redirect: '/home/<USER>',
      meta: {
        keepAlive: false,
      },
      children: [
        studyReportRouterMenu,
        {
          path: 'school-setting',
          name: 'schoolSetting',
          redirect: '/home/<USER>/permission-management',
          component: resolve => require(['../pages/schoolSetting/index.vue'], resolve),
          meta: {
            keepAlive: false,
          },
          children: [
            // 权限分配模块
            {
              path: 'permission-management',
              name: 'PermissionManagement',
              component: resolve => require(['../pages/schoolSetting/components/PermissionManagement/index.vue'], resolve),
              meta: { title: '权限分配' }
            },
            {
              path: 'excellent-answer',
              name: 'ExcellentAnswer',
              component: resolve => require(['../pages/schoolSetting/components/ExcellentAnswer/index.vue'], resolve),
              meta: { title: '优秀作答' }
            },
            {
              path: 'student-report',
              name: 'StudentReport',
              component: resolve => require(['../pages/schoolSetting/components/StudentReport/index.vue'], resolve),
              meta: { title: '学生报告' }
            },
            {
              path: 'paper-review',
              name: 'PaperReview',
              component: resolve => require(['../pages/schoolSetting/components/PaperReview/index.vue'], resolve),
              meta: { title: '试卷讲评' }
            },
            
            {
              path: 'rate-setting',
              name: 'RateSetting',
              component: resolve => require(['../pages/schoolSetting/components/RateSetting/index.vue'], resolve),
              meta: { title: '五率设置' }
            },
            {
              path: 'level-setting',
              name: 'LevelSetting',
              component: resolve => require(['../pages/schoolSetting/components/LevelSetting/index.vue'], resolve),
              meta: { title: '等级设置' }
            },
            {
              path: 'student-type-setting',
              name: 'StudentTypeSetting',
              component: resolve => require(['../pages/schoolSetting/components/StudentTypeSetting/index.vue'], resolve),
              meta: { title: '优困生设置' }
            },
            {
              path: 'score-line-setting',
              name: 'ScoreLineSetting',
              component: resolve => require(['../pages/schoolSetting/components/ScoreLineSetting/index.vue'], resolve),
              meta: { title: '上线设置' }
            },
            {
              path: 'score-range-setting',
              name: 'ScoreRangeSetting',
              component: resolve => require(['../pages/schoolSetting/components/ScoreRangeSetting/index.vue'], resolve),
              meta: { title: '分数段设置' }
            },
            {
              path: 'rank-setting',
              name: 'RankSetting',
              component: resolve => require(['../pages/schoolSetting/components/RankSetting/index.vue'], resolve),
              meta: { title: '名次设置' }
            },
            {
              path: 'score-statistic-rule',
              name: 'ScoreStatisticRule',
              component: resolve => require(['../pages/schoolSetting/components/ScoreStatisticRule/index.vue'], resolve),
              meta: { title: '成绩统计规则' }
            },
          ]
        },
        {
          path: 'question',
          component: resolve => require(['../pages/question/home.vue'], resolve),
          redirect: '/home/<USER>/paper',
          meta: {
            keepAlive: false,
          },
          children: [
            {
              path: 'search',
              name: '题库',
              component: resolve => require(['../pages/question/search.vue'], resolve),
            },
            {
              path: 'chapter',
              name: '教材章节',
              component: resolve => require(['../pages/question/chapter.vue'], resolve),
            },
            {
              path: 'paper',
              name: '试卷',
              component: resolve => require(['../pages/question/paper.vue'], resolve),
            },
            {
              path: 'school',
              name: '校本卷库',
              component: resolve => require(['../pages/question/school.vue'], resolve),
            },
            {
              path: 'test',
              name: '校本教辅',
              component: resolve => require(['../pages/question/test.vue'], resolve),
              meta: {
                keepAlive: true,
              },
            },
            {
              path: 'detail',
              name: '教辅题目',
              component: resolve => require(['../pages/question/testDetail.vue'], resolve),
            },
            {
              path: 'wrongques',
              name: '校内错题',
              component: resolve => require(['../pages/question/wrong.vue'], resolve),
            },
            {
              path: 'english',
              name: '英语专项',
              component: resolve => require(['../pages/question/english/index.vue'], resolve),
            },
          ],
        },
        {
          path: 'examReport',
          name: 'examReport',
          component: resolve => require(['../pages/examReportNew.vue'], resolve),
          meta: {
            keepAlive: true,
          },
        },
        // 学情中心（分析）
        {
          path: 'reportCenter',
          name: 'reportCenter',
          redirect: '/home/<USER>/list',
          component: resolve => require(['../pages/reportCenter/index.vue'], resolve),
          // meta: {
          //   keepAlive: true,
          // },
          children: [
            {
              path: 'list',
              name: 'reportCenterList',
              component: resolve => require(['../pages/reportCenter/list.vue'], resolve),
              meta: {
                keepAlive: true,
              },
            },
            {
              path: 'schoolList',
              name: 'reportCenterSchoolList',
              component: resolve => require(['../pages/reportCenter/list.vue'], resolve),
              meta: {
                keepAlive: true,
              },
            },
            {
              path: 'teaching',
              name: 'reportCenterTeaching',
              component: resolve => require(['../pages/teaching/index.vue'], resolve),
              children: [
                {
                  path: 'track',
                  name: 'teaching',
                  component: resolve => require(['../pages/teaching/track.vue'], resolve),
                },
                {
                  path: 'class',
                  name: 'teaching',
                  component: resolve => require(['../pages/teaching/class.vue'], resolve),
                },
                {
                  path: 'classDetail',
                  name: 'teaching',
                  component: resolve => require(['../pages/teaching/classDetail.vue'], resolve),
                },
                {
                  path: 'student',
                  name: 'teaching',
                  component: resolve => require(['../pages/teaching/student.vue'], resolve),
                },
                {
                  path: 'studentDetail',
                  name: 'teaching',
                  component: resolve => require(['../pages/teaching/studentDetail.vue'], resolve),
                },
                {
                  path: 'previewPaper',
                  name: 'teaching',
                  component: resolve => require(['../pages/teaching/previewPaper.vue'], resolve),
                },
                {
                  path: 'score',
                  name: 'teaching',
                  component: resolve =>
                    require(['../pages/teaching/StudentStudyExport.vue'], resolve),
                },
                {
                  path: 'stuGrowth',
                  name: 'stuGrowth',
                  component: resolve =>
                    require(['../pages/teaching/stuGrowth.vue'], resolve),
                }
              ],
            },
          ],
        },
        {
          path: 'xueke',
          name: '学科网',
          component: resolve => require(['../pages/xueke/index.vue'], resolve),
        },
        //创建考试
        {
          path: 'examInfo',
          name: 'examInfo',
          component: resolve => require(['../pages/examMange/create/index.vue'], resolve),
        },
        //考生管理
        {
          path: 'studentManage',
          name: 'studentManage',
          component: resolve => require(['../pages/examMange/student/index.vue'], resolve),
        },
        {
          path: 'examManage',
          name: 'examManage',
          component: resolve => require(['../pages/examMange/examManage/index.vue'], resolve),
        },
        {
          path: 'recordList',
          name: 'recordList',
          component: resolve => require(['../pages/examMange/examManage/recordList.vue'], resolve),
        },
        //成绩修改
        {
          path: 'scoreSetting',
          name: 'scoreSetting',
          component: resolve => require(['../pages/examMange/scoreSetting/index.vue'], resolve),
        },
        // 成绩发布
        {
          path: 'publishSetting',
          name: 'publishSetting',
          component: resolve => require(['../pages/examMange/publishSetting/index.vue'], resolve),
        },
        // 成绩补录
        {
          path: 'scoreEntry',
          name: 'scoreEntry',
          component: resolve => require(['../pages/examMange/scoreEntry/index.vue'], resolve),
        },
        //答案设置
        {
          path: 'answerSetting',
          name: 'answerSetting',
          component: resolve => require(['../pages/examMange/answerSetting/index.vue'], resolve),
        },
        //阅卷设置
        {
          path: 'markPaperSetting',
          name: 'markPaperSetting',
          component: resolve => require(['../pages/examMange/markPaper/settingMain.vue'], resolve),
        },
        //阅卷设置-old
        {
          path: 'markPaperSettingold',
          name: 'markPaperSettingold',
          component: resolve =>
            require(['../pages/examMange/markPaper/settingMainold.vue'], resolve),
        },
        //题块框选
        {
          path: 'settingQuesBlock',
          name: 'settingQuesBlock',
          component: resolve =>
            require(['../pages/examMange/markPaper/settingQuesBlock.vue'], resolve),
        },
        //错题互换
        {
          path: 'settingStuAnsArea',
          name: 'settingStuAnsArea',
          component: resolve =>
            require(['../pages/examMange/markPaper/settingStuAnsArea.vue'], resolve),
          meta: {
            keepAlive: true,
          },
        },
        //错题调整记录
        {
          path: 'stuAnswerRecordList',
          name: 'stuAnswerRecordList',
          component: resolve =>
            require(['../pages/examMange/markPaper/stuAnswerRecordList.vue'], resolve),
        },
        //阅卷进度
        {
          path: 'markPaperSpeed',
          name: 'markPaperSpeed',
          component: resolve => require(['../pages/examMange/markPaper/speedList.vue'], resolve),
        },
        //批改抽查
        {
          path: 'correctSpotCheck',
          name: 'correctSpotCheck',
          component: resolve =>
            require(['../pages/examMange/markPaper/correctSpotCheck.vue'], resolve),
        },
        //未处理异常考试列表
        {
          path: 'errExamList',
          name: 'errExamList',
          component: resolve => require(['../pages/examMange/markPaper/errExamList.vue'], resolve),
          meta: {
            keepAlive: true,
          },
        },
        //缺考名单
        {
          path: 'missExamList',
          name: 'missExamList',
          component: resolve => require(['../pages/examMange/markPaper/missExamList.vue'], resolve),
        },
        {
          path: 'previewTestQues',
          component: resolve => require(['../pages/previewTestQues.vue'], resolve),
        },
        {
          path: 'matchquesnum',
          name: '题号匹配',
          component: resolve => require(['../pages/matchQuesNum.vue'], resolve),
        },
        {
          path: 'personalAudit',
          component: resolve => require(['../pages/personalAudit.vue'], resolve),
        },
        targetRouterMenu,
        //成绩确认详情
        {
          path: 'scoreconfirom',
          component: resolve => require(['../pages/scoreConfiromDetail.vue'], resolve),
          meta: {
            keepAlive: false,
          },
        },

        {
          path: 'paperExport',
          name: 'paperExport',
          component: resolve => require(['../pages/lookReport/paperExport/index.vue'], resolve),
        },
        {
          path: 'reportDetail',
          component: resolve => require(['../pages/examReportDetail.vue'], resolve),
          meta: {
            keepAlive: false,
          },
          children: examReportRouterMenu,
        },
        {
          path: 'customExamReport',
          component: resolve => require(['../pages/customExamReport.vue'], resolve),
          redirect: '/home/<USER>/customReport',
          children: [
            {
              path: 'customReport',
              name: '报告设置',
              component: resolve => require(['../pages/customReport.vue'], resolve),
            },
            {
              path: 'customTargetResult',
              name: '考情指标设置',
              component: resolve => require(['../pages/target/result/new.vue'], resolve),
            },
            {
              path: 'customtargetScore',
              name: '成绩指标设置',
              component: resolve => require(['../pages/target/score/new.vue'], resolve),
            },
            {
              path: 'customtargetFraction',
              name: '分数换算设置',
              component: resolve => require(['../pages/target/fraction/index.vue'], resolve),
            },
          ],
        },
        {
          path: 'teaching',
          name: 'teaching',
          redirect: '/home/<USER>/teaching',
          component: resolve => require(['../pages/teaching/index.vue'], resolve),
          children: [
            {
              path: 'track',
              name: 'teaching',
              redirect: '/home/<USER>/teaching/track',

              component: resolve => require(['../pages/teaching/track.vue'], resolve),
            },
            {
              path: 'class',
              name: 'teaching',
              redirect: '/home/<USER>/teaching/class',

              component: resolve => require(['../pages/teaching/class.vue'], resolve),
            },
            {
              path: 'classDetail',
              name: 'teaching',
              redirect: '/home/<USER>/teaching/classDetail',

              component: resolve => require(['../pages/teaching/classDetail.vue'], resolve),
            },
            {
              path: 'student',
              name: 'teaching',
              redirect: '/home/<USER>/teaching/student',

              component: resolve => require(['../pages/teaching/student.vue'], resolve),
            },
            {
              path: 'studentDetail',
              name: 'teaching',
              redirect: '/home/<USER>/teaching/studentDetail',

              component: resolve => require(['../pages/teaching/studentDetail.vue'], resolve),
            },
            {
              path: 'previewPaper',
              name: 'teaching',
              redirect: '/home/<USER>/teaching/previewPaper',

              component: resolve => require(['../pages/teaching/previewPaper.vue'], resolve),
            },
            {
              path: 'score',
              name: 'teaching',
              redirect: '/home/<USER>/teaching/score',
            },
            {
              path: 'stuGrowth',
              name: 'stuGrowth',
              redirect: '/home/<USER>/teaching/stuGrowth',
            }
          ],
        },
        //三方卡
        {
          path: 'thirdcard',
          component: resolve => require(['../pages/thirdCard/index.vue'], resolve),
        },
        {
          path: 'externalQues',
          component: resolve => require(['../pages/externalQues/externalQues.vue'], resolve),
        },
        {
          path: 'number',
          component: resolve => require(['../pages/number/index.vue'], resolve),
        },

        {
          path: 'supervise',
          name: 'supervise',
          redirect: '/home/<USER>/overview',
          component: resolve => require(['../pages/supervise/index.vue'], resolve),
          children: [
            {
              path: 'overview',
              name: 'superviseOverview',
              component: resolve => require(['../pages/supervise/overview.vue'], resolve),
            },
            {
              path: 'scanTotal',
              name: 'scanTotal',
              component: resolve => require(['../pages/supervise/scanTotal.vue'], resolve),
            },
          ],
        },
        // 完成制卡列表
        {
          path: 'overcard',
          name: 'overcard',
          component: resolve => require(['../pages/cardList/index.vue'], resolve),
        },
        {
          path: '/help',
          name: 'help',
          component: resolve => require(['../pages/help/index.vue'], resolve),
          children: [
            {
              path: 'list',
              name: 'helplist',
              component: resolve => require(['../pages/help/list.vue'], resolve),
            },
            {
              path: 'detail',
              name: 'helpdetail',
              component: resolve => require(['../pages/help/detail.vue'], resolve),
            },
            {
              path: 'editor',
              name: 'editor',
              component: resolve => require(['../pages/help/editor.vue'], resolve),
            },
          ],
        },
        // 教辅讲评中间页
        {
          path: 'book',
          name: 'book',
          component: resolve => require(['../pages/book/index.vue'], resolve),
        },
        // 作文分析
        {
          path: 'compositionAnalyse',
          name: 'compositionAnalyse',
          component: resolve => require(['../pages/compositionAnalyse.vue'], resolve),
        },
        // 学生原卷
        {
          path: 'stuPaper',
          name: 'stuPaper',
          component: resolve => require(['../pages/stuPaper/index.vue'], resolve),
        },
      ],
    },
    {
      path: '/previewDetail',
      component: resolve => require(['../pages/teaching/previewDetail.vue'], resolve),
    },
    {
      path: '/previewSchoolDetail',
      component: resolve => require(['../pages/teaching/previewSchoolDetail.vue'], resolve),
    },
    {
      path: '/previewPaper',
      component: resolve => require(['../pages/teaching/previewPaper.vue'], resolve),
    },
    {
      path: '/downloadPreview',
      component: resolve => require(['../pages/downloadPreview.vue'], resolve),
    },
    {
      path: '/comment',
      name: 'comment',
      component: resolve => require(['../pages/comment/index.vue'], resolve),
      meta: {
        title: '讲评',
        keepAlive: true,
      },
    },
    {
      path: '/commentDetail',
      name: 'commentDetail',
      redirect: '/dReport/commentDetail',
      component: resolve => require(['../pages/comment/detail.vue'], resolve),
      meta: {
        // keepAlive: true,
      },
    },
    {
      path: '/dReport',
      name: 'dReport',
      redirect: '/dReport/commentDetail',
      component: resolve => require(['../pages/dReport/index.vue'], resolve),
      children: [
        {
          path: 'commentDetail',
          name: 'dReportCommentDetail',
          component: resolve => require(['../pages/comment/detail.vue'], resolve),
        },
        {
          path: 'reportDetail',
          component: resolve => require(['../pages/examReportDetail.vue'], resolve),
          meta: {
            keepAlive: false,
          },
          children: examReportRouterMenu,
        },
      ],
    },
    {
      path: '/paperCommentDetail',
      name: '试卷讲评详情',
      component: resolve => require(['../pages/lookReport/paperCommentDetail.vue'], resolve),
    },
    {
      path: '/login',
      component: resolve => {
        if (isYc()) return require(['../pages/login/yclogin.vue'], resolve);
        else if (isSj()) return require(['../pages/login/sjlogin.vue'], resolve);
        else return require(['../pages/login/login.vue'], resolve);
      },
      meta: {
        title: '登录',
      },
    },
    {
      path: '/quesManage',
      component: resolve => require(['../pages/quesManage.vue'], resolve),
      meta: {
        title: '题库中心',
      },
    },
    {
      path: '*',
      redirect: '/login',
    },
    {
      path: '/scan_task',
      name: 'scanTask',
      component: resolve => require(['../pages/scan/task'], resolve),
    },
    {
      path: '/scan/images',
      component: resolve => require(['../pages/scan/images'], resolve),
    },
    {
      path: '/scan_errornew',
      component: resolve => require(['../pages/scan/errornew'], resolve),
    },
    {
      path: '/scan_not',
      component: resolve => require(['../pages/scan/notScannedList'], resolve),
    },
    {
      path: '/scan_batch',
      name: 'scanBatch',
      component: resolve => require(['../pages/scan/batch'], resolve),
    },
    // {
    //   path: '/scan_deal',
    //   name: 'scanDeal',
    //   component: resolve => require(['../pages/scan/deal'], resolve),
    // },
    // 智能批改阅卷路由
    {
      path: '/correct',
      component: resolve => require(['../pages/correct/index.vue'], resolve),
    },
    // 线下作业
    {
      path: '/offlineHomeWork',
      name: 'offlineHomeWork',
      component: resolve => require(['../pages/desktop/offlineHomeWork.vue'], resolve),
    },
    // 线下考试
    {
      path: '/offlineExam',
      name: 'offlineExam',
      component: resolve => require(['../pages/desktop/offlineExam.vue'], resolve),
    },
  ],
});
export default router;

// 路由拦截
routerIntercept();

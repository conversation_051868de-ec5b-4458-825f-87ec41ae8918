<template>
  <div class="school-setting">
    <div class="left-panel">
      <div class="left-panel-sticky">
        <el-menu
          :router="true"
          :default-active="$route.path"
          class="menu-container"
          background-color="#f5f7fa"
          text-color="#303133"
          active-text-color="#409EFF"
          :default-openeds="['1', '2']"
          @select="handleMenuSelect"
        >
          <el-submenu index="1">
            <template slot="title">
              <i class="el-icon-setting"></i>
              <span>基础设置</span>
            </template>
            <el-menu-item index="/home/<USER>/permission-management">权限分配</el-menu-item>
            <el-menu-item index="/home/<USER>/excellent-answer">优秀作答</el-menu-item>
            <el-menu-item index="/home/<USER>/student-report">学生报告</el-menu-item>
            <el-menu-item index="/home/<USER>/paper-review">试卷讲评</el-menu-item>
            <!-- <el-menu-item index="/home/<USER>/report-setting">报告设置</el-menu-item> -->
          </el-submenu>

          <div class="section-switch">
            <div class="select-item">
              <span class="label">切换学段：</span>
              <el-select v-model="currentPhase" placeholder="请选择学段" @change="handleSectionChange">
                <el-option v-for="item in currentPhaseList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
            <div class="select-item">
              <span class="label">切换年级：</span>
              <el-select v-model="currentGradeId" placeholder="请选择年级" @change="handleGradeChange">
                <el-option v-for="item in currentGradeList" :key="item.id" :label="item.gradeName" :value="item.id" />
              </el-select>
            </div>
          </div>

          <el-submenu index="2">
            <template slot="title">
              <i class="el-icon-data-line"></i>
              <span>指标设置</span>
            </template>
            <el-menu-item index="/home/<USER>/rate-setting">五率设置</el-menu-item>
            <el-menu-item index="/home/<USER>/level-setting">等级设置</el-menu-item>
            <el-menu-item index="/home/<USER>/student-type-setting">优困生设置</el-menu-item>
            <!-- <el-menu-item index="/home/<USER>/score-line-setting">上线设置</el-menu-item> -->
            <el-menu-item index="/home/<USER>/score-range-setting">分数段设置</el-menu-item>
            <el-menu-item index="/home/<USER>/rank-setting">名次设置</el-menu-item>
            <el-menu-item index="/home/<USER>/score-statistic-rule">成绩统计规则</el-menu-item>
          </el-submenu>
        </el-menu>
      </div>
    </div>
    <div class="right-panel">
      <template v-if="isInit">
        <p class="tip">
          <span class="star">*</span
          >提示：自定义参数修改保存后，新发布的测评分析将使用修改后的参数生成，已发布的测评若需使用新参数请重新发布成绩。
        </p>
        <transition name="slide-fade" mode="out-in">
          <router-view
            :currentPhase="currentPhase"
            :currentGradeId="currentGradeId"
            :currentGradeItem="currentGradeItem"
            :currentGradeList="currentGradeList"
          ></router-view>
        </transition>
      </template>
      <el-skeleton v-else :rows="10" animated />
    </div>
  </div>
</template>

<script lang="ts">
import { getSchoolGrade } from '@/service/api';
import { Component, Vue } from 'vue-property-decorator';
import { GradeInfo, SchoolSettingEvent, SettingChangeParams } from './types';

// 获取通用年级
function getCommonGrade() {
  return {
    id: 0,
    phase: '',
    gradeName: '通用',
    gradeId: '',
    center_code: '',
    systemCode: '',
    year: '',
    gradeCode: '',
    grade_code: '',
    grade_name: '',
  };
}

@Component
export default class SchoolSetting extends Vue {
  private gradeList: GradeInfo[] = [];
  private phaseList = [
    { label: '小学', value: 1 },
    { label: '初中', value: 2 },
    { label: '高中', value: 3 },
  ];
  // 获取当前学段的年级列表
  get currentGradeList() {
    return this.gradeList.filter(item => item.phase === this.currentPhase || !item.phase);
  }
  // 获取当前学段列表
  get currentPhaseList() {
    let phaseList = this.gradeList.map(item => item.phase);
    return this.phaseList.filter(item => phaseList.includes(item.value));
  }
  // 当前学段
  currentPhase: number | string = null;
  // 当前年级
  currentGradeId: number | string = null;
  // 当前年级对象
  currentGradeItem: GradeInfo = null;

  // 是否初始化
  isInit = false;

  created() {
    this.getGradeList();
  }

  // 获取年级列表
  async getGradeList() {
    const res = await getSchoolGrade({
      schoolId: this.$sessionSave.get('schoolInfo').id,
    });
    this.gradeList = res.data;
    this.gradeList.unshift(getCommonGrade());
    this.handleSectionChange(this.currentPhaseList[0].value);
    this.isInit = true;
  }

  // 学段切换
  handleSectionChange(value: number | string) {
    this.currentPhase = value;
    this.handleGradeChange(this.currentGradeList[0].id);
  }

  // 年级切换
  handleGradeChange(value: number | string) {
    this.currentGradeId = value;
    this.currentGradeItem = this.gradeList.find(item => item.id === value);

    const params: SettingChangeParams = {
      phase: this.currentPhase,
      grade: this.currentGradeId,
    };

    this.$nextTick(() => {
      this.$bus.$emit(SchoolSettingEvent.SettingChange, params);
    });
  }

  handleMenuSelect() {}
}
</script>

<style scoped lang="scss">
.school-setting {
  display: flex;
  min-height: 100vh;
  height: auto !important;
  background-color: #fff;

  .left-panel {
    width: 240px;
    background-color: #f5f7fa;
    position: relative;
    border-right: 1px solid #e6e6e6;

    .left-panel-sticky {
      position: sticky;
      top: 0;
      max-height: calc(100vh - 60px);
      overflow-y: auto;
      padding-bottom: 10px;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background-color: #f5f7fa;
      }
    }

    .section-switch {
      padding: 20px;
      border-top: 1px solid #e6e6e6;

      .select-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-size: 14px;
          color: #606266;
        }

        .el-select {
          flex: 1;
        }
      }
    }

    .menu-container {
      border-right: none;

      :deep(.el-submenu__title) {
        font-size: 14px;

        i {
          margin-right: 8px;
        }
      }

      :deep(.el-menu-item) {
        font-size: 13px;
        padding-left: 48px !important;

        &:hover,
        &.is-active {
          background-color: #ecf5ff;
        }
      }
    }
  }

  .right-panel {
    flex: 1;
    padding: 24px;
    min-height: 100vh;

    color: #606266;
    font-size: 14px;
    line-height: 1.5;

    background-color: #fff;

    overflow-x: auto;
  }

  .tip {
    color: #909399;
    margin-bottom: 24px;
    color: #f56c6c;
    font-size: 15px;
    font-weight: 700;

    .star {
      color: #f56c6c;
    }
  }
}
</style>

<template>
  <div>
    <div v-show="!showDefault" id="rankSectionChart"
         :style="{width: '100%', height: '400px'}"></div>

    <div v-show="showDefault">
      <div style="text-align: center"><img style="width: 350px;" :src="noResImg" alt=""></div>
      <p style="text-align: center;font-size: 16px;margin-top: 10px;">暂无数据!</p>
    </div>
  </div>
</template>

<script>
export default {
  name: "RankSectionChart",
  props: ['tableData','targetData'],

  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      rankSectionChart: null,
      showDefault: false,
      colors: [
        '#07C29D', '#FF6A68', '#FFB400', '#9668FF', '#BD3DDD', '#BAFF68',
        '#FFE168', '#FFAF68', '#FF6A68', '#3E73F6', '#F368FF', '#68FFFA',
        '#3EF0CD', '#418AFD', '#B4F0FA', '#07C29D',
        '#FF8ACF', '#68BAFF'
      ],
      seriesData: [],
      selected: {},
      //分段名次
      rankName:[]
    }
  },
  watch: {
    tableData: {
      handler(val) {
        if(!val || !val.length) {
          this.showDefault = true;
          return;
        }
        if(val.length) {
          this.showDefault = false;
        }
        // this.resetDomSize('rankSectionChart', 400);
        this.$nextTick(()=>{
          this.drawImg();
        })
      },
      deep: true,
      immediate: true,
    },
  },
  beforeDestroy() {
    if(this.rankSectionChart != null && this.rankSectionChart != "" && this.rankSectionChart != undefined) {
      this.rankSectionChart.dispose();
      this.rankSectionChart = null;
    }
  },
  mounted(){
    let _this = this;
    window.onresize = ()  =>{
      return (() => {
        if(_this.rankSectionChart) {
          // this.resetDomSize('rankSectionChart', 400);
          _this.rankSectionChart.resize();
        }
      })()
    };
    if(!this.rankSectionChart) {
      this.drawImg();
    }

  },
  methods: {
    // resetDomSize(el, height) {
    //   let width = document.getElementById('pane-chart').clientWidth
    //   Object.defineProperty(document.getElementById(el),'clientWidth',
    //       {get:function(){return width;}, configurable: true})
    //   Object.defineProperty(document.getElementById(el),'clientHeight',
    //       {get:function(){return height;}, configurable: true})
    // },
    handleChartData(seriesItemSetting) {
      let data = this.tableData && this.tableData.length ? this.tableData : [] ;
      if(data.length) {
        this.seriesData = []
        data.filter(item => {
          return item.classId !=''
        }).map(it => {
          let arr1 = []
          let {className, score} = it;
          for(let i in score){
            arr1.push(score[i])
          }
          let obj = {
            name: className,
            data: arr1,
            ...seriesItemSetting
          };
          this.seriesData.push(obj)
          this.seriesData.map((it, index) => {
            if(index<10) {
              this.selected[it.name] = true
            } else {
              this.selected[it.name] = false
            }

          })
        })
      } else {
        this.showDefault = true;
      }
    },
    drawImg() {
      if(this.rankSectionChart != null && this.rankSectionChart != "" && this.rankSectionChart != undefined) {
        this.rankSectionChart.dispose();
        this.rankSectionChart = null;
      }
      //分段名次
      this.rankName = []
      this.targetData.rankSegment.forEach(it =>{
        if(it.start == -1){
          this.rankName.push(`${it.end}`)
        }else{
          this.rankName.push(`${it.start}-${it.end}`)
        }
      })

      let seriesItemSetting = {
        type: 'line',
        symbol: 'circle',
        symbolSize: 10,
      }
      this.handleChartData(seriesItemSetting);
      let _this = this;


      this.rankSectionChart = this.$echarts.init(
          document.getElementById('rankSectionChart')
      );
      this.rankSectionChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {            // 坐标轴指示器，坐标轴触发有效
            type: 'line'        // 默认为直线，可选为：'line' | 'shadow'
          },
          position: function (point, params, dom, rect, size) {
            // 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
            // 提示框位置
            let x = 0; // x坐标位置
            let y = 0; // y坐标位置

            // 当前鼠标位置
            let pointX = point[0];
            let pointY = point[1];

            // 外层div大小
            // var viewWidth = size.viewSize[0];
            // var viewHeight = size.viewSize[1];

            // 提示框大小
            let boxWidth = size.contentSize[0];
            let boxHeight = size.contentSize[1];

            // boxWidth > pointX 说明鼠标左边放不下提示框
            if (boxWidth > pointX) {
              x = 5;  // 自己定个x坐标值，以防出屏
              y -= 15; // 防止点被覆盖住，可根据情况自行调节
            } else { // 左边放的下
              x = pointX - boxWidth - 15;
            }

            // boxHeight > pointY 说明鼠标上边放不下提示框
            if (boxHeight + 20 > pointY) {
              y = pointY - 125;
            } else if (boxHeight > pointY) {
              y = 5;
            } else { // 上边放得下
              y += pointY - boxHeight;
            }
            return [x, y];
          }
        },
        color: this.colors,
        legend: {
          icon: "circle",
          type: 'scroll',
          orient: 'horizontal',
          top: 10,
          right: 50,
          textStyle: {
            color: '#757C8C',
            fontSize: '14'
          },
          padding: [10, 0, 0, 30],
          selected: _this.selected
        },
        grid: {
          left: '3%',
          right: '6%',
          top: '18%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          name: '分段',
          data: this.rankName
        },
        yAxis: {
          type: 'value',
          name: '人数'
        },
        series: _this.seriesData
      })
    }
  }
}
</script>

<style scoped>

</style>

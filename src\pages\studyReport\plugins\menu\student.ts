/*
 * @Description:
 * @Author: 小圆
 * @Date: 2024-03-21 09:35:35
 * @LastEditors: 小圆
 */
import { FilterDataKey } from '@/pages/studyReport/plugins/types';
import { menuOption } from '@/pages/studyReport/plugins/types';

export const Student_Option_Map: Map<string, menuOption> = new Map([
  [
    'academiclevelandbalance',
    {
      title: '学业水平与均衡性',
      path: 'academiclevelandbalance',
      name: 'academiclevelandbalance',
      component: () => import('@/pages/studyReport/student/index.vue'),
    },
  ],
  [
    'academicdevelopmenttrends',
    {
      title: '学业发展动态',
      path: 'academicdevelopmenttrends',
      name: 'academicdevelopmenttrends',
      component: () => import('@/pages/studyReport/student/index.vue'),
    },
  ],
  [
    'advancedProblemDetermination',
    {
      title: '小题诊断',
      path: 'advancedProblemDetermination',
      name: 'advancedProblemDetermination',
      component: () => import('@/pages/studyReport/student/index.vue'),
    },
  ],
  [
    'knowledgePointDiagnosis',
    {
      title: '知识点诊断',
      path: 'knowledgePointDiagnosis',
      name: 'knowledgePointDiagnosis',
      component: () => import('@/pages/studyReport/student/index.vue'),
    },
  ],
  [
    'abilityPointDiagnosis',
    {
      title: '能力点诊断',
      path: 'abilityPointDiagnosis',
      name: 'abilityPointDiagnosis',
      component: () => import('@/pages/studyReport/student/index.vue'),
    },
  ],
  [
    'topicDiagnosis',
    {
      title: '题型诊断',
      path: 'topicDiagnosis',
      name: 'topicDiagnosis',
      component: () => import('@/pages/studyReport/student/index.vue'),
    },
  ],
  [
    'academicEvaluationAndSubjectDiagnosis',
    {
      title: '学业评价与学科诊断报告',
      path: 'academicEvaluationAndSubjectDiagnosis',
      name: 'academicEvaluationAndSubjectDiagnosis',
      component: () => import('@/pages/studyReport/student/index.vue'),
    },
  ],
  [
    'studentSubjectRecommendation',
    {
      title: '学生选科推荐',
      path: 'studentSubjectRecommendation',
      name: 'studentSubjectRecommendation',
      component: () => import('@/pages/studyReport/student/index.vue'),
    },
  ],
] as [string, menuOption][]);

export const Student_Menu = Array.from(Student_Option_Map.values());

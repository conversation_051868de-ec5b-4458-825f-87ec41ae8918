<!--
 * @Description: 最高分名册
 * @Author: 小圆
 * @Date: 2024-04-02 09:02:53
 * @LastEditors: 小圆
-->
<template>
  <div>
    <div v-loading="tableLoading">
      <base-table
        v-if="tableData.length"
        :data="tableData"
        :column="tableColumns"
        v-bind="getTableAttr()"
        v-drag-table
      ></base-table>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>

    <!-- 小题难度-区分度折线图 -->
    <div style="margin-top: 20px; width: 100%; height: 400px" id="ClsDistinguishLineChart"></div>
    <!-- 小题难度-区分度二维图 -->
    <div style="margin-top: 20px; width: 100%; height: 400px" id="ClsDistinguishScatterChart"></div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins, Prop } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';
import FilterModule from '../plugins/FilterModule';
import {
  getDefaultGrid,
  getDefaultLegend,
  getDefaultTitle,
  getDefaultToolBox,
  getDefaultTooltipFormatter,
} from '../plugins/DefaultEchartsOption';

@Component({
  components: {},
})
export default class Index extends Mixins(TableCommon) {
  // 小题难度-区分度折线图
  clsDistinguishLineChart: EChartsType = null;
  // 小题难度-区分度散点图
  clsDistinguishScatterChart: EChartsType = null;

  callbackGetTableData() {
    this.renderClsDistinguishLineChart();
    this.renderClsDistinguishScatterChart();
  }

  renderClsDistinguishLineChart() {
    if (this.clsDistinguishLineChart) {
      this.clsDistinguishLineChart.dispose();
      this.clsDistinguishLineChart = null;
    }
    const dom = document.getElementById('ClsDistinguishLineChart');
    this.clsDistinguishLineChart = this.$echarts.init(dom);

    let option: EChartsOption = {
      grid: getDefaultGrid(),
      title: { ...getDefaultTitle(), text: '小题难度-区分度折线图' },
      legend: {
        ...getDefaultLegend(),
        data: ['难度', '区分度'],
      },
      tooltip: {
        trigger: 'axis',
      },
      toolbox: getDefaultToolBox(),
      xAxis: {
        type: 'category',
        data: this.tableData.map(item => item.quesNoDesc),
        axisLabel: {},
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '难度',
          data: this.tableData.map(item => item.clsScoreRate),
          type: 'line',
        },
        {
          name: '区分度',
          data: this.tableData.map(item => item.clsDistinguish),
          type: 'line',
          // markArea: {
          //   data: [
          //     [
          //       {
          //         yAxis: -0.2,
          //         itemStyle: {
          //           color: '#FF6464',
          //         },
          //       },
          //       {
          //         yAxis: 0.2,
          //       },
          //     ],
          //     [
          //       {
          //         yAxis: 0.2,
          //         itemStyle: {
          //           color: '#FFFF00',
          //         },
          //       },
          //       {
          //         yAxis: 0.3,
          //       },
          //     ],
          //   ],
          //   silent: true,
          // },
        },
      ],
    };

    this.clsDistinguishLineChart.setOption(option);
  }

  renderClsDistinguishScatterChart() {
    if (this.clsDistinguishScatterChart) {
      this.clsDistinguishScatterChart.dispose();
      this.clsDistinguishScatterChart = null;
    }
    const dom = document.getElementById('ClsDistinguishScatterChart');
    this.clsDistinguishScatterChart = this.$echarts.init(dom);

    const series = [];

    // 数组累加
    let sum = this.tableData
      .map(item => parseFloat(item.fullScore))
      .reduce((prev, curr) => {
        return prev + curr;
      }, 0);


    this.tableData.forEach(item => {
      series.push({
        data: [
          [
            parseFloat(item.clsScoreRate),
            parseFloat(item.clsDistinguish),
            parseFloat(item.fullScore),
            item.quesType,
          ],
        ],
        name: item.quesNoDesc,
        type: 'scatter',
        symbolSize: function (data) {
          return 30 + ((data[2] / sum) * 100);
          // return (300 / sum) * (data[2] / sum) * 100;
        },
        itemStyle: {
          normal: {
            label: {
              show: true,
              position: 'inside',
              formatter: '{a}',
            },
            value: item.quesNoDesc,
            opacity: 0.5,
          },
        },
        // silent: true,
      });
    });

    let option: EChartsOption = {
      title: { ...getDefaultTitle(), text: '小题难度-区分度二维图' },
      grid: getDefaultGrid(),
      toolbox: getDefaultToolBox(),
      tooltip: {
        // Option config. Can be overwrited by series or data
        trigger: 'item',
        axisPointer: {
          // Use axis to trigger tooltip
          type: 'shadow', // 'shadow' as default; can also be 'line' or 'shadow'
        },
        showDelay: 0,
        hideDelay: 50,
        transitionDuration: 0,
        borderRadius: 8,
        borderWidth: 2,
        padding: 10, // [5, 10, 15, 20]
        position: function (p) {
          // 位置回调
          return [p[0] + 10, p[1] - 10];
        },
        formatter: function (params) {
          var itemTexts = ['题型', '满分', '难度', '区分度'];
          var itemIndexs = [3, 2, 0, 1];
          var html =
            '<div style="font-size:14px;color:#666;font-weight:400;line-height:1;">' +
            params.seriesName +
            '</div>';
          for (var i = 0; i < itemTexts.length; i++) {
            html +=
              '<div style="margin: 10px 0 0;line-height:1;">' +
              '<span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">' +
              params.marker +
              itemTexts[i] +
              '</span>' +
              '<span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">' +
              params.value[itemIndexs[i]] +
              '</span>' +
              '<div style="clear:both"></div>';
            html += '</div>';
          }

          return html;
        },
        //confine: true,
        //extraCssText: 'z-index:1000;'
      },
      color: [
        '#ff0000',
        '#87cefa',
        '#da70d6',
        '#32cd32',
        '#6495ed',
        '#ff69b4',
        '#ba55d3',
        '#cd5c5c',
        '#ffa500',
        '#40e0d0',
        '#1e90ff',
        '#ff6347',
        '#7b68ee',
        '#00fa9a',
        '#ffd700',
        '#6699FF',
        '#ff6666',
        '#3cb371',
        '#b8860b',
        '#30e0e0',
      ],
      xAxis: {
        min: 0,
        max: 1,
        axisLine: {
          onZero: false,
        },
        name: '难度',
        splitNumber: 10,
        axisTick: {
          inside: true,
        },
        splitLine: {
          show: false,
        },
        scale: true,
      },
      yAxis: {
        min: -0.2,
        max: 1,
        name: '区分度',
        axisTick: {
          inside: true,
        },
        splitLine: {
          show: false,
        },
        scale: true,
      },
      series: series,
    };

    this.clsDistinguishScatterChart.setOption(option);
  }
}
</script>

<style scoped lang="scss"></style>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { getPaperChoice } from '@/service/pexam';
import { getStuElectiveQues } from '@/service/api';

export interface IPaperChoice {
  id: string;
  name: string;
  doQuesList?: DoQues[];
}

export interface DoQues {
  qsNos: number[];
  targetIds: string[];
  qsNames: string[];
  ids: string[];
  doCount: number;
}

export interface StuDoQues {
  quesNo:     number;
  chooseType: number;
  targetId:   string;
}

interface StuElectiveQuesParams {
  schoolId: string;
  examId: string;
  subjectId: string;
  stuId: string;
}

@Component
export default class DochoiceMixin extends Vue {
  /** 试卷选做题列表 */
  paperChoice: IPaperChoice[] = [];
  /** 选做题列表 */
  doQuesList: DoQues[] = [];

  /**
   * 获取选做题
   * @param examId 考试ID
   * @param subjectId 科目ID
   */
  getPaperChoice(examId, subjectId) {
    return getPaperChoice({
      examId: examId,
      subjectId: subjectId,
    })
      .then(res => {
        let data = res.data as IPaperChoice[];

        let doQuesList = [];
        for (const item of data) {
          if (item.doQuesList && item.doQuesList.length) {
            doQuesList.push(...item.doQuesList);
          }
        }
        this.paperChoice = data;
        this.doQuesList = doQuesList;
      })
      .catch(e => {
        console.error(e);
        this.paperChoice = [];
        this.doQuesList = [];
      });
  }


  /**
   * 获取学生选做题里列表
   * @param params 
   */
  getStuElectiveQues(params: StuElectiveQuesParams): Promise<StuDoQues[]> {
    return getStuElectiveQues({
      schoolId: params.schoolId,
      examId: params.examId,
      subjectId: params.subjectId,
      stuId: params.stuId,
    })
      .then(res => {
        if(res.code == 1) {
          return res.data || [];
        } 
        return [];
      })
      .catch(e => {
        console.error(e);
        return [];
      });
  }

  /**
   * 获取选做题提示文本列表
   */
  getDoQuesChoiceTipTextList() {
    let arr = [];
    if (this.doQuesList.length) {
      let textArr = [];
      this.paperChoice.forEach(item => {
        if (item.doQuesList && item.doQuesList.length) {
          let textItem = {
            name: item.name,
            list: [],
          };
          let smallTextArr = [];
          item.doQuesList.forEach(item => {
            smallTextArr.push(`${item.qsNames}为选做题(${this.getDoChoiceQuesCountText(item)})`);
          });
          textItem.list = smallTextArr;
          textArr.push(textItem);
        }
      });
      textArr = textArr.map(item => {
        return item.name + '中' + item.list.join('，');
      });
      arr = textArr;
    }
    return arr;
  }

  /**
   * 获取选做题提示Text
   */
  getDoQuesChoiceTipText() {
    let textArr = this.getDoQuesChoiceTipTextList();
    if (textArr.length) {
      let text = '注：' + textArr.join('；');
      return text;
    } else {
      return '';
    }
  }

  /**
   * 获取选做题提示Html
   */
  getDoQuesChoiceTipHtml() {
    let textArr = this.getDoQuesChoiceTipTextList();
    if (textArr.length) {
      let text = textArr.map((item, index) => {
        let t = '';
        t = `${item}`;
        if (index == 0) {
          t = `注：${item}`;
        }
        return `<p>${t}${index !== textArr.length - 1 ? '；' : ''}</p>`;
      });
      return text.join('');
    } else {
      return '';
    }
  }

  /**
   * 根据题目序号获取选做题
   * @param quesNo 题目QuesNo
   */
  getDoChoiceQues(quesNo) {
    let item = this.doQuesList.find(
      item => item.qsNos.includes(quesNo) || item.qsNos.includes(Number(quesNo))
    );
    return item;
  }

  getDoChoiceQuesById(quesId) {
    console.log(quesId);
    let item = this.doQuesList.find(item => item.ids.includes(quesId));
    return item;
  }
  /**
   * 获取选做题数量文本
   * @param item 选做题
   */
  getDoChoiceQuesCountText(item: DoQues) {
    return `${item.ids.length + '选' + item.doCount}`;
  }

  /**
   * 根据题目序号获取选做题数量文本
   * @param quesNo 题目QuesNo
   */
  getDoChoiceQuesCountTextByQuesNo(quesNo) {
    let item = this.getDoChoiceQues(quesNo);
    if (item) {
      return this.getDoChoiceQuesCountText(item);
    } else {
      return '';
    }
  }
}
</script>

<style lang="scss">
.mixin-dochoice-tip {
  display: block;

  width: 600px;
  line-height: 24px;
  font-size: 14px;
  font-weight: 400;

  color: #f59a23;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: break-all;
}

.mixin-dochoice-text {
  font-weight: 400;
  font-size: 14px;
  color: #f59a23;
  word-break: break-all;
}
</style>

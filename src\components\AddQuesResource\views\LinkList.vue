<!--
 * @Description: 链接管理列表，支持新增和预览
 * @Author: qmzhang
 * @Date: 2024-12-12 09:53:22
 * @LastEditTime: 2025-01-10 08:43:43
 * @FilePath: \personal-bigdata\src\components\AddQuesResource\views\LinkList.vue
-->
<template>
    <el-container class="link-list">
        <el-header v-show="mode == 'add' || addedLinkList.length">
            <el-input placeholder="请输入链接地址" @keypress.enter.native="previewEditInput" v-model="editInput" clearable>
                <el-button slot="append" icon="el-icon-view" :disabled="!editInput" @click="previewEditInput"
                    style="border-right:1px solid #DCDFE6;border-radius: 0;">预览</el-button>
                <el-button slot="append" icon="el-icon-plus" :disabled="!editInput" @click="addLink(editInput)"
                    v-if="!disableAdd && mode == 'add'">新增</el-button>
            </el-input>
        </el-header>

        <el-main>
            <el-container>
                <el-aside class="tab-aside" width="130px" v-if="addedLinkList.length">
                    <el-tabs class="el-tabs--links wh100" tab-position="left" type="card" @tab-click="hadleTabClick"
                        @tab-remove="handleTabRemove" v-model="tabActive" :editable="!isDesktop">
                        <el-tab-pane :name="item.resourceId" v-for="(item, index) in addedLinkList" :key="item.resourceId">
                            <span slot="label"><i class="el-icon-link"></i> 链接 - {{ index + 1 }}</span>
                        </el-tab-pane>
                    </el-tabs>
                </el-aside>
                <el-main class="el-main--player">
                    <div v-loading="iframeLoading" element-loading-text="页面加载中...">
                        <iframe ref="iframe" app-drag="none" class="dis-block wh100 my-iframe" width="100%" height="100%"
                            @load="iframeLoad" :src="previewLink" frameborder="0" v-show="previewLink"
                            v-if="!iframeLoadError && showIframe"></iframe>
                        <el-empty class="my-iframe" v-if="iframeLoadError && previewLink"
                            description="页面加载错误，请检查链接或网络..." />
                        <el-empty class="my-iframe" v-else-if="!previewLink"
                            :description="addedLinkList.length ? '点击链接开启预览' : '暂未添加链接资源'" />
                    </div>
                </el-main>
            </el-container>
        </el-main>
    </el-container>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { getPageInfoFromLink, getQueryString } from '@/utils'
import { v1 as uuid } from 'uuid';
import { deepClone, sleep } from '@/utils/index';
import { QuesResource } from './ResAddViewList.vue';
import moment from 'moment';

@Component
export default class ResViewList extends Vue {
    @Prop() vModel;
    // 模式：add|view:添加|预览
    @Prop({ default: 'add' }) mode: string
    // 禁用添加
    @Prop({ default: false }) disableAdd: boolean
    @Prop() originList: Array<any>
    public $refs!: {
        iframe: HTMLIFrameElement;
    };

    // 当前编辑的输入
    editInput: string = ""
    // 预览链接
    previewLink: string = ""
    iframeLoading = false;
    iframeLoadError = false;
    showIframe = true;
    // 新增的链接列表
    addedLinkList: QuesResource[] = []
    // 链接tab
    tabActive: string = ''
    // 是否桌面
    isDesktop = (!!window.cef  && this.$route.name != 'paperComment') || getQueryString('isDesktop') == 1;


    created() {
        this.addedLinkList = deepClone(this.originList);
        this.addedLinkList.forEach(it => this.$emit('res-add', 'link'));

        // let firstRes = this.addedLinkList[0]
        // if (firstRes) {
        //     this.tabActive = firstRes.resourceId;
        //     this.editInput = firstRes.resourceUrl;
        //     this.previewLink = firstRes.resourceUrl;
        //     this.iframeLoading = true;
        // }
    }

    iframeLoad(evt: any) {
        this.iframeLoading = false;
    }

    hadleTabClick(tab: any) {
        let link = this.addedLinkList.find(it => it.resourceId == tab.name);
        this.hadlePreviewLink(link.resourceUrl)
        this.editInput = link.resourceUrl;
    }

    previewEditInput() {
        // this.tabActive = "";
        this.hadlePreviewLink(this.editInput)
    }

    /**
     * @description: 预览链接
     * @param {*} url
     * @return {*}
     */
    async hadlePreviewLink(url: string) {
        if (!url.includes('.')) {
            this.$notify({
                title: '无法预览',
                offset: 100,
                message: "请输入正确格式的链接！",
                type: 'warning'
            });
            return;
        }
        if (!url) return;

        this.iframeLoading = true;
        if (!url.startsWith('http')) {
            url = "http://" + url;
        }
        this.iframeLoading = true;
        this.showIframe = false;

        await this.$nextTick();
        this.previewLink = url;
        this.showIframe = true;
    }

    /**
     * @description: 添加链接
     * @param {*} url
     * @return {*}
     */
    async addLink(url: string) {
        // let pageInfo = await getPageInfoFromLink(url);
        if (this.addedLinkList.some(it => it.resourceUrl == url)) {
            this.$notify({
                title: '无法添加',
                offset: 100,
                message: "请不要添加重复链接！",
                type: 'warning'
            });
            return
        }

        if (!url.includes('.')) {
            this.$notify({
                title: '无法添加',
                offset: 100,
                message: "请输入正确格式的链接！",
                type: 'warning'
            });
            return;
        }


        let link = this.createLinkItem(url);
        this.editInput = "";
        this.tabActive = link.resourceId;
        this.hadlePreviewLink(url)
        this.addedLinkList.push(link);
        this.$emit('res-add', 'link')
    }

    createLinkItem(url: string) {
        const now = moment();
        const formattedDate = now.format('YYYY-MM-DD HH:mm');

        return {
            resourceId: uuid(),
            resourceType: '7',
            resourceUrl: url,
            resourceExt: 'url',
            resourceName: "链接",
            uploadDate: formattedDate
        }
    }

    /**
     * @description: 删除链接
     * @param {*} tabName
     * @return {*}
     */
    handleTabRemove(tabName: string) {
        this.addedLinkList = this.addedLinkList.filter(it => it.resourceId != tabName);
        if (this.addedLinkList.length) {
            this.previewLink = this.addedLinkList[0].resourceUrl;
        } else {
            this.previewLink = "";
        }
        this.$emit('res-remove', 'link')
        this.$emit('turnAddMode')
    }

    confirm() {
        if (!this.addedLinkList.length && this.editInput) {
            return [this.createLinkItem(this.editInput)];
        }
        return this.addedLinkList;
    }
}

</script>

<style lang="scss" scoped>
.el-container,
.el-header,
.el-main {
    padding: 0;
}

.my-iframe {
    height: 400px;
}

.tab-aside {
    border-right: 1px solid #E4E7ED;
}

.el-tabs {
    ::v-deep {
        .el-tabs__header {
            border-bottom: none;
            margin-right: 0;
            width: 100%;
        }
    }
}

.el-tabs--links {
    ::v-deep {
        .el-tabs__new-tab {
            display: none;
        }
    }
}
</style>
<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-03-21 15:49:54
 * @LastEditors: 小圆
-->
<template>
  <el-dialog :visible.sync="visible" title="全书总览" width="1300px" v-bind="$attrs" v-on="$listeners">
    <el-table
      :data="dirOverview"
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :max-height="600"
    >
      <el-table-column label="章节" prop="name" align="left" min-width="200">
        <template #default="{ row }">
          <div class="chapter-name">{{ row.name }}</div>
        </template>
      </el-table-column>
      <el-table-column label="总题数" prop="qsNum" align="center" width="120" />
      <el-table-column label="高频错题" prop="errQsNum" align="center" width="120" />
      <el-table-column v-if="classId" label="班级提交率" prop="clsSubmitRate" align="center" min-width="180">
        <template #default="{ row }">
          <el-progress :percentage="row.clsSubmitRate" class="progress-bar" />
        </template>
      </el-table-column>
      <el-table-column label="年级提交率" prop="grdSubmitRate" align="center" min-width="180">
        <template #default="{ row }">
          <el-progress :percentage="row.grdSubmitRate" class="progress-bar" />
        </template>
      </el-table-column>
      <el-table-column v-if="classId" label="班级得分率" prop="clsRate" align="center" min-width="180">
        <template #default="{ row }">
          <el-progress :percentage="row.clsRate" class="progress-bar" />
        </template>
      </el-table-column>
      <el-table-column label="年级得分率" prop="grdRate" align="center" min-width="180">
        <template #default="{ row }">
          <el-progress :percentage="row.grdRate" class="progress-bar" />
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script lang="ts">
import { getDirOverviewAPI } from '@/service/pexam';
import { Component, Prop, Vue } from 'vue-property-decorator';

interface DirOverview {
  id: string;
  code: string;
  parent: string;
  name: string;
  path: string;
  qsNum: number;
  errQsNum: number;
  clsRate: number;
  grdRate: number;
  clsSubmitRate: number;
  grdSubmitRate: number;
  children: DirOverview[];
}

@Component
export default class BookOverviewDialog extends Vue {
  @Prop({ type: String }) classId: string;
  // 是否显示
  visible = false;

  // 目录总览
  dirOverview: DirOverview[] = [];

  mounted() {
    this.visible = true;
    this.getDirOverview();
  }

  // 获取目录总览
  async getDirOverview() {
    const res = await getDirOverviewAPI({
      bookCode: this.$route.query.bookCode,
      classId: this.classId,
      schoolId: this.$sessionSave.get('schoolInfo').id
    });
    this.dirOverview = res.data;
  }
}
</script>

<style scoped lang="scss">
.progress-bar {
  display: flex;
  flex-direction: column;
  align-items: center;

  ::v-deep(.el-progress-bar) {
    width: 100%;
    padding-right: 0;
    margin-right: 0;
    padding: 0 20px;
  }

  ::v-deep(.el-progress__text) {
    margin-top: 10px;
  }
}

::v-deep {
  .cell:has(.chapter-name) {
    display: flex;
    align-items: center;
  }

  @supports not selector(.cell:has(.chapter-name)) {
    .el-table_1_column_1 {
      .cell {
        display: flex;
        align-items: center;
      }
    }
  }
}

.chapter-name {
  display: flex;
}
</style>

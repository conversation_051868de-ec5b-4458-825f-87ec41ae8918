<!--
 * @Description: 我的题库
 * @Author: qmzhang
 * @Date: 2025-05-12 10:45:42
 * @LastEditTime: 2025-05-12 16:08:10
 * @FilePath: \personal-bigdata\src\components\paper\myQuestionBank\index.vue
-->
<template>
    <div class="my-question-bank">
        <!-- 我的卷库列表 -->
        <MyPaperList v-on="$listeners" @select="onSelectPaper" v-show="!showPaperDetail" />

        <!-- 试卷详情 -->
        <el-page-header @back="backList" v-if="showPaperDetail">
            <template #content>
                {{ paperInfo.tbName }}
                <i v-if="paperInfo.needCheckPassword" style="color:#ff0000;" class="el-icon-lock"></i>
            </template>
        </el-page-header>
        <MyPaperDetail v-on="$listeners" :paperId="paperInfo.id" :selectIds="selectIds" :maxVariantLength="maxVariantLength"
            v-if="showPaperDetail" />
    </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import MyPaperList from './myPaperList.vue'
import MyPaperDetail from './myPaperDetail.vue'

@Component({
    components: {
        MyPaperList,
        MyPaperDetail
    }
})
export default class MyQuestionBank extends Vue {
    @Prop({ default: () => [] }) selectIds: string[];
    @Prop() maxVariantLength: number;

    showPaperDetail: boolean = false;
    paperInfo = null;

    created() {

    }

    backList() {
        this.paperInfo = null;
        this.showPaperDetail = false
    }

    /**
     * @description: 当选择了试卷
     * @param {*} data
     * @return {*}
     */
    onSelectPaper(data: any) {
        this.paperInfo = data;
        this.showPaperDetail = true;
    }
}
</script>

<style lang="scss" scoped>
.el-page-header {
    position: sticky;
    top: 0px;
    margin-bottom: 15px;

    ::v-deep .el-page-header__content {
        font-size: 16px;
    }
}
</style>
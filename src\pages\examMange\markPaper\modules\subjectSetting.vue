<!--
 * @Description: 简答题设置页面
 * @Author: liuyue <EMAIL>
 * @Date: 2025-07-23 13:59:24
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-09-01 16:45:44
-->

<template>
  <div class="subject-setting-container">
    <div class="step-container">

      <div class="answer-setting">
        <el-form ref="answerForm" :model="currentQuestion" :rules="rules" label-width="100px">
          <el-form-item label="选择题号" prop="quesNo">
            <template v-for="item in quesDataWithTips">
              <el-button v-if="!item.hasTip" class="btn-item ellipsis-btn" :key="item.id"
                @click="onQuestionChange(item.quesNo)"
                :type="item.quesNo === currentQuestion.quesNo ? 'primary' : 'default'">{{ item.quesNos
                }}</el-button>
              <el-badge v-else is-dot class="btn-item" :key="'dot_' + item.id"><el-button class="ellipsis-btn"
                  :key="item.id" @click="onQuestionChange(item.quesNo)"
                  :type="item.quesNo === currentQuestion.quesNo ? 'primary' : 'default'">{{ item.quesNos
                  }}</el-button></el-badge>
            </template>
          </el-form-item>
          <el-form-item label="分值">
            <div class="score-display">{{ currentQuestion.score }}分</div>
          </el-form-item>
          <el-form-item v-if="!isMathSubject" label="答案类型" prop="answerType">
            <el-radio-group v-model="currentQuestion.answerType" @change="currentQuestion.answer = ''"
              :disabled="isReadonly">
              <el-radio label="txt">文本</el-radio>
              <el-radio label="img">图片</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 文本答案 -->
          <el-form-item v-if="currentQuestion.answerType === 'txt'" label="参考答案" prop="answer">
            <el-input v-if="!isMathSubject" type="textarea" v-model="currentQuestion.answer" :rows="4"
              placeholder="请输入文本答案" :disabled="isReadonly">
            </el-input>
            <div v-else class="answer-content">
              <div v-html="getAnsHtml(currentQuestion.answer)" ref="ansHtmlRef" style="display: inline-block;"></div>
            </div>
            <template v-if="isMathSubject">
              <el-button type="success" @click="setAnsHtml2Img" style="margin-left: 20px;"
                icon="el-icon-check">使用此答案</el-button>
              <el-button type="warning" @click="setAnswerType('img')" style="margin-left: 20px;"
                icon="el-icon-close">放弃</el-button>
            </template>
          </el-form-item>

          <!-- 图片答案 -->
          <el-form-item v-if="currentQuestion.answerType === 'img'" label="参考答案" prop="answer">
            <template v-if="!currentQuestion.answer">
              <el-upload class="answer-uploader" action="#" :show-file-list="false" :on-success="handleImageSuccess"
                :before-upload="beforeImageUpload" :http-request="handleHttpUpload" :disabled="isReadonly">
                <i class="el-icon-plus answer-uploader-icon"></i>
              </el-upload>
              <div class="upload-tip">支持jpg、png格式，大小不超过5MB</div>
            </template>
            <div v-else class="answer-image-container">
              <el-image :src="currentQuestion.answer" :preview-src-list="[currentQuestion.answer]">
              </el-image>
              <div class="delete-btn" @click.stop="currentQuestion.answer = ''">
                <i class="el-icon-delete"></i>
              </div>
            </div>

          </el-form-item>

          <el-form-item label="评分细则" prop="scoringCriteria">
            <el-input type="textarea" v-model="currentQuestion.scoringCriteria" :rows="6" placeholder="可在此补充输入评分细则，非必填"
              :disabled="isReadonly">
            </el-input>
          </el-form-item>
          <el-form-item v-if="correctType == ICORRECT_TYPES.WEB" label="得分说明" prop="aiCorrectMode">
            <el-radio-group v-model="currentQuestion.aiCorrectMode">
              <div class="radio-item">
                <el-radio :disabled="isReadonly" :label="1">
                   AI 批改：AI批改，老师复核，可直接使用AI评分</el-radio>
              </div>
              <div class="radio-item">
                <el-radio :disabled="isReadonly" :label="2">
                  人机双评：AI初评，老师二评，以老师打分为准，可设置误差分，达到误差分则由仲裁老师进行评阅打分</el-radio>
                <div style="margin-left: 92px;font-size:12px;" v-if="currentQuestion.aiCorrectMode == 2">
                  <span>开启仲裁：<el-switch v-model="currentQuestion.isArbitration" :disabled="isReadonly" /></span>
                  <template v-if="currentQuestion.isArbitration">
                    <span style="margin-left: 20px;">分差大于<el-input v-model="currentQuestion.beyondScore" :disabled="isReadonly"
                        style="width: 48px; height: 32px"></el-input>分进入仲裁</span>
                    <span style="margin-left: 20px;"><el-checkbox v-model="currentQuestion.isCheckScore" size="medium" :disabled="isReadonly"
                        border>仲裁时可查看AI和阅卷老师的评分</el-checkbox></span>
                  </template>
                </div>
              </div>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <el-button :disabled="isReadonly" style="float:right;" type="primary" @click="submitForm">保存设置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import ossUploadFile from '@/utils/ossUploadFile';
import { generateUUID } from '@/utils/index.js';
import { getQueryString } from '@/utils';
import { convertHtml } from '@/utils/common';
import { isSupportMathSubject } from '@/utils';
import { ICORRECT_TYPES } from '@/typings/card';
import html2canvas from 'html2canvas';
export default {
  name: 'SubjectSetting',
  props: {
    aiSubjectQuesData: {
      type: Array,
      default: () => []
    },
    isReadonly: {
      type: Boolean,
      default: false
    },
    correctType: {
      type: Number,
      default: ICORRECT_TYPES.WEB
    }
  },
  data() {
    return {
      ICORRECT_TYPES,
      quesData: [],
      currentQuestion: {},
      rules: {
        answer: [
          { required: true, message: '请输入答案', trigger: 'blur' },
        ],
        aiCorrectMode: [
          { required: true, message: '请选择得分说明', trigger: 'change' }
        ]
      },
      path: 'aliba/exam/answer',
      subjectId: Number(getQueryString('subjectId'))
    };
  },
  computed: {
    isMathSubject() {
      return isSupportMathSubject(this.subjectId);
    },
    quesDataWithTips() {
      return this.quesData.map(item => {
        let hasTip = false;
        if (this.isMathSubject) {
          hasTip = item.answerType != 'img' || !item.answer || (!item.aiCorrectMode && this.correctType == ICORRECT_TYPES.WEB);
        } else {
          hasTip = !item.answer || (this.correctType == ICORRECT_TYPES.WEB && !item.aiCorrectMode);
        }
        return {
          ...item, hasTip: hasTip
        }
      })
    }
  },
  created() {
    this.initData();
    if(this.correctType == ICORRECT_TYPES.HAND){
      this.rules.aiCorrectMode = [];
    }
  },
  methods: {
    initData() {
      this.quesData = JSON.parse(JSON.stringify(this.aiSubjectQuesData));
      if (this.quesData && this.quesData.length > 0) {
        //如果不包含公式学科 则只保留文本内容
        this.isMathSubject && this.quesData.forEach(item => {
          const tempElement = document.createElement('div');
          tempElement.innerHTML = item.answer;
          item.answer = tempElement.innerText;
        });
        this.onQuestionChange(this.quesData[0].quesNo);
      }
    },
    // 题目变更
    onQuestionChange(quesNo) {
      this.currentQuestion = this.quesData.find(item => item.quesNo === quesNo) || {};
      this.$set(this.currentQuestion, 'score', this.currentQuestion.score || 0);
      this.$set(this.currentQuestion, 'answer', this.currentQuestion.answer || '');
      this.$set(this.currentQuestion, 'aiCorrectMode', this.currentQuestion.aiCorrectMode);
      this.$set(this.currentQuestion, 'isArbitration', this.currentQuestion.isArbitration || false);
      this.$set(this.currentQuestion, 'beyondScore', this.currentQuestion.beyondScore || 2);
      this.$set(this.currentQuestion, 'isCheckScore', this.currentQuestion.isCheckScore || false);
      if (this.isMathSubject && !this.currentQuestion.answer) {
        this.$set(this.currentQuestion, 'answerType', 'img');
      } else {
        this.$set(this.currentQuestion, 'answerType', this.currentQuestion.answerType || 'txt');
      }
      this.$set(this.currentQuestion, 'scoringCriteria', this.currentQuestion.scoringCriteria || '');
    },
    setAnswerType(type) {
      this.$set(this.currentQuestion, 'answerType', type);
      this.$set(this.currentQuestion, 'answer', '');
    },
    getAnsHtml(ans) {
      return convertHtml(ans);
    },
    async setAnsHtml2Img() {
      let loadingInstance = this.$loading({
        lock: true,
        text: '答案解析中...',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      let canvas = await html2canvas(this.$refs.ansHtmlRef, {
        scale: 1
      });
      const base64 = canvas.toDataURL('image/jpeg');
      const file = ossUploadFile.blobToFile(ossUploadFile.dataURLtoBlob(base64), 'answer.jpg');
      this.handleHttpUpload({
        file: file,
        onSuccess: (res) => {
          this.$set(this.currentQuestion, 'answer', res.data.url);
          this.$set(this.currentQuestion, 'answerType', 'img');
          loadingInstance.close();
        },
        onError: (err) => {
          this.$message.error('图片上传失败，请重试');
          console.error(err);
          loadingInstance.close();
        }
      });
    },
    // 检查答案是否设置完成
    checkAnswerComplete() {
      const invalidItem = this.quesData.find(item => {
        const hasAnswer = !!item.answer;
        const isImgAnswer = item.answerType === 'img';
        const needAiCorrect = this.correctType === ICORRECT_TYPES.WEB;

        return !(
          hasAnswer &&
          (!this.isMathSubject || isImgAnswer) &&
          (!needAiCorrect || item.aiCorrectMode)
        );
      });

      return invalidItem;
    },
    saveSettings() {
      const errItem = this.checkAnswerComplete();
      if (errItem) {
        this.$message.error(`第${errItem.quesNo}题有必填项未设置，请确定！`);
        return;
      }
      this.aiSubjectQuesData.forEach(item => {
        const ques = this.quesData.find(q => q.quesNo === item.quesNo);
        if (ques) {
          if (ques.answer != item.answer) {
            item.updatedAt = new Date().getTime()
          }
          item.score = ques.score;
          item.answerType = ques.answerType;
          item.answer = ques.answer;
          item.scoringCriteria = ques.scoringCriteria;
          item.aiCorrectMode = ques.aiCorrectMode;
          item.isArbitration = ques.isArbitration;
          item.isCheckScore = ques.isCheckScore;
          item.beyondScore = ques.beyondScore;
        }
      });
      this.$emit('saveAICorrectQue');
    },
    async submitForm() {
      this.$refs.answerForm.validate(async (valid) => {
        if (valid) {
          this.saveSettings();
        } else {
          this.$message.error('请完成必填项');
          return false;
        }
      });
    },

    // 上传图片前的校验
    beforeImageUpload(file) {
      const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isJPGOrPNG) {
        this.$message.error('上传图片只能是JPG或PNG格式!');
        return false;
      }
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过5MB!');
        return false;
      }
      return true;
    },

    // 自定义上传方法
    async handleHttpUpload(options) {
      try {
        // 获取文件
        const file = options.file;
        // 获取上传路径
        const filePath = this.getDateForPath() + this.currentQuestion.id + '/' + generateUUID() + '.' + this.getFileExtension(file.name);

        // 确保OSS客户端已初始化
        await ossUploadFile.getSTSToken(this.path);

        // 上传文件
        ossUploadFile.uploadFile(file, filePath, (res) => {
          if (res.code === 1) {
            // 上传成功
            const imageUrl = `https://fs.iclass30.com/${res.res.name}`;
            this.currentQuestion.answer = imageUrl;

            // 调用成功回调
            if (options.onSuccess) {
              options.onSuccess({ code: 0, data: { url: imageUrl } });
            }
          } else {
            // 上传失败
            this.$message.error('图片上传失败，请重试');
            if (options.onError) {
              options.onError(new Error('上传失败'));
            }
          }
        });
      } catch (error) {
        console.error('上传出错:', error);
        this.$message.error('图片上传出错，请重试');
        if (options.onError) {
          options.onError(error);
        }
      }
    },

    // 图片上传成功回调
    handleImageSuccess(res) {
      if (res.code === 0) {
        this.currentQuestion.answer = res.data.url;
      } else {
        this.$message.error('图片上传失败');
      }
    },

    // 获取文件扩展名
    getFileExtension(filename) {
      return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
    },

    // 获取日期路径
    getDateForPath() {
      const date = new Date();
      const y = date.getFullYear();
      const m = date.getMonth() + 1;
      const month = m < 10 ? '0' + m : m;
      const d = date.getDate();
      const day = d < 10 ? '0' + d : d;
      return `${this.path}/${y}/${month}/${day}/`;
    }
  }
};
</script>

<style lang="scss" scoped>
.subject-setting-container {
  width: 100%;
  padding: 20px;
  background-color: #fff;

  .step-container {
    margin-bottom: 30px;

    .step-title {
      font-size: 16px;
      font-weight: bold;
      margin: 20px 0;
      color: #303133;
      background-color: #f2f6fc;
      padding: 10px 15px;
      border-left: 4px solid #409EFF;
      border-radius: 4px;
    }

    .question-selection {
      margin-bottom: 20px;

      .el-select {
        width: 100%;
      }
    }

    .answer-setting {
      .el-form-item {
        margin-bottom: 25px;
      }

      .btn-item {
        margin-right: 8px;
        margin-bottom: 8px;
        vertical-align: top;
      }

      .ellipsis-btn {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .score-display {
        font-size: 16px;
        color: #67c23a;
        font-weight: bold;
      }

      .answer-content {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        padding: 10px;
        margin: 10px 0;
      }

      .answer-uploader {
        width: 178px;
        height: 178px;
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

        &:hover {
          border-color: #409EFF;
        }

        .answer-uploader-icon {
          font-size: 28px;
          color: #8c939d;
          width: 178px;
          height: 178px;
          line-height: 178px;
          text-align: center;
        }

        .answer-image {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .upload-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 5px;
      }

      .el-textarea {
        ::v-deep .el-textarea__inner {
          border-color: #dcdfe6;
          transition: all 0.3s;

          &:focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }
        }
      }

      .answer-image-container {
        width: max-content;
        position: relative;

        .delete-btn {
          position: absolute;
          top: 5px;
          right: -25px;
          width: 24px;
          height: 24px;
          background: #f56c6c;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          z-index: 10;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          transition: all 0.2s;
          opacity: 0.8;

          &:hover {
            transform: scale(1.1);
            background: #f78989;
            opacity: 1;
          }
        }

        .el-image {
          position: relative;
          display: inline-block;
          max-width: 600px;
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
          padding: 10px;

          &:hover {
            .delete-btn {
              opacity: 1;
            }
          }
        }
      }
    .radio-item {
        display: flex;
        flex-direction: column;
        padding: 12px 16px;
        transition: all 0.3s;

        &:hover {
          border-color: #C0C4CC;
          background-color: #F5F7FA;
        }

        .radio-desc {
          margin-top: 8px;
          margin-left: 24px;
          font-size: 13px;
          color: #606266;
          line-height: 1.5;
        }
      }

      /* 当单选框被选中时，高亮显示对应项 */
      .radio-item:has(.el-radio.is-checked) {
        border-color: #409EFF;
        background-color: #ecf5ff;
      }

    }
  }
}
</style>

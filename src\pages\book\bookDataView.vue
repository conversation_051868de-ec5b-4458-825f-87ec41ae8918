<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-03-14 10:24:21
 * @LastEditors: 小圆
-->
<template>
  <div>
    <div class="select-container" v-if="classList.length">
      <div class="select__label">班级：</div>
      <div class="select__content">
        <span
          class="select__item"
          :class="{ active: classId == item.id }"
          v-for="item in classList"
          :key="item.id"
          @click="handleClassChange(item.id)"
          >{{ item.class_name }}</span
        >
      </div>
    </div>
    <div class="data-cards-container">
      <div class="data-card data-card--submit">
        <div class="data-card__rate clearfix">
          <span class="rate-title pull-left">提交率</span>
          <span class="rate-value pull-right">{{ dirRate.submitRate }}%</span>
        </div>
        <el-progress :percentage="dirRate.submitRate" :show-text="false" :stroke-width="8"></el-progress>

        <div class="data-card__info clearfix">
          <span class="info-item pull-left">已交：{{ dirRate.submitNum }}人</span>
          <span class="info-item pull-right">未交：{{ dirRate.noSubmitNum }}人</span>
        </div>

        <div class="data-card__btn">
          <el-button type="text" class="text-btn" @click="isShowMissDialog = true">未交名单 ></el-button>
        </div>
      </div>

      <div class="data-card data-card--score">
        <div class="data-card__rate clearfix">
          <span class="rate-title pull-left">得分率</span>
          <span class="rate-value pull-right">{{ dirRate.scoreRate }}%</span>
        </div>
        <el-progress :percentage="dirRate.scoreRate" :show-text="false" :stroke-width="8"></el-progress>

        <div class="data-card__info clearfix">
          <span class="info-item pull-left">平均分：{{ dirRate.avgScore }}/{{ dirRate.fullScore }}</span>
          <span class="info-item pull-right">及格率：{{ dirRate.passRate }}%</span>
        </div>

        <div class="data-card__btn">
          <el-button type="text" class="text-btn" @click="goReport">查看报告 ></el-button>
        </div>
      </div>

      <div class="data-card data-card--action">
        <div class="action-title">操作</div>
        <div class="action-grid">
          <el-button class="action-btn" @click="goReport('paperComment')">去讲评</el-button>
          <el-button class="action-btn" @click="goReport('cardHome')">学生详情</el-button>
          <el-button class="action-btn" @click="goReport('questionAnalysis')">班级对比</el-button>
          <el-button class="action-btn" @click="goReport('knowledgeAnalyze')">知识点分析</el-button>
        </div>
      </div>
    </div>

    <div class="chart-container" v-show="!classId">
      <div class="chart-title">提交率</div>
      <div ref="submitRateChart" class="chart"></div>
    </div>

    <div class="chart-container">
      <div class="chart-title">得分率</div>
      <div ref="scoreRateChart" class="chart"></div>
    </div>

    <miss-stu-dialog
      v-if="isShowMissDialog"
      :title="'未交名单'"
      :countLabel="'未交人数'"
      :examId="examId"
      :subjectId="subjectInfo?.id"
      :clzId="classId"
      @closed="isShowMissDialog = false"
    ></miss-stu-dialog>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import * as echarts from 'echarts';
import MissStuDialog from '../lookReport/classCompare/reportCard/missStuDialog.vue';
import { getDirRateAPI, getExamInfoAPI } from '@/service/pexam';
import { DirRate } from './types';
import { getExamReportClassList, getExamReportSubjectList } from '@/utils/examReportUtils';
import { findIntersection, getToRoles } from '@/utils/index';
import UserRole from '@/utils/UserRole';

@Component({
  components: {
    MissStuDialog,
  },
})
export default class BookDataView extends Vue {
  @Prop({ default: '' }) catalogCode: string;
  @Prop({ default: '' }) examId: string;
  // 目录得分率
  dirRate: DirRate = {
    examId: 0,
    workId: null,
    isPublish: true,
    submitNum: 0,
    submitRate: 0,
    noSubmitNum: 0,
    scoreRate: 0,
    avgScore: 0,
    fullScore: 0,
    passRate: 0,
    clzList: [],
    qsList: [],
  };
  /** 考试信息 */
  examInfo = null;
  /** 学科信息 */
  subjectInfo = null;
  /** 班级列表 */
  classList = [];
  /** 当前选中的班级 */
  classId = '';

  /** 提交率图表 */
  private submitRateChart: echarts.ECharts = null;
  /** 得分率图表 */
  private scoreRateChart: echarts.ECharts = null;
  // 是否显示缺考名单
  private isShowMissDialog = false;

  // 得分率等级列表
  levelList = [
    {
      start: 0,
      end: 60,
      color: '#f78989',
    },
    {
      start: 60,
      end: 70,
      color: '#ffbb19',
    },
    {
      start: 70,
      end: 90,
      color: '#5f9eff',
    },
    {
      start: 90,
      end: 100,
      color: '#39ceb1',
    },
  ];

  mounted() {
    this.initLevelList();
    this.getExamInfo();
  }

  beforeDestroy() {
    if (this.scoreRateChart) {
      this.scoreRateChart.dispose();
      this.scoreRateChart = null;
    }
    if (this.submitRateChart) {
      this.submitRateChart.dispose();
      this.submitRateChart = null;
    }
  }

  /**
   * @description: 初始化得分率等级
   * @return {*}
   */
  initLevelList() {
    let loginInfo = this.$sessionSave.get('loginInfo');
    let keypath = `levelList_${loginInfo.id}`;
    try {
      let data = this.$localSave.get(keypath);
      if (data) {
        data = this.levelList.map((item, index) => {
          return {
            start: data[index],
            end: data[index + 1] || 100,
            color: item.color,
          };
        });
        this.levelList = data;
        console.log(this.levelList);
      }
    } catch (error) {
      localStorage.removeItem(keypath);
    }
  }

  // 获取目录得分率
  async getDirRate() {
    const res = await getDirRateAPI({
      bookCode: this.$route.query.bookCode,
      catalogCode: this.catalogCode,
      classId: this.classId,
      schoolId: this.$sessionSave.get('schoolInfo').id,
    });
    this.dirRate = res.data;
    this.$nextTick(() => {
      this.initSubmitRateChart();
      this.initScoreRateChart();
    });
  }

  // 获取考试信息
  async getExamInfo() {
    const res = await getExamInfoAPI({
      examId: this.examId,
    });

    this.examInfo = res.data;
    this.examInfo.examId = this.examId;
    this.examInfo.examName = this.examInfo.name;
    this.examInfo.gradeCode = this.examInfo.gradeId;
    this.$sessionSave.set('reportDetail', this.examInfo);
    this.$sessionSave.set('reportParent', this.examInfo);
    await this.getExamSubjectItem();
    await this.getExamClassList();

    this.classId = this.classList[0].id;
    this.getDirRate();
    this.$emit('changeClassId', this.classId);
  }

  // 获取考试学科
  async getExamSubjectItem() {
    // 获取当前入学年份角色权限
    const { examId, year, gradeName, v } = this.examInfo;
    let roles = getToRoles(year);

    // 获取模块角色
    const { roleSubjectList, noRoleSubjectList } = await getExamReportSubjectList({
      examId: this.examId as any,
      roles,
      v,
      year,
      gradeName,
      statType: 1,
    });
    this.subjectInfo = roleSubjectList[0] || null;
  }

  // 获取考试班级列表
  async getExamClassList() {
    let roles = getToRoles(this.examInfo.year);
    if (!UserRole.isOperation) roles = (this.subjectInfo?.intersectionRoles || []).map(t => Number(t));

    const { roleClassList, noRoleClassList } = await getExamReportClassList({
      examId: this.examId,
      roles: roles,
      subjectId: this.subjectInfo?.id,
      year: this.examInfo.year,
    });
    let classList = roleClassList;
    let canAllRole =
      UserRole.isOperation || roles.includes(1) || roles.includes(2) || roles.includes(3) || roles.includes(4);
    if (canAllRole) {
      classList.unshift({
        id: '',
        class_name: '全部',
        intersectionRoles: findIntersection(this.subjectInfo?.intersectionRoles || [], [1, 2, 3, 4]),
      });
    }
    this.classList = classList;
  }

  // 初始化提交率图表
  initSubmitRateChart() {
    const chartDom = this.$refs.submitRateChart as HTMLElement;
    if (!chartDom) return;

    if (!this.submitRateChart) {
      this.submitRateChart = echarts.init(chartDom);
    }

    this.submitRateChart.setOption({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: params => {
          const arr = [];
          const className = this.dirRate.clzList[params[0].dataIndex].name;
          const submitNum = this.dirRate.clzList[params[0].dataIndex].submitNum;
          const noSubmitNum = this.dirRate.clzList[params[0].dataIndex].noSubmitNum;
          const bStyle = 'float: right;margin-left: 10px;';
          arr.push(`<div>${className}</div>`);
          arr.push(`<div>总人数：<b style="${bStyle}">${submitNum + noSubmitNum}</b></div>`);
          arr.push(`<div>已提交：<b style="${bStyle}">${submitNum}</b></div>`);
          arr.push(`<div>未提交：<b style="${bStyle}">${noSubmitNum}</b></div>`);
          return arr.join('');
        },
      },
      legend: {
        data: ['提交率', '年级平均提交率'],
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: this.dirRate.clzList.map(item => item.name),
        axisLabel: {
          interval: 0,
          rotate: 30,
        },
      },
      yAxis: {
        type: 'value',
        name: '提交率(%)',
        nameLocation: 'middle',
        nameGap: 40,
        nameRotate: 90,
        min: 0,
        max: 100,
        interval: 20,
      },
      series: [
        {
          name: '提交率',
          type: 'bar',
          data: this.dirRate.clzList.map(item => item.submitRate),
          itemStyle: {
            color: (params: any) => {
              // 如果当前柱状图的值小于对应的年级平均值，返回红色
              return this.dirRate.clzList[params.dataIndex].submitRate < this.dirRate.submitRate
                ? 'rgba(239, 68, 68, 0.7)' // 红色带透明度
                : 'rgba(59, 130, 246, 0.7)'; // 蓝色带透明度
            },
          },
          barWidth: '30%',
        },
        {
          name: '年级平均提交率',
          type: 'line',
          data: this.dirRate.clzList.map(item => this.dirRate.submitRate),
          itemStyle: {
            color: '#10b981',
          },
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 3,
          },
        },
      ],
    });
  }

  // 初始化得分率图表
  initScoreRateChart() {
    const chartDom = this.$refs.scoreRateChart as HTMLElement;
    if (!chartDom) return;

    if (!this.scoreRateChart) {
      this.scoreRateChart = echarts.init(chartDom);
    }

    this.scoreRateChart.setOption(
      {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        legend: {
          data: ['得分率', '年级平均得分率'],
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: this.dirRate.qsList.map(item => item.qsNoDesc),
          axisLabel: {
            interval: 0,
            fontSize: 14,
          },
        },
        yAxis: {
          type: 'value',
          name: '得分率(%)',
          nameLocation: 'middle',
          nameGap: 40,
          nameRotate: 90,
          min: 0,
          max: 100,
          interval: 20,
        },
        series: [
          {
            name: '得分率',
            type: 'bar',
            data: this.dirRate.qsList.map(item => item.clsRate),
            itemStyle: {
              color: (params: any) => {
                const clsRate = this.dirRate.qsList[params.dataIndex].clsRate;
                const level = this.levelList.find((item, index) => {
                  if (index == this.levelList.length - 1) return clsRate >= item.start && clsRate <= item.end;
                  return clsRate >= item.start && clsRate < item.end;
                });
                return level ? level.color : '#606266';
              },
            },
            barWidth: '30%',
          },
          this.classId
            ? {
                name: '年级平均得分率',
                type: 'line',
                data: this.dirRate.qsList.map(item => item.grdRate),
                itemStyle: {
                  color: '#10b981',
                },
                smooth: true,
                symbol: 'circle',
                symbolSize: 8,
                lineStyle: {
                  width: 3,
                },
              }
            : null,
        ].filter(item => item),
      },
      true
    );
  }

  // 切换班级
  handleClassChange(classId: string) {
    if (this.classId === classId) return;
    this.classId = classId;
    this.getDirRate();
    this.$emit('changeClassId', classId);
  }

  // 跳转报告
  goReport(menuId: string) {
    this.$sessionSave.set('lookReportFrom', '/home/<USER>' + this.$route.query.bookCode);
    this.$router.push({
      path: '/home/<USER>',
      query: {
        menuId,
      },
    });
  }
}
</script>

<style scoped lang="scss">
// 定义主题色变量
$blue-theme: #3b82f6;
$green-theme: #10b981;

.select-container {
  display: flex;
  gap: 10px;
  padding: 0 20px;

  .select__label {
    font-size: 15px;
    color: #303133;
  }

  .select__content {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 20px;
  }

  .select__item {
    font-size: 15px;
    color: #303133;
    cursor: pointer;

    &:hover {
      color: lighten($blue-theme, 10%);
    }

    &.active {
      color: $blue-theme;
    }
  }
}

.data-cards-container {
  display: flex;
  gap: 20px;
  padding: 20px;
}

.data-card {
  width: 0;
  flex: 1;
  //   max-width: 350px;
  padding: 24px;
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  }

  // 提交率主题
  &--submit {
    background-color: rgba($blue-theme, 0.1);
    box-shadow: 0 2px 12px transparentize($blue-theme, 0.9);

    .rate-value {
      color: $blue-theme;
    }

    ::v-deep .el-progress-bar__inner {
      background-color: $blue-theme;
    }

    .text-btn {
      color: $blue-theme;

      &:hover {
        color: darken($blue-theme, 10%);
      }
    }
  }

  // 得分率主题
  &--score {
    background-color: rgba($green-theme, 0.1);
    box-shadow: 0 2px 12px transparentize($green-theme, 0.9);

    .rate-value {
      color: $green-theme;
    }

    ::v-deep .el-progress-bar__inner {
      background-color: $green-theme;
    }

    .text-btn {
      color: $green-theme;

      &:hover {
        color: darken($green-theme, 10%);
      }
    }
  }

  .data-card__rate {
    margin-bottom: 16px;
    line-height: 36px;

    .rate-title {
      font-size: 16px;
      color: #303133;
      font-weight: 500;
    }

    .rate-value {
      font-size: 28px;
      font-weight: 600;
    }
  }

  .data-card__info {
    margin: 16px 0;

    .info-item {
      font-size: 14px;
      color: #606266;
    }
  }

  .data-card__btn {
    text-align: center;
    margin-top: 20px;

    .el-button {
      font-size: 16px;
      font-weight: 500;
      padding: 8px 0;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  // 操作卡片主题
  &--action {
    background-color: #fff;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .action-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 20px;
    }

    .action-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }

    .action-btn {
      margin-left: 0;
      width: 100%;
      height: 40px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.chart-container {
  margin: 20px;
  padding: 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 16px;
}

.chart {
  width: 100%;
  height: 400px;
}
</style>

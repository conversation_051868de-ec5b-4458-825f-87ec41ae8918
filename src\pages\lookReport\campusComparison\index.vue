<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-07-24 10:13:18
 * @LastEditors: 小圆
-->
<template>
  <div class="campus-comparison">
    <headerFilter @updateFilter="updateFilter" ref="headerFilter" class="header-filter"></headerFilter>

    <div class="sticky-tabs-container">
      <el-tabs v-model="activeName" ref="tabRef" @tab-click="handleTabClick">
        <el-tab-pane v-for="item in curTabList" :key="item.id" :label="item.label" :name="item.id"> </el-tab-pane>
      </el-tabs>
      <el-button class="sticky-tabs-btn" type="primary" @click="showExportDialog">一键导出</el-button>
    </div>

    <div v-if="isInit">
      <FiveRate type="campus" :filterData="filterData" ref="fiveRate"></FiveRate>
      <GradeDistribute
        v-if="filterData.qType == 0"
        type="campus"
        :filterData="filterData"
        ref="gradeDistribute"
      ></GradeDistribute>
      <Online type="campus" :filterData="filterData" ref="online"></Online>
      <ScoreSection type="campus" :filterData="filterData" ref="scoreSection"></ScoreSection>
      <TotalRank type="campus" :filterData="filterData" ref="totalRank"></TotalRank>
    </div>

    <el-dialog title="一键导出" :visible.sync="exportDialogVisible" width="400px">
      <el-checkbox v-model="isAllChecked" @change="handleAllCheckedChange">全选</el-checkbox>

      <el-checkbox-group
        class="export-checkbox-group"
        v-model="exportCheckedList"
        @change="handleExportCheckedListChange"
      >
        <el-checkbox v-for="item in curTabList" :key="item.id" :label="item.id">
          {{ item.label }}
        </el-checkbox>
      </el-checkbox-group>

      <span slot="footer" class="dialog-footer">
        <el-button :disabled="!exportCheckedList.length" type="primary" @click="exportData">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import HeaderFilter from '@/components/headerFilter.vue';
import { Component, Vue, Ref } from 'vue-property-decorator';
import FiveRate from '../reportOverview/fiveRate.vue';
import GradeDistribute from '../reportOverview/gradeDistribute.vue';
import Online from '../reportOverview/online.vue';
import ScoreSection from '../classCompare/scoreSection.vue';
import TotalRank from '../classCompare/totalRank.vue';
import UserRole from '@/utils/UserRole';
import { getToken } from '@/service/auth';

@Component({
  components: {
    HeaderFilter,
    FiveRate,
    GradeDistribute,
    Online,
    ScoreSection,
    TotalRank,
  },
})
export default class CampusComparison extends Vue {
  // 当前选中的筛选项数据
  filterData = {
    classId: '',
    classIds: [],
    subjectId: '',
    phaseId: '',
    xfId: '',
    classList: [],
    aliasName: '', // 组合学科
    qType: 0, // 0:得分 1：赋分
    source: 0, // 0:成绩榜 1：组合成绩榜
    abPaper: '', // -1:普通 0:A卷 1:B卷
    quesInfo: null, // 作文题
  };

  // 校区对比列表
  tabList = [
    {
      id: 'fiveRate',
      label: '校区一分五率',
      exportPath: '/pexam/_/exp-campus-score-rate',
    },
    {
      id: 'gradeDistribute',
      label: '校区等级分布',
      exportPath: '/pexam/_/exp-campus-lv',
    },
    {
      id: 'online',
      label: '校区上线分析',
      exportPath: '/pexam/_/exp-campus-line-data',
    },
    {
      id: 'scoreSection',
      label: '校区成绩分段分析',
      exportPath: '/pexam/_/exp-campus-stat-segment',
    },
    {
      id: 'totalRank',
      label: '校区名次分析',
      exportPath: '/pexam/_/exp-campus-stat-rank',
    },
  ];

  get curTabList() {
    if (this.filterData.qType == 1) {
      return this.tabList.filter(item => item.id !== 'gradeDistribute');
    }
    return this.tabList;
  }

  // 当前选中的tab
  activeName = 'fiveRate';
  // 是否初始化
  isInit = false;
  // 导出弹窗是否显示
  exportDialogVisible = false;
  // 导出选中的tab
  exportCheckedList = [];
  // 全选
  isAllChecked = false;
  // 滚动监听器
  scrollHandler = null;

  @Ref() readonly fiveRate!: Vue;
  @Ref() readonly gradeDistribute!: Vue;
  @Ref() readonly online!: Vue;
  @Ref() readonly scoreSection!: Vue;
  @Ref() readonly totalRank!: Vue;

  mounted() {
    // 初始化滚动监听
    this.initScrollListener();
  }

  beforeDestroy() {
    // 移除滚动监听
    this.removeScrollListener();
  }

  // 初始化滚动监听
  initScrollListener() {
    this.scrollHandler = this.handleScroll.bind(this);
    const scrollContainer = document.querySelector('.scrollContainer');
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', this.scrollHandler);
    }
  }

  // 移除滚动监听
  removeScrollListener() {
    if (this.scrollHandler) {
      const scrollContainer = document.querySelector('.scrollContainer');
      if (scrollContainer) {
        scrollContainer.removeEventListener('scroll', this.scrollHandler);
      }
      this.scrollHandler = null;
    }
  }

  // 处理滚动事件
  handleScroll(e: Event) {
    if (!this.isInit) return;

    const root = e.target as HTMLElement;
    const doms = [];

    // 收集所有组件的DOM元素
    for (const item of this.curTabList) {
      const componentRef = this[item.id];

      if (componentRef && componentRef.$el) {
        doms.push({
          id: item.id,
          dom: componentRef.$el,
        });
      }
    }

    if (doms.length === 0) return;

    // 获取吸顶容器高度
    const stickyTabs = document.querySelector('.sticky-tabs-container');
    const stickyHeight = stickyTabs ? stickyTabs.clientHeight : 0;

    // 判断当前应该激活哪个标签
    if (root.scrollTop <= 0) {
      // 滚动到顶部
      this.activeName = doms[0].id;
    } else if (root.scrollTop + root.clientHeight >= root.scrollHeight - 1) {
      // 滚动到底部
      this.activeName = doms[doms.length - 1].id;
    } else {
      // 中间滚动，找到第一个进入可视区域的组件
      for (const item of doms) {
        const rect = item.dom.getBoundingClientRect();
        const scrollContainerRect = root.getBoundingClientRect();

        // 判断组件是否在可视区域内（考虑吸顶容器的高度）
        if (rect.top <= scrollContainerRect.top + stickyHeight + 50) {
          this.activeName = item.id;
        } else {
          break;
        }
      }
    }
  }

  // 更新筛选条件
  updateFilter(filterData) {
    this.filterData = this.$deepClone(filterData);
    this.isInit = true;
  }

  // 处理标签页点击事件
  handleTabClick(tab) {
    this.scrollToComponent(tab.name as string);
  }

  // 滚动到指定组件
  scrollToComponent(componentName: string) {
    this.$nextTick(() => {
      const componentRef: any = this[componentName];
      if (componentRef && componentRef.$el) {
        // 计算吸顶元素的高度
        const scrollContainer = document.querySelector('.scrollContainer');
        const stickyTabs = document.querySelector('.sticky-tabs-container');
        const stickyHeight = stickyTabs ? stickyTabs.clientHeight : 0;

        scrollContainer.scrollTo({
          top: componentRef.$el.offsetTop - stickyHeight,
          behavior: 'smooth',
        });
      }
    });
  }

  // 显示导出弹窗
  showExportDialog() {
    this.exportCheckedList = [];
    this.isAllChecked = false;
    this.exportDialogVisible = true;
  }

  // 导出选中的tab
  handleExportCheckedListChange(val) {
    this.exportCheckedList = val;
    this.isAllChecked = val.length === this.curTabList.length;
  }

  // 全选
  handleAllCheckedChange(val) {
    this.exportCheckedList = val ? this.curTabList.map(item => item.id) : [];
  }

  // 导出数据
  async exportData() {
    this.exportDialogVisible = false;

    if (this.exportCheckedList.length === 0) {
      this.$notify.warning({
        title: '提示',
        message: '请选择要导出的内容',
      });
      return;
    }

    // 显示加载状态
    const loading = this.$loading({
      lock: true,
      text: '正在生成ZIP文件，请稍候...',
      background: 'rgba(0, 0, 0, 0.7)',
    });

    try {
      // 动态导入JSZip
      const JSZip = (await import('jszip')).default;
      const zip = new JSZip();

      // 并行获取所有选中的Excel文件
      const downloadPromises = this.exportCheckedList.map(async (tabId, index) => {
        const tabInfo = this.curTabList.find(item => item.id === tabId);
        if (!tabInfo) return null;

        try {
          // 添加延迟避免同时发起太多请求
          await new Promise(resolve => setTimeout(resolve, index * 500));

          const downloadUrl = await this.getDownloadUrl(tabId, tabInfo);
          if (downloadUrl) {
            // 通过fetch获取文件内容
            const response = await fetch(downloadUrl);
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const arrayBuffer = await response.arrayBuffer();
            const fileName = `${tabInfo.label}.xlsx`;
            zip.file(fileName, arrayBuffer);

            return { success: true, name: tabInfo.label };
          }
          return { success: false, name: tabInfo.label, error: '获取下载链接失败' };
        } catch (error) {
          console.error(`下载${tabInfo.label}失败:`, error);
          return { success: false, name: tabInfo.label, error: error.message };
        }
      });

      const results = await Promise.all(downloadPromises);
      const successResults = results.filter(r => r && r.success);
      const failedResults = results.filter(r => r && !r.success);

      if (successResults.length === 0) {
        this.$notify.error({
          title: '导出失败',
          message: '所有文件下载失败',
        });
        return;
      }

      // 生成ZIP文件
      const zipBlob = await zip.generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: {
          level: 6,
        },
      });

      // 下载ZIP文件
      const url = URL.createObjectURL(zipBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `校区对比_${this.$sessionSave.get('reportDetail').examName}.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // 显示结果消息
      if (failedResults.length > 0) {
        const failedNames = failedResults.map(r => r.name).join('、');
        this.$notify.warning({
          title: '部分导出失败',
          message: `部分文件下载失败: ${failedNames}，成功下载${successResults.length}个文件`,
        });
      } else {
        this.$notify.success({
          title: '导出成功',
          message: `成功下载${successResults.length}个文件并打包为ZIP`,
        });
      }
    } catch (error) {
      console.error('导出失败:', error);
      this.$notify.error({
        title: '导出失败',
        message: error.message,
      });
    } finally {
      loading.close();
    }
  }

  // 获取下载URL
  async getDownloadUrl(tabId: string, tabInfo: any): Promise<string | null> {
    const { examId, campusCode, v, year } = this.$sessionSave.get('reportDetail');
    let role = '';

    if (!UserRole.isOperation) {
      const map = await UserRole.utils.getRoleSubjectClassMap(
        year,
        campusCode,
        this.$sessionSave.get('reportType') == 'school' ? true : false
      );
      role = JSON.stringify(map);
    }

    // 构建请求参数
    const baseParams: any = {
      examId: String(examId),
      v: String(v),
      role,
      token: getToken(),
      qType: String(this.filterData.qType),
    };

    // 根据不同的tab添加特定参数
    let params: any = { ...baseParams };

    switch (tabId) {
      case 'fiveRate':
        // 获取五率数据的特定参数
        const fiveRateComponent = this.fiveRate as any;
        if (fiveRateComponent && fiveRateComponent.contrastObj) {
          params.subjectId = this.$sessionSave
            .get('innerSubjectList')
            .map(item => item.id)
            .join(','); // 默认为空，表示所有学科
          params.contrastId = fiveRateComponent.contrastObj.examId || '';
          params.filter = fiveRateComponent.checkTypeList ? fiveRateComponent.checkTypeList.join(',') : '';
        }
        break;
      case 'gradeDistribute':
        // 等级分布的参数 - 使用基础参数即可
        break;
      case 'online':
        // 上线分析的参数 - 使用基础参数即可
        break;
      case 'scoreSection':
        // 成绩分段的参数
        const scoreSectionComponent = this.scoreSection as any;
        if (scoreSectionComponent && scoreSectionComponent.checkTypeList) {
          params.filter = scoreSectionComponent.checkTypeList.join(',');
        }
        break;
      case 'totalRank':
        // 名次分析的参数
        const totalRankComponent = this.totalRank as any;
        if (totalRankComponent) {
          params.type = totalRankComponent.rankType || 0;
          params.filter = totalRankComponent.checkTypeList ? totalRankComponent.checkTypeList.join(',') : '';
        }
        break;
    }

    // 构建请求URL
    const urlSearch = new URLSearchParams(params);
    return process.env.VUE_APP_KKLURL + tabInfo.exportPath + `?${urlSearch.toString()}`;
  }
}
</script>

<style scoped lang="scss">
.campus-comparison {
  padding: 20px;
}

.sticky-tabs-container {
  position: sticky;
  top: 0;
  background: #fff;
  z-index: 100;

  .sticky-tabs-btn {
    position: absolute;
    right: 0px;
    top: 0px;
  }

  ::v-deep {
    .el-tabs__nav-wrap {
      &::after {
        background-color: initial;
      }
    }
  }
}

.export-checkbox-group {
  display: flex;
  flex-wrap: wrap;

  ::v-deep .el-checkbox {
    margin-top: 10px;
    width: 150px;
  }
}
</style>

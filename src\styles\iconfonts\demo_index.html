<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>IconFont Demo</title>
  <link rel="shortcut icon" href="https://gtms04.alicdn.com/tps/i4/TB1_oz6GVXXXXaFXpXXJDFnIXXX-64-64.ico" type="image/x-icon"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">&#xe86b;</a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=744484" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe64d;</span>
                <div class="name">多选</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64e;</span>
                <div class="name">点评</div>
                <div class="code-name">&amp;#xe64e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64f;</span>
                <div class="name">返回</div>
                <div class="code-name">&amp;#xe64f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe650;</span>
                <div class="name">改进</div>
                <div class="code-name">&amp;#xe650;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe651;</span>
                <div class="name">关闭</div>
                <div class="code-name">&amp;#xe651;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe652;</span>
                <div class="name">管理组员</div>
                <div class="code-name">&amp;#xe652;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe653;</span>
                <div class="name">排序</div>
                <div class="code-name">&amp;#xe653;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe654;</span>
                <div class="name">全选</div>
                <div class="code-name">&amp;#xe654;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe655;</span>
                <div class="name">切换</div>
                <div class="code-name">&amp;#xe655;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe656;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe656;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe657;</span>
                <div class="name">搜索</div>
                <div class="code-name">&amp;#xe657;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe658;</span>
                <div class="name">随机分组</div>
                <div class="code-name">&amp;#xe658;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe659;</span>
                <div class="name">按姓名首字母排序</div>
                <div class="code-name">&amp;#xe659;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65a;</span>
                <div class="name">保存</div>
                <div class="code-name">&amp;#xe65a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65b;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe65b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65c;</span>
                <div class="name">重新分组</div>
                <div class="code-name">&amp;#xe65c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65d;</span>
                <div class="name">统计</div>
                <div class="code-name">&amp;#xe65d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65e;</span>
                <div class="name">表扬</div>
                <div class="code-name">&amp;#xe65e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69d;</span>
                <div class="name">关闭</div>
                <div class="code-name">&amp;#xe69d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65f;</span>
                <div class="name">banji</div>
                <div class="code-name">&amp;#xe65f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe660;</span>
                <div class="name">pingjiaguanli</div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>兼容性最好，支持 IE6+，及所有现代浏览器。</li>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持多色图标，这些多色图标在 Unicode 模式下将不能使用，如果有需求建议使用symbol 的引用方式</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot');
  src: url('iconfont.eot?#iefix') format('embedded-opentype'),
      url('iconfont.woff2') format('woff2'),
      url('iconfont.woff') format('woff'),
      url('iconfont.ttf') format('truetype'),
      url('iconfont.svg#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont iconfont-duoxuan"></span>
            <div class="name">
              多选
            </div>
            <div class="code-name">.iconfont-duoxuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-dianping"></span>
            <div class="name">
              点评
            </div>
            <div class="code-name">.iconfont-dianping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-fanhui"></span>
            <div class="name">
              返回
            </div>
            <div class="code-name">.iconfont-fanhui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-gaijin"></span>
            <div class="name">
              改进
            </div>
            <div class="code-name">.iconfont-gaijin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-guanbi"></span>
            <div class="name">
              关闭
            </div>
            <div class="code-name">.iconfont-guanbi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-guanlizuyuan"></span>
            <div class="name">
              管理组员
            </div>
            <div class="code-name">.iconfont-guanlizuyuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-paixu"></span>
            <div class="name">
              排序
            </div>
            <div class="code-name">.iconfont-paixu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-quanxuan"></span>
            <div class="name">
              全选
            </div>
            <div class="code-name">.iconfont-quanxuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-qiehuan"></span>
            <div class="name">
              切换
            </div>
            <div class="code-name">.iconfont-qiehuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-shanchu"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.iconfont-shanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-sousuo"></span>
            <div class="name">
              搜索
            </div>
            <div class="code-name">.iconfont-sousuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-suijifenzu"></span>
            <div class="name">
              随机分组
            </div>
            <div class="code-name">.iconfont-suijifenzu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-xingmingpaixu"></span>
            <div class="name">
              按姓名首字母排序
            </div>
            <div class="code-name">.iconfont-xingmingpaixu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-baocun"></span>
            <div class="name">
              保存
            </div>
            <div class="code-name">.iconfont-baocun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-bianji"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.iconfont-bianji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-zhongxinfenzu"></span>
            <div class="name">
              重新分组
            </div>
            <div class="code-name">.iconfont-zhongxinfenzu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-tongji"></span>
            <div class="name">
              统计
            </div>
            <div class="code-name">.iconfont-tongji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-biaoyang"></span>
            <div class="name">
              表扬
            </div>
            <div class="code-name">.iconfont-biaoyang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-close"></span>
            <div class="name">
              关闭
            </div>
            <div class="code-name">.iconfont-close
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-class-evaluate"></span>
            <div class="name">
              banji
            </div>
            <div class="code-name">.iconfont-class-evaluate
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfont-evaluate"></span>
            <div class="name">
              pingjiaguanli
            </div>
            <div class="code-name">.iconfont-evaluate
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>兼容性良好，支持 IE8+，及所有现代浏览器。</li>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
          <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont iconfont--xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-duoxuan"></use>
                </svg>
                <div class="name">多选</div>
                <div class="code-name">#iconfont-duoxuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-dianping"></use>
                </svg>
                <div class="name">点评</div>
                <div class="code-name">#iconfont-dianping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-fanhui"></use>
                </svg>
                <div class="name">返回</div>
                <div class="code-name">#iconfont-fanhui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-gaijin"></use>
                </svg>
                <div class="name">改进</div>
                <div class="code-name">#iconfont-gaijin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-guanbi"></use>
                </svg>
                <div class="name">关闭</div>
                <div class="code-name">#iconfont-guanbi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-guanlizuyuan"></use>
                </svg>
                <div class="name">管理组员</div>
                <div class="code-name">#iconfont-guanlizuyuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-paixu"></use>
                </svg>
                <div class="name">排序</div>
                <div class="code-name">#iconfont-paixu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-quanxuan"></use>
                </svg>
                <div class="name">全选</div>
                <div class="code-name">#iconfont-quanxuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-qiehuan"></use>
                </svg>
                <div class="name">切换</div>
                <div class="code-name">#iconfont-qiehuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-shanchu"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#iconfont-shanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-sousuo"></use>
                </svg>
                <div class="name">搜索</div>
                <div class="code-name">#iconfont-sousuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-suijifenzu"></use>
                </svg>
                <div class="name">随机分组</div>
                <div class="code-name">#iconfont-suijifenzu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-xingmingpaixu"></use>
                </svg>
                <div class="name">按姓名首字母排序</div>
                <div class="code-name">#iconfont-xingmingpaixu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-baocun"></use>
                </svg>
                <div class="name">保存</div>
                <div class="code-name">#iconfont-baocun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-bianji"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#iconfont-bianji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-zhongxinfenzu"></use>
                </svg>
                <div class="name">重新分组</div>
                <div class="code-name">#iconfont-zhongxinfenzu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-tongji"></use>
                </svg>
                <div class="name">统计</div>
                <div class="code-name">#iconfont-tongji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-biaoyang"></use>
                </svg>
                <div class="name">表扬</div>
                <div class="code-name">#iconfont-biaoyang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-close"></use>
                </svg>
                <div class="name">关闭</div>
                <div class="code-name">#iconfont-close</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-class-evaluate"></use>
                </svg>
                <div class="name">banji</div>
                <div class="code-name">#iconfont-class-evaluate</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfont-evaluate"></use>
                </svg>
                <div class="name">pingjiaguanli</div>
                <div class="code-name">#iconfont-evaluate</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>

<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-04-02 09:03:13
 * @LastEditors: 小圆
-->

<template>
  <div>
    <div v-loading="tableLoading">
      <base-table
        v-if="tableData.length"
        :data="tableData"
        :column="tableColumns"
        v-bind="getTableAttr()"
        v-drag-table
      ></base-table>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>

    <div class="score-type">
      <el-radio-group v-model="chartType" @input="onChartTypeChange">
        <el-radio-button :label="0">柱状图</el-radio-button>
        <el-radio-button :label="1">雷达图</el-radio-button>
      </el-radio-group>
    </div>

    <div class="chart-wrapper">
      <div class="chart-box chart-box--left">
        <div
          v-for="(item, i) in subjectColumns"
          :key="item.title"
          style="margin-top: 50px; width: 90%"
          :id="BASE_SUBJECT_PREFIX + i"
        ></div>
      </div>

      <div class="chart-box chart-box--right">
        <div
          v-for="(item, i) in tableData"
          :key="item.sort"
          style="margin-top: 50px; width: 90%"
          :id="BASE_SCORE_PREFIX + i"
        ></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins, Prop } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';
import {
  getDefaultGrid,
  getDefaultLegend,
  getDefaultTitle,
  getDefaultToolBox,
} from '../plugins/DefaultEchartsOption';
const BASE_SUBJECT_PREFIX = 'subject_index_';
const BASE_SCORE_PREFIX = 'score_index_';

@Component({
  components: {},
})
export default class subjectEquilibrium extends Mixins(TableCommon) {
  BASE_SUBJECT_PREFIX = BASE_SUBJECT_PREFIX;
  BASE_SCORE_PREFIX = BASE_SCORE_PREFIX;

  // 表格左侧固定列
  tableLeftFixed: any[] = ['className', 'teaName'];

  // 0: 折线图 1: 雷达图
  chartType: 0 | 1 = 0;

  // 班级学科均衡性及对比图表列表
  standardScoreChartArr: EChartsType[] = [];

  get subjectColumns() {
    return this.tableColumns.filter(item => item.title !== '教学单位' && item.title !== '负责人');
  }

  async callbackGetTableData() {
    await this.$nextTick();
    this.renderSubjectAvgChart();
    this.renderStandardScoreChart();
  }

  // 渲染学科平均分对比图
  renderSubjectAvgChart() {
    this.subjectColumns.forEach((item, index) => {
      const dom = document.getElementById(BASE_SUBJECT_PREFIX + index);
      const echarts = this.$echarts.init(dom, null, { height: 600 });

      const prop = item.children[0].prop;

      let option: EChartsOption = {
        toolbox: getDefaultToolBox(),
        title: {
          ...getDefaultTitle(),
          text: `${item.title}平均分对比图`,
        },
        tooltip: {
          trigger: 'item',
        },
        legend: getDefaultLegend({ show: false }),
        grid: getDefaultGrid({
          bottom: '5%',
        }),
        xAxis: {
          type: 'value',
          axisTick: {
            // inside: true,
          },
          // scale: true,
          minInterval: 1,
        },
        yAxis: {
          type: 'category',
          data: this.tableData.map(item => item.className),
        },
        series: [
          {
            type: 'bar',
            data: this.tableData.map(item => item[prop]),
            barMaxWidth: 50,
            label: {
              show: true,
            },
          },
        ],
      };
      echarts.setOption(option);
    });
  }

  //渲染班级学科均衡性及对比图
  async renderStandardScoreChart() {
    this.clearChartArr(this.standardScoreChartArr);
    await this.$nextTick();
    if (this.chartType == 0) {
      this.renderStandardScoreBarChart();
    } else {
      this.renderStandardScoreRadarChart();
    }
  }

  // 渲染柱状图
  renderStandardScoreBarChart() {
    this.tableData.forEach((item, index) => {
      const dom = document.getElementById(BASE_SCORE_PREFIX + index);
      const echarts = this.$echarts.init(dom, null, { height: 500 });
      this.standardScoreChartArr.push(echarts);
      const data = [];
      this.subjectColumns.forEach(col => {
        const prop = col.children[1].prop;
        data.push(item[prop]);
      });

      let option: EChartsOption = {
        toolbox: getDefaultToolBox(),
        title: {
          ...getDefaultTitle(),
          text: `${item.className}:学科均衡性及对比图`,
        },
        tooltip: {
          trigger: 'item',
        },
        legend: getDefaultLegend({ show: false }),
        grid: getDefaultGrid(),
        xAxis: {
          type: 'category',
          data: this.subjectColumns.map(item => item.title),
          axisLabel: {
            rotate: 45,
          },
        },
        yAxis: {
          type: 'value',
          // axisTick: {
          // inside: true,
          // },
          // scale: true,
        },
        series: [
          {
            type: 'bar',
            data: data,
            label: {
              show: true,
            },
            barMaxWidth: 50,
          },
        ],
      };
      echarts.setOption(option);
    });
  }

  // 渲染雷达图
  renderStandardScoreRadarChart() {
    this.tableData.forEach((item, index) => {
      const dom = document.getElementById(BASE_SCORE_PREFIX + index);
      const echarts = this.$echarts.init(dom, null, { height: 500 });
      this.standardScoreChartArr.push(echarts);

      const data = [];
      const indicator = [];
      this.subjectColumns.forEach(col => {
        const prop = col.children[1].prop;
        data.push(parseFloat(item[prop]));
        indicator.push({
          name: col.title,
        });
      });

      let option: EChartsOption = {
        grid: getDefaultGrid(),
        legend: getDefaultLegend(),
        title: {
          ...getDefaultTitle(),
          text: `${item.className}:学科均衡性及对比图`,
        },
        tooltip: {
          trigger: 'item',
          axisPointer: {
            type: 'shadow',
          },
        },
        toolbox: getDefaultToolBox(),
        radar: {
          indicator: indicator,
          center: ['50%', '50%'],
          radius: 150,
          startAngle: 90,
          axisName: {
            color: '#428BD4',
          },
        },
        series: {
          type: 'radar',
          name: item.className,
          data: [data],
        },
      };

      echarts.setOption(option);
    });
  }

  // 清空图表数组
  clearChartArr(arr) {
    arr.forEach(item => {
      if (item) item.dispose();
    });
    arr = [];
  }

  onChartTypeChange(type) {
    this.renderStandardScoreChart();
  }
}
</script>

<style scoped lang="scss">
.chart-wrapper {
  display: flex;
  gap: 200px;

  .chart-box {
    &--left {
      width: 750px;
    }

    &--right {
      width: 500px;
    }
  }
}

.score-type {
  margin: 20px 0;
}
</style>

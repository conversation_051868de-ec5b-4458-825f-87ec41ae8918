<template>
  <div>
    <div class="setting-header">
      <div class="title">成绩统计规则</div>
      <div class="header-right" v-if="!examId">
        <el-button type="primary" size="small" @click="reset" :loading="resetLoading">恢复默认</el-button>
        <el-button type="primary" size="small" @click="save" :loading="saveLoading">保存</el-button>
      </div>
    </div>

    <div class="setting-content">
      <div class="switch-item" v-if="examId">
        <el-checkbox v-model="checkMin" @change="changeCheckMin">
          <span> 参考科目少于 </span>
          <el-input-number
            v-model="jCfg.min"
            style="width: 60px"
            size="small"
            :disabled="!checkMin"
            :min="0"
            :max="subjectList.length ? subjectList.length : Infinity"
            :controls="false"
            :step-strictly="true"
          />
          <span> 科，不纳入全科统计 </span>
        </el-checkbox>
      </div>

      <div class="switch-item">
        <div class="title">不统计实考0分的学生</div>
        <el-switch class="switch" v-model="jCfg.zero" :active-value="1" :inactive-value="0" />

        <span class="rule-setting tip">
          <span class="title">*注：计算平均分及一分五率等数据默认不包含0分学生，关闭后则表示0分学生包含在内。</span>
        </span>
      </div>
    </div>
    <div class="setting-content">
      <div class="switch-item">
        <div class="title">成绩单仅显示等级</div>
        <el-switch class="switch" v-model="jCfg.onlyLv" :active-value="1" :inactive-value="0" />

        <span class="rule-setting tip">
          <span class="title">*注：开启后，成绩单报表不显示学生分数、排名等数据，仅显示等级。</span>
        </span>
      </div>
    </div>

    <SettingSaveDialog
      v-if="isShowSaveDialog"
      :currentPhase="currentPhase"
      :currentGradeId="currentGradeId"
      :currentGradeItem="currentGradeItem"
      :currentGradeList="currentGradeList"
      :examId="examId"
      @save="handleSave"
      @closed="isShowSaveDialog = false"
    ></SettingSaveDialog>
  </div>
</template>

<script lang="ts">
import { Component, Mixins, Vue } from 'vue-property-decorator';
import SchoolSettingMixin from '../SchoolSetting.mixin';
import { getExamSubject, getSchCfgAPI, restoreSchCfgAPI, setSchCfgAPI } from '@/service/pexam';
import { SchoolSettingType, SettingChangeParams } from '../../types';
import SettingSaveDialog from '../SettingSaveDialog.vue';

@Component({
  components: {
    SettingSaveDialog,
  },
})
export default class ScoreStatisticRule extends Mixins(SchoolSettingMixin) {
  jCfg = {
    // 不统计实考0分的学生
    zero: 0,
    min: -1,
    // 仅等级
    onlyLv: 0,
  };
  // 是否勾选参考科目少于
  checkMin = false;
  // 考试科目
  subjectList = [];
  // 保存loading
  saveLoading: boolean = false;
  // 恢复loading
  resetLoading: boolean = false;
  // 保存对话框
  private isShowSaveDialog = false;

  mounted() {
    this.getConfig();

    if (this.examId) {
      this.getExamSubjectList();
    }
  }

  // 获取考试科目
  async getExamSubjectList() {
    const res = await getExamSubject({
      examId: this.examId,
    });

    this.subjectList = res.data;
  }

  // 获取成绩统计规则
  async getConfig() {
    const res = await getSchCfgAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      phase: this.currentPhase,
      type: SchoolSettingType.ScoreStatisticRule,
      gradeId: this.currentGradeId,
      examId: this.examId,
    });
    if (res.data && res.data.jCfg) {
      this.jCfg = res.data.jCfg;
    } else {
      this.jCfg = {
        zero: 0,
        min: -1,
        onlyLv: 0,
      };
    }
    this.checkMin = this.jCfg.min !== -1;
    this.jCfg.min = this.checkMin ? this.jCfg.min : undefined;
  }

  // 改变参考科目少于
  changeCheckMin(val) {
    this.jCfg.min = val ? this.subjectList.length : undefined;
  }

  // 设置改变
  onSettingChange(data: SettingChangeParams) {
    this.getConfig();
  }

  // 恢复默认
  async reset() {
    this.resetLoading = true;
    try {
      await this.restoreSchCfg();
      this.$notify.success({
        title: '成功',
        message: '恢复成功',
      });
      this.getConfig();
    } catch (error) {
      this.$notify.error({
        title: '失败',
        message: '恢复失败',
      });
    } finally {
      this.resetLoading = false;
    }
  }

  // 检查配置
  checkConfig() {
    if (this.examId) {
      if (this.checkMin && this.jCfg.min === undefined) {
        this.$notify.error({
          title: '【成绩统计规则】',
          message: '参考科目不能为空',
        });
        return Promise.reject(false);
      }
    } else {
      return Promise.resolve(true);
    }
  }

  // 保存
  async save() {
    await this.checkConfig();

    if (this.currentGradeId == '0') {
      this.isShowSaveDialog = true;
    } else {
      this.handleSave([this.currentGradeId]);
    }
  }

  // 执行保存
  async handleSave(gradeIds: string[]) {
    this.saveLoading = true;
    this.isShowSaveDialog = false;

    try {
      await this.setSchCfg(gradeIds);
      this.$notify.success({
        title: '成功',
        message: '保存成功',
      });
    } catch (error) {
      this.$notify.error({
        title: '失败',
        message: '保存失败',
      });
    } finally {
      this.saveLoading = false;
    }
  }

  // 设置学校配置
  async setSchCfg(gradeIds: string[]) {
    const jCfg = this.getCfg().jCfg;
    const promises = [];
    for (const gradeId of gradeIds) {
      promises.push(
        setSchCfgAPI({
          schId: this.$sessionSave.get('schoolInfo').id,
          schName: this.$sessionSave.get('schoolInfo').schoolName,
          phase: this.currentPhase,
          type: SchoolSettingType.ScoreStatisticRule,
          examId: this.examId,
          gradeId,
          jCfg,
        })
      );
    }
    return Promise.all(promises);
  }

  // 获取配置信息
  public getCfg() {
    const jCfg = this.$deepClone(this.jCfg);
    if (!this.checkMin || jCfg.min === undefined) {
      jCfg.min = -1;
    }
    return {
      type: SchoolSettingType.ScoreStatisticRule,
      jCfg,
    };
  }

  // 恢复学校配置
  public restoreSchCfg() {
    return restoreSchCfgAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      phase: this.currentPhase,
      type: SchoolSettingType.ScoreStatisticRule,
      gradeId: this.currentGradeId,
      examId: this.examId,
    });
  }
}
</script>

<style lang="scss" scoped>
@import '../page-style.scss';

.setting-content {
  display: flex;
  gap: 20px;
}

.switch-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .title {
    margin-right: 16px;
  }

  .switch {
    margin-right: 20px;
  }
}

.tip {
  margin-top: 0;
  margin-bottom: 0;
}
</style>

<template>
  <div>
    <div class="setting-header">
      <div class="title">总名次设置</div>
      <div class="header-right" v-if="!examId">
        <el-button type="primary" size="small" @click="reset" :loading="resetLoading">恢复默认</el-button>
        <el-button type="primary" size="small" @click="save" :loading="saveLoading">保存</el-button>
      </div>
    </div>

    <SettingSubjectHeader
      :single="true"
      :currentPhase="currentPhase"
      :currentGradeId="currentGradeId"
      :currentGradeItem="currentGradeItem"
      :currentGradeList="currentGradeList"
      :examId="examId"
      @init="onSubjectInit"
      @change="onSubjectChange"
      @changeSubjectId="onChangeSubjectId"
      ref="settingSubjectHeader"
    ></SettingSubjectHeader>

    <el-form v-if="currentData" :model="currentData" label-position="left" label-suffix="：">
      <el-form-item label="配置应用到所有学科">
        <el-radio-group v-model="subjectType">
          <el-radio :label="SubjectType.All">是</el-radio>
          <el-radio :label="SubjectType.Single">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="类型">
        <el-radio-group v-model="type" @change="changeType">
          <el-radio-button :label="DataType.rank">按名次分析</el-radio-button>
          <el-radio-button :label="DataType.rate">按比例分析</el-radio-button>
        </el-radio-group>
      </el-form-item>

      <el-table class="setting-table" :data="currentTableData" style="width: 50%" border>
        <el-table-column
          prop="value"
          :label="type == DataType.rank ? '名次设置' : '比例设置'"
          align="center"
          :resizable="false"
        >
          <template #default="scope">
            <el-form-item
              label=""
              :prop="`${type}[${scope.$index}]`"
              :rules="[{ required: true, message: '必填项', trigger: 'blur' }]"
            >
              <span>前</span>
              <el-input-number
                class="input-number"
                v-model="currentTableData[scope.$index]"
                size="small"
                :controls="false"
                :min="
                  scope.$index == 0
                    ? 0
                    : type == DataType.rate
                    ? currentTableData[scope.$index - 1]
                    : currentTableData[scope.$index - 1] + 1 || 0
                "
                :max="
                  scope.$index == currentTableData.length - 1
                    ? type == DataType.rate
                      ? 100
                      : Infinity
                    : currentTableData[scope.$index + 1] - 1
                "
                :step-strictly="type == DataType.rank ? true : false"
                @change="changeNumber(scope.$index)"
              ></el-input-number>
              <span v-if="type == DataType.rank">名</span>
              <span v-else>%</span>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" :resizable="false">
          <template #default="scope">
            <el-button type="text" size="small" icon="el-icon-plus" @click="deleteRow(currentTableData, scope.$index)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="add-row">
        <el-button type="primary" @click="addRow(currentTableData)">新增</el-button>
      </div>
    </el-form>

    <SettingSaveDialog
      v-if="isShowSaveDialog"
      :currentPhase="currentPhase"
      :currentGradeId="currentGradeId"
      :currentGradeItem="currentGradeItem"
      :currentGradeList="currentGradeList"
      :examId="examId"
      @save="handleSave"
      @closed="isShowSaveDialog = false"
    ></SettingSaveDialog>
  </div>
</template>

<script lang="ts">
import { Component, Mixins, Prop, Ref, Vue } from 'vue-property-decorator';
import SettingSubjectHeader from '../SettingSubjectHeader.vue';
import SchoolSettingMixin from '../SchoolSetting.mixin';
import { getSchCfgAPI, restoreSchCfgAPI, setSchCfgAPI } from '@/service/pexam';
import { SchoolSettingType, SettingChangeParams } from '../../types';
import SettingSaveDialog from '../SettingSaveDialog.vue';

enum SubjectType {
  /** 应用所有学科 */
  All = 1,
  /** 应用单个学科 */
  Single = 2,
}

enum DataType {
  rank = 'rankData',
  rate = 'rateData',
}

interface DataItem {
  id: string;
  name: string;
  rankData: number[];
  rateData: number[];
}

interface JCfg {
  data: DataItem[];
}

@Component({
  components: {
    SettingSubjectHeader,
    SettingSaveDialog,
  },
})
export default class RankSetting extends Mixins(SchoolSettingMixin) {
  @Ref() settingSubjectHeader!: SettingSubjectHeader;

  // 配置项
  private jCfg: JCfg = {
    data: [],
  };

  // 学科列表
  private subjectList = [];
  // 当前数据
  private currentData: DataItem = null;
  // 学科类型
  private subjectType: SubjectType = SubjectType.Single;
  // 类型
  private type: DataType = DataType.rank;
  // 重置loading
  private resetLoading = false;
  // 保存loading
  private saveLoading = false;
  // 保存对话框
  private isShowSaveDialog = false;
  // 学科类型
  SubjectType = SubjectType;
  // 类型
  DataType = DataType;

  // 当前表格数据
  get currentTableData() {
    if (this.type == DataType.rank) {
      return this.currentData.rankData;
    } else {
      return this.currentData.rateData;
    }
  }

  // 学科初始化
  onSubjectInit(subjectList) {
    this.subjectList = subjectList;
    this.getConfig();
  }

  // 获取配置项
  async getConfig() {
    const res = await getSchCfgAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      type: SchoolSettingType.RankSetting,
      phase: this.currentPhase,
      gradeId: this.currentGradeId,
      examId: this.examId,
    });
    if (res.data && res.data.jCfg) {
      this.jCfg = res.data.jCfg;
    } else {
      this.jCfg = {
        data: this.subjectList.map(item => {
          return {
            id: item.id,
            name: item.name,
            rankData: [50, 100, 200, 300],
            rateData: [5, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
          };
        }),
      };
      this.subjectType = SubjectType.Single;
    }
    this.currentData = this.jCfg.data[0];
    this.settingSubjectHeader.setCurrentSubjectIds(this.jCfg.data.map(item => item.id));
  }

  // 更换类型
  changeType(val) {
    this.type = val;
  }

  // 学科列表变化
  onSubjectChange(subjectList) {
    this.subjectList = subjectList;
    let data: DataItem[] = [];
    subjectList.forEach(subItem => {
      let dataItem = this.jCfg.data.find(item => item.id == subItem.id);
      if (dataItem) {
        data.push(dataItem);
      } else {
        data.push({
          id: subItem.id,
          name: subItem.name,
          rankData: [50, 100, 200, 300],
          rateData: [5, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
        });
      }
    });
    this.jCfg = {
      data: data,
    };
  }

  // 单选学科id变化
  onChangeSubjectId(id: string) {
    this.currentData = this.jCfg.data.find(item => item.id == id);
  }

  // 设置更改
  onSettingChange(data: SettingChangeParams) {}

  // 删除行
  deleteRow(data, index) {
    data.splice(index, 1);
  }

  // 新增行
  addRow(data) {
    if (data.length >= 50) {
      this.$message.warning('最多只能添加50段');
      return;
    }
    data.push(undefined);
  }

  // 恢复默认
  async reset() {
    this.resetLoading = true;
    try {
      await this.restoreSchCfg();
      this.$notify.success({
        title: '成功',
        message: '恢复成功',
      });
      this.settingSubjectHeader.reset();
    } catch (error) {
      this.$notify.error({
        title: '失败',
        message: '恢复失败',
      });
    } finally {
      this.resetLoading = false;
    }
  }

  // 保存检查
  checkSave() {
    let datas = [];
    if (this.subjectType == SubjectType.Single) {
      datas = this.jCfg.data;
    } else {
      datas = [this.currentData];
    }

    if (datas.filter(item => item).length == 0) {
      this.$notify.error({
        title: '【总名次设置】',
        message: '请选择学科',
      });
      return;
    }

    for (const dataItem of datas) {
      if (dataItem.rankData.some(item => item === undefined)) {
        this.$notify.error({
          title: '【总名次设置】',
          message: `${dataItem.name}按名次分析请填写完整`,
        });
        return;
      }
      if (dataItem.rateData.some(item => item === undefined)) {
        this.$notify.error({
          title: '【总名次设置】',
          message: `${dataItem.name}按比例分析请填写完整`,
        });
        return;
      }
    }
    return true;
  }

  // 保存
  async save() {
    if (!this.checkSave()) return;

    if (this.currentGradeId == '0') {
      this.isShowSaveDialog = true;
    } else {
      this.handleSave([this.currentGradeId]);
    }
  }

  // 执行保存
  async handleSave(gradeIds: string[]) {
    this.saveLoading = true;
    this.isShowSaveDialog = false;
    try {
      await this.setSchCfg(gradeIds);
      this.$notify.success({
        title: '成功',
        message: '保存成功',
      });
    } catch (error) {
      this.$notify.error({
        title: '失败',
        message: '保存失败',
      });
    } finally {
      this.saveLoading = false;
    }
  }

  // 恢复学校配置
  public restoreSchCfg() {
    return restoreSchCfgAPI({
      phase: this.currentPhase,
      schId: this.$sessionSave.get('schoolInfo').id,
      type: SchoolSettingType.RankSetting,
      gradeId: this.currentGradeId,
      examId: this.examId,
    });
  }

  // 设置学校配置
  public setSchCfg(gradeIds: string[]) {
    const jCfg = this.getCfg().jCfg;

    const promises = [];
    for (const gradeId of gradeIds) {
      promises.push(
        setSchCfgAPI({
          schId: this.$sessionSave.get('schoolInfo').id,
          schName: this.$sessionSave.get('schoolInfo').schoolName,
          type: SchoolSettingType.RankSetting,
          phase: this.currentPhase,
          gradeId,
          jCfg,
          examId: this.examId,
        })
      );
    }
    return Promise.all(promises);
  }

  // 获取配置信息
  public getCfg() {
    if (this.subjectType == SubjectType.All) {
      this.jCfg.data.forEach(item => {
        item.rankData = JSON.parse(JSON.stringify(this.currentData.rankData));
        item.rateData = JSON.parse(JSON.stringify(this.currentData.rateData));
      });
    }
    return {
      type: SchoolSettingType.RankSetting,
      jCfg: this.jCfg,
    };
  }

  // 修改名次
  changeNumber(index) {
    if (this.type != DataType.rate) return;

    const data = this.currentTableData;
    if (data[index] && data[index].toString().split('.')[1]?.length > 2) {
      let val = data[index];
      const factor = Math.pow(10, 2);
      val = Math.round(val * factor) / factor;

      this.$nextTick(() => {
        this.$set(data, index, val);
      });
    }
  }
}
</script>

<style scoped lang="scss">
@import '../page-style.scss';

.add-row {
  width: 50%;
  border: 1px solid #ebeef5;
  border-top: none;
  padding: 10px;
  text-align: center;
}

.input-number {
  margin: 0 10px;
  width: 80px;
}
</style>

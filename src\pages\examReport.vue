<template>
  <div>
    <div class="examReport display_flex flex-direction_column">
      <div class="examReport__header clearfix">
        <div class="header__select" v-if="accountType != 4 && accountType != 5">
          <span class="select__label">角色：</span>
          <el-select v-model="roles" @change="changeRoles" :collapse-tags="false" class="role-select" placeholder="请选择">
            <el-option v-for="item in roleList" :key="item.types" :label="item.status" :value="item.types">
            </el-option>
          </el-select>
        </div>
        <!--学年-->
        <div class="header__select">
          <span class="select__label">学年：</span>
          <el-select v-model="yearValue" class="year-select" @change="changeYear" placeholder="请选择">
            <el-option v-for="item in years" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </div>
        <!--学期-->
        <div class="header__select">
          <span class="select__label">学期：</span>
          <el-select v-model="termValue" class="term-select" @change="changeTerm" placeholder="请选择">
            <el-option v-for="item in terms" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </div>
        <!--年级 (仅校长和运营账号显示年级切换)-->
        <div class="header__select">
          <span class="select__label">年级：</span>
          <el-select v-model="gradeValue" class="grade-select" @change="changeGrade" placeholder="请选择">
            <el-option v-for="item in grdList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </div>
        <!--搜索-->
        <div class="header__select">
          <div class="header__serarch clearfix display_flex">
            <el-input class="search__text" placeholder="输入考试名称搜索" v-model="searchValue" @keyup.enter.native="searchReport"
              clearable>
            </el-input>
            <div class="search__icon el-icon-search display_flex align-items_center justify-content_flex-center"
              @click="searchReport"></div>
          </div>
        </div>
        <el-button type="primary" class="search-button" @click="searchReport">查询</el-button>
        <el-button class="refresh-button" :loading="refreshLoading" @click="refreshReport">刷新</el-button>
        <!--创建考试-->
        <el-button type="primary" v-if="$sessionSave.get('loginInfo') &&
          ($sessionSave.get('loginInfo').user_type === 5 ||
            $sessionSave.get('loginInfo').admin_type === 2)
          " class="pull-right" @click="createReport">创建考试
        </el-button>
      </div>
      <div class="examReport__main flex_1 display_flex flex-direction_column">
        <!--学科-->
        <div class="header__class display_flex clearfix">
          <span class="leftText pull-left">学科：</span>
          <ul class="class__ul pull-left flex_1">
            <li class="class__li" :class="{ active: subjectValue === item.id }" style="margin-bottom: 10px"
              v-for="item in subjectList" :key="item.id" v-if="item.show || item.show == undefined"
              @click="changeFilter('subject', item.id)">
              {{ item.name }}
            </li>
          </ul>
          <!--<span class="rightText pull-right">更多</span>-->
        </div>
        <!--类别-->
        <div class="header__class category display_flex clearfix">
          <span class="leftText pull-left">类别：</span>
          <ul class="class__ul pull-left flex_1">
            <li class="class__li" :class="{ active: typeValue === item.id }" v-for="item in typeList" :key="item.id"
              @click="changeFilter('category', item.id)">
              {{ item.name }}
            </li>
          </ul>
          <!--<span class="rightText pull-right">更多</span>-->
        </div>
        <div class="examReport__content flex_1" :class="examReportList.length ? '' : 'display_flex align-items_center'
          ">
          <!--个册列表数据-->
          <ul id="popoverUl" class="examReport__list list-none" v-if="examReportList.length" v-loading="listLoading"
            element-loading-text="加载中...">
            <li v-for="(item, index) in examReportList" :key="index">
              <div class="examReport__item">
                <div class="square" v-if="item.persRecoveryType === 1">
                  <span class="square-word">个性化</span>
                </div>
                <div class="exam-name-area">
                  <div class="exam-name">
                    <div class="exam-category">
                      <div>{{ item.categoryName }}</div>
                    </div>
                    <div style="margin-right: 20px">
                      {{ item.examName }}
                    </div>
                    <!-- <el-popover
                      placement="bottom"
                      title=""
                      popper-class="editPopper"
                      width="200"
                      trigger="click"
                    >
                      <ul class="list-none">
                        <li @click="createReport(item)" v-if="!item.source">
                          编辑
                        </li>
                        <li @click="deleteItem(item)" style="color: red">
                          删除
                        </li>
                      </ul>
                      <span
                        class="moreEdit"
                        slot="reference"
                        v-if="
                          $sessionSave.get('loginInfo') &&
                          ($sessionSave.get('loginInfo').user_type === 5 ||
                            $sessionSave.get('loginInfo').admin_type == 2)
                        "
                      >
                      </span>
                    </el-popover> -->
                  </div>
                  <div class="exam-detail display_flex align-items_center">
                    <span class="exam-grade">{{ item.gradeName }}</span>
                    <span class="line-gap"></span>
                    <span class="exam-subject text-ellipsis" :title="item.subjectName"
                      style="max-width: 350px">{{ item.subjectName }}</span>
                    <!-- <span class="line-gap"></span> -->
                    <!-- <span class="exam-category">{{ item.categoryName }}</span> -->
                    <span class="line-gap"></span>
                    <span class="exam-time">{{
                      item.examDateTime.substring(0, 16)
                    }}</span>
                  </div>
                </div>
                <div style="display: flex">
                  <!-- item.source来源 0:一起作业, 1:大精,   2 广州奥体, 3 扫描仪作业 -->
                  <span v-if="$sessionSave.get('loginInfo') &&
                      item.source != 3 &&
                      ($sessionSave.get('loginInfo').user_type === 5 ||
                        $sessionSave.get('loginInfo').admin_type == 2)
                      ">
                    <el-link :underline="false" type="primary" class="upload-btn" @click="showUpload(item, index)">
                      上传试卷
                      <i class="el-icon-d-arrow-right" :style="{
                        transform: item.isActive
                          ? 'rotate(-90deg)'
                          : 'rotate(90deg)',
                      }"></i>
                    </el-link>
                    <el-link :underline="false" class="line">-------</el-link>
                    <el-link :underline="false" type="primary" class="upload-btn" @click="exportGrade(item)"
                      v-if="item.source != 3">导入全科成绩
                    </el-link>
                    <el-link :underline="false" type="primary" class="score-btn" @click="syncData(item)" v-else>同步数据
                    </el-link>
                    <el-divider direction="vertical"></el-divider>
                  </span>
                  <el-link :underline="false" type="primary" class="report-btn" v-if="item.source == 3"
                    @click="openPublishDialog(item)">发布成绩
                  </el-link>
                  <el-link :underline="false" type="primary" class="report-btn" @click="lookReport(item)"
                    :disabled="item.dataState < 1">查看报告
                  </el-link>
                  <el-link :underline="false" type="primary" class="download-btn" @click="downLoadReport(item)"
                    :disabled="item.statState != 1">下载报告
                  </el-link>
                  <el-dropdown @command="handlerCommand" class="dropdown-menu">
                    <span class="el-dropdown-link">
                      <i slot="reference" class="el-icon-more"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :command="'addReport_' + index">新增报告
                      </el-dropdown-item>
                      <!-- <el-dropdown-item
                        :command="'editPaper_' + index"
                        v-show="item.source == 3"
                        >修改答卷
                      </el-dropdown-item> -->
                      <el-dropdown-item :command="'recognitionHandle_' + index" v-show="item.source == 3">识别处理
                      </el-dropdown-item>
                      <el-dropdown-item :command="'scoreConfirmDetail_' + index" v-show="item.scoreConfirm == 1">成绩确认详情
                      </el-dropdown-item>
                      <template v-if="$sessionSave.get('loginInfo') &&
                        ($sessionSave.get('loginInfo').user_type === 5 ||
                          $sessionSave.get('loginInfo').admin_type == 2)
                        ">
                        <el-dropdown-item :command="'createReport_' + index" v-if="item.source != 3">编辑
                        </el-dropdown-item>
                        <el-dropdown-item :command="'deleteItem_' + index">删除
                        </el-dropdown-item>
                      </template>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
              <!-- 试卷列表 -->
              <div v-show="item.isActive" class="uploadContent">
                <div class="uploadSubject" v-for="(ite, index) in item.paperList" :key="index">
                  <span>
                    <span style="width: 70px; display: inline-block">{{
                      ite.subectName
                    }}</span>
                    <span v-if="ite.isUploadWord == 0">
                      <span class="uploadingState" @click="toUploadWord(ite, item)">
                        上传试卷
                      </span>
                    </span>
                    <span v-else style="width: 355px; display: inline-block">
                      <span class="uploadingState" @click="createPaper(ite, item)">
                        {{
                          ite.state == 2 && ite.isUse
                          ? "查看试卷"
                          : ite.state == 2
                            ? "去制卷"
                            : "解析中"
                        }}
                      </span>
                      <span style="color: black; margin: 0 20px 0 10px">|</span>
                      <span class="uploadAgain" :class="{ banClick: ite.processState != 0 }"
                        @click="toUploadWord(ite, item, 'again')">重新上传</span>
                      <span style="color: #000000; margin-left: 20px">-------</span>
                      <span class="sendProcess" @click="processHandle(ite, item)" :class="{
                        allowClick:
                          (ite.isExamUse == 1 &&
                            ite.processState == 0 &&
                            ite.state == 2 &&
                            ite.isUse) ||
                          (ite.isExamUse == 0 &&
                            ite.processState == 0 &&
                            ite.isUse &&
                            ite.isTeachingSchedule &&
                            (ite.checkTeacherId != '' || !ite.needAudit)),
                      }">
                        <span>
                          {{
                            (ite.isExamUse == 1 && ite.workingState == 2) ||
                            ite.processState > 1
                            ? "加工完成"
                            : ite.processState == 0
                              ? "发送加工"
                              : ite.processState == 1
                                ? "加工中"
                                : " "
                          }}
                        </span>
                      </span>
                    </span>
                    <span class="uploadingState" @click="toExport(ite, item)" style="margin-left: 20px">{{
                      ite.importScoreState == 0 ? "导入成绩" : "重新导入"
                    }}</span>
                    <span class="sendProcess" :class="{
                        allowClick:
                          ((ite.isExamUse == 1 && ite.workingState == 2) ||
                            ite.processState > 1) &&
                          ite.importScoreState == 1,
                      }" @click="toMatchQuesNum(ite, item)" style="margin-left: 20px">{{
    ite.bindQueState == 1 ? "重新匹配" : "题号匹配"
  }}</span>
                  </span>
                </div>
              </div>
            </li>
          </ul>
          <!--没有个册数据缺省图-->
          <!-- <div class="nodata flex_1" v-else></div> -->
          <div class="nodata flex_1" v-if="!listLoading && !examReportList.length">
            <img :src="noResImg" alt="" />
            <p class="text-center">暂无数据</p>
          </div>
          <!--分页器-->
          <el-pagination background style="margin-bottom: 30px" :hide-on-single-page="!examReportList.length"
            class="text-center" layout="total, prev, pager, next" @current-change="handleCurrentChange"
            :current-page.sync="pagination.page" :page-size="pagination.limit" :total="pagination.total_rows">
          </el-pagination>
        </div>
      </div>
    </div>

    <!--添加个册弹框-->
    <crete-report-dialog :editObj="editObj" :dict="dict" @closeDialog="closeDialog" @updateData="getExamReportList"
      v-if="createReportDialogVisible"></crete-report-dialog>
    <!--添加个册弹框-->
    <look-report :reportInfo="reportInfo" @closeDialog="closeDialog" @lookReport1="lookReport1"
      v-if="lookReportDialogVisible"></look-report>
    <!--上传word-->
    <upload-word ref="uploadWord" :formVisible="uploadWordShow" :formDataParam="uploadWordInfo" :subjectInfo="subjectInfo"
      :editParamsData="editWordInfo" @closeDialog="closeUpload"></upload-word>
    <!--上传word-->

    <!--导入成绩-->
    <upload-excel ref="uploadWord" v-if="uploadExcelShow" :formVisible="uploadExcelShow" :excellData="excellData"
      :processState="curProcessState" @closeDialog="closeExcelUpload"></upload-excel>
    <!--导入成绩-->
    <!--下载报告-->
    <download-report ref="downloadreport" v-if="isShowDownReport" @cloase-dialog="closeDownload"></download-report>
    <!--下载报告-->
    <!-- 导入成绩弹框 -->
    <exportXlsx ref="exportXlsx" :examId="examInfo.examId" :year="importGrades.year" :phase="importGrades.phaseId - 2"
      :subjectIds="examInfo.subjectIds" type="single" @closeDialog="dlgExportXlsxVisible = false"
      v-if="dlgExportXlsxVisible"></exportXlsx>
    <!-- 发布成绩弹窗 -->
    <publish-score-dialog v-if="isPublishScoreDialog" :examInfo="examInfo" @close-publish="closePublish"
      @publish-score="publishScore"></publish-score-dialog>
  </div>
</template>

<script>
import { selectAllType, saveBaseRelation } from "../service/pbook";
import {
  getExamReportList,
  editWhether,
  deleteExamInfo,
  getDefaultData,
  verifyWordFile,
  getImportInfo,
  getExamInfoAPI,
} from "../service/pexam";
import creteReportDialog from "@/components/createReport.vue";
import lookReport from "@/components/lookReport.vue";
import UploadExcel from "@/components/uploadExcel.vue";
import {
  findTestBanksData,
  getUserInfoToPersonalityTest,
  getUserRoles,
  getGradeListByRole,
  getSubjectListByRole,
  getTeacherClassList,
  getUserRoleAPI,
  classList,
  getUserInfoAPI,
  publishScoreAPI,
  getScanPaperWorkStatAPI,
} from "../service/api";
import UploadWord from "@/components/uploadWord.vue";
import DownloadReport from "@/components/downloadReport.vue";
import ExportXlsx from "@/components/exportXlsx";
import { uniqueFunc, reviseListMap } from "@/utils/common";
import PublishScoreDialog from "@/components/PublishScoreDialog.vue";
import { getToken } from "@/service/auth";
import { Loading } from "@iclass/element-ui";
import UserRole from "@/utils/UserRole";

var now = "";
export default {
  name: "exam-report",
  data() {
    return {
      reportInfo: "",
      lookReportDialogVisible: false,
      isFirst: false,
      noResImg: require("../assets/no-res.png"),
      // 学年列表
      years: [],
      yearValue: "",
      // 学期列表
      terms: [
        { id: "", name: "全部学期" },
        { id: 110, name: "第一学期" },
        { id: 111, name: "第二学期" },
      ],
      termValue: "",
      // 年级列表
      grdList: [],
      gradeValue: "",

      subjectList: [],
      // 选中的学科索引
      subjectValue: "",
      // 类别列表
      typeList: [],
      // 选中的类别索引
      typeValue: "",
      // 搜索框值
      searchValue: "",
      // 考试报告列表
      examReportList: [],
      // 分页
      pagination: {
        page: 1,
        limit: 10,
        total_rows: 0,
      },
      // 加载中
      listLoading: false,
      // 是否显示创建报告弹窗
      createReportDialogVisible: false,
      // 筛选项
      dict: {
        grdList: [],
        subjectList: [],
        typeList: [],
        years: [],
        terms: [],
      },
      // 编辑的考试报告数据
      editObj: {},
      // 是否显示导入成绩弹窗
      uploadExcelShow: false,
      curProcessState: 0,
      excellData: {},
      isShowDetail: false,
      paperList: [],
      uploadWordInfo: {},
      subjectInfo: {},
      uploadWordShow: false,
      editWordInfo: {},
      isUploadAgain: false,
      fsUrl: process.env.VUE_APP_FS_URL,
      // 是否显示下载报告弹窗
      isShowDownReport: false,
      //导入成绩弹框
      dlgExportXlsxVisible: false,
      importGrades: {},
      examInfo: {},
      //角色列表
      roleList: [],
      roles: "",
      roleTypes: "",
      //年级信息
      gradeInfo: {},
      //角色下的班级
      roleClassList: [],
      //角色下的学科
      roleSubjectList: [],
      accountType: "",
      //查看详情是否打开
      isActive: "",
      //发布成绩确认弹窗
      isPublishScoreDialog: false,
      //刷新按钮加载
      refreshLoading: false,
      cardUrl: process.env.VUE_APP_CARDURL,
      //加载中状态
      scoreLoading: null,
    };
  },
  components: {
    creteReportDialog,
    UploadExcel,
    UploadWord,
    DownloadReport,
    ExportXlsx,
    lookReport,
    PublishScoreDialog,
  },
  mounted() {
    //删除未保存的报告
    this.deleteUnsavedReport();
    this.isFirst = true;
    if (this.years.length > 0) return;
    let date = new Date(),
      y = date.getFullYear(),
      M = date.getMonth(),
      d = date.getDate();
    for (let i = 0; i < 5; i++) {
      let y1 = y - i - 1;
      let y2 = y - i;
      if (Number(`${M + 1}${d < 10 ? `0${d}` : d}`) >= 810) {
        y1 = y - i;
        y2 = y - i + 1;
      }
      this.years.push({
        id: `${Number(y1.toString().substring(1))}${Number(
          y2.toString().substring(1)
        )}`,
        name: `${y1}-${y2}学年`,
      });
    }
    this.years.unshift({ id: "", name: "全部学年" });
    console.log("mounted");
    this.getUserRoles();
    this.getUserInfo();
    this.initSchool(function () {
      let year = this.$sessionSave.get("lastYear");
      console.log(year);
      this.getDefaultData();
      this.selectAllType();
      this.getExamReportList();
    });
    let loginInfo = this.$sessionSave.get("loginInfo");
    this.accountType = loginInfo.account_type;
  },
  activated() {
    if (this.isFirst) {
      this.isFirst = true;
      return;
    }
    console.log("activated");
    this.initSchool(function () {
      this.getExamReportList();
    });
  },
  methods: {
    //删除未保存的报告
    deleteUnsavedReport() {
      if (this.$sessionSave.get("customReportInfo")) {
        let reportInfo = this.$sessionSave.get("customReportInfo");
        deleteExamInfo({
          examId: reportInfo.examId,
        })
          .then((data) => {
            this.$message({
              message: "未保存的报告已删除！",
              type: "success",
              duration: 2000,
            });
            this.$sessionSave.remove("customReportInfo");
          })
          .catch((err) => {
          });
      }
    },

    // 初始化学校后
    async initSchool(cb) {
      if (now) {
        if (new Date().getTime() - now.getTime() < 1500) {
          return;
        }
      } else {
        now = new Date();
      }

      let $this = this;
      if ($this.subjectList.length > 0) {
        if (cb) {
          cb.call($this);
        }
        return;
      }

      const ret = await UserRole.getUserInfoPersonalityTest();
      if ($this.accountType == 4 || $this.accountType == 5) {
        $this.grdList = [];
      }
      $this.subjectList = [];
      if ($this.accountType == 4 || $this.accountType == 5) {
        ret.userGrdList.forEach((v, i) => {
          v.show = true;
          $this.grdList.push(v);
        });
        $this.grdList.unshift({
          id: "",
          name: "全部",
        });
      }

      ret.userSubList.forEach((v, i) => {
        v.show = true;
        $this.subjectList.push(v);
      });

      $this.subjectList.unshift({
        id: "",
        name: "全部",
      });

      if (cb) {
        cb.call($this);
      }
    },
    getUserInfo() {
      getUserInfoAPI({
        userId: this.$sessionSave.get("loginInfo").id,
      })
        .then((res) => {
          let subClass = res.data.substituteClassList;
          let subjectClz = reviseListMap(subClass, "subjectName", "className");
          this.$sessionSave.set("subjectClz", subjectClz);
        })
        .catch((err) => { });
    },
    /**
     * @name:获取用户角色列表
     */
    getUserRoles() {
      getUserRoles({
        schoolId: this.$sessionSave.get("schoolInfo").id,
        userId: this.$sessionSave.get("loginInfo").id,
      }).then((res) => {
        // 0总管理员 1校管 2年级组长 3学科组长 4备课组长 5班主任 6普通老师
        this.roleTypes = res.data.types;
        this.$sessionSave.set("roleTypes", res.data.types);

        let rolesStatus = res.data.status.split(",");
        let roles = res.data.types.split(",");
        rolesStatus.forEach((item, index) => {
          this.roleList.push({
            types: roles[index],
            status: item,
          });
        });
        this.roles = this.roleList[0].types;
        this.getRoleClass();
        this.getRoleSubject();
        // 1：普通教师 2：备课组长 3：年级组长 4：校长 5：运营
        if (this.accountType != 4 && this.accountType != 5) {
          this.getGradeList();
        }
      });
    },
    /**
     * @name:获取教师任课班级包含班主任班级
     */
    getRoleClass() {
      getTeacherClassList({
        userId: this.$sessionSave.get("loginInfo").id,
        containLeaderClass: 1,
      })
        .then((res) => {
          let classList = [];
          res.data.fcList.length &&
            res.data.fcList.forEach((item) => {
              this.roleClassList.push(item);
              classList.push(item);
            });
          res.data.xzList.length &&
            res.data.xzList.forEach((item) => {
              this.roleClassList.push(item);
              classList.push(item);
            });
          let leaderClass = classList.filter((ite) => {
            return ite.leader_class;
          });
          this.$sessionSave.set("leaderClass", leaderClass);
          if (this.roleTypes.indexOf("4") != -1) {
            this.getPreparationInfo();
          }
          this.getExamClassList();
        })
        .catch((err) => { });
    },
    /**
     * @name:获取班级
     */
    async getExamClassList() {
      setTimeout(() => {
        this.roleClassList = uniqueFunc(this.roleClassList, "id");
        this.$sessionSave.set("roleClassList", this.roleClassList);
      }, 300);
    },
    /**
     * @name:获取备课组长信息
     */
    getPreparationInfo() {
      getUserRoleAPI({
        userId: this.$sessionSave.get("loginInfo").id,
        roleType: 2,
      })
        .then((res) => {
          this.gradeInfo = res.data[0];
          this.getGradeClassList(this.gradeInfo.year, this.gradeInfo.phase);
        })
        .catch((err) => { });
    },
    /**
     * @name:获取年级下的班级
     */
    getGradeClassList(year, phase) {
      classList(year, phase)
        .then((data) => {
          data.data.rows.forEach((item) => {
            this.roleClassList.push(item);
          });
        })
        .catch((err) => { });
    },
    /**
     *
     * @name:根据用户角色获取学科
     */
    getRoleSubject() {
      let list = [];
      this.roleList.forEach((item) => {
        getSubjectListByRole({
          schoolId: this.$sessionSave.get("schoolInfo").id,
          userId: this.$sessionSave.get("loginInfo").id,
          userType: item.types,
        })
          .then((res) => {
            res.data.forEach((ite) => {
              list.push(ite);
            });
            if (item.types == "4") {
              //获取备课组长学科
              this.$sessionSave.set("mainSubjectList", res.data);
            }
          })
          .catch((err) => { });
      });
      setTimeout(() => {
        this.roleSubjectList = uniqueFunc(list, "subjectId");
        this.$sessionSave.set("roleSubjectList", this.roleSubjectList);
      }, 500);
    },
    /**
     * @name:获取角色对应年级
     */
    getGradeList() {
      getGradeListByRole({
        schoolId: this.$sessionSave.get("schoolInfo").id,
        userId: this.$sessionSave.get("loginInfo").id,
        userType: this.roles,
      })
        .then((res) => {
          res.data.forEach((item) => {
            this.$set(item, "id", item.gradeId);
            this.$set(item, "name", item.gradeName);
          });
          this.grdList = res.data;
          if (this.roles == 1) {
            this.grdList.unshift({
              id: "",
              name: "全部",
            });
          }

          this.gradeValue = this.grdList[0].id;
        })
        .catch((err) => {
          this.grdList = [];
        });
    },
    changeRoles() {
      this.gradeValue = "";
      this.getGradeList();
    },
    // 切换学校
    changeSchool() {
      this.subjectList = [];
      this.searchValue = "";
      this.gradeValue = "";
      this.subjectValue = "";
      this.typeValue = "";
      this.initSchool(function () {
        this.getExamReportList();
      });
    },
    // 创建、编辑报告
    createReport(item) {
      this.dict = {
        grdList: this.grdList.filter((q) => q.id != ""),
        subjectList: this.subjectList.filter((q) => q.id != ""),
        typeList: this.typeList.filter((q) => q.id != ""),
        years: this.years.filter((q) => q.id != ""),
        terms: this.terms.filter((q) => q.id != ""),
      };

      if (item.examId) {
        this.cancelPopover();
        // 编辑
        this.createReportDialogVisible = true;
        this.editObj = item;
        // editWhether({
        //   examId: item.examId,
        // })
        //   .then((data) => {
        //     this.createReportDialogVisible = true;
        //     this.editObj = item;
        //   })
        //   .catch((err) => {
        //     if (err.code === -1) {
        //       this.$message.error(err.msg);
        //     }
        //   });
      } else {
        // 创建
        this.createReportDialogVisible = true;
        this.editObj = "";
      }
    },
    // 删除报告
    deleteItem(item) {
      this.$confirm(
        "将删除本次考试的全部相关数据，删除后无法恢复，确定删除吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.cancelPopover();
          deleteExamInfo({
            examId: item.examId,
          })
            .then((data) => {
              this.$message({
                message: "删除成功！",
                type: "success",
                duration: 2000,
              });
              this.getExamReportList();
            })
            .catch((err) => {
            });
        })
        .catch(() => {
          this.cancelPopover();
        });
    },
    // 切换年级，联动显示学科
    changeGrade(data) {
      if (data == "") {
        this.subjectList.forEach((v) => {
          v.show = true;
        });
      } else {
        let grd = this.grdList.find((q) => q.id == data);

        this.subjectList.forEach((v) => {
          v.show = false;
          if (v.id == "" || v.phaseId == grd.phase + 2) {
            v.show = true;
          }
        });
      }

      this.subjectValue = "";

      this.getExamReportList();
    },
    // 获取类别列表
    selectAllType() {
      selectAllType().then((res) => {
        this.typeList = res.data;
        this.typeList.unshift({
          id: "",
          name: "全部",
        });
      });
    },
    // 切换学年
    changeYear(val) {
      this.$sessionSave.set("lastYear", val);
      this.getExamReportList();
    },
    // 切换学期
    changeTerm(val) {
      this.$sessionSave.set("lastTerm", val);
      this.getExamReportList();
    },
    // 搜索
    searchReport() {
      this.pagination.page = 1;
      this.getExamReportList();
    },
    refreshReport() {
      this.refreshLoading = true;
      this.pagination.page = 1;
      this.getExamReportList();
    },
    // 获取考试报告列表
    getExamReportList() {
      // this.pagination.page = isChangePage ? this.pagination.page : 1;
      this.listLoading = true;
      getExamReportList({
        schoolId: this.$sessionSave.get("schoolInfo").id,
        acadYearsId: this.yearValue,
        acadTermId: this.termValue,
        gradeId: this.gradeValue || "",
        subjectId: this.subjectValue || "",
        categoryId: this.typeValue || "",
        keyWord: this.searchValue.trim(),
        page: this.pagination.page, // 页码
        pageSize: this.pagination.limit,
      })
        .then((res) => {
          let testBankIds = [];
          this.examReportList = res.data.list;
          this.examReportList.forEach((item) => {
            item.isActive = this.isActive == item.examId ? true : false;
            item.paperList.forEach((it) => {
              if (it.testBankId != "") {
                testBankIds.push(it.testBankId);
              }
            });
            if (item.dataState >= 1 && item.statState != 1)
              this.getInitData(item.examId);
          });
          this.pagination.total_rows = res.data.total;
          this.listLoading = false;
          this.refreshLoading = false;

          this.findTestBanksData(testBankIds.join(","));
        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    /**
     * @name:初始化报告下载状态
     */
    getInitData(id) {
      // getCriticalConfAPI({examId:id})
      // getOnlineAPI({examId:id})
    },
    // 获取已上传word数据的解析状态和制卷状态
    findTestBanksData(testBankIds) {
      findTestBanksData({
        testBankIds: testBankIds,
      })
        .then((data) => {
          this.listLoading = false;
          this.testBankList = data.data;
          this.examReportList.forEach((item) => {
            item.paperList.forEach((ite) => {
              this.testBankList.forEach((bankItem) => {
                if (bankItem.id == ite.testBankId) {
                  this.$set(ite, "isUse", bankItem.isUse);
                  this.$set(ite, "state", bankItem.state);
                  this.$set(ite, "phase", bankItem.phase);
                  this.$set(ite, "qc", bankItem.qcount);
                }
              });
            });
          });
        })
        .catch((res) => {
          this.listLoading = false;
        });
    },
    // 切换筛选项
    changeFilter(type, v) {
      switch (type) {
        case "subject":
          this.subjectValue = v;
          break;
        default:
          this.typeValue = v;
          break;
      }
      this.getExamReportList();
    },
    // 分页查询
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getExamReportList("changePage");
    },
    closeDialog() {
      this.createReportDialogVisible = false;
      this.lookReportDialogVisible = false;
    },
    // 导入成绩弹框
    exportGrade(item) {
      this.examInfo.examId = item.examId;
      this.examInfo.subjectIds = item.subjectId.split(",");
      getImportInfo({ examId: item.examId })
        .then((data) => {
          this.importGrades = this.grdList.find(
            (q) => q.id == data.data.gradeId
          );
          this.dlgExportXlsxVisible = true;
        })
        .catch((err) => {
        });
    },
    /**
     * @name：更多操作
     */
    handlerCommand(command) {
      let commands = command.split("_");
      this[commands[0]](this.examReportList[commands[1]]);
    },
    //同步数据
    syncData(item) {
      window.open(`/bigdata/importExcel?examId=${item.examId}`, "_blank");
    },
    // 关闭导入成绩弹窗
    closeExcelUpload(success) {
      this.uploadExcelShow = false;
      if (success) {
        this.getExamReportList(true);
      }
    },
    // 查看报告
    async lookReport(item) {
      let params = {
        schoolId: this.$sessionSave.get("schoolInfo").id,
        parentId: item.examId,
        acadYearsId: "",
        acadTermId: "",
        gradeId: "",
        subjectId: "",
        categoryId: "",
        keyWord: "",
        page: 1, // 页码
        pageSize: 10,
      };
      const res = await getExamReportList(params);

      if (res.code == 1) {
        if (res.data.list.length == 0) {
          //无新增的报告
          this.lookReport1(item);
        } else {
          this.lookReportDialogVisible = true;
          this.reportInfo = item;
        }
      } else {
        this.$message({
          message: "查看报告失败！",
          type: "error",
          duration: 1000,
        });
      }
    },
    lookReport1(item) {
      this.$sessionSave.set("innerClassList", null);
      this.$sessionSave.set("innerSubjectList", null);

      this.$sessionSave.set("loadComment", true);
      this.$sessionSave.set("reportDetail", item);
      let dt = new Date(item.dateModified).getTime();
      this.$sessionSave.set("downLoadState", item.statState);
      this.$router.push({ path: "/home/<USER>" });
    },
    /**
     * @name:打开发布成绩弹窗
     */
    openPublishDialog(item) {
      this.isPublishScoreDialog = true;
      this.examInfo.examId = item.examId;
      this.examInfo.examName = item.examName;
    },
    /**
     * @name:发布成绩
     */
    publishScore() {
      this.isPublishScoreDialog = false;
      // this.scoreLoading = Loading.service({
      //   lock: true,
      //   text: "正在生成分析报告，请稍候，预计5-20s，若长时间未生成，请刷新页面重试",
      //   background: "rgba(0, 0, 0, 0.7)",
      // });
      publishScoreAPI({
        schoolId: this.$sessionSave.get("schoolInfo").id,
        examId: this.examInfo.examId,
      })
        .then((res) => {
          // this.$message({
          //   message: "发布成功！",
          //   type: "success",
          //   duration: 1000,
          // });
          // this.getExamReportList();
          this.scoreLoading = Loading.service({
            lock: true,
            text: "正在生成分析报告，请稍候，预计5-20s，若长时间未生成，请刷新页面重试",
            background: "rgba(0, 0, 0, 0.7)",
          });
          this.checkingScoreStatus();
        })
        .catch((err) => {
        });
    },
    checkingScoreStatus() {
      setTimeout(async () => {
        let params = {
          examId: this.examInfo.examId,
        };
        const res = await getExamInfoAPI(params);
        if (res.code == 1) {
          if (res.data.dataState >= 1) {
            this.scoreLoading.close();
            this.getExamReportList();
          } else {
            this.checkingScoreStatus();
          }
        } else {
          this.scoreLoading.close();
          this.$message({
            message: "发布失败！",
            type: "error",
            duration: 1000,
          });
        }
      }, 5000);
    },
    /**
     * @name:关闭发布成绩弹窗
     */
    closePublish() {
      this.isPublishScoreDialog = false;
    },
    //新增报告
    addReport(item) {
      this.$sessionSave.remove("customReportInfo");
      this.$sessionSave.set("innerClassList", null);
      this.$sessionSave.set("innerSubjectList", null);
      this.$sessionSave.set("reportDetail", item);
      this.$router.push({ path: "/home/<USER>" }).catch((err) => {
        console.error(err);
      });
    },
    //识别处理
    recognitionHandle(item) {
      this.$router.push({
        path: "/scan/errornew",
        query: {
          examId: item.yqExamId,
          examName: item.examName,
          paperNo: item.paperNo,
          schoolId: this.$sessionSave.get("schoolInfo").id,
        },
      });
    },
    editPaper(item) {
      let token = getToken();
      let routeData =
        this.cardUrl +
        `?id=${item.yqExamId}&examName=${item.examName}&token=${token}`;
      window.open(routeData, "_blank");
    },
    // 下载报告
    downLoadReport(item) {
      this.$sessionSave.set("reportDetail", item);
      this.isShowDownReport = true;
    },
    /**
     * @name:查看成绩确认详情
     */
    scoreConfirmDetail(item) {
      this.$sessionSave.set("reportDetail", item);
      this.$sessionSave.set("downLoadState", item.statState);
      this.$router.push({ path: "/home/<USER>" });
    },
    /**
     * @name:关闭下载报告弹窗
     */
    closeDownload() {
      this.isShowDownReport = false;
    },
    // 更新列表map信息
    updateListmap() {
      this.gradeMap = new Map(this.gradeMap);
    },
    cancelPopover() {
      document.getElementById("popoverUl").click();
    },
    // 获取默认值
    getDefaultData() {
      getDefaultData().then((data) => {
        this.$sessionSave.set("defaultList", data.data);
      });
    },
    showUpload(ite, ind) {
      //  this.verifyWordFile(ite)
      this.examReportList.forEach((item, index) => {
        if (index === ind) {
          let obj = { ...item };
          obj.isActive = !item.isActive;
          if (obj.isActive) {
            this.isActive = item.examId;
          } else {
            this.isActive = "";
          }
          console.log(this.isActive, item);
          this.$set(this.examReportList, index, { ...obj });
        } else {
          let obj = { ...item };
          obj.isActive = false;
          this.$set(this.examReportList, index, { ...obj });
        }
      });
    },
    // 去制卷
    createPaper(ite, item) {
      let loginInfo = this.$sessionSave.get("loginInfo");
      if (ite.state !== 2) {
        this.$message({
          message: "请刷新页面！",
          type: "warning",
        });
        return;
      }
      if (ite.state == 2 && ite.isUse) {
        this.$emit("updateTab", -1);
        this.$router.push({
          path: "/home/<USER>",
          query: {
            testBankId: ite.testBankId,
            testBankName: item.examName,
            subjectId: ite.subectId,
          },
        });
        return;
      }

      let routeData = `http://webtest.iclass30.com:6161/cutTestPaper/matchQuesNew.html?id=${ite.testBankId}&userid=${loginInfo.id}&url=${process.env.VUE_APP_CUT_URL}&subject=${ite.subectId}&phase=${ite.phase}&token=${loginInfo.token}`;
      window.open(routeData, "_blank");
      this.$confirm("是否制卷完成，刷新数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.getExamReportList();
        })
        .catch((err) => {
          console.log(err);
        });
    },
    //上传试卷
    toUploadWord(ite, item, isUploadAgain) {
      item.phase = this.setPhase(item.gradeName);
      this.isUploadAgain = isUploadAgain;
      this.uploadWordInfo = item;
      this.subjectInfo = ite;
      this.uploadWordShow = true;
    },
    //单科导入
    toExport(ite, item) {
      window.open(
        `/bigdata/importExcel?examId=${item.examId}&subjectId=${ite.subectId
        }&qc=${ite.qc ?? ""}`,
        "_blank"
      );
    },
    //跳转到题号匹配
    toMatchQuesNum(ite, item) {
      this.$router.push({
        path: "/home/<USER>",
        query: {
          personBookId: ite.personBookId,
          examName: item.examName,
          examId: item.examId,
          subjectId: ite.subectId,
          bindQueState: ite.bindQueState,
        },
      });
    },
    //计算学段
    setPhase(data) {
      let number = "";
      if (data.includes("高")) {
        number = "3";
      } else if (
        data.includes("七") ||
        data.includes("八") ||
        data.includes("九")
      ) {
        number = "2";
      } else {
        number = "1";
      }
      return number;
    },
    // 点击发送加工或者显示加工进度
    processHandle(ite, item) {
      if (ite.isExamUse == 1) {
        this.$confirm(
          "未关联个册，发送加工后将不能关联个册，是否发送加工?",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            this.sendProcess(ite);
          })
          .catch(() => { });
      } else {
        this.$confirm("已关联个册，是否直接发送加工?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.sendProcess(ite);
          })
          .catch(() => { });
      }
    },
    // 发送加工
    sendProcess(ite) {
      saveBaseRelation({
        personBookId: ite.personBookId,
        testBankId: ite.testBankId,
        schoolId: this.$sessionSave.get("schoolInfo").id,
      })
        .then((data) => {
          this.$message({
            message: "发送成功！",
            type: "success",
            duration: 2000,
          });
          this.getExamReportList();
        })
        .catch((err) => {
        });
    },
    // 关闭上传word弹窗
    closeUpload() {
      this.uploadWordShow = false;
      this.getExamReportList();
    },
    verifyWordFile(ite) {
      verifyWordFile({
        subjectId: ite.subjectId,
        examId: ite.examId,
      })
        .then((data) => {
          console.log("0000", data.data);
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.examReport {
  padding-right: 50px;
  overflow-y: auto;
  height: 100%;

  .examReport__header {
    font-size: 14px;

    .header__select {
      display: inline-block;
      margin-right: 29px;
      float: left;

      .role-select,
      .year-select,
      .term-select,
      .grade-select {
        width: 150px;
      }
    }

    .header__serarch {
      display: flex;
      width: 240px;

      .search__icon {
        width: 38px;
        font-size: 18px;
        color: #fff;
        background: #409eff;
        /*background: #0dc2b3 url(../../static/img/pub-icon_reportlist.cfdb858.png) 9px 7px no-repeat;*/
        border-radius: 0 3px 3px 0;
        outline: none;
        cursor: pointer;
      }

      .search__text {}
    }
  }

  .examReport__main {
    width: 100%;
    margin-top: 20px;
    padding: 10px 20px 20px;
    background-color: #fff;
    font-size: 16px;
    overflow: auto;

    .header__class {
      margin: 10px 0;

      .class__li {
        display: inline-block;
        margin-right: 15px;
        padding: 0 8px;
        border-radius: 3px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;

        &.active,
        &:hover {
          color: #409eff;
        }
      }

      .leftText {
        width: auto;
      }

      .rightText {
        width: auto;
      }

      &.category {
        border-bottom: 1px solid #e4e8eb;
        margin-bottom: 20px;
        padding-bottom: 8px;
      }
    }

    .examReport__content {
      width: 100%;

      .examReport__list {
        width: 100%;

        .examReport__item {
          width: 100%;
          height: 120px;
          box-sizing: border-box;
          background: #fff;
          padding: 16px 43px 16px 26px;
          border: 1px solid #e4e7eb;
          box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.15);
          border-radius: 6px;
          margin-bottom: 30px;
          color: #222;
          font-size: 16px;
          position: relative;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;

          .square {
            width: 70px;
            border-top: 24px solid #409eff;
            z-index: 100;
            position: absolute;
            left: 0;
            top: 0;
            border-radius: 6px 0 0;

            .square-word {
              position: absolute;
              left: 8px;
              top: -23px;
              color: #ffffff;
              font-size: 15px;
            }
          }

          .exam-name-area {
            .exam-name {
              display: flex;
              flex-direction: row;
              align-items: center;
              font-size: 20px;
              color: #3f4a54;
              line-height: 21px;

              .exam-category {
                margin-right: 15px;
                border: 1px solid #e4e8eb;
                background-color: #f7fafc;
                min-width: 80px;
                padding: 5px 20px;
                height: 30px;
                border-radius: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
              }
            }

            .exam-detail {
              margin-top: 15px;

              .line-gap {
                width: 32px;
                position: relative;

                &:before {
                  content: "";
                  position: absolute;
                  left: 50%;
                  margin-left: -1px;
                  top: 50%;
                  margin-top: -7px;
                  display: inline-block;
                  width: 2px;
                  height: 14px;
                  background-color: #97a0a8;
                }
              }

              >span {
                display: inline-block;
                font-size: 16px;
                color: #3f4a54;
              }
            }
          }

          .upload-btn {
            display: inline-block;
            width: 130px;
            height: 42px;
            font-size: 16px;
            margin-left: 25px;
            cursor: pointer;

            &.is-disabled {
              border-color: #80c6f5;
            }
          }

          .line {
            display: inline-block;
            height: 42px;
            font-size: 16px;
            color: #000000;
            pointer-events: none;
            cursor: pointer;
          }

          .score-btn {
            display: inline-block;
            width: 80px;
            height: 42px;
            font-size: 16px;
            margin-left: 25px;
            cursor: pointer;

            // margin-right: -20px;
            &.is-disabled {
              border-color: #b8b8b8;
              pointer-events: none;
              cursor: pointer;
            }
          }

          .report-btn,
          .download-btn {
            display: inline-block;
            // width: 120px;
            // height: 42px;
            margin-bottom: 20px;
            font-size: 16px;
            margin-left: 25px;
            cursor: pointer;

            // margin-right: -60px;
            &.is-disabled {
              color: #b8b8b8;
              pointer-events: none;
              cursor: pointer;
            }
          }

          .dropdown-menu {
            margin-left: 15px;
            font-size: 16px;
          }
        }
      }

      .uploadContent {
        width: 97%;
        box-sizing: border-box;
        background: #f7fafc;
        padding: 16px 43px 16px 26px;
        border: 1px solid #e4e7eb;
        border-top: unset;
        // box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.15);
        border-radius: 6px;
        color: #222;
        font-size: 16px;
        margin: -30px 10px 20px 20px;
      }

      .uploadSubject {
        margin: 10px 0px 10px 0px;
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 30px;
        // background-color: #008dea;
        font-weight: bold;
        color: #3f4a54;
      }

      .uploadingState {
        display: inline-block;
        width: 70px;
        color: #409eff;
        cursor: pointer;
        font-weight: 400;
      }

      .sendProcess {
        margin-left: 30px;
        color: #b8b8b8;
        pointer-events: none;
        cursor: pointer;
        font-weight: 400;
      }

      .sendProcess.allowClick {
        margin-left: 30px;
        color: #409eff;
        pointer-events: auto;
        cursor: pointer;
      }

      .uploadAgain {
        color: #409eff;
        pointer-events: auto;
        cursor: pointer;
        font-weight: 400;
      }

      .uploadAgain.banClick {
        color: #b8b8b8;
        pointer-events: none;
        cursor: pointer;
      }

      .nodata {
        width: 100%;
        height: auto;

        /*background : url("../assets/no-res.png") center center no-repeat;*/
        img {
          display: block;
          margin: 0 auto;
        }
      }
    }
  }
}

.search-button {
  position: absolute;
  right: 150px;
}

.refresh-button {
  position: absolute;
  right: 50px;
}

.pull-right {
  position: absolute;
  right: 150px;
}
</style>
<style lang="scss">
.search__text {
  .el-input__inner {
    border-right: none;
    border-radius: 3px 0 0 3px;
  }
}

.el-divider--vertical {
  display: inline-block;
  width: 1px;
  height: 100%; //更改竖向分割线长度
  margin: 0 8px;
  vertical-align: middle;
  position: absolute;
  top: 0;
}

.moreEdit {
  width: 63px;
  display: inline-block;
  height: 30px;
  margin-top: 13px;
  cursor: pointer;
  color: #008dea;
  font-size: 18px;
  background: url("../assets/edit.png") center center no-repeat;
}

.more,
.editPopper {
  width: 100px !important;
  min-width: 100px !important;
  // height: 100px;
  padding: 10px 0;

  ul {
    li {
      width: 100px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      cursor: pointer;

      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}

.examReport__item {
  .el-icon-more {
    width: 40px;
    text-align: center;
    transform: rotate(90deg);
  }
}
</style>

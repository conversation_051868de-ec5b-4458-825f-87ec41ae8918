<template>
  <div class="set-main">
    <div class="set-header">
      <span class="set-type" :class="{ active: item.type == activeType }" v-for="item in list" :key="item.type"
        @click="changeType(item)">{{ item.name }}({{ item.num }})</span>
      <span style="margin-left: 20px" v-if="
        ((activeType == HANDLE_TYPE.pending && waitList.length > 0) ||
          (activeType == HANDLE_TYPE.processed && waitedList.length > 0)) &&
        !isQuesError
      ">
        <el-checkbox v-if="isbatch" :indeterminate="isIndeterminate" v-model="checkAll"
          @change="handleCheckAllChange">全选</el-checkbox>
        <span @click="batchDelete">
          <span v-if="!isbatch">{{ errorType == 'absent' ? '批量操作' : '批量删除' }}</span>
          <span v-else style="color: #409eff; margin-left: 20px">取消批量</span>
        </span>
        <span style="margin-left: 20px" v-if="!isbatch && (errorType == 'point' || errorType == 'paper')"
          @click="batchTransfer">批量转移</span>
      </span>
      <span style="margin-left: 20px" v-if="isQuesError && errorType != 'choice'">
        <span @click="batchQuesError">批量操作</span>
      </span>
    </div>
    <div class="ques-list">
      <!-- 待处理列表 -->
      <template v-if="activeType == HANDLE_TYPE.pending">
        <ul v-if="waitList.length > 0">
          <!-- 题目异常 -->
          <template v-if="isQuesError">
            <li class="ques-li" :class="{ active: item.quesNo == currentQuesNo && item.abPaper == currentQuesABPaper }"
              v-for="item in waitList" :key="item.quesNo" @click="changeQues(item)">
              <span><template v-if="item.abPaper != -1">{{ item.abPaper ? 'B卷' : 'A卷' }}-</template>{{ item.name }}{{
                item.quesName }}({{ item.num }})</span>
            </li>
          </template>
          <!-- 考号异常、答卷异常、页码异常-->
          <template v-else>
            <template v-if="!isbatch">
              <li class="ques-li" :class="{ active: item.id == currentQuesNo }" v-for="(item, index) in waitList"
                :key="item.id" @click="changeQues(item, index)">
                <p>
                  {{ item.stuNo || 'xxxxxxxx' }}
                  <span v-if="item.stuName && item.stuName != ''"> ({{ item.stuName }})</span><span v-if="item.isRepeat" style="color: #F56C6C">（重复）</span>
                </p>
                <p>
                  <span style="font-size: 12px">{{ item.batchId }}-第 {{ item.idx }} 张{{
                    isSingleImage ? '-' + page_names[item.idxNum] : ''
                  }}</span>
                </p>
              </li>
            </template>
            <!-- 批量操作 -->
            <template v-else>
              <el-checkbox-group v-model="checkedTask" @change="handleCheckedChange" class="custom-checkbox">
                <el-checkbox v-for="item in waitList" :key="item.id" :label="item">
                  <p>
                    {{ item.stuNo || 'xxxxxxxx' }}
                    <span v-if="item.stuName && item.stuName != ''"> ({{ item.stuName }})</span>
                  </p>
                  <p>
                    <span>{{ item.batchId }}-第 {{ item.idx }} 张</span>
                  </p>
                </el-checkbox>
              </el-checkbox-group>
            </template>
          </template>
        </ul>
        <no-data type="zan" v-else text="异常已全部处理，辛苦啦~"></no-data>
      </template>
      <!-- 已处理列表 -->
      <template v-else>
        <ul v-if="waitedList.length > 0">
          <!-- 考号异常已处理-->
          <template v-if="errorType == 'exam'">
            <template v-if="!isbatch">
              <li class="ques-li" :class="{ active: item.id == currentQuesNo }" v-for="(item, index) in waitedList"
                :key="item.id" @click="changeQues(item, index)">
                <p>
                  {{ item.stuNo || 'xxxxxxxx' }}
                  <span v-if="item.stuName && item.stuName != ''"> ({{ item.stuName }})</span>
                </p>
                <p>
                  <span style="font-size: 12px">{{ item.batchId }}-第 {{ item.idx }} 张{{
                    isSingleImage ? '-' + page_names[item.idxNum] : ''
                  }}</span>
                </p>
              </li>
            </template>
            <!-- 批量操作 -->
            <template v-else>
              <el-checkbox-group v-model="checkedTask" @change="handleCheckedChange" class="custom-checkbox">
                <el-checkbox v-for="item in waitedList" :key="item.id" :label="item">
                  <p>
                    {{ item.stuNo || 'xxxxxxxx' }}
                    <span v-if="item.stuName && item.stuName != ''"> ({{ item.stuName }})</span>
                  </p>
                  <p>
                    <span>{{ item.batchId }}-第 {{ item.idx }} 张</span>
                  </p>
                </el-checkbox>
              </el-checkbox-group>
            </template>
          </template>
          <!-- 缺考异常已处理 -->
          <template v-else-if="errorType == 'absent'">
            <template v-if="!isbatch">
              <li class="ques-li" :class="{ active: item.id == currentQuesNo }" v-for="(item, index) in waitedList"
                :key="item.id" @click="changeQues(item, index)">
                <p>
                  {{ item.stuNo || 'xxxxxxxx' }}
                  <span v-if="item.stuName && item.stuName != ''"> ({{ item.stuName }})</span>
                  <span style="color: #409eff">
                    {{ item.status == '5' ? '缺考' : item.status == '1' ? '非缺考' : '' }}</span>
                </p>
                <p>
                  <span style="font-size: 12px">{{ item.batchId }}-第 {{ item.idx }} 张{{
                    isSingleImage ? '-' + page_names[item.idxNum] : ''
                  }}</span>
                </p>
              </li>
            </template>
            <!-- 批量操作 -->
            <template v-else>
              <el-checkbox-group v-model="checkedTask" @change="handleCheckedChange" class="custom-checkbox">
                <el-checkbox v-for="item in waitedList" :key="item.id" :label="item">
                  <p>
                    {{ item.stuNo || 'xxxxxxxx' }}
                    <span v-if="item.stuName && item.stuName != ''"> ({{ item.stuName }})</span>
                    <span style="color: #409eff">
                      {{ item.status == '5' ? '缺考' : item.status == '1' ? '非缺考' : '' }}</span>
                  </p>
                  <p>
                    <span>{{ item.batchId }}-第 {{ item.idx }} 张</span>
                  </p>
                </el-checkbox>
              </el-checkbox-group>
            </template>
          </template>
          <!-- ab卷异常已处理 -->
          <template v-else-if="errorType == 'abCard'">
            <template v-if="!isbatch">
              <li class="ques-li" :class="{ active: item.id == currentQuesNo }" v-for="(item, index) in waitedList"
                :key="item.id" @click="changeQues(item, index)">
                <p>
                  {{ item.stuNo || 'xxxxxxxx' }}
                  <span v-if="item.stuName && item.stuName != ''"> ({{ item.stuName }})</span>
                </p>
                <p>
                  <span style="font-size: 12px">{{ item.batchId }}-第 {{ item.idx }} 张{{
                    isSingleImage ? '-' + page_names[item.idxNum] : ''
                  }}</span>
                </p>
              </li>
            </template>
            <!-- 批量操作 -->
            <template v-else>
              <el-checkbox-group v-model="checkedTask" @change="handleCheckedChange" class="custom-checkbox">
                <el-checkbox v-for="item in waitedList" :key="item.id" :label="item">
                  <p>
                    {{ item.stuNo || 'xxxxxxxx' }}
                    <span v-if="item.stuName && item.stuName != ''"> ({{ item.stuName }})</span>
                    <span style="color: #409eff">
                      {{ item.status == '5' ? '缺考' : item.status == '1' ? '非缺考' : '' }}</span>
                  </p>
                  <p>
                    <span>{{ item.batchId }}-第 {{ item.idx }} 张</span>
                  </p>
                </el-checkbox>
              </el-checkbox-group>
            </template>
          </template>
          <!-- 题目异常已处理 -->
          <template v-else>
            <li class="ques-li" :class="{ active: item.quesNo == currentQuesNo && item.abPaper == currentQuesABPaper }"
              v-for="item in waitedList" :key="item.quesNo + '_' + item.abPaper" @click="changeQues(item)">
              <span><template v-if="item.abPaper != -1">{{ item.abPaper ? 'B卷' : 'A卷' }}-</template>{{ item.name }}{{
                item.quesName }}({{ item.num }})</span>
            </li>
          </template>
        </ul>
        <no-data type="zan" v-else text="异常已全部处理，辛苦啦~"></no-data>
      </template>
    </div>
    <div class="set-footer" v-if="pagination.page_count > 0 && isQuesError">
      <div class="set-page">
        <el-button size="small" :disabled="pagination.page == 1" @click="handleCurrentChange('prev')">上一页</el-button>
        <span class="page-num"> {{ pagination.page }} </span>/<span class="page-num">
          {{ pagination.page_count }}
        </span>
        <el-button size="small" :disabled="pagination.page >= pagination.page_count"
          @click="handleCurrentChange('next')">下一页</el-button>
      </div>
      <div class="submit-button">
        <el-button type="primary" size="medium" @click="submitData">提交本页</el-button>
      </div>
    </div>
    <!-- 考号异常操作区 -->
    <div class="set-exam-footer" v-if="!isQuesError">
      <!-- 搜索学生区域 -->
      <template v-if="errorType == 'exam' || errorType == 'page'">
        <div v-if="isShowStuList" @mouseleave="mouseLeave" v-load-more.expand="{
          func: loadStuMore,
          target: '.search-stu-list',
          distance: 20,
          delay: 100,
        }">
          <div class="search-stu-list" v-if="stuList.length > 0">
            <ul>
              <li class="stu-li" v-for="item in stuList" :key="item.id" @click="submitExamNo(item)">
                <div>
                  <p style="margin: 5px 0px">
                    <span style="font-weight: bold">{{ item.realName }} {{ item.stuNumber }}</span>
                  </p>
                  <p>{{ item.className }}</p>
                </div>
                <div>
                  <el-button type="primary" size="mini" plain @click.stop="submitExamNo(item)">确定</el-button>
                </div>
              </li>
            </ul>
          </div>
          <div class="stu-no-data" v-if="stuList.length == 0 && queryInfo.keyWord != '' && isSearch">
            <p class="no-data-tips">未搜索到，可能原因如下:</p>
            <p>1. 输入信息与学生填写不一致；</p>
          </div>
        </div>
        <div class="operat-container" v-if="(activeType == HANDLE_TYPE.pending && waitList.length > 0) ||
          (activeType == HANDLE_TYPE.processed && waitedList.length > 0)">
          <el-input placeholder="请输入考号或学生姓名" v-model="queryInfo.keyWord" class="input-with-select"
            @keyup.enter.native="searchStu" @focus="focusInput">
            <el-select v-model="queryInfo.range" slot="prepend" placeholder="请选择" class="input-search-select">
              <el-option label="全校" value="scool"></el-option>
              <el-option label="本次" value="present"></el-option>
            </el-select>
            <el-button slot="append" @click="searchStu" :disabled="activeList.length == 0">搜索</el-button>
          </el-input>
        </div>
      </template>
      <div class="footer-button" v-if="
        (activeType == HANDLE_TYPE.pending && waitList.length > 0) || (activeType == HANDLE_TYPE.processed && waitedList.length > 0)
      ">
        <div class="button-container">
          <template v-if="errorType == 'absent'">
            <template v-if="!isbatch">
              <el-button type="primary" class="absent-button" @click="submitPageSetabsent()">
                提交本页
              </el-button>
            </template>
            <template v-else>
              <el-button type="warning" class="absent-button" @click="submitSetabsent(true)">
                提交为非缺考
              </el-button>
              <el-button type="primary" class="absent-button" @click="submitSetabsent(false)">
                提交为缺考
              </el-button>
            </template>
          </template>
          <template v-else-if="errorType == 'abCard'">
            <el-button type="warning" class="ab-button" @click="submitABCard(IAB_CARD_SHEEFT_TYPE.aCard)">
              提交为A卷
            </el-button>
            <el-button type="primary" class="ab-button" @click="submitABCard(IAB_CARD_SHEEFT_TYPE.bCard)">
              提交为B卷
            </el-button>
          </template>
          <template v-else>
            <el-button v-if="errorType != 'page' && errorType != 'absent'" type="primary" plain @click="renewScan"
              :disabled="isbatch || waitList.length == 0">重新识别</el-button>
            <el-button class="delete-button" type="danger" plain @click="deletePaper"
              :disabled="waitList.length == 0">删除答卷</el-button>
          </template>
        </div>
      </div>
    </div>
    <!-- 答卷重新识别弹窗 -->
    <el-dialog title="重新识别" :visible.sync="choiceDialog" width="40%" :before-close="closeDialog" class="renew-dialog">
      <div>
        <div v-if="isABCard" class="choice-page">
          <span>当前卡为</span>
          <div v-for="(item, index) in ['A', 'B']" :key="index" class="page-list-one"
            :class="{ active: currentCard == item }" @click="currentCard = item">
            {{ item }}
          </div>
          <span style="margin-left: 10px">卡</span>
        </div>
        <div class="choice-page">
          <span>当前页为第</span>
          <div v-for="(item, index) in ((pageNum % 2 != 0) ? (pageNum + 1) : pageNum)" :key="index"
            class="page-list-one" :class="{ active: currentPage == index + 1 }" @click="currentPage = index + 1">
            {{ index + 1 }}
          </div>
          <span style="margin-left: 10px">页</span>
        </div>
        <div class="choice-page">
          <span>无定位矫正：</span>
          <el-switch v-model="noPoints"></el-switch>
        </div>

        <div class="choice-page">
          <span>二维码异常：</span>
          <el-switch v-model="qrCodeError"></el-switch>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="choiceDialog = false">取消</el-button>
        <el-button type="primary" @click="sureRenew" :loading="submitLoading">确定</el-button>
      </span>
    </el-dialog>
    <batch-transfer-dialog v-if="transferDialog" :dialog-visible.sync="transferDialog" :examId="examInfo.examId"
      :errType="errorType" @confirm="handleBatchTransfer" />
    <batch-shift-dialog v-if="batchShiftDialog" :schoolId="schoolId" :imageList="batchShiftImagesList"
      :visible.sync="batchShiftDialog" @cancel="closeBatchShiftDialog" @confirm="handleBatchShift" />
  </div>
</template>

<script>
import { HANDLE_TYPE, HANDLE_LIST, ERROR_TYPE } from '@/typings/scan';
import NoData from '@/components/noData';
import { searchStuInfoAPI, getExamRepeatNoAPI } from '@/service/pexam';
import { renewScanAPI, renewPageAPI } from '@/service/xueban';
import { searchStuInfoByWork } from '@/service/api';
import { IAB_CARD_SHEEFT_TYPE } from '@/typings/card';
import BatchTransferDialog from '@/components/scan/batchTransferDialog.vue';
import batchShiftDialog from '@/pages/scan/modules/batchShiftDialog.vue';
export default {
  components: { NoData, BatchTransferDialog, batchShiftDialog },

  props: {
    //当前错误类型
    handleInfo: {
      type: Object,
      default: () => { },
    },
    examInfo: {
      type: Object,
      default: () => { },
    },
    //分页数据
    pagination: {
      type: Object,
      default: {},
    },
    //异常类型
    errorType: {
      type: String,
      default: () => '',
    },
    //总页数
    pageNum: {
      type: Number,
      default: () => 1,
    },
    //是否ab卡或ab卷
    isABCard: {
      type: Boolean,
      default: () => false,
    },
  },
  data() {
    return {
      IAB_CARD_SHEEFT_TYPE,
      HANDLE_TYPE,
      page_names: ['', '正', '反'],
      list: [],
      activeType: HANDLE_TYPE.pending,
      //当前选择的题目
      currentQuesNo: '',
      //当前选择的题目属于A||B卷
      currentQuesABPaper: '',
      //选做题选做数
      doCount: 1,
      //选做题对应ids
      quesIds: [],
      //待处理列表
      waitList: [],
      //已处理列表
      waitedList: [],
      queryInfo: {
        keyWord: '',
        range: 'present',
      },
      //学生名单
      stuList: [],
      //是否批量操作
      isbatch: false,
      //是否全选
      checkAll: false,
      isIndeterminate: false,
      //已选择的批次
      checkedTask: [],
      //当前考号信息
      currentExamNoInfo: {},
      //是否为搜索
      isSearch: false,
      schoolId: '',
      //当前点击的下一个考号
      nextExamNo: '',
      choiceDialog: false,
      currentPage: 1,
      currentCard: 'A',
      //定位点错误或无定位点
      noPoints: false,
      // 二维码错位
      qrCodeError: false,
      //确定重新识别按钮加载
      submitLoading: false,
      isShowStuList: false,
      stuPage: 1,
      stuPagecount: 0,
      //异常类型
      errorWords: ERROR_TYPE,
      //是否为缺考
      isAbsent: false,
      //是否转移
      transferDialog: false,
      //是否确定批量转移
      batchShiftDialog: false,
      //批量转移的图片列表
      batchShiftImagesList: [],
    };
  },
  watch: {
    handleInfo(newVal, oldVal) {
      if (newVal && newVal !== oldVal) {
        this.stuList = [];
        this.isSearch = false;
        this.waitList = newVal.wait;
        //不是题目异常、缺考异常、考号异常
        if (!this.isQuesError) {
          if (this.errorType == 'absent' || this.errorType == 'exam' || this.errorType == 'abCard') {
            this.list = HANDLE_LIST;
            this.list[0].num = newVal.num;
            this.waitedList = newVal.waited;
            this.list[1].num = newVal.waitedNum;
          } else {
            this.list = HANDLE_LIST.slice(0, 1);
            this.list[0].num = newVal.num;
          }
          this.getCurrentInfo();
        } else {
          this.list = HANDLE_LIST;
          this.list[0].num = newVal.num;
          this.waitedList = newVal.waited;
          this.list[1].num = newVal.waitedNum;
          this.currentQuesNo = (this.waitList.length && this.waitList[0].quesNo) || '';
          this.currentQuesABPaper = (this.waitList.length && this.waitList[0].abPaper) || '';
          this.doCount = (this.waitList.length && this.waitList[0].doCount) || 1;
          this.quesIds = (this.waitList.length && this.waitList[0].quesIds) || [];
        }
      }
    },
    currentQuesNo(newVal, oldVal) {
      if (this.errorType == 'exam') {
        let stuNo = this.activeList?.find(item => item.id == newVal)?.stuNo || '';
        let matchNum = stuNo.match(/x/g);
        if (!matchNum || (matchNum && matchNum.length < 3)) {
          this.queryInfo.keyWord = stuNo;
        } else {
          this.queryInfo.keyWord = '';
        }
      }
    },
    'queryInfo.keyWord': {
      handler(newValue, oldValue) {
        if (this.queryInfo.keyWord.length >= 2) {
          this.searchStu();
        } else {
          this.stuList = [];
          this.isSearch = false;
        }
      },
      deep: true,
    },
  },
  computed: {
    image_matrix() {
      return this.$store.state.image_matrix;
    },
    //是否为题目异常
    isQuesError() {
      return this.errorType == 'subject' || this.errorType == 'objective' || this.errorType == 'choice';
    },
    isSingleImage() {
      return this.errorType === 'point' || this.errorType === 'paper';
    },
    activeList: {
      get() {
        return this.activeType == HANDLE_TYPE.pending ? this.waitList : this.waitedList;
      },
      set(val) {
        this.activeType == HANDLE_TYPE.pending ? this.waitList = [...val] : this.waitedList = [...val];
      }
    }
  },
  mounted() {
    this.isSearch = false;
    this.schoolId = this.$route.query.schoolId;
  },
  beforeDestroy() {
    this.$bus.$off('submitData');
  },
  methods: {
    getCurrentInfo() {
      this.currentQuesNo =
        this.nextExamNo == '' ? (this.activeList.length && this.activeList[0].id) || '' : this.nextExamNo;
      this.currentExamNoInfo =
        this.activeList.length &&
        this.activeList.filter(item => {
          return item.id == this.currentQuesNo;
        })[0];
    },
    /**
     * @name:改变处理状态
     */
    changeType(item) {
      this.activeType = item.type;
      if (this.errorType == 'absent' || this.errorType == 'exam') {
        //待处理或已处理列表
        this.currentQuesNo = this.activeList.length && this.activeList[0].id;
        this.currentExamNoInfo =
          this.activeList.length &&
          this.activeList.filter(item => {
            return item.id == this.currentQuesNo;
          })[0];
        this.nextExamNo = this.getNextObjectId(this.currentQuesNo, this.activeList);
      } else {
        this.currentQuesNo = this.activeList.length && this.activeList[0].quesNo;
        this.currentQuesABPaper = (this.activeList.length && this.activeList[0].abPaper) || '';
        this.doCount = (this.activeList.length && this.activeList[0].doCount) || 1;
        this.quesIds = (this.activeList.length && this.activeList[0].quesIds) || [];
      }
      this.$emit('change-type', {
        status: this.activeType,
        quesNo: this.currentQuesNo,
        examNoId: this.currentQuesNo, //考号id
        nextExamNo: this.nextExamNo,
        doCount: this.doCount,
        quesIds: this.quesIds
      });
    },
    focusInput() {
      this.stuList = [];
      this.isSearch = false;
    },
    /**
     * @name:搜索学生
     */
    searchStu() {
      this.stuPage = 1;
      this.getSearchStu();
    },
    async getSearchStu() {
      if (this.queryInfo.keyWord == '') {
        this.stuList = [];
        return;
      }
      let params = {
        schoolId: this.schoolId,
        keyWord: this.queryInfo.keyWord,
        page: this.stuPage,
        limit: 50,
        workId: this.$route.query.personBookId,
      };
      this.isSearch = true;
      let res =
        this.queryInfo.range == 'present'
          ? await searchStuInfoByWork(params)
          : await searchStuInfoAPI(params);
      if (res.code == 1) {
        this.stuPagecount = res.data.page_count;
        this.isShowStuList = true;
        if (this.stuPage == 1) {
          this.stuList = res.data.rows;
        } else {
          this.stuList = this.stuList.concat(res.data.rows);
        }
      }
    },
    loadStuMore() {
      console.log('滚动到底部了', this.stuPage);
      if (this.stuPage >= this.stuPagecount) return;
      this.stuPage++;
      this.getSearchStu();
    },
    getNextObjectId(currentId, myArray) {
      const currentIndex = myArray.findIndex(obj => obj.id === currentId);
      if (currentIndex !== -1 && currentIndex < myArray.length - 1) {
        return myArray[currentIndex + 1].id;
      } else {
        return myArray.length && myArray[0].id; // 当前ID是数组中的最后一个或者找不到时返回null
      }
    },
    /**
     * @name:数据初始化
     */
    initData() {
      this.activeType = HANDLE_TYPE.pending;
      this.nextExamNo = '';
      this.isbatch = false;
      this.checkAll = false;
    },
    getCurrent(id) {
      this.nextExamNo = id;
    },
    getNext() {
      this.isAbsent = false;
      this.nextExamNo = this.getNextObjectId(this.currentQuesNo, this.activeList);
    },
    /**
     * @name:提交数据
     */
    submitData() {
      this.$bus.$emit('submitData');
    },
    /**
     * @name:提交考号数据
     */
    submitExamNo(stu) {
      this.nextExamNo = this.getNextObjectId(this.currentQuesNo, this.activeList);
      this.$emit('submit-exam-no', {
        stuNo: stu.stuNumber,
        stuId: stu.userId,
        id: this.currentQuesNo,
        taskId: this.currentExamNoInfo.taskId,
        oldStuNo: this.currentExamNoInfo.stuNo,
        idx: this.currentExamNoInfo.idx,
        page: this.currentExamNoInfo.page,
      });
      this.stuList = [];
      this.queryInfo.keyWord = '';
      this.$emit('get-next-exam-no', this.nextExamNo);
    },
    async getRepeatStuList(item) {
      let params = {
        examId: this.examInfo.examId,
        taskId: this.examInfo.taskId,
        stuNo: item.stuNo,
        page: item.page,
      };
      let res = await getExamRepeatNoAPI(params);
      if (res.code == 1) {
        return res.data.map((stu) => {
          return {
            cardId: stu.card_id,
            code: stu.code,
            examId: stu.exam_id,
            id: stu.id,
            idx: stu.idx,
            idxNum: stu.idx_num,
            page: stu.page,
            status: stu.status,
            stuName: item.stuName,
            stuNo: stu.stu_no,
            studentId: stu.student_id,
            taskId: stu.task_id,
            batchId: stu.batchId || '其他扫描批次',
            isRepeat:true
          };
        })
      } else {
        return [];
      }
    },
    /**
     * @name:切换题目
     */
    async changeQues(item, index) {
      if (this.errorType == 'exam' && this.examInfo.taskId) {
        if ((item.code & 16) == 16 && !item.isRepeat) {
          item.isRepeat = true;
          //获取重复学生接口 插入重复学生 并标识被重复学生，后续不在重新获取对应重复学生
          const startIndex = this.activeList.findIndex(stu => stu.id === item.id);
          let stuList = await this.getRepeatStuList(item);
          this.activeList.splice(startIndex + 1, 0, ...stuList);
          this.activeList = [...this.activeList];
        }
      }
      this.currentExamNoInfo = item;
      this.currentQuesNo = !this.isQuesError ? item.id : item.quesNo;
      this.currentQuesABPaper = item.abPaper;
      if (!this.isQuesError) {
        this.nextExamNo = this.getNextObjectId(this.currentQuesNo, this.activeList);
      } else {
        this.doCount = item.doCount || 1;
        this.quesIds = item.quesIds || [];
      }

      if (item.page > 0) {
        this.currentPage = item.page;
      }
      this.$emit('change-type', {
        status: this.activeType,
        quesNo: this.currentQuesNo, //题目id
        examNoId: this.currentQuesNo, //考号id
        nextExamNo: this.nextExamNo,
        doCount: this.doCount,
        quesIds: this.quesIds,
        abPaper: this.currentQuesABPaper,
        list: this.activeList
      });
    },
    /**
     * @name：批量删除
     */
    batchDelete() {
      this.isbatch = !this.isbatch;
      this.checkAll = true;
      this.handleCheckAllChange(true);
      if (this.isbatch == false) {
        this.checkedTask = [];
      }
    },
    /**
     * @name:批量删除题目异常
     */
    batchQuesError() {
      let confirmText = [
        '全部未处理的异常主观题，将按以下规则处理：',
        '1.识别到未批改的作0分处理',
        '2.识别到多批时取最大分数',
      ];
      if (this.errorType == 'objective') {
        confirmText = ['确认后所有客观题异常均作0分处理'];
      }
      const newDatas = [];
      const h = this.$createElement;
      for (const i in confirmText) {
        newDatas.push(h('p', null, confirmText[i]));
      }
      this.$confirm('提示', {
        title: '提示',
        message: h('div', null, newDatas),
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        // center: true,
      }).then(() => {
        this.$bus.$emit('dealAllQues');
      });
    },
    handleBatchTransfer(imagesList) {
      this.batchShiftImagesList = imagesList;
      this.batchShiftDialog = true;
    },
    handleBatchShift() {
      this.transferDialog = false;
      this.$emit('refesh-data');
    },
    batchTransfer() {
      this.transferDialog = true;
    },
    closeBatchShiftDialog() {
      this.batchShiftDialog = false;
    },
    /**
     * @name:全选考号
     */
    handleCheckAllChange(val) {
      this.checkedTask = val ? this.activeList : [];
      this.isIndeterminate = false;
    },
    /**
     * @nae:选择要删除的数据
     */
    handleCheckedChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.activeList.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.activeList.length;
    },
    reversePaperDetect(info) {
      if (info.page > 0) {
        let page = info.page;
        if (page % 2 === 0) {
          page = page - 1;
        } else {
          page = page + 1;
        }
        this.currentPage = page;
      }
      this.choiceDialog = true;
    },
    /**
     * @name:重新识别
     */
    async renewScan() {
      //答卷重新识别
      if (this.errorType === 'paper') {
        this.choiceDialog = true;
        return;
      }
      this.$emit('start-loading');
      let params = {
        image_id: this.currentQuesNo,
        paper_no: this.$route.query.paperNo,
        exam_id: this.examInfo.examId,
      };

      if (this.isSingleImage && this.image_matrix.matrix) {
        params.matrix = this.image_matrix.matrix;
        params.image_height = this.image_matrix.height;
        params.rotate = this.image_matrix.rotate;
      }

      const res = await renewScanAPI(params);
      this.nextExamNo = this.currentQuesNo;
      this.$emit('get-next-exam-no', this.nextExamNo);
      if (res.code == 1) {
        this.$emit('close-loading', res.data);
      } else {
        this.$emit('close-loading', []);
      }
    },
    /**
     * @name:关闭答卷识别弹窗
     */
    closeDialog() {
      this.choiceDialog = false;
    },
    /**
     * @name:确定重新识别答卷
     */
    async sureRenew() {
      this.submitLoading = true;
      let params = {
        paper_no: this.$route.query.paperNo,
        image_id: this.currentQuesNo,
        page: this.currentPage,
        no_points: this.noPoints,
        qrcode_error: this.qrCodeError,
        ab_card_type: this.currentCard == "B" ? IAB_CARD_SHEEFT_TYPE.bCard : IAB_CARD_SHEEFT_TYPE.aCard,
        exam_id: this.examInfo.examId,
      };

      if (this.isSingleImage && this.image_matrix.matrix) {
        params.matrix = this.image_matrix.matrix;
        params.image_height = this.image_matrix.height;
        params.rotate = this.image_matrix.rotate;
      }

      this.nextExamNo = this.currentQuesNo;
      this.$emit('get-next-exam-no', this.nextExamNo);
      const res = await renewPageAPI(params);
      if (res.code == 1) {
        this.$message({
          message: '重新识别成功！',
          type: 'success',
          duration: 1000,
        });
        this.$emit('refesh-data');
      } else {
        this.$message({
          message: '重新识别失败！',
          type: 'error',
          duration: 1000,
        });
      }
      this.submitLoading = false;
      this.choiceDialog = false;
    },
    /**
     * @name:删除答卷
     */
    deletePaper() {
      if (this.isbatch && this.checkedTask.length == 0) {
        this.$message({
          message: '请先勾选需要删除的答卷',
          type: 'warning',
          duration: 1000,
        });
        return;
      }
      let tips = `确认后将删除${this.errorWords[this.errorType].text
        }试卷，无法统计对应试卷学情`;
      this.$confirm(tips, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        let deleteData = [];
        if (this.checkedTask.length == 0) {
          deleteData.push(this.currentExamNoInfo);
        } else {
          deleteData = this.checkedTask;
        }
        this.nextExamNo = this.getNextObjectId(this.currentQuesNo, this.activeList);
        this.$emit('delete-paper', deleteData);
      });
    },
    /**
     * @name:分页
     */
    handleCurrentChange(type) {
      let page = type == 'prev' ? this.pagination.page - 1 : this.pagination.page + 1;
      this.$emit('change-page', page);
    },
    mouseLeave() {
      this.isShowStuList = false;
      this.page = 1;
    },
    /**
     * @name：提交缺考
     */
    submitSetabsent(isAbsent) {
      let data = [];
      if (this.checkedTask.length == 0 && !this.isbatch) {
        data.push(this.currentExamNoInfo);
      } else {
        data = this.checkedTask;
      }
      this.nextExamNo = this.getNextObjectId(this.currentQuesNo, this.activeList);
      this.$emit('get-next-exam-no', this.nextExamNo);
      let absentData = data.map(item => {
        return {
          ...item,
          isAbsent: isAbsent
        }
      });
      this.$emit('set-absent', absentData);
    },
    /**
     * @name 提交本页缺考
     */
    submitPageSetabsent() {
      this.$bus.$emit('submitAbsent');
    },
    /**
     * @name：设置AB卷
    */
    submitABCard(abCard) {
      this.$emit('get-next-exam-no', this.nextExamNo);
      this.$emit('set-abpaper', [this.currentExamNoInfo], abCard)
    }
  },
};
</script>

<style lang="scss" scoped>
.set-main {
  border: 1px solid #e4e8eb;
  background-color: #fff;
  height: calc(100% - 10px);
  position: relative;
}

.set-header {
  padding: 10px 20px;
}

.set-footer {
  border-top: 1px solid #e4e8eb;
  position: absolute;
  bottom: 0px;
  width: 100%;
  height: 50px !important;
  line-height: 50px !important;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.set-type {
  margin-left: 20px;
  cursor: pointer;

  &.active {
    font-weight: bold;
  }
}

.ques-list {
  padding: 10px 0 10px 40px;
  height: calc(100% - 150px);
  overflow-y: scroll;

  ul {
    list-style-type: none;

    li {
      padding-top: 10px;
      cursor: pointer;
    }
  }
}

.ques-li {
  color: #909399;

  &.active {
    color: #333;
    font-weight: bold;
  }
}

.set-page,
.submit-button {
  padding: 10px;
}

.page-num {
  font-size: 16px;
  font-weight: bold;
}

.set-exam-footer {
  // border-top: 1px solid #e4e8eb;
  position: absolute;
  bottom: 0px;
  width: 100%;
  // height: 100px !important;
  // line-height: 100px !important;
  display: flex;
  justify-content: space-between;
  flex-direction: column;

  .operat-container {
    width: 100%;
    padding: 5px 15px;
  }

  .footer-button {
    display: flex;
    // justify-content: space-between;
    width: 100%;
    padding: 0 15px 15px 15px;

    .button-container {
      position: relative;
      width: 100%;
      height: 40px;

      .delete-button {
        position: absolute;
        right: 0px;
      }
    }
  }

  .search-stu-list {
    padding: 10px 10px 10px 0px;
    overflow-y: scroll;
    position: absolute;
    top: -300px;
    height: 300px;
    z-index: 2;
    width: 100%;
    border: 1px solid #99d1f7;
    background-color: #fff;

    .stu-li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-left: 10px;
    }

    ul {
      list-style-type: none;

      li {
        padding-bottom: 10px;
        cursor: pointer;
      }
    }
  }

  .stu-no-data {
    padding: 10px 10px 10px 0px;
    overflow-y: scroll;
    position: absolute;
    top: -300px;
    height: 300px;
    z-index: 2;
    width: 100%;
    border: 1px solid #99d1f7;
    background-color: #fff;

    .no-data-tips {
      color: red;
      margin-bottom: 10px;
    }
  }
}

.choice-page {
  display: flex;
  align-items: center;
  margin-top: 20px;
}

.page-list-one,
.page-list-two {
  margin-left: 15px;
  width: 60px;
  height: 30px;
  border-radius: 6px;
  background-color: #ffffff;
  border: 1px solid #cccccc;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  &.active {
    background-color: #409eff;
    color: #ffffff;
  }
}

.absent-button {
  margin-left: 20px;
}
</style>
<style lang="scss">
.operat-container {
  .el-select .el-input {
    width: 110px;
  }
}

.custom-checkbox {
  .el-checkbox {
    // display: flex;
    // align-items: center;
    padding-top: 10px;
    margin-left: unset;
  }
}

.input-with-select .el-input-group__prepend {
  background-color: #fff;
}
</style>

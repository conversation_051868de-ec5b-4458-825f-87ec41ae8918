<!--
 * @Descripttion: 
 * @Author: 小圆
 * @Date: 2023-11-30 13:52:02
 * @LastEditors: 小圆
-->
<template>
  <div class="supervise-filter clearfix">
    <div style="float: left; margin-right: 10px" v-show="$route.name !== 'superviseOverview'">
      <a @click="$router.push('/home/<USER>')"><i class="el-icon-arrow-left"></i>返回</a>
    </div>
    <div class="header__select">
      <span class="select__label">年级：</span>
      <el-select
        v-model="queryData.gradeValue"
        class="select"
        @change="onChangeGrade"
        placeholder="请选择"
      >
        <el-option v-for="item in gradeList" :key="item.id" :label="item.name" :value="item.id">
        </el-option>
      </el-select>
    </div>
    <div class="header__select">
      <span class="select__label">学科：</span>
      <el-select
        v-model="queryData.subjectValue"
        class="select"
        @change="changeSubject"
        placeholder="请选择"
      >
        <el-option
          v-for="item in subjectListFilter"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        >
        </el-option>
      </el-select>
    </div>

    <div class="header__select" v-show="isShowTypeSelect">
      <span class="select__label">类别：</span>
      <el-select
        class="select"
        v-model="queryData.categoryValue"
        @change="changeType"
        placeholder="请选择"
      >
        <el-option v-for="item in typeList" :key="item.id" :label="item.name" :value="item.id">
        </el-option>
      </el-select>
    </div>

    <time-select
      v-model="queryData.dateRange"
      :defaultType="defaultTimeType"
      @change="changeDate"
    ></time-select>

    <div class="header__select">
      <el-button type="primary" @click="onQuery">查询</el-button>
    </div>
  </div>
</template>

<script>
import { selectAllType } from '@/service/pbook';
import UserRole from '@/utils/UserRole';
import TimeSelect from '@/components/SelectComponents/timeSelect.vue'; // 时间选择器
import {
  getDefaultDateRangeByType,
  getDefaultStatType,
} from '@/components/SelectComponents/SelectDefault';
import { getPublicConfigBySchoolInfo } from '@/service/api';

export default {
  components: {
    TimeSelect,
  },
  props: {
    value: {
      type: Object,
      default: () => {
        return {
          gradeValue: '',
          subjectValue: '',
          categoryValue: '',
          dateRange: [],
        };
      },
    },

    isCanAllGrade: {
      type: Boolean,
      default: true,
    },

    // 是否显示类别选择器
    isShowTypeSelect: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      queryData: {
        gradeValue: '',
        subjectValue: '',
        categoryValue: '',
        dateRange: [],
        statType: 0,
      },
      // 年份列表
      yearList: [],
      // 年级列表
      gradeList: [],
      // 学科列表
      subjectList: [],
      // 类别列表
      typeList: [],

      // 统计类型列表
      statTypeList: [
        {
          label: '扫描时间',
          value: 0,
        },
        {
          label: '考试时间',
          value: 1,
        },
      ],

      // 是否加载
      isLoaded: false,

      // 默认时间类型
      defaultTimeType: 1,
    };
  },

  watch: {
    queryData: {
      deep: true,
      handler(newValue, oldValue) {
        this.$emit('input', newValue);
      },
    },
  },

  computed: {
    // 筛选可用的学科列表
    subjectListFilter() {
      if (!this.queryData.gradeValue) return this.subjectList;
      const grade = this.gradeList.find(item => item.id == this.queryData.gradeValue);
      const subjectList = this.subjectList.filter(
        item => item.id == '' || item.phaseId == grade.phaseId
      );
      return subjectList;
    },
  },

  mounted() {
    this.init();
  },

  methods: {
    async init() {
      this.queryData.dateRange = getDefaultDateRangeByType(this.defaultTimeType);
      this.queryData.statType = await this.getDefaultStatType();
      this.selectAllType();
      await this.getGradeList();
      await this.onChangeGrade(this.gradeList[0].id);
      this.isLoaded = true;
      this.$emit('onLoad', {
        subjectList: this.subjectList,
        gradeList: this.gradeList,
      });
    },

    // 获取默认统计类型（中控配置项）
    async getDefaultStatType() {
      let code = ['111'];
      try {
        const { data } = await getPublicConfigBySchoolInfo({
          userId: this.$sessionSave.get('loginInfo').id,
          schoolId: this.$sessionSave.get('schoolInfo').id,
          dictCode: code.join(','),
        });
        return Number(data[0].state) || 0;
      } catch (error) {
        return 0;
      }
    },

    // 获取年级列表
    async getGradeList() {
      let gradeList = await UserRole.getGradeList({
        excludeRoles: [5, 6],
      });
      this.gradeList = gradeList;
      if (
        (UserRole.isOperation || UserRole.isSchoolLeader || UserRole.isSubjectLeader) &&
        this.isCanAllGrade
      ) {
        this.gradeList.unshift({
          id: '',
          name: '全部',
        });
      }
    },

    // 获取学科列表
    async getSubjectList() {
      let grade = this.gradeList.find(item => item.id == this.queryData.gradeValue);
      let systemCode = grade?.systemCode || '';
      let year = grade?.year || '';
      let list = await UserRole.getSubjectList({
        excludeRoles: [5, 6],
        systemCode,
        year,
      });
      if (UserRole.isOperation || UserRole.isSchoolLeader) {
        list.unshift({
          id: '',
          name: '全部',
        });
      } else {
        let roles = UserRole.utils.getRolesByYear(year);
        if (roles.includes(1) || roles.includes(2)) {
          list.unshift({
            id: '',
            name: '全部',
          });
        }
      }
      this.subjectList = list;
    },

    // 获取类别列表
    async selectAllType() {
      this.typeList = await UserRole.getAllType();
      this.typeList.unshift({
        id: '',
        name: '全部',
      });
    },

    // 改变年级
    async onChangeGrade(value) {
      this.queryData.gradeValue = value;
      await this.getSubjectList();
      this.queryData.subjectValue = this.subjectListFilter[0].id;
      this.onQuery();
    },

    // 更换学科
    changeSubject() {
      this.onQuery();
    },

    // 更改日期
    changeDate() {
      this.onQuery();
    },

    // 更改类别
    changeType() {
      this.onQuery();
    },

    onQuery() {
      this.$emit('change', this.queryData);
      this.$bus.$emit('statistic-change', this.queryData);
    },
  },
};
</script>

<style lang="scss" scoped>
.fr {
  float: right;
}
.supervise-filter {
  position: relative;
  padding: 18px 0;
  padding-top: 24px;
  width: 100%;
  line-height: 38px;
  font-size: 14px;

  .header__select {
    display: inline-block;
    margin-right: 20px;
    float: left;
  }

  .select {
    width: 104px;

    &.statType {
      width: 120px;
    }
  }

  .date-picker {
    width: 238px;
  }

  .time {
    font-size: 14px;
    font-weight: 400;
    color: #606266;
  }
}
</style>

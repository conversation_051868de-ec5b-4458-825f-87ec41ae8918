<template>
    <div class="pageContainer">
        <div class="spark__iframe" v-loading="iframeLoading">
            {{$route.name}}
            <iframe class="noselect" :src="iframeSrc" ref="auditIfram" frameborder="0"
                    sandbox="allow-forms allow-scripts allow-same-origin allow-popups" @load="iframeLoaded">
            </iframe>
        </div>
    </div>
</template>
<script>
    export default {
        name   : 'audit-frame',
        data () {
            return {
                iframeSrc    : '',
                iframeLoading: true,
                auditIfram   : null,
                loginInfo    : {}
            };
        },
        created () {
            this.loginInfo = this.$sessionSave.get('loginInfo');
            const kklUrl = process.env.VUE_APP_KKLURL;
            this.iframeSrc =  '/book-audit/';
            this.iframeSrc += `?userId=${this.loginInfo.id}&token=${this.loginInfo.token}&from=bigData`;
            console.log('二审地址=========>', this.iframeSrc);
        },
        methods: {
            iframeLoaded () {
                this.iframeLoading = false;
                this.$nextTick(() => {
                    this.auditIfram = this.$refs.auditIfram.contentWindow;
                });
            },
        }
    };
</script>

<style lang="scss" scoped>
    .pageContainer {
        width      : 100% !important;
        height     : 100% !important;
        max-width  : initial !important;
        min-width  : initial !important;
        background : #eff1fa !important;
        padding    : 0 !important;
    }

    .spark__iframe {
        position         : relative;
        min-width        : 1300px;
        max-width        : 1500px;
        margin           : 0 auto !important;
        padding          : 20px 50px !important;
        background-color : #f7fafc;
        height           : 100% !important;
    }

    .spark__iframe,
    .spark__iframe > iframe {
        width  : 100%;
        height : 100%;
    }

    .spark__iframe {
        padding : 0 !important;
        margin  : 0;
        > iframe {
            display : block;
            border  : none;
        }
    }
</style>

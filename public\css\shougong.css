.clearfix {
  *zoom: 1;
}
.clearfix:before,
.clearfix:after {
  display: table;
  line-height: 0;
  content: "";
}
.clearfix:after {
  clear: both;
}
.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}
@-webkit-keyframes shake {
  from, to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }

  20%, 40%, 60%, 80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}
@keyframes shake {
  from, to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }

  20%, 40%, 60%, 80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}
.shake {
  -webkit-animation-name: shake;
  animation-name: shake;
}
#sub_page_bd{
  width:1100px;
  min-height: 660px !important;
  padding: 40px 0;
}
#sub_page_bd .content_nav{
  height: 50px;
  width: 1100px;
  position: relative;
  background-color: #f5f5f5;
  font-size: 0;
}
#sub_page_bd .content_nav .top_nav{
  width: 100px;
  display: inline-block;
  *display: inline;
  color: #93c187;
  height: 50px;
  line-height: 50px;
  vertical-align: middle;
  text-align: center;
  text-decoration: none;
  font-size: 16px;
  cursor: pointer;
}
#sub_page_bd .content_nav .top_nav:first-child{
  margin-left: 100px;
}
#sub_page_bd .content_nav .top_nav.active{
  background-color:#93c187;
  color:#fff;
  position: relative;
}
#sub_page_bd .content_nav .top_nav.active:hover{
  opacity: 0.8;
  color: #fff;
}
#sub_page_bd .content_nav .top_nav.active:after{
  content: '';
  display: block;
  position: absolute;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  _border-left: 10px solid white;
  _border-right: 10px solid white;
  border-top: 10px solid #93c187;
  overflow:hidden;
  left:40px;
}
#sub_page_bd .content_nav .top_nav:hover{
  color:#599d41;
}
.keyword_search_wrap{
    text-align: center;
    margin: 20px auto 0;
    vertical-align: top;
}
.keyword_search_wrap #keyword{
    width: 306px;
    vertical-align: middle;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
.keyword_search_wrap #query_btn{
    vertical-align: middle;
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
    background: url('/static/img/white_search_icon.png') no-repeat 12px;
    background-color: #85c381;
}
.tree_panel_header{
    height:40px;
    background: #F5F5F5;
    border: 1px solid #E5E5E5;
    border-radius: 4px 4px 0 0;
}
.left_panel #tree_panel.jyeoo{
    margin-top: 0px;
}
.left_options{
    background-color: #fafafa;
    transition: all 0.5s;
    margin-bottom: 10px;
}
.left_options .option{
    height: 40px;
    border-bottom: 1px solid #efefef;
    vertical-align: middle;
    line-height: 40px;
    position: relative;
    background: url(/static/img/arrow_v3.png) no-repeat 200px -5px;
    text-align: center;
    cursor: pointer;
    z-index: 77;
    background-color: #F5F5F5;
}
.left_options .option.versions{
    border: 1px solid #E5E5E5;
    border-radius: 4px 4px 0 0;
    border-bottom: 0;
}
.left_options .option.textbooks{
    border: 1px solid #E5E5E5;
    border-radius: 0 0 4px 4px;
}
.left_options .option:hover,
.left_options .option.active{
    background: url(/static/img/arrow_v3.png) no-repeat 200px -50px;
    border-radius: 4px;
    border:1px solid #92c086;
    border-top-right-radius:0;
    border-bottom-right-radius:0;
    border-right: none;
}
.left_options .option .slider{
    position: absolute;
    right: -1px;
    width: 1px;
    height: 40px;
    background-color: #F5F5F5;
    top: 0px;
    z-index: 101;
    display: none;
}
.left_options .option:hover .selects,
.left_options .option:hover .slider,
.left_options .option.active .slider{
    display: block;
}
.left_options .option .option_type{
    color: #666666;
    position: absolute;
    left: 20px;
}
.left_options .option .option_name{
    display: inline-block;
    max-width: 180px;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
    color: #555555;
}
.left_options .option .selects{
    min-height: 180px;
    z-index: 99;
    display: none;
    position: absolute;
    width: 180px;
    right: -182px;
    border-radius: 4px;
    border: 1px solid #92c086;
    top: -1px;
    text-align: left;
    background: #fff;
    max-height: 300px;
    overflow-y: auto;
}
.left_options .option .selects.versions,
.left_options .option .selects.textbooks{
    border-top-left-radius: 0;
}
.left_options .option .selects .select{
    text-align: center;
    color: #555555;
    display: block;
    line-height: 40px;
    height:40px;
    vertical-align: middle;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
    cursor:pointer;
}
.left_options .option .selects .select:hover{
    color:#85C381;
}
.left_options .option .selects .select.active{
    background: #DAEDD9;
}
.qs_filter_tb{
  margin: 0px 0;
  border-bottom: 1px solid #e9e9e9;
}
.qs_filter_tb table td{
  padding: 10px;
}
.qs_filter_tb table .bd a{
  min-width: 30px;
  color:#999;
}
.qs_filter_tb table .t_title{
  width: 90px;
  color: #666;
  font-weight: normal;
  background: #F5F5F5;
}
.qs_filter_tb table .t_title.bottom,
.qs_filter_tb table .bd.bottom{
    border-bottom: 1px solid #e9e9e9;
}
.qs_filter_tb table .bd.oneline .pull-left{
  width:458px !important;
}
.qs_filter_tb table .bd a.active{
  cursor: pointer;
  background-color: #93c187;
  border-radius: 4px;
}
.qs_filter_tb table .bd #province,
.qs_filter_tb table .bd #year,
.qs_filter_tb table .bd #source{
  width:460px;
  font-size: 0; /*è§£å†³inline-blocké—®é¢˜*/
}
.qs_filter_tb table .bd #province a,
.qs_filter_tb table .bd #year a,
.qs_filter_tb table .bd #source a{
  font-size: 16px;
  margin-right: 4px;
}
.qs_filter_tb table .bd #year a{
  padding:0 10px;
  margin-right:2px;
}
.qs_filter_tb table .bd #year a:first-child{
  padding:0;
  width:55px;
  margin-right: 5px;
}
.qs_filter_tb table .bd #year,
.qs_filter_tb table .bd #source{
  height: 20px;
}
.qs_filter_tb table .bd .switch_wrap {
  display: inline-block;
  position: absolute;
}
.qs_filter_tb table .bd #item_type{
  padding-left:60px;
  position: relative;
}
.qs_filter_tb table .bd #item_type a:first-child{
  position: absolute;
  left:0px;
}
.qs_filter_tb table .bd #item_type .has_nodes,
.qs_filter_tb table .bd #province .has_nodes{
  cursor: pointer;
  position: relative;
}
.qs_filter_tb table .bd #item_type a{
  margin-bottom: 4px;
}
.qs_filter_tb table .bd #item_type .has_nodes.active li,
.qs_filter_tb table .bd #province .has_nodes.active li{
  color: #999;
}
.qs_filter_tb table .bd #item_type .has_nodes.active .arrow,
.qs_filter_tb table .bd #province .has_nodes.active .arrow{
  border-top: 6px solid #fff;
}
.qs_filter_tb table .bd #item_type .has_nodes.active:hover .arrow,
.qs_filter_tb table .bd #province .has_nodes.active:hover .arrow{
  border-bottom: 6px solid #fff;
}
.qs_filter_tb table .bd #item_type .has_nodes .arrow,
.qs_filter_tb table .bd #province .has_nodes .arrow{
  display: inline-block;
  margin-left: 4px;
}
.qs_filter_tb table .bd #item_type .has_nodes .arrow,
.qs_filter_tb table .bd #province .has_nodes .arrow{
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  _border-left: 6px solid white;
  _border-right: 6px solid white;
  border-top: 6px solid #999;
  overflow:hidden;
  position: relative;
  top:-2px;
}
.qs_filter_tb table .bd #province .has_nodes .arrow{
    right:5px;
}
.qs_filter_tb table .bd #item_type .has_nodes:hover .arrow,
.qs_filter_tb table .bd #province .has_nodes:hover .arrow{
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  _border-left: 6px solid white;
  _border-right: 6px solid white;
  border-bottom: 6px solid #999;
  overflow:hidden;
  border-top: none;
}
.qs_filter_tb table .bd #item_type .has_nodes:hover .sub_node{
  display: block;
}
.qs_filter_tb table .bd #item_type .sub_node,
.qs_filter_tb table .bd #province .sub_node{
  display: none;
  position: absolute;
  border:1px solid red;
  background-color: #fff;
  border:1px solid #e3e3e3;
  width:200px;
  z-index: 99;
}
.qs_filter_tb table .bd #province .sub_node{
    width:100px;
}
.qs_filter_tb table .bd #item_type .sub_node li,
.qs_filter_tb table .bd #province .sub_node li{
    text-align: left;
    text-indent: 20px;
    padding:2px 0;
}
.qs_filter_tb table .bd #province .sub_node li.first{
    text-indent: 10px;
}
.qs_filter_tb table .bd #province .sub_node .node{
    max-width: 100px;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.qs_filter_tb table .bd #item_type .has_nodes.active .sub_node,
.qs_filter_tb table .bd #province .has_nodes.active .sub_node{
  left:-1px;
}
.qs_filter_tb table .bd #item_type .sub_node li.active,
.qs_filter_tb table .bd #item_type .sub_node li:hover,
.qs_filter_tb table .bd #province .sub_node li.active,
.qs_filter_tb table .bd #province .sub_node li:hover{
    background-color: #c4c4c4;
    color:#fff;
}
.qs_filter_tb table .bd #province .sub_node li.first{
    border-bottom: 1px solid #e9e9e9;
}
#add_all_to_basket.all_checked{
  background: none;
  background-color: #fff;
  color: #599d41;
  border: 1px solid #599d41;
}
.qs_filter_tb table .bd #province{
    width:510px;
}
.qs_filter_tb table .bd #province a{
  width: 57px;
  padding: 0px;
  height: 21px;
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  margin-bottom: 4px;
}
.qs_filter_tb table .bd #province a.type_a{
  width: 66px;
}
.qs_filter_tb table .bd #province a.type_a .node_name{
  width:50px;
  display: inline-block;
  vertical-align: top;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
#paper_list .no_result{
  color: #FD5959;
  margin-top: 10px;
}

.left_panel,.right_panel{
  display: inline-block;
  font-size: 0;
}
.left_panel{
  width: 338px;
  font-size: 16px;
  padding: 0px 18px;
  margin: 20px 0 0 20px;
}
.left_panel.jyeoo{
    width:260px;
    margin: 20px 0 0 10px;
}
.left_panel .choose{
  font-size: 0;
}
.left_panel .choose li{
  display: inline-block;
  font-size: 16px;
  width: 132px;
  text-align: center;
  border: 1px solid #dbdbdb;
  height: 32px;
  line-height: 32px;
  vertical-align: middle;
  cursor: pointer;
}
.left_panel .choose li#by_tag{
  border-right: none;
}
.left_panel .choose li#by_zhangjie{
  border-left: none;
}
.left_panel .choose li.active{
  background-color: #dbdbdb;
}
.left_panel .choose a{
  color: #555;
  text-decoration: none;
}
.left_panel #tree_panel{
  overflow: auto;
  min-height: 300px;
  padding: 0;
  margin-top: 5px;
  padding-top: 10px;
  background-color: #fafafa;
}
.left_panel .ztree li span.button.switch{
  margin-left:20px;
}
.left_panel .ztree li{
  margin: 10px !important;
}
.left_panel .ztree li span.node_name{
  max-width: 250px;
}
.left_panel .ztree li .curSelectedNode span.node_name{
  color: #7EC283;
}
.right_panel{
  font-size: 16px;
  margin-left: 15px;
  width: 665px;
  vertical-align: top;
}
.right_panel.jyeoo{
    margin-left: 0;
    width:760px;
    margin-top: 20px;
}
.right_panel>.button_wrap{
  margin:10px 0;
  display: none;
}
.right_panel>.button_wrap>#add_all_to_basket{
  float: right;
  background-image: none;
  background-color: #93c187;
  color:#fff;
  border:1px solid #93c187;
}
.right_panel>.button_wrap>#add_all_to_basket:focus{
  outline: none;
}
.right_panel>.button_wrap>#add_all_to_basket:hover{
  opacity: 0.8;
}
.right_panel .fake_basket_wrap{
  background-color: #FFF;
  z-index: 66;
  opacity:0.9;
  filter: Alpha(opacity=90);
  display: none;
}
.content_nav .item_basket{
  position: absolute;
  top:10px;
  right:0px;
  width: 100px;
  height:30px;
  line-height: 30px;
  margin-right: 30px;
  color: #FFF;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  background-color: #93c187;
  z-index: 99;
  font-size: 16px;
  border-radius: 4px;
}
.content_nav .item_basket .arrow{
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  _border-left: 6px solid white;
  _border-right: 6px solid white;
  border-top: 6px solid #fff;
  overflow:hidden;
  position: relative;
  top:12px;
  margin-left: 6px;
}
.content_nav .item_basket .basket_count{
  background-color: #fda535;
  color: #FFF;
  position: absolute;
  top:-10px;
  right: 5px;
  min-width: 12px;
  height: 20px;
  border-radius: 10px;
  line-height: 20px;
  vertical-align: middle;
  text-align: center;
  padding: 0 5px;
}
.content_nav .item_basket .basket_content{
  position: absolute;
  border:2px solid #93c187;
  min-height: 210px;
  width:300px;
  background-color: #FFF;
  right:0px;
  display: none;
  border-radius: 4px;
}
.basket_content .buttons{
  margin-bottom: 15px;
}
#basket_table {
  margin-top: 0px;
  margin-bottom: 10px;
  text-align: center;
  border-radius: 4px;
}
#basket_table thead tr{
  background-color: #f1f1f1;
  color: #333333;
}
#basket_table thead{
  cursor: default;
}
#basket_table thead tr td{
  border: 1px solid #dbdbdb;
  text-align: center;
}
#basket_table thead tr td:first-child{
  border-top-left-radius: 4px;
}
#basket_table thead tr td:last-child{
  border-top-right-radius: 4px;
}
#basket_table thead tr td{
  color: #999;
}
#basket_table tbody tr td{
  border: 1px solid #dbdbdb;
  text-align: center;
  color: #999;
  line-height: 6px;
  vertical-align: middle;
}
#basket_table tbody tr td.name{
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width:150px;
}
#basket_table tbody tr td span.clear{
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url("/static/img/dustbin_v2.png") no-repeat  -21px -11px;
}
#basket_table tbody tr td.single_td{
  height: 30px;
  color: #92c086;
  border: none;
}
#clear_all{
  color: #999;
  line-height: 26px;
  display: inline-block;
  padding-left: 24px;
  background: url(/static/img/dustbin_v2.png) no-repeat -57px -9px;
  vertical-align: middle;
  margin-top: 10px;
}
#generate_paper, #preview_paper{
  display: inline-block;
  width: 80px;
  text-align: center;
  height: 34px;
  line-height: 30px;
  vertical-align: middle;
  background-color: #92c086;
  border: 1px solid #92c086;
  color: #FFF;
  border-radius: 4px;
}
#generate_paper:focus, #preview_paper:focus{
  outline: none;
}
#generate_paper.disabled, #preview_paper.disabled{
  background-color: #D0D1D1;
  border: 1px solid #D0D1D1;
  color:white;
  cursor: not-allowed;
  border-radius: 4px;
}
#preview_paper:hover, #generate_paper:hover{
  opacity: 0.8;
}
.radios{
  display: inline-block;
  height: 20px;
  line-height: 20px;
  vertical-align: middle;
  margin: 10px 0;
}
.radios .radio{
  display: inline-block;
  cursor: pointer;
  margin-right: 20px;
}
.radios input{
  position: relative;
  top:3px;
}
q ol{
  margin: 20px 0 20px 28px;
}
q .leaf-q{
  margin-bottom: 0;
  margin-left: 25px;
}
.auxiliary .difficulty {
  padding: 5px 18px;
}
.item{
  margin-bottom: 15px;
  border-radius: 4px;
}
.item .operate_area{
  height: 50px;
  vertical-align: middle;
  line-height: 50px;
  color: #999999;
}
.item .operate_area .item_origins,
.item .operate_area .used_count,
.item .operate_area .show_info,
.item .operate_area .q_error,
.item .operate_area .collect,
.item .operate_area .cancel_collect,
.item .operate_area .add_to_basket,
.item .operate_area .remove_to_basket{
  display: inline-block;
  cursor: pointer;
}
.item .operate_area .item_origins,
.item .operate_area .used_count{
  margin-left: 10px;
}
.item .operate_area .show_info,
.item .operate_area .q_error,
.item .operate_area .collect,
.item .operate_area .cancel_collect,
.item .operate_area .add_to_basket,
.item .operate_area .remove_to_basket{
  float: right;
  margin-left: 20px;
}
.item .operate_area .show_info i,
.item .operate_area .q_error i,
.item .operate_area .collect i,
.item .operate_area .cancel_collect i,
.item .operate_area .add_to_basket i,
.item .operate_area .remove_to_basket i{
  display: inline-block;
  width:20px;
  height:20px;
  background-image: url(/static/img/shougong_icons_v2.png);
  position: relative;
  top: 5px;
  margin-right: 6px;
}
.item .operate_area .show_info i{
  background-image: url(/static/img/green_down_arrow.png);
  background-position: center;
  background-repeat: no-repeat;
}
.item .operate_area .show_info.hide i{
  background-image: url(/static/img/shougong_icons_v2.png);
  background-position: -13px -7px;
}
.item .operate_area .q_error i{
  background-position: -41px -7px;
}
.item .operate_area .q_error:hover i{
  background-position: -41px -40px;
}
.item .operate_area .collect i,
.item .operate_area .cancel_collect i{
  background-position: -74px -7px;
}
.item .operate_area .collect:hover i,
.item .operate_area .cancel_collect:hover i{
  background-position: -74px -41px;
}
.item .operate_area .q_error a{
  font-size: 16px;
  color: #92c086;
}
.item .operate_area .add_to_basket{
  background-color: #92c086;
  color: #FFF;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  margin-top: 8px;
  margin-right: 10px;
  padding: 0 8px;
  border: 1px solid #92c086;
  border-radius: 4px;
}
.item .operate_area .add_to_basket:hover{
  background-color: #fff;
  color: #92c086;
}
.item .operate_area .add_to_basket i{
  background-image: url(/static/img/shougong_icons_v2.png);
  background-position: -106px -5px;
  width: 23px;
  height: 23px;
  vertical-align: top;
  top: 2px;
}
.item .operate_area .add_to_basket:hover i{
  background-position: -106px -37px;
}
.item .operate_area .remove_to_basket{
  background-color: #92c086;
  color: #fff;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  margin-top: 8px;
  margin-right: 10px;
  padding: 0 8px;
  border: 1px solid #92c086;
  border-radius: 4px;
}
.item .operate_area .remove_to_basket:hover{
  background-color: #fff;
  color: #92c086;
}
.item .operate_area .remove_to_basket i{
  display: none;
}
.item .operate_area .show_info,
.item .operate_area .q_error,
.item .operate_area .collect,
.item .operate_area .cancel_collect{
  color: #92c086;
}
.item .operate_area .show_info:hover,
.item .operate_area .q_error:hover a,
.item .operate_area .collect:hover,
.item .operate_area .cancel_collect:hover{
  color: #84C588;
}
.item .q_error
{
  display: none;
}
.item .operate_area .q_error
{
  position: static;
  border: none;
  background: none;
  padding: 0;

  width: auto;
  display: block;
}
.item .q_error
{
  display: none;
}
.item .operate_area .q_error
{
    position: static;
    border: none;
    background: none;
    padding: 0;
    width: auto;
    display: block;
}
.table_page{
  margin: 10px 0 10px 0;
}
.table_page .pager,
.table_page .pager .pagination{
  margin: 0;
}
.search_panel{
  width: 765px;
  margin-bottom: 8px;
  margin-top: 20px;
}
.search_panel #tag_name{
    vertical-align: top;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.search_panel .btn-bianping-dgreen{
  background-image: none;
  background-color: #92c086;
  color: #fff;
  border:1px solid #92c086;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.search_panel .btn-bianping-dgreen:hover{
  opacity: 0.8;
}
.search_panel .btn-bianping-dgreen:focus{
  outline: none;
}
.search_panel #reset_btn{
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.search_panel  input:focus{
  border:1px solid #92c086;
}
#query_btn {
  margin-left: -4px;
}
button.switch{
  background-color: #93c187;
  color: #fff;
  border: 1px solid #93c187;
  font-size: 16px;
  border-radius: 4px;
  padding: 4px 10px;
  margin-bottom: 10px;
}
button.switch:hover{
  opacity: 0.8;
}
button.switch:focus{
  outline: none;
}
.left_panel span.select_type_tip{
  display: none;
  color:#999;
  font-size: 12px;
  position: relative;
  top: 5px;
  left:4px;
}
.choosed_tags .title{
  display: inline-block;
  line-height: 20px;
  height: 20px;
  vertical-align: middle;
  margin-left: -6px;
  visibility: hidden;
}
.choosed_tags .title.has_tag{
  visibility: visible;
  display: inline-block;
  vertical-align: middle;
  font-size: 16px;
  color: #999999;
}
.choosed_tags {
  margin: 6px 6px 0px 6px;
  font-size: 12px;
}

.choosed_tags.higher{
  margin: 4px 6px 0px 6px;
}
.choosed_tags .tag{
    position: relative;
    padding: 0 25px 0 10px;
    margin-left: 10px;
    border-radius: 4px;
    display: inline-block;
    min-width: 120px;
    max-width: 162px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 30px;
    line-height: 30px;
    background-color: #92c086;
    color: #fff;
    font-size: 16px;
    vertical-align: middle;
}
.choosed_tags .tag a.desc{
  color: #fff;
  text-decoration: none;
}
.choosed_tags .tag a.delete{
  position: absolute;
  right: 10px;
  font-size: 18px;
  cursor: pointer;
  vertical-align: middle;
  color: #fff;
}
.choosed_tags .tag a.delete:hover{
  text-decoration: none;
}
.query_type_radios{
  display: inline-block;
  margin-left: 10px;
}
.query_type_radios label{
  display: inline-block;
}
.qs_filter_tb table .bd .switch,
.qs_filter_tb table .bd .more_provinces{
  background-color: #a7cc9d;
  border-radius: 4px;
  color:#fff;
}
.qs_filter_tb table .bd .switch:hover,
.qs_filter_tb table .bd .more_provinces:hover{
  opacity: 0.8;
  background-color: #a7cc9d;
  border-radius: 4px;
  color:#fff;
}
.qs_filter_tb .switch:hover,
.qs_filter_tb .more_provinces:hover{
  background-color: #d4d4d4;
}
.qs_filter_tb table tr {
    border-radius: 0 4px 4px 0;
    border-right:1px solid #e9e9e9;
}
.qs_filter_tb table tr:first-child td{
  border-top: 1px solid #e9e9e9;
}
.qs_filter_tb table .bd{
  background-color: #fff;
}
.qs_filter_tb table .bd .pull-left.last{
  margin-left: 25px;
}
.sort_wrap{
    height:40px;
    line-height: 40px;
    vertical-align: middle;
    font-size: 0;
    box-sizing: border-box;
}
.sort_wrap .t_title{
    width: 92px;
    color: #666;
    font-weight: normal;
    background: #F5F5F5;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    border-top: 1px solid #e9e9e9;
    border-bottom: 1px solid #e9e9e9;
    font-size: 16px;
}
.sort_wrap .bd{
    display: inline-block;
    vertical-align: middle;
    border-top: 1px solid #e9e9e9;
    border-bottom: 1px solid #e9e9e9;
    font-size: 16px;
    width:666px;
    border-right: 1px solid #e9e9e9;
}
.sort_wrap .bd a{
    display: inline-block;
    text-decoration: none;
    cursor: pointer;
    text-align: center;
    padding: 0px 16px;
    min-width: 30px;
    color: #999;
    margin-left: 10px;
    line-height: 20px;
}
.sort_wrap .bd a.active{
    cursor: pointer;
    color:#85C381;
    border-radius: 4px;
}
.sort_wrap .bd a .sort_icon{
    background: url('/static/img/sort_disabled.png') no-repeat;
    display: inline-block;
    width:15px;
    height:15px;
    position: relative;
    top:5px;
    left:5px;
}
.sort_wrap .bd a .sort_icon.asc{
    background: url('/static/img/sort_asc.png') no-repeat;
}
.sort_wrap .bd a .sort_icon.desc{
    background: url('/static/img/sort_desc.png') no-repeat;
}
.arrow_down {
    width: 0;
    height: 0;
    display: inline-block;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    _border-left: 6px solid white;
    _border-right: 6px solid white;
    border-top: 6px solid #fff;
    overflow:hidden;
}
.arrow_up {
    width: 0;
    height: 0;
    display: inline-block;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    _border-left: 6px solid white;
    _border-right: 6px solid white;
    border-bottom: 6px solid #fff;
    overflow:hidden;
    position: relative;
    top:-2px;
}
#baocuo_box .modal-body{
  padding: 0;
}
#baocuo_box .modal-body h4{
  font-weight: normal;
  font-size: 16px;
  line-height: 30px;
  background-color: #B6D7A8;
  text-indent: 20px;
}
#baocuo_box .modal-body h4 .must_error_type,#baocuo_box .modal-body h4 .must_error_desc{
  color:red;
  display: none;
}
#baocuo_box .modal-body input{
  margin-left: 100px;
}
#baocuo_box .modal-body .input_row{
  margin-bottom: 10px;
}
#baocuo_box .modal-body textarea{
  margin-left: 20px;
  width: 89%;
  height: 80px;
}
#baocuo_success_box .modal-header{
  background-color: #B6D7A8;
  color: #000000;
  margin-top: 20px;
  height: 30px;
}
#baocuo_success_box .modal-header .close_box {
  top:30px;
}
#baocuo_success_box .modal-header h3{
  font-size: 16px;
  line-height: 25px;
}
#baocuo_success_box .modal-body .tips {
  margin: 0 auto;
  width: 400px;
  height: 100px;
  text-align: center;
}
#baocuo_success_box .modal-body .tips .left_img {
  width: 100px;
  height: 100px;
  float: left;
  background: url("/static/img/duihao.png") no-repeat 50px;
}
#baocuo_success_box .modal-body .tips .right_desc {
  float: left;
  margin-left: 20px;
}
#baocuo_success_box .modal-body .tips .right_desc h4{
  font-weight: normal;
  margin-top: 35px;
}
#baocuo_success_box .modal-body .bottom_tip{
  text-align: center;
}
#baocuo_success_box .modal-body .bottom_tip.bind_tip{
  display:none;
}
#baocuo_success_box .modal-body .bottom_tip.bind_tip a{
  color: #589C40;
  text-decoration: underline;
}
.ztree li a.curSelectedNode{
  background-color:inherit;
  color: inherit;
  border:none;
  opacity: 1;
  padding: 1px 3px 0 0;
  cursor: default;
}
.ztree li span.button.chk{
  cursor: pointer;
}
.quizPutTag{
  pointer-events: none !important;
}

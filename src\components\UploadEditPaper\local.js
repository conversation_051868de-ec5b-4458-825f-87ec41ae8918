export default {
  zh: {
    uploadEditPaper:{
      uploadExam: '上传试卷',
      editExam: '修改属性',
      upload: '上传',
      download: '下载试卷模板',
      tplText: '按模板上传，制作更智能，更快捷~',
      examLimitText: '请选择100M以内的文档上传(doc、docx)',
      chooseFile: '选择文件',
      grade: '适用年级',
      subject: '所属学科',
      type: '试卷类别',
      year: '所属年份',
      together: '试卷同步至校本卷库',
      noFileText: '请选择文件',
      noFileNameText: '请输入试卷名称',
      errFileText: '请选择符合要求的资源',
      errFileNameText: '试卷名称不能包含特殊字符',
      emptyFileNameText: '试卷名称不能为空',
      longFileNameText: '试卷名称过长，最多只能输入50字哦！'
    }
  },
  en: {
    transPaperDialog:{
      uploadExam: 'upload exam',
      editExam: 'edit exam',
      upload: 'upload',
      download: 'download exam',
      tplText: 'upload by template, making more intelligent and faster~',
      examLimitText: 'please select documents within 100m to upload (doc, docx)',
      chooseFile: 'select file',
      grade: 'grade',
      subject: 'subject',
      type: 'type',
      year: 'year',
      together: 'the test paper is synchronized to the school paper library',
      noFileText: 'please select file',
      noFileNameText: 'please enter the name of the test exam',
      errFileText: 'please select the resources that meet the requirements',
      errFileNameText: 'The test paper name cannot contain special characters',
      emptyFileNameText: 'test paper name cannot be empty',
      longFileNameText: 'test exam name is too long, no more then 50 words!',
    }
  }
}

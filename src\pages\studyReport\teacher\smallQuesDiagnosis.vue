<!--
 * @Description: 全科成绩
 * @Author: 小圆
 * @Date: 2024-04-02 09:02:32
 * @LastEditors: 小圆
-->

<template>
  <div>
    <div class="score-type">
      <div class="header-item">
        <el-radio-group v-model="type" @input="onTypeChange">
          <el-radio-button :label="0">按得分率</el-radio-button>
          <el-radio-button :label="1">按平均分</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <div v-loading="tableLoading">
      <base-table
        v-if="tableData.length"
        :data="tableData"
        :column="tableColumns"
        v-bind="getTableAttr()"
        v-drag-table
      ></base-table>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>

    <div class="score-type">
      <el-radio-group v-model="chartType" @input="onChartTypeChange">
        <el-radio-button :label="0">折线图</el-radio-button>
        <el-radio-button :label="1">雷达图</el-radio-button>
      </el-radio-group>
    </div>

    <div
      v-show="chartType == 0"
      style="width: 100%; height: 450px; margin-top: 20px"
      id="LineChart"
    ></div>
    <div
      v-show="chartType == 1"
      style="width: 100%; height: 500px; margin-top: 20px"
      id="RadarChart"
    ></div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins, Prop } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';
import FilterModule from '../plugins/FilterModule';
import {
  getDefaultGrid,
  getDefaultLegend,
  getDefaultPercentAxis,
  getDefaultTitle,
  getDefaultToolBox,
  getDefaultTooltipFormatter,
} from '../plugins/DefaultEchartsOption';

@Component({
  components: {},
})
export default class Index extends Mixins(TableCommon) {
  // 0: 按人数 1: 按选中率
  type: 0 | 1 = 0;
  // 0: 折线图 1: 雷达图
  chartType: 0 | 1 = 0;

  // 表格额外参数
  tableParams = {
    type: this.type,
  };

  // 折线图表
  lineCharts: EChartsType = null;
  // 雷达图表
  radarCharts: EChartsType = null;
  // 图表hover提示文字
  toolTipHtml: string = '';

  callbackGetTableData() {
    this.renderChart();
  }

  renderChart() {
    if (this.chartType == 0) {
      this.renderLineEChart();
    }
    if (this.chartType == 1) {
      this.renderRadarChart();
    }
  }

  // 渲染折线图
  renderLineEChart() {
    if (this.lineCharts) {
      this.lineCharts.dispose();
      this.lineCharts = null;
    }
    const dom = document.getElementById('LineChart');
    this.lineCharts = this.$echarts.init(dom);

    const currentClassInfo = FilterModule.filterInfo.classInfo; // 当前班级信息
    const currentSchoolInfo = FilterModule.filterInfo.schoolInfo; // 当前学校信息

    const legendTitle = currentSchoolInfo.schoolName + '-' + currentClassInfo.class_name; // 图表标题
    const xAxisData = this.tableData.map(item => item.quesNos).filter(item => item !== '整卷'); // 获取x轴数据
    const seriesData = this.tableData
      .map(item => {
        if (item.quesNos !== '整卷') return item['class' + FilterModule.filterInfo.classInfo.id];
      })
      .filter(item => item !== undefined)
      .map(item => parseFloat(item)); // 获取y轴数据

    let option: EChartsOption = {
      grid: getDefaultGrid({ top: 80 }),
      title: getDefaultTitle({
        text: this.type == 0 ? '小题得分率折线图' : '小题平均分折线图',
      }),
      legend: getDefaultLegend({
        data: [legendTitle],
      }),
      tooltip: {
        trigger: 'axis',
        valueFormatter: value => {
          return this.type == 0 ? value + '%' : value + '';
        },
      },
      toolbox: getDefaultToolBox(),
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLabel: {},
      },
      yAxis: this.type == 0 ? getDefaultPercentAxis() : { type: 'value' },
      series: [
        {
          name: legendTitle,
          data: seriesData,
          type: 'line',
          label: {
            show: true, // 显示数值
            position: 'top', // 数值显示的位置
            formatter: `{c}${this.type == 0 ? '%' : ''}`,
          },
        },
      ],
    };
    this.lineCharts.setOption(option);
  }

  // 渲染雷达图
  renderRadarChart() {
    if (this.radarCharts) {
      this.radarCharts.dispose();
      this.radarCharts = null;
    }
    const dom = document.getElementById('RadarChart');
    this.radarCharts = this.$echarts.init(dom);

    const currentClassInfo = FilterModule.filterInfo.classInfo; // 当前班级信息
    const currentSchoolInfo = FilterModule.filterInfo.schoolInfo; // 当前学校信息

    const legendTitle = currentSchoolInfo.schoolName + '-' + currentClassInfo.class_name; // 图表标题
    const seriesData = this.tableData
      .map(item => {
        if (item.quesNos !== '整卷') return item['class' + FilterModule.filterInfo.classInfo.id];
      })
      .filter(item => item !== undefined)
      .map(item => parseFloat(item));

    const indicator = this.tableData
      .map(item => item.quesNos)
      .filter(item => item !== '整卷')
      .map(item => {
        return { name: item };
      });

    this.toolTipHtml = '';
    let option: EChartsOption = {
      grid: getDefaultGrid(),
      legend: getDefaultLegend(),
      title: getDefaultTitle({
        text: this.type == 0 ? '小题得分率对比图' : '小题平均分对比图',
      }),
      tooltip: {
        trigger: 'item',
        triggerOn: 'mousemove',
        showDelay: 100,
        formatter: params => {
          return this.toolTipHtml;
        },
      },
      toolbox: getDefaultToolBox(),
      radar: {
        indicator: indicator,
        center: ['50%', '50%'],
        radius: 150,
        startAngle: 90,
        axisName: {
          color: '#428BD4',
        },
      },
      series: {
        name: legendTitle,
        type: 'radar',
        data: [seriesData],
        label: {
          show: true, // 显示数值
          position: 'top', // 数值显示的位置
          formatter: `{c}${this.type == 0 ? '%' : ''}`,
        },
      },
    };

    this.radarCharts.setOption(option);
    this.radarCharts.on('mouseover', (params: any) => {
      this.toolTipHtml = '';
      let isSelectedDot = params.event.target.__dimIdx;
      if (isSelectedDot == undefined) return;
      let percentStr = this.type == 0 ? '%' : '';
      let seriesName = (this.radarCharts as any)._model.option.radar[0].indicator[isSelectedDot]
        .name;
      this.toolTipHtml =
        '<div style="font-size:14px;color:#666;font-weight:400;line-height:1;text-align:left;">' +
        legendTitle +
        '</div>';
      this.toolTipHtml +=
        '<div style="margin: 10px 0 0;line-height:1;">' +
        '<span style="font-size:14px;color:#666;font-weight:400;margin-left:2px">' +
        '<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:' +
        params.color +
        ';"></span>' +
        seriesName +
        '</span>' +
        '<span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">' +
        params.value[isSelectedDot] +
        percentStr +
        '</span>' +
        '<div style="clear:both"></div>';
      this.toolTipHtml += '</div>';
    });
  }

  // 表格类型切换
  onTypeChange(val) {
    this.tableParams = {
      type: val,
    };
    this.getTableData();
  }

  // 图表类型切换
  async onChartTypeChange(val) {
    await this.$nextTick();
    this.renderChart();
  }
}
</script>

<style scoped lang="scss">
.score-type {
  margin: 20px 0;
}
</style>

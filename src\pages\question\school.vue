<template>
  <div class="paper-container">
    <!--头部筛选项-->
    <div class="header-filter clearfix">
      <!--年级 -->
      <div class="header__select">
        <span class="select__label">年级：</span>
        <el-select v-model="gradeId" class="grade-select" @change="changeGrade" placeholder="请选择">
          <el-option v-for="(item, index) in gradeLists" :key="index" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </div>
      <!--学科-->
      <div class="header__select">
        <span class="select__label">学科：</span>
        <el-select v-model="subjectId" class="grade-select" @change="changeSubject" placeholder="请选择">
          <el-option v-for="(item, index) in subjectList" :key="index" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </div>

      <!--试卷类型 -->
      <div class="header__select">
        <span class="select__label">试卷类型：</span>
        <el-select v-model="typeCode" class="grade-select" @change="changeCategory" placeholder="请选择">
          <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </div>
      <!--年份 -->
      <div class="header__select">
        <span class="select__label">年份：</span>
        <el-select v-model="filterData.year" class="grade-select" @change="changeYear" placeholder="请选择">
          <el-option v-for="item in yearList" :key="item" :label="item" :value="item"> </el-option>
        </el-select>
      </div>
      <!--搜索-->
      <div class="header__select">
        <div class="header__serarch clearfix display_flex">
          <el-input class="search__text" placeholder="输入试卷名称搜索" v-model="filterData.tbName"
            @keyup.enter.native="searchReport" clearable>
          </el-input>
          <div class="search__icon el-icon-search display_flex align-items_center justify-content_flex-center"
            @click="searchReport"></div>
        </div>
      </div>
    </div>
    <!--表格列表-->
    <div class="body-content">
      <el-table class="schoolTable" v-loading="tableLoading" ref="tableRef" :data="tableData" :row-style="{ padding: 0 }"
        :cell-style="{ position: 'relative' }" stripe @sort-change="sortChange">
        <el-table-column prop="tbName" label="试卷名称" width="360">
          <template slot-scope="scope">
            <!-- <div
              class="square"
              v-if="scope.row.qualityType && scope.row.qualityType !== 0"
            >
              <span class="square-word">精</span>
            </div> -->
            <el-tooltip :content="scope.row.tbName" placement="top">
              <span class="showOverTooltip">{{ scope.row.tbName }} </span>
            </el-tooltip>
            <!-- <p>{{ scope.row.tbName }}</p> -->
          </template>
        </el-table-column>
        <!--试题数-->
        <el-table-column sortable="custom" prop="qcount" label="试题数" width="100"></el-table-column>
        <!--总分-->
        <!-- <el-table-column sortable="custom" prop="score" label="总分" width="100"></el-table-column> -->
        <!--学科-->
        <el-table-column sortable="custom" label="学科" prop="subjectName"></el-table-column>
        <!--年级-->
        <el-table-column sortable="custom" label="年级" width="100" prop="gradeName"></el-table-column>
        <!--创建人-->
        <el-table-column sortable="custom" prop="creatorName" label="创建人"></el-table-column>
        <!--创建时间-->
        <el-table-column sortable="custom" prop="createTime" label="创建时间">
          <!-- <template slot-scope="scope">
                        <div>{{scope.row.dateCreated.split(' ')[0]}}</div>
                    </template> -->
        </el-table-column>
        <!--操作-->
        <el-table-column label="操作" fixed="right" width="240">
          <template slot-scope="scope">
            <div class="editCell display_flex align-items_center justify-content_flex-around">
              <span v-if="loginInfo.user_type == 1 && loginInfo.subjectid == scope.row.subjectCode"
                @click="goCreateCard(scope.row)">布置</span>
              <span @click="goPreview(scope.row)">试卷详情</span>

              <span @click="downloadPaper(scope.row)" v-if="scope.row.isUse === 1 && inArr(scope.row.source, [1, 3, 5])">
                下载
              </span>

              <el-tooltip v-else effect="dark" content="试卷正在生成中" placement="top">
                <span class="disabled">不可下载</span>
              </el-tooltip>

            </div>
          </template>
        </el-table-column>
      </el-table>
      <!--分页器-->
      <el-pagination background style="margin: 20px auto 0" :hide-on-single-page="!tableData.length" class="text-center"
        layout="total, prev, pager, next" @current-change="handleCurrentChange" :current-page.sync="pagination.page"
        :page-size="pagination.limit" :total="pagination.total_rows">
      </el-pagination>
    </div>
    <!--下载试卷弹窗-->
    <downloadDialog :downItem="downItem" v-if="showDialog" @close="closeDialog"></downloadDialog>
    <!--作业发送弹窗-->
    <send-hw-dialog :info="sendHwInfo" v-if="showSendHw" @close-dialog="showSendHw = false"></send-hw-dialog>
  </div>
</template>

<script>
import { delPaper } from '@/service/pexam';
import { getTestPaperList, delXBResource, getTBSearchInfo } from '@/service/testbank';
import { mapGetters } from 'vuex';
import downloadDialog from '@/components/paper/downloadDialog';
import SendHwDialog from '../../components/paper/SendHWDialog';
import { getUserInfoToPersonalityTest, getSchoolById } from '@/service/api';
import UserRole from '@/utils/UserRole';
import { getToken } from '@/service/auth';
import { inArr } from '@/utils/common';

export default {
  name: 'school',

  data() {
    return {
      tableLoading: false,
      // 表格数据
      tableData: [],
      // 分页
      pagination: {
        page: 1,
        pageSize: 10,
        total_rows: 0,
      },
      // 筛选条件
      filterData: {
        categoryName: '全部',
        year: '',
        tbName: '',
      },
      subjectId: '',
      gradeId: '',
      // 开始计算的年份
      startYear: 2021,
      categoryList: [],
      yearList: [],
      gradeLists: [],
      subjectLists: [],
      sortType: '',
      orderType: '',
      reqData: {
        schoolId: '',
        gradeId: '',
        subjectId: '',
        userId: '',
      },
      showDialog: false,
      downItem: {},
      timer: null,
      timerGap: 10000,
      categoryName: '',
      typeCode: '',
      year: '',
      showSendHw: false,
      sendHwInfo: null,
      cardUrl: process.env.VUE_APP_CARDURL,
      inArr: inArr,
    };
  },
  computed: {
    ...mapGetters(['subjectMap', 'gradeList', 'loginInfo']),
    phaseId() {
      let phaseId = '';
      if (this.gradeId) {
        phaseId = this.gradeLists.filter(item => {
          return item.id == this.gradeId;
        })[0].phaseId;
      }
      if (!this.gradeId && this.subjectId) {
        // phaseId = this.subjectMap[this.subjectId].phaseId;
        let list = this.subjectLists.filter(item => {
          return item.id === this.subjectId;
        });
        phaseId = list[0].phaseId;
      }
      return phaseId;
    },
    subjectList() {
      let subList = [];
      if (this.phaseId && this.gradeId) {
        subList = this.subjectLists.filter(item => {
          return item.phaseId === this.phaseId || item.name === '全部';
        });
        this.subjectId = subList[0].id;
      } else {
        subList = this.subjectLists;
      }
      return subList;
    },
  },
  components: {
    downloadDialog,
    SendHwDialog,
  },
  mounted() {
    this.initSchool();
    this.yearList = [];
    let now = new Date().getFullYear();
    for (let i = now; i > now - 6; i--) {
      this.yearList.push(i);
    }

    this.yearList.unshift('全部');
    this.filterData.year = '全部';
    this.getSchoolById();
  },
  methods: {
    /**
     * @name: 布置作业
     * @params: info 试卷信息
     */
    sendHomeWork(info) {
      this.sendHwInfo = info;
      this.showSendHw = true;
    },
    //去制卡
    goCreateCard(item) {
      let token = getToken();
      let routeData = this.cardUrl + `?id=${item.id}&examName=${item.tbName}&token=${token}`;
      window.open(routeData, '_blank');
    },
    // getParams() {
    //   return {
    //     schoolId: this.$sessionSave.get("schoolInfo").id,
    //     subjectId: this.subjectId,
    //   };
    // },
    closeDialog() {
      this.showDialog = false;
    },
    // 获取登录账号学段
    async getSchoolById() {
      await getSchoolById({
        schoolId: this.$sessionSave.get('schoolInfo').id,
      })
        .then(res => {
          this.accountPhase = res.data.phase;
          this.getTBSearchInfo();
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 初始化不同角色需要传的参数
    initReqData() {
      let loginInfo = this.$sessionSave.get('loginInfo'),
        accountType = loginInfo.account_type;
      this.reqData.schoolId = this.$sessionSave.get('schoolInfo').id;
      if (accountType !== 4 && accountType !== 5) {
        this.reqData.gradeId = this.gradeId;
      }
      if (accountType === 1 || accountType === 2) {
        this.reqData.subjectId = this.subjectId;
        this.reqData.userId = accountType === 1 ? loginInfo.id : '';
      }
    },
    // 切换学校后更新数据
    changeSchool() {
      let $this = this;
      this.initSchool(function () {
        $this.getTBSearchInfo();
        $this.getTestPaperList();
      });
    },
    // 点击下载试卷显示下载弹窗
    downloadPaper(item) {
      this.downItem = item;
      this.showDialog = true;
      // if (this.downItem.source !== 5) {
      //   this.showDialog = false;
      //   window.open(`${item.fileUrl}`);
      // } else {
      //   this.showDialog = true;
      // }
    },
    // 点击试卷详情跳到试卷预览页
    goPreview(item) {
      this.$router.push({
        path: '/previewSchoolDetail',
        query: {
          tbId: item.id,
          schoolId: item.schoolId,
          subId: item.subjectCode,
          userId: this.$sessionSave.get('loginInfo').id,
          year: item.year || '',
          source: item.source,
          dlStatus: item.dlStatus,
          fileUrl: item.fileUrl,
          cardType: item.cardType,
          correctType: item.correctType,
        },
      });
    },
    async initSchool(cb) {
      var $this = this;
      const ret = await UserRole.getUserInfoPersonalityTest();
      ret.schSubList.unshift({
        id: '',
        name: '全部',
        subjectId: '',
        phaseId: '',
      });

      $this.subjectLists = ret.schSubList;
      let sub = $this.subjectLists.find(it => it.id == this.$sessionSave.get('loginInfo').subjectid);
      if ($this.subjectList.length > 0) {
        $this.subjectId = (sub && sub.id) || $this.subjectList[0].id;
      }

      ret.schGrdList.unshift({
        id: '',
        name: '全部',
        phaseId: '',
      });

      $this.gradeLists = ret.schGrdList;
      if (cb) {
        cb();
      }
    },
    // 获取表格列表
    getTestPaperList(type) {
      let loginInfo = this.$sessionSave.get('loginInfo');
      this.tableLoading = true;
      this.pagination.page = type === 'changePage' ? this.pagination.page : 1;
      let year = this.filterData.year || '';
      if (year == '全部') {
        year = '';
      }
      getTestPaperList({
        type: 1,
        schoolId: this.$sessionSave.get('schoolInfo').id,
        userId: loginInfo.id,
        isUse: 1,
        serviceVersion: 5,
        page: this.pagination.page,
        limit: this.pagination.pageSize,
        qcount: this.pagination.qcount,
        gradeCode: this.gradeId,
        subjectCode: this.reqData.subjectId || this.subjectId,
        name: this.filterData.tbName || '',
        typeCode: this.typeCode,
        typeName: this.categoryName,
        // year     : this.filterData.year || '',
        sortType: this.sortType,
        sortOrder: this.orderType,
        ...this.filterData,
        year: year,
      })
        .then(data => {
          this.tableLoading = false;
          this.tableData = data.data.rows || [];
          // let dataRes = data.data.rows || [];
          // this.tableData = dataRes.filter(it => this.filterData.categoryName === it.typeName)
          this.pagination.total_rows = data.data.total_rows;
          //  this.pagination.total_rows = this.tableData.length
          if (this.tableData.length) {
            this.tableData.forEach(item => {
              this.$set(item, 'subjectName', this.subjectMap[item.subjectId].name);
              this.$set(item, 'gradeName', this.getGradeName(item.gradeId));
            });
            if (!this.checkDownState(this.tableData)) {
              this.timer = setTimeout(() => {
                this.getTestPaperList();
              }, this.timerGap);
            } else {
              this.clearTimer();
            }
          }
        })
        .catch(err => {
          this.tableLoading = false;
        });
    },

    clearTimer() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
    },
    checkDownState(data) {
      let dataLength = data.length;
      for (let i = 0; i < dataLength; i++) {
        let item = data[i];
        if (item.dlStatus !== 1) {
          return false;
        }
      }
      return true;
    },
    // 获取学科列表
    getCategoryList(c30Filterondition) {
      // 获取题类
      this.categoryList = c30Filterondition;
      this.categoryList.unshift({
        id: '',
        name: '全部',
      });
      // this.listPaper();
      this.getTestPaperList();
    },
    // 分页查询
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.clearTimer();
      // this.listPaper('changePage');
      this.getTestPaperList('changePage');
    },
    // 根据gradeId获取gradeName
    getGradeName(id) {
      return this.gradeLists.filter(item => {
        return item.id == id;
      })[0].name;
    },
    // 删除试卷
    delXBResource(item) {
      this.$confirm('确定删除试卷吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        delXBResource({
          id: item.id,
        }).then(data => {
          this.$message.success('删除成功');
          // this.listPaper('changePage');
          this.getTestPaperList('changePage');
        });
      });
    },

    // 删除试卷
    deletePaper(item) {
      if (item.userId !== this.$sessionSave.get('loginInfo').id) {
        this.$message.error('您没有权限进行此操作！');
        return;
      }
      this.$confirm('确定删除试卷吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        delPaper({
          id: item.id,
        }).then(data => {
          this.$message.success('删除成功');
          // this.listPaper('changePage');
          this.getTestPaperList('changePage');
        });
      });
    },
    // 排序

    sortChange({ prop, order }) {
      console.log(order);
      if (prop === 'createTime') {
        this.sortType = 0;
      } else if (prop === 'qcount') {
        this.sortType = 1;
      } else if (prop === 'score') {
        this.sortType = 2;
      } else if (prop === 'subjectName') {
        this.sortType = 3;
      } else if (prop === 'gradeName') {
        this.sortType = 4;
      } else if (prop === 'creatorName') {
        this.sortType = 5;
      }
      if (order == 'descending') {
        this.orderType = 'desc';
      } else if (order == 'ascending') {
        this.orderType = 'asc';
      } else {
        this.orderType = '';
      }
      this.getTestPaperList();
    },
    // 切换年级
    changeGrade(val) {
      this.getTestPaperList();
    },
    // 切换学科
    changeSubject() {
      this.getTestPaperList();
    },
    // 切换试卷类型
    changeCategory() {
      this.getTestPaperList();
    },
    // 切换年份
    changeYear(info) {
      if (this.forbidOperate) return;
      this.filterData.year = info;
      this.$nextTick(() => {
        this.getTestPaperList();
      });
    },
    getTBSearchInfo() {
      let phase = 3;
      if (this.accountPhase) {
        phase = Number(this.accountPhase.split(',')[0]);
      }
      let $this = this;
      getTBSearchInfo({ phase: phase }, function (data) {
        $this.yearList = (data && data.years) || [];
        $this.yearList.unshift('全部');
        $this.filterData.year = '全部';

        let c30 = data.sources;
        // 获取题类
        let otherIndex = -1,
          otherObj = {};
        c30.forEach((item, index) => {
          if (item.name === '其他') {
            otherIndex = index;
            otherObj = item;
          }
        });
        if (otherIndex !== -1) {
          c30.splice(otherIndex, 1);
          c30.push(otherObj);
        }
        $this.getCategoryList(c30);
      });
    },
    // 搜索
    searchReport() {
      this.filterData.tbName = this.filterData.tbName.trim();
      this.pagination.page = 1;
      this.getTestPaperList();
    },
  },
  beforeDestroy() {
    this.clearTimer();
  },
};
</script>

<style lang="scss" scoped>
.paper-container {

  .header-filter,
  .body-content {
    padding: 16px 18px;
    background: #fff;
    // box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
    border-radius: 6px;
  }

  .header-filter {
    .header__select {
      display: inline-block;
      margin-right: 29px;
      float: left;

      .year-select,
      .grade-select,
      .subject-select {
        width: 150px;
      }
    }

    .header__serarch {
      display: flex;
      width: 240px;

      .search__icon {
        width: 38px;
        font-size: 18px;
        color: #fff;
        background: #409eff;
        border-radius: 0 3px 3px 0;
        outline: none;
        cursor: pointer;
      }
    }
  }

  .body-content {
    // margin-top: 20px;
  }
}

.showOverTooltip {
  display: -webkit-box;
  position: relative;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
<style lang="scss">
.downDialog {
  .title {
    font-size: 16px;
    height: 40px;
    line-height: 40px;
  }

  .set-ul {
    margin-bottom: 20px;

    >li {
      display: inline-block;
      font-size: 16px;
      text-align: center;
      background-color: #f4f4f5;
      color: #909399;
      border: 1px solid #f4f4f5;
      border-radius: 4px;
      margin-right: 30px;
      cursor: pointer;

      &.active {
        color: #409eff;
        background: #ecf5ff;
        border: 1px solid #b3d8ff;
      }
    }
  }

  .paperSize-ul {
    >li {
      min-width: 90px;
      height: 40px;
      line-height: 40px;
    }
  }

  .paperType-ul {
    >li {
      width: 145px;
      height: 60px;
      line-height: 60px;
      padding: 10px 0;
      text-align: center;

      &:last-child {
        margin-right: 0;
      }

      .user {
        line-height: 20px;
        color: #3f4a54;
      }

      .type {
        line-height: 16px;
        font-size: 13px;
        margin-top: 3px;
      }

      &.active {
        .user {
          color: #409eff;
        }

        .type {
          color: #909399;
        }
      }
    }
  }
}

.search__text {
  .el-input__inner {
    border-radius: 4px 0 0 4px;
  }
}

.schoolTable {
  .editCell {
    >span {
      color: #409eff;
      display: inline-block;
      margin-right: 10px;
      cursor: pointer;

      &:last-child {
        margin-right: 0;
      }

      &.disabled {
        opacity: 0.6;
        cursor: no-drop;
      }
    }

    .el-icon-more {
      width: 40px;
      text-align: center;
      transform: rotate(90deg);
    }
  }
}

.schoolTable {
  border: 1px solid #e4e8eb;

  &.el-table th,
  &.el-table td {
    text-align: center;
  }

  &.el-table thead {
    font-size: 16px;
    color: rgb(63, 74, 84);
  }

  &.el-table th.el-table__cell {
    background-color: #f5f7fa !important;
  }

  .el-table td,
  .el-table th.is-leaf {
    border: 0.5px solid #ebeef5;
  }

  .square {
    width: 0;
    height: 0;
    border: 16px solid transparent;
    border-top: 16px solid #409eff;
    border-left: 16px solid #409eff;
    z-index: 100;
    border-radius: 10px 0 0 0;
    position: absolute;
    left: 0;
    top: 0;

    .square-word {
      position: absolute;
      left: -12px;
      top: -16px;
      color: #ffffff;
      font-size: 13px;
    }
  }
}

.editPopover {
  width: 100px !important;
  min-width: initial;
  margin-top: 0 !important;

  .edit-ul {
    >li {
      cursor: pointer;

      &.disabled {
        opacity: 0.6;
        cursor: no-drop;
      }
    }
  }
}
</style>

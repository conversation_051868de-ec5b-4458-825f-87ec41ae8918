@if $use-slideOutLeft == true {

	@-webkit-keyframes slideOutLeft {
		0% {
			-webkit-transform: translate3d(0, 0, 0);
			visibility: visible;
		}

		100% {
			-webkit-transform: translate3d(-100%, 0, 0);
		}
	}

	@keyframes slideOutLeft {
		0% {
			transform: translate3d(0, 0, 0);
			visibility: visible;
		}

		100% {
			transform: translate3d(-100%, 0, 0);
		}
	}

	.slideOutLeft {
		@include animate-prefixer(animation-name, slideOutLeft);
	}

}

/*
 * @Descripttion: 
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-09-01 16:24:04
 * @LastEditors: <PERSON><PERSON><PERSON>
 */

/**
 * 数据返回的结构
 **/
export interface ReqStructure<T> {
    page: number
    page_count: number
    // 数据列表
    rows: T[]
    total_rows: number
}

/**
 * 资源的额外状态
 **/
export interface ResExtraStates {
    downloadType?: string // 下载状态
    progress?: number // 进度条
    allowInsertPPT?: boolean // 允许插入PPT
}


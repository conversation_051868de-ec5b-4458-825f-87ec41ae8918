/*
 * @Descripttion: 
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2020-03-19 16:43:33
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-11-21 20:48:16
 */
import { Canceler } from 'axios';
import { IDBPDatabase } from 'idb';

declare global {
  interface Window {
    idbStore: IDBPDatabase;
    mozRequestAnimationFrame: Function;
    oRequestAnimationFrame: Function;
    msRequestAnimationFrame: Function;
    webkitRequestAnimationFrame: (callback: FrameRequestCallback) => number;
    httpVueLoader: any;
    // cef通信方法
    cef: {
      message: {
        sendMessage: Function
      }
      fs: {
        fileExists: Function
        readAll: Function
        writeAll: Function
      }
    }
    executePdu: Function;
    excecuCmd: Function
    wincore: any
    // 禁用手写板
    disableAiwriter: boolean
    // 验证当前端的值
    dtClientKeys: any;
    // 注入后声明的cef对象
    win_cefBrowser: any
    // 注入后声明的loading类
    CefLoading: any
    // 日志工具
    logger: any
    // 移动端通信bridge
    WVJBCallbacks: Array<any>
    WebViewJavascriptBridge: any,
    setupWebViewJavascriptBridge: any,
    // 观察者工具
    subsEventTool: any
    // 打开日志对控制台的输出
    openLogout: boolean
    // 获取当前窗口应用信息
    getDtAppInfo: Function
    // 请求信息集合
    pendingRequest: Map<string, Canceler>

    BChannel: any;
    getModuleExplain: Function
  }

  interface Document {
    webkitExitFullscreen: Function
    fullScreen: Function
    mozFullScreen: Function
    webkitIsFullScreen: Function
  }
}

/** 题卡异常类型*/
export const ERRORCARD_TYPE = [
  {
    text: '答卷异常',
    type: 'paper',
  },
  {
    text: '页码异常',
    type: 'page',
  },
  {
    text: '定位点异常',
    type: 'point',
  },
  {
    text: '考号异常',
    type: 'exam',
  },
  {
    text: '缺考异常',
    type: 'absent',
  },
  {
    text: 'AB卷异常',
    type: 'abCard',
  },
  {
    text: '客观题异常',
    type: 'objective',
  },
  {
    text: '选做题异常',
    type: 'choice',
  },
  {
    text: '主观题异常',
    type: 'subject',
  },
];
export const ERROR_TYPE = {
  paper: {
    text: '答卷异常',
    type: 'paper',
  },
  page: {
    text: '页码异常',
    type: 'page',
  },
  point: {
    text: '定位点异常',
    type: 'point',
  },
  exam: {
    text: '考号异常',
    type: 'exam',
  },
  absent: {
    text: '缺考异常',
    type: 'absent',
  },
  abCard: {
    text: 'AB卷异常',
    type: 'abCard',
  },
  objective: {
    text: '客观题异常',
    type: 'objective',
  },
  choice: {
    text: '选做题异常',
    type: 'choice',
  },
  subject: {
    text: '主观题异常',
    type: 'subject',
  },
};
/**
 * @name:错误提示
 */
export const ERROR_TIPS = {
  paper: {
    reason: '答卷可能存在残缺或扫错答卷',
    tips: [
      '1. 若答卷二维码不完整，请点击“重新识别”进行页码设置。',
      '2. 若答卷是扫错或多余的，请点击“删除答卷”。',
      '3. 若大量异常均属同一类问题，请选择“批量操作”。',
    ],
    video: "https://fs.iclass30.com/wj/scan_paper.mp4"
  },
  page: {
    reason: '页码未识别或扫描张数少于/多于实际答卷张数',
    tips: [
      '1. 若因考号填涂错误导致缺页或多页，请手动修改并搜索确认。',
      '2. 若无法确认页码信息或扫错答卷，请点击“删除答卷”。',
      '3. 若答卷是多余的，请点击“删除答卷”。',
      '4. 若答卷缺页，请根据扫描批次找到对应的答卷进行补扫，建议先处理完各类异常再确定是否为缺页。',
      '5. 若大量异常均需删除，请选择“批量操作”。',
    ],
    video: "https://fs.iclass30.com/wj/scan_page.mp4"
  },
  exam: {
    reason: '考号未识别、填涂错误或重复扫描',
    tips: [
      '1. 若考号识别有误，请手动修改并搜索确认。',
      '2. 若考号未填涂，请根据姓名搜索考号并确认；若无姓名请反馈老师确定解决方案。',
      '3. 若考号填涂重复，请确认考号是否填涂错误，若是则先处理涂错学生的答卷，填涂正确的，进行搜索确认；若为重复扫描，请删除重复答卷。',
      '4. 若考号识别出来但不在参考学生列表，请确认考号是否填涂错误，若填涂错误请手动修改并搜索确认；若填涂准确请反馈老师确定处理方案。',
      '5. 若大量异常均需删除，请选择“批量操作”。',
    ],
    video: "https://fs.iclass30.com/wj/scan_exam.mp4"
  },
  objective: {
    reason: '客观题未涂或多涂',
    tips: [
      '1. 请根据客观题实际填涂结果修改，每页修改完成后，请点击“提交本页”。',
      '2. 若发现处理错了，已处理中支持二次修改。',
    ],
    video: "https://fs.iclass30.com/wj/scan_object.mp4"
  },
  choice: {
    reason: '选做题未涂或多涂',
    tips: [
      '1. 请根据选做题实际填涂结果修改，每页修改完成后，请点击“提交本页”。',
      '2. 若发现处理错了，已处理中支持二次修改。',
    ],
    video: "https://fs.iclass30.com/wj/scan_object.mp4"
  },
  subject: {
    reason: '主观题批改痕迹不清晰或未批改',
    tips: [
      '1. 请根据主观题的实际批改结果修改，每页修改完成后，请点击“提交本页”。',
      '2. 若老师漏批，可根据学生作答情况修改。',
      '3. 若发现处理错了，已处理中支持二次修改。',
    ],
    video: "https://fs.iclass30.com/wj/scan_subject.mp4"
  },
  point: {
    reason: '定位信息可能存在残缺或扫错答卷',
    tips: [
      '1. 拖动定位点红框，使其与模板定位信息匹配后，点击“重新识别”。',
      '2. 若重新识别或者重扫均无法解决时，请确认考生信息后反馈老师确定处理方案。',
      '3. 若答卷是扫错或多余的，请点击“删除答卷”。',
      '4. 若大量异常均属同一类问题，请选择“批量操作”。',
    ],
    video: "https://fs.iclass30.com/wj/scan_point.mp4"
  },
  absent: {
    reason: '系统识别缺考标记被填涂',
    tips: [
      '1. 若确定学生为缺考，直接点击提交。',
      '2. 若确定学生为不缺考，点击设为非缺考，然后提交。',
    ],
    video: "https://fs.iclass30.com/wj/scan_absent.mp4"
  },
  abCard: {
    reason: '答卷可能存在残缺或扫错答卷',
    tips: [
      '1. 若确定学生为缺考，直接点击提交。',
      '2. 若确定学生为不缺考，点击设为非缺考，然后提交。',
    ],
    video: "https://fs.iclass30.com/wj/scan_absent.mp4"
  },
};
/**
 * @name:处理类型
 */
export const HANDLE_TYPE = {
  // 未处理
  pending: 0,
  // 已处理
  processed: 1,
};
/**
 * @name:图片异常类型
 */
export const IMG_ERRTYPE_WORD = {
  0: '无异常',
  1: '答卷异常', 
  2: '定位点不正确', 
  4: '缺页',
  8: '考号识别错误！',
  16: '考号重复！',
  32: '考号不存在！',
  64: '题目异常！',
  128: '考号重复未处理！',
  256: '缺考异常',
  512: '选做题异常',
  1024:' AB卷异常'
};
/**
 * @name:题目异常类型
 */
export const QUES_ERRTYPE_WORD = {
  0: '无异常',
  1: '未涂！', //未涂
  2: '多涂！', //多涂
  3: '未批！', //未批
  4: '分数超出！', //分数超出
  5: '多个批改！', //多个批改
  6: '临近异常！', //临近异常
  7: '少涂异常', //少涂异常
  8: '重涂异常', //重涂异常
  20: '识别确认'
};
/**
 * @name:处理列表类型
 */
export const HANDLE_LIST = [
  {
    type: HANDLE_TYPE.pending,
    name: '待处理',
    num: 0,
  },
  {
    type: HANDLE_TYPE.processed,
    name: '已处理',
    num: 0,
  },
];

/**
 * @name: 特殊题型类型
 */
export const ISPECIAL_QUES_TYPE = {
  /** 无  */
  none : 0,
  /** 送分题 */
  sendScore : 1,
  /** 0分题 */
  zeroScore : 2,
  /** 附加题 */
  appendix : 3,
  /** 作文题*/
  essay : 4,
  /** 作文智批题*/
  aiEssay : 5,
}
/**
 * @name:处理列表类型
 */
// specialTypeId:0:无 1:送分题 2:0分题 3:附加题
export const SPECIAL_QUES = [
  {
    value: ISPECIAL_QUES_TYPE.appendix,
    label: '附加题',
  },
  {
    value: ISPECIAL_QUES_TYPE.sendScore,
    label: '送分题',
  },
  {
    value: ISPECIAL_QUES_TYPE.zeroScore,
    label: '0分题',
  },
  {
    value: ISPECIAL_QUES_TYPE.none,
    label: '无',
  },
];
/**
 * @name: 三方卡操作步骤
 */
export const ThirdCardStep = {
  // 框选定位信息
  anchordot: {
    id: '1',
    type: 'anchordot',
    tips: '每页需框选四个定位点，请在答题卡四个角框选较为特殊的区域作为定位点。',
    name: '定位信息',
    item_type: 10,
  },
  // 框选标题
  title: {
    id: '2',
    type: 'title',
    tips: '请选择一行较长的完整文字，每页均需框选。',
    name: '标题',
    item_type: 11,
  },
  // 框选考号
  number: {
    id: '3',
    type: 'number',
    tips: '支持4~14位考号，仅正面需要框选。',
    name: '考号',
    item_type: 2,
  },
  // 框选客观题
  object: {
    id: '4',
    type: 'object',
    tips: '需带题号框选，不同题型请分开框选。题目顺序需与答题卡一致！',
    name: '客观题',
    item_type: 3,
  },
  // 框选主观题
  subject: {
    id: '5',
    type: 'subject',
    tips: '请确保框选区域大于作答区域。',
    name: '主观题',
    item_type: 4,
  },
  // 框选其他标记
  others: {
    id: '6',
    type: 'others',
    tips: '支持框选缺考标记和AB卷标记，请确保框选区域和实际填涂区域一致。',
    name: '其他标记',
    item_type: 8,
  },
  // 二维码
  // qrCode: {
  //   id: '12',
  //   type: 'qrCode',
  //   tips: '请选择一行较长的完整文字，每页均需框选。',
  //   name: '二维码',
  //   item_type: 12,
  // },
};
export const ITEM_TYPE = {
  // 二维码
  QRCODE: 1,
  // 考号
  EXAMNUMBER: 2,
  //填涂题
  FILLING: 3,
  // 划线题
  LINE: 4,
  //注意事项
  NOTICE: 5,
  //条形码
  BARCODE: 6,
  // 智能批改
  LINE_EVA: 7,
  // 缺考标记
  MISS_EXAM: 8,
  //定位点
  POINT_DOT: 10,
};

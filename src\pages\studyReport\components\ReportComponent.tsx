/*
 * @Description:
 * @Author: 小圆
 * @Date: 2024-03-25 10:20:01
 * @LastEditors: 小圆
 */
import { Component, Vue } from 'vue-property-decorator';
import FilterModule from '@/pages/studyReport/plugins/FilterModule';

@Component
export default class ReportComponent extends Vue {
  FilterModule: typeof FilterModule = FilterModule;

  // 获取筛选器信息
  get selectOption() {
    return this.FilterModule.selectOption;
  }

  // 获取筛选对象
  get filterInfo() {
    return this.FilterModule.filterInfo;
  }

  get filterData() {
    return this.FilterModule.filterData;
  }

  onChange(value) {}
}

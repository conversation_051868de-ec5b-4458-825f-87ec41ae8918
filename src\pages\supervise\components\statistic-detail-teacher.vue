<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-11-05 11:13:58
 * @LastEditors: 小圆
-->
<template>
  <div class="statistic-scan clearfix" v-loading="isLoading">
    <el-input
      style="width: 220px"
      v-model.trim="keyWord"
      placeholder="请输入姓名搜索"
      @keyup.enter.native="changePage(1)"
    >
      <i slot="suffix" class="input__icon el-input__icon el-icon-search" @click="changePage(1)"></i>
    </el-input>
    <el-table
      stripe
      :data="examTeaStatList"
      :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
      :header-cell-class-name="'table-sort-cell'"
      :default-sort="{ prop: 'examNum', order: 'descending' }"
      :cell-style="getCellStyle"
      style="margin-top: 10px; width: 100%; border: 1px solid #ebeef5"
      v-drag-table
      v-sticky-table="0"
      @sort-change="changeSort"
    >
      <el-table-column prop="no" label="序号" align="center" width="80"> </el-table-column>
      <el-table-column prop="teaName" label="姓名" align="center" width="160"> </el-table-column>
      <el-table-column prop="className" label="授课班级" align="center" width="220">
      </el-table-column>
      <el-table-column prop="subjectName" label="任教学科" align="center"> </el-table-column>
      <el-table-column prop="examNum" label="测评数" align="center" sortable="custom">
        <template #header>
          <span>测评数</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="所选时间内，网阅、手阅和补录的测评总数"
            placement="top"
          >
            <i class="header-icon el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="handExamNum" label="手阅" align="center" sortable="custom">
      </el-table-column>
      <el-table-column prop="onlineExamNum" label="网阅" align="center" sortable="custom">
      </el-table-column>
      <el-table-column prop="makeUpNum" label="补录" align="center" sortable="custom">
      </el-table-column>
      <el-table-column prop="submitRate" label="提交率%" align="center" sortable="custom">
        <template #default="scope">
          <span>{{ scope.row.submitRate }}%</span>
        </template>
      </el-table-column>
      <el-table-column prop="uploadTbNum" label="上传试卷" align="center"> </el-table-column>
      <el-table-column prop="shareTbNum" label="分享试卷" align="center">
        <template #header="scope">
          <span>分享试卷</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="所选时间内，试卷分享至校本的数量"
            placement="top"
          >
            <i class="header-icon el-icon-question"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-if="pagination.total > 0"
      background
      style="margin: 30px 0"
      class="text-center"
      layout="total, prev, pager, next, sizes"
      @current-change="changePage"
      @size-change="changeSize"
      :current-page.sync="pagination.page"
      :page-size="pagination.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="pagination.total"
    >
    </el-pagination>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import { QueryData } from './types';
import { getExamTeaStatDetails } from '@/service/pexam';
import NoData from '@/components/noData.vue';
import { getToken } from '@/service/auth';
import moment from 'moment';

export interface ExamTeaStat {
  teaId: string;
  teaName: string;
  className: string;
  subjectName: string;
  examNum: number;
  handExamNum: string;
  onlineExamNum: string;
  makeUpNum: string;
  submitRate: number;
  uploadTbNum: number;
  shareTbNum: number;
}

@Component({
  components: {
    NoData,
  },
})
export default class StatisticDetailTeacher extends Vue {
  @Prop({ default: {} }) queryData: QueryData;

  // 搜索关键字
  keyWord: string = '';
  // 教师统计数据列表
  examTeaStatList: ExamTeaStat[] = [];
  // 分页器
  pagination = {
    page: 1,
    pageSize: 20,
    total: 0,
  };
  /** 0-按测评数排序，1-手阅数排序，2-网阅数排序，3-提交率排序 */
  orderType = '0';
  /** 是否降序排序：0-否，1-是 */
  desc = '1';

  isLoading = false;

  mounted() {
    this.getExamTeaStat();
    this.$bus.$on('statistic-change', this.onQueryDataChange);
    this.$bus.$on('handle-export', this.handleExport);
  }

  beforeDestroy() {
    this.$bus.$off('statistic-change', this.onQueryDataChange);
    this.$bus.$off('handle-export');
  }

  onQueryDataChange() {
    this.changePage(1);
  }

  async getExamTeaStat() {
    try {
      this.isLoading = true;
      const res = await getExamTeaStatDetails({
        ...this.getParams(),
        page: this.pagination.page,
        limit: this.pagination.pageSize,
      });
      let list = res.data.rows || [];
      // 添加序号
      list.forEach((item, index) => {
        item.no = (this.pagination.page - 1) * this.pagination.pageSize + index + 1;
      });
      this.examTeaStatList = list;
      this.pagination.total = res.data.total_rows;
    } catch (error) {
      console.error(error);
      this.examTeaStatList = [];
      this.pagination.total = 0;
    }
    this.isLoading = false;
  }

  changeSort({ column, prop, order }) {
    this.desc = order == 'descending' ? '1' : order == 'ascending' ? '0' : '';
    if (this.desc == '') {
      this.orderType = '0';
      this.desc = '1';
      this.getExamTeaStat();
      return;
    }
    let propMap = {
      examNum: '0',
      handExamNum: '1',
      onlineExamNum: '2',
      makeUpNum: '3',
      submitRate: '4',
    };
    this.orderType = propMap[prop];
    this.getExamTeaStat();
  }

  changePage(page) {
    this.pagination.page = page;
    this.getExamTeaStat();
  }

  changeSize(val) {
    this.pagination.page = 1;
    this.pagination.pageSize = val;
    this.getExamTeaStat();
  }

  handleExport() {
    if (!this.examTeaStatList.length) {
      this.$message.warning('没有数据可以导出');
      return;
    }

    const params = this.getParams();
    const query = new URLSearchParams(params);
    const url = `${
      process.env.VUE_APP_KKLURL
    }/pexam/scanExam/exportExamTeaStatDetails?${query.toString()}`;
    window.open(url, '_blank');
  }

  getParams() {
    let token = getToken();
    return {
      schoolId: this.$sessionSave.get('schoolInfo').id,
      gradeId: this.queryData.gradeValue,
      subjectId: this.queryData.subjectValue,
      categoryId: this.queryData.categoryValue,
      keyWord: this.keyWord,
      orderType: this.orderType, // 0-按测评数排序，1-手阅数排序，2-网阅数排序，3-提交率排序
      desc: this.desc, // 是否降序排序：0-否，1-是
      startTime: moment(this.queryData.dateRange[0]).format('YYYY-MM-DD 00:00:00'),
      endTime: moment(this.queryData.dateRange[1]).format('YYYY-MM-DD 23:59:59'),
      token,
    };
  }

  getCellStyle({ row, column, rowIndex, columnIndex }) {
    let redStyle = { color: 'red', backgroundColor: 'rgb(253 163 163 / 30%)' };
    if (column.property == 'submitRate' && row.submitRate < 60) {
      return redStyle;
    }
  }
}
</script>

<style scoped lang="scss">
.header-icon {
  margin-bottom: 0;
}

.input__icon {
  cursor: pointer;

  &:hover {
    color: #409eff;
  }
}
</style>

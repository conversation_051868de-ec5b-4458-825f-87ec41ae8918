<template>
    <div class="card-container">
        <el-row>
            <el-button type="primary" @click="addNewCard">新建答题卡</el-button>
            <el-button :type="pageType === 'taskList' ? 'primary' : ''" @click="uploadPaper">上传试卷制卡</el-button>
        </el-row>
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="我的答题卡" name="my"></el-tab-pane>
            <el-tab-pane label="学校模板卡" name="school"></el-tab-pane>
        </el-tabs>
        
    </div>
</template>

<script>
export default {
    name: "test-manage-index",
    data() {
        return {
            //页面类型
            activeName: 'my'
        }
    },
    methods: {
        /**
        * @name: 新建答题卡
        */
        addNewCard() {
        },
        /**
        * @name: 上传试卷制卡
        */
        uploadPaper() {
        },
        /**
        * @name: tab点击事件
        */
        handleClick(tab, event) {
            console.log(tab, event);
        }
    },
    mounted() {

    }
}
</script>

<style lang="scss" scoped>
.card-container {
    padding-right: 50px;
    overflow-y: auto;
    height: 100%;
}
</style>
<template>
  <div v-html="showHtml"></div>
</template>

<script>
import { convertHtml } from '@/utils/common';

export default {
  name: 'latex-html',
  props: {
    html: '',
    subject: {
      type: [String, Number],
      default: () => {
        return '';
      },
    },
  },
  data() {
    return {};
  },
  computed: {
    showHtml() {
      return convertHtml(this.html, this.subject);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>

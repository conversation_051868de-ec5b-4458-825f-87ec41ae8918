<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-06-11 17:15:49
 * @LastEditors: 小圆
-->
<template>
  <div>
    <div class="custom-section" v-if="subjectList.length">
      <div class="header-title">赋分方式</div>
      <el-table id="ScoringTable" :data="data" border>
        <el-table-column
          label="学科"
          prop="label"
          width="140"
          align="center"
          fixed
          :resizable="false"
        ></el-table-column>
        <el-table-column
          v-for="item in subjectList"
          align="center"
          :label="item.subjectName"
          :resizable="false"
          :min-width="120"
        >
          <template #default="scope">
            <!-- 总分记分 -->
            <div v-if="scope.row.prop === 'scoreType'">
              <el-popover placement="bottom" width="200" trigger="click">
                <el-radio-group
                  :value="subjectRuleMap[String(item.subjectId)]['scoreType']"
                  @input="val => changeScoreType(item.subjectId, val)"
                >
                  <el-radio :label="1" style="margin-top: 10px">得分</el-radio>
                  <el-radio :label="2" style="margin-top: 10px">等级赋分</el-radio>
                  <el-radio :label="3" style="margin-top: 10px">不计入总分</el-radio>
                </el-radio-group>
                <span class="edit-cell" slot="reference">
                  <span>
                    {{
                      getScoreTypeByValue(subjectRuleMap[String(item.subjectId)]['scoreType']).label
                    }}
                  </span>
                  <i class="el-icon-edit-outline edit-cell__icon"></i>
                </span>
              </el-popover>
            </div>

            <!-- 等级 -->
            <div v-if="scope.row.prop === 'level'">
              <span>
                {{
                  subjectRuleMap[item.subjectId]['level'] == 0
                    ? '/'
                    : subjectRuleMap[item.subjectId]['level'] +
                      '等: ' +
                      getScoreTemplateByValue(subjectRuleMap[item.subjectId]['scoreTemplate'])
                        .label +
                      '-' +
                      getLevelTypeByValue(subjectRuleMap[item.subjectId]['levelType']).label
                }}
              </span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="custom-section" v-if="currentSubjectRule && subjectSettingList.length">
      <div class="header-title">赋分设置</div>
      <div class="setting-item">
        <span>选择学科：</span>
        <el-select :value="currentSubjectId" @change="changeSubject">
          <el-option
            v-for="item in subjectSettingList"
            :key="item.subjectId"
            :label="item.subjectName"
            :value="item.subjectId"
          >
          </el-option>
        </el-select>
      </div>
      <div class="setting-item">
        <div>
          <span>选择赋分模板：</span>
          <el-radio v-model="currentSubjectRule['scoreTemplate']" :label="1">分值赋分</el-radio>
          <el-radio v-model="currentSubjectRule['scoreTemplate']" :label="2">区间赋分</el-radio>
        </div>
      </div>

      <div class="setting-item clearfix">
        <div class="fl">
          <span>等级划分：</span>
          <el-radio v-model="currentSubjectRule['levelType']" :label="1">按比例</el-radio>
          <el-radio v-model="currentSubjectRule['levelType']" :label="2">按分数</el-radio>
        </div>

        <div class="fr">
          <span class="tip">提示: 完成当前学科设置后请点击保存</span>
          <el-button
            style="margin-left: 20px"
            type="primary"
            size="small"
            :loading="saveLoading"
            @click="saveCurrentSubjectRule()"
            >保存</el-button
          >
        </div>
      </div>
      <div>
        <el-table :data="currentSubjectRule['json']" border>
          <el-table-column
            label="赋分等级"
            prop="level"
            align="center"
            width="230"
            :resizable="false"
          >
            <template #default="scope">
              <el-input
                class="table-input"
                v-model="scope.row.level"
                placeholder="赋分等级"
              ></el-input>
            </template>
          </el-table-column>

          <template v-if="currentSubjectRule['levelType'] == 1">
            <el-table-column label="等级人数比例" prop="ratio" align="center" :resizable="false">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.ratio"
                  class="table-input"
                  placeholder="人数比例"
                  :precision="2"
                  :controls="false"
                  :min="0"
                  :max="100"
                ></el-input-number>
                %
              </template>
            </el-table-column>
            <el-table-column label="累计人数比例" align="center" :resizable="false">
              <template #default="scope">
                <span
                  >{{
                    getJsonAddRatio(currentSubjectRule['json'], scope.$index) >= 0
                      ? getJsonAddRatio(currentSubjectRule['json'], scope.$index)
                      : '--'
                  }}%</span
                >
              </template>
            </el-table-column>
          </template>
          <template v-if="currentSubjectRule['levelType'] == 2">
            <el-table-column label="卷面分" align="center" :resizable="false">
              <template #default="scope">
                <div class="table-range">
                  <span class="range-left">[</span>
                  <el-input-number
                    class="range-input table-input"
                    v-model="scope.row.addStart"
                    placeholder="分数"
                    :controls="false"
                    :min="0"
                    :max="scope.row.addEnd - 1 || Infinity"
                    @change="
                      (val, oldVal) =>
                        changeJsonAddStart(currentSubjectRule['json'], scope.$index, val, oldVal)
                    "
                  ></el-input-number>

                  <span class="range-line"></span>
                  <el-input-number
                    class="range-input table-input"
                    v-model="scope.row.addEnd"
                    placeholder="分数"
                    :controls="false"
                    :min="0"
                    :max="getSubjectFullScore(currentSubjectId)"
                    @change="
                      (val, oldVal) =>
                        changeJsonAddEnd(currentSubjectRule['json'], scope.$index, val, oldVal)
                    "
                  ></el-input-number>

                  <span v-if="scope.$index == 0" class="range-right">]</span>
                  <span v-else class="range-right">)</span>
                </div>
              </template>
            </el-table-column>
          </template>

          <el-table-column label="赋分分数" align="center" :resizable="false">
            <template #default="scope">
              <template v-if="currentSubjectRule['scoreTemplate'] == 1">
                <el-input-number
                  v-model="scope.row.score"
                  class="table-input"
                  @change="val => changeJsonScore(currentSubjectRule['json'], scope.$index, val)"
                  :controls="false"
                  :min="0"
                  :max="Infinity"
                  placeholder="赋分分数"
                ></el-input-number>
              </template>

              <template v-if="currentSubjectRule['scoreTemplate'] == 2">
                <div class="table-range">
                  <span class="range-left">[</span>
                  <el-input-number
                    class="range-input table-input"
                    v-model="scope.row.start"
                    placeholder="赋分分数"
                    step-strictly
                    :controls="false"
                    :min="0"
                    :max="scope.row.end || Infinity"
                    @change="
                      (val, oldVal) =>
                        changeJsonScoreStart(currentSubjectRule['json'], scope.$index, val, oldVal)
                    "
                  ></el-input-number>

                  <span class="range-line"></span>
                  <el-input-number
                    class="range-input table-input"
                    v-model="scope.row.end"
                    placeholder="赋分分数"
                    step-strictly
                    :controls="false"
                    :min="0"
                    :max="Infinity"
                    @change="
                      (val, oldVal) =>
                        changeJsonScoreEnd(currentSubjectRule['json'], scope.$index, val, oldVal)
                    "
                  ></el-input-number>
                  <span class="range-right">]</span>
                </div>
              </template>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" :resizable="false">
            <template #default="scope">
              <el-link
                class="action-btn el-icon-remove-outline"
                :underline="false"
                @click="removeJsonRow(currentSubjectRule['json'], scope.$index)"
              ></el-link>
              <el-link
                class="action-btn el-icon-circle-plus-outline"
                :underline="false"
                @click="addJsonRow(currentSubjectRule['json'], scope.$index)"
              ></el-link>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div v-if="subjectList.length" style="text-align: right; margin-top: 20px; margin-right: 20px">
      <span class="tip"> 提示：完成所有学科设置后请点击生成报告 </span>
      <el-button type="primary" @click="saveRule" :loading="saveLoading">生成报告</el-button>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';

import {
  delExamScoreRuleBySubjectId,
  getExamScoreRuleByRuleId,
  getExamScoreRuleBySubjectId,
  getCustomSubject,
  saveScoreRuleSet,
  setExamScoreRule,
  setScoreRuleId,
} from '@/service/pexam';

export interface IExamScoreRule {
  id: string;
  sourceId: string;
  sourceType: number;
  name: string;
  mode: string;
  subjectIds: string;
  subjectNames: string;
  rule: string;
  includedScore: number;
  divType: number;
  sort: number;
}

interface ISubject {
  subjectId: string;
  subjectName: string;
  fullScore: number;
}

interface ISubjectRuleMap {
  [x: string]: ISubjectRule;
}

interface IJsonType {
  level: string; // 等级
  ratio: number; // 等级人数比例 - ratio
  score: number; // 赋分分数 对应接口 - start
  start: number; // 赋分分数区间 对应接口 - start
  end: number; // 赋分分数区间 对应接口 - end
  addStart: number; // 卷面分区间 对应接口 - addStart
  addEnd: number; // 卷面分区间 对应接口 - addEnd
}

interface ISubjectRule {
  id: string; // 规则Id
  scoreType: 1 | 2 | 3; // 记分方式 1: 得分，2: 等级赋分，3: 不计入总分
  scoreTemplate: 1 | 2; // 赋分模板 1: 分值赋分，2: 区间赋分
  levelType: 1 | 2; // 等级划分 1: 按比例，2: 按分数
  level: number; // 等级（几等）

  json: IJsonType[];
}

const divTypeMap = {
  // 映射关系（scoreTemplate + levelType）
  '1_1': 1,
  '1_2': 2,
  '2_1': 3,
  '2_2': 4,
};

@Component
export default class ExamCusTom extends Vue {
  // 表格数据
  data: any = [
    {
      label: '总分记分',
      prop: 'scoreType',
    },
    { label: '等级', prop: 'level' },
  ];

  // 总分记分列表
  scoreTypeList: any = [
    {
      value: 1,
      label: '得分',
    },
    {
      value: 2,
      label: '等级赋分',
    },
    {
      value: 3,
      label: '不计入总分',
    },
  ];

  // 赋分模板列表
  scoreTemplateList: any = [
    {
      value: 1,
      label: '分值赋分',
    },
    {
      value: 2,
      label: '区间赋分',
    },
  ];

  // 等级划分列表
  levelTypeList: any = [
    {
      value: 1,
      label: '按比例',
    },
    {
      value: 2,
      label: '按分数',
    },
  ];

  // 学科列表
  subjectList: ISubject[] = [];
  // 学科对象
  subjectRuleMap: ISubjectRuleMap = {};
  // 当前选择的学科
  currentSubjectId: string = '';
  // 默认规则
  defaultRule = null;

  // 保存状态
  saveLoading: boolean = false;

  // 当前学科规则
  get currentSubjectRule() {
    return this.subjectRuleMap[this.currentSubjectId];
  }

  // 选择学科列表
  get subjectSettingList(): ISubject[] {
    let arr = [];
    this.subjectList.forEach(item => {
      if (this.subjectRuleMap[item.subjectId]['scoreType'] == 2) {
        arr.push(item);
      }
    });
    return arr;
  }

  mounted() {
    this.initDefaultRule();
    this.getExamSubjectList();
  }

  async initDefaultRule() {
    const scoreRuleId = this.$sessionSave.get('reportDetail').scoreRuleId;
    if (!scoreRuleId) return;
    const { data } = await getExamScoreRuleByRuleId({ id: scoreRuleId });
    data.rule = JSON.parse(data.rule);
    this.defaultRule = data.rule;
  }

  // 获取考试学科
  async getExamSubjectList() {
    var reportDetail = this.$sessionSave.get('reportDetail');
    const examId = reportDetail.examId;
    let examSubjectList = (await getCustomSubject({ examId: examId })).data; // 获取学科

    const ruleRes = await getExamScoreRuleBySubjectId({
      sourceId: examId,
      sourceType: 1,
      subjectId: '',
    });
    const rules: IExamScoreRule[] = ruleRes.data as IExamScoreRule[];
    let arr = [];
    let subjectMap = {};
    // 根据接口学科规则，设置学科规则
    examSubjectList.forEach(item => {
      const subjectName = item.name;
      const subjectId = String(item.id);
      let id = '';
      let scoreType = 1;
      let scoreTemplate = 1;
      let levelType = 1;
      let json = [];
      let level = 0;
      const rule = rules.find(r => r.subjectIds == subjectId);
      if (rule) {
        id = rule.id;
        scoreType = rule.includedScore === 0 ? 3 : 2;
        if (rule.divType == 1) {
          scoreTemplate = 1;
          levelType = 1;
        } else if (rule.divType == 2) {
          scoreTemplate = 1;
          levelType = 2;
        } else if (rule.divType == 3) {
          scoreTemplate = 2;
          levelType = 1;
        } else if (rule.divType == 4) {
          scoreTemplate = 2;
          levelType = 2;
        }
        if (rule.rule && scoreType == 2) {
          json = JSON.parse(rule.rule);
          json = json.map(item => {
            let { level, ratio, addStart, addEnd, start, end } = item;
            let score = start;

            // 根据当前规则，设置值
            if (rule.divType == 1) {
              addStart = addEnd = start = end = undefined;
            }
            if (rule.divType == 2) {
              ratio = end = undefined;
            }
            if (rule.divType == 3) {
              addStart = addEnd = undefined;
            }
            if (rule.divType == 4) {
              ratio = undefined;
            }

            return {
              level: level,
              ratio: ratio,
              addStart: addStart,
              addEnd: addEnd,
              score: score,
              start: start,
              end: end,
            };
          });
          level = json.length;
        }
      }

      subjectMap[subjectId] = {};
      subjectMap[subjectId]['id'] = id;
      subjectMap[subjectId]['scoreType'] = scoreType;
      subjectMap[subjectId]['level'] = level;
      subjectMap[subjectId]['scoreTemplate'] = scoreTemplate;
      subjectMap[subjectId]['levelType'] = levelType;
      subjectMap[subjectId]['json'] = json;
      subjectMap[subjectId]['level'] = level;
      arr.push({ subjectName, subjectId, fullScore: item.fullScore });
    });

    this.subjectList = arr;
    this.subjectRuleMap = subjectMap;
    if (this.subjectSettingList.length) {
      this.currentSubjectId = this.subjectSettingList[0].subjectId || '';
    }
  }

  getSubjectFullScore(subjectId) {
    return this.subjectList.find(item => item.subjectId == subjectId).fullScore || 100;
  }

  getScoreTypeByValue(value) {
    return this.scoreTypeList.find(item => item.value === value);
  }

  getScoreTemplateByValue(value) {
    return this.scoreTemplateList.find(item => item.value === value);
  }

  getLevelTypeByValue(value) {
    return this.levelTypeList.find(item => item.value === value);
  }

  // 更改总分记分
  changeScoreType(subjectId, value) {
    this.subjectRuleMap[subjectId].scoreType = value;

    if (value == 1 || value == 3) {
      this.subjectRuleMap[subjectId].level = 0;
      if (this.currentSubjectId == subjectId) {
        this.currentSubjectId = this.subjectSettingList.length
          ? this.subjectSettingList[0].subjectId
          : '';
      }
    }

    switch (value) {
      case 1:
        this.subjectRuleMap[subjectId].id = '';
        break;
      case 2:
        this.initRuleJson(subjectId);
        this.currentSubjectId = subjectId;
        break;
      case 3:
      // this.setNoTotalScore(subjectId);
      default:
        break;
    }
  }

  // 删除规则，设置得分
  async delExamScoreRuleBySubjectId(subjectId) {
    const res = await delExamScoreRuleBySubjectId({
      sourceId: this.$sessionSave.get('reportDetail').examId,
      sourceType: 1,
      subjectId: subjectId,
    });
    this.subjectRuleMap[subjectId].id = '';
  }

  // 设置等级赋分
  initRuleJson(subjectId) {
    let defaultRule: IJsonType[] = [
      {
        level: 'A', // 等级
        ratio: 3, // 等级人数比例
        score: undefined, // 赋分分数
        start: 91, // 赋分分数区间
        end: 100, // 赋分分数区间
        addStart: undefined, // 卷面分区间
        addEnd: undefined, // 卷面分区间
      },
      {
        level: 'B+',
        ratio: 7,
        score: undefined,
        start: 81,
        end: 90,
        addStart: undefined,
        addEnd: undefined,
      },
      {
        level: 'B',
        ratio: 16,
        score: undefined,
        start: 71,
        end: 80,
        addStart: undefined,
        addEnd: undefined,
      },
      {
        level: 'C+',
        ratio: 24,
        start: 61,
        end: 70,
        score: undefined,
        addStart: undefined,
        addEnd: undefined,
      },
      {
        level: 'C',
        ratio: 24,
        start: 51,
        end: 60,
        score: undefined,
        addStart: undefined,
        addEnd: undefined,
      },
      {
        level: 'D+',
        ratio: 16,
        start: 41,
        end: 50,
        score: undefined,
        addStart: undefined,
        addEnd: undefined,
      },
      {
        level: 'D',
        ratio: 7,
        start: 31,
        end: 40,
        score: undefined,
        addStart: undefined,
        addEnd: undefined,
      },
      {
        level: 'E',
        ratio: 3,
        start: 21,
        end: 30,
        score: undefined,
        addStart: undefined,
        addEnd: undefined,
      },
    ];
    if (this.defaultRule && this.defaultRule.length > 0) {
      defaultRule = this.defaultRule.map(item => {
        return {
          level: item.level,
          ratio: item.ratio,
          start: item.start,
          end: item.end,
          score: item.start,
          addStart: undefined,
          addEnd: undefined,
        };
      });
    }
    const fullScore = this.subjectList.find(item => item.subjectId == subjectId).fullScore;
    defaultRule[0].addEnd = fullScore;
    defaultRule[defaultRule.length - 1].addStart = 0;
    this.subjectRuleMap[subjectId]['json'] = defaultRule;
    this.subjectRuleMap[subjectId]['level'] = defaultRule.length;
  }

  // 设置不计入总分
  // async setNoTotalScore(subjectId) {
  //   const subjectName = this.subjectList.find(item => item.subjectId == subjectId).subjectName;
  //   const res = await setExamScoreRule({
  //     id: this.subjectRuleMap[subjectId].id,
  //     sourceId: this.$sessionSave.get('reportDetail').examId, // 来源ID
  //     sourceType: 1,
  //     name: 'custom',
  //     mode: 'custom',
  //     subjectIds: subjectId, // 学科ID,多个逗号分隔
  //     subjectNames: subjectName, // 学科名称,多个逗号分隔
  //     includedScore: 0,
  //   });
  //   this.subjectRuleMap[subjectId].id = res.data.id;
  // }

  // 获取累计人数比例
  getJsonAddRatio(json, index) {
    let addRatio = 0;
    for (let i = 0; i < index + 1; i++) {
      let ratio = json[i].ratio || 0;
      addRatio = addRatio + ratio;
    }
    return addRatio;
  }

  // 更改学科
  changeSubject(subjectId) {
    this.currentSubjectId = subjectId;
  }

  // 删除当前行
  removeJsonRow(json, index) {
    if (json.length == 1) return this.$message.warning('至少保留一个等级');
    json.splice(index, 1);
    const subjectRule = this.subjectRuleMap[this.currentSubjectId];
    subjectRule.level = json.length;
  }

  // 添加当前行
  addJsonRow(json, index) {
    if (json.length >= 100) return this.$message.warning('最多可设置100个等级');
    json.splice(index + 1, 0, {
      ratio: undefined,
    });
    const subjectRule = this.subjectRuleMap[this.currentSubjectId];
    subjectRule.level = json.length;
  }

  // 保存当前学科规则
  async saveSubjectRule(subjectId?: string, showMessage: boolean = true) {
    this.saveLoading = true;
    if (!subjectId) subjectId = this.currentSubjectId;
    const subjectRule = this.subjectRuleMap[subjectId];
    const subjectName = this.subjectList.find(item => item.subjectId == subjectId).subjectName;
    const includedScore = subjectRule.scoreType == 3 ? 0 : 1;
    const divType = divTypeMap[`${subjectRule.scoreTemplate}_${subjectRule.levelType}`];
    const rule = this.getJsonToRule(subjectId);
    try {
      const res = await setExamScoreRule({
        id: subjectRule.id, // 主键,有值为更新,无值为新增
        sourceId: this.$sessionSave.get('reportDetail').examId, // 来源ID
        sourceType: 1,
        name: 'custom',
        mode: 'custom',
        subjectIds: subjectId, // 学科ID,多个逗号分隔
        subjectNames: subjectName, // 学科名称,多个逗号分隔
        rule: JSON.stringify(rule), //  赋分规则 json
        includedScore: includedScore, // 是否计入总分 0:不计入 1:计入
        divType: includedScore == 0 ? '' : divType,
      });
      if (res.code == 1) {
        this.subjectRuleMap[subjectId].id = res.data.id;
        if (showMessage) this.$message.success('保存成功');
      }
    } catch (error) {
      console.error(error);
    }
    this.saveLoading = false;
  }

  // 将json转换为接口中的Rule
  getJsonToRule(subjectId) {
    let subjectRule = this.subjectRuleMap[subjectId];
    let divType = divTypeMap[`${subjectRule.scoreTemplate}_${subjectRule.levelType}`];
    let json = subjectRule.json;
    const rule = json.map((item, index) => {
      if (divType == 1) {
        return {
          level: item.level,
          ratio: item.ratio,
          addRatio: this.getJsonAddRatio(json, index),
          addStart: 0,
          addEnd: 0,
          start: item.score, // 赋分
          end: 0,
        };
      } else if (divType == 2) {
        return {
          level: item.level,
          ratio: 0,
          addRatio: 0,
          addStart: item.addStart,
          addEnd: item.addEnd,
          start: item.score, // 赋分
          end: 0,
        };
      } else if (divType == 3) {
        return {
          level: item.level,
          ratio: item.ratio,
          addRatio: this.getJsonAddRatio(json, index),
          addStart: 0,
          addEnd: 0,
          start: item.start, // 赋分
          end: item.end,
        };
      } else if (divType == 4) {
        return {
          level: item.level,
          ratio: 0,
          addRatio: 0,
          addStart: item.addStart,
          addEnd: item.addEnd,
          start: item.start,
          end: item.end,
        };
      }
    });
    return rule;
  }

  // 校验学科规则
  checkSubjectRule(subjectId) {
    const subjectRule = this.subjectRuleMap[subjectId];
    const subjectName = this.subjectList.find(item => item.subjectId == subjectId).subjectName;
    const fullScore = this.subjectList.find(item => item.subjectId == subjectId).fullScore;
    const json = subjectRule.json;

    for (const [index, item] of json.entries()) {
      if (!item.level)
        return this.$message.warning(`${subjectName}学科 - 赋分等级未设置，请检查设置信息`);
      if (subjectRule.levelType == 1) {
        if (!item.ratio && item.ratio !== 0)
          return this.$message.warning(`${subjectName}学科 - 等级人数比例未设置，请检查设置信息`);
      }
      if (subjectRule.levelType == 2) {
        if (!item.addStart && item.addStart !== 0)
          return this.$message.warning(`${subjectName}学科 - 卷面分未设置，请检查设置信息`);
        if (!item.addEnd && item.addEnd !== 0)
          return this.$message.warning(`${subjectName}学科 - 卷面分未设置，请检查设置信息`);
        if (item.addEnd === item.addStart)
          return this.$message.warning(`${subjectName}学科 - 卷面分区间不能相同`);
        if (json[index + 1]) {
          if (json[index].addStart !== json[index + 1].addEnd) {
            return this.$message.warning(`${subjectName}学科 - 卷面分区间必须连续`);
          }
        }
      }
      if (subjectRule.scoreTemplate == 1) {
        if (!item.score && item.score !== 0)
          return this.$message.warning(`${subjectName}学科 - 赋分分数未设置，请检查设置信息`);
      }
      if (subjectRule.scoreTemplate == 2) {
        if (!item.start && item.start !== 0)
          return this.$message.warning(`${subjectName}学科 - 赋分分数未设置，请检查设置信息`);
        if (!item.end && item.end !== 0)
          return this.$message.warning(`${subjectName}学科 - 赋分分数未设置，请检查设置信息`);
      }
    }
    if (subjectRule.levelType == 1) {
      let sum = json.reduce((prev, current) => {
        return prev + current.ratio || 0;
      }, 0);
      if (sum !== 100) {
        return this.$message.warning(`${subjectName}学科 - 人数比例总和必须等于100%`);
      }
    }
    if (subjectRule.levelType == 2) {
      if (json[json.length - 1].addStart !== 0)
        return this.$message.warning(`${subjectName}学科 - 卷面分最小值必须为0`);
      if (json[0].addEnd !== fullScore)
        return this.$message.warning(`${subjectName}学科 - 卷面分最大值必须为卷面分${fullScore}`);
    }
  }

  // 保存自定义赋分规则
  async saveRule() {
    this.saveLoading = true;
    for (const item of this.subjectSettingList) {
      if (this.checkSubjectRule(item.subjectId)) {
        this.saveLoading = false;
        return;
      };
    }
    for (const item of this.subjectList) {
      if (
        this.subjectRuleMap[item.subjectId]?.scoreType == 2 ||
        this.subjectRuleMap[item.subjectId]?.scoreType == 3
      ) {
        await this.saveSubjectRule(item.subjectId, false);
      }
    }

    for (const item of this.subjectList) {
      if (this.subjectRuleMap[item.subjectId].scoreType == 1) {
        await this.delExamScoreRuleBySubjectId(item.subjectId);
      }
    }

    try {
      const examId = this.$sessionSave.get('reportDetail').examId;
      const res = await setScoreRuleId({
        examId,
        ruleId: 'custom',
        type: 1, // 0:设置赋分规则ID ; 1:设置自定义赋分规则ID
      });
      if (res.code == 1) {
        const reportDetail = this.$sessionSave.get('reportDetail');
        await saveScoreRuleSet({
          examId: this.$sessionSave.get('reportDetail').examId,
        });
        this.$sessionSave.set('reportDetail', {
          ...reportDetail,
          customScoreRuleId: 'custom',
          v: reportDetail.v + 1,
        });
        this.$message.success('保存成功');
      }
    } catch (error) {
      console.error(error);
    }
    this.saveLoading = false;
  }

  saveCurrentSubjectRule() {
    let currentSubjectId = this.currentSubjectId;
    if (this.checkSubjectRule(currentSubjectId)) return;
    this.saveSubjectRule(currentSubjectId, true);
  }

  changeJsonScore(json, index, val) {
    this.$nextTick(() => {
      for (const [i, item] of json.entries()) {
        if (i < index && item.score <= val) {
          json[index].score = undefined;
        }
        if (i > index && item.score >= val) {
          json[i].score = undefined;
        }
      }
    });
  }

  changeJsonScoreEnd(json, index, val, oldVal) {
    this.changeJsonEnd(json, index, val, oldVal, 'start', 'end');
  }

  changeJsonScoreStart(json, index, val, oldVal) {
    this.changeJsonStart(json, index, val, 'start', 'end');
  }

  changeJsonAddEnd(json, index, val, oldVal) {
    this.changeJsonEnd(json, index, val, oldVal, 'addStart', 'addEnd');
  }

  changeJsonAddStart(json, index, val, oldVal) {
    this.changeJsonStart(json, index, val, 'addStart', 'addEnd', 0);
  }

  changeJsonEnd(json, index, val, oldVal, keyStart, keyEnd) {
    this.$nextTick(() => {
      if (json[index][keyStart] > val) {
        json[index][keyEnd] = undefined;
        this.$message.warning('分数填写左边值应小于等于右边值');
      }
      for (const [i, item] of json.entries()) {
        if (i < index && item[keyStart] < val) {
          json[index][keyEnd] = oldVal;
        }
      }
    });
  }

  changeJsonStart(json, index, val, keyStart, keyEnd, decrement = 1) {
    this.$nextTick(() => {
      if (json[index][keyEnd] < val) {
        json[index][keyStart] = json[index][keyEnd];
      }
      if (json[index + 1]) {
        json[index + 1][keyEnd] = json[index][keyStart] - decrement;
        if (json[index + 1][keyEnd] < json[index + 1][keyStart]) {
          json[index + 1][keyStart] = undefined;
        }
      }
    });
  }
}
</script>

<style scoped lang="scss">
.fl {
  float: left;
}

.fr {
  float: right;
}

.tip {
  color: #f45454;
}

.custom-section {
}

.header-title {
  position: relative;
  margin: 20px 0;
  padding: 0 10px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;

  &::before {
    overflow: hidden;
    position: absolute;
    left: 0;
    display: block;
    content: '';
    width: 4px;
    height: 24px;
    top: 0px;
    background: #409eff;
    border-radius: 3px;
  }
}

.edit-cell {
  cursor: pointer;

  .edit-cell__icon {
    display: none;
  }

  &:hover {
    color: #409eff;
    .edit-cell__icon {
      display: inline-block;
    }
  }
}

.setting-item {
  margin-top: 15px;
  margin-bottom: 5px;
}

.table-input {
  ::v-deep {
    .el-input__inner {
      height: 30px;
      line-height: 30px;
      text-align: center;
    }
  }
}

.table-range {
  .range-input {
    width: 100px;
  }

  .range-left {
    margin-right: 4px;
  }

  .range-line {
    display: inline-block;
    width: 12px;
    border-top: 1px solid rgba(0, 0, 0, 0.45);
    vertical-align: middle;
  }

  .range-right {
    margin-left: 4px;
  }
}

.action-btn {
  font-size: 18px;
  margin-left: 15px;

  &:first-of-type {
    margin-left: 0;
  }
}
</style>

<style lang="scss">
#ScoringTable .el-table__fixed .el-table__fixed-body-wrapper tr td:first-child,
#ScoringTable .el-table__fixed .el-table__fixed-header-wrapper tr th:first-child {
  background-color: #f8f8f8;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.85);
}
</style>

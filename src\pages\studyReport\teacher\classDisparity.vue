<!--
 * @Description: 班内差距
 * @Author: 小圆
 * @Date: 2024-04-02 09:02:41
 * @LastEditors: 小圆
-->

<script lang="ts">
import { Component, Vue, Mixins, Prop } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';

@Component({
  components: {},
})
export default class ClassDisparity extends Mixins(TableCommon) {
  @Prop({ default: 'teacher' }) apiModule;
  //   @Prop({ default: 'singleSubject' }) apiName;

  // 表格左侧固定列
  tableLeftFixed: any[] = ['rankNum', 'stuNo', 'stuName', 'className'];

  render(h) {
    const table = this.renderTable(h);
    return table;
  }
}
</script>

<style scoped lang="scss"></style>

<template>
  <div>
    <span>
      <span class="el-icon-arrow-left pointer" @click="backToList()"></span>
      <span class="back-name" v-if="!examList.length">
        <span>
          {{ getExamNamePrefix(reportDetail.source) }}
        </span>
        <span>
          {{ reportDetail.examName }}
        </span>
      </span>
      <el-dropdown v-else @command="setExamReport" placement="bottom-start">
        <span class="back-name pointer">
          <span>
            {{ getExamNamePrefix(reportDetail.source) }}
          </span>
          <span>
            {{ reportDetail.examName }}
          </span>
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-if="showMain"
            class="dropdown-item"
            :command="reportParent"
            :class="{ active: reportDetail.examId === reportParent.examId }"
            :title="reportParent.examName"
          >
            <span class="type">系统报告</span>
            <span class="name">{{ reportParent.examName }}</span>
          </el-dropdown-item>
          <el-dropdown-item
            v-for="item in examList"
            class="dropdown-item"
            :key="item.examId"
            :command="item"
            :class="{ active: reportDetail.examId === item.examId }"
            :title="getExamNamePrefix(item.source) + item.examName"
          >
            <template v-if="item.source == 101 || item.source == 102 || item.source == 103 || item.source == 105">
              <span class="type">系统报告</span>
              <span class="name">{{ getExamNamePrefix(item.source) }}{{ item.examName }}</span>
            </template>
            <template v-else>
              <span class="type custom-report">自定义报告</span>
              <span class="name hover">{{ item.examName }}</span>
            </template>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </span>
  </div>
</template>

<script lang="ts">
import { getChildReportList, getExamReportList } from '@/service/pexam';
import UserRole from '@/utils/UserRole';
import { Component, Vue } from 'vue-property-decorator';

@Component({})
export default class ReportHeader extends Vue {
  // 考试列表
  examList: any = [];
  // 是否存在校区
  hasCampus: boolean = false;

  // 考试报告主报告
  get reportParent() {
    return this.$sessionSave.get('reportParent');
  }

  // 当前报告
  get reportDetail() {
    return this.$sessionSave.get('reportDetail');
  }

  // 是否显示主报告
  get showMain() {
    if (this.hasCampus) {
      return UserRole.isOperation || UserRole.isSchoolLeader;
    }
    return true;
  }

  getExamNamePrefix(source) {
    if (source == 101) {
      return '【分层班】';
    }
    if (source == 102) {
      return '【行政班物理方向】';
    }
    if (source == 103) {
      return '【行政班历史方向】';
    }
    return '';
  }

  mounted() {
    this.getExamReport();
  }

  // 获取考试报告列表
  async getExamReport() {
    const { examList, hasCampus } = await getChildReportList({
      examId: this.reportParent.examId,
      examName: this.reportParent.examName,
    });
    this.examList = examList;
    this.hasCampus = hasCampus;
  }
  // 自定义排序
  customSort(a, b) {
    const order = [105, 101, 102, 103];

    const indexA = order.indexOf(a.source);
    const indexB = order.indexOf(b.source);

    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB;
    }
    if (indexA !== -1) {
      return -1;
    }
    if (indexB !== -1) {
      return 1;
    }
    return 0;
  }

  backToList() {
    this.$emit('back');
  }

  setExamReport(item) {
    this.$emit('setExamReport', item);
  }

  getCustomReport() {}
}
</script>

<style scoped lang="scss">
.back-name {
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
}

.pointer {
  cursor: pointer;
}

.type {
  position: relative;
  width: 100px;
  height: 32px;
  background: rgba(64, 158, 255, 0.1);
  border: 1px solid #409eff;
  border-radius: 4px;
  color: #409eff;
  justify-content: center;
  align-items: center;
}

.active {
  .name {
    color: #409eff;
  }
}

.name {
  width: calc(100% - 100px);
  padding-left: 1em;
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #3f4a54;
  line-height: 32px;
}

.custom-report {
  background: rgba(156, 134, 255, 0.1) !important;
  border: 1px solid #9c86ff !important;
  color: #9c86ff !important;
}

.dropdown-item {
  max-width: 700px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>

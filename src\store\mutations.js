import FilterModule from '@/pages/studyReport/plugins/FilterModule';
import { sessionSave } from '@/utils';
import UserRole from '@/utils/UserRole';
import Vue from 'vue';

// 存储loginInfo登录信息
export const saveLoginInfo = (state, data) => {
  state.loginInfo = data;
};
// 存储loginInfo登录信息
export const saveSchoolInfo = (state, data) => {
  if (data.school_name) {
    data.schoolName = data.school_name;
    data.schoolId = data.id;
  } else if (data.schoolName) data.school_name = data.schoolName;

  sessionSave.set('schoolInfo', data);
  state.schoolInfo = data;
  UserRole.setSchoolInfo(data);
  FilterModule.setSchoolInfo(data);
};
export const saveSchoolList = (state, data) => {
  state.schoolList = data;
};
export const saveGradeList = (state, data) => {
  state.gradeList = data;
};

export const saveYearList = (state, data) => {
  state.yearList = data;
};

export const saveSubjectList = (state, data) => {
  state.subjectList = data;
};
export const saveFilterSubjectList = (state, data) => {
  state.filterSubjectList = data;
};
export const saveTeachParams = (state, data) => {
  sessionSave.set('teachParams', data);
  state.teachParams = data;
};

export const saveCategoryList = (state, data) => {
  sessionSave.set('categoryList', data);
  state.categoryList = data;
};

export const saveAcadYearsList = (state, data) => {
  state.acadYearsList = data;
};

export const saveAcadTermList = (state, data) => {
  state.acadTermList = data;
};

export const SET_SUBJECT = (state, list) => {
  let map = {};
  let phaseMap = {};
  list.forEach(it => {
    map[it.id + ''] = it;
    phaseMap[it.phaseId + '_' + it.subjectId] = it;
  });
  Vue.set(state, 'subjectMap', map);
  Vue.set(state, 'subjectPhaseMap', phaseMap);

  // state.subjectMap = map;
  // state.subjectPhaseMap = phaseMap;
};

export const SET_REGION_PROVINCE = (state, list) => {
  list.unshift({
    areaName: '全国',
    children: 1,
    cityid: '',
    countryid: '1',
    districtid: '',
    id: '000000',
    isdelete: -1,
    pinyin: '',
    provinceid: '',
    region_code: '000000',
  });
  let map = {};
  list.forEach(it => {
    map[it.id] = it;
  });
  state.provinceMap = map;
  state.provinceList = list;
};

export const SET_IS_TEALEADER = (state, isTeaLeader) => {
  state.isTeaLeader = isTeaLeader;
};
export const SET_ROLE_LIST = (state, roleList) => {
  state.roleList = roleList;
};

/**
 * 设置教师学科
 * @param state
 * @param list
 * @constructor
 */
export const SET_TEACHER_SUBJECT_LIST = (state, list) => {
  state.teacherSubjects = list;
};

/**
 * 更新图片矩阵信息
 * @param state
 * @param matrix
 * @param rotate
 * @param height
 * @constructor
 */
export const UPDATE_IMAGE_MATRIX = (state, info) => {
  state.image_matrix.matrix = info.matrix;
  state.image_matrix.rotate = info.rotate;
  state.image_matrix.height = info.height;
};
export const savevipInfo = (state, data) => {
  sessionSave.set('vipInfo', data);
  state.vipInfo = data;
};

/**
 * @description: 清空对比讲评
 * @param {*} state
 * @param {*} data
 * @return {*}
 */
export const CLEAR_COMPARE_COMMENT = (state, data) => {
  state.joinCommentParam = {
    stuList: [],
    quesNo: -1,
    tQuesNo: ''
  };
};

/**
 * @description: 加入对比讲评
 * @param {*} state
 * @param {*} data
 * @return {*}
 */
export const JOIN_COMPARE_COMMENT = (state, data) => {
  if (!state.joinCommentParam.tQuesNo) {
    state.joinCommentParam.stuList.push(data.student);
    state.joinCommentParam.tQuesNo = data.tQuesNo
    state.joinCommentParam.quesNo = data.quesNo
  } else {
    if (data.tQuesNo != state.joinCommentParam.tQuesNo) {
      // 如果切换了题目，清空对比学生
      CLEAR_COMPARE_COMMENT(state);
      JOIN_COMPARE_COMMENT(state, data);
      return;
    }
    state.joinCommentParam.stuList.push(data.student);
  }
};

/**
 * @description: 退出对比讲评
 * @param {*} state
 * @param {*} stuNo
 * @return {*}
 */
export const QUIT_COMPARE_COMMENT = (state, stuNo) => {
  state.joinCommentParam.stuList = state.joinCommentParam.stuList.filter(stuNoItem => stuNoItem.uName != stuNo);
  if (!state.joinCommentParam.stuList.length) {
    CLEAR_COMPARE_COMMENT(state);
  }
}

/**
 * @description: 设置学生匿名
 * @param {*} state
 * @param {*} mode
 * @return {*}
 */
export const setStudentAnonymous = (state, mode) => {
  state.showAnonymousName = mode;
}

/**
 * @description: 设置学生是否显示分数
 * @param {*} state
 * @param {*} mode
 * @return {*}
 */
export const setStudentShowScore = (state, mode) => {
  state.showStudentScore = mode;
}

/**
 * @description: 设置校区列表
 * @param {*} state
 * @param {*} data
 * @return {*}
 */
export const saveCampusList = (state, data) => {
  state.campusList = data;
}
/*
 * @Description:
 * @Author: 小圆
 * @Date: 2024-03-27 15:14:38
 * @LastEditors: 小圆
 */
import * as httpApi from './index';

/**
 * api转换
 * @type {{POST: API.POST, GET: API.GET}}
 */
const API = {
  GET: function (url, params) {
    return httpApi.GET(url, params, process.env.VUE_APP_KKLURL);
  },
  POST: function (url, params) {
    return httpApi.POST(url, params, process.env.VUE_APP_KKLURL);
  },
};

/**
 * @description: 获取达美嘉报表
 * @param {*} module 接口模块 例如：student、teacher、custom
 * @param {*} path 接口路径
 * @param {*} params 参数
 * @return {*}
 */
export const GetEReportByModule = (module, path, params) => {
  return API.GET(`/eReport/${module}/${path}`, params);
};

/**
 * @description: 获取教师报表
 * @param {*} path 接口路径
 * @param {*} params 参数
 * @return {*}
 */
export const GetTeacherReport = (path, params) => {
  return GetEReportByModule('teacher', path, params);
};

/**
 * @description: 获取定制报表
 * @param {*} path 接口路径
 * @param {*} params 参数
 * @return {*}
 */
export const GetCustomReport = (path, params) => {
  return GetEReportByModule('custom', path, params);
};

/**
 * @description: 获取导出学生列表
 * @param {*} params 参数
 * @param examId     考试ID
 * @param subjectId  学科ID
 * @param classId    班级ID
 * @param quesNo     题号
 * @param exportType 导出类型 0:分数范围 1:排名范围 2:单个学生 3:优秀作答 4:典型错误
 * @param start      当exportType为1时为分数范围的开始分数,exportType为2时为排名范围的开始分数
 * @param end        当exportType为1时为分数范围的结束分数,exportType为2时为排名范围的结束分数
 * @param keyWord    搜索关键字
 * @param page       开始页数
 * @param limit      每页多少条
 * @return {*}
 */
export const getStuScoreList = params => {
  return API.GET('/eReport/testPaper/getStuScoreList', params);
};

/**
 * @description: 获取导出学生列表
 * @param {*} params 参数
 * @param userId   用户ID
 * @param schoolId 学校ID
 * @param page     开始页数
 * @param limit    每页多少条
 * @return {*}
 */
export const getDownloadPaperProgressList = params => {
  return API.GET('/eReport/testPaper/getDownloadPaperProgressList', params);
};

/**
 * @description: 原卷全部导出
 * @param examId     考试ID
 * @param subjectId  学科ID
 * @param classId    班级ID
 * @param quesNo     题号
 * @param tQuesNo    拆分题号
 * @param exportType 导出类型 0:分数范围 1:排名范围 2:单个学生 3:优秀作答 4:典型错误
 * @param start      当exportType为0时为分数范围的开始分数,exportType为1时为排名范围的开始分数
 * @param end        当exportType为0时为分数范围的结束分数,exportType为1时为排名范围的结束分数
 * @return {*}
 */
export const exportPaper = params => {
  return API.GET('/eReport/testPaper/exportPaper', params);
};

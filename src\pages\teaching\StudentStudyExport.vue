<!--
 * @Description: 查询学生历次成绩
 * @Author: 小圆
 * @Date: 2024-10-22 17:13:16
 * @LastEditors: 小圆
-->
<template>
  <div>
    <stu-filter-header
      ref="stuFilterHeader"
      v-model="filterData"
      @onMounted="init"
      @change-class="changeClass"
      @change-time="changePage(1)"
      @onSearch="getStuHisExamList"
    ></stu-filter-header>
    <div class="score-main" v-loading="isLoading">
      <div class="stu-wrapper">
        <div class="stu-search">
          <el-input
            v-model="filterData.realname"
            class="stu-search__input"
            placeholder="请输入学生姓名"
            @keyup.enter.native="getStuList"
          >
            <i slot="suffix" class="el-input__icon el-icon-search" @click="getStuList"></i>
          </el-input>
        </div>
        <div class="stu-ul" v-if="stuList.length" ref="stuList">
          <div
            v-for="item in stuList"
            class="stu-li"
            :key="item.id"
            :class="{ active: item.id == filterData.stuId }"
            :title="item.username"
            @click="switchStu(item.id)"
          >
            <div class="stu-name">
              {{ item.username }}
            </div>
          </div>
        </div>
      </div>

      <div class="stu-main">
        <div class="module">
          <div class="module-title">历次成绩</div>
          <div v-if="stuHisExamList.length">
            <el-button type="primary" @click="isShowExportStuScoreDialog = true">导出</el-button>
          </div>
        </div>
        <el-table
          :data="stuHisExamList"
          style="width: 100%"
          :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
          v-sticky-table="0"
          row-key="id"
        >
          <el-table-column prop="begin" label="考试时间" width="180" align="center"> </el-table-column>
          <el-table-column prop="name" label="考试名称" width="180" align="center"> </el-table-column>
          <el-table-column :label="filterData.subjectId === '' ? '总分' : currenctSubject.name" align="center">
            <el-table-column prop="score" label="得分" align="center">
              <template #default="scope">
                <el-button v-if="filterData.subjectId" type="text" @click="setShowPaper(scope.row)">{{
                  scope.row.score
                }}</el-button>
                <span v-else>{{ scope.row.score }}</span>
              </template>
            </el-table-column>
            <el-table-column label="班排/校排" align="center">
              <template #default="scope">
                <span>
                  <span v-if="isClsRankEnable">{{ scope.row.clsRank }}</span>
                  <span v-else>
                    <el-tooltip effect="dark" content="应相关部门/学校要求，该数据不予展示" placement="top">
                      <i class="el-icon-lock"></i>
                    </el-tooltip>
                  </span>
                </span>
                <span>/</span>
                <span>
                  <span v-if="isGrdRankEnable">{{ scope.row.grdRank }}</span>
                  <span v-else>
                    <el-tooltip effect="dark" content="应相关部门/学校要求，该数据不予展示" placement="top">
                      <i class="el-icon-lock"></i>
                    </el-tooltip>
                  </span>
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="ruleScore" label="赋分" align="center">
              <template #default="scope">
                <el-button
                  type="text"
                  v-if="scope.row.isRule && filterData.subjectId"
                  @click="setShowPaper(scope.row)"
                  >{{ scope.row.ruleScore }}</el-button
                >
                <span v-else-if="scope.row.isRule">
                  {{ scope.row.ruleScore }}
                </span>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column prop="address" label="班排/校排" align="center">
              <template #default="scope">
                <span v-if="scope.row.isRule">
                  <span>
                    <span v-if="isClsRankEnable">
                      {{ scope.row.ruleClsRank }}
                    </span>
                    <span v-else>
                      <i class="el-icon-lock"></i>
                    </span>
                  </span>
                  <span>/</span>
                  <span>
                    <span v-if="isGrdRankEnable">
                      {{ scope.row.ruleGrdBank }}
                    </span>
                    <span v-else>
                      <i class="el-icon-lock"></i>
                    </span>
                  </span>
                </span>
                <span v-else>--</span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="pagination.total > 0"
          background
          style="margin: 30px 0"
          class="text-center"
          layout="total, prev, pager, next, sizes"
          @current-change="changePage"
          @size-change="changeSize"
          :current-page.sync="pagination.page"
          :page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
        >
        </el-pagination>
      </div>
    </div>

    <StuPaperPreview
      v-if="paperImage.show"
      :queryParams="paperImage.params"
      :defaultScore="paperImage.score"
      @close="paperImage.show = false"
    ></StuPaperPreview>

    <el-dialog title="导出学生历次成绩" :visible.sync="isShowExportStuScoreDialog" width="500px">
      <div class="export-item">
        单个学生历次成绩导出
        <el-button class="export-btn" type="primary" @click="exportStuScore(2)">导出</el-button>
      </div>
      <div class="export-item">
        本班学生历次成绩导出
        <el-button class="export-btn" type="primary" @click="exportStuScore(1)">导出</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import NoData from '@/components/noData.vue';
import { getStuListByClassId } from '@/service/api';
import { ClassList, listStuHisExam } from '@/service/pexam';
import UserRole, { IUserSubList, IUserGrdList } from '@/utils/UserRole';
import moment from 'moment';
import { Component, Vue } from 'vue-property-decorator';
import StuPaperPreview from '@/components/SwiperViewer/StuPaperPreview.vue';
import { getViewPaper } from '@/service/testbank';
import StuFilterHeader, { FilterData } from './components/StuFilterHeader.vue';
import { FuncIdEnum } from '@/utils/examReportUtils';
import { findIntersection } from '@/utils/index';
import { listRoleMenuAPI } from '@/service/pstat';

export interface Stu {
  classid: string;
  examNo: string;
  user_name: string;
  mobile: string;
  pinyin_first: string;
  id: string;
  avatar: string;
  stuNo: string;
  username: string;
  realname: string;
}

export interface StuHisExam {
  id: number;
  name: string;
  begin: Date;
  end: Date;
  dateCreated: Date;
  scoreRate: number;
  subjectId: string;
  subjectName: string;
  categoryId: string;
  categoryName: string;
  stuId: string;
  stuNo: string;
  score: number;
  ruleScore: number;
  clsRank: number;
  grdRank: number;
  isRule: boolean;
  ruleClsRank: number;
  ruleGrdBank: number;
  scoreConfirm: number;
  source: number;
  published: number;
  v: number;
  workId: string;
  bookId: string;
  testBankId: string;
  abPaper: string | number;
}

@Component({
  components: {
    NoData,
    StuPaperPreview,
    StuFilterHeader,
  },
})
export default class StudentStudyExport extends Vue {
  $refs!: {
    stuFilterHeader: StuFilterHeader;
    stuList: HTMLDivElement;
  };

  /** 筛选数据 */
  filterData: FilterData = {
    /** 来源 */
    source: [1],
    /** 类别 */
    categoryId: '' as any,
    /** 学科 */
    subjectId: '' as any,
    /** 年级 */
    gradeId: '' as any,
    /** 班级 */
    classId: '',
    /** 学生姓名 */
    realname: '',
    /** 学生ID */
    stuId: '',
    // 时间类型
    timeType: 3,
    // 开始时间
    startYear: '',
    // 结束时间
    endYear: '',
    // 时间范围
    timeSlot: [],
    // 入学年份
    year: '',
  };
  /** 学生列表 */
  stuList: Stu[] = [];
  // 分页器
  pagination = {
    page: 1,
    pageSize: 10,
    total: 0,
  };
  // 学生历次考试列表
  stuHisExamList: StuHisExam[] = [];
  // 查看原卷
  paperImage = {
    show: false,
    params: {
      workId: '',
      examId: '' as number | string,
      classId: '',
      subjectId: '' as number | string,
      studentNo: '',
      studentId: '',
      abPaper: '' as string | number,
    },
    score: 0,
  };
  // 加载状态
  isLoading: boolean = false;
  // 是否显示导出学生历次成绩对话框
  isShowExportStuScoreDialog: boolean = false;
  // 校排功能是否启动
  isGrdRankEnable: boolean = true;
  // 班排功能是否启动
  isClsRankEnable: boolean = true;
  // 学校设置
  schoolPermission: ISchoolReportPermission = {
    funcRoles: [],
    funcIds: [],
    menuIds: [],
    menuRoles: [],
  };

  // 当前学科
  get currenctSubject() {
    return this.$refs.stuFilterHeader.subjectList.find(item => item.id == this.filterData.subjectId);
  }

  // 初始化
  async init() {
    await this.getSchoolPermission();
    await this.changeClass();
    await this.$nextTick();
    if (this.$refs.stuList) {
      let stuListDom: HTMLDivElement = this.$refs.stuList as HTMLDivElement;
      let dom = stuListDom.querySelector('.active') as HTMLDivElement;
      stuListDom.scrollTo({
        top: dom.offsetTop - dom.clientHeight,
        behavior: 'smooth',
      });
    }
  }

  // 获取学生列表
  async getStuList() {
    this.stuList = [];
    if (!this.filterData.classId) {
      return;
    }
    let res = await getStuListByClassId({
      classId: this.filterData.classId,
      realname: this.filterData.realname,
    });
    this.stuList = Array.isArray(res.data) ? res.data : [];
  }

  // 获取数据
  async getStuHisExamList() {
    if (this.filterData.stuId == '') {
      return;
    }
    this.isLoading = true;
    this.stuHisExamList = [];
    let grade = this.$refs.stuFilterHeader.allGradeList.find(item => item.id == this.filterData.gradeId);
    let roles = UserRole.utils.getSubjectRoles(this.filterData.subjectId, grade?.year);

    try {
      let res = await listStuHisExam({
        schoolId: UserRole.schoolInfo.id,
        stuId: this.filterData.stuId,
        subjectId: this.filterData.subjectId,
        start:
          this.filterData.timeType == 3
            ? `${this.filterData.startYear}`
            : moment(this.filterData.timeSlot[0]).format('YYYY-MM-DD'),
        end:
          this.filterData.timeType == 3
            ? `${moment(this.filterData.endYear).subtract(1, 'days').format('YYYY-MM-DD')}`
            : moment(this.filterData.timeSlot[1]).format('YYYY-MM-DD'),
        role: UserRole.isOperation ? '' : roles.join(','),
        gradeId: this.filterData.gradeId,
        categoryId: this.filterData.categoryId,
        page: this.pagination.page,
        limit: this.pagination.pageSize,
      });
      this.stuHisExamList = res.data.rows || [];
      this.pagination.total = res.data.total_rows;
    } catch (error) {
      console.error(error);
      this.stuHisExamList = [];
      this.pagination.total = 0;
    }
    this.isLoading = false;
  }

  // 查看原卷
  async setShowPaper(item: StuHisExam) {
    if (!this.filterData.subjectId) return;
    this.paperImage.params = {
      workId: item.workId,
      examId: item.id,
      classId: this.filterData.classId,
      subjectId: this.filterData.subjectId,
      studentNo: item.stuNo,
      studentId: item.stuId,
      abPaper: item.abPaper,
    };
    this.paperImage.score = item.isRule ? item.ruleScore : item.score;
    this.paperImage.show = true;
  }

  /**
   * 导出学生历次考试
   * @param code 1 班级 2 学生
   */
  async exportStuScore(code = 1) {
    let grade = this.$refs.stuFilterHeader.allGradeList.find(item => item.id == this.filterData.gradeId);

    let role = '';
    if (!UserRole.isOperation) {
      const map = await UserRole.utils.getRoleSubjectClassMap(grade?.year);
      role = JSON.stringify(map);
    }

    let params: any = {
      schoolId: this.$sessionSave.get('schoolInfo').id,
      code: code as any,
      codeId: code == 1 ? this.filterData.classId : this.filterData.stuId,
      classId: code == 1 ? '' : this.filterData.classId,
      subjectId: this.filterData.subjectId,
      start:
        this.filterData.timeType == 3
          ? `${this.filterData.startYear}`
          : moment(this.filterData.timeSlot[0]).format('YYYY-MM-DD'),
      end:
        this.filterData.timeType == 3
          ? `${moment(this.filterData.endYear).subtract(1, 'days').format('YYYY-MM-DD')}`
          : moment(this.filterData.timeSlot[1]).format('YYYY-MM-DD'),
      role: role,
      categoryId: this.filterData.categoryId,
    };
    let urlSearch = new URLSearchParams(params);
    let url = process.env.VUE_APP_KKLURL + `/pexam/_/export-stu-his-exam?${urlSearch.toString()}`;
    window.open(url);
  }

  // 获取学校权限
  async getSchoolPermission() {
    if (UserRole.isOperation) return;
    const res = await listRoleMenuAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      role: UserRole.examRolesTypes,
    });
    this.schoolPermission = res.data;
  }

  // 获取功能权限
  getFuncEnable(funcId: string) {
    if (UserRole.isOperation) return true;

    let classList = this.$refs.stuFilterHeader.classList;
    const classItem = classList.find(item => item.classId === this.filterData.classId);
    if (!classItem) return false;

    const classRoleIds = classItem.roles.map(role => Number(role));
    if ([1, 2, 3, 4].some(role => classRoleIds.includes(role))) return true;

    const funcRoles = this.schoolPermission.funcRoles || [];
    const func = funcRoles.find(item => item.funcId === funcId);
    if (!func) return false;

    const funcRoleIds = func.roleIds.map(item => Number(item));
    return findIntersection(funcRoleIds, classRoleIds).length > 0;
  }

  // 获取班级列表
  async initFuncPermission() {
    this.isGrdRankEnable = this.getFuncEnable(FuncIdEnum.GrdRankAscDesc);
    this.isClsRankEnable = this.getFuncEnable(FuncIdEnum.ClsRankAscDesc);
  }

  // 更换学生
  changeStu(id) {
    this.filterData.stuId = id;
  }

  // 更改班级
  async changeClass() {
    this.initFuncPermission();
    await this.getStuList();
    this.switchStu(this.stuList[0]?.id);
  }

  // 更改学生
  switchStu(id) {
    if (id) {
      this.filterData.stuId = id;
    } else {
      this.filterData.stuId = '';
    }
    this.changePage(1);
  }

  // 更换页码
  changePage(page) {
    this.pagination.page = page;
    this.getStuHisExamList();
  }

  // 更换每页大小
  changeSize(val) {
    this.pagination.pageSize = val;
    this.changePage(1);
  }
}
</script>
<style lang="scss" scoped>
.score-header {
  position: relative;
  width: 100%;
  background: #fff;

  ::v-deep {
    .el-button {
      font-size: 16px;
    }

    .el-input__inner {
      font-size: 16px;
    }
  }

  .header__select {
    display: inline-block;
    margin-right: 20px;
    color: #2e4b81;
    font-size: 16px;
  }

  .filter-text {
    font-size: 16px;
    color: #666666;
    margin-left: 15px;
  }

  .grade-text {
    font-weight: 700;
  }

  .export-button {
    font-weight: 700;
  }
}

.score-main {
  display: flex;
  margin-top: 20px;
}

.stu-wrapper {
  position: sticky;
  top: 0;

  display: flex;
  flex-direction: column;

  width: 225px;
  min-width: 225px;
  height: calc(100vh - 80px);

  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 6px 0 rgba(32, 112, 254, 0.1);
  margin-right: 20px;

  .stu-search {
    padding: 10px;
    background: #f5f5f5;
  }

  .stu-ul {
    flex: 1;
    padding: 10px 0;
    overflow: auto;

    .stu-li {
      padding: 0 28px;
      height: 55px;
      line-height: 55px;

      text-align: center;
      font-size: 16px;
      color: #3f4a54;

      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      cursor: pointer;

      &:hover {
        color: lighten(#2574ff, 0.2);
        background: lighten(#f0f5f9, 0.5);
      }

      &.active {
        color: #2574ff;
        background: #f0f5f9;
      }

      .stu-name {
        position: relative;

        &::after {
          content: '';
          display: block;
          position: absolute;
          bottom: 0;
          height: 1px;
          width: 100%;
          background-color: #e7eaed;
        }
      }
    }
  }
}

.stu-main {
  flex: 1;
}

.module {
  display: flex;

  .module-title {
    flex: 1;

    position: relative;

    display: flex;
    align-items: center;

    margin-bottom: 10px;

    line-height: 32px;
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;

    &::before {
      content: '';
      width: 6px;
      height: 24px;
      background: #409eff;
      border-radius: 3px;
      margin-right: 10px;
    }
  }
}

.export-item {
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 10px;
  padding-left: 50px;

  + .export-item {
    margin-top: 10px;
  }

  .export-btn {
    margin-left: 20px;
  }
}
</style>

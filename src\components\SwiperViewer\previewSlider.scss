.previewSlider {
  width: 100%;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1001;

  &.desktop {
    margin-bottom: 0;
  }

  .preview-swiper {
    &.gallery-thumbs {
      bottom: 0;
      height: 15%;
      box-sizing: border-box;
      padding: 10px 0;
      background-color: rgba(0, 0, 0, 0.5);

      .swiper-slide {
        height: 100%;
        background-color: #ccc;
        cursor: pointer;

        .thumb-box {
          background-size: cover;
          background-position: center;
          width: 100%;
          height: 100%;
          opacity: 0.25;
        }
      }

      .swiper-slide-active {
        .thumb-box {
          opacity: 1;
          border: 2px solid #0199ff;
        }
      }
    }
  }

  .gallery-top {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    height: 90%;
    width: 100%;
    z-index: 101;

    .swiper-rorate-box {
      transition-duration: 300ms;
      width: 100%;
      height: 100%;
      justify-content: center;
      align-items: center;
      display: flex;
    }

    .swiper-tanslate-box {
      width: 100%;
      height: 100%;
    }
  }

  .swiper-pagination {
    color: #fff;
  }

  .swiper-zoom-box {
    position: relative;
    width: 100%;
    height: 100%;

    .swiper-lazy {
      max-height: 100% !important;
      max-width: 100% !important;
    }
  }

  .preview-swiper-cover {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 100;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .swiper-imageTool-preAndNext {
    .icon-pre {
      position: fixed;
      left: 0;
      top: 40%;
      background-color: unset !important;
      width: 95px;
      height: 70px;
      background: url("~@/assets/img-preview-pre-hover-icon.png") no-repeat;
      background-size: 60px;
      margin-left: 30px;
      z-index: 101;
      cursor: pointer;
    }

    .icon-next {
      position: fixed;
      right: 0;
      top: 40%;
      background-color: unset !important;
      width: 95px;
      height: 70px;
      background: url("~@/assets/img-preview-next-hover-icon.png") no-repeat;
      background-size: 60px;
      margin-right: 30px;
      z-index: 101;
      cursor: pointer;
    }

    .disabled {
      opacity: 0.2;
      cursor: default;
    }
  }

  .swiper-imageTool-box {
    position: absolute;
    left: 50%;
    bottom: 100px;
    width: 100%;
    display: flex;
    justify-content: center;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    z-index: 1000;

    .swiper-imageTool {
      float: left;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 5px;
      padding: 0 10px;
      height: 70px;
      overflow: hidden;
      font-size: 24px;
      color: #fff;
      line-height: 70px;

      &-left {
        margin-right: 10px;
      }

      &-right {
        margin-left: 10px;
      }

      &-comment {
        padding: 0 20px;
      }

      &.primary,
      &.active {
        background-color: #3e73f6;
      }

      &.tool-block--add {
        background-color: #3e73f6;
      }

      &.tool-block--remove {
        background-color: #a8b0c6;
      }
    }


    .swiper-imageTool-left-short {
      width: 195px !important;
    }

    .swiper-imageTool-btn-wrap {
      float: right;
      font-size: 14px;
      margin-top: 3px;

      .swiper-imageTool-btn-item {
        width: 80px;
        height: 64px;

        .btn-img-wrap {
          width: 38px;
          height: 40px;
          margin: 0 20px;

          img {
            width: 100%;
            vertical-align: unset !important;
          }
        }
      }

      .excellent-btn {
        float: left;
        margin-right: 3px;
      }

      .excellent-btn-active {
        background: #07c29d;
        border-radius: 5px;
      }

      .wrong-btn {
        float: right;
      }

      .wrong-btn-active {
        background: #f56c6c;
        border-radius: 5px;
      }

      .swiper-imageTool-btn-item-txt {
        line-height: 20px;
        text-align: center;
        margin-top: 3px;
      }
    }
  }

  .swiper-imageTool-txt {
    min-width: 190px;
    text-align: center;
    float: left;
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .swiper-imageTool {
    float: left;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 5px;
    padding: 0 5px;
    height: 70px;
    overflow: hidden;
    margin-right: 5px;

    .preview-pagination {
      float: left;
      line-height: 40px;
      color: #fff;
      padding: 0 15px;
      width: auto;
      bottom: 0;
      font-size: 18px;
    }

    .icon-imageTools {
      float: left;
      width: 46px;
      height: 70px;
      background: url("~@/assets/img-preview-btn.png") no-repeat;
      cursor: pointer;
      margin: 0 10px;

      &:hover {
        background: url("~@/assets/img-preview-btn-hover.png") no-repeat;
      }

      &:active {
        opacity: 0.4;
      }

      &.swiper-button-disabled {
        opacity: 0.2;
        cursor: default;
      }

      &.icon-pre {
        background-position: -85px 11px;
        border-right: 1px solid rgba(255, 255, 255, 0.17);
      }

      &.icon-zoomin {
        background-position: -136px 21px;
      }

      &.icon-zoomout {
        background-position: 8px 21px;
      }

      &.icon-recover {
        background-position: -64px 21px;
      }

      &.icon-close {
        background-position: -317px 21px;
      }

      &.icon-rotateL {
        background-position: -246px 21px;
      }

      &.icon-rotateR {
        background-position: -227px 21px;
      }

      &.icon-next {
        background-position: -195px 21px;
        border-left: 1px solid rgba(255, 255, 255, 0.17);
      }
    }
  }

  .swiper-bottom-stu-list-cover {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 0 30px;
    background: #000;
    opacity: 0.7;
    height: 92px;
    line-height: 92px;
    text-align: center;
    z-index: 102;
  }

  .swiper-bottom-stu-list {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 0 30px;
    height: 92px;
    line-height: 92px;
    text-align: center;
    z-index: 103;
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;

    &::-webkit-scrollbar {
      width: 2px;
      height: 2px;
      overflow: auto;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #c6c6c665;
      min-height: 25px;
      min-width: 4px;
      border: 1px solid rgba(0, 0, 0, 0.08);
      border-radius: 5px;
    }

    ul {
      margin: 0 30px;
      display: inline-block;
      color: #fff;
      font-size: 30px;
    }

    .swiper-bottom-stu-active {
      height: 60px;
      padding: 0 20px;
      background: #3e73f6;
      border-radius: 30px;
      margin-top: 16px;
      line-height: 60px;
    }

    .ivu-tabs-bar {
      border-bottom: unset !important;
      height: 92px !important;
      color: #fff !important;
    }

    .ivu-tabs-nav-container {
      line-height: 60px !important;
      font-size: 30px !important;
    }

    .ivu-tabs-nav-next {
      line-height: 92px !important;
    }

    .ivu-tabs-nav-prev {
      line-height: 92px !important;
    }

    .ivu-icon {
      font-size: 28px;
    }

    .ivu-tabs-content {
      display: none;
    }

    .ivu-tabs-nav .ivu-tabs-tab-active {
      color: #2d8cf0;
      background: #fff;
      color: #fff;
      height: 60px;
      background: #3e73f6;
      margin-top: 16px;
      border-radius: 30px;
    }

    .ivu-tabs-nav .ivu-tabs-tab {
      padding: 0 16px !important;
      margin-right: 34px !important;
    }

    .ivu-tabs-nav-scroll {
      margin: 0 30px;
    }

    .ivu-tabs-ink-bar {
      display: none !important;
      height: 0 !important;
    }

    .ivu-tabs-nav-container:focus .ivu-tabs-tab-focused {
      border-top: unset !important;
    }

    .ivu-tabs .ivu-tabs-tabpane {
      display: none;
    }
  }

  .no-data {
    top: 30%;
    width: 100%;
    position: fixed;
    z-index: 101;
  }
}
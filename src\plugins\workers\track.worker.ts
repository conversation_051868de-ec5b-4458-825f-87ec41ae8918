import { init, getInstance, Rack } from "./indexDB";
const ctx: Worker = self as any;
let TracklUrl = 'https://recordlog.iclass30.com'
let deviceInfo = {
  product_code: "5", //产品线编码
  product_id: "homework", //应用编码
  os_type: "H5", //类型 1:windows 2:android 3:ios 4:H5 5:web
  product_version: "", //产品版本号
  disk_id: "", //设备磁盘id信息（端）
  model: "", //设备型号
  mac: "", //设备mac地址
  application_id: ""//应用ID 移动端应用挂产品下面的
}
// 发送数据到父线程
// ctx.postMessage({ foo: "foo" });

// 响应父线程的消息
ctx.addEventListener("message", (msg) => {
  if (msg.data.type === "trackInfo") {
    let data = msg.data.event;
    deviceInfo.product_version = data.productVersion;
    deviceInfo.model = data.model;
    TracklUrl = data.url;
  } else if (msg.data.type === "trackWork") {
    getInstance().insert<Rack>({
      tableName: "track",
      data: msg.data.event,
    });
  }
});

//初始化数据库
const initDB = async (): Promise<void> => {
  await init({
    dbName: "homework", // 数据库名称
    version: 1, // 版本号
    tables: [
      {
        tableName: "track", // 表名
        option: { keyPath: "id", autoIncrement: true }, // 指明主键为id
        indexs: [
          // 数据库索引
          {
            key: "id",
            option: {
              unique: true,
            }
          }
        ]
      }
    ]
  });

  //定时上传埋点数据
  setInterval(() => {
    getInstance()
      .queryLimit({ tableName: "track", limit: 10 })
      .then((list) => {
        if (list.length !== 0) {
          saveTrackData(list).then(() => {
            getInstance().deleteLimit<Rack>({
              tableName: "track",
              condition: (item) => item.event !== "",
              limit: 10,
            });
          });
        }
      });
  }, 15 * 1000);

  ctx.postMessage({
    type: 'connected'
  })
};

//上传埋点数据
const saveTrackData = (list: any) => {
  return new Promise<void>((resolve, reject) => {
    let xhr = new XMLHttpRequest();
    let formData = new FormData();
    formData.append("deviceInfo", JSON.stringify(deviceInfo)); //设备信息
    formData.append("events", JSON.stringify(list)); //事件信息
    xhr.open(
      "POST",
      TracklUrl + "/log/log_service/burying_point",
      true
    );
    // xhr.responseType = "ArrayBuffer";
    xhr.onload = (e: any) => {
      if (e.target.status === 200) {
        if (JSON.parse(e.target.response).code == 1) {
          resolve();
        } else {
          reject();
        }
      } else {
        reject();
      }
    };
    xhr.send(formData);
  });
};

initDB();

<template>
  <div>
    <div class="setting-header">
      <div class="title">报告设置</div>
      <!-- <div class="header-right"> -->
      <!-- <el-button type="primary" size="small" :loading="saveLoading" @click="saveConfig">保存</el-button> -->
      <!-- </div> -->
    </div>

    <div class="report-setting-container">
      <div class="report-setting-item" v-for="(item, index) in jCfg" :key="index">
        <el-radio v-model="selectedIndex" class="report-radio" border :label="index" @change="handleRadioChange">
          {{ '模式' + (index + 1) }}
        </el-radio>
        <div class="report-content">
          <span class="report-tag" v-for="report in item.data" :key="report.id">
            {{ report.name }}
          </span>
        </div>
      </div>
    </div>
    <div class="tip">
      <span class="title">
        *注：选择模式2时，所有角色可查看校级报告，即可查看任教年级所有学科的分析数据，务必谨慎操作！</span
      >
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Mixins, Vue } from 'vue-property-decorator';
import { getSchCfgAPI, setSchCfgAPI } from '@/service/pexam';
import { SchoolSettingType } from '../../types';
import SchoolSettingMixin from '../SchoolSetting.mixin';

interface JCfg {
  checked: number;
  data: {
    id: string;
    name: string;
  }[];
}

@Component({
  components: {},
})
export default class ReportSetting extends Mixins(SchoolSettingMixin) {
  // 配置项
  private jCfg: JCfg[] = [
    {
      data: [
        {
          id: 'class',
          name: '单次报告',
        },
        {
          id: 'track',
          name: '历次追踪',
        },
      ],
      checked: 1,
    },
    {
      data: [
        {
          id: 'class',
          name: '班级报告',
        },
        {
          id: 'school',
          name: '校级报告',
        },
        {
          id: 'track',
          name: '历次追踪',
        },
      ],
      checked: 0,
    },
  ];
  // 保存loading
  private saveLoading = false;
  // 当前选中的索引
  private selectedIndex = 1;

  mounted() {
    this.getConfig();
  }

  // 获取配置项
  async getConfig() {
    const res = await getSchCfgAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      type: SchoolSettingType.ReportSetting,
    });
    const data = res.data;
    if (data && data.jCfg) {
      this.jCfg = data.jCfg;
      const checkedIndex = this.jCfg.findIndex(item => item.checked === 1);
      this.selectedIndex = checkedIndex >= 0 ? checkedIndex : 0;
    }
  }

  // 处理单选变化
  handleRadioChange(selectedIndex: number) {
    // 重置所有项为未选中
    this.jCfg.forEach(item => {
      item.checked = 0;
    });
    // 设置选中的项
    if (this.jCfg[selectedIndex]) {
      this.jCfg[selectedIndex].checked = 1;
    }
  }

  // 保存配置
  async saveConfig() {
    this.saveLoading = true;
    try {
      await setSchCfgAPI({
        schId: this.$sessionSave.get('schoolInfo').id,
        schName: this.$sessionSave.get('schoolInfo').schoolName,
        type: SchoolSettingType.ReportSetting,
        jCfg: this.jCfg,
      });
      // this.$notify.success({
      //   title: '成功',
      //   message: '保存成功',
      // });
    } catch (error) {
      // this.$notify.error({
      //   title: '失败',
      //   message: '保存失败',
      // });
    } finally {
      this.saveLoading = false;
    }
  }

  // 获取配置信息
  public getCfg() {
    return {
      type: SchoolSettingType.ReportSetting,
      jCfg: this.jCfg,
    };
  }
}
</script>
<style lang="scss" scoped>
@import '../page-style.scss';

.report-setting-container {
  margin: 24px 0;
  //   padding: 20px;
  //   background: #fafafa;
  //   border-radius: 8px;
  //   border: 1px solid #e8e8e8;
}

.report-setting-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }

  .report-radio {
    // display: flex;
    // align-items: center;
    // width: 100%;
    // height: auto;
    // border: 2px solid transparent;
    // border-radius: 8px;
    // padding: 16px;
    // background: #fff;
    // transition: all 0.3s ease;
    // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    // &.is-checked {
    //   border-color: #1890ff;
    //   background: #e6f4ff;
    //   box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
    // }
  }

  .report-content {
    padding: 4px;
    margin-left: 8px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
  }

  .report-tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    font-weight: 500;
    position: relative;
    margin-right: 8px;
    color: #333;
    font-size: 15px;
  }
}

.notice-text {
  color: #666;
  font-size: 12px;
  line-height: 1.5;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
  padding: 12px;
  margin-top: 16px;

  &::before {
    content: '💡';
    margin-right: 6px;
  }
}
</style>

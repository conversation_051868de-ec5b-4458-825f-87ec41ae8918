<template>
  <div>
    <!-- <div>
      <el-popover
        placement="bottom"
        width="320"
        trigger="click"
        popper-class="popover"
        v-model="isPopoverVisible"
        @show="onShowTypePopover"
      >
        <div>
          <el-checkbox-group v-model="tempCheckTypeList">
            <el-checkbox
              v-for="item in filterTypeList"
              :key="item.prop"
              class="checkbox"
              :label="item.prop"
              size="small"
              >{{ item.name }}</el-checkbox
            >
          </el-checkbox-group>
          <div class="popover-footer">
            <el-button size="small" @click="isPopoverVisible = false">取消</el-button>
            <el-button type="primary" size="small" @click="handleCheckType">确定</el-button>
          </div>
        </div>
        <el-button slot="reference" type="text">筛选数据 <i class="el-icon-arrow-down"></i></el-button>
      </el-popover>
    </div> -->

    <div v-show="!showDefault" id="fiveRateChart" style="width: 100%; height: 400px"></div>
    <div v-show="showDefault">
      <div style="text-align: center">
        <img style="width: 350px" :src="noResImg" alt="" />
      </div>
      <p style="text-align: center; font-size: 16px; margin-top: 10px">暂无数据!</p>
    </div>
  </div>
</template>

<script>
const filterTypeList = [
  {
    name: '优秀率',
    prop: 'fineRate',
    color: '#07C29D ',
  },
  {
    name: '优良率',
    prop: 'goodRate',
    color: '#3E73F6 ',
  },
  {
    name: '及格率',
    prop: 'passRate',
    color: '#FFB400',
  },
  {
    name: '不及格率',
    prop: 'failRate',
    color: '#FF6A68',
  },
  {
    name: '低分率',
    prop: 'lowRate',
    color: '#C6C9CC',
  },
];

export default {
  name: 'FiveRateChart',
  props: ['tableData', 'filterData', 'queryType', 'type'],
  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      showDefault: false,
      fiveRateChart: null,
      // 数据
      data: [],

      // 筛选数据
      filterTypeList: filterTypeList,
      // 是否显示指标弹出框
      isPopoverVisible: false,
      // 指标筛选
      checkTypeList: filterTypeList.map(item => item.prop),
      // 临时筛选数据
      tempCheckTypeList: [],
    };
  },
  mounted() {
    if (this.tableData.length) {
      this.showDefault = false;
    }

    this.$nextTick(() => {
      this.drawImg();
    });
  },
  beforeDestroy() {
    if (this.fiveRateChart != null && this.fiveRateChart != '' && this.fiveRateChart != undefined) {
      this.fiveRateChart.dispose();
      this.fiveRateChart = null;
    }
  },
  watch: {
    tableData: {
      handler(val) {
        if (val.length) {
          this.showDefault = false;
        }
        this.$nextTick(() => {
          this.drawImg();
        });
      },
      deep: true,
    },
  },
  methods: {
    // 显示指标弹出框
    onShowTypePopover() {
      this.tempCheckTypeList = JSON.parse(JSON.stringify(this.checkTypeList));
    },

    // 处理指标筛选
    handleCheckType() {
      this.checkTypeList = JSON.parse(JSON.stringify(this.tempCheckTypeList));
      this.isPopoverVisible = false;
      this.drawImg();
    },

    // 处理图表数据
    handleChartData() {
      let data = this.tableData && this.tableData.length ? this.tableData : [];
      if (data.length) {
        data = this.tableData.filter(item => item.clsName !== '年级' && item.clsName !== '全校');
      } else {
        this.showDefault = true;
      }
      this.data = data;
    },
    drawImg() {
      if (this.fiveRateChart != null && this.fiveRateChart != '' && this.fiveRateChart != undefined) {
        this.fiveRateChart.dispose();
        this.fiveRateChart = null;
      }
      this.handleChartData();
      this.fiveRateChart = this.$echarts.init(document.getElementById('fiveRateChart'));

      let xAxisData =
        this.queryType == 1
          ? this.data.map(item => {
              return item.clsName;
            })
          : this.data.map(item => {
              return item.teaName;
            });

      let typeList = this.filterTypeList.filter(item => this.checkTypeList.includes(item.prop));

      this.fiveRateChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: (params, ticket) => {
            params.reverse();
            let html = '';
            let prop = this.queryType == 1 ? 'clsName' : 'teaName';
            let obj = this.tableData.find(item => {
              return params[0].name == item[prop];
            });
            let teaLabel = this.type == 'campus' ? '年级主任' : this.filterData?.subjectId == '' ? '班主任' : '任课老师';
            let classHtml = `<div style="font-size:14px;color:#666;font-weight:400;line-height:1.25;">${obj.clsName}</div>`;
            let teaHtml = `<div style="font-size:14px;color:#666;font-weight:400;line-height:1.25;margin-top: 5px">${teaLabel}：${
              obj?.teaName || '--'
            }</div>`;
            html = classHtml + teaHtml;
            params.forEach(item => {
              html += `<div class="clearfix" style="margin-top: 5px">${item.marker}<span>${item.seriesName}</span><span style="float:right;margin-left:20px;font-size:14px;color:#666;font-weight:900">${item.value}</span></div>`;
            });
            return html;
          },
        },
        legend: {
          icon: 'circle',
          top: 10,
          right: 40,
          textStyle: {
            color: '#757C8C',
            fontSize: '14',
          },
          data: typeList.map(item => item.name),
        },
        grid: {
          left: '3%',
          right: '6%',
          bottom: '9%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          name: this.queryType == 1 ? '班级' : '教师',
          axisLabel: {
            interval: 0,
            rotate: 30,
            margin: 20,
          },
          axisLine: {
            lineStyle: {
              color: '#757C8C',
              fontSize: 14,
            },
          },
          data: xAxisData,
        },
        yAxis: {
          type: 'value',
          name: '百分比',
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100,
          },
        ],
        series: typeList
          .map(item => {
            return {
              name: item.name,
              type: 'bar',
              stack: '百分比',
              label: {
                show: true,
                position: 'left',
              },
              color: item.color,
              barMaxWidth: 60,
              barGap: 20,
              data: this.data.map(it => it[item.prop]),
            };
          })
          .reverse(),
      });
    },
  },
};
</script>

<style scoped lang="scss">
.popover-footer {
  margin-top: 10px;
  text-align: right;
}
</style>

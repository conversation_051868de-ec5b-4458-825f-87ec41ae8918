const getters = {
  schoolList(state) {
    return state.schoolList;
  },
  gradeList(state) {
    return state.gradeList;
  },
  yearList(state) {
    return state.yearList;
  },
  subjectList(state) {
    return state.subjectList;
  },
  filterSubjectList(state) {
    return state.filterSubjectList;
  },
  categoryList(state) {
    return state.categoryList;
  },
  acadYearsList(state) {
    return state.acadYearsList;
  },
  acadTermList(state) {
    return state.acadTermList;
  },
  roleList(state) {
    return state.roleList;
  },
  subjectMap: state => state.subjectMap,
  subjectPhaseMap: state => state.subjectPhaseMap,
  provinceList: state => state.provinceList,
  provinceMap: state => state.provinceMap,
  loginInfo: state => state.loginInfo,
  image_matrix: state => state.image_matrix,
  // 已加入对比讲评的人数
  joinedCommentCount: state => state.joinCommentParam.stuList.length,
  joinedCommentIds: state => state.joinCommentParam.stuList.map(stu => stu.uName),
  // 是否可创建考试
  examEnabled: state => {
    return state.vipInfo.examCount - state.vipInfo.examUsed > 0;
  }
};
export default getters;

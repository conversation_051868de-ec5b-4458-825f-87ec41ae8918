<template>
  <div>
    <div class="setting-header">
      <div class="title">五率设置</div>
      <div class="header-right" v-if="!examId">
        <el-button type="primary" size="small" @click="reset" :loading="resetLoading">恢复默认</el-button>
        <el-button type="primary" size="small" @click="save" :loading="saveLoading">保存</el-button>
      </div>
    </div>

    <SettingSubjectHeader
      v-if="!examId"
      ref="settingSubjectHeader"
      :currentPhase="currentPhase"
      :currentGradeId="currentGradeId"
      :currentGradeItem="currentGradeItem"
      :currentGradeList="currentGradeList"
      :examId="examId"
      @init="onSubjectInit"
      @change="onSubjectChange"
    >
    </SettingSubjectHeader>

    <el-form
      label-position="left"
      :inline="true"
      :model="{ modelData: jCfg }"
      ref="form"
      hide-required-asterisk
      v-loading="loading"
    >
      <el-form-item v-for="(rate, i) in jCfg" :key="rate.id" :label="rate.name" label-width="70px">
        <template v-for="(item, index) in rate.data">
          <template v-if="!item.isCustom">
            <el-form-item
              label=""
              v-if="index === 0"
              :rules="rules.numberMaxRules"
              :prop="`modelData[${i}].data[${index}].end`"
            >
              <el-input-number
                v-model="item.end"
                class="input-number"
                :controls="false"
                :min="0"
                :max="100"
                size="small"
                @change="changeFiveRate(i, index, 'end')"
              >
              </el-input-number>
            </el-form-item>

            <el-form-item
              v-if="index !== rate.data.length - 2"
              :label="index === 0 ? `≥${item.name}≥` : `${item.name}≥`"
              :rules="rules.numberMaxRules"
              :prop="`modelData[${i}].data[${index}].start`"
            >
              <el-input-number
                v-model="item.start"
                class="input-number"
                :controls="false"
                :min="0"
                :max="100"
                size="small"
                @change="changeFiveRate(i, index, 'start')"
              >
              </el-input-number>
            </el-form-item>

            <el-form-item
              v-if="index == rate.data.length - 2"
              :label="`${item.name}<`"
              :rules="rules.numberMaxRules"
              :prop="`modelData[${i}].data[${index}].end`"
            >
              <el-input-number
                v-model="item.end"
                class="input-number"
                :controls="false"
                :min="0"
                :max="100"
                size="small"
                @change="changeFiveRate(i, index, 'end')"
              >
              </el-input-number>
            </el-form-item>
          </template>
          <template v-else>
            <div style="display: inline-block; vertical-align: middle">
              <el-form-item label="" :rules="rules.numberMaxRules" :prop="`modelData[${i}].data[${index}].end`">
                <el-input-number
                  v-model="item.end"
                  class="input-number"
                  :controls="false"
                  :min="item.start"
                  :max="100"
                  size="small"
                  @change="changeFiveRate(i, index, 'end')"
                >
                </el-input-number>
              </el-form-item>
              <span>{{ `>${item.name}≥` }} </span>
              <el-form-item label="" :rules="rules.numberMaxRules" :prop="`modelData[${i}].data[${index}].start`">
                <el-input-number
                  v-model="item.start"
                  class="input-number"
                  :controls="false"
                  :min="0"
                  :max="item.end"
                  size="small"
                  @change="changeFiveRate(i, index, 'start')"
                >
                </el-input-number>
              </el-form-item>
            </div>
          </template>
        </template>
      </el-form-item>
    </el-form>

    <SettingSaveDialog
      v-if="isShowSaveDialog"
      :currentPhase="currentPhase"
      :currentGradeId="currentGradeId"
      :currentGradeItem="currentGradeItem"
      :currentGradeList="currentGradeList"
      :examId="examId"
      @save="handleSave"
      @closed="isShowSaveDialog = false"
    ></SettingSaveDialog>
  </div>
</template>

<script lang="ts">
import { Component, Mixins, Ref, Vue } from 'vue-property-decorator';
import SettingSubjectHeader from '../SettingSubjectHeader.vue';
import SchoolSettingMixin from '../SchoolSetting.mixin';
import { getSchCfgAPI, restoreSchCfgAPI, setSchCfgAPI } from '@/service/pexam';
import { ElForm } from '@iclass/element-ui/types/form';
import { SchoolSettingType, SettingChangeParams } from '../../types';
import SettingSaveDialog from '../SettingSaveDialog.vue';

interface JCfg {
  id: string;
  name: string;
  checked: number;
  data: RateData[];
}

interface RateData {
  name: string;
  start: number;
  end: number;
  isCustom?: boolean;
}

@Component({
  components: {
    SettingSubjectHeader,
    SettingSaveDialog,
  },
})
export default class RateSetting extends Mixins(SchoolSettingMixin) {
  @Ref() settingSubjectHeader!: SettingSubjectHeader;
  @Ref() form!: ElForm;

  // 表格数据
  private jCfg: JCfg[] = [];
  // 保存状态
  private saveLoading = false;
  // 表单验证规则
  private rules = {
    // 数字和必填项验证规则 不超过100
    numberMaxRules: [
      {
        required: true,
        message: '必填项',
        trigger: 'blur',
      },
    ],
  };

  // 重置loading
  private resetLoading = false;
  // 获取配置loading
  private loading = false;
  // 保存对话框
  private isShowSaveDialog = false;

  mounted() {
    if (this.examId) {
      this.getConfig();
    }
  }

  // 设置更改
  onSettingChange(data: SettingChangeParams) {}

  // 学科列表初始化
  onSubjectInit() {
    this.jCfg = [];
    this.getConfig();
  }

  // 获取一分五率配置
  async getConfig() {
    const res = await getSchCfgAPI({
      schId: this.$sessionSave.get('schoolInfo').id,
      type: SchoolSettingType.Rate,
      phase: this.currentPhase,
      gradeId: this.currentGradeId,
      examId: this.examId,
    });
    if (res.data && res.data.jCfg) {
      this.jCfg = res.data.jCfg;
    } else {
      this.jCfg = [];
    }
    if (this.jCfg && this.jCfg.length) {
      this.settingSubjectHeader?.setCurrentSubjectIds(this.jCfg.map(item => item.id));
    } else {
      this.settingSubjectHeader?.resetSubject();
    }
  }

  // 学科列表变化
  onSubjectChange(subjectList) {
    let data: JCfg[] = [];
    subjectList.forEach(item => {
      let tableItem = this.jCfg.find(tableItem => tableItem.id == item.id);
      if (tableItem) {
        data.push(tableItem);
      } else {
        data.push({
          id: item.id,
          name: item.name,
          checked: 1,
          data: [
            {
              isCustom: false,
              name: '优秀率',
              start: 85,
              end: 100,
            },
            {
              isCustom: false,
              name: '优良率',
              start: 70,
              end: 85,
            },
            {
              isCustom: false,
              name: '及格率',
              start: 60,
              end: 70,
            },
            {
              isCustom: false,
              name: '不及格率',
              start: 0,
              end: 60,
            },
            {
              isCustom: true,
              name: '低分率',
              start: 0,
              end: 30,
            },
          ],
        });
      }
    });
    this.jCfg = data;
  }

  // 修改五率
  changeFiveRate(i, index, type = 'start') {
    const data = this.jCfg[i].data;
    const curValue = data[index][type];

    if (!data[index].isCustom) {
      // 向前更新值，如果小于该值则置为空
      for (let curIndex = index; curIndex >= 0; curIndex--) {
        const item = data[curIndex];
        if (item.isCustom) {
          continue;
        }

        // 优秀率单独判断
        if (curIndex == 0) {
          if (type == 'end') {
            if (item.start > item.end) {
              item.start = undefined;
            }
          } else if (type == 'start') {
            if (item.start > item.end) {
              item.end = undefined;
            }
          }
          break;
        }

        if (item.end < item.start) {
          item.end = undefined;
        }
        let prevItem = data[curIndex - 1];
        if (!prevItem) {
          continue;
        }
        if (prevItem.isCustom) {
          continue;
        }
        if ((prevItem.end || 0) < curValue) {
          prevItem.end = undefined;
        }
        if ((prevItem.start || 0) < curValue) {
          prevItem.start = undefined;
        }
      }

      // 向后更新值
      for (let curIndex = index; curIndex < data.length; curIndex++) {
        const item = data[curIndex];
        if (item.isCustom) {
          continue;
        }
        let nextItem = data[curIndex + 1];
        if (!nextItem) {
          continue;
        }
        if (nextItem.isCustom) {
          continue;
        }
        nextItem.end = item.start;
        if ((nextItem.end || 0) < (nextItem.start || 0)) {
          nextItem.start = undefined;
        }
      }
    }

    // 保留两位小数
    if (data[index][type] && data[index][type].toString().split('.')[1]?.length > 2) {
      let val = data[index][type];
      const factor = Math.pow(10, 2);
      val = Math.round(val * factor) / factor;

      this.$nextTick(() => {
        data[index][type] = val;
      });
    }
  }

  // 恢复默认
  async reset() {
    this.resetLoading = true;
    try {
      await this.restoreSchCfg();
      this.$notify.success({
        title: '成功',
        message: '恢复成功',
      });
      this.settingSubjectHeader?.reset();
    } catch (error) {
      this.$notify.error({
        title: '失败',
        message: '恢复失败',
      });
    } finally {
      this.resetLoading = false;
    }
  }

  // 保存
  async save() {
    await this.checkConfig();

    if (this.currentGradeId == '0') {
      this.isShowSaveDialog = true;
    } else {
      this.handleSave([this.currentGradeId]);
    }
  }

  // 执行保存
  async handleSave(gradeIds: string[]) {
    this.saveLoading = true;
    this.isShowSaveDialog = false;
    try {
      await this.setSchCfg(gradeIds);
      this.$notify.success({
        title: '成功',
        message: '保存成功',
      });
    } catch (error) {
      this.$notify.error({
        title: '失败',
        message: '保存失败',
      });
    } finally {
      this.saveLoading = false;
    }
  }

  // 恢复学校配置
  public restoreSchCfg() {
    return restoreSchCfgAPI({
      phase: this.currentPhase,
      schId: this.$sessionSave.get('schoolInfo').id,
      type: SchoolSettingType.Rate,
      gradeId: this.currentGradeId,
      examId: this.examId,
    });
  }

  // 设置学校配置
  public setSchCfg(gradeIds: string[]) {
    const promises = [];
    for (const gradeId of gradeIds) {
      promises.push(
        setSchCfgAPI({
          schId: this.$sessionSave.get('schoolInfo').id,
          schName: this.$sessionSave.get('schoolInfo').schoolName,
          type: SchoolSettingType.Rate,
          phase: this.currentPhase,
          gradeId,
          jCfg: this.jCfg,
          examId: this.examId,
        })
      );
    }
    return Promise.all(promises);
  }

  // 获取配置信息
  public getCfg() {
    return {
      type: SchoolSettingType.Rate,
      jCfg: this.jCfg,
    };
  }

  // 检查配置
  public async checkConfig() {
    await new Promise((resolve, reject) => {
      this.form.validate(async valid => {
        if (valid) {
          resolve(true);
        } else {
          this.$notify.error({
            title: '【五率设置】',
            message: '请检查输入内容',
          });
          reject(false);
        }
      });
    });

    if (this.jCfg.length == 0) {
      this.$notify.error({
        title: '【五率设置】',
        message: '请选择学科',
      });
      return Promise.reject(false);
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../page-style.scss';

.input-number {
  width: 80px;
  margin: 0 5px;

  &::after {
    display: inline-block;
    content: '%';
    position: absolute;
    top: 2px;
    bottom: 0;
    right: 5px;
    color: #c0c4cc;
  }
}
</style>

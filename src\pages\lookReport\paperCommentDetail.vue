<template>
  <div>
    <el-container class="comment-container" v-loading="listLoading && !$sessionSave.get('configInfo')">
      <el-header height="72px" class="comment-header">
        <span class="title" v-if="quesObj">{{ quesObj.quesName }}{{ '-' + $getQuesType(quesObj.quesType) }}</span>
      </el-header>
      <el-main class="comment-body">
        <div class="comment-main" id="comment">
          <div class="ques-content" v-if="quesObj">
            <el-tabs v-model="activeName" @tab-click="handleClick">
              <!-- 原题 -->
              <el-tab-pane label="原题" name="first">
                <div class="question_content">
                  <div class="question_body" v-html="quesObj.topic"></div>
                  <div v-if="quesObj.data.levelcode != ''">
                    <div class="small-ques-content" v-for="(small, smallIndex) in quesObj.data.qs" :key="smallIndex">
                      <div class="question_body" v-html="small.topic"></div>
                    </div>
                  </div>
                  <div class="ques-detail" v-if="quesObj.showDetail">
                    <!--答案-->
                    <div class="answer_box display_flex align-items_flex-start">
                      <span class="flex_shrink_0 answer-tag" style="width: 165px"><strong>【答案】</strong></span>
                      <div class="flex_1" v-if="quesObj.levelcode == ''">
                        <div class="answer_content" v-if="quesObj.quesType === 2">
                          {{
                            quesObj.answer.split(",")[0] === "A"
                            ? "正确"
                            : "错误"
                          }}
                        </div>
                        <div class="answer_content" v-else v-html="quesObj.answer"></div>
                      </div>
                      <div v-else class="flex_1">
                        <div class="answer_content" v-if="quesObj.quesType === 2">
                          {{
                            quesObj.answer.split(",")[0] === "A"
                            ? "正确"
                            : "错误"
                          }}
                        </div>
                        <template v-else>
                          <div v-for="(small, smallIndex) in quesObj.data.qs" :key="smallIndex" class="answer_content">
                            <div style="display: flex">
                              <span>({{ smallIndex + 1 }})</span>
                              <div v-html="small.answer"></div>
                            </div>
                          </div>
                        </template>
                      </div>
                    </div>
                    <!--相关资源-->
                    <div class="answer_box display_flex align-items_center"
                      v-if="quesObj.resList && quesObj.resList.length">
                      <strong class="flex_shrink_0 answer-tag">【相关资源】</strong>
                      <div class="flex_1 display_flex align-items_flex-start flex-wrap_wrap">
                        <img class="answer-imgList" v-for="(imgItem, idx) in quesObj.resList" :key="idx"
                          @click="showImg(quesObj.resList, idx)" :src="`https://fs.iclass30.com/${imgItem.thumbnailUrl}`"
                          alt="" />
                      </div>
                    </div>
                    <!--考点-->
                    <div v-if="quesObj.points && quesObj.points.length">
                      <strong>【考点】</strong>
                      <span v-for="(it, idx) in quesObj.points" :key="it.name + Math.random()">{{ it.name
                      }}{{
  idx != quesObj.points.length - 1 ? "," : ""
}}</span>
                    </div>
                    <!--解析-->
                    <div v-if="quesObj.analysis" class="answer_box display_flex align-items_flex-start">
                      <div class="flex_shrink_0 answer-tag"><strong>【解析】</strong></div>
                      <div class="answer_content flex_1" v-html="'' + quesObj.analysis"></div>
                    </div>
                    <!--
                                  <div class="display_flex align-items_flex-start" style="margin-top:4px;">
                                      <strong>【统计】</strong>
                                      <div>
                                          共有{{quesObj.clsFullNum}}人答对，{{quesObj.clsErrorNum}}人答错
                                      </div>
                                  </div>
                                  -->
                  </div>
                </div>
              </el-tab-pane>
              <!-- 巩固题 -->
              <el-tab-pane :label="quesObj.normal.name" name="second" v-if="quesObj.normal">
                <div class="question_content" v-if="!quesObj.normal">
                  暂无巩固题
                </div>
                <div class="question_content" v-else>
                  <div class="question_body" v-html="quesObj.normal.content.topic"></div>
                  <div class="ques-detail" v-if="quesObj.showDetail">
                    <!--答案-->
                    <div class="answer_box display_flex align-items_flex-start">
                      <span class="flex_shrink_0 answer-tag" style="width: 152px"><strong>【答案】</strong></span>
                      <div class="flex_1">
                        <div class="answer_content" v-if="quesObj.quesType === 2">
                          {{
                            quesObj.answer.split(",")[0] === "A"
                            ? "正确"
                            : "错误"
                          }}
                        </div>
                        <div class="answer_content" v-else v-html="quesObj.normal.content.answer"></div>
                      </div>
                    </div>
                    <!--考点-->
                    <div v-if="quesObj.normal.points && quesObj.normal.points.length
                      ">
                      <strong>【考点】</strong>
                      <span v-for="(it, idx) in quesObj.normal.points" :key="it.name + Math.random()">{{ it.name
                      }}{{
  idx != quesObj.points.length - 1 ? "," : ""
}}</span>
                    </div>
                    <!--解析-->
                    <div v-if="quesObj.analysis" class="answer_box display_flex align-items_flex-start">
                      <div class="flex_shrink_0 answer-tag"><strong>【解析】</strong></div>
                      <div class="answer_content flex_1" v-html="'' + quesObj.normal.content.analysis"></div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <!-- 拔高题 -->
              <el-tab-pane :label="quesObj.hard.name" name="third" v-if="quesObj.hard">
                <div class="question_content" v-if="!quesObj.hard">
                  暂无拔高题
                </div>
                <div class="question_content" v-else>
                  <div class="question_body" v-html="quesObj.hard.content.topic"></div>
                  <div class="ques-detail" v-if="quesObj.showDetail">
                    <!--答案-->
                    <div class="answer_box display_flex align-items_flex-start">
                      <span class="flex_shrink_0 answer-tag"
                        style="width: 165px; min-width: 165px;"><strong>【答案】</strong></span>
                      <div class="flex_1">
                        <div class="answer_content" v-if="quesObj.quesType === 2">
                          {{
                            quesObj.answer.split(",")[0] === "A"
                            ? "正确"
                            : "错误"
                          }}
                        </div>
                        <div class="answer_content" v-else v-html="quesObj.hard.content.answer"></div>
                      </div>
                    </div>
                    <!--考点-->
                    <div v-if="quesObj.hard.points && quesObj.hard.points.length">
                      <strong>【考点】</strong>
                      <span v-for="(it, idx) in quesObj.hard.points" :key="it.name + Math.random()">{{ it.name
                      }}{{
  idx != quesObj.points.length - 1 ? "," : ""
}}</span>
                    </div>
                    <!--解析-->
                    <div v-if="quesObj.analysis" class="answer_box display_flex align-items_flex-start">
                      <div class="flex_shrink_0 answer-tag"><strong>【解析】</strong></div>
                      <div class="answer_content flex_1" v-html="'' + quesObj.hard.content.analysis"></div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 拔高题2 -->
              <el-tab-pane :label="quesObj.hard2.name" name="four" v-if="quesObj.hard2">
                <div class="question_content" v-if="!quesObj.hard2">
                  暂无拔高题
                </div>
                <div class="question_content" v-else>
                  <div class="question_body" v-html="quesObj.hard2.content.topic"></div>
                  <div class="ques-detail" v-if="quesObj.showDetail">
                    <!--答案-->
                    <div class="answer_box display_flex align-items_flex-start">
                      <span class="flex_shrink_0 answer-tag"
                        style="width: 165px; min-width: 165px;"><strong>【答案】</strong></span>
                      <div class="flex_1">
                        <div class="answer_content" v-if="quesObj.quesType === 2">
                          {{
                            quesObj.answer.split(",")[0] === "A"
                            ? "正确"
                            : "错误"
                          }}
                        </div>
                        <div class="answer_content" v-else v-html="quesObj.hard2.content.answer"></div>
                      </div>
                    </div>
                    <!--考点-->
                    <div v-if="quesObj.hard2.points && quesObj.hard2.points.length">
                      <strong>【考点】</strong>
                      <span v-for="(it, idx) in quesObj.hard2.points" :key="it.name + Math.random()">{{ it.name
                      }}{{
  idx != quesObj.points.length - 1 ? "," : ""
}}</span>
                    </div>
                    <!--解析-->
                    <div v-if="quesObj.analysis" class="answer_box display_flex align-items_flex-start">
                      <div class="flex_shrink_0 answer-tag"><strong>【解析】</strong></div>
                      <div class="answer_content flex_1" v-html="'' + quesObj.hard2.content.analysis"></div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
        <div class="showAnswer" @click="showAnswer">
          {{ quesObj.showDetail ? "隐藏答案" : "答案解析" }}
        </div>
      </el-main>
      <el-footer height="110px" class="comment-footer">
        <div class="back-btn" @click="backPage">返回</div>
        <el-pagination background :page-size="1" :pager-count="5" @current-change="handleCurrentChange"
          :current-page="currentPage" layout="prev, pager, next" class="comment-pagination" :total="list.length">
        </el-pagination>
      </el-footer>
    </el-container>
    <sparkDialog :curIndex="curIndex" :list="sparkList" @closeDialog="closeDialog" v-if="sparkVisible"></sparkDialog>
  </div>
</template>

<script>
import { chooseQuesSearch } from "@/service/pbook";
import sparkDialog from "@/components/sparkDialog.vue";

export default {
  name: "comment-page",
  data() {
    return {
      sparkVisible: false,
      sparkList: [],
      curIndex: 0,
      quesNumber: 0,
      listLoading: true,
      currentPage: 1,
      quesObj: {},
      list: [],
      queryData: {},
      activeName: "first",
    };
  },
  components: {
    sparkDialog,
  },
  mounted() {
    console.log("papercommentdetail mounted");
    this.queryData = this.$route.query;
    this.list = this.$sessionSave.get("selectQuesList");
    this.chooseQuesSearch();
    this.$nextTick(() => {
      this.quesNumber = Math.floor(
        ((document.body.clientWidth -
          document.getElementsByClassName("back-btn")[0].offsetWidth) /
          2 -
          100) /
        58 -
        3
      );
      this.quesNumber =
        this.quesNumber <= 5 ? 7 : this.quesNumber > 21 ? 19 : this.quesNumber;
      this.quesNumber =
        this.quesNumber % 2 === 0 ? this.quesNumber - 1 : this.quesNumber;
    });
  },
  methods: {
    closeDialog() {
      this.sparkVisible = false;
    },
    backPage() {
      this.$sessionSave.set("fromCommentDetail", true);
      let path = this.$sessionSave.get("configInfo")
        ? "/commentDetail"
        : "/home/<USER>/paperComment";
      this.$router.push({
        path: path,
        query: {
          from: "commentDetail",
          examId: this.$route.query.examId,
          subjectRealId: this.$route.query.subjectRealId,
          schoolId: this.$route.query.schoolId,
        },
      });
    },
    // 分页查询
    handleCurrentChange(val) {
      this.currentPage = val;
      this.chooseQuesSearch();
      //滚动条位置默认
      let doc = document.getElementById("comment");
      doc.scrollTop = 0;
      this.activeName = "first";
    },
    //切换题型
    handleClick() {
      if (this.quesObj.showDetail == true) {
        this.quesObj.showDetail = false;
      }
    },
    showAnswer() {
      this.$set(this.quesObj, "showDetail", !this.quesObj.showDetail);
      this.$nextTick(() => {
        this.$katexUpdate();
      });
    },
    showImg(list, index) {
      let copyList = this.$deepClone(list);
      copyList.forEach((item) => {
        if (
          item.resourceUrl.indexOf("huohuaschool") >= 0 &&
          item.resourceUrl.indexOf("play/") == -1
        ) {
          item.resourceUrl =
            item.resourceUrl.slice(0, 39) +
            "play/" +
            item.resourceUrl.slice(39);
        } else {
          let match = item.resourceUrl.match(
            /\/cooperation\/tencent\/let\/download_video.php\?id=([^&]*)/
          );
          if (match) {
            let id = match[1];
            let LLKT_URL = process.env.VUE_APP_LLKTURL;
            if (id) {
              item.resourceUrl = `${LLKT_URL}/llkt/${id}.mp4`;
            }
          }
        }
      });

      this.curIndex = index;
      this.sparkList = this.$deepClone(copyList);
      this.sparkVisible = true;
    },
    // 获取涉及题目的题面
    chooseQuesSearch() {
      this.listLoading = true;
      let ques = this.list[this.currentPage - 1];
      let qidStr = ques.quesId;
      if (ques.normalId) {
        qidStr += `,${ques.normalId}`;
      }
      if (ques.hardId) {
        qidStr += `,${ques.hardId}`;
      }
      this.quesObj = {};
      chooseQuesSearch({
        qIds: qidStr,
        subjectId: this.queryData.subjectId,
        phaseId: this.queryData.phaseId,
      })
        .then((data) => {
          for (let i = 0; i < data.data.length; i++) {
            let v = data.data[i];
            let quesItem = {
              content: v,
              points: (v.data && v.data.tag_ids) || [],
              type: v.data.type,
            };

            if (v.qId == ques.quesId) {
              for (let k in v) {
                this.quesObj[k] = v[k];
              }

              this.quesObj.quesName = ques.quesName;
              this.quesObj.points = v.tag_ids || [];

              let item = this.list.find((q) => q.quesId == v.qId);
              this.$set(
                this.quesObj,
                "totalIndex",
                Number(item.totalIndex) + 1
              );
              this.$set(this.quesObj, "clsFullNum", item.clsFullNum);
              this.$set(this.quesObj, "clsErrorNum", item.clsErrorNum);
              this.$set(this.quesObj, "resList", item.resList);
            } else if (v.qId == ques.normalId) {
              this.quesObj.normal = quesItem;
            } else if (v.qId == ques.hardId) {
              this.quesObj.hard = quesItem;
            }
          }
          console.log(111, this.quesObj);

          this.listLoading = false;

          this.$nextTick(() => {
            this.$katexUpdate();
          });
        })
        .catch((err) => {
          this.listLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.ques-content {
  min-height: 100px;
  padding: 0px 0 10px;
  line-height: 2em;
}

.question_body {
  font-size: 36px;
}

.katex-display {
  font-size: 36px !important;
}

.optionsClass {
  font-size: 36px;
}

.comment-container {
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: #f1f5f8ff;
}

.comment-header {
  font-size: 24px;
  font-weight: bold;
  color: #3f4a54;
  box-shadow: 0px 1px 6px 0px rgba(33, 35, 36, 0.1);
  line-height: 72px;
  padding-left: 20px;
  background: #fff;
}

.comment-body {
  position: relative;
  padding: 20px 20px 0;
  overflow: hidden;

  .comment-main {
    width: 100%;
    height: 100%;
    background: #fff;
    box-shadow: 0px 1px 3px 0px rgba(84, 84, 84, 0.1);
    overflow-y: auto;
    overflow-x: hidden;
    font-size: 36px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #3f4a54;
    padding: 20px;
  }

  .showAnswer {
    position: absolute;
    right: 20px;
    bottom: 0;
    width: 200px;
    height: 90px;
    background: #feab40;
    border-radius: 20px 0 0 0;
    font-size: 36px;
    font-weight: 400;
    color: #fff;
    text-align: center;
    line-height: 90px;
    cursor: pointer;
  }
}

.comment-footer {
  position: relative;

  .back-btn {
    position: absolute;
    width: 200px;
    height: 90px;
    background: #fff;
    border: 1px solid #dadde0;
    border-radius: 20px 20px 0px 0px;
    left: 50%;
    margin-left: -45px;
    bottom: 0;
    font-size: 36px;
    color: #3f4a54;
    text-align: center;
    line-height: 90px;
    cursor: pointer;
  }
}

.ques-detail {
  padding: 20px;
  width: 100%;
  height: auto;
  background: #f7fafc;

  >div {
    margin-bottom: 10px;
  }

  .answer_box {
    margin-top: 10px;
    margin-bottom: 0;
  }

  .answer-imgList {
    display: inline-block;
    width: 140px;
    height: 85px;
    margin-right: 10px;
    cursor: pointer;
    margin-bottom: 10px;
    border: 1px solid #cecece;
  }
}

.small-ques-content {
  padding: 26px 20px 20px 20px;
}
</style>
<style lang="scss">
.comment-pagination {
  padding: 0;
  position: absolute;
  right: 20px;
  bottom: 31px;

  &.el-pagination.is-background {
    button {
      width: 48px;
      height: 48px;
      background: #fff;
      border: 1px solid #e4e8eb;
      border-radius: 50%;

      .el-icon {
        font-size: 20px;
      }
    }

    .el-pager {
      height: 48px;
      line-height: 48px;

      .number {
        width: 48px;
        height: 48px;
        line-height: 48px;
        border-radius: 50%;
        background: #fff;
        border: 1px solid #e4e8eb;
        font-size: 24px;
        font-weight: 400;
        color: #3f4a54ff;

        &.active {
          color: #fff;
          background: #feab40;
          box-shadow: 0px 1px 9px 0px rgba(255, 160, 38, 0.15);
        }
      }

      .more {
        height: 48px;
        line-height: 48px;
      }
    }
  }
}

.ques-content {
  .el-tabs__item {
    font-size: 30px !important;
  }
}

.answer-tag {
  min-width: 4em;
}
</style>

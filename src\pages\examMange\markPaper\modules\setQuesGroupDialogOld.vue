<template>
    <el-dialog
      custom-class="seled-ques-grooup"
      :visible="modalVisible"
      width="790px"
      :before-close="handleClose"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
    >
      <div slot="title" class="dialog-title">
        <span style="margin-right: 10px">设置</span>
      </div>
      <div class="set-group-container">
        <el-radio-group v-model="currQuesInfo.scoreMode" v-if="currQuesInfo.isMerge">
          <el-radio :label="0">合并给一个分</el-radio>
          <el-radio :label="1">独立小题给分</el-radio>
        </el-radio-group>
        <div class="score-container">
          <div v-if="currQuesInfo.scoreMode == 0 || !currQuesInfo.isMerge">
            <span v-if="currQuesInfo.isMerge">
              <el-input
                v-model="currQuesInfo.score"
                class="input-container"
                @input="checkScore"
              ></el-input
              >&nbsp;分
            </span>
            <span class="set-scoreStep">
              步长
              <el-select v-model="currQuesInfo.scoreStep" placeholder="请选择" class="group-select">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              &nbsp;分
              <!-- <el-input v-model="currQuesInfo.scoreStep" class="input-container"></el-input>&nbsp;分 -->
            </span>
          </div>
          <div v-else class="small-container">
            <div v-for="(item, index) in currQuesInfo.data" :key="index" class="small-ques">
              <span>{{ item.quesNos }}</span>
              <span>
                <el-input v-model="item.score" class="input-container" style=""></el-input>&nbsp;分
              </span>
              <span class="set-scoreStep">
                步长
                <el-select v-model="item.scoreStep" placeholder="请选择" class="group-select">
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
                &nbsp;分
                <!-- <el-input v-model="item.scoreStep" class="input-container"></el-input>&nbsp;分 -->
              </span>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeModal">取 消</el-button>
        <el-button type="primary" :loading="isComfirming" @click="sureClick">确 定</el-button>
      </div>
    </el-dialog>
  </template>
  
  <script>
  export default {
    props: {
      modalVisible: {
        type: Boolean,
        required: false,
      },
      currentQues: {
        type: Object,
        required: false,
      },
      currentSmallIndex: {
        type: Number,
        required: false,
      },
    },
    data() {
      return {
        isComfirming: false,
        currQuesInfo: {},
        options: [
          {
            value: 0.5,
            label: 0.5,
          },
          {
            value: 1,
            label: 1,
          },
          {
            value: 1.5,
            label: 1.5,
          },
          {
            value: 2,
            label: 2,
          },
          {
            value: 3,
            label: 3,
          },
          {
            value: 4,
            label: 4,
          },
          {
            value: 5,
            label: 5,
          },
        ],
      };
    },
    watch: {
      modalVisible(newVal, oldVal) {
        this.currQuesInfo = JSON.parse(JSON.stringify(this.currentQues));
      },
    },
    mounted() {},
    methods: {
      // 关闭弹窗
      handleClose(done) {
        this.$emit("close-set-group");
        done();
      },
      /**
       * 取消
       */
      closeModal() {
        this.$emit("close-set-group");
      },
      sureClick() {
        if (this.currQuesInfo.scoreMode == 1) {
          this.currQuesInfo.score = this.currQuesInfo.data.reduce(
            (acc, item) => acc + Number(item.score),
            0
          );
        }
        this.$emit("confirm-set-group", this.currQuesInfo, this.currentSmallIndex);
      },
      checkScore() {},
    },
  };
  </script>
  
  <style lang="scss" scopeed>
  .set-group-container {
    display: flex;
    flex-direction: column;
    .score-container {
      margin-top: 20px;
    }
  }
  .input-container {
    width: 86px;
    height: 32px;
    margin-left: 10px;
  }
  .small-container {
    display: flex;
    flex-wrap: wrap;
    .small-ques {
      display: flex;
      align-items: center;
      margin-right: 15px;
      margin-bottom: 10px;
    }
  }
  .set-scoreStep {
    margin-left: 20px;
  }
  </style>
  <style lang="scss">
  .seled-ques-grooup {
    .el-dialog__header {
      height: 45px;
      .dialog-title {
        line-height: 45px;
      }
    }
  }
  .group-select {
    width: 86px;
    .el-input__suffix {
      display: flex;
      align-items: center;
    }
  }
  </style>
<template>
  <div>
    <div class="header-box">
      <div class="titleLine">优困生分布</div>
      <el-button type="primary" @click="exportTable">导出</el-button>
    </div>
    <div v-loading="listLoading" style="min-height: 300px">
      <el-table
        v-if="tableData.length"
        class="top-diff--table"
        :data="tableData"
        stripe
        @sort-change="sortTable"
        :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
        style="width: 100%"
        v-sticky-table="0"
      >
        <el-table-column align="center" prop="clzName" fixed label="班级">
          <template slot-scope="scope">
            <span>{{ scope.row.clzName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="item" align="center" :label="`${filterData.subjectName}`">
          <el-table-column align="center" prop="fineNum" sortable="custom" key="1" label="学优生">
            <template slot-scope="scope">
              <span>{{ scope.row.fineNum }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="fineRate" sortable="custom" key="2" label="占比">
            <template slot-scope="scope">
              <span>{{ scope.row.fineRate ? scope.row.fineRate + '%' : '0%' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="fineAvg" sortable="custom" key="2" label="均分">
            <template slot-scope="scope">
              <span>{{ !scope.row.fineAvg || scope.row.fineAvg == 0 ? '--' : scope.row.fineAvg }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="badNum" sortable="custom" label="学困生">
            <template slot-scope="scope">
              <span>{{ scope.row.badNum }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="badRate" sortable="custom" label="占比">
            <template slot-scope="scope">
              <span>{{ scope.row.badRate ? scope.row.badRate + '%' : '0%' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="badAvg" sortable="custom" label="均分">
            <template slot-scope="scope">
              <span>{{ !scope.row.badAvg || scope.row.badAvg == 0 ? '--' : scope.row.badAvg }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
    <div v-if="!tableData.length && !listLoading" class="nodata">
      <img :src="noResImg" alt="" />
      <p class="text-center">暂无数据</p>
    </div>
  </div>
</template>

<script>
import NumericInput from '@/components/NumericInput';
import { getTopAndDiffStu } from '@/service/pexam';
import UserRole from '@/utils/UserRole';

export default {
  name: 'top-and-diff',
  components: {
    NumericInput,
  },
  props: {
    //指标数据
    targetData: {
      type: Object,
      default: {},
    },
    filterData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      // 表格数据
      tableData: [],
      // 获取的学科id和学科name
      listLoading: false,
    };
  },
  watch: {
    filterData: {
      deep: true,
      handler(newValue, oldValue) {
        if (!newValue) return;
        this.updateFilter();
      },
    },
  },
  mounted() {
    this.updateFilter();
  },
  methods: {
    // 获取到班级和学科后更新
    updateFilter(data) {
      this.getTopAndDiffStu();
    },
    // 获取学优生学困生列表数据
    getTopAndDiffStu() {
      this.listLoading = true;
      getTopAndDiffStu({
        examId: this.$sessionSave.get('reportDetail').examId,
        subjectId: this.filterData.subjectId,
        qType: this.filterData.qType,
      })
        .then(res => {
          this.listLoading = false;
          let clsList = this.$sessionSave.get('innerClassList');
          let list = [];
          clsList.forEach(clz => {
            var item = res.data.find(x => x.clzId == clz.id);
            if (item) {
              list.push(item);
            }
          });

          this.tableData = list;
        })
        .catch(err => {
          console.error(err);
          this.listLoading = false;
          this.tableData = [];
        });
    },
    // 处理表格需要数据
    handleData() {
      let obj = {},
        copyData = this.$deepClone(this.tableData);
      copyData.map(item => {
        if (obj[item.clzId]) {
          obj[item.clzId] = {
            ...item,
            ...obj[item.clzId],
          };
        } else {
          obj[item.clzId] = item;
        }
      });
      let headVal = Object.values(obj).filter(item => item.clzId === '');
      let restVal = Object.values(obj).filter(item => item.clzId !== '');
      this.tableData = headVal.concat(restVal);
    },
    // 全部班级不参与排序
    sortTable({ prop, order }) {
      let data = this.tableData.map(item => {
          if (!item[prop]) {
            item[prop] = 0;
          }
          return item;
        }),
        headData = {};
      if (order == 'descending') {
        // 降序
        data = data.sort((a, b) => a[prop] - b[prop]);
      } else if (order == 'ascending') {
        // 升序
        data = data.sort((a, b) => b[prop] - a[prop]);
      } else {
        data = this.$deepClone(this.tableData);
      }
      headData = data.filter(item => item.clzId === '');
      data = data.filter(item => item.clzId !== '');
      data.unshift(headData[0]);
      this.tableData = data;
    },

    // 导出
    async exportTable() {
      const { examId, campusCode, v, year } = this.$sessionSave.get('reportDetail');
      let role = '';
      if (!UserRole.isOperation) {
        const { year, campusCode } = this.$sessionSave.get('reportDetail');
        const map = await UserRole.utils.getRoleSubjectClassMap(year, campusCode, this.$sessionSave.get('reportType') == 'school' ? true : false);
        role = JSON.stringify(map);
      }
      const params = {
        examId,
        qType: this.filterData.qType,
        role,
        v,
      };
      const urlSearch = new URLSearchParams(params);
      const path = '/pexam/_/exp-fine-bad';
      let url = process.env.VUE_APP_KKLURL + path + `?${urlSearch.toString()}`;
      window.open(url);
    },
  },
};
</script>

<style lang="scss" scoped>
.filter {
  line-height: 70px;
  .type-select {
    width: 110px;
    margin-right: 30px;
  }
  .dis_block {
    display: inline-block;
    margin-right: 20px;
  }
}

.scoreInput {
  width: 50px;
  position: relative;
  margin-right: 26px;
  &:after {
    content: '%';
    position: absolute;
    right: -26px;
    top: 0;
    border: 1px solid #dcdfe6;
    border-radius: 0 4px 4px 0;
    width: 26px;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    text-align: center;
    line-height: 30px;
    z-index: 11;
    border-left: none;
  }
}

.noPercent {
  margin-right: 3px;
  .numeric-input-inner {
    border-radius: 4px !important;
  }
  &:after {
    content: none;
  }
}

.sureScoreBtn {
  margin-left: 20px;
  background: #409eff;
  height: 30px;
  padding: 0;
  width: 60px;
  line-height: 28px;
  text-align: center;
}
.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;
  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 15px;
  }
}
.header-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
<style lang="scss">
.scoreInput {
  .el-input-group__append {
    padding: 0 10px;
  }
  &.numeric-input-box .numeric-input-inner {
    border-radius: 4px 0 0 4px;
  }
}
.hasBorder {
  &.numeric-input-box .numeric-input-inner {
    border-radius: 4px !important;
  }
}
</style>

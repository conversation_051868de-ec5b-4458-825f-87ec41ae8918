/** 学校设置类型 */
export enum SchoolSettingType {
  /** 试卷讲评 */
  PaperReview = 2,
  /** 优秀作答 */
  ExcellentAnswer = 3,
  /** 一分五率 */
  Rate = 6,
  /** 等级设置 */
  LevelSetting = 7,
  /** 优困生设置 */
  StudentTypeSetting = 8,
  /** 分数段设置 */
  ScoreLineSetting = 9,
  /** 分数段设置 */
  ScoreRangeSetting = 10,
  /** 名次设置 */
  RankSetting = 11,
  /** 成绩统计规则 */
  ScoreStatisticRule = 12,
  /** 报告设置 */
  ReportSetting = 13,
}

/** 年级信息 */
export interface GradeInfo {
  phase: number | string;
  gradeName: string;
  gradeId: string | number;
  center_code: string;
  systemCode: string;
  year: number | string;
  id: string | number;
  gradeCode: string;
  grade_code: string;
  grade_name: string;
}

// 事件类型
export enum SchoolSettingEvent {
  /** 设置变化 */
  SettingChange = 'setting-change',
}

// 设置变化参数
export interface SettingChangeParams {
  phase: string | number;
  grade: string | number;
}

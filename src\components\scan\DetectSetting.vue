<template>
  <div>
    <el-dialog title="重新识别" :visible.sync="dlgVisible" :show-close="false" :before-close="closeDialog" width="500px">
      <p class="tips-box"><i class="el-icon-warning-outline detect-tips"></i>重新识别后已处理的异常会再次出现！！！</p>
      <el-form :model="info">
        <el-form-item label="仅识别红笔：" :label-width="labelWidth" title="主观题只有用红笔批改才识别，黑笔是未批">
          <el-switch v-model="info.IsRed">
          </el-switch>
          <span class="tips-text">（主观题只有用红笔批改才识别，黑笔是未批）</span>
        </el-form-item>
        <el-form-item label="A3转A4：" :label-width="labelWidth" title="A4的答题卡印成A3纸">
          <el-switch v-model="info.A3ToA4">
          </el-switch>
          <span class="tips-text">（A4的答题卡印成A3纸）</span>
        </el-form-item>
        <el-form-item label="压缩图片：" :label-width="labelWidth">
          <el-switch v-model="info.IsZip">
          </el-switch>
        </el-form-item>
        <el-form-item v-if="info.Version" label="扫描端版本：" :label-width="labelWidth">
          <label>{{ info.Version }}</label>
        </el-form-item>
        <el-form-item v-if="info.DeviceName" label="扫描仪型号：" :label-width="labelWidth">
          <label>{{ info.DeviceName }}</label>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" :loading="scanLoading" :disabled="!info.IsRed && !info.A3ToA4" @click="toReDetectTask">开始识别</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>


import {getTaskSetting, taskReDetect} from "@/service/pexam";

export default {
  props: {
    taskId: {
      type: String,
      default: '',
    },
    preId: {
      type: String,
      default: '',
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      labelWidth: '150px',
      defaultInfo: null,
      info: {
        IsZip: true,
        IsRed: false,
        A3ToA4: false,
        Version: '',
        DeviceName: '',
      },
      scanData: '',
      dlgVisible: false,
      scanLoading: false,
    };
  },
  watch: {
    visible(newVal, oldVal) {
      if (newVal) {
        this.requestTaskSetting()
      }
      this.dlgVisible = newVal
    }
  },
  mounted() {
    this.defaultInfo = JSON.stringify(this.info)
  },
  methods: {
    /**
     * 加载设置参数
     * @param scanData
     */
    loadScanData(scanData) {
      this.scanData = scanData
      if (scanData) {
        this.info = Object.assign(this.info, JSON.parse(scanData))
      } else {
        this.info = Object.assign(this.info, JSON.parse(this.defaultInfo))
      }
    },
    /**
     * 获取批次信息
     */
    requestTaskSetting() {
      getTaskSetting(this.preId).then(res => {
        if (res.code === 1) {
          this.loadScanData(res.data.scanData)
        }
      })
    },
    /**
     * 重新识别
     * @param item
     */
    toReDetectTask() {
      if (this.taskId) {
        this.scanLoading = true;
        let data = JSON.stringify(this.info)
        taskReDetect({
          taskId: this.taskId,
          data: data
        }).then((res) => {
          this.scanLoading = false;
          this.$emit('onTaskReDetect', this.taskId)
          this.closeDialog()
        }).catch((data) => {
        });
      }
    },
    closeDialog() {
      this.$emit('update:visible', false)
    },
  },
};
</script>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 5px;
}

.tips-box {
  margin-left: 40px;
  margin-bottom: 10px;
}
.tips-text {
  font-size: 12px;
  color: #999;
}

.detect-tips {
  color: #F56C6C;
  padding-right: 10px;
  font-size: 16px;
}
</style>

/*
 * @Descripttion: 自定义指令
 * @Author: 小圆
 * @Date: 2023-12-09 10:53:44
 * @LastEditors: liuyue <EMAIL>
 */

import '@/styles/sticky-table-header.scss';
import Vue from 'vue';

const debounce = function (func, delay) {
  let timer = null;
  return function () {
    if (timer) clearTimeout(timer);
    timer = null;
    let self = this;
    let args = arguments;
    timer = setTimeout(() => {
      func.apply(self, args);
    }, delay);
  };
};

function createDragDirective(getDivData) {
  return {
    bind(el, binding) {
      const state = {
        mouseOffset: 0,
        mouseFlag: false,
      };

      const mouseDownHandler = e => {
        state.mouseOffset = e.clientX;
        state.mouseFlag = true;
      };

      const mouseUpHandler = () => {
        state.mouseFlag = false;
      };

      const mouseMoveHandler = e => {
        if (state.mouseFlag) {
          let divData = getDivData(el);
          divData.scrollLeft -= -state.mouseOffset + (state.mouseOffset = e.clientX);
        }
      };

      const mouseLeaveHandler = () => {
        state.mouseFlag = false;
      };

      el.addEventListener('mousedown', mouseDownHandler);
      el.addEventListener('mouseup', mouseUpHandler);
      el.addEventListener('mousemove', mouseMoveHandler);
      el.addEventListener('mouseleave', mouseLeaveHandler);

      el.__dragEventHandlers__ = {
        mouseDownHandler,
        mouseUpHandler,
        mouseMoveHandler,
        mouseLeaveHandler
      };

      el.style.setProperty('cursor', 'move');
      el.style.setProperty('user-select', 'none');
      el.style.setProperty('-webkit-touch-callout', 'none');
      el.style.setProperty('-webkit-user-select', 'none');
      el.style.setProperty('-khtml-user-select', 'none');
      el.style.setProperty('-moz-user-select', 'none');
      el.style.setProperty('-ms-user-select', 'none');
    },
    unbind(el) {
      const { mouseDownHandler, mouseUpHandler, mouseMoveHandler, mouseLeaveHandler } =
        el.__dragEventHandlers__;
      el.removeEventListener('mousedown', mouseDownHandler);
      el.removeEventListener('mouseup', mouseUpHandler);
      el.removeEventListener('mousemove', mouseMoveHandler);
      el.removeEventListener('mouseleave', mouseLeaveHandler);
    },
  };
}

// 拖动el表格指令
export const dragTable = createDragDirective(el => el.querySelector('.el-table__body-wrapper'));
// 自定义拖拽指定
export const drag = createDragDirective(el => el);

export const loadMore = {
  bind(el, binding, vnode) {
    const { expand } = binding.modifiers;
    // 使用更丰富的功能，支持父组件的指令作用在指定的子组件上
    if (expand) {
      /**
       * target 目标DOM节点的类名
       * distance 减少触发加载的距离阈值，单位为px
       * func 触发的方法
       * delay 防抖时延，单位为ms
       * load-more-disabled 是否禁用无限加载
       */
      let { target, distance = 0, func, delay = 200 } = binding.value;
      if (typeof target !== 'string') return;
      let targetEl = el.querySelector(target);
      if (!targetEl) {
        console.log('找不到容器');
        return;
      }
      binding.handler = debounce(function () {
        const { scrollTop, scrollHeight, clientHeight } = targetEl;
        let disabled = el.getAttribute('load-more-disabled');
        disabled = vnode[disabled] || disabled;

        if (scrollHeight <= scrollTop + clientHeight + distance) {
          if (disabled) return;
          func && func();
        }
      }, delay);
      targetEl.addEventListener('scroll', binding.handler);
    } else {
      binding.handler = debounce(function () {
        const { scrollTop, scrollHeight, clientHeight } = el;
        if (scrollHeight === scrollTop + clientHeight) {
          binding.value && binding.value();
        }
      }, 200);
      el.addEventListener('scroll', binding.handler);
    }
  },
  // unbind(el, binding) {
  //   let { arg } = binding;
  //   // 使用更丰富的功能，支持父组件的指令作用在指定的子组件上
  //   if (arg === "expand") {
  //     /**
  //      * target 目标DOM节点的类名
  //      * offset 触发加载的距离阈值，单位为px
  //      * method 触发的方法
  //      * delay 防抖时延，单位为ms
  //      */
  //     const { target } = binding.value;
  //     if (typeof target !== "string") return;
  //     let targetEl = el.querySelector(target);
  //     targetEl && targetEl.removeEventListener("scroll", binding.handler);
  //     targetEl = null;
  //   } else {
  //     el.removeEventListener("scroll", binding.handler);
  //     el = null;
  //   }
  // },
};
export const clickFiveTimesDirective = {
  bind(el, binding) {
    let clickCount = 0;
    let lastClickTime = null;

    const handleClick = () => {
      const currentTime = Date.now();
      if (lastClickTime && currentTime - lastClickTime < 300) {
        clickCount++;
        if (clickCount >= 10) {
          binding.value();
          clickCount = 0;
        }
      } else {
        clickCount = 1;
      }

      lastClickTime = currentTime;
    };
    el.addEventListener('click', handleClick);
  },
};

export const dragDialog = {
  bind(el, binding, vnode) {
    // 确保在 Dialog 渲染完成后再获取元素
    const dialogWrapperEl = el
    const dialogHeaderEl = el.querySelector('.el-dialog__header')
    const dragDom = el.querySelector('.el-dialog')
    
    if (!dialogHeaderEl || !dragDom) {
      console.warn('v-drag: 未找到 .el-dialog 或 .el-dialog__header 元素')
      return
    }

    // 重置对话框位置
    const resetPosition = () => {
      dragDom.style.position = 'relative'
      dragDom.style.left = '0px'
      dragDom.style.top = '0px'
    }

    // 记录对话框是否已经显示
    let isDialogShown = false

    // 监听对话框显示状态
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
          const isCurrentlyShown = dialogWrapperEl.style.display !== 'none'
          
          // 只在对话框从隐藏变为显示时重置位置
          if (!isDialogShown && isCurrentlyShown) {
            resetPosition()
          }
          isDialogShown = isCurrentlyShown
        }
      })
    })

    // 开始观察对话框元素
    observer.observe(dialogWrapperEl, {
      attributes: true,
      attributeFilter: ['style']
    })

    dialogHeaderEl.style.cursor = 'move'
    dragDom.style.position = 'relative'

    // 获取原有属性
    const sty = getComputedStyle(dragDom)
    
    dialogHeaderEl.onmousedown = (e) => {
      // 鼠标按下，计算当前元素距离可视区的距离
      const disX = e.clientX - dialogHeaderEl.offsetLeft
      const disY = e.clientY - dialogHeaderEl.offsetTop
      
      // 获取到的值带px 正则匹配替换
      let styL, styT

      // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
      if (sty.left.includes('%')) {
        styL = +document.body.clientWidth * (+sty.left.replace(/\%/g, '') / 100)
        styT = +document.body.clientHeight * (+sty.top.replace(/\%/g, '') / 100)
      } else {
        styL = +sty.left.replace(/\px/g, '') || 0
        styT = +sty.top.replace(/\px/g, '') || 0
      }

      document.onmousemove = function(e) {
        // 通过事件委托，计算移动的距离 
        const l = e.clientX - disX
        const t = e.clientY - disY

        // 移动当前元素  
        dragDom.style.left = `${l + styL}px`
        dragDom.style.top = `${t + styT}px`
      }

      document.onmouseup = function(e) {
        document.onmousemove = null
        document.onmouseup = null
      }

      // 阻止事件冒泡
      return false
    }
  }
}

// 限制连续点击
export const preventReClick = {
  bind: function (el, binding, vnode) {
    el.addEventListener('click', function (event) {
      if (!el.disabled) {
        console.log('按钮被点击');
        // 禁用按钮
        el.disabled = true;
        setTimeout(function () {
          el.disabled = false;
        }, binding.value || 500); // 默认为500毫秒
      }
    });
  },
};

function updateStickyTop(el, binding) {
  const { value, oldValue } = binding;
  if (value === oldValue) return;

  const top = Number(value);
  if (!isNaN(top)) {
    el.style.setProperty('--sticky-top', `${top}px`);
  }
}

let stickyDom;
export const stickyTable = {
  bind(el, binding) {
    stickyDom = el.querySelector('.el-table') || el;
    stickyDom.setAttribute('is-sticky', true);
    updateStickyTop(stickyDom, binding);
  },
  update(el, binding) {
    updateStickyTop(stickyDom, binding);
  },
};

/**
 * @name 支持元素自由拖动
 * @description 支持传入操作句柄 v-drag-dom="{ handle: '.move' }"
 * */
export const dragDom = {
    bind(el, binding) {
      let handle = null;
      let { handle: handleSelector } = binding.value || {};

      if (handleSelector) {
        handle = el.querySelector(handleSelector);
      }
      if (!handle) {
        handle = el;
      }

      handle.addEventListener('mousedown', (e) => {
        e.preventDefault();
        const startX = e.clientX;
        const startY = e.clientY;
        const initialLeft = parseInt(getComputedStyle(el).left) || 0;
        const initialTop = parseInt(getComputedStyle(el).top) || 0;

        // 获取屏幕的宽度和高度
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;

        const onMouseMove = (moveEvent) => {
          const dx = moveEvent.clientX - startX;
          const dy = moveEvent.clientY - startY;
          let newLeft = initialLeft + dx;
          let newTop = initialTop + dy;

          // 限制元素的左边界
          newLeft = Math.max(0, newLeft);
          // 限制元素的右边界
          newLeft = Math.min(screenWidth - el.offsetWidth, newLeft);
          // 限制元素的上边界
          newTop = Math.max(0, newTop);
          // 限制元素的下边界
          newTop = Math.min(screenHeight - el.offsetHeight, newTop);

          el.style.left = newLeft + 'px';
          el.style.top = newTop + 'px';
        };

        const onMouseUp = () => {
          document.removeEventListener('mousemove', onMouseMove);
          document.removeEventListener('mouseup', onMouseUp);
        };

        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);
      });
    },
}

export const setupDirective = () => {
  //定义全局列表滚动加载方法
  Vue.directive('load-more', loadMore);
  Vue.directive('drag', drag);
  Vue.directive('drag-table', dragTable);
  Vue.directive('drag-dom', dragDom);
  //连续点击
  Vue.directive('click-five-times', clickFiveTimesDirective);
  Vue.directive('preventReClick', preventReClick);
  Vue.directive('sticky-table', stickyTable);
  Vue.directive('dragDialog', dragDialog)
}

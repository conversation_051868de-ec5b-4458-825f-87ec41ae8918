<!--
 * @Descripttion: 学生查看原卷预览
 * @Author: 小圆
 * @Date: 2024-01-13 09:26:00
 * @LastEditors: 小圆
-->
<template>
  <previewSlider
    ref="previewSlider"
    :class="{ desktop: isDesktop }"
    :data="stuScanData"
    :loading="loading"
    :no-text="'该场考试未上传试卷'"
    @slideChange="slideChange"
    @tabPrev="tabPrev"
    @tabNext="tabNext"
    @close="$emit('close')"
  >
    <template slot-scope="{ item, index }">
      <StuPaperImg
        :currentRow="item"
        :index="index"
        :defaultRotate="defaultRotate"
        :defaultScore="defaultScore"
        :no-point="true"
        :mark="isMark"
        v-loading="loading"
        @loadImage="loadImage"
      ></StuPaperImg>
    </template>

    <!-- 配置项与网阅 -->
    <div class="clearfix" slot="toolLeft" v-if="isCanMark && source == 4">
      <el-button
        type="text"
        class="swiper-imageTool swiper-imageTool-right swiper-imageTool-comment click-element pull-left"
        :loading="btnLoading"
        :class="{ primary: isMark }"
        @click="switchMark"
      >
        笔迹痕迹
      </el-button>
    </div>
  </previewSlider>
</template>

<script>
import previewSlider from '@/components/SwiperViewer/previewSlider.vue';
import ImgList from '@/components/scan/ImgList.vue';

import StuPaperImg from './StuPaperImg.vue';
import { getStudentPaperImage } from '@/service/xueban';
import { getPublicConfigBySchoolInfo } from '@/service/api';
import { supportsAvif } from '@/utils/index';
import { getStuQuesAnalyze, getStuScanDataAPI } from '@/service/pexam';
import DochoiceMixin from '@/pages/lookReport/mixin/DochoiceMixin.vue';

export default {
  mixins: [DochoiceMixin],
  components: {
    previewSlider,
    ImgList,
    StuPaperImg,
  },
  props: {
    // 查询参数
    queryParams: {
      type: Object,
      default: () => {
        return {
          workId: '',
          examId: '',
          classId: '',
          subjectId: '',
          studentNo: '',
          studentId: '',
          abPaper: '',
        };
      },
    },
    // 默认旋转角度
    defaultRotate: {
      type: Number,
      default: 90,
    },

    // 默认总分
    defaultScore: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      source: '', // 来源 3: 手阅 4: 网阅
      isCanMark: false, // 是否可以画笔留痕
      isMark: false, // 是否开启画笔痕迹
      hasMark: false, // 是否已存在画笔痕迹
      loading: false, // 是否正在加载
      btnLoading: false, // 按钮loading
      isDesktop: !!window.cef,
      stuScanData: [],
    };
  },

  async created() {
    this.getMarkConfig();
    let report = this.$sessionSave.get('reportParent');
    this.source = report?.source;
  },

  mounted() {
    this.getStuPaperIMG();
  },

  methods: {
    async getStuPaperIMG() {
      this.stuScanData = [];
      this.loading = true;
      try {
        const scanRes = await getStuScanDataAPI({
          examId: this.queryParams.workId,
          stuNo: this.queryParams.studentNo,
          stuId: this.queryParams.studentId,
        });
        if (scanRes.code != 1) return;
        const stuQuesAnalyzeRes = await getStuQuesAnalyze({
          examId: this.queryParams.examId,
          classId: this.queryParams.classId,
          subjectId: this.queryParams.subjectId,
          studentId: this.queryParams.studentId,
          abPaper: this.queryParams.abPaper,
        });
        if (stuQuesAnalyzeRes.code != 1) return;

        let scanData = scanRes.data;
        let stuQuesAnalyzeData = stuQuesAnalyzeRes.data;

        const quesMap = {};
        stuQuesAnalyzeData.forEach(item => {
          let no = item.sort;
          if (quesMap[no]) {
            if (Array.isArray(quesMap[no])) {
              quesMap[no].push(item);
            } else {
              quesMap[no] = [quesMap[no], item];
            }
          } else {
            quesMap[no] = item;
          }
        });

        await this.setQuesBlock(scanData);

        scanData.forEach(ques => {
          this.$set(ques, 'isEdit', false);
          ques.questions.forEach(item => {
            this.$set(item, 'showScore', false);
            this.$set(item, 'tempScore', item.score);
            // 跳过选做题
            if (item.type == 18) return;

            if (Reflect.has(quesMap, String(item.question_no))) {
              const analyzeQues = quesMap[String(item.question_no)];
              if (Array.isArray(analyzeQues)) {
                const sumScore = analyzeQues.map(item => item.score).join(',');
                this.$set(item, 'showScore', sumScore !== null);
                this.$set(item, 'tempScore', sumScore || 0);
                this.$set(item, 'tempTitle', analyzeQues.map(item => item.quesNo).join(','));
                this.$set(
                  item,
                  'tempScoreList',
                  analyzeQues.map(t => {
                    return {
                      score: t.score,
                      title: t.quesNo,
                    };
                  })
                );
              } else {
                this.$set(
                  item,
                  'showScore',
                  analyzeQues.fullScore !== 0 && analyzeQues.score !== null && analyzeQues.chooseType !== '0'
                );
                this.$set(item, 'tempScore', analyzeQues.score || 0);
                this.$set(item, 'tempTitle', analyzeQues.quesNo);
                this.$set(item, 'tempScoreList', [{ score: analyzeQues.score, title: analyzeQues.quesNo }]);
              }

              delete quesMap[String(item.question_no)];
            }
          });
        });
        this.stuScanData = scanData;
      } catch (error) {
        console.error(error);
      } finally {
        this.loading = false;
      }
    },

    // 设置题块
    async setQuesBlock(stuScanData) {
      let stuDoQuesList = await this.getStuElectiveQues({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        examId: this.queryParams.examId,
        subjectId: this.queryParams.subjectId,
        stuId: this.queryParams.studentId,
      });

      let chooseQuesList = stuDoQuesList.filter(item => {
        return item.chooseType == 1;
      });

      for (const item of stuScanData) {
        for (const ques of item.questions) {
          for (const chooseQues of chooseQuesList) {
            // 对应题块位置更新所选题目
            if (chooseQues.targetId == ques.question_id) {
              ques.question_no = chooseQues.quesNo;
            }
          }
        }
      }
    },

    // 获取是否开启画笔留痕（中控配置项）
    async getMarkConfig() {
      let code = '119';
      let flag = 0;
      try {
        const { data } = await getPublicConfigBySchoolInfo({
          schoolId: this.$sessionSave.get('schoolInfo').id || this.$sessionSave.get('loginInfo').schoolid,
          dictCode: code,
          userId: this.$sessionSave.get('loginInfo').id,
        });
        flag = Number(data[0].state) || 0;
      } catch (error) {
        console.error(error);
        flag = 0;
      }
      this.isCanMark = !!flag;
    },

    // 笔迹痕迹
    async switchMark() {
      if (!this.hasMark) {
        await this.getMarkImage();
      }
      if (this.hasMark) this.isMark = !this.isMark;
    },

    // 获取批改痕迹图片
    async getMarkImage() {
      this.btnLoading = true;
      this.loading = true;
      try {
        const res = await getStudentPaperImage({
          examId: this.queryParams.workId,
          studentNo: this.queryParams.studentNo,
          showScore: 0,
        });
        if (res.data && res.data.length) {
          let isSupportsAvif = await supportsAvif();
          res.data.forEach((item, i) => {
            this.stuScanData[i].markImage = isSupportsAvif ? item + '.avif' : item;
          });
          this.hasMark = true;
        }
      } catch (error) {
        console.error(error);
      } finally {
        this.btnLoading = false;
        this.loading = false;
      }
    },

    loadImage(img, index) {
      this.$refs.previewSlider.previewTo('fitWindow', index);
    },

    // 索引更新
    slideChange(index) {
      console.log('当前索引', index);
    },

    // 按钮切换上一个
    tabPrev(activeIndex) {},

    // 按钮切换下一个
    tabNext(activeIndex) {},

    // 关闭预览
    closePreview(type) {
      this.$emit('close', type);
    },
  },
};
</script>

<style lang="scss" scoped>
.previewSlider.desktop {
  bottom: 20px !important;
}

::v-deep .gallery-top {
  height: 100% !important;
}

::v-deep .swiper-imageTool-box {
  bottom: 5% !important;
}
</style>

<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2025-08-29 09:51:43
 * @LastEditors: 小圆
-->
<template>
  <div>
    <div class="recent-exam-header">
      <div class="recent-exam-header--left">
        <span class="label">最近考试</span>
        <exam-list-filter
          ref="filter"
          class="list-filter"
          v-model="examQuery"
          :filter-config="filterConfig"
          @change="getCommentExamList"
        >
        </exam-list-filter>

        <!-- 类别 -->
        <div class="header__select">
          <span class="select__label">类别：</span>
          <el-select
            v-model="categoryId"
            :multiple="true"
            collapse-tags
            style="width: 155px"
            class="source-select"
            placeholder="全部类别"
            @change="changeCategory"
          >
            <div style="padding: 5px">
              <el-button @click="checkAllCategory(true)">全选</el-button>
              <el-button @click="checkAllCategory(false)">全不选</el-button>
            </div>
            <el-option v-for="item in categoryList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </div>
      </div>

      <div class="recent-exam-header--right">
        <span class="err-handle" @click="goHandleError">
          <span class="err-handle-text">待处理异常</span>
          <span class="err-handle-tag">{{ errExamNums }}</span>
        </span>

        <el-button class="more-btn" type="text" @click="goReportList">查看更多 ></el-button>
      </div>
    </div>
    <div class="recent-exam-list">
      <ul class="examReport__list list-none" v-if="examReportList.length">
        <div class="examReport__item" v-for="item in examReportList" :key="item.examId">
          <div class="exam-item">
            <div class="exam-item--left">
              <div class="exam-name">
                <div class="exam-icon"></div>

                <div class="exam-title">
                  {{ item.examName }}
                </div>
                <div class="exam-tag">
                  <el-tag size="small" class="tag-item" type="info">{{ item.categoryName }}</el-tag>
                  <el-tag size="small" class="tag-item" v-if="item.analysisMode == 1" type="warning">新高考</el-tag>
                </div>
              </div>
              <div class="exam-detail">
                <span class="exam-label exam-grade"
                  >年级：<span class="exam-value">{{ item.gradeName }}</span>
                </span>
                <span class="exam-value exam-time"
                  >考试时间：
                  <span>
                    {{ item.examDateTime }}
                  </span>
                </span>
              </div>
              <div class="exam-detail">
                <span class="exam-label exam-subject"
                  >学科：
                  <span class="exam-value">
                    {{ item.subjectName }}
                  </span>
                </span>
              </div>
              <div class="exam-detail">
                <span class="exam-label exam-class"
                  >班级：
                  <span class="exam-value">
                    {{ item.classNames }}
                  </span>
                </span>
              </div>
            </div>
            <div class="exam-item--right">
              <el-button v-if="item.published == 1" type="primary" @click.stop="lookReport(item)"> 查看报告 </el-button>
              <div class="no-publish" v-else>未发布</div>
            </div>
          </div>
        </div>
      </ul>
      <no-data v-else></no-data>
    </div>
    <look-report-dialog
      v-if="isShowSelReportDialog"
      :reportInfo="currentReportInfo"
      @closeDialog="isShowSelReportDialog = false"
      @lookReport1="sureLookReport"
    ></look-report-dialog>
  </div>
</template>

<script lang="ts">
import { getChildReportList, getCommentExamList, getErrorExamNums } from '@/service/pexam';
import UserRole from '@/utils/UserRole';
import { Component, Prop, Vue } from 'vue-property-decorator';
import ExamListFilter, { ExamQuery } from '@/pages/reportCenter/components/exam-list-filter.vue';
import lookReport from '@/components/lookReport.vue';
import NoData from '@/components/noData.vue';
import { openRoute } from '../desktopUtils';

@Component({
  components: {
    ExamListFilter,
    LookReportDialog: lookReport,
    NoData,
  },
})
export default class OfflineExamList extends Vue {
  @Prop({
    default: () => {
      return [];
    },
  })
  defaultCategory;

  // 筛选器配置 - 只显示年级和学科筛选
  filterConfig = {
    showSchool: false,
    showYearType: false,
    showGrade: true,
    showSubject: true,
    showClass: false,
    showCategory: false,
    showKeyword: false,
  };

  categoryList = [];
  categoryId = [12, 1];

  errExamNums = 0;

  // 请求参数
  examQuery: ExamQuery = {
    /** 学年 */
    acadYearsId: '',
    /** 入学年份 */
    year: '',
    /** 年级id */
    gradeId: '',
    /** 学科id */
    subjectId: '',
    /** 分类id */
    categoryId: '',
    /** 关键字 */
    keyWord: '',
    /** 当前所选班级id */
    classId: '',
    /** 行政班ids */
    clzId: '',
    /** 分层班ids */
    layeredClassIds: '',
  };
  // 考试列表加载状态
  isLoading = false;
  // 考试列表
  examReportList = [];
  // 分页器
  pagination = {
    page: 1,
    pageSize: 10,
    total: 0,
  };
  // 当前考试报告
  currentReportInfo = null;
  // 是否显示选择报告对话框
  isShowSelReportDialog: boolean = false;

  async mounted() {
    this.categoryId = [...this.defaultCategory];
    this.getErrorExamNums();
    await this.getCategoryList();
  }

  // 获取分类列表
  async getCategoryList() {
    this.categoryList = await UserRole.getAllType();
  }

  /**
   * 获取未处理异常考试数量
   */
  async getErrorExamNums() {
    let params: any = {
      schoolId: this.$sessionSave.get('schoolInfo').id,
      type: 1, //1-异常考试,0-无异常
      page: 1,
      pageSize: 1,
    };
    //非校领导和运营人员只能查看自己任教班级的异常数据
    if (!UserRole.isSchoolLeader && !UserRole.isOperation) {
      let classList = await UserRole.getClassList({ classType: -1 });
      params.classIdList = classList.map(it => it.classId).join(',');
      let subjectIds = [
        ...new Set(
          UserRole.substituteClassList.map(item => {
            return String(item.subjectId);
          })
        ),
      ];
      params.subjectIdList = subjectIds.join(',');
    }
    getErrorExamNums(params)
      .then(res => {
        this.errExamNums = res.data.total;
      })
      .catch(err => {
        this.errExamNums = 0;
      });
  }

  // 获取考试列表
  async getCommentExamList() {
    this.isLoading = true;

    // 获取角色
    let roles = this.getRole(this.examQuery.year);

    try {
      let res = await getCommentExamList({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        acadYearsId: this.examQuery.acadYearsId,
        gradeId: this.examQuery.gradeId,
        subjectId: this.examQuery.subjectId,
        categoryId: this.categoryId.join(',') || '',
        role: roles,
        clzId: this.examQuery.clzId, // 行政班
        layeredClassIds: this.examQuery.layeredClassIds, // 分层班
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
        source: 1, // 来源 0: 试卷讲评 1: 报告
        shieldWork: 1, // 是否屏蔽作业数据 0:否 1:是
      });

      let resData = res.data;
      this.examReportList = resData.list;
      this.pagination.total = resData.total;
    } catch (error) {
      console.log(error);
      this.examReportList = [];
    }
    this.isLoading = false;
  }

  // 点击进入报告，判断是否需要选择报告
  async lookReport(item) {
    if (!item.published) {
      return this.$message.warning('该考试未发布');
    }
    this.$sessionSave.set('reportParent', item);
    this.$sessionSave.set('currentRoles', null);

    const { examList, hasCampus } = await getChildReportList({
      examId: item.examId,
      examName: item.examName,
    });

    if (hasCampus && !UserRole.isOperation && !UserRole.isSchoolLeader) {
      if (examList.length == 0) {
        this.$message.warning('暂无所在校区的考试报告');
        return;
      }
      if (examList.length == 1) {
        this.sureLookReport(examList[0]);
        return;
      }
    }
    if (examList.length) {
      this.currentReportInfo = item;
      this.isShowSelReportDialog = true;
    } else {
      this.sureLookReport(item);
    }
  }

  // 进入报告
  sureLookReport(item) {
    this.isShowSelReportDialog = false;
    this.$sessionSave.set('lookReportFrom', this.$route.path);
    this.$sessionSave.set('innerClassList', null);
    this.$sessionSave.set('innerSubjectList', null);
    this.$sessionSave.set('loadComment', true);
    this.$sessionSave.set('reportDetail', item);
    this.$sessionSave.remove('subjectId');
    this.$sessionSave.set('downLoadState', item.statState);
    this.$sessionSave.remove('contrastObj');
    this.$router.push({ path: '/home/<USER>' });
  }

  // 获取角色
  getRole(year) {
    let roleSubjectMap = UserRole.utils.getRoleSubjectListMapByYear(year);
    let roles: string;
    if (UserRole.isOperation) {
      roles = ''; // 运营获取所有权限
    } else {
      let roleSub = {}; // 角色的学科
      for (const key in roleSubjectMap) {
        if (Object.prototype.hasOwnProperty.call(roleSubjectMap, key)) {
          let item = roleSubjectMap[key];
          roleSub[key] = item
            .map(item => item.subjectId)
            .filter(t => t)
            .join(',');
        }
      }
      roles = JSON.stringify(roleSub);
    }
    return roles;
  }

  changeCategory(val) {
    this.categoryId = val;
    this.getCommentExamList();
  }

  checkAllCategory(flag) {
    if (flag) {
      this.categoryId = this.categoryList.map(item => item.id);
    } else {
      this.categoryId = [];
    }
    this.getCommentExamList();
  }

  goReportList() {
    openRoute('/home/<USER>');
  }

  goHandleError() {
    openRoute('/home/<USER>');
  }
}
</script>

<style scoped lang="scss">
.recent-exam-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .recent-exam-header--left {
    display: flex;
    align-items: center;

    .list-filter {
      margin-left: 20px;
      font-size: 14px;
    }
  }
}

::v-deep {
  .header__select {
    font-size: 14px;
    margin-bottom: 0;
  }
}

.err-handle {
  font-weight: 400;
  font-size: 14px;
  color: #545454;
  cursor: pointer;

  &:hover {
    color: #2574ff;
  }

  .err-handle-tag {
    display: inline-block;
    margin-left: 4px;
    padding: 0 10px;
    height: 22px;
    line-height: 22px;
    text-align: center;
    color: #fff;
    background: #fa7878;
    border-radius: 11px 11px 11px 11px;
  }
}

.more-btn {
  margin-left: 20px;
  font-weight: 400;
  font-size: 14px;
  color: #2574ff;

  &:hover {
    color: rgba(#2574ff, 0.8);
  }
}

.examReport__list {
  position: relative;
  width: 100%;
  overflow-y: auto;

  .examReport__item {
    position: relative;

    width: 100%;
    min-height: 120px;
    background: #fff;
    padding: 20px;
    margin-bottom: 14px;
    color: #222;
    font-size: 14px;

    border-bottom: 1px solid #f0f3fa;

    .exam-item {
      display: flex;
      flex-direction: row;
      align-items: center;

      .exam-name {
        display: flex;
        flex-direction: row;
        align-items: center;

        font-weight: 700;
        font-size: 18px;
        color: #303233;
        line-height: 1.5;

        margin-right: 100px;
        width: 90%;
        justify-content: flex-start;

        .exam-icon {
          flex: none;
          margin-right: 8px;
          width: 20px;
          height: 20px;
          background: url('~@/assets/desktop/exam_icon.png') no-repeat;
          background-size: 100% 100%;
        }

        .exam-title {
          margin-right: 15px;
        }

        .exam-tag {
          .tag-item {
            width: 54px;
            height: 26px;
            border-radius: 4px;
            border: 1px solid #9ad2cb;
            background-color: #fff;

            font-weight: 400;
            font-size: 14px;
            color: #67b2a8;
            line-height: 26px;
            text-align: center;
          }
        }
      }

      .exam-detail {
        margin-top: 8px;
        line-height: 1.5;

        .exam-grade {
          margin-right: 20px;
        }

        .exam-label {
          color: #bbbbbb;
          font-size: 14px;
        }

        .exam-value {
          color: #4e5668;
          font-size: 14px;
        }

        > span {
          display: inline-block;
          color: #3f4a54;
        }
      }
    }

    .exam-item--left {
      flex: 1;
    }

    .exam-item--right {
      min-width: 150px;
      padding-left: 20px;
      text-align: right;

      ::v-deep .el-button {
        width: 100px;
        height: 34px;
        padding: 0;

        font-size: 15px;
        line-height: 34px;

        border: 0;
        border-radius: 17px;
        background: #2574ff;

        &:hover {
          background: rgba(#2574ff, 0.8);
        }
      }
    }

    .no-publish {
      color: #d9001b;
      font-size: 18px;
      font-weight: 700;
    }
  }
}

.label {
  display: flex;
  align-items: center;
  font-weight: 700;
  font-size: 18px;
  color: #000000;

  &::before {
    display: inline-block;
    content: '';
    width: 4px;
    height: 18px;
    background: #1e6ffe;
    border-radius: 2px;
    margin-right: 8px;
  }
}
</style>

<!--
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2024-08-26 16:36:00
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-06-09 16:42:26
-->
<template>
  <div class="english-wrapper">
    <div class="english-left">
      <div class="english-book-header display_flex align-items_center justify-content_space-between">
        <!-- <span class="english-book-title">人教版</span> -->
         <el-select v-if="phaseList.length > 1" class="select-phase" v-model="phase" @change="getBookList">
          <el-option v-for="item in phaseList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
         </el-select>
        <el-select class="select-book" filterable placeholder="请输入关键词" :remote-method="getBookList" v-model="currentBook" value-key="book_id" @change="bookChange">
          <el-option v-for="item in bookList" :key="item.book_id" :label="item.book_name" :value="item"> </el-option>
        </el-select>
      </div>
      <div class="english-book-menu">
        <el-tree
          ref="tree"
          :data="treeData"
          :default-expand-all="true"
          :props="defaultProps"
          node-key="id"
          @node-click="handleNodeClick"
          :highlight-current="true"
        >
          <template #default="{ node, data }">
            <span :title="data.name" class="el-tree-node__label">{{ data.name }}</span>
          </template>  
        </el-tree>
      </div>
    </div>
    <div class="english-right">
        <el-tabs v-model="activeName">
          <el-tab-pane label="中译英" name="en"
            ><span slot="label"
              >中译英<span style="margin-left: 5px">（{{ WordStore.choseEnList.length }}）</span></span
            >
            <div class="english-ques-content">
              <div
                class="english-ques-item"
                :class="{ active: WordStore.choseEnList.find(word => word.id == item.id), selected: WordStore.choseZhList.find(word => word.id == item.id) }"
                v-for="(item, index) in wordList"
                :key="index"
                @click="choseWord(item)"
              >
                <div>{{ item.word }}</div>
                <div class="desc">{{ item.translation }}</div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane v-if="false" label="英译中" name="zh"
            ><span slot="label"
              >英译中<span style="margin-left: 5px">（{{ WordStore.choseZhList.length }}）</span></span
            >
            <div class="english-ques-content">
              <div
                class="english-ques-item"
                :class="{ active: WordStore.choseZhList.find(word => word.id == item.id), selected: WordStore.choseEnList.find(word => word.id == item.id) }"
                v-for="(item, index) in wordList"
                :key="index"
                @click="choseWord(item)"
              >
                <div>{{ item.word }}</div>
                <div class="desc">{{ item.translation }}</div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
        <div class="english-handle">
          <el-checkbox
            v-model="isAll"
            size="small"
            @change="choseAll"
            border
            style="background: #fff; margin-right: 10px"
            >全选</el-checkbox
          >
        </div>
    </div>
  </div>
</template>

<script>
import { getBookListAPI, getBookCatalogAPI, getWordListAPI } from '@/service/resource';
import { saveTestBanks } from '@/service/api';
import { localSave, sessionSave } from '@/utils/index.js';
import { guid } from '@/utils/index';
import { getToken } from '@/service/auth';
import { mapState } from 'vuex';
import WordStore from './WordStore';

export default {
  data() {
    return {
      userId: this.$sessionSave.get('loginInfo').id,
      phaseList: [],
      bookList: [],
      phase: '',
      currentBook: null,
      treeData: [],
      currentNode: null,
      defaultProps: {
        children: 'child',
        label: 'name',
      },
      wordList: [],
      wordInfoList: [],
      choseZhList: [],
      choseEnList: [],
      isAll: false,
      activeName: 'en',

      WordStore: WordStore,
    };
  },
  watch: {
    currentBook(val) {
      this.getBookCatalog(val);
    },
    async currentNode(val) {
      await this.getWordList(val);
      let curNodeList = this.choseList.filter(item => {
        return this.wordList.find(word => {
          return word.id == item.id;
        });
      });
      this.isAll = this.wordList.length == curNodeList.length;
    },
    choseList(val) {
      let curNodeList = this.choseList.filter(item => {
        return this.wordList.find(word => {
          return word.id == item.id;
        });
      });
      this.isAll = this.wordList.length == curNodeList.length;
    },
  },
  computed: {
    ...mapState(['loginInfo']),
    choseList: {
      get() {
        return this.activeName == 'zh' ? this.WordStore.choseZhList : this.WordStore.choseEnList;
      },
      set(val) {
        this.activeName == 'zh' ? (this.WordStore.choseZhList = val) : (this.WordStore.choseEnList = val);
      },
    },
  },
  async mounted() {
    this.getPhaseList();
    await this.getBookList();
  },
  methods: {
    getPhaseList() {
      this.phase = localSave.get('en_word_phase' + this.userId) || this.loginInfo.phase;
      this.phaseList = this.loginInfo.multiple_phase.split(',').map(item => {
        return {
          label: item == "1" ? "小学" : item == "2" ? "初中" : "高中",
          value: Number(item),
        };
      });
    },
    async getBookList(title="") {
      let res = await getBookListAPI({
        schoolId: sessionSave.get('schoolInfo').id,
        subjectId: '',
        phase: this.phase,
        enable: 1,
        title: title,
      });
      let list = [];
      res.data.book_list.forEach(publish => {
        publish.books.forEach(book => {
          list.push(book);
        });
      });
      this.bookList = list;
      if(!this.currentBook){
        this.currentBook = localSave.get('en_word_book' + this.userId) || this.bookList[0];
      }else{
        this.currentBook = this.bookList[0];
      }
    },
    async getBookCatalog(item) {
      let res = await getBookCatalogAPI({
        bookId: item.book_id,
        all: 0,
        schoolId: sessionSave.get('schoolInfo').id,
      });
      this.treeData = res.data;
      this.currentNode = localSave.get('en_word_catalog' + this.userId) || res.data[0];
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(this.currentNode.id);
      });
    },
    async getWordList(item) {
      let res = await getWordListAPI({
        bookId: this.currentBook.book_id,
        catalogId: item.id,
        schoolId: sessionSave.get('schoolInfo').id,
      });
      this.wordList = res.data;
    },
    async bookChange() {
      // await this.getBookCatalog(this.currentBook);
      localSave.set('en_word_book' + this.userId, this.currentBook);
      localSave.set('en_word_catalog' + this.userId, null);
      localSave.set('en_word_phase' + this.userId, this.phase);
      this.WordStore.clearEnList();
      this.WordStore.clearZhList();
      this.wordInfoList = [];
      this.wordList = [];
    },
    choseAll() {
      if (!this.isAll) {
        this.choseList = this.choseList.filter(item => {
          return !this.wordList.find(word => {
            return word.id == item.id;
          });
        });
      } else {
        const words = this.wordList.map(item => {
          return {
            id: item.id,
            word: item.word,
            translation: item.translation,
          };
        });

        this.choseList = [...words, ...this.choseList]; 
        this.choseList = this.choseList.filter((item, index, arr) => {
          return arr.findIndex(word => word.id === item.id) === index;
        });
        // this.choseList = [...new Set([...allIds, ...this.choseList])];
        //数组对象暂不去重，最终能从数组中找到对应单词即可
        this.wordInfoList = [...this.wordList, ...this.wordInfoList];
      }
    },
    handleNodeClick(data, node) {
      if (!node.isLeaf && this.currentNode.id !== node.data.id) {
        node.expanded = true;
      }
      this.currentNode = data;
      localSave.set('en_word_catalog' + this.userId, data);
    },
    choseWord(word) {
      let hasWord = this.choseList.find(item => {
        return item.id == word.id;
      });
      if (hasWord) {
        this.choseList = this.choseList.filter(item => {
          return item.id != word.id;
        });
        // this.wordInfoList = this.wordInfoList.filter(item => {
          // return item.id != word.id;
        // });
      } else {
        this.choseList.push({
          id: word.id,
          word: word.word,
          translation: word.translation,
        });
        this.wordInfoList.push(word);
      }
    },
    /**
     * @name: 获取一个空的题目对象
     * @return: 空的大题对象
     */
    getEmptyQuesObj(name) {
      return {
        id: guid(),
        name: name,
        count: 0,
        score: 0,
        typeId: 7,
        type: '填空智批题',
        data: new Array(),
      };
    },
    /**
     * @name: 获取一个空的小题对象
     * @return: 空的小题对象
     */
    getEmptyChildQuesObj(item, index, isEn) {
      return {
        id: guid(),
        name: '填空智批题',
        wordId: item.id,
        word: isEn ? item.translation : item.word,
        optionCount: 0,
        quesNos: index,
        quesNo: index,
        answer: isEn ? item.word : item.translation,
        score: 1,
        typeId: 7,
        type: '填空智批题',
      };
    },
    buildQuesInfo() {
      let bigQuesList = [];
      let quesNo = 1;
      if (this.choseEnList.length) {
        let bigQues = this.getEmptyQuesObj('中译英');
        this.choseEnList.forEach(item => {
          let word = this.wordInfoList.find(word => {
            return word.id == item;
          });
          bigQues.data.push(this.getEmptyChildQuesObj(word, quesNo++, true));
        });
        bigQues.score = bigQues.data.length;
        bigQues.count = bigQues.data.length;
        bigQuesList.push(bigQues);
      }
      if (this.choseZhList.length) {
        let bigQues = this.getEmptyQuesObj('英译中');
        this.choseZhList.forEach(item => {
          let word = this.wordInfoList.find(word => {
            return word.id == item;
          });
          bigQues.data.push(this.getEmptyChildQuesObj(word, quesNo++, false));
        });
        bigQues.score = bigQues.data.length;
        bigQues.count = bigQues.data.length;
        bigQuesList.push(bigQues);
      }
      return bigQuesList;
    },
    /**
     * @name：根据名称匹配学科
     * @param {*} name 学科名称
     * @return {*} 学科id
     */
    matchSubjectByName(name = this.currentBook.clazz_level_name) {
      if (['一年级', '二年级', '三年级', '四年级', '五年级', '六年级'].includes(name)) {
        return 25;
      } else if (['高一', '高二', '高三', '高中'].includes(name)) {
        return 12;
      } else {
        return 3;
      }
    },
    /**
     * @name:确定新建答题卡
     */
    async confirmAddCard(quesinfo) {
      let loginInfo = this.$sessionSave.get('loginInfo');
      let params = {
        tbName: '英语专项练习卡',
        schoolId: this.$sessionSave.get('schoolInfo').id,
        userId: loginInfo.id,
        userName: loginInfo.realname,
        cardType: 4,
        quesInfo: JSON.stringify(quesinfo),
      };
      let result = await saveTestBanks(params).catch(function (error) {});
      if (result && parseInt(result.code) == 1) {
        if (result.data && result.data.tbInfo && result.data.tbInfo.tbId) {
          let testBankId = result.data.tbInfo.tbId;
          let subjectId = this.matchSubjectByName(this.currentBook.clazz_level_name);
          let token = getToken();
          let url =
            process.env.VUE_APP_CARDURL +
            `?id=${testBankId}&examName=英语专项练习卡&cardType=4&correctType=1&pageLayout=1&subjectId=${subjectId}&token=${token}`;
          window.open(url, '_blank');
        }
      } else {
        this.$message.error(result.msg);
      }
    },
    gotoCard() {
      if (!this.choseEnList.length && !this.choseZhList.length) {
        return;
      }
      let quesInfo = this.buildQuesInfo();
      this.confirmAddCard(quesInfo);
    },
  },
};
</script>

<style lang="scss" scoped>
.english-wrapper {
  position: relative;
  height: 100%;
  display: flex;
  background: #fff;
  padding-top: 0px !important;

  .english-left {
    position: sticky;
    top: 0;
    height: 100%;
    width: 300px;
    border-radius: 3px;
    overflow: auto;

    .english-book-header {
      width: 100%;
      height: 52px;
      line-height: 52px;
      background: #F5F6FA;
      font-size: 16px;
      font-weight: 400;
      color: #666;
      padding: 0 15px 0 13px;

      .english-book-title {
        position: relative;
        padding-left: 10px;

        &:before {
          content: '';
          position: absolute;
          width: 4px;
          height: 16px;
          background: #008dea;
          border-radius: 2px;
          left: 0;
          top: 17px;
        }
      }
      .select-phase{
        width: 100px;
      }
      .select-book{
        width: 100%;
      }

      .select-book,.select-phase {

        ::v-deep {
          .el-input__inner {
            padding-left: 10px;
            height: 34px;
            font-size: 16px;
            color: #7F8795;
          }
        }
      }
    }

    .english-book-menu {
      height: calc(100% - 52px);
      overflow: auto;
    }
  }

  .english-right {
    position: relative;
    flex: 1;
    height: 100%;
    margin: 0 0 0 10px;
    border-radius: 3px;
    overflow: auto;

    .english-handle {
      position: absolute;
      right: 0px;
      top: 10px;

      ::v-deep {
        .el-checkbox {
          border-radius: 6px;
          margin-right: 0 !important;
          
          .el-checkbox__label {
            font-size: 16px;
          }
        }
      }
    }

    .english-ques-content {
      border: 1px solid #f6f6f6;
      .english-ques-item {
        line-height: 18px;
        display: inline-block;
        padding: 10px 20px;
        border: 1px solid #dbdbdb;
        border-radius: 10px;
        margin: 10px;
        text-align: center;
        cursor: pointer;

        &.selected {
          background: rgba(61,154,255,0.1);
          border: 1px solid #3D9AFF;
          color: #3D9AFF;
        }

        &.active {
          background: rgba(61,154,255,0.1);
          border: 1px solid #3D9AFF;
          color: #3D9AFF;
        }

        .desc {
          font-size: 12px;
        }
      }
    }
  }
}

::v-deep {
  .el-tabs__header {
    padding-left: 10px;
    margin: 0;
  }

  .el-tabs__nav-wrap {
    .el-tabs__item {
      height: 52px;
      line-height: 52px;
      font-size: 18px;
    }

    &::after {
      background: none;
    }
  }

  
  .el-tree-node__content {
    height: 36px;
  }

  .el-tree-node__label {
    font-size: 16px;
    color: #171717;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .el-tree-node__children {
    .el-tree-node__content {
      height: 30px;
    }

     .el-tree-node__label {
      font-size: 14px;
      color: #444444;
    }
  }


  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background: rgba(#3d9aff, 0.2);
    border-radius: 18px;
    .el-tree-node__label {
      color: #3d9aff;
    }

    .el-tree-node__expand-icon {
      color: #3d9aff;

      &.is-leaf {
        color: transparent;
      }
    }
  }
}
</style>

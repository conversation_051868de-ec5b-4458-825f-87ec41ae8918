<!--
 * @Description: 学生报告
 * @Author: 小圆
 * @Date: 2025-02-11 10:43:05
 * @LastEditors: 小圆
-->
<template>
  <div>
    <div class="tip"><span class="star">*</span> 提示：以下设置仅影响学生/家长查看报告</div>

    <div class="setting-box">
      <div class="setting-content">
        <div class="setting-header">
          <div class="title">学生成绩显示</div>
        </div>
        <div v-if="scoreSettingList.length">
          <div v-for="item in scoreSettingList" :key="item.code" class="type-item">
            <div class="type-item-name">{{ item.name }}：</div>
            <el-radio-group v-model="item.state" size="small" class="type-item-radio" @change="saveConfig(item)">
              <el-radio v-for="state in item.stateList" :key="state.state" :label="state.state" border>
                {{ state.label }}
              </el-radio>
            </el-radio-group>
          </div>
        </div>
        <el-skeleton :rows="13" animated v-else />
      </div>

      <div class="setting-content">
        <div class="setting-header">
          <div class="title">更多设置</div>
        </div>
        <div v-if="moreSettingList.length">
          <div v-for="item in moreSettingList" :key="item.code" class="type-item">
            <div class="type-item-name more-name">{{ item.name }}：</div>
            <el-radio-group v-model="item.state" size="small" class="type-item-radio" @change="saveConfig(item)">
              <el-radio v-for="state in item.stateList" :key="state.state" :label="state.state" border>
                {{ state.label }}
              </el-radio>
            </el-radio-group>
          </div>
        </div>
        <el-skeleton :rows="2" animated v-else />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import UserRole from '@/utils/UserRole';
import { Component, Mixins, Vue } from 'vue-property-decorator';
import SchoolSettingMixin from '../SchoolSetting.mixin';
import { addOrUpdatePublicConfigSchool, getPublicConfigBySchoolInfo } from '@/service/api';

interface TypeItem {
  // 类型id
  id?: number;
  // 类型名称
  name: string;
  /** 中控字典code */
  code: string;
  /** 中控上的标题 */
  title: string;
  /** 中控值 */
  state: string;
  /** 中控值列表 */
  stateList: {
    state: string;
    label: string;
  }[];
}

const ExamTitleMap = {
  1: '作业报告-练习', // 练习
  2: '作业报告-周测', // 周测
  3: '作业报告-月考', // 月考
  4: '作业报告-期中', // 期中
  5: '作业报告-期末', // 期末
  6: '作业报告-章节测试', // 章节测试
  7: '作业报告-阶段复习', // 阶段复习
  8: '作业报告-模拟', // 模拟
  9: '作业报告-联考', // 联考
  10: '作业报告-普通', // 普通
  11: '作业报告-竞赛', // 竞赛
  12: '作业报告-导学案', // 导学案
};

const homeWorkRerpotItem: TypeItem = {
  name: '在线作业',
  code: '010',
  title: '作业报告',
  state: '1',
  stateList: [
    {
      state: '1',
      label: '按分数',
    },
    {
      state: '2',
      label: '按等级',
    },
  ],
};

const stuAvgItem: TypeItem = {
  name: '班级、年级均分显示',
  code: '112',
  state: '1',
  title: '学生端学情报告——班级、年级均分显示',
  stateList: [
    {
      state: '1',
      label: '开启',
    },
    {
      state: '0',
      label: '关闭',
    },
  ],
};

const stuGradeLineItem: TypeItem = {
  name: '年级成绩趋势变化',
  code: '142',
  state: '1',
  title: '学校设置-年级趋势变化',
  stateList: [
    {
      state: '1',
      label: '开启',
    },
    {
      state: '0',
      label: '关闭',
    },
  ],
};

@Component({
  components: {},
})
export default class StudentReport extends Mixins(SchoolSettingMixin) {
  // 类型列表
  private scoreSettingList: TypeItem[] = [];
  // 更多设置列表
  private moreSettingList: TypeItem[] = [];

  mounted() {
    this.getConfig();
  }

  // 获取学生成绩显示类型列表
  async getScoreSettingList() {
    let typeList: TypeItem[] = (await UserRole.getAllType()) as TypeItem[];
    typeList = typeList.map(item => {
      let title = ExamTitleMap[item.id];
      return {
        ...item,
        title,
        stateList: [
          {
            state: '1',
            label: '按分数',
          },
          {
            state: '2',
            label: '按等级',
          },
        ],
      };
    });
    typeList.push(homeWorkRerpotItem);
    return typeList;
  }

  // 获取更多设置类型列表
  async getMoreSettingList() {
    let moreSettingList = [stuAvgItem, stuGradeLineItem];
    return moreSettingList;
  }

  // 获取配置
  async getConfig() {
    let scoreSettingList = await this.getScoreSettingList();
    let moreSettingList = await this.getMoreSettingList();
    let allSettingList = [...scoreSettingList, ...moreSettingList];

    const res = await getPublicConfigBySchoolInfo({
      schoolId: this.$sessionSave.get('schoolInfo').id,
      dictCode: allSettingList.map(item => item.code).join(','),
    });
    res.data.forEach(item => {
      const typeItem = allSettingList.find(type => type.code === item.dictCode);
      if (typeItem) {
        typeItem.state = item.state;
      }
    });

    this.scoreSettingList = scoreSettingList;
    this.moreSettingList = moreSettingList;
  }

  // 保存配置
  async saveConfig(item: TypeItem) {
    try {
      const res = await addOrUpdatePublicConfigSchool({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        dictCode: item.code,
        title: item.title,
        state: item.state,
        expireType: -1,
        optUserId: this.$sessionSave.get('loginInfo').id,
        optRealName: this.$sessionSave.get('loginInfo').realname,
      });
      if (res.code == 1) {
        this.$notify.success({
          title: '成功',
          message: '保存成功',
        });
      } else {
        this.$notify.error({
          title: '失败',
          message: '保存失败',
        });
      }
    } catch (error) {
      this.$notify.error({
        title: '失败',
        message: '保存失败',
      });
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../page-style.scss';

.tip {
  font-size: 14px;
  color: #909399;
  margin-bottom: 24px;

  .star {
    color: #f56c6c;
  }
}

.setting-box {
  display: flex;
}

.setting-content {
  flex: 1;
}

.type-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .type-item-name {
    padding-left: 20px;
    min-width: 7em;
  }

  .more-name {
    min-width: 12em;
  }
}
</style>

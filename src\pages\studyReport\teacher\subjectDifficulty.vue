<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-04-02 09:03:13
 * @LastEditors: 小圆
-->
<template>
  <div>
    <div v-loading="tableLoading">
      <base-table
        v-if="tableData.length"
        :data="tableData"
        :column="tableColumns"
        v-bind="getTableAttr()"
        v-drag-table
      ></base-table>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>

    <!-- 学科难度 -->
    <div style="margin-top: 20px; width: 100%; height: 450px" id="SubjectDifficultyChart"></div>
  </div>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator';
import TableCommon from '../components/TableCommon';
import {
  getDefaultGrid,
  getDefaultLegend,
  getDefaultTitle,
  getDefaultToolBox,
} from '../plugins/DefaultEchartsOption';

@Component({
  components: {},
})
export default class subjectDifficulty extends Mixins(TableCommon) {
  // 学科难度图表
  subjectDifficultyChart: EChartsType = null;

  callbackGetTableData() {
    this.renderSubjectDifficultyChart();
  }

  // 渲染学科难度图表
  renderSubjectDifficultyChart() {
    if (this.subjectDifficultyChart) {
      this.subjectDifficultyChart.dispose();
      this.subjectDifficultyChart = null;
    }

    this.subjectDifficultyChart = this.$echarts.init(
      document.getElementById('SubjectDifficultyChart')
    );

    const series = [];
    this.tableColumns.forEach(col => {
      if (col.prop == 'examName') return;
      const prop = col.prop;
      const title = col.title;
      const data = [];
      this.tableData.forEach(item => {
        data.push(item[prop] || 0);
      });
      series.push({
        name: title,
        type: 'line',
        data: data,
      });
    });

    let option: EChartsOption = {
      toolbox: getDefaultToolBox(),
      title: {
        ...getDefaultTitle(),
        text: `学科难度`,
      },
      tooltip: {
        trigger: 'axis',
      },
      legend: getDefaultLegend(),
      grid: getDefaultGrid(),
      xAxis: {
        type: 'category',
        data: this.tableData.map(item => item.examName),
        // boundaryGap: false,
      },
      yAxis: {
        type: 'value',
      },
      series: series,
    };
    this.subjectDifficultyChart.setOption(option);
  }
}
</script>

<style scoped lang="scss"></style>

<template>
  <div class="header-item">
    <span class="header-item__label">考试：</span>
    <el-select
      class="header-item__select"
      value-key="examId"
      :value="filterInfo.examInfos"
      popper-class="select-popper"
      multiple
      collapse-tags
      :loading="!examList.length && loading"
      :popper-append-to-body="false"
      :filterable="true"
      :filter-method="onFilterMethod"
      @visible-change="onVisibleChange"
      @change="onExamChange"
      v-lazyloading="getExamList"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <el-option
        v-for="item in examList"
        :key="item.examId"
        :label="item.examName"
        :value="item"
        :title="item.examName"
      >
      </el-option>
      <div v-if="!finished && loading" style="text-align: center">
        <el-button type="text" :loading="loading">加载中</el-button>
      </div>
    </el-select>
  </div>
</template>

<script lang="ts">
import { IExamReportInfo } from '@/pages/studyReport/plugins/types';
import { getExamReportList } from '@/service/pexam';
import { Component } from 'vue-property-decorator';
import ReportComponent from './ReportComponent';

// 定义防抖函数
const debounce = (func, delay) => {
  let timerId;
  return function () {
    const context = this;
    const args = arguments;
    clearTimeout(timerId);
    timerId = setTimeout(function () {
      func.apply(context, args);
    }, delay);
  };
};

@Component({
  directives: {
    // 懒加载
    lazyloading: {
      bind(el, binding) {
        let SELECT_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
        SELECT_DOM.addEventListener('scroll', function () {
          let condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      },
    },
  },
})
export default class ReportExamInfoSelect extends ReportComponent {
  // 考试列表查询参数
  queryParams = {
    keyWord: '',
  };
  // 考试列表分页器
  pagination = {
    page: 1,
    pageSize: 50,
    total: 0,
  };
  // 考试列表
  examList = [] as IExamReportInfo[];
  // 是否查询结束
  finished = false;
  // 是否正在加载
  loading = false;

  async mounted() {
    this.onFilterMethod = debounce(this.onFilterMethod, 300);
    if (this.filterInfo.examInfos.length > 0) {
      this.examList = this.filterInfo.examInfos;
    } else {
      await this.getExamList();
      await this.FilterModule.initExam(this.examList[0]);
    }
  }

  // 获取考试列表
  async getExamList() {
    if (this.finished) return;
    if (this.loading) return;
    this.loading = true;

    try {
      const res = await getExamReportList({
        schoolId: this.$sessionSave.get('schoolInfo').id,
        acadYearsId: '',
        acadTermId: '',
        gradeId: '',
        subjectId: '',
        categoryId: '',
        importScoreState: 1, // 导入成绩状态(-1:全部 0:未导入 1:已导入)
        keyWord: this.queryParams.keyWord,
        page: this.pagination.page,
        pageSize: this.pagination.pageSize,
      });
      if (this.pagination.page === 1) {
        this.examList = res.data.list;
      } else {
        this.examList = this.examList.concat(res.data.list);
      }
      this.pagination.page++;
      this.pagination.total = res.data.total;
      if (this.examList.length >= this.pagination.total) {
        this.finished = true;
      }
      this.loading = false;
    } catch (error) {
      console.error(error);
      this.examList = [];
      this.loading = false;
      this.finished = true;
    }
  }

  // 搜索考试
  onFilterMethod(val) {
    this.queryParams.keyWord = val;
    this.pagination.page = 1;
    this.pagination.total = 0;
    this.examList = [];
    this.finished = false;
    this.getExamList();
  }

  // 显示隐藏
  onVisibleChange(show) {
    this.queryParams.keyWord = '';
    this.pagination.page = 1;
    this.pagination.total = 0;
    this.examList = [];
    this.finished = false;
    this.getExamList();
  }

  onSelectAll(isAll: boolean) {
    if (isAll) {
      this.FilterModule.setExamInfos(this.examList);
    } else {
      this.FilterModule.setExamInfos([]);
    }
  }

  // 考试更换事件
  async onExamChange(value: IExamReportInfo[]) {
    this.FilterModule.setExamInfos(value);
  }
}
</script>

<style scoped lang="scss">
@import './reportSelect.scss';
</style>

<template>
  <div class="scan-task-container" v-loading="headloding">
    <template v-if="totalData.length != 0">
      <div class="task-info" v-for="(item, index) in examData" :key="index">
        <el-page-header
          class="batch-header"
          @back="backReport"
          content=""
          v-if="isEdit == 2 && index == 0"
        >
        </el-page-header>
        <div class="exam-info">
          <div v-if="item.workId">
            <p class="exam-name">{{ item.title }}</p>
            <div style="display: flex" class="info-row">
              <div class="info-item">
                <span class="info-subject">{{ item.gradeName }}</span>
              </div>
              <div class="info-item">
                <span class="info-subject">{{ item.subjectName }}</span>
              </div>
              <span
                ><label>已扫：</label><span class="info-nums">{{ item.realScanNum }}</span></span
              >
              <span
                ><label>未扫：</label><span class="info-nums">{{ item.noScanNum }}</span>
                <el-button
                  type="text"
                  style="margin-left: 20px; font-size: 16px"
                  @click="toNoScanNumPage(index)"
                  >查看未扫名单
                </el-button></span
              >
            </div>
          </div>
          <div v-else>
            <p>暂无考试信息</p>
          </div>
          <div class="error-handle-btn" v-if="index == 0">
            <Button class="btn" type="default" @click="getPreTaskInfo">刷新</Button>
          </div>
        </div>
        <div class="scan-task-list-box">
          <!--扫描批次数据-->
          <el-table
            :data="groupedData[item.workId] || groupedData[item.sourceId]"
            v-loading="listLoading"
            :row-class-name="tableRowClassName"
            style="width: 100%"
            class="bacth-table"
            :header-cell-style="headerStyle"
          >
            <el-table-column prop="batchId" label="扫描批次" width="260"></el-table-column>
            <el-table-column label="扫描时间" width="180">
              <template slot-scope="scope">
                <span v-if="scope.row.dateCreated">{{ scope.row.dateCreated.substr(0, 16) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="classNames" show-overflow-tooltip label="班级" width="260">
            </el-table-column>
            <!-- <el-table-column prop="teacherName" label="教师"> </el-table-column> -->
            <el-table-column prop="scan_num" label="已扫描（页）"> </el-table-column>
            <el-table-column label="已识别（页）">
              <template slot-scope="scope">
                <div v-show="scope.row.status == 1">
                  <span class="task_progress_label">识别中：<i class="el-icon-loading"></i></span>
                  <el-progress
                    :percentage="scope.row.percentage"
                    :format="process_format"
                    class="task_progress"
                  ></el-progress>
                </div>
                <div v-show="scope.row.status != 1">
                  {{ scope.row.detect_num }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="error_num" label="异常">
              <template slot-scope="scope">
                <span class="task_error_num" :class="{ error_no_zero: scope.row.error_num > 0 }">{{
                  scope.row.error_num
                }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status_str" label="状态" width="120"> </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <div v-if="scope.row.id" class="operate-row">
                  <!-- <div v-show="scope.row.status != 1"> -->
                  <template v-if="item.source == 3 && scope.row.error_num == 0">
                    <el-button v-if="scope.row.status == 4" type="success" class="success-button">
                      已发布
                    </el-button>
                    <el-button
                      v-else
                      type="warning"
                      class="publish-button"
                      @click="openPublishDialog(scope.$index, scope.row, index)"
                    >
                      发布成绩
                    </el-button>
                  </template>
                  <el-button
                    v-if="scope.row.error_num > 0"
                    type="primary"
                    class="error-button"
                    @click="onGogoHandleError(scope.$index, scope.row)"
                    >处理异常
                  </el-button>
                  <!-- <el-button
                    v-if="!scope.row.cardId || scope.row.examId == 'no_exam_id'"
                    type="warning"
                    class="publish-button"
                    @click="relationExam(scope.$index, scope.row)"
                  >
                    关联考试
                  </el-button> -->
                  <el-dropdown @command="handleCommand">
                    <span class="el-dropdown-link"> 更多 </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item @click.native="handleViewImage(scope.$index, scope.row)"
                        >查看详情</el-dropdown-item
                      >
                      <el-dropdown-item @click.native="onReDetectTask(scope.$index, scope.row)"
                        >重新识别</el-dropdown-item
                      >
                      <el-dropdown-item
                        style="color: #ff4646"
                        @click.native="handleDeleteTask(scope.$index, scope.row)"
                        >删除</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </el-dropdown>
                  <!-- </div> -->
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="no-totaldata">
        <el-button class="no-data-button" type="primary" @click="getPreTaskInfo"
          >暂无数据，请点击刷新</el-button
        >
      </div>
    </template>
    <!-- 发布成绩弹窗 -->
    <publish-score-dialog
      v-if="isPublishScoreDialog"
      :examInfo="publishScoreInfo"
      @close-publish="closePublish"
      @publish-score="publishScore"
    ></publish-score-dialog>
    <DetectSetting
      :visible.sync="reDetectDlg.show"
      :taskId="reDetectDlg.taskId"
      :preId="reDetectDlg.preId"
      @onTaskReDetect="onTaskReDetect"
    >
    </DetectSetting>
    <el-dialog title="修改考试信息" :visible.sync="showEditDlg" width="600px">
      <p class="alter-tips">温馨提示：请确认考试信息，选择错误将无法生成考试分析报告</p>
      <el-form ref="form" label-width="120px">
        <el-form-item label="年级：">
          <el-select
            v-model="dlgExamInfo.gradeId"
            @change="onSelectGradeChange"
            clearable
            class="grade-select"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in gradeList"
              :key="item.id"
              :label="item.grade_name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="学科：">
          <el-select
            v-model="dlgExamInfo.subjectId"
            @change="getSchoolExamList"
            clearable
            class="grade-select"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in gradeSubjectList"
              :key="item.id"
              :label="item.subject_name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="考试名称：">
          <el-select
            v-model="dlgExamInfo.examId"
            filterable
            remote
            @change="onSelectExamChange"
            style="width: 300px"
            reserve-keyword
            :remote-method="queryExamList"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in examList"
              :key="'subject_' + index"
              :label="item.title"
              :value="item.workId"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showEditDlg = false">取 消</el-button>
        <el-button type="primary" @click="onSaveExamInfo">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getScanPaperWorkStat,
  publishScoreAPI,
  getSchoolGrade,
  getSubjectsAPI,
  searchScanPaperWorkList,
} from '@/service/api';

import {
  deleteScanTask,
  getTaskProcess,
  taskReDetect,
  getPreTaskInfo,
  getExamInfoAPI,
  getPersonalBookInfo,
  updateTaskExam,
} from '@/service/pexam';
import PublishScoreDialog from '@/components/PublishScoreDialog.vue';
import { Loading } from '@iclass/element-ui';
import { isNullOrUndefined } from '@/utils';
import DetectSetting from '@/components/scan/DetectSetting';

export default {
  name: 'scan-task',
  data() {
    return {
      // 表格头部样式
      headerStyle: {
        background: '#F5F7FA ',
        color: '#606266',
        fontWeight: '400',
        fontSize: '14px',
        width: '100%',
        height: '40px',
      },
      noResImg: require('@/assets/no-res.png'),
      schoolId: this.$route.query.schoolId,
      processTaskList: [],
      getProcessId: 0,
      listLoading: true,
      showEditDlg: false,
      statusTypes: ['扫描中', '识别中', '识别完成', '处理完成', '处理完成'],
      isEdit: 1,
      examData: [],
      totalData: [],
      groupedData: {},
      isPublishScoreDialog: false,
      publishScoreInfo: {},
      //加载中状态
      scoreLoading: null,
      askId: this.$route.query.id || this.$route.query.Id,
      examReportId: '',
      headloding: false,
      reDetectDlg: {
        show: false,
        taskId: '',
        preId: '',
      },
      taskInfo: {},
      subjectList: [],
      examList: [],
      gradeList: [],
      dlgExamInfo: {
        gradeId: '',
        selectPhase: 0,
        subjectId: '',
        examId: '',
        loading: false,
      },
      taskId: '',
    };
  },
  components: {
    DetectSetting,
    PublishScoreDialog,
  },
  computed: {
    gradeSubjectList() {
      if (!this.dlgExamInfo.selectPhase) {
        return this.subjectList;
      }
      return this.subjectList.filter(it => it.phase === this.dlgExamInfo.selectPhase);
    },
  },
  mounted() {
    this.isEdit = this.$route.query.isEdit || 1;
    this.getPreTaskInfo();
  },
  methods: {
    /***
     * @name:返回上一页
     */
    backReport() {
      this.$router.back();
    },
    /**
     * @name:关联考试跳转到登录页
     */
    relationExam(data, row) {
      this.taskId = row.id;
      this.showEditDlg = true;
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.id) {
        return '';
      }
      return 'sum_table_info';
    },
    process_format(percentage) {
      return `${percentage.toFixed(1)}%`;
    },
    handleCommand(index) {},
    /**
     * @name:获取多批次列表
     */
    getPreTaskInfo() {
      this.listLoading = true;
      this.headloding = true;
      this.processTaskList = [];
      getPreTaskInfo({ id: this.askId })
        .then(res => {
          this.listLoading = false;
          this.headloding = false;
          this.totalData = res.data.taskList;
          this.taskInfo = res.data.taskInfo;
          this.getGradeSubjectInfo(res.data.schoolId);
          if (this.totalData.length > 0) {
            let firstInfo = this.totalData.filter(item => {
              return item.schoolId;
            });
            if (firstInfo.length > 0) {
              this.schoolId = firstInfo[0].schoolId;
            }
            this.groupByExamId();
          } else {
            this.examData = [];
            this.groupedData = {};
          }
        })
        .catch(err => {
          this.headloding = false;
        });
    },
    /**
     * @name:根据examId分组
     */
    async groupByExamId() {
      const uniqueExamIds = [...new Set(this.totalData.map(item => item.examId))];
      this.groupedData = this.totalData.reduce((result, item) => {
        const examId = item.examId;
        if (!result[examId]) {
          result[examId] = [];
        }
        result[examId].push(item);
        return result;
      }, {});
      for (let key in this.groupedData) {
        let error_num = 0;
        let detect_num = 0;
        let scan_num = 0;
        this.groupedData[key].forEach(it => {
          it.error_num = it.error_num || 0;
          it.detect_num = it.detect_num || 0;
          it.percentage = (it.detect_num / it.scan_num) * 100;
          if (it.status >= 0) {
            it.status_str = this.statusTypes[it.status];
          } else {
            it.status_str = '识别失败';
          }
          if (it.status == 0 || it.status == 1) {
            this.processTaskList.push(it);
          }
          error_num += it.error_num;
          detect_num += it.detect_num;
          scan_num += it.scan_num;
        });
        this.groupedData[key].push({
          batchId: '合计',
          scan_num: scan_num,
          detect_num: detect_num,
          error_num: error_num,
        });
      }
      if (this.processTaskList.length > 0) {
        this.updateTaskProcess(this.getProcessId + 1);
      }
      this.examData = [];
      const promises = uniqueExamIds.map(async item => {
        let paras = {
          workId: item,
          schoolId: this.schoolId,
        };
        try {
          if (item == 'no_exam_id') {
            return { title: '暂无考试信息', sourceId: 'no_exam_id' };
          } else {
            const res = await getScanPaperWorkStat(paras);
            return res.data;
          }
        } catch (err) {
          // 错误处理
          this.examData = [];
          this.groupedData = {};
        }
      });
      try {
        const results = await Promise.all(promises);
        this.examData = results;
      } catch (err) {
        console.error(err); // 错误处理
      }
    },
    /**
     * @name:更新状态
     */
    updateTaskProcess(id) {
      if (!this.processTaskList.length) return;
      if (id <= this.getProcessId) {
        return;
      }
      this.getProcessId = id;
      let processId = id;
      let id_list = this.processTaskList.map(it => it.id);

      getTaskProcess({
        taskIds: id_list.join(';'),
      }).then(res => {
        let result = res.data;
        let hasChange = false;
        for (let key in this.groupedData) {
          let error_num = 0;
          let detect_num = 0;
          let scan_num = 0;
          this.groupedData[key].forEach(it => {
            if (!it.id) return;
            let data = result[it.id];
            if (data) {
              let finish = parseInt(data.finish);
              let status = parseInt(data.status || '1');
              let error = parseInt(data.error);
              it.detect_num = finish;
              it.error_num = error;
              it.scan_num = parseInt(data.image_num) || it.detect_num;

              it.status = status;
            }

            if (it.scan_num > 0) {
              it.percentage = (it.detect_num / it.scan_num) * 100;
            } else {
              it.percentage = 0;
            }

            if (it.status >= 0) {
              it.status_str = this.statusTypes[it.status];
            } else {
              it.status_str = '识别失败';
            }
            error_num += it.error_num;
            detect_num += it.detect_num;
            scan_num += it.scan_num;

            if (data) {
              hasChange = hasChange || it.status !== 1;
            }
          });
          this.groupedData[key].splice(this.groupedData[key].length - 1, 1, {
            batchId: '合计',
            scan_num: scan_num,
            detect_num: detect_num,
            error_num: error_num,
          });
        }
        if (hasChange) {
          this.getPreTaskInfo();
        } else {
          setTimeout(() => {
            this.updateTaskProcess(processId + 1);
          }, 1000);
        }
      });
    },
    /**
     * @name:跳转到查看全部答卷
     */
    handleViewImage(index, item) {
      this.$router.push({
        path: '/scan/images',
        query: {
          examId: item.examId,
          taskId: item.id,
          paperNo: item.cardId,
        },
      });
    },
    /**
     * @name:重新识别
     */
    onReDetectTask(index, item) {
      this.reDetectDlg.taskId = item.id;
      this.reDetectDlg.preId = item.preId;
      this.reDetectDlg.show = true;
    },
    onTaskReDetect(taskId) {
      let item = this.totalData.find(it => it.id === taskId);
      if (!item) {
        this.$message.error('批次不存在');
        return;
      }
      item.status = 1;
      this.$message.success('正在重新识别');
      this.processTaskList.push(item);
      this.updateTaskProcess(this.getProcessId + 1);
    },
    toReDetectTask(item) {
      item.percentage = 0;
      taskReDetect({
        taskId: item.id,
      })
        .then(res => {
          item.status = 1;
          this.$message.success('正在重新识别');
          this.processTaskList.push(item);
          this.updateTaskProcess(this.getProcessId + 1);
        })
        .catch(data => {
        });
    },
    /**
     * 删除任务
     * @param index
     * @param item
     */
    handleDeleteTask(index, item) {
      this.$confirm('删除后数据不可恢复，是否删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.toDeleteTask(item);
        })
        .catch(() => {
          // 用户点击了取消，不做任何事情
        });
    },
    toDeleteTask(item) {
      deleteScanTask({ taskId: item.id }).then(res => {
        if (res.code == 1) {
          this.$message.success('删除成功');
          this.getPreTaskInfo();
        } else {
          this.$message.error(res.msg || '删除失败');
        }
      });
    },
    /**
     * @name:处理异常
     */
    onGogoHandleError(index, item) {
      this.$router.push({
        path: '/scan/errornew',
        query: {
          examId: item.examId,
          paperNo: item.cardId,
          examName: item.examTitle,
          schoolId: item.schoolId,
          personBookId: item.examId,
          taskId: item.id,
          fromPage: 'batch',
          fromId: this.askId,
        },
      });
    },
    /**
     * @name:跳转到未扫名单列表
     */
    toNoScanNumPage(index) {
      this.$router.push({
        path: '/scan_not',
        query: {
          workId: this.examData[index].workId,
          schoolId: this.schoolId,
        },
      });
    },
    /**
     * @name:打开发布成绩弹窗
     */
    openPublishDialog(index, item, Index) {
      let paperInfo = {
        personBookId: item.examId,
        subectName: this.examData[Index].subjectName,
      };
      this.publishScoreInfo.examId = item.examId;
      this.publishScoreInfo.examName = item.examTitle;
      this.publishScoreInfo.paperInfo = paperInfo;
      this.publishScoreInfo.schoolId = item.schoolId;
      this.isPublishScoreDialog = true;
      this.getPersonalBookInfo();
    },
    /**
     * @name:获取考试id
     */
    async getPersonalBookInfo() {
      let res = await getPersonalBookInfo({
        id: this.publishScoreInfo.examId,
      });
      if (res.code == 1) {
        this.examReportId = res.data.examId;
      } else {
        this.examReportId = '';
      }
    },
    /**
     * @name:发布成绩
     */
    async publishScore() {
      this.isPublishScoreDialog = false;
      await publishScoreAPI({
        schoolId: this.publishScoreInfo.schoolId,
        workId: this.publishScoreInfo.examId,
      })
        .then(res => {
          this.scoreLoading = Loading.service({
            lock: true,
            text: '正在生成分析报告，请稍候，预计5-20s，若长时间未生成，请刷新页面重试',
            background: 'rgba(0, 0, 0, 0.7)',
          });
          this.checkingScoreStatus();
        })
        .catch(err => {
        });
    },
    /**
     * @name:检查发布成绩状态
     */
    checkingScoreStatus() {
      setTimeout(async () => {
        let params = {
          examId: this.examReportId,
        };
        getExamInfoAPI(params)
          .then(res => {
            if (res.data.dataState >= 1) {
              this.scoreLoading.close();
              this.$message({
                message: '发布成功！',
                type: 'success',
                duration: 1500,
              });
              this.getPreTaskInfo();
            } else {
              this.checkingScoreStatus();
            }
          })
          .catch(err => {
            this.scoreLoading.close();
          });
      }, 5000);
    },
    /**
     * @name:关闭发布成绩弹窗
     */
    closePublish() {
      this.isPublishScoreDialog = false;
    },
    onSelectExamChange(item) {
      console.log(this.dlgExamInfo.examId);
    },
    /**
     * 获取年级学科列表
     * @param schoolId
     */
    getGradeSubjectInfo(schoolId) {
      if (!schoolId) return;
      // 获取年级列表
      if (this.gradeList.length === 0) {
        getSchoolGrade({
          schoolId: schoolId,
        }).then(res => {
          this.gradeList = res.data;
        });
      }

      if (this.subjectList.length === 0) {
        getSubjectsAPI({
          schoolId: schoolId,
        }).then(res => {
          this.subjectList = res.data;
        });
      }
    },
    onSelectGradeChange(gradeId) {
      let phase = 0;
      if (gradeId >= 1 && gradeId <= 6) {
        phase = 1;
      } else if (gradeId >= 7 && gradeId <= 9) {
        phase = 2;
      } else if (gradeId >= 10 && gradeId <= 12) {
        phase = 3;
      }
      this.dlgExamInfo.selectPhase = phase;
      this.getSchoolExamList();
    },
    /**
     * 获取考试列表
     */
    getSchoolExamList() {
      this.queryExamList(this.dlgExamInfo.query);
    },
    queryExamList(query) {
      this.dlgExamInfo.query = query;
      if (this.dlgExamInfo.loading) return;
      if (!this.dlgExamInfo.gradeId || !this.dlgExamInfo.subjectId) {
        // return
      }
      this.dlgExamInfo.loading = true;
      searchScanPaperWorkList({
        schoolId: this.schoolId,
        keyWord: query,
        gradeId: this.dlgExamInfo.gradeId,
        subjectId: this.dlgExamInfo.subjectId,
      })
        .then(res => {
          this.dlgExamInfo.loading = false;
          this.examList = res.data.rows;
          if (this.dlgExamInfo.examId) {
            let examInfo = this.examList.find(it => it.workId === this.dlgExamInfo.examId);
            if (!examInfo) {
              this.dlgExamInfo.examId = '';
            }
          }

          // 查询条件变化，再次查询
          if (query !== this.dlgExamInfo.query) {
            this.queryExamList(this.dlgExamInfo.query);
          }
        })
        .catch(data => {
          this.dlgExamInfo.loading = false;
        });
    },
    // 确定关联考试
    onSaveExamInfo() {
      if (!this.dlgExamInfo.examId) {
        this.$message.error('没有选择考试');
        return;
      }
      updateTaskExam({
        taskId: this.taskId,
        examId: this.dlgExamInfo.examId,
      }).then(res => {
        this.showEditDlg = false;
        this.$message.success('更新成功');
        this.getPreTaskInfo();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.scan-task-container {
  .exam-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #000;
    p {
      margin: 10px;
    }
    span {
      margin: 0px 5px;
      font-size: 16px;
    }
    .exam-name {
      font-size: 18px;
      font-weight: bold;
      color: #161e26;
    }
    .exam-change {
      position: absolute;
      bottom: 100px;
    }
    .info-row {
      display: flex;
      align-items: center;
    }
    .info-item {
      display: flex;
      align-items: center;
      height: 24px;
      background: #f0f2f5;
      border-radius: 12px;
      margin-right: 8px;
      padding: 6px;
      .info-subject {
        font-weight: 400;
        color: #606266;
        line-height: 24px;
        padding: 10px;
      }
    }
    .info-nums {
      color: #303233;
      font-weight: bold;
    }
  }

  .task-info {
    padding: 30px 20px 0px 20px;
    flex: 1;

    .error-handle-btn {
      float: right;
      .btn {
        margin-left: 10px;
      }
    }
    .scan-task-list-box {
      margin-top: 20px;
      border: solid #f2f2f2;
      .task_progress_label {
        color: #3a5eff;
      }
      .task_progress {
        width: 200px;
        display: inline-block;
        ::v-deep .el-progress-bar {
          padding-right: 65px;
          margin-right: -70px;
        }
      }
    }
  }

  .nodata {
    width: 100%;
    height: auto;
    /*background : url("../assets/no-res.png") center center no-repeat;*/
    img {
      display: block;
      margin: 0 auto;
    }
  }

  .alter-tips {
    color: #ff4646;
    margin-left: 40px;
    padding: 0 20px 20px;
  }

  .error_no_zero {
    color: #ff4646;
    font-weight: bold;
  }

  ::v-deep .el-table__row.sum_table_info {
    background: #f5f7fa;
  }
  .el-dropdown-link {
    margin-left: 10px;
    color: unset;
  }
}
.batch-header {
  padding: 0px 0px 5px 0px;
}
.operate-row {
  display: flex;
  align-items: center;
}
.publish-button {
  background: #ff7c1f;
}
.error-button {
  background: #008dea;
}
.success-button,
.publish-button,
.error-button {
  width: 72px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.no-totaldata {
  width: 100%;
  height: 100%;
  .no-data-button {
    display: block;
    margin: 0 auto;
    margin-top: 10%;
  }
}
</style>

import CryptoJS from 'crypto-js';

export default {
    // 解密  data：要加密解密的数据，AES_KEY：密钥，IV:偏移量
    decrypt(data, AES_KEY) {
        let key = CryptoJS.enc.Utf8.parse(AES_KEY)
        let decrypt = CryptoJS.AES.decrypt(data, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7
        })
        return CryptoJS.enc.Utf8.stringify(decrypt).toString();
    },
    // 加密
    encrypt(data, AES_KEY) {
        let key = CryptoJS.enc.Utf8.parse(AES_KEY)
        let srcs = CryptoJS.enc.Utf8.parse(data)
        let encrypted = CryptoJS.AES.encrypt(srcs, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7
        }) // 加密模式为ECB，补码方式为PKCS5Padding（也就是PKCS7）

        return encrypted.toString()
    },
};
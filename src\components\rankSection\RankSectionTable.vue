<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-01-29 13:50:12
 * @LastEditors: 小圆
-->
<template>
  <el-table
    :data="tableData"
    stripe
    :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
    style="width: 100%"
    v-sticky-table="0"
  >
    <el-table-column align="center" prop="className" label="班级" width="140">
      <template slot-scope="scope">
        <span>{{ scope.row.className }}</span>
      </template>
    </el-table-column>
    <el-table-column
      v-for="(item, index) in targetData.rankSegment"
      prop=""
      :label="`${item.start}` == -1 ? `${item.end}` : `${item.start}-${item.end}`"
      align="center"
      :key="index"
    >
      <template slot-scope="scope">
        <span>{{ scope.row.score[index] }}</span>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'RankSectionTable',
  props: ['tableData', 'targetData'],
  data() {
    return {
    };
  },
  mounted() {},
};
</script>

<style scoped></style>

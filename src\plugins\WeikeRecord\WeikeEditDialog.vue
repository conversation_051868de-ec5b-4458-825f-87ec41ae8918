<!--
 * @Description: 微课录制编辑弹窗
 * @Author: qmzhang
 * @Date: 2024-07-29 10:12:44
 * @LastEditTime: 2024-12-17 17:33:08
-->
<template>
    <div class="weike-record-edit text-center noselect">
        <el-input class="input-wkname" v-model="videoName" placeholder="请输入微课名称" />
        <div class="actions text-left">
            接收对象:
            <el-select class="selector-types" v-model="selectedReciveType" placeholder="请选择">
                <el-option v-for="item in reciverList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>
            <el-checkbox class="checkbox-netdisk" v-model="saveToNetdisk">保存至网盘</el-checkbox>
        </div>

        <div class="footer">
            <el-button class="footer-btn btn-delete" @click="deleteWk">取消</el-button>
            <el-button class="footer-btn" type="primary" @click="addWkToQues">添加微课</el-button>
        </div>
    </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { WeikeRecord } from './WeikeRecord.class';

@Component
export default class WeikeRecordEdit extends Vue {
    @Prop() vModel: any;
    // 视频名称
    videoName: string = ''
    // 接收对象列表
    reciverList = WeikeRecord.ReciverList;
    // 微课录制工具
    wkRecorder = WeikeRecord.GetInstance();
    // 已选择的接收类型
    selectedReciveType = 7;
    // 是否保存至网盘
    saveToNetdisk: boolean = true;
    loading: boolean = false

    created() {
        this.videoName = this.vModel.weikeName;
    }

    /**
     * @description: 删除微课
     */
    deleteWk() {
        // TODO 

        this.$emit('close')
    }

    /**
     * @description: 添加微课到题目
     */
    async addWkToQues() {
        let isOk = this.verifyDdata();
        if (!isOk) return;

        this.loading = true;
        let isSuc = await this.wkRecorder.AddWeikeToQuestion(this.videoName, this.selectedReciveType, this.saveToNetdisk);
        this.loading = false;
        if (isSuc) this.$emit('close');
    }

    /**
     * @description: 验证数据
     * @return {*}
     */
    verifyDdata(): boolean {
        if (!/\S/.test(this.videoName)) {
            this.$message({
                message: '文件名不能为空',
                type: 'warning',
                duration: 1500,
            });
            return false
        }

        if (/[$\*\|\\:"<>\/]+/g.test(this.videoName)) {
            this.$message({
                message: '文件名不能包含特殊字符 $ * | \\ : " < > ?/',
                type: 'warning',
                duration: 1500,
            });
            return false;
        }
        return true;
    }
}
</script>

<style lang="scss" scoped>
.weike-record-edit {
    padding: 30px;

    .input-wkname {
        margin-bottom: 20px;
        font-size: 24px;

        ::v-deep input {
            height: 60px;
            line-height: 60px;
        }
    }

    .checkbox-netdisk {
        ::v-deep .el-checkbox__label {
            font-size: 16px;
        }
    }

    .actions {
        margin-bottom: 50px;
        font-size: 16px;
    }

    .selector-types {
        margin-right: 20px;
    }

    .footer-btn {
        font-size: 18px;
    }

    .btn-delete {
        margin-right: 15px;
    }
}
</style>
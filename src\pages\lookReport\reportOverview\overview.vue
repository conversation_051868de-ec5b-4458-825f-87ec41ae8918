<template>
  <div class="overView">
    <!--头部统计数据-->
    <div class="clearfix">
      <div class="titleLine">学情概览</div>
    </div>
    <div class="statisticData" v-loading="statisticLoading">
      <div v-if="gradeStatistic.length">
        <div class="statistic__header">
          共<span class="titleInfo">{{ stat.grd.clzNum }}</span
          >个班，<span class="titleInfo">{{ stat.grd.total }}</span
          >人参考

          <span v-if="filterData.subjectId">
            <span>
              ，<span class="titleInfo"> {{ stat.grd.absent }}</span
              >人缺考
            </span>
            <!-- <span>
              ，<span class="titleInfo"> {{ stat.grd.noScan }}</span
              >人未扫描
            </span> -->
            <span v-if="stat.grd.noZero != 0">
              ，<span class="titleInfo"> {{ stat.grd.noZero }}</span
              >人0分不参与统计
            </span>
          </span>

          <span v-if="filterData.classId">
            <span>
              ，本班参考<span class="titleInfo"> {{ stat.clz.total }}</span
              >人
            </span>
            <span v-if="filterData.subjectId">
              <span>
                ，本班缺考<span class="titleInfo"> {{ stat.clz.absent }}</span
                >人
              </span>
              <!-- <span>
                ，本班<span class="titleInfo"> {{ stat.clz.noScan }}</span
                >人未扫描
              </span> -->
              <span v-if="stat.clz.noZero != 0">
                ，本班<span class="titleInfo"> {{ stat.clz.noZero }}</span
                >人0分不参与统计
              </span>
            </span>
            <span>
              ，班级名次<span class="titleInfo"> {{ stat.clz.rank }}</span>
            </span>
          </span>

          <el-button style="margin-left: 20px" type="primary" @click="openMissDialog"
            >缺考名单</el-button
          >
          <!-- <span v-if="!filterData.classId">
                         <el-button style="font-size:16px " type="text" @click="showMissingList(stat)"> 缺考人数{{stat.missExamNum}}人。</el-button>
                    </span> -->
          <!-- 
                    <span v-if="filterData.classId">本班<span>{{stat.classExamStuNum}}</span>人实际参与测验 ,
                    <el-button style="font-size:16px " type="text" @click="showMissingList(stat)">  本班缺考人数{{stat.missExamNum}}人。</el-button>
                    </span> -->
        </div>
        <el-row :gutter="20" class="statisticUl" v-if="gradeStatistic.length">
          <el-col
            :span="3"
            class="statisticLi"
            v-for="(item, index) in gradeStatistic"
            :key="index"
          >
            <div class="title">{{ item.name }}</div>
            <div class="score">{{ item.score }}</div>
          </el-col>
        </el-row>
      </div>
      <div
        v-else
        class="noDataText display_flex align-items_center justify-content_flex-center"
        style="min-height: 100px"
      >
        未查询该次班级该次考试数据
      </div>
    </div>
    <!--重点关注-->
    <div class="attnBlock" v-if="isShowAttn">
      <div style="display: flex; align-items: center">
        <div class="titleLine">重点关注</div>

        <!-- 添加对比考试按钮 -->
        <contrast-report-btn
          v-if="isMainReport"
          class="pull-left"
          label="查看进退步"
          :examName="contrastObj.examName"
          :examId="contrastObj.examId"
          @updateData="getReportCard"
        >
        </contrast-report-btn>
      </div>

      <div class="attn-table display_flex">
        <ul
          class="left-tab-ul list-none"
          v-if="attnObj.attnUlList.length"
          :class="attnObj.attnActiveIndex === 0 ? 'active_0' : 'active_1'"
        >
          <li
            v-for="(item, index) in attnObj.attnUlList"
            :key="index"
            @click="changeShowStu(index)"
            class="display_flex align-items_center justify-content_flex-center"
            :class="{ active: attnObj.attnActiveIndex === index }"
          >
            {{ item }}的同学
          </li>
        </ul>
        <div class="right-list flex_1 display_flex" v-if="attnObj.attnTableList.length">
          <ul class="attnUl pull-left" v-for="(item, index) in attnObj.attnTableList" :key="index">
            <li
              class="attnLi display_flex justify-content_flex-center align-items_center"
              v-for="(subItem, subIndex) in item"
              :key="subIndex"
            >
              <ul class="data-ul list-none clearfix display_flex">
                <li class="data-li ellipsis" :title="subItem.stuName">
                  {{ subItem.stuName }}
                </li>
                <li class="data-li">{{ subItem.score }}分</li>
                <li class="data-li">
                  第{{ filterData.classId ? subItem.clsRank : subItem.grdRank }}名
                </li>
                <li class="data-li">
                  {{ attnObj.attnActiveIndex ? '退步' : '进步'
                  }}{{
                    filterData.classId
                      ? Math.abs(subItem.examClsRankUp)
                      : Math.abs(subItem.examGrdRankUp)
                  }}名
                </li>
              </ul>
            </li>
          </ul>
        </div>
        <div class="right-list flex_1 display_flex" v-else>
          <div
            class="noDataText display_flex align-items_center justify-content_flex-center"
            style="width: 100%"
          >
            暂无学生
          </div>
        </div>
      </div>
    </div>
    <!--高频错题-->
    <div class="mistackBlock">
      <div class="clearfix">
        <div class="titleLine">高频错题</div>
        <div class="right-filter pull-right" v-if="scoring">
          题目{{ filterData.classId ? '班级' : '年级' }}得分率≤
          <NumericInput
            class="scoreInput"
            v-model="scoring"
            :type="'digit'"
            :isPositive="true"
          ></NumericInput>
          <el-button class="sureScoreBtn" type="primary" @click="sureScoring('mistake')"
            >确定</el-button
          >
        </div>
      </div>
      <div class="mimstackTable" v-loading="mistackLoading">
        <el-table
          v-if="filterData.subjectId"
          class="mistack-table"
          :data="mistackTable"
          height="370"
          :header-cell-style="{ fontSize: '16px', color: '#3F4A54' }"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="quesNoDesc" label="题号">
            <template slot-scope="scope">
              <span>
                {{ scope.row.quesNoDesc }} {{scope.row.abPaper == 0 ? '(A卷)' : scope.row.abPaper == 1 ? '(B卷)' : ''}}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="quesType" label="题型">
          </el-table-column>
          <!--如果报告没有关联个册则没有知识点数据-->
          <el-table-column v-if="mistackTable.length && mistackTable[0].pointNames" label="知识点">
            <template slot-scope="scope">
              <span>
                {{ scope.row.pointNames.length ? scope.row.pointNames : '' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column v-if="filterData.classId" label="班级得分率">
            <template slot-scope="scope">
              {{ Number(scope.row.clsScoreRate * 100).toFixed(2) }}%
            </template>
          </el-table-column>
          <el-table-column label="年级得分率">
            <template slot-scope="scope">
              {{ Number(scope.row.grdScoreRate * 100).toFixed(2) }}%
            </template>
          </el-table-column>
        </el-table>
        <div class="hideBlock" style="height: 370px" v-else>选择具体学科后显示数据</div>
      </div>
    </div>
    <!--班级成绩分布-->
    <div class="distBlock">
      <div class="clearfix">
        <div class="titleLine">{{ filterData.classId ? '班级' : '年级' }}成绩分布</div>
      </div>
      <div class="distTable display_flex" v-loading="disLoading">
        <div class="left-chart flex_1" id="distTable" :style="{ height: '368px' }"></div>

        <div class="right-table display_flex">
          <ul class="left-tab-ul list-none">
            <li
              v-for="(item, index) in distObj.distStuList"
              :key="index"
              @click="changeShowGrade(index)"
              :class="{ active: distObj.distActiveIndex === index }"
            >
              {{ item.name }}
              <div class="gradeDetail" v-if="distObj.distActiveIndex === index">
                <p>{{ distObj.activeTableLength }}人</p>
                <p>
                  占比{{distObj.rate}}%
                </p>
              </div>
            </li>
          </ul>
          <div class="right-list flex_1 display_flex">
            <div v-if="distObj.distTableList.length" style="display: flex; width: 100%">
              <ul
                class="list-none attnUl"
                v-for="(item, index) in distObj.distTableList"
                :key="index"
              >
                <li
                  class="attnLi display_flex justify-content_flex-center"
                  v-for="(subItem, subIndex) in item"
                  :key="subIndex"
                >
                  <ul class="data-ul list-none clearfix display_flex">
                    <li class="data-li">{{ subItem.index }}</li>
                    <li class="data-li text-ellipsis" :title="subItem.stuName">
                      {{ subItem.stuName }}
                    </li>
                    <li
                      v-if="!filterData.classId"
                      class="data-li text-ellipsis"
                      :title="subItem.clsName"
                    >
                      {{ subItem.clsName }}
                    </li>
                  </ul>
                </li>
              </ul>
            </div>
            <div
              v-if="!distObj.distTableList.length"
              class="display_flex align-items_center justify-content_flex-center"
              style="width: 100%"
            >
              该等级暂无学生
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="缺考名单" :visible.sync="dialogVisible" width="30%">
      <div class="missingStulist">
        <span v-for="(item, index) in missStuList" :key="index" class="stuList">{{
          item.name
        }}</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
    <miss-stu-dialog
      v-if="isShowMissDialog"
      :subjectId="filterData.subjectId"
      :clzId="filterData.classId"
      @closed="onCloseMissDialog"
    ></miss-stu-dialog>
  </div>
</template>

<script>
import contrastReport from '@/components/contrastReport.vue';
import {
  getReportCard,
  getStatisticData,
  getAttentionStuList,
  getHighMIstakes,
  getScoreDistribute,
  getNewReportCard,
} from '@/service/pexam';
import NumericInput from '@/components/NumericInput';
// 引入 ECharts 主模块
import echarts from 'echarts/lib/echarts';
// 引入柱形图
import 'echarts/lib/chart/bar';
// 引入提示框和title组件
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/legend';
import MissStuDialog from '@/pages/lookReport/classCompare/reportCard/missStuDialog.vue';
import ContrastReportBtn from '@/components/contrastReportBtn.vue';

export default {
  name: 'overView',
  components: {
    NumericInput,
    contrastReport,
    MissStuDialog,
    ContrastReportBtn,
  },
  data() {
    return {
      contrastObj: {
        examName: '',
        examId: '',
      },
      pagination: {
        page: 1,
        pageSize: 60,
        total: 0,
      },
      isFirst: true,
      contrastReportDialogVisible: false,
      statisticLoading: false,
      mistackLoading: false,
      disLoading: false,
      gradeArray: ['A', 'B', 'C', 'D', 'E'],
      // 默认值列表
      defaultList: {},
      scoring: 50,
      // 头部统计数据列表
      stat: {},
      gradeStatistic: [],
      // 重点关注
      attnObj: {
        attnUlList: ['进步', '退步'],
        attnActiveIndex: 0,
        attnTableList: [],
        attnStuList: {},
      },
      // 成绩分布
      distObj: {
        distActiveIndex: 0,
        distTableList: [],
        activeTableLength: 0,
        distStuList: {},
        totalStu: 0,
        rate: 0,
      },
      // 重点关注
      mistackTable: [],
      curPageDefault: {},
      //显示缺考名单
      dialogVisible: false,
      missStuList: [],

      // 是否显示缺考名单
      isShowMissDialog: false,
    };
  },
  props: {
    filterData: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    filterData: {
      deep: true,
      handler(newValue) {
        if (!newValue) return;
        this.updateFilter(newValue);
      },
    },
  },
  computed: {
    isDisabled() {
      return (
        this.$sessionSave.get('loginInfo').user_type != 5 &&
        this.$sessionSave.get('loginInfo').admin_type != 2
      );
    },

    // 是否显示重点关注
    isShowAttn() {
      let reportDetail = this.$sessionSave.get('reportDetail');
      let reportParent = this.$sessionSave.get('reportParent');
      // 方向报告显示
      if (reportDetail.source == 102 || reportDetail.source == 103 || reportDetail.source == 105) {
        return true;
      }
      // 分层班报告、自定义报告隐藏
      if (reportDetail.source == 101 || reportParent.examId !== reportDetail.examId) {
        return false;
      }
      // 主报告显示
      return true;
    },

    // 是否分层班
    isFcClass() {
      const is_fc_class = this.$sessionSave.get('reportDetail').source == 101;
      return is_fc_class;
    },

    isMainReport() {
      return (
        this.$sessionSave.get('reportDetail').examId == this.$sessionSave.get('reportParent').examId
      );
    },
  },
  mounted() {

    this.defaultList = this.$sessionSave.get('defaultList') || {};
    let contrastObj = this.$sessionSave.get('contrastObj');
    if (contrastObj) {
      this.contrastObj.examId = contrastObj.examId;
      this.contrastObj.examName = contrastObj.examName;
    }
    this.updateFilter(this.filterData);
  },
  methods: {
    // 获取到班级和学科后更新
    updateFilter(data) {
      this.defaultList = this.$sessionSave.get('defaultList') || {};
      this.getStatisticData();
      this.isFirst = false;
      this.getReportCard();
      this.getScoreDistribute();
      if (this.filterData.subjectId) {
        this.mistackTable = [];
        this.getHighMIstakes();
      }
    },
    sureScoring(type) {
      let getGradeExcRate = JSON.parse(this.defaultList.scoringLimit[0]);
      let getGradePassRate = JSON.parse(this.defaultList.scoringLimit[1]);
      switch (type) {
        case 'scoring':
          if (getGradeExcRate > 100 || getGradePassRate > 100) {
            this.$message.error('得分率不能大于100');
            return;
          }
          if (getGradeExcRate <= getGradePassRate) {
            this.$message.error('优秀率设置的得分率不能小于等于及格率');
            return;
          }
          this.getStatisticData();
          break;
        case 'mistake':
          if (this.filterData.subjectId) {
            this.mistackTable = [];
            this.getHighMIstakes();
          }
          break;
        case 'dist':
          if (this.defaultList.gradeLimit[0] > 100) {
            this.$message.error('得分率不能大于100');
            return;
          }
          let gradeLimit = this.defaultList.gradeLimit;
          let dataLength = gradeLimit.length;
          for (let i = 0; i < dataLength; i++) {
            let item = gradeLimit[i];
            if (i < dataLength - 1 && Number(item) < Number(gradeLimit[i + 1])) {
              this.$message.error('区间最大值不能小于最小值！');
              return;
            }
          }
          this.getScoreDistribute();
          break;
      }
    },
    // 头部统计数据
    getStatisticData() {
      this.statisticLoading = true;
      this.stat = {};
      this.gradeStatistic = [];
      getStatisticData({
        examId: this.$sessionSave.get('reportDetail').examId,
        clzId: this.filterData.classId,
        subjectId: this.filterData.subjectId,
        v: this.$sessionSave.get('reportDetail').v,
        qType: this.filterData.qType,
        // excScoringLimit: this.defaultList.scoringLimit[0],
        // passScoringLimit: this.defaultList.scoringLimit[1],
      })
        .then(res => {
          this.statisticLoading = false;
          var stat = res.data;
          this.gradeStatistic = [
            { name: '年级平均分', score: stat.grd.avg },
            { name: '年级最高分', score: stat.grd.max },
            { name: '年级最低分', score: stat.grd.min },
            { name: '年级优秀率', score: stat.grd.fine + '%' },
            { name: '年级及格率', score: stat.grd.pass + '%' },
            { name: '年级中位数', score: stat.grd.median },
            { name: '年级标准差', score: stat.grd.standard },
          ];
          if (this.filterData.classId) {
            this.gradeStatistic.push(
              { name: '班级平均分', score: stat.clz.avg },
              { name: '班级最高分', score: stat.clz.max },
              { name: '班级最低分', score: stat.clz.min },
              { name: '班级优秀率', score: stat.clz.fine + '%' },
              { name: '班级及格率', score: stat.clz.pass + '%' },
              { name: '班级中位数', score: stat.clz.median },
              { name: '班级标准差', score: stat.clz.standard }
              // { name: "班级名次", score: res.data.classRanking }
            );
          }
          this.stat = stat;
        })
        .catch(err => {
          this.statisticLoading = false;
          this.stat = {};
          this.gradeStatistic = [];
        });
    },
    // 重点关注
    getAttentionStuList() {
      this.attnObj = {
        attnUlList: ['进步', '退步'],
        attnActiveIndex: 0,
        attnTableList: [],
        attnStuList: {},
      };
      getAttentionStuList({
        examId: this.$sessionSave.get('reportDetail').examId,
        classId: this.filterData.classId,
        subjectId: this.filterData.subjectId,
        contrastId: this.contrastObj.examId,
        qType: this.filterData.qType,
      }).then(res => {
        if (res.data.contrastName && res.data.contrastId) {
          // this.contrastObj.examName = res.data.contrastName;
          // this.contrastObj.examId = res.data.contrastId;
          // this.$sessionSave.set('contrastObj', this.contrastObj);
        }
        this.attnObj.attnStuList = res.data;
        this.changeShowStu(0);
      });
    },
    // 高频错题
    getHighMIstakes() {
      this.mistackLoading = true;
      this.mistackTable = [];
      getHighMIstakes({
        examId: this.$sessionSave.get('reportDetail').examId,
        classId: this.filterData.classId,
        subjectId: this.filterData.subjectId,
        grdRate: this.scoring,
        qType: this.filterData.qType,
      })
        .then(res => {
          this.mistackLoading = false;
          this.mistackTable = res.data;
          this.mistackTable.forEach(item => {
            this.$set(item, 'quesType', this.$getQuesType(item.quesType));
          });
        })
        .catch(err => {
          this.mistackLoading = false;
        });
    },
    // 成绩分布
    getScoreDistribute() {
      this.disLoading = true;
      let gradeLimit = this.defaultList.gradeLimit;
      this.distObj = {
        distActiveIndex: 0,
        distTableList: [],
        activeTableLength: 0,
        distStuList: [],
        totalStu: 0,
        rate: 0,
      };
      getScoreDistribute({
        examId: this.$sessionSave.get('reportDetail').examId,
        classId: this.filterData.classId,
        subjectId: this.filterData.subjectId,
        qType: this.filterData.qType,
      })
        .then(res => {
          this.disLoading = false;
          this.distObj.distStuList = res.data || [];
          this.distObj.totalStu = this.distObj.distStuList.reduce((prev, current) => {
            return prev + current.data.length
          }, 0)
          this.changeShowGrade(0);

          this.initDistChart();
        })
        .catch(err => {
          this.disLoading = false;
        });
    },
    // 渲染班级成绩分析图表
    initDistChart() {
      // let res = this.distObj.distStuList;
      // 基于准备好的dom，初始化echarts实例
      let distChart = this.$echarts.init(document.getElementById('distTable'));
      // 绘制图表
      distChart.setOption({
        title: { text: '' },
        xAxis: {
          type: 'category',
          data: this.distObj.distStuList.map(item => item.name),
        },
        yAxis: {
          type: 'value',
          name: '人数', // 给X轴加单位
          nameTextStyle: {
            padding: [0, 0, 10, -40], // 四个数字分别为上右下左与原位置距离
          },
        },
        series: [
          {
            data: this.distObj.distStuList.map(item => item.data.length),
            //配置样式
            itemStyle: {
              //通常情况下：
              normal: {
                label: {
                  show: true, //开启显示
                  position: 'top', //在上方显示
                  textStyle: {
                    //数值样式
                    color: '#757C8C',
                    fontSize: 14,
                  },
                },
                //每个柱子的颜色即为colorList数组里的每一项，如果柱子数目多于colorList的长度，则柱子颜色循环使用该数组
                color: params => {
                  let colorList = ['#07C29D', '#409EFF', '#FFB400', '#FF6A68', '#C6C9CC'];
                  return colorList[params.dataIndex % colorList.length];
                },
              },
              //鼠标悬停时：
              // emphasis: {
              //     shadowBlur: 10,
              //     shadowOffsetX: 0,
              //     shadowColor: 'rgba(0, 0, 0, 0.5)'
              // }
            },
            type: 'bar',
          },
        ],
      });
    },
    // 切换显示进步学生或退步学生
    changeShowStu(index) {
      this.attnObj.attnActiveIndex = index;
      let originList = !index
        ? this.attnObj.attnStuList.progressStuInfo
        : this.attnObj.attnStuList.retrogressionStuInfo;
      this.attnObj.attnTableList = this.filterDatas(originList);
    },
    // 切换显示不同等级学生列表
    changeShowGrade(index) {
      this.distObj.distActiveIndex = index;
      // 显示不同等级学生的数据
      let distStuList = this.distObj.distStuList;
      let originList = distStuList[index].data;
      let rate = distStuList[index].rate;

      this.distObj.rate = rate;
      this.distObj.activeTableLength = originList.length;
      originList.length &&
        originList.forEach((item, index) => {
          this.$set(item, 'index', index + 1);
        });

      let innerClassList = this.$sessionSave.get('innerClassList');
      let dts = this.filterDatas(originList);
      dts.forEach(v => {
        v.forEach(v2 => {
          let cls = innerClassList.find(q => q.id == v2.classId);
          if (cls) {
            v2.clsId = cls.id;
            v2.clsName = cls.class_name;
          }
        });
      });
      this.distObj.distTableList = originList.length ? dts : [];
      // console.log(this.distObj.distTableList);
    },
    // 筛选数据
    filterDatas(originList) {
      if (!originList) return [];
      let firstArr = originList.filter((item, index) => {
          return index % 2 === 0;
        }),
        secondArr = originList.filter((item, index) => {
          return index % 2 !== 0;
        }),
        Arr = [];
      if (firstArr.length) {
        Arr.push(firstArr);
      }
      if (secondArr.length) {
        Arr.push(secondArr);
      }
      return Arr;
    },
    //缺考名单
    showMissingList(item) {
      console.log(item);
      this.dialogVisible = true;
    },
    // 添加对比考试
    addCompare() {
      this.contrastReportDialogVisible = true;
    },
    //关闭弹窗
    closeDialog() {
      this.$nextTick(() => {
        this.contrastReportDialogVisible = false;
      });
    },
    //对比考试报考列表
    async getReportCard(reportObj) {
      // 添加了对比考试
      if (reportObj) {
        this.contrastObj = reportObj;
        this.$sessionSave.set('contrastObj', this.contrastObj); // 保存对比考试，切换目录时还原
      }
      // try {
      //   const { data } = await getNewReportCard({
      //     examId: this.$sessionSave.get('reportDetail').examId,
      //     classId: this.filterData.classId,
      //     subjectId: this.filterData.subjectId,
      //     withExamId: this.contrastObj.examId || '',
      //     text: '',
      //     sort: '',
      //     page: this.pagination.page,
      //     pageSize: this.pagination.pageSize,
      //   });
      //   if (!reportObj && data.rows.contrast.name) {
      //     this.contrastObj.examName = data.rows.contrast.name;
      //     this.contrastObj.examId = data.rows.contrast.id;
      //     this.$sessionSave.set('contrastObj', this.contrastObj);
      //   }
      // } catch (error) {
      //   console.error(error);
      // }
      this.getAttentionStuList();
    },

    /**
     * @name:打开缺考名单弹窗
     */
    openMissDialog() {
      this.isShowMissDialog = true;
    },
    onCloseMissDialog() {
      this.isShowMissDialog = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.header__filter {
  margin: 10px 0;

  .class__li {
    display: inline-block;
    margin-right: 15px;
    padding: 0 8px;
    border-radius: 3px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;

    &.active,
    &:hover {
      color: #409eff;
    }
  }

  .leftText {
    width: auto;
  }

  .rightText {
    width: auto;
  }

  &.bottomLine {
    border-bottom: 1px solid #e4e8eb;
    padding-bottom: 8px;
  }
}

.scoreInput {
  width: 50px;
  position: relative;
  margin-right: 26px;

  &:after {
    content: '%';
    position: absolute;
    right: -26px;
    top: 0;
    border: 1px solid #dcdfe6;
    border-radius: 0 4px 4px 0;
    width: 26px;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    text-align: center;
    line-height: 30px;
    z-index: 11;
    border-left: none;
  }
}

.sureScoreBtn {
  margin-left: 20px;
  background: #409eff;
  height: 30px;
  padding: 0;
  width: 60px;
  line-height: 30px;
  text-align: center;
}

.noDataText {
  font-size: 14px;
  color: #909399;
}

.statisticData {
  width: 100%;
  height: auto;
  background: #f7fafc;
  border-radius: 8px;
  padding: 20px 0;
  font-size: 16px;
  color: #7e94a8;

  .statistic__header {
    margin-bottom: 20px;
    margin-left: 20px;
  }

  .statisticUl {
    .statisticLi {
      margin-bottom: 4px;
      text-align: center;

      &:nth-child(7) {
        margin-right: 30px;
      }

      .title {
        color: #8f9ca8;
      }

      .score {
        font-size: 20px;
        font-weight: bold;
        color: #3f4a54;
      }
    }
  }
}

.titleInfo {
  font-size: 20px;
  font-weight: bold;
  color: #3f4a54;
}

.titleLine {
  display: inline-block;
  position: relative;
  height: 66px;
  line-height: 66px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;
  min-width: 5em;

  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 21px;
  }
}

.attnBlock {
  .right-list {
    overflow-y: auto;
    background: #fff;

    .attnUl {
      width: 50%;
      height: 100%;
      font-size: 16px;
      font-weight: 400;

      .attnLi {
        width: 100%;
        height: 40px;
        line-height: 40px;

        // &:nth-of-type(even) {
        //   background: #fff;
        // }
        // &:nth-of-type(odd) {
        //   background: #fff;
        // }
        .data-ul {
          // display: inline-block;
          width: 100%;
          margin: 0 50px;
          .data-li {
            display: inline-block;
            float: left;
            color: #81888f;
            min-width: 80px;
            text-align: left;
            margin-right: 10px;
            margin-top: 20px;
            align-items: center;

            &:nth-child(1) {
              min-width: 8em;
              max-width: 8em;
            }

            &:nth-child(2) {
              width: 45px;
            }

            &:nth-child(3) {
              width: 62px;
            }

            &:nth-child(4) {
              width: 100px;
              margin-right: 0;
            }

            &:nth-child(1),
            &:nth-child(2) {
              color: #3f4a54;
            }
          }
        }
      }
    }
  }
}

.attn-table {
  width: 100%;
  background: #f7fafc;
  border: 1px solid #e4e8eb;
  min-height: 100px;
  max-height: 200px;

  .left-tab-ul {
    width: 150px;

    &.active_0 {
      li:nth-child(2) {
        border-top: 1px solid #e4e8eb;
        border-right: 1px solid #e4e8eb;
      }
    }

    &.active_1 {
      li:nth-child(1) {
        border-bottom: 1px solid #e4e8eb;
        border-right: 1px solid #e4e8eb;
      }
    }

    li {
      position: relative;
      width: 100%;
      height: 50%;
      text-align: center;
      background: #f7fafc;
      cursor: pointer;

      &.active {
        background: #fff;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 4px;
          height: 100%;
          background: #409EFF;
        }
      }
    }
  }
}

.mistackBlock {
  .right-filter {
    font-size: 16px;
    line-height: 66px;
    color: #3f4a54;

    .el-button {
      margin-left: 17px;
    }
  }
}

.hideBlock {
  line-height: 370px;
  text-align: center;
  font-size: 20px;
  border: 1px solid #e4e8eb;
}

.distTable {
  height: 368px;

  .left-chart,
  .right-table {
    height: 100%;
    background: #fff;
    border: 1px solid #e4e8eb;
    border-radius: 3px;
  }

  .left-chart {
    margin-right: 30px;
  }

  .right-table {
    width: 494px;

    .left-tab-ul {
      width: 73px;
      height: 100%;
      overflow: auto;

      li {
        width: 100%;
        height: 73px;
        text-align: center;
        background: #f7fafc;
        cursor: pointer;
        font-size: 16px;
        color: #3f4a54;
        line-height: 73px;

        &.active {
          background: #fff;
          color: #409eff;
          line-height: 38px;
        }

        .gradeDetail {
          > p {
            margin: 0;
            text-align: center;
            font-size: 12px;
            color: #3f4a54;
            line-height: 15px;
          }
        }
      }
    }

    .right-list {
      overflow-y: auto;

      .attnUl {
        width: 50%;
        height: 100%;
        font-size: 16px;
        font-weight: 400;
        margin: 0;
        padding: 0;
        list-style: none;

        .attnLi {
          width: 100%;
          height: 32px;
          line-height: 32px;

          &:nth-of-type(even) {
            background: #fafafa;
          }

          &:nth-of-type(odd) {
            background: #fff;
          }

          .data-ul {
            display: inline-block;
            width: auto;
            margin: 0 auto;

            .data-li {
              display: inline-block;
              float: left;
              color: #81888f;
              /*min-width    : 80px;*/
              text-align: left;
              padding: 0 4px;
              box-sizing: border-box;

              &:nth-child(1) {
                width: auto;
              }

              &:nth-child(2) {
                width: 84px;
                text-align: center;
              }

              &:nth-child(3) {
                width: 80px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
.mimstackTable {
  .mistack-table {
    border: 1px solid #e4e8eb;
  }

  .el-table th,
  .el-table td {
    text-align: center;
  }
}

.scoreInput {
  .el-input-group__append {
    padding: 0 10px;
  }

  &.numeric-input-box .numeric-input-inner {
    border-radius: 4px 0 0 4px;
  }
}
</style>
<style lang="scss" scoped>
.stuList {
  margin-left: 20px;
  font-size: 16px;
}

.pull-left {
  margin-left: 20px;

  .selectClass {
    color: #409eff;
    font-size: 16px;

    .el-icon-close {
      margin-left: 5px;
      cursor: pointer;
    }
  }
}

.addCompare {
  width: 136px;
  height: 36px;
  padding: 0;
  text-align: center;
  line-height: 35px;
  background: #409eff;
  border-radius: 4px;
  margin-left: 20px;
  color: #fff;
}
</style>

<template>
  <div>
    <div class="ques-type-avg-box">
      <div class="titleLine">题型均分</div>
      <div class="table-box" v-loading="isLoading">
        <QuesTypeAvgTable :tableData="tableData" :quesGrdAvgScoreMap="quesGrdAvgScoreMap"></QuesTypeAvgTable>
      </div>
      <div class="subjects-box" v-if="quesTypeList.length" style="margin-top: 15px">
        <el-radio-group v-model="chartType">
          <el-radio-button label="avgScore">均分</el-radio-button>
          <el-radio-button label="scoreRate">得分率</el-radio-button>
        </el-radio-group>
        <span class="subject-label ft-14 mgr-10">选择题型:</span>
        <el-select v-model="currentQuesType" placeholder="请选择">
          <el-option v-for="item in quesTypeList" :key="item.quesType" :label="item.typeName" :value="item.quesType">
          </el-option>
        </el-select>
      </div>
      <div class="chart-box" v-loading="isLoading" id="pane-chart">
        <QuesTypeAvgChart
          ref="quesTypeAvgChart"
          :tableData="tableData"
          :currentQuesType="currentQuesType"
          :chartType="chartType"
        ></QuesTypeAvgChart>
      </div>
    </div>
  </div>
</template>

<script>
import QuesTypeAvgChart from '@/components/quesTypeAvg/QuesTypeAvgChart';
import QuesTypeAvgTable from '@/components/quesTypeAvg/QuesTypeAvgTable';
import { getQuesTypeAvg } from '@/service/pexam';

export default {
  name: 'quesTypeAvg',
  props: ['filterData'],
  components: {
    QuesTypeAvgChart,
    QuesTypeAvgTable,
  },
  data() {
    return {
      activeName: 'chart',
      isLoading: false,
      // 数据
      tableData: [],
      // 题目列表
      quesTypeList: [],
      // 当前题目类型
      currentQuesType: '',
      // 图表类型：scoreRate(得分率) | avgScore(均分)
      chartType: 'avgScore',
      // 班级
      clsList: [],

      quesGrdAvgScoreMap: {},
    };
  },
  watch: {
    filterData: {
      immediate: true,
      deep: true,
      handler(newValue, oldValue) {
        this.clsList = this.$sessionSave.get('innerNoRoleClassList');
        this.getQuesTypeAvg();
      },
    },
  },

  mounted() {
    this.clsList = this.$sessionSave.get('innerNoRoleClassList');
  },
  methods: {
    // 题型均分
    async getQuesTypeAvg() {
      try {
        this.tableData = [];
        this.quesTypeList = [];
        const data = await getQuesTypeAvg({
          subjectId: this.filterData.subjectId,
          examId: this.$sessionSave.get('reportDetail').examId,
          abPaper: this.filterData.abPaper,
        });

        if (!data.data.length) {
          this.tableData = [];
          return;
        }
        let typeSortMap = { 8: 0, 1: 1, 3: 2, 6: 4 };
        for (let i = 0; i < data.data.length; i++) {
          let v = data.data[i];

          for (let j = 0; j < v.questionTypes.length; j++) {
            if (v.quesNameSort) {
              let v1 = v.quesNameSort[j];
              v1.sort = typeSortMap[v1.quesType];
            }
            let v2 = v.questionTypes[j];
            if (v2.quesType) {
              v2.sort = typeSortMap[v2.quesType];
            }

            if (v2.questionType) {
              v2.sort = typeSortMap[v2.questionType];
            }
          }
          if (v.quesNameSort) {
            v.quesNameSort.sort((a, b) => a.sort - b.sort);
          }
          v.questionTypes.sort((a, b) => a.sort - b.sort);
        }
        this.quesTypeList = data.data[0].quesNameSort;
        // 在题型列表前添加总分选项
        this.quesTypeList.unshift({
          quesType: 'total',
          typeName: '总分',
          sort: -1,
        });
        this.currentQuesType = this.quesTypeList[0].quesType;
        let tempList = [];
        this.clsList.forEach(item => {
          let cls = data.data.find(q => q.classId == item.id);
          if (cls) {
            cls.classId = item.class_name;
            tempList.push(cls);
          }
        });

        let quesGrdAvgScoreMap = {};
        // 添加总分的均分和得分率
        let totalScore = {
          avgScore: parseFloat(data.data[0].totalAverageScore || 0),
          scoreRate: parseFloat(data.data[0].totalScoreRate || 0),
        };
        quesGrdAvgScoreMap['总分'] = totalScore;

        data.data[0].questionTypes.forEach(item => {
          let score = {
            avgScore: parseFloat(item.avgScore),
            scoreRate: parseFloat(item.scoreRate),
          };
          quesGrdAvgScoreMap[item.typeName] = score;
        });
        this.quesGrdAvgScoreMap = quesGrdAvgScoreMap;
        tempList.unshift(data.data[0]);
        this.tableData = tempList;
      } catch (error) {
        console.log(error);
        this.tableData = [];
      }
    },

    updateFilter(data) {},
    updateData({ isLoading }) {
      // console.log('updatting', isLoading)
      this.isLoading = isLoading;
    },
    handleClick(tab, event) {
      if (tab.name == 'chart') {
        this.$nextTick(() => {
          if (this.$refs.quesTypeAvgChart.quesTypeAvgChart) {
            this.$refs.quesTypeAvgChart.quesTypeAvgChart.resize();
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.ques-type-avg-box {
  //height: calc(100vh - 300px);
  border-radius: 6px;
  padding-bottom: 30px;
  background-color: #fff;

  .chart-box {
    height: 415px;
    background: #ffffff;
    border: 1px solid #e4e8eb;
    border-radius: 3px;
    margin-top: 20px;
  }

  .table-box {
    border: 1px solid #e4e8eb;
    border-radius: 3px;
  }
}
.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;
  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 15px;
  }
}

.subjects-box {
  display: flex;
  align-items: center;
  gap: 16px;
}
</style>
<style lang="scss">
.ques-type-avg-box {
  .el-tabs {
    .el-tabs__header {
      margin: 0 0 1px 4px;
    }
    .el-tabs__nav-wrap::after {
      width: 160px;
    }
    .el-tabs__item {
      width: 80px;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      &.is-top:last-child {
        padding-left: 0;
      }
      &.is-top:nth-child(2) {
        padding-right: 0;
      }
    }
  }
}
</style>

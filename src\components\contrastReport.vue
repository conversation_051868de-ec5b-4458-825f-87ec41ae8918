<template>
  <div>
    <el-dialog
      custom-class="contrast-dialog"
      title="选择对比"
      :visible.sync="dialogFormVisible"
      width="950px"
      @closed="handleClose"
      :close-on-click-modal="false"
    >
      <div class="select_header">
        <year-select ref="yearSelect" v-model="queryData.yearValue" :noAll="true" @change="onChangeYear"></year-select>
        <grade-select
          ref="gradeSelect"
          v-model="queryData.gradeValue"
          :noAll="true"
          :year="startTime"
          @change="onChangeGrade"
        ></grade-select>
        <subject-select
          ref="subjectSelect"
          v-model="queryData.subjectValue"
          :phase-id="phaseId"
          :enterYear="gradeYear"
          :systemCode="gradeSystemCode"
          @change="onChangeSubject"
        ></subject-select>
        <category-select v-model="queryData.categoryValue" @change="onSelectChange"></category-select>

        <el-input
          style="width: 180px"
          v-model="queryData.name"
          placeholder="请输入考试名称搜索"
          @change="onSelectChange"
        >
          <i slot="suffix" class="el-input__icon el-icon-search" @click="onSelectChange"></i>
        </el-input>
      </div>
      <!-- <StatisticFilter></StatisticFilter> -->

      <el-table
        class="contrast-table"
        ref="multipleTable"
        :data="reportList"
        tooltip-effect="dark"
        style="width: 100%"
        max-height="430"
        @row-click="selectRadio"
      >
        <el-table-column width="45">
          <template slot-scope="scope">
            <el-radio class="table-radio" v-model="examVal" :label="scope.row.examId"></el-radio>
          </template>
        </el-table-column>
        <el-table-column prop="examName" label="考试名称"></el-table-column>
        <el-table-column prop="subjectNames" label="学科">
          <template slot-scope="scope">
            <span class="ellipsis" :title="splitArrayToText(scope.row.subjectNames)">
              {{ splitArrayToText(scope.row.subjectNames) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="examTime" label="考试时间" width="180"></el-table-column>
        <el-table-column prop="examType" label="类别" width="100"></el-table-column>
      </el-table>
      <!--分页-->
      <el-pagination
        background
        style="margin: 30px auto"
        :hide-on-single-page="!reportList.length"
        class="text-center"
        layout="prev, pager, next"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-size="pagination.pageSize"
        :total="pagination.total"
      >
      </el-pagination>
      <div slot="footer" class="dialog-footer">
        <el-button @click="$emit('closeDialog')">取 消</el-button>
        <el-button type="primary" @click="addReport">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getContrastTestList } from '@/service/pexam';
import YearSelect from '@/components/SelectComponents/yearSelect.vue';
import SubjectSelect from '@/components/SelectComponents/subjectSelect.vue';
import GradeSelect from '@/components/SelectComponents/gradeSelect.vue';
import CategorySelect from '@/components/SelectComponents/categorySelect.vue';

export default {
  name: 'createReports',
  props: ['contrastId'],
  components: {
    YearSelect,
    SubjectSelect,
    GradeSelect,
    CategorySelect,
  },
  data() {
    return {
      dialogFormVisible: true,
      reportDetail: {},
      reportList: [],
      pagination: {
        page: 1,
        pageSize: 6,
        total: 0,
      },
      examVal: '',

      // 查询参数
      queryData: {
        gradeValue: '',
        subjectValue: '',
        categoryValue: '',
        yearValue: '',
        name: '',
      },
      // 学年列表
      years: [],
      // 学年
      startTime: '',
      // 入学年份
      gradeYear: '',
      // 学制
      gradeSystemCode: '',
      // 学段
      phaseId: '',
    };
  },
  async mounted() {
    this.reportDetail = this.$sessionSave.get('reportDetail');
    this.examVal = this.contrastId || '';
    await this.$nextTick();

    this.years = await this.$refs.yearSelect.initYearsList();
    let yearItem = this.years.find(item => item.id == this.reportDetail.schoolYearId);
    if (!yearItem) {
      yearItem = this.years[0];
    }
    this.queryData.yearValue = yearItem.id;
    this.startTime = yearItem.startTime;

    await this.$nextTick();

    let gradeList = await this.$refs.gradeSelect.initGrade();
    this.queryData.gradeValue = Number(this.reportDetail.gradeCode);
    const grade = gradeList.find(item => item.id == this.queryData.gradeValue);
    this.gradeYear = grade?.year;
    this.phaseId = grade?.phaseId || '';
    this.gradeSystemCode = grade?.systemCode || '';

    await this.$nextTick();

    let subjectList = await this.$refs.subjectSelect.initSubject();
    this.queryData.subjectValue = subjectList[0].id;
    this.getContrastTestList();
  },
  methods: {
    // 获取对比考试列表
    async getContrastTestList() {
      try {
        let gradeId = this.queryData.gradeValue;
        let subjectIds = this.queryData.subjectValue;
        let categoryId = this.queryData.categoryValue;
        let year = this.queryData.yearValue;
        let name = this.queryData.name;

        const data = await getContrastTestList({
          examId: this.reportDetail.examId,
          schoolId: this.$sessionSave.get('schoolInfo').id,
          gradeId,
          subjectIds,
          categoryId,
          year,
          name,
          page: this.pagination.page,
          pageSize: this.pagination.pageSize,
        });
        this.reportList = data.data.list;
        this.pagination.total = data.data.total;
      } catch (err) {
        console.error(err);
        this.reportList = [];
      }
    },
    // 点击行选中当前对比考试
    selectRadio(row) {
      this.examVal = row.examId;
    },
    // 分页查询
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getContrastTestList();
    },
    // 关闭弹窗
    async handleClose() {
      this.$emit('closeDialog');
    },
    // 创建，编辑考试报告
    addReport() {
      // 编辑考试报告
      this.$message({
        message: `选择对比成功！`,
        type: 'success',
        duration: 1000,
      });

      let reportObj = this.reportList.filter(item => {
        return item.examId == this.examVal;
      })[0];

      this.dialogFormVisible = false;
      this.$emit('closeDialog');
      this.$emit('updateData', reportObj);
    },

    // 筛选
    onSelectChange() {
      this.getContrastTestList();
    },

    // 更换年级
    async onChangeGrade(val, grade) {
      this.queryData.gradeValue = val;
      this.gradeYear = grade?.year;
      this.gradeSystemCode = grade?.systemCode || '';
      this.phaseId = grade?.phaseId || '';
      await this.$nextTick();
      let list = await this.$refs.subjectSelect.initSubject();
      this.queryData.subjectValue = list[0]?.id;
      this.getContrastTestList();
    },

    // 更换学科
    onChangeSubject(val) {
      this.queryData.subjectValue = val;
      this.getContrastTestList();
    },
    // 更换学年
    async onChangeYear(val) {
      const yearItem = this.years.find(item => item.id == val);
      this.queryData.yearValue = val;
      this.startTime = yearItem.startTime;

      await this.$nextTick();
      let gradeList = await this.$refs.gradeSelect.initGrade();
      this.onChangeGrade(gradeList[0]?.id, gradeList[0]);
    },

    splitArrayToText(val) {
      if (!val || !val.length) return '--';
      return val.join(' / ');
    },
  },
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  margin-right: 10px;
}

.dialog-footer button span {
  font-size: 16px;
}

.select_header {
  padding: 10px 20px;
}
</style>
<style lang="scss">
.contrast-table {
  .el-table__body-wrapper {
    .el-table__row {
      cursor: pointer;
    }
  }
}
.subjectCheckBox {
  line-height: 30px;
  .el-checkbox {
    margin-right: 20px;
    &.el-checkbox + .el-checkbox {
      margin-left: 0;
    }
  }
}

.table-radio {
  margin-left: 10px;
  .el-radio__inner {
    width: 18px;
    height: 18px;
  }
}

.dialog-title {
  line-height: 54px;
}

.table-radio {
  .el-radio__label {
    display: none;
  }
}

.contrast-dialog {
  .el-dialog__body {
    padding: 0;
  }
}
</style>

<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-04-15 14:40:54
 * @LastEditors: 小圆
-->
<template>
  <el-table
    class="total-rank-table"
    stripe
    :data="tableData"
    :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
    style="width: 100%"
    v-sticky-table="0"
  >
    <el-table-column align="center" prop="className" label="班级" width="140">
      <template slot-scope="scope">
        <span>{{ scope.row.className }}</span>
      </template>
    </el-table-column>
    <el-table-column
      v-for="(item, index) in targetData.totalRankSegment"
      prop=""
      :label="`${item.start}` == -1 ? `后${item.end}` : `前${item.end}`"
      align="center"
      :key="index"
    >
      <template slot-scope="scope">
        <span>{{ scope.row.score[index] }}</span>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'TotalRankTable',
  props: ['tableData', 'targetData'],
  data() {
    return {
    };
  },
  mounted() {},
};
</script>

<style lang="scss" scoped></style>

<style lang="scss">
//.total-rank-table {
//  .el-table__row>td{
//    border: none;
//  }
//  .el-table::before {//去掉最下面的那一条线
//    height: 0px;
//  }
//}
</style>

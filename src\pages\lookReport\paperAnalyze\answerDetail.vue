<template>
  <div class="answer-detail-box" v-loading="isLoading">
    <div class="titleLine">
      <span> 作答详情 </span>
    </div>
    <el-tooltip placement="bottom-start" v-if="doQuesList.length">
      <div slot="content">
        <div v-html="getDoQuesChoiceTipHtml()"></div>
      </div>
      <span class="mixin-dochoice-tip">{{ getDoQuesChoiceTipText() }}</span>
    </el-tooltip>
    <!-- <div v-if="doQuesList.length" class="mixin-dochoice-tip" v-html="getDoQuesChoiceTipHtml()"></div> -->
    <el-table
      class="answer-detail-table"
      :data="tableData"
      stripe
      @sort-change="sortTableByAvg"
      :header-cell-class-name="'table-sort-cell'"
      :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
      style="width: 100%"
      v-drag-table
      v-sticky-table="0"
    >
      <el-table-column align="center" prop="quesNoDesc" label="题号">
        <template slot-scope="scope">
          <div class="small-no-cell">
            {{ scope.row.quesNoDesc }}
            <div class="detail-square" v-if="getSpecialTypeTag(scope.row.specialTypeId)">
              <span class="detail-square-word">
                {{ getSpecialTypeTag(scope.row.specialTypeId) }}
              </span>
            </div>
            <span v-if="getDoChoiceQuesCountTextByQuesNo(scope.row.quesNo)">
              (<span class="mixin-dochoice-text">{{ getDoChoiceQuesCountTextByQuesNo(scope.row.quesNo) }}</span
              >)
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="quesType" label="题型">
        <template slot-scope="scope">
          <span>{{ scope.row.typeName ? scope.row.typeName : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="fullScore" label="分值"> </el-table-column>
      <el-table-column align="center" label="平均分">
        <template slot-scope="scope">
          <span v-if="getDoChoiceQues(scope.row.quesNo) && (scope.row.grdTotalNum == 0 || scope.row.clsTotalNum == 0)">
            --
          </span>

          <span v-else-if="scope.row.grdAvgScore != null">
            {{ scope.row.grdAvgScore === '' ? '-' : scope.row.grdAvgScore }}
          </span>
          <span v-else>
            {{ scope.row.clsAvgScore === '' ? '-' : scope.row.clsAvgScore }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="区分度">
        <template slot-scope="scope">
          <span v-show="!isNaN(Number(getDistinguishValue(scope.row)))">
            {{ formatDistinguish(scope.row) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :prop="getScoreRateProp()"
        sortable="custom"
        class-name="head-td-cell"
        label="得分率"
      >
        <template slot-scope="scope">
          <span v-if="shouldShowEmptyScore(scope.row)"> -- </span>
          <span v-else-if="!isNaN(getScoreRateValue(scope.row))">
            {{ formatScoreRate(scope.row) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        :prop="getFullNumProp()"
        sortable="custom"
        class-name="head-td-cell"
        label="答对人数"
      >
        <template slot-scope="scope">
          <span v-if="isSpecialQuestionType(scope.row.quesType) && scope.row.rightAnswer">
            {{ scope.row.rightAnswer }}:
          </span>
          <span>{{ getFullNumValue(scope.row) }}人</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="accuracy" sortable="custom" class-name="head-td-cell" label="正确率">
        <template slot-scope="scope">
          <span>{{ scope.row.accuracy }}%</span>
        </template>
      </el-table-column>
      <el-table-column align="left" class-name="head-td-cell" label="答错人数" width="160">
        <template slot-scope="scope">
          <div v-for="(item, index) in scope.row.data" :key="index">
            <span
              >{{ item.text + ':' }} <span type="text">{{ item.num || 0 }}</span
              >人
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="left" class-name="head-td-cell" label="错选率" width="160">
        <template slot-scope="scope">
          <div v-for="(item, index) in scope.row.data" :key="index">
            <span
              ><span type="text">{{ item.errRate || 0 }}%</span>
            </span>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getStuAnswerDetails, listExamStu } from '@/service/pexam';
import NoData from '@/components/noData.vue';
import DochoiceMixin from '../mixin/DochoiceMixin.vue';

export default {
  components: {
    NoData,
  },
  mixins: [DochoiceMixin],
  name: 'answerDetail',
  props: ['filterData'],
  data() {
    return {
      tableData: [],
      // 列对象数组
      column: [],
      // 学生列表
      stuList: [],
      // 是否正在加载
      isLoading: false,
    };
  },
  watch: {
    filterData: {
      immediate: true,
      deep: true,
      handler(newValue, oldValue) {
        this.getStuAnswerDetails(this.filterData.classId);
        this.getPaperChoice(this.$sessionSave.get('reportDetail').examId, this.filterData.subjectId);
      },
    },
  },

  mounted() {},
  methods: {
    // 获取特殊类型标签
    getSpecialTypeTag(specialTypeId) {
      if (specialTypeId == 1) {
        return '送';
      } else if (specialTypeId == 2) {
        return '零';
      } else if (specialTypeId == 3) {
        return '附';
      } else if (specialTypeId == 5) {
        return 'AI';
      }
      return '';
    },

    // 作答详情列表
    async getStuAnswerDetails(id) {
      try {
        const data = await getStuAnswerDetails({
          subjectId: this.filterData.subjectId,
          examId: this.$sessionSave.get('reportDetail').examId,
          classId: id,
          abPaper: this.filterData.abPaper,
        });
        if (!data.data.length) {
          this.tableData = [];
          return;
        }
        this.tableData = data.data;
        // this.getExamStu();
      } catch (error) {
        console.log(error);
        this.tableData = [];
      }
    },

    // 设置列
    setQuesColumn() {
      const stuListSet = new Set(this.stuList.map(stu => stu.id));
      this.stuList.forEach(stu => (stu.score = {}));
      this.column = this.tableData.map(item => {
        const columnItem = {
          quesNo: item.quesNo,
          quesNoDesc: item.quesNoDesc,
          fullScore: item.fullScore,
          stuInfo: item.stuInfo,
          tQuesNo: item.tQuesNo,
          score: {},
        };
        columnItem.stuInfo.forEach(stu => {
          let stuId = stu[0];
          let score = stu[1];
          let isDo = stu[3];
          if (stuListSet.has(stuId)) {
            let stu = this.stuList.find(stu => stu.id == stuId);
            stu.score[item.tQuesNo] = isDo !== '0' ? score : '--';
          }
        });
        return columnItem;
      });
    },
    updateData({ isLoading }) {
      // console.log('updatting', isLoading)
      this.isLoading = isLoading;
    },
    updateFilter(data) {},
    // 按照平均分排序
    sortTableByAvg({ prop, order }) {
      console.log(prop, order);
      let tableData = this.tableData;

      if (order == 'ascending') {
        // 降序
        tableData = tableData.sort((a, b) => a[prop] - b[prop]);
      } else if (order == 'descending') {
        // 升序
        tableData = tableData.sort((a, b) => b[prop] - a[prop]);
      } else {
        this.tableData = this.$deepClone(this.tableData);
      }
    },

    // 打开详情
    openDetail() {
      this.$message.success('TODO');
    },

    // 获取区分度值
    getDistinguishValue(row) {
      return this.filterData.classId === '' ? row.grdDistinguish : row.clsDistinguish;
    },

    // 格式化区分度显示
    formatDistinguish(row) {
      const value = this.getDistinguishValue(row);
      return value === '' ? '-' : Number(value).toFixed(2);
    },

    // 获取得分率属性名
    getScoreRateProp() {
      return this.filterData.classId !== '' ? 'clsScoreRate' : 'grdScoreRate';
    },

    // 获取得分率值
    getScoreRateValue(row) {
      return this.filterData.classId !== '' ? row.clsScoreRate : row.grdScoreRate;
    },

    // 判断是否显示空得分率
    shouldShowEmptyScore(row) {
      const totalNum = this.filterData.classId !== '' ? row.clsTotalNum : row.grdTotalNum;
      return this.getDoChoiceQues(row.quesNo) && totalNum == 0;
    },

    // 格式化得分率显示
    formatScoreRate(row) {
      const value = this.getScoreRateValue(row);
      return Number(value).toFixed(2) + '%';
    },

    // 获取答对人数属性名
    getFullNumProp() {
      return this.filterData.classId !== '' ? 'clsFullNum' : 'grdFullNum';
    },

    // 获取答对人数值
    getFullNumValue(row) {
      const value = this.filterData.classId !== '' ? row.clsFullNum : row.grdFullNum;
      return value || 0;
    },

    // 判断是否为特殊题型
    isSpecialQuestionType(quesType) {
      return '158'.indexOf(quesType) >= 0;
    },
  },
};
</script>

<style lang="scss" scoped>
.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;

  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 15px;
  }
}
.small-no-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}
.small-ques-no {
  position: relative;
  display: flex;
  justify-content: flex-start;
}
.small-ques-mark {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  border: 1px solid;
  width: 20px;
  height: 20px;
  color: #e6a23c;
  background: #fdf6ec;
  border-color: #f5dab1;
  border-radius: 5px;
}

.ques-detail-table {
  border: 1px solid #e4e8eb;
}
.detail-square {
  width: 0;
  height: 0;
  border: 16px solid transparent;
  border-top: 16px solid #fdf6ec;
  border-left: 16px solid #fdf6ec;
  z-index: 100;
  border-radius: 5px 0 0 0;
  position: absolute;
  left: 0;
  top: 0;
  .detail-square-word {
    position: absolute;
    left: -12px;
    top: -16px;
    color: #e6a23c;
    font-size: 13px;
    white-space: nowrap;
  }
}
</style>

<template>
    <el-dialog title="修改选项个数" :visible.sync="dialogVisible" width="500px" :close-on-click-modal="false" @close="handleCancel"
        :append-to-body="true" class="modify-options-dialog">
        <div class="dialog-content" v-if="selectedQuestion">
            <div class="form-item">
                <div class="label">题目名称及题号：</div>
                <el-select v-model="selectedQuestion" placeholder="请选择题目" class="question-select" value-key="id"
                    @change="handleQuestionChange" @visible-change="handleQuestionVisibleChange">
                    <el-option v-for="item in questionList" :key="item.value"
                        :label="item.quesName + '-' + item.quesNos" :value="item"></el-option>
                </el-select>
            </div>

            <div class="form-item">
                <div class="label">选项个数修改为：</div>
                <el-select v-model="selectedQuestion.optionCount" placeholder="请选择选项个数" class="options-select"
                    @change="handleOptionChange">
                    <el-option v-for="item in optsList" :key="item" :label="item" :value="item"></el-option>
                </el-select>
            </div>

            <div class="form-item">
                <div class="label">框选区域：</div>
                <div class="image-container">
                    <div class="image-clipper" :style="imageBoxStyle" ref="clipperRef">
                        <img :src="pageUrl" class="image-item" :style="imageStyle">
                        <div v-for="(item, index) in dragItems" :key="index" class="drag-item" :style="{
                            left: item.x + 'px',
                            top: item.y + 'px',
                            width: item.width + 'px',
                            height: item.height + 'px'
                        }" @mousedown="startDrag($event, index)">
                            {{ item.text }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="tip-text">
                定位点调整完成后请<span class="highlight-text">重新识别所有扫描批次</span>。
            </div>
        </div>
        <el-empty v-else description="暂无题目"></el-empty>
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleConfirm">确认</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { getPaperImages } from '@/service/xueban';
export default {
    name: 'SetChoiceOptsNumDialog',
    props: {
        modalVisible: {
            type: Boolean,
            default: false
        },
        objectQuesData: {
            type: Array,
            default: () => []
        },
        cardInfo: {
            type: Object,
            default: () => { }
        },
        paperNo: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            dialogVisible: this.modalVisible,
            questionList: [],
            cardPonits: null,
            selectedQuestion: null,
            optionsCount: 4,
            optsList: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26],
            imgList: [],
            pageUrl: '',
            point: null,
            //图片分辨率
            imgDpi: 4.173,
            dragItems: [],
            isDragging: false,
            currentDragIndex: null,
            startX: 0,
            startY: 0,
            marginNum: 5
        };
    },
    computed: {
        imageBoxStyle() {
            return {
                width: `${(Number(this.point.pos[2]) + this.marginNum * 3) * this.imgDpi}px`,
                height: `${(Number(this.point.pos[3]) + this.marginNum * 2) * this.imgDpi}px`
            };
        },
        imageStyle() {
            return {
                top: `-${(this.point.pos[1] - this.marginNum) * this.imgDpi}px`,
                left: `-${(this.point.pos[0] - this.marginNum) * this.imgDpi}px`
            }
        }
    },
    async created() {
        this.questionList = JSON.parse(JSON.stringify(this.objectQuesData));
        this.cardPonits = JSON.parse(JSON.stringify(this.cardInfo));
        await this.getImgUrls();
        this.handleQuestionChange(this.questionList[0]);
    },
    mounted() {
        // 添加全局鼠标事件监听
        document.addEventListener('mousemove', this.onDrag);
        document.addEventListener('mouseup', this.stopDrag);
    },
    beforeUnmount() {
        // 移除事件监听
        document.removeEventListener('mousemove', this.onDrag);
        document.removeEventListener('mouseup', this.stopDrag);
    },
    methods: {
        /**
         * @name:获取生成的pdf图片
         */
        async getImgUrls() {
            const params = {
                paper_no: this.paperNo,
                show_answer: false,
            };
            const res = await getPaperImages(params);
            if (res.code == 1) {
                this.imgList = res.data;
            } else {
                this.imgList = [];
            }
        },
        updateDataToOrgin() {
            for (let i = 0; i < this.cardPonits.pages.length; i++) {
                let page = this.cardPonits.pages[i];
                for (let j = 0; j < page.length; j++) {
                    let item = page[j];
                    if (item.question_id == this.selectedQuestion.id) {
                        item.option_list = this.dragItems.map(q => [q.x / this.imgDpi - this.marginNum + Number(this.point.pos[0]), q.y / this.imgDpi - this.marginNum + Number(this.point.pos[1]), q.width / this.imgDpi, q.height / this.imgDpi]);
                        item.option_len = this.dragItems.length;
                        const [firstX, firstY] = item.option_list[0];
                        const [lastX, lastY, lastW, lastH] = item.option_list[item.option_list.length - 1];

                        // 校正左上角坐标
                        if (item.pos[0] > firstX) {
                            item.pos[0] = firstX - this.marginNum;
                        }
                        if (item.pos[1] > firstY) {
                            item.pos[1] = firstY - this.marginNum;
                        }

                        // 校正宽高
                        const minWidth = lastX + lastW - item.pos[0];
                        if  (item.pos[2] < minWidth) {
                            item.pos[2] = minWidth;
                        }
                        // item.pos[2] = firstX - item.pos[0] + minWidth;
                        const minHeight = lastY + lastH - item.pos[1];
                        if  (item.pos[3] < minHeight) {
                            item.pos[3] = minHeight;
                        }
                        // item.pos[3] = firstY - item.pos[1] + minHeight;
                        return;
                    }
                }
            }
        },
        handleQuestionVisibleChange(isShow){
            if(isShow){
                this.updateDataToOrgin()
            }
        },
        handleQuestionChange(val) {
            this.selectedQuestion = val;
            for (let i = 0; i < this.cardPonits.pages.length; i++) {
                let page = this.cardPonits.pages[i];
                for (let j = 0; j < page.length; j++) {
                    let item = page[j];
                    if (item.question_id == val.id) {
                        this.point = {
                            pos: item.pos,
                            option_list: item.option_list
                        };
                        this.dragItems = this.point.option_list.map((q, i) => {
                            return {
                                x: (Number(q[0]) - Number(this.point.pos[0]) + this.marginNum) * this.imgDpi,
                                y: (Number(q[1]) - Number(this.point.pos[1]) + this.marginNum) * this.imgDpi,
                                width: q[2] * this.imgDpi,
                                height: q[3] * this.imgDpi,
                                text: String.fromCharCode(65 + i)
                            }
                        })
                        this.pageUrl = this.imgList[i];
                        return;
                    }
                }
            }
        },
        handleOptionChange(val) {
            if (val > this.dragItems.length) {
                for (let i = this.dragItems.length; i < val; i++) {
                    let lastItem = this.dragItems[this.dragItems.length - 1];
                    if(!lastItem){
                        lastItem = {
                            x: 0,
                            y: 0,
                            width: 20,
                            height: 10
                        }
                    }
                    this.dragItems.push({
                        x: lastItem.x + lastItem.width,
                        y: lastItem.y,
                        width: lastItem.width,
                        height: lastItem.height,
                        text: String.fromCharCode(65 + i)
                    })
                }
            }
            if (val < this.dragItems.length) {
                this.dragItems = this.dragItems.slice(0, val);
            }
            // 检查答案是否在有效选项范围内，如果不在则清空答案
            const answerIndex = this.selectedQuestion.answer ? this.selectedQuestion.answer.charCodeAt(0) - 65 : -1;
            if (answerIndex < 0 || answerIndex >= this.dragItems.length) {
                this.selectedQuestion.answer = "";
            }
        },
        handleCancel() {
            this.dialogVisible = false;
            this.$emit('close-dialog');
        },
        handleConfirm() {
            this.updateDataToOrgin();
            this.$emit('confirm', {
                question: this.questionList,
                cardPonits: this.cardPonits
            });
            this.dialogVisible = false;
        },

        // 开始拖拽
        startDrag(event, index) {
            this.isDragging = true;
            this.currentDragIndex = index;
            this.startX = event.clientX;
            this.startY = event.clientY;
            event.preventDefault();
        },

        // 拖拽中
        onDrag(event) {
            if (!this.isDragging || this.currentDragIndex === null) return;
            if (!event.target.className.includes('drag-item')) return;
            // 计算移动距离
            const dx = event.clientX - this.startX;
            const dy = event.clientY - this.startY;

            // 更新起始位置
            this.startX = event.clientX;
            this.startY = event.clientY;

            // 更新元素位置（基于图片坐标系）
            const item = this.dragItems[this.currentDragIndex];
            let newX = item.x + dx;
            let newY = item.y + dy;

            // 限制在裁剪区域内
            const clipperRect = this.$refs.clipperRef.getBoundingClientRect();
            const maxX = Math.max(0, clipperRect.width - event.target.offsetWidth);
            const maxY = Math.max(0, clipperRect.height - event.target.offsetHeight);
            if (newX < 0) {
                newX = 0;
            } else if (newX > maxX) {
                newX = maxX;
            }
            if (newY < 0) {
                newY = 0;
            } else if (newY > maxY) {
                newY = maxY;
            }

            // 更新位置
            this.dragItems[this.currentDragIndex].x = newX;
            this.dragItems[this.currentDragIndex].y = newY;
        },

        // 停止拖拽
        stopDrag() {
            this.isDragging = false;
            this.currentDragIndex = null;
        }
    }
};
</script>

<style lang="scss" scoped>
.modify-options-dialog {
    .dialog-content {
        padding: 0 20px;
    }

    .form-item {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .label {
            width: 150px;
            font-size: 14px;
            color: #606266;
            text-align: right;
            padding-right: 12px;
        }

        .question-select,
        .options-select {
            width: 200px;
        }
    }


    .tip-text {
        font-size: 14px;
        color: #606266;
        margin-left: 26px;

        .highlight-text {
            color: #f56c6c;
            font-weight: bold;
        }
    }

    .dialog-footer {
        text-align: right;
        padding-top: 10px;
    }
}

.image-container {
    .image-clipper {
        outline: 1px solid #e1e1e1;
        overflow: hidden;
        position: relative;

        img {
            height: 1238px;
            width: auto;
            position: absolute;
        }
    }
}

.image-clipper {
    position: relative;
    overflow: hidden;
}

.drag-item {
    position: absolute;
    border: 1px solid #409EFF;
    cursor: move;
    z-index: 10;
    color: #409EFF;
    text-align: center;
}
</style>

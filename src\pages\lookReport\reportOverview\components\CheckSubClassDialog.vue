<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-07-06 14:23:13
 * @LastEditors: 小圆
-->
<template>
  <el-dialog title="导出" :visible.sync="isDialogVisible" width="30%" @closed="closed">
    <div class="exprot-box">
      <div class="titleLine">导出学科</div>
      <el-checkbox
        style="margin-left: 20px"
        :indeterminate="check.subject.isIndeterminate"
        v-model="check.subject.checkAll"
        @change="val => handleCheckAllChange(val, 'subject')"
        >全选</el-checkbox
      >
      <el-checkbox-group v-model="check.subject.checkList" @change="val => handleCheckedChange(val, 'subject')">
        <el-checkbox v-for="item in check.subject.list" :label="item.id" :key="item.id">{{ item.name }}</el-checkbox>
      </el-checkbox-group>
    </div>

    <div class="exprot-box" v-if="canIndicator">
      <div class="titleLine">指标</div>
      <el-checkbox
        style="margin-left: 20px"
        :indeterminate="check.indicator.isIndeterminate"
        v-model="check.indicator.checkAll"
        @change="val => handleCheckAllChange(val, 'indicator')"
        >全选</el-checkbox
      >
      <el-checkbox-group
        class="checkbox-type-group"
        v-model="check.indicator.checkList"
        @change="val => handleCheckedChange(val, 'indicator')"
      >
        <el-checkbox class="checkbox" v-for="item in check.indicator.list" :label="item.value || item.label" :key="item.value || item.label">{{
          item.label
        }}</el-checkbox>
      </el-checkbox-group>
    </div>

    <div class="exprot-box" v-if="showQType">
      <div class="titleLine">分数来源</div>
      <div>
        <el-radio-group v-model="check.qType">
          <el-radio :label="0">得分</el-radio>
          <el-radio :label="1">赋分</el-radio>
        </el-radio-group>
      </div>
    </div>

    <!-- <div class="exprot-box">
      <div class="titleLine">导出班级</div>
      <el-checkbox
        style="margin-left: 20px"
        :indeterminate="check.class.isIndeterminate"
        v-model="check.class.checkAll"
        @change="val => handleCheckAllChange(val, 'class')"
        >全选</el-checkbox
      >
      <el-checkbox-group
        v-model="check.class.checkList"
        @change="val => handleCheckedChange(val, 'class')"
      >
        <el-checkbox v-for="item in check.class.list" :label="item.id" :key="item.id">{{
          item.class_name
        }}</el-checkbox>
      </el-checkbox-group>
    </div> -->

    <span slot="footer" class="dialog-footer">
      <el-button @click="isDialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component
export default class CheckSubClassDialog extends Vue {
  @Prop({ type: Boolean, default: false }) canAllSubject: boolean;
  @Prop({ type: Boolean, default: false }) canQType: boolean;
  @Prop({ type: Boolean, default: false }) canIndicator: boolean;
  @Prop({ type: Array, default: () => [] }) indicatorList: any[];
  @Prop({ type: Array, default: () => [] }) defaultIndicators: any[];

  // 是否显示对话框
  isDialogVisible: boolean = false;

  // 筛选列表
  check = {
    subject: {
      list: [],
      checkList: [],
      isIndeterminate: false,
      checkAll: false,
    },
    class: {
      list: [],
      checkList: [],
      isIndeterminate: false,
      checkAll: false,
    },
    indicator: {
      list: [],
      checkList: [],
      isIndeterminate: false,
      checkAll: false,
    },
    // 分数来源 0:得分 1:赋分
    qType: 0,
  };

  // 是否显示分数来源
  get showQType() {
    return (
      this.canQType &&
      this.check.subject.checkList.some(item => {
        const subject = this.check.subject.list.find(sub => sub.id === item);
        return subject?.isRule;
      })
    );
  }

  mounted() {
    this.isDialogVisible = true;
    this.check.qType = 0;
    this.check.subject.list = this.$sessionSave.get('innerSubjectList');
    if (this.canAllSubject) {
      let allSubjectItem = this.check.subject.list.find(item => item.id === '');
      if (allSubjectItem) {
        allSubjectItem.name = '总分';
        allSubjectItem.id = 'all';
      }
    } else {
      this.check.subject.list = this.check.subject.list.filter(item => item.id !== '');
    }

    // 初始化指标列表
    if (this.canIndicator && this.indicatorList.length > 0) {
      this.check.indicator.list = this.indicatorList;
      if (this.defaultIndicators.length > 0) {
        this.check.indicator.checkList = this.$deepClone(this.defaultIndicators);
      } 
      this.check.indicator.checkAll = this.check.indicator.checkList.length === this.check.indicator.list.length;
      this.check.indicator.isIndeterminate = this.check.indicator.checkList.length > 0 && this.check.indicator.checkList.length < this.check.indicator.list.length;
    }

    if (this.check.subject.list.length == 1) {
      this.check.subject.checkList = this.check.subject.list.map(item => item.id);
      if (!this.showQType && !this.canIndicator) {
        this.confirm();
      }
    }
  }

  handleCheckAllChange(value, type: 'subject' | 'class' | 'indicator') {
    if (type === 'indicator') {
      this.check[type].checkList = value ? this.check[type].list.map(item => item.value || item.label) : [];
    } else {
      this.check[type].checkList = value ? this.check[type].list.map(item => item.id) : [];
    }
    this.check[type].isIndeterminate = false;
  }

  handleCheckedChange(value, type: 'subject' | 'class' | 'indicator') {
    let checkedCount = value.length;
    this.check[type].checkList = value;
    this.check[type].checkAll = checkedCount === this.check[type].list.length;
    this.check[type].isIndeterminate = checkedCount > 0 && checkedCount < this.check[type].list.length;
  }

  closed() {
    this.$emit('closed');
  }

  confirm() {
    if (!this.check.subject.checkList.length) {
      return this.$message.warning('请选择学科');
    }
    if (this.canIndicator && !this.check.indicator.checkList.length) {
      return this.$message.warning('请选择指标');
    }
    // if (!this.check.class.checkList.length) {
    //   return this.$message.warning('请选择班级');
    // }
    const result: any = {
      subjectCheckList: this.check.subject.checkList,
      qType: this.check.qType,
      // classCheckList: this.check.class.checkList,
    };
    
    if (this.canIndicator) {
      result.indicatorCheckList = this.check.indicator.checkList;
    }
    
    this.$emit('confirm', result);
    this.isDialogVisible = false;
  }
}
</script>

<style scoped lang="scss">
.exprot-box {
  margin-bottom: 20px;
}

.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;

  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    margin: auto;
  }
}

.checkbox-type-group {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  column-gap: 10px;
  row-gap: 10px;
}
</style>

<template>
  <div class="five-rate-box" v-loading="isLoading">
    <div class="five-rate__header clearfix">
      <span class="titleLine"> <span v-if="type === 'campus'">校区</span>一分五率 </span>

      <el-popover placement="top-start" title="" width="500" trigger="hover">
        <div style="line-height: 25px; font-size: 13px">
          {{
            type == 'campus'
              ? '超均率=（校区均分-全校均分）/全校均分*100%'
              : '超均率=（班级均分-年级均分）/年级均分*100%'
          }}
        </div>
        <div style="line-height: 25px; font-size: 13px" v-if="type != 'campus'">
          增长率=本次考试超均率-对比考试超均率
        </div>
        <i class="el-icon-question" slot="reference"></i>
      </el-popover>
      <div class="five-rate-btns">
        <contrast-report-btn
          v-if="contrastEnable"
          label="查看增长率"
          :examName="contrastObj.examName"
          :examId="contrastObj.examId"
          @updateData="updateContrastInfo"
        >
        </contrast-report-btn>
      </div>
    </div>

    <div class="group__header">
      <div class="group__header--left">
        <el-radio-group v-if="type == 'default'" class="radio-group" v-model="queryType" @input="getTableData">
          <el-radio-button :label="1">班级</el-radio-button>
          <el-radio-button :label="2">教师</el-radio-button>
        </el-radio-group>
        <el-popover
          placement="bottom"
          width="350"
          trigger="click"
          popper-class="popover"
          v-model="isPopoverVisible"
          @show="onShowTypePopover"
        >
          <el-checkbox-group v-model="tempCheckTypeList">
            <el-checkbox
              v-for="item in getFilterTypeList"
              class="checkbox"
              :key="item.value"
              :label="item.value"
              :disabled="item.disabled"
              size="small"
              >{{ item.label }}</el-checkbox
            >
          </el-checkbox-group>
          <div class="popover-footer">
            <el-button size="small" @click="isPopoverVisible = false">取消</el-button>
            <el-button type="primary" size="small" @click="handleCheckType">确定</el-button>
          </div>
          <el-button class="filtrate-btn" slot="reference" type="text"
            >指标筛选 <i class="el-icon-arrow-down"></i
          ></el-button>
        </el-popover>
        <div class="tip">
          注：平均分低于{{ type == 'campus' ? '校区' : '班级' }}标红显示；超均率{{
            type == 'campus' ? '' : '、增长率'
          }}为负数标红显示
        </div>
      </div>
      <div class="group__header--right">
        <el-button style="margin-left: 20px" type="primary" @click="showExportScoreRate"> 导出 </el-button>
      </div>
    </div>

    <div class="table-box">
      <FiveRateTable
        v-if="isInit"
        :type="type"
        :queryType="queryType"
        :tableData="tableData"
        :contrastId="contrastObj.examId"
        :filterData="filterData"
        :checkTypeList="checkTypeList"
      ></FiveRateTable>
    </div>

    <div class="chart-box" id="pane-chart">
      <div class="titleLine"><span v-if="type === 'campus'">校区</span>均分</div>
      <AvgChart
        v-if="isInit"
        :key="filterData.subjectId"
        class="avg-chart"
        ref="avgChart"
        :type="type"
        :isShowFiveAll="isShowFiveAll"
        :queryType="queryType"
        :tableData="tableData"
        :filterData="filterData"
      ></AvgChart>
    </div>

    <div class="chart-box" v-if="contrastObj.examId">
      <div class="titleLine">增长率</div>
      <div style="width: 100%; height: 400px" ref="growthChart" v-if="data.length"></div>
      <no-data v-else></no-data>
    </div>

    <div class="chart-box">
      <div class="titleLine"><span v-if="type === 'campus'">校区</span>五率</div>
      <FiveRateChart
        ref="fiveRateChart"
        v-if="isInit"
        :type="type"
        :queryType="queryType"
        :tableData="tableData"
        :filterData="filterData"
      ></FiveRateChart>
    </div>

    <CheckSubClassDialog
      v-if="isDialogVisible"
      :canAllSubject="true"
      :canQType="true"
      @closed="isDialogVisible = false"
      @confirm="exportScoreRate"
    />
  </div>
</template>

<script>
import ContrastReportBtn from '@/components/contrastReportBtn.vue';
import AvgChart from '@/components/fiveRate/AvgChart.vue';
import FiveRateChart from '@/components/fiveRate/FiveRateChart.vue';
import FiveRateTable from '@/components/fiveRate/FiveRateTable.vue';
import {
  getNewReportCard,
  getScoreRate,
  getTeaScoreRate,
  exportScoreRate,
  getHisExam,
  getCampusScoreRateAPI,
} from '@/service/pexam';
import { findComplement, getToRoles } from '@/utils/index';
import { getToken } from '@/service/auth';
import { FuncIdEnum, getFuncEnable, indicatorManager } from '@/utils/examReportUtils';
import UserRole from '@/utils/UserRole';
import CheckSubClassDialog from './components/CheckSubClassDialog.vue';
import NoData from '@/components/noData.vue';

export default {
  name: 'fiveRate',
  components: {
    AvgChart,
    FiveRateChart,
    FiveRateTable,
    ContrastReportBtn,
    CheckSubClassDialog,
    NoData,
  },
  data() {
    return {
      isLoading: false,
      // 是否初始化成功
      isInit: false,
      // 表格数据
      tableData: [],
      // 对比考试对象
      contrastObj: {
        examName: '',
        examId: '',
      },
      // 查询类型 1：班级 2：教师
      queryType: 1,
      // 是否显示全部数据
      isShowFiveAll: true,
      // 是否显示对话框
      isDialogVisible: false,
      // 是否显示指标弹出框
      isPopoverVisible: false,
      // 筛选指标列表
      filterTypeList: indicatorManager.fiveRateIndicatorList,
      // 指标筛选
      checkTypeList: [],
      // 指标筛选临时列表
      tempCheckTypeList: [],
    };
  },
  props: {
    filterData: {
      type: Object,
      default: () => {},
    },
    // default：默认 campus：校区报告
    type: {
      type: String,
      default: 'default',
    },
  },
  watch: {
    filterData: {
      deep: true,
      handler(newValue) {
        if (!newValue) return;
        this.updateFilter(newValue);
      },
    },
  },
  activated() {
    if (this.$refs.avgChart.avgChart && this.$refs.fiveRateChart.fiveRateChart) {
      this.$nextTick(() => {
        this.$refs.avgChart.avgChart.resize();
        this.$refs.fiveRateChart.fiveRateChart.resize();
      });
    }
  },
  computed: {
    data() {
      return this.tableData.filter(item => item.clsName !== '年级');
    },

    source() {
      return this.$sessionSave.get('reportDetail').source;
    },

    isMainReport() {
      return this.$sessionSave.get('reportDetail').examId == this.$sessionSave.get('reportParent').examId;
    },

    // 是否可以对比
    contrastEnable() {
      return this.isMainReport && this.filterData.qType == 0 && this.type != 'campus';
    },

    // 获取指标筛选列表
    getFilterTypeList() {
      if (this.type == 'campus') {
        return this.filterTypeList.filter(item => item.value != 'growthRate');
      }

      if (!this.contrastObj.examId) {
        return this.filterTypeList.map(item => {
          if (item.value == 'growthRate') {
            return { ...item, disabled: true };
          }
          return item;
        });
      }

      return this.filterTypeList;
    },
  },
  async mounted() {
    this.isInit = false;
    let contrastObj = this.$sessionSave.get('contrastObj');
    if (contrastObj) {
      this.contrastObj.examId = contrastObj.examId;
      this.contrastObj.examName = contrastObj.examName;
    }
    if (this.source == 101 || this.source == 102 || this.source == 103 || this.type == 'campus') {
      this.contrastObj.examId = '';
      this.contrastObj.examName = '';
    }
    const checkTypeList = indicatorManager.getIndicator('fiveRate');
    this.checkTypeList = this.getFilterTypeList
      .filter(item => !item.disabled && checkTypeList.includes(item.value))
      .map(item => item.value);
    await this.updateFilter();
    this.isInit = true;
  },
  methods: {
    getClassIds() {
      let innerClassList = this.$sessionSave.get('innerClassList') || [];
      return innerClassList.map(item => item.id);
    },

    // 获取一分五率数据
    async getScoreRate() {
      try {
        const data = await getScoreRate({
          subjectName: this.filterData.subjectName,
          subjectId: this.filterData.subjectId,
          examId: this.$sessionSave.get('reportDetail').examId,
          contrastId: this.contrastObj.examId,
          v: this.$sessionSave.get('reportDetail').v,
          qType: this.filterData.qType,
        });
        if (data.code !== 1 || !data.data.length) {
          this.tableData = [];
          return;
        }
        let gradeAvg = data.data[0].avgScore;
        let list = [];
        data.data.forEach(item => {
          item.gradeAvg = gradeAvg;
          list.push(item);
        });

        if (!this.isShowFiveAll) {
          let ids = this.getClassIds();
          list = list.filter(item => ids.includes(item.classId));
        }
        this.tableData = list;
        this.initGrowthRateChart();
      } catch (e) {
        console.error(e);
        this.tableData = [];
      }
    },

    // 获取一分五率教师数据
    async getTeaScoreRate() {
      try {
        const data = await getTeaScoreRate({
          subjectId: this.filterData.subjectId,
          examId: this.$sessionSave.get('reportDetail').examId,
          contrastId: this.contrastObj.examId,
          v: this.$sessionSave.get('reportDetail').v,
          qType: this.filterData.qType,
        });
        if (data.code !== 1 || !data.data.length) {
          this.tableData = [];
          return;
        }
        let list = [];
        data.data.forEach(item => {
          list.push(item);
        });

        if (!this.isShowFiveAll) {
          let ids = this.getClassIds();
          list = list.filter(item => {
            let classIds = item.classId.split(',');
            return classIds.every(id => ids.includes(id));
          });
        }
        this.tableData = list;
        this.initGrowthRateChart();
      } catch (e) {
        this.tableData = [];
      }
    },

    // 获取校区一分五率
    async getCampusScoreRate() {
      const res = await getCampusScoreRateAPI({
        examId: this.$sessionSave.get('reportDetail').examId,
        subjectId: this.filterData.subjectId,
        qType: this.filterData.qType,
        v: this.$sessionSave.get('reportDetail').v,
      });
      let gradeAvg = res.data[0].avgScore;
      let list = [];
      res.data.forEach(item => {
        item.gradeAvg = gradeAvg;
        list.push(item);
      });
      this.tableData = res.data || [];
    },

    updateContrastInfo(reportObj) {
      // 添加了对比考试
      if (reportObj) {
        this.contrastObj = reportObj;
        this.$sessionSave.set('contrastObj', this.contrastObj);
      }
      this.getTableData();
    },

    async updateFilter(filter) {
      this.clsList = this.$sessionSave.get('innerClassList');
      await this.getTableData();
    },

    getTableData() {
      this.isShowFiveAll = getFuncEnable({ funcId: FuncIdEnum.FiveAll, subjectId: this.filterData.subjectId });
      this.tableData = [];
      if (this.type == 'campus') {
        return this.getCampusScoreRate();
      }

      if (this.queryType == 1) {
        return this.getScoreRate();
      }
      if (this.queryType == 2) {
        return this.getTeaScoreRate();
      }
    },

    // 显示指标弹出框
    onShowTypePopover() {
      this.tempCheckTypeList = JSON.parse(JSON.stringify(this.checkTypeList));
    },

    // 处理指标筛选
    handleCheckType() {
      this.checkTypeList = JSON.parse(JSON.stringify(this.tempCheckTypeList));
      this.isPopoverVisible = false;
      indicatorManager.setIndicator('fiveRate', this.checkTypeList);
    },

    // 显示导出五率弹窗
    showExportScoreRate() {
      this.isDialogVisible = true;
    },
    // 导出五率
    async exportScoreRate({ subjectCheckList, classCheckList, qType }) {
      const { examId, campusCode, v, year } = this.$sessionSave.get('reportDetail');
      let role = '';
      if (!UserRole.isOperation) {
        const map = await UserRole.utils.getRoleSubjectClassMap(
          year,
          campusCode,
          this.$sessionSave.get('reportType') == 'school' ? true : false
        );
        role = JSON.stringify(map);
      }
      const params = {
        examId,
        subjectId: subjectCheckList.join(','),
        contrastId: this.contrastObj.examId,
        v,
        role: role,
        filter: this.checkTypeList.join(','),
        token: getToken(),
        qType: qType,
      };
      const urlSearch = new URLSearchParams(params);
      const path = this.type == 'campus' ? '/pexam/_/exp-campus-score-rate' : '/pexam/_/exportScoreRate';
      let url = process.env.VUE_APP_KKLURL + path + `?${urlSearch.toString()}`;
      window.open(url);
    },

    // 初始化增长图表
    async initGrowthRateChart() {
      if (!this.data.length) {
        return;
      }
      await this.$nextTick();
      const chart = this.$echarts.init(this.$refs.growthChart);
      chart.setOption(
        {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            formatter: params => {
              let prop = this.queryType == 1 ? 'clsName' : 'teaName';
              let obj = this.data.find(item => {
                return params[0].name == item[prop];
              });
              if (!obj) {
                return '';
              }
              return [
                `<div>${obj.clsName}</div>`,
                `<div>${this.filterData?.subjectId == '' ? '班主任' : '任课老师'}：${obj.teaName || '--'}</div>`,
                ...params.map(item => {
                  return `<div>${item.marker}  ${item.seriesName}: <b style="float: right; margin-left: 20px;">${item.value}</b> </div>`;
                }),
              ].join('');
            },
          },
          grid: {
            left: '3%',
            right: '7%',
            bottom: '11%',
            containLabel: true,
          },
          legend: {
            icon: 'circle',
            top: 10,
            right: 70,
            data: ['增长率'],
          },
          yAxis: {
            type: 'value',
            name: '增长率',
          },
          xAxis: {
            type: 'category',
            axisLine: {
              lineStyle: {
                color: '#757C8C',
              },
            },
            data: this.queryType == 1 ? this.data.map(item => item.clsName) : this.data.map(item => item.teaName),
            axisLabel: {
              interval: 0,
              formatter: function (value) {
                if (value.length > 15) {
                  value = value.substring(0, 15) + '..';
                }
                return value;
              },
            },
          },
          series: [
            {
              name: '增长率',
              data: this.data.map(item => item.growthRate),
              color: '#409eff',
              type: 'bar',
              label: {
                show: true,
                position: 'left',
              },
              barMaxWidth: 60,
              barGap: 20,
            },
          ],

          dataZoom: [
            {
              type: 'slider',
              show: true,
              xAxisIndex: [0],
              start: 0,
              end: 100,
            },
          ],
        },
        true
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.fl {
  float: left;
}
.fr {
  float: right;
}

.five-rate-box {
  // margin-top: 20px;
  // padding: 10px 20px;
  background: #ffffff;
  border-radius: 6px;
  padding-bottom: 30px;

  .chart-box {
    // height: 475px;
    background: #ffffff;
    // border: 1px solid #E4E8EB;
    // border-radius: 3px;
    margin: 20px 0;
  }
  .avg-chart {
    border: 1px solid #e4e8eb;
    border-radius: 3px;
  }

  .table-box {
    // border: 1px solid #e4e8eb;
    // border-radius: 3px;
  }
  .titleLine {
    display: inline-block;
    position: relative;
    height: 54px;
    line-height: 54px;
    font-size: 16px;
    font-weight: bold;
    color: #3f4a54;
    padding-left: 16px;
    &:before {
      content: '';
      width: 6px;
      height: 24px;
      background: #409eff;
      border-radius: 3px;
      position: absolute;
      left: 0;
      top: 15px;
    }
  }
}

.five-rate__header {
  display: flex;
  height: 54px;
  line-height: 54px;
  align-items: center;
}

.five-rate-btns {
  height: 54px;
  line-height: 54px;
  margin-left: 20px;
}

.group__header {
  display: flex;
  align-items: center;
  margin: 10px 0;

  .group__header--left {
    flex: 1;
    display: flex;
    align-items: center;
  }

  .group__header--right {
    display: flex;
    align-items: center;
  }

  .radio-group {
    // flex: 1;
  }

  .tip {
    color: red;
    font-size: 14px;
    margin-left: 10px;
  }
}

.filtrate-btn {
  margin-left: 10px;
}

.popover-footer {
  margin-top: 10px;
  text-align: right;
}
</style>
<style lang="scss">
.five-rate-box {
  .el-tabs {
    .el-tabs__header {
      margin: 0 0 1px 4px;
    }
    .el-tabs__nav-wrap::after {
      width: 160px;
    }
    .el-tabs__item {
      width: 80px;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      &.is-top:last-child {
        padding-left: 0;
      }
      &.is-top:nth-child(2) {
        padding-right: 0;
      }
    }
  }
}
</style>

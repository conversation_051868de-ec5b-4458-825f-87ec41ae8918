<template>
  <!--视频播放弹窗-->
  <el-dialog :title="title" append-to-body custom-class="video-player-dialog" :visible="true" @close="closeDialog"
    ref="view">
    <div v-show="loading" v-loading="loading" class="video-player-dialog-loading" height="100%" width="100%"></div>
    <video :src="videoSrc" width="100%" height="100%" controls :autoplay="true" v-if="isVideo" v-show="!loading">
      您的浏览器不支持 video 标签
    </video>
    <iframe v-else v-show="!loading" height="100%" width="100%" :src="videoSrc" frameborder="0"
      @load="loading = false"></iframe>
    <div class="close"></div>
  </el-dialog>
</template>

<script>
import { getHuohuaPreviewLink } from '@/utils';

let filterTimer = null;
export default {
  props: ["src", "title"],
  data() {
    return {
      videoSrc: "",
      loading: true,
      isVideo: false,
    };
  },
  async created() {
    if (this.src.startsWith("https://www.huohuaschool.com/")) {
      let loginInfo = this.$sessionSave.get('loginInfo');
      this.videoSrc = await getHuohuaPreviewLink(this.src, loginInfo.huohua_id);
    } else {
      let match = this.src.match(
        /\/cooperation\/tencent\/let\/download_video.php\?id=([^&]*)/
      );
      if (match) {
        let id = match[1];
        let LLKT_URL = "https://gc.iclass30.com/";
        if (id) {
          this.videoSrc = `${LLKT_URL}llkt/${id}.mp4`;
        }
      } else {
        this.videoSrc = this.src;
      }
    }
    this.isVideo = !/videoPlay\?src=/gi.test(this.videoSrc) && /.mp4/g.test(this.videoSrc);
    this.loading = !this.isVideo;
  },
  mounted() {
    // this.resizeScreen()
  },
  methods: {
    closeDialog() {
      this.$emit("close-dialog");
    },
    resizeScreen() {
      const _this = this;
      // 利用 CSS3 旋转 对根容器逆时针旋转 90 度
      const detectOrient = function () {
        let width = document.documentElement.clientWidth,
          height = document.documentElement.clientHeight,
          $wrapper = _this.$refs.view, // 这里是页面最外层元素
          style = "";
        if (width >= height) {
          // 横屏
          style += "width:" + width + "px;"; // 注意旋转后的宽高切换
          style += "height:" + height + "px;";
          style += "-webkit-transform: rotate(0); transform: rotate(0);";
          style += "-webkit-transform-origin: 0 0;";
          style += "transform-origin: 0 0;";
        } else {
          // 竖屏
          style += "width:" + height + "px;";
          style += "height:" + width + "px;";
          style +=
            "-webkit-transform: rotate(90deg); transform: rotate(90deg);";
          // 注意旋转中点的处理
          style +=
            "-webkit-transform-origin: " +
            width / 2 +
            "px " +
            width / 2 +
            "px;";
          style += "transform-origin: " + width / 2 + "px " + width / 2 + "px;";
        }
        $wrapper.style.cssText = style;
      };
      window.onresize = detectOrient;
      detectOrient();
    }
  },
};
</script>

<style lang="scss">
// @media screen and (min-width: 220px) and (max-width:600px) {
//   .el-dialog{
//     width: 90% !important;
//   }
// }
.video-player-dialog {
  margin-top: 0 !important;
  height: 100% !important;

  // transform: rotate(90deg);
  .el-dialog__body {
    height: 650px;

    // min-height: 650px !important;
    .video-player-dialog-loading {
      width: 100%;
      height: 100%;
    }
  }

  .el-dialog__headerbtn .el-dialog__close {
    // color: #909399;
    // position: absolute;
    // top: -12px;
    // right: -8px;
  }
}
</style>


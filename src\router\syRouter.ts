/*
 * @Description:
 * @Author: 小圆
 * @Date: 2024-11-19 09:06:17
 * @LastEditors: 小圆
 */

import Vue from 'vue';
import Router from 'vue-router';

import { getFirstOftenMenuRoute } from '@/components/ExamReport/constant';
import examReportRouterMenu from './routerMenu/examReportRouterMenu'; // 考试报告路由
import { routerIntercept } from './syRouterIntercept';
import targetRouterMenu from './routerMenu/targetRouterMenu';

Vue.use(Router);
const originalPush = Router.prototype.push;
//修改原型对象中的push方法
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err);
};

export const router = new Router({
  mode: 'history',
  base: process.env.VUE_APP_BASE_URL,
  routes: [
    { path: '/', redirect: '/home' },
    {
      path: '/home',
      redirect: '/home/<USER>',
      component: resolve => require(['../pages/sy/home.vue'], resolve),
      children: [
        {
          path: 'sySchoolStudy',
          component: resolve => require(['../pages/sy/schoolStudy.vue'], resolve),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: 'syRegionStudy',
          component: resolve => require(['../pages/sy/regionStudy.vue'], resolve),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: 'syRegionStudyContrast',
          component: resolve => require(['../pages/sy/regionStudyContrast.vue'], resolve),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: 'reportDetail',
          component: resolve => require(['../pages/examReportDetail.vue'], resolve),
          redirect: to => {
            const route = getFirstOftenMenuRoute();
            return {
              path: route.path,
              query: { ...to.query, ...route.query },
            };
          },
          meta: {
            keepAlive: false,
          },
          children: examReportRouterMenu,
        },
        targetRouterMenu,
      ],
    },
    {
      path: '*',
      redirect: '/',
    },
  ],
});
export default router;

routerIntercept();

<template>
  <div>
    <div v-show="!showDefault" id="totalRankChart"
         style="width: 100%; height: 400px"></div>
    <div v-show="showDefault">
      <div style="text-align: center"><img style="width: 350px;" :src="noResImg" alt=""></div>
      <p style="text-align: center;font-size: 16px;margin-top: 10px;">暂无数据!</p>
    </div>
  </div>
</template>

<script>
export default {
  name: "TotalRankChart",
  props: ['tableData','targetData'],
  data() {
    return {
      noResImg: require('@/assets/no-res.png'),
      totalRankChart: null,
      allClassRes: [],
      showDefault: false,
      source: [],
    }
  },
  watch: {
    tableData: {
      handler(val) {
        if(!val || !val.length) {
          this.showDefault = true;
          return;
        };
        if(val.length) {
          this.showDefault = false;
        }
        // this.resetDomSize('totalRankChart', 400);
        this.$nextTick(()=>{
          this.drawImg();
        })
      },
      deep: true,
      immediate: true,
    },
  },
  beforeDestroy() {
    if(this.totalRankChart != null && this.totalRankChart != "" && this.totalRankChart != undefined) {
      this.totalRankChart.dispose();
      this.totalRankChart = null;
    }
  },
  mounted(){
    let _this = this;
    window.onresize = ()  =>{
      return (() => {
        if(_this.totalRankChart) {
          // this.resetDomSize('totalRankChart', 400);
          _this.totalRankChart.resize();
        }
      })()
    };
    if(!this.totalRankChart) {
      this.drawImg();
    }
  },
  methods: {
    // resetDomSize(el, height) {
    //   let width = document.getElementById('pane-chart').clientWidth
    //   Object.defineProperty(document.getElementById(el),'clientWidth',
    //       {get:function(){return width;}, configurable: true})
    //   Object.defineProperty(document.getElementById(el),'clientHeight',
    //       {get:function(){return height;}, configurable: true})
    // },
    handleChartData() {
      let data = this.tableData && this.tableData.length ? this.tableData : [] ;
      //分段名次
      let rankName = []
      this.targetData.totalRankSegment.forEach(it =>{
        if(it.start != -1){
          rankName.push(`前${it.end}名`)
        }else{
          rankName.push(`后${it.end}名`)
        }
      })
      if(data.length) {
        this.allClassRes = [];
        data.filter(item => {
          return item.classId!=''
        }).map(it => {
          let arr1 = []
          let {className, score} = it;
          for(let i in score){
            arr1.push(score[i])
          }
          let arr = [className, ...arr1];
          this.allClassRes.push(arr)
          this.source = [
            ['班级', ...rankName],
            ...this.allClassRes
          ]
        })
      } else {
        this.showDefault = true;
      }
    },
    drawImg() {
      if(this.totalRankChart != null && this.totalRankChart != "" && this.totalRankChart != undefined) {
        this.totalRankChart.dispose();
        this.totalRankChart = null;
      }
      this.handleChartData();
      let _this = this;

      let seriesItem = {type: 'bar', itemStyle: {barBorderRadius: [3, 3, 0, 0]}, barWidth: 14};
      this.totalRankChart = this.$echarts.init(
          document.getElementById('totalRankChart')
      );
      this.totalRankChart.setOption({
        color: ['#409EFF', '#07C29D', '#FF6A68', '#3E73F6', '#FFB400', '#BD3DDD', '#9EEBAA'],
        legend: {
          icon: 'circle',
          top: 10,
          right: 50
        },
        tooltip: {},
        grid: {
          left: '3%',
          right: '6%',
          // bottom: '10%',
          containLabel: true
        },
        dataset: {
          source: _this.source
        },
        xAxis: {type: 'category', name: '班级'},
        yAxis: {name: '人数'},
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            startValue: 0,
            endValue: 8,
          },
        ],
        series: [
          seriesItem,
          seriesItem,
          seriesItem,
          seriesItem,
          seriesItem,
          seriesItem,
          seriesItem,
        ]
      })
    }
  }
}
</script>

<style scoped>

</style>

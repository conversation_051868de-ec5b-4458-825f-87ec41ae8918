<template>
  <div>
    <el-dialog
      custom-class="workbook-dialog"
      :visible="dialogFormVisible"
      width="800px"
      :before-close="handleClose"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
    >
      <div slot="title" class="dialog-title">
        <span style="margin-right: 10px">{{
          editObj ? "编辑考试报告" : "创建考试"
        }}</span>
        <el-popover placement="right" width="380" trigger="hover">
          <p>1.若本场考试包含多个学科，请一次性选择这些学科</p>
          <p>2.已关联个册的学科不支持取消，未关联的支持取消</p>
          <i class="el-icon-warning" slot="reference"></i>
        </el-popover>
      </div>
      <el-form :model="submitData">
        <el-form-item label="名称" :label-width="formLabelWidth">
          <el-input
            v-model="submitData.name"
            autocomplete="off"
            maxlength="20"
            style="width: 98%"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="年级"
          :label-width="formLabelWidth"
          style="margin-bottom: 5px"
        >
          <el-select
            v-model="submitData.gradeId"
            placeholder="请选择年级"
            @focus="gradeFocus"
            @change="changeGrade"
            style="width: 98%"
            :disabled="editObj.examId && editObj.examId != ''"
          >
            <el-option
              v-for="item in dict.grdList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="学科"
          :label-width="formLabelWidth"
          style="margin-bottom: 5px"
        >
          <div>
            <el-checkbox v-model="checkAll" @change="handleCheckAllChange"
              >全选</el-checkbox
            >
            <el-checkbox-group
              class="subjectCheckBox"
              v-model="checkSubjects"
              @change="handleCheckedSubjectChange"
            >
              <el-checkbox
                v-for="sub in dict.subjectList"
                v-if="hasMap[sub.id]"
                :label="sub.id"
                :key="sub.id"
                :disabled="
                  relatedSubids.indexOf(sub.id) != -1 ||
                  processSubids.indexOf(sub.id) != -1
                "
                style="position: relative"
              >
                <div
                  class="dialog-gece-tip"
                  v-if="relatedSubids.indexOf(sub.id) != -1"
                >
                  个
                </div>
                {{ sub.name }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>
        <el-form-item label="类别" :label-width="formLabelWidth">
          <el-select
            v-model="submitData.category"
            placeholder="请选择类别"
            style="width: 98%"
          >
            <el-option
              v-for="item in dict.typeList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="考试时间" :label-width="formLabelWidth">
          <el-date-picker
            popper-class="datePicker__time"
            v-model="examTime"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            type="date"
            @change="changeData"
            :picker-options="pickerOptions"
            placeholder="选择日期"
          >
            <!--:picker-options="pickerOptions"-->
          </el-date-picker>

          <el-select
            v-model="submitData.schoolYearId"
            placeholder="请选择日期"
            :disabled="true"
            style="margin-left: 6px"
          >
            <el-option
              v-for="item in dict.years"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>

          <el-select
            v-model="submitData.schoolTermId"
            placeholder="请选择日期"
            :disabled="true"
            style="margin-left: 6px"
          >
            <el-option
              v-for="item in dict.terms"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发布成绩" :label-width="formLabelWidth">
          <el-radio-group v-model="submitData.isPublish">
            <el-radio :label="0">否</el-radio>
            <el-radio :label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="成绩确认"
          :label-width="formLabelWidth"
          v-if="submitData.isPublish == 1"
        >
          <el-radio-group
            v-model="submitData.scoreConfirm"
            :disabled="editObj.examId && editObj.examId != ''"
          >
            <el-radio :label="0">学生可查看，无需确认</el-radio>
            <el-radio :label="1">需学生确认</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="$emit('closeDialog')">取 消</el-button>
        <el-button type="primary" :loading="isComfirming" @click="addReport"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createdExam } from "../service/pexam";
let date = new Date(),
  y = Number(date.getFullYear().toString().substr(2)),
  M = date.getMonth(),
  d = date.getDate();

let min = new Date(`${date.getFullYear() - 5}-08-09`);
let max = new Date(`${date.getFullYear()}-08-09`);
let md = Number(`${M}${d < 10 ? `0${d}` : d}`);
if (md >= 710) {
  min = new Date(`${date.getFullYear() + 1 - 5}-08-09`);
  max = new Date(`${date.getFullYear() + 1}-08-09`);
}

export default {
  name: "createReports",
  props: ["dict", "editObj"],
  computed: {
    // 对选择后的时间格式化
    timeSlotFormat() {
      return this.examTime ? this.$formatDate(new Date(this.examTime)) : "";
    },
  },
  data() {
    return {
      hasMap: {},
      // 提交按钮是否在加载中
      isComfirming: false,
      // 弹窗是否显示
      dialogFormVisible: true,
      // 考试时间
      examTime: "",
      // 考试时间选择器的配置项
      pickerOptions: {
        // 限制时间
        // selectableRange: ['12:00:00 - 23:59:59'],
        // 限制日期
        disabledDate: (time) => {
          return (
            time.getTime() < min.getTime() || time.getTime() > max.getTime()
          );
        },
      },
      // 提交数据
      submitData: {
        name: "",
        gradeId: "",
        subjectIds: "",
        category: "",
        type: "2",
        // 选择的具体时间数值 - 默认本周
        examTime: "",
        schoolId: "",
        schoolYearId: "",
        schoolTermId: "",
        scoreConfirm: 0, //是否需确认成绩
        isPublish: 1, //是否发布成绩
      },
      // form项宽度
      formLabelWidth: "80px",
      // 学科是否全选
      checkAll: false,
      checkSubjects: [],
      // 上次选择的年级
      historyGrade: "",
      // 是否选择考试时间选择器
      showTimePicker: true,
      // 已关联个册的学科
      relatedSubids: [],
      //发送加工的学科
      processSubids: [],
    };
  },
  mounted() {
    this.hasMap = {};

    this.submitData.examTime = this.timeSlotFormat;
    this.submitData.schoolId = this.$sessionSave.get("schoolInfo").id;
    let grd = "";
    // 若为编辑个册
    if (this.editObj) {
      this.submitData.examId = this.editObj.examId;
      this.submitData.name = this.editObj.examName;
      this.submitData.gradeId = Number(this.editObj.gradeCode);
      grd = this.dict.grdList.find((q) => q.id == this.submitData.gradeId);

      this.submitData.category = Number(this.editObj.categoryCode);
      this.submitData.examTime = this.editObj.examDateTime;
      this.examTime = this.editObj.examDateTime;
      this.submitData.schoolYearId = this.editObj.schoolYearId;
      this.submitData.schoolTermId = Number(this.editObj.schoolTermId);
      this.submitData.subjectIds = this.editObj.subjectId;
      this.editObj.paperList.forEach((item) => {
        //已关联个册的学科
        if (item.isExamUse == 0) {
          this.relatedSubids.push(Number(item.subectId));
        }
        //发送加工的学科
        if (item.processState > 0) {
          this.processSubids.push(Number(item.subectId));
        }
      });
      this.submitData.scoreConfirm = this.editObj.scoreConfirm;
      this.submitData.isPublish = this.editObj.isPublish;
    }

    this.dict.subjectList.forEach((v) => {
      if (this.editObj) {
        if (grd) {
          if (v.phaseId == grd.phaseId) {
            this.hasMap[v.id] = v.show;
          }
        } else {
          this.hasMap[v.id] = v.show;
        }
      } else {
        this.hasMap[v.id] = true;
      }
    });

    if (this.editObj && this.editObj.subjectId) {
      let subjectIds = this.editObj.subjectId.split(",");
      // 显示单科和多科的已选择学科
      let subCount = subjectIds.length;
      this.submitData.type = subCount > 1 ? "2" : "1";
      let subjectList = this.dict.subjectList.filter((q) => this.hasMap[q.id]);
      this.checkAll = subCount === subjectList.length;
      let ids = [];
      subjectIds.forEach((v) => {
        ids.push(Number(v));
      });
      this.checkSubjects = ids;
    }
  },
  methods: {
    // 考试时间筛选项的禁止范围
    disabledDate(time) {
      let date = new Date(),
        y = Number(date.getFullYear().toString().substr(2)),
        M = date.getMonth(),
        d = date.getDate();

      let min = new Date(`${date.getFullYear() - 5}-08-10`);
      let max = new Date(`${date.getFullYear()}-08-10`);
      let md = Number(`${M}${d < 10 ? `0${d}` : d}`);
      if (md >= 710) {
        min = new Date(`${date.getFullYear() + 1 - 5}-08-10`);
        max = new Date(`${date.getFullYear() + 1}-08-10`);
      }
      return time.getTime() < max.getTime() || time.getTime() > min.getTime();
    },
    // 切换考试时间
    changeData(val) {
      let time = this.timeSlotFormat;
      this.submitData.examTime = time;
      if (!time) {
        this.submitData.schoolYearId = "";
        this.submitData.schoolTermId = "";
        return;
      }

      let date = new Date(time),
        y = Number(date.getFullYear().toString().substr(2)),
        M = date.getMonth(),
        d = date.getDate();
      // 判断学期时间范围-2021到2022年上学期[2021-8-10，2022-2-15], 下学期[2022-2-16，2022-8-9]

      let md = Number(`${M}${d < 10 ? `0${d}` : d}`);
      if (md >= 710) {
        this.submitData.schoolYearId = `${y}${y + 1}`;

        this.submitData.schoolTermId = 110;
      } else {
        this.submitData.schoolYearId = `${y - 1}${y}`;
        if (md <= 115) {
          this.submitData.schoolTermId = 110;
        } else {
          this.submitData.schoolTermId = 111;
        }
      }
    },
    // 全选
    handleCheckAllChange(val) {
      let ids = this.dict.subjectList
        .filter((q) => this.hasMap[q.id])
        .map((q) => q.id);
      this.checkSubjects = val
        ? ids
        : this.relatedSubids.concat(this.processSubids);
    },
    // 选择学科
    handleCheckedSubjectChange(value) {
      this.checkAll =
        this.checkSubjects.length ==
        this.dict.subjectList.filter((q) => this.hasMap[q.id]).length;
    },
    // 年级下拉框获取焦点事件
    gradeFocus(val) {
      this.historyGrade = this.submitData.gradeId;
    },
    // 切换年级，联动显示学科
    changeGrade(data) {
      this.checkAll = false;
      this.checkSubjects = [];

      let grd = this.dict.grdList.find((q) => q.id == data);
      this.dict.subjectList.forEach((v) => {
        this.hasMap[v.id] = false;
        if (v.id == "" || v.phaseId == grd.phaseId) {
          this.hasMap[v.id] = true;
        }
      });
    },
    // 关闭弹窗
    handleClose(done) {
      this.$emit("closeDialog");
      this.dialogFormVisible = false;
      done();
    },
    // 创建，编辑考试报告
    addReport() {
      // 名称校验
      if (!this.submitData.name) {
        this.$message.error("名称不能为空！");
        return;
      }
      // 年级校验
      if (!this.submitData.gradeId) {
        this.$message.error("年级不能为空！");
        return;
      }
      // 学科校验
      if (this.checkSubjects.length == 0) {
        this.$message.error("学科不能为空！");
        return;
      }
      // 类别校验
      if (!this.submitData.category) {
        this.$message.error("类别不能为空！");
        return;
      }

      if (!this.submitData.examTime) {
        this.$message.error("考试时间不能为空！");
        return;
      }
      let grd = this.dict.grdList.find((q) => q.id == this.submitData.gradeId);
      let ids = [];
      this.dict.subjectList.forEach((v) => {
        if (
          v.phaseId == grd.phaseId &&
          this.checkSubjects.find((q) => q == v.id)
        ) {
          ids.push(v.id);
        }
      });
      this.submitData.subjectIds = ids.join(",");
      this.createReport();
    },
    createReport() {
      // 编辑考试报告
      this.isComfirming = true;
      createdExam(this.submitData)
        .then((data) => {
          this.$message({
            message: `报告${this.editObj ? "编辑" : "创建"}成功！`,
            type: "success",
            duration: 1000,
          });
          this.dialogFormVisible = false;
          this.$emit("closeDialog");
          this.$emit("updateData");
          this.isComfirming = false;
        })
        .catch((err) => {
          this.isComfirming = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  margin-right: 10px;
}

.dialog-footer button span {
  font-size: 16px;
}
.dialog-gece-tip {
  position: absolute;
  top: -2px;
  left: -20px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: red;
  font-size: 12px;
  color: #fff;
  text-align: center;
}
</style>
<style lang="scss">
.workbook-dialog {
  .el-dialog__header {
    height: 45px;
    .dialog-title {
      line-height: 45px;
    }
  }
  .el-dialog__body {
    padding: 15px 20px 0px;
  }
  .el-form-item {
    margin-bottom: 15px;
  }
  .el-dialog__footer {
    padding: 10px 20px 15px;
  }
}

.subjectCheckBox {
  line-height: 26px;
  .el-checkbox,.el-radio {
    margin-right: 40px;
    &.el-checkbox + .el-checkbox,&.el-radio + .el-radio {
      margin-left: 0;
    }
  }
}

.dialog-title {
  line-height: 54px;
}
</style>

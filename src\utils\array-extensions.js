/**
 * @name: Array数组拓展类
 * @param {type}
 * @return:
 * @description:
 */
Array.prototype.get = function (id) {
  for (let i = 0; i < this.length; i++) {
    if (this[i].id === id) return this[i];
  }
  return null;
};
Array.prototype.getByKey = function (key, value) {
  for (let i = 0; i < this.length; i++) {
    if (this[i][key] == value) return this[i];
  }
  return null;
};

Array.prototype.getByKeys = function (data1, data2) {
  for (let i = 0; i < this.length; i++) {
    if (this[i][data1.key] == data1.value && this[i][data2.key] == data2.value) return this[i];
  }
  return null;
};

Array.prototype.getNextByKey = function (key, value) {
  for (let i = 0; i < this.length; i++) {
    if (this[i][key] == value) return this[i + 1];
  }
  return null;
};

Array.prototype.removeDuplicatesByKey = function(key) {
  const map = new Map();
  this.forEach(item => {
      if (!map.has(item[key])) {
          map.set(item[key], item);
      }
  });
  return Array.from(map.values());
}

export {};

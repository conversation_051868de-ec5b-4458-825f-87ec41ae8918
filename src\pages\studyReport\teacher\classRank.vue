<!--
 * @Description: 班级排名
 * @Author: 小圆
 * @Date: 2024-04-02 09:02:47
 * @LastEditors: 小圆
-->
<template>
  <div>
    <div v-loading="tableLoading">
      <div v-if="tableColumns.length > 0" class="class-table-wrapper" v-drag>
        <base-table
          v-for="(item, index) in tableColumns"
          :key="index"
          class="class-table"
          :data="tableData[index]"
          :column="[tableColumns[index]]"
          v-bind="getTableAttr()"
        ></base-table>
      </div>

      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins, Prop } from 'vue-property-decorator';
import TableCommon, { getDefaultTableAttr } from '../components/TableCommon';
import FilterModule from '../plugins/FilterModule';
import { exportExcel } from '@/plugins/frontend-excelify';

@Component({
  components: {},
})
export default class ClassRank extends Mixins(TableCommon) {
  getTableAttr() {
    const attr = getDefaultTableAttr();
    delete attr.maxHeight;
    return attr;
  }

  // 获取数据
  async getTableData() {
    this.tableColumns = [];
    this.tableData = [];
    this.tableLoading = true;
    await this.$nextTick();
    try {
      const res = await FilterModule.getReportTableData(this.apiName || this.$route.name);
      if (res.code != 1) {
        return this.$message.error(res.msg);
      }
      const data = res.data;
      data.table = this.setColumnProp(data.table);
      this.tableColumns = data.table as IColumn[];
      const classInfos = FilterModule.getClassInfos();
      const tableData = [];
      classInfos.forEach(item => {
        let arr = [];
        data.result.forEach(data => {
          if (data.classId == item.id) {
            arr.push(data);
          }
        });
        tableData.push(arr);
      });
      this.tableData = tableData;
    } catch (error) {
      console.error(error);
      this.tableColumns = [];
      this.tableData = [];
    } finally {
      this.tableLoading = false;
    }
  }

  // render(h) {
  //   const table = this.renderTable(h);
  //   return table;
  // }
}
</script>

<style scoped lang="scss">
.class-table-wrapper {
  display: flex;
  overflow: auto;

  .class-table {
    width: 100%;
    min-width: 450px;
  }
}
</style>

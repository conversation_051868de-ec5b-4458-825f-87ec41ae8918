/**
 * Created by ssh on 2018/12/19
 * description: 请求配置
 **/
 import http from 'http';
 import https from 'https';
 import qs from "qs"
 
 export default {
     // 自定义的请求头
     headers: {
         post: {
             'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
         },
         'X-Requested-Width': 'XMLHttpRequest'
     },
     // 超时设置
     // timeout: 10000,
     // 跨域是否带token
     withCredentials: true,
     responseType: 'json',
     // baseURL: process.env.VUE_APP_BASE_API,
     httpAgent: new http.Agent({
         keepAlive: true
     }),
     httpsAgent: new https.Agent({
         keepAlive: true
     }),
   transformRequest: [function (data) {
     // 将数据转换为表单数据
     return qs.stringify(data)
   }]
 };
 
// =============================================================================
// Normalize.scss based on <PERSON> and <PERSON>'s
// normalize.css v2.1.3 | MIT License | git.io/normalize
// =============================================================================

// =============================================================================
// Normalize.scss settings
// =============================================================================


// Set to true if you want to add support for IE6 and IE7
// Notice: setting to true might render some elements
// slightly differently than when set to false
$legacy_support_for_ie: false !default; // Used also in Compass


// Set the default font family here so you don't have to override it later
$normalized_font_family: sans-serif !default;

$normalize_headings: true !default;

$h1_font_size: 2em !default;
$h2_font_size: 1.5em !default;
$h3_font_size: 1.17em !default;
$h4_font_size: 1em !default;
$h5_font_size: 0.83em !default;
$h6_font_size: 0.75em !default;

$h1_margin: 0.67em 0 !default;
$h2_margin: 0.83em 0 !default;
$h3_margin: 1em 0 !default;
$h4_margin: 1.33em 0 !default;
$h5_margin: 1.67em 0 !default;
$h6_margin: 2.33em 0 !default;

$background: #fff !default;
$color: #000 !default;

// =============================================================================
// HTML5 display definitions
// =============================================================================

// Corrects block display not defined in IE6/7/8/9 & FF3

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
nav,
section,
summary {
    display: block;
}

// Corrects inline-block display not defined in IE6/7/8/9 & FF3

audio,
canvas,
video {
    display: inline-block;
    @if $legacy_support_for_ie {
        *display: inline;
        *zoom: 1;
    }
}

// 1. Prevents modern browsers from displaying 'audio' without controls
// 2. Remove excess height in iOS5 devices

audio:not([controls]) {
    display: none; // 1
    height: 0; // 2
}

//
// Address `[hidden]` styling not present in IE 8/9.
// Hide the `template` element in IE, Safari, and Firefox < 22.
//

[hidden], template {
    display: none;
}

// =============================================================================
// Base
// =============================================================================

// 1. Corrects text resizing oddly in IE6/7 when body font-size is set using em units
//    http://clagnut.com/blog/348/#c790
// 2. Prevents iOS text size adjust after orientation change, without disabling user zoom
//    www.456bereastreet.com/archive/201012/controlling_text_size_in_safari_for_ios_without_disabling_user_zoom/

html {
    @if $legacy_support_for_ie {
        font-size: 100%; // 1
    }
		background: $background;
		color: $color;
    -webkit-text-size-adjust: 100%; // 2
    -ms-text-size-adjust: 100%; // 2
}

// Addresses font-family inconsistency between 'textarea' and other form elements.

html,
button,
input,
select,
textarea {
    font-family: $normalized_font_family;
}

// Addresses margins handled incorrectly in IE6/7

body {
    margin: 0;
}

// =============================================================================
// Links
// =============================================================================

// 1. Remove the gray background color from active links in IE 10.
// 2. Addresses outline displayed oddly in Chrome
// 3. Improves readability when focused and also mouse hovered in all browsers
//    people.opera.com/patrickl/experiments/keyboard/test

a {
    // 1
    
    background: transparent;

    // 2

    &:focus {
        outline: thin dotted;
    }

    // 3

    &:hover,
    &:active {
        outline: 0;
    }
}

// =============================================================================
// Typography
// =============================================================================

// Addresses font sizes and margins set differently in IE6/7
// Addresses font sizes within 'section' and 'article' in FF4+, Chrome, S5

@if $normalize_headings == true {
    h1 {
        font-size: $h1_font_size;
        margin: $h1_margin;
    }

    h2 {
        font-size: $h2_font_size;
        margin: $h2_margin;
    }

    h3 {
        font-size: $h3_font_size;
        margin: $h3_margin;
    }

    h4 {
        font-size: $h4_font_size;
        margin: $h4_margin;
    }

    h5 {
        font-size: $h5_font_size;
        margin: $h5_margin;
    }

    h6 {
        font-size: $h6_font_size;
        margin: $h6_margin;
    }
}

// Addresses styling not present in IE 8/9, S5, Chrome

abbr[title] {
    border-bottom: 1px dotted;
}

// Addresses style set to 'bolder' in FF3+, S4/5, Chrome

b,
strong {
    font-weight: bold;
}

@if $legacy_support_for_ie {
    blockquote {
        margin: 1em 40px;
    }
}

// Addresses styling not present in S5, Chrome

dfn {
    font-style: italic;
}

// Addresses styling not present in IE6/7/8/9

mark {
    background: #ff0;
    color: #000;
}

// Addresses margins set differently in IE6/7
@if $legacy_support_for_ie {
    p,
    pre {
        margin: 1em 0;
    }
}

// Corrects font family set oddly in IE6, S4/5, Chrome
// en.wikipedia.org/wiki/User:Davidgothberg/Test59

code,
kbd,
pre,
samp {
    font-family: monospace, serif;
    @if $legacy_support_for_ie {
        _font-family: 'courier new', monospace;
    }
    font-size: 1em;
}

// Improves readability of pre-formatted text in all browsers

pre {
    white-space: pre;
    white-space: pre-wrap;
    word-wrap: break-word;
}

// Set consistent quote types.

q {
    quotes: "\201C" "\201D" "\2018" "\2019";
}

// 1. Addresses CSS quotes not supported in IE6/7
// 2. Addresses quote property not supported in S4

// 1
@if $legacy_support_for_ie {
    q {
        quotes: none;
    }
}

// 2
q {
    &:before, 
    &:after {
        content: '';
        content: none;	
    }
}

// Address inconsistent and variable font size in all browsers.

small {
    font-size: 80%;
}

// Prevents sub and sup affecting line-height in all browsers
// gist.github.com/413930

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}

sup {
    top: -0.5em;
}

sub {
    bottom: -0.25em;
}

// =============================================================================
// Lists
// =============================================================================

// Addresses margins set differently in IE6/7
@if $legacy_support_for_ie {
    dl,
    menu,
    ol,
    ul {
        margin: 1em 0;
    }
}

@if $legacy_support_for_ie {
    dd {
        margin: 0 0 0 40px;
    }
}

// Addresses paddings set differently in IE6/7
@if $legacy_support_for_ie {
    menu,
    ol,
    ul {
        padding: 0 0 0 40px;
    }
}

// Corrects list images handled incorrectly in IE7

nav {
    ul,
    ol {
        @if $legacy_support_for_ie {
            list-style-image: none;
        }
    }
}

// =============================================================================
// Embedded content
// =============================================================================

// 1. Removes border when inside 'a' element in IE6/7/8/9, FF3
// 2. Improves image quality when scaled in IE7
//    code.flickr.com/blog/2008/11/12/on-ui-quality-the-little-things-client-side-image-resizing/

img {
    border: 0; // 1
    @if $legacy_support_for_ie {
        -ms-interpolation-mode: bicubic; // 2
    }
}

// Corrects overflow displayed oddly in IE9

svg:not(:root) {
    overflow: hidden;
}

// =============================================================================
// Figures
// =============================================================================

// Addresses margin not present in IE6/7/8/9, S5, O11

figure {
    margin: 0;
}

// =============================================================================
// Forms
// =============================================================================

// Corrects margin displayed oddly in IE6/7
@if $legacy_support_for_ie {
    form {
        margin: 0;
    }
}

// Define consistent border, margin, and padding

fieldset {
    border: 1px solid #c0c0c0;
    margin: 0 2px;
    padding: 0.35em 0.625em 0.75em;
}

// 1. Corrects color not being inherited in IE6/7/8/9
// 2. Remove padding so people aren't caught out if they zero out fieldsets.
// 3. Corrects text not wrapping in FF3
// 4. Corrects alignment displayed oddly in IE6/7

legend {
    border: 0; // 1
    padding: 0; // 2
    white-space: normal; // 3
    @if $legacy_support_for_ie {
        *margin-left: -7px; // 4
    }
}

// 1. Correct font family not being inherited in all browsers.
// 2. Corrects font size not being inherited in all browsers
// 3. Addresses margins set differently in IE6/7, FF3+, S5, Chrome
// 4. Improves appearance and consistency in all browsers

button,
input,
select,
textarea {
    font-family: inherit; // 1
    font-size: 100%; // 2
    margin: 0; // 3
    vertical-align: baseline; // 4
    @if $legacy_support_for_ie {
        *vertical-align: middle; // 4
    }
}

// Addresses FF3/4 setting line-height on 'input' using !important in the UA stylesheet

button, input {
    line-height: normal;
}

// Address inconsistent `text-transform` inheritance for `button` and `select`.
// All other form control elements do not inherit `text-transform` values.
// Correct `button` style inheritance in Chrome, Safari 5+, and IE 8+.
// Correct `select` style inheritance in Firefox 4+ and Opera.

button,
select {
    text-transform: none;
}

// 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`
//    and `video` controls
// 2. Corrects inability to style clickable 'input' types in iOS
// 3. Improves usability and consistency of cursor style between image-type
//    'input' and others
// 4. Removes inner spacing in IE7 without affecting normal text inputs
//    Known issue: inner spacing remains in IE6

button,
html input[type="button"], // 1
input[type="reset"],
input[type="submit"] {
    -webkit-appearance: button; // 2
    cursor: pointer; // 3
    @if $legacy_support_for_ie {
        *overflow: visible;  // 4
    }
}

// Re-set default cursor for disabled elements

button[disabled],
input[disabled] {
    cursor: default;
}

// Removes inner padding and border in FF3+
// www.sitepen.com/blog/2008/05/14/the-devils-in-the-details-fixing-dojos-toolbar-buttons/

button, input {
    &::-moz-focus-inner {
        border: 0;
        padding: 0;
    }
}

// 1. Removes default vertical scrollbar in IE6/7/8/9
// 2. Improves readability and alignment in all browsers

textarea {
    overflow: auto; // 1
    vertical-align: top; // 2
}

// =============================================================================
// Tables
// =============================================================================

// Remove most spacing between table cells

table {
    border-collapse: collapse;
    border-spacing: 0;
}

input {
    // 1. Addresses appearance set to searchfield in S5, Chrome
    // 2. Addresses box-sizing set to border-box in S5, Chrome (include -moz to future-proof)
    &[type="search"] {
        -webkit-appearance: textfield; // 1
        -moz-box-sizing: content-box;
        -webkit-box-sizing: content-box; // 2
        box-sizing: content-box;
        
    	// Remove inner padding and search cancel button in Safari 5 and Chrome
        // on OS X.
        &::-webkit-search-cancel-button,
        &::-webkit-search-decoration {
            -webkit-appearance: none;
        }
    }
    
    // 1. Address box sizing set to `content-box` in IE 8/9/10.
    // 2. Remove excess padding in IE 8/9/10.
    // 3. Removes excess padding in IE7
    //    Known issue: excess padding remains in IE6
    &[type="checkbox"],
    &[type="radio"] {
    	box-sizing: border-box; // 1
    	padding: 0; // 2
    	@if $legacy_support_for_ie {
        	*height: 13px; // 3
        	*width: 13px; // 3
    	}
    }
}

<template>
  <div class="ques-image">
    <img ref="image" :src="image.url" alt="ques">
    <div v-show="pointer.show&&canEdit" class="pointer-x" :style="{left: pointer.x+'px'}"></div>
    <div v-show="pointer.show&&canEdit" class="pointer-y" :style="{top: pointer.y+'px'}"></div>
    <div v-show="pointer.showDrawBox&&canEdit" class="ques_box" :style="drawBoxStyle"></div>
    <div ref="drawImage"
         v-show="canEdit"
         @mousemove="mousemove"
         @mouseleave="mouseleave"
         @mouseenter="mouseenter"
         class="draw_box_image"></div>

    <ques-box v-for="(box,j) in image.boxes" :box="box" @delete-ques-box="deleteQues(box)"
              :index="j" @add-ques-answer-box="addAnswerBox"
              :can-edit="canEdit" :key="'ques_image_'+index+'_'+j"></ques-box>
  </div>
</template>

<script>

import {inArr} from "@/utils/common";
import QuesBox from "@/components/scan/QuesBox";

export default {
  name: 'scan-image',
  components: {QuesBox},
  props: ['image', 'index', 'canEdit'],
  data() {
    return {
      inArr: inArr,
      startPoint: {
        x: 0,
        y: 0
      },
      //指针位置
      pointer: {
        x: 0,
        y: 0,
        show: true,
        showDrawBox: false,
        drawBox: false
      },
      start: {
        x: 0,
        y: 0
      },
      end: {
        x: 0,
        y: 0
      },
      drawBoxStyle: {
        display: 'none'
      }
    };
  },
  mounted() {
    this.pointer.show = false
  },
  methods: {
    addAnswerBox(index, box) {
      let width = this.$refs.image.width
      let height = this.$refs.image.height
      this.$emit('add-ques-answer-box', this.image.boxes[index], box, {width, height, path: this.image.path});

      // this.image.boxes[index].answerBox = box
      // this.image.boxes[index].answerStyle = this.getBoxStyle(box)
    },
    endAddBox() {
      this.pointer.showDrawBox = false
      this.drawBoxStyle = {
        display: 'none'
      }
    },
    setDrawBoxStyle() {
      this.drawBoxStyle = {
        left: Math.min(this.start.x, this.end.x) + "px",
        top: Math.min(this.start.y, this.end.y) + "px",
        width: Math.abs(this.end.x - this.start.x) + "px",
        height: Math.abs(this.end.y - this.start.y) + "px",
      };
    },
    deleteQues(box) {
      this.$emit('delete-ques-box', box)
    },
    getBoxStyle(box) {
      return {
        left: (box[0] - box[2] / 2) * 100 + "%",
        top: (box[1] - box[3] / 2) * 100 + "%",
        width: box[2] * 100 + "%",
        height: box[3] * 100 + "%",
      }
    },
    mouseenter(e) {
      if (!this.canEdit) {
        return
      }
      this.pointer.show = true
      document.addEventListener('mousedown', this.mousedown)
    },
    mouseleave(e) {
      if (!this.canEdit) {
        return
      }
      this.pointer.show = false
      document.removeEventListener('mousedown', this.mousedown)
    },
    mousemove(e) {
      if (!this.canEdit) {
        return
      }
      if (e.button === 0) {
        this.pointer.x = e.layerX;
        this.pointer.y = e.layerY;
        this.end.x = this.pointer.x;
        this.end.y = this.pointer.y;
        if (this.pointer.drawBox) {
          this.setDrawBoxStyle();
        }
      }
    },
    mousedown(e) {
      if (!this.canEdit) {
        return
      }
      if (e.button === 0) {
        this.pointer.showDrawBox = true
        this.pointer.drawBox = true
        this.start.x = this.end.x
        this.start.y = this.end.y
        document.addEventListener('mouseup', this.mouseup)
      }
    },
    mouseup(e) {
      if (!this.canEdit) {
        return
      }
      document.removeEventListener('mouseup', this.mouseup)

      if (e.button === 0) {
        this.pointer.drawBox = false

        let srcWidth = 1200;
        let width = this.$refs.image.width
        let height = this.$refs.image.height
        let imgWidth = Math.abs(this.end.x - this.start.x)
        let imgHeight = Math.abs(this.end.y - this.start.y)
        let startX = Math.min(this.end.x, this.start.x)
        let startY = Math.min(this.end.y, this.start.y)
        let scale = srcWidth / width;

        if (scale !== 1) {
          imgWidth = Math.round(imgWidth * scale)
          imgHeight = Math.round(imgHeight * scale)
          startX = Math.round(startX * scale)
          startY = Math.round(startY * scale)
        }

        let url = `${this.image.path}?x-oss-process=image/resize,w_${srcWidth},x-oss-process=image/crop,x_${startX},y_${startY},w_${imgWidth},h_${imgHeight}`;

        let box = [
          (this.end.x + this.start.x) / 2 / width,
          (this.end.y + this.start.y) / 2 / height,
          Math.abs(this.end.x - this.start.x) / width,
          Math.abs(this.end.y - this.start.y) / height,
        ]
        if (box[2] > 0.5) {
          this.$emit('add-ques-box', {page: this.index, box, url});
        } else {
          this.endAddBox()
        }
      }
    },
    getImage() {
      return this.$refs.image
    }
  }
};
</script>

<style lang="scss" scoped>
.ques-image {
  position: relative;
  width: 100%;

  .draw_box_image {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }

  .ques_box {
    position: absolute;
    border: solid 1px red;

    .ques-inner-draw {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }

    .ques_no_scroll {
      position: absolute;
      top: -30px;
    }

    .ques_delete {
      position: absolute;
      top: 5px;
      right: -15px;
      color: red;
      background: white;
      border-radius: 50%;
      font-size: 30px;
    }

    .ques_no {
      border: 3px solid red;
      border-radius: 50%;
      position: absolute;
      text-align: center;
      font-weight: 700;
      color: red;
      width: 30px;
      height: 30px;
      font-size: 15px;
      line-height: 25px;
      left: -15px;
      background: white;
      top: 5px;
    }

    .ques_answer_box {
      position: absolute;
      border: solid 3px yellow;

      .rec_result {
        position: absolute;
        color: red;
        font-size: 20px;
        left: 0;
        top: -23px;
        background: #ffffffaa;
        line-height: 1em;
      }

      .rec_error {
        position: absolute;
        color: red;
        font-size: 20px;
        left: 0;
        bottom: -23px;
        background: #ffffffaa;
        line-height: 1em;

        :before {
          content: '错误：';
        }
      }
    }
  }

  //指针位置
  .pointer-x {
    position: absolute;
    left: -100px;
    top: 0;
    width: 1px;
    height: 100%;
    background: #FD5959;
  }

  .pointer-y {
    position: absolute;
    left: 0;
    top: -100px;
    width: 100%;
    height: 1px;
    background: #FD5959;
  }


  img {
    width: 100%;
    padding: 0;
  }
}

</style>

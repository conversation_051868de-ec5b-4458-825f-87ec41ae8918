<template>
  <el-form label-position="left" label-width="80px" label-suffix="：">
    <el-form-item label="学科">
      <div class="check-form-content">
        <template v-if="!single">
          <el-checkbox
            v-model="isCheckAll"
            class="check-item check-all"
            border
            size="small"
            :indeterminate="isIndeterminate"
            @change="handleCheckAll"
            >全选</el-checkbox
          >
          <el-checkbox
            v-for="item in currentSubjectList"
            class="check-item"
            v-model="checkSubjectIdList"
            :key="item.id"
            :label="item.id"
            size="small"
            border
            @change="handleCheckChange"
            >{{ item.name }}</el-checkbox
          >
        </template>
        <template v-else>
          <el-radio-group class="radio-group" v-model="currectSubjectId" @change="handleSubjectIdChange">
            <el-radio class="radio-item" :label="item.id" v-for="item in currentSubjectList" :key="item.id" border>{{
              item.name
            }}</el-radio>
          </el-radio-group>
        </template>
        <div>
          <el-button
            v-if="addSubjectList.length > 0 && !examId"
            type="primary"
            icon="el-icon-circle-plus"
            size="small"
            @click="showAddSubjectDialog"
          ></el-button>
          <el-button
            v-if="removeSubjectList.length > 0 && !examId"
            type="primary"
            icon="el-icon-remove-outline"
            size="small"
            @click="showRemoveSubjectDialog"
          ></el-button>
        </div>
      </div>
    </el-form-item>

    <!-- 添加学科对话框 -->
    <el-dialog :visible.sync="isAddSubjectDialog" title="添加学科">
      <el-checkbox-group v-model="addSubjectIdList" class="check-form-content" size="small">
        <el-checkbox v-for="item in addSubjectList" class="check-item" :key="item.id" :label="item.id" border>{{
          item.name
        }}</el-checkbox>
      </el-checkbox-group>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isAddSubjectDialog = false">取消</el-button>
        <el-button type="primary" @click="handleAddSubject">添加</el-button>
      </div>
    </el-dialog>

    <!-- 删除学科对话框 -->
    <el-dialog :visible.sync="isRemoveSubjectDialog" title="删除学科">
      <el-checkbox-group v-model="removeSubjectIdList" class="check-form-content" size="small">
        <el-checkbox v-for="item in removeSubjectList" class="check-item" :key="item.id" :label="item.id" border>{{
          item.name
        }}</el-checkbox>
      </el-checkbox-group>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isRemoveSubjectDialog = false">取消</el-button>
        <el-button type="primary" @click="handleRemoveSubject">删除</el-button>
      </div>
    </el-dialog>
  </el-form>
</template>

<script lang="ts">
import UserRole from '@/utils/UserRole';
import { Component, Mixins, Prop, Vue } from 'vue-property-decorator';
import SchoolSettingMixin from './SchoolSetting.mixin';
import { SettingChangeParams } from '../types';

@Component({})
export default class SettingSubjectHeader extends Mixins(SchoolSettingMixin) {
  // 是否开启单选
  @Prop({ type: Boolean, default: false }) single: boolean;
  // 学科列表
  public allSubjectList = [];
  // 当前学科列表
  public currentSubjectList = [];
  // 选中的学科id列表
  public checkSubjectIdList = [];

  // 待添加学科列表
  get addSubjectList() {
    return this.allSubjectList.filter(item => !this.currentSubjectList.some(currentItem => currentItem.id === item.id));
  }
  // 待添加学科id列表
  addSubjectIdList = [];

  // 待删除学科列表
  get removeSubjectList() {
    return this.allSubjectList.filter(item => this.currentSubjectList.some(currentItem => currentItem.id === item.id));
  }
  // 待删除学科id列表
  removeSubjectIdList = [];

  // 是否显示添加对话框
  isAddSubjectDialog = false;
  // 是否显示删除对话框
  isRemoveSubjectDialog = false;
  // 是否全选
  isCheckAll = false;
  // 是否半选
  isIndeterminate = false;
  // 当前学科id
  currectSubjectId = '';

  // 设置当前学科列表
  public setCurrentSubjectIds(ids: string[]) {
    this.currentSubjectList = this.allSubjectList.filter(item => ids.some(id => id == item.id));
    this.checkSubjectIdList = this.currentSubjectList.map(item => item.id);
    this.checkIsCheckAll();
    if (this.single) {
      this.currectSubjectId = this.currentSubjectList[0]?.id;
    }
    this.emitChange();
  }

  mounted() {
    this.getSubjectList();
  }

  // 设置变化
  onSettingChange(data: SettingChangeParams) {
    this.getSubjectList();
  }

  // 获取学科列表
  async getSubjectList() {
    const ret = await UserRole.getUserInfoPersonalityTest();
    this.allSubjectList = ret.schSubList
      .map(item => ({
        ...item,
        examPhase: item.phaseId,
        name: item.name.replace('小学', '').replace('初中', '').replace('高中', ''),
      }))
      .filter(item => {
        if (this.getExamPhase()) {
          return item.examPhase == this.getExamPhase();
        }
        return true;
      });

    this.allSubjectList.unshift({
      id: '',
      name: '总分',
      examPhase: this.getExamPhase(),
    });

    this.currentSubjectList = this.getDefaultSubjectList();
    this.sortCurrectSubjectList();
    this.checkIsCheckAll();
    if (this.single) {
      this.currectSubjectId = this.currentSubjectList[0]?.id;
    }
    this.emitInit();
  }

  // 重置
  public reset() {
    this.resetSubject();
    this.emitInit();
  }

  // 重置学科
  public resetSubject() {
    this.currentSubjectList = this.getDefaultSubjectList();
    this.checkSubjectIdList = [];
    this.sortCurrectSubjectList();
    this.checkIsCheckAll();
  }

  getDefaultSubjectList() {
    let list = [];
    if (this.currentPhase == 1) {
      list = ['总分', '语文', '数学', '英语'];
    } else if (this.currentPhase == 2) {
      list = ['总分', '语文', '数学', '英语', '物理', '化学', '生物', '政治', '历史', '地理'];
    } else if (this.currentPhase == 3) {
      list = ['总分', '语文', '数学', '英语', '物理', '化学', '生物', '政治', '历史', '地理'];
    }
    return this.allSubjectList.filter(item => list.includes(item.name));
  }

  // 学科列表变化
  handleCheckChange() {
    this.checkIsCheckAll();
    this.emitChange();
  }

  // 显示添加学科对话框
  showAddSubjectDialog() {
    this.addSubjectIdList = [];
    this.isAddSubjectDialog = true;
  }

  // 删除学科
  showRemoveSubjectDialog() {
    this.removeSubjectIdList = [];
    this.isRemoveSubjectDialog = true;
  }

  // 添加学科
  handleAddSubject() {
    this.currentSubjectList = this.allSubjectList.filter(
      item =>
        this.currentSubjectList.some(currentItem => currentItem.id === item.id) ||
        this.addSubjectIdList.includes(item.id)
    );
    this.checkSubjectIdList = [...this.checkSubjectIdList, ...this.addSubjectIdList];
    this.sortCurrectSubjectList();
    this.isAddSubjectDialog = false;
    this.checkIsCheckAll();
    if (this.single) {
      if (this.currectSubjectId === undefined || this.currectSubjectId === null) {
        this.currectSubjectId = this.currentSubjectList[0]?.id;
      }
    }
    this.emitChange();
  }

  // 删除学科
  handleRemoveSubject() {
    this.currentSubjectList = this.currentSubjectList.filter(item => !this.removeSubjectIdList.includes(item.id));
    this.checkSubjectIdList = this.checkSubjectIdList.filter(item => !this.removeSubjectIdList.includes(item));
    this.sortCurrectSubjectList();
    this.isRemoveSubjectDialog = false;
    if (this.single) {
      if (this.currentSubjectList.findIndex(item => item.id == this.currectSubjectId) == -1) {
        this.currectSubjectId = this.currentSubjectList[0]?.id;
      }
    }
    this.emitChange();
  }

  // 学科排序
  sortCurrectSubjectList() {
    this.currentSubjectList = this.allSubjectList.filter(item =>
      this.currentSubjectList.some(currentItem => currentItem.id === item.id)
    );
  }

  // 检查全选
  checkIsCheckAll() {
    this.isCheckAll = this.checkSubjectIdList.length === this.currentSubjectList.length;
    this.isIndeterminate =
      this.checkSubjectIdList.length > 0 && this.checkSubjectIdList.length < this.currentSubjectList.length;
  }

  // 全选
  handleCheckAll(value: boolean) {
    this.checkSubjectIdList = value ? this.currentSubjectList.map(item => item.id) : [];
    this.isIndeterminate = false;
    this.isCheckAll = value;
    this.emitChange();
  }

  // 单选学科变化
  handleSubjectIdChange(id: string) {
    this.$emit('changeSubjectId', id);
  }

  // 发送学科列表初始化事件
  emitInit() {
    if (this.single) {
      this.$emit('init', this.currentSubjectList);
    } else {
      let subjectList = this.allSubjectList.filter(item => this.checkSubjectIdList.includes(item.id));
      this.$emit('init', subjectList);
    }
  }

  // 发送学科列表变化事件
  emitChange() {
    if (this.single) {
      this.$emit('change', this.currentSubjectList);
      this.$emit('changeSubjectId', this.currectSubjectId);
    } else {
      let subjectList = this.allSubjectList.filter(item => this.checkSubjectIdList.includes(item.id));
      this.$emit('change', subjectList);
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-checkbox.is-bordered + .el-checkbox.is-bordered {
  margin-left: 0;
}

.check-form-content {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;

  .check-item {
    margin-right: 0;
  }
}

.radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;

  .radio-item {
    margin-left: 0 !important;
    margin-right: 0;
  }
}
</style>

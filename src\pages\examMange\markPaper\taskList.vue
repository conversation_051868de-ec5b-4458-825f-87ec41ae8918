<template>
  <div class="examReport__main flex_1 display_flex flex-direction_column">
    <div class="exam-card-content">
      <!-- <div class="header__select">
                <span class="select__label">学科：</span>
                <el-select v-model="pagination.subjectValue" class="grade-select" @change="getCorrectExamList" placeholder="请选择">
                    <el-option v-for="item in subjectList" v-if="item.show || item.show == undefined" :key="item.id"
                        :label="item.name" :value="item.id">
                    </el-option>
                </el-select>
            </div> -->
      <div class="header-box">
        <div class="header__select">
          <!-- <span class="select__label">时间：</span> -->
          <el-select
            v-model="timeValue"
            style="width: 80px; margin-right: 15px"
            @change="changeTimeValue()"
          >
            <el-option
              v-for="item in timeType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <!-- 学年 -->
          <el-select
            v-if="timeValue == 3"
            v-model="yearValue"
            class="year-select"
            @change="changeYear"
            placeholder="请选择"
            style="width: 150px"
          >
            <el-option v-for="item in years" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
          <!-- 按月 -->
          <el-date-picker
            v-if="timeValue == 2"
            popper-class="datePicker__time"
            style="width: 240px"
            v-model="timeSlot"
            :clearable="false"
            type="monthrange"
            align="right"
            range-separator="—"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            @change="changeMonth"
          >
          </el-date-picker>
          <!-- 按日 -->
          <el-date-picker
            v-if="timeValue == 1"
            popper-class="datePicker__time"
            style="width: 240px"
            v-model="timeSlot"
            :clearable="false"
            type="daterange"
            align="right"
            unlink-panels
            format="yyyy-MM-dd"
            range-separator="—"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="changeDay"
          >
          </el-date-picker>
        </div>
        <div class="header__select">
          <div class="header-serarch clearfix display_flex">
            <el-input
              class="search__text"
              placeholder="输入名称搜索"
              v-model="pagination.keyWord"
              @keyup.enter.native="handleSearch"
              clearable
            >
            </el-input>
            <div
              class="search-icon el-icon-search display_flex align-items_center justify-content_flex-center"
              @click="handleSearch"
            ></div>
          </div>
        </div>
      </div>
      <div class="examReport__content">
        <!--个册列表数据-->
        <ul
          id="popoverUl"
          class="examReport__list list-none"
          v-if="examReportList.length"
          v-loading="loading"
          element-loading-text="加载中..."
        >
          <li v-for="(item, index) in examReportList" :key="index">
            <task-item :item="item" :index="index"></task-item>
          </li>
        </ul>
        <!--没有个册数据缺省图-->
        <div class="nodata flex_1" v-if="!loading && !examReportList.length">
          <img :src="noResImg" alt="" />
          <p class="text-center">暂无数据</p>
        </div>
        <!--分页器-->
        <el-pagination
          background
          style="margin-bottom: 30px"
          :hide-on-single-page="pagination.page <= 1"
          class="text-center"
          layout="total, prev, pager, next"
          @current-change="handleCurrentChange"
          :current-page.sync="pagination.page"
          :page-size="pagination.limit"
          :total="pagination.total_rows"
        >
        </el-pagination>
      </div>
    </div>
    <!-- <div class="examReport__content flex_1" :class="examReportList.length ? '' : 'display_flex align-items_center'">
            
        </div> -->
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import TaskItem from "@/components/TaskItem/index.vue";
import {
  getMarkingTaskList,
  getSchoolYearListAPI,
  getUserInfoToPersonalityTest,
} from "@/service/api";
import UserRole from "@/utils/UserRole";
import moment from "moment";

export default {
  name: "mark-paper-index",
  data() {
    return {
      // 需要阅卷的考试列表
      examReportList: [],
      // 加载中
      loading: false,
      // 分页
      pagination: {
        page: 1,
        limit: 10,
        total_rows: 0,
        keyWord: "",
        subjectValue: "",
      },
      noResImg: require("../../../assets/no-res.png"),
      //年级列表
      gradeList: [],
      //学科列表
      subjectList: [],
      timer: null,
      timerGap: 20000,
      pageType: "",
      // 学年列表
      years: [],
      yearValue: '',
      //时间筛选类型
      timeType: [
        { value: 1, label: '按日' },
        { value: 2, label: '按月' },
        { value: 3, label: '学年' },
      ],
      timeValue: 2,
      timeSlot: [],
      defMonthSlot: [],
    };
  },
  components: {
    TaskItem,
  },
  computed: {
    ...mapGetters([]),
    // 对选择后的时间格式化
    timeSlotFormat() {
      return [
        this.timeSlot[0] ? this.$formatDate(new Date(this.timeSlot[0])) : '',
        this.timeSlot[0] ? this.$formatDate(new Date(this.timeSlot[1])) : '',
      ];
    },
  },
  created() {},
  beforeDestroy() {
    this.clearTimer();
  },
  mounted() {
    this.pageType = this.$sessionSave.get("pageType") || "testMge";
    this.initMounted();
    this.changeTimeValue();
    // this.getSubjectList();
    // this.getCorrectExamList();
  },
  methods: {
    async initMounted() {
      const start = moment().subtract(1, 'months').startOf('month').format('YYYY-MM-DD');
      const end = moment().add(1, 'months').endOf('month').format('YYYY-MM-DD');
      this.defMonthSlot = [start, end];

      const res = await getSchoolYearListAPI({
        schoolId: this.$sessionSave.get('schoolInfo').id
      })
      const years = res.data || [];
      this.years = years.map(item => {
        return {
          id: item.schoolYearId,
          name: item.schoolYear
        }
      })
      this.years.unshift({ id: '', name: '全部学年' });
      this.yearValue = this.years[1].id;
    },
    //切换时间筛选类型，按日默认最近一周，按月默认当前月
    changeTimeValue() {
      let date = new Date(),
        y = date.getFullYear(),
        M = date.getMonth(),
        d = date.getDate();
      if (this.timeValue === 1) {
        this.timeSlot = [new Date(y, M, d - 7), new Date(y, M, d)];
      } else if (this.timeValue === 2) {
        this.timeSlot = this.defMonthSlot;
      }
      this.pagination.page = 1;
      this.getCorrectExamList();
    },
    changeDay(val){
      this.pagination.page = 1;
      this.getCorrectExamList();
    },
    // 选择月份筛选后
    changeMonth(val) {
      const start = moment(val[0]).startOf('month').format('YYYY-MM-DD');
      const end = moment(val[1]).endOf('month').format('YYYY-MM-DD');
      this.timeSlot = [start, end];
      this.pagination.page = 1;
      this.getCorrectExamList();
    },
    // 切换学年
    changeYear(val) {
      this.pagination.page = 1;
      this.getCorrectExamList();
    },
    /**
     * @name: 获取学科列表
     */
    async getSubjectList() {
      let accountType = this.$sessionSave.get("loginInfo").account_type;
      const ret = await UserRole.getUserInfoPersonalityTest();
      if (accountType == 4 || accountType == 5) {
        this.grdList = [];
      }
      this.subjectList = [];
      if (this.accountType == 4 || this.accountType == 5) {
        ret.userGrdList.forEach((v, i) => {
          v.show = true;
          this.gradeList.push(v);
        });
        this.gradeList.unshift({
          id: "",
          name: "全部",
        });
      }

      ret.userSubList.forEach((v, i) => {
        v.show = true;
        this.subjectList.push(v);
      });

      this.subjectList.unshift({
        id: "",
        name: "全部",
      });
    },
    /**
     * @name: 获取考试报告列表
     */
    getCorrectExamList(showLoading = true) {
      this.loading = showLoading;
      this.clearTimer();
      let params = {
        schoolId: this.$sessionSave.get("schoolInfo").id,
        userId: this.$sessionSave.get("loginInfo").id,
        keyWord: this.pagination.keyWord,
        page: this.pagination.page, // 页码
        pageSize: this.pagination.limit,
        schoolYearId: this.timeValue == 3 ? this.yearValue : '',
        begin: this.timeValue != 3 ? this.timeSlotFormat[0] : '',
        end: this.timeValue != 3 ? this.timeSlotFormat[1] : '',
      };
      getMarkingTaskList(params)
        .then((res) => {
          this.examReportList = res.data.rows;
          this.pagination.total_rows = res.data.total_rows;
          this.loading = false;
          if (this.examReportList.length && this.pageType == "checkTask") {
            if (
              !this.checkPaperState(this.examReportList) &&
              this.$sessionSave.get("pageType") == "checkTask"
            ) {
              this.timer = setTimeout(() => {
                this.getCorrectExamList(false);
              }, this.timerGap);
            }
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    /**
     * @name:判断阅卷是否结束
     */
    checkPaperState(data) {
      // let dataLength = data.length;
      // for (let i = 0; i < dataLength; i++) {
      //   let item = data[i];
      //   if (item.isOver == 0 && item.allStuQuesCount > 0) {
      //     return false;
      //   }
      // }
      return false;
    },
    /**
     * @name：清除定时器
     */
    clearTimer() {
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
    },
    // 分页查询
    handleCurrentChange(val) {
      this.clearTimer();
      this.pagination.page = val;
      this.getCorrectExamList("changePage");
    },
    /**
     * @name：查询
     */
    handleSearch() {
      this.clearTimer();
      this.pagination.page = 1;
      this.getCorrectExamList();
    },
  },
};
</script>

<style lang="scss" scoped>
.examReport__main {
  width: 100%;
  font-size: 16px;
  margin-top: unset !important;
  overflow: auto;

  .exam-card-content {
    position: relative;
    // padding: 0px 15px;
    background: #fff;
    font-size: 14px;
    // border: 1px solid #e4e7ed;
    border-top: unset;

    .header-box {
      position: relative;
      padding: 15px 5px;
      font-size: 14px;
      border-top: unset;
    }

    .header__select {
      display: inline-block;
      margin-right: 10px;
      vertical-align: top;
    }

    .header-serarch {
      display: flex;
      width: 240px;

      .search-icon {
        width: 38px;
        font-size: 18px;
        color: #fff;
        background: #409eff;
        border-radius: 0 3px 3px 0;
        outline: none;
        cursor: pointer;
      }
    }
  }

  .examReport__content {
    width: 100%;
    margin-top: 14px;

    .examReport__list {
      width: 100%;
    }

    .nodata {
      width: 100%;
      height: auto;

      img {
        display: block;
        margin: 0 auto;
      }
    }
  }

  .nodata {
    width: 100%;
    height: auto;

    img {
      display: block;
      margin: 0 auto;
    }
  }
}
</style>
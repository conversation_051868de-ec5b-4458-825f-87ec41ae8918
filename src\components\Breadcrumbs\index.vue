<template>
    <el-breadcrumb separator-class="el-icon-arrow-right" class="header-bc">
        <el-breadcrumb-item @click.native="goBack" style="cursor: pointer;">
            <i class="el-icon-back"></i>
            返回
        </el-breadcrumb-item>
        <el-breadcrumb-item v-if="title">{{ title }}
            <slot name="titleSlot"></slot>
        </el-breadcrumb-item>
    </el-breadcrumb>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
export default {
    name: 'bread-crumbs',
    components: {
    },
    props: ['title'],
    data() {
        return {
        }
    },
    computed: {
        ...mapGetters([
        ])
    },
    created() {
    },
    mounted() {
    },
    methods: {
        /**
         * @name: 返回上一页
         */
        goBack() {
            if(this.$listeners.goBack) {
                this.$emit('goBack')
            } else {
                this.$router.back(-1);
            }
        }
    }
}
</script>
  
<style lang="scss" scoped>
.header-bc {
    line-height: 40px;
    font-size: 16px;
    // margin-bottom: 20px;
}
</style>
<style lang="scss">
.header-bc {
    .el-breadcrumb__separator {
        color: #606266;
    }
}
</style>
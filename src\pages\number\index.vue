﻿<template>
  <div class="number-setting-container">
    <!--头部返回-->
    <el-page-header @back="goBack">
      <template #content>
        <el-row type="flex" align="middle">
          <el-select
              v-model="apiParams.provinceId"
              placeholder="请选择省份"
              @change="switchProvince"
              class="select-item">
            <el-option
                v-for="item in provinceList"
                :key="item.id"
                :label="item.areaName"
                :value="item.id">
              {{ item.areaName }}
            </el-option>
          </el-select>
          <el-select
              v-model="apiParams.cityId"
              placeholder="请选择城市"
              @change="switchCity"
              :disabled="!apiParams.provinceId"
              class="select-item">
            <el-option
                v-for="item in cityList"
                :key="item.id"
                :label="item.areaName"
                :value="item.id"
            >
              {{ item.areaName }}
            </el-option>
          </el-select>
          <el-select
              v-model="apiParams.districtId"
              placeholder="请选择地区"
              :disabled="!apiParams.cityId"
              @change="switchPage(1)"
              class="select-item">
            <el-option
                v-for="item in areaList"
                :key="item.id"
                :label="item.areaName"
                :value="item.id"
            >
              {{ item.areaName }}
            </el-option>
          </el-select>
          <div class="header__serarch clearfix display_flex select-item">
            <el-input class="search__text"
                      placeholder="输入学校名称搜索"
                      v-model="apiParams.keyWord"
                      @clear="switchPage(1)"
                      @keyup.enter.native="switchPage(1)"
                      clearable>
            </el-input>
            <div class="search__icon el-icon-search display_flex align-items_center justify-content_flex-center"
                 @click="switchPage(1)"></div>
          </div>
        </el-row>
      </template>

    </el-page-header>
    <!--表格列表-->
    <div class="table-container">
      <el-table
          v-loading="isLoading"
          ref="tableRef"
          class="number-table"
          :data="schoolList"
          :row-style="{padding:0}"
          :cell-style="{ position:'relative'}"
          stripe
      >
        <el-table-column prop="schoolName" label="学校名称"></el-table-column>
        <el-table-column label="区域">
          <template slot-scope="scope">
            {{ scope.row.provinceName }} {{ scope.row.cityName }} {{ scope.row.districtName }}
          </template>
        </el-table-column>
        <el-table-column label="学制" align="center">
          <template slot-scope="scope">
            {{ scope.row.systemName || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200">
          <template slot-scope="scope">
            <el-button
                v-if="scope.row.numberStatus===STATE_TYPE.LOADING"
                type="text"
                disabled
                @click="toBuild(scope.row)">
              正在生成
            </el-button>
            <el-button
                v-else
                type="text"
                @click="toBuild(scope.row)">
              {{ scope.row.numberStatus === STATE_TYPE.FINISHED ? '重新生成' : '一键生成' }}
            </el-button>
            <el-button type="text" @click="toExport(scope.row)"
                       :disabled="scope.row.numberStatus!==STATE_TYPE.FINISHED">导出
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!--分页器-->
      <el-pagination
          background
          style="margin:20px auto 0;"
          :hide-on-single-page="!schoolList.length"
          class="text-center"
          layout="total, prev, pager, next"
          @current-change="switchPage"
          :current-page.sync="apiParams.page"
          :page-size="apiParams.limit"
          :total="totalCount">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import {getRegion} from '@/service/api'
import {getSchoolListAPI, generateAPI, updateSchoolExamNumberAPI, exportNumberAPI} from '@/service/pexam'
import {mapState} from 'vuex';

export default {
  name: "number",

  data() {
    return {
      // 请求参数
      apiParams: {
        // 页码
        page: 1,
        // 请求条目
        limit: 10,
        // 省id
        provinceId: '',
        // 市id
        cityId: '',
        // 区id
        districtId: '',
        // 关键词
        keyWord: ''
      },
      // 页码总数
      pageSize: 0,
      // 数据总数
      totalCount: 0,
      // 学校列表
      schoolList: [],
      // 省份列表
      provinceList: [{areaName: '全部省份', id: ''}],
      // 城市列表
      cityList: [{areaName: '全部城市', id: ''}],
      // 地区列表
      areaList: [{areaName: '全部地区', id: ''}],
      // 是否正在加载
      isLoading: false,
      // 区域类型
      REGION_TYPE: {
        // 省
        PROVINCE: 1,
        // 市
        CITY: 2,
        // 区
        AREA: 3,
      },
      //考号生成状态
      STATE_TYPE: {
        // 考号未生成
        NO: 0,
        // 生成考号中
        LOADING: 1,
        // 生成完成
        FINISHED: 2
      }
    }
  },

  computed: {
    ...mapState(['loginInfo', 'gradeList', 'roleList'])
  },

  mounted() {
    this.getRegionList(this.REGION_TYPE.PROVINCE)
    this.getSchoolList()
  },

  methods: {
    /**
     * @name: 获取地区列表
     * @param type 地区类型 1获取省2获取市 3获取区
     * @param id
     */
    async getRegionList(type, id = '') {

      let data = []
      let res = await getRegion({type, id})
      if (res.code === 1) {
        data = res.data
      } else {
        this.$message.warning(res.msg);

      }
      switch (type) {
        case this.REGION_TYPE.PROVINCE:
          this.provinceList = [{areaName: '全部省份', id: ''}, ...data]
          break
        case this.REGION_TYPE.CITY:
          this.cityList = [{areaName: '全部城市', id: ''}, ...data]
          break
        case this.REGION_TYPE.AREA:
          this.areaList = [{areaName: '全部地区', id: ''}, ...data]
          break
      }
    },
    /**
     * @name: 获取学校列表
     */
    async getSchoolList() {
      this.isLoading = true
      getSchoolListAPI({
        ...this.apiParams,
        roleIds: this.roleList.join(','),
        userId: this.loginInfo.id
      }).then((res) => {
        this.pageSize = res.data.page_count
        this.totalCount = res.data.total_rows
        this.schoolList = res.data.rows
        this.isLoading = false
      }).catch((err) => {
        this.isLoading = false
      })
    },
    /**
     * @name: 返回
     */
    goBack() {
      this.$router.back()
    },
    /**
     * @name: 分页切换
     * @param page 当前页码
     */
    switchPage(page) {
      this.apiParams.page = page
      this.getSchoolList()
    },
    /**
     * @name: 切换省份
     */
    switchProvince() {
      this.apiParams.cityId = ''
      this.apiParams.districtId = ''
      this.getRegionList(this.REGION_TYPE.CITY, this.apiParams.provinceId)
      this.switchPage()
    },
    /**
     * @name: 切换城市
     */
    switchCity() {
      this.apiParams.districtId = ''
      this.getRegionList(this.REGION_TYPE.AREA, this.apiParams.cityId)
      this.switchPage()
    },
    /**
     * @name: 一键生成
     * @param item 当前学校对象
     */
    async toBuild(item) {
      let res = await generateAPI({schoolId: item.schoolId})
      if (res.code === 1) {
        this.switchPage()
      } else {
        this.$message.warning(res.msg)
      }

    },
    /**
     * @name: 导出
     * @param item 当前学校对象
     */
    toExport(item) {
      this.$prompt('请输入邮箱', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,
        inputErrorMessage: '邮箱格式不正确'
      }).then(async ({value}) => {
        const params = {
          schoolId: item.schoolId,
          emailAddress: value,
          optUserId: this.loginInfo.id
        }
        const res = await exportNumberAPI(params)
        if (res.code === 1) {
          this.$message.success(`考号已发送至邮箱${value},请稍后查收!`)
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    /**
     * @name: 刷新
     * @param item 当前学校对象
     */
    async toRefresh(item) {
      let res = await updateSchoolExamNumberAPI({schoolId: item.schoolId})
      if (res.code === 1) {
        item.numberStatus = res.data
        this.$forceUpdate()
      } else {
        this.$message.warning(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.number-setting-container {
  .table-container {
    padding: 20px;
    border-radius: 12px;
    background-color: #fff;
  }

  .el-page-header {
    height: 80px;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background-color: #fff;
    border-radius: 12px;

    .select-item {
      margin-right: 30px;
    }

    .header__serarch {
      display: flex;
      width: 240px;

      .search__icon {
        width: 38px;
        font-size: 18px;
        color: #fff;
        background: #409eff;
        border-radius: 0 3px 3px 0;
        outline: none;
        cursor: pointer;
      }
    }
  }

}
</style>
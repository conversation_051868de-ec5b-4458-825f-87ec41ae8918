<template>
  <div class="teaching">
    <div class="tab-wrapper">
      <div
        v-for="item in tabListTemp"
        :key="item.id"
        class="tab-item"
        :class="{ active: activeTab == item.id }"
        @click="changeRadioTab(item.id)"
      >
        {{ item.name }}
      </div>

      <!-- <el-radio-group v-model="activeTab" class="radioGroup" @change="changeRadioTab">
        <el-radio-button :label="item.id" v-for="item in tabListTemp" :key="item.id">{{
          item.name
        }}</el-radio-button>
      </el-radio-group> -->
    </div>
    <div>
      <div
        class="teachingPage-header"
        v-if="
          $route.path.indexOf('classDetail') < 0 &&
          $route.path.indexOf('previewPaper') < 0 &&
          activeTab != 3 &&
          activeTab != 4
        "
      >
        <div>
          <!--来源-->
          <div class="header__select" v-show="activeTab">
            <span class="select__label">来源：</span>
            <el-select
              v-model="sourceVal"
              multiple
              @change="changeSource"
              :collapse-tags="false"
              style="width: 220px"
              class="source-select"
              placeholder="请选择"
            >
              <el-option v-for="item in sourceList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </div>

          <!--学科-->
          <div class="header__select" v-if="activeTab != 0">
            <span class="select__label">学科：</span>
            <el-select
              v-model="subjectVal"
              style="width: 140px"
              class="source-select"
              @change="changeSubject"
              placeholder="请选择"
            >
              <el-option v-for="item in subjectList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </div>
          <!-- 学情追踪学科 -->
          <div class="header__select" v-else>
            <span class="select__label">学科：</span>
            <el-select
              v-model="subjectTrack"
              style="width: 140px"
              class="source-select"
              @change="changeTrackSubject"
              placeholder="请选择"
            >
              <el-option v-for="item in subjectListTrackFilter" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </div>
          <!-- 年级 -->
          <div class="header__select" v-show="activeTab">
            <span class="select__label">年级：</span>
            <el-select
              v-model="gradeValue"
              style="width: 120px"
              class="source-select"
              @change="changeGrade"
              placeholder="请选择"
            >
              <el-option v-for="item in grdList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </div>
          <!-- 班级 -->
          <div class="header__select" v-show="activeTab">
            <span class="select__label">班级：</span>
            <el-select
              ref="grdSelectRef"
              v-model="classValue"
              style="width: 120px"
              class="source-select"
              @change="changeClass"
              placeholder="请选择"
            >
              <el-option v-for="item in classList" :key="item.clsId" :label="item.clsName" :value="item.clsId">
              </el-option>
            </el-select>
          </div>
          <!--时间-->
          <div class="header__select" v-show="activeTab">
            <span class="select__label">时间：</span>
            <el-select v-model="timeValue" style="width: 100px; margin-right: 25px" @change="changeTimeValue()">
              <el-option v-for="item in timeType" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
            <!-- 学年 -->
            <el-select
              v-model="yearTimeSlot"
              v-if="timeValue === 3"
              style="width: 250px; margin-right: 10px"
              @change="changeYearValue()"
            >
              <el-option v-for="item in yearList" :key="item.id" :label="item.label" :value="item.value"> </el-option>
            </el-select>
            <!-- 按月 -->
            <el-date-picker
              v-if="timeValue === 2"
              popper-class="datePicker__time"
              style="margin-right: 10px; width: 250px"
              v-model="timeSlot"
              :clearable="false"
              type="monthrange"
              align="right"
              range-separator="-"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              @change="beforeFindPersonalBookListMonth"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
            <!-- 按日 -->
            <el-date-picker
              v-if="timeValue === 1"
              popper-class="datePicker__time"
              style="margin-right: 10px; width: 250px"
              v-model="timeSlot"
              :clearable="false"
              type="daterange"
              align="right"
              unlink-panels
              format="yyyy-MM-dd"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="getClassList"
              :picker-options="pickerOptionsDay"
            >
            </el-date-picker>
          </div>
          <el-button type="primary" @click="handleFindData">查询</el-button>
          <el-button v-if="!activeTab" @click="openFilterExamDialog">选择考试</el-button>
          <span v-if="!activeTab" class="filter-text"
            >当前展示&nbsp; <span class="grade-text">{{ gradeName }}</span
            >&nbsp;&nbsp;年级考试学情追踪数据，可通过筛选考试查看更多</span
          >
        </div>

        <div v-show="!activeTab">
          <el-button class="export-button" type="text" @click="openSetDownloadDialog"
            >一键导出<i class="el-icon-download el-icon--right"></i
          ></el-button>
        </div>
      </div>
      <router-view
        class="teaching-main"
        @getParams="getParams"
        ref="teachingRef"
        style="width: 100%"
        :subjectId="subjectVal"
        :classId="classValue"
        @updateData="findData"
        @scrollToTop="scrollToTop"
      ></router-view>
    </div>

    <set-download-dialog
      v-if="isSetDownloadDialog"
      @close-dialog="closeDialog"
      @get-download="downloadReport"
    ></set-download-dialog>
    <filter-exam-dialog v-if="isFilterExamDialog" @close-dialog="closeDialog" @get-exam-ids="getFilterExamIds">
    </filter-exam-dialog>
  </div>
</template>

<script>
import {
  getUserInfoToPersonalityTest,
  getClassList,
  getUserRoles,
  getSubjectListByRole,
  getGradeListByRole,
  getSchoolYearListAPI,
} from '@/service/api';
import { ClassList } from '@/service/pexam';
import { mapState } from 'vuex';
import filterExamDialog from '../../components/scoreDistribution/filterExamDialog.vue';
import setDownloadDialog from '../../components/scoreDistribution/setDownloadDialog.vue';
import { getToken } from '@/service/auth';
import UserRole from '@/utils/UserRole';
import moment from 'moment';

// 当前日期
let today = new Date();
// 当前月
// 当前年
let nowYear = today.getYear();
nowYear += nowYear < 2000 ? 1900 : 0;
export default {
  components: { filterExamDialog, setDownloadDialog },
  name: 'index',
  data() {
    return {
      casckey: 1,
      //时间筛选类型
      timeType: [
        { value: 1, label: '按日' },
        { value: 2, label: '按月' },
        { value: 3, label: '学年' },
      ],
      timeValue: 3,
      //学年
      yearList: [],
      tabList: [
        { id: 0, name: '年级学情', path: 'track', roles: [0, 1, 2, 3, 4] },
        { id: 1, name: '班级学情', path: 'class' },
        { id: 2, name: '学生学情', path: 'student' },
        { id: 3, name: '查询学生历次成绩', path: 'score' },
        { id: 4, name: '学生增值评价', path: 'stuGrowth' },
      ],
      tabListTemp: [],
      activeTab: 0,
      sourceVal: [1],
      sourceList: [
        { id: 1, name: '考试' },
        { id: 2, name: '个册' },
        { id: 3, name: '作业' },
      ],
      // 选择的具体时间数值
      defTimeSlot: [],
      defMonthSlot: [],
      timeSlot: [],
      yearTimeSlot: '',
      pickerMinDate: '', //第一次选中的时间
      pickerOptions: {
        //选择当前日期之前的时间
        onPick: obj => {
          this.pickerMinDate = new Date(obj.minDate).getTime();
        },
        //选择时间范围为一年
        disabledDate: time => {
          if (this.pickerMinDate) {
            const day1 = 364 * 24 * 3600 * 1000;
            let maxTime = this.pickerMinDate + day1;
            let minTime = this.pickerMinDate - day1;
            return time.getTime() > Date.now() || time.getTime() > maxTime || time.getTime() < minTime;
          }
        },
      },
      pickerOptionsDay: {
        //选择当前日期之前的时间
        onPick: obj => {
          this.pickerMinDate = new Date(obj.minDate).getTime();
        },
        //可选择的时间范围为一年
        disabledDate: time => {
          if (this.pickerMinDate) {
            const day1 = 365 * 24 * 3600 * 1000;
            let maxTime = this.pickerMinDate + day1;
            let minTime = this.pickerMinDate - day1;
            return time.getTime() > Date.now() || time.getTime() > maxTime || time.getTime() < minTime;
          }
        },
      },
      cascaderVal: [],
      cascAll: [],
      cascGrds: [],
      subjectList: [],
      grdList: [],
      allGradeList: [],
      subjectVal: '',
      gradeValue: '',
      classList: [],
      classValue: '',
      paramsData: {},
      account_type: '',
      pms: {},
      //角色列表
      roleList: [],
      roles: '',
      //是否打开下载设置弹窗
      isSetDownloadDialog: false,
      //是否打开筛选考试弹窗
      isFilterExamDialog: false,
      //已选择的对比考试ids
      compareExamIds: [],
      subjectTrack: '',
      subjectListTrack: [],
      //班级名
      className: '',
      //需要导出的考试id
      downloadExamIds: [],
      fsUrl: process.env.VUE_APP_KKLURL,
      gradeName: '',
      userId: '',

      trackPhaseId: '',
    };
  },
  watch: {
    yearTimeSlot: function (v) {
      this.timeSlot = v.split('|');
    },
    $route(to, from) {
      this.matchingRoutes();
      this.replaceFirstTab();
    },
  },
  computed: {
    ...mapState(['teachParams']),
    // 对选择后的时间格式化
    timeSlotFormat() {
      return [
        this.timeSlot[0] ? this.$formatDate(new Date(this.timeSlot[0])) : '',
        this.timeSlot[0] ? this.$formatDate(new Date(this.timeSlot[1])) : '',
      ];
    },

    subjectListTrackFilter() {
      if (!this.trackPhaseId) return this.subjectListTrack;
      const subjectList = this.subjectListTrack.filter(item => item.id == '' || item.phaseId == this.trackPhaseId);
      return subjectList;
    },
  },
  async mounted() {
    this.userId = this.$sessionSave.get('loginInfo') && this.$sessionSave.get('loginInfo').id;
    if (this.teachParams.schoolId != this.$sessionSave.get('schoolInfo').id) {
      this.$store.commit('saveTeachParams', {});
    }
    this.downloadExamIds = this.$localSave.get(
      'compareExamIds_' + this.$sessionSave.get('schoolInfo').id + '_' + this.userId
    );
    this.gradeName = this.$localSave.get(
      'compareExamGradeName_' + this.$sessionSave.get('schoolInfo').id + '_' + this.userId
    );
    await this.initTabRole();
    await this.initSchool();
    await this.replaceFirstTab();
    await this.$nextTick();
    this.findData();
  },
  methods: {
    // 进入第一个Tab
    async replaceFirstTab() {
      if (this.$route.path === '/home/<USER>' || this.$route.path === '/home/<USER>/teaching') {
        this.activeTab == this.tabListTemp[0].id;
        await this.$router.replace({
          path: `/home/<USER>/${this.tabListTemp[0].path}`,
        });
      }
    },

    // 初始化Tab权限
    async initTabRole() {
      const roles = UserRole.examRolesTypes.split(',');

      // 筛选含有权限的tab列表
      const tabList = this.tabList.filter(item => {
        if (UserRole.isSchoolLeader || UserRole.isOperation) {
          return item;
        }
        const tabRoles = item.roles;
        if (!tabRoles) {
          return item;
        }
        const list = tabRoles.filter(item => {
          return roles.includes(String(item));
        });
        return !item.roles || list.length;
      });
      this.tabListTemp = tabList;
    },

    scrollToTop() {
      this.$emit('scrollToTop');
    },
    //切换时间筛选类型，按日默认最近一周，按月默认当前月
    changeTimeValue() {
      this.pickerMinDate = '';
      let date = new Date(),
        y = date.getFullYear(),
        M = date.getMonth(),
        d = date.getDate();
      if (this.timeValue === 1) {
        this.timeSlot = [new Date(y, M, d - 7), new Date(y, M, d)];
      } else if (this.timeValue === 2) {
        this.timeSlot = this.defMonthSlot;
      } else {
        this.yearTimeSlot = this.yearList[0].value;
        this.timeSlot = this.yearTimeSlot.split('|');
      }
      this.getClassList();
    },
    changeYearValue() {
      setTimeout(() => {
        this.getClassList();
      }, 100);
    },
    /**
     * @name：切换年级
     */
    changeGrade() {
      let info = this.grdList.filter(item => {
        return this.gradeValue == item.id;
      })[0];
      this.getClassList();
    },
    /**
     * @name:切换班级
     */
    changeClass() {
      this.className = this.classList.filter(item => {
        return item.clsId == this.classValue;
      })[0].clsName;
      this.findData();
    },
    /**
     * @name:获取班级
     * @param {*} subjectId  学科id
     * @param {*} gradeId 年级Id
     * @param {*} startTime  开始时间
     * @param {*} endTime 结束时间
     */
    async getClassList() {
      this.classList = [];
      let token = getToken();
      let grade = this.allGradeList.find(t => t.id == this.gradeValue);
      let subject = this.subjectList.find(t => t.id == this.subjectVal);
      let classList = await UserRole.getClassList({
        year: grade?.year,
        systemCode: grade?.systemCode,
        subjectId: subject.id,
        classType: -1,
      });

      this.classList = classList.map(item => {
        return {
          clsId: item.classId,
          clsName: item.class_name,
        };
      });

      let isAddAllClass = this.isAddAllClass();
      if (isAddAllClass) {
        this.addAllClass();
      }

      if (this.classList.length > 0) {
        const classItem = this.classList.find(item => item.clsId == this.classValue);
        if (classItem) {
          this.classValue = classItem.clsId;
          this.className = classItem.clsName;
        } else {
          this.classValue = this.classList[0].clsId;
          this.className = this.classList[0].clsName;
        }

        await this.$nextTick();
        if (this.$refs.grdSelectRef) {
          this.$refs.grdSelectRef.setSelected();
        }
        this.findData();
      }
    },
    /***
     * @name:班级名排序
     */
    convertToNumber(str) {
      const numberMap = {
        一: 1,
        二: 2,
        三: 3,
        四: 4,
        五: 5,
        六: 6,
        七: 7,
        八: 8,
        九: 9,
        十: 10,
        百: 100,
        千: 1000,
        万: 10000,
      };
      let result = 0;
      let temp = 0;
      let isTen = false; // 是否遇到了十
      let isNumber = false; // 是否遇到了纯数字
      for (let i = 0; i < str.length; i++) {
        const char = str[i];
        if (numberMap[char]) {
          if (numberMap[char] >= 10) {
            if (isTen) {
              result += temp * numberMap[char];
              temp = 0;
            } else {
              temp = Math.max(1, temp) * numberMap[char];
              isTen = true;
            }
          } else {
            temp += numberMap[char];
            isNumber = true;
          }
        }
      }
      result += temp;
      if (!isNumber) {
        result = parseInt(str);
      }
      return result;
    },
    /**
     * @name:导出
     */
    async downloadReport(contain) {
      let ids = this.$localSave.get('compareExamIds_' + this.$sessionSave.get('schoolInfo').id + '_' + this.userId);
      let token = getToken();

      let role = {};
      if (UserRole.isOperation) {
        role = '';
      } else {
        role = await UserRole.utils.getRoleSubjectClassMap();
        delete role['5'];
        delete role['6'];
      }
      let params = {
        examIds: ids.join(','),
        token: token,
        contain: contain,
        role: role ? JSON.stringify(role) : '',
      };
      let searchParams = new URLSearchParams(params);
      let downloadUrl = `${this.fsUrl}/pexam/_/his/sheet?${searchParams.toString()}`;
      window.open(`${downloadUrl}&response-content-type=application%2Foctet-stream`, '_self');
    },
    /**
     * @name:初始化数据
     */
    async initSchool() {
      let $this = this;
      let query = this.$route.query;

      this.matchingRoutes();
      await this.initYear();
      await this.initSubjectList();
      await this.initGradeList();
      if (Object.hasOwn(query, 'gradeId')) {
        this.gradeValue = Number(query.gradeId);
      }
      await this.getClassList();
      if (Object.hasOwn(query, 'classId')) {
        this.classValue = query.classId;
        this.className = this.classList.find(item => {
          return item.clsId == this.classValue;
        }).clsName;
      }
      $this.$sessionSave.set('teachingpage_grdList', $this.grdList);
      if ($this.activeTab == 3) {
        $this.$refs.teachingRef && $this.$refs.teachingRef.init && $this.$refs.teachingRef.init();
      }

      if (
        !$this.$localSave.get('compareExamGradeName_' + $this.$sessionSave.get('schoolInfo').id + '_' + this.userId)
      ) {
        $this.gradeName = $this.grdList[0]?.name || '';
      } else {
        $this.gradeName = $this.$localSave.get(
          'compareExamGradeName_' + $this.$sessionSave.get('schoolInfo').id + '_' + this.userId
        );
      }
    },

    async initYear() {
      const res = await getSchoolYearListAPI({
        schoolId: this.$sessionSave.get('schoolInfo').id,
      });
      let yearList = res.data || [];
      yearList = yearList.map(item => {
        return {
          id: item.schoolYearId,
          value: `${moment(item.startTime).format('YYYY-MM-DD')}|${moment(item.endTime).format('YYYY-MM-DD')}`,
          label: item.schoolYear,
        };
      });
      this.yearList = yearList;
      this.timeValue = 3;
      this.yearTimeSlot = this.yearList[0].value;
      this.timeSlot = this.yearTimeSlot.split('|');
      this.defMonthSlot = [new Date().setDate(1), new Date()];
    },

    async initSubjectList() {
      this.subjectList = await UserRole.getSubjectList();
      this.subjectListTrack = await UserRole.getSubjectList({
        excludeRoles: ['5', '6'],
      });
      if (UserRole.isSchoolLeader || UserRole.isOperation || UserRole.isGradeLeader)
        this.subjectListTrack.unshift({ id: '', name: '全部学科' });

      this.subjectVal = Object.hasOwn(this.$route.query, 'subjectId')
        ? this.$route.query.subjectId || this.subjectList[0]?.id
        : this.subjectList[0]?.id;

      this.subjectTrack = this.subjectListTrackFilter[0]?.id;
    },

    async initGradeList() {
      let gradeList = await UserRole.getGradeList({
        subjectId: this.subjectVal,
      });
      this.allGradeList = gradeList;
      const subjectItem = this.subjectList.find(q => q.id == this.subjectVal);
      this.grdList = subjectItem ? this.allGradeList.filter(q => q.phaseId == subjectItem.phaseId) : [];

      const gradeItem = this.grdList.find(q => q.id == this.gradeValue);
      if (gradeItem) {
        this.gradeValue = gradeItem.id;
      } else {
        this.gradeValue = this.grdList[0]?.id;
      }
      this.$sessionSave.set('teachingpage_grdList', this.grdList); // 老代码
    },

    getParams(isSet) {
      const gradeItem = this.grdList.find(q => q.id == this.gradeValue);

      this.pms = {
        schoolId: this.$sessionSave.get('schoolInfo').id,
        gradeId: Object.hasOwn(this.$route.query, 'gradeId') ? this.$route.query.gradeId : this.gradeValue || '',
        classId: Object.hasOwn(this.$route.query, 'classId') ? this.$route.query.classId : this.classValue || '',
        className: this.className || '',
        timeValue: this.timeValue,
        subjectId: Object.hasOwn(this.$route.query, 'subjectId') ? this.$route.query.subjectId : this.subjectVal || '',
        subjectIdTrack: this.subjectTrack || '',
        startTime: Object.hasOwn(this.$route.query, 'startTime') ? this.$route.query.startTime : this.timeSlotFormat[0],
        endTime: Object.hasOwn(this.$route.query, 'endTime') ? this.$route.query.endTime : this.timeSlotFormat[1],
        sourceType: this.sourceVal.length ? (this.sourceVal.length === 3 ? '' : this.sourceVal.join(',')) : '',
        year: gradeItem?.year,
      };
      if (isSet) {
        this.$store.commit('saveTeachParams', this.pms);
      }
      return this.pms;
    },
    // 切换学校
    changeSchool(type = '') {
      if (type) {
        this.subjectList = [];
        this.$store.commit('saveTeachParams', {});
        this.initSchool();
      }
    },
    matchingRoutes() {
      let localPath = this.$route.path;
      for (let item of this.tabList) {
        if (localPath.indexOf(item.path) !== -1) {
          this.activeTab = item.id;
          break;
        }
      }
      if (this.activeTab == 0) {
        //更新学情追踪数据
        // this.subjectTrack = "";
        this.$bus.$emit('updateTrackData');
      }
    },
    // 切换班级学情和学生学情
    async changeRadioTab(val) {
      this.activeTab = val;
      if (val == 1) {
        let isAddAllClass = this.isAddAllClass();
        if (isAddAllClass) {
          this.addAllClass();
        }
      }

      if (val == 2) {
        this.removeAllClass();
      }

      let tab = this.tabList.find(item => item.id == val);
      if (tab) {
        this.$router.push({
          path: `/home/<USER>/teaching/${tab.path}`,
        });
      }
    },
    /**
     * @name:打开选择考试弹窗
     */
    openFilterExamDialog() {
      this.isFilterExamDialog = true;
    },
    /**
     * @name:打开选择设置弹窗
     */
    openSetDownloadDialog() {
      this.isSetDownloadDialog = true;
    },
    /**
     * @name:关闭选择考试弹窗
     */
    closeDialog() {
      this.isFilterExamDialog = false;
      this.isSetDownloadDialog = false;
    },
    /**
     * @name:获取选择的考试Id
     */
    getFilterExamIds(data) {
      this.gradeName = this.$localSave.get(
        'compareExamGradeName_' + this.$sessionSave.get('schoolInfo').id + '_' + this.userId
      );
      // const grd = this.allGradeList.find((q) => q.name == this.gradeName);
      // this.trackPhaseId = (grd && grd.phaseId) || "";
      // this.subjectTrack = this.subjectListTrackFilter[0].id;
      this.compareExamIds = data;
      this.$refs.teachingRef && this.$refs.teachingRef.getTrackData && this.$refs.teachingRef.getTrackData(data);
      this.$refs.teachingRef && this.$refs.teachingRef.getOnlineData && this.$refs.teachingRef.getOnlineData(data);
      this.$refs.teachingRef && this.$refs.teachingRef.getHisFiveRate && this.$refs.teachingRef.getHisFiveRate(data);
    },
    // 切换学科
    async changeSubject(val) {
      await this.initGradeList();
      this.getClassList();
    },
    changeTrackSubject() {},
    // 切换年级、班级
    changeData(val) {
      this.findData();
    },

    // 搜索
    handleFindData() {
      if (this.activeTab == 2 && !this.classValue) {
        this.$message.warning('请选择班级');
        return;
      }
      this.findData();
    },
    // 查询数据
    findData(isChild) {
      // if (
      //   isChild === "child" &&
      //   (!this.cascaderVal.length ||
      //     (this.cascaderVal.length && !this.cascaderVal[0]))
      // )
      //   return;
      if (!this.sourceVal || !this.sourceVal.length) {
        this.$message.error('请选择来源');
        this.emptyData();
        return;
      }
      if (!this.timeSlot) {
        this.$message.error('请选择查询时间范围');
        this.emptyData();
        return;
      }
      // if (
      //   !this.cascaderVal ||
      //   !this.cascaderVal.length ||
      //   (this.cascaderVal.length && !this.cascaderVal[0])
      // ) {
      //   return;
      // }
      this.$nextTick(() => {
        // 班级
        if (this.activeTab == 1 && this.$route.path.indexOf('previewPaper') < 0) {
          this.$refs.teachingRef &&
            this.$refs.teachingRef.listClsWeakPoint('findData', this.initScore ? 'initScore' : '');
          this.$refs.teachingRef &&
            this.$refs.teachingRef.getClzHistoryScore &&
            this.$refs.teachingRef.getClzHistoryScore();
        }
        // 学生
        else if (this.activeTab == 2 || this.$route.path.indexOf('previewPaper') > 0) {
          this.$refs.teachingRef && this.$refs.teachingRef.listClsStu && this.$refs.teachingRef.listClsStu();
        } else if (this.activeTab == 3) {
          this.$refs.teachingRef && this.$refs.teachingRef.init && this.$refs.teachingRef.init();
        }
        //学情追踪
        else if (!this.activeTab) {
          let ids = this.$localSave.get('compareExamIds_' + this.$sessionSave.get('schoolInfo').id + '_' + this.userId);
          this.$refs.teachingRef && this.$refs.teachingRef.getData && this.$refs.teachingRef.getData(ids);
        }
      });
    },
    emptyData() {
      this.$nextTick(() => {
        this.$refs.teachingRef && this.$refs.teachingRef.emptyData();
      });
    },
    // 切换来源
    changeSource() {},
    // 让得分率恢复默认值全部
    initScoreRate() {
      this.$nextTick(() => {
        if (this.activeTab == 1) {
          this.$refs.teachingRef && this.$refs.teachingRef.initScoreRate();
        }
      });
    },
    // 选择月份筛选后
    beforeFindPersonalBookListMonth(val) {
      let now = new Date(),
        y = now.getFullYear(),
        M = now.getMonth(),
        d = now.getDate();

      let start = val[0],
        y0 = start.getFullYear(),
        M0 = start.getMonth();
      let end = val[1],
        y1 = end.getFullYear(),
        M1 = end.getMonth();

      if (Number(`${y}${M < 10 ? `0${M}` : M}`) == Number(`${y1}${M1 < 10 ? `0${M1}` : M1}`)) {
        this.timeSlot = [`${y0}-${M0 + 1}-01`, `${y1}-${M + 1}-${d}`];
      } else {
        let e2 = new Date(y1, M1 + 1, 0),
          y2 = e2.getFullYear(),
          M2 = e2.getMonth(),
          d2 = e2.getDate();

        this.timeSlot = [`${y0}-${M0 + 1}-01`, `${y2}-${M2 + 1}-${d2}`];
      }

      this.getClassList();
    },

    // 判断是否添加全部班级
    isAddAllClass() {
      if (this.activeTab != 1) return false;
      if (this.classList.some(item => item.clsId == '')) return false;

      let grade = this.allGradeList.find(t => t.id == this.gradeValue);
      let subject = this.subjectList.find(t => t.id == this.subjectVal);

      if (UserRole.isOperation || UserRole.isSchoolLeader) {
        return true;
      } else {
        let roles = UserRole.utils.getSubjectRoles(subject.id, grade?.year);
        return [1, 2, 3, 4].some(role => roles.includes(role));
      }
    },

    // 添加全部班级
    addAllClass() {
      this.classList.unshift({
        clsId: '',
        clsName: '全部班级',
      });
    },

    // 移除全部班级
    removeAllClass() {
      this.classList = this.classList.filter(item => item.clsId != '');
      if (this.classValue == '' && this.classList.length) {
        this.classValue = this.classList[0].clsId;
        this.className = this.classList[0].clsName;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.disFlex {
  display: flex;
  flex-direction: column;
}

.radioGroup {
}

.teaching {
  border-radius: 10px;
}

.teaching-main {
  background: #fff;
  padding: 20px;
}

.teachingPage-header {
  position: relative;
  display: flex;
  justify-content: space-between;

  width: 100%;
  padding: 16px 40px 16px 28px;

  background: #fff;

  ::v-deep {
    .el-button {
      font-size: 16px;
    }

    .el-input__inner {
      font-size: 16px;
    }
  }

  .header__select {
    display: inline-block;
    margin-right: 20px;
    color: #2e4b81;
    font-size: 16px;
  }

  .filter-text {
    font-size: 16px;
    color: #666666;
    margin-left: 15px;
  }

  .grade-text {
    font-weight: 700;
  }

  .export-button {
    font-weight: 700;
  }
}

.tab-wrapper {
  width: 100%;
  line-height: 50px;
  background: #e9f4ff;
  display: flex;
  border-radius: 10px 10px 0 0;

  .tab-item {
    min-width: 150px;
    height: 50px;
    font-size: 18px;
    border-radius: 10px 10px 0px 0px;
    text-align: center;
    padding: 0 15px;
    cursor: pointer;

    &.active {
      background: #ffffff;
      border: 1px solid #ffffff;
      font-weight: bold;
      font-size: 18px;
      color: #409eff;
    }
  }
  // margin-bottom: 16px;
}
</style>

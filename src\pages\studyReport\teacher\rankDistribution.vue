<!--
 * @Description: 
 * @Author: 小圆
 * @Date: 2024-05-10 16:06:26
 * @LastEditors: 小圆
-->
<template>
  <div>
    <el-radio-group v-model="tableParams.isHideThisParagraph" @input="onHideTypeChange">
      <el-radio-button :label="0">显示本段</el-radio-button>
      <el-radio-button :label="1">隐藏本段</el-radio-button>
    </el-radio-group>
    <div v-loading="tableLoading">
      <base-table
        v-if="tableData.length"
        :data="tableData"
        :column="tableColumns"
        v-bind="getTableAttr()"
        v-drag-table
      ></base-table>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins, Prop } from 'vue-property-decorator';
import TableCommon, { getDefaultTableAttr } from '../components/TableCommon';

@Component({
  components: {},
})
export default class Index extends Mixins(TableCommon) {
  tableParams = {
    // 是否隐藏本段 0:不隐藏 1:隐藏
    isHideThisParagraph: 0 as 0 | 1,
  };

  /**
   * 设置列属性
   * @param columns 列数组
   * @returns 返回处理后的列数组
   */
  setColumnProp(columns: IColumn[]) {
    const recursion = (columns: IColumn[]) => {
      columns.forEach(item => {
        if (item.children && item.children.length > 0) {
          item.title = item.title && (item as any).title !== 0 ? item.title : '--';
          if (
            item.title === '教学单位' ||
            item.title === '任课教师' ||
            item.title === '参考人数' ||
            item.title === '名次段'
          ) {
            item.width = 140;
            item.fixed = true;
          }
          recursion(item.children);
        } else {
          item.title = item.title && (item as any).title !== 0 ? item.title : '--';
          item.minWidth = item.minWidth ? item.minWidth : 120;
          if (
            item.title === '教学单位' ||
            item.title === '任课教师' ||
            item.title === '参考人数' ||
            item.title === '名次段'
          ) {
            item.width = 140;
            item.fixed = true;
          }
          item.resizable = false;
          item.fixed =
            this.tableLeftFixed.includes(item.title) || this.tableLeftFixed.includes(item.prop)
              ? 'left'
              : this.tableRightFixed.includes(item.title) ||
                this.tableRightFixed.includes(item.prop)
              ? 'right'
              : false;
        }
      });
    };
    recursion(columns);
    return columns;
  }

  getTableAttr() {
    const attr = getDefaultTableAttr();
    return { ...attr, maxHeight: 800 };
  }

  onHideTypeChange(type) {
    this.getTableData();
  }
}
</script>

<style scoped lang="scss"></style>

<!--
 * @Description: 校内差距
 * @Author: 小圆
 * @Date: 2024-04-02 09:02:59
 * @LastEditors: 小圆
-->

<template>
  <div>
    <div v-loading="tableLoading">
      <virtual-scroll
        v-if="tableData.length"
        :data="tableData"
        :item-size="62"
        key-prop="stuNo"
        :throttleTime="100"
        @change="data => (virtualList = data)"
      >
        <base-table
          :data="virtualList"
          :column="tableColumns"
          row-key="stuNo"
          v-bind="getTableAttr()"
          v-drag-table
        ></base-table>
      </virtual-scroll>
      <no-data v-else text="暂无数据" style="height: 500px"></no-data>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Mixins, Prop } from 'vue-property-decorator';
import TableCommon, { getDefaultTableAttr } from '../components/TableCommon';
import VirtualScroll from '@/plugins/el-table-virtual-scroll';

@Component({
  components: {
    VirtualScroll,
  },
})
export default class SchoolDisparity extends Mixins(TableCommon) {
  // 虚拟列表
  virtualList: any[] = [];
  // 表格左侧固定列
  tableLeftFixed: any[] = ['rank', 'stuNo', 'stuName', 'className'];

  getTableAttr() {
    const attr = getDefaultTableAttr();
    delete attr.maxHeight;
    return {
      ...attr,
      height: '600px',
    };
  }

  render(h) {
    const table = this.renderTable(h);
    return table;
  }
}
</script>

<style scoped lang="scss"></style>
